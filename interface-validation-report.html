
<!DOCTYPE html>
<html>
<head>
    <title>接口校验报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        .test-case { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .passed { border-left: 4px solid #4CAF50; }
        .failed { border-left: 4px solid #f44336; }
        .issues { background: #fff3cd; padding: 10px; margin-top: 10px; }
        .errors { background: #f8d7da; padding: 10px; margin-top: 10px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>数据体检模块接口校验报告</h1>
    
    <div class="summary">
        <h2>测试总结</h2>
        <p><strong>测试时间:</strong> 2025-09-09T12:00:49.608Z</p>
        <p><strong>总计:</strong> 11</p>
        <p><strong>通过:</strong> 11</p>
        <p><strong>失败:</strong> 0</p>
        <p><strong>错误:</strong> 0</p>
    </div>
    
    <h2>详细结果</h2>
    
        <div class="test-case passed">
            <h3>获取用户权限 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /permissions/1</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取异常概览统计 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /exceptions/overview</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>按异常类型统计 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /exceptions/statistics/by-type</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>按单位统计 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /exceptions/statistics/by-unit</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取异常详情列表 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /exceptions/details?page=1&pageSize=10</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取党组织规则 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /rules/party-organization</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取所有规则 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /rules</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取自动体检配置 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /auto-inspection/config</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取数据源列表 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /data-sources</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取定时任务列表 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /scheduled-tasks</p>
            
            
            
            
        </div>
    
        <div class="test-case passed">
            <h3>获取整改权限 ✓</h3>
            <p><strong>方法:</strong> GET <strong>路径:</strong> /remediation/permissions/1</p>
            
            
            
            
        </div>
    
    
</body>
</html>