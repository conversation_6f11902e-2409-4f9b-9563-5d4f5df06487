/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AAffix: typeof import('ant-design-vue/es')['Affix']
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAutoComplete: typeof import('ant-design-vue/es')['AutoComplete']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    AAvatarGroup: typeof import('ant-design-vue/es')['AvatarGroup']
    ABackTop: typeof import('ant-design-vue/es')['BackTop']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    AButtonGroup: typeof import('ant-design-vue/es')['ButtonGroup']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACardMeta: typeof import('ant-design-vue/es')['CardMeta']
    ACarousel: typeof import('ant-design-vue/es')['Carousel']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AImage: typeof import('ant-design-vue/es')['Image']
    AImagePreviewGroup: typeof import('ant-design-vue/es')['ImagePreviewGroup']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputGroup: typeof import('ant-design-vue/es')['InputGroup']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AMenuItemGroup: typeof import('ant-design-vue/es')['MenuItemGroup']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APagination: typeof import('ant-design-vue/es')['Pagination']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioButton: typeof import('ant-design-vue/es')['RadioButton']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARate: typeof import('ant-design-vue/es')['Rate']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASlider: typeof import('ant-design-vue/es')['Slider']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    AStatisticCountdown: typeof import('ant-design-vue/es')['StatisticCountdown']
    AStep: typeof import('ant-design-vue/es')['Step']
    ASteps: typeof import('ant-design-vue/es')['Steps']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATableColumn: typeof import('ant-design-vue/es')['TableColumn']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATimeline: typeof import('ant-design-vue/es')['Timeline']
    ATimelineItem: typeof import('ant-design-vue/es')['TimelineItem']
    ATimePicker: typeof import('ant-design-vue/es')['TimePicker']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATransfer: typeof import('ant-design-vue/es')['Transfer']
    ATree: typeof import('ant-design-vue/es')['Tree']
    ATreeSelect: typeof import('ant-design-vue/es')['TreeSelect']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    ATypographyTitle: typeof import('ant-design-vue/es')['TypographyTitle']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AUploadDragger: typeof import('ant-design-vue/es')['UploadDragger']
    CaseBreadcrumb: typeof import('./src/components/common/CaseBreadcrumb.vue')['default']
    ConversionRulesConfig: typeof import('./src/components/indicator-rules/ConversionRulesConfig.vue')['default']
    DataSourceConfig: typeof import('./src/components/indicator-rules/DataSourceConfig.vue')['default']
    FileUpload: typeof import('./src/components/file-upload/index.vue')['default']
    MapContainer: typeof import('./src/components/map-container/index.vue')['default']
    NavigationPanel: typeof import('./src/components/NavigationPanel.vue')['default']
    RankingCard: typeof import('./src/components/RankingCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
