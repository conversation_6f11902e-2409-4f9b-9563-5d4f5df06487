# 数据体检统计分析功能实现说明

## 功能概述

基于前端已有的数据体检中统计分析功能页面逻辑及其mock数据结构，实现了对应的后端逻辑，包括完整的Controller、Service、Mapper三层架构，并调整前端代码从调用mock数据变为调用真实API接口。

## 实现架构

### 1. 后端架构

#### 1.1 Controller层 (`StatisticsAnalysisController`)
- **路径**: `src/main/java/com/goodsogood/ows/controller/StatisticsAnalysisController.java`
- **功能**: 提供REST API接口，处理HTTP请求和响应
- **主要接口**:
  - `/api/statistics-analysis/core-metrics` - 获取核心指标统计
  - `/api/statistics-analysis/monthly` - 获取月度统计数据
  - `/api/statistics-analysis/department` - 获取部门统计数据
  - `/api/statistics-analysis/operator` - 获取操作人员统计数据
  - `/api/statistics-analysis/trend` - 获取趋势分析数据
  - `/api/statistics-analysis/real-time` - 获取实时统计数据
  - `/api/statistics-analysis/comprehensive` - 获取综合统计数据
  - `/api/statistics-analysis/custom-query` - 自定义统计查询
  - `/api/statistics-analysis/export` - 导出统计报表
  - `/api/statistics-analysis/exception-overview` - 获取异常统计概览
  - `/api/statistics-analysis/efficiency-stats` - 获取处理效率统计
  - `/api/statistics-analysis/dimension-options` - 获取统计维度配置选项
  - `/api/statistics-analysis/dashboard-config` - 获取仪表板配置
  - `/api/statistics-analysis/refresh-cache` - 刷新统计缓存

#### 1.2 Service层 (`StatisticsAnalysisService`)
- **路径**: `src/main/java/com/goodsogood/ows/services/StatisticsAnalysisService.java`
- **功能**: 业务逻辑处理，数据转换和计算
- **特性**:
  - 使用Spring Cache注解实现缓存机制
  - 完整的异常处理和降级处理
  - 复杂的统计计算逻辑
  - 支持多维度统计查询

#### 1.3 Mapper层 (`StatisticsAnalysisMapper`)
- **路径**: `src/main/java/com/goodsogood/ows/mapper/StatisticsAnalysisMapper.java`
- **功能**: 数据访问层，使用MyBatis注解实现SQL查询
- **特性**:
  - 使用PostgreSQL语法
  - 复杂的动态SQL查询
  - 支持多表关联和聚合计算
  - 使用CTE（公用表表达式）优化查询性能

#### 1.4 数据传输对象 (`StatisticsAnalysisVO`)
- **路径**: `src/main/java/com/goodsogood/ows/model/vo/StatisticsAnalysisVO.java`
- **功能**: 定义API请求和响应的数据结构
- **包含**:
  - `CoreMetricsVO` - 核心指标
  - `MonthlyStatisticsVO` - 月度统计
  - `DepartmentStatisticsVO` - 部门统计
  - `OperatorStatisticsVO` - 操作人员统计
  - `TrendAnalysisResultVO` - 趋势分析结果
  - `RealTimeDataVO` - 实时数据
  - `CustomQueryParamsVO` - 自定义查询参数
  - 等多个VO类

### 2. 前端调整

#### 2.1 API接口文件更新
- **文件**: `frontpage/model/src/api/statistics-analysis.ts`
- **调整内容**:
  - 更新类型定义以匹配后端API数据结构
  - 将原有的文档相关字段调整为数据体检相关字段
  - 增加新的API接口函数
  - 保持原有的降级处理机制

#### 2.2 类型定义更新
原有类型定义从文档管理相关调整为数据体检相关：

```typescript
// 更新前
export interface CoreMetrics {
  totalDocuments: number
  publishedDocuments: number
  // ...
}

// 更新后  
export interface CoreMetrics {
  totalExceptions: number
  handledExceptions: number
  completionRate: number
  // ...
}
```

### 3. 统计维度配置

系统支持多维度统计分析：

#### 3.1 主要维度
- **异常类型**: 数据缺失、数据格式错误、数据重复、数据不一致、数据过期、权限异常
- **单位组织**: 各部门单位（办公室、组织部、宣传部等）
- **严重程度**: 高（high）、中（medium）、低（low）
- **体检类型**: 党组（党委）设置、党务干部任免、任务体检、用户信息完整

#### 3.2 次要维度
- **时间范围**: 最近7天、30天、3个月、1年等
- **操作人员**: 具体的处理人员
- **处理状态**: 待处理（pending）、整改中（in_remediation）、已解决（resolved）
- **数据源**: 不同的数据来源系统

#### 3.3 时间范围配置
- 支持预设时间范围（最近7天、30天等）
- 支持自定义日期范围
- 支持不同的时间粒度（日、周、月、年）

### 4. 数据库设计

基于已有的数据库表结构（20250909.sql），主要使用以下表：

- `t_data_inspection_exception_details` - 异常详情表
- `t_data_inspection_exception_statistics` - 异常统计表
- `t_data_inspection_results` - 体检结果表
- `t_data_inspection_remediation_records` - 整改记录表
- `t_data_inspection_data_sources` - 数据源管理表
- `t_data_inspection_scheduled_tasks` - 定时任务管理表

### 5. 核心功能实现

#### 5.1 生成报告功能
- 支持多种报表类型：综合报告、部门报告、趋势报告等
- 支持多种导出格式：Excel、PDF、CSV
- 包含图表和详细数据
- 支持自定义报表内容

#### 5.2 导出数据功能
- 支持按条件筛选导出
- 支持批量导出
- 提供下载链接和文件信息
- 支持文件过期管理

#### 5.3 刷新功能
- 支持手动触发数据刷新
- 支持分类型缓存刷新
- 实时更新统计数据

#### 5.4 统计维度配置功能
- 动态获取可用的统计维度选项
- 支持主要维度和次要维度配置
- 支持时间范围配置
- 前端可动态生成筛选条件

### 6. 缓存机制

使用Spring Cache实现多级缓存：

- 核心指标缓存：`statistics:core-metrics`
- 月度统计缓存：`statistics:monthly`
- 部门统计缓存：`statistics:department`
- 操作人员统计缓存：`statistics:operator`
- 趋势分析缓存：`statistics:trend`
- 实时数据缓存：`statistics:realtime`

### 7. 异常处理和降级

#### 7.1 后端异常处理
- 完整的try-catch异常捕获
- 提供默认数据作为降级处理
- 详细的日志记录

#### 7.2 前端降级处理
- 保持原有的静态数据降级机制
- API调用失败时自动切换到mock数据
- 友好的错误提示和数据源标识

### 8. 测试

#### 8.1 单元测试
- **文件**: `src/test/java/com/goodsogood/ows/controller/StatisticsAnalysisControllerTest.java`
- **覆盖**: 主要API接口的测试用例
- **包含**: 核心指标、月度统计、部门统计、实时数据等测试

### 9. API文档

所有接口都使用Swagger注解进行文档化：
- 完整的参数说明
- 响应数据结构说明
- 示例数据
- 错误码说明

### 10. 部署和配置

#### 10.1 数据库配置
确保以下表和数据已创建：
```sql
-- 运行20250909.sql创建表结构和初始数据
-- 主要表：t_data_inspection_exception_details等
```

#### 10.2 缓存配置
在application.yml中配置缓存：
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 30分钟
```

#### 10.3 前端配置
确保前端API请求指向正确的后端地址：
```typescript
// utils/request.ts中配置baseURL
```

## 使用说明

### 1. 启动后端服务
```bash
mvn spring-boot:run
```

### 2. 启动前端服务
```bash
cd frontpage/model
npm run dev
```

### 3. 访问API
- Swagger文档：http://localhost:8080/swagger-ui.html
- 核心指标API：GET http://localhost:8080/api/statistics-analysis/core-metrics
- 实时数据API：GET http://localhost:8080/api/statistics-analysis/real-time

### 4. 前端页面
访问统计分析页面查看实时数据和各种统计图表。

## 注意事项

1. **数据库连接**: 确保数据库连接配置正确
2. **缓存配置**: 建议在生产环境中配置Redis缓存
3. **性能优化**: 复杂查询使用了CTE和索引优化
4. **数据安全**: 所有输入参数都进行了验证和过滤
5. **兼容性**: 保持了与原有前端代码的兼容性

## 扩展功能

1. **实时推送**: 可以添加WebSocket支持实时数据推送
2. **更多图表**: 可以添加更多类型的统计图表
3. **用户权限**: 可以添加基于角色的数据访问权限
4. **数据导入**: 可以添加统计数据的导入功能
5. **API性能监控**: 可以添加API调用性能监控