# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Project Overview

This is the **ows-acceptance** (OWS验收项目) - a full-stack application for data inspection and statistical analysis in the OWS (Organization Work System) platform. The project consists of a Spring Boot backend with REST APIs and a Vue.js frontend application.

## Technology Stack

### Backend
- **Framework**: Spring Boot 2.5.14 with Java 17
- **Database**: PostgreSQL with MyBatis mapping (log4jdbc for SQL logging)
- **Cache**: Redis with Spring Cache annotations
- **Message Queue**: RabbitMQ (optional, controlled by `spring.rabbitmq.run`)
- **API Documentation**: SpringFox Swagger 3.0.0
- **Additional**: Kotlin support, Excel processing (Apache POI, EasyPOI), SFTP integration (JSch)

### Frontend
- **Framework**: Vue 3.2.45 + TypeScript 4.9.5 + Vite 4.1.0
- **UI Library**: Ant Design Vue 4.2.6 with auto-import
- **State Management**: Pinia 3.0.3
- **HTTP Client**: Axios 1.11.0 (testing utilities included)

## Project Structure

### Backend Structure
```
src/main/java/com/goodsogood/ows/
├── controller/          # REST API controllers with Swagger annotations
├── services/           # Business logic layer with caching and transactions
├── mapper/             # MyBatis data access layer with dynamic SQL
├── model/
│   ├── db/            # Database entity classes
│   └── vo/            # Value objects for API requests/responses
├── util/              # Utility classes
└── helper/            # Helper classes

sql/                    # Database migration scripts
└── 20250909.sql       # Latest schema updates
```

### Frontend Structure
```
frontpage/model/
├── src/
│   ├── api/           # HTTP API modules (domain-specific)
│   ├── components/    # Reusable Vue components
│   ├── store/modules/ # Pinia stores (domain-specific)
│   ├── views/         # Page components (organized by business system)
│   ├── types/         # TypeScript definitions per domain
│   ├── utils/         # Shared utilities and helpers
│   └── router/        # Route definitions with nested structure
├── (mock-api/)        # Express mock server would be here (not implemented yet)
└── package.json       # Frontend dependencies
```

## Common Development Commands

### Backend Development
```bash
# Build the project
mvn clean compile

# Run the backend application (port 8080)
mvn spring-boot:run

# Run tests
mvn test

# Package as JAR
mvn clean package

# Run single test
mvn test -Dtest=ClassName#methodName
```

### Frontend Development
```bash
cd frontpage/model

# Install dependencies
npm install

# Start frontend dev server (port 5173/5174)
npm run dev

# Start Mock API server (REQUIRED - run in separate terminal)
# Note: Mock API directory does not exist - create if needed for development

# Build for production
npm run build

# Build with TypeScript checking
npm run build1

# Preview production build
npm run preview
```

### Database Operations
```bash
# Apply database migrations (manual execution required)
psql -h <host> -U <user> -d gs_ows_mas_test -f sql/20250909.sql

# Check database connection
psql -h ************** -U vbadmin -d gs_ows_mas_test
```

## Core Business Modules

1. **Data Inspection** (`DataInspection*`) - Main data health check functionality with 7 sub-modules
2. **Honor Management** (`Honor*`) - Party member honor and achievement system
3. **Statistics Analysis** (`StatisticsAnalysis*`) - Comprehensive data analytics and reporting
4. **Remediation Records** (`RemediationRecord*`) - Exception handling and remediation tracking

## Database Configuration

The project uses multiple database connections configured in `application.yml`:

- **Main DB**: `gs_ows_mas_test` (PostgreSQL) - Core application data on **************:5432
- **Payment DB**: `gs_pay` (PostgreSQL) - Payment related data on **************
- **Doris DB**: `ows_ppmd_test` (MySQL) - Analytics data warehouse on **************

Connection pooling managed by HikariCP with optimized settings for PostgreSQL.

## Key Architecture Patterns

### Backend: Three-Layer Architecture
1. **Controller Layer**: REST endpoints with Swagger documentation (`@Api`, `@ApiOperation`)
2. **Service Layer**: Business logic with caching (`@Cacheable`) and transaction management (`@Transactional`)
3. **Mapper Layer**: MyBatis-based data access with dynamic SQL and complex CTEs for analytics

### Frontend: Domain-Driven Modular Architecture
- **Dual-Server Setup**: Frontend dev server + Mock API server (both required)
- **Auto-Import**: Ant Design Vue components automatically imported via Vite plugin
- **Type Safety**: Comprehensive TypeScript definitions per business domain
- **State Management**: Domain-specific Pinia stores following composition API pattern

### Error Handling Strategy
- **Backend**: Global exception handler with standardized error response format
- **Frontend**: Unified request wrapper in `utils/request.ts` with error interceptors
- **Error Codes**: Defined in `application.yml` with specific error messages

### Caching Strategy
- **Backend**: Redis-based caching with Spring Cache annotations
- **Cache Pattern**: `<module>:<operation>` (e.g., `statistics:core-metrics`)
- **TTL Configuration**: Per cache type with automatic expiration
- **Cache Database**: Redis database 89 on **************:36379

## Environment Setup Requirements

### Backend Dependencies
1. **Java 17** - JVM target for Kotlin and Java compilation
2. **PostgreSQL 10+** - Main and payment databases with required schemas
3. **Redis** - Cache server with password authentication
4. **RabbitMQ** - Message queue (optional, disabled by default)
5. **Maven** - Build tool with internal repository access

### Frontend Dependencies
1. **Node.js** - For Vue.js development and build tools
2. **Vite** - Build tool with hot module replacement
3. **API Testing** - Uses interface-validation-test.js for API validation

### Critical Configuration Files
- **Backend**: `application.yml`, `log4j2-dev.xml`, `pom.xml`
- **Frontend**: `package.json`, `vite.config.ts`, `interface-validation-test.js`

## Testing Strategy

### Backend Testing
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=DataInspectionRuleConfigControllerTest

# Integration testing with @SpringBootTest
# Mock external dependencies with @MockBean
# Database tests with @Transactional rollback
```

### Frontend Testing
- Interface validation testing via `interface-validation-test.js`
- Generated test reports: `interface-validation-report.html` and `interface-validation-report.json`
- Axios-based API testing with standardized response validation

## Development Workflow

### Adding New Backend Features
1. Create database entities in `model/db/`
2. Define MyBatis mappers in `mapper/` with XML or annotations
3. Implement service layer with caching and transaction handling
4. Create REST controllers with Swagger documentation
5. Add integration tests following existing patterns

### Adding New Frontend Features
1. Create API module in `src/api/[module].ts`
2. Create Pinia store in `src/store/modules/[module].ts`
3. Define TypeScript types in `src/types/[module].ts`
4. Build views in `src/views/[module]/`
5. Add routes to `src/router/index.ts`
6. Test APIs using `interface-validation-test.js`

### API Development Pattern
- **Backend**: Use `@RestController`, `@RequestMapping` with Swagger annotations
- **Frontend**: Use Axios for HTTP requests with Vite proxy configuration
- **Testing**: Use interface validation for API response format consistency
- **Response Format**: Standardized `{code, message, data}` structure

## Production Considerations

### Security
- Database credentials in environment variables (not `application.yml`)
- SFTP credentials externalized for BOC integration
- Redis password protection enabled
- No sensitive data in logs or cache

### Performance
- PostgreSQL CTE optimization for complex statistical queries
- HikariCP connection pooling with tuned settings
- Redis caching for frequently accessed data
- Async processing with `@EnableAsync` for time-intensive operations

### Monitoring
- Actuator endpoints enabled for health checks and metrics
- Centralized logging with log4j2 async appenders
- Eureka service discovery integration
- Request/response logging via log4jdbc

