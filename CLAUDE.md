# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the OWS (Organization Work System) Acceptance project - a comprehensive Spring Boot 2.5.14 application with Java 17, focusing on organizational management with health checks, honor management, and achievement tracking. The system includes both backend services and a Vue.js frontend in the `frontpage/` directory.

## Build and Development Commands

### Backend Development
```bash
# Build the project
mvn clean compile

# Run tests  
mvn test

# Package the application
mvn package

# Run the application (default port: 8567)
mvn spring-boot:run
```

### Frontend Development (in frontpage/ directory)
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start Mock API server (required for frontend)
cd mock-api && node app.js
```

### Database Operations
```bash
# Run MyBatis generator
mvn mybatis-generator:generate
```

### Deployment and Packaging
```bash
# Package for test environment (using deployment scripts in bin/)
./bin/package.sh [target_path] [jar_name] [version]

# Package for production
./bin/package-online.sh

# Development packaging
./bin/pkg-dev.sh

# Stop running application
./bin/stop.sh
```

## Architecture and Structure

### Backend Architecture (Spring Boot)
- **Main Application**: `com.goodsogood.ows.OwsAcceptanceApplication`
- **Package Structure**:
  - `controller/` - REST controllers (3 controllers: HealthCheck, Honor, HonorAchievement)
  - `services/` - Business logic services
  - `mapper/` - MyBatis mapper interfaces  
  - `model/` - Data models (db entities in `model/db/`, view objects in `model/vo/`)
  - `helper/` - Utility helpers
  - `util/` - General utilities

### Key Technologies and Dependencies
- **Spring Boot 2.5.14** with Spring Cloud 2020.0.5
- **Java 17** with Kotlin 1.7.10 support
- **PostgreSQL** database with MyBatis for ORM
- **Redis** for caching (Jedis 3.6.3)
- **RabbitMQ** for messaging
- **Swagger/OpenAPI 3.0** (SpringFox 3.0.0)
- **Lombok** for code generation
- **Apache POI** for Excel processing
- **Hutool** utilities library

### Frontend Architecture (Vue 3)
Located in `frontpage/model/` - see that directory's CLAUDE.md for detailed frontend guidance.

### Database Schema
Key modules include:
- **Health Check Module**: Tables for health check records, rules, tasks, and exceptions
- **Honor Management**: Tables for honors and achievements
- Database scripts located in `sql/` directory

## Configuration

### Environment Profiles
- `application.yml` - Base configuration
- `application-dev.yml` - Development environment  
- `application-test.yml` - Test environment
- `application-pre.yml` - Pre-production environment
- `application-online.yml` - Production environment

### Key Configuration Points
- **Server Port**: 8567
- **Database**: PostgreSQL on **************:5432
- **Redis**: Port 36379, database 89
- **Eureka**: Service discovery enabled
- **Logging**: Log4j2 with different profiles

## Development Workflow

### Making Changes to Backend
1. Follow the existing package structure under `com.goodsogood.ows`
2. Use the existing OWS starter framework (`ows-starter-spring-boot 4.0.2-zzb-SNAPSHOT`)
3. Add new controllers following the pattern of existing ones
4. Create corresponding service and mapper classes
5. Update database scripts in `sql/` directory if schema changes needed

### Database Changes
- Place new SQL scripts in `sql/` directory
- Follow PostgreSQL syntax and naming conventions
- Use sequence-based auto-increment for primary keys
- Add proper comments to tables and columns

### Code Style and Conventions
- Use Lombok annotations for boilerplate code
- Follow Spring Boot best practices
- RestTemplate with load balancing configured
- Error handling through `ClientExceptionHandler`
- Async processing with custom thread pool executors

### Testing and Quality
- Run `mvn test` before committing changes
- Ensure all Spring Boot actuator endpoints work (health, metrics, etc.)
- Test with appropriate environment profiles

## Deployment and Operations

### Environment Setup
The build system supports multiple deployment targets:
- **Test Environment**: Uses `application-test.yml` profile, version format `v1.0.1-test`
- **Production Environment**: Uses `application-online.yml` profile
- **Development**: Quick packaging with `pkg-dev.sh`

### Build Process Details
- Maven build with Kotlin compilation: `mvn clean kotlin:compile package`
- Automatic version management with Maven versions plugin
- SQL scripts automatically included in deployment package
- Log4j2 configuration files copied per environment

### Process Management
- Application creates `pid` file for process tracking
- Graceful shutdown with 120-second timeout before force kill
- Process monitoring via shell scripts

## Important Notes

- **Service Discovery**: Uses Eureka client for service registration
- **Multi-language Support**: Includes Kotlin support alongside Java
- **SFTP Integration**: Bank of China SFTP configuration present but disabled by default
- **Scheduler**: Cron-based scheduling disabled by default (`run-scheduler: false`)
- **RabbitMQ**: Messaging support configured but can be disabled
- **Dual Frontend**: Contains both a main Vue.js frontend and additional frontpage system
- **Custom OWS Framework**: Built on top of custom `ows-starter-spring-boot` framework

## Branch Strategy
- Main branch: `master`
- Current working branch: `v4.0.2-mams-tanglilin`
- Follow git flow for feature development