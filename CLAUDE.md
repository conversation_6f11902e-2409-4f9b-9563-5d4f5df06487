# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + TypeScript enterprise application for the "Model Government Agencies" (模范机关) system. The project uses Vite, Ant Design Vue, and Pinia, implementing a modular architecture with 9 major business subsystems and integrated Mock API architecture.

## Core Development Commands

### Development Workflow
```bash
# Start frontend development server (port 5173/5174)
npm run dev

# Start Mock API server (required for frontend - run in separate terminal)
cd mock-api && node app.js

# Build for production
npm run build

# Build with TypeScript checking
npm run build1

# Development build
npm run test

# Preview production build
npm run preview
```

### Build Scripts
```bash
# Package build with custom configs
npm run build:package

# Production packaging
npm run build:master

# Simple build variants
npm run build:simple
npm run build:simple:prod
```

## Architecture Overview

### Technology Stack
- **Frontend**: Vue 3.2.45 + TypeScript 4.9.5 + Vite 4.1.0
- **UI Framework**: Ant Design Vue 4.2.6 with auto-import
- **State Management**: Pinia 3.0.3 with modular stores
- **Routing**: Vue Router 4.2.4 (Hash mode)
- **HTTP Client**: Axios 1.10.0 with unified request wrapper
- **Mock API**: Express.js server (port 3002) with CORS support

### Project Structure Patterns
The codebase follows a domain-driven modular architecture:

```
src/
├── api/              # HTTP API modules, one per business domain
├── components/       # Reusable Vue components
├── store/modules/    # Pinia stores, domain-specific
├── views/           # Page components organized by business system  
├── types/           # TypeScript definitions per domain
├── utils/           # Shared utilities and helpers
├── router/          # Route definitions with nested structure
└── config/          # App configuration (breadcrumbs, permissions)
```

### Business Systems Architecture
The application contains 9 integrated business systems:
- **Health Check** (`health-check`) - Data quality monitoring with 7 sub-modules
- **Honor Management** (`honor`) - Honor application and approval
- **Review System** (`review`) - Intelligent audit workflows  
- **Case Collection** (`case-collection`) - Case submission and review
- **Model Agency Dashboard** (`model-agency-dashboard`) - Executive dashboard
- **Cultivation Warning** (`cultivation-warning`) - Process monitoring alerts
- **Sensitive Words** (`sensitive-words`) - Content filtering
- **Workflow Engine** (`workflow`) - Process design and management
- **Message Center** (`message-center`) - Notification system

Each system follows the pattern: `api/` → `store/` → `types/` → `views/[system]/`

## Mock API Architecture (Critical)

### Dual-Server Setup
This project requires **two servers running simultaneously**:
1. **Frontend Dev Server**: `npm run dev` (Vite on port 5173/5174)
2. **Mock API Server**: `cd mock-api && node app.js` (Express on port 3002)

### Mock API Structure
```
mock-api/
├── app.js                           # Express server with CORS, logging
├── routes/
│   ├── data-inspection.js           # Health check API endpoints
│   ├── audit-confirmation.js        # Review system endpoints  
│   └── indicator-rules-management.js # Rules management endpoints
└── package.json                     # Express dependencies
```

### Request Flow Architecture
- Frontend makes HTTP requests via `utils/request.ts`
- Vite proxy redirects `/api/*` calls to `http://localhost:8567` (prod) or Mock API
- Mock API returns standardized JSON responses with `{code, message, data}` format
- 17+ API functions converted from inline mocks to HTTP endpoints

### Mock API Features
- Health check endpoint: `GET /health`
- API documentation: `GET /api`
- Request/response logging with timestamps
- CORS enabled for `localhost:5173`
- Unified error handling and 404 responses

## Development Patterns

### TypeScript Configuration
- Strict mode enabled with `vue-tsc` checking
- Auto-imports configured for Ant Design Vue components
- Path aliases: `@/`, `store/`, `components/`, `utils/`, `api/`
- Type definitions organized by business domain

### State Management Pattern
```typescript
// Each business system has its own Pinia store
store/modules/
├── health-check.ts      # Health check system state
├── audit-confirmation.ts # Audit workflow state
├── honor.ts            # Honor management state
└── [system].ts         # Pattern for other systems
```

### API Pattern
```typescript
// API modules follow consistent structure
api/[system].ts:
- Interface definitions
- HTTP request functions using utils/request.ts
- Error handling and response typing
```

### Component Architecture
- Auto-imported Ant Design Vue components (via `unplugin-vue-components`)
- Shared components in `components/common/`
- Business-specific components in `views/[system]/components/`
- Layout system with `BasicLayout` and specialized layouts

## Router Architecture

### Route Structure
- Hash-based routing (`createWebHashHistory`)
- Nested route architecture supporting multi-level business systems
- Route metadata for breadcrumbs, titles, and permissions
- Layout-based organization with specialized layouts per system

### Key Route Patterns
```typescript
// Business system routes follow this pattern:
{
  path: '/system-name',
  component: Layout,
  redirect: '/system-name/index',
  children: [
    { path: 'index', component: Index },
    { path: 'sub-feature', component: SubFeature }
  ]
}
```

## Data Safety and Validation

The project implements comprehensive data safety utilities in `utils/data-validation.ts`:
- `safeNumber()` - Prevents NaN errors in numeric operations
- `safePercentage()` - Safe percentage calculations with fallbacks
- `safeDateFormat()` - Robust date formatting with error handling

## Build and Deployment

### Environment Configuration
- `.env` - Development environment variables
- `.env.production` - Production environment settings
- Vite proxy configuration for API routing
- PostCSS with `postcss-px-to-viewport` for responsive design

### Vite Configuration Key Points
- Base URL: `/pc/verify` for deployment path
- Auto-import resolvers for Ant Design Vue
- Alias configuration matching TypeScript paths
- Development proxy for API and file endpoints to port 8567

## Testing and Debugging

### Built-in Testing Tools
- Route testing utility at `/route-test`
- Component testing pages in various modules
- Data validation testing via `test-data-validation.html`
- Dashboard testing with `dashboard-test.html`

### Development Utilities
- Request/response logging in Mock API server
- Vue DevTools compatibility
- Hot module replacement via Vite
- TypeScript checking in build process

## Critical Development Notes

1. **Always start both servers**: Frontend dev server AND Mock API server are required
2. **API Pattern**: All new API functions should use HTTP requests, not inline mocks
3. **Component Imports**: Ant Design Vue components auto-import; no manual imports needed
4. **State Management**: Use domain-specific Pinia stores, not global state
5. **Type Safety**: All API responses and component props should be properly typed
6. **Responsive Design**: Use `postcss-px-to-viewport` for mobile compatibility

## Common Development Tasks

### Adding a New Business Module
1. Create API module in `src/api/[module].ts`
2. Add corresponding Mock API routes in `mock-api/routes/[module].js`  
3. Create Pinia store in `src/store/modules/[module].ts`
4. Define TypeScript types in `src/types/[module].ts`
5. Build views in `src/views/[module]/`
6. Add routes to `src/router/index.ts`

### API Development
- Use `utils/request.ts` for all HTTP requests
- Follow the `{code, message, data}` response format
- Add new Mock routes to `mock-api/app.js` and route files
- Test endpoints using `/api` documentation endpoint

### State Management
- Create domain-specific stores using Pinia composition API
- Import stores using the `store/` alias
- Use TypeScript for all store state and actions

This architecture supports enterprise-scale Vue.js development with proper separation of concerns, type safety, and mock-first API development.