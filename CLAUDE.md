# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the OWS (Organization Work System) Acceptance project - a comprehensive Spring Boot 2.5.14 application with Java 17, focusing on organizational management with health checks, honor management, and achievement tracking. The system includes both backend services and a Vue.js frontend in the `frontpage/` directory.

The project implements a data inspection and quality management system with comprehensive health check capabilities, honor management workflows, and achievement tracking functionality. It's built on the custom OWS framework (`ows-starter-spring-boot 4.0.2-zzb-SNAPSHOT`) with microservices architecture using Eureka for service discovery.

## Build and Development Commands

### Backend Development
```bash
# Build the project
mvn clean compile

# Run tests  
mvn test

# Package the application
mvn package

# Run the application (default port: 8080 for dev, 8567 for production)
mvn spring-boot:run

# Build with Kotlin compilation (required due to Kotlin support)
mvn clean kotlin:compile compile

# Full build with Kotlin compilation and skip tests
mvn clean kotlin:compile package -Dmaven.test.skip=true
```

### Frontend Development (in frontpage/model/ directory)
```bash
# Start development server (port 5173/5174)
npm run dev

# Build for production
npm run build

# Build with TypeScript checking
npm run build1

# Preview production build
npm run preview

# Test build for development
npm run test

# Package builds
npm run build:package
npm run build:master
npm run build:test
npm run build:simple
npm run build:simple:prod
```

### Root Level Frontend Testing
```bash
# Run interface validation tests (uses axios)
node interface-validation-test.js
```

### Database Operations
```bash
# Run MyBatis generator
mvn mybatis-generator:generate
```

### Deployment and Packaging
```bash
# Package for test environment (using deployment scripts in bin/)
./bin/package.sh [target_path] [jar_name] [version]

# Package for production
./bin/package-online.sh

# Development packaging
./bin/pkg-dev.sh

# Stop running application
./bin/stop.sh
```

## Architecture and Structure

### Backend Architecture (Spring Boot)
- **Main Application**: `com.goodsogood.ows.OwsAcceptanceApplication`
- **Package Structure**:
  - `controller/` - REST controllers (DataInspectionController, DataInspectionHealthCheckController, HonorController, HonorAchievementController)
  - `services/` - Business logic services (9 services covering data inspection, honor management, remediation records)
    - DataInspectionDataSourceService, DataInspectionExceptionService, DataInspectionResultService
    - DataInspectionRuleService, DataInspectionTaskLogService, DataInspectionUserPermissionService
    - HonorService, HonorAchievementService, RemediationRecordService
  - `mapper/` - MyBatis mapper interfaces for database operations (auto-generated and custom mappers)
  - `model/` - Data models (db entities in `model/db/`, view objects in `model/vo/`)
  - `helper/` - Utility helpers (HeaderHelper, SpringContextUtil, RemoteApiHelper)
  - `util/` - General utilities

### Key Technologies and Dependencies
- **Spring Boot 2.5.14** with Spring Cloud 2020.0.5
- **Java 17** with Kotlin 1.7.10 support
- **PostgreSQL** database with MyBatis for ORM
- **Redis** for caching (Jedis 3.6.3)
- **RabbitMQ** for messaging (Spring AMQP)
- **Swagger/OpenAPI 3.0** (SpringFox 3.0.0)
- **Lombok 1.18.30** for code generation
- **Apache POI 4.1.2** for Excel processing
- **Hutool 5.8.18** utilities library
- **FastJSON 1.2.72** for JSON processing
- **EasyPOI 4.4.0** for Excel import/export
- **JSch 0.1.55** for SFTP operations
- **Disruptor 3.4.4** for async logging

### Frontend Architecture (Vue 3)
Located in `frontpage/model/` - Vue 3 + TypeScript enterprise application using Vite, Ant Design Vue, and Pinia. Implements modular architecture with 9 major business subsystems and integrated Mock API architecture.

**Key Frontend Technologies**:
- Vue 3.2.45 + TypeScript 4.9.5 + Vite 4.1.0
- Ant Design Vue 4.2.6 with auto-import
- Pinia 3.0.3 for state management
- Vue Router 4.2.4 (Hash mode)
- Mock API server (Express.js on port 3002)

**Root Level Frontend**: Contains interface validation testing (`interface-validation-test.js`, `interface-validation-report.*`) using axios for API validation and testing.

### Database Schema
Key modules include:
- **Data Inspection Module**: Tables for health check records, rules, tasks, exceptions, and user permissions
- **Honor Management**: Tables for honors and achievements (`t_honor`, `t_honor_achievement`)
- **Remediation Records**: Tables for tracking data remediation activities
- Database scripts located in `sql/` directory with files like `health_check.sql`, `t_honor.sql`, `t_honor_achievement.sql`, `20250909.sql`

**Database Configuration**:
- Primary Database: PostgreSQL on `**************:5432` (gs_ows_mas_test)
- Additional databases: gs_ows_zzb_test, gs_pay, ows_ppmd_test (Doris)

## Configuration

### Environment Profiles
- `application.yml` - Base configuration
- `application-dev.yml` - Development environment  
- `application-test.yml` - Test environment
- `application-pre.yml` - Pre-production environment
- `application-online.yml` - Production environment

### Key Configuration Points
- **Server Port**: 8080 (development), 8567 (production-ready port in documentation)
- **Database**: PostgreSQL on **************:5432 (database name: gs_ows_mas_test)
- **Redis**: Port 36379, database 89, host **************, password: Qwer1234..
- **Eureka**: Service discovery enabled on **************:8104 (appname: ows-acceptance-tll)
- **RabbitMQ**: Messaging on **************:5672 (disabled by default: `run: false`)
- **Logging**: Log4j2 with different profiles (log4j2-dev.xml, log4j2-for-test.xml)
- **Actuator Endpoints**: Health check at `/ows-ppmd-sta/root/ping`, other endpoints at `/actuator/*`

## Development Workflow

### Making Changes to Backend
1. Follow the existing package structure under `com.goodsogood.ows`
2. Use the existing OWS starter framework (`ows-starter-spring-boot 4.0.2-zzb-SNAPSHOT`)
3. Add new controllers following the pattern of existing ones
4. Create corresponding service and mapper classes
5. Update database scripts in `sql/` directory if schema changes needed

### Database Changes
- Place new SQL scripts in `sql/` directory
- Follow PostgreSQL syntax and naming conventions
- Use sequence-based auto-increment for primary keys
- Add proper comments to tables and columns

### Code Style and Conventions
- Use Lombok annotations for boilerplate code
- Follow Spring Boot best practices
- RestTemplate with load balancing configured (`@LoadBalanced`)
- Error handling through `ClientExceptionHandler`
- Async processing with custom thread pool executors
- Controllers use `@CrossOrigin(origins = "*", maxAge = 3600)`
- Request monitoring with `@HttpMonitorLogger` for API logging
- Service registration with Eureka for microservices architecture

### Testing and Quality
- Run `mvn test` before committing changes
- Ensure all Spring Boot actuator endpoints work (health, metrics, etc.)
- Test with appropriate environment profiles

## Deployment and Operations

### Environment Setup
The build system supports multiple deployment targets:
- **Test Environment**: Uses `application-test.yml` profile, version format `v1.0.1-test`
- **Production Environment**: Uses `application-online.yml` profile
- **Development**: Quick packaging with `pkg-dev.sh`

### Build Process Details
- Maven build with Kotlin compilation: `mvn clean kotlin:compile package`
- Automatic version management with Maven versions plugin (`mvn versions:set -DnewVersion=${VER}`)
- SQL scripts automatically included in deployment package
- Log4j2 configuration files copied per environment
- Deployment scripts handle configuration file swapping and restoration
- Bootstrap configuration files temporarily moved during build process

### Process Management
- Application creates `pid` file for process tracking
- Graceful shutdown with 120-second timeout before force kill
- Process monitoring via shell scripts

## Frontend Development Notes

### Frontend Architecture Patterns
- Domain-driven modular architecture with business systems
- TypeScript strict mode with auto-imports for Ant Design Vue
- State management via domain-specific Pinia stores
- Hash-based routing with nested route architecture
- API modules with integrated mock data for development and testing

### API Structure
```
frontpage/model/src/api/
├── health-check.ts                 # Health check/data inspection API
├── audit-confirmation.ts           # Audit confirmation endpoints
├── honor.ts                        # Honor management API
├── indicator-rules.ts              # Rules management
├── case-collection.ts              # Case collection with mock support
├── sensitive-words.ts              # Sensitive words management
├── statistics-analysis.ts          # Statistics and analysis
└── json/                           # Mock data files
```

### Development Environment
- Uses Vite dev server on port 5173/5174
- Built-in API mocking through TypeScript modules
- Environment configuration via .env files (.env, .env.production, .env.test)
- Docker support with nginx configuration

## Important Notes

- **Service Discovery**: Uses Eureka client for service registration (appname: `ows-acceptance-tll`)
- **Multi-language Support**: Includes Kotlin support alongside Java (Kotlin 1.7.10, JVM target 17)
- **SFTP Integration**: Bank of China SFTP configuration present but disabled by default (`run-flag: false`)
- **Scheduler**: Cron-based scheduling disabled by default (`run-scheduler: false`)
- **RabbitMQ**: Messaging support configured but disabled (`run: false` in spring.rabbitmq)
- **Dual Frontend**: Contains Vue.js frontend in `frontpage/model/` and interface validation testing
- **Custom OWS Framework**: Built on top of custom `ows-starter-spring-boot 4.0.2-zzb-SNAPSHOT` framework
- **Cross-Origin**: Controllers configured with `@CrossOrigin(origins = "*", maxAge = 3600)`
- **API Monitoring**: Uses `@HttpMonitorLogger` for request logging
- **Thread Pool**: Custom thread pool executors configured for async operations
- **Interface Testing**: Root-level axios-based testing for API validation (`interface-validation-test.js`)
- **Build Flexibility**: Multiple build configurations for different deployment scenarios
- **Development Tools**: Comprehensive ESLint, Prettier, and TypeScript configuration

## Branch Strategy
- Main branch: `master`
- Current working branch: `v4.0.2-mams-tanglilin`
- Follow git flow for feature development