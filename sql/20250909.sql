-- 数据体检模块数据库表结构设计
-- 创建时间: 2025-09-09
-- 说明: 基于前端mock-api/routes/data-inspection.js分析生成
-- 数据库: PostgreSQL 10+

-- ================================
-- 1. 用户权限表
-- ================================
CREATE SEQUENCE  t_data_inspection_user_permissions_id_seq;
CREATE TABLE t_data_inspection_user_permissions (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_user_permissions_id_seq'::regclass) PRIMARY KEY,
    user_id BIGINT NOT NULL UNIQUE,
    can_view_inspection BOOLEAN DEFAULT FALSE,
    can_configure_rules BOOLEAN DEFAULT FALSE,
    can_handle_exceptions BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_user_permissions IS '数据体检用户权限表';
COMMENT ON COLUMN t_data_inspection_user_permissions.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_user_permissions.user_id IS '用户ID';
COMMENT ON COLUMN t_data_inspection_user_permissions.can_view_inspection IS '是否可以查看体检';
COMMENT ON COLUMN t_data_inspection_user_permissions.can_configure_rules IS '是否可以配置规则';
COMMENT ON COLUMN t_data_inspection_user_permissions.can_handle_exceptions IS '是否可以处理异常';
COMMENT ON COLUMN t_data_inspection_user_permissions.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_user_permissions.update_time IS '更新时间';

-- ================================
-- 2. 异常详情表
-- ================================
CREATE SEQUENCE  t_data_inspection_exception_details_id_seq;
CREATE TABLE t_data_inspection_exception_details (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_exception_details_id_seq'::regclass) PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    unit VARCHAR(100),
    source VARCHAR(100),
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high')),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_remediation', 'resolved')),
    detect_time TIMESTAMP,
    affected_records INTEGER DEFAULT 0,
    impact TEXT,
    solution TEXT,
    handle_time TIMESTAMP,
    handler VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_exception_details IS '异常详情表';
COMMENT ON COLUMN t_data_inspection_exception_details.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_exception_details.type IS '异常类型: 数据缺失,数据格式错误,数据重复,数据不一致,数据过期,权限异常';
COMMENT ON COLUMN t_data_inspection_exception_details.title IS '异常标题';
COMMENT ON COLUMN t_data_inspection_exception_details.description IS '异常描述';
COMMENT ON COLUMN t_data_inspection_exception_details.unit IS '涉及单位';
COMMENT ON COLUMN t_data_inspection_exception_details.source IS '数据来源';
COMMENT ON COLUMN t_data_inspection_exception_details.severity IS '严重程度: low-低, medium-中, high-高';
COMMENT ON COLUMN t_data_inspection_exception_details.status IS '状态: pending-待处理, in_remediation-整改中, resolved-已解决';
COMMENT ON COLUMN t_data_inspection_exception_details.detect_time IS '发现时间';
COMMENT ON COLUMN t_data_inspection_exception_details.affected_records IS '影响记录数';
COMMENT ON COLUMN t_data_inspection_exception_details.impact IS '影响分析';
COMMENT ON COLUMN t_data_inspection_exception_details.solution IS '解决方案';
COMMENT ON COLUMN t_data_inspection_exception_details.handle_time IS '处理时间';
COMMENT ON COLUMN t_data_inspection_exception_details.handler IS '处理人';
COMMENT ON COLUMN t_data_inspection_exception_details.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_exception_details.update_time IS '更新时间';

-- ================================
-- 3. 异常统计表
-- ================================
CREATE SEQUENCE  t_data_inspection_exception_statistics_id_seq;
CREATE TABLE t_data_inspection_exception_statistics (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_exception_statistics_id_seq'::regclass) PRIMARY KEY,
    statistics_type VARCHAR(20) NOT NULL CHECK (statistics_type IN ('by_type', 'by_unit')),
    statistics_key VARCHAR(100) NOT NULL,
    total_count INTEGER DEFAULT 0,
    high_severity_count INTEGER DEFAULT 0,
    medium_severity_count INTEGER DEFAULT 0,
    low_severity_count INTEGER DEFAULT 0,
    description TEXT,
    statistics_date DATE DEFAULT CURRENT_DATE,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(statistics_type, statistics_key, statistics_date)
);

COMMENT ON TABLE t_data_inspection_exception_statistics IS '异常统计表';
COMMENT ON COLUMN t_data_inspection_exception_statistics.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_exception_statistics.statistics_type IS '统计类型: by_type-按类型, by_unit-按单位';
COMMENT ON COLUMN t_data_inspection_exception_statistics.statistics_key IS '统计键值(类型名或单位名)';
COMMENT ON COLUMN t_data_inspection_exception_statistics.total_count IS '总数量';
COMMENT ON COLUMN t_data_inspection_exception_statistics.high_severity_count IS '高严重程度数量';
COMMENT ON COLUMN t_data_inspection_exception_statistics.medium_severity_count IS '中严重程度数量';
COMMENT ON COLUMN t_data_inspection_exception_statistics.low_severity_count IS '低严重程度数量';
COMMENT ON COLUMN t_data_inspection_exception_statistics.description IS '统计描述';
COMMENT ON COLUMN t_data_inspection_exception_statistics.statistics_date IS '统计日期';
COMMENT ON COLUMN t_data_inspection_exception_statistics.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_exception_statistics.update_time IS '更新时间';

-- ================================
-- 4. 体检规则表
-- ================================
CREATE SEQUENCE  t_data_inspection_rules_id_seq;
CREATE TABLE t_data_inspection_rules (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_rules_id_seq'::regclass) PRIMARY KEY,
    rule_category VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_rules IS '体检规则分类表';
COMMENT ON COLUMN t_data_inspection_rules.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_rules.rule_category IS '规则分类: partyOrganization,partyOfficials,tasks,userInfo';
COMMENT ON COLUMN t_data_inspection_rules.name IS '规则名称';
COMMENT ON COLUMN t_data_inspection_rules.description IS '规则描述';
COMMENT ON COLUMN t_data_inspection_rules.status IS '状态: active-激活, inactive-停用';
COMMENT ON COLUMN t_data_inspection_rules.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_rules.update_time IS '更新时间';

-- ================================
-- 5. 体检规则详情表
-- ================================
CREATE SEQUENCE  t_data_inspection_rule_details_id_seq;
CREATE TABLE t_data_inspection_rule_details (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_rule_details_id_seq'::regclass) PRIMARY KEY,
    rule_id BIGINT NOT NULL ,
    field_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(20) NOT NULL CHECK (rule_type IN ('required', 'enum', 'pattern', 'range')),
    rule_value TEXT NOT NULL,
    error_message VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_rule_details IS '体检规则详情表';
COMMENT ON COLUMN t_data_inspection_rule_details.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_rule_details.rule_id IS '规则ID';
COMMENT ON COLUMN t_data_inspection_rule_details.field_name IS '字段名称';
COMMENT ON COLUMN t_data_inspection_rule_details.rule_type IS '规则类型: required-必填, enum-枚举, pattern-正则, range-范围';
COMMENT ON COLUMN t_data_inspection_rule_details.rule_value IS '规则值';
COMMENT ON COLUMN t_data_inspection_rule_details.error_message IS '错误提示信息';
COMMENT ON COLUMN t_data_inspection_rule_details.create_time IS '创建时间';

-- ================================
-- 6. 数据源管理表 (P0级新增功能)
-- ================================
CREATE SEQUENCE  t_data_inspection_data_sources_id_seq;
CREATE TABLE t_data_inspection_data_sources (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_data_sources_id_seq'::regclass) PRIMARY KEY,
    source_id VARCHAR(50) UNIQUE NOT NULL,
    source_name VARCHAR(100) NOT NULL,
    source_type VARCHAR(20) DEFAULT 'mysql' CHECK (source_type IN ('mysql', 'postgresql', 'oracle', 'mongodb')),
    connection_url VARCHAR(500),
    status INTEGER DEFAULT 3 CHECK (status IN (1, 2, 3)),
    last_sync_time TIMESTAMP,
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('success', 'failed', 'pending', 'running')),
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_data_sources IS '数据源管理表';
COMMENT ON COLUMN t_data_inspection_data_sources.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_data_sources.source_id IS '数据源标识';
COMMENT ON COLUMN t_data_inspection_data_sources.source_name IS '数据源名称';
COMMENT ON COLUMN t_data_inspection_data_sources.source_type IS '数据源类型: mysql,postgresql,oracle,mongodb';
COMMENT ON COLUMN t_data_inspection_data_sources.connection_url IS '连接地址';
COMMENT ON COLUMN t_data_inspection_data_sources.status IS '连接状态: 1-正常, 2-异常, 3-未测试';
COMMENT ON COLUMN t_data_inspection_data_sources.last_sync_time IS '最后同步时间';
COMMENT ON COLUMN t_data_inspection_data_sources.sync_status IS '同步状态: success-成功, failed-失败, pending-待处理, running-运行中';
COMMENT ON COLUMN t_data_inspection_data_sources.description IS '数据源描述';
COMMENT ON COLUMN t_data_inspection_data_sources.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_data_sources.update_time IS '更新时间';

-- ================================
-- 7. 数据源同步历史表
-- ================================
CREATE SEQUENCE  t_data_inspection_sync_history_id_seq;
CREATE TABLE t_data_inspection_sync_history (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_sync_history_id_seq'::regclass) PRIMARY KEY,
    data_source_id BIGINT NOT NULL,
    sync_type VARCHAR(20) DEFAULT 'manual' CHECK (sync_type IN ('manual', 'scheduled')),
    sync_status VARCHAR(20) NOT NULL CHECK (sync_status IN ('success', 'failed', 'running')),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    sync_records INTEGER DEFAULT 0,
    error_message TEXT,
    operator VARCHAR(50),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_sync_history IS '数据源同步历史表';
COMMENT ON COLUMN t_data_inspection_sync_history.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_sync_history.data_source_id IS '数据源ID';
COMMENT ON COLUMN t_data_inspection_sync_history.sync_type IS '同步类型: manual-手动, scheduled-定时';
COMMENT ON COLUMN t_data_inspection_sync_history.sync_status IS '同步状态: success-成功, failed-失败, running-运行中';
COMMENT ON COLUMN t_data_inspection_sync_history.start_time IS '开始时间';
COMMENT ON COLUMN t_data_inspection_sync_history.end_time IS '结束时间';
COMMENT ON COLUMN t_data_inspection_sync_history.duration_seconds IS '持续时间(秒)';
COMMENT ON COLUMN t_data_inspection_sync_history.sync_records IS '同步记录数';
COMMENT ON COLUMN t_data_inspection_sync_history.error_message IS '错误信息';
COMMENT ON COLUMN t_data_inspection_sync_history.operator IS '操作人';
COMMENT ON COLUMN t_data_inspection_sync_history.create_time IS '创建时间';

-- ================================
-- 8. 定时任务管理表 (P1级新增功能)
-- ================================
CREATE SEQUENCE  t_data_inspection_scheduled_tasks_id_seq;
CREATE TABLE t_data_inspection_scheduled_tasks (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_scheduled_tasks_id_seq'::regclass) PRIMARY KEY,
    task_name VARCHAR(100) NOT NULL,
    task_type INTEGER DEFAULT 1 CHECK (task_type IN (1, 2, 3, 4)),
    check_types INTEGER[] DEFAULT ARRAY[1],
    cron_expression VARCHAR(100),
    cron_description VARCHAR(200),
    is_enabled BOOLEAN DEFAULT TRUE,
    status INTEGER DEFAULT 1 CHECK (status IN (1, 2, 3, 4)),
    last_execute_time TIMESTAMP,
    next_execute_time TIMESTAMP,
    execution_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    avg_duration_seconds INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    creator VARCHAR(50)
);

COMMENT ON TABLE t_data_inspection_scheduled_tasks IS '定时任务管理表';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.task_name IS '任务名称';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.task_type IS '任务类型: 1-数据完整性检查, 2-数据一致性检查, 3-数据准确性检查, 4-数据安全性检查';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.check_types IS '检查类型数组: 1-完整性, 2-一致性, 3-准确性, 4-安全性';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.cron_expression IS 'Cron表达式';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.cron_description IS 'Cron描述';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.is_enabled IS '是否启用';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.status IS '任务状态: 1-待执行, 2-执行中, 3-已完成, 4-执行失败';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.last_execute_time IS '最后执行时间';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.next_execute_time IS '下次执行时间';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.execution_count IS '执行次数';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.success_count IS '成功次数';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.failed_count IS '失败次数';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.avg_duration_seconds IS '平均执行时间(秒)';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.update_time IS '更新时间';
COMMENT ON COLUMN t_data_inspection_scheduled_tasks.creator IS '创建人';

-- ================================
-- 9. 任务执行历史表 (高级监控功能)
-- ================================
CREATE SEQUENCE  t_data_inspection_task_execution_history_id_seq;
CREATE TABLE t_data_inspection_task_execution_history (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_task_execution_history_id_seq'::regclass) PRIMARY KEY,
    task_id BIGINT NOT NULL,
    execution_id VARCHAR(50) NOT NULL,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_seconds INTEGER,
    status INTEGER NOT NULL CHECK (status IN (1, 2, 3, 4)),
    total_records INTEGER DEFAULT 0,
    exception_count INTEGER DEFAULT 0,
    exception_rate DECIMAL(5, 4) DEFAULT 0,
    result_summary JSONB,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_task_execution_history IS '任务执行历史表';
COMMENT ON COLUMN t_data_inspection_task_execution_history.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_task_execution_history.task_id IS '任务ID';
COMMENT ON COLUMN t_data_inspection_task_execution_history.execution_id IS '执行批次ID';
COMMENT ON COLUMN t_data_inspection_task_execution_history.start_time IS '开始时间';
COMMENT ON COLUMN t_data_inspection_task_execution_history.end_time IS '结束时间';
COMMENT ON COLUMN t_data_inspection_task_execution_history.duration_seconds IS '执行时间(秒)';
COMMENT ON COLUMN t_data_inspection_task_execution_history.status IS '执行状态: 1-运行中, 2-成功, 3-失败, 4-超时';
COMMENT ON COLUMN t_data_inspection_task_execution_history.total_records IS '总记录数';
COMMENT ON COLUMN t_data_inspection_task_execution_history.exception_count IS '异常数量';
COMMENT ON COLUMN t_data_inspection_task_execution_history.exception_rate IS '异常率';
COMMENT ON COLUMN t_data_inspection_task_execution_history.result_summary IS '结果摘要(JSON格式)';
COMMENT ON COLUMN t_data_inspection_task_execution_history.error_message IS '错误信息';
COMMENT ON COLUMN t_data_inspection_task_execution_history.create_time IS '创建时间';

-- ================================
-- 10. 任务执行日志表
-- ================================
CREATE SEQUENCE  t_data_inspection_task_logs_id_seq;
CREATE TABLE t_data_inspection_task_logs (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_task_logs_id_seq'::regclass) PRIMARY KEY,
    execution_id VARCHAR(50) NOT NULL,
    log_level VARCHAR(10) DEFAULT 'INFO' CHECK (log_level IN ('DEBUG', 'INFO', 'WARN', 'ERROR')),
    log_message TEXT NOT NULL,
    log_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    thread_name VARCHAR(50),
    class_name VARCHAR(200)
);

COMMENT ON TABLE t_data_inspection_task_logs IS '任务执行日志表';
COMMENT ON COLUMN t_data_inspection_task_logs.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_task_logs.execution_id IS '执行批次ID';
COMMENT ON COLUMN t_data_inspection_task_logs.log_level IS '日志级别: DEBUG, INFO, WARN, ERROR';
COMMENT ON COLUMN t_data_inspection_task_logs.log_message IS '日志内容';
COMMENT ON COLUMN t_data_inspection_task_logs.log_time IS '日志时间';
COMMENT ON COLUMN t_data_inspection_task_logs.thread_name IS '线程名称';
COMMENT ON COLUMN t_data_inspection_task_logs.class_name IS '类名';

-- ================================
-- 11. 自动体检配置表
-- ================================
CREATE SEQUENCE  t_data_inspection_auto_config_id_seq;
CREATE TABLE t_data_inspection_auto_config (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_auto_config_id_seq'::regclass) PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    frequency VARCHAR(20) CHECK (frequency IN ('daily', 'weekly', 'monthly')),
    execute_time TIME,
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    inspection_scope JSONB,
    check_types INTEGER[] DEFAULT ARRAY[1,2,3,4],
    last_run TIMESTAMP,
    next_run TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_auto_config IS '自动体检配置表';
COMMENT ON COLUMN t_data_inspection_auto_config.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_auto_config.config_name IS '配置名称';
COMMENT ON COLUMN t_data_inspection_auto_config.enabled IS '是否启用';
COMMENT ON COLUMN t_data_inspection_auto_config.frequency IS '执行频率: daily-每日, weekly-每周, monthly-每月';
COMMENT ON COLUMN t_data_inspection_auto_config.execute_time IS '执行时间';
COMMENT ON COLUMN t_data_inspection_auto_config.timezone IS '时区';
COMMENT ON COLUMN t_data_inspection_auto_config.inspection_scope IS '检查范围(JSON格式)';
COMMENT ON COLUMN t_data_inspection_auto_config.check_types IS '检查类型数组';
COMMENT ON COLUMN t_data_inspection_auto_config.last_run IS '最后运行时间';
COMMENT ON COLUMN t_data_inspection_auto_config.next_run IS '下次运行时间';
COMMENT ON COLUMN t_data_inspection_auto_config.status IS '状态: active-激活, inactive-停用';
COMMENT ON COLUMN t_data_inspection_auto_config.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_auto_config.update_time IS '更新时间';

-- ================================
-- 12. 体检结果表
-- ================================
CREATE SEQUENCE  t_data_inspection_results_id_seq;
CREATE TABLE t_data_inspection_results (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_results_id_seq'::regclass) PRIMARY KEY,
    inspection_name VARCHAR(100) DEFAULT NULL,
    inspection_date TIMESTAMP,
    inspection_type VARCHAR(20) DEFAULT NULL,
    total_records INTEGER DEFAULT 0,
    exception_count INTEGER DEFAULT 0,
    exception_rate DECIMAL(5, 4) DEFAULT 0,
    status VARCHAR(20) DEFAULT 1,
    duration_seconds INTEGER DEFAULT 0,
    summary JSONB,
    update_time TIMESTAMP DEFAULT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_results IS '体检结果表';
COMMENT ON COLUMN t_data_inspection_results.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_results.inspection_name IS '体检名称';
COMMENT ON COLUMN t_data_inspection_results.inspection_date IS '体检日期';
COMMENT ON COLUMN t_data_inspection_results.inspection_type IS '体检类型:1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整';
COMMENT ON COLUMN t_data_inspection_results.total_records IS '总记录数';
COMMENT ON COLUMN t_data_inspection_results.exception_count IS '异常数量';
COMMENT ON COLUMN t_data_inspection_results.exception_rate IS '异常率';
COMMENT ON COLUMN t_data_inspection_results.status IS '状态: 1-待执行 2-执行中 3-已完成 4-失败';
COMMENT ON COLUMN t_data_inspection_results.duration_seconds IS '持续时间(秒)';
COMMENT ON COLUMN t_data_inspection_results.summary IS '结果摘要(JSON格式)';
COMMENT ON COLUMN t_data_inspection_results.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_results.update_time IS '更新时间';

-- ================================
-- 13. 整改记录表
-- ================================
CREATE SEQUENCE  t_data_inspection_remediation_records_id_seq;
CREATE TABLE t_data_inspection_remediation_records (
    id BIGINT NOT NULL DEFAULT nextval('t_data_inspection_remediation_records_id_seq'::regclass) PRIMARY KEY,
    exception_id BIGINT NOT NULL,
    exception_title VARCHAR(200),
    remediation_action VARCHAR(500),
    remediation_detail TEXT,
    operator VARCHAR(50),
    start_time TIMESTAMP,
    complete_time TIMESTAMP,
    status VARCHAR(20) DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'failed')),
    verification_result VARCHAR(10) CHECK (verification_result IN ('passed', 'failed')),
    verification_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_data_inspection_remediation_records IS '整改记录表';
COMMENT ON COLUMN t_data_inspection_remediation_records.id IS '主键ID';
COMMENT ON COLUMN t_data_inspection_remediation_records.exception_id IS '异常ID';
COMMENT ON COLUMN t_data_inspection_remediation_records.exception_title IS '异常标题';
COMMENT ON COLUMN t_data_inspection_remediation_records.remediation_action IS '整改措施';
COMMENT ON COLUMN t_data_inspection_remediation_records.remediation_detail IS '整改详情';
COMMENT ON COLUMN t_data_inspection_remediation_records.operator IS '操作人';
COMMENT ON COLUMN t_data_inspection_remediation_records.start_time IS '开始时间';
COMMENT ON COLUMN t_data_inspection_remediation_records.complete_time IS '完成时间';
COMMENT ON COLUMN t_data_inspection_remediation_records.status IS '状态: in_progress-进行中, completed-已完成, failed-失败';
COMMENT ON COLUMN t_data_inspection_remediation_records.verification_result IS '验证结果: passed-通过, failed-失败';
COMMENT ON COLUMN t_data_inspection_remediation_records.verification_time IS '验证时间';
COMMENT ON COLUMN t_data_inspection_remediation_records.create_time IS '创建时间';
COMMENT ON COLUMN t_data_inspection_remediation_records.update_time IS '更新时间';

-- ================================
-- 14. 创建索引
-- ================================

-- 异常详情表索引
CREATE INDEX idx_exception_details_type ON t_data_inspection_exception_details(type);
CREATE INDEX idx_exception_details_severity ON t_data_inspection_exception_details(severity);
CREATE INDEX idx_exception_details_status ON t_data_inspection_exception_details(status);
CREATE INDEX idx_exception_details_unit ON t_data_inspection_exception_details(unit);
CREATE INDEX idx_exception_details_detect_time ON t_data_inspection_exception_details(detect_time);

-- 异常统计表索引
CREATE INDEX idx_exception_statistics_type_key ON t_data_inspection_exception_statistics(statistics_type, statistics_key);
CREATE INDEX idx_exception_statistics_date ON t_data_inspection_exception_statistics(statistics_date);

-- 规则相关索引
CREATE INDEX idx_rules_category ON t_data_inspection_rules(rule_category);
CREATE INDEX idx_rule_details_rule_id ON t_data_inspection_rule_details(rule_id);

-- 数据源相关索引
CREATE INDEX idx_data_sources_type ON t_data_inspection_data_sources(source_type);
CREATE INDEX idx_data_sources_status ON t_data_inspection_data_sources(status);
CREATE INDEX idx_sync_history_source_id ON t_data_inspection_sync_history(data_source_id);
CREATE INDEX idx_sync_history_time ON t_data_inspection_sync_history(start_time);

-- 定时任务相关索引
CREATE INDEX idx_scheduled_tasks_type ON t_data_inspection_scheduled_tasks(task_type);
CREATE INDEX idx_scheduled_tasks_status ON t_data_inspection_scheduled_tasks(status);
CREATE INDEX idx_scheduled_tasks_enabled ON t_data_inspection_scheduled_tasks(is_enabled);
CREATE INDEX idx_scheduled_tasks_next_time ON t_data_inspection_scheduled_tasks(next_execute_time);

-- 执行历史索引
CREATE INDEX idx_execution_history_task_id ON t_data_inspection_task_execution_history(task_id);
CREATE INDEX idx_execution_history_time ON t_data_inspection_task_execution_history(start_time);
CREATE INDEX idx_task_logs_execution_id ON t_data_inspection_task_logs(execution_id);
CREATE INDEX idx_task_logs_time ON t_data_inspection_task_logs(log_time);

-- 整改记录索引
CREATE INDEX idx_remediation_exception_id ON t_data_inspection_remediation_records(exception_id);
CREATE INDEX idx_remediation_status ON t_data_inspection_remediation_records(status);

-- ================================
-- 15. 初始化数据
-- ================================

-- 插入默认体检规则分类
INSERT INTO t_data_inspection_rules (rule_category, name, description) VALUES
('partyOrganization', '党组（党委）设置体检', '检查党组（党委）设置的完整性和规范性'),
('partyOfficials', '党务干部任免体检', '检查党务干部任免信息的完整性和合规性'),
('tasks', '任务体检', '检查任务执行情况和关键节点'),
('userInfo', '用户信息完整体检', '检查用户信息的完整性和准确性');

-- 插入默认规则详情（党组织设置体检）
INSERT INTO t_data_inspection_rule_details (rule_id, field_name, rule_type, rule_value, error_message) VALUES
(1, '党组织名称', 'required', 'true', '党组织名称不能为空'),
(1, '成立时间', 'required', 'true', '成立时间不能为空'),
(1, '党组织类型', 'enum', '["党委", "党总支", "党支部"]', '党组织类型必须是：党委、党总支、党支部之一'),
(1, '负责人', 'required', 'true', '党组织负责人不能为空');

-- 插入默认规则详情（党务干部任免体检）
INSERT INTO t_data_inspection_rule_details (rule_id, field_name, rule_type, rule_value, error_message) VALUES
(2, '干部姓名', 'required', 'true', '党务干部姓名不能为空'),
(2, '任职时间', 'required', 'true', '任职时间不能为空'),
(2, '职务', 'required', 'true', '职务信息不能为空'),
(2, '任免文件编号', 'pattern', '^[A-Z]{2,4}\\[\\d{4}\\]\\d{1,3}号$', '任免文件编号格式不正确');

-- 插入默认规则详情（任务体检）
INSERT INTO t_data_inspection_rule_details (rule_id, field_name, rule_type, rule_value, error_message) VALUES
(3, '任务名称', 'required', 'true', '任务名称不能为空'),
(3, '完成时间', 'required', 'true', '任务完成时间不能为空'),
(3, '完成状态', 'enum', '["已完成", "进行中", "未开始", "已逾期"]', '任务状态必须是规定的状态值之一'),
(3, '执行质量评分', 'range', '{"min": 0, "max": 100}', '执行质量评分必须在0-100之间');

-- 插入默认规则详情（用户信息完整体检）
INSERT INTO t_data_inspection_rule_details (rule_id, field_name, rule_type, rule_value, error_message) VALUES
(4, '用户姓名', 'required', 'true', '用户姓名不能为空'),
(4, '联系电话', 'pattern', '^1[3-9]\\d{9}$|^\\d{3,4}-\\d{7,8}$', '联系电话格式不正确'),
(4, '邮箱地址', 'pattern', '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$', '邮箱地址格式不正确'),
(4, '身份证号', 'pattern', '^\\d{17}[\\dXx]$', '身份证号格式不正确');

-- 插入示例数据源
INSERT INTO t_data_inspection_data_sources (source_id, source_name, source_type, description) VALUES
('ds001', '党组织管理系统', 'postgresql', '主要的党组织信息数据源'),
('ds002', '党员信息管理系统', 'mysql', '党员基础信息数据源'),
('ds003', '任务管理系统', 'postgresql', '任务执行和跟踪数据源');

-- 插入示例定时任务
INSERT INTO t_data_inspection_scheduled_tasks (task_name, task_type, cron_expression, cron_description, creator) VALUES
('每日数据完整性检查', 1, '0 2 * * *', '每天凌晨2点执行', 'system'),
('每周数据一致性检查', 2, '0 3 * * 1', '每周一凌晨3点执行', 'system'),
('月度数据安全检查', 4, '0 1 1 * *', '每月1号凌晨1点执行', 'system');

-- 插入示例自动体检配置
INSERT INTO t_data_inspection_auto_config (config_name, frequency, execute_time, check_types) VALUES
('日常数据体检', 'daily', '02:00:00', ARRAY[1,2,3]),
('周度全面体检', 'weekly', '01:00:00', ARRAY[1,2,3,4]);

-- 创建索引
CREATE INDEX idx_remediation_records_exception_id ON t_data_inspection_remediation_records(exception_id);
CREATE INDEX idx_remediation_records_status ON t_data_inspection_remediation_records(status);
CREATE INDEX idx_remediation_records_operator ON t_data_inspection_remediation_records(operator);
CREATE INDEX idx_remediation_records_start_time ON t_data_inspection_remediation_records(start_time);

-- 插入示例整改记录
INSERT INTO t_data_inspection_remediation_records (exception_id, exception_title, remediation_action, remediation_detail, operator, start_time, complete_time, status, verification_result, verification_time) VALUES
(3, '党员信息重复录入', '合并重复记录', '保留ID为12345的记录，删除重复记录ID为12346的记录', '李数据员', '2025-06-14 09:00:00', '2025-06-14 10:30:00', 'completed', 'passed', '2025-06-14 11:00:00'),
(1, '党组织基本信息缺失', '补充缺失信息', '联系市委办公室党支部，补充成立时间为2020年3月15日', '王数据员', '2025-06-15 10:00:00', NULL, 'in_progress', NULL, NULL);

COMMIT;