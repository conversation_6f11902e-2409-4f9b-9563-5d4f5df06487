

-- 删除已存在的表（如果需要重建）
-- DROP TABLE IF EXISTS t_honor;

-- 创建荣誉表彰表
CREATE TABLE t_honor (
    -- 主键ID
    id SERIAL NOT NULL,
    -- 基本信息
    applicant VARCHAR(100) NOT NULL,               -- 申报人
    department VARCHAR(200) NOT NULL,              -- 所属部门
    type INTEGER NOT NULL,                         -- 申报类型：1-优秀共产党员，2-优秀党务工作者，3-先进基层党组织，4-先进工作者，5-模范机关标兵单位
    material_url VARCHAR(500) NOT NULL,           -- 申报材料URL
    description TEXT,                              -- 申报说明
    
    -- 审核信息
    status INTEGER NOT NULL DEFAULT 1,            -- 状态：0-已删除，1-待审核，2-已通过，3-已驳回
    audit_opinion TEXT,                           -- 审核意见
    
    -- 系统字段
    organization_id BIGINT,                       -- 组织ID
    region_id BIGINT,                            -- 区域ID
    
    -- 审计字段
    create_user BIGINT,                          -- 创建人ID
    create_user_name VARCHAR(100),               -- 创建人姓名
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_user BIGINT,                          -- 更新人ID
    update_user_name VARCHAR(100),               -- 更新人姓名
    update_time TIMESTAMP,                       -- 更新时间
    
    -- 主键约束
    CONSTRAINT pk_honor PRIMARY KEY (id)
);

-- 创建索引
CREATE INDEX idx_honor_applicant ON t_honor(applicant);
CREATE INDEX idx_honor_department ON t_honor(department);
CREATE INDEX idx_honor_type ON t_honor(type);
CREATE INDEX idx_honor_status ON t_honor(status);
CREATE INDEX idx_honor_organization_id ON t_honor(organization_id);
CREATE INDEX idx_honor_region_id ON t_honor(region_id);
CREATE INDEX idx_honor_create_time ON t_honor(create_time);
CREATE INDEX idx_honor_create_user ON t_honor(create_user);

-- 添加表注释
COMMENT ON TABLE t_honor IS '荣誉表彰申报表';

-- 添加字段注释
COMMENT ON COLUMN t_honor.id IS '主键ID';
COMMENT ON COLUMN t_honor.applicant IS '申报人';
COMMENT ON COLUMN t_honor.department IS '所属部门';
COMMENT ON COLUMN t_honor.type IS '申报类型：1-优秀共产党员，2-优秀党务工作者，3-先进基层党组织，4-先进工作者，5-模范机关标兵单位';
COMMENT ON COLUMN t_honor.material_url IS '申报材料URL';
COMMENT ON COLUMN t_honor.description IS '申报说明';
COMMENT ON COLUMN t_honor.status IS '状态：0-已删除，1-待审核，2-已通过，3-已驳回';
COMMENT ON COLUMN t_honor.audit_opinion IS '审核意见';
COMMENT ON COLUMN t_honor.organization_id IS '组织ID';
COMMENT ON COLUMN t_honor.region_id IS '区域ID';
COMMENT ON COLUMN t_honor.create_user IS '创建人ID';
COMMENT ON COLUMN t_honor.create_user_name IS '创建人姓名';
COMMENT ON COLUMN t_honor.create_time IS '创建时间';
COMMENT ON COLUMN t_honor.update_user IS '更新人ID';
COMMENT ON COLUMN t_honor.update_user_name IS '更新人姓名';
COMMENT ON COLUMN t_honor.update_time IS '更新时间';

-- 插入测试数据（可选）
INSERT INTO t_honor (
    applicant, department, type, material_url, description, status, 
    organization_id, region_id, create_user, create_user_name, create_time
) VALUES 
(
    '张三', '党委办公室', 1, 'http://example.com/material1.pdf', '优秀共产党员申报材料', 1,
    14092, 3, 1001, '管理员', CURRENT_TIMESTAMP
),
(
    '李四', '组织部', 2, 'http://example.com/material2.pdf', '优秀党务工作者申报材料', 2,
    14092, 3, 1001, '管理员', CURRENT_TIMESTAMP
),
(
    '王五', '宣传部', 4, 'http://example.com/material3.pdf', '先进工作者申报材料', 3,
    14092, 3, 1001, '管理员', CURRENT_TIMESTAMP
);

-- 查询验证
SELECT 
    id, applicant, department, type, material_url, description, status, audit_opinion,
    organization_id, region_id, create_user, create_user_name, create_time,
    update_user, update_user_name, update_time
FROM t_honor 
WHERE status != 0 
ORDER BY create_time DESC;
