-- =====================================================
-- 荣誉成果表 PostgreSQL DDL
-- 基于 HonorAchievementEntity 实体类创建
-- 数据库: PostgreSQL
-- 作者: system
-- 创建时间: 2024-06-25
-- =====================================================

-- 删除表（如果存在）
DROP TABLE IF EXISTS t_honor_achievement CASCADE;

-- 删除序列（如果存在）
DROP SEQUENCE IF EXISTS seq_honor_achievement_id CASCADE;

-- 创建主键序列
CREATE SEQUENCE seq_honor_achievement_id
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

-- 创建荣誉成果表
CREATE TABLE t_honor_achievement (
    -- 主键ID
    id BIGINT NOT NULL DEFAULT nextval('seq_honor_achievement_id'),
    
    -- 基本信息
    name VARCHAR(200) NOT NULL,                    -- 荣誉名称
    type INTEGER NOT NULL,                         -- 类型：1-奖项，2-证书，3-专利，4-论文，5-其他
    level INTEGER NOT NULL,                        -- 级别：1-国际级，2-国家级，3-省级，4-市级，5-区县级，6-单位内部
    issue_org VARCHAR(200),                        -- 颁发单位
    achieve_time TIMESTAMP,                        -- 获得时间
    
    -- 展示信息
    cover_url VARCHAR(500),                        -- 封面图片URL
    summary TEXT,                                  -- 摘要
    description TEXT,                              -- 详细描述（富文本）
    attachments_json TEXT,                         -- 附件JSON
    
    -- 统计信息
    download_count INTEGER NOT NULL DEFAULT 0,     -- 下载次数
    view_count INTEGER NOT NULL DEFAULT 0,         -- 查看次数
    
    -- 权限控制
    is_public BOOLEAN NOT NULL DEFAULT true,       -- 是否公开：false-不公开，true-公开
    allow_download BOOLEAN NOT NULL DEFAULT true,  -- 是否允许下载：false-不允许，true-允许
    
    -- 状态管理
    status INTEGER NOT NULL DEFAULT 1,             -- 状态：0-删除，1-正常，2-草稿
    
    -- 组织信息
    organization_id BIGINT,                        -- 组织ID
    region_id BIGINT,                              -- 区域ID
    
    -- 审计字段
    create_user BIGINT,                            -- 创建人ID
    create_user_name VARCHAR(100),                 -- 创建人姓名
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_user BIGINT,                            -- 更新人ID
    update_user_name VARCHAR(100),                 -- 更新人姓名
    update_time TIMESTAMP,                         -- 更新时间
    
    -- 主键约束
    CONSTRAINT pk_honor_achievement PRIMARY KEY (id),
    
    -- 检查约束
    CONSTRAINT chk_honor_achievement_type CHECK (type IN (1, 2, 3, 4, 5)),
    CONSTRAINT chk_honor_achievement_level CHECK (level IN (1, 2, 3, 4, 5, 6)),
    CONSTRAINT chk_honor_achievement_status CHECK (status IN (0, 1, 2)),
    CONSTRAINT chk_honor_achievement_download_count CHECK (download_count >= 0),
    CONSTRAINT chk_honor_achievement_view_count CHECK (view_count >= 0),
    CONSTRAINT chk_honor_achievement_name_length CHECK (char_length(name) >= 1)
);

-- 添加表注释
COMMENT ON TABLE t_honor_achievement IS '荣誉成果表 - 存储组织和个人的各类荣誉成果信息';

-- 添加字段注释
COMMENT ON COLUMN t_honor_achievement.id IS '主键ID，自增长';
COMMENT ON COLUMN t_honor_achievement.name IS '荣誉名称，必填，最大200字符';
COMMENT ON COLUMN t_honor_achievement.type IS '荣誉类型：1-奖项，2-证书，3-专利，4-论文，5-其他';
COMMENT ON COLUMN t_honor_achievement.level IS '荣誉级别：1-国际级，2-国家级，3-省级，4-市级，5-区县级，6-单位内部';
COMMENT ON COLUMN t_honor_achievement.issue_org IS '颁发单位名称';
COMMENT ON COLUMN t_honor_achievement.achieve_time IS '获得荣誉的时间';
COMMENT ON COLUMN t_honor_achievement.cover_url IS '封面图片的URL地址';
COMMENT ON COLUMN t_honor_achievement.summary IS '荣誉成果摘要描述';
COMMENT ON COLUMN t_honor_achievement.description IS '荣誉成果详细描述，支持富文本';
COMMENT ON COLUMN t_honor_achievement.attachments_json IS '附件信息的JSON格式存储';
COMMENT ON COLUMN t_honor_achievement.download_count IS '下载次数统计，默认0';
COMMENT ON COLUMN t_honor_achievement.view_count IS '查看次数统计，默认0';
COMMENT ON COLUMN t_honor_achievement.is_public IS '是否公开展示，默认true';
COMMENT ON COLUMN t_honor_achievement.allow_download IS '是否允许下载，默认true';
COMMENT ON COLUMN t_honor_achievement.status IS '记录状态：0-已删除，1-正常，2-草稿，默认1';
COMMENT ON COLUMN t_honor_achievement.organization_id IS '所属组织ID';
COMMENT ON COLUMN t_honor_achievement.region_id IS '所属区域ID';
COMMENT ON COLUMN t_honor_achievement.create_user IS '创建人用户ID';
COMMENT ON COLUMN t_honor_achievement.create_user_name IS '创建人姓名';
COMMENT ON COLUMN t_honor_achievement.create_time IS '创建时间，默认当前时间';
COMMENT ON COLUMN t_honor_achievement.update_user IS '最后更新人用户ID';
COMMENT ON COLUMN t_honor_achievement.update_user_name IS '最后更新人姓名';
COMMENT ON COLUMN t_honor_achievement.update_time IS '最后更新时间';

-- =====================================================
-- 索引创建 - 基于业务查询需求优化
-- =====================================================

-- 1. 组织查询索引（最高频查询）
CREATE INDEX idx_honor_achievement_org_status ON t_honor_achievement (organization_id, status);

-- 2. 分页查询复合索引（支持多条件筛选）
CREATE INDEX idx_honor_achievement_query ON t_honor_achievement (organization_id, status, type, level, create_time DESC);

-- 3. 公开展示查询索引
CREATE INDEX idx_honor_achievement_public ON t_honor_achievement (status, is_public, organization_id, create_time DESC);

-- 4. 统计查询索引
CREATE INDEX idx_honor_achievement_stats_type ON t_honor_achievement (organization_id, status, type);
CREATE INDEX idx_honor_achievement_stats_level ON t_honor_achievement (organization_id, status, level);

-- 5. 热门内容查询索引
CREATE INDEX idx_honor_achievement_popular ON t_honor_achievement (status, is_public, organization_id, view_count DESC, create_time DESC);

-- 6. 时间范围查询索引
CREATE INDEX idx_honor_achievement_time_range ON t_honor_achievement (organization_id, status, achieve_time);

-- 7. 名称模糊查询索引（支持中文全文搜索）
CREATE INDEX idx_honor_achievement_name_gin ON t_honor_achievement USING gin(to_tsvector('simple', name));

-- 8. 颁发单位查询索引
CREATE INDEX idx_honor_achievement_issue_org ON t_honor_achievement (organization_id, status, issue_org);

-- 9. 区域查询索引
CREATE INDEX idx_honor_achievement_region ON t_honor_achievement (region_id, status, create_time DESC);

-- 10. 创建人查询索引
CREATE INDEX idx_honor_achievement_creator ON t_honor_achievement (create_user, status, create_time DESC);

-- =====================================================
-- 触发器 - 自动更新时间戳
-- =====================================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_honor_achievement_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trg_honor_achievement_update_time
    BEFORE UPDATE ON t_honor_achievement
    FOR EACH ROW
    EXECUTE FUNCTION update_honor_achievement_timestamp();

-- =====================================================
-- 权限设置（可选）
-- =====================================================

-- 如果有特定的数据库用户，可以设置权限
-- GRANT SELECT, INSERT, UPDATE, DELETE ON t_honor_achievement TO your_app_user;
-- GRANT USAGE, SELECT ON seq_honor_achievement_id TO your_app_user;

-- =====================================================
-- 验证脚本
-- =====================================================

-- 验证表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length
FROM information_schema.columns 
WHERE table_name = 't_honor_achievement' 
ORDER BY ordinal_position;

-- 验证索引
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 't_honor_achievement';

-- 验证约束
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 't_honor_achievement';

-- =====================================================
-- 示例数据插入（可选）
-- =====================================================

-- 插入示例数据用于测试
INSERT INTO t_honor_achievement (
    name, type, level, issue_org, achieve_time, summary, description,
    download_count, view_count, is_public, allow_download, status,
    organization_id, region_id, create_user, create_user_name
) VALUES 
(
    '优秀员工奖', 1, 6, '公司人力资源部', '2024-01-15 10:00:00',
    '年度优秀员工表彰', '在2023年度工作中表现突出，获得优秀员工称号',
    0, 0, true, true, 1,
    1001, 2001, 1, '系统管理员'
),
(
    '技术创新专利', 3, 2, '国家知识产权局', '2024-02-20 14:30:00',
    '人工智能算法优化专利', '针对机器学习算法的创新性改进，获得国家发明专利',
    5, 25, true, true, 1,
    1001, 2001, 2, '张三'
),
(
    '学术论文发表', 4, 3, '省级学术期刊', '2024-03-10 09:15:00',
    '关于数据挖掘的研究论文', '在省级核心期刊发表的关于大数据挖掘技术的学术论文',
    12, 48, true, true, 1,
    1001, 2001, 3, '李四'
);

-- 验证数据插入
SELECT COUNT(*) as total_records FROM t_honor_achievement WHERE status = 1;

-- =====================================================
-- 完成提示
-- =====================================================

-- 表创建完成！
-- 1. 表结构已按照 HonorAchievementEntity 实体类创建
-- 2. 已添加必要的约束和索引
-- 3. 已设置自动更新时间戳触发器
-- 4. 已插入示例数据用于测试
-- 5. 可以开始使用 HonorAchievementMapper 进行数据操作
