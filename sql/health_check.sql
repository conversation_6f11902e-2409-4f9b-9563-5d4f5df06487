-- 健康检查模块数据库表脚本 (PostgreSQL 10)

-- 体检记录表
create sequence t_health_check_record_id_seq;
CREATE TABLE t_health_check_record (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_record_id_seq'::regclass),
    check_name VARCHAR(255) NOT NULL,
    check_type INTEGER NOT NULL,
    target_object VARCHAR(255) NOT NULL,
    status INTEGER NOT NULL DEFAULT 1,
    description TEXT,
    check_result TEXT,
    exception_count INTEGER DEFAULT 0,
    last_check_time TIMESTAMP,
    operator VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_record IS '体检记录表';
COMMENT ON COLUMN t_health_check_record.id IS '主键ID';
COMMENT ON COLUMN t_health_check_record.check_name IS '体检名称';
COMMENT ON COLUMN t_health_check_record.check_type IS '体检类型: 1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整';
COMMENT ON COLUMN t_health_check_record.target_object IS '检查对象';
COMMENT ON COLUMN t_health_check_record.status IS '状态: 1-待执行, 2-执行中, 3-已完成, 4-执行失败';
COMMENT ON COLUMN t_health_check_record.description IS '描述';
COMMENT ON COLUMN t_health_check_record.check_result IS '检查结果';
COMMENT ON COLUMN t_health_check_record.exception_count IS '异常数量';
COMMENT ON COLUMN t_health_check_record.last_check_time IS '最后检查时间';
COMMENT ON COLUMN t_health_check_record.operator IS '操作人';
COMMENT ON COLUMN t_health_check_record.create_time IS '创建时间';
COMMENT ON COLUMN t_health_check_record.update_time IS '更新时间';

-- 体检规则配置表
create sequence t_health_check_rule_id_seq;
CREATE TABLE t_health_check_rule (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_rule_id_seq'::regclass),
    rule_name VARCHAR(255) NOT NULL,
    rule_type INTEGER NOT NULL,
    rule_content TEXT NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER NOT NULL DEFAULT 0,
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_rule IS '体检规则配置表';
COMMENT ON COLUMN t_health_check_rule.id IS '主键ID';
COMMENT ON COLUMN t_health_check_rule.rule_name IS '规则名称';
COMMENT ON COLUMN t_health_check_rule.rule_type IS '规则类型: 1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整';
COMMENT ON COLUMN t_health_check_rule.rule_content IS '规则内容JSON';
COMMENT ON COLUMN t_health_check_rule.is_enabled IS '是否启用';
COMMENT ON COLUMN t_health_check_rule.priority IS '优先级';
COMMENT ON COLUMN t_health_check_rule.description IS '描述';
COMMENT ON COLUMN t_health_check_rule.create_time IS '创建时间';
COMMENT ON COLUMN t_health_check_rule.update_time IS '更新时间';

-- 体检执行任务表
create sequence t_health_check_task_id_seq;
CREATE TABLE t_health_check_task (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_task_id_seq'::regclass),
    task_name VARCHAR(255) NOT NULL,
    task_type INTEGER NOT NULL,
    schedule_type INTEGER NOT NULL,
    cron_expression VARCHAR(100),
    status INTEGER NOT NULL DEFAULT 1,
    progress INTEGER DEFAULT 0,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    result TEXT,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_task IS '体检执行任务表';
COMMENT ON COLUMN t_health_check_task.id IS '主键ID';
COMMENT ON COLUMN t_health_check_task.task_name IS '任务名称';
COMMENT ON COLUMN t_health_check_task.task_type IS '任务类型: 1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整';
COMMENT ON COLUMN t_health_check_task.schedule_type IS '调度类型: 1-手动执行, 2-定时执行';
COMMENT ON COLUMN t_health_check_task.cron_expression IS 'Cron表达式';
COMMENT ON COLUMN t_health_check_task.status IS '状态: 1-待执行, 2-执行中, 3-已完成, 4-已暂停, 5-执行失败';
COMMENT ON COLUMN t_health_check_task.progress IS '执行进度 0-100';
COMMENT ON COLUMN t_health_check_task.start_time IS '开始时间';
COMMENT ON COLUMN t_health_check_task.end_time IS '结束时间';
COMMENT ON COLUMN t_health_check_task.result IS '执行结果';
COMMENT ON COLUMN t_health_check_task.error_message IS '错误信息';
COMMENT ON COLUMN t_health_check_task.create_time IS '创建时间';
COMMENT ON COLUMN t_health_check_task.update_time IS '更新时间';

-- 体检结果异常项表
create sequence t_health_check_exception_id_seq;
CREATE TABLE t_health_check_exception (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_exception_id_seq'::regclass),
    check_task_id BIGINT NOT NULL,
    exception_type INTEGER NOT NULL,
    exception_level INTEGER NOT NULL,
    exception_title VARCHAR(255) NOT NULL,
    exception_description TEXT NOT NULL,
    affected_object VARCHAR(255),
    solution TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    fix_time TIMESTAMP,
    fix_operator VARCHAR(100),
    fix_result TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_exception IS '体检结果异常项表';
COMMENT ON COLUMN t_health_check_exception.id IS '主键ID';
COMMENT ON COLUMN t_health_check_exception.check_task_id IS '检查任务ID';
COMMENT ON COLUMN t_health_check_exception.exception_type IS '异常类型: 1-完整性异常, 2-准确性异常, 3-一致性异常, 4-安全性异常';
COMMENT ON COLUMN t_health_check_exception.exception_level IS '异常级别: 1-低, 2-中, 3-高';
COMMENT ON COLUMN t_health_check_exception.exception_title IS '异常标题';
COMMENT ON COLUMN t_health_check_exception.exception_description IS '异常描述';
COMMENT ON COLUMN t_health_check_exception.affected_object IS '影响对象';
COMMENT ON COLUMN t_health_check_exception.solution IS '解决方案';
COMMENT ON COLUMN t_health_check_exception.status IS '状态: 1-待处理, 2-处理中, 3-已修复, 4-已忽略';
COMMENT ON COLUMN t_health_check_exception.fix_time IS '整改时间';
COMMENT ON COLUMN t_health_check_exception.fix_operator IS '整改人';
COMMENT ON COLUMN t_health_check_exception.fix_result IS '整改结果';
COMMENT ON COLUMN t_health_check_exception.create_time IS '创建时间';
COMMENT ON COLUMN t_health_check_exception.update_time IS '更新时间';

-- 统计信息表
create sequence t_health_check_statistics_id_seq;
CREATE TABLE t_health_check_statistics (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_statistics_id_seq'::regclass),
    total_checks INTEGER DEFAULT 0,
    completed_checks INTEGER DEFAULT 0,
    failed_checks INTEGER DEFAULT 0,
    total_exceptions INTEGER DEFAULT 0,
    high_level_exceptions INTEGER DEFAULT 0,
    fixed_exceptions INTEGER DEFAULT 0,
    stat_date DATE NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_date)
);

COMMENT ON TABLE t_health_check_statistics IS '体检统计信息表';
COMMENT ON COLUMN t_health_check_statistics.id IS '主键ID';
COMMENT ON COLUMN t_health_check_statistics.total_checks IS '总体检次数';
COMMENT ON COLUMN t_health_check_statistics.completed_checks IS '已完成次数';
COMMENT ON COLUMN t_health_check_statistics.failed_checks IS '失败次数';
COMMENT ON COLUMN t_health_check_statistics.total_exceptions IS '异常总数';
COMMENT ON COLUMN t_health_check_statistics.high_level_exceptions IS '高级别异常数';
COMMENT ON COLUMN t_health_check_statistics.fixed_exceptions IS '已修复异常数';
COMMENT ON COLUMN t_health_check_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN t_health_check_statistics.create_time IS '创建时间';
COMMENT ON COLUMN t_health_check_statistics.update_time IS '更新时间';

-- 异常统计详情表
create sequence t_health_check_exception_stat_id_seq;
CREATE TABLE t_health_check_exception_stat (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_exception_stat_id_seq'::regclass),
    stat_id BIGINT NOT NULL,
    check_type INTEGER NOT NULL,
    count INTEGER DEFAULT 0,
    exception_count INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_exception_stat IS '异常统计详情表';
COMMENT ON COLUMN t_health_check_exception_stat.id IS '主键ID';
COMMENT ON COLUMN t_health_check_exception_stat.stat_id IS '统计ID';
COMMENT ON COLUMN t_health_check_exception_stat.check_type IS '体检类型';
COMMENT ON COLUMN t_health_check_exception_stat.count IS '检查次数';
COMMENT ON COLUMN t_health_check_exception_stat.exception_count IS '异常数量';
COMMENT ON COLUMN t_health_check_exception_stat.create_time IS '创建时间';

-- 异常趋势表
create sequence t_health_check_exception_trend_id_seq;
CREATE TABLE t_health_check_exception_trend (
    "id" int8 NOT NULL DEFAULT nextval('t_health_check_exception_trend_id_seq'::regclass),
    stat_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    count INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_health_check_exception_trend IS '异常趋势表';
COMMENT ON COLUMN t_health_check_exception_trend.id IS '主键ID';
COMMENT ON COLUMN t_health_check_exception_trend.stat_id IS '统计ID';
COMMENT ON COLUMN t_health_check_exception_trend.stat_date IS '统计日期';
COMMENT ON COLUMN t_health_check_exception_trend.count IS '异常数量';
COMMENT ON COLUMN t_health_check_exception_trend.create_time IS '创建时间';