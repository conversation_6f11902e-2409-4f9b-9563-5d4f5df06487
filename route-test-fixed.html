<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由跳转修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        
        .routes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .route-card {
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .route-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .route-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.3em;
        }
        
        .route-card p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.5;
        }
        
        .route-link {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .route-link:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .status h3 {
            margin: 0 0 15px 0;
            color: #2d5a2d;
        }
        
        .status ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .status li {
            margin-bottom: 8px;
            color: #2d5a2d;
        }
        
        .fix-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .fix-info h3 {
            margin: 0 0 15px 0;
            color: #856404;
        }
        
        .fix-info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .fix-info li {
            margin-bottom: 8px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 路由跳转修复测试</h1>
        <p class="subtitle">测试消息中心和选育树推项目看板的路由跳转功能</p>
        
        <div class="fix-info">
            <h3>🛠️ 已修复的问题</h3>
            <ul>
                <li>✅ 删除了重复的消息中心路由定义</li>
                <li>✅ 删除了重复的选育树推项目看板路由定义</li>
                <li>✅ 添加了缺失的模板管理路由配置</li>
                <li>✅ 创建了TemplateManage.vue页面组件</li>
                <li>✅ 修复了项目看板中getAverageCompletionRate函数调用问题</li>
            </ul>
        </div>
        
        <div class="routes-grid">
            <div class="route-card">
                <h3>📧 消息中心首页</h3>
                <p>消息中心主页面，包含快速操作和统计概览</p>
                <a href="http://localhost:5174/#/message-center" class="route-link" target="_blank">消息中心首页</a>
            </div>
            
            <div class="route-card">
                <h3>📋 消息列表</h3>
                <p>查看和管理所有消息</p>
                <a href="http://localhost:5174/#/message-center/list" class="route-link" target="_blank">消息列表</a>
            </div>
            
            <div class="route-card">
                <h3>🚀 推送管理</h3>
                <p>配置推送规则和策略</p>
                <a href="http://localhost:5174/#/message-center/push" class="route-link" target="_blank">推送管理</a>
            </div>
            
            <div class="route-card">
                <h3>📝 模板管理</h3>
                <p>管理消息模板库 (新增)</p>
                <a href="http://localhost:5174/#/message-center/template" class="route-link" target="_blank">模板管理</a>
            </div>
            
            <div class="route-card">
                <h3>📊 消息分析</h3>
                <p>消息统计和分析报告</p>
                <a href="http://localhost:5174/#/message-center/analytics" class="route-link" target="_blank">消息分析</a>
            </div>
            
            <div class="route-card">
                <h3>🌳 选育树推项目看板</h3>
                <p>项目管理看板和数据展示</p>
                <a href="http://localhost:5174/#/project-dashboard" class="route-link" target="_blank">项目看板</a>
            </div>
        </div>
        
        <div class="status">
            <h3>✅ 修复状态</h3>
            <ul>
                <li>✅ 路由重复定义问题已修复</li>
                <li>✅ 模板管理页面已创建</li>
                <li>✅ 项目看板函数调用问题已修复</li>
                <li>✅ 开发服务器已启动 (http://localhost:5174/)</li>
            </ul>
            <p><strong>测试说明：</strong>点击上方链接测试各个路由是否能正常跳转，页面是否能正确显示。</p>
        </div>
    </div>
</body>
</html>
