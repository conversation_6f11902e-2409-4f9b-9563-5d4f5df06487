import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  base: '/pc/verify',
	plugins: [
		vue(),
		Components({
			resolvers: [
				AntDesignVueResolver({
					importStyle: false, // 禁用自动样式导入，避免路径问题
					exclude: ['TimePicker'], // 排除 TimePicker 组件的自动导入
				}),
			],
		}),
	],
	resolve: {
		//文件系统路径的别名, 绝对路径
		alias: {
			'@': '/src',
			store: '@/store',
			config: '@/config',
			components: '@/components',
			assets: '@/assets',
			utils: '@/utils',
			api: '@/api',
		},
	},
	server: {
		host: '0.0.0.0',
		proxy: {
			"/api": {
				target: "http://localhost:8567",
				changeOrigin: true,
				cookieDomainRewrite: "",
				secure: false,
			},
			"/file": {
				target: "http://localhost:8567",
				changeOrigin: true,
				cookieDomainRewrite: "",
				secure: false,
			},
		},
	},
	css: {
		preprocessorOptions: {
			less: {
				javascriptEnabled: true,
			},
		},
	},
})
