/**
 * 数据体检模块接口校验测试脚本
 * 用于验证后端接口与 mock 数据的一致性
 */
const axios = require('axios');
const fs = require('fs');

// 配置
const config = {
  mockApiUrl: 'http://localhost:3002/api/data-inspection',  // Mock API 服务器地址
  backendUrl: 'http://localhost:8080/api',  // 后端服务器地址（暂时不可用）
  outputFile: 'interface-validation-report.json',
  testMockOnly: true  // 当后端不可用时，只测试Mock API
};

// 测试用例定义
const testCases = [
  {
    name: '获取用户权限',
    method: 'GET',
    path: '/permissions/1',
    expectedFields: ['userId', 'canViewInspection', 'canConfigureRules', 'canHandleExceptions']
  },
  {
    name: '获取异常概览统计',
    method: 'GET', 
    path: '/exceptions/overview',
    expectedFields: ['totalExceptions', 'highSeverityCount', 'mediumSeverityCount', 'lowSeverityCount', 'lastInspectionTime', 'nextInspectionTime']
  },
  {
    name: '按异常类型统计',
    method: 'GET',
    path: '/exceptions/statistics/by-type',
    expectedStructure: 'array',
    expectedFields: ['type', 'count', 'severity', 'description']
  },
  {
    name: '按单位统计',
    method: 'GET',
    path: '/exceptions/statistics/by-unit', 
    expectedStructure: 'array',
    expectedFields: ['unit', 'totalExceptions', 'highSeverity', 'mediumSeverity', 'lowSeverity']
  },
  {
    name: '获取异常详情列表',
    method: 'GET',
    path: '/exceptions/details?page=1&pageSize=10',
    expectedFields: ['list', 'total', 'page', 'pageSize'],
    dataStructure: {
      list: {
        type: 'array',
        fields: ['id', 'type', 'title', 'description', 'unit', 'source', 'severity', 'status']
      }
    }
  },
  {
    name: '获取党组织规则',
    method: 'GET',
    path: '/rules/party-organization',
    expectedFields: ['id', 'name', 'description', 'rules', 'status', 'lastUpdate'],
    dataStructure: {
      rules: {
        type: 'array',
        fields: ['id', 'fieldName', 'ruleType', 'ruleValue', 'errorMessage']
      }
    }
  },
  {
    name: '获取所有规则',
    method: 'GET',
    path: '/rules',
    expectedFields: ['partyOrganization', 'partyOfficials', 'tasks', 'userInfo']
  },
  {
    name: '获取自动体检配置',
    method: 'GET',
    path: '/auto-inspection/config',
    expectedFields: ['id', 'name', 'enabled', 'schedule', 'inspectionScope', 'checkTypes', 'status']
  },
  {
    name: '获取数据源列表',
    method: 'GET',
    path: '/data-sources',
    expectedStructure: 'array',
    expectedFields: ['id', 'sourceName', 'sourceType', 'status', 'lastSyncTime']
  },
  {
    name: '获取定时任务列表',
    method: 'GET',
    path: '/scheduled-tasks',
    expectedStructure: 'array', 
    expectedFields: ['id', 'taskName', 'taskType', 'isEnabled', 'status', 'lastExecuteTime']
  },
  {
    name: '获取整改权限',
    method: 'GET',
    path: '/remediation/permissions/1',
    expectedFields: ['canHandleExceptions']
  }
];

// 校验结果存储
let validationResults = {
  timestamp: new Date().toISOString(),
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    errors: 0
  },
  details: []
};

/**
 * 发送HTTP请求
 */
async function makeRequest(baseUrl, method, path, headers = {}) {
  try {
    const url = `${baseUrl}${path}`;
    const config = {
      method: method.toLowerCase(),
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: 10000
    };
    
    const response = await axios(config);
    return {
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      data: error.response?.data
    };
  }
}

/**
 * 校验数据结构
 */
function validateDataStructure(data, expectedFields, expectedStructure = 'object') {
  const issues = [];
  
  if (expectedStructure === 'array' && !Array.isArray(data)) {
    issues.push(`Expected array but got ${typeof data}`);
    return issues;
  }
  
  if (expectedStructure === 'object' && (typeof data !== 'object' || Array.isArray(data))) {
    issues.push(`Expected object but got ${Array.isArray(data) ? 'array' : typeof data}`);
    return issues;
  }
  
  // 检查必要字段
  if (expectedStructure === 'array' && data.length > 0) {
    const firstItem = data[0];
    expectedFields.forEach(field => {
      if (!(field in firstItem)) {
        issues.push(`Missing field '${field}' in array items`);
      }
    });
  } else if (expectedStructure === 'object') {
    expectedFields.forEach(field => {
      if (!(field in data)) {
        issues.push(`Missing field '${field}' in response`);
      }
    });
  }
  
  return issues;
}

/**
 * 比较 Mock 和后端响应
 */
function compareResponses(mockResponse, backendResponse, testCase) {
  const comparison = {
    structureMatch: true,
    fieldMatch: true,
    dataTypeMatch: true,
    issues: []
  };
  
  if (!mockResponse.success || !backendResponse.success) {
    if (mockResponse.success !== backendResponse.success) {
      comparison.issues.push('Response success status mismatch');
    }
    return comparison;
  }
  
  const mockData = mockResponse.data;
  const backendData = backendResponse.data;
  
  // 检查数据结构
  const mockIssues = validateDataStructure(
    mockData, 
    testCase.expectedFields, 
    testCase.expectedStructure
  );
  const backendIssues = validateDataStructure(
    backendData, 
    testCase.expectedFields, 
    testCase.expectedStructure
  );
  
  if (mockIssues.length > 0) {
    comparison.issues.push(`Mock API issues: ${mockIssues.join(', ')}`);
  }
  
  if (backendIssues.length > 0) {
    comparison.issues.push(`Backend API issues: ${backendIssues.join(', ')}`);
    comparison.structureMatch = false;
  }
  
  // 比较字段类型
  if (testCase.expectedFields) {
    const mockSample = testCase.expectedStructure === 'array' && mockData.length > 0 ? mockData[0] : mockData;
    const backendSample = testCase.expectedStructure === 'array' && backendData.length > 0 ? backendData[0] : backendData;
    
    testCase.expectedFields.forEach(field => {
      const mockType = typeof mockSample[field];
      const backendType = typeof backendSample[field];
      
      if (mockType !== backendType && mockSample[field] != null && backendSample[field] != null) {
        comparison.issues.push(`Field '${field}' type mismatch: mock(${mockType}) vs backend(${backendType})`);
        comparison.dataTypeMatch = false;
      }
    });
  }
  
  return comparison;
}

/**
 * 运行单个测试用例
 */
async function runTestCase(testCase) {
  console.log(`Testing: ${testCase.name}`);
  
  const result = {
    name: testCase.name,
    method: testCase.method,
    path: testCase.path,
    passed: false,
    mockResponse: null,
    backendResponse: null,
    comparison: null,
    errors: []
  };
  
  try {
    // 请求 Mock API
    const mockResponse = await makeRequest(config.mockApiUrl, testCase.method, testCase.path);
    result.mockResponse = mockResponse;
    
    if (config.testMockOnly) {
      // 只测试Mock API，验证数据结构
      if (mockResponse.success) {
        // Mock API返回格式为 {success: true, data: actualData}
        const actualData = mockResponse.data?.data || mockResponse.data;
        const mockIssues = validateDataStructure(
          actualData, 
          testCase.expectedFields, 
          testCase.expectedStructure
        );
        
        if (mockIssues.length === 0) {
          result.passed = true;
        } else {
          result.errors.push(`Mock API数据结构问题: ${mockIssues.join(', ')}`);
        }
      } else {
        result.errors.push(`Mock API请求失败: ${mockResponse.error}`);
      }
    } else {
      // 请求后端 API
      const backendResponse = await makeRequest(config.backendUrl, testCase.method, testCase.path, {
        'X-User-Id': '1',
        'X-Org-Id': '1'
      });
      result.backendResponse = backendResponse;
      
      // 比较响应
      if (mockResponse.success && backendResponse.success) {
        result.comparison = compareResponses(mockResponse, backendResponse, testCase);
        result.passed = result.comparison.issues.length === 0;
      } else {
        result.errors.push('One or both API calls failed');
        if (!mockResponse.success) {
          result.errors.push(`Mock API: ${mockResponse.error}`);
        }
        if (!backendResponse.success) {
          result.errors.push(`Backend API: ${backendResponse.error}`);
        }
      }
    }
    
  } catch (error) {
    result.errors.push(`Test execution error: ${error.message}`);
  }
  
  return result;
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('开始接口校验测试...\n');
  console.log(`Mock API: ${config.mockApiUrl}`);
  console.log(`Backend API: ${config.backendUrl}\n`);
  
  validationResults.summary.total = testCases.length;
  
  for (const testCase of testCases) {
    const result = await runTestCase(testCase);
    validationResults.details.push(result);
    
    if (result.passed) {
      validationResults.summary.passed++;
      console.log(`✓ ${testCase.name}`);
    } else {
      validationResults.summary.failed++;
      console.log(`✗ ${testCase.name}`);
      if (result.errors.length > 0) {
        result.errors.forEach(error => console.log(`  Error: ${error}`));
        validationResults.summary.errors++;
      }
      if (result.comparison && result.comparison.issues.length > 0) {
        result.comparison.issues.forEach(issue => console.log(`  Issue: ${issue}`));
      }
    }
    console.log('');
  }
  
  // 保存详细报告
  fs.writeFileSync(config.outputFile, JSON.stringify(validationResults, null, 2));
  
  // 输出总结
  console.log('=== 校验结果总结 ===');
  console.log(`总计: ${validationResults.summary.total}`);
  console.log(`通过: ${validationResults.summary.passed}`);
  console.log(`失败: ${validationResults.summary.failed}`);
  console.log(`错误: ${validationResults.summary.errors}`);
  console.log(`\n详细报告已保存至: ${config.outputFile}`);
}

/**
 * 生成简单的 HTML 报告
 */
function generateHtmlReport() {
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>接口校验报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        .test-case { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
        .passed { border-left: 4px solid #4CAF50; }
        .failed { border-left: 4px solid #f44336; }
        .issues { background: #fff3cd; padding: 10px; margin-top: 10px; }
        .errors { background: #f8d7da; padding: 10px; margin-top: 10px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>数据体检模块接口校验报告</h1>
    
    <div class="summary">
        <h2>测试总结</h2>
        <p><strong>测试时间:</strong> ${validationResults.timestamp}</p>
        <p><strong>总计:</strong> ${validationResults.summary.total}</p>
        <p><strong>通过:</strong> ${validationResults.summary.passed}</p>
        <p><strong>失败:</strong> ${validationResults.summary.failed}</p>
        <p><strong>错误:</strong> ${validationResults.summary.errors}</p>
    </div>
    
    <h2>详细结果</h2>
    ${validationResults.details.map(detail => `
        <div class="test-case ${detail.passed ? 'passed' : 'failed'}">
            <h3>${detail.name} ${detail.passed ? '✓' : '✗'}</h3>
            <p><strong>方法:</strong> ${detail.method} <strong>路径:</strong> ${detail.path}</p>
            
            ${detail.errors.length > 0 ? `
                <div class="errors">
                    <strong>错误:</strong>
                    <ul>${detail.errors.map(error => `<li>${error}</li>`).join('')}</ul>
                </div>
            ` : ''}
            
            ${detail.comparison && detail.comparison.issues.length > 0 ? `
                <div class="issues">
                    <strong>问题:</strong>
                    <ul>${detail.comparison.issues.map(issue => `<li>${issue}</li>`).join('')}</ul>
                </div>
            ` : ''}
        </div>
    `).join('')}
    
</body>
</html>`;

  fs.writeFileSync('interface-validation-report.html', htmlContent);
  console.log('HTML 报告已生成: interface-validation-report.html');
}

// 主函数
async function main() {
  try {
    await runAllTests();
    generateHtmlReport();
  } catch (error) {
    console.error('测试执行失败:', error);
  }
}

if (require.main === module) {
  main();
}

module.exports = { runAllTests, compareResponses, validateDataStructure };