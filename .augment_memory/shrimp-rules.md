# 案例收集审核系统开发标准

## 项目概览

- **项目性质**: Vue 3 + TypeScript + Ant Design Vue 的案例收集审核系统
- **核心模块**: 活动管理、案例提交、审核管理、分类管理、数据统计
- **技术特点**: Mock数据系统、完整类型定义、响应式设计、现代化UI

## 核心架构规则

### 关键目录结构
- `src/views/case-collection/` - 所有功能页面
- `src/api/case-collection-service.ts` - API服务层
- `src/api/case-collection-mock.ts` - Mock数据实现
- `src/types/case-collection.ts` - TypeScript类型定义
- `src/router/index.ts` - 路由配置
- `.env` - 环境配置

### 模块划分
- `activities/` - 活动管理模块
- `submit/` - 案例提交模块
- `submissions/` - 提交管理模块
- `review/` - 审核管理模块
- `categories/` - 分类管理模块
- `dashboard/` - 数据统计模块

## API开发标准

### 强制三层架构
**新增任何API时必须同时修改以下三个文件**：

1. **类型定义** (`src/types/case-collection.ts`)
   ```typescript
   export interface NewFeatureType {
     id: number;
     name: string;
     // 其他字段
   }
   ```

2. **Mock实现** (`src/api/case-collection-mock.ts`)
   ```typescript
   export async function mockGetNewFeature(): Promise<ApiResponse<NewFeatureType[]>> {
     await mockDelay();
     return wrapApiResponse(mockData);
   }
   ```

3. **服务层** (`src/api/case-collection-service.ts`)
   ```typescript
   async getNewFeature(): Promise<ApiResponse<NewFeatureType[]>> {
     if (useMockData) {
       return mockApi.mockGetNewFeature();
     }
     const response = await realApi.getNewFeature();
     return response.data;
   }
   ```

### API命名规范
- 获取列表: `getXxxList`
- 获取详情: `getXxxById`
- 创建: `createXxx`
- 更新: `updateXxx`
- 删除: `deleteXxx`
- 批量操作: `batchXxx`

## UI开发标准

### 现代化设计语言要求
**所有新页面必须使用以下设计元素**：

#### 背景和容器
```css
.page-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 24px;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 32px;
}
```

#### 标题样式
```css
h2 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  font-size: 32px;
}
```

#### 按钮样式
```css
.ant-btn {
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }
}

.ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}
```

#### 卡片样式
```css
.ant-card {
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
```

### 响应式设计要求
**必须包含以下断点**：
- `@media (max-width: 768px)` - 平板适配
- `@media (max-width: 480px)` - 手机适配
- `@media (prefers-color-scheme: dark)` - 深色模式

## 类型安全标准

### TypeScript严格模式
- **禁止使用 `any` 类型**，必须定义具体类型
- **所有API响应必须有类型定义**
- **所有组件props必须有类型定义**
- **所有事件处理函数必须有类型定义**

### 类型导入规范
```typescript
import type { 
  CaseSubmission, 
  CaseSubmissionStatus,
  ApiResponse 
} from '@/types/case-collection';
```

## Mock数据标准

### 环境变量控制
- **开发环境**: 自动使用Mock数据
- **生产环境**: 通过 `VITE_USE_MOCK_DATA=true/false` 控制

### Mock数据要求
- **必须模拟真实的网络延迟**: 使用 `mockDelay()`
- **必须包含完整的业务逻辑**: 筛选、分页、排序
- **必须提供丰富的测试数据**: 至少10条以上记录
- **必须包含各种状态的数据**: 成功、失败、进行中等

### Mock方法命名
```typescript
// 正确命名
export async function mockGetReviewHistory() { }
export async function mockCreateActivity() { }
export async function mockUpdateSubmission() { }

// 错误命名 - 禁止
export async function getReviewHistoryMock() { }
export async function reviewHistoryMock() { }
```

## 路由配置标准

### 新页面路由添加
**在 `src/router/index.ts` 的 case-collection 路由组下添加**：

```typescript
{
  path: 'new-feature',
  name: 'CaseCollectionNewFeature',
  component: () => import('@/views/case-collection/new-feature/Index.vue'),
  meta: { title: '新功能' },
}
```

### 路由命名规范
- **路由名称**: `CaseCollection` + 功能名称（PascalCase）
- **路径**: 小写字母，用连字符分隔
- **组件路径**: 与路由路径对应

## 文件协调标准

### 新增功能时必须同步修改的文件
1. **页面组件** - `src/views/case-collection/xxx/`
2. **路由配置** - `src/router/index.ts`
3. **API服务** - `src/api/case-collection-service.ts`
4. **Mock数据** - `src/api/case-collection-mock.ts`
5. **类型定义** - `src/types/case-collection.ts`

### 修改API时必须同步的操作
1. **更新service层的方法签名**
2. **更新mock层的对应方法**
3. **更新或新增相关类型定义**
4. **确保useMockData条件分支正确**

## 组件开发标准

### 组件结构要求
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入和类型定义
// 响应式数据
// 计算属性
// 方法定义
// 生命周期
</script>

<style scoped lang="less">
// 样式定义
</style>
```

### 组件命名规范
- **文件名**: PascalCase (如 `ReviewList.vue`)
- **组件内部**: 使用 `<script setup>` 语法
- **样式**: 必须使用 `scoped` 和 `lang="less"`

## 错误处理标准

### API调用错误处理
```typescript
try {
  const response = await caseCollectionService.getXxx();
  if (response.success) {
    // 处理成功逻辑
  } else {
    message.error(response.message || '操作失败');
  }
} catch (error) {
  console.error('操作失败:', error);
  message.error('操作失败');
}
```

### 必须包含的错误处理
- **网络请求异常**
- **数据格式错误**
- **权限验证失败**
- **用户操作错误**

## 禁止操作清单

### 严格禁止的操作
- ❌ **修改package.json依赖** - 除非明确需要新增依赖
- ❌ **使用内联样式** - 必须使用scoped样式
- ❌ **跳过TypeScript类型检查** - 不允许使用@ts-ignore
- ❌ **创建没有Mock支持的API** - 所有API必须有Mock实现
- ❌ **直接修改.env文件** - 除非明确需要新增环境变量
- ❌ **使用any类型** - 必须定义具体类型
- ❌ **忽略响应式设计** - 所有页面必须支持移动端
- ❌ **不一致的UI风格** - 必须遵循现代化设计标准

### 代码质量禁止项
- ❌ **硬编码数据** - 使用配置或常量
- ❌ **重复代码** - 提取公共组件或工具函数
- ❌ **缺少注释** - 复杂逻辑必须添加注释
- ❌ **不规范的命名** - 遵循项目命名规范

## AI决策标准

### 优先级判断
1. **功能完整性** > 代码优雅性
2. **类型安全** > 开发速度
3. **用户体验** > 技术实现复杂度
4. **Mock数据完整性** > 真实API调用

### 冲突处理原则
- **UI设计冲突**: 优先使用现代化设计语言
- **API设计冲突**: 优先保持三层架构一致性
- **类型定义冲突**: 优先使用更严格的类型定义
- **功能需求冲突**: 优先满足核心业务需求

### 不确定情况的处理
1. **检查现有代码模式** - 参考已有实现
2. **查阅项目文档** - 查看相关说明文档
3. **保持一致性** - 与现有代码风格保持一致
4. **选择保守方案** - 优先选择风险较低的实现方式
