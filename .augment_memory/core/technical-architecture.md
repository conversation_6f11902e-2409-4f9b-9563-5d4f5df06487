# 技术架构详细说明

## 整体架构设计

### 架构模式
**采用分层架构 + 组件化设计**

```
┌─────────────────────────────────────┐
│           表现层 (Presentation)      │
│         Vue 3 Components           │
├─────────────────────────────────────┤
│           业务层 (Business)         │
│        Service Layer API           │
├─────────────────────────────────────┤
│           数据层 (Data)             │
│        Mock Data System            │
└─────────────────────────────────────┘
```

### 核心设计原则
1. **单一职责**: 每个组件和模块职责明确
2. **开闭原则**: 对扩展开放，对修改封闭
3. **依赖倒置**: 依赖抽象而非具体实现
4. **接口隔离**: 接口设计精简，职责单一

## 前端架构

### Vue 3 组件架构
```
App.vue
├── Router View
    ├── Layout Components
    │   ├── Header
    │   ├── Sidebar
    │   └── Main Content
    └── Feature Modules
        ├── Activities Module
        ├── Submit Module
        ├── Review Module ⭐
        ├── Categories Module
        └── Dashboard Module
```

### 组件设计模式

#### 1. 容器组件 (Container Components)
**职责**: 数据获取、状态管理、业务逻辑
```typescript
// 示例: ReviewList.vue
<script setup lang="ts">
const cases = ref<CaseSubmission[]>([]);
const loading = ref(false);

const loadCases = async () => {
  loading.value = true;
  try {
    const response = await caseCollectionService.getSubmissionList(params);
    cases.value = response.data.list;
  } finally {
    loading.value = false;
  }
};
</script>
```

#### 2. 展示组件 (Presentational Components)
**职责**: UI渲染、用户交互、样式展示
```vue
<!-- 示例: CaseCard.vue -->
<template>
  <a-card class="case-card">
    <template #title>{{ case.title }}</template>
    <p>{{ case.summary }}</p>
    <a-tag :color="getStatusColor(case.status)">
      {{ getStatusText(case.status) }}
    </a-tag>
  </a-card>
</template>
```

### 状态管理策略

#### Vue 3 Reactivity API
**使用场景**: 组件内状态、简单的跨组件通信
```typescript
// 响应式数据
const cases = ref<CaseSubmission[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 计算属性
const filteredCases = computed(() => {
  return cases.value.filter(item => 
    item.status === searchForm.status
  );
});
```

#### Provide/Inject 模式
**使用场景**: 深层组件通信、主题配置
```typescript
// 父组件提供
provide('theme', themeConfig);

// 子组件注入
const theme = inject('theme');
```

## API架构设计

### 三层API架构

#### 1. 服务层 (Service Layer)
**文件**: `src/api/case-collection-service.ts`
**职责**: API接口定义、环境切换、错误处理

```typescript
class CaseCollectionService {
  async getSubmissionList(params: QueryParams): Promise<ApiResponse<PageResult<CaseSubmission>>> {
    if (useMockData) {
      return mockApi.mockGetSubmissionList(params);
    }
    const response = await realApi.getSubmissionList(params);
    return response.data;
  }
}
```

#### 2. Mock层 (Mock Layer)
**文件**: `src/api/case-collection-mock.ts`
**职责**: 模拟数据生成、业务逻辑模拟

```typescript
export async function mockGetSubmissionList(params?: QueryParams): Promise<ApiResponse<PageResult<CaseSubmission>>> {
  await mockDelay(); // 模拟网络延迟
  
  // 业务逻辑模拟
  let filteredData = mockSubmissions;
  if (params?.status) {
    filteredData = filteredData.filter(item => item.status === params.status);
  }
  
  // 分页处理
  const paginatedList = filteredData.slice(startIndex, endIndex);
  
  return wrapApiResponse({
    list: paginatedList,
    total: filteredData.length,
    page: params?.page || 1,
    pageSize: params?.pageSize || 10
  });
}
```

#### 3. 类型层 (Type Layer)
**文件**: `src/types/case-collection.ts`
**职责**: 类型定义、接口约束

```typescript
export interface CaseSubmission {
  id: number;
  title: string;
  content: string;
  status: CaseSubmissionStatus;
  submitTime?: string;
  reviewTime?: string;
  reviewScore?: number;
}

export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: string;
}
```

### API设计规范

#### RESTful API设计
```typescript
// 资源命名规范
GET    /api/case-collection/activities        // 获取活动列表
POST   /api/case-collection/activities        // 创建活动
GET    /api/case-collection/activities/:id    // 获取活动详情
PUT    /api/case-collection/activities/:id    // 更新活动
DELETE /api/case-collection/activities/:id    // 删除活动
```

#### 统一响应格式
```typescript
// 成功响应
{
  "code": 0,
  "message": "success",
  "data": { /* 实际数据 */ },
  "success": true,
  "timestamp": "2025-01-07T10:30:00Z"
}

// 错误响应
{
  "code": 500,
  "message": "操作失败",
  "data": null,
  "success": false,
  "timestamp": "2025-01-07T10:30:00Z"
}
```

## 数据流架构

### 单向数据流
```
User Action → Component Event → API Call → Data Update → UI Re-render
```

### 数据流示例
```typescript
// 1. 用户操作
const handleSearch = () => {
  searchForm.keyword = 'test';
  loadCases(); // 触发数据加载
};

// 2. API调用
const loadCases = async () => {
  const response = await caseCollectionService.getSubmissionList(searchForm);
  
  // 3. 数据更新
  cases.value = response.data.list;
  
  // 4. UI自动重新渲染 (Vue响应式系统)
};
```

## Mock数据架构

### Mock系统设计
```
Environment Variable (VITE_USE_MOCK_DATA)
           ↓
Service Layer Decision
           ↓
    ┌─────────────┬─────────────┐
    ↓             ↓             ↓
Mock Data     Real API     Hybrid Mode
```

### Mock数据分层
```
Base Mock Data (mock/case-collection.ts)
           ↓
Mock API Implementation (api/case-collection-mock.ts)
           ↓
Service Layer Integration (api/case-collection-service.ts)
```

### Mock数据特点
1. **真实性**: 模拟真实的业务数据和逻辑
2. **完整性**: 覆盖所有API接口和业务场景
3. **一致性**: 与真实API保持相同的数据格式
4. **可控性**: 支持各种测试场景和边界条件

## 路由架构

### 路由设计模式
```typescript
// 嵌套路由结构
{
  path: '/case-collection',
  component: Layout,
  children: [
    {
      path: 'activities',
      component: () => import('@/views/case-collection/activities/Index.vue')
    },
    {
      path: 'review',
      children: [
        { path: 'list', component: ReviewList },
        { path: 'batch', component: BatchReview },
        { path: 'history', component: ReviewHistory },
        { path: 'settings', component: ReviewSettings }
      ]
    }
  ]
}
```

### 路由守卫
```typescript
// 权限验证
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !isAuthenticated()) {
    next('/login');
  } else {
    next();
  }
});
```

## 样式架构

### CSS架构模式
**采用 BEM + Scoped CSS + CSS变量**

```less
// BEM命名规范
.review-list {
  &__header {
    // 头部样式
  }
  
  &__content {
    // 内容样式
  }
  
  &__item {
    // 列表项样式
    
    &--selected {
      // 选中状态
    }
  }
}
```

### 设计系统
```less
// CSS变量定义
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  --border-radius-large: 20px;
  --border-radius-medium: 16px;
  --border-radius-small: 12px;
  --shadow-large: 0 20px 40px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.08);
}
```

### 响应式设计
```less
// 断点定义
@screen-xs: 480px;
@screen-sm: 768px;
@screen-md: 1024px;
@screen-lg: 1200px;

// 响应式混入
.responsive-padding() {
  padding: 24px;
  
  @media (max-width: @screen-sm) {
    padding: 16px;
  }
  
  @media (max-width: @screen-xs) {
    padding: 12px;
  }
}
```

## 性能优化架构

### 代码分割
```typescript
// 路由级别的代码分割
const ReviewHistory = () => import('@/views/case-collection/review/ReviewHistory.vue');

// 组件级别的懒加载
const HeavyComponent = defineAsyncComponent(() => import('./HeavyComponent.vue'));
```

### 缓存策略
```typescript
// API响应缓存
const cache = new Map();

const getCachedData = async (key: string, fetcher: () => Promise<any>) => {
  if (cache.has(key)) {
    return cache.get(key);
  }
  
  const data = await fetcher();
  cache.set(key, data);
  return data;
};
```

### 虚拟滚动 (计划中)
```typescript
// 大列表优化
const VirtualList = defineComponent({
  props: {
    items: Array,
    itemHeight: Number
  },
  setup(props) {
    const visibleItems = computed(() => {
      // 计算可见项目
    });
    
    return { visibleItems };
  }
});
```

## 错误处理架构

### 全局错误处理
```typescript
// 全局错误捕获
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err);
  // 错误上报
  errorReporting.report(err, { vm, info });
};
```

### API错误处理
```typescript
// 统一错误处理
const handleApiError = (error: any) => {
  if (error.response) {
    // HTTP错误
    message.error(`请求失败: ${error.response.status}`);
  } else if (error.request) {
    // 网络错误
    message.error('网络连接失败');
  } else {
    // 其他错误
    message.error('操作失败');
  }
};
```

## 测试架构 (计划中)

### 单元测试
```typescript
// 组件测试
describe('ReviewList', () => {
  it('should render case list correctly', () => {
    const wrapper = mount(ReviewList, {
      props: { cases: mockCases }
    });
    
    expect(wrapper.findAll('.case-item')).toHaveLength(mockCases.length);
  });
});
```

### 集成测试
```typescript
// API测试
describe('CaseCollectionService', () => {
  it('should fetch submission list', async () => {
    const result = await caseCollectionService.getSubmissionList();
    
    expect(result.success).toBe(true);
    expect(result.data.list).toBeInstanceOf(Array);
  });
});
```

## 部署架构

### 构建优化
```typescript
// Vite配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router'],
          antd: ['ant-design-vue'],
          utils: ['lodash-es', 'dayjs']
        }
      }
    }
  }
});
```

### 环境配置
```bash
# 开发环境
VITE_USE_MOCK_DATA=true
VITE_APP_ENV=development

# 生产环境
VITE_USE_MOCK_DATA=false
VITE_APP_ENV=production
```

这个技术架构确保了系统的可扩展性、可维护性和高性能，为后续的功能扩展和优化提供了坚实的基础。
