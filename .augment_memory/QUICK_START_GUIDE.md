# 案例收集系统快速启动指南

## 🚀 快速开始

### 1. 环境准备
确保已安装以下环境：
- Node.js 16+
- npm 或 yarn
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 启动项目
```bash
# 进入项目目录
cd model

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

### 3. 访问系统
- **主页**: http://localhost:3000
- **案例收集系统**: http://localhost:3000/case-collection
- **API测试页面**: http://localhost:3000/case-collection/test/api

## 📋 功能导航

### 核心功能页面

#### 🎯 活动管理
- **活动列表**: `/case-collection/activities/list`
- **创建活动**: `/case-collection/activities/create`
- **活动详情**: `/case-collection/activities/:id`

#### 📝 案例提交
- **提交入口**: `/case-collection/submit`
- **提交表单**: `/case-collection/submit/form`
- **我的提交**: `/case-collection/submissions`

#### ⚖️ 审核管理
- **审核列表**: `/case-collection/review/list`
- **批量审核**: `/case-collection/review/batch` ⭐ 新功能
- **审核历史**: `/case-collection/review/history` ⭐ 新功能
- **审核设置**: `/case-collection/review/settings` ⭐ 新功能

#### 📊 数据统计
- **数据面板**: `/case-collection/dashboard`
- **分类管理**: `/case-collection/categories`

## 🔧 新功能详解

### 1. 批量审核 (`/case-collection/review/batch`)
**功能特点**：
- ✅ 多条件筛选案例
- ✅ 批量选择和操作
- ✅ 复用现有审核表单
- ✅ 实时操作反馈

**使用流程**：
1. 设置筛选条件
2. 选择要审核的案例
3. 点击"批量审核"按钮
4. 填写审核意见和评分
5. 提交完成批量审核

### 2. 审核历史 (`/case-collection/review/history`)
**功能特点**：
- ✅ 统计卡片展示关键指标
- ✅ 多维度筛选和搜索
- ✅ 详细的历史记录表格
- ✅ 数据导出功能
- ✅ 现代化UI设计

**主要指标**：
- 总审核数、今日审核数
- 通过率、平均分
- 审核趋势分析

### 3. 审核设置 (`/case-collection/review/settings`)
**功能特点**：
- ✅ 三大配置模块（规则、标准、模板）
- ✅ 动态评分标准配置
- ✅ 审核模板管理
- ✅ 实时权重验证

**配置项目**：
- **审核规则**: 时限、分数线、通知设置
- **评分标准**: 自定义维度和权重
- **快速模板**: 预设审核模板管理

## 🧪 API测试

访问 `/case-collection/test/api` 进行API连接测试：

### 可测试的API：
- ✅ 活动列表API
- ✅ 案例列表API  
- ✅ 审核历史API
- ✅ 审核统计API
- ✅ 审核设置API
- ✅ 审核模板API

### 测试步骤：
1. 打开API测试页面
2. 点击各个测试按钮
3. 查看测试结果
4. 检查浏览器控制台的网络请求

## 🔍 故障排除

### 常见问题

#### 1. API请求失败
**症状**: 页面显示"加载失败"或空白数据
**解决方案**:
```bash
# 检查环境变量
cat .env
# 确认 VITE_USE_MOCK_DATA=false

# 检查API服务状态
curl https://mfjg-pc.aidangqun.com/owsz/api/case-collection/activities
```

#### 2. 页面加载错误
**症状**: 页面白屏或组件报错
**解决方案**:
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install

# 重启开发服务器
npm run dev
```

#### 3. 路由访问404
**症状**: 直接访问URL显示404
**解决方案**:
- 确保从主页导航进入
- 检查路由配置是否正确
- 刷新浏览器页面

### 开发者工具

#### 浏览器控制台检查：
1. **Network面板**: 查看API请求状态
2. **Console面板**: 查看错误日志
3. **Vue DevTools**: 检查组件状态

#### 日志级别：
- `console.log`: 一般信息
- `console.warn`: 警告信息  
- `console.error`: 错误信息

## 📱 移动端适配

所有新页面都支持移动端访问：
- ✅ 响应式布局
- ✅ 触摸友好的交互
- ✅ 移动端优化的表格显示
- ✅ 适配的表单控件

## 🎨 UI特色

### 设计亮点：
- **渐变背景**: 现代化视觉效果
- **毛玻璃效果**: 增强层次感
- **动画过渡**: 流畅的交互体验
- **深色模式**: 自动适配系统主题

### 交互特色：
- **悬停效果**: 丰富的视觉反馈
- **加载状态**: 清晰的操作进度
- **错误提示**: 友好的错误信息
- **成功反馈**: 及时的操作确认

## 📞 技术支持

如遇到问题，请：
1. 查看浏览器控制台错误信息
2. 检查API测试页面的连接状态
3. 参考 `API_INTEGRATION_SUMMARY.md` 文档
4. 联系开发团队获取支持

---

**祝您使用愉快！** 🎉
