<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>案例征集数据看板 - UI测试</title>
    <link href="https://cdn.jsdelivr.net/npm/ant-design-vue@4.2.6/dist/antd.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 24px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-header {
            background: white;
            padding: 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        .metric-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin: 8px 0;
        }
        .metric-label {
            color: #666;
            font-size: 14px;
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        .chart-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }
        .table-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        .data-table th {
            background: #fafafa;
            font-weight: 600;
        }
        .status-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status-active { background: #f6ffed; color: #52c41a; }
        .status-ended { background: #fff7e6; color: #fa8c16; }
        .error-value { color: #ff4d4f; background: #fff2f0; padding: 2px 4px; border-radius: 4px; }
        .placeholder-value { color: #999; }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>案例征集数据看板</h1>
            <p>实时监控案例征集活动的各项指标和数据</p>
        </div>

        <!-- 关键指标 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">总活动数</div>
                <div class="metric-value" id="total-activities">-</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">总案例数</div>
                <div class="metric-value" id="total-cases">-</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">通过率</div>
                <div class="metric-value" id="approval-rate">-</div>
            </div>
            <div class="metric-card">
                <div class="metric-label">参与人数</div>
                <div class="metric-value" id="total-participants">-</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-grid">
            <div class="chart-card">
                <div class="chart-title">审核状态分布</div>
                <div id="review-status-chart">
                    <div>已通过: <span id="approved-count">-</span></div>
                    <div>已驳回: <span id="rejected-count">-</span></div>
                    <div>审核中: <span id="pending-count">-</span></div>
                </div>
            </div>
            <div class="chart-card">
                <div class="chart-title">月度提交趋势</div>
                <div id="trend-chart">
                    <div>1月: <span id="month-1">-</span></div>
                    <div>2月: <span id="month-2">-</span></div>
                    <div>3月: <span id="month-3">-</span></div>
                    <div>4月: <span id="month-4">-</span></div>
                    <div>5月: <span id="month-5">-</span></div>
                    <div>6月: <span id="month-6">-</span></div>
                </div>
            </div>
        </div>

        <!-- 活动排行表 -->
        <div class="table-card">
            <div class="chart-title">活动排行榜</div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>活动名称</th>
                        <th>提交数</th>
                        <th>参与人数</th>
                        <th>状态</th>
                        <th>创建时间</th>
                    </tr>
                </thead>
                <tbody id="activity-ranking">
                    <!-- 数据将通过JavaScript填充 -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 数据安全处理函数
        const safeNumber = (value, defaultValue = 0) => {
            if (value === null || value === undefined || value === '') {
                return defaultValue;
            }
            const num = Number(value);
            return isNaN(num) || !isFinite(num) ? defaultValue : num;
        };

        const safePercentage = (numerator, denominator, precision = 0) => {
            const num = safeNumber(numerator);
            const den = safeNumber(denominator);
            if (den === 0) return 0;
            const percentage = (num / den) * 100;
            return precision > 0 ? Number(percentage.toFixed(precision)) : Math.round(percentage);
        };

        const safeDateFormat = (dateValue) => {
            if (!dateValue || dateValue === 'Invalid Date') {
                return '-';
            }
            try {
                const date = new Date(dateValue);
                if (isNaN(date.getTime())) {
                    return '-';
                }
                return date.toLocaleDateString('zh-CN');
            } catch {
                return '-';
            }
        };

        const formatMetricValue = (value) => {
            const num = safeNumber(value);
            if ((value === null || value === undefined || value === '') && num === 0) {
                return '-';
            }
            return num.toLocaleString();
        };

        // 模拟数据（包含一些可能导致NaN的异常数据）
        const mockData = {
            totalActivities: 15,
            totalSubmissions: 127,
            approvedSubmissions: 78,
            rejectedSubmissions: 26,
            pendingReviews: 23,
            // 故意添加一些异常数据来测试
            badData: {
                nanValue: NaN,
                infinityValue: Infinity,
                nullValue: null,
                undefinedValue: undefined,
                emptyString: '',
                invalidDate: 'Invalid Date'
            }
        };

        // 填充数据的函数
        function populateData() {
            // 关键指标
            document.getElementById('total-activities').textContent = formatMetricValue(mockData.totalActivities);
            document.getElementById('total-cases').textContent = formatMetricValue(mockData.totalSubmissions);
            
            // 测试通过率计算（可能产生NaN的场景）
            const approvalRate = safePercentage(mockData.approvedSubmissions, mockData.totalSubmissions);
            document.getElementById('approval-rate').textContent = approvalRate + '%';
            
            const participants = Math.floor(safeNumber(mockData.totalSubmissions) * 0.8);
            document.getElementById('total-participants').textContent = formatMetricValue(participants);

            // 审核状态分布
            document.getElementById('approved-count').textContent = formatMetricValue(mockData.approvedSubmissions);
            document.getElementById('rejected-count').textContent = formatMetricValue(mockData.rejectedSubmissions);
            document.getElementById('pending-count').textContent = formatMetricValue(mockData.pendingReviews);

            // 月度趋势（使用固定分布）
            const distributionRatios = [0.12, 0.15, 0.18, 0.22, 0.20, 0.13];
            const baseSubmissions = safeNumber(mockData.totalSubmissions);
            
            for (let i = 1; i <= 6; i++) {
                const monthValue = Math.floor(baseSubmissions * distributionRatios[i-1]);
                document.getElementById(`month-${i}`).textContent = formatMetricValue(monthValue);
            }

            // 活动排行表
            const activities = [
                { name: '数字化转型案例征集', submissions: 35, participants: 28, status: 'active', createTime: '2024-01-15' },
                { name: '绿色发展实践案例', submissions: 28, participants: 22, status: 'active', createTime: '2024-02-01' },
                { name: '党建创新案例征集', submissions: null, participants: undefined, status: 'ended', createTime: 'Invalid Date' }, // 异常数据
                { name: '技术创新案例', submissions: NaN, participants: Infinity, status: 'active', createTime: '2024-03-10' }, // 异常数据
                { name: '管理创新案例', submissions: '', participants: '', status: 'active', createTime: '' } // 空数据
            ];

            const tbody = document.getElementById('activity-ranking');
            tbody.innerHTML = '';
            
            activities.forEach((activity, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${activity.name}</td>
                    <td>${formatMetricValue(activity.submissions)}</td>
                    <td>${formatMetricValue(activity.participants)}</td>
                    <td><span class="status-tag status-${activity.status}">${activity.status === 'active' ? '进行中' : '已结束'}</span></td>
                    <td>${safeDateFormat(activity.createTime)}</td>
                `;
                tbody.appendChild(row);
            });

            // 测试异常数据处理
            testBadData();
        }

        // 测试异常数据处理
        function testBadData() {
            console.log('=== 异常数据测试 ===');
            console.log('NaN处理:', formatMetricValue(mockData.badData.nanValue));
            console.log('Infinity处理:', formatMetricValue(mockData.badData.infinityValue));
            console.log('null处理:', formatMetricValue(mockData.badData.nullValue));
            console.log('undefined处理:', formatMetricValue(mockData.badData.undefinedValue));
            console.log('空字符串处理:', formatMetricValue(mockData.badData.emptyString));
            console.log('无效日期处理:', safeDateFormat(mockData.badData.invalidDate));
            
            // 除零测试
            console.log('除零测试:', safePercentage(100, 0));
            console.log('NaN除法测试:', safePercentage(NaN, 100));
        }

        // 页面加载后填充数据
        window.onload = populateData;
    </script>
</body>
</html>
