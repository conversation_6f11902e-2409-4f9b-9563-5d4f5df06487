#online
spring:
  config:
    import: optional:configserver:http://configserver.aidangqun.test:8194?fail-fast=true
  cloud:
    config:
      #失败就停止
      fail-fast: true
      uri: http://configserver.goodsogood.online:8194
      label: v1.0.4
  profiles:
    active: pre
  application:
    name: finereport-assist
logging:
  level: DEBUG

#端口
sys-port: 16999
#节点
sys-id: szf1
#局点
sys-channel: goodsogood.online
#config dir
sys-config-dir: -szf
#数据库
sys-db0:
    db-url: *************
    db-name: gs_ows_szf_online
    db-user: gszw_owsszf
    db-password: IyfNa#Li71GZ
#redis
sys-redis-config:
    database: 9
    password: d7f153db47f04b24
    host: *************
    port: 6379
#是否开启定时任务
sys-scheduler: true
