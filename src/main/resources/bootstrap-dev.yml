spring:
  cloud:
    config:
      #失败就停止
      fail-fast: true
      uri: http://192.168.2.245:19999
      label: v1.0.6
  profiles:
    active: dev
  application:
    name: sas
logging:
  level: DEBUG

local:
  url: 172.16.30.133
  username: root
  password: Sj930211!
  db: ows-local

test:
  url: 192.168.2.238
  username: root
  password: 123654
  db: ows-test

mysql:
  url: ${test.url}
  username: ${test.username}
  password:  ${test.password}
  db: ${test.db}

#是否开启定时任务
sys-scheduler: false