sys-id: 1

#数据库
sys-db0:
  db-jdbc-url: **************
  db-name: gs_ows_zzb_test
  db-user: vbadmin
  db-password: vbad<PERSON>@123
#支付数据库
sys-pay-db0:
  db-jdbc-url: **************
  db-name: gs_pay
  db-user: vbadmin
  db-password: vbad<PERSON>@123
#redis
sys-redis-config:
  database: 89
  password: Qwer1234..
  host: **************
  port: 36379

sys-rabbit:
  address: **************
  port: 5672
  username: rabbitmq
  password: Qwer1234..

# doris 数据库信息
sys-doris-db0:
  db-jdbc-url: **************
  db-name: ows_ppmd_test
  db-user: root
  db-password: 4o&U80QlLCYmxQ1

management:
  health:
    elasticsearch:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: '/'
      path-mapping:
        health: ows-ppmd-sta/root/ping
        info: actuator/info
        metrics: actuator/metrics
        env: actuator/env
        beans: actuator/beans
        configprops: actuator/configprops
        auditevents: actuator/auditevents
        flyway: actuator/flyway
        threaddump: actuator/threaddumpf
        logfile: actuator/logfile
        prometheus: actuator/prometheus

#测试环境
server:
  port: 8080

#eureka:
#client:
#  register-with-eureka: false
#  fetch-registry: false

#eureka客户端，服务自动发现
eureka:
  client:
    service-url: # 指定服务注册中心
      defaultZone: http://**************:8104/eureka/
  instance:
    #服务名称，多个节点用相同的名称
    appname: ows-acceptance-tll
    #节点监听的ip
    hostname: 127.0.0.1

#mapper
mapper:
  not-empty: true
  identity: PostgreSQL

error-message:
  errors:
    9999: 当前接口已废除
    9901: 未知错误
    9902: 当前请求过多或服务器繁忙，请稍后再试
    9903: 调用外部接口发生错误
    9904: 当前客户端版本已经过期
    9905: 登录失效请重新登录
    9009: ${0}

ppmd:
  #sta-regionIds
  sta-region-ids:
    - 3
    - 34


scheduler:
  # 统计每个月一号的数据
  sta-month-one: 0 0 0 2 * ?
  sta-month-start-month: 2019-01

spring:
  cloud:
    config:
      enabled: false
  redis:
    database: ${sys-redis-config.database}
    password: ${sys-redis-config.password}
    host: ${sys-redis-config.host}
    port: ${sys-redis-config.port}
  main:
    allow-bean-definition-overriding: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    username: vbadmin
    url: *******************************************************************************************************************
    password: vbadmin@123
    driver-class-name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
    hikari:
      #connection-test-query: SELECT 1 FROM DUAL
      minimum-idle: 5
      maximum-pool-size: 15
      pool-name: hikari-jdbc-pool
      idle-timeout: 30000
      max-lifetime: 7170000
      connection-timeout: 60000
      register-mbeans: true
      data-source-properties:
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        cachePrepStmts: true
        useServerPrepStmts: true
  application:
    name: ows-acceptance
  sleuth:
    sampler:
      percentage: 1.0
  #rabbitmq
  rabbitmq:
    addresses: ${sys-rabbit.address}
    port: ${sys-rabbit.port}
    username: ${sys-rabbit.username}
    password: ${sys-rabbit.password}
    run: false
#其他模块名称（eureka.client.instance.appname）
tog-services:
  # 用户中心
  user-center: USER-CENTER-TLL

logging:
  config: classpath:log4j2-dev.xml

org-type-child: 10280304,10280309,10280314,10280315,10280319 #党支部类型

#saas配置
saas:
  label: V4.0.0


amountFormat:
  payUnit: 2 #交费精度 1 元、2 角、3 分
  payRule: 1 #交费规则 1 向上取整、2 向下取整、3 四舍五入

boc-sftp:
  run-flag: false
  host: ***************
  port: 21000
  username: DPBPada4
  password: UGIlXZ8Zg@KaMBcw
  remote-path: /download/DP01/
  local-path-party-money-data: E://sftp/PartyMoneyData/
  local-path-party-settlement-data: E://sftp/PartyMoneySetlementData/

run-scheduler: false