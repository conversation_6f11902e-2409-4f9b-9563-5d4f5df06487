package com.goodsogood.ows;

import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.filter.TranslateRequestFilter;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.helper.SpringContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.Executor;

@EnableScheduling
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.goodsogood.ows", "com.aidangqun.ows"})
@EnableDiscoveryClient
public class OwsAcceptanceApplication {
    private final HttpComponentsClientHttpRequestFactory clientHttpRequestFactory;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public OwsAcceptanceApplication(HttpComponentsClientHttpRequestFactory clientHttpRequestFactory,
                                    StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.clientHttpRequestFactory = clientHttpRequestFactory;
        RemoteApiHelper.setRedisTemplate(redisTemplate);
    }

    //必须new 一个RestTemplate并放入spring容器当中,否则启动时报错
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }

    @LoadBalanced
    @Bean //必须new 一个RestTemplate并放入spring容器当中,否则启动时报错
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    public static void main(String[] args) {
        ApplicationContext app = SpringApplication.run(OwsAcceptanceApplication.class, args);
        SpringContextUtil.setApplicationContext(app);
    }


    @Bean
    public Executor refreshOrgInfoExecutor() {
        return getExecutor(3, 6, 1000, "REFRESH_ORG_INFO");
    }


    @Bean
    public FilterRegistrationBean<TranslateRequestFilter> translateRequestFilterRegistration() {
        FilterRegistrationBean<TranslateRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TranslateRequestFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }

    private Executor getExecutor(int size, int maxSize, int queue, String name) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(size);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(queue);
        executor.setThreadNamePrefix(name);
        executor.initialize();
        return executor;
    }


}
