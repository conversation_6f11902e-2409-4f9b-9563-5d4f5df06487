package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.HonorEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 荣誉申报数据访问层
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Mapper
public interface HonorMapper {

    /**
     * 插入荣誉申报
     *
     * @param entity 荣誉申报实体
     * @return 影响行数
     */
    @Insert("""
        INSERT INTO t_honor (
            applicant, department, type, material_url, description, status, audit_opinion,
            organization_id, region_id, create_user, create_user_name, create_time
        ) VALUES (
            #{applicant}, #{department}, #{type}, #{materialUrl}, #{description}, #{status}, #{auditOpinion},
            #{organizationId}, #{regionId}, #{createUser}, #{createUserName}, #{createTime}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(HonorEntity entity);

    /**
     * 根据ID删除荣誉申报（逻辑删除）
     *
     * @param id 主键ID
     * @param updateUser 更新人ID
     * @param updateUserName 更新人姓名
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor
        SET status = 0, update_user = #{updateUser}, update_user_name = #{updateUserName}, update_time = NOW()
        WHERE id = #{id}
    """)
    int deleteById(@Param("id") Long id, @Param("updateUser") Long updateUser, @Param("updateUserName") String updateUserName);

    /**
     * 更新荣誉申报
     *
     * @param entity 荣誉申报实体
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor
        SET
            applicant = #{applicant},
            department = #{department},
            type = #{type},
            material_url = #{materialUrl},
            description = #{description},
            status = #{status},
            audit_opinion = #{auditOpinion},
            update_user = #{updateUser},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE id = #{id}
    """)
    int updateById(HonorEntity entity);

    /**
     * 根据ID查询荣誉申报
     *
     * @param id 主键ID
     * @return 荣誉申报实体
     */
    @Select("""
        SELECT
            id, applicant, department, type, material_url, description, status, audit_opinion,
            organization_id, region_id, create_user, create_user_name, create_time,
            update_user, update_user_name, update_time
        FROM t_honor
        WHERE id = #{id} AND status != 0
    """)
    HonorEntity findById(@Param("id") Long id);

    /**
     * 分页查询荣誉申报列表
     *
     * @param params 查询参数
     * @return 荣誉申报列表
     */
    @Select("""
        <script>
        SELECT
            id, applicant, department, type, material_url as materialUrl, description, status, audit_opinion as auditOpinion,
            organization_id as organizationId, region_id  as regionId, create_user, create_user_name, create_time as createTime,
            update_user, update_user_name, update_time
        FROM t_honor
        WHERE status != 0
            <if test='applicant != null'>AND applicant LIKE CONCAT('%', #{applicant}, '%')</if>
            <if test='department != null'>AND department LIKE CONCAT('%', #{department}, '%')</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='status != null'>AND status = #{status}</if>
            <if test='organizationId != null'>AND organization_id = #{organizationId}</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='startTime != null'>AND create_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND create_time <![CDATA[<= ]]>  #{endTime}</if>
        ORDER BY create_time DESC
        <if test='offset != null and pageSize != null'>LIMIT #{offset}, #{pageSize}</if>
        </script>
    """)
    List<HonorEntity> findByPage(Map<String, Object> params);

    /**
     * 查询荣誉申报总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
       <script>
        SELECT COUNT(1)
        FROM t_honor
        WHERE status != 0
            <if test='applicant != null'>AND applicant LIKE CONCAT('%', #{applicant}, '%')</if>
            <if test='department != null'>AND department LIKE CONCAT('%', #{department}, '%')</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='status != null'>AND status = #{status}</if>
            <if test='organizationId != null'>AND organization_id = #{organizationId}</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='startTime != null'>AND create_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND create_time <![CDATA[<= ]]> #{endTime}</if>
       </script>
    """)
    int countByParams(Map<String, Object> params);

    /**
     * 根据类型统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    @Select("""
        SELECT
            type,
            COUNT(1) as count
        FROM t_honor
        WHERE status != 0
        AND organization_id = #{organizationId}
        GROUP BY type
        ORDER BY type
    """)
    List<Map<String, Object>> countByType(@Param("organizationId") Long organizationId);

    /**
     * 根据状态统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    @Select("""
        SELECT
            status,
            COUNT(1) as count
        FROM t_honor
        WHERE status != 0
        AND organization_id = #{organizationId}
        GROUP BY status
        ORDER BY status
    """)
    List<Map<String, Object>> countByStatus(@Param("organizationId") Long organizationId);

    /**
     * 查询最新的荣誉申报
     *
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉申报列表
     */
    @Select("""
        SELECT
            id, applicant, department, type, material_url, description, status, audit_opinion,
            organization_id, region_id, create_user, create_user_name, create_time,
            update_user, update_user_name, update_time
        FROM t_honor
        WHERE status != 0
        AND organization_id = #{organizationId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    """)
    List<HonorEntity> findLatest(@Param("limit") Integer limit, @Param("organizationId") Long organizationId);

    /**
     * 按月统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    @Select("""
        SELECT
            DATE_FORMAT(create_time, '%Y-%m') as month,
            COUNT(1) as count
        FROM t_honor
        WHERE status != 0
        AND organization_id = #{organizationId}
        AND create_time >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(create_time, '%Y-%m')
        ORDER BY month
    """)
    List<Map<String, Object>> countByMonth(@Param("organizationId") Long organizationId);

    /**
     * 兼容旧版API - 查询所有
     *
     * @return 荣誉申报列表
     */
    @Select("""
        SELECT
            id, applicant, department, type, material_url, description, status, audit_opinion,
            organization_id, region_id, create_user, create_user_name, create_time,
            update_user, update_user_name, update_time
        FROM t_honor
        WHERE status != 0
        ORDER BY create_time DESC
    """)
    List<HonorEntity> selectAll();

    /**
     * 兼容旧版API - 根据ID查询
     *
     * @param id 主键ID
     * @return 荣誉申报实体
     */
    @Select("""
        SELECT
            id, applicant, department, type, material_url, description, status, audit_opinion,
            organization_id, region_id, create_user, create_user_name, create_time,
            update_user, update_user_name, update_time
        FROM t_honor
        WHERE id = #{id}
    """)
    HonorEntity selectById(@Param("id") Long id);

    /**
     * 兼容旧版API - 更新
     *
     * @param entity 荣誉申报实体
     */
    @Update("""
        UPDATE t_honor
        SET
            applicant = #{applicant},
            department = #{department},
            type = #{type},
            material_url = #{materialUrl},
            description = #{description},
            status = #{status},
            audit_opinion = #{auditOpinion}
        WHERE id = #{id}
    """)
    void update(HonorEntity entity);

    /**
     * 兼容旧版API - 删除
     *
     * @param id 主键ID
     */
    @Delete("DELETE FROM t_honor WHERE id = #{id}")
    void delete(@Param("id") Long id);
}