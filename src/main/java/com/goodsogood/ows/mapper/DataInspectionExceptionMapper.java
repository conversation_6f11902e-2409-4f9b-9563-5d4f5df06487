package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionExceptionEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 数据体检异常详情数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface DataInspectionExceptionMapper {

    /**
     * 插入异常记录
     *
     * @param entity 异常实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_exception_details (
                    type, title, description, unit, source, severity, status, detect_time, affected_records,
                    impact, solution, handle_time, handler, create_time, update_time
                ) VALUES (
                    #{type}, #{title}, #{description}, #{unit}, #{source}, #{severity}, #{status}, #{detectTime}, #{affectedRecords},
                    #{impact}, #{solution}, #{handleTime}, #{handler}, #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionExceptionEntity entity);

    /**
     * 根据ID删除异常记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_exception_details
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新异常记录
     *
     * @param entity 异常实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_exception_details
                SET
                    type = #{type},
                    title = #{title},
                    description = #{description},
                    unit = #{unit},
                    source = #{source},
                    severity = #{severity},
                    status = #{status},
                    detect_time = #{detectTime},
                    affected_records = #{affectedRecords},
                    impact = #{impact},
                    solution = #{solution},
                    handle_time = #{handleTime},
                    handler = #{handler},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(DataInspectionExceptionEntity entity);

    /**
     * 根据ID查询异常记录
     *
     * @param id 主键ID
     * @return 异常实体
     */
    @Select("""
                SELECT
                    id, type, title, description, unit, source, severity, status, detect_time as detectTime,
                    affected_records as affectedRecords, impact, solution, handle_time as handleTime, handler,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_exception_details
                WHERE id = #{id}
            """)
    DataInspectionExceptionEntity findById(@Param("id") Long id);

    /**
     * 分页查询异常记录列表
     *
     * @param params 查询参数
     * @return 异常记录列表
     */
    @Select("""
                <script>
                SELECT
                    id, type, title, description, unit, source, severity, status, detect_time as detectTime,
                    affected_records as affectedRecords, impact, solution, handle_time as handleTime, handler,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_exception_details
                WHERE 1=1
                <if test="type != null and type != ''">
                    AND type = #{type}
                </if>
                <if test="severity != null and severity != ''">
                    AND severity = #{severity}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="unit != null and unit != ''">
                    AND unit LIKE '%' || #{unit} || '%'
                </if>
                <if test="source != null and source != ''">
                    AND source = #{source}
                </if>
                <if test="startTime != null and startTime != ''">
                    AND detect_time &gt;= TO_TIMESTAMP(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endTime != null and endTime != ''">
                    AND detect_time &lt;= TO_TIMESTAMP(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                ORDER BY detect_time DESC, create_time DESC
                </script>
            """)
    List<DataInspectionExceptionEntity> findPage(Map<String, Object> params);

    /**
     * 查询异常记录总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_exception_details
                WHERE 1=1
                <if test="type != null and type != ''">
                    AND type = #{type}
                </if>
                <if test="severity != null and severity != ''">
                    AND severity = #{severity}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="unit != null and unit != ''">
                    AND unit LIKE '%' || #{unit} || '%'
                </if>
                <if test="source != null and source != ''">
                    AND source = #{source}
                </if>
                <if test="startTime != null and startTime != ''">
                    AND detect_time &gt;= TO_TIMESTAMP(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endTime != null and endTime != ''">
                    AND detect_time &lt;= TO_TIMESTAMP(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 批量更新异常状态
     *
     * @param ids 异常ID列表
     * @param status 状态
     * @param handler 处理人
     * @return 影响行数
     */
    @Update("""
                <script>
                UPDATE t_data_inspection_exception_details
                SET status = #{status}, handler = #{handler}, handle_time = NOW(), update_time = NOW()
                WHERE id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                </script>
            """)
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status, @Param("handler") String handler);

    /**
     * 按异常类型统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT type, COUNT(*) as count,
                       SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highCount,
                       SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as mediumCount,
                       SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as lowCount
                FROM t_data_inspection_exception_details
                GROUP BY type
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByType();

    /**
     * 按单位统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT unit, COUNT(*) as totalExceptions,
                       SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highSeverity,
                       SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as mediumSeverity,
                       SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as lowSeverity
                FROM t_data_inspection_exception_details
                WHERE unit IS NOT NULL
                GROUP BY unit
                ORDER BY totalExceptions DESC
            """)
    List<Map<String, Object>> countByUnit();

    /**
     * 获取异常概览统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT 
                    COUNT(*) as totalCount,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingCount,
                    SUM(CASE WHEN status = 'in_remediation' THEN 1 ELSE 0 END) as inRemediationCount,
                    SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolvedCount,
                    SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highSeverityCount,
                    SUM(affected_records) as totalAffectedRecords
                FROM t_data_inspection_exception_details
            """)
    Map<String, Object> getOverviewStatistics();

    /**
     * 按严重程度统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT severity, COUNT(*) as count
                FROM t_data_inspection_exception_details
                GROUP BY severity
                ORDER BY CASE severity WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 END
            """)
    List<Map<String, Object>> countBySeverity();

    /**
     * 按状态统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count
                FROM t_data_inspection_exception_details
                GROUP BY status
                ORDER BY CASE status WHEN 'pending' THEN 1 WHEN 'in_remediation' THEN 2 WHEN 'resolved' THEN 3 END
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 查询最近的异常记录
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 异常记录列表
     */
    @Select("""
                SELECT
                    id, type, title, description, unit, source, severity, status, detect_time as detectTime,
                    affected_records as affectedRecords, impact, solution, handle_time as handleTime, handler,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_exception_details
                WHERE detect_time &gt;= CURRENT_TIMESTAMP - INTERVAL '#{days} day'
                ORDER BY detect_time DESC
                LIMIT #{limit}
            """)
    List<DataInspectionExceptionEntity> findRecentExceptions(@Param("days") Integer days, @Param("limit") Integer limit);
}