package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionDataSourceEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 数据体检数据源数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface DataInspectionDataSourceMapper extends MyMapper<DataInspectionDataSourceEntity> {

    /**
     * 插入数据源
     *
     * @param entity 数据源实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_data_sources (
                    source_id, source_name, source_type, connection_url, status, last_sync_time,
                    sync_status, description, create_time, update_time
                ) VALUES (
                    #{sourceId}, #{sourceName}, #{sourceType}, #{connectionUrl}, #{status}, #{lastSyncTime},
                    #{syncStatus}, #{description}, #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionDataSourceEntity entity);

    /**
     * 根据ID删除数据源
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_data_sources
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新数据源
     *
     * @param entity 数据源实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_data_sources
                SET
                    source_id = #{sourceId},
                    source_name = #{sourceName},
                    source_type = #{sourceType},
                    connection_url = #{connectionUrl},
                    status = #{status},
                    last_sync_time = #{lastSyncTime},
                    sync_status = #{syncStatus},
                    description = #{description},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(DataInspectionDataSourceEntity entity);

    /**
     * 根据ID查询数据源
     *
     * @param id 主键ID
     * @return 数据源实体
     */
    @Select("""
                SELECT
                    id, source_id as sourceId, source_name as sourceName, source_type as sourceType,
                    connection_url as connectionUrl, status, last_sync_time as lastSyncTime, sync_status as syncStatus,
                    description, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_data_sources
                WHERE id = #{id}
            """)
    DataInspectionDataSourceEntity findById(@Param("id") Long id);

    /**
     * 根据数据源ID查询数据源
     *
     * @param sourceId 数据源ID
     * @return 数据源实体
     */
    @Select("""
                SELECT
                    id, source_id as sourceId, source_name as sourceName, source_type as sourceType,
                    connection_url as connectionUrl, status, last_sync_time as lastSyncTime, sync_status as syncStatus,
                    description, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_data_sources
                WHERE source_id = #{sourceId}
            """)
    DataInspectionDataSourceEntity findBySourceId(@Param("sourceId") String sourceId);

    /**
     * 分页查询数据源列表
     *
     * @param params 查询参数
     * @return 数据源列表
     */
    @Select("""
                <script>
                SELECT
                    id, source_id as sourceId, source_name as sourceName, source_type as sourceType,
                    connection_url as connectionUrl, status, last_sync_time as lastSyncTime, sync_status as syncStatus,
                    description, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_data_sources
                WHERE 1=1
                <if test="sourceName != null and sourceName != ''">
                    AND source_name LIKE '%' || #{sourceName} || '%'
                </if>
                <if test="sourceType != null and sourceType != ''">
                    AND source_type = #{sourceType}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="syncStatus != null and syncStatus != ''">
                    AND sync_status = #{syncStatus}
                </if>
                ORDER BY create_time DESC
                </script>
            """)
    List<DataInspectionDataSourceEntity> findPage(Map<String, Object> params);

    /**
     * 查询数据源总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_data_sources
                WHERE 1=1
                <if test="sourceName != null and sourceName != ''">
                    AND source_name LIKE '%' || #{sourceName} || '%'
                </if>
                <if test="sourceType != null and sourceType != ''">
                    AND source_type = #{sourceType}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="syncStatus != null and syncStatus != ''">
                    AND sync_status = #{syncStatus}
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 更新数据源连接状态
     *
     * @param id 数据源ID
     * @param status 连接状态
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_data_sources
                SET status = #{status}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateConnectionStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新数据源同步状态
     *
     * @param id 数据源ID
     * @param syncStatus 同步状态
     * @param lastSyncTime 最后同步时间
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_data_sources
                SET sync_status = #{syncStatus}, last_sync_time = #{lastSyncTime}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateSyncStatus(@Param("id") Long id, @Param("syncStatus") String syncStatus, @Param("lastSyncTime") java.util.Date lastSyncTime);

    /**
     * 按数据源类型统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT source_type as sourceType, COUNT(*) as count
                FROM t_data_inspection_data_sources
                GROUP BY source_type
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countBySourceType();

    /**
     * 按连接状态统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count
                FROM t_data_inspection_data_sources
                GROUP BY status
                ORDER BY status
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 根据数据源ID更新数据源
     *
     * @param entity 数据源实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_data_sources
                SET
                    source_name = #{sourceName},
                    source_type = #{sourceType},
                    connection_url = #{connectionUrl},
                    status = #{status},
                    last_sync_time = #{lastSyncTime},
                    sync_status = #{syncStatus},
                    description = #{description},
                    update_time = NOW()
                WHERE source_id = #{sourceId}
            """)
    int updateBySourceId(DataInspectionDataSourceEntity entity);

    /**
     * 根据数据源ID删除数据源
     *
     * @param sourceId 数据源ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_data_sources
                WHERE source_id = #{sourceId}
            """)
    int deleteBySourceId(@Param("sourceId") String sourceId);

    /**
     * 查询所有启用的数据源
     *
     * @return 数据源列表
     */
    @Select("""
                SELECT
                    id, source_id as sourceId, source_name as sourceName, source_type as sourceType,
                    connection_url as connectionUrl, status, last_sync_time as lastSyncTime, sync_status as syncStatus,
                    description, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_data_sources
                WHERE status = 1
                ORDER BY source_name
            """)
    List<DataInspectionDataSourceEntity> findAllEnabled();
}