package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionAutoConfigEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据体检自动配置数据访问接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Mapper
public interface DataInspectionAutoConfigMapper extends MyMapper<DataInspectionAutoConfigEntity> {

    /**
     * 插入自动配置
     *
     * @param entity 自动配置实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_auto_config (
                    config_name, enabled, frequency, execute_time, timezone, 
                    inspection_scope, check_types, last_run, next_run, status, 
                    create_time, update_time
                ) VALUES (
                    #{configName}, #{enabled}, #{frequency}, #{executeTime}, #{timezone},
                    #{inspectionScope}, #{checkTypes}, #{lastRun}, #{nextRun}, #{status},
                    #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionAutoConfigEntity entity);

    /**
     * 根据ID删除自动配置
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_auto_config
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新自动配置
     *
     * @param entity 自动配置实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_auto_config
                SET
                    config_name = #{configName},
                    enabled = #{enabled},
                    frequency = #{frequency},
                    execute_time = #{executeTime},
                    timezone = #{timezone},
                    inspection_scope = #{inspectionScope},
                    check_types = #{checkTypes},
                    last_run = #{lastRun},
                    next_run = #{nextRun},
                    status = #{status},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(DataInspectionAutoConfigEntity entity);

    /**
     * 根据ID查询自动配置
     *
     * @param id 主键ID
     * @return 自动配置实体
     */
    @Select("""
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE id = #{id}
            """)
    DataInspectionAutoConfigEntity selectById(@Param("id") Long id);

    /**
     * 根据ID查询自动配置
     *
     * @param id 主键ID
     * @return 自动配置实体
     */
    @Select("""
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE id = #{id}
            """)
    DataInspectionAutoConfigEntity findById(@Param("id") Long id);

    /**
     * 根据配置名称查询自动配置
     *
     * @param configName 配置名称
     * @return 自动配置实体
     */
    @Select("""
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE config_name = #{configName}
            """)
    DataInspectionAutoConfigEntity findByConfigName(@Param("configName") String configName);

    /**
     * 分页查询自动配置列表
     *
     * @param params 查询参数
     * @return 自动配置列表
     */
    @Select("""
                <script>
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE 1=1
                <if test="configName != null and configName != ''">
                    AND config_name LIKE '%' || #{configName} || '%'
                </if>
                <if test="enabled != null">
                    AND enabled = #{enabled}
                </if>
                <if test="frequency != null and frequency != ''">
                    AND frequency = #{frequency}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                ORDER BY create_time DESC
                <if test="offset != null and pageSize != null">
                    LIMIT #{pageSize} OFFSET #{offset}
                </if>
                </script>
            """)
    List<DataInspectionAutoConfigEntity> findPage(Map<String, Object> params);

    /**
     * 查询自动配置总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_auto_config
                WHERE 1=1
                <if test="configName != null and configName != ''">
                    AND config_name LIKE '%' || #{configName} || '%'
                </if>
                <if test="enabled != null">
                    AND enabled = #{enabled}
                </if>
                <if test="frequency != null and frequency != ''">
                    AND frequency = #{frequency}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 更新配置启用状态
     *
     * @param id 配置ID
     * @param enabled 是否启用
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_auto_config
                SET enabled = #{enabled}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateEnabledStatus(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 更新配置状态
     *
     * @param id 配置ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_auto_config
                SET status = #{status}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 更新执行时间信息
     *
     * @param id 配置ID
     * @param lastRun 最后运行时间
     * @param nextRun 下次运行时间
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_auto_config
                SET last_run = #{lastRun}, next_run = #{nextRun}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateRunTimes(@Param("id") Long id, 
                      @Param("lastRun") Date lastRun, 
                      @Param("nextRun") Date nextRun);

    /**
     * 查询所有启用的自动配置
     *
     * @return 自动配置列表
     */
    @Select("""
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE enabled = true AND status = 'active'
                ORDER BY execute_time
            """)
    List<DataInspectionAutoConfigEntity> findAllEnabled();

    /**
     * 查询需要执行的配置
     *
     * @param currentTime 当前时间
     * @return 自动配置列表
     */
    @Select("""
                SELECT
                    id, config_name as configName, enabled, frequency, execute_time as executeTime,
                    timezone, inspection_scope as inspectionScope, check_types as checkTypes,
                    last_run as lastRun, next_run as nextRun, status,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_auto_config
                WHERE enabled = true 
                  AND status = 'active'
                  AND (next_run IS NULL OR next_run <= #{currentTime})
                ORDER BY next_run
            """)
    List<DataInspectionAutoConfigEntity> findPendingConfigs(@Param("currentTime") Date currentTime);

    /**
     * 按频率统计配置数量
     *
     * @return 统计结果
     */
    @Select("""
                SELECT frequency, COUNT(*) as count,
                       SUM(CASE WHEN enabled = true THEN 1 ELSE 0 END) as enabledCount
                FROM t_data_inspection_auto_config
                GROUP BY frequency
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByFrequency();

    /**
     * 按状态统计配置数量
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count,
                       SUM(CASE WHEN enabled = true THEN 1 ELSE 0 END) as enabledCount
                FROM t_data_inspection_auto_config
                GROUP BY status
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 获取配置概览统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT
                    COUNT(*) as totalConfigs,
                    SUM(CASE WHEN enabled = true THEN 1 ELSE 0 END) as enabledConfigs,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as activeConfigs,
                    COUNT(DISTINCT frequency) as frequencyTypes,
                    AVG(EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - last_run))/3600) as avgHoursSinceLastRun
                FROM t_data_inspection_auto_config
            """)
    Map<String, Object> getOverviewStatistics();

    /**
     * 批量删除自动配置
     *
     * @param ids ID列表
     * @return 影响行数
     */
    @Delete("""
                <script>
                DELETE FROM t_data_inspection_auto_config
                WHERE id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                </script>
            """)
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 批量更新启用状态
     *
     * @param ids ID列表
     * @param enabled 是否启用
     * @return 影响行数
     */
    @Update("""
                <script>
                UPDATE t_data_inspection_auto_config
                SET enabled = #{enabled}, update_time = NOW()
                WHERE id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                </script>
            """)
    int batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);
}