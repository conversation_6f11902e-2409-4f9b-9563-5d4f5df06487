package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionScheduledTaskEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据体检定时任务数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Repository
@Mapper
public interface DataInspectionScheduledTaskMapper extends MyMapper<DataInspectionScheduledTaskEntity> {

    /**
     * 插入定时任务
     *
     * @param entity 定时任务实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_scheduled_tasks (
                    task_name, task_type, check_types, cron_expression, cron_description, is_enabled,
                    status, last_execute_time, next_execute_time, execution_count, success_count, failed_count,
                    avg_duration_seconds, create_time, update_time, creator
                ) VALUES (
                    #{taskName}, #{taskType}, #{checkTypes}, #{cronExpression}, #{cronDescription}, #{isEnabled},
                    #{status}, #{lastExecuteTime}, #{nextExecuteTime}, #{executionCount}, #{successCount}, #{failedCount},
                    #{avgDurationSeconds}, #{createTime}, #{updateTime}, #{creator}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionScheduledTaskEntity entity);

    /**
     * 根据ID删除定时任务
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_scheduled_tasks
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新定时任务
     *
     * @param entity 定时任务实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_scheduled_tasks
                SET
                    task_name = #{taskName},
                    task_type = #{taskType},
                    check_types = #{checkTypes},
                    cron_expression = #{cronExpression},
                    cron_description = #{cronDescription},
                    is_enabled = #{isEnabled},
                    status = #{status},
                    last_execute_time = #{lastExecuteTime},
                    next_execute_time = #{nextExecuteTime},
                    execution_count = #{executionCount},
                    success_count = #{successCount},
                    failed_count = #{failedCount},
                    avg_duration_seconds = #{avgDurationSeconds},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(DataInspectionScheduledTaskEntity entity);

    /**
     * 根据ID查询定时任务
     *
     * @param id 主键ID
     * @return 定时任务实体
     */
    @Select("""
                SELECT
                    id, task_name as taskName, task_type as taskType, check_types as checkTypes,
                    cron_expression as cronExpression, cron_description as cronDescription, is_enabled as isEnabled,
                    status, last_execute_time as lastExecuteTime, next_execute_time as nextExecuteTime,
                    execution_count as executionCount, success_count as successCount, failed_count as failedCount,
                    avg_duration_seconds as avgDurationSeconds, create_time as createTime, update_time as updateTime, creator
                FROM t_data_inspection_scheduled_tasks
                WHERE id = #{id}
            """)
    DataInspectionScheduledTaskEntity findById(@Param("id") Long id);

    /**
     * 分页查询定时任务列表
     *
     * @param params 查询参数
     * @return 定时任务列表
     */
    @Select("""
                <script>
                SELECT
                    id, task_name as taskName, task_type as taskType, check_types as checkTypes,
                    cron_expression as cronExpression, cron_description as cronDescription, is_enabled as isEnabled,
                    status, last_execute_time as lastExecuteTime, next_execute_time as nextExecuteTime,
                    execution_count as executionCount, success_count as successCount, failed_count as failedCount,
                    avg_duration_seconds as avgDurationSeconds, create_time as createTime, update_time as updateTime, creator
                FROM t_data_inspection_scheduled_tasks
                WHERE 1=1
                <if test="taskName != null and taskName != ''">
                    AND task_name LIKE '%' || #{taskName} || '%'
                </if>
                <if test="taskType != null">
                    AND task_type = #{taskType}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="isEnabled != null">
                    AND is_enabled = #{isEnabled}
                </if>
                <if test="creator != null and creator != ''">
                    AND creator = #{creator}
                </if>
                ORDER BY create_time DESC
                </script>
            """)
    List<DataInspectionScheduledTaskEntity> findPage(Map<String, Object> params);

    /**
     * 查询定时任务总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_scheduled_tasks
                WHERE 1=1
                <if test="taskName != null and taskName != ''">
                    AND task_name LIKE '%' || #{taskName} || '%'
                </if>
                <if test="taskType != null">
                    AND task_type = #{taskType}
                </if>
                <if test="status != null">
                    AND status = #{status}
                </if>
                <if test="isEnabled != null">
                    AND is_enabled = #{isEnabled}
                </if>
                <if test="creator != null and creator != ''">
                    AND creator = #{creator}
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 启用/禁用定时任务
     *
     * @param id 任务ID
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_scheduled_tasks
                SET is_enabled = #{isEnabled}, update_time = NOW()
                WHERE id = #{id}
            """)
    int toggleEnable(@Param("id") Long id, @Param("isEnabled") Boolean isEnabled);

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_scheduled_tasks
                SET status = #{status}, update_time = NOW()
                WHERE id = #{id}
            """)
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新任务执行信息
     *
     * @param id 任务ID
     * @param status 状态
     * @param lastExecuteTime 最后执行时间
     * @param nextExecuteTime 下次执行时间
     * @param isSuccess 是否成功
     * @param duration 执行时长(秒)
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_scheduled_tasks
                SET status = #{status},
                    last_execute_time = #{lastExecuteTime},
                    next_execute_time = #{nextExecuteTime},
                    execution_count = execution_count + 1,
                    success_count = CASE WHEN #{isSuccess} THEN success_count + 1 ELSE success_count END,
                    failed_count = CASE WHEN #{isSuccess} THEN failed_count ELSE failed_count + 1 END,
                    avg_duration_seconds = CASE 
                        WHEN execution_count = 0 THEN #{duration}
                        ELSE (avg_duration_seconds * execution_count + #{duration}) / (execution_count + 1)
                    END,
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateExecutionInfo(@Param("id") Long id, @Param("status") Integer status, 
                           @Param("lastExecuteTime") Date lastExecuteTime, @Param("nextExecuteTime") Date nextExecuteTime,
                           @Param("isSuccess") Boolean isSuccess, @Param("duration") Integer duration);

    /**
     * 查询需要执行的定时任务
     *
     * @return 任务列表
     */
    @Select("""
                SELECT
                    id, task_name as taskName, task_type as taskType, check_types as checkTypes,
                    cron_expression as cronExpression, cron_description as cronDescription, is_enabled as isEnabled,
                    status, last_execute_time as lastExecuteTime, next_execute_time as nextExecuteTime,
                    execution_count as executionCount, success_count as successCount, failed_count as failedCount,
                    avg_duration_seconds as avgDurationSeconds, create_time as createTime, update_time as updateTime, creator
                FROM t_data_inspection_scheduled_tasks
                WHERE is_enabled = true 
                  AND status IN (1, 3)
                  AND (next_execute_time IS NULL OR next_execute_time <= NOW())
                ORDER BY next_execute_time
            """)
    List<DataInspectionScheduledTaskEntity> findTasksToExecute();

    /**
     * 按任务类型统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT task_type as taskType, COUNT(*) as count,
                       SUM(CASE WHEN is_enabled THEN 1 ELSE 0 END) as enabledCount
                FROM t_data_inspection_scheduled_tasks
                GROUP BY task_type
                ORDER BY task_type
            """)
    List<Map<String, Object>> countByTaskType();

    /**
     * 按状态统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count
                FROM t_data_inspection_scheduled_tasks
                GROUP BY status
                ORDER BY status
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 获取任务执行概览统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT 
                    COUNT(*) as totalTasks,
                    SUM(CASE WHEN is_enabled THEN 1 ELSE 0 END) as enabledTasks,
                    SUM(execution_count) as totalExecutions,
                    SUM(success_count) as totalSuccess,
                    SUM(failed_count) as totalFailed,
                    AVG(avg_duration_seconds) as avgDuration
                FROM t_data_inspection_scheduled_tasks
            """)
    Map<String, Object> getExecutionOverviewStatistics();

    /**
     * 查询最近创建的任务
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("""
                SELECT
                    id, task_name as taskName, task_type as taskType, check_types as checkTypes,
                    cron_expression as cronExpression, cron_description as cronDescription, is_enabled as isEnabled,
                    status, last_execute_time as lastExecuteTime, next_execute_time as nextExecuteTime,
                    execution_count as executionCount, success_count as successCount, failed_count as failedCount,
                    avg_duration_seconds as avgDurationSeconds, create_time as createTime, update_time as updateTime, creator
                FROM t_data_inspection_scheduled_tasks
                ORDER BY create_time DESC
                LIMIT #{limit}
            """)
    List<DataInspectionScheduledTaskEntity> findRecentTasks(@Param("limit") Integer limit);
}