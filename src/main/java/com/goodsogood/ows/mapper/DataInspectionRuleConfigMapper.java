package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionRuleEntity;
import com.goodsogood.ows.model.db.DataInspectionRuleDetailEntity;
import com.goodsogood.ows.model.vo.DataInspectionRuleVO;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 数据体检规则配置Mapper
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Repository
@Mapper
public interface DataInspectionRuleConfigMapper {

    /**
     * 分页查询规则列表
     */
    @Select("""
        <script>
        SELECT
            id, rule_category, name as ruleName, description, status,
            create_time as createTime, update_time as updateTime,
            CASE
                WHEN rule_category = 'partyOrganization' THEN 1
                WHEN rule_category = 'partyOfficials' THEN 2
                WHEN rule_category = 'tasks' THEN 3
                WHEN rule_category = 'userInfo' THEN 4
                ELSE 1
            END as ruleType,
            CASE WHEN status = 'active' THEN true ELSE false END as isEnabled,
            1 as priority
        FROM t_data_inspection_rules
        <where>
            <if test="ruleName != null and ruleName != ''">
                AND name ILIKE CONCAT('%', #{ruleName}, '%')
            </if>
            <if test="ruleType != null">
                AND (
                    (#{ruleType} = 1 AND rule_category = 'partyOrganization') OR
                    (#{ruleType} = 2 AND rule_category = 'partyOfficials') OR
                    (#{ruleType} = 3 AND rule_category = 'tasks') OR
                    (#{ruleType} = 4 AND rule_category = 'userInfo')
                )
            </if>
            <if test="isEnabled != null">
                AND status = #{isEnabled, typeHandler=com.goodsogood.ows.util.BooleanStatusTypeHandler}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
        </script>
    """)
    List<DataInspectionRuleVO.HealthCheckRuleVO> selectRuleListPage(
            @Param("ruleName") String ruleName,
            @Param("ruleType") Integer ruleType,
            @Param("isEnabled") Boolean isEnabled,
            @Param("pageSize") Integer pageSize,
            @Param("offset") Integer offset);

    /**
     * 查询规则列表总数
     */
    @Select("""
        <script>
        SELECT COUNT(*)
        FROM t_data_inspection_rules
        <where>
            <if test="ruleName != null and ruleName != ''">
                AND name ILIKE CONCAT('%', #{ruleName}, '%')
            </if>
            <if test="ruleType != null">
                AND (
                    (#{ruleType} = 1 AND rule_category = 'partyOrganization') OR
                    (#{ruleType} = 2 AND rule_category = 'partyOfficials') OR
                    (#{ruleType} = 3 AND rule_category = 'tasks') OR
                    (#{ruleType} = 4 AND rule_category = 'userInfo')
                )
            </if>
            <if test="isEnabled != null">
                AND status = #{isEnabled, typeHandler=com.goodsogood.ows.util.BooleanStatusTypeHandler}
            </if>
        </where>
        </script>
    """)
    Long countRuleList(
            @Param("ruleName") String ruleName,
            @Param("ruleType") Integer ruleType,
            @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据ID查询规则
     */
    @Select("""
        SELECT
            id, rule_category, name as rule_name, description, status,
            create_time, update_time,
            CASE
                WHEN rule_category = 'partyOrganization' THEN 1
                WHEN rule_category = 'partyOfficials' THEN 2
                WHEN rule_category = 'tasks' THEN 3
                WHEN rule_category = 'userInfo' THEN 4
                ELSE 1
            END as rule_type,
            CASE WHEN status = 'active' THEN true ELSE false END as is_enabled,
            1 as priority
        FROM t_data_inspection_rules
        WHERE id = #{id}
    """)
    DataInspectionRuleVO.HealthCheckRuleVO selectRuleById(@Param("id") Long id);

    /**
     * 根据规则类型查询规则列表
     */
    @Select("""
        SELECT
            id, rule_category, name as rule_name, description, status,
            create_time, update_time,
            CASE
                WHEN rule_category = 'partyOrganization' THEN 1
                WHEN rule_category = 'partyOfficials' THEN 2
                WHEN rule_category = 'tasks' THEN 3
                WHEN rule_category = 'userInfo' THEN 4
                ELSE 1
            END as rule_type,
            CASE WHEN status = 'active' THEN true ELSE false END as is_enabled,
            1 as priority
        FROM t_data_inspection_rules
        WHERE (
            (#{ruleType} = 1 AND rule_category = 'partyOrganization') OR
            (#{ruleType} = 2 AND rule_category = 'partyOfficials') OR
            (#{ruleType} = 3 AND rule_category = 'tasks') OR
            (#{ruleType} = 4 AND rule_category = 'userInfo')
        )
        ORDER BY create_time DESC
    """)
    List<DataInspectionRuleVO.HealthCheckRuleVO> selectRulesByType(@Param("ruleType") Integer ruleType);

    /**
     * 插入规则
     */
    @Insert("""
        INSERT INTO t_data_inspection_rules
        (rule_category, name, description, status, create_time, update_time)
        VALUES (
            CASE
                WHEN #{ruleType} = 1 THEN 'partyOrganization'
                WHEN #{ruleType} = 2 THEN 'partyOfficials'
                WHEN #{ruleType} = 3 THEN 'tasks'
                WHEN #{ruleType} = 4 THEN 'userInfo'
                ELSE 'partyOrganization'
            END,
            #{ruleName}, #{description},
            CASE WHEN #{isEnabled} = true THEN 'active' ELSE 'inactive' END,
            NOW(), NOW()
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertRule(@Param("ruleName") String ruleName,
                   @Param("ruleType") Integer ruleType,
                   @Param("description") String description,
                   @Param("isEnabled") Boolean isEnabled,
                   @Param("id") Long id);

    /**
     * 更新规则
     */
    @Update("""
        <script>
        UPDATE t_data_inspection_rules
        <set>
            <if test="ruleName != null and ruleName != ''">
                name = #{ruleName},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="isEnabled != null">
                status = CASE WHEN #{isEnabled} = true THEN 'active' ELSE 'inactive' END,
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
        </script>
    """)
    int updateRule(@Param("id") Long id,
                   @Param("ruleName") String ruleName,
                   @Param("description") String description,
                   @Param("isEnabled") Boolean isEnabled);

    /**
     * 删除规则
     */
    @Delete("DELETE FROM t_data_inspection_rules WHERE id = #{id}")
    int deleteRule(@Param("id") Long id);

    /**
     * 批量删除规则
     */
    @Delete("""
        <script>
        DELETE FROM t_data_inspection_rules WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        </script>
    """)
    int batchDeleteRules(@Param("ids") List<Long> ids);

    /**
     * 查询规则详情列表
     */
    @Select("""
        SELECT
            id, rule_id, field_name, rule_type, rule_value, error_message, create_time
        FROM t_data_inspection_rule_details
        WHERE rule_id = #{ruleId}
        ORDER BY create_time ASC
    """)
    List<DataInspectionRuleDetailEntity> selectRuleDetails(@Param("ruleId") Long ruleId);

    /**
     * 插入规则详情
     */
    @Insert("""
        INSERT INTO t_data_inspection_rule_details
        (rule_id, field_name, rule_type, rule_value, error_message, create_time)
        VALUES (#{ruleId}, #{fieldName}, #{ruleType}, #{ruleValue}, #{errorMessage}, NOW())
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertRuleDetail(DataInspectionRuleDetailEntity ruleDetail);

    /**
     * 删除规则的所有详情
     */
    @Delete("DELETE FROM t_data_inspection_rule_details WHERE rule_id = #{ruleId}")
    int deleteRuleDetailsByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 构建规则内容JSON
     */
    @Select("""
        WITH rule_details AS (
            SELECT
                field_name,
                rule_type,
                rule_value,
                error_message
            FROM t_data_inspection_rule_details
            WHERE rule_id = #{ruleId}
        )
        SELECT COALESCE(
            JSON_OBJECT(
                'fields', JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'fieldName', field_name,
                        'ruleType', rule_type,
                        'ruleValue', rule_value,
                        'errorMessage', error_message
                    )
                )
            ),
            '{}'::json
        ) as rule_content
        FROM rule_details
    """)
    String buildRuleContentJson(@Param("ruleId") Long ruleId);

    /**
     * 启用/禁用规则
     */
    @Update("""
        UPDATE t_data_inspection_rules
        SET status = CASE WHEN #{enabled} = true THEN 'active' ELSE 'inactive' END,
            update_time = NOW()
        WHERE id = #{id}
    """)
    int toggleRuleStatus(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 检查规则名称是否已存在
     */
    @Select("""
        SELECT COUNT(*) > 0
        FROM t_data_inspection_rules
        WHERE name = #{ruleName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    """)
    boolean existsRuleName(@Param("ruleName") String ruleName, @Param("excludeId") Long excludeId);

    /**
     * 获取规则类型统计
     */
    @Select("""
        SELECT
            CASE
                WHEN rule_category = 'partyOrganization' THEN 1
                WHEN rule_category = 'partyOfficials' THEN 2
                WHEN rule_category = 'tasks' THEN 3
                WHEN rule_category = 'userInfo' THEN 4
                ELSE 1
            END as rule_type,
            rule_category,
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_count
        FROM t_data_inspection_rules
        GROUP BY rule_category
        ORDER BY rule_type
    """)
    List<RuleTypeStatistics> getRuleTypeStatistics();

    /**
     * 规则类型统计结果类
     */
    class RuleTypeStatistics {
        private Integer ruleType;
        private String ruleCategory;
        private Integer totalCount;
        private Integer activeCount;
        private Integer inactiveCount;

        // getters and setters
        public Integer getRuleType() { return ruleType; }
        public void setRuleType(Integer ruleType) { this.ruleType = ruleType; }
        public String getRuleCategory() { return ruleCategory; }
        public void setRuleCategory(String ruleCategory) { this.ruleCategory = ruleCategory; }
        public Integer getTotalCount() { return totalCount; }
        public void setTotalCount(Integer totalCount) { this.totalCount = totalCount; }
        public Integer getActiveCount() { return activeCount; }
        public void setActiveCount(Integer activeCount) { this.activeCount = activeCount; }
        public Integer getInactiveCount() { return inactiveCount; }
        public void setInactiveCount(Integer inactiveCount) { this.inactiveCount = inactiveCount; }
    }
}