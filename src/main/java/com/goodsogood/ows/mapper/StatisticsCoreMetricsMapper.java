package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.StatisticsCoreMetricsEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 统计分析核心指标数据访问接口
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Mapper
@Repository
public interface StatisticsCoreMetricsMapper extends MyMapper<StatisticsCoreMetricsEntity> {

    /**
     * 获取最新的核心指标
     *
     * @param organizationId 组织ID
     * @return 核心指标实体
     */
    @Select("""
            SELECT
                id, statistics_date as statisticsDate, total_documents as totalDocuments,
                published_documents as publishedDocuments, completion_rate as completionRate,
                total_speakers as totalSpeakers, total_departments as totalDepartments,
                average_views as averageViews, total_downloads as totalDownloads,
                user_engagement as userEngagement, organization_id as organizationId,
                region_id as regionId, create_time as createTime, update_time as updateTime
            FROM t_statistics_core_metrics
            WHERE organization_id = #{organizationId}
            ORDER BY statistics_date DESC
            LIMIT 1
            """)
    StatisticsCoreMetricsEntity findLatestByOrganization(@Param("organizationId") Long organizationId);

    /**
     * 根据日期范围查询核心指标
     *
     * @param organizationId 组织ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 核心指标列表
     */
    @Select("""
            SELECT
                id, statistics_date as statisticsDate, total_documents as totalDocuments,
                published_documents as publishedDocuments, completion_rate as completionRate,
                total_speakers as totalSpeakers, total_departments as totalDepartments,
                average_views as averageViews, total_downloads as totalDownloads,
                user_engagement as userEngagement, organization_id as organizationId,
                region_id as regionId, create_time as createTime, update_time as updateTime
            FROM t_statistics_core_metrics
            WHERE organization_id = #{organizationId}
            AND statistics_date BETWEEN #{startDate} AND #{endDate}
            ORDER BY statistics_date DESC
            """)
    List<StatisticsCoreMetricsEntity> findByDateRange(@Param("organizationId") Long organizationId,
                                                      @Param("startDate") Date startDate,
                                                      @Param("endDate") Date endDate);

    /**
     * 插入或更新核心指标
     *
     * @param entity 核心指标实体
     * @return 影响行数
     */
    @Insert("""
            INSERT INTO t_statistics_core_metrics (
                statistics_date, total_documents, published_documents, completion_rate,
                total_speakers, total_departments, average_views, total_downloads,
                user_engagement, organization_id, region_id, create_time, update_time
            ) VALUES (
                #{statisticsDate}, #{totalDocuments}, #{publishedDocuments}, #{completionRate},
                #{totalSpeakers}, #{totalDepartments}, #{averageViews}, #{totalDownloads},
                #{userEngagement}, #{organizationId}, #{regionId}, #{createTime}, #{updateTime}
            )
            ON DUPLICATE KEY UPDATE
                total_documents = VALUES(total_documents),
                published_documents = VALUES(published_documents),
                completion_rate = VALUES(completion_rate),
                total_speakers = VALUES(total_speakers),
                total_departments = VALUES(total_departments),
                average_views = VALUES(average_views),
                total_downloads = VALUES(total_downloads),
                user_engagement = VALUES(user_engagement),
                update_time = VALUES(update_time)
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertOrUpdate(StatisticsCoreMetricsEntity entity);

    /**
     * 删除过期的核心指标数据
     *
     * @param cutoffDate 截止日期
     * @return 删除记录数
     */
    @Delete("""
            DELETE FROM t_statistics_core_metrics
            WHERE statistics_date < #{cutoffDate}
            """)
    int deleteExpiredData(@Param("cutoffDate") Date cutoffDate);

    /**
     * 统计核心指标趋势
     *
     * @param organizationId 组织ID
     * @param days 天数
     * @return 趋势统计
     */
    @Select("""
            SELECT
                DATE(statistics_date) as date,
                AVG(total_documents) as avgTotalDocuments,
                AVG(published_documents) as avgPublishedDocuments,
                AVG(completion_rate) as avgCompletionRate,
                AVG(user_engagement) as avgUserEngagement
            FROM t_statistics_core_metrics
            WHERE organization_id = #{organizationId}
            AND statistics_date >= DATE_SUB(CURRENT_DATE, INTERVAL #{days} DAY)
            GROUP BY DATE(statistics_date)
            ORDER BY date DESC
            """)
    List<Map<String, Object>> getTrendStatistics(@Param("organizationId") Long organizationId,
                                                 @Param("days") Integer days);
}
