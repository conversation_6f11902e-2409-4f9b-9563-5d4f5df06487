package com.goodsogood.ows.mapper;

import tk.mybatis.mapper.common.BaseMapper;
import tk.mybatis.mapper.common.ConditionMapper;
import tk.mybatis.mapper.common.IdsMapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

/**
 * 自定义通用Mapper接口
 * 继承通用的Mapper，特别注意，该接口不能被扫描到，否则会出错
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
public interface MyMapper<T> extends BaseMapper<T>, ConditionMapper<T>, IdsMapper<T>, InsertListMapper<T> {
}
