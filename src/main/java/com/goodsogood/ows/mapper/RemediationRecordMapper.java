package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.RemediationRecordEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 整改记录数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface RemediationRecordMapper extends MyMapper<RemediationRecordEntity> {

    /**
     * 插入整改记录
     *
     * @param entity 整改记录实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_remediation_records (
                    exception_id, exception_title, remediation_action, remediation_detail, operator, 
                    start_time, complete_time, status, verification_result, verification_time, create_time, update_time
                ) VALUES (
                    #{exceptionId}, #{exceptionTitle}, #{remediationAction}, #{remediationDetail}, #{operator},
                    #{startTime}, #{completeTime}, #{status}, #{verificationResult}, #{verificationTime}, #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RemediationRecordEntity entity);

    /**
     * 根据ID删除整改记录
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_remediation_records
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新整改记录
     *
     * @param entity 整改记录实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_remediation_records
                SET
                    exception_id = #{exceptionId},
                    exception_title = #{exceptionTitle},
                    remediation_action = #{remediationAction},
                    remediation_detail = #{remediationDetail},
                    operator = #{operator},
                    start_time = #{startTime},
                    complete_time = #{completeTime},
                    status = #{status},
                    verification_result = #{verificationResult},
                    verification_time = #{verificationTime},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(RemediationRecordEntity entity);

    /**
     * 根据ID查询整改记录
     *
     * @param id 主键ID
     * @return 整改记录实体
     */
    @Select("""
                SELECT
                    id, exception_id as exceptionId, exception_title as exceptionTitle, 
                    remediation_action as remediationAction, remediation_detail as remediationDetail, operator,
                    start_time as startTime, complete_time as completeTime, status, 
                    verification_result as verificationResult, verification_time as verificationTime,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_remediation_records
                WHERE id = #{id}
            """)
    RemediationRecordEntity findById(@Param("id") Long id);

    /**
     * 分页查询整改记录列表
     *
     * @param params 查询参数
     * @return 整改记录列表
     */
    @Select("""
                <script>
                SELECT
                    id, exception_id as exceptionId, exception_title as exceptionTitle, 
                    remediation_action as remediationAction, remediation_detail as remediationDetail, operator,
                    start_time as startTime, complete_time as completeTime, status, 
                    verification_result as verificationResult, verification_time as verificationTime,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_remediation_records
                WHERE 1=1
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="operator != null and operator != ''">
                    AND operator LIKE '%' || #{operator} || '%'
                </if>
                <if test="exceptionId != null">
                    AND exception_id = #{exceptionId}
                </if>
                <if test="startTime != null and startTime != ''">
                    AND start_time &gt;= TO_TIMESTAMP(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endTime != null and endTime != ''">
                    AND start_time &lt;= TO_TIMESTAMP(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                ORDER BY create_time DESC
                </script>
            """)
    List<RemediationRecordEntity> findPage(Map<String, Object> params);

    /**
     * 查询整改记录总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_remediation_records
                WHERE 1=1
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="operator != null and operator != ''">
                    AND operator LIKE '%' || #{operator} || '%'
                </if>
                <if test="exceptionId != null">
                    AND exception_id = #{exceptionId}
                </if>
                <if test="startTime != null and startTime != ''">
                    AND start_time &gt;= TO_TIMESTAMP(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endTime != null and endTime != ''">
                    AND start_time &lt;= TO_TIMESTAMP(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 根据异常ID查询整改记录
     *
     * @param exceptionId 异常ID
     * @return 整改记录列表
     */
    @Select("""
                SELECT
                    id, exception_id as exceptionId, exception_title as exceptionTitle, 
                    remediation_action as remediationAction, remediation_detail as remediationDetail, operator,
                    start_time as startTime, complete_time as completeTime, status, 
                    verification_result as verificationResult, verification_time as verificationTime,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_remediation_records
                WHERE exception_id = #{exceptionId}
                ORDER BY create_time DESC
            """)
    List<RemediationRecordEntity> findByExceptionId(@Param("exceptionId") Long exceptionId);

    /**
     * 按状态统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count
                FROM t_data_inspection_remediation_records
                GROUP BY status
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 按操作人统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT operator, COUNT(*) as totalRecords,
                       SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedRecords,
                       SUM(CASE WHEN verification_result = 'passed' THEN 1 ELSE 0 END) as passedRecords
                FROM t_data_inspection_remediation_records
                WHERE operator IS NOT NULL
                GROUP BY operator
                ORDER BY totalRecords DESC
                LIMIT 20
            """)
    List<Map<String, Object>> countByOperator();

    /**
     * 获取最近的整改记录
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 整改记录列表
     */
    @Select("""
                SELECT
                    id, exception_id as exceptionId, exception_title as exceptionTitle, 
                    remediation_action as remediationAction, remediation_detail as remediationDetail, operator,
                    start_time as startTime, complete_time as completeTime, status, 
                    verification_result as verificationResult, verification_time as verificationTime,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_remediation_records
                WHERE create_time &gt;= CURRENT_TIMESTAMP - INTERVAL '#{days} day'
                ORDER BY create_time DESC
                LIMIT #{limit}
            """)
    List<RemediationRecordEntity> findRecentRecords(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 更新整改状态和验证结果
     *
     * @param id 整改记录ID
     * @param status 状态
     * @param verificationResult 验证结果
     * @param completeTime 完成时间
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_remediation_records
                SET status = #{status}, 
                    verification_result = #{verificationResult},
                    complete_time = #{completeTime},
                    verification_time = #{verificationTime},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateStatusAndVerification(@Param("id") Long id, 
                                   @Param("status") String status, 
                                   @Param("verificationResult") String verificationResult,
                                   @Param("completeTime") Date completeTime,
                                   @Param("verificationTime") Date verificationTime);
}