package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionResultEntity;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据体检结果数据访问接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Mapper
public interface DataInspectionResultMapper extends MyMapper<DataInspectionResultEntity> {

    /**
     * 插入体检结果
     *
     * @param entity 体检结果实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_results (
                    inspection_date, inspection_type, total_records, exception_count, 
                    exception_rate, status, duration_seconds, summary, create_time
                ) VALUES (
                    #{inspectionDate}, #{inspectionType}, #{totalRecords}, #{exceptionCount},
                    #{exceptionRate}, #{status}, #{durationSeconds}, #{summary}, #{createTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionResultEntity entity);

    /**
     * 根据ID删除体检结果
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_results
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新体检结果
     *
     * @param entity 体检结果实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_results
                SET
                    inspection_date = #{inspectionDate},
                    inspection_type = #{inspectionType},
                    total_records = #{totalRecords},
                    exception_count = #{exceptionCount},
                    exception_rate = #{exceptionRate},
                    status = #{status},
                    duration_seconds = #{durationSeconds},
                    summary = #{summary}
                WHERE id = #{id}
            """)
    int updateById(DataInspectionResultEntity entity);

    /**
     * 根据ID查询体检结果
     *
     * @param id 主键ID
     * @return 体检结果实体
     */
    @Select("""
                SELECT
                    id, inspection_date as inspectionDate, inspection_type as inspectionType,
                    total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, status, duration_seconds as durationSeconds,
                    summary, create_time as createTime
                FROM t_data_inspection_results
                WHERE id = #{id}
            """)
    DataInspectionResultEntity selectById(@Param("id") Long id);

    /**
     * 根据ID查询体检结果
     *
     * @param id 主键ID
     * @return 体检结果实体
     */
    @Select("""
                SELECT
                    id, inspection_date as inspectionDate, inspection_type as inspectionType,
                    total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, status, duration_seconds as durationSeconds,
                    summary, create_time as createTime
                FROM t_data_inspection_results
                WHERE id = #{id}
            """)
    DataInspectionResultEntity findById(@Param("id") Long id);

    /**
     * 分页查询体检结果列表
     *
     * @param params 查询参数
     * @return 体检结果列表
     */
    @Select("""
                <script>
                SELECT
                    id, inspection_date as inspectionDate, inspection_type as inspectionType,
                    total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, status, duration_seconds as durationSeconds,
                    summary, create_time as createTime
                FROM t_data_inspection_results
                WHERE 1=1
                <if test="inspectionType != null and inspectionType != ''">
                    AND inspection_type = #{inspectionType}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="startDate != null and startDate != ''">
                    AND inspection_date &gt;= TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endDate != null and endDate != ''">
                    AND inspection_date &lt;= TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                ORDER BY create_time DESC
                <if test="offset != null and pageSize != null">
                    LIMIT #{pageSize} OFFSET #{offset}
                </if>
                </script>
            """)
    List<DataInspectionResultEntity> findPage(Map<String, Object> params);

    /**
     * 查询体检结果总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_results
                WHERE 1=1
                <if test="inspectionType != null and inspectionType != ''">
                    AND inspection_type = #{inspectionType}
                </if>
                <if test="status != null and status != ''">
                    AND status = #{status}
                </if>
                <if test="startDate != null and startDate != ''">
                    AND inspection_date &gt;= TO_TIMESTAMP(#{startDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if test="endDate != null and endDate != ''">
                    AND inspection_date &lt;= TO_TIMESTAMP(#{endDate}, 'YYYY-MM-DD HH24:MI:SS')
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 更新体检结果状态和持续时间
     *
     * @param id 体检结果ID
     * @param status 状态
     * @param durationSeconds 持续时间(秒)
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_results
                SET status = #{status}, duration_seconds = #{durationSeconds}
                WHERE id = #{id}
            """)
    int updateStatusAndDuration(@Param("id") Long id, 
                               @Param("status") String status, 
                               @Param("durationSeconds") Integer durationSeconds);

    /**
     * 获取最近的体检结果
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 体检结果列表
     */
    @Select("""
                SELECT
                    id, inspection_date as inspectionDate, inspection_type as inspectionType,
                    total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, status, duration_seconds as durationSeconds,
                    summary, create_time as createTime
                FROM t_data_inspection_results
                WHERE create_time &gt;= CURRENT_TIMESTAMP - INTERVAL '#{days} day'
                ORDER BY create_time DESC
                LIMIT #{limit}
            """)
    List<DataInspectionResultEntity> findRecentResults(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 按状态统计体检结果
     *
     * @return 统计结果
     */
    @Select("""
                SELECT status, COUNT(*) as count
                FROM t_data_inspection_results
                GROUP BY status
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByStatus();

    /**
     * 按体检类型统计体检结果
     *
     * @return 统计结果
     */
    @Select("""
                SELECT inspection_type as inspectionType, COUNT(*) as count,
                       AVG(exception_rate) as avgExceptionRate,
                       AVG(duration_seconds) as avgDuration
                FROM t_data_inspection_results
                WHERE status = 'completed'
                GROUP BY inspection_type
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByInspectionType();

    /**
     * 获取体检结果概览统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT
                    COUNT(*) as totalResults,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedResults,
                    SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as runningResults,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedResults,
                    AVG(CASE WHEN status = 'completed' THEN exception_rate ELSE NULL END) as avgExceptionRate,
                    AVG(CASE WHEN status = 'completed' THEN duration_seconds ELSE NULL END) as avgDuration
                FROM t_data_inspection_results
            """)
    Map<String, Object> getOverviewStatistics();

    /**
     * 批量删除体检结果
     *
     * @param ids ID列表
     * @return 影响行数
     */
    @Delete("""
                <script>
                DELETE FROM t_data_inspection_results
                WHERE id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                </script>
            """)
    int batchDelete(@Param("ids") List<Long> ids);
}