package com.goodsogood.ows.mapper;

import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 统计分析综合数据访问接口
 * 用于复杂的统计查询和分析
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Mapper
@Repository
public interface StatisticsAnalysisMapper {

    /**
     * 获取部门统计数据
     *
     * @param organizationId 组织ID
     * @param sortBy 排序字段
     * @param order 排序方向
     * @param limit 限制数量
     * @return 部门统计列表
     */
    @Select("""
            <script>
            SELECT
                department,
                COUNT(*) as documentCount,
                ROUND(AVG(CASE WHEN status = 'published' THEN 1 ELSE 0 END) * 100, 2) as publishRate,
                SUM(view_count) as totalViews,
                ROUND(AVG(rating), 2) as avgScore
            FROM t_documents
            WHERE organization_id = #{organizationId}
            GROUP BY department
            <if test="sortBy != null and sortBy != ''">
                ORDER BY ${sortBy}
                <if test="order != null and order != ''">
                    ${order}
                </if>
            </if>
            <if test="limit != null">
                LIMIT #{limit}
            </if>
            </script>
            """)
    List<Map<String, Object>> getDepartmentStatistics(@Param("organizationId") Long organizationId,
                                                      @Param("sortBy") String sortBy,
                                                      @Param("order") String order,
                                                      @Param("limit") Integer limit);

    /**
     * 获取主讲人统计数据
     *
     * @param organizationId 组织ID
     * @param department 部门
     * @param sortBy 排序字段
     * @param order 排序方向
     * @param limit 限制数量
     * @return 主讲人统计列表
     */
    @Select("""
            <script>
            SELECT
                speaker,
                department,
                COUNT(*) as documentCount,
                SUM(view_count) as totalViews,
                ROUND(AVG(rating), 2) as avgRating
            FROM t_documents
            WHERE organization_id = #{organizationId}
            <if test="department != null and department != ''">
                AND department = #{department}
            </if>
            GROUP BY speaker, department
            <if test="sortBy != null and sortBy != ''">
                ORDER BY ${sortBy}
                <if test="order != null and order != ''">
                    ${order}
                </if>
            </if>
            <if test="limit != null">
                LIMIT #{limit}
            </if>
            </script>
            """)
    List<Map<String, Object>> getSpeakerStatistics(@Param("organizationId") Long organizationId,
                                                   @Param("department") String department,
                                                   @Param("sortBy") String sortBy,
                                                   @Param("order") String order,
                                                   @Param("limit") Integer limit);

    /**
     * 获取趋势分析数据
     *
     * @param organizationId 组织ID
     * @param trendType 趋势类型
     * @param period 周期数
     * @return 趋势分析列表
     */
    @Select("""
            <script>
            SELECT
                <choose>
                    <when test="trendType == 'daily'">
                        DATE(create_time) as date
                    </when>
                    <when test="trendType == 'weekly'">
                        DATE_FORMAT(create_time, '%Y-%u') as date
                    </when>
                    <when test="trendType == 'monthly'">
                        DATE_FORMAT(create_time, '%Y-%m') as date
                    </when>
                    <otherwise>
                        DATE_FORMAT(create_time, '%Y') as date
                    </otherwise>
                </choose>,
                COUNT(*) as documentCount,
                SUM(view_count) as viewCount,
                SUM(download_count) as downloadCount,
                COUNT(DISTINCT user_id) as userCount
            FROM t_documents
            WHERE organization_id = #{organizationId}
            <choose>
                <when test="trendType == 'daily'">
                    AND create_time >= DATE_SUB(CURRENT_DATE, INTERVAL #{period} DAY)
                </when>
                <when test="trendType == 'weekly'">
                    AND create_time >= DATE_SUB(CURRENT_DATE, INTERVAL #{period} WEEK)
                </when>
                <when test="trendType == 'monthly'">
                    AND create_time >= DATE_SUB(CURRENT_DATE, INTERVAL #{period} MONTH)
                </when>
                <otherwise>
                    AND create_time >= DATE_SUB(CURRENT_DATE, INTERVAL #{period} YEAR)
                </otherwise>
            </choose>
            GROUP BY
            <choose>
                <when test="trendType == 'daily'">
                    DATE(create_time)
                </when>
                <when test="trendType == 'weekly'">
                    DATE_FORMAT(create_time, '%Y-%u')
                </when>
                <when test="trendType == 'monthly'">
                    DATE_FORMAT(create_time, '%Y-%m')
                </when>
                <otherwise>
                    DATE_FORMAT(create_time, '%Y')
                </otherwise>
            </choose>
            ORDER BY date DESC
            </script>
            """)
    List<Map<String, Object>> getTrendAnalysis(@Param("organizationId") Long organizationId,
                                              @Param("trendType") String trendType,
                                              @Param("period") Integer period);

    /**
     * 获取用户行为分析数据
     *
     * @param organizationId 组织ID
     * @return 用户行为分析数据
     */
    @Select("""
            SELECT
                HOUR(access_time) as hour,
                COUNT(*) as accessCount,
                AVG(reading_duration) as avgReadingTime,
                device_type,
                source_type
            FROM t_user_access_logs
            WHERE organization_id = #{organizationId}
            AND access_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY HOUR(access_time), device_type, source_type
            ORDER BY hour
            """)
    List<Map<String, Object>> getUserBehaviorAnalysis(@Param("organizationId") Long organizationId);

    /**
     * 获取实时统计数据
     *
     * @param organizationId 组织ID
     * @return 实时统计数据
     */
    @Select("""
            SELECT
                (SELECT COUNT(DISTINCT user_id) FROM t_user_sessions WHERE organization_id = #{organizationId} AND is_active = 1) as currentOnlineUsers,
                (SELECT COUNT(*) FROM t_user_access_logs WHERE organization_id = #{organizationId} AND DATE(access_time) = CURRENT_DATE) as todayViews,
                (SELECT COUNT(*) FROM t_download_logs WHERE organization_id = #{organizationId} AND DATE(download_time) = CURRENT_DATE) as todayDownloads
            """)
    Map<String, Object> getRealTimeStatistics(@Param("organizationId") Long organizationId);

    /**
     * 获取最近活动
     *
     * @param organizationId 组织ID
     * @param limit 限制数量
     * @return 最近活动列表
     */
    @Select("""
            SELECT
                activity_time as time,
                activity_description as activity,
                activity_type as type
            FROM t_activity_logs
            WHERE organization_id = #{organizationId}
            ORDER BY activity_time DESC
            LIMIT #{limit}
            """)
    List<Map<String, Object>> getRecentActivities(@Param("organizationId") Long organizationId,
                                                  @Param("limit") Integer limit);

    /**
     * 自定义统计查询
     *
     * @param params 查询参数
     * @return 查询结果
     */
    @Select("""
            <script>
            SELECT *
            FROM t_documents d
            WHERE d.organization_id = #{organizationId}
            <if test="startDate != null and startDate != ''">
                AND d.create_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND d.create_time <= #{endDate}
            </if>
            <if test="departments != null and departments.size() > 0">
                AND d.department IN
                <foreach collection="departments" item="dept" open="(" close=")" separator=",">
                    #{dept}
                </foreach>
            </if>
            <if test="speakers != null and speakers.size() > 0">
                AND d.speaker IN
                <foreach collection="speakers" item="speaker" open="(" close=")" separator=",">
                    #{speaker}
                </foreach>
            </if>
            ORDER BY d.create_time DESC
            </script>
            """)
    List<Map<String, Object>> customQuery(Map<String, Object> params);
}
