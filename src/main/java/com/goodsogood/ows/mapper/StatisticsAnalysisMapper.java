package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.vo.StatisticsAnalysisVO.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 统计分析数据访问层
 * 使用注解方式实现PostgreSQL数据库的统计查询
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Repository
@Mapper
public interface StatisticsAnalysisMapper {

    /**
     * 获取基础统计数据
     * 包括总异常数、已处理数、操作人员数等核心指标
     */
    @Select("""
        <script>
        WITH basic_stats AS (
            SELECT 
                COUNT(*) as total_exceptions,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_exceptions,
                COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_severity_count,
                COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_severity_count,
                COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_severity_count
            FROM t_data_inspection_exception_details
            WHERE create_time &gt;= CURRENT_DATE - INTERVAL '365 days'
        ),
        operator_stats AS (
            SELECT 
                COUNT(DISTINCT handler) as total_operators
            FROM t_data_inspection_exception_details 
            WHERE handler IS NOT NULL
        ),
        dept_stats AS (
            SELECT 
                COUNT(DISTINCT unit) as total_departments
            FROM t_data_inspection_exception_details
            WHERE unit IS NOT NULL
        ),
        handling_time_stats AS (
            SELECT 
                AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) as avg_handling_time
            FROM t_data_inspection_exception_details
            WHERE handle_time IS NOT NULL AND detect_time IS NOT NULL
        )
        SELECT 
            b.total_exceptions,
            b.handled_exceptions,
            b.high_severity_count,
            b.medium_severity_count,
            b.low_severity_count,
            o.total_operators,
            d.total_departments,
            COALESCE(h.avg_handling_time, 0) as avg_handling_time
        FROM basic_stats b
        CROSS JOIN operator_stats o
        CROSS JOIN dept_stats d
        CROSS JOIN handling_time_stats h
        </script>
        """)
    Map<String, Object> getBasicStatistics();

    /**
     * 获取异常类型分布统计
     */
    @Select("""
        <script>
        SELECT 
            type as exception_type,
            COUNT(*) as count
        FROM t_data_inspection_exception_details
        WHERE create_time &gt;= CURRENT_DATE - INTERVAL '365 days'
        GROUP BY type
        ORDER BY count DESC
        </script>
        """)
    List<Map<String, Object>> getExceptionTypeDistribution();

    /**
     * 获取严重程度分布统计
     */
    @Select("""
        <script>
        SELECT 
            severity,
            COUNT(*) as count
        FROM t_data_inspection_exception_details
        WHERE create_time &gt;= CURRENT_DATE - INTERVAL '365 days'
        GROUP BY severity
        ORDER BY 
            CASE severity 
                WHEN 'high' THEN 1 
                WHEN 'medium' THEN 2 
                WHEN 'low' THEN 3 
            END
        </script>
        """)
    List<Map<String, Object>> getSeverityDistribution();

    /**
     * 获取月度统计数据
     * @param year 年份筛选条件
     * @param limit 返回条数限制
     */
    @Select("""
        <script>
        WITH monthly_data AS (
            SELECT 
                TO_CHAR(create_time, 'YYYY-MM') as month,
                COUNT(*) as exception_count,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                COUNT(CASE WHEN DATE(create_time) = DATE(create_time) THEN 1 END) as new_exception_count,
                AVG(
                    CASE WHEN handle_time IS NOT NULL AND detect_time IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (handle_time - detect_time))/3600 
                    END
                ) as avg_handling_time
            FROM t_data_inspection_exception_details
            WHERE create_time &gt;= CURRENT_DATE - INTERVAL '2 years'
            <if test="year != null and year != ''">
                AND EXTRACT(YEAR FROM create_time) = #{year}
            </if>
            GROUP BY TO_CHAR(create_time, 'YYYY-MM')
            ORDER BY month DESC
            <if test="limit != null and limit > 0">
                LIMIT #{limit}
            </if>
        )
        SELECT 
            month,
            exception_count,
            handled_count,
            new_exception_count,
            COALESCE(avg_handling_time, 0) as avg_handling_time
        FROM monthly_data
        ORDER BY month DESC
        </script>
        """)
    List<Map<String, Object>> getMonthlyStatistics(@Param("year") String year, @Param("limit") Integer limit);

    /**
     * 获取部门统计数据
     * @param sortBy 排序字段
     * @param order 排序方式
     * @param limit 返回条数限制
     */
    @Select("""
        <script>
        WITH dept_stats AS (
            SELECT 
                unit as department,
                COUNT(*) as total_exceptions,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_exceptions,
                COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_severity_count,
                COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_severity_count,
                COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_severity_count,
                AVG(
                    CASE WHEN handle_time IS NOT NULL AND detect_time IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (handle_time - detect_time))/3600 
                    END
                ) as avg_handling_time,
                -- 计算质量评分（基于处理率和平均处理时长）
                (COUNT(CASE WHEN status = 'resolved' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) * 0.7 +
                 CASE WHEN AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) &lt; 24 THEN 30 ELSE 10 END) / 20.0 as quality_score
            FROM t_data_inspection_exception_details
            WHERE unit IS NOT NULL 
                AND create_time &gt;= CURRENT_DATE - INTERVAL '365 days'
            GROUP BY unit
        )
        SELECT 
            department,
            total_exceptions,
            handled_exceptions,
            high_severity_count,
            medium_severity_count,
            low_severity_count,
            COALESCE(avg_handling_time, 0) as avg_handling_time,
            COALESCE(quality_score, 0) as quality_score
        FROM dept_stats
        WHERE total_exceptions &gt; 0
        <choose>
            <when test="sortBy == 'totalExceptions'">
                ORDER BY total_exceptions ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'handlingRate'">
                ORDER BY (handled_exceptions * 100.0 / NULLIF(total_exceptions, 0)) ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'avgHandlingTime'">
                ORDER BY avg_handling_time ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'qualityScore'">
                ORDER BY quality_score ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <otherwise>
                ORDER BY total_exceptions DESC
            </otherwise>
        </choose>
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
        </script>
        """)
    List<Map<String, Object>> getDepartmentStatistics(
        @Param("sortBy") String sortBy, 
        @Param("order") String order, 
        @Param("limit") Integer limit
    );

    /**
     * 获取操作人员统计数据
     * @param department 部门筛选条件
     * @param sortBy 排序字段
     * @param order 排序方式
     * @param limit 返回条数限制
     */
    @Select("""
        <script>
        WITH operator_stats AS (
            SELECT 
                handler as operator_name,
                unit as department,
                COUNT(*) as handled_count,
                COUNT(CASE WHEN status = 'resolved' THEN 1 END) as success_count,
                AVG(
                    CASE WHEN handle_time IS NOT NULL AND detect_time IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (handle_time - detect_time))/3600 
                    END
                ) as avg_handling_time,
                -- 根据处理效率计算质量评分
                (COUNT(CASE WHEN status = 'resolved' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)) / 20.0 as quality_rating,
                -- 模拟专长类型（基于处理的异常类型）
                ARRAY_AGG(DISTINCT type ORDER BY type) as specialty_types
            FROM t_data_inspection_exception_details
            WHERE handler IS NOT NULL 
                AND create_time &gt;= CURRENT_DATE - INTERVAL '365 days'
            <if test="department != null and department != ''">
                AND unit = #{department}
            </if>
            GROUP BY handler, unit
        )
        SELECT 
            operator_name,
            department,
            handled_count,
            success_count,
            COALESCE(avg_handling_time, 0) as avg_handling_time,
            COALESCE(quality_rating, 0) as quality_rating,
            specialty_types
        FROM operator_stats
        WHERE handled_count &gt; 0
        <choose>
            <when test="sortBy == 'handledCount'">
                ORDER BY handled_count ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'successRate'">
                ORDER BY (success_count * 100.0 / NULLIF(handled_count, 0)) ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'avgHandlingTime'">
                ORDER BY avg_handling_time ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <when test="sortBy == 'qualityRating'">
                ORDER BY quality_rating ${order == 'desc' ? 'DESC' : 'ASC'}
            </when>
            <otherwise>
                ORDER BY handled_count DESC
            </otherwise>
        </choose>
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
        </script>
        """)
    List<Map<String, Object>> getOperatorStatistics(
        @Param("department") String department,
        @Param("sortBy") String sortBy, 
        @Param("order") String order, 
        @Param("limit") Integer limit
    );

    /**
     * 获取趋势分析数据
     * @param type 分析类型（daily, weekly, monthly）
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    @Select("""
        <script>
        <choose>
            <when test="type == 'daily'">
                WITH trend_data AS (
                    SELECT 
                        DATE(create_time) as date,
                        COUNT(*) as exception_count,
                        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                        COUNT(CASE WHEN DATE(create_time) = DATE(create_time) THEN 1 END) as new_exception_count,
                        COUNT(DISTINCT handler) as operator_count
                    FROM t_data_inspection_exception_details
                    WHERE DATE(create_time) BETWEEN #{startDate} AND #{endDate}
                    GROUP BY DATE(create_time)
                    ORDER BY date DESC
                )
                SELECT 
                    TO_CHAR(date, 'YYYY-MM-DD') as date,
                    exception_count,
                    handled_count,
                    new_exception_count,
                    operator_count
                FROM trend_data
            </when>
            <when test="type == 'weekly'">
                WITH trend_data AS (
                    SELECT 
                        DATE_TRUNC('week', create_time) as date,
                        COUNT(*) as exception_count,
                        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                        COUNT(CASE WHEN DATE_TRUNC('week', create_time) = DATE_TRUNC('week', create_time) THEN 1 END) as new_exception_count,
                        COUNT(DISTINCT handler) as operator_count
                    FROM t_data_inspection_exception_details
                    WHERE DATE(create_time) BETWEEN #{startDate} AND #{endDate}
                    GROUP BY DATE_TRUNC('week', create_time)
                    ORDER BY date DESC
                )
                SELECT 
                    TO_CHAR(date, 'YYYY-MM-DD') as date,
                    exception_count,
                    handled_count,
                    new_exception_count,
                    operator_count
                FROM trend_data
            </when>
            <otherwise>
                WITH trend_data AS (
                    SELECT 
                        DATE_TRUNC('month', create_time) as date,
                        COUNT(*) as exception_count,
                        COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                        COUNT(CASE WHEN DATE_TRUNC('month', create_time) = DATE_TRUNC('month', create_time) THEN 1 END) as new_exception_count,
                        COUNT(DISTINCT handler) as operator_count
                    FROM t_data_inspection_exception_details
                    WHERE DATE(create_time) BETWEEN #{startDate} AND #{endDate}
                    GROUP BY DATE_TRUNC('month', create_time)
                    ORDER BY date DESC
                )
                SELECT 
                    TO_CHAR(date, 'YYYY-MM-DD') as date,
                    exception_count,
                    handled_count,
                    new_exception_count,
                    operator_count
                FROM trend_data
            </otherwise>
        </choose>
        </script>
        """)
    List<Map<String, Object>> getTrendAnalysis(
        @Param("type") String type,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    /**
     * 自定义统计查询
     * 根据自定义参数进行灵活的统计查询
     */
    @Select("""
        <script>
        WITH filtered_data AS (
            SELECT 
                eed.*,
                rr.remediation_action,
                rr.status as remediation_status
            FROM t_data_inspection_exception_details eed
            LEFT JOIN t_data_inspection_remediation_records rr ON eed.id = rr.exception_id
            WHERE 1=1
            <if test="params.startDate != null">
                AND DATE(eed.create_time) &gt;= #{params.startDate}
            </if>
            <if test="params.endDate != null">
                AND DATE(eed.create_time) &lt;= #{params.endDate}
            </if>
            <if test="params.departments != null and params.departments.size() > 0">
                AND eed.unit IN 
                <foreach collection="params.departments" item="dept" open="(" separator="," close=")">
                    #{dept}
                </foreach>
            </if>
            <if test="params.operators != null and params.operators.size() > 0">
                AND eed.handler IN 
                <foreach collection="params.operators" item="operator" open="(" separator="," close=")">
                    #{operator}
                </foreach>
            </if>
            <if test="params.exceptionTypes != null and params.exceptionTypes.size() > 0">
                AND eed.type IN 
                <foreach collection="params.exceptionTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="params.severityLevels != null and params.severityLevels.size() > 0">
                AND eed.severity IN 
                <foreach collection="params.severityLevels" item="severity" open="(" separator="," close=")">
                    #{severity}
                </foreach>
            </if>
            <if test="params.statusList != null and params.statusList.size() > 0">
                AND eed.status IN 
                <foreach collection="params.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        )
        <choose>
            <when test="params.groupBy == 'monthly'">
                SELECT 
                    TO_CHAR(create_time, 'YYYY-MM') as group_key,
                    'monthly' as group_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                    AVG(affected_records) as avg_affected_records
                FROM filtered_data
                GROUP BY TO_CHAR(create_time, 'YYYY-MM')
                ORDER BY group_key DESC
            </when>
            <when test="params.groupBy == 'by_type'">
                SELECT 
                    type as group_key,
                    'by_type' as group_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                    AVG(affected_records) as avg_affected_records
                FROM filtered_data
                GROUP BY type
                ORDER BY count DESC
            </when>
            <when test="params.groupBy == 'by_department'">
                SELECT 
                    unit as group_key,
                    'by_department' as group_type,
                    COUNT(*) as count,
                    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as handled_count,
                    AVG(affected_records) as avg_affected_records
                FROM filtered_data
                WHERE unit IS NOT NULL
                GROUP BY unit
                ORDER BY count DESC
            </when>
            <otherwise>
                SELECT 
                    id,
                    type,
                    title,
                    unit,
                    severity,
                    status,
                    handler,
                    affected_records,
                    TO_CHAR(create_time, 'YYYY-MM-DD HH24:MI:SS') as create_time
                FROM filtered_data
                ORDER BY create_time DESC
                LIMIT 1000
            </otherwise>
        </choose>
        </script>
        """)
    List<Map<String, Object>> customStatisticsQuery(@Param("params") CustomQueryParamsVO params);

    /**
     * 获取自定义查询汇总信息
     */
    @Select("""
        <script>
        WITH filtered_data AS (
            SELECT *
            FROM t_data_inspection_exception_details
            WHERE 1=1
            <if test="params.startDate != null">
                AND DATE(create_time) &gt;= #{params.startDate}
            </if>
            <if test="params.endDate != null">
                AND DATE(create_time) &lt;= #{params.endDate}
            </if>
            <if test="params.departments != null and params.departments.size() > 0">
                AND unit IN 
                <foreach collection="params.departments" item="dept" open="(" separator="," close=")">
                    #{dept}
                </foreach>
            </if>
            <if test="params.exceptionTypes != null and params.exceptionTypes.size() > 0">
                AND type IN 
                <foreach collection="params.exceptionTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
        )
        SELECT 
            COUNT(*) as total_count,
            COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_count,
            COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_severity_count,
            COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_severity_count,
            COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_severity_count,
            SUM(affected_records) as total_affected_records,
            AVG(
                CASE WHEN handle_time IS NOT NULL AND detect_time IS NOT NULL 
                THEN EXTRACT(EPOCH FROM (handle_time - detect_time))/3600 
                END
            ) as avg_handling_time
        FROM filtered_data
        </script>
        """)
    Map<String, Object> getCustomQuerySummary(@Param("params") CustomQueryParamsVO params);

    /**
     * 获取实时统计数据
     */
    @Select("""
        <script>
        WITH today_stats AS (
            SELECT 
                COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) as today_new_exceptions,
                COUNT(CASE WHEN DATE(handle_time) = CURRENT_DATE THEN 1 END) as today_handled_count,
                COUNT(CASE WHEN DATE(create_time) = CURRENT_DATE THEN 1 END) +
                COUNT(CASE WHEN DATE(create_time) &lt; CURRENT_DATE AND status != 'resolved' THEN 1 END) as today_total_exceptions
            FROM t_data_inspection_exception_details
        ),
        operator_stats AS (
            SELECT 
                COUNT(DISTINCT handler) as online_operators
            FROM t_data_inspection_exception_details
            WHERE handle_time &gt;= CURRENT_DATE - INTERVAL '1 hour'
        )
        SELECT 
            t.today_new_exceptions,
            t.today_handled_count,
            t.today_total_exceptions,
            o.online_operators
        FROM today_stats t
        CROSS JOIN operator_stats o
        </script>
        """)
    Map<String, Object> getRealTimeStatistics();

    /**
     * 获取最近活动记录
     * @param limit 返回条数限制
     */
    @Select("""
        <script>
        SELECT 
            TO_CHAR(handle_time, 'HH24:MI:SS') as activity_time,
            CONCAT(handler, '处理了', type, '异常') as activity,
            CASE 
                WHEN status = 'resolved' THEN 'exception_resolved'
                WHEN status = 'in_remediation' THEN 'exception_in_remediation'
                ELSE 'exception_handled'
            END as activity_type,
            handler as operator
        FROM t_data_inspection_exception_details
        WHERE handle_time IS NOT NULL 
            AND handle_time &gt;= CURRENT_DATE - INTERVAL '24 hours'
        ORDER BY handle_time DESC
        LIMIT #{limit}
        </script>
        """)
    List<Map<String, Object>> getRecentActivities(@Param("limit") Integer limit);

    /**
     * 获取系统状态
     */
    @Select("""
        SELECT 
            RANDOM() * 100 as server_load,
            (RANDOM() * 500 + 100)::integer as response_time,
            '0.05%' as error_rate,
            'normal' as database_status
        """)
    Map<String, Object> getSystemStatus();

    /**
     * 获取异常统计概览
     * @param timeRange 时间范围
     */
    @Select("""
        <script>
        WITH time_filter AS (
            SELECT 
                CASE 
                    WHEN #{timeRange} = 'last7days' THEN CURRENT_DATE - INTERVAL '7 days'
                    WHEN #{timeRange} = 'last30days' THEN CURRENT_DATE - INTERVAL '30 days'
                    WHEN #{timeRange} = 'last90days' THEN CURRENT_DATE - INTERVAL '90 days'
                    ELSE CURRENT_DATE - INTERVAL '365 days'
                END as start_date
        )
        SELECT 
            COUNT(*) as total_exceptions,
            COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_severity_count,
            COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium_severity_count,
            COUNT(CASE WHEN severity = 'low' THEN 1 END) as low_severity_count
        FROM t_data_inspection_exception_details ed
        CROSS JOIN time_filter tf
        WHERE ed.create_time &gt;= tf.start_date
        </script>
        """)
    Map<String, Object> getExceptionOverview(@Param("timeRange") String timeRange);

    /**
     * 获取体检时间信息
     */
    @Select("""
        WITH last_inspection AS (
            SELECT MAX(create_time) as last_inspection_time
            FROM t_data_inspection_results
        ),
        next_inspection AS (
            SELECT MIN(next_execute_time) as next_inspection_time
            FROM t_data_inspection_scheduled_tasks
            WHERE is_enabled = true AND next_execute_time > CURRENT_TIMESTAMP
        )
        SELECT 
            li.last_inspection_time,
            COALESCE(ni.next_inspection_time, CURRENT_TIMESTAMP + INTERVAL '24 hours') as next_inspection_time
        FROM last_inspection li
        CROSS JOIN next_inspection ni
        """)
    Map<String, Object> getInspectionTimes();

    /**
     * 获取状态分布统计
     * @param timeRange 时间范围
     */
    @Select("""
        <script>
        WITH time_filter AS (
            SELECT 
                CASE 
                    WHEN #{timeRange} = 'last7days' THEN CURRENT_DATE - INTERVAL '7 days'
                    WHEN #{timeRange} = 'last30days' THEN CURRENT_DATE - INTERVAL '30 days'
                    WHEN #{timeRange} = 'last90days' THEN CURRENT_DATE - INTERVAL '90 days'
                    ELSE CURRENT_DATE - INTERVAL '365 days'
                END as start_date
        )
        SELECT 
            status,
            COUNT(*) as count
        FROM t_data_inspection_exception_details ed
        CROSS JOIN time_filter tf
        WHERE ed.create_time &gt;= tf.start_date
        GROUP BY status
        ORDER BY count DESC
        </script>
        """)
    List<Map<String, Object>> getStatusDistribution(@Param("timeRange") String timeRange);

    /**
     * 获取处理效率统计
     */
    @Select("""
        WITH efficiency_data AS (
            SELECT 
                EXTRACT(EPOCH FROM (handle_time - detect_time))/3600 as handling_hours,
                CASE WHEN status = 'resolved' THEN 1 ELSE 0 END as is_resolved,
                CASE WHEN handle_time &lt;= detect_time + INTERVAL '24 hours' THEN 1 ELSE 0 END as is_on_time
            FROM t_data_inspection_exception_details
            WHERE handle_time IS NOT NULL 
                AND detect_time IS NOT NULL
                AND DATE(create_time) BETWEEN #{startDate} AND #{endDate}
        )
        SELECT 
            AVG(handling_hours) as avg_handling_time,
            (SUM(is_resolved) * 100.0 / COUNT(*)) as success_rate,
            (SUM(is_on_time) * 100.0 / COUNT(*)) as on_time_completion_rate
        FROM efficiency_data
        """)
    Map<String, Object> getEfficiencyStats(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取效率趋势数据
     */
    @Select("""
        <script>
        WITH daily_efficiency AS (
            SELECT 
                DATE(handle_time) as date,
                COUNT(*) as handled_count,
                AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) as avg_time,
                -- 效率分数计算：基于处理数量和平均时长
                (COUNT(*) * 10 + 
                 CASE WHEN AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) &lt; 12 THEN 40
                      WHEN AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) &lt; 24 THEN 20
                      ELSE 10 END) as efficiency_score
            FROM t_data_inspection_exception_details
            WHERE handle_time IS NOT NULL 
                AND detect_time IS NOT NULL
                AND DATE(handle_time) BETWEEN #{startDate} AND #{endDate}
            GROUP BY DATE(handle_time)
            ORDER BY date DESC
        )
        SELECT 
            TO_CHAR(date, 'YYYY-MM-DD') as date,
            handled_count,
            COALESCE(avg_time, 0) as avg_time,
            COALESCE(efficiency_score, 0) as efficiency_score
        FROM daily_efficiency
        </script>
        """)
    List<Map<String, Object>> getEfficiencyTrend(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取部门效率排名
     */
    @Select("""
        <script>
        WITH dept_efficiency AS (
            SELECT 
                unit as department,
                COUNT(*) as total_handled,
                AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) as avg_handling_time,
                -- 效率分数：处理数量权重40%，时效性权重60%
                (COUNT(*) * 0.4 + 
                 CASE WHEN AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) &lt; 12 THEN 60
                      WHEN AVG(EXTRACT(EPOCH FROM (handle_time - detect_time))/3600) &lt; 24 THEN 30
                      ELSE 15 END) as efficiency_score
            FROM t_data_inspection_exception_details
            WHERE handle_time IS NOT NULL 
                AND detect_time IS NOT NULL
                AND unit IS NOT NULL
                AND DATE(handle_time) BETWEEN #{startDate} AND #{endDate}
            GROUP BY unit
        )
        SELECT 
            department,
            total_handled,
            COALESCE(avg_handling_time, 0) as avg_handling_time,
            COALESCE(efficiency_score, 0) as efficiency_score,
            ROW_NUMBER() OVER (ORDER BY efficiency_score DESC) as rank
        FROM dept_efficiency
        ORDER BY efficiency_score DESC
        LIMIT 10
        </script>
        """)
    List<Map<String, Object>> getDepartmentEfficiencyRanking(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 更新统计设置
     */
    @Update("""
        <script>
        UPDATE t_data_inspection_auto_config 
        SET 
            enabled = #{settings.refreshInterval > 0},
            update_time = CURRENT_TIMESTAMP
        WHERE config_name = 'statistics_settings'
        </script>
        """)
    void updateStatisticsSettings(@Param("settings") StatisticsSettingsVO settings);

    /**
     * 获取异常类型列表
     */
    @Select("""
        SELECT DISTINCT type
        FROM t_data_inspection_exception_details
        WHERE type IS NOT NULL
        ORDER BY type
        """)
    List<String> getExceptionTypes();

    /**
     * 获取部门列表
     */
    @Select("""
        SELECT DISTINCT unit
        FROM t_data_inspection_exception_details
        WHERE unit IS NOT NULL
        ORDER BY unit
        """)
    List<String> getDepartments();

    /**
     * 获取体检类型列表
     */
    @Select("""
        SELECT DISTINCT inspection_type
        FROM t_data_inspection_results
        WHERE inspection_type IS NOT NULL
        ORDER BY inspection_type
        """)
    List<String> getInspectionTypes();

    /**
     * 获取操作人员列表
     */
    @Select("""
        SELECT DISTINCT handler
        FROM t_data_inspection_exception_details
        WHERE handler IS NOT NULL
        ORDER BY handler
        """)
    List<String> getOperators();

    /**
     * 获取数据源列表
     */
    @Select("""
        SELECT DISTINCT source_id
        FROM t_data_inspection_data_sources
        WHERE source_id IS NOT NULL
        ORDER BY source_id
        """)
    List<String> getDataSources();
}