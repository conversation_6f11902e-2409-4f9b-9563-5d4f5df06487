package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.HonorAchievementEntity;
import org.apache.ibatis.annotations.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 荣誉成果数据访问层
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface HonorAchievementMapper {

    /**
     * 插入荣誉成果
     *
     * @param entity 荣誉成果实体
     * @return 影响行数
     */
    @Insert("""
        INSERT INTO t_honor_achievement (
            name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time
        ) VALUES (
            #{name}, #{type}, #{level}, #{issueOrg}, #{achieveTime}, #{coverUrl}, #{summary}, #{description}, #{attachmentsJson},
            #{downloadCount}, #{viewCount}, #{isPublic}, #{allowDownload}, #{status}, #{organizationId}, #{regionId},
            #{createUser}, #{createUserName}, #{createTime}
        )
    """)
    int insert(HonorAchievementEntity entity);

    /**
     * 根据ID删除荣誉成果（逻辑删除）
     *
     * @param id 主键ID
     * @param updateUser 更新人ID
     * @param updateUserName 更新人姓名
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor_achievement
        SET status = 0, update_user = #{updateUser}, update_user_name = #{updateUserName}, update_time = NOW()
        WHERE id = #{id}
    """)
    int deleteById(@Param("id") Long id, @Param("updateUser") Long updateUser, @Param("updateUserName") String updateUserName);

    /**
     * 更新荣誉成果
     *
     * @param entity 荣誉成果实体
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor_achievement
        SET
            name = #{name},
            type = #{type},
            level = #{level},
            issue_org = #{issueOrg},
            achieve_time = #{achieveTime},
            cover_url = #{coverUrl},
            summary = #{summary},
            description = #{description},
            attachments_json = #{attachmentsJson},
            is_public = #{isPublic},
            allow_download = #{allowDownload},
            status = #{status},
            update_user = #{updateUser},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE id = #{id}
    """)
    int updateById(HonorAchievementEntity entity);

    /**
     * 根据ID查询荣誉成果
     *
     * @param id 主键ID
     * @return 荣誉成果实体
     */
    @Select("""
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
        WHERE id = #{id} AND status != 0
    """)
    HonorAchievementEntity findById(@Param("id") Long id);

    /**
     * 分页查询荣誉成果列表
     *
     * @param params 查询参数
     * @return 荣誉成果列表
     */
    @Select("""
        <script>
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
          where   status != 0
            <if test='name != null'>AND name  LIKE '%' || #{name} || '%'</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='level != null'>AND level = #{level}</if>
            <if test='issueOrg != null'>AND issue_org  LIKE '%' || #{issueOrg} || '%' </if>
            <if test='organizationId != null'>AND organization_id = #{organizationId}</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='status != null'>AND status = #{status}</if>
            <if test='startTime != null'>AND achieve_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND achieve_time <![CDATA[<= ]]>  #{endTime}</if>
        ORDER BY create_time DESC
        <if test='offset != null and pageSize != null'>LIMIT #{offset}, #{pageSize}</if>
        </script>
    """)
    List<HonorAchievementEntity> findByPage(Map<String, Object> params);

    /**
     * 查询荣誉成果总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
       <script>
        SELECT COUNT(1)
        FROM t_honor_achievement
            where status != 0
            <if test='name != null'>AND name LIKE '%' || #{name} || '%'</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='level != null'>AND level = #{level}</if>
            <if test='issueOrg != null'>AND issue_org LIKE '%' || #{issueOrg} || '%'</if>
            <if test='organizationId != null'>AND organization_id = #{organizationId}</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='status != null'>AND status = #{status}</if>
            <if test='startTime != null'>AND achieve_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND achieve_time <![CDATA[<= ]]> #{endTime}</if>
       </script>
    """)
    int countByParams(Map<String, Object> params);

    /**
     * 查询公开的荣誉成果列表（用于共享页面）
     *
     * @param params 查询参数
     * @return 荣誉成果列表
     */
    @Select("""
       <script>
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
           where  status = 1 AND is_public = 1
            <if test='name != null'>AND name LIKE '%' || #{name} || '%'</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='level != null'>AND level = #{level}</if>
            <if test='issueOrg != null'>AND issue_org LIKE '%' || #{issueOrg} || '%'</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='startTime != null'>AND achieve_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND achieve_time <![CDATA[<= ]]> #{endTime}</if>
        <if test='offset != null and pageSize != null'>LIMIT #{offset}, #{pageSize}</if>
       </script>
    """)
    List<HonorAchievementEntity> findPublicByPage(Map<String, Object> params);

    /**
     * 查询公开的荣誉成果总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
         <script>
        SELECT COUNT(1)
        FROM t_honor_achievement
           where status = 1 AND is_public = 1
            <if test='name != null'>AND name LIKE '%' || #{name} || '%'</if>
            <if test='type != null'>AND type = #{type}</if>
            <if test='level != null'>AND level = #{level}</if>
            <if test='issueOrg != null'>AND issue_org LIKE '%' || #{issueOrg} || '%'</if>
            <if test='regionId != null'>AND region_id = #{regionId}</if>
            <if test='startTime != null'>AND achieve_time <![CDATA[ >=  ]]> #{startTime}</if>
            <if test='endTime != null'>AND achieve_time   <![CDATA[ <=  ]]>  #{endTime}</if>
       </script>
    """)
    int countPublicByParams(Map<String, Object> params);

    /**
     * 增加查看次数
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor_achievement
        SET view_count = view_count + 1
        WHERE id = #{id}
    """)
    int increaseViewCount(@Param("id") Long id);

    /**
     * 增加下载次数
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Update("""
        UPDATE t_honor_achievement
        SET download_count = download_count + 1
        WHERE id = #{id}
    """)
    int increaseDownloadCount(@Param("id") Long id);

    /**
     * 批量删除荣誉成果（逻辑删除）
     *
     * @param ids 主键ID列表
     * @param updateUser 更新人ID
     * @param updateUserName 更新人姓名
     * @return 影响行数
     */
    @Update("""
        <script>
        UPDATE t_honor_achievement
        SET status = 0, update_user = #{updateUser}, update_user_name = #{updateUserName}, update_time = NOW()
        WHERE id IN
        <foreach item='id' collection='ids' open='(' separator=',' close=')'>
            #{id}
        </foreach>
        </script>
    """)
    int batchDeleteByIds(@Param("ids") List<Long> ids, @Param("updateUser") Long updateUser, @Param("updateUserName") String updateUserName);

    /**
     * 根据组织ID查询荣誉成果列表
     *
     * @param organizationId 组织ID
     * @param status 状态
     * @return 荣誉成果列表
     */
    @Select("""
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
        WHERE organization_id = #{organizationId}
        AND status = #{status}
        ORDER BY create_time DESC
    """)
    List<HonorAchievementEntity> findByOrganizationId(@Param("organizationId") Long organizationId, @Param("status") Integer status);

    /**
     * 根据类型统计荣誉成果数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    @Select("""
        SELECT
            type,
            COUNT(1) as count
        FROM t_honor_achievement
        WHERE status = 1
        AND organization_id = #{organizationId}
        GROUP BY type
        ORDER BY type
    """)
    List<Map<String, Object>> countByType(@Param("organizationId") Long organizationId);

    /**
     * 根据级别统计荣誉成果数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    @Select("""
        SELECT
            level,
            COUNT(1) as count
        FROM t_honor_achievement
        WHERE status = 1
        AND organization_id = #{organizationId}
        GROUP BY level
        ORDER BY level
    """)
    List<Map<String, Object>> countByLevel(@Param("organizationId") Long organizationId);

    /**
     * 查询最新的荣誉成果
     *
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉成果列表
     */
    @Select("""
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
        WHERE status = 1 AND is_public = 1
        AND organization_id = #{organizationId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    """)
    List<HonorAchievementEntity> findLatest(@Param("limit") Integer limit, @Param("organizationId") Long organizationId);

    /**
     * 查询热门的荣誉成果（按查看次数排序）
     *
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉成果列表
     */
    @Select("""
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
        WHERE status = 1 AND is_public = 1
        AND organization_id = #{organizationId}
        ORDER BY view_count DESC, create_time DESC
        LIMIT #{limit}
    """)
    List<HonorAchievementEntity> findPopular(@Param("limit") Integer limit, @Param("organizationId") Long organizationId);

    /**
     * 获取相关推荐（基于类型和级别）
     *
     * @param type 类型
     * @param level 级别
     * @param excludeId 排除的ID
     * @param limit 限制数量
     * @return 荣誉成果列表
     */
    @Select("""
        SELECT
            id, name, type, level, issue_org, achieve_time, cover_url, summary, description, attachments_json,
            download_count, view_count, is_public, allow_download, status, publish_time, organization_id, region_id,
            create_user, create_user_name, create_time, update_user, update_user_name, update_time
        FROM t_honor_achievement
        WHERE status = 1 AND is_public = 1
        AND id != #{excludeId}
        AND (type = #{type} OR level = #{level})
        ORDER BY
            CASE WHEN type = #{type} AND level = #{level} THEN 1
                 WHEN type = #{type} THEN 2
                 WHEN level = #{level} THEN 3
                 ELSE 4 END,
            view_count DESC, create_time DESC
        LIMIT #{limit}
    """)
    List<HonorAchievementEntity> getRecommendations(
            @Param("type") Integer type,
            @Param("level") Integer level,
            @Param("excludeId") Long excludeId,
            @Param("limit") Integer limit);
}