package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.vo.DataInspectionResultVO.*;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 数据体检结果管理Mapper
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Repository
@Mapper
public interface DataInspectionResultsMapper {

    /**
     * 分页查询异常列表
     */
    @Select("<script>" +
            "SELECT e.id, e.type, e.title as exception_title, e.description as exception_description, " +
            "       e.unit as affected_object, e.source, e.severity, e.status, e.detect_time, " +
            "       e.affected_records, e.impact, e.solution, e.handle_time, e.handler, " +
            "       e.create_time " +
            "FROM t_data_inspection_exception_details e " +
            "WHERE 1=1 " +
            "<if test='searchParams.exceptionTitle != null and searchParams.exceptionTitle != \"\"'>" +
            "  AND e.title ILIKE CONCAT('%', #{searchParams.exceptionTitle}, '%') " +
            "</if>" +
            "<if test='searchParams.exceptionType != null'>" +
            "  AND e.type = #{searchParams.exceptionType}::varchar " +
            "</if>" +
            "<if test='searchParams.exceptionLevel != null'>" +
            "  AND CASE " +
            "    WHEN e.severity = 'high' THEN 3 " +
            "    WHEN e.severity = 'medium' THEN 2 " +
            "    WHEN e.severity = 'low' THEN 1 " +
            "  END = #{searchParams.exceptionLevel} " +
            "</if>" +
            "<if test='searchParams.status != null'>" +
            "  AND CASE " +
            "    WHEN e.status = 'pending' THEN 1 " +
            "    WHEN e.status = 'in_remediation' THEN 2 " +
            "    WHEN e.status = 'resolved' THEN 3 " +
            "  END = #{searchParams.status} " +
            "</if>" +
            "<if test='searchParams.affectedObject != null and searchParams.affectedObject != \"\"'>" +
            "  AND e.unit ILIKE CONCAT('%', #{searchParams.affectedObject}, '%') " +
            "</if>" +
            "<if test='searchParams.assignee != null and searchParams.assignee != \"\"'>" +
            "  AND e.handler ILIKE CONCAT('%', #{searchParams.assignee}, '%') " +
            "</if>" +
            "<if test='dateRangeStart != null'>" +
            "  AND e.detect_time &gt;= #{dateRangeStart}::timestamp " +
            "</if>" +
            "<if test='dateRangeEnd != null'>" +
            "  AND e.detect_time &lt;= #{dateRangeEnd}::timestamp " +
            "</if>" +
            "ORDER BY e.detect_time DESC " +
            "LIMIT #{pageSize} OFFSET #{offset}" +
            "</script>")
    @Results({
            @Result(column = "id", property = "id"),
            @Result(column = "type", property = "exceptionType", typeHandler = ExceptionTypeHandler.class),
            @Result(column = "severity", property = "exceptionLevel", typeHandler = SeverityLevelHandler.class),
            @Result(column = "status", property = "status", typeHandler = ExceptionStatusHandler.class),
            @Result(column = "exception_title", property = "exceptionTitle"),
            @Result(column = "exception_description", property = "exceptionDescription"),
            @Result(column = "affected_object", property = "affectedObject"),
            @Result(column = "source", property = "source"),
            @Result(column = "affected_records", property = "affectedRecords"),
            @Result(column = "impact", property = "impact"),
            @Result(column = "solution", property = "solution"),
            @Result(column = "detect_time", property = "detectTime"),
            @Result(column = "handle_time", property = "handleTime"),
            @Result(column = "handler", property = "handler"),
            @Result(column = "create_time", property = "createTime")
    })
    List<HealthCheckExceptionVO> selectExceptionList(
            @Param("searchParams") ExceptionSearchVO searchParams,
            @Param("dateRangeStart") String dateRangeStart,
            @Param("dateRangeEnd") String dateRangeEnd,
            @Param("pageSize") Integer pageSize,
            @Param("offset") Integer offset
    );

    /**
     * 查询异常列表总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM t_data_inspection_exception_details e " +
            "WHERE 1=1 " +
            "<if test='searchParams.exceptionTitle != null and searchParams.exceptionTitle != \"\"'>" +
            "  AND e.title ILIKE CONCAT('%', #{searchParams.exceptionTitle}, '%') " +
            "</if>" +
            "<if test='searchParams.exceptionType != null'>" +
            "  AND e.type = #{searchParams.exceptionType}::varchar " +
            "</if>" +
            "<if test='searchParams.exceptionLevel != null'>" +
            "  AND CASE " +
            "    WHEN e.severity = 'high' THEN 3 " +
            "    WHEN e.severity = 'medium' THEN 2 " +
            "    WHEN e.severity = 'low' THEN 1 " +
            "  END = #{searchParams.exceptionLevel} " +
            "</if>" +
            "<if test='searchParams.status != null'>" +
            "  AND CASE " +
            "    WHEN e.status = 'pending' THEN 1 " +
            "    WHEN e.status = 'in_remediation' THEN 2 " +
            "    WHEN e.status = 'resolved' THEN 3 " +
            "  END = #{searchParams.status} " +
            "</if>" +
            "<if test='searchParams.affectedObject != null and searchParams.affectedObject != \"\"'>" +
            "  AND e.unit ILIKE CONCAT('%', #{searchParams.affectedObject}, '%') " +
            "</if>" +
            "<if test='searchParams.assignee != null and searchParams.assignee != \"\"'>" +
            "  AND e.handler ILIKE CONCAT('%', #{searchParams.assignee}, '%') " +
            "</if>" +
            "<if test='dateRangeStart != null'>" +
            "  AND e.detect_time &gt;= #{dateRangeStart}::timestamp " +
            "</if>" +
            "<if test='dateRangeEnd != null'>" +
            "  AND e.detect_time &lt;= #{dateRangeEnd}::timestamp " +
            "</if>" +
            "</script>")
    Long countExceptionList(
            @Param("searchParams") ExceptionSearchVO searchParams,
            @Param("dateRangeStart") String dateRangeStart,
            @Param("dateRangeEnd") String dateRangeEnd
    );

    /**
     * 获取异常统计数据
     */
    @Select("WITH exception_stats AS ( " +
            "  SELECT " +
            "    COUNT(*) as total_exceptions, " +
            "    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_level_exceptions, " +
            "    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as fixed_exceptions " +
            "  FROM t_data_inspection_exception_details " +
            "), " +
            "type_stats AS ( " +
            "  SELECT " +
            "    CASE " +
            "      WHEN type = '1' THEN 1 " +
            "      WHEN type = '2' THEN 2 " +
            "      WHEN type = '3' THEN 3 " +
            "      WHEN type = '4' THEN 4 " +
            "      ELSE 1 " +
            "    END as type_num, " +
            "    COUNT(*) as exception_count " +
            "  FROM t_data_inspection_exception_details " +
            "  GROUP BY type " +
            ") " +
            "SELECT " +
            "  es.total_exceptions, " +
            "  15 as completed_checks, " + // 模拟已完成检查数
            "  3 as failed_checks, " + // 模拟失败检查数
            "  es.high_level_exceptions, " +
            "  es.fixed_exceptions " +
            "FROM exception_stats es")
    @Results({
            @Result(column = "total_exceptions", property = "totalExceptions"),
            @Result(column = "completed_checks", property = "completedChecks"),
            @Result(column = "failed_checks", property = "failedChecks"),
            @Result(column = "high_level_exceptions", property = "highLevelExceptions"),
            @Result(column = "fixed_exceptions", property = "fixedExceptions")
    })
    Map<String, Object> getExceptionStatistics();

    /**
     * 获取体检类型统计
     */
    @Select("SELECT " +
            "  CASE " +
            "    WHEN type = '1' THEN 1 " +
            "    WHEN type = '2' THEN 2 " +
            "    WHEN type = '3' THEN 3 " +
            "    WHEN type = '4' THEN 4 " +
            "    ELSE 1 " +
            "  END as type, " +
            "  15 as count, " + // 模拟体检数量
            "  COUNT(*) as exception_count " +
            "FROM t_data_inspection_exception_details " +
            "GROUP BY type")
    @Results({
            @Result(column = "type", property = "type"),
            @Result(column = "count", property = "count"),
            @Result(column = "exception_count", property = "exceptionCount")
    })
    List<CheckTypeStatVO> getCheckTypeStats();

    /**
     * 获取异常趋势数据(最近7天)
     */
    @Select("WITH date_series AS ( " +
            "  SELECT " +
            "    generate_series( " +
            "      CURRENT_DATE - INTERVAL '6 days', " +
            "      CURRENT_DATE, " +
            "      '1 day'::INTERVAL " +
            "    )::DATE as date " +
            ") " +
            "SELECT " +
            "  TO_CHAR(ds.date, 'YYYY-MM-DD') as date, " +
            "  COALESCE(COUNT(e.id), 0) as count " +
            "FROM date_series ds " +
            "LEFT JOIN t_data_inspection_exception_details e " +
            "  ON DATE(e.detect_time) = ds.date " +
            "GROUP BY ds.date " +
            "ORDER BY ds.date")
    @Results({
            @Result(column = "date", property = "date"),
            @Result(column = "count", property = "count")
    })
    List<ExceptionTrendVO> getExceptionTrend();

    /**
     * 根据ID列表批量更新异常状态为整改中
     */
    @Update("<script>" +
            "UPDATE t_data_inspection_exception_details " +
            "SET status = 'in_remediation', " +
            "    handler = #{assignee}, " +
            "    handle_time = CURRENT_TIMESTAMP, " +
            "    update_time = CURRENT_TIMESTAMP " +
            "WHERE id IN " +
            "<foreach item='id' collection='exceptionIds' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateExceptionStatus(
            @Param("exceptionIds") List<Long> exceptionIds,
            @Param("assignee") String assignee
    );

    /**
     * 单项更新异常整改信息
     */
    @Update("UPDATE t_data_inspection_exception_details " +
            "SET status = 'in_remediation', " +
            "    solution = #{solution}, " +
            "    handler = COALESCE(#{assignee}, handler), " +
            "    handle_time = CURRENT_TIMESTAMP, " +
            "    update_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{exceptionId}")
    int updateExceptionRemediation(
            @Param("exceptionId") Long exceptionId,
            @Param("solution") String solution,
            @Param("assignee") String assignee
    );

    /**
     * 提交整改结果
     */
    @Update("UPDATE t_data_inspection_exception_details " +
            "SET status = CASE " +
            "      WHEN #{status} = 1 THEN 'pending' " +
            "      WHEN #{status} = 2 THEN 'in_remediation' " +
            "      WHEN #{status} = 3 THEN 'resolved' " +
            "      ELSE 'pending' " +
            "    END, " +
            "    solution = COALESCE(#{description}, solution), " +
            "    handle_time = CURRENT_TIMESTAMP, " +
            "    update_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{exceptionId}")
    int submitRemediationResult(
            @Param("exceptionId") Long exceptionId,
            @Param("status") Integer status,
            @Param("description") String description
    );

    /**
     * 获取影响单位排行
     */
    @Select("WITH unit_stats AS ( " +
            "  SELECT " +
            "    unit as unit_name, " +
            "    COUNT(*) as total_exceptions, " +
            "    COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_level_exceptions, " +
            "    COUNT(CASE WHEN status = 'resolved' AND handle_time IS NOT NULL THEN 1 END) as fixed_count, " +
            "    AVG( " +
            "      CASE " +
            "        WHEN status = 'resolved' AND handle_time IS NOT NULL AND detect_time IS NOT NULL " +
            "        THEN EXTRACT(EPOCH FROM (handle_time - detect_time)) / 86400.0 " +
            "        ELSE NULL " +
            "      END " +
            "    ) as avg_fix_time " +
            "  FROM t_data_inspection_exception_details " +
            "  WHERE unit IS NOT NULL AND unit != '' " +
            "  GROUP BY unit " +
            ") " +
            "SELECT " +
            "  unit_name, " +
            "  total_exceptions, " +
            "  high_level_exceptions, " +
            "  ROUND(COALESCE(avg_fix_time, 0)::numeric, 1) as avg_fix_time, " +
            "  ROUND( " +
            "    CASE " +
            "      WHEN total_exceptions > 0 " +
            "      THEN (fixed_count * 100.0 / total_exceptions)::numeric " +
            "      ELSE 0 " +
            "    END, " +
            "    1 " +
            "  ) as fix_rate " +
            "FROM unit_stats " +
            "ORDER BY total_exceptions DESC, fix_rate ASC " +
            "LIMIT 10")
    @Results({
            @Result(column = "unit_name", property = "unitName"),
            @Result(column = "total_exceptions", property = "totalExceptions"),
            @Result(column = "high_level_exceptions", property = "highLevelExceptions"),
            @Result(column = "avg_fix_time", property = "avgFixTime"),
            @Result(column = "fix_rate", property = "fixRate")
    })
    List<UnitStatsVO> getAffectedUnitsRanking();

    /**
     * 根据ID查询异常详情
     */
    @Select("SELECT e.id, e.type, e.title as exception_title, e.description as exception_description, " +
            "       e.unit as affected_object, e.source, e.severity, e.status, e.detect_time, " +
            "       e.affected_records, e.impact, e.solution, e.handle_time, e.handler, " +
            "       e.create_time " +
            "FROM t_data_inspection_exception_details e " +
            "WHERE e.id = #{exceptionId}")
    @Results({
            @Result(column = "id", property = "id"),
            @Result(column = "type", property = "exceptionType", typeHandler = ExceptionTypeHandler.class),
            @Result(column = "severity", property = "exceptionLevel", typeHandler = SeverityLevelHandler.class),
            @Result(column = "status", property = "status", typeHandler = ExceptionStatusHandler.class),
            @Result(column = "exception_title", property = "exceptionTitle"),
            @Result(column = "exception_description", property = "exceptionDescription"),
            @Result(column = "affected_object", property = "affectedObject"),
            @Result(column = "source", property = "source"),
            @Result(column = "affected_records", property = "affectedRecords"),
            @Result(column = "impact", property = "impact"),
            @Result(column = "solution", property = "solution"),
            @Result(column = "detect_time", property = "detectTime"),
            @Result(column = "handle_time", property = "handleTime"),
            @Result(column = "handler", property = "handler"),
            @Result(column = "create_time", property = "createTime")
    })
    HealthCheckExceptionVO selectExceptionById(@Param("exceptionId") Long exceptionId);

    /**
     * 检查异常ID是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM t_data_inspection_exception_details " +
            "WHERE id IN " +
            "<foreach item='id' collection='exceptionIds' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "</script>")
    long countExistingExceptionIds(@Param("exceptionIds") List<Long> exceptionIds);

    /**
     * 异常类型转换器
     */
    class ExceptionTypeHandler implements org.apache.ibatis.type.TypeHandler<Integer> {
        @Override
        public void setParameter(java.sql.PreparedStatement ps, int i, Integer parameter, org.apache.ibatis.type.JdbcType jdbcType) throws java.sql.SQLException {
            ps.setString(i, String.valueOf(parameter));
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, String columnName) throws java.sql.SQLException {
            String value = rs.getString(columnName);
            return value != null ? Integer.valueOf(value) : 1;
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, int columnIndex) throws java.sql.SQLException {
            String value = rs.getString(columnIndex);
            return value != null ? Integer.valueOf(value) : 1;
        }

        @Override
        public Integer getResult(java.sql.CallableStatement cs, int columnIndex) throws java.sql.SQLException {
            String value = cs.getString(columnIndex);
            return value != null ? Integer.valueOf(value) : 1;
        }
    }

    /**
     * 严重程度转换器
     */
    class SeverityLevelHandler implements org.apache.ibatis.type.TypeHandler<Integer> {
        @Override
        public void setParameter(java.sql.PreparedStatement ps, int i, Integer parameter, org.apache.ibatis.type.JdbcType jdbcType) throws java.sql.SQLException {
            String value = parameter == 3 ? "high" : parameter == 2 ? "medium" : "low";
            ps.setString(i, value);
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, String columnName) throws java.sql.SQLException {
            String value = rs.getString(columnName);
            return "high".equals(value) ? 3 : "medium".equals(value) ? 2 : 1;
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, int columnIndex) throws java.sql.SQLException {
            String value = rs.getString(columnIndex);
            return "high".equals(value) ? 3 : "medium".equals(value) ? 2 : 1;
        }

        @Override
        public Integer getResult(java.sql.CallableStatement cs, int columnIndex) throws java.sql.SQLException {
            String value = cs.getString(columnIndex);
            return "high".equals(value) ? 3 : "medium".equals(value) ? 2 : 1;
        }
    }

    /**
     * 异常状态转换器
     */
    class ExceptionStatusHandler implements org.apache.ibatis.type.TypeHandler<Integer> {
        @Override
        public void setParameter(java.sql.PreparedStatement ps, int i, Integer parameter, org.apache.ibatis.type.JdbcType jdbcType) throws java.sql.SQLException {
            String value = parameter == 2 ? "in_remediation" : parameter == 3 ? "resolved" : "pending";
            ps.setString(i, value);
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, String columnName) throws java.sql.SQLException {
            String value = rs.getString(columnName);
            return "in_remediation".equals(value) ? 2 : "resolved".equals(value) ? 3 : 1;
        }

        @Override
        public Integer getResult(java.sql.ResultSet rs, int columnIndex) throws java.sql.SQLException {
            String value = rs.getString(columnIndex);
            return "in_remediation".equals(value) ? 2 : "resolved".equals(value) ? 3 : 1;
        }

        @Override
        public Integer getResult(java.sql.CallableStatement cs, int columnIndex) throws java.sql.SQLException {
            String value = cs.getString(columnIndex);
            return "in_remediation".equals(value) ? 2 : "resolved".equals(value) ? 3 : 1;
        }
    }
}