package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionRuleEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 数据体检规则数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface DataInspectionRuleMapper {

    /**
     * 插入数据体检规则
     *
     * @param entity 规则实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_rules (
                    rule_name, rule_type, table_name, field_name, check_logic, threshold_value, is_enabled,
                    severity, description, creator, create_time, update_time
                ) VALUES (
                    #{ruleName}, #{ruleType}, #{tableName}, #{fieldName}, #{checkLogic}, #{thresholdValue}, #{isEnabled},
                    #{severity}, #{description}, #{creator}, #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionRuleEntity entity);

    /**
     * 根据ID删除数据体检规则
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_rules
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 更新数据体检规则
     *
     * @param entity 规则实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_rules
                SET
                    rule_name = #{ruleName},
                    rule_type = #{ruleType},
                    table_name = #{tableName},
                    field_name = #{fieldName},
                    check_logic = #{checkLogic},
                    threshold_value = #{thresholdValue},
                    is_enabled = #{isEnabled},
                    severity = #{severity},
                    description = #{description},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateById(DataInspectionRuleEntity entity);

    /**
     * 根据ID查询数据体检规则
     *
     * @param id 主键ID
     * @return 规则实体
     */
    @Select("""
                SELECT
                    id, rule_name as ruleName, rule_type as ruleType, table_name as tableName, field_name as fieldName,
                    check_logic as checkLogic, threshold_value as thresholdValue, is_enabled as isEnabled, severity,
                    description, creator, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_rules
                WHERE id = #{id}
            """)
    DataInspectionRuleEntity findById(@Param("id") Long id);

    /**
     * 分页查询数据体检规则列表
     *
     * @param params 查询参数
     * @return 规则列表
     */
    @Select("""
                <script>
                SELECT
                    id, rule_name as ruleName, rule_type as ruleType, table_name as tableName, field_name as fieldName,
                    check_logic as checkLogic, threshold_value as thresholdValue, is_enabled as isEnabled, severity,
                    description, creator, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_rules
                WHERE 1=1
                <if test="ruleName != null and ruleName != ''">
                    AND rule_name LIKE '%' || #{ruleName} || '%'
                </if>
                <if test="ruleType != null and ruleType != ''">
                    AND rule_type = #{ruleType}
                </if>
                <if test="tableName != null and tableName != ''">
                    AND table_name = #{tableName}
                </if>
                <if test="isEnabled != null">
                    AND is_enabled = #{isEnabled}
                </if>
                <if test="severity != null and severity != ''">
                    AND severity = #{severity}
                </if>
                <if test="creator != null and creator != ''">
                    AND creator = #{creator}
                </if>
                ORDER BY create_time DESC
                </script>
            """)
    List<DataInspectionRuleEntity> findPage(Map<String, Object> params);

    /**
     * 查询数据体检规则总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_rules
                WHERE 1=1
                <if test="ruleName != null and ruleName != ''">
                    AND rule_name LIKE '%' || #{ruleName} || '%'
                </if>
                <if test="ruleType != null and ruleType != ''">
                    AND rule_type = #{ruleType}
                </if>
                <if test="tableName != null and tableName != ''">
                    AND table_name = #{tableName}
                </if>
                <if test="isEnabled != null">
                    AND is_enabled = #{isEnabled}
                </if>
                <if test="severity != null and severity != ''">
                    AND severity = #{severity}
                </if>
                <if test="creator != null and creator != ''">
                    AND creator = #{creator}
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 启用/禁用数据体检规则
     *
     * @param id 规则ID
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_rules
                SET is_enabled = #{isEnabled}, update_time = NOW()
                WHERE id = #{id}
            """)
    int toggleEnable(@Param("id") Long id, @Param("isEnabled") Boolean isEnabled);

    /**
     * 批量启用/禁用规则
     *
     * @param ids 规则ID列表
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    @Update("""
                <script>
                UPDATE t_data_inspection_rules
                SET is_enabled = #{isEnabled}, update_time = NOW()
                WHERE id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                </script>
            """)
    int batchToggleEnable(@Param("ids") List<Long> ids, @Param("isEnabled") Boolean isEnabled);

    /**
     * 按规则类型统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT rule_type as ruleType, COUNT(*) as count,
                       SUM(CASE WHEN is_enabled THEN 1 ELSE 0 END) as enabledCount
                FROM t_data_inspection_rules
                GROUP BY rule_type
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByRuleType();

    /**
     * 按表名统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT table_name as tableName, COUNT(*) as count,
                       SUM(CASE WHEN is_enabled THEN 1 ELSE 0 END) as enabledCount
                FROM t_data_inspection_rules
                WHERE table_name IS NOT NULL
                GROUP BY table_name
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByTableName();

    /**
     * 按严重程度统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT severity, COUNT(*) as count
                FROM t_data_inspection_rules
                GROUP BY severity
                ORDER BY CASE severity WHEN 'high' THEN 1 WHEN 'medium' THEN 2 WHEN 'low' THEN 3 END
            """)
    List<Map<String, Object>> countBySeverity();

    /**
     * 获取规则概览统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT 
                    COUNT(*) as totalRules,
                    SUM(CASE WHEN is_enabled THEN 1 ELSE 0 END) as enabledRules,
                    SUM(CASE WHEN severity = 'high' THEN 1 ELSE 0 END) as highSeverityRules,
                    SUM(CASE WHEN severity = 'medium' THEN 1 ELSE 0 END) as mediumSeverityRules,
                    SUM(CASE WHEN severity = 'low' THEN 1 ELSE 0 END) as lowSeverityRules
                FROM t_data_inspection_rules
            """)
    Map<String, Object> getOverviewStatistics();

    /**
     * 查询启用的规则
     *
     * @return 规则列表
     */
    @Select("""
                SELECT
                    id, rule_name as ruleName, rule_type as ruleType, table_name as tableName, field_name as fieldName,
                    check_logic as checkLogic, threshold_value as thresholdValue, is_enabled as isEnabled, severity,
                    description, creator, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_rules
                WHERE is_enabled = true
                ORDER BY severity DESC, create_time DESC
            """)
    List<DataInspectionRuleEntity> findAllEnabled();

    /**
     * 根据表名查询启用的规则
     *
     * @param tableName 表名
     * @return 规则列表
     */
    @Select("""
                SELECT
                    id, rule_name as ruleName, rule_type as ruleType, table_name as tableName, field_name as fieldName,
                    check_logic as checkLogic, threshold_value as thresholdValue, is_enabled as isEnabled, severity,
                    description, creator, create_time as createTime, update_time as updateTime
                FROM t_data_inspection_rules
                WHERE is_enabled = true AND table_name = #{tableName}
                ORDER BY severity DESC, create_time DESC
            """)
    List<DataInspectionRuleEntity> findEnabledByTableName(@Param("tableName") String tableName);
}