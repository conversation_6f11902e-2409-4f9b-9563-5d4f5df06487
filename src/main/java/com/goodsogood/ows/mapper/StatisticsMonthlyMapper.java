package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.StatisticsMonthlyEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 统计分析月度统计数据访问接口
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Mapper
@Repository
public interface StatisticsMonthlyMapper extends MyMapper<StatisticsMonthlyEntity> {

    /**
     * 根据年份查询月度统计
     *
     * @param year 年份
     * @param organizationId 组织ID
     * @param limit 限制数量
     * @return 月度统计列表
     */
    @Select("""
            <script>
            SELECT
                id, month, document_count as documentCount, publish_count as publishCount,
                view_count as viewCount, download_count as downloadCount,
                organization_id as organizationId, region_id as regionId,
                create_time as createTime, update_time as updateTime
            FROM t_statistics_monthly
            WHERE organization_id = #{organizationId}
            <if test="year != null and year != ''">
                AND month LIKE CONCAT(#{year}, '%')
            </if>
            ORDER BY month DESC
            <if test="limit != null">
                LIMIT #{limit}
            </if>
            </script>
            """)
    List<StatisticsMonthlyEntity> findByYear(@Param("year") String year,
                                             @Param("organizationId") Long organizationId,
                                             @Param("limit") Integer limit);

    /**
     * 查询月度统计总数
     *
     * @param year 年份
     * @param organizationId 组织ID
     * @return 总数
     */
    @Select("""
            <script>
            SELECT COUNT(*)
            FROM t_statistics_monthly
            WHERE organization_id = #{organizationId}
            <if test="year != null and year != ''">
                AND month LIKE CONCAT(#{year}, '%')
            </if>
            </script>
            """)
    int countByYear(@Param("year") String year, @Param("organizationId") Long organizationId);

    /**
     * 插入或更新月度统计
     *
     * @param entity 月度统计实体
     * @return 影响行数
     */
    @Insert("""
            INSERT INTO t_statistics_monthly (
                month, document_count, publish_count, view_count, download_count,
                organization_id, region_id, create_time, update_time
            ) VALUES (
                #{month}, #{documentCount}, #{publishCount}, #{viewCount}, #{downloadCount},
                #{organizationId}, #{regionId}, #{createTime}, #{updateTime}
            )
            ON DUPLICATE KEY UPDATE
                document_count = VALUES(document_count),
                publish_count = VALUES(publish_count),
                view_count = VALUES(view_count),
                download_count = VALUES(download_count),
                update_time = VALUES(update_time)
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertOrUpdate(StatisticsMonthlyEntity entity);

    /**
     * 获取月度趋势统计
     *
     * @param organizationId 组织ID
     * @param months 月份数
     * @return 趋势统计
     */
    @Select("""
            SELECT
                month,
                document_count as documentCount,
                publish_count as publishCount,
                view_count as viewCount,
                download_count as downloadCount
            FROM t_statistics_monthly
            WHERE organization_id = #{organizationId}
            AND month >= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL #{months} MONTH), '%Y-%m')
            ORDER BY month DESC
            """)
    List<Map<String, Object>> getTrendStatistics(@Param("organizationId") Long organizationId,
                                                 @Param("months") Integer months);

    /**
     * 删除过期的月度统计数据
     *
     * @param cutoffMonth 截止月份
     * @return 删除记录数
     */
    @Delete("""
            DELETE FROM t_statistics_monthly
            WHERE month < #{cutoffMonth}
            """)
    int deleteExpiredData(@Param("cutoffMonth") String cutoffMonth);
}
