package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionUserPermissionEntity;
import org.apache.ibatis.annotations.*;

/**
 * 数据体检用户权限数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface DataInspectionUserPermissionMapper extends MyMapper<DataInspectionUserPermissionEntity>{

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限实体
     */
    @Select("""
                SELECT
                    id, user_id as userId, can_view_inspection as canViewInspection, 
                    can_configure_rules as canConfigureRules, can_handle_exceptions as canHandleExceptions,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_user_permissions
                WHERE user_id = #{userId}
            """)
    DataInspectionUserPermissionEntity findByUserId(@Param("userId") Long userId);

    /**
     * 插入用户权限
     *
     * @param entity 权限实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_user_permissions (
                    user_id, can_view_inspection, can_configure_rules, can_handle_exceptions, create_time, update_time
                ) VALUES (
                    #{userId}, #{canViewInspection}, #{canConfigureRules}, #{canHandleExceptions}, #{createTime}, #{updateTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionUserPermissionEntity entity);

    /**
     * 更新用户权限
     *
     * @param entity 权限实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_user_permissions
                SET
                    can_view_inspection = #{canViewInspection},
                    can_configure_rules = #{canConfigureRules},
                    can_handle_exceptions = #{canHandleExceptions},
                    update_time = NOW()
                WHERE user_id = #{userId}
            """)
    int updateByUserId(DataInspectionUserPermissionEntity entity);

    /**
     * 插入或更新权限（upsert）
     *
     * @param entity 权限实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_user_permissions (
                    user_id, can_view_inspection, can_configure_rules, can_handle_exceptions, create_time, update_time
                ) VALUES (
                    #{userId}, #{canViewInspection}, #{canConfigureRules}, #{canHandleExceptions}, #{createTime}, #{updateTime}
                )
                ON CONFLICT (user_id) DO UPDATE SET
                    can_view_inspection = EXCLUDED.can_view_inspection,
                    can_configure_rules = EXCLUDED.can_configure_rules,
                    can_handle_exceptions = EXCLUDED.can_handle_exceptions,
                    update_time = NOW()
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int upsert(DataInspectionUserPermissionEntity entity);


    /**
     * 根据主键更新
     *
     * @param entity 权限实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_user_permissions
                SET
                    user_id = #{userId},
                    can_view_inspection = #{canViewInspection},
                    can_configure_rules = #{canConfigureRules},
                    can_handle_exceptions = #{canHandleExceptions},
                    update_time = NOW()
                WHERE id = #{id}
            """)
    int updateByKey(DataInspectionUserPermissionEntity entity);

    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_user_permissions
                WHERE id = #{id}
            """)
    int deleteByKey(@Param("id") Long id);

    /**
     * 查询所有权限
     *
     * @return 权限列表
     */
    @Select("""
                SELECT
                    id, user_id as userId, can_view_inspection as canViewInspection, 
                    can_configure_rules as canConfigureRules, can_handle_exceptions as canHandleExceptions,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_user_permissions
                ORDER BY create_time DESC
            """)
    java.util.List<DataInspectionUserPermissionEntity> selectAll();

    /**
     * 根据用户ID删除权限
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_user_permissions
                WHERE user_id = #{userId}
            """)
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 批量查询用户权限
     *
     * @param userIds 用户ID列表
     * @return 权限列表
     */
    @Select("""
                <script>
                SELECT
                    id, user_id as userId, can_view_inspection as canViewInspection, 
                    can_configure_rules as canConfigureRules, can_handle_exceptions as canHandleExceptions,
                    create_time as createTime, update_time as updateTime
                FROM t_data_inspection_user_permissions
                WHERE user_id IN
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
                </script>
            """)
    java.util.List<DataInspectionUserPermissionEntity> selectByUserIds(@Param("userIds") java.util.List<Long> userIds);
}