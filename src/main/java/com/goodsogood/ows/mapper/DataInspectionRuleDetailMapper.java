package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionRuleDetailEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 数据体检规则详情数据访问层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Mapper
public interface DataInspectionRuleDetailMapper extends MyMapper<DataInspectionRuleDetailEntity>{

    /**
     * 插入规则详情
     *
     * @param entity 规则详情实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_rule_details (
                    rule_id, field_name, rule_type, rule_value, error_message, create_time
                ) VALUES (
                    #{ruleId}, #{fieldName}, #{ruleType}, #{ruleValue}, #{errorMessage}, #{createTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionRuleDetailEntity entity);

    /**
     * 根据ID删除规则详情
     *
     * @param id 主键ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_rule_details
                WHERE id = #{id}
            """)
    int deleteById(@Param("id") Long id);

    /**
     * 根据规则ID删除所有详情
     *
     * @param ruleId 规则ID
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_rule_details
                WHERE rule_id = #{ruleId}
            """)
    int deleteByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 更新规则详情
     *
     * @param entity 规则详情实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_rule_details
                SET
                    rule_id = #{ruleId},
                    field_name = #{fieldName},
                    rule_type = #{ruleType},
                    rule_value = #{ruleValue},
                    error_message = #{errorMessage}
                WHERE id = #{id}
            """)
    int updateById(DataInspectionRuleDetailEntity entity);

    /**
     * 根据ID查询规则详情
     *
     * @param id 主键ID
     * @return 规则详情实体
     */
    @Select("""
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE id = #{id}
            """)
    DataInspectionRuleDetailEntity findById(@Param("id") Long id);

    /**
     * 根据规则ID查询详情列表
     *
     * @param ruleId 规则ID
     * @return 规则详情列表
     */
    @Select("""
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE rule_id = #{ruleId}
                ORDER BY id
            """)
    List<DataInspectionRuleDetailEntity> findByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 分页查询规则详情列表
     *
     * @param params 查询参数
     * @return 规则详情列表
     */
    @Select("""
                <script>
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE 1=1
                <if test="ruleId != null">
                    AND rule_id = #{ruleId}
                </if>
                <if test="fieldName != null and fieldName != ''">
                    AND field_name LIKE '%' || #{fieldName} || '%'
                </if>
                <if test="ruleType != null and ruleType != ''">
                    AND rule_type = #{ruleType}
                </if>
                ORDER BY rule_id, id
                </script>
            """)
    List<DataInspectionRuleDetailEntity> findPage(Map<String, Object> params);

    /**
     * 查询规则详情总数
     *
     * @param params 查询参数
     * @return 总数
     */
    @Select("""
                <script>
                SELECT COUNT(*)
                FROM t_data_inspection_rule_details
                WHERE 1=1
                <if test="ruleId != null">
                    AND rule_id = #{ruleId}
                </if>
                <if test="fieldName != null and fieldName != ''">
                    AND field_name LIKE '%' || #{fieldName} || '%'
                </if>
                <if test="ruleType != null and ruleType != ''">
                    AND rule_type = #{ruleType}
                </if>
                </script>
            """)
    int count(Map<String, Object> params);

    /**
     * 批量插入规则详情
     *
     * @param entities 规则详情列表
     * @return 影响行数
     */
    @Insert("""
                <script>
                INSERT INTO t_data_inspection_rule_details (
                    rule_id, field_name, rule_type, rule_value, error_message, create_time
                ) VALUES
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleId}, #{entity.fieldName}, #{entity.ruleType}, #{entity.ruleValue}, #{entity.errorMessage}, #{entity.createTime})
                </foreach>
                </script>
            """)
    int batchInsert(@Param("entities") List<DataInspectionRuleDetailEntity> entities);

    /**
     * 根据规则ID列表查询详情
     *
     * @param ruleIds 规则ID列表
     * @return 规则详情列表
     */
    @Select("""
                <script>
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE rule_id IN
                <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
                    #{ruleId}
                </foreach>
                ORDER BY rule_id, id
                </script>
            """)
    List<DataInspectionRuleDetailEntity> findByRuleIds(@Param("ruleIds") List<Long> ruleIds);

    /**
     * 按规则类型统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT rule_type as ruleType, COUNT(*) as count
                FROM t_data_inspection_rule_details
                GROUP BY rule_type
                ORDER BY count DESC
            """)
    List<Map<String, Object>> countByRuleType();

    /**
     * 按字段名统计
     *
     * @return 统计结果
     */
    @Select("""
                SELECT field_name as fieldName, COUNT(*) as count
                FROM t_data_inspection_rule_details
                WHERE field_name IS NOT NULL
                GROUP BY field_name
                ORDER BY count DESC
                LIMIT 20
            """)
    List<Map<String, Object>> countByFieldName();

    /**
     * 根据规则ID和字段名查询详情
     *
     * @param ruleId 规则ID
     * @param fieldName 字段名
     * @return 规则详情实体
     */
    @Select("""
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE rule_id = #{ruleId} AND field_name = #{fieldName}
                LIMIT 1
            """)
    DataInspectionRuleDetailEntity findByRuleIdAndFieldName(@Param("ruleId") Long ruleId, @Param("fieldName") String fieldName);

    /**
     * 查询指定规则类型的详情
     *
     * @param ruleType 规则类型
     * @return 规则详情列表
     */
    @Select("""
                SELECT
                    id, rule_id as ruleId, field_name as fieldName, rule_type as ruleType,
                    rule_value as ruleValue, error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_rule_details
                WHERE rule_type = #{ruleType}
                ORDER BY create_time DESC
            """)
    List<DataInspectionRuleDetailEntity> findByRuleType(@Param("ruleType") String ruleType);
}