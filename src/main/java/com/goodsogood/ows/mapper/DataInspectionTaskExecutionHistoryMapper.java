package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.DataInspectionTaskExecutionHistoryEntity;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 任务执行历史数据访问接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Mapper
public interface DataInspectionTaskExecutionHistoryMapper extends MyMapper<DataInspectionTaskExecutionHistoryEntity>{

    /**
     * 插入执行历史记录
     *
     * @param entity 执行历史实体
     * @return 影响行数
     */
    @Insert("""
                INSERT INTO t_data_inspection_task_execution_history (
                    task_id, execution_id, start_time, end_time, duration_seconds,
                    status, total_records, exception_count, exception_rate,
                    progress, result_summary, error_message, create_time
                ) VALUES (
                    #{taskId}, #{executionId}, #{startTime}, #{endTime}, #{durationSeconds},
                    #{status}, #{totalRecords}, #{exceptionCount}, #{exceptionRate},
                    #{progress}, #{resultSummary}, #{errorMessage}, #{createTime}
                )
            """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DataInspectionTaskExecutionHistoryEntity entity);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 执行历史实体
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                WHERE id = #{id}
            """)
    DataInspectionTaskExecutionHistoryEntity selectByKey(@Param("id") Long id);

    /**
     * 根据主键更新
     *
     * @param entity 执行历史实体
     * @return 影响行数
     */
    @Update("""
                UPDATE t_data_inspection_task_execution_history
                SET
                    task_id = #{taskId},
                    execution_id = #{executionId},
                    start_time = #{startTime},
                    end_time = #{endTime},
                    duration_seconds = #{durationSeconds},
                    status = #{status},
                    total_records = #{totalRecords},
                    exception_count = #{exceptionCount},
                    exception_rate = #{exceptionRate},
                    progress = #{progress},
                    result_summary = #{resultSummary},
                    error_message = #{errorMessage}
                WHERE id = #{id}
            """)
    int updateByKey(DataInspectionTaskExecutionHistoryEntity entity);

    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return 影响行数
     */
    @Delete("""
                DELETE FROM t_data_inspection_task_execution_history
                WHERE id = #{id}
            """)
    int deleteByKey(@Param("id") Long id);

    /**
     * 查询所有执行历史
     *
     * @return 执行历史列表
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                ORDER BY create_time DESC
            """)
    List<DataInspectionTaskExecutionHistoryEntity> selectAll();

    /**
     * 根据执行ID查询
     *
     * @param executionId 执行ID
     * @return 执行历史实体
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                WHERE execution_id = #{executionId}
            """)
    DataInspectionTaskExecutionHistoryEntity selectByExecutionId(@Param("executionId") String executionId);

    /**
     * 根据任务ID查询执行历史列表
     *
     * @param taskId 任务ID
     * @return 执行历史列表
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                WHERE task_id = #{taskId}
                ORDER BY start_time DESC
            """)
    List<DataInspectionTaskExecutionHistoryEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据状态查询执行历史
     *
     * @param status 状态
     * @return 执行历史列表
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                WHERE status = #{status}
                ORDER BY start_time DESC
            """)
    List<DataInspectionTaskExecutionHistoryEntity> selectByStatus(@Param("status") Integer status);

    /**
     * 批量删除过期记录
     *
     * @param cutoffTime 截止时间
     * @return 删除记录数
     */
    @Delete("""
                DELETE FROM t_data_inspection_task_execution_history
                WHERE create_time < #{cutoffTime}
            """)
    int deleteExpiredRecords(@Param("cutoffTime") java.util.Date cutoffTime);

    /**
     * 统计任务执行次数
     *
     * @param taskId 任务ID
     * @return 执行次数
     */
    @Select("""
                SELECT COUNT(*)
                FROM t_data_inspection_task_execution_history
                WHERE task_id = #{taskId}
            """)
    long countByTaskId(@Param("taskId") Long taskId);

    /**
     * 统计任务成功执行次数
     *
     * @param taskId 任务ID
     * @return 成功执行次数
     */
    @Select("""
                SELECT COUNT(*)
                FROM t_data_inspection_task_execution_history
                WHERE task_id = #{taskId} AND status = 2
            """)
    long countSuccessfulByTaskId(@Param("taskId") Long taskId);

    /**
     * 获取任务最新的执行记录
     *
     * @param taskId 任务ID
     * @return 最新执行记录
     */
    @Select("""
                SELECT
                    id, task_id as taskId, execution_id as executionId,
                    start_time as startTime, end_time as endTime, duration_seconds as durationSeconds,
                    status, total_records as totalRecords, exception_count as exceptionCount,
                    exception_rate as exceptionRate, progress, result_summary as resultSummary,
                    error_message as errorMessage, create_time as createTime
                FROM t_data_inspection_task_execution_history
                WHERE task_id = #{taskId}
                ORDER BY start_time DESC
                LIMIT 1
            """)
    DataInspectionTaskExecutionHistoryEntity selectLatestByTaskId(@Param("taskId") Long taskId);
}