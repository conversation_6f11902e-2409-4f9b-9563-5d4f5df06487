package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.RemediationRecordMapper;
import com.goodsogood.ows.mapper.DataInspectionExceptionMapper;
import com.goodsogood.ows.model.db.RemediationRecordEntity;
import com.goodsogood.ows.model.db.DataInspectionExceptionEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 整改记录业务逻辑层
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class RemediationRecordService {

    @Autowired
    private RemediationRecordMapper remediationRecordMapper;

    @Autowired
    private DataInspectionExceptionMapper dataInspectionExceptionMapper;

    /**
     * 创建整改记录
     * 
     * @param entity 整改记录实体
     * @param sysHeader 系统头信息
     * @return 整改记录实体
     */
    @Transactional(rollbackFor = Exception.class)
    public RemediationRecordEntity create(RemediationRecordEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查异常是否存在
        DataInspectionExceptionEntity exception = dataInspectionExceptionMapper.findById(entity.getExceptionId());
        if (exception == null) {
            throw new RuntimeException("异常信息不存在");
        }

        // 设置创建信息
        entity.setStartTime(new Date());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus("in_progress");
        }
        if (entity.getExceptionTitle() == null) {
            entity.setExceptionTitle(exception.getTitle());
        }

        int result = remediationRecordMapper.insert(entity);
        if (result > 0) {
            // 更新异常状态为整改中
            exception.setStatus("in_remediation");
            exception.setHandler(entity.getOperator());
            exception.setHandleTime(new Date());
            dataInspectionExceptionMapper.updateById(exception);

            log.info("创建整改记录成功，ID: {}, 异常ID: {}", entity.getId(), entity.getExceptionId());
            return entity;
        } else {
            throw new RuntimeException("创建整改记录失败");
        }
    }

    /**
     * 更新整改记录
     * 
     * @param entity 整改记录实体
     * @param sysHeader 系统头信息
     * @return 整改记录实体
     */
    @Transactional(rollbackFor = Exception.class)
    public RemediationRecordEntity update(RemediationRecordEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        RemediationRecordEntity existing = remediationRecordMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("整改记录不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        // 如果状态变为完成且验证通过，更新异常状态
        if ("completed".equals(entity.getStatus()) && "passed".equals(entity.getVerificationResult())) {
            DataInspectionExceptionEntity exception = dataInspectionExceptionMapper.findById(existing.getExceptionId());
            if (exception != null) {
                exception.setStatus("resolved");
                dataInspectionExceptionMapper.updateById(exception);
            }
        }

        int result = remediationRecordMapper.updateById(entity);
        if (result > 0) {
            log.info("更新整改记录成功，ID: {}, 状态: {}", entity.getId(), entity.getStatus());
            return remediationRecordMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新整改记录失败");
        }
    }

    /**
     * 删除整改记录
     * 
     * @param id 整改记录ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        RemediationRecordEntity existing = remediationRecordMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("整改记录不存在");
        }

        int result = remediationRecordMapper.deleteById(id);
        if (result > 0) {
            log.info("删除整改记录成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除整改记录失败");
        }
    }

    /**
     * 根据ID查询整改记录
     * 
     * @param id 整改记录ID
     * @return 整改记录实体
     */
    public RemediationRecordEntity findById(Long id) {
        return remediationRecordMapper.findById(id);
    }

    /**
     * 分页查询整改记录列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<RemediationRecordEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<RemediationRecordEntity> list = remediationRecordMapper.findPage(params);
        int total = remediationRecordMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 根据异常ID查询整改记录
     * 
     * @param exceptionId 异常ID
     * @return 整改记录列表
     */
    public List<RemediationRecordEntity> findByExceptionId(Long exceptionId) {
        return remediationRecordMapper.findByExceptionId(exceptionId);
    }

    /**
     * 按状态统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus() {
        return remediationRecordMapper.countByStatus();
    }

    /**
     * 按操作人统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByOperator() {
        return remediationRecordMapper.countByOperator();
    }

    /**
     * 获取最近的整改记录
     * 
     * @param days 天数
     * @param limit 限制数量
     * @return 整改记录列表
     */
    public List<RemediationRecordEntity> findRecentRecords(Integer days, Integer limit) {
        return remediationRecordMapper.findRecentRecords(days, limit);
    }

    /**
     * 构建查询参数
     * 
     * @param status 状态
     * @param operator 操作人
     * @param exceptionId 异常ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String status, String operator, Long exceptionId, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(status)) {
            params.put("status", status.trim());
        }
        if (StringUtils.hasText(operator)) {
            params.put("operator", operator.trim());
        }
        if (exceptionId != null) {
            params.put("exceptionId", exceptionId);
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }
        
        return params;
    }

    /**
     * 完成整改并进行验证
     * 
     * @param id 整改记录ID
     * @param verificationResult 验证结果
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean completeAndVerify(Long id, String verificationResult, HeaderHelper.SysHeader sysHeader) {
        RemediationRecordEntity record = remediationRecordMapper.findById(id);
        if (record == null) {
            throw new RuntimeException("整改记录不存在");
        }

        if (!"in_progress".equals(record.getStatus())) {
            throw new RuntimeException("整改尚未完成，无法进行验证");
        }

        Date now = new Date();
        int result = remediationRecordMapper.updateStatusAndVerification(id, "completed", verificationResult, now, now);
        
        if (result > 0) {
            // 如果验证通过，更新异常状态
            if ("passed".equals(verificationResult)) {
                DataInspectionExceptionEntity exception = dataInspectionExceptionMapper.findById(record.getExceptionId());
                if (exception != null) {
                    exception.setStatus("resolved");
                    dataInspectionExceptionMapper.updateById(exception);
                }
            }

            log.info("完成整改并验证成功，ID: {}, 验证结果: {}", id, verificationResult);
            return true;
        } else {
            throw new RuntimeException("完成整改并验证失败");
        }
    }

    /**
     * 获取整改概览统计
     * 
     * @return 统计结果
     */
    public Map<String, Object> getOverviewStatistics() {
        Map<String, Object> overview = new HashMap<>();
        
        // 按状态统计
        List<Map<String, Object>> statusStats = countByStatus();
        int totalRecords = 0;
        int inProgressCount = 0;
        int completedCount = 0;
        
        for (Map<String, Object> stat : statusStats) {
            String status = (String) stat.get("status");
            Integer count = (Integer) stat.get("count");
            totalRecords += count;
            
            switch (status) {
                case "in_progress":
                    inProgressCount = count;
                    break;
                case "completed":
                    completedCount = count;
                    break;
            }
        }
        
        overview.put("totalRecords", totalRecords);
        overview.put("inProgressCount", inProgressCount);
        overview.put("completedCount", completedCount);
        
        // 完成率
        if (totalRecords > 0) {
            double completionRate = (double) completedCount / totalRecords * 100;
            overview.put("completionRate", Math.round(completionRate * 100.0) / 100.0);
        } else {
            overview.put("completionRate", 0.0);
        }
        
        return overview;
    }

    /**
     * 触发复检
     * 
     * @param remediationId 整改记录ID
     * @param sysHeader 系统头信息
     * @return 复检结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> triggerReInspection(Long remediationId, HeaderHelper.SysHeader sysHeader) {
        RemediationRecordEntity remediation = remediationRecordMapper.findById(remediationId);
        if (remediation == null) {
            throw new RuntimeException("整改记录不存在");
        }

        if (!"completed".equals(remediation.getStatus())) {
            throw new RuntimeException("整改尚未完成，无法进行复检");
        }

        // 模拟复检过程
        Map<String, Object> reInspectionResult = new HashMap<>();
        reInspectionResult.put("id", UUID.randomUUID().toString().substring(0, 9));
        reInspectionResult.put("remediationId", remediationId);
        reInspectionResult.put("exceptionId", remediation.getExceptionId());
        reInspectionResult.put("inspectionTime", new Date());
        reInspectionResult.put("result", Math.random() > 0.2 ? "passed" : "failed"); // 80%通过率
        reInspectionResult.put("details", "针对整改项目进行专项复检");
        reInspectionResult.put("operator", "系统自动复检");

        // 更新整改记录的验证结果
        String verificationResult = (String) reInspectionResult.get("result");
        Date now = new Date();
        remediationRecordMapper.updateStatusAndVerification(remediationId, "completed", verificationResult, remediation.getCompleteTime(), now);

        log.info("触发复检完成，整改记录ID: {}, 复检结果: {}", remediationId, verificationResult);
        return reInspectionResult;
    }

    /**
     * 开始整改
     */
    @Transactional
    public boolean startRemediation(Long exceptionId, String description, String estimatedTime, 
                                   List<String> resources, String assignee, Long userId) {
        try {
            RemediationRecordEntity record = new RemediationRecordEntity();
            record.setExceptionId(exceptionId);
            record.setRemediationDetail(description);
            record.setOperator(assignee != null ? assignee : "admin");
            record.setStatus("in_progress");
            record.setStartTime(new Date());
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            
            int result = remediationRecordMapper.insert(record);
            if (result > 0) {
                log.info("开始整改异常成功, 异常ID: {}, 描述: {}, 分配给: {}", 
                        exceptionId, description, assignee);
                return true;
            }
        } catch (Exception e) {
            log.error("开始整改失败, 异常ID: {}", exceptionId, e);
        }
        return false;
    }

    /**
     * 记录整改结果
     */
    @Transactional
    public boolean recordResult(Long exceptionId, Integer status, String description, 
                               String actualTime, List<String> attachments, String nextSteps, Long userId) {
        try {
            // 根据异常ID查找整改记录
            List<RemediationRecordEntity> records = remediationRecordMapper.findByExceptionId(exceptionId);
            if (records == null || records.isEmpty()) {
                log.warn("未找到异常的整改记录, 异常ID: {}", exceptionId);
                return false;
            }
            
            // 获取最新的整改记录
            RemediationRecordEntity record = records.get(0);
            
            // 更新整改记录
            record.setStatus(status == 3 ? "completed" : "failed"); // 3表示已修复
            record.setRemediationDetail(record.getRemediationDetail() + "\n\n" + description);
            record.setCompleteTime(new Date());
            record.setUpdateTime(new Date());
            
            int result = remediationRecordMapper.updateById(record);
            if (result > 0) {
                log.info("记录整改结果成功, 异常ID: {}, 状态: {}, 描述: {}", 
                        exceptionId, status, description);
                return true;
            }
        } catch (Exception e) {
            log.error("记录整改结果失败, 异常ID: {}", exceptionId, e);
        }
        return false;
    }
}