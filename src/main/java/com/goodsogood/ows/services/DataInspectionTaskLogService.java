package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionTaskLogMapper;
import com.goodsogood.ows.model.db.DataInspectionTaskLogEntity;
import com.goodsogood.ows.model.vo.PageResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * 任务执行日志服务
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@Service
public class DataInspectionTaskLogService {

    @Autowired
    private DataInspectionTaskLogMapper taskLogMapper;

    /**
     * 添加任务日志
     */
    @Transactional
    public void addLog(String executionId, String logLevel, String logMessage, String threadName, String className) {
        try {
            DataInspectionTaskLogEntity logEntity = new DataInspectionTaskLogEntity();
            logEntity.setExecutionId(executionId);
            logEntity.setLogLevel(logLevel);
            logEntity.setLogMessage(logMessage);
            logEntity.setLogTime(new Date());
            logEntity.setThreadName(threadName);
            logEntity.setClassName(className);

            taskLogMapper.insertSelective(logEntity);
            log.debug("添加任务日志成功, 执行ID: {}, 级别: {}", executionId, logLevel);
        } catch (Exception e) {
            log.error("添加任务日志失败, 执行ID: {}", executionId, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 添加DEBUG级别日志
     */
    public void addDebugLog(String executionId, String message) {
        addLog(executionId, DataInspectionTaskLogEntity.LogLevel.DEBUG.getCode(), message, 
               Thread.currentThread().getName(), getCallerClassName());
    }

    /**
     * 添加INFO级别日志
     */
    public void addInfoLog(String executionId, String message) {
        addLog(executionId, DataInspectionTaskLogEntity.LogLevel.INFO.getCode(), message, 
               Thread.currentThread().getName(), getCallerClassName());
    }

    /**
     * 添加WARN级别日志
     */
    public void addWarnLog(String executionId, String message) {
        addLog(executionId, DataInspectionTaskLogEntity.LogLevel.WARN.getCode(), message, 
               Thread.currentThread().getName(), getCallerClassName());
    }

    /**
     * 添加ERROR级别日志
     */
    public void addErrorLog(String executionId, String message) {
        addLog(executionId, DataInspectionTaskLogEntity.LogLevel.ERROR.getCode(), message, 
               Thread.currentThread().getName(), getCallerClassName());
    }

    /**
     * 获取执行日志列表
     */
    public PageResult<DataInspectionTaskLogEntity> getLogList(String executionId, String logLevel,
                                                             Date startTime, Date endTime,
                                                             Integer pageNum, Integer pageSize) {
        try {
            PageHelper.startPage(pageNum, pageSize, "log_time ASC");
            
            Example example = new Example(DataInspectionTaskLogEntity.class);
            Example.Criteria criteria = example.createCriteria();
            
            if (executionId != null && !executionId.trim().isEmpty()) {
                criteria.andEqualTo("executionId", executionId);
            }
            if (logLevel != null && !logLevel.trim().isEmpty()) {
                criteria.andEqualTo("logLevel", logLevel);
            }
            if (startTime != null) {
                criteria.andGreaterThanOrEqualTo("logTime", startTime);
            }
            if (endTime != null) {
                criteria.andLessThanOrEqualTo("logTime", endTime);
            }

            List<DataInspectionTaskLogEntity> list = taskLogMapper.selectByExample(example);
            PageInfo<DataInspectionTaskLogEntity> pageInfo = new PageInfo<>(list);
            
            log.debug("获取执行日志列表成功, 执行ID: {}, 总数: {}", executionId, pageInfo.getTotal());
            return new PageResult<>(list, pageInfo.getTotal());
        } catch (Exception e) {
            log.error("获取执行日志列表失败, 执行ID: {}", executionId, e);
            throw new RuntimeException("获取执行日志列表失败", e);
        }
    }

    /**
     * 获取执行的所有日志
     */
    public List<DataInspectionTaskLogEntity> getAllLogs(String executionId) {
        try {
            Example example = new Example(DataInspectionTaskLogEntity.class);
            example.createCriteria().andEqualTo("executionId", executionId);
            example.setOrderByClause("log_time ASC");

            List<DataInspectionTaskLogEntity> list = taskLogMapper.selectByExample(example);
            log.debug("获取执行所有日志成功, 执行ID: {}, 数量: {}", executionId, list.size());
            return list;
        } catch (Exception e) {
            log.error("获取执行所有日志失败, 执行ID: {}", executionId, e);
            throw new RuntimeException("获取执行所有日志失败", e);
        }
    }

    /**
     * 获取最近的日志
     */
    public List<DataInspectionTaskLogEntity> getRecentLogs(String executionId, int limit) {
        try {
            Example example = new Example(DataInspectionTaskLogEntity.class);
            example.createCriteria().andEqualTo("executionId", executionId);
            example.setOrderByClause("log_time DESC");
            
            PageHelper.startPage(1, limit);
            List<DataInspectionTaskLogEntity> list = taskLogMapper.selectByExample(example);
            
            log.debug("获取最近日志成功, 执行ID: {}, 数量: {}", executionId, list.size());
            return list;
        } catch (Exception e) {
            log.error("获取最近日志失败, 执行ID: {}", executionId, e);
            throw new RuntimeException("获取最近日志失败", e);
        }
    }

    /**
     * 获取错误日志
     */
    public List<DataInspectionTaskLogEntity> getErrorLogs(String executionId) {
        try {
            DataInspectionTaskLogEntity example = new DataInspectionTaskLogEntity();
            example.setExecutionId(executionId);
            example.setLogLevel(DataInspectionTaskLogEntity.LogLevel.ERROR.getCode());

            List<DataInspectionTaskLogEntity> list = taskLogMapper.select(example);
            log.debug("获取错误日志成功, 执行ID: {}, 数量: {}", executionId, list.size());
            return list;
        } catch (Exception e) {
            log.error("获取错误日志失败, 执行ID: {}", executionId, e);
            throw new RuntimeException("获取错误日志失败", e);
        }
    }

    /**
     * 删除执行的所有日志
     */
    @Transactional
    public int deleteLogsByExecutionId(String executionId) {
        try {
            DataInspectionTaskLogEntity example = new DataInspectionTaskLogEntity();
            example.setExecutionId(executionId);
            
            int result = taskLogMapper.delete(example);
            log.info("删除执行日志成功, 执行ID: {}, 删除数量: {}", executionId, result);
            return result;
        } catch (Exception e) {
            log.error("删除执行日志失败, 执行ID: {}", executionId, e);
            throw new RuntimeException("删除执行日志失败", e);
        }
    }

    /**
     * 根据ID删除日志
     */
    @Transactional
    public boolean deleteLog(Long id) {
        try {
            int result = taskLogMapper.deleteByPrimaryKey(id);
            log.info("删除任务日志成功, ID: {}", id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除任务日志失败, ID: {}", id, e);
            throw new RuntimeException("删除任务日志失败", e);
        }
    }

    /**
     * 清理历史日志
     */
    @Transactional
    public int cleanupLogs(int keepDays) {
        try {
            Date cutoffDate = new Date(System.currentTimeMillis() - (long) keepDays * 24 * 60 * 60 * 1000);
            
            Example example = new Example(DataInspectionTaskLogEntity.class);
            example.createCriteria().andLessThan("logTime", cutoffDate);

            int deletedCount = taskLogMapper.deleteByExample(example);
            log.info("清理任务日志记录完成, 删除数量: {}, 保留天数: {}", deletedCount, keepDays);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理任务日志记录失败", e);
            throw new RuntimeException("清理任务日志记录失败", e);
        }
    }

    /**
     * 获取调用者类名
     */
    private String getCallerClassName() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            // 跳过当前方法和调用的公共方法
            for (int i = 2; i < Math.min(stackTrace.length, 10); i++) {
                String className = stackTrace[i].getClassName();
                if (!className.equals(this.getClass().getName()) && 
                    !className.startsWith("java.") && 
                    !className.startsWith("sun.")) {
                    return className;
                }
            }
            return "Unknown";
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * 批量添加日志
     */
    @Transactional
    public void addLogs(List<DataInspectionTaskLogEntity> logs) {
        try {
            if (logs != null && !logs.isEmpty()) {
                for (DataInspectionTaskLogEntity log : logs) {
                    if (log.getLogTime() == null) {
                        log.setLogTime(new Date());
                    }
                    taskLogMapper.insertSelective(log);
                }
                log.debug("批量添加任务日志成功, 数量: {}", logs.size());
            }
        } catch (Exception e) {
            log.error("批量添加任务日志失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}