package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionRuleMapper;
import com.goodsogood.ows.mapper.DataInspectionRuleDetailMapper;
import com.goodsogood.ows.model.db.DataInspectionRuleEntity;
import com.goodsogood.ows.model.db.DataInspectionRuleDetailEntity;
import com.goodsogood.ows.model.vo.PageResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据体检规则业务逻辑层
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionRuleService {

    @Autowired
    private DataInspectionRuleMapper dataInspectionRuleMapper;
    
    @Autowired
    private DataInspectionRuleDetailMapper dataInspectionRuleDetailMapper;

    /**
     * 创建数据体检规则
     * 
     * @param entity 规则实体
     * @param sysHeader 系统头信息
     * @return 规则实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionRuleEntity create(DataInspectionRuleEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreator(sysHeader.getUserName());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 设置默认值
        if (entity.getIsEnabled() == null) {
            entity.setIsEnabled(true);
        }
        if (entity.getSeverity() == null) {
            entity.setSeverity("medium");
        }

        int result = dataInspectionRuleMapper.insert(entity);
        if (result > 0) {
            log.info("创建数据体检规则成功，ID: {}, 名称: {}", entity.getId(), entity.getRuleName());
            return entity;
        } else {
            throw new RuntimeException("创建数据体检规则失败");
        }
    }

    /**
     * 创建规则及详情
     * 
     * @param entity 规则实体
     * @param details 规则详情列表
     * @param sysHeader 系统头信息
     * @return 规则实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionRuleEntity createWithDetails(DataInspectionRuleEntity entity, List<DataInspectionRuleDetailEntity> details, HeaderHelper.SysHeader sysHeader) {
        // 创建规则
        entity = create(entity, sysHeader);
        
        // 创建规则详情
        if (details != null && !details.isEmpty()) {
            for (DataInspectionRuleDetailEntity detail : details) {
                detail.setRuleId(entity.getId());
                detail.setCreateTime(new Date());
            }
            dataInspectionRuleDetailMapper.batchInsert(details);
            log.info("创建规则详情成功，规则ID: {}, 详情数量: {}", entity.getId(), details.size());
        }
        
        return entity;
    }

    /**
     * 更新数据体检规则
     * 
     * @param entity 规则实体
     * @param sysHeader 系统头信息
     * @return 规则实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionRuleEntity update(DataInspectionRuleEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionRuleEntity existing = dataInspectionRuleMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("数据体检规则不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        int result = dataInspectionRuleMapper.updateById(entity);
        if (result > 0) {
            log.info("更新数据体检规则成功，ID: {}, 名称: {}", entity.getId(), entity.getRuleName());
            return dataInspectionRuleMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新数据体检规则失败");
        }
    }

    /**
     * 更新规则及详情
     * 
     * @param entity 规则实体
     * @param details 规则详情列表
     * @param sysHeader 系统头信息
     * @return 规则实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionRuleEntity updateWithDetails(DataInspectionRuleEntity entity, List<DataInspectionRuleDetailEntity> details, HeaderHelper.SysHeader sysHeader) {
        // 更新规则
        entity = update(entity, sysHeader);
        
        // 删除原有详情
        dataInspectionRuleDetailMapper.deleteByRuleId(entity.getId());
        
        // 创建新的规则详情
        if (details != null && !details.isEmpty()) {
            for (DataInspectionRuleDetailEntity detail : details) {
                detail.setRuleId(entity.getId());
                detail.setCreateTime(new Date());
            }
            dataInspectionRuleDetailMapper.batchInsert(details);
            log.info("更新规则详情成功，规则ID: {}, 详情数量: {}", entity.getId(), details.size());
        }
        
        return entity;
    }

    /**
     * 删除数据体检规则
     * 
     * @param id 规则ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionRuleEntity existing = dataInspectionRuleMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("数据体检规则不存在");
        }

        // 删除规则详情
        dataInspectionRuleDetailMapper.deleteByRuleId(id);
        
        // 删除规则
        int result = dataInspectionRuleMapper.deleteById(id);
        if (result > 0) {
            log.info("删除数据体检规则成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除数据体检规则失败");
        }
    }

    /**
     * 根据ID查询数据体检规则
     * 
     * @param id 规则ID
     * @return 规则实体
     */
    public DataInspectionRuleEntity findById(Long id) {
        return dataInspectionRuleMapper.findById(id);
    }

    /**
     * 根据ID查询规则及详情
     * 
     * @param id 规则ID
     * @return 包含详情的规则信息
     */
    public Map<String, Object> findWithDetailsById(Long id) {
        DataInspectionRuleEntity rule = dataInspectionRuleMapper.findById(id);
        if (rule == null) {
            return null;
        }
        
        List<DataInspectionRuleDetailEntity> details = dataInspectionRuleDetailMapper.findByRuleId(id);
        
        Map<String, Object> result = new HashMap<>();
        result.put("rule", rule);
        result.put("details", details);
        
        return result;
    }

    /**
     * 分页查询数据体检规则列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<DataInspectionRuleEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<DataInspectionRuleEntity> list = dataInspectionRuleMapper.findPage(params);
        int total = dataInspectionRuleMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 启用/禁用数据体检规则
     * 
     * @param id 规则ID
     * @param isEnabled 是否启用
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleEnable(Long id, Boolean isEnabled, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionRuleMapper.toggleEnable(id, isEnabled);
        if (result > 0) {
            log.info("{}数据体检规则成功，ID: {}", isEnabled ? "启用" : "禁用", id);
            return true;
        } else {
            throw new RuntimeException((isEnabled ? "启用" : "禁用") + "数据体检规则失败");
        }
    }

    /**
     * 批量启用/禁用规则
     * 
     * @param ids 规则ID列表
     * @param isEnabled 是否启用
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchToggleEnable(List<Long> ids, Boolean isEnabled, HeaderHelper.SysHeader sysHeader) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("规则ID列表不能为空");
        }

        int result = dataInspectionRuleMapper.batchToggleEnable(ids, isEnabled);
        if (result > 0) {
            log.info("批量{}规则成功，更新数量: {}", isEnabled ? "启用" : "禁用", result);
            return true;
        } else {
            throw new RuntimeException("批量" + (isEnabled ? "启用" : "禁用") + "规则失败");
        }
    }

    /**
     * 按规则类型统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByRuleType() {
        return dataInspectionRuleMapper.countByRuleType();
    }

    /**
     * 按表名统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByTableName() {
        return dataInspectionRuleMapper.countByTableName();
    }

    /**
     * 按严重程度统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countBySeverity() {
        return dataInspectionRuleMapper.countBySeverity();
    }

    /**
     * 获取规则概览统计
     * 
     * @return 统计结果
     */
    public Map<String, Object> getOverviewStatistics() {
        return dataInspectionRuleMapper.getOverviewStatistics();
    }

    /**
     * 查询启用的规则
     * 
     * @return 规则列表
     */
    public List<DataInspectionRuleEntity> findAllEnabled() {
        return dataInspectionRuleMapper.findAllEnabled();
    }

    /**
     * 根据表名查询启用的规则
     * 
     * @param tableName 表名
     * @return 规则列表
     */
    public List<DataInspectionRuleEntity> findEnabledByTableName(String tableName) {
        return dataInspectionRuleMapper.findEnabledByTableName(tableName);
    }

    /**
     * 构建查询参数
     * 
     * @param ruleName 规则名称
     * @param ruleType 规则类型
     * @param tableName 表名
     * @param isEnabled 是否启用
     * @param severity 严重程度
     * @param creator 创建人
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String ruleName, String ruleType, String tableName, 
                                                Boolean isEnabled, String severity, String creator) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(ruleName)) {
            params.put("ruleName", ruleName.trim());
        }
        if (StringUtils.hasText(ruleType)) {
            params.put("ruleType", ruleType.trim());
        }
        if (StringUtils.hasText(tableName)) {
            params.put("tableName", tableName.trim());
        }
        if (isEnabled != null) {
            params.put("isEnabled", isEnabled);
        }
        if (StringUtils.hasText(severity)) {
            params.put("severity", severity.trim());
        }
        if (StringUtils.hasText(creator)) {
            params.put("creator", creator.trim());
        }
        
        return params;
    }

    /**
     * 验证规则配置
     * 
     * @param entity 规则实体
     * @param details 规则详情列表
     * @return 验证结果
     */
    public Map<String, Object> validateRule(DataInspectionRuleEntity entity, List<DataInspectionRuleDetailEntity> details) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        
        // 验证规则基本信息
        if (!StringUtils.hasText(entity.getRuleName())) {
            errors.add("规则名称不能为空");
        }
        if (!StringUtils.hasText(entity.getRuleType())) {
            errors.add("规则类型不能为空");
        }
        if (!StringUtils.hasText(entity.getCheckLogic())) {
            errors.add("检查逻辑不能为空");
        }
        
        // 验证规则详情
        if (details != null && !details.isEmpty()) {
            for (int i = 0; i < details.size(); i++) {
                DataInspectionRuleDetailEntity detail = details.get(i);
                if (!StringUtils.hasText(detail.getFieldName())) {
                    errors.add("第" + (i + 1) + "个规则详情的字段名不能为空");
                }
                if (!StringUtils.hasText(detail.getRuleType())) {
                    errors.add("第" + (i + 1) + "个规则详情的规则类型不能为空");
                }
                if (!StringUtils.hasText(detail.getRuleValue())) {
                    errors.add("第" + (i + 1) + "个规则详情的规则值不能为空");
                }
            }
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        
        return result;
    }

    /**
     * 复制规则
     * 
     * @param id 原规则ID
     * @param newRuleName 新规则名称
     * @param sysHeader 系统头信息
     * @return 新规则实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionRuleEntity copyRule(Long id, String newRuleName, HeaderHelper.SysHeader sysHeader) {
        DataInspectionRuleEntity originalRule = dataInspectionRuleMapper.findById(id);
        if (originalRule == null) {
            throw new RuntimeException("原规则不存在");
        }
        
        List<DataInspectionRuleDetailEntity> originalDetails = dataInspectionRuleDetailMapper.findByRuleId(id);
        
        // 创建新规则
        DataInspectionRuleEntity newRule = new DataInspectionRuleEntity();
        newRule.setRuleName(newRuleName);
        newRule.setRuleType(originalRule.getRuleType());
        newRule.setTableName(originalRule.getTableName());
        newRule.setFieldName(originalRule.getFieldName());
        newRule.setCheckLogic(originalRule.getCheckLogic());
        newRule.setThresholdValue(originalRule.getThresholdValue());
        newRule.setIsEnabled(false); // 默认禁用
        newRule.setSeverity(originalRule.getSeverity());
        newRule.setDescription(originalRule.getDescription() + " (复制)");
        
        newRule = create(newRule, sysHeader);
        
        // 复制规则详情
        if (originalDetails != null && !originalDetails.isEmpty()) {
            List<DataInspectionRuleDetailEntity> newDetails = new ArrayList<>();
            for (DataInspectionRuleDetailEntity originalDetail : originalDetails) {
                DataInspectionRuleDetailEntity newDetail = new DataInspectionRuleDetailEntity();
                newDetail.setRuleId(newRule.getId());
                newDetail.setFieldName(originalDetail.getFieldName());
                newDetail.setRuleType(originalDetail.getRuleType());
                newDetail.setRuleValue(originalDetail.getRuleValue());
                newDetail.setErrorMessage(originalDetail.getErrorMessage());
                newDetail.setCreateTime(new Date());
                newDetails.add(newDetail);
            }
            dataInspectionRuleDetailMapper.batchInsert(newDetails);
        }
        
        log.info("复制规则成功，原规则ID: {}, 新规则ID: {}", id, newRule.getId());
        return newRule;
    }

    /**
     * 根据规则分类获取规则列表
     * 
     * @param category 规则分类 (partyOrganization, partyStaff, taskCheck, userInfo)
     * @return 规则列表
     */
    public List<DataInspectionRuleEntity> getRulesByCategory(String category) {
        Map<String, Object> params = new HashMap<>();
        params.put("ruleCategory", category);
        params.put("isEnabled", true);
        
        List<DataInspectionRuleEntity> rules = dataInspectionRuleMapper.findPage(params);
        log.info("获取{}分类规则成功，数量: {}", category, rules.size());
        return rules;
    }

    /**
     * 批量更新规则
     * 
     * @param category 规则分类
     * @param rulesData 规则数据列表
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateRules(String category, List<Map<String, Object>> rulesData, Long userId) {
        if (rulesData == null || rulesData.isEmpty()) {
            log.warn("批量更新规则数据为空");
            return;
        }

        for (Map<String, Object> ruleData : rulesData) {
            try {
                Long ruleId = ruleData.get("id") != null ? 
                    ((Number) ruleData.get("id")).longValue() : null;
                
                if (ruleId != null) {
                    // 更新现有规则
                    DataInspectionRuleEntity rule = new DataInspectionRuleEntity();
                    rule.setId(ruleId);
                    rule.setRuleName((String) ruleData.get("ruleName"));
                    rule.setRuleCategory(category);
                    rule.setDescription((String) ruleData.get("description"));
                    rule.setIsEnabled((Boolean) ruleData.getOrDefault("isEnabled", true));
                    rule.setUpdateTime(new Date());
                    
                    dataInspectionRuleMapper.updateById(rule);
                    log.info("更新规则成功，ID: {}", ruleId);
                } else {
                    // 创建新规则
                    DataInspectionRuleEntity rule = new DataInspectionRuleEntity();
                    rule.setRuleName((String) ruleData.get("ruleName"));
                    rule.setRuleCategory(category);
                    rule.setRuleType((String) ruleData.getOrDefault("ruleType", "custom"));
                    rule.setDescription((String) ruleData.get("description"));
                    rule.setIsEnabled((Boolean) ruleData.getOrDefault("isEnabled", true));
                    rule.setSeverity((String) ruleData.getOrDefault("severity", "medium"));
                    rule.setCheckLogic((String) ruleData.getOrDefault("checkLogic", "{}"));
                    rule.setCreateTime(new Date());
                    rule.setUpdateTime(new Date());
                    
                    dataInspectionRuleMapper.insert(rule);
                    log.info("创建新规则成功，名称: {}", rule.getRuleName());
                }
            } catch (Exception e) {
                log.error("批量更新规则失败，规则数据: {}", ruleData, e);
                throw new RuntimeException("批量更新规则失败: " + e.getMessage());
            }
        }
        
        log.info("批量更新{}分类规则成功，更新数量: {}", category, rulesData.size());
    }
}