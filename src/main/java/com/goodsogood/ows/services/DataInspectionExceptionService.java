package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionExceptionMapper;
import com.goodsogood.ows.model.db.DataInspectionExceptionEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据体检异常业务逻辑层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionExceptionService {

    @Autowired
    private DataInspectionExceptionMapper dataInspectionExceptionMapper;

    /**
     * 创建数据体检异常
     *
     * @param entity    异常实体
     * @param sysHeader 系统头信息
     * @return 异常实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionExceptionEntity create(DataInspectionExceptionEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        // 设置默认值
        if (entity.getDetectTime() == null) {
            entity.setDetectTime(new Date());
        }
        if (entity.getStatus() == null) {
            entity.setStatus("pending");
        }
        if (entity.getAffectedRecords() == null) {
            entity.setAffectedRecords(0);
        }

        int result = dataInspectionExceptionMapper.insert(entity);
        if (result > 0) {
            log.info("创建数据体检异常成功，ID: {}, 类型: {}", entity.getId(), entity.getType());
            return entity;
        } else {
            throw new RuntimeException("创建数据体检异常失败");
        }
    }

    /**
     * 更新数据体检异常
     *
     * @param entity    异常实体
     * @param sysHeader 系统头信息
     * @return 异常实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionExceptionEntity update(DataInspectionExceptionEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionExceptionEntity existing = dataInspectionExceptionMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        int result = dataInspectionExceptionMapper.updateById(entity);
        if (result > 0) {
            log.info("更新数据体检异常成功，ID: {}, 类型: {}", entity.getId(), entity.getType());
            return dataInspectionExceptionMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新数据体检异常失败");
        }
    }

    /**
     * 删除数据体检异常
     *
     * @param id        异常ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionExceptionEntity existing = dataInspectionExceptionMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        int result = dataInspectionExceptionMapper.deleteById(id);
        if (result > 0) {
            log.info("删除数据体检异常成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除数据体检异常失败");
        }
    }

    /**
     * 批量更新异常状态
     *
     * @param ids       异常ID列表
     * @param status    状态
     * @param handler   处理人
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, String status, String handler, HeaderHelper.SysHeader sysHeader) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("异常ID列表不能为空");
        }

        int result = dataInspectionExceptionMapper.batchUpdateStatus(ids, status, handler);
        if (result > 0) {
            log.info("批量更新异常状态成功，更新数量: {}", result);
            return true;
        } else {
            throw new RuntimeException("批量更新异常状态失败");
        }
    }

    /**
     * 根据ID查询数据体检异常
     *
     * @param id 异常ID
     * @return 异常实体
     */
    public DataInspectionExceptionEntity findById(Long id) {
        return dataInspectionExceptionMapper.findById(id);
    }

    /**
     * 分页查询数据体检异常列表
     *
     * @param page      页码
     * @param pageSize  每页大小
     * @param params    查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<DataInspectionExceptionEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<DataInspectionExceptionEntity> list = dataInspectionExceptionMapper.findPage(params);
        int total = dataInspectionExceptionMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 按异常类型统计
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> countByType() {
        return dataInspectionExceptionMapper.countByType();
    }

    /**
     * 按单位统计
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> countByUnit() {
        return dataInspectionExceptionMapper.countByUnit();
    }

    /**
     * 获取异常概览统计
     *
     * @return 统计结果
     */
    public Map<String, Object> getOverviewStatistics() {
        return dataInspectionExceptionMapper.getOverviewStatistics();
    }

    /**
     * 按严重程度统计
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> countBySeverity() {
        return dataInspectionExceptionMapper.countBySeverity();
    }

    /**
     * 按状态统计
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus() {
        return dataInspectionExceptionMapper.countByStatus();
    }

    /**
     * 查询最近的异常记录
     *
     * @param days  天数
     * @param limit 限制数量
     * @return 异常记录列表
     */
    public List<DataInspectionExceptionEntity> findRecentExceptions(Integer days, Integer limit) {
        return dataInspectionExceptionMapper.findRecentExceptions(limit);
    }

    /**
     * 构建查询参数
     *
     * @param type      异常类型
     * @param severity  严重程度
     * @param status    状态
     * @param unit      单位
     * @param source    来源
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String type, String severity, String status, String unit,
                                                String source, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();

        if (StringUtils.hasText(type)) {
            params.put("type", type.trim());
        }
        if (StringUtils.hasText(severity)) {
            params.put("severity", severity.trim());
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status.trim());
        }
        if (StringUtils.hasText(unit)) {
            params.put("unit", unit.trim());
        }
        if (StringUtils.hasText(source)) {
            params.put("source", source.trim());
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }

        return params;
    }

    /**
     * 根据查询参数查找异常列表
     *
     * @param params 查询参数
     * @return 异常列表
     */
    public List<DataInspectionExceptionEntity> find(Map<String, Object> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        return dataInspectionExceptionMapper.findPage(params);
    }

    /**
     * 处理异常
     *
     * @param id        异常ID
     * @param status    处理状态
     * @param solution  解决方案
     * @param handler   处理人
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handleException(Long id, String status, String solution, String handler, HeaderHelper.SysHeader sysHeader) {
        DataInspectionExceptionEntity entity = dataInspectionExceptionMapper.findById(id);
        if (entity == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        entity.setStatus(status);
        entity.setSolution(solution);
        entity.setHandler(handler);
        entity.setHandleTime(new Date());
        entity.setUpdateTime(new Date());

        int result = dataInspectionExceptionMapper.updateById(entity);
        if (result > 0) {
            log.info("处理数据体检异常成功，ID: {}, 状态: {}", id, status);
            return true;
        } else {
            throw new RuntimeException("处理数据体检异常失败");
        }
    }

    /**
     * 获取异常趋势数据
     *
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getExceptionTrend(Integer days) {
        // 这里可以根据需要实现趋势分析逻辑
        // 暂时返回最近异常记录作为示例
        List<DataInspectionExceptionEntity> recentExceptions = findRecentExceptions(days, 100);

        List<Map<String, Object>> trendData = new ArrayList<>();
        // 按日期分组统计
        Map<String, Integer> dateCountMap = new HashMap<>();

        for (DataInspectionExceptionEntity exception : recentExceptions) {
            String date = exception.getDetectTime().toString().substring(0, 10);
            dateCountMap.put(date, dateCountMap.getOrDefault(date, 0) + 1);
        }

        for (Map.Entry<String, Integer> entry : dateCountMap.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", entry.getKey());
            item.put("count", entry.getValue());
            trendData.add(item);
        }

        return trendData;
    }

    /**
     * 获取异常总数
     *
     * @return 异常总数
     */
    public long countTotal() {
        return dataInspectionExceptionMapper.count(new HashMap<>());
    }

    /**
     * 按等级统计异常数量
     *
     * @param level 等级
     * @return 统计数量
     */
    public long countByLevel(String level) {
        Map<String, Object> params = new HashMap<>();
        params.put("severity", level);
        return dataInspectionExceptionMapper.count(params);
    }

    /**
     * 按状态统计异常数量
     *
     * @param status 状态
     * @return 统计数量
     */
    public long countByStatus(String status) {
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        return dataInspectionExceptionMapper.count(params);
    }

    /**
     * 获取检查类型统计
     *
     * @return 统计结果
     */
    public List<Map<String, Object>> getCheckTypeStats() {
        return dataInspectionExceptionMapper.getCheckTypeStats();
    }

    /**
     * 获取异常趋势（重载方法）
     *
     * @return 趋势数据
     */
    public List<Map<String, Object>> getExceptionTrend() {
        return getExceptionTrend(30); // 默认30天
    }

    /**
     * 执行健康检查
     *
     * @param checkTypes 检查类型列表
     * @param taskId     任务ID
     * @return 发现的异常列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<DataInspectionExceptionEntity> executeHealthCheck(List<Integer> checkTypes, Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();

        log.info("开始执行健康检查，任务ID: {}, 检查类型: {}", taskId, checkTypes);

        try {
            for (Integer checkType : checkTypes) {
                List<DataInspectionExceptionEntity> typeExceptions = executeCheckByType(checkType, taskId);
                exceptions.addAll(typeExceptions);
            }

            // 批量保存异常记录
            if (!exceptions.isEmpty()) {
                for (DataInspectionExceptionEntity exception : exceptions) {
                    dataInspectionExceptionMapper.insert(exception);
                }
                log.info("健康检查完成，发现异常: {}个", exceptions.size());
            }

        } catch (Exception e) {
            log.error("执行健康检查失败，任务ID: {}", taskId, e);
            throw new RuntimeException("健康检查执行失败: " + e.getMessage());
        }

        return exceptions;
    }

    /**
     * 按类型执行检查
     *
     * @param checkType 检查类型
     * @param taskId    任务ID
     * @return 发现的异常列表
     */
    private List<DataInspectionExceptionEntity> executeCheckByType(Integer checkType, Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();

        switch (checkType) {
            case 1: // 党组织设置检查
                exceptions.addAll(checkPartyOrganization(taskId));
                break;
            case 2: // 党务干部任免检查
                exceptions.addAll(checkPartyStaff(taskId));
                break;
            case 3: // 任务执行检查
                exceptions.addAll(checkTaskExecution(taskId));
                break;
            case 4: // 用户信息完整性检查
                exceptions.addAll(checkUserInfoCompleteness(taskId));
                break;
            default:
                log.warn("不支持的检查类型: {}", checkType);
        }

        return exceptions;
    }

    /**
     * 党组织设置检查
     */
    private List<DataInspectionExceptionEntity> checkPartyOrganization(Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();

        // 模拟检查逐辑，实际中需要根据具体业务规则实现
        DataInspectionExceptionEntity exception = new DataInspectionExceptionEntity();
        exception.setType("数据缺失");
        exception.setTitle("党组织信息不完整");
        exception.setDescription("部分党组织缺少书记信息，影响组织架构完整性");
        exception.setUnit("第三党支部");
        exception.setSource("组织管理系统");
        exception.setSeverity("中级");
        exception.setStatus("待处理");
        exception.setAffectedRecords(1);
        exception.setDetectTime(new Date());
        exception.setImpact("组织架构不完整");
        exception.setSolution("请补充完善党组织书记信息");
        exception.setCreateTime(new Date());
        exception.setUpdateTime(new Date());

        exceptions.add(exception);
        return exceptions;
    }

    /**
     * 党务干部任免检查
     */
    private List<DataInspectionExceptionEntity> checkPartyStaff(Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();
        // 模拟检查逐辑
        return exceptions;
    }

    /**
     * 任务执行检查
     */
    private List<DataInspectionExceptionEntity> checkTaskExecution(Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();
        // 模拟检查逐辆
        return exceptions;
    }

    /**
     * 用户信息完整性检查
     */
    private List<DataInspectionExceptionEntity> checkUserInfoCompleteness(Long taskId) {
        List<DataInspectionExceptionEntity> exceptions = new ArrayList<>();
        // 模拟检查造辑
        return exceptions;
    }

    /**
     * 获取异常列表
     *
     * @param exceptionType  异常类型
     * @param status         状态
     * @param affectedObject 影响对象
     * @param pageNum        页码
     * @param pageSize       每页数量
     * @return 分页结果
     */
    public PageResult<DataInspectionExceptionEntity> getExceptionList(
            String exceptionType, String status, String affectedObject,
            Integer pageNum, Integer pageSize) {

        Map<String, Object> params = buildQueryParams(
                exceptionType, null, status, affectedObject, null, null, null);

        return findByPage(pageNum, pageSize, params, null);
    }

    /**
     * 根据ID获取异常
     *
     * @param id 异常ID
     * @return 异常实体
     */
    public DataInspectionExceptionEntity getById(Long id) {
        return findById(id);
    }

    /**
     * 执行复检
     *
     * @param exceptionId 异常ID
     * @param userId      用户ID
     * @return 复检任务ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long executeRecheck(Long exceptionId, Long userId) {
        DataInspectionExceptionEntity exception = findById(exceptionId);
        if (exception == null) {
            throw new RuntimeException("异常记录不存在");
        }

        // 模拟复检逐辑，返回一个模拟的任务ID
        Long recheckTaskId = System.currentTimeMillis();

        log.info("开始执行复检，异常ID: {}, 复检任务ID: {}", exceptionId, recheckTaskId);

        return recheckTaskId;
    }
}