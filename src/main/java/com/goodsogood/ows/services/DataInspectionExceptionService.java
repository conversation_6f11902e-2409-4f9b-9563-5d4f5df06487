package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionExceptionMapper;
import com.goodsogood.ows.model.db.DataInspectionExceptionEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据体检异常业务逻辑层
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionExceptionService {

    @Autowired
    private DataInspectionExceptionMapper dataInspectionExceptionMapper;

    /**
     * 创建数据体检异常
     * 
     * @param entity 异常实体
     * @param sysHeader 系统头信息
     * @return 异常实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionExceptionEntity create(DataInspectionExceptionEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 设置默认值
        if (entity.getDetectTime() == null) {
            entity.setDetectTime(new Date());
        }
        if (entity.getStatus() == null) {
            entity.setStatus("pending");
        }
        if (entity.getAffectedRecords() == null) {
            entity.setAffectedRecords(0);
        }

        int result = dataInspectionExceptionMapper.insert(entity);
        if (result > 0) {
            log.info("创建数据体检异常成功，ID: {}, 类型: {}", entity.getId(), entity.getType());
            return entity;
        } else {
            throw new RuntimeException("创建数据体检异常失败");
        }
    }

    /**
     * 更新数据体检异常
     * 
     * @param entity 异常实体
     * @param sysHeader 系统头信息
     * @return 异常实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionExceptionEntity update(DataInspectionExceptionEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionExceptionEntity existing = dataInspectionExceptionMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        int result = dataInspectionExceptionMapper.updateById(entity);
        if (result > 0) {
            log.info("更新数据体检异常成功，ID: {}, 类型: {}", entity.getId(), entity.getType());
            return dataInspectionExceptionMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新数据体检异常失败");
        }
    }

    /**
     * 删除数据体检异常
     * 
     * @param id 异常ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionExceptionEntity existing = dataInspectionExceptionMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        int result = dataInspectionExceptionMapper.deleteById(id);
        if (result > 0) {
            log.info("删除数据体检异常成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除数据体检异常失败");
        }
    }

    /**
     * 批量更新异常状态
     * 
     * @param ids 异常ID列表
     * @param status 状态
     * @param handler 处理人
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, String status, String handler, HeaderHelper.SysHeader sysHeader) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("异常ID列表不能为空");
        }

        int result = dataInspectionExceptionMapper.batchUpdateStatus(ids, status, handler);
        if (result > 0) {
            log.info("批量更新异常状态成功，更新数量: {}", result);
            return true;
        } else {
            throw new RuntimeException("批量更新异常状态失败");
        }
    }

    /**
     * 根据ID查询数据体检异常
     * 
     * @param id 异常ID
     * @return 异常实体
     */
    public DataInspectionExceptionEntity findById(Long id) {
        return dataInspectionExceptionMapper.findById(id);
    }

    /**
     * 分页查询数据体检异常列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<DataInspectionExceptionEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<DataInspectionExceptionEntity> list = dataInspectionExceptionMapper.findPage(params);
        int total = dataInspectionExceptionMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 按异常类型统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByType() {
        return dataInspectionExceptionMapper.countByType();
    }

    /**
     * 按单位统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByUnit() {
        return dataInspectionExceptionMapper.countByUnit();
    }

    /**
     * 获取异常概览统计
     * 
     * @return 统计结果
     */
    public Map<String, Object> getOverviewStatistics() {
        return dataInspectionExceptionMapper.getOverviewStatistics();
    }

    /**
     * 按严重程度统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countBySeverity() {
        return dataInspectionExceptionMapper.countBySeverity();
    }

    /**
     * 按状态统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus() {
        return dataInspectionExceptionMapper.countByStatus();
    }

    /**
     * 查询最近的异常记录
     * 
     * @param days 天数
     * @param limit 限制数量
     * @return 异常记录列表
     */
    public List<DataInspectionExceptionEntity> findRecentExceptions(Integer days, Integer limit) {
        return dataInspectionExceptionMapper.findRecentExceptions(days, limit);
    }

    /**
     * 构建查询参数
     * 
     * @param type 异常类型
     * @param severity 严重程度
     * @param status 状态
     * @param unit 单位
     * @param source 来源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String type, String severity, String status, String unit, 
                                                String source, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(type)) {
            params.put("type", type.trim());
        }
        if (StringUtils.hasText(severity)) {
            params.put("severity", severity.trim());
        }
        if (StringUtils.hasText(status)) {
            params.put("status", status.trim());
        }
        if (StringUtils.hasText(unit)) {
            params.put("unit", unit.trim());
        }
        if (StringUtils.hasText(source)) {
            params.put("source", source.trim());
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }
        
        return params;
    }

    /**
     * 处理异常
     * 
     * @param id 异常ID
     * @param status 处理状态
     * @param solution 解决方案
     * @param handler 处理人
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handleException(Long id, String status, String solution, String handler, HeaderHelper.SysHeader sysHeader) {
        DataInspectionExceptionEntity entity = dataInspectionExceptionMapper.findById(id);
        if (entity == null) {
            throw new RuntimeException("数据体检异常不存在");
        }

        entity.setStatus(status);
        entity.setSolution(solution);
        entity.setHandler(handler);
        entity.setHandleTime(new Date());
        entity.setUpdateTime(new Date());

        int result = dataInspectionExceptionMapper.updateById(entity);
        if (result > 0) {
            log.info("处理数据体检异常成功，ID: {}, 状态: {}", id, status);
            return true;
        } else {
            throw new RuntimeException("处理数据体检异常失败");
        }
    }

    /**
     * 获取异常趋势数据
     * 
     * @param days 天数
     * @return 趋势数据
     */
    public List<Map<String, Object>> getExceptionTrend(Integer days) {
        // 这里可以根据需要实现趋势分析逻辑
        // 暂时返回最近异常记录作为示例
        List<DataInspectionExceptionEntity> recentExceptions = findRecentExceptions(days, 100);
        
        List<Map<String, Object>> trendData = new ArrayList<>();
        // 按日期分组统计
        Map<String, Integer> dateCountMap = new HashMap<>();
        
        for (DataInspectionExceptionEntity exception : recentExceptions) {
            String date = exception.getDetectTime().toString().substring(0, 10);
            dateCountMap.put(date, dateCountMap.getOrDefault(date, 0) + 1);
        }
        
        for (Map.Entry<String, Integer> entry : dateCountMap.entrySet()) {
            Map<String, Object> item = new HashMap<>();
            item.put("date", entry.getKey());
            item.put("count", entry.getValue());
            trendData.add(item);
        }
        
        return trendData;
    }
}