package com.goodsogood.ows.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.goodsogood.ows.mapper.DataInspectionRuleConfigMapper;
import com.goodsogood.ows.model.db.DataInspectionRuleDetailEntity;
import com.goodsogood.ows.model.vo.DataInspectionRuleVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 数据体检规则配置服务
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Slf4j
@Service
public class DataInspectionRuleConfigService {

    @Resource
    private DataInspectionRuleConfigMapper ruleMapper;

    /**
     * 分页查询规则列表
     */
    @Cacheable(value = "inspection:rules", key = "'list:' + #searchVO.ruleType + ':' + #searchVO.pageNum + ':' + #searchVO.pageSize", unless = "#result == null")
    public List<DataInspectionRuleVO.HealthCheckRuleVO> getRuleList(DataInspectionRuleVO.RuleSearchVO searchVO) {
        log.info("分页查询体检规则列表，查询条件: {}", searchVO);

        // 计算分页参数
        int pageNum = searchVO.getPageNum() != null ? searchVO.getPageNum() : 1;
        int pageSize = searchVO.getPageSize() != null ? searchVO.getPageSize() : 10;
        int offset = (pageNum - 1) * pageSize;

        // 查询数据列表
        List<DataInspectionRuleVO.HealthCheckRuleVO> ruleList = ruleMapper.selectRuleListPage(
                searchVO.getRuleName(),
                searchVO.getRuleType(),
                searchVO.getIsEnabled(),
                pageSize,
                offset
        );

        // 查询总数
        Long total = ruleMapper.countRuleList(
                searchVO.getRuleName(),
                searchVO.getRuleType(),
                searchVO.getIsEnabled()
        );

        // 填充规则内容
        for (DataInspectionRuleVO.HealthCheckRuleVO rule : ruleList) {
            if (rule.getId() != null) {
                String ruleContent = buildRuleContentFromDetails(rule.getId());
                rule.setRuleContent(ruleContent);
            }
        }

        log.info("查询到规则数量: {}, 总数: {}", ruleList.size(), total);

        return ruleList;
    }

    /**
     * 根据ID查询规则详情
     */
    @Cacheable(value = "inspection:rules", key = "'detail:' + #id", unless = "#result == null")
    public DataInspectionRuleVO.HealthCheckRuleVO getRuleById(Long id) {
        try {
            log.info("查询规则详情，ID: {}", id);

            DataInspectionRuleVO.HealthCheckRuleVO rule = ruleMapper.selectRuleById(id);
            if (rule != null) {
                String ruleContent = buildRuleContentFromDetails(id);
                rule.setRuleContent(ruleContent);
                log.info("查询到规则: {}", rule.getRuleName());
            } else {
                log.warn("未找到ID为{}的规则", id);
            }

            return rule;
        } catch (Exception e) {
            log.error("查询规则详情失败，ID: {}", id, e);
            return null;
        }
    }

    /**
     * 根据类型查询规则列表
     */
    @Cacheable(value = "inspection:rules", key = "'type:' + #ruleType", unless = "#result == null")
    public List<DataInspectionRuleVO.HealthCheckRuleVO> getRulesByType(Integer ruleType) {
        try {
            log.info("根据类型查询规则列表，类型: {}", ruleType);

            List<DataInspectionRuleVO.HealthCheckRuleVO> ruleList = ruleMapper.selectRulesByType(ruleType);

            // 填充规则内容
            for (DataInspectionRuleVO.HealthCheckRuleVO rule : ruleList) {
                if (rule.getId() != null) {
                    String ruleContent = buildRuleContentFromDetails(rule.getId());
                    rule.setRuleContent(ruleContent);
                }
            }

            log.info("查询到{}类型的规则数量: {}", ruleType, ruleList.size());
            return ruleList;
        } catch (Exception e) {
            log.error("根据类型查询规则列表失败，类型: {}", ruleType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "inspection:rules", allEntries = true)
    public Long createRule(DataInspectionRuleVO.RuleCreateVO createVO) {
        try {
            log.info("创建体检规则: {}", createVO.getRuleName());

            // 检查规则名称是否已存在
            if (ruleMapper.existsRuleName(createVO.getRuleName(), null)) {
                throw new IllegalArgumentException("规则名称已存在: " + createVO.getRuleName());
            }

            // 验证规则内容格式
            validateRuleContent(createVO.getRuleContent());

            // 插入规则主表
            Long ruleId = System.currentTimeMillis(); // 临时ID，实际会由数据库生成
            int result = ruleMapper.insertRule(
                    createVO.getRuleName(),
                    createVO.getRuleType(),
                    createVO.getDescription(),
                    createVO.getIsEnabled(),
                    ruleId
            );

            if (result > 0) {
                // 解析并保存规则详情
                saveRuleDetails(ruleId, createVO.getRuleContent());

                log.info("成功创建规则，ID: {}, 名称: {}", ruleId, createVO.getRuleName());
                return ruleId;
            } else {
                throw new RuntimeException("创建规则失败");
            }
        } catch (Exception e) {
            log.error("创建规则失败: {}", createVO.getRuleName(), e);
            throw new RuntimeException("创建规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "inspection:rules", allEntries = true)
    public boolean updateRule(Long id, DataInspectionRuleVO.RuleUpdateVO updateVO) {
        try {
            log.info("更新体检规则，ID: {}", id);

            // 检查规则是否存在
            DataInspectionRuleVO.HealthCheckRuleVO existingRule = ruleMapper.selectRuleById(id);
            if (existingRule == null) {
                throw new IllegalArgumentException("规则不存在，ID: " + id);
            }

            // 检查规则名称是否冲突
            if (StringUtils.hasText(updateVO.getRuleName()) &&
                ruleMapper.existsRuleName(updateVO.getRuleName(), id)) {
                throw new IllegalArgumentException("规则名称已存在: " + updateVO.getRuleName());
            }

            // 验证规则内容格式（如果有更新）
            if (StringUtils.hasText(updateVO.getRuleContent())) {
                validateRuleContent(updateVO.getRuleContent());
            }

            // 更新规则主表
            int result = ruleMapper.updateRule(
                    id,
                    updateVO.getRuleName(),
                    updateVO.getDescription(),
                    updateVO.getIsEnabled()
            );

            if (result > 0) {
                // 如果有规则内容更新，重新保存规则详情
                if (StringUtils.hasText(updateVO.getRuleContent())) {
                    ruleMapper.deleteRuleDetailsByRuleId(id);
                    saveRuleDetails(id, updateVO.getRuleContent());
                }

                log.info("成功更新规则，ID: {}", id);
                return true;
            } else {
                log.warn("更新规则失败，未找到记录，ID: {}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("更新规则失败，ID: {}", id, e);
            throw new RuntimeException("更新规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "inspection:rules", allEntries = true)
    public boolean deleteRule(Long id) {
        try {
            log.info("删除体检规则，ID: {}", id);

            // 先删除规则详情
            ruleMapper.deleteRuleDetailsByRuleId(id);

            // 删除规则主记录
            int result = ruleMapper.deleteRule(id);

            if (result > 0) {
                log.info("成功删除规则，ID: {}", id);
                return true;
            } else {
                log.warn("删除规则失败，未找到记录，ID: {}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("删除规则失败，ID: {}", id, e);
            throw new RuntimeException("删除规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量删除规则
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "inspection:rules", allEntries = true)
    public boolean batchDeleteRules(List<Long> ids) {
        try {
            log.info("批量删除体检规则，IDs: {}", ids);

            if (ids == null || ids.isEmpty()) {
                return true;
            }

            // 先删除所有相关的规则详情
            for (Long id : ids) {
                ruleMapper.deleteRuleDetailsByRuleId(id);
            }

            // 批量删除规则主记录
            int result = ruleMapper.batchDeleteRules(ids);

            log.info("批量删除规则完成，删除数量: {}", result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除规则失败，IDs: {}", ids, e);
            throw new RuntimeException("批量删除规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启用/禁用规则
     */
    @CacheEvict(value = "inspection:rules", allEntries = true)
    public boolean toggleRuleStatus(Long id, Boolean enabled) {
        try {
            log.info("切换规则状态，ID: {}, 启用: {}", id, enabled);

            int result = ruleMapper.toggleRuleStatus(id, enabled);

            if (result > 0) {
                log.info("成功切换规则状态，ID: {}, 启用: {}", id, enabled);
                return true;
            } else {
                log.warn("切换规则状态失败，未找到记录，ID: {}", id);
                return false;
            }
        } catch (Exception e) {
            log.error("切换规则状态失败，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 验证规则内容格式
     */
    public DataInspectionRuleVO.RuleValidationVO validateRuleContent(String ruleContent) {
        try {
            if (!StringUtils.hasText(ruleContent)) {
                return new DataInspectionRuleVO.RuleValidationVO(false, "规则内容不能为空");
            }

            // 尝试解析JSON格式
            JSON.parseObject(ruleContent);

            log.debug("规则内容格式验证通过");
            return new DataInspectionRuleVO.RuleValidationVO(true, "规则内容格式正确");
        } catch (JSONException e) {
            log.warn("规则内容JSON格式错误: {}", e.getMessage());
            return new DataInspectionRuleVO.RuleValidationVO(false, "规则内容必须是有效的JSON格式", e.getMessage());
        } catch (Exception e) {
            log.error("验证规则内容时发生错误", e);
            return new DataInspectionRuleVO.RuleValidationVO(false, "验证规则内容时发生错误", e.getMessage());
        }
    }

    /**
     * 获取规则类型选项
     */
    public List<DataInspectionRuleVO.RuleTypeOptionVO> getRuleTypeOptions() {
        return Arrays.asList(
                new DataInspectionRuleVO.RuleTypeOptionVO(1, "党组（党委）设置", "检查党组织设置的完整性和规范性"),
                new DataInspectionRuleVO.RuleTypeOptionVO(2, "党务干部任免", "检查党务干部任免信息的完整性和合规性"),
                new DataInspectionRuleVO.RuleTypeOptionVO(3, "任务体检", "检查任务执行情况和关键节点"),
                new DataInspectionRuleVO.RuleTypeOptionVO(4, "用户信息完整", "检查用户信息的完整性和准确性")
        );
    }

    /**
     * 获取规则类型统计
     */
    @Cacheable(value = "inspection:rules", key = "'statistics'")
    public List<DataInspectionRuleConfigMapper.RuleTypeStatistics> getRuleTypeStatistics() {
        try {
            return ruleMapper.getRuleTypeStatistics();
        } catch (Exception e) {
            log.error("获取规则类型统计失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据规则详情构建规则内容JSON
     */
    private String buildRuleContentFromDetails(Long ruleId) {
        try {
            List<DataInspectionRuleDetailEntity> details = ruleMapper.selectRuleDetails(ruleId);
            if (details.isEmpty()) {
                return "{}";
            }

            // 构建简化的JSON结构
            Map<String, Object> ruleContent = new java.util.HashMap<>();
            List<Map<String, Object>> fields = new ArrayList<>();

            for (DataInspectionRuleDetailEntity detail : details) {
                Map<String, Object> field = new java.util.HashMap<>();
                field.put("fieldName", detail.getFieldName());
                field.put("ruleType", detail.getRuleType());
                field.put("ruleValue", detail.getRuleValue());
                field.put("errorMessage", detail.getErrorMessage());
                fields.add(field);
            }

            ruleContent.put("fields", fields);
            return JSON.toJSONString(ruleContent);
        } catch (Exception e) {
            log.warn("构建规则内容失败，规则ID: {}", ruleId, e);
            return "{}";
        }
    }

    /**
     * 保存规则详情
     */
    private void saveRuleDetails(Long ruleId, String ruleContent) {
        try {
            Map<String, Object> contentMap = JSON.parseObject(ruleContent, Map.class);
            List<Map<String, Object>> fields = (List<Map<String, Object>>) contentMap.get("fields");

            if (fields != null) {
                for (Map<String, Object> field : fields) {
                    DataInspectionRuleDetailEntity detail = new DataInspectionRuleDetailEntity();
                    detail.setRuleId(ruleId);
                    detail.setFieldName((String) field.get("fieldName"));
                    detail.setRuleType((String) field.get("ruleType"));
                    detail.setRuleValue((String) field.get("ruleValue"));
                    detail.setErrorMessage((String) field.get("errorMessage"));
                    detail.setCreateTime(new Date());

                    ruleMapper.insertRuleDetail(detail);
                }
            }
        } catch (Exception e) {
            log.error("保存规则详情失败，规则ID: {}", ruleId, e);
            throw new RuntimeException("保存规则详情失败", e);
        }
    }
}