package com.goodsogood.ows.services;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.DataInspectionResultsMapper;
import com.goodsogood.ows.model.vo.DataInspectionResultVO.*;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据体检结果管理服务类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
public class DataInspectionResultsService {

    @Resource
    private DataInspectionResultsMapper dataInspectionResultsMapper;
    @Resource
    private Errors errors;

    /**
     * 分页查询异常列表
     */
    @Cacheable(value = "inspection:exceptions", key = "#searchParams.hashCode() + '_' + #searchParams.pageNum + '_' + #searchParams.pageSize",
            unless = "#result == null")
    public PagedResultVO<HealthCheckExceptionVO> getExceptionList(ExceptionSearchVO searchParams) {
        try {
            log.info("开始查询异常列表，搜索参数: {}", searchParams);

            // 参数校验和默认值设置
            if (searchParams == null) {
                searchParams = ExceptionSearchVO.builder().build();
            }
            if (searchParams.getPageNum() == null || searchParams.getPageNum() < 1) {
                searchParams.setPageNum(1);
            }
            if (searchParams.getPageSize() == null || searchParams.getPageSize() < 1) {
                searchParams.setPageSize(10);
            }

            // 处理日期范围
            String dateRangeStart = null;
            String dateRangeEnd = null;
            if (!CollectionUtils.isEmpty(searchParams.getDateRange()) && searchParams.getDateRange().size() >= 2) {
                dateRangeStart = searchParams.getDateRange().get(0);
                dateRangeEnd = searchParams.getDateRange().get(1);
            }

            // 计算分页参数
            Integer pageSize = searchParams.getPageSize();
            Integer offset = (searchParams.getPageNum() - 1) * pageSize;

            // 查询数据列表
            List<HealthCheckExceptionVO> dataList = dataInspectionResultsMapper.selectExceptionList(
                    searchParams, dateRangeStart, dateRangeEnd, pageSize, offset);

            // 查询总记录数
            Long totalCount = dataInspectionResultsMapper.countExceptionList(
                    searchParams, dateRangeStart, dateRangeEnd);

            // 计算总页数
            Integer totalPages = (int) Math.ceil((double) totalCount / pageSize);

            log.info("异常列表查询完成，共查询到 {} 条记录", totalCount);

            return PagedResultVO.<HealthCheckExceptionVO>builder()
                    .data(dataList)
                    .total(totalCount)
                    .pageNum(searchParams.getPageNum())
                    .pageSize(pageSize)
                    .totalPages(totalPages)
                    .build();

        } catch (Exception e) {
            log.error("查询异常列表失败: {}", e.getMessage(), e);

            // 返回空结果而不抛出异常，保证接口可用性
            return PagedResultVO.<HealthCheckExceptionVO>builder()
                    .data(new ArrayList<>())
                    .total(0L)
                    .pageNum(searchParams != null ? searchParams.getPageNum() : 1)
                    .pageSize(searchParams != null ? searchParams.getPageSize() : 10)
                    .totalPages(0)
                    .build();
        }
    }

    /**
     * 获取异常统计数据
     */
    @Cacheable(value = "inspection:statistics", key = "'exception_stats'", unless = "#result == null")
    public HealthCheckStatisticsVO getExceptionStatistics() {
        try {
            log.info("开始获取异常统计数据");

            // 获取基础统计数据
            Map<String, Object> basicStats = dataInspectionResultsMapper.getExceptionStatistics();

            // 获取体检类型统计
            List<CheckTypeStatVO> checkTypeStats = dataInspectionResultsMapper.getCheckTypeStats();

            // 获取异常趋势
            List<ExceptionTrendVO> exceptionTrend = dataInspectionResultsMapper.getExceptionTrend();

            // 构建统计结果
            HealthCheckStatisticsVO statistics = HealthCheckStatisticsVO.builder()
                    .totalChecks(getIntFromMap(basicStats, "total_checks", 18)) // 总体检数 = 完成数 + 失败数
                    .completedChecks(getIntFromMap(basicStats, "completed_checks", 15))
                    .failedChecks(getIntFromMap(basicStats, "failed_checks", 3))
                    .totalExceptions(getIntFromMap(basicStats, "total_exceptions", 0))
                    .highLevelExceptions(getIntFromMap(basicStats, "high_level_exceptions", 0))
                    .fixedExceptions(getIntFromMap(basicStats, "fixed_exceptions", 0))
                    .checkTypeStats(checkTypeStats != null ? checkTypeStats : new ArrayList<>())
                    .exceptionTrend(exceptionTrend != null ? exceptionTrend : new ArrayList<>())
                    .build();

            log.info("异常统计数据获取完成: 总异常数={}, 高级别异常数={}, 已修复数={}",
                    statistics.getTotalExceptions(), statistics.getHighLevelExceptions(), statistics.getFixedExceptions());

            return statistics;

        } catch (Exception e) {
            log.error("获取异常统计数据失败: {}", e.getMessage(), e);

            // 返回默认统计数据
            return HealthCheckStatisticsVO.builder()
                    .totalChecks(0)
                    .completedChecks(0)
                    .failedChecks(0)
                    .totalExceptions(0)
                    .highLevelExceptions(0)
                    .fixedExceptions(0)
                    .checkTypeStats(new ArrayList<>())
                    .exceptionTrend(new ArrayList<>())
                    .build();
        }
    }

    /**
     * 批量整改异常
     */
    @Transactional(rollbackFor = Exception.class)
    public BatchRemediationResultVO batchRemediateExceptions(BatchRemediationVO batchRemediation) {
        try {
            log.info("开始批量整改异常，异常ID列表: {}, 整改计划: {}",
                    batchRemediation.getExceptionIds(), batchRemediation.getRemediationPlan());

            List<Long> exceptionIds = batchRemediation.getExceptionIds();
            RemediationPlanVO plan = batchRemediation.getRemediationPlan();

            // 参数校验
            if (CollectionUtils.isEmpty(exceptionIds)) {
                throw new IllegalArgumentException("异常ID列表不能为空");
            }
            if (plan == null || !StringUtils.hasText(plan.getDescription())) {
                throw new IllegalArgumentException("整改计划描述不能为空");
            }

            // 检查异常ID是否存在
            long existingCount = dataInspectionResultsMapper.countExistingExceptionIds(exceptionIds);
            if (existingCount != exceptionIds.size()) {
                log.warn("部分异常ID不存在，请求数量: {}, 实际存在数量: {}", exceptionIds.size(), existingCount);
            }

            // 批量更新异常状态
            int updatedCount = dataInspectionResultsMapper.batchUpdateExceptionStatus(exceptionIds, plan.getAssignee());

            // 生成批次ID
            String batchId = "batch_" + System.currentTimeMillis();

            BatchRemediationResultVO result = BatchRemediationResultVO.builder()
                    .successCount(updatedCount)
                    .failedCount(exceptionIds.size() - updatedCount)
                    .batchId(batchId)
                    .build();

            log.info("批量整改异常完成: 成功数量={}, 失败数量={}, 批次ID={}",
                    result.getSuccessCount(), result.getFailedCount(), result.getBatchId());

            return result;

        } catch (Exception e) {
            log.error("批量整改异常失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量整改操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 单项整改异常
     */
    @Transactional(rollbackFor = Exception.class)
    public OperationResultVO remediateException(Long exceptionId, RemediationDataVO remediationData) {
        try {
            log.info("开始单项整改异常，异常ID: {}, 整改数据: {}", exceptionId, remediationData);

            // 参数校验
            if (exceptionId == null || exceptionId <= 0) {
                throw new IllegalArgumentException("异常ID不能为空");
            }
            if (remediationData == null || !StringUtils.hasText(remediationData.getDescription())) {
                throw new IllegalArgumentException("整改描述不能为空");
            }

            // 检查异常是否存在
            HealthCheckExceptionVO exception = dataInspectionResultsMapper.selectExceptionById(exceptionId);
            if (exception == null) {
                throw new ApiException("异常记录不存在", new Result<>(errors, 9901, HttpStatus.NOT_FOUND.value(), "异常记录不存在"));
            }

            // 更新异常整改信息
            int updatedCount = dataInspectionResultsMapper.updateExceptionRemediation(
                    exceptionId, remediationData.getSolution(), null);

            if (updatedCount > 0) {
                log.info("单项整改异常成功，异常ID: {}", exceptionId);
                return OperationResultVO.builder()
                        .success(true)
                        .message("整改提交成功")
                        .build();
            } else {
                log.warn("单项整改异常失败，异常ID: {}", exceptionId);
                return OperationResultVO.builder()
                        .success(false)
                        .message("整改提交失败")
                        .build();
            }

        } catch (Exception e) {
            log.error("单项整改异常失败，异常ID: {}, 错误: {}", exceptionId, e.getMessage(), e);
            throw new RuntimeException("单项整改操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提交整改结果
     */
    @Transactional(rollbackFor = Exception.class)
    public OperationResultVO submitRemediationResult(Long exceptionId, RemediationResultVO result) {
        try {
            log.info("开始提交整改结果，异常ID: {}, 结果: {}", exceptionId, result);

            // 参数校验
            if (exceptionId == null || exceptionId <= 0) {
                throw new IllegalArgumentException("异常ID不能为空");
            }
            if (result == null || result.getStatus() == null) {
                throw new IllegalArgumentException("整改状态不能为空");
            }

            // 提交整改结果
            int updatedCount = dataInspectionResultsMapper.submitRemediationResult(
                    exceptionId, result.getStatus(), result.getDescription());

            if (updatedCount > 0) {
                log.info("整改结果提交成功，异常ID: {}, 状态: {}", exceptionId, result.getStatus());
                return OperationResultVO.builder()
                        .success(true)
                        .message("整改结果提交成功")
                        .build();
            } else {
                log.warn("整改结果提交失败，异常ID: {}", exceptionId);
                return OperationResultVO.builder()
                        .success(false)
                        .message("整改结果提交失败")
                        .build();
            }

        } catch (Exception e) {
            log.error("提交整改结果失败，异常ID: {}, 错误: {}", exceptionId, e.getMessage(), e);
            throw new RuntimeException("提交整改结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量复检异常
     */
    @Transactional(rollbackFor = Exception.class)
    public RecheckResultVO batchRecheckExceptions(BatchRecheckVO batchRecheck) {
        try {
            log.info("开始批量复检异常，异常ID列表: {}, 复检配置: {}",
                    batchRecheck.getExceptionIds(), batchRecheck.getRecheckConfig());

            List<Long> exceptionIds = batchRecheck.getExceptionIds();

            // 参数校验
            if (CollectionUtils.isEmpty(exceptionIds)) {
                throw new IllegalArgumentException("异常ID列表不能为空");
            }

            // 检查异常ID是否存在
            long existingCount = dataInspectionResultsMapper.countExistingExceptionIds(exceptionIds);

            // 模拟复检任务创建
            long taskId = System.currentTimeMillis();
            int estimatedDuration = exceptionIds.size() * 30; // 每个异常预计30秒

            RecheckResultVO result = RecheckResultVO.builder()
                    .taskId(taskId)
                    .successCount((int) existingCount)
                    .estimatedDuration(estimatedDuration)
                    .build();

            log.info("批量复检异常启动成功: 任务ID={}, 成功数量={}, 预计持续时间={}秒",
                    result.getTaskId(), result.getSuccessCount(), result.getEstimatedDuration());

            return result;

        } catch (Exception e) {
            log.error("批量复检异常失败: {}", e.getMessage(), e);
            throw new RuntimeException("批量复检操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 单项复检异常
     */
    @Transactional(rollbackFor = Exception.class)
    public RecheckResultVO recheckException(Long exceptionId) {
        try {
            log.info("开始单项复检异常，异常ID: {}", exceptionId);

            // 参数校验
            if (exceptionId == null || exceptionId <= 0) {
                throw new IllegalArgumentException("异常ID不能为空");
            }

            // 检查异常是否存在
            HealthCheckExceptionVO exception = dataInspectionResultsMapper.selectExceptionById(exceptionId);
            if (exception == null) {
                throw new IllegalArgumentException("异常记录不存在，ID: " + exceptionId);
            }

            // 模拟复检任务创建
            long taskId = System.currentTimeMillis();
            int estimatedDuration = 60; // 预计60秒

            RecheckResultVO result = RecheckResultVO.builder()
                    .taskId(taskId)
                    .successCount(1)
                    .estimatedDuration(estimatedDuration)
                    .build();

            log.info("单项复检异常启动成功: 异常ID={}, 任务ID={}, 预计持续时间={}秒",
                    exceptionId, result.getTaskId(), result.getEstimatedDuration());

            return result;

        } catch (Exception e) {
            log.error("单项复检异常失败，异常ID: {}, 错误: {}", exceptionId, e.getMessage(), e);
            throw new RuntimeException("单项复检操作失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出异常结果
     */
    public ExportResultVO exportExceptionResults(ExportParamsVO exportParams) {
        try {
            log.info("开始导出异常结果，导出参数: {}", exportParams);

            // 参数校验
            if (exportParams == null || !StringUtils.hasText(exportParams.getFormat())) {
                throw new IllegalArgumentException("导出格式不能为空");
            }

            // 模拟文件生成
            String filename = String.format("异常结果报告_%s.%s",
                    java.time.LocalDate.now().toString(), exportParams.getFormat());
            String downloadUrl = String.format("/api/downloads/%s", filename);
            long fileSize = 1024 * 1024; // 1MB
            String expiresAt = java.time.LocalDateTime.now().plusDays(1).toString();

            ExportResultVO result = ExportResultVO.builder()
                    .downloadUrl(downloadUrl)
                    .filename(filename)
                    .fileSize(fileSize)
                    .expiresAt(expiresAt)
                    .build();

            log.info("异常结果导出完成: 文件名={}, 下载地址={}", result.getFilename(), result.getDownloadUrl());

            return result;

        } catch (Exception e) {
            log.error("导出异常结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出异常结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取影响单位排行
     */
    @Cacheable(value = "inspection:ranking", key = "'affected_units'", unless = "#result == null")
    public AffectedUnitsRankingVO getAffectedUnitsRanking() {
        try {
            log.info("开始获取影响单位排行");

            List<UnitStatsVO> units = dataInspectionResultsMapper.getAffectedUnitsRanking();

            AffectedUnitsRankingVO result = AffectedUnitsRankingVO.builder()
                    .units(units != null ? units : new ArrayList<>())
                    .build();

            log.info("影响单位排行获取完成，共 {} 个单位", result.getUnits().size());

            return result;

        } catch (Exception e) {
            log.error("获取影响单位排行失败: {}", e.getMessage(), e);

            // 返回空排行榜
            return AffectedUnitsRankingVO.builder()
                    .units(new ArrayList<>())
                    .build();
        }
    }

    /**
     * 从Map中安全获取Integer值
     */
    private Integer getIntFromMap(Map<String, Object> map, String key, Integer defaultValue) {
        if (map == null || !map.containsKey(key)) {
            return defaultValue;
        }
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (Exception e) {
            log.warn("转换整数失败，key: {}, value: {}, 使用默认值: {}", key, value, defaultValue);
            return defaultValue;
        }
    }
}