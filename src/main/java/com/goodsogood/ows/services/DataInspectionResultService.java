package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionResultMapper;
import com.goodsogood.ows.model.db.DataInspectionResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import com.goodsogood.ows.model.vo.PageResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**
 * 数据体检结果服务
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@Service
public class DataInspectionResultService {

    @Autowired
    private DataInspectionResultMapper resultMapper;

    /**
     * 创建体检任务
     */
    @Transactional
    public DataInspectionResultEntity createInspectionTask(Long taskId, List<Integer> checkTypes, Long userId) {
        try {
            DataInspectionResultEntity result = new DataInspectionResultEntity();
            result.setInspectionDate(new Date());
            result.setInspectionType(DataInspectionResultEntity.InspectionType.MANUAL.getCode());
            result.setStatus(DataInspectionResultEntity.InspectionStatus.RUNNING.getCode());
            result.setTotalRecords(0);
            result.setExceptionCount(0);
            result.setExceptionRate(BigDecimal.ZERO);
            result.setDurationSeconds(0);
            
            // 构建摘要信息
            StringBuilder summary = new StringBuilder();
            summary.append("{\"taskId\":").append(taskId != null ? taskId : "null");
            summary.append(",\"checkTypes\":").append(checkTypes.toString());
            summary.append(",\"userId\":").append(userId);
            summary.append(",\"startTime\":\"").append(new Date()).append("\"");
            summary.append("}");
            result.setSummary(summary.toString());

            resultMapper.insertSelective(result);
            log.info("创建体检任务成功, ID: {}, 检查类型: {}", result.getId(), checkTypes);
            return result;
        } catch (Exception e) {
            log.error("创建体检任务失败", e);
            throw new RuntimeException("创建体检任务失败", e);
        }
    }

    /**
     * 更新体检结果
     */
    @Transactional
    public boolean updateInspectionResult(Long id, int exceptionCount) {
        try {
            DataInspectionResultEntity result = resultMapper.selectByPrimaryKey(id);
            if (result == null) {
                log.warn("体检结果不存在, ID: {}", id);
                return false;
            }

            // 计算异常率
            BigDecimal exceptionRate = BigDecimal.ZERO;
            if (result.getTotalRecords() > 0) {
                exceptionRate = BigDecimal.valueOf(exceptionCount)
                    .divide(BigDecimal.valueOf(result.getTotalRecords()), 4, RoundingMode.HALF_UP);
            }

            // 更新结果
            DataInspectionResultEntity updateEntity = new DataInspectionResultEntity();
            updateEntity.setId(id);
            updateEntity.setExceptionCount(exceptionCount);
            updateEntity.setExceptionRate(exceptionRate);
            updateEntity.setStatus(DataInspectionResultEntity.InspectionStatus.COMPLETED.getCode());
            
            // 计算执行时间
            long durationSeconds = (System.currentTimeMillis() - result.getInspectionDate().getTime()) / 1000;
            updateEntity.setDurationSeconds((int) durationSeconds);

            int updateResult = resultMapper.updateByPrimaryKeySelective(updateEntity);
            log.info("更新体检结果成功, ID: {}, 异常数: {}, 异常率: {}", id, exceptionCount, exceptionRate);
            return updateResult > 0;
        } catch (Exception e) {
            log.error("更新体检结果失败, ID: {}", id, e);
            throw new RuntimeException("更新体检结果失败", e);
        }
    }

    /**
     * 标记体检失败
     */
    @Transactional
    public boolean markInspectionFailed(Long id, String errorMessage) {
        try {
            DataInspectionResultEntity updateEntity = new DataInspectionResultEntity();
            updateEntity.setId(id);
            updateEntity.setStatus(DataInspectionResultEntity.InspectionStatus.FAILED.getCode());
            
            // 更新摘要信息添加错误信息
            String summary = "{\"error\":\"" + errorMessage + "\",\"failedTime\":\"" + new Date() + "\"}";
            updateEntity.setSummary(summary);

            int result = resultMapper.updateByPrimaryKeySelective(updateEntity);
            log.warn("标记体检失败, ID: {}, 错误信息: {}", id, errorMessage);
            return result > 0;
        } catch (Exception e) {
            log.error("标记体检失败失败, ID: {}", id, e);
            throw new RuntimeException("标记体检失败失败", e);
        }
    }

    /**
     * 获取总体检数量
     */
    public long getTotalChecks() {
        try {
            return resultMapper.selectCount(new DataInspectionResultEntity());
        } catch (Exception e) {
            log.error("获取总体检数量失败", e);
            return 0;
        }
    }

    /**
     * 获取已完成体检数量
     */
    public long getCompletedChecks() {
        try {
            DataInspectionResultEntity example = new DataInspectionResultEntity();
            example.setStatus(DataInspectionResultEntity.InspectionStatus.COMPLETED.getCode());
            return resultMapper.selectCount(example);
        } catch (Exception e) {
            log.error("获取已完成体检数量失败", e);
            return 0;
        }
    }

    /**
     * 获取失败体检数量
     */
    public long getFailedChecks() {
        try {
            DataInspectionResultEntity example = new DataInspectionResultEntity();
            example.setStatus(DataInspectionResultEntity.InspectionStatus.FAILED.getCode());
            return resultMapper.selectCount(example);
        } catch (Exception e) {
            log.error("获取失败体检数量失败", e);
            return 0;
        }
    }

    /**
     * 获取运行中的体检
     */
    public List<DataInspectionResultEntity> getRunningInspections() {
        try {
            DataInspectionResultEntity example = new DataInspectionResultEntity();
            example.setStatus(DataInspectionResultEntity.InspectionStatus.RUNNING.getCode());
            return resultMapper.select(example);
        } catch (Exception e) {
            log.error("获取运行中体检失败", e);
            throw new RuntimeException("获取运行中体检失败", e);
        }
    }

    /**
     * 根据ID获取体检结果
     */
    public DataInspectionResultEntity getById(Long id) {
        try {
            return resultMapper.selectByPrimaryKey(id);
        } catch (Exception e) {
            log.error("获取体检结果失败, ID: {}", id, e);
            throw new RuntimeException("获取体检结果失败", e);
        }
    }

    /**
     * 更新总记录数
     */
    @Transactional
    public boolean updateTotalRecords(Long id, int totalRecords) {
        try {
            DataInspectionResultEntity updateEntity = new DataInspectionResultEntity();
            updateEntity.setId(id);
            updateEntity.setTotalRecords(totalRecords);

            int result = resultMapper.updateByPrimaryKeySelective(updateEntity);
            log.debug("更新总记录数成功, ID: {}, 记录数: {}", id, totalRecords);
            return result > 0;
        } catch (Exception e) {
            log.error("更新总记录数失败, ID: {}", id, e);
            throw new RuntimeException("更新总记录数失败", e);
        }
    }

    /**
     * 删除体检结果
     */
    @Transactional
    public boolean deleteResult(Long id) {
        try {
            int result = resultMapper.deleteByPrimaryKey(id);
            log.info("删除体检结果成功, ID: {}", id);
            return result > 0;
        } catch (Exception e) {
            log.error("删除体检结果失败, ID: {}", id, e);
            throw new RuntimeException("删除体检结果失败", e);
        }
    }

    /**
     * 获取体检记录列表
     */
    public PageResult<DataInspectionResultEntity> getHealthCheckList(
            String checkName, Integer checkType, Integer status, Integer page, Integer pageSize) {
        try {
            PageHelper.startPage(page, pageSize, "inspection_date DESC");
            
            // 构建查询条件
            DataInspectionResultEntity example = new DataInspectionResultEntity();
            if (checkType != null) {
                example.setInspectionType(checkType.toString());
            }
            if (status != null) {
                example.setStatus(status.toString());
            }
            if (checkName != null) {
                example.setInspectionName(checkName);
            }
            List<DataInspectionResultEntity> list = resultMapper.select(example);
            PageInfo<DataInspectionResultEntity> pageInfo = new PageInfo<>(list);
            
            return new PageResult<>(pageInfo.getList(), pageInfo.getTotal(), page, pageSize);
        } catch (Exception e) {
            log.error("获取体检记录列表失败", e);
            return new PageResult<>(new ArrayList<>(), 0, page, pageSize);
        }
    }

    /**
     * 创建体检记录
     */
    @Transactional
    public Long createHealthCheck(Map<String, Object> data, Long userId) {
        try {
            DataInspectionResultEntity result = new DataInspectionResultEntity();
            result.setInspectionDate(new Date());
            result.setInspectionType((String) data.getOrDefault("inspectionType", "manual"));
            result.setStatus(DataInspectionResultEntity.InspectionStatus.RUNNING.getCode());
            result.setTotalRecords(0);
            result.setExceptionCount(0);
            result.setExceptionRate(BigDecimal.ZERO);
            result.setDurationSeconds(0);
            
            // 构建摘要信息
            Map<String, Object> summary = new HashMap<>();
            summary.put("userId", userId);
            summary.put("createTime", new Date());
            summary.putAll(data);
            result.setSummary(summary.toString());

            resultMapper.insertSelective(result);
            log.info("创建体检记录成功, ID: {}", result.getId());
            return result.getId();
        } catch (Exception e) {
            log.error("创建体检记录失败", e);
            throw new RuntimeException("创建体检记录失败", e);
        }
    }

    /**
     * 更新体检记录
     */
    @Transactional
    public boolean updateHealthCheck(Long id, Map<String, Object> data, Long userId) {
        try {
            DataInspectionResultEntity existing = resultMapper.selectByPrimaryKey(id);
            if (existing == null) {
                log.warn("体检记录不存在, ID: {}", id);
                return false;
            }

            DataInspectionResultEntity updateEntity = new DataInspectionResultEntity();
            updateEntity.setId(id);
            
            if (data.containsKey("inspectionType")) {
                updateEntity.setInspectionType((String) data.get("inspectionType"));
            }
            if (data.containsKey("status")) {
                updateEntity.setStatus((String) data.get("status"));
            }
            if (data.containsKey("summary")) {
                updateEntity.setSummary((String) data.get("summary"));
            }

            int result = resultMapper.updateByPrimaryKeySelective(updateEntity);
            log.info("更新体检记录成功, ID: {}", id);
            return result > 0;
        } catch (Exception e) {
            log.error("更新体检记录失败, ID: {}", id, e);
            throw new RuntimeException("更新体检记录失败", e);
        }
    }

    /**
     * 导出体检结果
     */
    public Map<String, Object> exportResults(String format, Map<String, Object> filters, Long userId) {
        try {
            // 模拟导出逻辑
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = "数据体检报告_" + timestamp + "." + format;
            String downloadUrl = "/api/downloads/health-check/" + fileName;
            
            Map<String, Object> result = new HashMap<>();
            result.put("downloadUrl", downloadUrl);
            result.put("fileName", fileName);
            
            log.info("导出体检结果成功, 文件名: {}, 用户ID: {}", fileName, userId);
            return result;
        } catch (Exception e) {
            log.error("导出体检结果失败", e);
            throw new RuntimeException("导出体检结果失败", e);
        }
    }
}