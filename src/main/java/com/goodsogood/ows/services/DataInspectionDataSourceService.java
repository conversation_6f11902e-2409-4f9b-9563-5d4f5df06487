package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionDataSourceMapper;
import com.goodsogood.ows.model.db.DataInspectionDataSourceEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据体检数据源业务逻辑层
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionDataSourceService {

    @Autowired
    private DataInspectionDataSourceMapper dataInspectionDataSourceMapper;

    /**
     * 创建数据源
     * 
     * @param entity 数据源实体
     * @param sysHeader 系统头信息
     * @return 数据源实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionDataSourceEntity create(DataInspectionDataSourceEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1); // 正常状态
        }
        if (entity.getSyncStatus() == null) {
            entity.setSyncStatus("未同步");
        }

        // 检查数据源ID是否已存在
        if (dataInspectionDataSourceMapper.findBySourceId(entity.getSourceId()) != null) {
            throw new RuntimeException("数据源ID已存在");
        }

        int result = dataInspectionDataSourceMapper.insert(entity);
        if (result > 0) {
            log.info("创建数据源成功，ID: {}, 名称: {}", entity.getId(), entity.getSourceName());
            return entity;
        } else {
            throw new RuntimeException("创建数据源失败");
        }
    }

    /**
     * 更新数据源
     * 
     * @param entity 数据源实体
     * @param sysHeader 系统头信息
     * @return 数据源实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionDataSourceEntity update(DataInspectionDataSourceEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionDataSourceEntity existing = dataInspectionDataSourceMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("数据源不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        int result = dataInspectionDataSourceMapper.updateById(entity);
        if (result > 0) {
            log.info("更新数据源成功，ID: {}, 名称: {}", entity.getId(), entity.getSourceName());
            return dataInspectionDataSourceMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新数据源失败");
        }
    }

    /**
     * 删除数据源
     * 
     * @param id 数据源ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionDataSourceEntity existing = dataInspectionDataSourceMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("数据源不存在");
        }

        int result = dataInspectionDataSourceMapper.deleteById(id);
        if (result > 0) {
            log.info("删除数据源成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除数据源失败");
        }
    }

    /**
     * 根据ID查询数据源
     * 
     * @param id 数据源ID
     * @return 数据源实体
     */
    public DataInspectionDataSourceEntity findById(Long id) {
        return dataInspectionDataSourceMapper.findById(id);
    }

    /**
     * 根据数据源ID查询数据源
     * 
     * @param sourceId 数据源ID
     * @return 数据源实体
     */
    public DataInspectionDataSourceEntity findBySourceId(String sourceId) {
        return dataInspectionDataSourceMapper.findBySourceId(sourceId);
    }

    /**
     * 分页查询数据源列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<DataInspectionDataSourceEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<DataInspectionDataSourceEntity> list = dataInspectionDataSourceMapper.findPage(params);
        int total = dataInspectionDataSourceMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 更新数据源连接状态
     * 
     * @param id 数据源ID
     * @param status 连接状态
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConnectionStatus(Long id, Integer status, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionDataSourceMapper.updateConnectionStatus(id, status);
        if (result > 0) {
            log.info("更新数据源连接状态成功，ID: {}, 状态: {}", id, status);
            return true;
        } else {
            throw new RuntimeException("更新数据源连接状态失败");
        }
    }

    /**
     * 更新数据源同步状态
     * 
     * @param id 数据源ID
     * @param syncStatus 同步状态
     * @param lastSyncTime 最后同步时间
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSyncStatus(Long id, String syncStatus, Date lastSyncTime, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionDataSourceMapper.updateSyncStatus(id, syncStatus, lastSyncTime);
        if (result > 0) {
            log.info("更新数据源同步状态成功，ID: {}, 状态: {}", id, syncStatus);
            return true;
        } else {
            throw new RuntimeException("更新数据源同步状态失败");
        }
    }

    /**
     * 按数据源类型统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countBySourceType() {
        return dataInspectionDataSourceMapper.countBySourceType();
    }

    /**
     * 按连接状态统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus() {
        return dataInspectionDataSourceMapper.countByStatus();
    }

    /**
     * 查询所有启用的数据源
     * 
     * @return 数据源列表
     */
    public List<DataInspectionDataSourceEntity> findAllEnabled() {
        return dataInspectionDataSourceMapper.findAllEnabled();
    }

    /**
     * 构建查询参数
     * 
     * @param sourceName 数据源名称
     * @param sourceType 数据源类型
     * @param status 连接状态
     * @param syncStatus 同步状态
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String sourceName, String sourceType, Integer status, String syncStatus) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(sourceName)) {
            params.put("sourceName", sourceName.trim());
        }
        if (StringUtils.hasText(sourceType)) {
            params.put("sourceType", sourceType.trim());
        }
        if (status != null) {
            params.put("status", status);
        }
        if (StringUtils.hasText(syncStatus)) {
            params.put("syncStatus", syncStatus.trim());
        }
        
        return params;
    }

    /**
     * 测试数据源连接
     * 
     * @param id 数据源ID
     * @param sysHeader 系统头信息
     * @return 是否连接成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean testConnection(Long id, HeaderHelper.SysHeader sysHeader) {
        DataInspectionDataSourceEntity dataSource = dataInspectionDataSourceMapper.findById(id);
        if (dataSource == null) {
            throw new RuntimeException("数据源不存在");
        }

        // 这里应该实现具体的连接测试逻辑
        // 暂时模拟连接测试
        boolean isConnected = true; // 模拟连接成功
        
        // 更新连接状态
        int status = isConnected ? 1 : 0;
        updateConnectionStatus(id, status, sysHeader);
        
        log.info("测试数据源连接，ID: {}, 结果: {}", id, isConnected ? "成功" : "失败");
        return isConnected;
    }

    /**
     * 同步数据源
     * 
     * @param id 数据源ID
     * @param sysHeader 系统头信息
     * @return 是否同步成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean syncDataSource(Long id, HeaderHelper.SysHeader sysHeader) {
        DataInspectionDataSourceEntity dataSource = dataInspectionDataSourceMapper.findById(id);
        if (dataSource == null) {
            throw new RuntimeException("数据源不存在");
        }

        // 这里应该实现具体的数据同步逻辑
        // 暂时模拟同步过程
        boolean isSynced = true; // 模拟同步成功
        
        // 更新同步状态
        String syncStatus = isSynced ? "同步成功" : "同步失败";
        updateSyncStatus(id, syncStatus, new Date(), sysHeader);
        
        log.info("同步数据源，ID: {}, 结果: {}", id, syncStatus);
        return isSynced;
    }

    /**
     * 获取数据源概览统计
     * 
     * @return 统计结果
     */
    public Map<String, Object> getOverviewStatistics() {
        Map<String, Object> overview = new HashMap<>();
        
        // 总数据源数量
        Map<String, Object> params = new HashMap<>();
        int totalCount = dataInspectionDataSourceMapper.count(params);
        overview.put("totalCount", totalCount);
        
        // 按状态统计
        List<Map<String, Object>> statusStats = countByStatus();
        int enabledCount = 0;
        for (Map<String, Object> stat : statusStats) {
            Integer status = (Integer) stat.get("status");
            Integer count = (Integer) stat.get("count");
            if (status != null && status == 1) {
                enabledCount = count;
            }
        }
        overview.put("enabledCount", enabledCount);
        overview.put("disabledCount", totalCount - enabledCount);
        
        // 按类型统计
        List<Map<String, Object>> typeStats = countBySourceType();
        overview.put("typeStats", typeStats);
        
        return overview;
    }
}