package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.HonorMapper;
import com.goodsogood.ows.model.db.HonorEntity;
import com.goodsogood.ows.model.vo.HonorApplyVO;
import com.goodsogood.ows.model.vo.HonorAuditVO;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 荣誉表彰服务层
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@Service
public class HonorService {

    @Autowired
    private HonorMapper honorMapper;

    /**
     * 提交荣誉申报
     *
     * @param vo 申报VO
     * @param sysHeader 系统头信息
     * @return 荣誉实体
     */
    @Transactional(rollbackFor = Exception.class)
    public HonorEntity apply(HonorApplyVO vo, HeaderHelper.SysHeader sysHeader) {
        HonorEntity entity = new HonorEntity();
        BeanUtils.copyProperties(vo, entity);

        // 设置创建信息
        entity.setCreateUser(sysHeader.getUserId());
        entity.setCreateUserName(sysHeader.getUserName());
        entity.setCreateTime(new Date());
        entity.setOrganizationId(sysHeader.getOid());
        entity.setRegionId(sysHeader.getRegionId());

        // 设置默认值
        entity.setStatus(1); // 1-待审核

        int result = honorMapper.insert(entity);
        if (result > 0) {
            log.info("提交荣誉申报成功，ID: {}, 申报人: {}", entity.getId(), entity.getApplicant());
            return entity;
        } else {
            throw new RuntimeException("提交荣誉申报失败");
        }
    }

    /**
     * 更新荣誉申报
     *
     * @param vo 申报VO
     * @param sysHeader 系统头信息
     * @return 荣誉实体
     */
    @Transactional(rollbackFor = Exception.class)
    public HonorEntity update(HonorApplyVO vo, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        HonorEntity existing = honorMapper.findById(vo.getId());
        if (existing == null) {
            throw new RuntimeException("荣誉申报不存在");
        }

        // 复制属性
        BeanUtils.copyProperties(vo, existing);

        // 设置更新信息
        existing.setUpdateUser(sysHeader.getUserId());
        existing.setUpdateUserName(sysHeader.getUserName());
        existing.setUpdateTime(new Date());

        int result = honorMapper.updateById(existing);
        if (result > 0) {
            log.info("更新荣誉申报成功，ID: {}", existing.getId());
            return existing;
        } else {
            throw new RuntimeException("更新荣誉申报失败");
        }
    }

    /**
     * 删除荣誉申报（逻辑删除）
     *
     * @param id 申报ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        HonorEntity existing = honorMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("荣誉申报不存在");
        }

        int result = honorMapper.deleteById(id, sysHeader.getUserId(), sysHeader.getUserName());
        if (result > 0) {
            log.info("删除荣誉申报成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除荣誉申报失败");
        }
    }

    /**
     * 根据ID查询荣誉申报
     *
     * @param id 申报ID
     * @param sysHeader 系统头信息
     * @return 荣誉实体
     */
    public HonorEntity findById(Long id, HeaderHelper.SysHeader sysHeader) {
        return honorMapper.findById(id);
    }

    /**
     * 分页查询荣誉申报列表
     *
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<HonorEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 添加组织ID过滤
        if (sysHeader != null) {
            params.put("organizationId", sysHeader.getOid());
            params.put("regionId", sysHeader.getRegionId());
        }

        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<HonorEntity> list = honorMapper.findByPage(params);
        int total = honorMapper.countByParams(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 构建查询参数
     *
     * @param applicant 申报人
     * @param department 部门
     * @param type 申报类型
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String applicant, String department, Integer type, Integer status, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();

        if (StringUtils.hasText(applicant)) {
            params.put("applicant", applicant);
        }
        if (StringUtils.hasText(department)) {
            params.put("department", department);
        }
        if (type != null) {
            params.put("type", type);
        }
        if (status != null) {
            params.put("status", status);
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }

        return params;
    }

    /**
     * 审核荣誉申报
     *
     * @param vo 审核VO
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean audit(HonorAuditVO vo, HeaderHelper.SysHeader sysHeader) {
        HonorEntity entity = honorMapper.findById(vo.getId());
        if (entity == null) {
            throw new RuntimeException("申报不存在");
        }

        entity.setStatus(vo.getStatus());
        entity.setAuditOpinion(vo.getOpinion());
        entity.setUpdateUser(sysHeader.getUserId());
        entity.setUpdateUserName(sysHeader.getUserName());
        entity.setUpdateTime(new Date());

        int result = honorMapper.updateById(entity);
        if (result > 0) {
            log.info("审核荣誉申报成功，ID: {}, 状态: {}", entity.getId(), entity.getStatus());
            return true;
        } else {
            throw new RuntimeException("审核荣誉申报失败");
        }
    }

    /**
     * 根据类型统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    public List<Map<String, Object>> countByType(Long organizationId) {
        return honorMapper.countByType(organizationId);
    }

    /**
     * 根据状态统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus(Long organizationId) {
        return honorMapper.countByStatus(organizationId);
    }

    /**
     * 查询最新的荣誉申报
     *
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉申报列表
     */
    public List<HonorEntity> findLatest(Integer limit, Long organizationId) {
        return honorMapper.findLatest(limit, organizationId);
    }

    /**
     * 按月统计荣誉申报数量
     *
     * @param organizationId 组织ID
     * @return 统计结果
     */
    public List<Map<String, Object>> countByMonth(Long organizationId) {
        return honorMapper.countByMonth(organizationId);
    }

    /**
     * 兼容旧版API - 获取列表
     *
     * @param sysHeader 系统头信息
     * @return 申报VO列表
     */
    public List<HonorApplyVO> list(HeaderHelper.SysHeader sysHeader) {
        Map<String, Object> params = new HashMap<>();
        if (sysHeader != null) {
            params.put("organizationId", sysHeader.getOid());
            params.put("regionId", sysHeader.getRegionId());
        }

        List<HonorEntity> entities = honorMapper.findByPage(params);
        return entities.stream().map(entity -> {
            HonorApplyVO vo = new HonorApplyVO();
            BeanUtils.copyProperties(entity, vo);

            // 手动设置时间字段，统一使用createTime
            if (entity.getCreateTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                vo.setCreateTime(sdf.format(entity.getCreateTime()));
            }

            // 确保审核意见字段正确映射
            vo.setAuditOpinion(entity.getAuditOpinion());

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 兼容旧版API - 删除（无返回值）
     *
     * @param id 申报ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        honorMapper.delete(id);
    }
}