package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionTaskExecutionHistoryMapper;
import com.goodsogood.ows.model.db.DataInspectionTaskExecutionHistoryEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据体检任务执行历史服务
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataInspectionTaskExecutionHistoryService {

    private final DataInspectionTaskExecutionHistoryMapper executionHistoryMapper;

    /**
     * 根据任务ID获取最新的执行记录
     */
    public DataInspectionTaskExecutionHistoryEntity getByTaskId(Long taskId) {
        return executionHistoryMapper.selectLatestByTaskId(taskId);
    }

    /**
     * 获取任务执行历史列表
     */
    public PageResult<DataInspectionTaskExecutionHistoryEntity> getHistoryByTaskId(
            Long taskId, List<String> dateRange, Integer status,
            Integer pageNum, Integer pageSize) {
        
        // 使用优化的Mapper方法获取任务的执行历史
        List<DataInspectionTaskExecutionHistoryEntity> taskHistories;
        if (status != null) {
            // 如果有状态过滤，先获取任务的所有记录再过滤
            taskHistories = executionHistoryMapper.selectByTaskId(taskId).stream()
                    .filter(history -> status.equals(history.getStatus()))
                    .collect(java.util.stream.Collectors.toList());
        } else {
            taskHistories = executionHistoryMapper.selectByTaskId(taskId);
        }
        
        // 手动分页
        long total = taskHistories.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, taskHistories.size());
        List<DataInspectionTaskExecutionHistoryEntity> pagedHistories = fromIndex < taskHistories.size() ? 
            taskHistories.subList(fromIndex, toIndex) : new java.util.ArrayList<>();
        
        return new PageResult<>(pagedHistories, total, pageNum, pageSize);
    }

    /**
     * 创建执行历史记录
     */
    @Transactional
    public Long createExecutionHistory(Long taskId, String executionId) {
        DataInspectionTaskExecutionHistoryEntity history = new DataInspectionTaskExecutionHistoryEntity();
        history.setTaskId(taskId);
        history.setExecutionId(executionId != null ? executionId : UUID.randomUUID().toString());
        history.setStartTime(new Date());
        history.setStatus(1); // 运行中
        history.setTotalRecords(0);
        history.setExceptionCount(0);
        history.setCreateTime(new Date());
        
        executionHistoryMapper.insert(history);
        return history.getId();
    }

    /**
     * 更新执行历史记录
     */
    @Transactional
    public boolean updateExecutionHistory(Long id, Integer status,
                                        Integer totalChecked, Integer exceptionsFound,
                                        String errorMessage) {
        DataInspectionTaskExecutionHistoryEntity history = executionHistoryMapper.selectByKey(id);
        if (history == null) {
            return false;
        }
        
        if (status != null) {
            history.setStatus(status);
            if (status == 2 || status == 3 || status == 4) { // 完成、失败或超时
                history.setEndTime(new Date());
                
                // 计算持续时间
                if (history.getStartTime() != null) {
                    long duration = java.time.Duration.between(
                            history.getStartTime().toInstant(), history.getEndTime().toInstant()).getSeconds();
                    history.setDurationSeconds((int) duration);
                }
            }
        }
        
        
        if (totalChecked != null) {
            history.setTotalRecords(totalChecked);
        }
        
        if (exceptionsFound != null) {
            history.setExceptionCount(exceptionsFound);
        }
        
        if (errorMessage != null) {
            history.setErrorMessage(errorMessage);
        }
        
        return executionHistoryMapper.updateByKey(history) > 0;
    }

    /**
     * 根据执行ID获取执行记录
     */
    public DataInspectionTaskExecutionHistoryEntity getByExecutionId(String executionId) {
        List<DataInspectionTaskExecutionHistoryEntity> allExecutions = executionHistoryMapper.selectAll();
        return allExecutions.stream()
                .filter(execution -> executionId.equals(execution.getExecutionId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 删除执行历史记录
     */
    @Transactional
    public boolean deleteExecutionHistory(Long id) {
        return executionHistoryMapper.deleteByKey(id) > 0;
    }

    /**
     * 获取任务执行统计信息
     */
    public Map<String, Object> getExecutionStatistics(Long taskId) {
        Map<String, Object> statistics = new java.util.HashMap<>();
        
        // 使用优化的Mapper方法
        long totalExecutions = executionHistoryMapper.countByTaskId(taskId);
        statistics.put("totalExecutions", totalExecutions);
        
        long successCount = executionHistoryMapper.countSuccessfulByTaskId(taskId);
        statistics.put("successCount", successCount);
        
        // 获取任务的所有执行记录用于统计失败和超时
        List<DataInspectionTaskExecutionHistoryEntity> taskExecutions = executionHistoryMapper.selectByTaskId(taskId);
        
        // 失败次数（状态为3）
        long failedCount = taskExecutions.stream()
                .filter(execution -> Integer.valueOf(3).equals(execution.getStatus()))
                .count();
        statistics.put("failedCount", failedCount);
        
        // 超时次数（状态为4）
        long timeoutCount = taskExecutions.stream()
                .filter(execution -> Integer.valueOf(4).equals(execution.getStatus()))
                .count();
        statistics.put("timeoutCount", timeoutCount);
        
        // 成功率
        double successRate = totalExecutions > 0 ? (double) successCount / totalExecutions : 0.0;
        statistics.put("successRate", successRate);
        
        // 平均执行时间
        double avgDuration = taskExecutions.stream()
                .filter(execution -> execution.getDuration() != null)
                .mapToInt(DataInspectionTaskExecutionHistoryEntity::getDuration)
                .average()
                .orElse(0.0);
        statistics.put("avgDuration", avgDuration);
        
        return statistics;
    }

    /**
     * 获取最近的执行记录
     */
    public List<DataInspectionTaskExecutionHistoryEntity> getRecentExecutions(Long taskId, int limit) {
        return executionHistoryMapper.selectByTaskId(taskId).stream()
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 清理过期的执行历史记录
     */
    @Transactional
    public int cleanupExpiredHistory(int keepDays) {
        Date cutoffTime = new Date(System.currentTimeMillis() - (keepDays * 24 * 60 * 60 * 1000L));
        return executionHistoryMapper.deleteExpiredRecords(cutoffTime);
    }

    /**
     * 获取执行进度
     */
    public Map<String, Object> getExecutionProgress(String executionId) {
        DataInspectionTaskExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
        if (history == null) {
            return null;
        }
        
        Map<String, Object> progress = new java.util.HashMap<>();
        progress.put("executionId", history.getExecutionId());
        progress.put("status", history.getStatus());
        progress.put("progress", history.getProgress());
        progress.put("startTime", history.getStartTime());
        progress.put("endTime", history.getEndTime());
        progress.put("duration", history.getDuration());
        progress.put("totalChecked", history.getTotalRecords());
        progress.put("exceptionsFound", history.getExceptionCount());
        progress.put("errorMessage", history.getErrorMessage());
        
        return progress;
    }
}