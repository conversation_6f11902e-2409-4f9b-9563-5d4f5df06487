package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.HonorAchievementMapper;
import com.goodsogood.ows.model.db.HonorAchievementEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 荣誉成果业务逻辑层
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
@Log4j2
public class HonorAchievementService {

    @Autowired
    private HonorAchievementMapper honorAchievementMapper;

    /**
     * 创建荣誉成果
     * 
     * @param entity 荣誉成果实体
     * @param sysHeader 系统头信息
     * @return 荣誉成果实体
     */
    @Transactional(rollbackFor = Exception.class)
    public HonorAchievementEntity create(HonorAchievementEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreateUser(sysHeader.getUserId());
        entity.setCreateUserName(sysHeader.getUserName());
        entity.setCreateTime(new Date());
        entity.setOrganizationId(sysHeader.getOid());
        entity.setRegionId(sysHeader.getRegionId());
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1); // 正常状态
        }
        if (entity.getDownloadCount() == null) {
            entity.setDownloadCount(0);
        }
        if (entity.getViewCount() == null) {
            entity.setViewCount(0);
        }
        if (entity.getIsPublic() == null) {
            entity.setIsPublic(true);
        }
        if (entity.getAllowDownload() == null) {
            entity.setAllowDownload(true);
        }

        int result = honorAchievementMapper.insert(entity);
        if (result > 0) {
            log.info("创建荣誉成果成功，ID: {}, 名称: {}", entity.getId(), entity.getName());
            return entity;
        } else {
            throw new RuntimeException("创建荣誉成果失败");
        }
    }

    /**
     * 更新荣誉成果
     * 
     * @param entity 荣誉成果实体
     * @param sysHeader 系统头信息
     * @return 荣誉成果实体
     */
    @Transactional(rollbackFor = Exception.class)
    public HonorAchievementEntity update(HonorAchievementEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        HonorAchievementEntity existing = honorAchievementMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("荣誉成果不存在");
        }

        // 设置更新信息
        entity.setUpdateUser(sysHeader.getUserId());
        entity.setUpdateUserName(sysHeader.getUserName());
        entity.setUpdateTime(new Date());

        int result = honorAchievementMapper.updateById(entity);
        if (result > 0) {
            log.info("更新荣誉成果成功，ID: {}, 名称: {}", entity.getId(), entity.getName());
            return honorAchievementMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新荣誉成果失败");
        }
    }

    /**
     * 删除荣誉成果（逻辑删除）
     * 
     * @param id 荣誉成果ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        HonorAchievementEntity existing = honorAchievementMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("荣誉成果不存在");
        }

        int result = honorAchievementMapper.deleteById(id, sysHeader.getUserId(), sysHeader.getUserName());
        if (result > 0) {
            log.info("删除荣誉成果成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除荣誉成果失败");
        }
    }

    /**
     * 批量删除荣誉成果（逻辑删除）
     * 
     * @param ids 荣誉成果ID列表
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids, HeaderHelper.SysHeader sysHeader) {
        if (ids == null || ids.isEmpty()) {
            throw new RuntimeException("删除ID列表不能为空");
        }

        int result = honorAchievementMapper.batchDeleteByIds(ids, sysHeader.getUserId(), sysHeader.getUserName());
        if (result > 0) {
            log.info("批量删除荣誉成果成功，删除数量: {}", result);
            return true;
        } else {
            throw new RuntimeException("批量删除荣誉成果失败");
        }
    }

    /**
     * 根据ID查询荣誉成果
     * 
     * @param id 荣誉成果ID
     * @return 荣誉成果实体
     */
    public HonorAchievementEntity findById(Long id) {
        return honorAchievementMapper.findById(id);
    }

    /**
     * 根据ID查询荣誉成果并增加查看次数
     * 
     * @param id 荣誉成果ID
     * @return 荣誉成果实体
     */
    @Transactional(rollbackFor = Exception.class)
    public HonorAchievementEntity findByIdAndIncreaseView(Long id) {
        HonorAchievementEntity entity = honorAchievementMapper.findById(id);
        if (entity != null) {
            // 增加查看次数
            honorAchievementMapper.increaseViewCount(id);
            entity.setViewCount(entity.getViewCount() + 1);
        }
        return entity;
    }

    /**
     * 分页查询荣誉成果列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<HonorAchievementEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 添加组织ID过滤
        if (sysHeader != null) {
            params.put("organizationId", sysHeader.getOid());
            params.put("regionId", sysHeader.getRegionId());
        }
        
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<HonorAchievementEntity> list = honorAchievementMapper.findByPage(params);
        int total = honorAchievementMapper.countByParams(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 分页查询公开的荣誉成果列表（用于共享页面）
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @return 分页结果
     */
    public PageResult<HonorAchievementEntity> findPublicByPage(int page, int pageSize, Map<String, Object> params) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<HonorAchievementEntity> list = honorAchievementMapper.findPublicByPage(params);
        int total = honorAchievementMapper.countPublicByParams(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 增加下载次数
     * 
     * @param id 荣誉成果ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseDownloadCount(Long id) {
        int result = honorAchievementMapper.increaseDownloadCount(id);
        return result > 0;
    }

    /**
     * 根据类型统计荣誉成果数量
     * 
     * @param organizationId 组织ID
     * @return 统计结果
     */
    public List<Map<String, Object>> countByType(Long organizationId) {
        return honorAchievementMapper.countByType(organizationId);
    }

    /**
     * 根据级别统计荣誉成果数量
     * 
     * @param organizationId 组织ID
     * @return 统计结果
     */
    public List<Map<String, Object>> countByLevel(Long organizationId) {
        return honorAchievementMapper.countByLevel(organizationId);
    }

    /**
     * 查询最新的荣誉成果
     * 
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉成果列表
     */
    public List<HonorAchievementEntity> findLatest(Integer limit, Long organizationId) {
        return honorAchievementMapper.findLatest(limit, organizationId);
    }

    /**
     * 查询热门的荣誉成果
     * 
     * @param limit 限制数量
     * @param organizationId 组织ID
     * @return 荣誉成果列表
     */
    public List<HonorAchievementEntity> findPopular(Integer limit, Long organizationId) {
        return honorAchievementMapper.findPopular(limit, organizationId);
    }

    /**
     * 构建查询参数
     * 
     * @param name 名称
     * @param type 类型
     * @param level 级别
     * @param issueOrg 颁发单位
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String name, Integer type, Integer level, String issueOrg, 
                                                String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(name)) {
            params.put("name", name.trim());
        }
        if (type != null) {
            params.put("type", type);
        }
        if (level != null) {
            params.put("level", level);
        }
        if (StringUtils.hasText(issueOrg)) {
            params.put("issueOrg", issueOrg.trim());
        }
        if (StringUtils.hasText(startTime)) {
            params.put("startTime", startTime);
        }
        if (StringUtils.hasText(endTime)) {
            params.put("endTime", endTime);
        }
        
        return params;
    }

    /**
     * 获取相关推荐
     */
    public List<HonorAchievementEntity> getRecommendations(Long achievementId, Integer limit) {
        // 先获取当前成果信息
        HonorAchievementEntity current = findById(achievementId);
        if (current == null) {
            return new ArrayList<>();
        }

        return honorAchievementMapper.getRecommendations(
                current.getType(),
                current.getLevel(),
                achievementId,
                limit != null ? limit : 5
        );
    }
}
