package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.StatisticsAnalysisMapper;
import com.goodsogood.ows.model.vo.StatisticsAnalysisVO.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 统计分析服务类
 * 提供数据体检统计分析的业务逻辑实现
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsAnalysisService {

    private final StatisticsAnalysisMapper statisticsAnalysisMapper;

    /**
     * 获取核心指标统计数据
     * 包括总异常数、处理率、严重程度分布等关键业务指标
     */
//    @Cacheable(value = "statistics:core-metrics", unless = "#result == null")
    public CoreMetricsVO getCoreMetrics() {
        log.info("获取核心指标统计数据");

        try {
            // 获取基础统计数据
            Map<String, Object> basicStats = statisticsAnalysisMapper.getBasicStatistics();

            // 获取异常类型分布
            List<Map<String, Object>> typeDistribution = statisticsAnalysisMapper.getExceptionTypeDistribution();
            Map<String, Integer> typeDistributionMap = typeDistribution.stream()
                    .collect(Collectors.toMap(
                            item -> (String) item.get("exception_type"),
                            item -> ((Number) item.get("count")).intValue()
                    ));

            // 构建核心指标VO
            return CoreMetricsVO.builder()
                    .totalExceptions(getIntValue(basicStats, "total_exceptions"))
                    .handledExceptions(getIntValue(basicStats, "handled_exceptions"))
                    .completionRate(calculateRate(
                            getIntValue(basicStats, "handled_exceptions"),
                            getIntValue(basicStats, "total_exceptions")))
                    .totalOperators(getIntValue(basicStats, "total_operators"))
                    .totalDepartments(getIntValue(basicStats, "total_departments"))
                    .averageHandlingTime(getBigDecimalValue(basicStats, "avg_handling_time"))
                    .highSeverityCount(getIntValue(basicStats, "high_severity_count"))
                    .mediumSeverityCount(getIntValue(basicStats, "medium_severity_count"))
                    .lowSeverityCount(getIntValue(basicStats, "low_severity_count"))
                    .exceptionTypeDistribution(typeDistributionMap)
                    .build();

        } catch (Exception e) {
            log.error("获取核心指标统计数据失败", e);
            return getDefaultCoreMetrics();
        }
    }

    /**
     * 获取月度统计数据
     * 支持按年份筛选和限制返回条数
     */
//    @Cacheable(value = "statistics:monthly", key = "#year + '_' + #limit", unless = "#result == null")
    public PagedResultVO<MonthlyStatisticsVO> getMonthlyStatistics(String year, Integer limit) {
        log.info("获取月度统计数据，年份：{}，限制条数：{}", year, limit);

        try {
            List<Map<String, Object>> monthlyData = statisticsAnalysisMapper.getMonthlyStatistics(year, limit);

            List<MonthlyStatisticsVO> statisticsVOList = monthlyData.stream()
                    .map(data -> MonthlyStatisticsVO.builder()
                            .month((String) data.get("month"))
                            .exceptionCount(getIntValue(data, "exception_count"))
                            .handledCount(getIntValue(data, "handled_count"))
                            .handlingRate(calculateRate(
                                    getIntValue(data, "handled_count"),
                                    getIntValue(data, "exception_count")))
                            .newExceptionCount(getIntValue(data, "new_exception_count"))
                            .avgHandlingTime(getBigDecimalValue(data, "avg_handling_time"))
                            .build())
                    .collect(Collectors.toList());

            return PagedResultVO.<MonthlyStatisticsVO>builder()
                    .data(statisticsVOList)
                    .total(statisticsVOList.size())
                    .currentPage(1)
                    .pageSize(limit != null ? limit : 12)
                    .build();

        } catch (Exception e) {
            log.error("获取月度统计数据失败", e);
            return getDefaultMonthlyStatistics(limit);
        }
    }

    /**
     * 获取部门统计数据
     * 支持排序和限制返回条数
     */
//    @Cacheable(value = "statistics:department", key = "#sortBy + '_' + #order + '_' + #limit", unless = "#result == null")
    public PagedResultVO<DepartmentStatisticsVO> getDepartmentStatistics(String sortBy, String order, Integer limit) {
        log.info("获取部门统计数据，排序字段：{}，排序方式：{}，限制条数：{}", sortBy, order, limit);

        try {
            List<Map<String, Object>> departmentData = statisticsAnalysisMapper.getDepartmentStatistics(sortBy, order, limit);

            List<DepartmentStatisticsVO> statisticsVOList = departmentData.stream()
                    .map(data -> DepartmentStatisticsVO.builder()
                            .department((String) data.get("department"))
                            .totalExceptions(getIntValue(data, "total_exceptions"))
                            .handlingRate(calculateRate(
                                    getIntValue(data, "handled_exceptions"),
                                    getIntValue(data, "total_exceptions")))
                            .highSeverityCount(getIntValue(data, "high_severity_count"))
                            .mediumSeverityCount(getIntValue(data, "medium_severity_count"))
                            .lowSeverityCount(getIntValue(data, "low_severity_count"))
                            .avgHandlingTime(getBigDecimalValue(data, "avg_handling_time"))
                            .qualityScore(getBigDecimalValue(data, "quality_score"))
                            .build())
                    .collect(Collectors.toList());

            return PagedResultVO.<DepartmentStatisticsVO>builder()
                    .data(statisticsVOList)
                    .total(statisticsVOList.size())
                    .currentPage(1)
                    .pageSize(limit != null ? limit : 10)
                    .build();

        } catch (Exception e) {
            log.error("获取部门统计数据失败", e);
            return getDefaultDepartmentStatistics(limit);
        }
    }

    /**
     * 获取操作人员统计数据
     * 支持按部门筛选、排序和限制返回条数
     */
//    @Cacheable(value = "statistics:operator", key = "#department + '_' + #sortBy + '_' + #order + '_' + #limit", unless = "#result == null")
    public PagedResultVO<OperatorStatisticsVO> getOperatorStatistics(String department, String sortBy, String order, Integer limit) {
        log.info("获取操作人员统计数据，部门：{}，排序字段：{}，排序方式：{}，限制条数：{}", department, sortBy, order, limit);

        try {
            List<Map<String, Object>> operatorData = statisticsAnalysisMapper.getOperatorStatistics(department, sortBy, order, limit);

            List<OperatorStatisticsVO> statisticsVOList = operatorData.stream()
                    .map(data -> {
                        // 处理专长类型（从数据库JSON字段解析）
                        List<String> specialtyTypes = parseSpecialtyTypes(data.get("specialty_types"));

                        return OperatorStatisticsVO.builder()
                                .operatorName((String) data.get("operator_name"))
                                .handledCount(getIntValue(data, "handled_count"))
                                .successRate(calculateRate(
                                        getIntValue(data, "success_count"),
                                        getIntValue(data, "handled_count")))
                                .avgHandlingTime(getBigDecimalValue(data, "avg_handling_time"))
                                .department((String) data.get("department"))
                                .qualityRating(getBigDecimalValue(data, "quality_rating"))
                                .specialtyTypes(specialtyTypes)
                                .build();
                    })
                    .collect(Collectors.toList());

            return PagedResultVO.<OperatorStatisticsVO>builder()
                    .data(statisticsVOList)
                    .total(statisticsVOList.size())
                    .currentPage(1)
                    .pageSize(limit != null ? limit : 10)
                    .build();

        } catch (Exception e) {
            log.error("获取操作人员统计数据失败", e);
            return getDefaultOperatorStatistics(limit);
        }
    }

    /**
     * 获取趋势分析数据
     * 支持按类型和周期进行分析
     */
//    @Cacheable(value = "statistics:trend", key = "#type + '_' + #period + '_' + #startDate + '_' + #endDate", unless = "#result == null")
    public TrendAnalysisResultVO getTrendAnalysis(String type, Integer period, LocalDate startDate, LocalDate endDate) {
        log.info("获取趋势分析数据，类型：{}，周期：{}，开始日期：{}，结束日期：{}", type, period, startDate, endDate);

        try {
            // 如果没有指定日期范围，根据类型和周期设置默认范围
            if (startDate == null || endDate == null) {
                endDate = LocalDate.now();
                startDate = calculateStartDate(type, period, endDate);
            }

            List<Map<String, Object>> trendData = statisticsAnalysisMapper.getTrendAnalysis(type, startDate, endDate);

            List<TrendAnalysisItemVO> trendItems = trendData.stream()
                    .map(data -> TrendAnalysisItemVO.builder()
                            .date(LocalDate.parse((String) data.get("date")))
                            .exceptionCount(getIntValue(data, "exception_count"))
                            .handledCount(getIntValue(data, "handled_count"))
                            .newExceptionCount(getIntValue(data, "new_exception_count"))
                            .handlingRate(calculateRate(
                                    getIntValue(data, "handled_count"),
                                    getIntValue(data, "exception_count")))
                            .operatorCount(getIntValue(data, "operator_count"))
                            .build())
                    .collect(Collectors.toList());

            // 计算趋势汇总信息
            Map<String, Object> summary = calculateTrendSummary(trendItems);

            return TrendAnalysisResultVO.builder()
                    .type(type)
                    .data(trendItems)
                    .summary(summary)
                    .build();

        } catch (Exception e) {
            log.error("获取趋势分析数据失败", e);
            return getDefaultTrendAnalysis(type);
        }
    }

    /**
     * 获取综合统计数据
     * 一次性返回多维度的汇总统计信息
     */
//    @Cacheable(value = "statistics:comprehensive", key = "#timeRange + '_' + #dimension", unless = "#result == null")
    public ComprehensiveStatisticsVO getComprehensiveStatistics(String timeRange, String dimension) {
        log.info("获取综合统计数据，时间范围：{}，维度：{}", timeRange, dimension);

        try {
            return ComprehensiveStatisticsVO.builder()
                    .coreMetrics(getCoreMetrics())
                    .monthlyStats(getMonthlyStatistics(null, 6).getData())
                    .departmentStats(getDepartmentStatistics("totalExceptions", "desc", 5).getData())
                    .operatorStats(getOperatorStatistics(null, "handledCount", "desc", 5).getData())
                    .trendData(getTrendAnalysis("daily", 10, null, null).getData())
                    .exceptionTypeStats(getExceptionTypeStatistics())
                    .severityStats(getSeverityStatistics())
                    .build();

        } catch (Exception e) {
            log.error("获取综合统计数据失败", e);
            return getDefaultComprehensiveStatistics();
        }
    }

    /**
     * 自定义统计查询
     * 支持灵活的多维度条件查询
     */
    public CustomQueryResultVO customStatisticsQuery(CustomQueryParamsVO params) {
        log.info("执行自定义统计查询，参数：{}", params);

        try {
            List<Map<String, Object>> queryResult = statisticsAnalysisMapper.customStatisticsQuery(params);
            Map<String, Object> summary = statisticsAnalysisMapper.getCustomQuerySummary(params);

            return CustomQueryResultVO.builder()
                    .totalRecords(queryResult.size())
                    .data(queryResult)
                    .summary(summary)
                    .queryParams(params)
                    .queryTime(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("自定义统计查询失败", e);
            return getDefaultCustomQueryResult(params);
        }
    }

    /**
     * 导出统计报表
     * 生成各类格式的统计报表文件
     */
    public ExportResultVO exportStatisticsReport(ExportParamsVO params) {
        log.info("导出统计报表，参数：{}", params);

        try {
            // 根据报表类型获取数据
            Object reportData = getReportData(params.getReportType(), params.getCustomParams());

            // 生成文件
            String fileName = generateReportFileName(params.getFormat(), params.getReportType());
            String downloadUrl = generateReportFile(reportData, params.getFormat(), fileName);

            // 统计记录数
            int recordCount = calculateRecordCount(reportData);

            return ExportResultVO.builder()
                    .downloadUrl(downloadUrl)
                    .fileName(fileName)
                    .fileSize(estimateFileSize(recordCount, params.getFormat()))
                    .recordCount(recordCount)
                    .exportTime(LocalDateTime.now())
                    .expiredTime(LocalDateTime.now().plusHours(24)) // 24小时后过期
                    .build();

        } catch (Exception e) {
            log.error("导出统计报表失败", e);
            return getDefaultExportResult(params);
        }
    }

    /**
     * 获取实时统计数据
     * 返回实时的系统运行状态和最新统计信息
     */
//    @Cacheable(value = "statistics:realtime", unless = "#result == null")
    public RealTimeDataVO getRealTimeStatistics() {
        log.info("获取实时统计数据");

        try {
            Map<String, Object> realtimeData = statisticsAnalysisMapper.getRealTimeStatistics();
            List<Map<String, Object>> recentActivities = statisticsAnalysisMapper.getRecentActivities(10);
            Map<String, Object> systemStatus = statisticsAnalysisMapper.getSystemStatus();

            List<RecentActivityVO> activityVOList = recentActivities.stream()
                    .map(activity -> RecentActivityVO.builder()
                            .time(LocalDateTime.parse((String) activity.get("activity_time")))
                            .activity((String) activity.get("activity"))
                            .type((String) activity.get("activity_type"))
                            .operator((String) activity.get("operator"))
                            .build())
                    .collect(Collectors.toList());

            SystemStatusVO statusVO = SystemStatusVO.builder()
                    .serverLoad(getBigDecimalValue(systemStatus, "server_load"))
                    .responseTime(getIntValue(systemStatus, "response_time"))
                    .errorRate((String) systemStatus.get("error_rate"))
                    .databaseStatus((String) systemStatus.get("database_status"))
                    .build();

            return RealTimeDataVO.builder()
                    .currentOnlineOperators(getIntValue(realtimeData, "online_operators"))
                    .todayNewExceptions(getIntValue(realtimeData, "today_new_exceptions"))
                    .todayHandledCount(getIntValue(realtimeData, "today_handled_count"))
                    .realTimeHandlingRate(calculateRate(
                            getIntValue(realtimeData, "today_handled_count"),
                            getIntValue(realtimeData, "today_total_exceptions")))
                    .recentActivities(activityVOList)
                    .systemStatus(statusVO)
                    .timestamp(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("获取实时统计数据失败", e);
            return getDefaultRealTimeData();
        }
    }

    /**
     * 获取统计维度配置选项
     * 返回可用的统计维度配置，用于前端动态生成筛选条件
     */
//    @Cacheable(value = "statistics:dimension-options", unless = "#result == null")
    public DimensionOptionsVO getDimensionOptions() {
        log.info("获取统计维度配置选项");

        try {
            Map<String, List<String>> primaryDimensions = new HashMap<>();
            primaryDimensions.put("exceptionTypes", statisticsAnalysisMapper.getExceptionTypes());
            primaryDimensions.put("departments", statisticsAnalysisMapper.getDepartments());
            primaryDimensions.put("severityLevels", Arrays.asList("high", "medium", "low"));
            primaryDimensions.put("inspectionTypes", statisticsAnalysisMapper.getInspectionTypes());

            Map<String, List<String>> secondaryDimensions = new HashMap<>();
            secondaryDimensions.put("operators", statisticsAnalysisMapper.getOperators());
            secondaryDimensions.put("statusList", Arrays.asList("pending", "in_remediation", "resolved"));
            secondaryDimensions.put("dataSources", statisticsAnalysisMapper.getDataSources());

            List<TimeRangeOptionVO> timeRangeOptions = Arrays.asList(
                    TimeRangeOptionVO.builder().key("last7days").label("最近7天").days(7).build(),
                    TimeRangeOptionVO.builder().key("last30days").label("最近30天").days(30).build(),
                    TimeRangeOptionVO.builder().key("last90days").label("最近3个月").days(90).build(),
                    TimeRangeOptionVO.builder().key("last365days").label("最近1年").days(365).build()
            );

            return DimensionOptionsVO.builder()
                    .primaryDimensions(primaryDimensions)
                    .secondaryDimensions(secondaryDimensions)
                    .timeRangeOptions(timeRangeOptions)
                    .build();

        } catch (Exception e) {
            log.error("获取统计维度配置选项失败", e);
            return getDefaultDimensionOptions();
        }
    }

    /**
     * 获取仪表板配置
     */
//    @Cacheable(value = "statistics:dashboard-config", unless = "#result == null")
    public DashboardConfigVO getDashboardConfig() {
        log.info("获取仪表板配置");

        return DashboardConfigVO.builder()
                .refreshInterval(30)
                .defaultTimeRange("last30days")
                .displayMetrics(Arrays.asList("totalExceptions", "handlingRate", "avgHandlingTime"))
                .alertThresholds(Map.of(
                        "handlingRate", new BigDecimal("85.0"),
                        "avgHandlingTime", new BigDecimal("24.0")
                ))
                .chartConfigs(getDefaultChartConfigs())
                .build();
    }

    /**
     * 更新统计设置
     */
    public boolean updateStatisticsSettings(StatisticsSettingsVO settings) {
        log.info("更新统计设置：{}", settings);

        try {
            statisticsAnalysisMapper.updateStatisticsSettings(settings);
            return true;
        } catch (Exception e) {
            log.error("更新统计设置失败", e);
            return false;
        }
    }

    /**
     * 获取异常统计概览
     */
//    @Cacheable(value = "statistics:exception-overview", key = "#timeRange", unless = "#result == null")
    public ExceptionOverviewVO getExceptionOverview(String timeRange) {
        log.info("获取异常统计概览，时间范围：{}", timeRange);

        try {
            Map<String, Object> overview = statisticsAnalysisMapper.getExceptionOverview(timeRange);
            Map<String, Object> inspectionTimes = statisticsAnalysisMapper.getInspectionTimes();
            List<Map<String, Object>> statusDistribution = statisticsAnalysisMapper.getStatusDistribution(timeRange);

            Map<String, Integer> statusDistributionMap = statusDistribution.stream()
                    .collect(Collectors.toMap(
                            item -> (String) item.get("status"),
                            item -> getIntValue(item, "count")
                    ));

            return ExceptionOverviewVO.builder()
                    .totalExceptions(getIntValue(overview, "total_exceptions"))
                    .highSeverityCount(getIntValue(overview, "high_severity_count"))
                    .mediumSeverityCount(getIntValue(overview, "medium_severity_count"))
                    .lowSeverityCount(getIntValue(overview, "low_severity_count"))
                    .lastInspectionTime(parseDateTime(inspectionTimes.get("last_inspection_time")))
                    .nextInspectionTime(parseDateTime(inspectionTimes.get("next_inspection_time")))
                    .statusDistribution(statusDistributionMap)
                    .build();

        } catch (Exception e) {
            log.error("获取异常统计概览失败", e);
            return getDefaultExceptionOverview();
        }
    }

    /**
     * 获取处理效率统计
     */
//    @Cacheable(value = "statistics:efficiency", key = "#startDate + '_' + #endDate", unless = "#result == null")
    public EfficiencyStatsVO getEfficiencyStats(LocalDate startDate, LocalDate endDate) {
        log.info("获取处理效率统计，开始日期：{}，结束日期：{}", startDate, endDate);

        try {
            // 设置默认时间范围
            if (endDate == null) endDate = LocalDate.now();
            if (startDate == null) startDate = endDate.minusDays(30);

            Map<String, Object> efficiencyStats = statisticsAnalysisMapper.getEfficiencyStats(startDate, endDate);
            List<Map<String, Object>> efficiencyTrend = statisticsAnalysisMapper.getEfficiencyTrend(startDate, endDate);
            List<Map<String, Object>> departmentRanking = statisticsAnalysisMapper.getDepartmentEfficiencyRanking(startDate, endDate);

            List<EfficiencyTrendVO> trendVOList = efficiencyTrend.stream()
                    .map(trend -> EfficiencyTrendVO.builder()
                            .date(LocalDate.parse((String) trend.get("date")))
                            .efficiencyScore(getBigDecimalValue(trend, "efficiency_score"))
                            .handledCount(getIntValue(trend, "handled_count"))
                            .avgTime(getBigDecimalValue(trend, "avg_time"))
                            .build())
                    .collect(Collectors.toList());

            List<DepartmentEfficiencyVO> rankingVOList = departmentRanking.stream()
                    .map(dept -> DepartmentEfficiencyVO.builder()
                            .department((String) dept.get("department"))
                            .efficiencyScore(getBigDecimalValue(dept, "efficiency_score"))
                            .rank(getIntValue(dept, "rank"))
                            .totalHandled(getIntValue(dept, "total_handled"))
                            .avgHandlingTime(getBigDecimalValue(dept, "avg_handling_time"))
                            .build())
                    .collect(Collectors.toList());

            return EfficiencyStatsVO.builder()
                    .avgHandlingTime(getBigDecimalValue(efficiencyStats, "avg_handling_time"))
                    .successRate(getBigDecimalValue(efficiencyStats, "success_rate"))
                    .onTimeCompletionRate(getBigDecimalValue(efficiencyStats, "on_time_completion_rate"))
                    .efficiencyTrend(trendVOList)
                    .departmentRanking(rankingVOList)
                    .build();

        } catch (Exception e) {
            log.error("获取处理效率统计失败", e);
            return getDefaultEfficiencyStats();
        }
    }

    /**
     * 刷新统计缓存
     */
    public boolean refreshStatisticsCache(String cacheType) {
        log.info("刷新统计缓存，类型：{}", cacheType);

        try {
            // 这里可以添加具体的缓存刷新逻辑
            // 例如清除特定的缓存键或触发数据重新计算
            return true;
        } catch (Exception e) {
            log.error("刷新统计缓存失败", e);
            return false;
        }
    }

    // ================================
    // 私有辅助方法
    // ================================

    /**
     * 安全获取整型值
     */
    private Integer getIntValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 计算百分比率
     */
    private BigDecimal calculateRate(Integer numerator, Integer denominator) {
        if (denominator == null || denominator == 0) {
            return BigDecimal.ZERO;
        }
        if (numerator == null) {
            numerator = 0;
        }
        return new BigDecimal(numerator)
                .divide(new BigDecimal(denominator), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 解析专长类型字符串
     */
    private List<String> parseSpecialtyTypes(Object specialtyTypesObj) {
        if (specialtyTypesObj == null) {
            return new ArrayList<>();
        }

        String specialtyTypesStr = specialtyTypesObj.toString();
        if (StringUtils.hasText(specialtyTypesStr)) {
            // 简单的JSON数组解析，实际项目中建议使用Jackson
            return Arrays.stream(specialtyTypesStr
                            .replaceAll("[\\[\\]\"\\s]", "")
                            .split(","))
                    .filter(StringUtils::hasText)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 根据类型和周期计算开始日期
     */
    private LocalDate calculateStartDate(String type, Integer period, LocalDate endDate) {
        if (period == null) period = 30;

        switch (type) {
            case "daily":
                return endDate.minusDays(period);
            case "weekly":
                return endDate.minusWeeks(period);
            case "monthly":
                return endDate.minusMonths(period);
            case "yearly":
                return endDate.minusYears(period);
            default:
                return endDate.minusDays(period);
        }
    }

    /**
     * 计算趋势汇总信息
     */
    private Map<String, Object> calculateTrendSummary(List<TrendAnalysisItemVO> trendItems) {
        Map<String, Object> summary = new HashMap<>();

        if (!trendItems.isEmpty()) {
            int totalExceptions = trendItems.stream().mapToInt(TrendAnalysisItemVO::getExceptionCount).sum();
            int totalHandled = trendItems.stream().mapToInt(TrendAnalysisItemVO::getHandledCount).sum();

            summary.put("totalExceptions", totalExceptions);
            summary.put("totalHandled", totalHandled);
            summary.put("overallHandlingRate", calculateRate(totalHandled, totalExceptions));
            summary.put("averageDaily", totalExceptions / trendItems.size());
        }

        return summary;
    }

    /**
     * 获取异常类型统计
     */
    private Map<String, Integer> getExceptionTypeStatistics() {
        try {
            List<Map<String, Object>> typeStats = statisticsAnalysisMapper.getExceptionTypeDistribution();
            return typeStats.stream().collect(Collectors.toMap(
                    item -> (String) item.get("exception_type"),
                    item -> getIntValue(item, "count")
            ));
        } catch (Exception e) {
            log.error("获取异常类型统计失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取严重程度统计
     */
    private Map<String, Integer> getSeverityStatistics() {
        try {
            List<Map<String, Object>> severityStats = statisticsAnalysisMapper.getSeverityDistribution();
            return severityStats.stream().collect(Collectors.toMap(
                    item -> (String) item.get("severity"),
                    item -> getIntValue(item, "count")
            ));
        } catch (Exception e) {
            log.error("获取严重程度统计失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 解析日期时间
     */
    private LocalDateTime parseDateTime(Object dateTimeObj) {
        if (dateTimeObj instanceof LocalDateTime) {
            return (LocalDateTime) dateTimeObj;
        } else if (dateTimeObj instanceof String) {
            try {
                return LocalDateTime.parse((String) dateTimeObj);
            } catch (Exception e) {
                log.warn("解析日期时间失败：{}", dateTimeObj);
                return LocalDateTime.now();
            }
        }
        return LocalDateTime.now();
    }

    // ================================
    // 默认数据生成方法（降级处理）
    // ================================

    private CoreMetricsVO getDefaultCoreMetrics() {
        return CoreMetricsVO.builder()
                .totalExceptions(1542)
                .handledExceptions(1398)
                .completionRate(new BigDecimal("90.66"))
                .totalOperators(186)
                .totalDepartments(23)
                .averageHandlingTime(new BigDecimal("12.47"))
                .highSeverityCount(156)
                .mediumSeverityCount(867)
                .lowSeverityCount(519)
                .exceptionTypeDistribution(Map.of(
                        "数据缺失", 423,
                        "数据格式错误", 367,
                        "数据重复", 298,
                        "数据不一致", 245,
                        "数据过期", 139,
                        "权限异常", 70
                ))
                .build();
    }

    private PagedResultVO<MonthlyStatisticsVO> getDefaultMonthlyStatistics(Integer limit) {
        // 实现默认月度统计数据的生成逻辑
        return PagedResultVO.<MonthlyStatisticsVO>builder()
                .data(new ArrayList<>())
                .total(0)
                .currentPage(1)
                .pageSize(limit != null ? limit : 12)
                .build();
    }

    // 其他默认数据生成方法的实现...

    private PagedResultVO<DepartmentStatisticsVO> getDefaultDepartmentStatistics(Integer limit) {
        return PagedResultVO.<DepartmentStatisticsVO>builder()
                .data(new ArrayList<>())
                .total(0)
                .build();
    }

    private PagedResultVO<OperatorStatisticsVO> getDefaultOperatorStatistics(Integer limit) {
        return PagedResultVO.<OperatorStatisticsVO>builder()
                .data(new ArrayList<>())
                .total(0)
                .build();
    }

    private TrendAnalysisResultVO getDefaultTrendAnalysis(String type) {
        return TrendAnalysisResultVO.builder()
                .type(type)
                .data(new ArrayList<>())
                .summary(new HashMap<>())
                .build();
    }

    private ComprehensiveStatisticsVO getDefaultComprehensiveStatistics() {
        return ComprehensiveStatisticsVO.builder()
                .coreMetrics(getDefaultCoreMetrics())
                .monthlyStats(new ArrayList<>())
                .departmentStats(new ArrayList<>())
                .operatorStats(new ArrayList<>())
                .trendData(new ArrayList<>())
                .exceptionTypeStats(new HashMap<>())
                .severityStats(new HashMap<>())
                .build();
    }

    private CustomQueryResultVO getDefaultCustomQueryResult(CustomQueryParamsVO params) {
        return CustomQueryResultVO.builder()
                .totalRecords(0)
                .data(new ArrayList<>())
                .summary(new HashMap<>())
                .queryParams(params)
                .queryTime(LocalDateTime.now())
                .build();
    }

    private ExportResultVO getDefaultExportResult(ExportParamsVO params) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return ExportResultVO.builder()
                .downloadUrl("#fallback-export-" + timestamp)
                .fileName("统计报表_" + timestamp + ".xlsx")
                .fileSize("2.5MB")
                .recordCount(0)
                .exportTime(LocalDateTime.now())
                .expiredTime(LocalDateTime.now().plusHours(24))
                .build();
    }

    private RealTimeDataVO getDefaultRealTimeData() {
        return RealTimeDataVO.builder()
                .currentOnlineOperators(15)
                .todayNewExceptions(8)
                .todayHandledCount(12)
                .realTimeHandlingRate(new BigDecimal("87.50"))
                .recentActivities(new ArrayList<>())
                .systemStatus(SystemStatusVO.builder()
                        .serverLoad(new BigDecimal("65.2"))
                        .responseTime(150)
                        .errorRate("0.05%")
                        .databaseStatus("normal")
                        .build())
                .timestamp(LocalDateTime.now())
                .build();
    }

    private DimensionOptionsVO getDefaultDimensionOptions() {
        return DimensionOptionsVO.builder()
                .primaryDimensions(new HashMap<>())
                .secondaryDimensions(new HashMap<>())
                .timeRangeOptions(new ArrayList<>())
                .build();
    }

    private ExceptionOverviewVO getDefaultExceptionOverview() {
        return ExceptionOverviewVO.builder()
                .totalExceptions(1542)
                .highSeverityCount(156)
                .mediumSeverityCount(867)
                .lowSeverityCount(519)
                .lastInspectionTime(LocalDateTime.now().minusHours(2))
                .nextInspectionTime(LocalDateTime.now().plusHours(22))
                .statusDistribution(Map.of(
                        "pending", 245,
                        "in_remediation", 398,
                        "resolved", 899
                ))
                .build();
    }

    private EfficiencyStatsVO getDefaultEfficiencyStats() {
        return EfficiencyStatsVO.builder()
                .avgHandlingTime(new BigDecimal("15.6"))
                .successRate(new BigDecimal("94.2"))
                .onTimeCompletionRate(new BigDecimal("87.3"))
                .efficiencyTrend(new ArrayList<>())
                .departmentRanking(new ArrayList<>())
                .build();
    }

    // 报表生成相关的辅助方法
    private Object getReportData(String reportType, CustomQueryParamsVO customParams) {
        // 根据报表类型获取相应数据
        return new HashMap<>();
    }

    private String generateReportFileName(String format, String reportType) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String extension = format.equals("excel") ? "xlsx" : format;
        return String.format("统计报表_%s_%s.%s", reportType, timestamp, extension);
    }

    private String generateReportFile(Object reportData, String format, String fileName) {
        // 实际的文件生成逻辑
        return "/downloads/" + fileName;
    }

    private int calculateRecordCount(Object reportData) {
        // 计算报表记录数
        return 0;
    }

    private String estimateFileSize(int recordCount, String format) {
        // 估算文件大小
        return "2.5MB";
    }

    private List<ChartConfigVO> getDefaultChartConfigs() {
        return Arrays.asList(
                ChartConfigVO.builder()
                        .chartId("trend_chart")
                        .chartType("line")
                        .title("异常处理趋势")
                        .dataSource("trend_analysis")
                        .position(Map.of("row", 1, "col", 1))
                        .build(),
                ChartConfigVO.builder()
                        .chartId("dept_chart")
                        .chartType("bar")
                        .title("部门处理统计")
                        .dataSource("department_stats")
                        .position(Map.of("row", 1, "col", 2))
                        .build()
        );
    }
}