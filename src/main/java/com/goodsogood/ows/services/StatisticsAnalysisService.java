package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.StatisticsAnalysisMapper;
import com.goodsogood.ows.mapper.StatisticsCoreMetricsMapper;
import com.goodsogood.ows.mapper.StatisticsMonthlyMapper;
import com.goodsogood.ows.model.db.StatisticsCoreMetricsEntity;
import com.goodsogood.ows.model.db.StatisticsMonthlyEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 统计分析服务层
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Slf4j
@Service
public class StatisticsAnalysisService {

    @Autowired
    private StatisticsCoreMetricsMapper coreMetricsMapper;

    @Autowired
    private StatisticsMonthlyMapper monthlyMapper;

    @Autowired
    private StatisticsAnalysisMapper analysisMapper;

    /**
     * 获取核心指标数据
     *
     * @param sysHeader 系统头信息
     * @return 核心指标数据
     */
    public Map<String, Object> getCoreMetrics(HeaderHelper.SysHeader sysHeader) {
        try {
            StatisticsCoreMetricsEntity entity = coreMetricsMapper.findLatestByOrganization(sysHeader.getOid());
            
            Map<String, Object> result = new HashMap<>();
            if (entity != null) {
                result.put("totalDocuments", entity.getTotalDocuments());
                result.put("publishedDocuments", entity.getPublishedDocuments());
                result.put("completionRate", entity.getCompletionRate());
                result.put("totalSpeakers", entity.getTotalSpeakers());
                result.put("totalDepartments", entity.getTotalDepartments());
                result.put("averageViews", entity.getAverageViews());
                result.put("totalDownloads", entity.getTotalDownloads());
                result.put("userEngagement", entity.getUserEngagement());
            } else {
                // 返回默认值
                result.put("totalDocuments", 0);
                result.put("publishedDocuments", 0);
                result.put("completionRate", BigDecimal.ZERO);
                result.put("totalSpeakers", 0);
                result.put("totalDepartments", 0);
                result.put("averageViews", BigDecimal.ZERO);
                result.put("totalDownloads", 0L);
                result.put("userEngagement", BigDecimal.ZERO);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取核心指标数据失败", e);
            throw new RuntimeException("获取核心指标数据失败", e);
        }
    }

    /**
     * 获取月度统计数据
     *
     * @param year 年份
     * @param limit 限制数量
     * @param sysHeader 系统头信息
     * @return 月度统计数据
     */
    public Map<String, Object> getMonthlyStatistics(String year, Integer limit, HeaderHelper.SysHeader sysHeader) {
        try {
            List<StatisticsMonthlyEntity> data = monthlyMapper.findByYear(year, sysHeader.getOid(), limit);
            int total = monthlyMapper.countByYear(year, sysHeader.getOid());
            
            List<Map<String, Object>> resultData = new ArrayList<>();
            for (StatisticsMonthlyEntity entity : data) {
                Map<String, Object> item = new HashMap<>();
                item.put("month", entity.getMonth());
                item.put("documentCount", entity.getDocumentCount());
                item.put("publishCount", entity.getPublishCount());
                item.put("viewCount", entity.getViewCount());
                item.put("downloadCount", entity.getDownloadCount());
                resultData.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", resultData);
            result.put("total", total);
            
            return result;
        } catch (Exception e) {
            log.error("获取月度统计数据失败", e);
            throw new RuntimeException("获取月度统计数据失败", e);
        }
    }

    /**
     * 获取部门统计数据
     *
     * @param sortBy 排序字段
     * @param order 排序方向
     * @param limit 限制数量
     * @param sysHeader 系统头信息
     * @return 部门统计数据
     */
    public Map<String, Object> getDepartmentStatistics(String sortBy, String order, Integer limit, HeaderHelper.SysHeader sysHeader) {
        try {
            List<Map<String, Object>> data = analysisMapper.getDepartmentStatistics(sysHeader.getOid(), sortBy, order, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("total", data.size());
            
            return result;
        } catch (Exception e) {
            log.error("获取部门统计数据失败", e);
            throw new RuntimeException("获取部门统计数据失败", e);
        }
    }

    /**
     * 获取主讲人统计数据
     *
     * @param department 部门
     * @param sortBy 排序字段
     * @param order 排序方向
     * @param limit 限制数量
     * @param sysHeader 系统头信息
     * @return 主讲人统计数据
     */
    public Map<String, Object> getSpeakerStatistics(String department, String sortBy, String order, Integer limit, HeaderHelper.SysHeader sysHeader) {
        try {
            List<Map<String, Object>> data = analysisMapper.getSpeakerStatistics(sysHeader.getOid(), department, sortBy, order, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("total", data.size());
            
            return result;
        } catch (Exception e) {
            log.error("获取主讲人统计数据失败", e);
            throw new RuntimeException("获取主讲人统计数据失败", e);
        }
    }

    /**
     * 获取趋势分析数据
     *
     * @param type 趋势类型
     * @param period 周期数
     * @param sysHeader 系统头信息
     * @return 趋势分析数据
     */
    public Map<String, Object> getTrendAnalysis(String type, Integer period, HeaderHelper.SysHeader sysHeader) {
        try {
            String trendType = type != null ? type : "daily";
            Integer periodValue = period != null ? period : 30;
            
            List<Map<String, Object>> data = analysisMapper.getTrendAnalysis(sysHeader.getOid(), trendType, periodValue);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("type", trendType);
            
            return result;
        } catch (Exception e) {
            log.error("获取趋势分析数据失败", e);
            throw new RuntimeException("获取趋势分析数据失败", e);
        }
    }

    /**
     * 获取用户行为分析
     *
     * @param type 分析类型
     * @param sysHeader 系统头信息
     * @return 用户行为分析数据
     */
    public Map<String, Object> getUserBehaviorAnalysis(String type, HeaderHelper.SysHeader sysHeader) {
        try {
            List<Map<String, Object>> behaviorData = analysisMapper.getUserBehaviorAnalysis(sysHeader.getOid());
            
            // 处理数据，构建前端需要的格式
            Map<String, Object> readingPatterns = new HashMap<>();
            List<String> peakHours = new ArrayList<>();
            BigDecimal avgReadingTime = BigDecimal.ZERO;
            BigDecimal bounceRate = new BigDecimal("0.15"); // 模拟数据
            BigDecimal completionRate = new BigDecimal("0.85"); // 模拟数据
            
            // 分析高峰时段
            for (Map<String, Object> item : behaviorData) {
                Integer hour = (Integer) item.get("hour");
                Long accessCount = (Long) item.get("accessCount");
                if (accessCount > 100) { // 假设超过100次访问为高峰
                    peakHours.add(hour + ":00");
                }
                if (item.get("avgReadingTime") != null) {
                    avgReadingTime = avgReadingTime.add((BigDecimal) item.get("avgReadingTime"));
                }
            }
            
            readingPatterns.put("peakHours", peakHours);
            readingPatterns.put("avgReadingTime", avgReadingTime.divide(new BigDecimal(behaviorData.size()), 2, RoundingMode.HALF_UP));
            readingPatterns.put("bounceRate", bounceRate);
            readingPatterns.put("completionRate", completionRate);
            
            // 设备分析
            Map<String, Map<String, Object>> deviceAnalysis = new HashMap<>();
            deviceAnalysis.put("desktop", Map.of("count", 1200, "percentage", 60.0));
            deviceAnalysis.put("mobile", Map.of("count", 600, "percentage", 30.0));
            deviceAnalysis.put("tablet", Map.of("count", 200, "percentage", 10.0));
            
            // 来源分析
            Map<String, Map<String, Object>> sourceAnalysis = new HashMap<>();
            sourceAnalysis.put("direct", Map.of("count", 800, "percentage", 40.0));
            sourceAnalysis.put("search", Map.of("count", 600, "percentage", 30.0));
            sourceAnalysis.put("social", Map.of("count", 400, "percentage", 20.0));
            sourceAnalysis.put("referral", Map.of("count", 200, "percentage", 10.0));
            
            Map<String, Object> result = new HashMap<>();
            result.put("readingPatterns", readingPatterns);
            result.put("deviceAnalysis", deviceAnalysis);
            result.put("sourceAnalysis", sourceAnalysis);
            
            return result;
        } catch (Exception e) {
            log.error("获取用户行为分析失败", e);
            throw new RuntimeException("获取用户行为分析失败", e);
        }
    }

    /**
     * 获取综合统计数据
     *
     * @param timeRange 时间范围
     * @param dimension 维度
     * @param sysHeader 系统头信息
     * @return 综合统计数据
     */
    public Map<String, Object> getComprehensiveStatistics(String timeRange, String dimension, HeaderHelper.SysHeader sysHeader) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取核心指标
            result.put("coreMetrics", getCoreMetrics(sysHeader));
            
            // 获取月度统计（最近6个月）
            Map<String, Object> monthlyStats = getMonthlyStatistics(null, 6, sysHeader);
            result.put("monthlyStats", ((List<?>) monthlyStats.get("data")));
            
            // 获取部门统计（前5名）
            Map<String, Object> departmentStats = getDepartmentStatistics("documentCount", "desc", 5, sysHeader);
            result.put("departmentStats", ((List<?>) departmentStats.get("data")));
            
            // 获取主讲人统计（前5名）
            Map<String, Object> speakerStats = getSpeakerStatistics(null, "documentCount", "desc", 5, sysHeader);
            result.put("speakerStats", ((List<?>) speakerStats.get("data")));
            
            // 获取趋势数据（最近10天）
            Map<String, Object> trendData = getTrendAnalysis("daily", 10, sysHeader);
            result.put("trendData", ((List<?>) trendData.get("data")));
            
            return result;
        } catch (Exception e) {
            log.error("获取综合统计数据失败", e);
            throw new RuntimeException("获取综合统计数据失败", e);
        }
    }
}
