package com.goodsogood.ows.services;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionScheduledTaskMapper;
import com.goodsogood.ows.model.db.DataInspectionScheduledTaskEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 数据体检定时任务业务逻辑层
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionScheduledTaskService {

    @Autowired
    private DataInspectionScheduledTaskMapper dataInspectionScheduledTaskMapper;

    /**
     * 创建定时任务
     * 
     * @param entity 任务实体
     * @param sysHeader 系统头信息
     * @return 任务实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionScheduledTaskEntity create(DataInspectionScheduledTaskEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 设置创建信息
        entity.setCreator(sysHeader.getUserName());
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        
        // 设置默认值
        if (entity.getIsEnabled() == null) {
            entity.setIsEnabled(true);
        }
        if (entity.getStatus() == null) {
            entity.setStatus(1); // 待运行状态
        }
        if (entity.getExecutionCount() == null) {
            entity.setExecutionCount(0);
        }
        if (entity.getSuccessCount() == null) {
            entity.setSuccessCount(0);
        }
        if (entity.getFailedCount() == null) {
            entity.setFailedCount(0);
        }
        if (entity.getAvgDurationSeconds() == null) {
            entity.setAvgDurationSeconds(0);
        }

        int result = dataInspectionScheduledTaskMapper.insert(entity);
        if (result > 0) {
            log.info("创建定时任务成功，ID: {}, 名称: {}", entity.getId(), entity.getTaskName());
            return entity;
        } else {
            throw new RuntimeException("创建定时任务失败");
        }
    }

    /**
     * 更新定时任务
     * 
     * @param entity 任务实体
     * @param sysHeader 系统头信息
     * @return 任务实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionScheduledTaskEntity update(DataInspectionScheduledTaskEntity entity, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionScheduledTaskEntity existing = dataInspectionScheduledTaskMapper.findById(entity.getId());
        if (existing == null) {
            throw new RuntimeException("定时任务不存在");
        }

        // 设置更新信息
        entity.setUpdateTime(new Date());

        int result = dataInspectionScheduledTaskMapper.updateById(entity);
        if (result > 0) {
            log.info("更新定时任务成功，ID: {}, 名称: {}", entity.getId(), entity.getTaskName());
            return dataInspectionScheduledTaskMapper.findById(entity.getId());
        } else {
            throw new RuntimeException("更新定时任务失败");
        }
    }

    /**
     * 删除定时任务
     * 
     * @param id 任务ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, HeaderHelper.SysHeader sysHeader) {
        // 检查记录是否存在
        DataInspectionScheduledTaskEntity existing = dataInspectionScheduledTaskMapper.findById(id);
        if (existing == null) {
            throw new RuntimeException("定时任务不存在");
        }

        int result = dataInspectionScheduledTaskMapper.deleteById(id);
        if (result > 0) {
            log.info("删除定时任务成功，ID: {}", id);
            return true;
        } else {
            throw new RuntimeException("删除定时任务失败");
        }
    }

    /**
     * 根据ID查询定时任务
     * 
     * @param id 任务ID
     * @return 任务实体
     */
    public DataInspectionScheduledTaskEntity findById(Long id) {
        return dataInspectionScheduledTaskMapper.findById(id);
    }

    /**
     * 分页查询定时任务列表
     * 
     * @param page 页码
     * @param pageSize 每页大小
     * @param params 查询参数
     * @param sysHeader 系统头信息
     * @return 分页结果
     */
    public PageResult<DataInspectionScheduledTaskEntity> findByPage(int page, int pageSize, Map<String, Object> params, HeaderHelper.SysHeader sysHeader) {
        // 计算偏移量
        int offset = (page - 1) * pageSize;
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        // 查询数据
        List<DataInspectionScheduledTaskEntity> list = dataInspectionScheduledTaskMapper.findPage(params);
        int total = dataInspectionScheduledTaskMapper.count(params);

        return new PageResult<>(list, total, page, pageSize);
    }

    /**
     * 启用/禁用定时任务
     * 
     * @param id 任务ID
     * @param isEnabled 是否启用
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleEnable(Long id, Boolean isEnabled, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionScheduledTaskMapper.toggleEnable(id, isEnabled);
        if (result > 0) {
            log.info("{}定时任务成功，ID: {}", isEnabled ? "启用" : "禁用", id);
            return true;
        } else {
            throw new RuntimeException((isEnabled ? "启用" : "禁用") + "定时任务失败");
        }
    }

    /**
     * 更新任务状态
     * 
     * @param id 任务ID
     * @param status 状态
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionScheduledTaskMapper.updateStatus(id, status);
        if (result > 0) {
            log.info("更新任务状态成功，ID: {}, 状态: {}", id, status);
            return true;
        } else {
            throw new RuntimeException("更新任务状态失败");
        }
    }

    /**
     * 更新任务执行信息
     * 
     * @param id 任务ID
     * @param status 状态
     * @param lastExecuteTime 最后执行时间
     * @param nextExecuteTime 下次执行时间
     * @param isSuccess 是否成功
     * @param duration 执行时长(秒)
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExecutionInfo(Long id, Integer status, Date lastExecuteTime, Date nextExecuteTime, 
                                       Boolean isSuccess, Integer duration, HeaderHelper.SysHeader sysHeader) {
        int result = dataInspectionScheduledTaskMapper.updateExecutionInfo(id, status, lastExecuteTime, nextExecuteTime, isSuccess, duration);
        if (result > 0) {
            log.info("更新任务执行信息成功，ID: {}, 执行结果: {}", id, isSuccess ? "成功" : "失败");
            return true;
        } else {
            throw new RuntimeException("更新任务执行信息失败");
        }
    }

    /**
     * 查询需要执行的定时任务
     * 
     * @return 任务列表
     */
    public List<DataInspectionScheduledTaskEntity> findTasksToExecute() {
        return dataInspectionScheduledTaskMapper.findTasksToExecute();
    }

    /**
     * 按任务类型统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByTaskType() {
        return dataInspectionScheduledTaskMapper.countByTaskType();
    }

    /**
     * 按状态统计
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> countByStatus() {
        return dataInspectionScheduledTaskMapper.countByStatus();
    }

    /**
     * 获取任务执行概览统计
     * 
     * @return 统计结果
     */
    public Map<String, Object> getExecutionOverviewStatistics() {
        return dataInspectionScheduledTaskMapper.getExecutionOverviewStatistics();
    }

    /**
     * 查询最近创建的任务
     * 
     * @param limit 限制数量
     * @return 任务列表
     */
    public List<DataInspectionScheduledTaskEntity> findRecentTasks(Integer limit) {
        return dataInspectionScheduledTaskMapper.findRecentTasks(limit);
    }

    /**
     * 构建查询参数
     * 
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param status 状态
     * @param isEnabled 是否启用
     * @param creator 创建人
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String taskName, Integer taskType, Integer status, Boolean isEnabled, String creator) {
        Map<String, Object> params = new HashMap<>();
        
        if (StringUtils.hasText(taskName)) {
            params.put("taskName", taskName.trim());
        }
        if (taskType != null) {
            params.put("taskType", taskType);
        }
        if (status != null) {
            params.put("status", status);
        }
        if (isEnabled != null) {
            params.put("isEnabled", isEnabled);
        }
        if (StringUtils.hasText(creator)) {
            params.put("creator", creator.trim());
        }
        
        return params;
    }

    /**
     * 立即执行任务
     * 
     * @param id 任务ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTask(Long id, HeaderHelper.SysHeader sysHeader) {
        DataInspectionScheduledTaskEntity task = dataInspectionScheduledTaskMapper.findById(id);
        if (task == null) {
            throw new RuntimeException("定时任务不存在");
        }

        if (!task.getIsEnabled()) {
            throw new RuntimeException("任务已禁用，无法执行");
        }

        // 更新任务状态为运行中

        updateStatus(id, 2, sysHeader); // 2表示运行中
        try {
            // 这里应该实现具体的任务执行逻辑
            // 暂时模拟执行过程
            Thread.sleep(1000); // 模拟执行时间
            
            // 模拟执行结果
            boolean isSuccess = Math.random() > 0.1; // 90%成功率
            
            // 更新执行信息
            Date now = new Date();
            Date nextTime = new Date(now.getTime() + 60 * 60 * 1000); // 下次执行时间为1小时后
            updateExecutionInfo(id, 3, now, nextTime, isSuccess, 1, sysHeader); // 3表示执行完成
            
            log.info("立即执行任务，ID: {}, 结果: {}", id, isSuccess ? "成功" : "失败");
            return isSuccess;
            
        } catch (Exception e) {
            // 执行失败，更新状态
            Date now = new Date();
            Date nextTime = new Date(now.getTime() + 60 * 60 * 1000);
            updateExecutionInfo(id, 4, now, nextTime, false, 0, sysHeader); // 4表示执行失败
            
            log.error("执行任务失败，ID: {}", id, e);
            throw new RuntimeException("执行任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务执行历史
     * 
     * @param id 任务ID
     * @return 执行历史
     */
    public Map<String, Object> getExecutionHistory(Long id) {
        DataInspectionScheduledTaskEntity task = dataInspectionScheduledTaskMapper.findById(id);
        if (task == null) {
            throw new RuntimeException("定时任务不存在");
        }

        Map<String, Object> history = new HashMap<>();
        history.put("taskId", id);
        history.put("taskName", task.getTaskName());
        history.put("executionCount", task.getExecutionCount());
        history.put("successCount", task.getSuccessCount());
        history.put("failedCount", task.getFailedCount());
        history.put("avgDurationSeconds", task.getAvgDurationSeconds());
        history.put("lastExecuteTime", task.getLastExecuteTime());
        history.put("nextExecuteTime", task.getNextExecuteTime());
        
        // 计算成功率
        if (task.getExecutionCount() > 0) {
            double successRate = (double) task.getSuccessCount() / task.getExecutionCount() * 100;
            history.put("successRate", Math.round(successRate * 100.0) / 100.0);
        } else {
            history.put("successRate", 0.0);
        }
        
        return history;
    }
}