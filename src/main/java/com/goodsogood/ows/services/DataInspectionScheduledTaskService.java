package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionScheduledTaskMapper;
import com.goodsogood.ows.model.db.DataInspectionScheduledTaskEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据体检定时任务服务
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataInspectionScheduledTaskService {

    private final DataInspectionScheduledTaskMapper scheduledTaskMapper;

    /**
     * 创建定时任务
     */
    @Transactional
    public DataInspectionScheduledTaskEntity createScheduledTask(
            String name, List<Integer> checkTypes, String cronExpression,
            Boolean isEnabled, Map<String, Object> notificationSettings, Long userId) {
        
        DataInspectionScheduledTaskEntity task = new DataInspectionScheduledTaskEntity();
        task.setTaskName(name);
        task.setTaskType(1); // 默认数据完整性检查
        
        // Convert List<Integer> to JSON string
        String checkTypesJson = "[" + checkTypes.stream()
                .map(String::valueOf)
                .reduce((a, b) -> a + "," + b)
                .orElse("1") + "]";
        task.setCheckTypes(checkTypesJson);
        
        task.setCronExpression(cronExpression);
        task.setCronDescription(parseCronDescription(cronExpression));
        task.setIsEnabled(isEnabled);
        task.setStatus(1); // 待执行
        task.setExecutionCount(0);
        task.setSuccessCount(0);
        task.setFailedCount(0);
        task.setAvgDurationSeconds(0);
        task.setCreator(userId.toString());
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());

        scheduledTaskMapper.insert(task);
        return task;
    }

    /**
     * 获取任务列表
     */
    public PageResult<DataInspectionScheduledTaskEntity> getTaskList(
            String taskName, Integer taskType, Integer status, Boolean isEnabled,
            Integer pageNum, Integer pageSize) {
        
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("taskName", taskName);
        params.put("taskType", taskType);
        params.put("status", status);
        params.put("isEnabled", isEnabled);
        
        List<DataInspectionScheduledTaskEntity> tasks = scheduledTaskMapper.findPage(params);
        
        // 手动分页处理
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, tasks.size());
        List<DataInspectionScheduledTaskEntity> pagedTasks = fromIndex < tasks.size() ? 
            tasks.subList(fromIndex, toIndex) : new java.util.ArrayList<>();
        
        int total = scheduledTaskMapper.count(params);
        
        return new PageResult<>(pagedTasks, (long) total, pageNum, pageSize);
    }

    /**
     * 创建任务
     */
    @Transactional
    public Long createTask(Map<String, Object> taskData, Long userId) {
        DataInspectionScheduledTaskEntity task = new DataInspectionScheduledTaskEntity();
        task.setTaskName((String) taskData.get("taskName"));
        task.setTaskType((Integer) taskData.getOrDefault("taskType", 1));
        
        @SuppressWarnings("unchecked")
        List<Integer> checkTypes = (List<Integer>) taskData.get("checkTypes");
        String checkTypesJson = "[" + checkTypes.stream()
                .map(String::valueOf)
                .reduce((a, b) -> a + "," + b)
                .orElse("1") + "]";
        task.setCheckTypes(checkTypesJson);
        
        task.setCronExpression((String) taskData.get("cronExpression"));
        task.setCronDescription(parseCronDescription((String) taskData.get("cronExpression")));
        task.setIsEnabled((Boolean) taskData.getOrDefault("isEnabled", true));
        task.setStatus(1);
        task.setExecutionCount(0);
        task.setSuccessCount(0);
        task.setFailedCount(0);
        task.setAvgDurationSeconds(0);
        task.setCreator(userId.toString());
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());

        scheduledTaskMapper.insert(task);
        return task.getId();
    }

    /**
     * 更新任务
     */
    @Transactional
    public boolean updateTask(Long id, Map<String, Object> taskData, Long userId) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }

        if (taskData.containsKey("taskName")) {
            task.setTaskName((String) taskData.get("taskName"));
        }
        if (taskData.containsKey("taskType")) {
            task.setTaskType((Integer) taskData.get("taskType"));
        }
        if (taskData.containsKey("checkTypes")) {
            @SuppressWarnings("unchecked")
            List<Integer> checkTypes = (List<Integer>) taskData.get("checkTypes");
            String checkTypesJson = "[" + checkTypes.stream()
                    .map(String::valueOf)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("1") + "]";
            task.setCheckTypes(checkTypesJson);
        }
        if (taskData.containsKey("cronExpression")) {
            String cronExpression = (String) taskData.get("cronExpression");
            task.setCronExpression(cronExpression);
            task.setCronDescription(parseCronDescription(cronExpression));
        }
        if (taskData.containsKey("isEnabled")) {
            task.setIsEnabled((Boolean) taskData.get("isEnabled"));
        }
        
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 删除任务
     */
    @Transactional
    public boolean deleteTask(Long id, Long userId) {
        return scheduledTaskMapper.deleteById(id) > 0;
    }

    /**
     * 删除定时任务
     *
     * @param id 任务ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id, com.goodsogood.ows.helper.HeaderHelper.SysHeader sysHeader) {
        // 权限验证
        if (sysHeader.getUserId() == null) {
            throw new RuntimeException("用户信息不能为空");
        }

        // 检查任务是否存在
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            throw new RuntimeException("定时任务不存在，ID: " + id);
        }

        // 检查任务状态，如果正在执行中则不允许删除
        if (task.getStatus().equals(DataInspectionScheduledTaskEntity.TaskStatus.RUNNING.getCode())) {
            throw new RuntimeException("任务正在执行中，无法删除，请先停止任务，任务ID: " + id);
        }

        // 检查删除权限：只允许任务创建者或管理员删除
        if (!task.getCreator().equals(sysHeader.getUserId().toString())) {
            // 这里可以添加管理员权限检查的逻辑
            // 暂时允许所有用户删除，实际项目中应该根据业务需求添加权限控制
            log.warn("用户[{}]尝试删除其他用户创建的任务，任务ID: {}, 创建者: {}", 
                    sysHeader.getUserId(), id, task.getCreator());
        }

        try {
            // 执行删除操作
            int result = scheduledTaskMapper.deleteById(id);
            if (result > 0) {
                log.info("用户[{}]删除定时任务成功，任务ID: {}, 任务名称: {}", 
                        sysHeader.getUserId(), id, task.getTaskName());
                return true;
            } else {
                throw new RuntimeException("删除定时任务失败，任务ID: " + id);
            }
        } catch (Exception e) {
            log.error("删除定时任务失败，任务ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("删除定时任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 启用/禁用任务
     */
    @Transactional
    public boolean toggleTask(Long id, Boolean enabled, Long userId) {
        return scheduledTaskMapper.toggleEnable(id, enabled) > 0;
    }

    /**
     * 启用/禁用定时任务
     *
     * @param id 任务ID
     * @param isEnabled 是否启用
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleEnable(Long id, Boolean isEnabled, com.goodsogood.ows.helper.HeaderHelper.SysHeader sysHeader) {
        // 权限验证
        if (sysHeader.getUserId() == null) {
            throw new RuntimeException("用户信息不能为空");
        }

        // 检查任务是否存在
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            throw new RuntimeException("定时任务不存在，ID: " + id);
        }

        // 执行启用/禁用操作
        int result = scheduledTaskMapper.toggleEnable(id, isEnabled);
        if (result > 0) {
            log.info("用户[{}]{}定时任务成功，任务ID: {}, 任务名称: {}", 
                    sysHeader.getUserId(), isEnabled ? "启用" : "禁用", id, task.getTaskName());
            
            // 如果禁用任务，同时设置任务状态为待执行（避免正在执行的任务被禁用后继续执行）
            if (!isEnabled && task.getStatus().equals(DataInspectionScheduledTaskEntity.TaskStatus.RUNNING.getCode())) {
                updateTaskStatus(id, DataInspectionScheduledTaskEntity.TaskStatus.WAITING.getCode());
                log.info("禁用任务时将执行中状态改为待执行，任务ID: {}", id);
            }
            
            return true;
        } else {
            throw new RuntimeException((isEnabled ? "启用" : "禁用") + "定时任务失败，任务ID: " + id);
        }
    }

    /**
     * 立即执行任务
     */
    @Transactional
    public Map<String, Object> executeImmediately(Long id, Long userId) {
        // 构造系统头信息
        com.goodsogood.ows.helper.HeaderHelper.SysHeader sysHeader = new com.goodsogood.ows.helper.HeaderHelper.SysHeader();
        sysHeader.setUserId(userId);
        
        // 调用完整的执行方法
        boolean success = executeTask(id, sysHeader);
        
        // 创建执行记录
        String executionId = UUID.randomUUID().toString();
        
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("executionId", executionId);
        result.put("taskId", id);
        result.put("status", success ? "completed" : "failed");
        result.put("message", success ? "任务执行成功" : "任务执行失败");
        
        return result;
    }

    /**
     * 解析Cron表达式描述
     */
    private String parseCronDescription(String cronExpression) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return "";
        }
        
        // 简单的Cron表达式描述解析
        String[] parts = cronExpression.trim().split("\\s+");
        if (parts.length < 5) {
            return cronExpression;
        }
        
        StringBuilder description = new StringBuilder();
        String minute = parts[0];
        String hour = parts[1];
        String day = parts[2];
        String month = parts[3];
        String weekday = parts[4];
        
        if ("*".equals(day) && !"*".equals(weekday)) {
            // 按星期执行
            description.append("每");
            if ("1".equals(weekday)) description.append("周一");
            else if ("2".equals(weekday)) description.append("周二");
            else if ("3".equals(weekday)) description.append("周三");
            else if ("4".equals(weekday)) description.append("周四");
            else if ("5".equals(weekday)) description.append("周五");
            else if ("6".equals(weekday)) description.append("周六");
            else if ("0".equals(weekday) || "7".equals(weekday)) description.append("周日");
            else description.append("周").append(weekday);
        } else if (!"*".equals(day)) {
            // 按月执行
            description.append("每月").append(day).append("号");
        } else {
            // 每天执行
            description.append("每天");
        }
        
        if (!"*".equals(hour)) {
            description.append(" ").append(hour).append("点");
        }
        if (!"*".equals(minute) && !"0".equals(minute)) {
            description.append(minute).append("分");
        }
        description.append("执行");
        
        return description.toString();
    }

    /**
     * 根据ID获取任务详情
     */
    public DataInspectionScheduledTaskEntity getTaskById(Long id) {
        return scheduledTaskMapper.findById(id);
    }

    /**
     * 获取所有启用的任务
     */
    public List<DataInspectionScheduledTaskEntity> getEnabledTasks() {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("isEnabled", true);
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 获取需要执行的任务
     */
    public List<DataInspectionScheduledTaskEntity> getTasksToExecute() {
        return scheduledTaskMapper.findTasksToExecute();
    }

    /**
     * 更新任务状态
     */
    @Transactional
    public boolean updateTaskStatus(Long id, Integer status) {
        return scheduledTaskMapper.updateStatus(id, status) > 0;
    }

    /**
     * 更新任务执行信息
     */
    @Transactional
    public boolean updateTaskExecutionInfo(Long id, Integer status, 
                                          java.util.Date lastExecuteTime, java.util.Date nextExecuteTime,
                                          Boolean isSuccess, Integer duration) {
        return scheduledTaskMapper.updateExecutionInfo(id, status, lastExecuteTime, 
                                                      nextExecuteTime, isSuccess, duration) > 0;
    }

    /**
     * 完成任务执行
     */
    @Transactional
    public boolean completeTaskExecution(Long id, Boolean isSuccess, Integer duration, String errorMessage) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }

        // 计算下次执行时间（简化实现）
        java.util.Date nextExecuteTime = calculateNextExecuteTime(task.getCronExpression());
        
        return updateTaskExecutionInfo(id, 1, new Date(), nextExecuteTime, isSuccess, duration);
    }

    /**
     * 获取任务统计信息
     */
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> overview = scheduledTaskMapper.getExecutionOverviewStatistics();
        
        // 添加按类型和状态的统计
        List<Map<String, Object>> typeStats = scheduledTaskMapper.countByTaskType();
        List<Map<String, Object>> statusStats = scheduledTaskMapper.countByStatus();
        
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("overview", overview);
        result.put("typeStatistics", typeStats);
        result.put("statusStatistics", statusStats);
        
        return result;
    }

    /**
     * 获取最近的任务
     */
    public List<DataInspectionScheduledTaskEntity> getRecentTasks(Integer limit) {
        return scheduledTaskMapper.findRecentTasks(limit != null ? limit : 10);
    }

    /**
     * 批量启用/禁用任务
     */
    @Transactional
    public int batchToggleTasks(List<Long> taskIds, Boolean enabled) {
        int count = 0;
        for (Long taskId : taskIds) {
            if (scheduledTaskMapper.toggleEnable(taskId, enabled) > 0) {
                count++;
            }
        }
        return count;
    }

    /**
     * 批量删除任务
     */
    @Transactional
    public int batchDeleteTasks(List<Long> taskIds) {
        int count = 0;
        for (Long taskId : taskIds) {
            if (scheduledTaskMapper.deleteById(taskId) > 0) {
                count++;
            }
        }
        return count;
    }

    /**
     * 批量删除定时任务
     *
     * @param taskIds 任务ID列表
     * @param sysHeader 系统头信息
     * @return 删除成功的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<Long> taskIds, com.goodsogood.ows.helper.HeaderHelper.SysHeader sysHeader) {
        if (taskIds == null || taskIds.isEmpty()) {
            throw new RuntimeException("删除任务ID列表不能为空");
        }

        // 权限验证
        if (sysHeader.getUserId() == null) {
            throw new RuntimeException("用户信息不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder failedTasks = new StringBuilder();

        for (Long taskId : taskIds) {
            try {
                // 检查任务是否存在
                DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(taskId);
                if (task == null) {
                    failCount++;
                    failedTasks.append("任务ID[").append(taskId).append("]不存在; ");
                    continue;
                }

                // 检查任务状态，如果正在执行中则不允许删除
                if (task.getStatus().equals(DataInspectionScheduledTaskEntity.TaskStatus.RUNNING.getCode())) {
                    failCount++;
                    failedTasks.append("任务ID[").append(taskId).append("]正在执行中; ");
                    continue;
                }

                // 执行删除
                int result = scheduledTaskMapper.deleteById(taskId);
                if (result > 0) {
                    successCount++;
                    log.info("用户[{}]批量删除定时任务成功，任务ID: {}, 任务名称: {}", 
                            sysHeader.getUserId(), taskId, task.getTaskName());
                } else {
                    failCount++;
                    failedTasks.append("任务ID[").append(taskId).append("]删除失败; ");
                }
            } catch (Exception e) {
                failCount++;
                failedTasks.append("任务ID[").append(taskId).append("]删除异常: ").append(e.getMessage()).append("; ");
                log.error("批量删除定时任务异常，任务ID: {}, 错误信息: {}", taskId, e.getMessage(), e);
            }
        }

        // 记录批量删除结果
        log.info("用户[{}]批量删除定时任务完成，总数: {}, 成功: {}, 失败: {}", 
                sysHeader.getUserId(), taskIds.size(), successCount, failCount);

        // 如果有失败的任务，抛出异常说明详情
        if (failCount > 0) {
            String errorMessage = String.format("批量删除任务部分失败，成功: %d, 失败: %d。失败详情: %s", 
                    successCount, failCount, failedTasks.toString());
            
            // 如果全部失败，抛出异常
            if (successCount == 0) {
                throw new RuntimeException("批量删除任务全部失败: " + failedTasks.toString());
            } else {
                // 部分成功的情况，记录警告日志
                log.warn(errorMessage);
            }
        }

        return successCount;
    }

    /**
     * 根据创建者查询任务列表
     */
    public List<DataInspectionScheduledTaskEntity> getTasksByCreator(String creator) {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("creator", creator);
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 检查任务名称是否存在
     */
    public boolean isTaskNameExists(String taskName, Long excludeId) {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("taskName", taskName);
        List<DataInspectionScheduledTaskEntity> tasks = scheduledTaskMapper.findPage(params);
        
        if (excludeId != null) {
            return tasks.stream().anyMatch(task -> !task.getId().equals(excludeId));
        } else {
            return !tasks.isEmpty();
        }
    }

    /**
     * 验证Cron表达式
     */
    public boolean validateCronExpression(String cronExpression) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return false;
        }
        
        // 简单的Cron表达式验证
        String[] parts = cronExpression.trim().split("\\s+");
        return parts.length >= 5;
    }

    /**
     * 计算下次执行时间（简化实现）
     */
    private java.util.Date calculateNextExecuteTime(String cronExpression) {
        // 简化实现：返回1小时后的时间
        return new java.util.Date(System.currentTimeMillis() + 3600 * 1000);
    }

    /**
     * 获取任务执行概览
     */
    public Map<String, Object> getTaskExecutionOverview() {
        return scheduledTaskMapper.getExecutionOverviewStatistics();
    }

    /**
     * 重置任务执行统计
     */
    @Transactional
    public boolean resetTaskStatistics(Long id) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        task.setExecutionCount(0);
        task.setSuccessCount(0);
        task.setFailedCount(0);
        task.setAvgDurationSeconds(0);
        task.setLastExecuteTime(null);
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 获取任务详细信息（包括统计）
     */
    public Map<String, Object> getTaskDetail(Long id) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return null;
        }
        
        Map<String, Object> detail = new java.util.HashMap<>();
        detail.put("task", task);
        detail.put("cronDescription", task.getCronDescription());
        
        // 计算成功率
        double successRate = 0.0;
        if (task.getExecutionCount() > 0) {
            successRate = (double) task.getSuccessCount() / task.getExecutionCount();
        }
        detail.put("successRate", successRate);
        
        // 计算失败率
        double failedRate = 0.0;
        if (task.getExecutionCount() > 0) {
            failedRate = (double) task.getFailedCount() / task.getExecutionCount();
        }
        detail.put("failedRate", failedRate);
        
        return detail;
    }

    /**
     * 获取所有任务（不分页）
     */
    public List<DataInspectionScheduledTaskEntity> getAllTasks() {
        Map<String, Object> params = new java.util.HashMap<>();
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 根据任务类型获取任务列表
     */
    public List<DataInspectionScheduledTaskEntity> getTasksByType(Integer taskType) {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("taskType", taskType);
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 根据状态获取任务列表
     */
    public List<DataInspectionScheduledTaskEntity> getTasksByStatus(Integer status) {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("status", status);
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 获取停用的任务
     */
    public List<DataInspectionScheduledTaskEntity> getDisabledTasks() {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("isEnabled", false);
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 统计任务数量
     */
    public Map<String, Object> getTaskCount() {
        Map<String, Object> params = new java.util.HashMap<>();
        
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("totalTasks", scheduledTaskMapper.count(params));
        
        params.put("isEnabled", true);
        result.put("enabledTasks", scheduledTaskMapper.count(params));
        
        params.clear();
        params.put("isEnabled", false);
        result.put("disabledTasks", scheduledTaskMapper.count(params));
        
        return result;
    }

    /**
     * 验证任务参数
     */
    public boolean validateTaskParams(Map<String, Object> taskData) {
        if (taskData == null) {
            return false;
        }
        
        String taskName = (String) taskData.get("taskName");
        if (taskName == null || taskName.trim().isEmpty()) {
            return false;
        }
        
        String cronExpression = (String) taskData.get("cronExpression");
        if (!validateCronExpression(cronExpression)) {
            return false;
        }
        
        @SuppressWarnings("unchecked")
        List<Integer> checkTypes = (List<Integer>) taskData.get("checkTypes");
        if (checkTypes == null || checkTypes.isEmpty()) {
            return false;
        }
        
        return true;
    }

    /**
     * 根据ID列表获取任务
     */
    public List<DataInspectionScheduledTaskEntity> getTasksByIds(List<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        return taskIds.stream()
                .map(this::getTaskById)
                .filter(task -> task != null)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 更新任务最后执行时间
     */
    @Transactional
    public boolean updateLastExecuteTime(Long id, java.util.Date executeTime) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        task.setLastExecuteTime(executeTime);
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 获取任务执行概要信息
     */
    public Map<String, Object> getTaskExecutionSummary(Long id) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return null;
        }
        
        Map<String, Object> summary = new java.util.HashMap<>();
        summary.put("taskId", task.getId());
        summary.put("taskName", task.getTaskName());
        summary.put("executionCount", task.getExecutionCount());
        summary.put("successCount", task.getSuccessCount());
        summary.put("failedCount", task.getFailedCount());
        summary.put("lastExecuteTime", task.getLastExecuteTime());
        summary.put("nextExecuteTime", task.getNextExecuteTime());
        summary.put("avgDurationSeconds", task.getAvgDurationSeconds());
        summary.put("isEnabled", task.getIsEnabled());
        summary.put("status", task.getStatus());
        
        return summary;
    }

    /**
     * 修改任务Cron表达式
     */
    @Transactional
    public boolean updateTaskCron(Long id, String cronExpression) {
        if (!validateCronExpression(cronExpression)) {
            return false;
        }
        
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        task.setCronExpression(cronExpression);
        task.setCronDescription(parseCronDescription(cronExpression));
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 更新任务的检查类型
     */
    @Transactional
    public boolean updateTaskCheckTypes(Long id, List<Integer> checkTypes) {
        if (checkTypes == null || checkTypes.isEmpty()) {
            return false;
        }
        
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        String checkTypesJson = "[" + checkTypes.stream()
                .map(String::valueOf)
                .reduce((a, b) -> a + "," + b)
                .orElse("1") + "]";
        
        task.setCheckTypes(checkTypesJson);
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 获取单个任务详情（用于REST API）
     */
    public DataInspectionScheduledTaskEntity getTask(Long id) {
        return scheduledTaskMapper.findById(id);
    }

    /**
     * 检查任务是否存在
     */
    public boolean taskExists(Long id) {
        return scheduledTaskMapper.findById(id) != null;
    }

    /**
     * 获取正在运行的任务
     */
    public List<DataInspectionScheduledTaskEntity> getRunningTasks() {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("status", 2); // 运行中
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 获取失败的任务
     */
    public List<DataInspectionScheduledTaskEntity> getFailedTasks() {
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("status", 4); // 失败
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 停止任务执行
     */
    @Transactional
    public boolean stopTask(Long id, Long userId) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null || task.getStatus() != 2) { // 不是运行中状态
            return false;
        }
        
        task.setStatus(1); // 设置为待执行
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 重启任务
     */
    @Transactional
    public boolean restartTask(Long id, Long userId) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        task.setStatus(1); // 设置为待执行
        task.setIsEnabled(true); // 启用任务
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 复制任务
     */
    @Transactional
    public DataInspectionScheduledTaskEntity copyTask(Long id, String newTaskName, Long userId) {
        DataInspectionScheduledTaskEntity originalTask = scheduledTaskMapper.findById(id);
        if (originalTask == null) {
            return null;
        }
        
        DataInspectionScheduledTaskEntity newTask = new DataInspectionScheduledTaskEntity();
        newTask.setTaskName(newTaskName);
        newTask.setTaskType(originalTask.getTaskType());
        newTask.setCheckTypes(originalTask.getCheckTypes());
        newTask.setCronExpression(originalTask.getCronExpression());
        newTask.setCronDescription(originalTask.getCronDescription());
        newTask.setIsEnabled(false); // 默认禁用
        newTask.setStatus(1); // 待执行
        newTask.setExecutionCount(0);
        newTask.setSuccessCount(0);
        newTask.setFailedCount(0);
        newTask.setAvgDurationSeconds(0);
        newTask.setCreator(userId.toString());
        newTask.setCreateTime(new Date());
        newTask.setUpdateTime(new Date());
        
        scheduledTaskMapper.insert(newTask);
        return newTask;
    }

    /**
     * 获取任务的下次执行时间
     */
    public java.util.Date getNextExecuteTime(Long id) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return null;
        }
        return task.getNextExecuteTime();
    }

    /**
     * 设置任务的下次执行时间
     */
    @Transactional
    public boolean setNextExecuteTime(Long id, java.util.Date nextExecuteTime) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return false;
        }
        
        task.setNextExecuteTime(nextExecuteTime);
        task.setUpdateTime(new Date());
        
        return scheduledTaskMapper.updateById(task) > 0;
    }

    /**
     * 清理过期任务（删除长时间未执行的任务）
     */
    @Transactional
    public int cleanupExpiredTasks(int daysSinceLastExecution) {
        java.util.Date cutoffTime = new java.util.Date(System.currentTimeMillis() - (daysSinceLastExecution * 24 * 60 * 60 * 1000L));
        
        Map<String, Object> params = new java.util.HashMap<>();
        List<DataInspectionScheduledTaskEntity> allTasks = scheduledTaskMapper.findPage(params);
        
        int deletedCount = 0;
        for (DataInspectionScheduledTaskEntity task : allTasks) {
            if ((task.getLastExecuteTime() == null && task.getCreateTime().before(cutoffTime)) ||
                (task.getLastExecuteTime() != null && task.getLastExecuteTime().before(cutoffTime))) {
                if (scheduledTaskMapper.deleteById(task.getId()) > 0) {
                    deletedCount++;
                }
            }
        }
        
        return deletedCount;
    }

    /**
     * 批量更新任务状态
     */
    @Transactional
    public int batchUpdateTaskStatus(List<Long> taskIds, Integer status) {
        int count = 0;
        for (Long taskId : taskIds) {
            if (updateTaskStatus(taskId, status)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取任务执行日志统计
     */
    public Map<String, Object> getTaskExecutionStats(Long id) {
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            return null;
        }
        
        Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("taskId", task.getId());
        stats.put("taskName", task.getTaskName());
        stats.put("totalExecutions", task.getExecutionCount());
        stats.put("successfulExecutions", task.getSuccessCount());
        stats.put("failedExecutions", task.getFailedCount());
        
        // 计算成功率
        double successRate = 0.0;
        if (task.getExecutionCount() > 0) {
            successRate = (double) task.getSuccessCount() / task.getExecutionCount() * 100;
        }
        stats.put("successRate", successRate);
        
        // 计算失败率
        double failureRate = 0.0;
        if (task.getExecutionCount() > 0) {
            failureRate = (double) task.getFailedCount() / task.getExecutionCount() * 100;
        }
        stats.put("failureRate", failureRate);
        
        stats.put("avgDuration", task.getAvgDurationSeconds());
        stats.put("lastExecuteTime", task.getLastExecuteTime());
        stats.put("nextExecuteTime", task.getNextExecuteTime());
        
        return stats;
    }

    /**
     * 获取系统任务概览
     */
    public Map<String, Object> getSystemTaskOverview() {
        Map<String, Object> overview = new java.util.HashMap<>();
        
        // 获取总体统计
        Map<String, Object> params = new java.util.HashMap<>();
        int totalTasks = scheduledTaskMapper.count(params);
        
        params.put("isEnabled", true);
        int enabledTasks = scheduledTaskMapper.count(params);
        
        params.clear();
        params.put("status", 2);
        int runningTasks = scheduledTaskMapper.count(params);
        
        overview.put("totalTasks", totalTasks);
        overview.put("enabledTasks", enabledTasks);
        overview.put("disabledTasks", totalTasks - enabledTasks);
        overview.put("runningTasks", runningTasks);
        
        // 获取最近的任务
        List<DataInspectionScheduledTaskEntity> recentTasks = getRecentTasks(5);
        overview.put("recentTasks", recentTasks);
        
        return overview;
    }

    /**
     * 搜索任务（模糊查询）
     */
    public List<DataInspectionScheduledTaskEntity> searchTasks(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllTasks();
        }
        
        Map<String, Object> params = new java.util.HashMap<>();
        params.put("taskName", keyword.trim());
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 按日期范围查询任务
     */
    public List<DataInspectionScheduledTaskEntity> getTasksByDateRange(java.util.Date startDate, java.util.Date endDate) {
        // 由于Mapper中没有具体的日期范围查询，这里用Java过滤
        List<DataInspectionScheduledTaskEntity> allTasks = getAllTasks();
        return allTasks.stream()
                .filter(task -> {
                    java.util.Date createTime = task.getCreateTime();
                    return createTime != null && 
                           !createTime.before(startDate) && 
                           !createTime.after(endDate);
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 构建查询参数Map
     *
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param status 任务状态
     * @param isEnabled 是否启用
     * @param creator 创建者
     * @return 查询参数Map
     */
    public Map<String, Object> buildQueryParams(String taskName, Integer taskType, Integer status, Boolean isEnabled, String creator) {
        Map<String, Object> params = new java.util.HashMap<>();
        
        if (taskName != null && !taskName.trim().isEmpty()) {
            params.put("taskName", taskName.trim());
        }
        if (taskType != null) {
            params.put("taskType", taskType);
        }
        if (status != null) {
            params.put("status", status);
        }
        if (isEnabled != null) {
            params.put("isEnabled", isEnabled);
        }
        if (creator != null && !creator.trim().isEmpty()) {
            params.put("creator", creator.trim());
        }
        
        return params;
    }

    /**
     * 根据查询参数查找任务列表
     *
     * @param params 查询参数
     * @return 任务列表
     */
    public List<DataInspectionScheduledTaskEntity> find(Map<String, Object> params) {
        if (params == null) {
            params = new java.util.HashMap<>();
        }
        return scheduledTaskMapper.findPage(params);
    }

    /**
     * 执行定时任务
     *
     * @param id 任务ID
     * @param sysHeader 系统头信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean executeTask(Long id, com.goodsogood.ows.helper.HeaderHelper.SysHeader sysHeader) {
        // 权限验证
        if (sysHeader.getUserId() == null) {
            throw new RuntimeException("用户信息不能为空");
        }

        // 检查任务是否存在
        DataInspectionScheduledTaskEntity task = scheduledTaskMapper.findById(id);
        if (task == null) {
            throw new RuntimeException("定时任务不存在，ID: " + id);
        }

        // 检查任务是否启用
        if (!task.getIsEnabled()) {
            throw new RuntimeException("任务已禁用，无法执行，任务ID: " + id);
        }

        // 检查任务状态
        if (task.getStatus().equals(DataInspectionScheduledTaskEntity.TaskStatus.RUNNING.getCode())) {
            throw new RuntimeException("任务正在执行中，请勿重复执行，任务ID: " + id);
        }

        try {
            // 设置任务状态为执行中
            updateTaskStatus(id, DataInspectionScheduledTaskEntity.TaskStatus.RUNNING.getCode());
            
            // 更新最后执行时间
            updateLastExecuteTime(id, new Date());
            
            log.info("用户[{}]手动执行定时任务，任务ID: {}, 任务名称: {}", 
                    sysHeader.getUserId(), id, task.getTaskName());
            
            // 这里可以添加实际的任务执行逻辑
            // 比如调用具体的数据体检执行服务
            // executeDataInspectionTask(task);
            
            // 模拟执行过程 - 实际项目中应该调用真实的执行逻辑
            Thread.sleep(100); // 模拟短暂执行时间
            
            // 执行成功，更新统计信息
            task.setExecutionCount(task.getExecutionCount() + 1);
            task.setSuccessCount(task.getSuccessCount() + 1);
            task.setLastExecuteTime(new Date());
            task.setStatus(DataInspectionScheduledTaskEntity.TaskStatus.COMPLETED.getCode());
            
            // 计算平均执行时长
            int currentDuration = 100; // 模拟执行时长(毫秒转秒)
            if (task.getExecutionCount() == 1) {
                task.setAvgDurationSeconds(currentDuration / 1000);
            } else {
                int newAvgDuration = (task.getAvgDurationSeconds() * (task.getExecutionCount() - 1) + currentDuration / 1000) / task.getExecutionCount();
                task.setAvgDurationSeconds(newAvgDuration);
            }
            
            // 计算下次执行时间
            Date nextExecuteTime = calculateNextExecuteTime(task.getCronExpression());
            task.setNextExecuteTime(nextExecuteTime);
            
            task.setUpdateTime(new Date());
            scheduledTaskMapper.updateById(task);
            
            log.info("定时任务执行成功，任务ID: {}, 执行时长: {}ms", id, currentDuration);
            return true;
            
        } catch (Exception e) {
            // 执行失败，更新失败统计
            task.setExecutionCount(task.getExecutionCount() + 1);
            task.setFailedCount(task.getFailedCount() + 1);
            task.setStatus(DataInspectionScheduledTaskEntity.TaskStatus.FAILED.getCode());
            task.setUpdateTime(new Date());
            scheduledTaskMapper.updateById(task);
            
            log.error("定时任务执行失败，任务ID: {}, 错误信息: {}", id, e.getMessage(), e);
            throw new RuntimeException("执行定时任务失败: " + e.getMessage(), e);
        }
    }
}