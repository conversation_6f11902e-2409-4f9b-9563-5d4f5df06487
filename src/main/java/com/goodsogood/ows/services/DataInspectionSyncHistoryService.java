package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionSyncHistoryMapper;
import com.goodsogood.ows.model.db.DataInspectionSyncHistoryEntity;
import com.goodsogood.ows.model.vo.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据体检同步历史服务
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataInspectionSyncHistoryService {

    private final DataInspectionSyncHistoryMapper syncHistoryMapper;

    /**
     * 获取同步状态
     */
    public Map<String, Object> getSyncStatus(String dataSourceId) {
        // 简化实现：查询所有记录并找到最新的
        List<DataInspectionSyncHistoryEntity> allSyncs = syncHistoryMapper.selectAll();
        DataInspectionSyncHistoryEntity latestSync = allSyncs.stream()
                .filter(sync -> sync.getDataSourceId().equals(Long.parseLong(dataSourceId)))
                .max(java.util.Comparator.comparing(DataInspectionSyncHistoryEntity::getStartTime,
                        java.util.Comparator.nullsLast(java.util.Comparator.naturalOrder())))
                .orElse(null);
        
        Map<String, Object> status = new HashMap<>();
        if (latestSync != null) {
            status.put("status", latestSync.getSyncStatus());
            status.put("lastSyncTime", latestSync.getStartTime());
            status.put("duration", latestSync.getDurationSeconds());
//            status.put("syncedRecords", latestSync.getSyncedRecords());
            status.put("errorMessage", latestSync.getErrorMessage());
        } else {
            status.put("status", "never_synced");
            status.put("lastSyncTime", null);
            status.put("duration", 0);
            status.put("syncedRecords", 0);
            status.put("errorMessage", null);
        }
        
        return status;
    }

    /**
     * 获取同步历史列表
     */
    public PageResult<DataInspectionSyncHistoryEntity> getSyncHistory(
            String dataSourceId, List<String> dateRange, String status,
            Integer pageNum, Integer pageSize) {
        
        // 简化实现：查询所有记录并手动过滤分页
        List<DataInspectionSyncHistoryEntity> allHistories = syncHistoryMapper.selectAll();
        
        // 过滤条件
        java.util.stream.Stream<DataInspectionSyncHistoryEntity> stream = allHistories.stream()
                .filter(history -> history.getDataSourceId().equals(Long.parseLong(dataSourceId)));
        
        if (status != null && !status.isEmpty()) {
            stream = stream.filter(history -> status.equals(history.getSyncStatus()));
        }
        
        List<DataInspectionSyncHistoryEntity> filteredHistories = stream
                .sorted(java.util.Comparator.comparing(DataInspectionSyncHistoryEntity::getStartTime,
                        java.util.Comparator.nullsLast(java.util.Comparator.reverseOrder())))
                .collect(java.util.stream.Collectors.toList());
        
        // 手动分页
        long total = filteredHistories.size();
        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, filteredHistories.size());
        List<DataInspectionSyncHistoryEntity> pagedHistories = fromIndex < filteredHistories.size() ? 
            filteredHistories.subList(fromIndex, toIndex) : new java.util.ArrayList<>();
        
        return new PageResult<>(pagedHistories, total, pageNum, pageSize);
    }

    /**
     * 创建同步历史记录
     */
    @Transactional
    public Long createSyncHistory(Long dataSourceId, String syncType, String operator) {
        DataInspectionSyncHistoryEntity history = new DataInspectionSyncHistoryEntity();
        history.setDataSourceId(dataSourceId);
        history.setSyncType(syncType);
        history.setSyncStatus("running");
        history.setStartTime(new Date());
//        history.setSyncedRecords(0);
        history.setOperator(operator);
        history.setCreateTime(new Date());
        
        syncHistoryMapper.insert(history);
        return history.getId();
    }

    /**
     * 更新同步历史记录
     */
    @Transactional
    public boolean updateSyncHistory(Long id, String status, Integer syncedRecords, String errorMessage) {
        DataInspectionSyncHistoryEntity history = syncHistoryMapper.selectByPrimaryKey(id);
        if (history == null) {
            return false;
        }
        
        history.setSyncStatus(status);
        history.setEndTime(new Date());
        
        if (history.getStartTime() != null && history.getEndTime() != null) {
            // 计算持续时间（秒）
            long duration = (history.getEndTime().getTime() - history.getStartTime().getTime()) / 1000;
            history.setDurationSeconds((int) duration);
        }
        
        if (syncedRecords != null) {
            history.setSyncRecords(syncedRecords);
        }
        
        if (errorMessage != null) {
            history.setErrorMessage(errorMessage);
        }
        
        return syncHistoryMapper.updateByPrimaryKey(history) > 0;
    }

    /**
     * 删除同步历史记录
     */
    @Transactional
    public boolean deleteSyncHistory(Long id) {
        return syncHistoryMapper.deleteByPrimaryKey(id) > 0;
    }

    /**
     * 获取数据源的同步统计信息
     */
    public Map<String, Object> getSyncStatistics(Long dataSourceId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取所有记录并手动统计
        List<DataInspectionSyncHistoryEntity> allSyncs = syncHistoryMapper.selectAll();
        List<DataInspectionSyncHistoryEntity> dataSourceSyncs = allSyncs.stream()
                .filter(sync -> sync.getDataSourceId().equals(dataSourceId))
                .toList();
        
        // 总同步次数
        long totalSyncs = dataSourceSyncs.size();
        statistics.put("totalSyncs", totalSyncs);
        
        // 成功次数
        long successCount = dataSourceSyncs.stream()
                .filter(sync -> "success".equals(sync.getSyncStatus()))
                .count();
        statistics.put("successCount", successCount);
        
        // 失败次数
        long failedCount = dataSourceSyncs.stream()
                .filter(sync -> "failed".equals(sync.getSyncStatus()))
                .count();
        statistics.put("failedCount", failedCount);
        
        // 成功率
        double successRate = totalSyncs > 0 ? (double) successCount / totalSyncs : 0.0;
        statistics.put("successRate", successRate);
        
        // 最近7天同步次数（简化处理）
        Date sevenDaysAgo = new Date(System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L));
        long recentSyncs = dataSourceSyncs.stream()
                .filter(sync -> sync.getStartTime() != null && sync.getStartTime().after(sevenDaysAgo))
                .count();
        statistics.put("recentSyncs", recentSyncs);
        
        return statistics;
    }

    /**
     * 清理过期的同步历史记录
     */
    @Transactional
    public int cleanupExpiredHistory(int keepDays) {
        Date cutoffTime = new Date(System.currentTimeMillis() - (keepDays * 24 * 60 * 60 * 1000L));
        
        // 获取所有记录并手动删除过期记录
        List<DataInspectionSyncHistoryEntity> allSyncs = syncHistoryMapper.selectAll();
        int deletedCount = 0;
        
        for (DataInspectionSyncHistoryEntity sync : allSyncs) {
            if (sync.getCreateTime() != null && sync.getCreateTime().before(cutoffTime)) {
                if (syncHistoryMapper.deleteByPrimaryKey(sync.getId()) > 0) {
                    deletedCount++;
                }
            }
        }
        
        return deletedCount;
    }
}