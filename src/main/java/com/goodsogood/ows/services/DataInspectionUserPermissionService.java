package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionUserPermissionMapper;
import com.goodsogood.ows.model.db.DataInspectionUserPermissionEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据体检用户权限业务逻辑层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionUserPermissionService {

    @Autowired
    private DataInspectionUserPermissionMapper userPermissionMapper;

    /**
     * 获取用户权限
     *
     * @param userId 用户ID
     * @return 权限信息
     */
    public Map<String, Object> getUserPermissions(Long userId) {
        DataInspectionUserPermissionEntity permission = userPermissionMapper.findByUserId(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        
        if (permission != null) {
            result.put("canViewInspection", permission.getCanViewInspection());
            result.put("canConfigureRules", permission.getCanConfigureRules());
            result.put("canHandleExceptions", permission.getCanHandleExceptions());
        } else {
            // 如果没有配置权限，给默认值
            result.put("canViewInspection", true);
            result.put("canConfigureRules", false);
            result.put("canHandleExceptions", false);
        }
        
        return result;
    }

    /**
     * 根据用户ID获取权限实体
     *
     * @param userId 用户ID
     * @return 权限实体
     */
    public DataInspectionUserPermissionEntity getByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return userPermissionMapper.findByUserId(userId);
    }

    /**
     * 创建默认权限
     *
     * @param userId 用户ID
     * @return 创建的权限实体
     */
    @Transactional(rollbackFor = Exception.class)
    public DataInspectionUserPermissionEntity createDefaultPermission(Long userId) {
        DataInspectionUserPermissionEntity entity = new DataInspectionUserPermissionEntity();
        entity.setUserId(userId);
        entity.setCanViewInspection(true);  // 默认可以查看
        entity.setCanConfigureRules(false); // 默认不能配置规则
        entity.setCanHandleExceptions(false); // 默认不能处理异常
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        userPermissionMapper.insert(entity);
        log.info("为用户创建默认权限，用户ID: {}", userId);
        return entity;
    }

    /**
     * 检查用户是否可以查看体检
     *
     * @param userId 用户ID
     * @return 是否有权限
     */
    public boolean canViewInspection(Long userId) {
        if (userId == null) {
            return false;
        }
        
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            // 如果没有权限记录，创建默认权限并返回默认值
            permission = createDefaultPermission(userId);
        }
        
        return permission.getCanViewInspection() != null && permission.getCanViewInspection();
    }

    /**
     * 检查用户是否可以配置规则
     *
     * @param userId 用户ID
     * @return 是否有权限
     */
    public boolean canConfigureRules(Long userId) {
        if (userId == null) {
            return false;
        }
        
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            // 如果没有权限记录，创建默认权限并返回默认值
            permission = createDefaultPermission(userId);
        }
        
        return permission.getCanConfigureRules() != null && permission.getCanConfigureRules();
    }

    /**
     * 检查用户是否可以处理异常
     *
     * @param userId 用户ID
     * @return 是否有权限
     */
    public boolean canHandleExceptions(Long userId) {
        if (userId == null) {
            return false;
        }
        
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            // 如果没有权限记录，创建默认权限并返回默认值
            permission = createDefaultPermission(userId);
        }
        
        return permission.getCanHandleExceptions() != null && permission.getCanHandleExceptions();
    }

    /**
     * 设置用户权限
     *
     * @param userId 用户ID
     * @param canViewInspection 是否可以查看体检
     * @param canConfigureRules 是否可以配置规则
     * @param canHandleExceptions 是否可以处理异常
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean setUserPermissions(Long userId, Boolean canViewInspection, 
                                     Boolean canConfigureRules, Boolean canHandleExceptions) {
        DataInspectionUserPermissionEntity existing = getByUserId(userId);
        
        if (existing != null) {
            // 更新现有权限
            existing.setCanViewInspection(canViewInspection);
            existing.setCanConfigureRules(canConfigureRules);
            existing.setCanHandleExceptions(canHandleExceptions);
            existing.setUpdateTime(new Date());
            
            int result = userPermissionMapper.updateByKey(existing);
            if (result > 0) {
                log.info("更新用户权限成功，用户ID: {}", userId);
                return true;
            }
        } else {
            // 创建新权限
            DataInspectionUserPermissionEntity entity = new DataInspectionUserPermissionEntity();
            entity.setUserId(userId);
            entity.setCanViewInspection(canViewInspection);
            entity.setCanConfigureRules(canConfigureRules);
            entity.setCanHandleExceptions(canHandleExceptions);
            entity.setCreateTime(new Date());
            entity.setUpdateTime(new Date());

            int result = userPermissionMapper.insert(entity);
            if (result > 0) {
                log.info("设置用户权限成功，用户ID: {}", userId);
                return true;
            }
        }
        
        throw new RuntimeException("设置用户权限失败");
    }

    /**
     * 删除用户权限
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserPermissions(Long userId) {
        DataInspectionUserPermissionEntity existing = getByUserId(userId);
        if (existing != null) {
            int result = userPermissionMapper.deleteByKey(existing.getId());
            if (result > 0) {
                log.info("删除用户权限成功，用户ID: {}", userId);
                return true;
            }
        }
        return false;
    }

    /**
     * 批量设置用户权限
     *
     * @param permissions 权限设置列表，格式：[{userId: 1, canViewInspection: true, ...}, ...]
     * @return 成功设置的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchSetPermissions(java.util.List<Map<String, Object>> permissions) {
        int successCount = 0;
        
        for (Map<String, Object> permission : permissions) {
            Long userId = ((Number) permission.get("userId")).longValue();
            Boolean canViewInspection = (Boolean) permission.get("canViewInspection");
            Boolean canConfigureRules = (Boolean) permission.get("canConfigureRules");
            Boolean canHandleExceptions = (Boolean) permission.get("canHandleExceptions");
            
            try {
                if (setUserPermissions(userId, canViewInspection, canConfigureRules, canHandleExceptions)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量设置用户权限失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            }
        }
        
        return successCount;
    }

    /**
     * 获取所有用户权限列表
     *
     * @return 用户权限列表
     */
    public java.util.List<DataInspectionUserPermissionEntity> getAllPermissions() {
        return userPermissionMapper.selectAll();
    }

    /**
     * 检查用户是否为管理员（拥有所有权限）
     *
     * @param userId 用户ID
     * @return 是否为管理员
     */
    public boolean isAdmin(Long userId) {
        return canViewInspection(userId) && 
               canConfigureRules(userId) && 
               canHandleExceptions(userId);
    }

    /**
     * 获取用户权限摘要信息
     *
     * @param userId 用户ID
     * @return 权限摘要
     */
    public Map<String, Object> getUserPermissionSummary(Long userId) {
        Map<String, Object> summary = new HashMap<>();
        summary.put("userId", userId);
        summary.put("canViewInspection", canViewInspection(userId));
        summary.put("canConfigureRules", canConfigureRules(userId));
        summary.put("canHandleExceptions", canHandleExceptions(userId));
        summary.put("isAdmin", isAdmin(userId));
        
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission != null) {
            summary.put("createTime", permission.getCreateTime());
            summary.put("updateTime", permission.getUpdateTime());
        }
        
        return summary;
    }

    /**
     * 启用/禁用用户的查看权限
     *
     * @param userId 用户ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleViewPermission(Long userId, boolean enabled) {
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            permission = createDefaultPermission(userId);
        }
        
        permission.setCanViewInspection(enabled);
        permission.setUpdateTime(new Date());
        
        return userPermissionMapper.updateByKey(permission) > 0;
    }

    /**
     * 启用/禁用用户的配置权限
     *
     * @param userId 用户ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleConfigurePermission(Long userId, boolean enabled) {
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            permission = createDefaultPermission(userId);
        }
        
        permission.setCanConfigureRules(enabled);
        permission.setUpdateTime(new Date());
        
        return userPermissionMapper.updateByKey(permission) > 0;
    }

    /**
     * 启用/禁用用户的异常处理权限
     *
     * @param userId 用户ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleExceptionPermission(Long userId, boolean enabled) {
        DataInspectionUserPermissionEntity permission = getByUserId(userId);
        if (permission == null) {
            permission = createDefaultPermission(userId);
        }
        
        permission.setCanHandleExceptions(enabled);
        permission.setUpdateTime(new Date());
        
        return userPermissionMapper.updateByKey(permission) > 0;
    }

    /**
     * 重置用户权限为默认值
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefault(Long userId) {
        return setUserPermissions(userId, true, false, false);
    }

    /**
     * 批量查询用户权限
     *
     * @param userIds 用户ID列表
     * @return 权限映射表 userId -> PermissionEntity
     */
    public Map<Long, DataInspectionUserPermissionEntity> getBatchUserPermissions(java.util.List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        
        java.util.List<DataInspectionUserPermissionEntity> permissions = userPermissionMapper.selectByUserIds(userIds);
        Map<Long, DataInspectionUserPermissionEntity> result = new HashMap<>();
        
        for (DataInspectionUserPermissionEntity permission : permissions) {
            result.put(permission.getUserId(), permission);
        }
        
        return result;
    }

    /**
     * 统计权限分布情况
     *
     * @return 权限统计信息
     */
    public Map<String, Object> getPermissionStatistics() {
        java.util.List<DataInspectionUserPermissionEntity> allPermissions = getAllPermissions();
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalUsers", allPermissions.size());
        
        long canViewCount = allPermissions.stream()
                .mapToLong(p -> (p.getCanViewInspection() != null && p.getCanViewInspection()) ? 1 : 0)
                .sum();
        statistics.put("canViewCount", canViewCount);
        
        long canConfigureCount = allPermissions.stream()
                .mapToLong(p -> (p.getCanConfigureRules() != null && p.getCanConfigureRules()) ? 1 : 0)
                .sum();
        statistics.put("canConfigureCount", canConfigureCount);
        
        long canHandleCount = allPermissions.stream()
                .mapToLong(p -> (p.getCanHandleExceptions() != null && p.getCanHandleExceptions()) ? 1 : 0)
                .sum();
        statistics.put("canHandleCount", canHandleCount);
        
        long adminCount = allPermissions.stream()
                .mapToLong(p -> {
                    boolean isAdmin = (p.getCanViewInspection() != null && p.getCanViewInspection()) &&
                                    (p.getCanConfigureRules() != null && p.getCanConfigureRules()) &&
                                    (p.getCanHandleExceptions() != null && p.getCanHandleExceptions());
                    return isAdmin ? 1 : 0;
                })
                .sum();
        statistics.put("adminCount", adminCount);
        
        return statistics;
    }
}