package com.goodsogood.ows.services;

import com.goodsogood.ows.mapper.DataInspectionUserPermissionMapper;
import com.goodsogood.ows.model.db.DataInspectionUserPermissionEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据体检用户权限业务逻辑层
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
@Log4j2
public class DataInspectionUserPermissionService {

    @Autowired
    private DataInspectionUserPermissionMapper userPermissionMapper;

    /**
     * 获取用户权限
     *
     * @param userId 用户ID
     * @return 权限信息
     */
    public Map<String, Object> getUserPermissions(Long userId) {
        DataInspectionUserPermissionEntity permission = userPermissionMapper.findByUserId(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        
        if (permission != null) {
            result.put("canViewInspection", permission.getCanViewInspection());
            result.put("canConfigureRules", permission.getCanConfigureRules());
            result.put("canHandleExceptions", permission.getCanHandleExceptions());
        } else {
            // 如果没有配置权限，给默认值
            result.put("canViewInspection", true);
            result.put("canConfigureRules", false);
            result.put("canHandleExceptions", false);
        }
        
        return result;
    }

    /**
     * 设置用户权限
     *
     * @param userId 用户ID
     * @param canViewInspection 是否可以查看体检
     * @param canConfigureRules 是否可以配置规则
     * @param canHandleExceptions 是否可以处理异常
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean setUserPermissions(Long userId, Boolean canViewInspection, 
                                     Boolean canConfigureRules, Boolean canHandleExceptions) {
        DataInspectionUserPermissionEntity entity = new DataInspectionUserPermissionEntity();
        entity.setUserId(userId);
        entity.setCanViewInspection(canViewInspection);
        entity.setCanConfigureRules(canConfigureRules);
        entity.setCanHandleExceptions(canHandleExceptions);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        int result = userPermissionMapper.upsert(entity);
        if (result > 0) {
            log.info("设置用户权限成功，用户ID: {}", userId);
            return true;
        } else {
            throw new RuntimeException("设置用户权限失败");
        }
    }
}