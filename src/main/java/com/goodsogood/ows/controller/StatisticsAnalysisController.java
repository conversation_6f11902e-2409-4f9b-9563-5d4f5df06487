package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.StatisticsAnalysisVO.*;
import com.goodsogood.ows.services.StatisticsAnalysisService;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;

/**
 * 统计分析控制器
 * 提供数据体检统计分析相关的REST API接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Slf4j
@Api(tags = "统计分析管理")
@RestController
@RequestMapping("/api/statistics-analysis")
@CrossOrigin(origins = "*", maxAge = 3600)
public class StatisticsAnalysisController {

    private final StatisticsAnalysisService statisticsAnalysisService;
    private final Errors errors;

    public StatisticsAnalysisController(StatisticsAnalysisService statisticsAnalysisService, Errors errors) {
        this.statisticsAnalysisService = statisticsAnalysisService;
        this.errors = errors;
    }

    /**
     * 获取核心指标统计数据
     * 包括：总异常数、处理率、严重程度分布等核心业务指标
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取核心指标统计", notes = "获取数据体检核心指标统计信息")
    @GetMapping("/core-metrics")
    public ResponseEntity<Result<CoreMetricsVO>> getCoreMetrics(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        CoreMetricsVO metrics = statisticsAnalysisService.getCoreMetrics();
        return new ResponseEntity<>(new Result<>(metrics, errors), HttpStatus.OK);
    }

    /**
     * 获取月度统计数据
     * 支持按年份和数量限制进行查询
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取月度统计", notes = "获取按月份统计的数据体检情况")
    @GetMapping("/monthly")
    public ResponseEntity<Result<PagedResultVO<MonthlyStatisticsVO>>> getMonthlyStatistics(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "年份", example = "2024")
            @RequestParam(required = false) String year,
            @ApiParam(value = "限制条数", example = "12")
            @RequestParam(required = false, defaultValue = "12") Integer limit) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        PagedResultVO<MonthlyStatisticsVO> result = statisticsAnalysisService.getMonthlyStatistics(year, limit);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取部门统计数据
     * 支持排序、排序方式和数量限制
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取部门统计", notes = "获取按部门统计的数据体检情况")
    @GetMapping("/department")
    public ResponseEntity<Result<PagedResultVO<DepartmentStatisticsVO>>> getDepartmentStatistics(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "排序字段", example = "totalExceptions")
            @RequestParam(required = false) String sortBy,
            @ApiParam(value = "排序方式", example = "desc")
            @RequestParam(required = false, defaultValue = "desc") String order,
            @ApiParam(value = "限制条数", example = "10")
            @RequestParam(required = false, defaultValue = "10") Integer limit) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        PagedResultVO<DepartmentStatisticsVO> result =
                statisticsAnalysisService.getDepartmentStatistics(sortBy, order, limit);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取操作人员统计数据
     * 支持按部门筛选、排序和数量限制
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取操作人员统计", notes = "获取按操作人员统计的数据体检处理情况")
    @GetMapping("/operator")
    public ResponseEntity<Result<PagedResultVO<OperatorStatisticsVO>>> getOperatorStatistics(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "部门", example = "办公室")
            @RequestParam(required = false) String department,
            @ApiParam(value = "排序字段", example = "handledCount")
            @RequestParam(required = false) String sortBy,
            @ApiParam(value = "排序方式", example = "desc")
            @RequestParam(required = false, defaultValue = "desc") String order,
            @ApiParam(value = "限制条数", example = "10")
            @RequestParam(required = false, defaultValue = "10") Integer limit) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        PagedResultVO<OperatorStatisticsVO> result =
                statisticsAnalysisService.getOperatorStatistics(department, sortBy, order, limit);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取趋势分析数据
     * 支持按类型（日、周、月、年）和周期数进行查询
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取趋势分析", notes = "获取数据体检趋势分析数据")
    @GetMapping("/trend")
    public ResponseEntity<Result<TrendAnalysisResultVO>> getTrendAnalysis(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "分析类型", example = "daily")
            @RequestParam(required = false, defaultValue = "daily") String type,
            @ApiParam(value = "分析周期数", example = "30")
            @RequestParam(required = false, defaultValue = "30") Integer period,
            @ApiParam(value = "开始日期", example = "2024-01-01")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam(value = "结束日期", example = "2024-12-31")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        TrendAnalysisResultVO result =
                statisticsAnalysisService.getTrendAnalysis(type, period, startDate, endDate);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取综合统计数据
     * 一次性获取多种统计维度的汇总数据
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取综合统计", notes = "获取多维度综合统计数据")
    @GetMapping("/comprehensive")
    public ResponseEntity<Result<ComprehensiveStatisticsVO>> getComprehensiveStatistics(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "时间范围", example = "last30days")
            @RequestParam(required = false, defaultValue = "last30days") String timeRange,
            @ApiParam(value = "统计维度", example = "all")
            @RequestParam(required = false, defaultValue = "all") String dimension) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        ComprehensiveStatisticsVO result =
                statisticsAnalysisService.getComprehensiveStatistics(timeRange, dimension);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 自定义统计查询
     * 支持灵活的多维度条件查询
     */
    @HttpMonitorLogger
    @ApiOperation(value = "自定义统计查询", notes = "支持多维度条件的灵活统计查询")
    @PostMapping("/custom-query")
    public ResponseEntity<Result<CustomQueryResultVO>> customStatisticsQuery(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody CustomQueryParamsVO params,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        CustomQueryResultVO result = statisticsAnalysisService.customStatisticsQuery(params);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 导出统计报表
     * 支持Excel、PDF、CSV等多种格式导出
     */
    @HttpMonitorLogger
    @ApiOperation(value = "导出统计报表", notes = "导出各类统计数据为报表文件")
    @PostMapping("/export")
    public ResponseEntity<Result<ExportResultVO>> exportStatisticsReport(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody ExportParamsVO params,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        ExportResultVO result = statisticsAnalysisService.exportStatisticsReport(params);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取实时统计数据
     * 实时展示当前系统运行状态和最新统计信息
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取实时统计数据", notes = "获取实时的系统运行状态和统计数据")
    @GetMapping("/real-time")
    public ResponseEntity<Result<RealTimeDataVO>> getRealTimeStatistics(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        RealTimeDataVO result = statisticsAnalysisService.getRealTimeStatistics();
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取统计维度配置选项
     * 返回可用的统计维度配置选项，用于前端动态生成筛选条件
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取统计维度配置", notes = "获取可用的统计维度配置选项")
    @GetMapping("/dimension-options")
    public ResponseEntity<Result<DimensionOptionsVO>> getDimensionOptions(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        DimensionOptionsVO options = statisticsAnalysisService.getDimensionOptions();
        return new ResponseEntity<>(new Result<>(options, errors), HttpStatus.OK);
    }

    /**
     * 获取仪表板配置
     * 返回仪表板显示配置，包括图表类型、布局等信息
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取仪表板配置", notes = "获取统计仪表板显示配置")
    @GetMapping("/dashboard-config")
    public ResponseEntity<Result<DashboardConfigVO>> getDashboardConfig(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        DashboardConfigVO config = statisticsAnalysisService.getDashboardConfig();
        return new ResponseEntity<>(new Result<>(config, errors), HttpStatus.OK);
    }

    /**
     * 更新统计设置
     * 更新统计分析的相关配置设置
     */
    @HttpMonitorLogger
    @ApiOperation(value = "更新统计设置", notes = "更新统计分析相关配置设置")
    @PutMapping("/settings")
    public ResponseEntity<Result<Boolean>> updateStatisticsSettings(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody StatisticsSettingsVO settings,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        boolean success = statisticsAnalysisService.updateStatisticsSettings(settings);
        return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
    }

    /**
     * 获取异常统计概览
     * 快速获取异常数据的概览统计信息
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取异常统计概览", notes = "获取异常数据的快速概览统计")
    @GetMapping("/exception-overview")
    public ResponseEntity<Result<ExceptionOverviewVO>> getExceptionOverview(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "时间范围", example = "last7days")
            @RequestParam(required = false, defaultValue = "last7days") String timeRange) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);


        ExceptionOverviewVO overview = statisticsAnalysisService.getExceptionOverview(timeRange);
        return new ResponseEntity<>(new Result<>(overview, errors), HttpStatus.OK);

    }

    /**
     * 获取处理效率统计
     * 统计异常处理的效率相关数据
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取处理效率统计", notes = "统计异常处理效率相关数据")
    @GetMapping("/efficiency-stats")
    public ResponseEntity<Result<EfficiencyStatsVO>> getEfficiencyStats(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "开始日期", example = "2024-01-01")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @ApiParam(value = "结束日期", example = "2024-12-31")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        EfficiencyStatsVO stats = statisticsAnalysisService.getEfficiencyStats(startDate, endDate);
        return new ResponseEntity<>(new Result<>(stats, errors), HttpStatus.OK);

    }

    /**
     * 刷新统计缓存
     * 手动触发统计数据的重新计算和缓存更新
     */
    @HttpMonitorLogger
    @ApiOperation(value = "刷新统计缓存", notes = "手动刷新统计数据缓存")
    @PostMapping("/refresh-cache")
    public ResponseEntity<Result<Boolean>> refreshStatisticsCache(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "缓存类型", example = "all")
            @RequestParam(required = false, defaultValue = "all") String cacheType) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        boolean success = statisticsAnalysisService.refreshStatisticsCache(cacheType);
        return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);

    }
}