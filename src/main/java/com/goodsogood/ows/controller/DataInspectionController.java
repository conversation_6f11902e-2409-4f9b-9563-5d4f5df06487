package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.DataInspectionDataSourceEntity;
import com.goodsogood.ows.model.db.DataInspectionExceptionEntity;
import com.goodsogood.ows.model.db.DataInspectionScheduledTaskEntity;
import com.goodsogood.ows.model.db.RemediationRecordEntity;
import com.goodsogood.ows.model.vo.PageResult;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.*;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据体检统一控制器
 * 严格按照data-inspection.js中的API路由定义
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Slf4j
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DataInspectionController {

    private final DataInspectionExceptionService dataInspectionExceptionService;
    private final DataInspectionDataSourceService dataInspectionDataSourceService;
    private final DataInspectionRuleService dataInspectionRuleService;
    private final DataInspectionScheduledTaskService dataInspectionScheduledTaskService;
    private final DataInspectionUserPermissionService dataInspectionUserPermissionService;
    private final RemediationRecordService remediationRecordService;
    private final Errors errors;

    public DataInspectionController(DataInspectionExceptionService dataInspectionExceptionService,
                                    DataInspectionDataSourceService dataInspectionDataSourceService,
                                    DataInspectionRuleService dataInspectionRuleService,
                                    DataInspectionScheduledTaskService dataInspectionScheduledTaskService,
                                    DataInspectionUserPermissionService dataInspectionUserPermissionService,
                                    RemediationRecordService remediationRecordService,
                                    Errors errors) {
        this.dataInspectionExceptionService = dataInspectionExceptionService;
        this.dataInspectionDataSourceService = dataInspectionDataSourceService;
        this.dataInspectionRuleService = dataInspectionRuleService;
        this.dataInspectionScheduledTaskService = dataInspectionScheduledTaskService;
        this.dataInspectionUserPermissionService = dataInspectionUserPermissionService;
        this.remediationRecordService = remediationRecordService;
        this.errors = errors;
    }

    // ================= 权限控制接口 =================

    /**
     * 获取数据体检用户权限
     */
    @HttpMonitorLogger
    @GetMapping("/permissions/{userId}")
    @ApiOperation(value = "获取数据体检用户权限", notes = "获取用户在数据体检模块的权限信息")
    public ResponseEntity<Result<Map<String, Object>>> getPermissions(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> permissions = dataInspectionUserPermissionService.getUserPermissions(userId);
            return new ResponseEntity<>(new Result<>(permissions, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取数据体检用户权限失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 数据体检展示接口 =================

    /**
     * 获取异常信息概览
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/overview")
    @ApiOperation(value = "获取异常信息概览", notes = "获取数据体检异常的概览统计信息")
    public ResponseEntity<Result<Map<String, Object>>> getExceptionsOverview(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> overview = dataInspectionExceptionService.getOverviewStatistics();
            return new ResponseEntity<>(new Result<>(overview, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取数据体检异常概览失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 按异常项统计
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/statistics/by-type")
    @ApiOperation(value = "按异常项统计", notes = "按异常类型进行统计分析")
    public ResponseEntity<Result<List<Map<String, Object>>>> getExceptionStatisticsByType(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            List<Map<String, Object>> statistics = dataInspectionExceptionService.countByType();
            return new ResponseEntity<>(new Result<>(statistics, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("按异常项统计失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 按单位统计
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/statistics/by-unit")
    @ApiOperation(value = "按单位统计", notes = "按单位进行异常统计分析")
    public ResponseEntity<Result<List<Map<String, Object>>>> getExceptionStatisticsByUnit(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            List<Map<String, Object>> statistics = dataInspectionExceptionService.countByUnit();
            return new ResponseEntity<>(new Result<>(statistics, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("按单位统计失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取异常详情列表
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/details")
    @ApiOperation(value = "获取异常详情列表", notes = "分页查询异常详情列表，支持多条件筛选")
    public ResponseEntity<Result<PageResult<DataInspectionExceptionEntity>>> getExceptionDetails(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "异常类型") @RequestParam(required = false) String type,
            @ApiParam(value = "单位") @RequestParam(required = false) String unit,
            @ApiParam(value = "严重程度") @RequestParam(required = false) String severity,
            @ApiParam(value = "状态") @RequestParam(required = false) String status,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pageSize) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> params = dataInspectionExceptionService.buildQueryParams(type, severity, status, unit, null, null, null);
            PageResult<DataInspectionExceptionEntity> result = dataInspectionExceptionService.findByPage(page, pageSize, params, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取异常详情列表失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取特定异常详情
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/details/{id}")
    @ApiOperation(value = "获取特定异常详情", notes = "根据ID获取异常详情信息")
    public ResponseEntity<Result<DataInspectionExceptionEntity>> getExceptionDetail(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "异常ID", required = true) @PathVariable Long id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            DataInspectionExceptionEntity result = dataInspectionExceptionService.findById(id);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取特定异常详情失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 体检规则配置接口 =================

    /**
     * 获取党组（党委）设置体检规则
     */
    @HttpMonitorLogger
    @GetMapping("/rules/party-organization")
    @ApiOperation(value = "获取党组（党委）设置体检规则", notes = "获取党组（党委）设置相关的体检规则配置")
    public ResponseEntity<Result<Map<String, Object>>> getPartyOrganizationRules(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 查询党组（党委）设置体检规则
            Map<String, Object> result = dataInspectionRuleService.findWithDetailsById(1L);
            if (result == null) {
                // 如果没有找到，返回默认结构
                Map<String, Object> rules = new HashMap<>();
                rules.put("id", 1);
                rules.put("name", "党组（党委）设置体检");
                rules.put("description", "检查党组（党委）设置的完整性和规范性");
                rules.put("status", "active");
                rules.put("details", new java.util.ArrayList<>());
                return new ResponseEntity<>(new Result<>(rules, errors), HttpStatus.OK);
            }

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取党组（党委）设置体检规则失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 设置党组（党委）设置体检规则
     */
    @HttpMonitorLogger
    @PostMapping("/rules/party-organization")
    @ApiOperation(value = "设置党组（党委）设置体检规则", notes = "更新党组（党委）设置相关的体检规则配置")
    public ResponseEntity<Result<Map<String, Object>>> setPartyOrganizationRules(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            // 这里应该实现规则更新逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "党组（党委）设置体检规则更新成功");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("设置党组（党委）设置体检规则失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取所有体检规则
     */
    @HttpMonitorLogger
    @GetMapping("/rules")
    @ApiOperation(value = "获取所有体检规则", notes = "获取所有体检规则配置")
    public ResponseEntity<Result<Map<String, Object>>> getAllRules(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 获取所有规则分类和详情
            Map<String, Object> allRules = new HashMap<>();

            // 按分类获取规则
            allRules.put("partyOrganization", dataInspectionRuleService.findWithDetailsById(1L));
            allRules.put("partyOfficials", dataInspectionRuleService.findWithDetailsById(2L));
            allRules.put("tasks", dataInspectionRuleService.findWithDetailsById(3L));
            allRules.put("userInfo", dataInspectionRuleService.findWithDetailsById(4L));

            return new ResponseEntity<>(new Result<>(allRules, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取所有体检规则失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 自动体检执行接口 =================

    /**
     * 获取自动体检配置
     */
    @HttpMonitorLogger
    @GetMapping("/auto-inspection/config")
    @ApiOperation(value = "获取自动体检配置", notes = "获取自动体检的配置信息")
    public ResponseEntity<Result<Map<String, Object>>> getAutoInspectionConfig(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该从数据库查询自动体检配置
            Map<String, Object> config = new java.util.HashMap<>();
            config.put("id", 1);
            config.put("name", "定时全面体检");
            config.put("enabled", true);

            return new ResponseEntity<>(new Result<>(config, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取自动体检配置失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 设置自动体检配置
     */
    @HttpMonitorLogger
    @PutMapping("/auto-inspection/config")
    @ApiOperation(value = "设置自动体检配置", notes = "更新自动体检的配置信息")
    public ResponseEntity<Result<Map<String, Object>>> updateAutoInspectionConfig(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody Map<String, Object> config,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现配置更新逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "自动体检配置更新成功");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("设置自动体检配置失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 手动触发体检
     */
    @HttpMonitorLogger
    @PostMapping("/inspection/trigger")
    @ApiOperation(value = "手动触发体检", notes = "手动触发数据体检任务")
    public ResponseEntity<Result<Map<String, Object>>> triggerInspection(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现手动触发体检的逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "体检任务已启动");
            result.put("id", System.currentTimeMillis());
            result.put("status", "running");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("手动触发体检失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取体检结果列表
     */
    @HttpMonitorLogger
    @GetMapping("/inspection/results")
    @ApiOperation(value = "获取体检结果列表", notes = "分页查询体检结果列表")
    public ResponseEntity<Result<PageResult<Map<String, Object>>>> getInspectionResults(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "状态") @RequestParam(required = false) String status,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pageSize) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该实现体检结果查询逻辑
            List<Map<String, Object>> list = new java.util.ArrayList<>();
            PageResult<Map<String, Object>> result = new PageResult<>(list, 0, page, pageSize);

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取体检结果列表失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取特定体检结果详情
     */
    @HttpMonitorLogger
    @GetMapping("/inspection/results/{id}")
    @ApiOperation(value = "获取特定体检结果详情", notes = "根据ID获取体检结果详情")
    public ResponseEntity<Result<Map<String, Object>>> getInspectionResult(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "体检结果ID", required = true) @PathVariable Long id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该实现特定体检结果查询逻辑
            Map<String, Object> result = new java.util.HashMap<>();

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取特定体检结果详情失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 数据源管理接口 =================

    /**
     * 获取数据源列表
     */
    @HttpMonitorLogger
    @GetMapping("/data-sources")
    @ApiOperation(value = "获取数据源列表", notes = "查询数据源列表，支持条件筛选")
    public ResponseEntity<Result<Map<String, Object>>> getDataSources(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "数据源名称") @RequestParam(required = false) String sourceName,
            @ApiParam(value = "数据源类型") @RequestParam(required = false) String sourceType,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> params = dataInspectionDataSourceService.buildQueryParams(sourceName, sourceType, status, null);
            List<DataInspectionDataSourceEntity> data = dataInspectionDataSourceService.findAllEnabled();

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("data", data);
            result.put("total", data.size());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取数据源列表失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 创建数据源
     */
    @HttpMonitorLogger
    @PostMapping("/data-sources")
    @ApiOperation(value = "创建数据源", notes = "创建新的数据源")
    public ResponseEntity<Result<Map<String, Object>>> createDataSource(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现数据源创建逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "创建数据源成功");
            result.put("id", "ds-" + System.currentTimeMillis());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("创建数据源失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 测试数据源连接
     */
    @HttpMonitorLogger
    @PostMapping("/data-sources/{id}/test-connection")
    @ApiOperation(value = "测试数据源连接", notes = "测试数据源连接是否正常")
    public ResponseEntity<Result<Map<String, Object>>> testDataSourceConnection(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "数据源ID", required = true) @PathVariable String id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该实现连接测试逻辑
            Map<String, Object> testResult = new java.util.HashMap<>();
            testResult.put("success", true);
            testResult.put("message", "连接测试成功");
            testResult.put("latency", 150);

            return new ResponseEntity<>(new Result<>(testResult, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("测试数据源连接失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 启动数据源同步
     */
    @HttpMonitorLogger
    @PostMapping("/data-sources/{id}/sync")
    @ApiOperation(value = "启动数据源同步", notes = "启动数据源同步任务")
    public ResponseEntity<Result<Map<String, Object>>> syncDataSource(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "数据源ID", required = true) @PathVariable String id,
            @Valid @RequestBody(required = false) Map<String, Object> requestData) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该实现数据源同步逻辑
            Map<String, Object> syncResult = new java.util.HashMap<>();
            syncResult.put("message", "同步任务已启动");
            syncResult.put("taskId", System.currentTimeMillis());
            syncResult.put("estimatedDuration", 600);

            return new ResponseEntity<>(new Result<>(syncResult, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("启动数据源同步失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 定时任务管理接口 =================

    /**
     * 获取定时任务列表
     */
    @HttpMonitorLogger
    @GetMapping("/scheduled-tasks")
    @ApiOperation(value = "获取定时任务列表", notes = "查询定时任务列表，支持条件筛选")
    public ResponseEntity<Result<Map<String, Object>>> getScheduledTasks(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "任务名称") @RequestParam(required = false) String taskName,
            @ApiParam(value = "任务类型") @RequestParam(required = false) Integer taskType,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status,
            @ApiParam(value = "是否启用") @RequestParam(required = false) String isEnabled) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Boolean enabledFlag = null;
            if (isEnabled != null) {
                enabledFlag = "true".equals(isEnabled);
            }

            Map<String, Object> params = dataInspectionScheduledTaskService.buildQueryParams(taskName, taskType, status, enabledFlag, null);
            List<DataInspectionScheduledTaskEntity> data = dataInspectionScheduledTaskService.findRecentTasks(100);

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("data", data);
            result.put("total", data.size());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取定时任务列表失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 创建定时任务
     */
    @HttpMonitorLogger
    @PostMapping("/scheduled-tasks")
    @ApiOperation(value = "创建定时任务", notes = "创建新的定时任务")
    public ResponseEntity<Result<Map<String, Object>>> createScheduledTask(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现定时任务创建逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "创建定时任务成功");
            result.put("id", System.currentTimeMillis());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("创建定时任务失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 启用/禁用定时任务
     */
    @HttpMonitorLogger
    @PutMapping("/scheduled-tasks/{id}/toggle")
    @ApiOperation(value = "启用/禁用定时任务", notes = "切换定时任务的启用状态")
    public ResponseEntity<Result<Map<String, Object>>> toggleScheduledTask(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "任务ID", required = true) @PathVariable Long id,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            Boolean enabled = (Boolean) requestData.get("enabled");
            boolean result = dataInspectionScheduledTaskService.toggleEnable(id, enabled, sysHeader);

            Map<String, Object> response = new java.util.HashMap<>();
            response.put("message", (enabled ? "启用" : "禁用") + "定时任务成功");

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("启用/禁用定时任务失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 立即执行定时任务
     */
    @HttpMonitorLogger
    @PostMapping("/scheduled-tasks/{id}/execute")
    @ApiOperation(value = "立即执行定时任务", notes = "立即执行指定的定时任务")
    public ResponseEntity<Result<Map<String, Object>>> executeScheduledTask(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "任务ID", required = true) @PathVariable Long id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = dataInspectionScheduledTaskService.executeTask(id, sysHeader);

            Map<String, Object> response = new java.util.HashMap<>();
            response.put("message", "任务已加入执行队列");
            response.put("taskId", System.currentTimeMillis());
            response.put("estimatedDuration", 300);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("立即执行定时任务失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 体检结果整改接口 =================

    /**
     * 获取整改权限验证
     */
    @HttpMonitorLogger
    @GetMapping("/remediation/permissions/{userId}")
    @ApiOperation(value = "获取整改权限验证", notes = "验证用户是否具有整改异常的权限")
    public ResponseEntity<Result<Map<String, Object>>> getRemediationPermissions(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "用户ID", required = true) @PathVariable Long userId) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> permissions = dataInspectionUserPermissionService.getUserPermissions(userId);
            // 只返回整改相关权限
            Map<String, Object> result = new HashMap<>();
            result.put("canHandleExceptions", permissions.get("canHandleExceptions"));

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取整改权限验证失败", e);
            throw new ApiException("获取整改权限验证失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 创建整改记录
     */
    @HttpMonitorLogger
    @PostMapping("/remediation/records")
    @ApiOperation(value = "创建整改记录", notes = "为异常创建新的整改记录")
    public ResponseEntity<Result<RemediationRecordEntity>> createRemediationRecord(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody RemediationRecordEntity entity,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            RemediationRecordEntity result = remediationRecordService.create(entity, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("创建整改记录失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 更新整改记录
     */
    @HttpMonitorLogger
    @PutMapping("/remediation/records/{id}")
    @ApiOperation(value = "更新整改记录", notes = "更新整改记录的状态和信息")
    public ResponseEntity<Result<RemediationRecordEntity>> updateRemediationRecord(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "整改记录ID", required = true) @PathVariable Long id,
            @Valid @RequestBody RemediationRecordEntity entity,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        entity.setId(id);

        try {
            RemediationRecordEntity result = remediationRecordService.update(entity, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("更新整改记录失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取整改记录列表
     */
    @HttpMonitorLogger
    @GetMapping("/remediation/records")
    @ApiOperation(value = "获取整改记录列表", notes = "分页查询整改记录列表")
    public ResponseEntity<Result<PageResult<RemediationRecordEntity>>> getRemediationRecords(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "状态") @RequestParam(required = false) String status,
            @ApiParam(value = "操作人") @RequestParam(required = false) String operator,
            @ApiParam(value = "异常ID") @RequestParam(required = false) Long exceptionId,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pageSize) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> params = remediationRecordService.buildQueryParams(status, operator, exceptionId, null, null);
            PageResult<RemediationRecordEntity> result = remediationRecordService.findByPage(page, pageSize, params, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取整改记录列表失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 一键跳转到问题源头
     */
    @HttpMonitorLogger
    @GetMapping("/exceptions/{id}/jump-to-source")
    @ApiOperation(value = "一键跳转到问题源头", notes = "生成跳转链接，定位到问题的源头系统")
    public ResponseEntity<Result<Map<String, Object>>> jumpToSource(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "异常ID", required = true) @PathVariable Long id) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            DataInspectionExceptionEntity exception = dataInspectionExceptionService.findById(id);
            if (exception == null) {
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value()), HttpStatus.NOT_FOUND);
            }

            // 模拟生成跳转链接
            String jumpUrl = "/systems/" + exception.getSource().replaceAll("\\s", "-").toLowerCase() +
                             "?recordId=" + exception.getId() + "&unit=" + exception.getUnit();

            Map<String, Object> result = new java.util.HashMap<>();
            result.put("jumpUrl", jumpUrl);
            result.put("source", exception.getSource());
            result.put("description", "跳转到" + exception.getSource() + "，定位相关数据记录");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("一键跳转到问题源头失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 整改后自动触发复检
     */
    @HttpMonitorLogger
    @PostMapping("/remediation/re-inspection/{remediationId}")
    @ApiOperation(value = "整改后自动触发复检", notes = "整改完成后触发自动复检")
    public ResponseEntity<Result<Map<String, Object>>> triggerReInspection(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "整改记录ID", required = true) @PathVariable Long remediationId) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> result = remediationRecordService.triggerReInspection(remediationId, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("整改后自动触发复检失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================= 其他data-inspection.js中定义的接口 =================

    /**
     * 更新数据源
     */
    @HttpMonitorLogger
    @PutMapping("/data-sources/{id}")
    @ApiOperation(value = "更新数据源", notes = "更新指定ID的数据源信息")
    public ResponseEntity<Result<Map<String, Object>>> updateDataSource(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "数据源ID", required = true) @PathVariable String id,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现数据源更新逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "更新数据源成功");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("更新数据源失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 删除数据源
     */
    @HttpMonitorLogger
    @DeleteMapping("/data-sources/{id}")
    @ApiOperation(value = "删除数据源", notes = "删除指定ID的数据源")
    public ResponseEntity<Result<Map<String, Object>>> deleteDataSource(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "数据源ID", required = true) @PathVariable String id) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里应该实现数据源删除逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "删除数据源成功");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("删除数据源失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 更新定时任务
     */
    @HttpMonitorLogger
    @PutMapping("/scheduled-tasks/{id}")
    @ApiOperation(value = "更新定时任务", notes = "更新指定ID的定时任务")
    public ResponseEntity<Result<Map<String, Object>>> updateScheduledTask(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "任务ID", required = true) @PathVariable Long id,
            @Valid @RequestBody Map<String, Object> requestData,
            BindingResult bindingResult) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            // 这里应该实现定时任务更新逻辑
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("message", "更新定时任务成功");

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("更新定时任务失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 删除定时任务
     */
    @HttpMonitorLogger
    @DeleteMapping("/scheduled-tasks/{id}")
    @ApiOperation(value = "删除定时任务", notes = "删除指定ID的定时任务")
    public ResponseEntity<Result<Map<String, Object>>> deleteScheduledTask(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "任务ID", required = true) @PathVariable Long id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = dataInspectionScheduledTaskService.delete(id, sysHeader);

            Map<String, Object> response = new java.util.HashMap<>();
            response.put("message", "删除定时任务成功");

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("删除定时任务失败", new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }
}