package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.DataInspectionResultVO.*;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.DataInspectionResultsService;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 数据体检结果管理控制器
 * 提供异常列表管理、批量整改、复检等功能的REST API接口
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Api(tags = "数据体检结果管理")
@RestController
@RequestMapping("/api/data-inspection/health-check")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DataInspectionResultsController {

    @Resource
    private DataInspectionResultsService dataInspectionResultsService;

    @Resource
    private Errors errors;

    /**
     * 获取异常列表 - 支持搜索和筛选
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取异常列表", notes = "分页查询异常列表，支持多种筛选条件")
    @GetMapping("/exceptions")
    public ResponseEntity<Result<PagedResultVO<HealthCheckExceptionVO>>> getExceptionList(
            @ApiParam(value = "异常标题") @RequestParam(required = false) String exceptionTitle,
            @ApiParam(value = "异常类型: 1-完整性异常 2-准确性异常 3-一致性异常 4-安全性异常") @RequestParam(required = false) Integer exceptionType,
            @ApiParam(value = "异常级别: 1-低 2-中 3-高") @RequestParam(required = false) Integer exceptionLevel,
            @ApiParam(value = "状态: 1-待处理 2-处理中 3-已修复 4-已忽略") @RequestParam(required = false) Integer status,
            @ApiParam(value = "影响对象") @RequestParam(required = false) String affectedObject,
            @ApiParam(value = "责任人") @RequestParam(required = false) String assignee,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {

        log.info("获取异常列表请求: exceptionTitle={}, exceptionType={}, exceptionLevel={}, status={}, affectedObject={}, assignee={}, pageNum={}, pageSize={}",
                exceptionTitle, exceptionType, exceptionLevel, status, affectedObject, assignee, pageNum, pageSize);

        // 构建搜索参数
        ExceptionSearchVO searchParams = ExceptionSearchVO.builder()
                .exceptionTitle(exceptionTitle)
                .exceptionType(exceptionType)
                .exceptionLevel(exceptionLevel)
                .status(status)
                .affectedObject(affectedObject)
                .assignee(assignee)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();

        PagedResultVO<HealthCheckExceptionVO> result = dataInspectionResultsService.getExceptionList(searchParams);

        log.info("获取异常列表成功，总数: {}, 当前页数据量: {}",
                result.getTotal(), result.getData().size());

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);

    }

    /**
     * 获取异常统计数据
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取异常统计数据", notes = "获取异常统计汇总信息，包括总数、趋势等")
    @GetMapping("/statistics")
    public ResponseEntity<Result<HealthCheckStatisticsVO>> getExceptionStatistics() {

        try {
            log.info("获取异常统计数据请求");

            HealthCheckStatisticsVO statistics = dataInspectionResultsService.getExceptionStatistics();

            log.info("获取异常统计数据成功: 总异常数={}, 高级别异常数={}, 已修复数={}",
                    statistics.getTotalExceptions(), statistics.getHighLevelExceptions(), statistics.getFixedExceptions());

            return new ResponseEntity<>(new Result<>(statistics, errors), HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取异常统计数据失败: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(null, errors, "获取异常统计数据失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 批量整改异常项
     */
    @HttpMonitorLogger
    @ApiOperation(value = "批量整改异常", notes = "对多个异常项执行批量整改操作")
    @PostMapping("/exceptions/batch-remediate")
    public ResponseEntity<Result<BatchRemediationResultVO>> batchRemediateExceptions(
            @ApiParam(value = "批量整改参数", required = true) @Valid @RequestBody BatchRemediationVO batchRemediation,
            @RequestHeader HttpHeaders headers,
            BindingResult bindingResult) {

        try {
            // 参数校验
            if (bindingResult.hasErrors()) {
                String errorMsg = bindingResult.getFieldError() != null ?
                        bindingResult.getFieldError().getDefaultMessage() : "参数校验失败";
                log.warn("批量整改异常参数校验失败: {}", errorMsg);
                return new ResponseEntity<>(new Result<>(null, errors, errorMsg), HttpStatus.BAD_REQUEST);
            }

            log.info("批量整改异常请求: 异常数量={}, 整改计划={}",
                    batchRemediation.getExceptionIds().size(), batchRemediation.getRemediationPlan().getDescription());

            // 获取系统头信息
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            if (batchRemediation.getRemediationPlan().getAssignee() == null) {
                batchRemediation.getRemediationPlan().setAssignee(
                        sysHeader != null ? sysHeader.getUserName() : "系统管理员");
            }

            BatchRemediationResultVO result = dataInspectionResultsService.batchRemediateExceptions(batchRemediation);

            log.info("批量整改异常成功: 成功数量={}, 失败数量={}, 批次ID={}",
                    result.getSuccessCount(), result.getFailedCount(), result.getBatchId());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.warn("批量整改异常参数错误: {}", e.getMessage());
            return new ResponseEntity<>(new Result<>(null, errors, e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("批量整改异常失败: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(null, errors, "批量整改异常失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 单项整改异常项
     */
    @HttpMonitorLogger
    @ApiOperation(value = "单项整改异常", notes = "对单个异常项执行整改操作")
    @PostMapping("/exceptions/{exceptionId}/remediate")
    public ResponseEntity<Result<OperationResultVO>> remediateException(
            @ApiParam(value = "异常ID", required = true) @PathVariable Long exceptionId,
            @ApiParam(value = "整改数据", required = true) @Valid @RequestBody RemediationParamVO paramVo,
            BindingResult bindingResult) {

        log.info("单项整改异常请求: 异常ID={}, 整改描述={}",
                exceptionId, paramVo.getRemediationData());

        OperationResultVO result = dataInspectionResultsService.remediateException(exceptionId, paramVo.getRemediationData());

        log.info("单项整改异常完成: 异常ID={}, 结果={}",
                exceptionId, result.getSuccess() ? "成功" : "失败");

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 提交整改结果
     */
    @HttpMonitorLogger
    @ApiOperation(value = "提交整改结果", notes = "提交异常项的整改结果和状态更新")
    @PostMapping("/exceptions/{exceptionId}/submit-remediation")
    public ResponseEntity<Result<OperationResultVO>> submitRemediationResult(
            @ApiParam(value = "异常ID", required = true) @PathVariable Long exceptionId,
            @ApiParam(value = "整改结果", required = true) @Valid @RequestBody RemediationResultVO result,
            BindingResult bindingResult) {
        OperationResultVO operationResult = dataInspectionResultsService.submitRemediationResult(exceptionId, result);
        return new ResponseEntity<>(new Result<>(operationResult, errors), HttpStatus.OK);

    }

    /**
     * 批量复检异常项
     */
    @HttpMonitorLogger
    @ApiOperation(value = "批量复检异常", notes = "对多个异常项执行批量复检操作")
    @PostMapping("/exceptions/batch-recheck")
    public ResponseEntity<Result<RecheckResultVO>> batchRecheckExceptions(
            @ApiParam(value = "批量复检参数", required = true) @Valid @RequestBody BatchRecheckVO batchRecheck,
            BindingResult bindingResult) {

        log.info("批量复检异常请求: 异常数量={}", batchRecheck.getExceptionIds().size());

        RecheckResultVO result = dataInspectionResultsService.batchRecheckExceptions(batchRecheck);

        log.info("批量复检异常启动成功: 任务ID={}, 成功数量={}, 预计时长={}秒",
                result.getTaskId(), result.getSuccessCount(), result.getEstimatedDuration());

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 单项复检异常项
     */
    @HttpMonitorLogger
    @ApiOperation(value = "单项复检异常", notes = "对单个异常项执行复检操作")
    @PostMapping("/exceptions/{exceptionId}/recheck")
    public ResponseEntity<Result<RecheckResultVO>> recheckException(
            @ApiParam(value = "异常ID", required = true) @PathVariable Long exceptionId) {

        RecheckResultVO result = dataInspectionResultsService.recheckException(exceptionId);

        log.info("单项复检异常启动成功: 异常ID={}, 任务ID={}, 预计时长={}秒",
                exceptionId, result.getTaskId(), result.getEstimatedDuration());

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 导出异常结果报告
     */
    @HttpMonitorLogger
    @ApiOperation(value = "导出异常结果", notes = "导出异常结果报告，支持多种格式")
    @PostMapping("/results/export")
    public ResponseEntity<Result<ExportResultVO>> exportExceptionResults(
            @ApiParam(value = "导出参数", required = true) @Valid @RequestBody ExportParamsVO exportParams,
            BindingResult bindingResult) {

        log.info("导出异常结果请求: 格式={}", exportParams.getFormat());

        ExportResultVO result = dataInspectionResultsService.exportExceptionResults(exportParams);

        log.info("导出异常结果成功: 文件名={}, 下载地址={}",
                result.getFilename(), result.getDownloadUrl());

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取影响单位排行
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取影响单位排行", notes = "获取受异常影响最多的单位排行榜")
    @GetMapping("/exceptions/affected-units-ranking")
    public ResponseEntity<Result<AffectedUnitsRankingVO>> getAffectedUnitsRanking() {

        AffectedUnitsRankingVO result = dataInspectionResultsService.getAffectedUnitsRanking();

        log.info("获取影响单位排行成功，单位数量: {}", result.getUnits().size());

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取异常趋势分析
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取异常趋势分析", notes = "获取异常趋势分析数据，支持多种时间粒度")
    @GetMapping("/exceptions/trend")
    public ResponseEntity<Result<Object>> getExceptionTrend(
            @ApiParam(value = "时间粒度: daily, weekly, monthly") @RequestParam(defaultValue = "daily") String granularity) {

        log.info("获取异常趋势分析请求: 粒度={}", granularity);

        // 这里可以根据粒度返回不同的趋势数据
        // 当前简化返回基础趋势数据
        HealthCheckStatisticsVO statistics = dataInspectionResultsService.getExceptionStatistics();

        Object trendData = java.util.Map.of(
                "trendData", statistics.getExceptionTrend(),
                "insights", java.util.Map.of(
                        "trend", "stable",
                        "suggestion", "异常数量相对稳定，建议继续关注高级别异常"
                )
        );

        log.info("获取异常趋势分析成功");

        return new ResponseEntity<>(new Result<>(trendData, errors), HttpStatus.OK);

    }
}