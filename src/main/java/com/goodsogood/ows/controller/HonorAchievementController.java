package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.HonorAchievementEntity;
import com.goodsogood.ows.model.vo.PageResult;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.HonorAchievementService;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 荣誉成果管理控制器
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/honor-achievement")
@Api(tags = "荣誉成果管理")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
public class HonorAchievementController {

    private final HonorAchievementService honorAchievementService;

    private final Errors errors;

    public HonorAchievementController(HonorAchievementService honorAchievementService, Errors errors) {
        this.honorAchievementService = honorAchievementService;
        this.errors = errors;
    }


    /**
     * 创建荣誉成果
     */
    @HttpMonitorLogger
    @PostMapping("/insert")
    @ApiOperation(value = "创建荣誉成果", notes = "创建新的荣誉成果记录")
    public ResponseEntity<Result<HonorAchievementEntity>> insert(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody HonorAchievementEntity entity,
            BindingResult bindingResult) {
        
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        
        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "参数验证失败"), HttpStatus.BAD_REQUEST);
        }

        try {
            HonorAchievementEntity result = honorAchievementService.create(entity, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("创建荣誉成果失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 更新荣誉成果
     */
    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation(value = "更新荣誉成果", notes = "更新荣誉成果信息")
    public ResponseEntity<Result<HonorAchievementEntity>> update(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody HonorAchievementEntity entity,
            BindingResult bindingResult) {
        
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        
        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "参数验证失败"), HttpStatus.BAD_REQUEST);
        }

        if (entity.getId() == null) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "ID不能为空"), HttpStatus.BAD_REQUEST);
        }

        try {
            HonorAchievementEntity result = honorAchievementService.update(entity, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("更新荣誉成果失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除荣誉成果
     */
    @HttpMonitorLogger
    @DeleteMapping("/remove/{id}")
    @ApiOperation(value = "删除荣誉成果", notes = "逻辑删除荣誉成果")
    public ResponseEntity<Result<Boolean>> remove(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "荣誉成果ID", required = true) @PathVariable Long id) {
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            boolean result = honorAchievementService.delete(id, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除荣誉成果失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 批量删除荣誉成果
     */
    @HttpMonitorLogger
    @PostMapping("/batch-delete")
    @ApiOperation(value = "批量删除荣誉成果", notes = "批量逻辑删除荣誉成果")
    public ResponseEntity<Result<Boolean>> batchDelete(
            @RequestHeader HttpHeaders headers,
            @RequestBody List<Long> ids) {
        
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = honorAchievementService.batchDelete(ids, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("批量删除荣誉成果失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据ID查询荣誉成果
     */
    @HttpMonitorLogger
    @GetMapping("/find-id/{id}")
    @ApiOperation(value = "查询荣誉成果详情", notes = "根据ID查询荣誉成果详情")
    public ResponseEntity<Result<HonorAchievementEntity>> findById(
            @ApiParam(value = "荣誉成果ID", required = true) @PathVariable Long id) {

        try {
            HonorAchievementEntity result = honorAchievementService.findByIdAndIncreaseView(id);
            if (result == null) {
                return new ResponseEntity<>(new Result<>(errors, 404,
                        HttpStatus.NOT_FOUND.value(), "荣誉成果不存在"), HttpStatus.NOT_FOUND);
            }
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("查询荣誉成果详情失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分页查询荣誉成果列表
     */
    @HttpMonitorLogger
    @GetMapping("/find-all")
    @ApiOperation(value = "分页查询荣誉成果列表", notes = "分页查询荣誉成果列表，支持多条件筛选")
    public ResponseEntity<Result<PageResult<HonorAchievementEntity>>> findAll(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pagesize,
            @ApiParam(value = "名称") @RequestParam(required = false) String name,
            @ApiParam(value = "类型") @RequestParam(required = false) Integer type,
            @ApiParam(value = "级别") @RequestParam(required = false) Integer level,
            @ApiParam(value = "颁发单位") @RequestParam(required = false) String issueOrg,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime) {
        
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> params = honorAchievementService.buildQueryParams(name, type, level, issueOrg, startTime, endTime);
            PageResult<HonorAchievementEntity> result = honorAchievementService.findByPage(page, pagesize, params, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("分页查询荣誉成果列表失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分页查询公开的荣誉成果列表（用于共享页面）
     */
    @HttpMonitorLogger
    @GetMapping("/find-public")
    @ApiOperation(value = "分页查询公开荣誉成果列表", notes = "分页查询公开的荣誉成果列表，用于共享展示")
    public ResponseEntity<Result<PageResult<HonorAchievementEntity>>> findPublic(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pagesize,
            @ApiParam(value = "名称") @RequestParam(required = false) String name,
            @ApiParam(value = "类型") @RequestParam(required = false) Integer type,
            @ApiParam(value = "级别") @RequestParam(required = false) Integer level,
            @ApiParam(value = "颁发单位") @RequestParam(required = false) String issueOrg,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime,
            @ApiParam(value = "排序方式") @RequestParam(required = false) String orderBy) {

        try {
            Map<String, Object> params = honorAchievementService.buildQueryParams(name, type, level, issueOrg, startTime, endTime);
            if (orderBy != null) {
                params.put("orderBy", orderBy);
            }
            PageResult<HonorAchievementEntity> result = honorAchievementService.findPublicByPage(page, pagesize, params);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("分页查询公开荣誉成果列表失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 增加下载次数
     */
    @HttpMonitorLogger
    @PostMapping("/download/{id}")
    @ApiOperation(value = "增加下载次数", notes = "记录荣誉成果下载次数")
    public ResponseEntity<Result<Boolean>> download(
            @ApiParam(value = "荣誉成果ID", required = true) @PathVariable Long id) {

        try {
            boolean result = honorAchievementService.increaseDownloadCount(id);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("增加下载次数失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 统计荣誉成果数据
     */
    @HttpMonitorLogger
    @GetMapping("/statistics")
    @ApiOperation(value = "统计荣誉成果数据", notes = "按类型和级别统计荣誉成果数据")
    public ResponseEntity<Result<Map<String, Object>>> statistics(
            @RequestHeader HttpHeaders headers) {
        
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("typeStats", honorAchievementService.countByType(sysHeader.getOid()));
            result.put("levelStats", honorAchievementService.countByLevel(sysHeader.getOid()));
            result.put("latest", honorAchievementService.findLatest(5, sysHeader.getOid()));
            result.put("popular", honorAchievementService.findPopular(5, sysHeader.getOid()));
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("统计荣誉成果数据失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 获取相关推荐
     */
    @HttpMonitorLogger
    @GetMapping("/recommendations/{id}")
    @ApiOperation(value = "获取相关推荐", notes = "根据荣誉成果获取相关推荐")
    public ResponseEntity<Result<List<HonorAchievementEntity>>> getRecommendations(
            @ApiParam(value = "荣誉成果ID", required = true) @PathVariable Long id,
            @ApiParam(value = "限制数量", defaultValue = "5") @RequestParam(defaultValue = "5") Integer limit) {

        try {
            List<HonorAchievementEntity> result = honorAchievementService.getRecommendations(id, limit);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取相关推荐失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取荣誉成果影响力分析
     */
    @HttpMonitorLogger
    @GetMapping("/influence/{id}")
    @ApiOperation(value = "获取荣誉成果影响力分析", notes = "分析荣誉成果的影响力数据")
    public ResponseEntity<Result<Map<String, Object>>> getInfluenceAnalysis(
            @ApiParam(value = "荣誉成果ID", required = true) @PathVariable Long id) {

        try {
            // 获取荣誉成果信息
            HonorAchievementEntity achievement = honorAchievementService.findById(id);
            if (achievement == null) {
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value(), "荣誉成果不存在"), HttpStatus.NOT_FOUND);
            }

            // 计算影响力指标
            Map<String, Object> influenceData = new HashMap<>();

            // 基础数据
            influenceData.put("viewCount", achievement.getViewCount() != null ? achievement.getViewCount() : 0);
            influenceData.put("downloadCount", achievement.getDownloadCount() != null ? achievement.getDownloadCount() : 0);

            // 影响力评分计算（简单算法）
            int viewCount = achievement.getViewCount() != null ? achievement.getViewCount() : 0;
            int downloadCount = achievement.getDownloadCount() != null ? achievement.getDownloadCount() : 0;
            int levelScore = getLevelScore(achievement.getLevel());
            int typeScore = getTypeScore(achievement.getType());

            // 影响力评分 = 查看次数 * 0.1 + 下载次数 * 0.5 + 级别分数 + 类型分数
            double influenceScore = viewCount * 0.1 + downloadCount * 0.5 + levelScore + typeScore;
            influenceData.put("influenceScore", Math.round(influenceScore * 100.0) / 100.0);

            // 影响力等级
            String influenceLevel = getInfluenceLevel(influenceScore);
            influenceData.put("influenceLevel", influenceLevel);

            // 排名信息（模拟数据，实际应该查询数据库）
            influenceData.put("ranking", calculateRanking(achievement));

            // 趋势数据（模拟数据，实际应该从访问日志中统计）
            influenceData.put("trends", generateTrendData(achievement));

            return new ResponseEntity<>(new Result<>(influenceData, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取影响力分析失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 计算排名信息
     */
    private Map<String, Object> calculateRanking(HonorAchievementEntity achievement) {
        Map<String, Object> ranking = new HashMap<>();
        // 这里应该查询数据库计算真实排名，现在使用模拟数据
        ranking.put("totalRank", Math.max(1, (int)(Math.random() * 100) + 1));
        ranking.put("typeRank", Math.max(1, (int)(Math.random() * 20) + 1));
        ranking.put("levelRank", Math.max(1, (int)(Math.random() * 30) + 1));
        return ranking;
    }

    /**
     * 生成趋势数据
     */
    private Map<String, Object> generateTrendData(HonorAchievementEntity achievement) {
        Map<String, Object> trends = new HashMap<>();
        // 这里应该从访问日志中统计真实数据，现在使用模拟数据
        trends.put("viewTrend", generateRandomTrend(7)); // 最近7天查看趋势
        trends.put("downloadTrend", generateRandomTrend(7)); // 最近7天下载趋势
        return trends;
    }

    /**
     * 获取级别分数
     */
    private int getLevelScore(Integer level) {
        if (level == null) return 0;
        return switch (level) {
            case 1 -> 50; // 国际级
            case 2 -> 40; // 国家级
            case 3 -> 30; // 省级
            case 4 -> 20; // 市级
            case 5 -> 10; // 区县级
            case 6 -> 5;  // 单位内部
            default -> 0;
        };
    }

    /**
     * 获取类型分数
     */
    private int getTypeScore(Integer type) {
        if (type == null) return 0;
        return switch (type) {
            case 1 -> 20; // 奖项
            case 2 -> 15; // 证书
            case 3 -> 25; // 专利
            case 4 -> 30; // 论文
            case 5 -> 10; // 其他
            default -> 0;
        };
    }

    /**
     * 获取影响力等级
     */
    private String getInfluenceLevel(double score) {
        if (score >= 100) return "极高";
        if (score >= 80) return "高";
        if (score >= 60) return "中等";
        if (score >= 40) return "较低";
        return "低";
    }

    /**
     * 生成随机趋势数据
     */
    private List<Integer> generateRandomTrend(int days) {
        List<Integer> trend = new java.util.ArrayList<>();
        for (int i = 0; i < days; i++) {
            trend.add((int)(Math.random() * 20));
        }
        return trend;
    }
}
