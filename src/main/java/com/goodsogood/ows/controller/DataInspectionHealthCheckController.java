package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.PageResult;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.*;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据体检健康检查控制器 - 基于 health-check.ts 接口需求实现
 * 严格按照前端API调用结构设计
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Slf4j
@RestController
@RequestMapping("/api/data-inspection")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(tags = "数据体检健康检查模块")
public class DataInspectionHealthCheckController {

    private final DataInspectionExceptionService exceptionService;
    private final DataInspectionUserPermissionService userPermissionService;
    private final DataInspectionRuleService ruleService;
    private final DataInspectionDataSourceService dataSourceService;
    private final DataInspectionScheduledTaskService scheduledTaskService;
    private final DataInspectionAutoConfigService autoConfigService;
    private final DataInspectionResultService resultService;
    private final DataInspectionSyncHistoryService syncHistoryService;
    private final DataInspectionTaskExecutionHistoryService executionHistoryService;
    private final DataInspectionTaskLogService taskLogService;
    private final RemediationRecordService remediationRecordService;
    private final Errors errors;

    @Autowired
    public DataInspectionHealthCheckController(
            DataInspectionExceptionService exceptionService,
            DataInspectionUserPermissionService userPermissionService,
            DataInspectionRuleService ruleService,
            DataInspectionDataSourceService dataSourceService,
            DataInspectionScheduledTaskService scheduledTaskService,
            DataInspectionAutoConfigService autoConfigService,
            DataInspectionResultService resultService,
            DataInspectionSyncHistoryService syncHistoryService,
            DataInspectionTaskExecutionHistoryService executionHistoryService,
            DataInspectionTaskLogService taskLogService,
            RemediationRecordService remediationRecordService,
            Errors errors) {
        this.exceptionService = exceptionService;
        this.userPermissionService = userPermissionService;
        this.ruleService = ruleService;
        this.dataSourceService = dataSourceService;
        this.scheduledTaskService = scheduledTaskService;
        this.autoConfigService = autoConfigService;
        this.resultService = resultService;
        this.syncHistoryService = syncHistoryService;
        this.executionHistoryService = executionHistoryService;
        this.taskLogService = taskLogService;
        this.remediationRecordService = remediationRecordService;
        this.errors = errors;
    }

    // ================================
    // 用户权限管理 API - 对应 getUserPermissions()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/permissions")
    @ApiOperation("获取用户权限")
    public ResponseEntity<Result<?>> getUserPermissions(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (userId == null) {
                throw new ApiException("登录失效", new Result<>(errors, 9905, HttpStatus.UNAUTHORIZED.value()));
            }

            DataInspectionUserPermissionEntity permission = userPermissionService.getByUserId(userId);
            if (permission == null) {
                // 如果用户权限不存在，创建默认权限
                permission = userPermissionService.createDefaultPermission(userId);
            }

            Map<String, Boolean> permissions = new HashMap<>();
            permissions.put("canViewHealthCheck", permission.getCanViewInspection());
            permissions.put("canManageRules", permission.getCanConfigureRules());
            permissions.put("canExecuteCheck", permission.getCanHandleExceptions());
            permissions.put("canViewExceptions", permission.getCanViewInspection());
            permissions.put("canFixExceptions", permission.getCanHandleExceptions());

            return new ResponseEntity<>(new Result<>(permissions, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取用户权限失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 异常概览统计 API - 对应 getExceptionOverview()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/exceptions/overview")
    @ApiOperation("获取异常概览统计")
    public ResponseEntity<Result<?>> getExceptionOverview(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canViewInspection(userId)) {
                throw new ApiException("无权限查看体检结果", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            // 获取异常总数
            long totalExceptions = exceptionService.countTotal();

            // 按类型统计
            List<Map<String, Object>> byType = exceptionService.countByType();

            // 按单位统计
            List<Map<String, Object>> byUnit = exceptionService.countByUnit();

            // 按严重程度统计
            List<Map<String, Object>> severityDistribution = exceptionService.countBySeverity();

            Map<String, Object> overview = new HashMap<>();
            overview.put("totalExceptions", totalExceptions);
            overview.put("byType", byType);
            overview.put("byUnit", byUnit);
            overview.put("severityDistribution", severityDistribution);

            return new ResponseEntity<>(new Result<>(overview, errors), HttpStatus.OK);
        } catch (Exception e) {
            throw new ApiException("获取异常概览失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 体检规则管理 API - 对应 getPartyOrganizationRules()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/rules/party-organization")
    @ApiOperation("获取党组织设置体检规则")
    public ResponseEntity<Result<?>> getPartyOrganizationRules(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            List<DataInspectionRuleEntity> rules = ruleService.getRulesByCategory("partyOrganization");
            List<Map<String, Object>> result = rules.stream().map(this::convertRuleToMap).collect(Collectors.toList());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取党组织规则失败", e);
            throw new ApiException("获取党组织规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/rules/party-organization")
    @ApiOperation("设置党组织体检规则")
    public ResponseEntity<Result<?>> setPartyOrganizationRules(
            @RequestBody @Valid List<Map<String, Object>> rulesData,
            BindingResult result,
            @RequestHeader HttpHeaders headers) {
        try {
            if (result.hasErrors()) {
                throw new ApiException("参数验证失败", new Result<>(errors, 9901, HttpStatus.BAD_REQUEST.value()));
            }

            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            ruleService.batchUpdateRules("partyOrganization", rulesData, userId);
            return new ResponseEntity<>(new Result<>("规则更新成功", errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("设置党组织规则失败", e);
            throw new ApiException("设置党组织规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 体检执行 API - 对应 triggerHealthCheck()
    // ================================

    @HttpMonitorLogger
    @PostMapping("/health-check/execute")
    @ApiOperation("手动触发体检")
    public ResponseEntity<Result<?>> triggerHealthCheck(
            @RequestBody Map<String, Object> params,
            @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限执行体检", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            Long taskId = params.get("taskId") != null ? ((Number) params.get("taskId")).longValue() : null;
            @SuppressWarnings("unchecked")
            List<Integer> checkTypes = (List<Integer>) params.getOrDefault("checkTypes", Arrays.asList(1, 2, 3, 4));

            // 创建体检结果记录
            DataInspectionResultEntity inspectionResult = resultService.createInspectionTask(taskId, checkTypes, userId);

            // 执行体检逻辑
            List<DataInspectionExceptionEntity> exceptions = exceptionService.executeHealthCheck(checkTypes, inspectionResult.getId());

            // 更新体检结果
            resultService.updateInspectionResult(inspectionResult.getId(), exceptions.size());

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", inspectionResult.getId());
            response.put("exceptions", exceptions.stream().map(this::convertExceptionToMap).collect(Collectors.toList()));

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("执行体检失败", e);
            throw new ApiException("执行体检失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 异常处理 API - 对应 getExceptionList()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/exceptions")
    @ApiOperation("获取异常项列表")
    public ResponseEntity<Result<?>> getExceptionList(
            @RequestParam(required = false) @ApiParam("异常类型") String exceptionType,
            @RequestParam(required = false) @ApiParam("状态") String status,
            @RequestParam(required = false) @ApiParam("影响对象") String affectedObject,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer pageNum,
            @RequestParam(defaultValue = "20") @ApiParam("每页数量") Integer pageSize,
            @RequestHeader  HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canViewInspection(userId)) {
                throw new ApiException("无权限查看异常", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            PageResult<DataInspectionExceptionEntity> pageResult = exceptionService.getExceptionList(
                    exceptionType, status, affectedObject, pageNum, pageSize);

            Map<String, Object> response = new HashMap<>();
            response.put("data", pageResult.getList().stream().map(this::convertExceptionToMap).collect(Collectors.toList()));
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取异常列表失败", e);
            throw new ApiException("获取异常列表失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 党务干部任免体检规则 API - 对应 getPartyStaffRules()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/rules/party-staff")
    @ApiOperation("获取党务干部任免体检规则")
    public ResponseEntity<Result<?>> getPartyStaffRules(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            List<DataInspectionRuleEntity> rules = ruleService.getRulesByCategory("partyStaff");
            List<Map<String, Object>> result = rules.stream().map(this::convertRuleToMap).collect(Collectors.toList());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取党务干部规则失败", e);
            throw new ApiException("获取党务干部规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/rules/party-staff")
    @ApiOperation("设置党务干部任免体检规则")
    public ResponseEntity<Result<?>> setPartyStaffRules(
            @RequestBody @Valid List<Map<String, Object>> rulesData,
            BindingResult result,
            @RequestHeader HttpHeaders headers) {
        try {
            if (result.hasErrors()) {
                throw new ApiException("参数验证失败", new Result<>(errors, 9901, HttpStatus.BAD_REQUEST.value()));
            }

            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            ruleService.batchUpdateRules("partyStaff", rulesData, userId);
            return new ResponseEntity<>(new Result<>("规则更新成功", errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("设置党务干部规则失败", e);
            throw new ApiException("设置党务干部规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 任务体检规则 API - 对应 getTaskCheckRules()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/rules/task-check")
    @ApiOperation("获取任务体检规则")
    public ResponseEntity<Result<?>> getTaskCheckRules(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            List<DataInspectionRuleEntity> rules = ruleService.getRulesByCategory("taskCheck");
            List<Map<String, Object>> result = rules.stream().map(this::convertRuleToMap).collect(Collectors.toList());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取任务体检规则失败", e);
            throw new ApiException("获取任务体检规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 用户信息完整性规则 API - 对应 getUserInfoRules()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/rules/user-info")
    @ApiOperation("获取用户信息完整性体检规则")
    public ResponseEntity<Result<?>> getUserInfoRules(@RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置规则", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            List<DataInspectionRuleEntity> rules = ruleService.getRulesByCategory("userInfo");
            List<Map<String, Object>> result = rules.stream().map(this::convertRuleToMap).collect(Collectors.toList());

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取用户信息规则失败", e);
            throw new ApiException("获取用户信息规则失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 定时体检设置 API - 对应 setScheduledCheck()
    // ================================

    @HttpMonitorLogger
    @PostMapping("/scheduled-check")
    @ApiOperation("设置定时体检")
    public ResponseEntity<Result<?>> setScheduledCheck(
            @RequestBody Map<String, Object> schedule,
            @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置定时任务", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            String name = (String) schedule.get("name");
            @SuppressWarnings("unchecked")
            List<Integer> checkTypes = (List<Integer>) schedule.get("checkTypes");
            String cronExpression = (String) schedule.get("cronExpression");
            Boolean isEnabled = (Boolean) schedule.getOrDefault("isEnabled", true);
            @SuppressWarnings("unchecked")
            Map<String, Object> notificationSettings = (Map<String, Object>) schedule.get("notificationSettings");

            DataInspectionScheduledTaskEntity task = scheduledTaskService.createScheduledTask(
                    name, checkTypes, cronExpression, isEnabled, notificationSettings, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("id", task.getId());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("设置定时体检失败", e);
            throw new ApiException("设置定时体检失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 全面数据检测 API - 对应 executeComprehensiveCheck()
    // ================================

    @HttpMonitorLogger
    @PostMapping("/comprehensive-check")
    @ApiOperation("执行全面数据检测")
    public ResponseEntity<Result<?>> executeComprehensiveCheck(
            @RequestBody Map<String, Object> config,
            @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
            Long userId = sysHeader.getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限执行检测", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            @SuppressWarnings("unchecked")
            List<Integer> checkTypes = (List<Integer>) config.get("checkTypes");
            String dataScope = (String) config.getOrDefault("dataScope", "all");
            @SuppressWarnings("unchecked")
            List<String> dateRange = (List<String>) config.get("dateRange");
            @SuppressWarnings("unchecked")
            List<String> targetObjects = (List<String>) config.get("targetObjects");
            @SuppressWarnings("unchecked")
            Map<String, Boolean> checkDimensions = (Map<String, Boolean>) config.get("checkDimensions");

            Long taskId = autoConfigService.executeComprehensiveCheck(
                    checkTypes, dataScope, dateRange, targetObjects, checkDimensions, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("执行全面检测失败", e);
            throw new ApiException("执行全面检测失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 任务状态查询 API - 对应 getTaskStatus()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/task/{taskId}/status")
    @ApiOperation("获取任务执行状态")
    public ResponseEntity<Result<?>> getTaskStatus(
            @PathVariable @ApiParam("任务ID") Long taskId,
            @RequestHeader HttpHeaders headers) {
        try {
            DataInspectionTaskExecutionHistoryEntity execution = executionHistoryService.getByTaskId(taskId);
            if (execution == null) {
                throw new ApiException("任务不存在", new Result<>(errors, 9901, HttpStatus.NOT_FOUND.value()));
            }

            List<DataInspectionTaskLogEntity> logs = taskLogService.getRecentLogs(execution.getExecutionId(), 50);

            Map<String, Object> status = new HashMap<>();
            status.put("status", execution.getStatus());
            status.put("startTime", execution.getStartTime());
            status.put("endTime", execution.getEndTime());
            status.put("resultSummary", execution.getResultSummary());
            status.put("errorMessage", execution.getErrorMessage());
            status.put("logs", logs.stream().map(log -> {
                Map<String, Object> logMap = new HashMap<>();
                logMap.put("time", log.getLogTime());
                logMap.put("level", log.getLogLevel().toLowerCase());
                logMap.put("message", log.getLogMessage());
                return logMap;
            }).collect(Collectors.toList()));

            return new ResponseEntity<>(new Result<>(status, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取任务状态失败", e);
            throw new ApiException("获取任务状态失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 异常详情 API - 对应 getExceptionDetail()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/exceptions/{id}")
    @ApiOperation("获取异常详情")
    public ResponseEntity<Result<?>> getExceptionDetail(
            @PathVariable @ApiParam("异常ID") Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canViewInspection(userId)) {
                throw new ApiException("无权限查看异常", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            DataInspectionExceptionEntity exception = exceptionService.getById(id);
            if (exception == null) {
                throw new ApiException("异常记录不存在", new Result<>(errors, 9901, HttpStatus.NOT_FOUND.value()));
            }

            Map<String, Object> result = convertExceptionToMap(exception);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取异常详情失败", e);
            throw new ApiException("获取异常详情失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 异常整改 API - 对应 startFixing(), recordFixResult(), jumpToSource(), recheck()
    // ================================

    @HttpMonitorLogger
    @PostMapping("/exceptions/{id}/start-fixing")
    @ApiOperation("开始整改异常项")
    public ResponseEntity<Result<?>> startFixing(
            @PathVariable @ApiParam("异常ID") Long id,
            @RequestBody Map<String, Object> fixPlan,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限处理异常", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            String description = (String) fixPlan.get("description");
            String estimatedTime = (String) fixPlan.get("estimatedTime");
            @SuppressWarnings("unchecked")
            List<String> resources = (List<String>) fixPlan.get("resources");
            String assignee = (String) fixPlan.get("assignee");

            boolean success = remediationRecordService.startRemediation(id, description, estimatedTime, resources, assignee, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("开始整改异常失败", e);
            throw new ApiException("开始整改异常失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/exceptions/{id}/record-fix")
    @ApiOperation("记录整改结果")
    public ResponseEntity<Result<?>> recordFixResult(
            @PathVariable @ApiParam("异常ID") Long id,
            @RequestBody Map<String, Object> fixResult,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限处理异常", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            Integer status = (Integer) fixResult.get("status");
            String description = (String) fixResult.get("description");
            String actualTime = (String) fixResult.get("actualTime");
            @SuppressWarnings("unchecked")
            List<String> attachments = (List<String>) fixResult.get("attachments");
            String nextSteps = (String) fixResult.get("nextSteps");

            boolean success = remediationRecordService.recordResult(id, status, description, actualTime, attachments, nextSteps, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("记录整改结果失败", e);
            throw new ApiException("记录整改结果失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @GetMapping("/exceptions/{id}/jump-source")
    @ApiOperation("一键跳转至问题源头")
    public ResponseEntity<Result<?>> jumpToSource(
            @PathVariable @ApiParam("异常ID") Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            DataInspectionExceptionEntity exception = exceptionService.getById(id);
            if (exception == null) {
                throw new ApiException("异常记录不存在", new Result<>(errors, 9901, HttpStatus.NOT_FOUND.value()));
            }

            Map<String, Object> source = new HashMap<>();
            source.put("sourceType", "data");
            source.put("sourceUrl", "/organization-management");
            Map<String, Object> sourceParams = new HashMap<>();
            sourceParams.put("orgId", exception.getUnit());
            sourceParams.put("tab", "basic-info");
            source.put("sourceParams", sourceParams);
            source.put("instructions", "请在组织管理页面补充相关信息");

            return new ResponseEntity<>(new Result<>(source, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取问题源头失败", e);
            throw new ApiException("获取问题源头失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/exceptions/{id}/recheck")
    @ApiOperation("整改后复检")
    public ResponseEntity<Result<?>> recheck(
            @PathVariable @ApiParam("异常ID") Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限执行复检", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            Long recheckTaskId = exceptionService.executeRecheck(id, userId);

            Map<String, Object> result = new HashMap<>();
            result.put("taskId", recheckTaskId);
            result.put("expectedDuration", 300); // 5分钟

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("执行复检失败", e);
            throw new ApiException("执行复检失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 数据源管理 API - 对应 getDataSourceList()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/data-sources")
    @ApiOperation("获取数据源列表")
    public ResponseEntity<Result<?>> getDataSourceList(
            @RequestParam(required = false) String sourceName,
            @RequestParam(required = false) String sourceType,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestHeader HttpHeaders headers) {
        try {
            PageResult<DataInspectionDataSourceEntity> pageResult = dataSourceService.getDataSourceList(
                    sourceName, sourceType, status, pageNum, pageSize);

            List<Map<String, Object>> dataList = pageResult.getList().stream()
                    .map(this::convertDataSourceToMap)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("data", dataList);
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取数据源列表失败", e);
            throw new ApiException("获取数据源列表失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/data-sources")
    @ApiOperation("创建数据源配置")
    public ResponseEntity<Result<?>> createDataSource(
            @RequestBody Map<String, Object> dataSource,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置数据源", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            String sourceId = dataSourceService.createDataSource(dataSource, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("id", sourceId);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("创建数据源失败", e);
            throw new ApiException("创建数据源失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PutMapping("/data-sources/{id}")
    @ApiOperation("更新数据源配置")
    public ResponseEntity<Result<?>> updateDataSource(
            @PathVariable String id,
            @RequestBody Map<String, Object> dataSource,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置数据源", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            boolean success = dataSourceService.updateDataSource(id, dataSource, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("更新数据源失败", e);
            throw new ApiException("更新数据源失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @DeleteMapping("/data-sources/{id}")
    @ApiOperation("删除数据源配置")
    public ResponseEntity<Result<?>> deleteDataSource(
            @PathVariable String id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限配置数据源", new Result<>(errors, 9901, HttpStatus.FORBIDDEN.value()));
            }

            boolean success = dataSourceService.deleteDataSource(id, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除数据源失败", e);
            throw new ApiException("删除数据源失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/data-sources/{id}/test-connection")
    @ApiOperation("测试数据源连接")
    public ResponseEntity<Result<?>> testDataSourceConnection(
            @PathVariable String id,
            @RequestHeader HttpHeaders headers) {
        try {
            Map<String, Object> testResult = dataSourceService.testConnection(id);
            return new ResponseEntity<>(new Result<>(testResult, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("测试数据源连接失败", e);
            Map<String, Object> failResult = new HashMap<>();
            failResult.put("success", false);
            failResult.put("message", "连接测试失败: " + e.getMessage());
            return new ResponseEntity<>(new Result<>(failResult, errors), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @PostMapping("/data-sources/{id}/sync")
    @ApiOperation("启动数据源同步")
    public ResponseEntity<Result<?>> startDataSourceSync(
            @PathVariable String id,
            @RequestBody(required = false) Map<String, Object> options,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            Map<String, Object> syncResult = dataSourceService.startSync(id, options, userId);
            return new ResponseEntity<>(new Result<>(syncResult, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("启动数据源同步失败", e);
            throw new ApiException("启动数据源同步失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @GetMapping("/data-sources/{id}/sync-status")
    @ApiOperation("获取数据源同步状态")
    public ResponseEntity<Result<?>> getDataSourceSyncStatus(
            @PathVariable String id,
            @RequestHeader HttpHeaders headers) {
        try {
            Map<String, Object> syncStatus = syncHistoryService.getSyncStatus(id);
            return new ResponseEntity<>(new Result<>(syncStatus, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            throw new ApiException("获取同步状态失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/data-sources/{id}/stop-sync")
    @ApiOperation("停止数据源同步")
    public ResponseEntity<Result<?>> stopDataSourceSync(
            @PathVariable String id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            boolean success = dataSourceService.stopSync(id, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("停止同步失败", e);
            throw new ApiException("停止同步失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @GetMapping("/data-sources/{id}/sync-history")
    @ApiOperation("获取数据源同步历史")
    public ResponseEntity<Result<?>> getDataSourceSyncHistory(
            @PathVariable String id,
            @RequestParam(required = false) List<String> dateRange,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestHeader HttpHeaders headers) {
        try {
            PageResult<DataInspectionSyncHistoryEntity> pageResult = syncHistoryService.getSyncHistory(
                    id, dateRange, status, pageNum, pageSize);

            List<Map<String, Object>> dataList = pageResult.getList().stream()
                    .map(this::convertSyncHistoryToMap)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("data", dataList);
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取同步历史失败", e);
            throw new ApiException("获取同步历史失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 体检记录管理 API - 对应 fetchHealthCheckList(), createHealthCheck(), updateHealthCheck(), deleteHealthCheck()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/health-check/list")
    @ApiOperation("获取体检记录列表")
    public ResponseEntity<Result<?>> fetchHealthCheckList(
            @RequestParam(required = false, value = "checkName") String checkName,
            @RequestParam(required = false, value = "checkType") Integer checkType,
            @RequestParam(required = false, value = "status") Integer status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        try {
            PageResult<DataInspectionResultEntity> pageResult = resultService.getHealthCheckList(
                    checkName, checkType, status, page, pageSize);

            List<Map<String, Object>> dataList = pageResult.getList().stream()
                    .map(this::convertHealthCheckRecordToMap)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("data", dataList);
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取体检记录列表失败", e);
            throw new ApiException("获取体检记录列表失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/health-check")
    @ApiOperation("创建体检记录")
    public ResponseEntity<Result<?>> createHealthCheck(
            @RequestBody Map<String, Object> data,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限创建体检记录", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            Long id = resultService.createHealthCheck(data, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("id", id);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("创建体检记录失败", e);
            throw new ApiException("创建体检记录失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PutMapping("/health-check/{id}")
    @ApiOperation("更新体检记录")
    public ResponseEntity<Result<?>> updateHealthCheck(
            @PathVariable Long id,
            @RequestBody Map<String, Object> data,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限更新体检记录", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            boolean success = resultService.updateHealthCheck(id, data, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("更新体检记录失败", e);
            throw new ApiException("更新体检记录失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @DeleteMapping("/health-check/{id}")
    @ApiOperation("删除体检记录")
    public ResponseEntity<Result<?>> deleteHealthCheck(
            @PathVariable Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限删除体检记录", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            boolean success = resultService.deleteResult(id);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除体检记录失败", e);
            throw new ApiException("删除体检记录失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 批量执行和导出 API - 对应 batchExecuteHealthCheck(), exportHealthCheckResults()
    // ================================

    @HttpMonitorLogger
    @PostMapping("/health-check/batch-execute")
    @ApiOperation("批量执行体检")
    public ResponseEntity<Result<?>> batchExecuteHealthCheck(
            @RequestBody Map<String, Object> params,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限执行批量体检", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            @SuppressWarnings("unchecked")
            List<Integer> checkTypes = (List<Integer>) params.get("checkTypes");
            @SuppressWarnings("unchecked")
            Map<String, Object> config = (Map<String, Object>) params.get("config");

            Long batchId = autoConfigService.batchExecuteHealthCheck(checkTypes, config, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("batchId", batchId);
            response.put("totalExceptions", 0); // 异步执行，暂时返回0
            response.put("summary", "批量体检已启动");

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("批量执行体检失败", e);
            throw new ApiException("批量执行体检失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/health-check/export")
    @ApiOperation("导出体检结果")
    public ResponseEntity<Result<?>> exportHealthCheckResults(
            @RequestBody Map<String, Object> params,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canViewInspection(userId)) {
                throw new ApiException("无权限导出报告", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            String format = (String) params.getOrDefault("format", "excel");
            @SuppressWarnings("unchecked")
            Map<String, Object> filters = (Map<String, Object>) params.get("filters");

            Map<String, Object> exportResult = resultService.exportResults(format, filters, userId);

            return new ResponseEntity<>(new Result<>(exportResult, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("导出体检结果失败", e);
            throw new ApiException("导出体检结果失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 定时任务管理 API - 对应 getScheduledTaskList(), createScheduledTask(), updateScheduledTask(), deleteScheduledTask()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/scheduled-tasks")
    @ApiOperation("获取定时任务列表")
    public ResponseEntity<Result<?>> getScheduledTaskList(
            @RequestParam(required = false) String taskName,
            @RequestParam(required = false) Integer taskType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Boolean isEnabled,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestHeader HttpHeaders headers) {
        try {
            PageResult<DataInspectionScheduledTaskEntity> pageResult = scheduledTaskService.getTaskList(
                    taskName, taskType, status, isEnabled, pageNum, pageSize);

            List<Map<String, Object>> dataList = pageResult.getList().stream()
                    .map(this::convertScheduledTaskToMap)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("data", dataList);
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取定时任务列表失败", e);
            throw new ApiException("获取定时任务列表失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/scheduled-tasks")
    @ApiOperation("创建定时任务")
    public ResponseEntity<Result<?>> createScheduledTask(
            @RequestBody Map<String, Object> task,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限创建定时任务", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            Long id = scheduledTaskService.createTask(task, userId);

            Map<String, Object> response = new HashMap<>();
            response.put("id", id);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("创建定时任务失败", e);
            throw new ApiException("创建定时任务失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PutMapping("/scheduled-tasks/{id}")
    @ApiOperation("更新定时任务")
    public ResponseEntity<Result<?>> updateScheduledTask(
            @PathVariable Long id,
            @RequestBody Map<String, Object> task,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限更新定时任务", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            boolean success = scheduledTaskService.updateTask(id, task, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("更新定时任务失败", e);
            throw new ApiException("更新定时任务失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @DeleteMapping("/scheduled-tasks/{id}")
    @ApiOperation("删除定时任务")
    public ResponseEntity<Result<?>> deleteScheduledTask(
            @PathVariable Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限删除定时任务", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            boolean success = scheduledTaskService.deleteTask(id, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除定时任务失败", e);
            throw new ApiException("删除定时任务失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PutMapping("/scheduled-tasks/{id}/toggle")
    @ApiOperation("启用/禁用定时任务")
    public ResponseEntity<Result<?>> toggleScheduledTask(
            @PathVariable Long id,
            @RequestBody Map<String, Object> params,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canConfigureRules(userId)) {
                throw new ApiException("无权限操作定时任务", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            Boolean enabled = (Boolean) params.get("enabled");
            boolean success = scheduledTaskService.toggleTask(id, enabled, userId);
            return new ResponseEntity<>(new Result<>(success, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("切换定时任务状态失败", e);
            throw new ApiException("切换定时任务状态失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @PostMapping("/scheduled-tasks/{id}/execute")
    @ApiOperation("立即执行定时任务")
    public ResponseEntity<Result<?>> executeScheduledTaskImmediately(
            @PathVariable Long id,
            @RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canHandleExceptions(userId)) {
                throw new ApiException("无权限执行任务", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            Map<String, Object> result = scheduledTaskService.executeImmediately(id, userId);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("立即执行定时任务失败", e);
            throw new ApiException("立即执行定时任务失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @GetMapping("/scheduled-tasks/{taskId}/history")
    @ApiOperation("获取任务执行历史")
    public ResponseEntity<Result<?>> getTaskExecutionHistory(
            @PathVariable Long taskId,
            @RequestParam(required = false) List<String> dateRange,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestHeader HttpHeaders headers) {
        try {
            PageResult<DataInspectionTaskExecutionHistoryEntity> pageResult = executionHistoryService.getHistoryByTaskId(
                    taskId, dateRange, status, pageNum, pageSize);

            List<Map<String, Object>> dataList = pageResult.getList().stream()
                    .map(this::convertExecutionHistoryToMap)
                    .collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("data", dataList);
            response.put("total", pageResult.getTotal());

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取任务执行历史失败", e);
            throw new ApiException("获取任务执行历史失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    @HttpMonitorLogger
    @GetMapping("/executions/{executionId}/logs")
    @ApiOperation("获取任务执行日志")
    public ResponseEntity<Result<?>> getTaskExecutionLogs(
            @PathVariable String executionId,
            @RequestHeader HttpHeaders headers) {
        try {
            List<DataInspectionTaskLogEntity> logs = taskLogService.getAllLogs(executionId);

            List<Map<String, Object>> logList = logs.stream().map(log -> {
                Map<String, Object> logMap = new HashMap<>();
                logMap.put("time", log.getLogTime());
                logMap.put("level", log.getLogLevel().toLowerCase());
                logMap.put("message", log.getLogMessage());
                Map<String, Object> details = new HashMap<>();
                details.put("threadName", log.getThreadName());
                details.put("className", log.getClassName());
                logMap.put("details", details);
                return logMap;
            }).collect(Collectors.toList());

            Map<String, Object> response = new HashMap<>();
            response.put("logs", logList);

            return new ResponseEntity<>(new Result<>(response, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取任务执行日志失败", e);
            throw new ApiException("获取任务执行日志失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 统计数据 API - 对应 fetchHealthCheckStatistics()
    // ================================

    @HttpMonitorLogger
    @GetMapping("/dashboard/statistics")
    @ApiOperation("获取统计数据")
    public ResponseEntity<Result<?>> getStatistics(@RequestHeader HttpHeaders headers) {
        try {
            Long userId = SysHeaderHelper.getSysHeader(headers, errors).getUserId();
            if (!userPermissionService.canViewInspection(userId)) {
                throw new ApiException("无权限查看统计", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }

            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalChecks", resultService.getTotalChecks());
            statistics.put("completedChecks", resultService.getCompletedChecks());
            statistics.put("failedChecks", resultService.getFailedChecks());
            statistics.put("totalExceptions", exceptionService.countTotal());
            statistics.put("highLevelExceptions", exceptionService.countByLevel("high"));
            statistics.put("fixedExceptions", exceptionService.countByStatus("resolved"));
            statistics.put("checkTypeStats", exceptionService.getCheckTypeStats());
            statistics.put("exceptionTrend", exceptionService.getExceptionTrend());

            return new ResponseEntity<>(new Result<>(statistics, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            throw new ApiException("获取统计数据失败", new Result<>(errors, 9901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    // ================================
    // 辅助方法
    // ================================

    private Map<String, Object> convertSyncHistoryToMap(DataInspectionSyncHistoryEntity history) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", history.getId());
        map.put("dataSourceId", history.getDataSourceId());
        map.put("syncType", history.getSyncType());
        map.put("syncStatus", history.getSyncStatus());
        map.put("startTime", history.getStartTime());
        map.put("endTime", history.getEndTime());
        map.put("durationSeconds", history.getDurationSeconds());
        map.put("syncedRecords", history.getSyncRecords());
        map.put("errorMessage", history.getErrorMessage());
        map.put("operator", history.getOperator());
        map.put("createTime", history.getCreateTime());
        return map;
    }

    private Map<String, Object> convertHealthCheckRecordToMap(DataInspectionResultEntity record) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", record.getId());
        map.put("inspectionType", record.getInspectionType());
        map.put("status", record.getStatus());
        map.put("totalRecords", record.getTotalRecords());
        map.put("exceptionCount", record.getExceptionCount());
        map.put("exceptionRate", record.getExceptionRate());
        map.put("inspectionDate", record.getInspectionDate());
        map.put("durationSeconds", record.getDurationSeconds());
        map.put("inspectionName", record.getInspectionName());
        map.put("summary", record.getSummary());
        map.put("createTime", record.getCreateTime());
        return map;
    }

    private Map<String, Object> convertScheduledTaskToMap(DataInspectionScheduledTaskEntity task) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", task.getId());
        map.put("taskName", task.getTaskName());
        map.put("taskType", task.getTaskType());
        map.put("checkTypes", task.getCheckTypes());
        map.put("cronExpression", task.getCronExpression());
        map.put("cronDescription", task.getCronDescription());
        map.put("isEnabled", task.getIsEnabled());
        map.put("status", task.getStatus());
        map.put("lastExecuteTime", task.getLastExecuteTime());
        map.put("nextExecuteTime", task.getNextExecuteTime());
        map.put("executionCount", task.getExecutionCount());
        map.put("successCount", task.getSuccessCount());
        map.put("failedCount", task.getFailedCount());
        map.put("avgDurationSeconds", task.getAvgDurationSeconds());
        map.put("createTime", task.getCreateTime());
        map.put("updateTime", task.getUpdateTime());
        map.put("creator", task.getCreator());
        return map;
    }

    private Map<String, Object> convertExecutionHistoryToMap(DataInspectionTaskExecutionHistoryEntity execution) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", execution.getId());
        map.put("taskId", execution.getTaskId());
        map.put("executionId", execution.getExecutionId());
        map.put("startTime", execution.getStartTime());
        map.put("endTime", execution.getEndTime());
        map.put("status", execution.getStatus());
        map.put("durationSeconds", execution.getDurationSeconds());
        map.put("totalRecords", execution.getTotalRecords());
        map.put("exceptionCount", execution.getExceptionCount());
        map.put("exceptionRate", execution.getExceptionRate());
        map.put("resultSummary", execution.getResultSummary());
        map.put("errorMessage", execution.getErrorMessage());
        map.put("createTime", execution.getCreateTime());
        return map;
    }

    private Map<String, Object> convertRuleToMap(DataInspectionRuleEntity rule) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", rule.getId());
        map.put("ruleName", rule.getName());
        map.put("ruleCategory", rule.getRuleCategory());
        map.put("description", rule.getDescription());
        map.put("status", rule.getStatus());
        map.put("createTime", rule.getCreateTime());
        map.put("updateTime", rule.getUpdateTime());
        return map;
    }

    private Map<String, Object> convertExceptionToMap(DataInspectionExceptionEntity exception) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", exception.getId());
        map.put("type", exception.getType());
        map.put("title", exception.getTitle());
        map.put("description", exception.getDescription());
        map.put("unit", exception.getUnit());
        map.put("source", exception.getSource());
        map.put("severity", exception.getSeverity());
        map.put("status", exception.getStatus());
        map.put("detectTime", exception.getDetectTime());
        map.put("affectedRecords", exception.getAffectedRecords());
        map.put("impact", exception.getImpact());
        map.put("solution", exception.getSolution());
        map.put("handleTime", exception.getHandleTime());
        map.put("handler", exception.getHandler());
        return map;
    }

    private Map<String, Object> convertDataSourceToMap(DataInspectionDataSourceEntity dataSource) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", dataSource.getSourceId());
        map.put("sourceName", dataSource.getSourceName());
        map.put("sourceType", dataSource.getSourceType());
        map.put("connectionUrl", dataSource.getConnectionUrl());
        map.put("status", dataSource.getStatus());
        map.put("lastSyncTime", dataSource.getLastSyncTime());
        map.put("syncStatus", dataSource.getSyncStatus());
        map.put("description", dataSource.getDescription());
        map.put("createTime", dataSource.getCreateTime());
        map.put("updateTime", dataSource.getUpdateTime());
        return map;
    }
}