package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.DataInspectionRuleConfigMapper;
import com.goodsogood.ows.model.vo.DataInspectionRuleVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.DataInspectionRuleConfigService;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 数据体检规则配置Controller
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Api(tags = "数据体检规则配置管理")
@Slf4j
@RestController
@RequestMapping("/api/health-check/rules")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class DataInspectionRuleConfigController {

    @Resource
    private DataInspectionRuleConfigService ruleConfigService;

    @Resource
    private Errors errors;

    /**
     * 分页查询规则列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "分页查询规则列表", notes = "支持按规则名称、类型、启用状态等条件查询")
    @GetMapping
    public ResponseEntity<Result<?>> getRuleList(
            @RequestHeader HttpHeaders headers,
            @Valid DataInspectionRuleVO.RuleSearchVO searchVO,
            BindingResult bindingResult) {

//        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            List<DataInspectionRuleVO.HealthCheckRuleVO> result =
                    ruleConfigService.getRuleList(searchVO);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】分页查询规则列表失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据ID查询规则详情
     */
    @HttpMonitorLogger
    @ApiOperation(value = "根据ID查询规则详情", notes = "获取单个规则的完整信息")
    @GetMapping("/{id}")
    public ResponseEntity<Result<DataInspectionRuleVO.HealthCheckRuleVO>> getRuleById(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则ID", required = true) @PathVariable Long id) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            DataInspectionRuleVO.HealthCheckRuleVO rule = ruleConfigService.getRuleById(id);

            if (rule != null) {
                log.info("【规则配置】查询规则详情成功，规则名称: {}", rule.getRuleName());
                return new ResponseEntity<>(new Result<>(rule, errors), HttpStatus.OK);
            } else {
                log.warn("【规则配置】未找到规则，ID: {}", id);
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value(), "规则不存在"), HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("【规则配置】查询规则详情失败，ID: {}", id, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据类型查询规则列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "根据类型查询规则列表", notes = "获取指定类型的所有规则")
    @GetMapping("/type/{ruleType}")
    public ResponseEntity<Result<List<DataInspectionRuleVO.HealthCheckRuleVO>>> getRulesByType(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则类型：1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整", required = true)
            @PathVariable Integer ruleType) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            List<DataInspectionRuleVO.HealthCheckRuleVO> rules = ruleConfigService.getRulesByType(ruleType);

            log.info("【规则配置】查询类型{}的规则成功，数量: {}", ruleType, rules.size());

            return new ResponseEntity<>(new Result<>(rules, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】根据类型查询规则列表失败，类型: {}", ruleType, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 创建规则
     */
    @HttpMonitorLogger
    @ApiOperation(value = "创建规则", notes = "创建新的体检规则")
    @PostMapping
    public ResponseEntity<Result<Long>> createRule(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则创建信息", required = true)
            @Valid @RequestBody DataInspectionRuleVO.RuleCreateVO createVO,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            Long ruleId = ruleConfigService.createRule(createVO);

            log.info("【规则配置】创建规则成功，ID: {}, 名称: {}", ruleId, createVO.getRuleName());

            return new ResponseEntity<>(new Result<>(ruleId, errors), HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            log.warn("【规则配置】创建规则参数错误: {}", e.getMessage());
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value(), e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("【规则配置】创建规则失败，名称: {}", createVO.getRuleName(), e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "创建失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 更新规则
     */
    @HttpMonitorLogger
    @ApiOperation(value = "更新规则", notes = "更新现有规则信息")
    @PutMapping("/{id}")
    public ResponseEntity<Result<Boolean>> updateRule(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则ID", required = true) @PathVariable Long id,
            @ApiParam(value = "规则更新信息", required = true)
            @Valid @RequestBody DataInspectionRuleVO.RuleUpdateVO updateVO,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value()), HttpStatus.BAD_REQUEST);
        }

        try {
            boolean result = ruleConfigService.updateRule(id, updateVO);

            if (result) {
                log.info("【规则配置】更新规则成功，ID: {}", id);
                return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
            } else {
                log.warn("【规则配置】更新规则失败，未找到记录，ID: {}", id);
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value(), "规则不存在"), HttpStatus.NOT_FOUND);
            }
        } catch (IllegalArgumentException e) {
            log.warn("【规则配置】更新规则参数错误: {}", e.getMessage());
            return new ResponseEntity<>(new Result<>(errors, 400, HttpStatus.BAD_REQUEST.value(), e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            log.error("【规则配置】更新规则失败，ID: {}", id, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "更新失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除规则
     */
    @HttpMonitorLogger
    @ApiOperation(value = "删除规则", notes = "删除指定规则")
    @DeleteMapping("/{id}")
    public ResponseEntity<Result<Boolean>> deleteRule(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则ID", required = true) @PathVariable Long id,
            @ApiParam(value = "规则类型", required = false) @RequestParam(required = false) Integer ruleType) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = ruleConfigService.deleteRule(id);

            if (result) {
                log.info("【规则配置】删除规则成功，ID: {}", id);
                return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
            } else {
                log.warn("【规则配置】删除规则失败，未找到记录，ID: {}", id);
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value(), "规则不存在"), HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("【规则配置】删除规则失败，ID: {}", id, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "删除失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 批量删除规则
     */
    @HttpMonitorLogger
    @ApiOperation(value = "批量删除规则", notes = "批量删除多个规则")
    @DeleteMapping("/batch")
    public ResponseEntity<Result<Boolean>> batchDeleteRules(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则ID列表", required = true)
            @RequestBody List<Long> ids) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = ruleConfigService.batchDeleteRules(ids);

            log.info("【规则配置】批量删除规则完成，IDs: {}, 结果: {}", ids, result);

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】批量删除规则失败，IDs: {}", ids, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "批量删除失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 启用/禁用规则
     */
    @HttpMonitorLogger
    @ApiOperation(value = "启用/禁用规则", notes = "切换规则的启用状态")
    @PutMapping("/{id}/toggle")
    public ResponseEntity<Result<Boolean>> toggleRuleStatus(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则ID", required = true) @PathVariable Long id,
            @ApiParam(value = "启用状态", required = true)
            @RequestBody DataInspectionRuleVO.RuleUpdateVO updateVO) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            boolean result = ruleConfigService.toggleRuleStatus(id, updateVO.getIsEnabled());

            if (result) {
                log.info("【规则配置】切换规则状态成功，ID: {}, 启用: {}", id, updateVO.getIsEnabled());
                return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
            } else {
                log.warn("【规则配置】切换规则状态失败，未找到记录，ID: {}", id);
                return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.NOT_FOUND.value(), "规则不存在"), HttpStatus.NOT_FOUND);
            }
        } catch (Exception e) {
            log.error("【规则配置】切换规则状态失败，ID: {}", id, e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "状态切换失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 验证规则内容格式
     */
    @HttpMonitorLogger
    @ApiOperation(value = "验证规则内容格式", notes = "验证规则内容的JSON格式是否正确")
    @PostMapping("/validate")
    public ResponseEntity<Result<DataInspectionRuleVO.RuleValidationVO>> validateRuleContent(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则内容", required = true)
            @RequestBody String ruleContent) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            DataInspectionRuleVO.RuleValidationVO validation = ruleConfigService.validateRuleContent(ruleContent);

            log.info("【规则配置】规则内容验证完成，结果: {}", validation.getValid());

            return new ResponseEntity<>(new Result<>(validation, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】验证规则内容失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "验证失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取规则类型选项
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取规则类型选项", notes = "获取所有可用的规则类型选项")
    @GetMapping("/type-options")
    public ResponseEntity<Result<List<DataInspectionRuleVO.RuleTypeOptionVO>>> getRuleTypeOptions(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            List<DataInspectionRuleVO.RuleTypeOptionVO> options = ruleConfigService.getRuleTypeOptions();

            log.info("【规则配置】获取规则类型选项成功，数量: {}", options.size());

            return new ResponseEntity<>(new Result<>(options, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】获取规则类型选项失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取规则类型统计
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取规则类型统计", notes = "获取各类型规则的统计信息")
    @GetMapping("/statistics")
    public ResponseEntity<Result<List<DataInspectionRuleConfigMapper.RuleTypeStatistics>>> getRuleTypeStatistics(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            List<DataInspectionRuleConfigMapper.RuleTypeStatistics> statistics =
                    ruleConfigService.getRuleTypeStatistics();

            log.info("【规则配置】获取规则类型统计成功");

            return new ResponseEntity<>(new Result<>(statistics, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】获取规则类型统计失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 刷新规则缓存
     */
    @HttpMonitorLogger
    @ApiOperation(value = "刷新规则缓存", notes = "清除规则相关的缓存数据")
    @PostMapping("/refresh-cache")
    public ResponseEntity<Result<Boolean>> refreshCache(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里可以调用缓存管理服务清除相关缓存
            // cacheManager.evict("inspection:rules");

            log.info("【规则配置】刷新规则缓存成功");

            return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】刷新规则缓存失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "缓存刷新失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 导出规则配置
     */
    @HttpMonitorLogger
    @ApiOperation(value = "导出规则配置", notes = "导出规则配置信息")
    @GetMapping("/export")
    public ResponseEntity<Result<String>> exportRules(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "规则类型") @RequestParam(required = false) Integer ruleType,
            @ApiParam(value = "导出格式: excel, json") @RequestParam(defaultValue = "excel") String format) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            // 这里实现具体的导出逻辑
            String downloadUrl = "/api/downloads/rules_" + System.currentTimeMillis() + "." + format;

            log.info("【规则配置】导出规则配置成功，下载地址: {}", downloadUrl);

            return new ResponseEntity<>(new Result<>(downloadUrl, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("【规则配置】导出规则配置失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), "导出失败: " + e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}