package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.HonorEntity;
import com.goodsogood.ows.model.vo.HonorApplyVO;
import com.goodsogood.ows.model.vo.HonorAuditVO;
import com.goodsogood.ows.model.vo.PageResult;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.services.HonorService;
import com.goodsogood.ows.util.SysHeaderHelper;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 荣誉表彰控制器
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/api/honor")
@CrossOrigin(origins = "*", maxAge = 3600)
public class HonorController {

    private final HonorService honorService;

    private final Errors errors;

    public HonorController(HonorService honorService, Errors errors) {
        this.honorService = honorService;
        this.errors = errors;
    }

    /**
     * 提交荣誉申报
     */
    @PostMapping("/apply")
    public ResponseEntity<Result<HonorEntity>> apply(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody HonorApplyVO vo,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "参数验证失败"), HttpStatus.BAD_REQUEST);
        }

        try {
            HonorEntity result = honorService.apply(vo, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("提交荣誉申报失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 更新荣誉申报
     */
    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation(value = "更新荣誉申报", notes = "更新荣誉申报信息")
    public ResponseEntity<Result<HonorEntity>> update(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody HonorApplyVO vo,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "参数验证失败"), HttpStatus.BAD_REQUEST);
        }

        if (vo.getId() == null) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "ID不能为空"), HttpStatus.BAD_REQUEST);
        }

        try {
            HonorEntity result = honorService.update(vo, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("更新荣誉申报失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 删除荣誉申报
     */
    @HttpMonitorLogger
    @DeleteMapping("/remove/{id}")
    @ApiOperation(value = "删除荣誉申报", notes = "逻辑删除荣誉申报")
    public ResponseEntity<Result<Boolean>> remove(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "荣誉申报ID", required = true) @PathVariable Long id) {
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            boolean result = honorService.delete(id, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除荣誉申报失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据ID查询荣誉申报详情
     */
    @HttpMonitorLogger
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "查询荣誉申报详情", notes = "根据ID查询荣誉申报详情")
    public ResponseEntity<Result<HonorEntity>> detail(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "荣誉申报ID", required = true) @PathVariable Long id) {
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            HonorEntity result = honorService.findById(id, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("查询荣誉申报详情失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分页查询荣誉申报列表
     */
    @HttpMonitorLogger
    @GetMapping("/find-all")
    @ApiOperation(value = "分页查询荣誉申报列表", notes = "分页查询荣誉申报列表，支持多条件筛选")
    public ResponseEntity<Result<PageResult<HonorEntity>>> findAll(
            @RequestHeader HttpHeaders headers,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") int page,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") int pagesize,
            @ApiParam(value = "申报人") @RequestParam(required = false) String applicant,
            @ApiParam(value = "部门") @RequestParam(required = false) String department,
            @ApiParam(value = "申报类型") @RequestParam(required = false) Integer type,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> params = honorService.buildQueryParams(applicant, department, type, status, startTime, endTime);
            PageResult<HonorEntity> result = honorService.findByPage(page, pagesize, params, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("分页查询荣誉申报列表失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 审核荣誉申报
     */
    @HttpMonitorLogger
    @PostMapping("/audit")
    @ApiOperation(value = "审核荣誉申报", notes = "审核荣誉申报，设置审核状态和意见")
    public ResponseEntity<Result<Boolean>> audit(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody HonorAuditVO vo,
            BindingResult bindingResult) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        if (bindingResult.hasErrors()) {
            return new ResponseEntity<>(new Result<>(errors, 400,
                    HttpStatus.BAD_REQUEST.value(), "参数验证失败"), HttpStatus.BAD_REQUEST);
        }

        try {
            boolean result = honorService.audit(vo, sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("审核荣誉申报失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 统计荣誉申报数据
     */
    @HttpMonitorLogger
    @GetMapping("/statistics")
    @ApiOperation(value = "统计荣誉申报数据", notes = "统计荣誉申报的各种数据")
    public ResponseEntity<Result<Map<String, Object>>> statistics(
            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);

        try {
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("typeStats", honorService.countByType(sysHeader.getOid()));
            result.put("statusStats", honorService.countByStatus(sysHeader.getOid()));
            result.put("latest", honorService.findLatest(5, sysHeader.getOid()));
            result.put("monthlyStats", honorService.countByMonth(sysHeader.getOid()));

            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("统计荣誉申报数据失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500, HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 兼容旧版API - 获取列表
     */
    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation(value = "获取荣誉申报列表", notes = "兼容旧版API，获取荣誉申报列表")
    public ResponseEntity<Result<List<HonorApplyVO>>> list(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            List<HonorApplyVO> result = honorService.list(sysHeader);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取荣誉申报列表失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 兼容旧版API - 删除
     */
    @HttpMonitorLogger
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除荣誉申报", notes = "兼容旧版API，删除荣誉申报")
    public ResponseEntity<Result<Void>> delete(
            @RequestHeader HttpHeaders headers,
            @PathVariable Long id) {
        HeaderHelper.SysHeader sysHeader = SysHeaderHelper.getSysHeader(headers, errors);
        try {
            honorService.delete(id, sysHeader);
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除荣誉申报失败", e);
            return new ResponseEntity<>(new Result<>(errors, 500,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}