package com.goodsogood.ows.helper;

import org.springframework.http.HttpHeaders;
import lombok.Data;

/**
 * 请求头处理工具类
 * 
 * <AUTHOR>
 * @date 2024-12-25
 */
public class HeaderHelper {
    
    public static final String TOKEN = "_tk";
    public static final String OPERATOR_ID = "_uid";
    public static final String OPERATOR_NAME = "_un";
    public static final String OPERATOR_TYPE = "_type";
    public static final String OPERATOR_OID = "_oid";
    public static final String OPERATOR_ORG_NAME = "_org_name";
    public static final String OPERATOR_ORG_TYPE = "_org_type";
    public static final String OPERATOR_REGION = "_region_id";
    public static final String OPERATOR_MENU_ID = "_menu_id";
    
    /**
     * 构建系统头信息
     */
    public static SysHeader buildMyHeader(HttpHeaders headers) {
        SysHeader sysHeader = new SysHeader();
        
        sysHeader.setToken(getHeaderValue(headers, TOKEN));
        sysHeader.setUserId(parseLong(getHeaderValue(headers, OPERATOR_ID)));
        sysHeader.setUserName(getHeaderValue(headers, OPERATOR_NAME));
        sysHeader.setType(getHeaderValue(headers, OPERATOR_TYPE));
        sysHeader.setOid(parseLong(getHeaderValue(headers, OPERATOR_OID)));
        sysHeader.setOrgName(getHeaderValue(headers, OPERATOR_ORG_NAME));
        sysHeader.setOrgType(getHeaderValue(headers, OPERATOR_ORG_TYPE));
        sysHeader.setRegionId(parseLong(getHeaderValue(headers, OPERATOR_REGION)));
        sysHeader.setMenuId(getHeaderValue(headers, OPERATOR_MENU_ID));
        
        return sysHeader;
    }
    
    private static String getHeaderValue(HttpHeaders headers, String key) {
        return headers.getFirst(key);
    }
    
    private static Long parseLong(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * 系统头信息封装类
     */
    @Data
    public static class SysHeader {
        private String token;
        private Long userId;
        private String userName;
        private String type;
        private Long oid;
        private String orgName;
        private String orgType;
        private Long regionId;
        private String menuId;
    }
}
