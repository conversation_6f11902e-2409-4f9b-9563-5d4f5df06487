package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据体检规则配置相关VO类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public class DataInspectionRuleVO {

    /**
     * 体检规则信息VO
     */
    @Data
    @ApiModel(description = "体检规则信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class HealthCheckRuleVO implements Serializable {

        @ApiModelProperty(value = "规则ID")
        private Long id;

        @ApiModelProperty(value = "规则名称", required = true)
        @NotBlank(message = "规则名称不能为空")
        private String ruleName;

        @ApiModelProperty(value = "规则类型：1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整", required = true)
        @NotNull(message = "规则类型不能为空")
        @Min(value = 1, message = "规则类型值必须在1-4之间")
        @Max(value = 4, message = "规则类型值必须在1-4之间")
        private Integer ruleType;

        @ApiModelProperty(value = "规则内容JSON字符串", required = true)
        @NotBlank(message = "规则内容不能为空")
        private String ruleContent;

        @ApiModelProperty(value = "是否启用", required = true)
        @NotNull(message = "启用状态不能为空")
        private Boolean isEnabled;

        @ApiModelProperty(value = "优先级：1-高, 2-中, 3-低", required = true)
        @NotNull(message = "优先级不能为空")
        @Min(value = 1, message = "优先级值必须在1-3之间")
        @Max(value = 3, message = "优先级值必须在1-3之间")
        private Integer priority;

        @ApiModelProperty(value = "规则描述")
        private String description;

        @ApiModelProperty(value = "创建时间")
        private LocalDateTime createTime;

        @ApiModelProperty(value = "更新时间")
        private LocalDateTime updateTime;
    }

    /**
     * 体检规则搜索参数VO
     */
    @Data
    @ApiModel(description = "体检规则搜索参数")
    public static class RuleSearchVO implements Serializable {

        @ApiModelProperty(value = "规则名称（模糊搜索）")
        private String ruleName;

        @ApiModelProperty(value = "规则类型：1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整")
        @Min(value = 1, message = "规则类型值必须在1-4之间")
        @Max(value = 4, message = "规则类型值必须在1-4之间")
        private Integer ruleType;

        @ApiModelProperty(value = "是否启用")
        private Boolean isEnabled;

        @ApiModelProperty(value = "优先级：1-高, 2-中, 3-低")
        @Min(value = 1, message = "优先级值必须在1-3之间")
        @Max(value = 3, message = "优先级值必须在1-3之间")
        private Integer priority;

        @ApiModelProperty(value = "页码", example = "1")
        @Min(value = 1, message = "页码必须大于0")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "每页大小", example = "10")
        @Min(value = 1, message = "每页大小必须大于0")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer pageSize = 10;
    }

    /**
     * 分页结果VO
     */
    @Data
    @ApiModel(description = "分页结果")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PageResult<T> {

        @ApiModelProperty(value = "数据列表")
        private List<T> data;

        @ApiModelProperty(value = "总数量")
        private Long total;

        @ApiModelProperty(value = "当前页码")
        private Integer pageNum;

        @ApiModelProperty(value = "每页大小")
        private Integer pageSize;

        @ApiModelProperty(value = "总页数")
        private Integer totalPages;

        public PageResult() {
        }

        public PageResult(List<T> data, Long total, Integer pageNum, Integer pageSize) {
            this.data = data;
            this.total = total;
            this.pageNum = pageNum;
            this.pageSize = pageSize;
            this.totalPages = total == null ? 0 : (int) Math.ceil((double) total / pageSize);
        }
    }

    /**
     * 规则创建请求VO
     */
    @Data
    @ApiModel(description = "规则创建请求")
    public static class RuleCreateVO implements Serializable {

        @ApiModelProperty(value = "规则名称", required = true)
        @NotBlank(message = "规则名称不能为空")
        private String ruleName;

        @ApiModelProperty(value = "规则类型：1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整", required = true)
        @NotNull(message = "规则类型不能为空")
        @Min(value = 1, message = "规则类型值必须在1-4之间")
        @Max(value = 4, message = "规则类型值必须在1-4之间")
        private Integer ruleType;

        @ApiModelProperty(value = "规则内容JSON字符串", required = true)
        @NotBlank(message = "规则内容不能为空")
        private String ruleContent;

        @ApiModelProperty(value = "是否启用", required = true)
        @NotNull(message = "启用状态不能为空")
        private Boolean isEnabled;

        @ApiModelProperty(value = "优先级：1-高, 2-中, 3-低", required = true)
        @NotNull(message = "优先级不能为空")
        @Min(value = 1, message = "优先级值必须在1-3之间")
        @Max(value = 3, message = "优先级值必须在1-3之间")
        private Integer priority;

        @ApiModelProperty(value = "规则描述")
        private String description;
    }

    /**
     * 规则更新请求VO
     */
    @Data
    @ApiModel(description = "规则更新请求")
    public static class RuleUpdateVO implements Serializable {

        @ApiModelProperty(value = "规则名称")
        private String ruleName;

        @ApiModelProperty(value = "规则内容JSON字符串")
        private String ruleContent;

        @ApiModelProperty(value = "是否启用")
        private Boolean isEnabled;

        @ApiModelProperty(value = "优先级：1-高, 2-中, 3-低")
        @Min(value = 1, message = "优先级值必须在1-3之间")
        @Max(value = 3, message = "优先级值必须在1-3之间")
        private Integer priority;

        @ApiModelProperty(value = "规则描述")
        private String description;
    }

    /**
     * 统一响应结果VO
     */
    @Data
    @ApiModel(description = "统一响应结果")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CommonResult<T> {

        @ApiModelProperty(value = "响应码：200-成功")
        private Integer code;

        @ApiModelProperty(value = "响应消息")
        private String message;

        @ApiModelProperty(value = "响应数据")
        private T data;

        @ApiModelProperty(value = "是否成功")
        private Boolean success;

        public CommonResult() {
        }

        public CommonResult(Integer code, String message, T data) {
            this.code = code;
            this.message = message;
            this.data = data;
            this.success = code == 200;
        }

        public static <T> CommonResult<T> success(T data) {
            return new CommonResult<>(200, "操作成功", data);
        }

        public static <T> CommonResult<T> success(String message, T data) {
            return new CommonResult<>(200, message, data);
        }

        public static <T> CommonResult<T> error(String message) {
            return new CommonResult<>(500, message, null);
        }

        public static <T> CommonResult<T> error(Integer code, String message) {
            return new CommonResult<>(code, message, null);
        }
    }

    /**
     * 规则类型枚举映射VO
     */
    @Data
    @ApiModel(description = "规则类型选项")
    public static class RuleTypeOptionVO implements Serializable {

        @ApiModelProperty(value = "类型值")
        private Integer value;

        @ApiModelProperty(value = "类型标签")
        private String label;

        @ApiModelProperty(value = "类型描述")
        private String description;

        public RuleTypeOptionVO(Integer value, String label, String description) {
            this.value = value;
            this.label = label;
            this.description = description;
        }
    }

    /**
     * 规则验证结果VO
     */
    @Data
    @ApiModel(description = "规则验证结果")
    public static class RuleValidationVO implements Serializable {

        @ApiModelProperty(value = "是否验证通过")
        private Boolean valid;

        @ApiModelProperty(value = "验证消息")
        private String message;

        @ApiModelProperty(value = "错误详情")
        private String errorDetails;

        public RuleValidationVO(Boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public RuleValidationVO(Boolean valid, String message, String errorDetails) {
            this.valid = valid;
            this.message = message;
            this.errorDetails = errorDetails;
        }
    }
}