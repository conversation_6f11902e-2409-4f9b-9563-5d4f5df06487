package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 统计分析相关VO类集合
 * 包含统计分析模块所需的所有数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public class StatisticsAnalysisVO {

    /**
     * 核心指标统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "核心指标统计数据")
    public static class CoreMetricsVO {
        
        @ApiModelProperty(value = "总异常数", example = "1542")
        private Integer totalExceptions;
        
        @ApiModelProperty(value = "已处理异常数", example = "1398") 
        private Integer handledExceptions;
        
        @ApiModelProperty(value = "处理完成率", example = "90.66")
        private BigDecimal completionRate;
        
        @ApiModelProperty(value = "操作人员总数", example = "186")
        private Integer totalOperators;
        
        @ApiModelProperty(value = "涉及部门总数", example = "23")
        private Integer totalDepartments;
        
        @ApiModelProperty(value = "平均处理时长(小时)", example = "12.47")
        private BigDecimal averageHandlingTime;
        
        @ApiModelProperty(value = "高严重程度异常数", example = "156")
        private Integer highSeverityCount;
        
        @ApiModelProperty(value = "中严重程度异常数", example = "867")
        private Integer mediumSeverityCount;
        
        @ApiModelProperty(value = "低严重程度异常数", example = "519")
        private Integer lowSeverityCount;
        
        @ApiModelProperty(value = "异常类型分布")
        private Map<String, Integer> exceptionTypeDistribution;
    }

    /**
     * 月度统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "月度统计数据")
    public static class MonthlyStatisticsVO {
        
        @ApiModelProperty(value = "月份", example = "2024-01")
        private String month;
        
        @ApiModelProperty(value = "异常总数", example = "124")
        private Integer exceptionCount;
        
        @ApiModelProperty(value = "已处理数量", example = "118")
        private Integer handledCount;
        
        @ApiModelProperty(value = "处理率", example = "95.16")
        private BigDecimal handlingRate;
        
        @ApiModelProperty(value = "新增异常数", example = "35")
        private Integer newExceptionCount;
        
        @ApiModelProperty(value = "平均处理时长(小时)", example = "15.42")
        private BigDecimal avgHandlingTime;
    }

    /**
     * 部门统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "部门统计数据")
    public static class DepartmentStatisticsVO {
        
        @ApiModelProperty(value = "部门名称", example = "办公室")
        private String department;
        
        @ApiModelProperty(value = "异常总数", example = "156")
        private Integer totalExceptions;
        
        @ApiModelProperty(value = "处理率", example = "92.31")
        private BigDecimal handlingRate;
        
        @ApiModelProperty(value = "高严重程度数量", example = "23")
        private Integer highSeverityCount;
        
        @ApiModelProperty(value = "中严重程度数量", example = "89")
        private Integer mediumSeverityCount;
        
        @ApiModelProperty(value = "低严重程度数量", example = "44")
        private Integer lowSeverityCount;
        
        @ApiModelProperty(value = "平均处理时长(小时)", example = "18.5")
        private BigDecimal avgHandlingTime;
        
        @ApiModelProperty(value = "质量评分", example = "4.5")
        private BigDecimal qualityScore;
    }

    /**
     * 操作人员统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "操作人员统计数据")
    public static class OperatorStatisticsVO {
        
        @ApiModelProperty(value = "操作人员姓名", example = "张明华")
        private String operatorName;
        
        @ApiModelProperty(value = "处理数量", example = "45")
        private Integer handledCount;
        
        @ApiModelProperty(value = "处理成功率", example = "95.56")
        private BigDecimal successRate;
        
        @ApiModelProperty(value = "平均处理时长(小时)", example = "14.2")
        private BigDecimal avgHandlingTime;
        
        @ApiModelProperty(value = "所属部门", example = "党校")
        private String department;
        
        @ApiModelProperty(value = "处理质量评分", example = "4.7")
        private BigDecimal qualityRating;
        
        @ApiModelProperty(value = "专长类型")
        private List<String> specialtyTypes;
    }

    /**
     * 趋势分析数据项VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "趋势分析数据项")
    public static class TrendAnalysisItemVO {
        
        @ApiModelProperty(value = "日期", example = "2024-12-01")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;
        
        @ApiModelProperty(value = "异常总数", example = "12")
        private Integer exceptionCount;
        
        @ApiModelProperty(value = "已处理数量", example = "10")
        private Integer handledCount;
        
        @ApiModelProperty(value = "新增异常数", example = "5")
        private Integer newExceptionCount;
        
        @ApiModelProperty(value = "处理率", example = "83.33")
        private BigDecimal handlingRate;
        
        @ApiModelProperty(value = "操作人员数", example = "8")
        private Integer operatorCount;
    }

    /**
     * 趋势分析结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "趋势分析结果")
    public static class TrendAnalysisResultVO {
        
        @ApiModelProperty(value = "分析类型", example = "daily")
        private String type;
        
        @ApiModelProperty(value = "数据项列表")
        private List<TrendAnalysisItemVO> data;
        
        @ApiModelProperty(value = "统计汇总")
        private Map<String, Object> summary;
    }

    /**
     * 综合统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "综合统计数据")
    public static class ComprehensiveStatisticsVO {
        
        @ApiModelProperty(value = "核心指标")
        private CoreMetricsVO coreMetrics;
        
        @ApiModelProperty(value = "月度统计(最近6个月)")
        private List<MonthlyStatisticsVO> monthlyStats;
        
        @ApiModelProperty(value = "部门统计(Top 5)")
        private List<DepartmentStatisticsVO> departmentStats;
        
        @ApiModelProperty(value = "操作人员统计(Top 5)")
        private List<OperatorStatisticsVO> operatorStats;
        
        @ApiModelProperty(value = "趋势数据(最近10天)")
        private List<TrendAnalysisItemVO> trendData;
        
        @ApiModelProperty(value = "异常类型分布")
        private Map<String, Integer> exceptionTypeStats;
        
        @ApiModelProperty(value = "严重程度分布")
        private Map<String, Integer> severityStats;
    }

    /**
     * 自定义查询参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "自定义查询参数")
    public static class CustomQueryParamsVO {
        
        @ApiModelProperty(value = "开始日期", example = "2024-01-01")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate startDate;
        
        @ApiModelProperty(value = "结束日期", example = "2024-12-31")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        
        @ApiModelProperty(value = "部门列表", example = "[\"办公室\", \"组织部\"]")
        private List<String> departments;
        
        @ApiModelProperty(value = "操作人员列表", example = "[\"张三\", \"李四\"]")
        private List<String> operators;
        
        @ApiModelProperty(value = "异常类型列表", example = "[\"数据缺失\", \"数据格式错误\"]")
        private List<String> exceptionTypes;
        
        @ApiModelProperty(value = "严重程度列表", example = "[\"high\", \"medium\"]")
        private List<String> severityLevels;
        
        @ApiModelProperty(value = "处理状态列表", example = "[\"resolved\", \"in_remediation\"]")
        private List<String> statusList;
        
        @ApiModelProperty(value = "数据源列表", example = "[\"ds001\", \"ds002\"]")
        private List<String> dataSources;
        
        @ApiModelProperty(value = "统计维度", example = "[\"by_type\", \"by_department\"]")
        private List<String> metrics;
        
        @ApiModelProperty(value = "分组方式", example = "monthly")
        private String groupBy;
    }

    /**
     * 自定义查询结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "自定义查询结果")
    public static class CustomQueryResultVO {
        
        @ApiModelProperty(value = "总记录数", example = "150")
        private Integer totalRecords;
        
        @ApiModelProperty(value = "查询结果数据")
        private List<Map<String, Object>> data;
        
        @ApiModelProperty(value = "统计汇总")
        private Map<String, Object> summary;
        
        @ApiModelProperty(value = "查询参数")
        private CustomQueryParamsVO queryParams;
        
        @ApiModelProperty(value = "查询时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime queryTime;
    }

    /**
     * 导出参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "导出参数")
    public static class ExportParamsVO {
        
        @ApiModelProperty(value = "导出格式", example = "excel", allowableValues = "excel,pdf,csv")
        private String format;
        
        @ApiModelProperty(value = "报表类型", example = "comprehensive")
        private String reportType;
        
        @ApiModelProperty(value = "时间范围", example = "last30days")
        private String timeRange;
        
        @ApiModelProperty(value = "是否包含图表", example = "true")
        private Boolean includeCharts;
        
        @ApiModelProperty(value = "自定义查询参数")
        private CustomQueryParamsVO customParams;
        
        @ApiModelProperty(value = "导出字段列表")
        private List<String> exportFields;
    }

    /**
     * 导出结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "导出结果")
    public static class ExportResultVO {
        
        @ApiModelProperty(value = "下载链接", example = "http://localhost:8080/downloads/report_20241207_143052.xlsx")
        private String downloadUrl;
        
        @ApiModelProperty(value = "文件名", example = "统计报表_20241207_143052.xlsx")
        private String fileName;
        
        @ApiModelProperty(value = "文件大小", example = "2.5MB")
        private String fileSize;
        
        @ApiModelProperty(value = "记录数量", example = "1542")
        private Integer recordCount;
        
        @ApiModelProperty(value = "导出时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime exportTime;
        
        @ApiModelProperty(value = "有效期")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expiredTime;
    }

    /**
     * 实时数据VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "实时统计数据")
    public static class RealTimeDataVO {
        
        @ApiModelProperty(value = "当前在线处理人员数", example = "15")
        private Integer currentOnlineOperators;
        
        @ApiModelProperty(value = "今日新增异常数", example = "8")
        private Integer todayNewExceptions;
        
        @ApiModelProperty(value = "今日处理完成数", example = "12")
        private Integer todayHandledCount;
        
        @ApiModelProperty(value = "实时处理率", example = "87.5")
        private BigDecimal realTimeHandlingRate;
        
        @ApiModelProperty(value = "最近活动记录")
        private List<RecentActivityVO> recentActivities;
        
        @ApiModelProperty(value = "系统状态")
        private SystemStatusVO systemStatus;
        
        @ApiModelProperty(value = "数据时间戳")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
    }

    /**
     * 最近活动VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "最近活动记录")
    public static class RecentActivityVO {
        
        @ApiModelProperty(value = "活动时间")
        @JsonFormat(pattern = "HH:mm:ss")
        private LocalDateTime time;
        
        @ApiModelProperty(value = "活动描述", example = "张三处理了数据缺失异常")
        private String activity;
        
        @ApiModelProperty(value = "活动类型", example = "exception_handled")
        private String type;
        
        @ApiModelProperty(value = "操作人员", example = "张三")
        private String operator;
    }

    /**
     * 系统状态VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "系统运行状态")
    public static class SystemStatusVO {
        
        @ApiModelProperty(value = "服务器负载", example = "65.2")
        private BigDecimal serverLoad;
        
        @ApiModelProperty(value = "响应时间(ms)", example = "150")
        private Integer responseTime;
        
        @ApiModelProperty(value = "错误率", example = "0.05%")
        private String errorRate;
        
        @ApiModelProperty(value = "数据库连接状态", example = "normal")
        private String databaseStatus;
    }

    /**
     * 统计维度配置选项VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "统计维度配置选项")
    public static class DimensionOptionsVO {
        
        @ApiModelProperty(value = "主要维度选项")
        private Map<String, List<String>> primaryDimensions;
        
        @ApiModelProperty(value = "次要维度选项") 
        private Map<String, List<String>> secondaryDimensions;
        
        @ApiModelProperty(value = "时间范围选项")
        private List<TimeRangeOptionVO> timeRangeOptions;
    }

    /**
     * 时间范围选项VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "时间范围选项")
    public static class TimeRangeOptionVO {
        
        @ApiModelProperty(value = "选项键", example = "last7days")
        private String key;
        
        @ApiModelProperty(value = "显示名称", example = "最近7天")
        private String label;
        
        @ApiModelProperty(value = "天数", example = "7")
        private Integer days;
    }

    /**
     * 分页结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "分页结果")
    public static class PagedResultVO<T> {
        
        @ApiModelProperty(value = "数据列表")
        private List<T> data;
        
        @ApiModelProperty(value = "总记录数", example = "150")
        private Integer total;
        
        @ApiModelProperty(value = "当前页", example = "1")
        private Integer currentPage;
        
        @ApiModelProperty(value = "每页大小", example = "10")
        private Integer pageSize;
    }

    /**
     * 仪表板配置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "仪表板配置")
    public static class DashboardConfigVO {
        
        @ApiModelProperty(value = "刷新间隔(秒)", example = "30")
        private Integer refreshInterval;
        
        @ApiModelProperty(value = "默认时间范围", example = "last30days")
        private String defaultTimeRange;
        
        @ApiModelProperty(value = "显示指标列表")
        private List<String> displayMetrics;
        
        @ApiModelProperty(value = "警报阈值配置")
        private Map<String, BigDecimal> alertThresholds;
        
        @ApiModelProperty(value = "图表配置")
        private List<ChartConfigVO> chartConfigs;
    }

    /**
     * 图表配置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "图表配置")
    public static class ChartConfigVO {
        
        @ApiModelProperty(value = "图表ID", example = "trend_chart")
        private String chartId;
        
        @ApiModelProperty(value = "图表类型", example = "line")
        private String chartType;
        
        @ApiModelProperty(value = "图表标题", example = "异常处理趋势")
        private String title;
        
        @ApiModelProperty(value = "数据源", example = "trend_analysis")
        private String dataSource;
        
        @ApiModelProperty(value = "显示位置")
        private Map<String, Integer> position;
    }

    /**
     * 统计设置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "统计设置")
    public static class StatisticsSettingsVO {
        
        @ApiModelProperty(value = "刷新间隔(秒)", example = "60")
        private Integer refreshInterval;
        
        @ApiModelProperty(value = "默认时间范围", example = "last30days")
        private String defaultTimeRange;
        
        @ApiModelProperty(value = "显示指标列表")
        private List<String> displayMetrics;
        
        @ApiModelProperty(value = "警报阈值配置")
        private Map<String, BigDecimal> alertThresholds;
        
        @ApiModelProperty(value = "邮件通知设置")
        private Map<String, Object> emailNotificationSettings;
    }

    /**
     * 异常统计概览VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "异常统计概览")
    public static class ExceptionOverviewVO {
        
        @ApiModelProperty(value = "总异常数", example = "1542")
        private Integer totalExceptions;
        
        @ApiModelProperty(value = "高严重程度数量", example = "156")
        private Integer highSeverityCount;
        
        @ApiModelProperty(value = "中严重程度数量", example = "867")
        private Integer mediumSeverityCount;
        
        @ApiModelProperty(value = "低严重程度数量", example = "519")
        private Integer lowSeverityCount;
        
        @ApiModelProperty(value = "最后体检时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastInspectionTime;
        
        @ApiModelProperty(value = "下次体检时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextInspectionTime;
        
        @ApiModelProperty(value = "状态分布")
        private Map<String, Integer> statusDistribution;
    }

    /**
     * 处理效率统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "处理效率统计")
    public static class EfficiencyStatsVO {
        
        @ApiModelProperty(value = "平均处理时长(小时)", example = "15.6")
        private BigDecimal avgHandlingTime;
        
        @ApiModelProperty(value = "处理成功率", example = "94.2")
        private BigDecimal successRate;
        
        @ApiModelProperty(value = "按时完成率", example = "87.3")
        private BigDecimal onTimeCompletionRate;
        
        @ApiModelProperty(value = "效率趋势")
        private List<EfficiencyTrendVO> efficiencyTrend;
        
        @ApiModelProperty(value = "部门效率排名")
        private List<DepartmentEfficiencyVO> departmentRanking;
    }

    /**
     * 效率趋势VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "效率趋势数据")
    public static class EfficiencyTrendVO {
        
        @ApiModelProperty(value = "日期", example = "2024-12-01")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;
        
        @ApiModelProperty(value = "处理效率分数", example = "85.2")
        private BigDecimal efficiencyScore;
        
        @ApiModelProperty(value = "处理数量", example = "12")
        private Integer handledCount;
        
        @ApiModelProperty(value = "平均处理时长", example = "14.5")
        private BigDecimal avgTime;
    }

    /**
     * 部门效率VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "部门效率统计")
    public static class DepartmentEfficiencyVO {
        
        @ApiModelProperty(value = "部门名称", example = "办公室")
        private String department;
        
        @ApiModelProperty(value = "效率分数", example = "88.5")
        private BigDecimal efficiencyScore;
        
        @ApiModelProperty(value = "排名", example = "2")
        private Integer rank;
        
        @ApiModelProperty(value = "处理总数", example = "156")
        private Integer totalHandled;
        
        @ApiModelProperty(value = "平均处理时长", example = "16.2")
        private BigDecimal avgHandlingTime;
    }
}