package com.goodsogood.ows.model.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 荣誉申报VO类
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
public class HonorApplyVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申报人
     */
    @NotBlank(message = "申报人不能为空")
    private String applicant;

    /**
     * 所属部门
     */
    @NotBlank(message = "所属部门不能为空")
    private String department;

    /**
     * 申报类型：1-优秀共产党员，2-优秀党务工作者，3-先进基层党组织，4-先进工作者，5-模范机关标兵单位
     */
    @NotNull(message = "申报类型不能为空")
    private Integer type;

    /**
     * 申报材料URL
     */
    @NotBlank(message = "申报材料不能为空")
    private String materialUrl;

    /**
     * 申报说明
     */
    private String description;

    /**
     * 状态：1-待审核，2-已通过，3-已驳回
     * 注意：此字段仅用于返回数据，提交时会被忽略
     */
    private Integer status;

    /**
     * 创建时间
     * 注意：此字段仅用于返回数据，提交时会被忽略
     */
    private String createTime;

    /**
     * 审核意见
     * 注意：此字段仅用于返回数据，提交时会被忽略
     */
    private String auditOpinion;
}