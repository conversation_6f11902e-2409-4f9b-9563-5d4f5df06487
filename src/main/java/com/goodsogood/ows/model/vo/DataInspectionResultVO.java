package com.goodsogood.ows.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 数据体检结果管理相关VO类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public class DataInspectionResultVO {

    /**
     * 异常详情VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "异常详情", description = "数据体检异常详情信息")
    public static class HealthCheckExceptionVO implements Serializable {
        @ApiModelProperty(value = "异常ID")
        private Long id;

        @ApiModelProperty(value = "体检任务ID")
        @JsonProperty("checkTaskId")
        private Long checkTaskId;

        @ApiModelProperty(value = "异常类型: 1-完整性异常 2-准确性异常 3-一致性异常 4-安全性异常")
        @JsonProperty("exceptionType")
        private Integer exceptionType;

        @ApiModelProperty(value = "异常级别: 1-低 2-中 3-高")
        @JsonProperty("exceptionLevel")
        private Integer exceptionLevel;

        @ApiModelProperty(value = "异常标题")
        @JsonProperty("exceptionTitle")
        private String exceptionTitle;

        @ApiModelProperty(value = "异常描述")
        @JsonProperty("exceptionDescription")
        private String exceptionDescription;

        @ApiModelProperty(value = "影响对象/涉及单位")
        @JsonProperty("affectedObject")
        private String affectedObject;

        @ApiModelProperty(value = "数据来源")
        private String source;

        @ApiModelProperty(value = "影响记录数")
        @JsonProperty("affectedRecords")
        private Integer affectedRecords;

        @ApiModelProperty(value = "影响分析")
        private String impact;

        @ApiModelProperty(value = "解决方案")
        private String solution;

        @ApiModelProperty(value = "状态: 1-待处理 2-处理中 3-已修复 4-已忽略")
        private Integer status;

        @ApiModelProperty(value = "发现时间")
        @JsonProperty("detectTime")
        private String detectTime;

        @ApiModelProperty(value = "处理时间")
        @JsonProperty("handleTime")
        private String handleTime;

        @ApiModelProperty(value = "处理人")
        private String handler;

        @ApiModelProperty(value = "整改时间")
        @JsonProperty("fixTime")
        private String fixTime;

        @ApiModelProperty(value = "整改人")
        @JsonProperty("fixOperator")
        private String fixOperator;

        @ApiModelProperty(value = "整改结果")
        @JsonProperty("fixResult")
        private String fixResult;

        @ApiModelProperty(value = "创建时间")
        @JsonProperty("createTime")
        private String createTime;
    }

    /**
     * 异常搜索参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "异常搜索参数", description = "异常列表搜索筛选参数")
    public static class ExceptionSearchVO implements Serializable {
        @ApiModelProperty(value = "异常标题")
        @JsonProperty("exceptionTitle")
        private String exceptionTitle;

        @ApiModelProperty(value = "异常类型")
        @JsonProperty("exceptionType")
        private Integer exceptionType;

        @ApiModelProperty(value = "异常级别")
        @JsonProperty("exceptionLevel")
        private Integer exceptionLevel;

        @ApiModelProperty(value = "状态")
        private Integer status;

        @ApiModelProperty(value = "影响对象")
        @JsonProperty("affectedObject")
        private String affectedObject;

        @ApiModelProperty(value = "责任人/处理人")
        private String assignee;

        @ApiModelProperty(value = "发现时间范围")
        @JsonProperty("dateRange")
        private List<String> dateRange;

        @ApiModelProperty(value = "页码", example = "1")
        @JsonProperty("pageNum")
        private Integer pageNum = 1;

        @ApiModelProperty(value = "页大小", example = "10")
        @JsonProperty("pageSize")
        private Integer pageSize = 10;
    }

    /**
     * 统计数据VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "统计数据", description = "异常统计汇总信息")
    public static class HealthCheckStatisticsVO implements Serializable {
        @ApiModelProperty(value = "总体检数")
        @JsonProperty("totalChecks")
        private Integer totalChecks;

        @ApiModelProperty(value = "已完成体检数")
        @JsonProperty("completedChecks")
        private Integer completedChecks;

        @ApiModelProperty(value = "失败体检数")
        @JsonProperty("failedChecks")
        private Integer failedChecks;

        @ApiModelProperty(value = "总异常数")
        @JsonProperty("totalExceptions")
        private Integer totalExceptions;

        @ApiModelProperty(value = "高级别异常数")
        @JsonProperty("highLevelExceptions")
        private Integer highLevelExceptions;

        @ApiModelProperty(value = "已修复异常数")
        @JsonProperty("fixedExceptions")
        private Integer fixedExceptions;

        @ApiModelProperty(value = "体检类型统计")
        @JsonProperty("checkTypeStats")
        private List<CheckTypeStatVO> checkTypeStats;

        @ApiModelProperty(value = "异常趋势")
        @JsonProperty("exceptionTrend")
        private List<ExceptionTrendVO> exceptionTrend;
    }

    /**
     * 体检类型统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "体检类型统计", description = "按类型的统计信息")
    public static class CheckTypeStatVO implements Serializable {
        @ApiModelProperty(value = "类型")
        private Integer type;

        @ApiModelProperty(value = "体检数量")
        private Integer count;

        @ApiModelProperty(value = "异常数量")
        @JsonProperty("exceptionCount")
        private Integer exceptionCount;
    }

    /**
     * 异常趋势VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "异常趋势", description = "异常趋势数据")
    public static class ExceptionTrendVO implements Serializable {
        @ApiModelProperty(value = "日期")
        private String date;

        @ApiModelProperty(value = "异常数量")
        private Integer count;
    }

    /**
     * 批量整改参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "批量整改参数", description = "批量整改操作参数")
    public static class BatchRemediationVO implements Serializable {
        @ApiModelProperty(value = "异常ID列表", required = true)
        @NotEmpty(message = "异常ID列表不能为空")
        @JsonProperty("exceptionIds")
        private List<Long> exceptionIds;

        @ApiModelProperty(value = "整改计划", required = true)
        @NotNull(message = "整改计划不能为空")
        @Valid
        @JsonProperty("remediationPlan")
        private RemediationPlanVO remediationPlan;
    }

    /**
     * 整改计划VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "整改计划", description = "整改计划详情")
    public static class RemediationPlanVO implements Serializable {
        @ApiModelProperty(value = "整改描述", required = true)
        @NotBlank(message = "整改描述不能为空")
        private String description;

        @ApiModelProperty(value = "预计时间", required = true)
        @NotBlank(message = "预计时间不能为空")
        @JsonProperty("estimatedTime")
        private String estimatedTime;

        @ApiModelProperty(value = "指派人员")
        private String assignee;

        @ApiModelProperty(value = "优先级: 1-低 2-中 3-高")
        private Integer priority;
    }

    /**
     * 批量整改结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "批量整改结果", description = "批量整改操作结果")
    public static class BatchRemediationResultVO implements Serializable {
        @ApiModelProperty(value = "成功数量")
        @JsonProperty("successCount")
        private Integer successCount;

        @ApiModelProperty(value = "失败数量")
        @JsonProperty("failedCount")
        private Integer failedCount;

        @ApiModelProperty(value = "批次ID")
        @JsonProperty("batchId")
        private String batchId;
    }

    /**
     * 导出配置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RemediationParamVO implements Serializable {
        @ApiModelProperty(value = "是否包含图表")
        @JsonProperty("remediationData")
        private RemediationDataVO remediationData;
    }

    /**
     * 单项整改参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "单项整改参数", description = "单项整改操作参数")
    public static class RemediationDataVO implements Serializable {
        @ApiModelProperty(value = "整改描述", required = true)
        @NotBlank(message = "整改描述不能为空")
        private String description;

        @ApiModelProperty(value = "解决方案", required = true)
        @NotBlank(message = "解决方案不能为空")
        private String solution;

        @ApiModelProperty(value = "附件列表")
        private List<String> attachments;

        @ApiModelProperty(value = "预计时间", required = true)
        @NotBlank(message = "预计时间不能为空")
        @JsonProperty("estimatedTime")
        private String estimatedTime;
    }

    /**
     * 整改结果提交VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "整改结果提交", description = "整改结果提交参数")
    public static class RemediationResultVO implements Serializable {
        @ApiModelProperty(value = "状态: 1-待处理 2-处理中 3-已修复 4-已关闭", required = true)
        @NotNull(message = "状态不能为空")
        private Integer status;

        @ApiModelProperty(value = "整改描述", required = true)
        @NotBlank(message = "整改描述不能为空")
        private String description;

        @ApiModelProperty(value = "实际用时", required = true)
        @NotBlank(message = "实际用时不能为空")
        @JsonProperty("actualTime")
        private String actualTime;

        @ApiModelProperty(value = "附件列表")
        private List<String> attachments;

        @ApiModelProperty(value = "下一步骤")
        @JsonProperty("nextSteps")
        private String nextSteps;
    }

    /**
     * 批量复检参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "批量复检参数", description = "批量复检操作参数")
    public static class BatchRecheckVO implements Serializable {
        @ApiModelProperty(value = "异常ID列表", required = true)
        @NotEmpty(message = "异常ID列表不能为空")
        @JsonProperty("exceptionIds")
        private List<Long> exceptionIds;

        @ApiModelProperty(value = "复检配置")
        @JsonProperty("recheckConfig")
        private RecheckConfigVO recheckConfig;
    }

    /**
     * 复检配置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "复检配置", description = "复检配置参数")
    public static class RecheckConfigVO implements Serializable {
        @ApiModelProperty(value = "检查类型列表")
        @JsonProperty("checkTypes")
        private List<Integer> checkTypes;

        @ApiModelProperty(value = "检查范围")
        private String scope;

        @ApiModelProperty(value = "优先级: 1-低 2-中 3-高")
        private Integer priority;
    }

    /**
     * 复检结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "复检结果", description = "复检操作结果")
    public static class RecheckResultVO implements Serializable {
        @ApiModelProperty(value = "任务ID")
        @JsonProperty("taskId")
        private Long taskId;

        @ApiModelProperty(value = "成功数量")
        @JsonProperty("successCount")
        private Integer successCount;

        @ApiModelProperty(value = "预计持续时间(秒)")
        @JsonProperty("estimatedDuration")
        private Integer estimatedDuration;
    }

    /**
     * 导出参数VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "导出参数", description = "异常结果导出参数")
    public static class ExportParamsVO implements Serializable {
        @ApiModelProperty(value = "导出格式: excel, pdf, json", required = true)
        @NotBlank(message = "导出格式不能为空")
        private String format;

        @ApiModelProperty(value = "筛选条件")
        private ExportFiltersVO filters;

        @ApiModelProperty(value = "导出配置")
        @JsonProperty("exportConfig")
        private ExportConfigVO exportConfig;
    }

    /**
     * 导出筛选条件VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "导出筛选条件", description = "导出数据筛选条件")
    public static class ExportFiltersVO implements Serializable {
        @ApiModelProperty(value = "异常类型列表")
        @JsonProperty("exceptionTypes")
        private List<Integer> exceptionTypes;

        @ApiModelProperty(value = "严重级别列表")
        @JsonProperty("severityLevels")
        private List<Integer> severityLevels;

        @ApiModelProperty(value = "状态列表")
        @JsonProperty("statusList")
        private List<Integer> statusList;

        @ApiModelProperty(value = "日期范围")
        @JsonProperty("dateRange")
        private List<String> dateRange;

        @ApiModelProperty(value = "是否包含已修复项")
        @JsonProperty("includeFixedItems")
        private Boolean includeFixedItems;
    }

    /**
     * 导出配置VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "导出配置", description = "导出配置选项")
    public static class ExportConfigVO implements Serializable {
        @ApiModelProperty(value = "是否包含图表")
        @JsonProperty("includeCharts")
        private Boolean includeCharts;

        @ApiModelProperty(value = "是否包含摘要")
        @JsonProperty("includeSummary")
        private Boolean includeSummary;

        @ApiModelProperty(value = "模板")
        private String template;
    }

    /**
     * 导出结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "导出结果", description = "导出操作结果")
    public static class ExportResultVO implements Serializable {
        @ApiModelProperty(value = "下载链接")
        @JsonProperty("downloadUrl")
        private String downloadUrl;

        @ApiModelProperty(value = "文件名")
        private String filename;

        @ApiModelProperty(value = "文件大小(字节)")
        @JsonProperty("fileSize")
        private Long fileSize;

        @ApiModelProperty(value = "过期时间")
        @JsonProperty("expiresAt")
        private String expiresAt;
    }

    /**
     * 影响单位排行VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "影响单位排行", description = "影响单位统计排行")
    public static class AffectedUnitsRankingVO implements Serializable {
        @ApiModelProperty(value = "单位列表")
        private List<UnitStatsVO> units;
    }

    /**
     * 单位统计VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "单位统计", description = "单位异常统计信息")
    public static class UnitStatsVO implements Serializable {
        @ApiModelProperty(value = "单位名称")
        @JsonProperty("unitName")
        private String unitName;

        @ApiModelProperty(value = "总异常数")
        @JsonProperty("totalExceptions")
        private Integer totalExceptions;

        @ApiModelProperty(value = "高级别异常数")
        @JsonProperty("highLevelExceptions")
        private Integer highLevelExceptions;

        @ApiModelProperty(value = "平均修复时间(天)")
        @JsonProperty("avgFixTime")
        private Double avgFixTime;

        @ApiModelProperty(value = "修复率(%)")
        @JsonProperty("fixRate")
        private Double fixRate;
    }

    /**
     * 通用操作结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "操作结果", description = "通用操作结果")
    public static class OperationResultVO implements Serializable {
        @ApiModelProperty(value = "是否成功")
        private Boolean success;

        @ApiModelProperty(value = "消息")
        private String message;

        @ApiModelProperty(value = "数据")
        private Object data;
    }

    /**
     * 分页结果VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "分页结果", description = "分页查询结果")
    public static class PagedResultVO<T> implements Serializable {
        @ApiModelProperty(value = "数据列表")
        private List<T> data;

        @ApiModelProperty(value = "总记录数")
        private Long total;

        @ApiModelProperty(value = "页码")
        @JsonProperty("pageNum")
        private Integer pageNum;

        @ApiModelProperty(value = "页大小")
        @JsonProperty("pageSize")
        private Integer pageSize;

        @ApiModelProperty(value = "总页数")
        @JsonProperty("totalPages")
        private Integer totalPages;
    }
}