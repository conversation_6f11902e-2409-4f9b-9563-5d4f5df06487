package com.goodsogood.ows.model.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 荣誉审核VO类
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
public class HonorAuditVO {

    /**
     * 申报ID
     */
    @NotNull(message = "申报ID不能为空")
    private Long id;

    /**
     * 审核状态：2-已通过，3-已驳回
     */
    @NotNull(message = "审核状态不能为空")
    private Integer status;

    /**
     * 审核意见
     */
    private String opinion;
}