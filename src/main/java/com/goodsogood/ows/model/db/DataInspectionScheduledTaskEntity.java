package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检定时任务实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_scheduled_tasks")
public class DataInspectionScheduledTaskEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 任务名称
     */
    @Column(name = "task_name", nullable = false, length = 100)
    private String taskName;

    /**
     * 任务类型
     */
    @Column(name = "task_type")
    private Integer taskType;

    /**
     * 检查类型数组(JSON格式存储)
     */
    @Column(name = "check_types")
    private String checkTypes;

    /**
     * Cron表达式
     */
    @Column(name = "cron_expression", length = 100)
    private String cronExpression;

    /**
     * 定时描述
     */
    @Column(name = "cron_description", length = 200)
    private String cronDescription;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled;

    /**
     * 任务状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 最后执行时间
     */
    @Column(name = "last_execute_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastExecuteTime;

    /**
     * 下次执行时间
     */
    @Column(name = "next_execute_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextExecuteTime;

    /**
     * 执行次数
     */
    @Column(name = "execution_count")
    private Integer executionCount;

    /**
     * 成功次数
     */
    @Column(name = "success_count")
    private Integer successCount;

    /**
     * 失败次数
     */
    @Column(name = "failed_count")
    private Integer failedCount;

    /**
     * 平均执行时长(秒)
     */
    @Column(name = "avg_duration_seconds")
    private Integer avgDurationSeconds;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "creator", length = 50)
    private String creator;

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        DATA_COMPLETENESS(1, "数据完整性检查"),
        DATA_CONSISTENCY(2, "数据一致性检查"),
        DATA_ACCURACY(3, "数据准确性检查"),
        DATA_SECURITY(4, "数据安全性检查");

        private final int code;
        private final String description;

        TaskType(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskType fromCode(Integer code) {
            if (code == null) {
                return DATA_COMPLETENESS;
            }
            for (TaskType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return DATA_COMPLETENESS;
        }
    }

    /**
     * 检查类型枚举
     */
    public enum CheckType {
        COMPLETENESS(1, "完整性"),
        CONSISTENCY(2, "一致性"),
        ACCURACY(3, "准确性"),
        SECURITY(4, "安全性");

        private final int code;
        private final String description;

        CheckType(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        WAITING(1, "待执行"),
        RUNNING(2, "执行中"),
        COMPLETED(3, "已完成"),
        FAILED(4, "执行失败");

        private final int code;
        private final String description;

        TaskStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskStatus fromCode(Integer code) {
            if (code == null) {
                return WAITING;
            }
            for (TaskStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return WAITING;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (taskType == null) {
            taskType = TaskType.DATA_COMPLETENESS.getCode();
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
        if (status == null) {
            status = TaskStatus.WAITING.getCode();
        }
        if (executionCount == null) {
            executionCount = 0;
        }
        if (successCount == null) {
            successCount = 0;
        }
        if (failedCount == null) {
            failedCount = 0;
        }
        if (avgDurationSeconds == null) {
            avgDurationSeconds = 0;
        }
        if (checkTypes == null) {
            checkTypes = "[1]"; // 默认完整性检查
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}