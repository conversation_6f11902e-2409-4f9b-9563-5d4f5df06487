package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检任务执行日志实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_task_logs")
public class DataInspectionTaskLogEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 执行批次ID
     */
    @Column(name = "execution_id", nullable = false, length = 50)
    private String executionId;

    /**
     * 日志级别
     */
    @Column(name = "log_level", length = 10)
    private String logLevel;

    /**
     * 日志内容
     */
    @Column(name = "log_message", nullable = false, columnDefinition = "TEXT")
    private String logMessage;

    /**
     * 日志时间
     */
    @Column(name = "log_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date logTime;

    /**
     * 线程名称
     */
    @Column(name = "thread_name", length = 50)
    private String threadName;

    /**
     * 类名
     */
    @Column(name = "class_name", length = 200)
    private String className;

    /**
     * 日志级别枚举
     */
    public enum LogLevel {
        DEBUG("DEBUG", "调试"),
        INFO("INFO", "信息"),
        WARN("WARN", "警告"),
        ERROR("ERROR", "错误");

        private final String code;
        private final String description;

        LogLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LogLevel fromCode(String code) {
            if (code == null) {
                return INFO;
            }
            for (LogLevel level : values()) {
                if (level.code.equals(code)) {
                    return level;
                }
            }
            return INFO;
        }
    }

    @PrePersist
    protected void onCreate() {
        if (logTime == null) {
            logTime = new Date();
        }
        if (logLevel == null) {
            logLevel = LogLevel.INFO.getCode();
        }
    }
}