package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 整改记录实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_remediation_records")
public class RemediationRecordEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 异常ID
     */
    @Column(name = "exception_id", nullable = false)
    private Long exceptionId;

    /**
     * 异常标题
     */
    @Column(name = "exception_title", length = 200)
    private String exceptionTitle;

    /**
     * 整改动作
     */
    @Column(name = "remediation_action", length = 200)
    private String remediationAction;

    /**
     * 整改详情
     */
    @Column(name = "remediation_detail", columnDefinition = "TEXT")
    private String remediationDetail;

    /**
     * 操作人
     */
    @Column(name = "operator", length = 100)
    private String operator;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date completeTime;

    /**
     * 状态
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 验证结果
     */
    @Column(name = "verification_result", length = 20)
    private String verificationResult;

    /**
     * 验证时间
     */
    @Column(name = "verification_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date verificationTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 整改状态枚举
     */
    public enum RemediationStatus {
        IN_PROGRESS("in_progress", "进行中"),
        COMPLETED("completed", "已完成"),
        CANCELLED("cancelled", "已取消");

        private final String code;
        private final String description;

        RemediationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 验证结果枚举
     */
    public enum VerificationResult {
        PASSED("passed", "通过"),
        FAILED("failed", "失败");

        private final String code;
        private final String description;

        VerificationResult(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = new Date();
        }
        if (updateTime == null) {
            updateTime = new Date();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}