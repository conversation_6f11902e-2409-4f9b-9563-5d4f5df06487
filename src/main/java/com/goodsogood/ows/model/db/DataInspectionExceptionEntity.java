package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检异常详情实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_exception_details")
public class DataInspectionExceptionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 异常类型
     */
    @Column(name = "type", nullable = false, length = 50)
    private String type;

    /**
     * 异常标题
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 异常描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 所属单位
     */
    @Column(name = "unit", length = 100)
    private String unit;

    /**
     * 数据源系统名称
     */
    @Column(name = "source", length = 100)
    private String source;

    /**
     * 异常严重程度
     */
    @Column(name = "severity", length = 20)
    private String severity;

    /**
     * 处理状态
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 发现时间
     */
    @Column(name = "detect_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date detectTime;

    /**
     * 受影响记录数
     */
    @Column(name = "affected_records")
    private Integer affectedRecords;

    /**
     * 影响描述
     */
    @Column(name = "impact", columnDefinition = "TEXT")
    private String impact;

    /**
     * 建议解决方案
     */
    @Column(name = "solution", columnDefinition = "TEXT")
    private String solution;

    /**
     * 处理时间
     */
    @Column(name = "handle_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date handleTime;

    /**
     * 处理人员
     */
    @Column(name = "handler", length = 50)
    private String handler;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 异常类型枚举
     */
    public enum ExceptionType {
        DATA_MISSING("数据缺失"),
        DATA_FORMAT_ERROR("数据格式错误"),
        DATA_DUPLICATE("数据重复"),
        DATA_INCONSISTENT("数据不一致"),
        DATA_EXPIRED("数据过期"),
        PERMISSION_ERROR("权限异常");

        private final String description;

        ExceptionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 严重程度枚举
     */
    public enum SeverityLevel {
        LOW("low", "低"),
        MEDIUM("medium", "中"),
        HIGH("high", "高");

        private final String code;
        private final String description;

        SeverityLevel(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 处理状态枚举
     */
    public enum ProcessStatus {
        PENDING("pending", "待处理"),
        IN_REMEDIATION("in_remediation", "整改中"),
        RESOLVED("resolved", "已解决");

        private final String code;
        private final String description;

        ProcessStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (severity == null) {
            severity = SeverityLevel.MEDIUM.getCode();
        }
        if (status == null) {
            status = ProcessStatus.PENDING.getCode();
        }
        if (affectedRecords == null) {
            affectedRecords = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}