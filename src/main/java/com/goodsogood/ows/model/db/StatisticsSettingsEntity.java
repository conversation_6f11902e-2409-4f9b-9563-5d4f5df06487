package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 统计分析设置实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_settings")
public class StatisticsSettingsEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 刷新间隔 (秒)
     */
    @Column(name = "refresh_interval")
    private Integer refreshInterval;

    /**
     * 默认时间范围
     */
    @Column(name = "default_time_range", length = 50)
    private String defaultTimeRange;

    /**
     * 显示指标 (JSON格式存储)
     */
    @Column(name = "display_metrics", columnDefinition = "TEXT")
    private String displayMetrics;

    /**
     * 告警阈值 (JSON格式存储)
     */
    @Column(name = "alert_thresholds", columnDefinition = "TEXT")
    private String alertThresholds;

    /**
     * 仪表板配置 (JSON格式存储)
     */
    @Column(name = "dashboard_config", columnDefinition = "TEXT")
    private String dashboardConfig;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建人ID
     */
    @Column(name = "create_user")
    private Long createUser;

    /**
     * 创建人姓名
     */
    @Column(name = "create_user_name", length = 50)
    private String createUserName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新人ID
     */
    @Column(name = "update_user")
    private Long updateUser;

    /**
     * 更新人姓名
     */
    @Column(name = "update_user_name", length = 50)
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (refreshInterval == null) {
            refreshInterval = 300; // 默认5分钟
        }
        if (defaultTimeRange == null) {
            defaultTimeRange = "30d"; // 默认30天
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
