package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 统计分析趋势分析实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_trend")
public class StatisticsTrendEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 统计日期
     */
    @Column(name = "date")
    @Temporal(TemporalType.DATE)
    private Date date;

    /**
     * 趋势类型：daily-日度，weekly-周度，monthly-月度，yearly-年度
     */
    @Column(name = "trend_type", length = 20)
    private String trendType;

    /**
     * 文档数量
     */
    @Column(name = "document_count")
    private Integer documentCount;

    /**
     * 浏览数量
     */
    @Column(name = "view_count")
    private Long viewCount;

    /**
     * 下载数量
     */
    @Column(name = "download_count")
    private Long downloadCount;

    /**
     * 用户数量
     */
    @Column(name = "user_count")
    private Integer userCount;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 趋势类型枚举
     */
    public enum TrendType {
        DAILY("daily", "日度"),
        WEEKLY("weekly", "周度"),
        MONTHLY("monthly", "月度"),
        YEARLY("yearly", "年度");

        private final String code;
        private final String description;

        TrendType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TrendType fromCode(String code) {
            if (code == null) {
                return DAILY;
            }
            for (TrendType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return DAILY;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (date == null) {
            date = now;
        }
        if (trendType == null) {
            trendType = TrendType.DAILY.getCode();
        }
        if (documentCount == null) {
            documentCount = 0;
        }
        if (viewCount == null) {
            viewCount = 0L;
        }
        if (downloadCount == null) {
            downloadCount = 0L;
        }
        if (userCount == null) {
            userCount = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
