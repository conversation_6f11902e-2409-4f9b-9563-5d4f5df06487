package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据体检任务执行历史实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_task_execution_history")
public class DataInspectionTaskExecutionHistoryEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 任务ID
     */
    @Column(name = "task_id", nullable = false)
    private Long taskId;

    /**
     * 执行批次ID
     */
    @Column(name = "execution_id", nullable = false, length = 50)
    private String executionId;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 执行时间(秒)
     */
    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    /**
     * 执行状态
     */
    @Column(name = "status", nullable = false)
    private Integer status;

    /**
     * 总记录数
     */
    @Column(name = "total_records")
    private Integer totalRecords = 0;

    /**
     * 异常数量
     */
    @Column(name = "exception_count")
    private Integer exceptionCount = 0;

    /**
     * 异常率
     */
    @Column(name = "exception_rate", precision = 5, scale = 4)
    private BigDecimal exceptionRate = BigDecimal.ZERO;

    /**
     * 结果摘要(JSON格式)
     */
    @Column(name = "result_summary", columnDefinition = "JSONB")
    private String resultSummary;

    /**
     * 执行进度（百分比）
     */
    @Column(name = "progress")
    private Integer progress = 0;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 执行状态枚举
     */
    public enum ExecutionStatus {
        RUNNING(1, "运行中"),
        SUCCESS(2, "成功"),
        FAILED(3, "失败"),
        TIMEOUT(4, "超时");

        private final int code;
        private final String description;

        ExecutionStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ExecutionStatus fromCode(Integer code) {
            if (code == null) {
                return RUNNING;
            }
            for (ExecutionStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return RUNNING;
        }
    }

    /**
     * 便捷方法：获取持续时间
     */
    public Integer getDuration() {
        return this.durationSeconds;
    }

    /**
     * 便捷方法：设置持续时间
     */
    public void setDuration(Integer duration) {
        this.durationSeconds = duration;
    }

    /**
     * 便捷方法：获取总检查记录数
     */
    public Integer getTotalChecked() {
        return this.totalRecords;
    }

    /**
     * 便捷方法：设置总检查记录数
     */
    public void setTotalChecked(Integer totalChecked) {
        this.totalRecords = totalChecked;
    }

    /**
     * 便捷方法：获取发现的异常数量
     */
    public Integer getExceptionsFound() {
        return this.exceptionCount;
    }

    /**
     * 便捷方法：设置发现的异常数量
     */
    public void setExceptionsFound(Integer exceptionsFound) {
        this.exceptionCount = exceptionsFound;
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (totalRecords == null) {
            totalRecords = 0;
        }
        if (exceptionCount == null) {
            exceptionCount = 0;
        }
        if (exceptionRate == null) {
            exceptionRate = BigDecimal.ZERO;
        }
        if (status == null) {
            status = ExecutionStatus.RUNNING.getCode();
        }
        if (progress == null) {
            progress = 0;
        }
    }
}