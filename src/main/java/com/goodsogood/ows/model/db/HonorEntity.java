package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 荣誉申报实体类
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HonorEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 申报人
     */
    @NotBlank(message = "申报人不能为空")
    private String applicant;

    /**
     * 所属部门
     */
    @NotBlank(message = "所属部门不能为空")
    private String department;

    /**
     * 申报类型：1-优秀共产党员，2-优秀党务工作者，3-先进基层党组织，4-先进工作者，5-模范机关标兵单位
     */
    @NotNull(message = "申报类型不能为空")
    private Integer type;

    /**
     * 申报材料URL
     */
    @NotBlank(message = "申报材料不能为空")
    private String materialUrl;

    /**
     * 申报说明
     */
    private String description;

    /**
     * 状态：1-待审核，2-已通过，3-已驳回
     */
    private Integer status;

    /**
     * 审核意见
     */
    private String auditOpinion;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    // 申报类型枚举
    public enum Type {
        EXCELLENT_PARTY_MEMBER(1, "优秀共产党员"),
        EXCELLENT_PARTY_WORKER(2, "优秀党务工作者"),
        ADVANCED_PARTY_ORGANIZATION(3, "先进基层党组织"),
        ADVANCED_WORKER(4, "先进工作者"),
        MODEL_ORGANIZATION(5, "模范机关标兵单位");

        private final int code;
        private final String name;

        Type(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(int code) {
            for (Type type : Type.values()) {
                if (type.getCode() == code) {
                    return type.getName();
                }
            }
            return "未知";
        }
    }

    // 状态枚举
    public enum Status {
        PENDING(1, "待审核"),
        APPROVED(2, "已通过"),
        REJECTED(3, "已驳回");

        private final int code;
        private final String name;

        Status(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(int code) {
            for (Status status : Status.values()) {
                if (status.getCode() == code) {
                    return status.getName();
                }
            }
            return "未知";
        }
    }

    /**
     * 获取申报类型名称
     */
    public String getTypeName() {
        return Type.getNameByCode(this.type);
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        return Status.getNameByCode(this.status);
    }

    /**
     * 兼容旧版字段 - 获取创建时间字符串
     */
    public String getCreatedAt() {
        if (createTime != null) {
            return createTime.toString();
        }
        return null;
    }
}