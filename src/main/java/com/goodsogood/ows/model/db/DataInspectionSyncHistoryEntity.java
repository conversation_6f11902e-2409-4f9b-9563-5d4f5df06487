package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检数据源同步历史实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_sync_history")
public class DataInspectionSyncHistoryEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 数据源ID
     */
    @Column(name = "data_source_id", nullable = false)
    private Long dataSourceId;

    /**
     * 同步类型
     */
    @Column(name = "sync_type", length = 20)
    private String syncType;

    /**
     * 同步状态
     */
    @Column(name = "sync_status", nullable = false, length = 20)
    private String syncStatus;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 持续时间(秒)
     */
    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    /**
     * 同步记录数
     */
    @Column(name = "sync_records")
    private Integer syncRecords = 0;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 操作人
     */
    @Column(name = "operator", length = 50)
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 同步类型枚举
     */
    public enum SyncType {
        MANUAL("manual", "手动同步"),
        SCHEDULED("scheduled", "定时同步");

        private final String code;
        private final String description;

        SyncType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static SyncType fromCode(String code) {
            if (code == null) {
                return MANUAL;
            }
            for (SyncType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MANUAL;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SUCCESS("success", "同步成功"),
        FAILED("failed", "同步失败"),
        RUNNING("running", "运行中");

        private final String code;
        private final String description;

        SyncStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static SyncStatus fromCode(String code) {
            if (code == null) {
                return RUNNING;
            }
            for (SyncStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return RUNNING;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (syncType == null) {
            syncType = SyncType.MANUAL.getCode();
        }
        if (syncRecords == null) {
            syncRecords = 0;
        }
    }
}