package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检用户权限实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_user_permissions")
public class DataInspectionUserPermissionEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false, unique = true)
    private Long userId;

    /**
     * 是否可以查看体检
     */
    @Column(name = "can_view_inspection")
    private Boolean canViewInspection;

    /**
     * 是否可以配置规则
     */
    @Column(name = "can_configure_rules")
    private Boolean canConfigureRules;

    /**
     * 是否可以处理异常
     */
    @Column(name = "can_handle_exceptions")
    private Boolean canHandleExceptions;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (canViewInspection == null) {
            canViewInspection = false;
        }
        if (canConfigureRules == null) {
            canConfigureRules = false;
        }
        if (canHandleExceptions == null) {
            canHandleExceptions = false;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}