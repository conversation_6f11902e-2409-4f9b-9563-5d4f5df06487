package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计分析实时数据实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_real_time")
public class StatisticsRealTimeEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 当前在线用户数
     */
    @Column(name = "current_online_users")
    private Integer currentOnlineUsers;

    /**
     * 今日浏览量
     */
    @Column(name = "today_views")
    private Long todayViews;

    /**
     * 今日下载量
     */
    @Column(name = "today_downloads")
    private Long todayDownloads;

    /**
     * 最近活动 (JSON格式存储)
     */
    @Column(name = "recent_activities", columnDefinition = "TEXT")
    private String recentActivities;

    /**
     * 服务器负载
     */
    @Column(name = "server_load", precision = 5, scale = 2)
    private BigDecimal serverLoad;

    /**
     * 响应时间 (毫秒)
     */
    @Column(name = "response_time")
    private Integer responseTime;

    /**
     * 错误率
     */
    @Column(name = "error_rate", length = 10)
    private String errorRate;

    /**
     * 时间戳
     */
    @Column(name = "timestamp")
    @Temporal(TemporalType.TIMESTAMP)
    private Date timestamp;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (timestamp == null) {
            timestamp = now;
        }
        if (currentOnlineUsers == null) {
            currentOnlineUsers = 0;
        }
        if (todayViews == null) {
            todayViews = 0L;
        }
        if (todayDownloads == null) {
            todayDownloads = 0L;
        }
        if (serverLoad == null) {
            serverLoad = BigDecimal.ZERO;
        }
        if (responseTime == null) {
            responseTime = 0;
        }
        if (errorRate == null) {
            errorRate = "0%";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
