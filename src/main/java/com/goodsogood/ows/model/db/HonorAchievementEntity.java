package com.goodsogood.ows.model.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 荣誉成果实体类
 * 
 * <AUTHOR>
 * @date 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HonorAchievementEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 荣誉名称
     */
    @NotBlank(message = "荣誉名称不能为空")
    private String name;

    /**
     * 类型：1-奖项，2-证书，3-专利，4-论文，5-其他
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 级别：1-国际级，2-国家级，3-省级，4-市级，5-区县级，6-单位内部
     */
    @NotNull(message = "级别不能为空")
    private Integer level;

    /**
     * 颁发单位
     */
    private String issueOrg;

    /**
     * 获得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date achieveTime;

    /**
     * 封面图片URL
     */
    private String coverUrl;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 详细描述（富文本）
     */
    private String description;

    /**
     * 附件JSON
     */
    private String attachmentsJson;

    /**
     * 下载次数
     */
    private Integer downloadCount = 0;

    /**
     * 查看次数
     */
    private Integer viewCount = 0;

    /**
     * 是否公开：0-不公开，1-公开
     */
    private Boolean isPublic = true;

    /**
     * 是否允许下载：0-不允许，1-允许
     */
    private Boolean allowDownload = true;

    /**
     * 状态：0-删除，1-正常，2-草稿
     */
    private Integer status = 1;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 创建人ID
     */
    private Long createUser;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updateUser;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    // 类型枚举
    public enum Type {
        AWARD(1, "奖项"),
        CERTIFICATE(2, "证书"),
        PATENT(3, "专利"),
        PAPER(4, "论文"),
        OTHER(5, "其他");

        private final int code;
        private final String name;

        Type(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(int code) {
            for (Type type : Type.values()) {
                if (type.getCode() == code) {
                    return type.getName();
                }
            }
            return "未知";
        }
    }

    // 级别枚举
    public enum Level {
        INTERNATIONAL(1, "国际级"),
        NATIONAL(2, "国家级"),
        PROVINCIAL(3, "省级"),
        MUNICIPAL(4, "市级"),
        COUNTY(5, "区县级"),
        INTERNAL(6, "单位内部");

        private final int code;
        private final String name;

        Level(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(int code) {
            for (Level level : Level.values()) {
                if (level.getCode() == code) {
                    return level.getName();
                }
            }
            return "未知";
        }
    }

    // 状态枚举
    public enum Status {
        DELETED(0, "已删除"),
        NORMAL(1, "正常"),
        DRAFT(2, "草稿");

        private final int code;
        private final String name;

        Status(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static String getNameByCode(int code) {
            for (Status status : Status.values()) {
                if (status.getCode() == code) {
                    return status.getName();
                }
            }
            return "未知";
        }
    }

    /**
     * 获取类型名称
     */
    public String getTypeName() {
        return Type.getNameByCode(this.type);
    }

    /**
     * 获取级别名称
     */
    public String getLevelName() {
        return Level.getNameByCode(this.level);
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        return Status.getNameByCode(this.status);
    }
}
