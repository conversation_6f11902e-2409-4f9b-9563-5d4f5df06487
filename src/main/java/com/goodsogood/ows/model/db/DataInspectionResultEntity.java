package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 数据体检结果实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_results")
public class DataInspectionResultEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    @Column(name = "inspection_name")
    private String inspectionName;

    /**
     * 体检日期
     */
    @Column(name = "inspection_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date inspectionDate;

    /**
     * 体检类型
     */
    @Column(name = "inspection_type", length = 20)
    private String inspectionType;

    /**
     * 总记录数
     */
    @Column(name = "total_records")
    private Integer totalRecords;

    /**
     * 异常数量
     */
    @Column(name = "exception_count")
    private Integer exceptionCount;

    /**
     * 异常率
     */
    @Column(name = "exception_rate", precision = 5, scale = 4)
    private BigDecimal exceptionRate;

    /**
     * 状态
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 持续时间(秒)
     */
    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    /**
     * 结果摘要(JSON格式)
     */
    @Column(name = "summary", columnDefinition = "JSONB")
    private String summary;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 体检类型枚举 1-党组（党委）设置, 2-党务干部任免, 3-任务体检, 4-用户信息完整
     */
    public enum InspectionType {
        ONE("1", "党组（党委）设置"),
        TWO("2", "党务干部任免"),
        THREE("3", "任务体检"),
        FOUR("4", "用户信息完整");

        private final String code;
        private final String description;

        InspectionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static InspectionType fromCode(String code) {
            if (code == null) {
                return ONE;
            }
            for (InspectionType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return ONE;
        }
    }

    /**
     * 体检状态枚举 1-待执行 2-执行中 3-已完成 4-失败
     */
    public enum InspectionStatus {
        READY("1", "待执行"),
        RUNNING("2", "运行中"),
        COMPLETED("3", "已完成"),
        FAILED("4", "失败");

        private final String code;
        private final String description;

        InspectionStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static InspectionStatus fromCode(String code) {
            if (code == null) {
                return RUNNING;
            }
            for (InspectionStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return RUNNING;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (inspectionDate == null) {
            inspectionDate = now;
        }
        if (inspectionType == null) {
            inspectionType = InspectionType.ONE.getCode();
        }
        if (status == null) {
            status = InspectionStatus.RUNNING.getCode();
        }
        if (totalRecords == null) {
            totalRecords = 0;
        }
        if (exceptionCount == null) {
            exceptionCount = 0;
        }
        if (exceptionRate == null) {
            exceptionRate = BigDecimal.ZERO;
        }
        if (durationSeconds == null) {
            durationSeconds = 0;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}