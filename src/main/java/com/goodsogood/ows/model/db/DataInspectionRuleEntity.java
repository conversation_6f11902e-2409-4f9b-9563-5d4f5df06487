package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检规则实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_rules")
public class DataInspectionRuleEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 规则名称
     */
    @Column(name = "rule_name", nullable = false, length = 100)
    private String ruleName;

    /**
     * 规则类型
     */
    @Column(name = "rule_type", nullable = false, length = 50)
    private String ruleType;

    /**
     * 表名
     */
    @Column(name = "table_name", length = 100)
    private String tableName;

    /**
     * 字段名
     */
    @Column(name = "field_name", length = 100)
    private String fieldName;

    /**
     * 检查逻辑
     */
    @Column(name = "check_logic", columnDefinition = "TEXT")
    private String checkLogic;

    /**
     * 阈值
     */
    @Column(name = "threshold_value", length = 100)
    private String thresholdValue;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Boolean isEnabled;

    /**
     * 严重程度
     */
    @Column(name = "severity", length = 20)
    private String severity;

    /**
     * 规则分类
     */
    @Column(name = "rule_category", nullable = false, length = 50)
    private String ruleCategory;

    /**
     * 规则名称（原name字段保留兼容）
     */
    @Column(name = "name", nullable = false, length = 100)
    private String name;

    /**
     * 规则描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 规则状态
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 创建者
     */
    @Column(name = "creator", length = 50)
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 规则分类枚举
     */
    public enum RuleCategory {
        PARTY_ORGANIZATION("partyOrganization", "党组（党委）设置体检"),
        PARTY_OFFICIALS("partyOfficials", "党务干部任免体检"),
        TASKS("tasks", "任务体检"),
        USER_INFO("userInfo", "用户信息完整体检");

        private final String code;
        private final String description;

        RuleCategory(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 规则状态枚举
     */
    public enum RuleStatus {
        ACTIVE("active", "启用"),
        INACTIVE("inactive", "禁用");

        private final String code;
        private final String description;

        RuleStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (status == null) {
            status = RuleStatus.ACTIVE.getCode();
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
        if (severity == null) {
            severity = "medium";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}