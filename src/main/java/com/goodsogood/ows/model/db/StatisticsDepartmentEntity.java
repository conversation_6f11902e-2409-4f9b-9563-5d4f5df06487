package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计分析部门统计实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_department")
public class StatisticsDepartmentEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 部门名称
     */
    @Column(name = "department", length = 100)
    private String department;

    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 文档数量
     */
    @Column(name = "document_count")
    private Integer documentCount;

    /**
     * 发布率
     */
    @Column(name = "publish_rate", precision = 5, scale = 4)
    private BigDecimal publishRate;

    /**
     * 总浏览量
     */
    @Column(name = "total_views")
    private Long totalViews;

    /**
     * 平均评分
     */
    @Column(name = "avg_score", precision = 3, scale = 2)
    private BigDecimal avgScore;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    @Temporal(TemporalType.DATE)
    private Date statisticsDate;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (statisticsDate == null) {
            statisticsDate = now;
        }
        if (documentCount == null) {
            documentCount = 0;
        }
        if (publishRate == null) {
            publishRate = BigDecimal.ZERO;
        }
        if (totalViews == null) {
            totalViews = 0L;
        }
        if (avgScore == null) {
            avgScore = BigDecimal.ZERO;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
