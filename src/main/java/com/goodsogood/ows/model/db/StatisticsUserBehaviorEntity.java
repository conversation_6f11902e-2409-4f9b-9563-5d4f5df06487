package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计分析用户行为分析实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_user_behavior")
public class StatisticsUserBehaviorEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    @Temporal(TemporalType.DATE)
    private Date statisticsDate;

    /**
     * 高峰时段 (JSON格式存储)
     */
    @Column(name = "peak_hours", columnDefinition = "TEXT")
    private String peakHours;

    /**
     * 平均阅读时间 (分钟)
     */
    @Column(name = "avg_reading_time", precision = 8, scale = 2)
    private BigDecimal avgReadingTime;

    /**
     * 跳出率
     */
    @Column(name = "bounce_rate", precision = 5, scale = 4)
    private BigDecimal bounceRate;

    /**
     * 完成率
     */
    @Column(name = "completion_rate", precision = 5, scale = 4)
    private BigDecimal completionRate;

    /**
     * 设备分析数据 (JSON格式存储)
     */
    @Column(name = "device_analysis", columnDefinition = "TEXT")
    private String deviceAnalysis;

    /**
     * 来源分析数据 (JSON格式存储)
     */
    @Column(name = "source_analysis", columnDefinition = "TEXT")
    private String sourceAnalysis;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (statisticsDate == null) {
            statisticsDate = now;
        }
        if (avgReadingTime == null) {
            avgReadingTime = BigDecimal.ZERO;
        }
        if (bounceRate == null) {
            bounceRate = BigDecimal.ZERO;
        }
        if (completionRate == null) {
            completionRate = BigDecimal.ZERO;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
