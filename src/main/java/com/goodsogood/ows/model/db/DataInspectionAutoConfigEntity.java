package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检自动配置实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_auto_config")
public class DataInspectionAutoConfigEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 配置名称
     */
    @Column(name = "config_name", nullable = false, length = 100)
    private String configName;

    /**
     * 是否启用
     */
    @Column(name = "enabled")
    private Boolean enabled = true;

    /**
     * 执行频率
     */
    @Column(name = "frequency", length = 20)
    private String frequency;

    /**
     * 执行时间
     */
    @Column(name = "execute_time")
    @Temporal(TemporalType.TIME)
    private Date executeTime;

    /**
     * 时区
     */
    @Column(name = "timezone", length = 50)
    private String timezone = "Asia/Shanghai";

    /**
     * 检查范围(JSON格式)
     */
    @Column(name = "inspection_scope", columnDefinition = "JSONB")
    private String inspectionScope;

    /**
     * 检查类型数组
     */
    @Column(name = "check_types")
    private String checkTypes;

    /**
     * 最后运行时间
     */
    @Column(name = "last_run")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastRun;

    /**
     * 下次运行时间
     */
    @Column(name = "next_run")
    @Temporal(TemporalType.TIMESTAMP)
    private Date nextRun;

    /**
     * 状态
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 执行频率枚举
     */
    public enum Frequency {
        DAILY("daily", "每日"),
        WEEKLY("weekly", "每周"),
        MONTHLY("monthly", "每月");

        private final String code;
        private final String description;

        Frequency(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Frequency fromCode(String code) {
            if (code == null) {
                return DAILY;
            }
            for (Frequency freq : values()) {
                if (freq.code.equals(code)) {
                    return freq;
                }
            }
            return DAILY;
        }
    }

    /**
     * 配置状态枚举
     */
    public enum ConfigStatus {
        ACTIVE("active", "激活"),
        INACTIVE("inactive", "停用");

        private final String code;
        private final String description;

        ConfigStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ConfigStatus fromCode(String code) {
            if (code == null) {
                return ACTIVE;
            }
            for (ConfigStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (enabled == null) {
            enabled = true;
        }
        if (timezone == null) {
            timezone = "Asia/Shanghai";
        }
        if (status == null) {
            status = ConfigStatus.ACTIVE.getCode();
        }
        if (checkTypes == null) {
            checkTypes = "{1,2,3,4}";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}