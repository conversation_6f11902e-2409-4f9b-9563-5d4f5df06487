package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检规则详情实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_rule_details")
public class DataInspectionRuleDetailEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 规则ID
     */
    @Column(name = "rule_id", nullable = false)
    private Long ruleId;

    /**
     * 字段名称
     */
    @Column(name = "field_name", nullable = false, length = 100)
    private String fieldName;

    /**
     * 规则类型
     */
    @Column(name = "rule_type", nullable = false, length = 20)
    private String ruleType;

    /**
     * 规则值
     */
    @Column(name = "rule_value", nullable = false, columnDefinition = "TEXT")
    private String ruleValue;

    /**
     * 错误消息
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 规则类型枚举
     */
    public enum RuleType {
        REQUIRED("required", "必填验证"),
        ENUM("enum", "枚举验证"),
        PATTERN("pattern", "正则表达式验证"),
        RANGE("range", "范围验证");

        private final String code;
        private final String description;

        RuleType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = new Date();
        }
    }
}