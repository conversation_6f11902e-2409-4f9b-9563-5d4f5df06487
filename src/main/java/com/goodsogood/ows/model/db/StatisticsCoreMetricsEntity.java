package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计分析核心指标实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_core_metrics")
public class StatisticsCoreMetricsEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    @Temporal(TemporalType.DATE)
    private Date statisticsDate;

    /**
     * 总文档数
     */
    @Column(name = "total_documents")
    private Integer totalDocuments;

    /**
     * 已发布文档数
     */
    @Column(name = "published_documents")
    private Integer publishedDocuments;

    /**
     * 完成率
     */
    @Column(name = "completion_rate", precision = 5, scale = 4)
    private BigDecimal completionRate;

    /**
     * 总主讲人数
     */
    @Column(name = "total_speakers")
    private Integer totalSpeakers;

    /**
     * 总部门数
     */
    @Column(name = "total_departments")
    private Integer totalDepartments;

    /**
     * 平均浏览量
     */
    @Column(name = "average_views", precision = 10, scale = 2)
    private BigDecimal averageViews;

    /**
     * 总下载量
     */
    @Column(name = "total_downloads")
    private Long totalDownloads;

    /**
     * 用户参与度
     */
    @Column(name = "user_engagement", precision = 5, scale = 4)
    private BigDecimal userEngagement;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (statisticsDate == null) {
            statisticsDate = now;
        }
        if (totalDocuments == null) {
            totalDocuments = 0;
        }
        if (publishedDocuments == null) {
            publishedDocuments = 0;
        }
        if (completionRate == null) {
            completionRate = BigDecimal.ZERO;
        }
        if (totalSpeakers == null) {
            totalSpeakers = 0;
        }
        if (totalDepartments == null) {
            totalDepartments = 0;
        }
        if (averageViews == null) {
            averageViews = BigDecimal.ZERO;
        }
        if (totalDownloads == null) {
            totalDownloads = 0L;
        }
        if (userEngagement == null) {
            userEngagement = BigDecimal.ZERO;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
