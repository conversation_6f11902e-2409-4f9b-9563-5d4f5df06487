package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检异常统计实体
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_exception_statistics", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"statistics_type", "statistics_key", "statistics_date"}))
public class DataInspectionExceptionStatisticsEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 统计类型
     */
    @Column(name = "statistics_type", nullable = false, length = 20)
    private String statisticsType;

    /**
     * 统计键值(类型名或单位名)
     */
    @Column(name = "statistics_key", nullable = false, length = 100)
    private String statisticsKey;

    /**
     * 总数量
     */
    @Column(name = "total_count")
    private Integer totalCount = 0;

    /**
     * 高严重程度数量
     */
    @Column(name = "high_severity_count")
    private Integer highSeverityCount = 0;

    /**
     * 中严重程度数量
     */
    @Column(name = "medium_severity_count")
    private Integer mediumSeverityCount = 0;

    /**
     * 低严重程度数量
     */
    @Column(name = "low_severity_count")
    private Integer lowSeverityCount = 0;

    /**
     * 统计描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    @Temporal(TemporalType.DATE)
    private Date statisticsDate;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 统计类型枚举
     */
    public enum StatisticsType {
        BY_TYPE("by_type", "按类型统计"),
        BY_UNIT("by_unit", "按单位统计");

        private final String code;
        private final String description;

        StatisticsType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static StatisticsType fromCode(String code) {
            if (code == null) {
                return BY_TYPE;
            }
            for (StatisticsType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return BY_TYPE;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (statisticsDate == null) {
            statisticsDate = new Date();
        }
        if (totalCount == null) {
            totalCount = 0;
        }
        if (highSeverityCount == null) {
            highSeverityCount = 0;
        }
        if (mediumSeverityCount == null) {
            mediumSeverityCount = 0;
        }
        if (lowSeverityCount == null) {
            lowSeverityCount = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}