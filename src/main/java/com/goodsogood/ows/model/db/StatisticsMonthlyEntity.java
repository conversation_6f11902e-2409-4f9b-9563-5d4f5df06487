package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 统计分析月度统计实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_monthly")
public class StatisticsMonthlyEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 统计月份 (格式: YYYY-MM)
     */
    @Column(name = "month", length = 7)
    private String month;

    /**
     * 文档数量
     */
    @Column(name = "document_count")
    private Integer documentCount;

    /**
     * 发布数量
     */
    @Column(name = "publish_count")
    private Integer publishCount;

    /**
     * 浏览数量
     */
    @Column(name = "view_count")
    private Long viewCount;

    /**
     * 下载数量
     */
    @Column(name = "download_count")
    private Long downloadCount;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (documentCount == null) {
            documentCount = 0;
        }
        if (publishCount == null) {
            publishCount = 0;
        }
        if (viewCount == null) {
            viewCount = 0L;
        }
        if (downloadCount == null) {
            downloadCount = 0L;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
