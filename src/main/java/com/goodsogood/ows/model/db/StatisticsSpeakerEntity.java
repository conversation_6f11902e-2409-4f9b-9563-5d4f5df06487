package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 统计分析主讲人统计实体
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_statistics_speaker")
public class StatisticsSpeakerEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    private Long id;

    /**
     * 主讲人姓名
     */
    @Column(name = "speaker", length = 50)
    private String speaker;

    /**
     * 主讲人ID
     */
    @Column(name = "speaker_id")
    private Long speakerId;

    /**
     * 文档数量
     */
    @Column(name = "document_count")
    private Integer documentCount;

    /**
     * 总浏览量
     */
    @Column(name = "total_views")
    private Long totalViews;

    /**
     * 平均评分
     */
    @Column(name = "avg_rating", precision = 3, scale = 2)
    private BigDecimal avgRating;

    /**
     * 所属部门
     */
    @Column(name = "department", length = 100)
    private String department;

    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 统计日期
     */
    @Column(name = "statistics_date")
    @Temporal(TemporalType.DATE)
    private Date statisticsDate;

    /**
     * 组织ID
     */
    @Column(name = "organization_id")
    private Long organizationId;

    /**
     * 区域ID
     */
    @Column(name = "region_id")
    private Long regionId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (statisticsDate == null) {
            statisticsDate = now;
        }
        if (documentCount == null) {
            documentCount = 0;
        }
        if (totalViews == null) {
            totalViews = 0L;
        }
        if (avgRating == null) {
            avgRating = BigDecimal.ZERO;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}
