package com.goodsogood.ows.model.db;

import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.*;
import java.util.Date;

/**
 * 数据体检数据源实体
 *
 * <AUTHOR>
 * @date 2025-09-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "t_data_inspection_data_sources")
public class DataInspectionDataSourceEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", insertable = false, updatable = false)
    @KeySql(useGeneratedKeys = true)
    private Long id;

    /**
     * 数据源标识
     */
    @Column(name = "source_id", unique = true, nullable = false, length = 50)
    private String sourceId;

    /**
     * 数据源名称
     */
    @Column(name = "source_name", nullable = false, length = 100)
    private String sourceName;

    /**
     * 数据源类型
     */
    @Column(name = "source_type", length = 20)
    private String sourceType;

    /**
     * 连接地址
     */
    @Column(name = "connection_url", length = 500)
    private String connectionUrl;

    /**
     * 连接状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 最后同步时间
     */
    @Column(name = "last_sync_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastSyncTime;

    /**
     * 同步状态
     */
    @Column(name = "sync_status", length = 20)
    private String syncStatus;

    /**
     * 数据源描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 数据源类型枚举
     */
    public enum SourceType {
        MYSQL("mysql", "MySQL数据库"),
        POSTGRESQL("postgresql", "PostgreSQL数据库"),
        ORACLE("oracle", "Oracle数据库"),
        MONGODB("mongodb", "MongoDB数据库");

        private final String code;
        private final String description;

        SourceType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 连接状态枚举
     */
    public enum ConnectionStatus {
        NORMAL(1, "正常"),
        ERROR(2, "异常"),
        UNTESTED(3, "未测试");

        private final int code;
        private final String description;

        ConnectionStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ConnectionStatus fromCode(Integer code) {
            if (code == null) {
                return UNTESTED;
            }
            for (ConnectionStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return UNTESTED;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SUCCESS("success", "同步成功"),
        FAILED("failed", "同步失败"),
        PENDING("pending", "待处理"),
        RUNNING("running", "运行中");

        private final String code;
        private final String description;

        SyncStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (sourceType == null) {
            sourceType = SourceType.MYSQL.getCode();
        }
        if (status == null) {
            status = ConnectionStatus.UNTESTED.getCode();
        }
        if (syncStatus == null) {
            syncStatus = SyncStatus.PENDING.getCode();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
}