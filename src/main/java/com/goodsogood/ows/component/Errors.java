package com.goodsogood.ows.component;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误信息组件
 * 用于统一管理错误码和错误消息
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@Component
public class Errors {
    
    /**
     * 错误码映射
     */
    private final Map<Integer, String> errorMessages;
    
    public Errors() {
        this.errorMessages = new HashMap<>();
        initializeErrorMessages();
    }
    
    /**
     * 初始化错误消息
     */
    private void initializeErrorMessages() {
        // 基于application.yml中的错误消息配置
        errorMessages.put(9999, "当前接口已废除");
        errorMessages.put(9901, "未知错误");
        errorMessages.put(9902, "当前请求过多或服务器繁忙，请稍后再试");
        errorMessages.put(9903, "调用外部接口发生错误");
        errorMessages.put(9904, "当前客户端版本已经过期");
        errorMessages.put(9905, "登录失效请重新登录");
        
        // HTTP状态码对应的错误消息
        errorMessages.put(400, "请求参数错误");
        errorMessages.put(401, "未授权访问");
        errorMessages.put(403, "权限不足");
        errorMessages.put(404, "资源不存在");
        errorMessages.put(500, "服务器内部错误");
    }
    
    /**
     * 根据错误码获取错误消息
     * @param errorCode 错误码
     * @return 错误消息
     */
    public String getErrorMessage(Integer errorCode) {
        return errorMessages.getOrDefault(errorCode, "未知错误");
    }
    
    /**
     * 添加错误消息
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public void addErrorMessage(Integer errorCode, String message) {
        errorMessages.put(errorCode, message);
    }
}