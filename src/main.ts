import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './store'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'ant-design-vue/dist/reset.css'
import './style.scss'
import { permissionDirective } from './router/guards/permission'

dayjs.locale('zh-cn')
const app = createApp(App)

app.use(pinia)
app.use(router)

// 注册权限指令
// app.directive('permission', permissionDirective)
app.mount('#app')
