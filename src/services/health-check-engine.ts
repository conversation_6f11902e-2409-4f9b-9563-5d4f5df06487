// 数据体检核心引擎 - 实现真正的数据检测逻辑
import type { 
  HealthCheckRule, 
  HealthCheckException, 
  HealthCheckType,
  ExceptionType,
  ExceptionLevel 
} from '@/types/health-check'

// 数据体检引擎核心类
export class HealthCheckEngine {
  private rules: Map<HealthCheckType, HealthCheckRule[]> = new Map()
  private mockData: any = this.generateMockData()
  private statistics: any = this.initializeStatistics()
  private executionHistory: any[] = []

  constructor() {
    this.initializeDefaultRules()
  }

  // 初始化统计数据
  private initializeStatistics() {
    return {
      totalChecks: 0,
      completedChecks: 0,
      failedChecks: 0,
      totalExceptions: 0,
      highLevelExceptions: 0,
      fixedExceptions: 0,
      checkTypeStats: [
        { type: 1, count: 0, exceptionCount: 0 },
        { type: 2, count: 0, exceptionCount: 0 },
        { type: 3, count: 0, exceptionCount: 0 },
        { type: 4, count: 0, exceptionCount: 0 }
      ],
      exceptionTrend: []
    }
  }

  // 初始化默认规则
  private initializeDefaultRules() {
    // 党组（党委）设置体检规则
    const partyOrgRules: HealthCheckRule[] = [
      {
        id: 1,
        ruleName: '党组织架构完整性检查',
        ruleType: 1,
        ruleContent: JSON.stringify({
          requiredFields: ['组织名称', '书记', '副书记', '组织委员', '宣传委员'],
          minMembers: 3,
          maxMembers: 15,
          leadershipStructure: {
            书记: { required: true, maxCount: 1 },
            副书记: { required: false, maxCount: 2 },
            委员: { required: true, minCount: 2 }
          }
        }),
        isEnabled: true,
        priority: 1,
        description: '检查党组织架构设置是否完整规范',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      },
      {
        id: 2,
        ruleName: '党员人数合规性检查',
        ruleType: 1,
        ruleContent: JSON.stringify({
          minPartyMembers: 3,
          memberToLeaderRatio: 0.2,
          checkActiveMembership: true,
          validateMembershipDuration: true
        }),
        isEnabled: true,
        priority: 2,
        description: '检查党员人数及比例是否符合规范',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    // 党务干部任免体检规则
    const cadreRules: HealthCheckRule[] = [
      {
        id: 3,
        ruleName: '任免程序合规性检查',
        ruleType: 2,
        ruleContent: JSON.stringify({
          requiredSteps: ['民主推荐', '组织考察', '集体讨论', '任免决定'],
          requiredDocuments: ['任免申请', '考察材料', '民主推荐记录', '会议纪要'],
          approvalLevels: 2,
          timelineRequirements: {
            maxProcessingDays: 30,
            minDiscussionDays: 7
          }
        }),
        isEnabled: true,
        priority: 1,
        description: '检查党务干部任免程序是否规范',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    // 任务体检规则
    const taskRules: HealthCheckRule[] = [
      {
        id: 4,
        ruleName: '任务完成度检查',
        ruleType: 3,
        ruleContent: JSON.stringify({
          completionThreshold: 0.9,
          timelinessWeight: 0.3,
          qualityWeight: 0.4,
          processWeight: 0.3,
          keyCheckPoints: ['完成时间', '质量标准', '过程记录']
        }),
        isEnabled: true,
        priority: 1,
        description: '检查任务执行情况和完成质量',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    // 用户信息完整性规则
    const userInfoRules: HealthCheckRule[] = [
      {
        id: 5,
        ruleName: '用户基础信息完整性检查',
        ruleType: 4,
        ruleContent: JSON.stringify({
          requiredFields: ['姓名', '身份证号', '联系电话', '工作单位', '职务'],
          optionalFields: ['邮箱', '办公地址'],
          validationRules: {
            身份证号: /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/,
            联系电话: /^1[3-9]\d{9}$/,
            邮箱: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
          }
        }),
        isEnabled: true,
        priority: 1,
        description: '检查用户信息的完整性和有效性',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }
    ]

    this.rules.set(1, partyOrgRules)
    this.rules.set(2, cadreRules)
    this.rules.set(3, taskRules)
    this.rules.set(4, userInfoRules)
  }

  // 生成模拟数据
  private generateMockData() {
    return {
      partyOrganizations: [
        {
          id: 1,
          name: '第一党支部',
          secretary: '张三',
          viceSecretary: '李四',
          members: ['王五', '赵六', '钱七'],
          organizationalStructure: { 书记: '张三', 副书记: '李四', 组织委员: '王五' },
          memberCount: 15,
          activeMemberCount: 14
        },
        {
          id: 2,
          name: '第二党支部',
          secretary: '刘明',
          viceSecretary: null, // 缺少副书记 - 会被检测为异常
          members: ['陈华', '周强'],
          organizationalStructure: { 书记: '刘明' }, // 缺少委员 - 会被检测为异常
          memberCount: 8,
          activeMemberCount: 8
        },
        {
          id: 3,
          name: '第三党支部',
          secretary: null, // 缺少书记 - 严重异常
          viceSecretary: '马超',
          members: ['黄忠', '魏延', '姜维'],
          organizationalStructure: {},
          memberCount: 2, // 人数不足 - 异常
          activeMemberCount: 2
        }
      ],
      cadreAppointments: [
        {
          id: 1,
          candidateName: '张三',
          position: '党支部书记',
          processSteps: ['民主推荐', '组织考察', '集体讨论', '任免决定'],
          documents: ['任免申请', '考察材料', '民主推荐记录'],
          startDate: '2024-01-01',
          completionDate: '2024-01-20',
          processingDays: 20
        },
        {
          id: 2,
          candidateName: '李四',
          position: '组织委员',
          processSteps: ['民主推荐', '组织考察'], // 程序不完整
          documents: ['任免申请'], // 文件不全
          startDate: '2024-02-01',
          completionDate: null,
          processingDays: 45 // 超时
        }
      ],
      tasks: [
        {
          id: 1,
          name: '党建活动组织',
          completionRate: 0.95,
          quality: 'excellent',
          timeliness: 'onTime',
          processRecords: ['计划制定', '活动执行', '总结反馈']
        },
        {
          id: 2,
          name: '理论学习安排',
          completionRate: 0.7, // 完成度不足
          quality: 'good',
          timeliness: 'delayed', // 延期
          processRecords: ['计划制定'] // 过程记录不完整
        }
      ],
      users: [
        {
          id: 1,
          name: '张三',
          idCard: '110101199001011234',
          phone: '13800138001',
          email: '<EMAIL>',
          workUnit: '第一党支部',
          position: '书记'
        },
        {
          id: 2,
          name: '李四',
          idCard: '110101199002021234',
          phone: '138001380', // 电话号码不完整
          email: 'invalid-email', // 邮箱格式错误
          workUnit: null, // 工作单位缺失
          position: '副书记'
        },
        {
          id: 3,
          name: '王五',
          idCard: null, // 身份证缺失
          phone: '13800138003',
          email: '<EMAIL>',
          workUnit: '第二党支部',
          position: null // 职务缺失
        }
      ]
    }
  }

  // 执行数据体检
  async executeHealthCheck(checkTypes: HealthCheckType[]): Promise<HealthCheckException[]> {
    const exceptions: HealthCheckException[] = []
    let exceptionId = 1


    for (const checkType of checkTypes) {
      const rules = this.rules.get(checkType) || []
      
      for (const rule of rules) {
        if (!rule.isEnabled) continue

        try {
          const ruleConfig = JSON.parse(rule.ruleContent)
          const ruleExceptions = await this.applyRule(checkType, rule, ruleConfig, exceptionId)
          exceptions.push(...ruleExceptions)
          exceptionId += ruleExceptions.length
        } catch (error) {
          console.error(`规则执行失败: ${rule.ruleName}`, error)
          this.statistics.failedChecks++
        }
      }
    }

    // 更新统计数据
    this.updateStatistics(checkTypes, exceptions)
    
    // 记录执行历史
    this.executionHistory.push({
      timestamp: new Date().toISOString(),
      checkTypes,
      exceptionCount: exceptions.length,
      success: true
    })

    return exceptions
  }

  // 更新统计数据
  private updateStatistics(checkTypes: HealthCheckType[], exceptions: HealthCheckException[]) {
    // 更新总体检次数
    this.statistics.totalChecks++
    this.statistics.completedChecks++
    
    // 更新异常总数
    this.statistics.totalExceptions = exceptions.length
    this.statistics.highLevelExceptions = exceptions.filter(ex => ex.exceptionLevel === 3).length
    
    // 更新各类型统计
    checkTypes.forEach(type => {
      const stat = this.statistics.checkTypeStats.find((s: any) => s.type === type)
      if (stat) {
        stat.count++
        stat.exceptionCount = exceptions.filter(ex => ex.exceptionType === type).length
      }
    })
    
    // 更新异常趋势（保留最近7天）
    const today = new Date().toISOString().split('T')[0]
    const existingTrend = this.statistics.exceptionTrend.find((t: any) => t.date === today)
    if (existingTrend) {
      existingTrend.count = exceptions.length
    } else {
      this.statistics.exceptionTrend.push({ date: today, count: exceptions.length })
      // 只保留最近7天
      if (this.statistics.exceptionTrend.length > 7) {
        this.statistics.exceptionTrend = this.statistics.exceptionTrend.slice(-7)
      }
    }
    
  }

  // 应用具体规则
  private async applyRule(
    checkType: HealthCheckType, 
    rule: HealthCheckRule, 
    config: any, 
    startId: number
  ): Promise<HealthCheckException[]> {
    const exceptions: HealthCheckException[] = []

    switch (checkType) {
      case 1: // 党组（党委）设置体检
        return this.checkPartyOrganizations(rule, config, startId)
      case 2: // 党务干部任免体检
        return this.checkCadreAppointments(rule, config, startId)
      case 3: // 任务体检
        return this.checkTasks(rule, config, startId)
      case 4: // 用户信息完整体检
        return this.checkUserInfo(rule, config, startId)
      default:
        return exceptions
    }
  }

  // 检查党组织设置
  private checkPartyOrganizations(rule: HealthCheckRule, config: any, startId: number): HealthCheckException[] {
    const exceptions: HealthCheckException[] = []
    const orgs = this.mockData.partyOrganizations

    orgs.forEach((org: any, index: number) => {
      // 检查必需字段
      if (config.requiredFields) {
        config.requiredFields.forEach((field: string) => {
          const fieldMap: any = {
            '组织名称': org.name,
            '书记': org.secretary,
            '副书记': org.viceSecretary,
            '组织委员': org.organizationalStructure?.组织委员,
            '宣传委员': org.organizationalStructure?.宣传委员
          }

          if (!fieldMap[field] && field !== '副书记') { // 副书记不是必需的
            exceptions.push({
              id: startId + exceptions.length,
              checkTaskId: 1,
              exceptionType: 1, // 完整性异常
              exceptionLevel: field === '书记' ? 3 : 2, // 书记缺失为高级异常
              exceptionTitle: `${field}信息缺失`,
              exceptionDescription: `${org.name} 缺少${field}信息，影响组织架构完整性`,
              affectedObject: org.name,
              solution: `请及时补充${org.name}的${field}信息，确保组织架构完整`,
              status: 1,
              createTime: new Date().toISOString()
            })
          }
        })
      }

      // 检查党员人数
      if (config.minMembers && org.memberCount < config.minMembers) {
        exceptions.push({
          id: startId + exceptions.length,
          checkTaskId: 1,
          exceptionType: 2, // 准确性异常
          exceptionLevel: 3, // 高级
          exceptionTitle: '党员人数不足',
          exceptionDescription: `${org.name} 党员人数仅有${org.memberCount}人，低于最低要求${config.minMembers}人`,
          affectedObject: org.name,
          solution: '建议通过发展新党员或调整组织架构解决人数不足问题',
          status: 1,
          createTime: new Date().toISOString()
        })
      }

      // 检查领导结构
      if (config.leadershipStructure) {
        Object.entries(config.leadershipStructure).forEach(([position, requirements]: [string, any]) => {
          const currentCount = org.organizationalStructure[position] ? 1 : 0
          
          if (requirements.required && currentCount === 0) {
            exceptions.push({
              id: startId + exceptions.length,
              checkTaskId: 1,
              exceptionType: 1,
              exceptionLevel: position === '书记' ? 3 : 2,
              exceptionTitle: `${position}职位空缺`,
              exceptionDescription: `${org.name} 缺少${position}，组织领导结构不完整`,
              affectedObject: org.name,
              solution: `请及时选举或任命${position}，完善组织领导结构`,
              status: 1,
              createTime: new Date().toISOString()
            })
          }
        })
      }
    })

    return exceptions
  }

  // 检查干部任免
  private checkCadreAppointments(rule: HealthCheckRule, config: any, startId: number): HealthCheckException[] {
    const exceptions: HealthCheckException[] = []
    const appointments = this.mockData.cadreAppointments

    appointments.forEach((appointment: any) => {
      // 检查程序步骤
      if (config.requiredSteps) {
        const missingSteps = config.requiredSteps.filter((step: string) => 
          !appointment.processSteps.includes(step)
        )

        if (missingSteps.length > 0) {
          exceptions.push({
            id: startId + exceptions.length,
            checkTaskId: 2,
            exceptionType: 2, // 准确性异常
            exceptionLevel: 2,
            exceptionTitle: '任免程序不完整',
            exceptionDescription: `${appointment.candidateName}的${appointment.position}任免程序缺少步骤: ${missingSteps.join(', ')}`,
            affectedObject: appointment.candidateName,
            solution: `请补充完成缺失的程序步骤: ${missingSteps.join(', ')}`,
            status: 1,
            createTime: new Date().toISOString()
          })
        }
      }

      // 检查必需文件
      if (config.requiredDocuments) {
        const missingDocs = config.requiredDocuments.filter((doc: string) => 
          !appointment.documents.includes(doc)
        )

        if (missingDocs.length > 0) {
          exceptions.push({
            id: startId + exceptions.length,
            checkTaskId: 2,
            exceptionType: 1, // 完整性异常
            exceptionLevel: 2,
            exceptionTitle: '任免文件不全',
            exceptionDescription: `${appointment.candidateName}的任免材料缺少: ${missingDocs.join(', ')}`,
            affectedObject: appointment.candidateName,
            solution: `请补充提交缺失的文件: ${missingDocs.join(', ')}`,
            status: 1,
            createTime: new Date().toISOString()
          })
        }
      }

      // 检查处理时长
      if (config.timelineRequirements?.maxProcessingDays && 
          appointment.processingDays > config.timelineRequirements.maxProcessingDays) {
        exceptions.push({
          id: startId + exceptions.length,
          checkTaskId: 2,
          exceptionType: 2, // 准确性异常
          exceptionLevel: 2,
          exceptionTitle: '处理时间超期',
          exceptionDescription: `${appointment.candidateName}的任免处理时间${appointment.processingDays}天，超过规定${config.timelineRequirements.maxProcessingDays}天`,
          affectedObject: appointment.candidateName,
          solution: '请加快处理进度，严格控制任免处理时限',
          status: 1,
          createTime: new Date().toISOString()
        })
      }
    })

    return exceptions
  }

  // 检查任务执行情况
  private checkTasks(rule: HealthCheckRule, config: any, startId: number): HealthCheckException[] {
    const exceptions: HealthCheckException[] = []
    const tasks = this.mockData.tasks

    tasks.forEach((task: any) => {
      // 检查完成度
      if (config.completionThreshold && task.completionRate < config.completionThreshold) {
        exceptions.push({
          id: startId + exceptions.length,
          checkTaskId: 3,
          exceptionType: 2, // 准确性异常
          exceptionLevel: task.completionRate < 0.5 ? 3 : 2,
          exceptionTitle: '任务完成度不足',
          exceptionDescription: `任务"${task.name}"完成度仅为${(task.completionRate * 100).toFixed(1)}%，低于要求的${(config.completionThreshold * 100)}%`,
          affectedObject: task.name,
          solution: '请加快任务进度，确保按时按质完成任务目标',
          status: 1,
          createTime: new Date().toISOString()
        })
      }

      // 检查时效性
      if (task.timeliness === 'delayed') {
        exceptions.push({
          id: startId + exceptions.length,
          checkTaskId: 3,
          exceptionType: 2, // 准确性异常
          exceptionLevel: 2,
          exceptionTitle: '任务执行延期',
          exceptionDescription: `任务"${task.name}"存在延期情况，影响整体工作进度`,
          affectedObject: task.name,
          solution: '分析延期原因，制定赶工计划，确保后续任务按时完成',
          status: 1,
          createTime: new Date().toISOString()
        })
      }

      // 检查关键检查点
      if (config.keyCheckPoints) {
        const processMap: any = {
          '完成时间': task.timeliness === 'onTime',
          '质量标准': task.quality === 'excellent',
          '过程记录': task.processRecords && task.processRecords.length >= 3
        }

        config.keyCheckPoints.forEach((checkpoint: string) => {
          if (!processMap[checkpoint]) {
            exceptions.push({
              id: startId + exceptions.length,
              checkTaskId: 3,
              exceptionType: 1, // 完整性异常
              exceptionLevel: 1,
              exceptionTitle: `${checkpoint}不达标`,
              exceptionDescription: `任务"${task.name}"在${checkpoint}方面存在不足`,
              affectedObject: task.name,
              solution: `请重点关注并改进任务的${checkpoint}`,
              status: 1,
              createTime: new Date().toISOString()
            })
          }
        })
      }
    })

    return exceptions
  }

  // 检查用户信息完整性
  private checkUserInfo(rule: HealthCheckRule, config: any, startId: number): HealthCheckException[] {
    const exceptions: HealthCheckException[] = []
    const users = this.mockData.users

    users.forEach((user: any) => {
      // 检查必需字段
      if (config.requiredFields) {
        config.requiredFields.forEach((field: string) => {
          const fieldMap: any = {
            '姓名': user.name,
            '身份证号': user.idCard,
            '联系电话': user.phone,
            '工作单位': user.workUnit,
            '职务': user.position
          }

          if (!fieldMap[field]) {
            exceptions.push({
              id: startId + exceptions.length,
              checkTaskId: 4,
              exceptionType: 1, // 完整性异常
              exceptionLevel: ['身份证号', '姓名'].includes(field) ? 3 : 2,
              exceptionTitle: `${field}信息缺失`,
              exceptionDescription: `用户"${user.name || '未知'}"缺少${field}信息`,
              affectedObject: user.name || `用户ID: ${user.id}`,
              solution: `请补充完善用户的${field}信息`,
              status: 1,
              createTime: new Date().toISOString()
            })
          }
        })
      }

      // 验证字段格式
      if (config.validationRules) {
        Object.entries(config.validationRules).forEach(([field, pattern]: [string, any]) => {
          const fieldMap: any = {
            '身份证号': user.idCard,
            '联系电话': user.phone,
            '邮箱': user.email
          }

          const value = fieldMap[field]
          if (value && !pattern.test(value)) {
            exceptions.push({
              id: startId + exceptions.length,
              checkTaskId: 4,
              exceptionType: 2, // 准确性异常
              exceptionLevel: 2,
              exceptionTitle: `${field}格式错误`,
              exceptionDescription: `用户"${user.name}"的${field}"${value}"格式不正确`,
              affectedObject: user.name,
              solution: `请修正${field}格式，确保符合规范要求`,
              status: 1,
              createTime: new Date().toISOString()
            })
          }
        })
      }
    })

    return exceptions
  }

  // 获取规则配置
  getRules(checkType?: HealthCheckType): HealthCheckRule[] {
    if (checkType) {
      return this.rules.get(checkType) || []
    }
    
    const allRules: HealthCheckRule[] = []
    this.rules.forEach(rules => allRules.push(...rules))
    return allRules
  }

  // 更新规则
  updateRule(rule: HealthCheckRule): void {
    const typeRules = this.rules.get(rule.ruleType) || []
    const index = typeRules.findIndex(r => r.id === rule.id)
    
    if (index >= 0) {
      typeRules[index] = { ...rule, updateTime: new Date().toISOString() }
    } else {
      typeRules.push({ ...rule, createTime: new Date().toISOString(), updateTime: new Date().toISOString() })
    }
    
    this.rules.set(rule.ruleType, typeRules)
  }

  // 删除规则
  deleteRule(ruleId: number, checkType: HealthCheckType): void {
    const typeRules = this.rules.get(checkType) || []
    const filteredRules = typeRules.filter(r => r.id !== ruleId)
    this.rules.set(checkType, filteredRules)
  }

  // 获取统计数据
  getStatistics(): any {
    // 返回实时统计数据
    return { ...this.statistics }
  }
}

// 导出全局实例
export const healthCheckEngine = new HealthCheckEngine()