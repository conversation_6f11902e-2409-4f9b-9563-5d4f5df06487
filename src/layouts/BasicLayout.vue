<template>
	<a-layout class="layout">
		 <a-layout-header v-if="env === 'dev'">
			<div class="logo">
				<h3>党建管理系统</h3>
			</div>
			<a-menu
				v-model:selectedKeys="selectedKeys"
				v-model:openKeys="openKeys"
				theme="dark"
				mode="horizontal"
				:style="{ lineHeight: '64px' }"
				@click="handleMenuClick"
			>
				<a-menu-item key="home">
					<template #icon>
						<home-outlined />
					</template>
					首页
				</a-menu-item>

				<a-sub-menu key="honor">
					<template #icon>
						<trophy-outlined />
					</template>
					<template #title>荣誉申报</template>
					<a-menu-item key="honor-apply">荣誉申报</a-menu-item>
					<a-menu-item key="honor-audit">荣誉审核</a-menu-item>
				</a-sub-menu>

				<a-sub-menu key="health-check">
					<template #icon>
						<dashboard-outlined />
					</template>
					<template #title>数据体检</template>
					<a-menu-item key="health-check-dashboard">仪表板</a-menu-item>
					<a-menu-item key="health-check-rules">规则配置</a-menu-item>
					<a-menu-item key="health-check-execution">执行监控</a-menu-item>
					<a-menu-item key="health-check-results">结果管理</a-menu-item>
				</a-sub-menu>

				<a-sub-menu key="review">
					<template #icon>
						<audit-outlined />
					</template>
					<template #title>智能审核</template>
					<a-menu-item key="review-application">申报管理</a-menu-item>
					<a-menu-item key="review-audit">审核管理</a-menu-item>
					<a-menu-item key="review-results">结果管理</a-menu-item>
					<a-menu-item key="review-appeal">申诉管理</a-menu-item>
				</a-sub-menu>

				<a-menu-item key="special-team">
					<template #icon>
						<team-outlined />
					</template>
					专班推荐
				</a-menu-item>

				<a-menu-item key="dynamics">
					<template #icon>
						<thunderbolt-outlined />
					</template>
					创建动态管理
				</a-menu-item>

				<a-sub-menu key="sensitive-words">
					<template #icon>
						<safety-outlined />
					</template>
					<template #title>敏感词管理</template>
					<a-menu-item key="sensitive-words-index">敏感词管理首页</a-menu-item>
					<a-menu-item key="sensitive-words-library">敏感词库管理</a-menu-item>
					<a-menu-item key="sensitive-words-manage">敏感词管理</a-menu-item>
					<a-menu-item key="sensitive-words-policy">过滤策略管理</a-menu-item>
					<a-menu-item key="sensitive-words-detection">内容检测</a-menu-item>
				</a-sub-menu>

				<a-sub-menu key="case-promotion">
					<template #icon>
						<book-outlined />
					</template>
					<template #title>案例推广</template>
					<a-menu-item key="case-promotion-index">案例推广首页</a-menu-item>
					<a-menu-item key="case-promotion-list">案例列表</a-menu-item>
					<a-menu-item key="case-promotion-library">案例库管理</a-menu-item>
					<a-menu-item key="case-promotion-category">分类管理</a-menu-item>
					<a-menu-item key="case-promotion-tag">标签管理</a-menu-item>
				</a-sub-menu>

				<a-menu-item key="model-agency-dashboard">
					<template #icon>
						<dashboard-outlined />
					</template>
					模范机关总览看板
				</a-menu-item>

				<a-menu-item key="workflow">
					<template #icon>
						<apartment-outlined />
					</template>
					流程引擎
				</a-menu-item>

				<a-menu-item key="message-center">
					<template #icon>
						<message-outlined />
					</template>
					消息中心
				</a-menu-item>

				<a-sub-menu key="activity-management">
					<template #icon>
						<calendar-outlined />
					</template>
					<template #title>活动管理</template>
					<a-menu-item key="activity-management-index">活动首页</a-menu-item>
					<a-menu-item key="activity-management-list">活动列表</a-menu-item>
				</a-sub-menu>

				<a-sub-menu key="audit-confirmation">
					<template #icon>
						<check-circle-outlined />
					</template>
					<template #title>审核确认</template>
					<a-menu-item key="audit-confirmation-index">审核确认首页</a-menu-item>
					<a-menu-item key="audit-confirmation-project-audit">项目审核管理</a-menu-item>
					<a-menu-item key="audit-confirmation-score-management">评分结果管理</a-menu-item>
					<a-menu-item key="audit-confirmation-audit-history">审核历史</a-menu-item>
				</a-sub-menu>

				<a-sub-menu key="yulifaceto-face">
					<template #icon>
						<message-outlined />
					</template>
					<template #title>渝理面对面</template>
					<a-menu-item key="yulifaceto-face-index">渝理面对面首页</a-menu-item>
					<a-menu-item key="yulifaceto-face-document-management">纪实管理</a-menu-item>
					<a-menu-item key="yulifaceto-face-template-management">模板管理</a-menu-item>
					<a-menu-item key="yulifaceto-face-data-display">数据展示</a-menu-item>
					<a-menu-item key="yulifaceto-face-statistics-analysis">统计分析</a-menu-item>
				</a-sub-menu>

				<a-menu-item key="project-dashboard">
					<template #icon>
						<project-outlined />
					</template>
					选育树推项目看板
				</a-menu-item>

				<a-sub-menu key="cultivation-warning">
					<template #icon>
						<alert-outlined />
					</template>
					<template #title>培育过程预警</template>
					<a-menu-item key="cultivation-warning-index">预警首页</a-menu-item>
					<a-menu-item key="cultivation-warning-config">预警配置管理</a-menu-item>
					<a-menu-item key="cultivation-warning-data-collection">数据采集与处理</a-menu-item>
					<a-menu-item key="cultivation-warning-alert-trigger">预警触发与通知</a-menu-item>
					<a-menu-item key="cultivation-warning-supervision">督办管理</a-menu-item>
					<a-menu-item key="cultivation-warning-report">预警报告与模板</a-menu-item>
				</a-sub-menu>

				<a-menu-item key="indicator-rules">
					<template #icon>
						<setting-outlined />
					</template>
					指标规则管理
				</a-menu-item>

				<a-menu-item key="quarterly-showcase">
					<template #icon>
						<bar-chart-outlined />
					</template>
					季度亮晒
				</a-menu-item>
			</a-menu>
		</a-layout-header>
		<a-layout-content>
			<div class="content-wrapper">
				<router-view />
			</div>
		</a-layout-content>
	</a-layout>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  HomeOutlined,
  TrophyOutlined,
  DashboardOutlined,
  AuditOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  BookOutlined,
  ApartmentOutlined,
  MessageOutlined,
  ProjectOutlined,
  BarChartOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  AlertOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const env = import.meta.env.VITE_APP_ENV

const selectedKeys = ref<string[]>(['home'])
const openKeys = ref<string[]>([])

// 菜单路由映射
const menuRouteMap: Record<string, string> = {
  'home': '/home',
  'honor-apply': '/honor/apply',
  'honor-audit': '/honor/audit',
  'health-check-dashboard': '/health-check/dashboard',
  'health-check-rules': '/health-check/rules',
  'health-check-execution': '/health-check/execution',
  'health-check-results': '/health-check/results',
  'review-application': '/review/application',
  'review-audit': '/review/audit',
  'review-results': '/review/results',
  'review-appeal': '/review/appeal',
  'special-team': '/special-team',
  'dynamics': '/dynamics',
  'sensitive-words-index': '/sensitive-words/index',
  'sensitive-words-library': '/sensitive-words/word-library',
  'sensitive-words-manage': '/sensitive-words/word-manage',
  'sensitive-words-policy': '/sensitive-words/policy-manage',
  'sensitive-words-detection': '/sensitive-words/detection',
  'case-promotion-index': '/case-promotion',
  'case-promotion-list': '/case-promotion/list',
  'case-promotion-library': '/case-promotion/library',
  'case-promotion-category': '/case-promotion/category',
  'case-promotion-tag': '/case-promotion/tag',
  'model-agency-dashboard': '/model-agency-dashboard',
  'workflow': '/workflow',
  'message-center': '/message-center',
  'activity-management-index': '/activity-management/index',
  'activity-management-list': '/activity-management/list',
  'audit-confirmation-index': '/audit-confirmation/index',
  'audit-confirmation-project-audit': '/audit-confirmation/project-audit',
  'audit-confirmation-score-management': '/audit-confirmation/score-management',
  'audit-confirmation-audit-history': '/audit-confirmation/audit-history',
  'yulifaceto-face-index': '/yulifaceto-face/index',
  'yulifaceto-face-document-management': '/yulifaceto-face/document-management',
  'yulifaceto-face-template-management': '/yulifaceto-face/template-management',
  'yulifaceto-face-data-display': '/yulifaceto-face/data-display',
  'yulifaceto-face-statistics-analysis': '/yulifaceto-face/statistics-analysis',
  'cultivation-warning-index': '/cultivation-warning/index',
  'cultivation-warning-config': '/cultivation-warning/config',
  'cultivation-warning-data-collection': '/cultivation-warning/data-collection',
  'cultivation-warning-alert-trigger': '/cultivation-warning/alert-trigger',
  'cultivation-warning-supervision': '/cultivation-warning/supervision',
  'cultivation-warning-report': '/cultivation-warning/report',
  'project-dashboard': '/project-dashboard',
  'indicator-rules': '/indicator-rules',
  'quarterly-showcase': '/quarterly-showcase'
}

// 路由菜单映射（反向映射）
const routeMenuMap: Record<string, { key: string; parentKey?: string }> = {
  '/home': { key: 'home' },
  '/honor/apply': { key: 'honor-apply', parentKey: 'honor' },
  '/honor/audit': { key: 'honor-audit', parentKey: 'honor' },
  '/health-check/dashboard': { key: 'health-check-dashboard', parentKey: 'health-check' },
  '/health-check/rules': { key: 'health-check-rules', parentKey: 'health-check' },
  '/health-check/execution': { key: 'health-check-execution', parentKey: 'health-check' },
  '/health-check/results': { key: 'health-check-results', parentKey: 'health-check' },
  '/review/application': { key: 'review-application', parentKey: 'review' },
  '/review/audit': { key: 'review-audit', parentKey: 'review' },
  '/review/results': { key: 'review-results', parentKey: 'review' },
  '/review/appeal': { key: 'review-appeal', parentKey: 'review' },
  '/special-team': { key: 'special-team' },
  '/dynamics': { key: 'dynamics' },
  '/sensitive-words': { key: 'sensitive-words-index', parentKey: 'sensitive-words' },
  '/sensitive-words/index': { key: 'sensitive-words-index', parentKey: 'sensitive-words' },
  '/sensitive-words/word-library': { key: 'sensitive-words-library', parentKey: 'sensitive-words' },
  '/sensitive-words/word-manage': { key: 'sensitive-words-manage', parentKey: 'sensitive-words' },
  '/sensitive-words/policy-manage': { key: 'sensitive-words-policy', parentKey: 'sensitive-words' },
  '/sensitive-words/detection': { key: 'sensitive-words-detection', parentKey: 'sensitive-words' },
  '/case-promotion': { key: 'case-promotion-index', parentKey: 'case-promotion' },
  '/case-promotion/list': { key: 'case-promotion-list', parentKey: 'case-promotion' },
  '/case-promotion/detail': { key: 'case-promotion-list', parentKey: 'case-promotion' },
  '/case-promotion/edit': { key: 'case-promotion-list', parentKey: 'case-promotion' },
  '/case-promotion/library': { key: 'case-promotion-library', parentKey: 'case-promotion' },
  '/case-promotion/category': { key: 'case-promotion-category', parentKey: 'case-promotion' },
  '/case-promotion/tag': { key: 'case-promotion-tag', parentKey: 'case-promotion' },
  '/model-agency-dashboard': { key: 'model-agency-dashboard' },
  '/workflow': { key: 'workflow' },
  '/message-center': { key: 'message-center' },
  '/activity-management': { key: 'activity-management-index', parentKey: 'activity-management' },
  '/activity-management/index': { key: 'activity-management-index', parentKey: 'activity-management' },
  '/activity-management/list': { key: 'activity-management-list', parentKey: 'activity-management' },
  '/activity-management/detail': { key: 'activity-management-list', parentKey: 'activity-management' },
  '/activity-management/participation': { key: 'activity-management-list', parentKey: 'activity-management' },
  '/cultivation-warning': { key: 'cultivation-warning-index', parentKey: 'cultivation-warning' },
  '/cultivation-warning/index': { key: 'cultivation-warning-index', parentKey: 'cultivation-warning' },
  '/cultivation-warning/config': { key: 'cultivation-warning-config', parentKey: 'cultivation-warning' },
  '/cultivation-warning/data-collection': { key: 'cultivation-warning-data-collection', parentKey: 'cultivation-warning' },
  '/cultivation-warning/alert-trigger': { key: 'cultivation-warning-alert-trigger', parentKey: 'cultivation-warning' },
  '/cultivation-warning/supervision': { key: 'cultivation-warning-supervision', parentKey: 'cultivation-warning' },
  '/cultivation-warning/report': { key: 'cultivation-warning-report', parentKey: 'cultivation-warning' },
  '/project-dashboard': { key: 'project-dashboard' },
  '/indicator-rules': { key: 'indicator-rules' },
  '/quarterly-showcase': { key: 'quarterly-showcase' }
}

// 处理菜单点击
function handleMenuClick({ key }: { key: string }) {
  const routePath = menuRouteMap[key]
  if (routePath) {
    router.push(routePath)
  }
}

// 根据当前路由设置选中的菜单项
function setActiveMenu() {
  const currentPath = route.path
  let menuInfo = routeMenuMap[currentPath]

  // 如果没有找到精确匹配，尝试匹配路由模式
  if (!menuInfo) {
    // 处理动态路由参数
    if (currentPath.startsWith('/case-promotion/detail/')) {
      menuInfo = routeMenuMap['/case-promotion/detail']
    } else if (currentPath.startsWith('/case-promotion/edit/')) {
      menuInfo = routeMenuMap['/case-promotion/edit']
    } else if (currentPath.startsWith('/quarterly-showcase/detail/')) {
      menuInfo = routeMenuMap['/quarterly-showcase']
    } else if (currentPath.startsWith('/cultivation-warning/')) {
      // 培育过程预警系统路由匹配
      const warningPath = currentPath.replace('/cultivation-warning/', '')
      if (warningPath === '' || warningPath === 'index') {
        menuInfo = routeMenuMap['/cultivation-warning/index']
      } else {
        menuInfo = routeMenuMap[currentPath]
      }
    }
  }

  if (menuInfo) {
    selectedKeys.value = [menuInfo.key]
    if (menuInfo.parentKey) {
      openKeys.value = [menuInfo.parentKey]
    }
  }
}

// 监听路由变化
onMounted(() => {
  setActiveMenu()
})

// 监听路由变化，实时更新菜单状态
watch(() => route.path, () => {
  setActiveMenu()
})

// 监听路由变化，实时更新菜单状态
watch(
  () => route.path,
  () => {
    setActiveMenu()
  },
  { immediate: true }
)
</script>

<style scoped>
.layout {
	min-height: 100vh;
}

.logo {
	float: left;
	width: 200px;
	height: 31px;
	margin: 16px 24px 16px 0;
	display: flex;
	align-items: center;
}

.logo h3 {
	color: white;
	margin: 0;
	font-size: 18px;
	font-weight: 600;
}

.content-wrapper {
	background: #f5f5f5;
	min-height: calc(100vh - 64px - 70px);
	padding: 0;
}

.ant-layout-header {
	padding: 0 50px;
	background: #001529;
}

.ant-layout-content {
	padding: 0;
}

.ant-layout-footer {
	background: #f0f2f5;
	color: #666;
	padding: 24px 50px;
}

.ant-menu-horizontal {
	border-bottom: none;
}

.ant-menu-horizontal > .ant-menu-item,
.ant-menu-horizontal > .ant-menu-submenu {
	border-bottom: 2px solid transparent;
}

.ant-menu-horizontal > .ant-menu-item:hover,
.ant-menu-horizontal > .ant-menu-submenu:hover,
.ant-menu-horizontal > .ant-menu-item-selected,
.ant-menu-horizontal > .ant-menu-submenu-selected {
	border-bottom-color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.ant-layout-header {
		padding: 0 20px;
	}

	.logo {
		width: 150px;
		margin: 16px 16px 16px 0;
	}

	.logo h3 {
		font-size: 16px;
	}

	.ant-layout-footer {
		padding: 24px 20px;
	}
}
</style>
