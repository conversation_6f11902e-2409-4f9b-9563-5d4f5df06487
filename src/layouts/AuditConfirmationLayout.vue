<template>
  <div class="audit-confirmation-layout">
    <a-layout style="min-height: 100vh;">
      <!-- 侧边导航 -->
      <a-layout-sider 
        v-model:collapsed="collapsed" 
        :width="240"
        collapsible
        theme="light"
        style="background: #fff; border-right: 1px solid #f0f0f0;"
      >
        <div class="logo-section">
          <div class="logo-content">
            <audit-outlined class="logo-icon" />
            <span v-if="!collapsed" class="logo-text">审核确认系统</span>
          </div>
        </div>
        
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          theme="light"
          style="border: none;"
          @click="handleMenuClick"
        >
          <!-- 项目管理分组 -->
          <a-menu-item-group key="project" title="项目管理">
            <a-menu-item key="index">
              <template #icon><dashboard-outlined /></template>
              审核仪表板
            </a-menu-item>
            <a-menu-item key="project-audit">
              <template #icon><audit-outlined /></template>
              项目审核
            </a-menu-item>
            <a-menu-item key="cultivation-object-list">
              <template #icon><team-outlined /></template>
              培育对象管理
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 评分管理分组 -->
          <a-menu-item-group key="scoring" title="评分管理">
            <a-menu-item key="score-management">
              <template #icon><bar-chart-outlined /></template>
              评分结果管理
            </a-menu-item>
            <a-menu-item key="rescore-function">
              <template #icon><edit-outlined /></template>
              人工校正
            </a-menu-item>
            <a-menu-item key="rescore-management">
              <template #icon><history-outlined /></template>
              校正管理
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 流程配置分组 -->
          <a-menu-item-group key="process" title="流程配置">
            <a-menu-item key="review-process-config">
              <template #icon><setting-outlined /></template>
              审核流程配置
            </a-menu-item>
            <a-menu-item key="workflow-config">
              <template #icon><apartment-outlined /></template>
              工作流配置
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 历史追溯分组 -->
          <a-menu-item-group key="history" title="历史追溯">
            <a-menu-item key="audit-history">
              <template #icon><file-text-outlined /></template>
              审核历史
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 申诉处理分组 -->
          <a-menu-item-group key="appeal" title="申诉处理">
            <a-menu-item key="appeal-management">
              <template #icon><customer-service-outlined /></template>
              申诉管理
            </a-menu-item>
            <a-menu-item key="appeal-config">
              <template #icon><control-outlined /></template>
              申诉流程配置
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 通知管理分组 -->
          <a-menu-item-group key="notification" title="通知管理">
            <a-menu-item key="notification-template-config">
              <template #icon><mail-outlined /></template>
              通知模板配置
            </a-menu-item>
          </a-menu-item-group>
          
        </a-menu>
      </a-layout-sider>
      
      <!-- 主内容区域 -->
      <a-layout>
        <!-- 面包屑导航 -->
        <a-layout-header style="background: #fff; padding: 0 24px; border-bottom: 1px solid #f0f0f0; height: auto; line-height: normal; padding-top: 16px; padding-bottom: 16px;">
          <a-breadcrumb>
            <a-breadcrumb-item>
              <home-outlined />
              <router-link to="/home">首页</router-link>
            </a-breadcrumb-item>
            <a-breadcrumb-item>
              <audit-outlined />
              审核确认
            </a-breadcrumb-item>
            <a-breadcrumb-item>
              {{ currentPageTitle }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </a-layout-header>
        
        <!-- 页面内容 -->
        <a-layout-content style="padding: 24px; background: #f5f5f5;">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  AuditOutlined,
  DashboardOutlined, 
  BarChartOutlined, 
  SettingOutlined, 
  TeamOutlined,
  EditOutlined,
  HistoryOutlined,
  ApartmentOutlined,
  FileTextOutlined,
  CustomerServiceOutlined,
  ControlOutlined,
  MailOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const router = useRouter()
const route = useRoute()

// 路由映射
const routeMapping: Record<string, string> = {
  'index': '/audit-confirmation/index',
  'project-audit': '/audit-confirmation/project-audit',
  'cultivation-object-list': '/audit-confirmation/cultivation-object-list',
  'score-management': '/audit-confirmation/score-management',
  'rescore-function': '/audit-confirmation/rescore-function',
  'rescore-management': '/audit-confirmation/rescore-management',
  'review-process-config': '/audit-confirmation/review-process-config',
  'workflow-config': '/audit-confirmation/workflow-config',
  'audit-history': '/audit-confirmation/audit-history',
  'appeal-management': '/audit-confirmation/appeal-management',
  'appeal-config': '/audit-confirmation/appeal-config',
  'notification-template-config': '/audit-confirmation/notification-template-config'
}

// 页面标题映射
const titleMapping: Record<string, string> = {
  'index': '审核仪表板',
  'project-audit': '项目审核',
  'cultivation-object-list': '培育对象管理',
  'score-management': '评分结果管理',
  'rescore-function': '人工校正',
  'rescore-management': '校正管理',
  'review-process-config': '审核流程配置',
  'workflow-config': '工作流配置',
  'audit-history': '审核历史',
  'appeal-management': '申诉管理',
  'appeal-config': '申诉流程配置',
  'notification-template-config': '通知模板配置'
}

// 反向路由映射
const pathToMenuKey: Record<string, string> = {
  '/audit-confirmation/index': 'index',
  '/audit-confirmation/project-audit': 'project-audit',
  '/audit-confirmation/cultivation-object-list': 'cultivation-object-list',
  '/audit-confirmation/score-management': 'score-management',
  '/audit-confirmation/rescore-function': 'rescore-function',
  '/audit-confirmation/rescore-management': 'rescore-management',
  '/audit-confirmation/review-process-config': 'review-process-config',
  '/audit-confirmation/workflow-config': 'workflow-config',
  '/audit-confirmation/audit-history': 'audit-history',
  '/audit-confirmation/appeal-management': 'appeal-management',
  '/audit-confirmation/appeal-config': 'appeal-config',
  '/audit-confirmation/notification-template-config': 'notification-template-config'
}

// 计算属性
const currentPageTitle = computed(() => {
  const currentKey = pathToMenuKey[route.path]
  return titleMapping[currentKey] || '审核确认'
})

// 方法
const handleMenuClick = ({ key }: { key: string }) => {
  const targetPath = routeMapping[key]
  if (targetPath && targetPath !== route.path) {
    router.push(targetPath)
  }
}

// 监听路由变化，更新选中状态
watch(() => route.path, (newPath) => {
  const menuKey = pathToMenuKey[newPath]
  if (menuKey) {
    selectedKeys.value = [menuKey]
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 初始化选中状态
  const currentKey = pathToMenuKey[route.path]
  if (currentKey) {
    selectedKeys.value = [currentKey]
  } else {
    // 如果是默认进入audit-confirmation，跳转到index
    if (route.path === '/audit-confirmation' || route.path === '/audit-confirmation/') {
      router.replace('/audit-confirmation/index')
    }
  }
})
</script>

<style scoped>
.audit-confirmation-layout {
  height: 100vh;
}

.logo-section {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.logo-content {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.logo-icon {
  font-size: 20px;
  margin-right: 8px;
}

.logo-text {
  white-space: nowrap;
}

/* 自定义菜单分组标题样式 */
:deep(.ant-menu-item-group-title) {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  padding: 8px 0 4px 0;
}

/* 菜单项样式 */
:deep(.ant-menu-item) {
  height: 40px;
  line-height: 40px;
  margin-bottom: 4px;
  border-radius: 6px;
}

:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
}

/* 面包屑样式 */
:deep(.ant-breadcrumb) {
  font-size: 14px;
}

:deep(.ant-breadcrumb a) {
  color: #666;
  text-decoration: none;
}

:deep(.ant-breadcrumb a:hover) {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-text {
    display: none;
  }
  
  :deep(.ant-layout-sider) {
    width: 60px !important;
  }
}
</style>