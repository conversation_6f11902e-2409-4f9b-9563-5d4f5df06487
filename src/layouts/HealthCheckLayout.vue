<template>
  <div class="health-check-layout">
    <a-layout style="min-height: 100vh;">
      <!-- 侧边导航 -->
      <a-layout-sider 
        v-model:collapsed="collapsed" 
        :width="240"
        collapsible
        theme="light"
        style="background: #fff; border-right: 1px solid #f0f0f0;"
      >
        <div class="logo-section">
          <div class="logo-content">
            <dashboard-outlined class="logo-icon" />
            <span v-if="!collapsed" class="logo-text">数据体检系统</span>
          </div>
        </div>
        
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="inline"
          theme="light"
          style="border: none;"
          @click="handleMenuClick"
        >
          <!-- 数据监控分组 -->
          <a-menu-item-group key="monitoring" title="数据监控">
            <a-menu-item key="dashboard">
              <template #icon><dashboard-outlined /></template>
              数据仪表板
            </a-menu-item>
            <a-menu-item key="statistics">
              <template #icon><bar-chart-outlined /></template>
              统计分析
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 规则管理分组 -->
          <a-menu-item-group key="rules" title="规则管理">
            <a-menu-item key="rules-config">
              <template #icon><setting-outlined /></template>
              规则配置
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 执行管理分组 -->
          <a-menu-item-group key="execution" title="执行管理">
            <a-menu-item key="execution-monitor">
              <template #icon><monitor-outlined /></template>
              执行监控
            </a-menu-item>
            <a-menu-item key="scheduled">
              <template #icon><clock-circle-outlined /></template>
              定时体检
            </a-menu-item>
          </a-menu-item-group>
          
          <!-- 结果处理分组 -->
          <a-menu-item-group key="results" title="结果处理">
            <a-menu-item key="results-manage">
              <template #icon><file-text-outlined /></template>
              结果管理
            </a-menu-item>
            <a-menu-item key="data-collection">
              <template #icon><database-outlined /></template>
              数据收集
            </a-menu-item>
          </a-menu-item-group>
          
        </a-menu>
      </a-layout-sider>
      
      <!-- 主内容区域 -->
      <a-layout>
        <!-- 面包屑导航 -->
        <a-layout-header style="background: #fff; padding: 0 24px; border-bottom: 1px solid #f0f0f0; height: auto; line-height: normal; padding-top: 16px; padding-bottom: 16px;">
          <a-breadcrumb>
            <a-breadcrumb-item>
              <home-outlined />
              <router-link to="/home">首页</router-link>
            </a-breadcrumb-item>
            <a-breadcrumb-item>
              <dashboard-outlined />
              数据体检
            </a-breadcrumb-item>
            <a-breadcrumb-item>
              {{ currentPageTitle }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </a-layout-header>
        
        <!-- 页面内容 -->
        <a-layout-content style="padding: 24px; background: #f5f5f5;">
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  DashboardOutlined, 
  BarChartOutlined, 
  SettingOutlined, 
  ExperimentOutlined,
  MonitorOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  HomeOutlined,
  ApiOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const collapsed = ref(false)
const selectedKeys = ref<string[]>([])
const router = useRouter()
const route = useRoute()

// 路由映射
const routeMapping: Record<string, string> = {
  'dashboard': '/health-check/dashboard',
  'statistics': '/health-check/statistics', 
  'rules-config': '/health-check/rules',
  'execution-monitor': '/health-check/execution',
  'scheduled': '/health-check/scheduled',
  'results-manage': '/health-check/results',
  'data-collection': '/health-check/data-collection',
}

// 页面标题映射
const titleMapping: Record<string, string> = {
  'dashboard': '数据仪表板',
  'statistics': '统计分析',
  'rules-config': '规则配置', 
  'execution-monitor': '执行监控',
  'scheduled': '定时体检',
  'results-manage': '结果管理',
  'data-collection': '数据收集',
}

// 反向路由映射
const pathToMenuKey: Record<string, string> = {
  '/health-check/dashboard': 'dashboard',
  '/health-check/statistics': 'statistics',
  '/health-check/rules': 'rules-config',
 
  '/health-check/execution': 'execution-monitor',
  '/health-check/scheduled': 'scheduled',
  '/health-check/results': 'results-manage',
  '/health-check/data-collection': 'data-collection',
}

// 计算属性
const currentPageTitle = computed(() => {
  const currentKey = pathToMenuKey[route.path]
  return titleMapping[currentKey] || '数据体检'
})

// 方法
const handleMenuClick = ({ key }: { key: string }) => {
  const targetPath = routeMapping[key]
  if (targetPath && targetPath !== route.path) {
    router.push(targetPath)
  }
}

// 监听路由变化，更新选中状态
watch(() => route.path, (newPath) => {
  const menuKey = pathToMenuKey[newPath]
  if (menuKey) {
    selectedKeys.value = [menuKey]
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 初始化选中状态
  const currentKey = pathToMenuKey[route.path]
  if (currentKey) {
    selectedKeys.value = [currentKey]
  } else {
    // 如果是默认进入health-check，跳转到dashboard
    if (route.path === '/health-check' || route.path === '/health-check/') {
      router.replace('/health-check/dashboard')
    }
  }
})
</script>

<style scoped>
.health-check-layout {
  height: 100vh;
}

.logo-section {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.logo-content {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.logo-icon {
  font-size: 20px;
  margin-right: 8px;
}

.logo-text {
  white-space: nowrap;
}

/* 自定义菜单分组标题样式 */
:deep(.ant-menu-item-group-title) {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  padding: 8px 0 4px 0;
}

/* 菜单项样式 */
:deep(.ant-menu-item) {
  height: 40px;
  line-height: 40px;
  margin-bottom: 4px;
  border-radius: 6px;
}

:deep(.ant-menu-item-selected) {
  background-color: #e6f7ff;
}

/* 面包屑样式 */
:deep(.ant-breadcrumb) {
  font-size: 14px;
}

:deep(.ant-breadcrumb a) {
  color: #666;
  text-decoration: none;
}

:deep(.ant-breadcrumb a:hover) {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-text {
    display: none;
  }
  
  :deep(.ant-layout-sider) {
    width: 60px !important;
  }
}
</style>