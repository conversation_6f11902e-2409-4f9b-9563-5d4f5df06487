import type {
  CaseCollectionActivity,
  CaseSubmission,
  CaseReview,
  CaseCategory,
  CaseCollectionStatistics,
  ActivityProgress,
  PageResult,
  ApiResponse
} from '@/types/case-collection';

import {
  CaseCollectionActivityStatus,
  CaseSubmissionStatus,
  CaseReviewStatus,
  CaseReviewResult,
  ActivityPriority,
  CaseCategoryStatus
} from '@/types/case-collection';

// ==================== 模拟数据生成工具 ====================

/**
 * 生成随机ID
 */
function generateId(): number {
  return Math.floor(Math.random() * 10000) + 1;
}

/**
 * 生成随机日期
 */
function generateDate(daysOffset: number = 0): string {
  const date = new Date();
  date.setDate(date.getDate() + daysOffset);
  return date.toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * 生成随机字符串
 */
function generateRandomString(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// ==================== 模拟案例分类数据 ====================

export const mockCategories: CaseCategory[] = [
  {
    id: 1,
    name: '技术创新',
    description: '技术创新类案例',
    parentId: undefined,
    level: 1,
    path: '/1',
    sortOrder: 1,
    icon: 'tech',
    color: '#1890ff',
    status: CaseCategoryStatus.ACTIVE,
    usageCount: 25,
    createTime: generateDate(-30),
    updateTime: generateDate(-1),
    createUser: 1001,
    children: [
      {
        id: 11,
        name: '人工智能',
        description: 'AI相关技术创新',
        parentId: 1,
        level: 2,
        path: '/1/11',
        sortOrder: 1,
        status: CaseCategoryStatus.ACTIVE,
        usageCount: 12,
        createTime: generateDate(-25),
        updateTime: generateDate(-1),
        createUser: 1001
      },
      {
        id: 12,
        name: '大数据',
        description: '大数据技术应用',
        parentId: 1,
        level: 2,
        path: '/1/12',
        sortOrder: 2,
        status: CaseCategoryStatus.ACTIVE,
        usageCount: 8,
        createTime: generateDate(-25),
        updateTime: generateDate(-1),
        createUser: 1001
      }
    ]
  },
  {
    id: 2,
    name: '管理创新',
    description: '管理创新类案例',
    parentId: undefined,
    level: 1,
    path: '/2',
    sortOrder: 2,
    icon: 'management',
    color: '#52c41a',
    status: CaseCategoryStatus.ACTIVE,
    usageCount: 18,
    createTime: generateDate(-30),
    updateTime: generateDate(-1),
    createUser: 1001,
    children: []
  },
  {
    id: 3,
    name: '服务创新',
    description: '服务创新类案例',
    parentId: undefined,
    level: 1,
    path: '/3',
    sortOrder: 3,
    icon: 'service',
    color: '#fa8c16',
    status: CaseCategoryStatus.ACTIVE,
    usageCount: 15,
    createTime: generateDate(-30),
    updateTime: generateDate(-1),
    createUser: 1001,
    children: []
  }
];

// ==================== 模拟案例征集活动数据 ====================

export const mockActivities: CaseCollectionActivity[] = [
  {
    id: 1,
    title: '2024年度数字化转型优秀案例征集',
    description: '征集各单位在数字化转型过程中的优秀实践案例，推广先进经验',
    content: '为深入推进数字化转型工作，现面向全系统征集数字化转型优秀案例...',
    theme: '数字化转型',
    rules: '1. 案例必须真实有效\n2. 具有推广价值\n3. 提交完整材料',
    startTime: generateDate(-10),
    endTime: generateDate(30),
    submitDeadline: generateDate(25),
    maxSubmissions: 100,
    allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'png'],
    maxFileSize: 10,
    awards: ['一等奖3名', '二等奖5名', '三等奖10名'],
    totalPrize: 50000,
    contactInfo: {
      name: '张三',
      phone: '13800138000',
      email: '<EMAIL>',
      department: '信息化部'
    },
    requirements: ['具有创新性', '可复制推广', '效果显著'],
    tags: ['数字化', '转型', '创新'],
    coverImage: undefined, // 移除封面图片以测试默认背景
    images: ['/images/activity1-1.jpg', '/images/activity1-2.jpg'],
    attachments: ['/files/activity1-guide.pdf'],
    status: CaseCollectionActivityStatus.ACTIVE,
    priority: ActivityPriority.HIGH,
    currentSubmissions: 35,
    viewCount: 256,
    isPublic: true,
    organizerId: 1001,
    organizerName: '李四',
    regionId: 1,
    createTime: generateDate(-15),
    updateTime: generateDate(-1),
    createUser: 1001,
    createUserName: '李四',
    publishTime: generateDate(-10),
    metadata: { featured: true }
  },
  {
    id: 2,
    title: '绿色发展实践案例征集活动',
    description: '征集绿色发展、节能减排方面的优秀实践案例',
    content: '为推进绿色发展理念，现征集相关优秀案例...',
    theme: '绿色发展',
    rules: '1. 突出环保效益\n2. 具有示范意义\n3. 数据真实可靠',
    startTime: generateDate(-5),
    endTime: generateDate(45),
    submitDeadline: generateDate(40),
    maxSubmissions: 50,
    allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'png', 'mp4'],
    maxFileSize: 20,
    awards: ['优秀奖10名'],
    totalPrize: 20000,
    contactInfo: {
      name: '王五',
      phone: '13900139000',
      email: '<EMAIL>',
      department: '环保部'
    },
    requirements: ['环保效益明显', '可持续发展', '技术先进'],
    tags: ['绿色', '环保', '节能'],
    coverImage: undefined, // 移除封面图片以测试默认背景
    images: ['/images/activity2-1.jpg'],
    attachments: [],
    status: CaseCollectionActivityStatus.ACTIVE,
    priority: ActivityPriority.NORMAL,
    currentSubmissions: 12,
    viewCount: 128,
    isPublic: true,
    organizerId: 1002,
    organizerName: '赵六',
    regionId: 1,
    createTime: generateDate(-8),
    updateTime: generateDate(-1),
    createUser: 1002,
    createUserName: '赵六',
    publishTime: generateDate(-5),
    metadata: { category: 'environment' }
  },
  {
    id: 3,
    title: '党建工作创新案例征集',
    description: '征集党建工作创新做法和典型经验',
    content: '为加强党建工作交流，现征集创新案例...',
    theme: '党建创新',
    rules: '1. 体现党建特色\n2. 具有创新性\n3. 效果突出',
    startTime: generateDate(5),
    endTime: generateDate(60),
    submitDeadline: generateDate(55),
    maxSubmissions: 80,
    allowedFileTypes: ['pdf', 'doc', 'docx'],
    maxFileSize: 5,
    awards: ['特等奖1名', '一等奖2名', '二等奖5名'],
    totalPrize: 30000,
    contactInfo: {
      name: '孙七',
      phone: '13700137000',
      email: '<EMAIL>',
      department: '组织部'
    },
    requirements: ['政治性强', '创新突出', '可推广'],
    tags: ['党建', '创新', '组织'],
    coverImage: undefined, // 移除封面图片以测试默认背景
    images: [],
    attachments: ['/files/activity3-template.doc'],
    status: CaseCollectionActivityStatus.PUBLISHED,
    priority: ActivityPriority.HIGH,
    currentSubmissions: 0,
    viewCount: 89,
    isPublic: true,
    organizerId: 1003,
    organizerName: '周八',
    regionId: 1,
    createTime: generateDate(-2),
    updateTime: generateDate(-1),
    createUser: 1003,
    createUserName: '周八',
    publishTime: undefined,
    metadata: JSON.stringify({ department: 'party' })
  }
];

// ==================== 模拟案例提交数据 ====================

export const mockSubmissions: CaseSubmission[] = [
  {
    id: 1,
    activityId: 1,
    activityTitle: '2024年度数字化转型优秀案例征集',
    title: '智慧办公平台建设案例',
    summary: '通过建设智慧办公平台，实现了办公流程数字化，提高了工作效率',
    content: '我单位在数字化转型过程中，重点建设了智慧办公平台...',
    submitterName: '张明',
    submitterDepartment: '信息中心',
    submitterContact: '13800138001',
    submitterEmail: '<EMAIL>',
    categoryId: 1,
    tags: ['智慧办公', '数字化', '效率提升'],
    coverImage: '/images/case1-cover.jpg',
    images: ['/images/case1-1.jpg', '/images/case1-2.jpg'],
    attachments: JSON.stringify([
      { name: '案例详细说明.pdf', url: '/files/case1-detail.pdf', size: 2048000, type: 'pdf' }
    ]),
    status: CaseSubmissionStatus.APPROVED,
    submitTime: generateDate(-8),
    updateTime: generateDate(-3),
    reviewTime: generateDate(-3),
    reviewerId: 2001,
    reviewerName: '审核员A',
    reviewComments: '案例内容详实，具有很好的推广价值',
    reviewScore: 92.5,
    isQualified: true,
    viewCount: 45,
    downloadCount: 12,
    organizationId: 1001,
    regionId: 1,
    createTime: generateDate(-10),
    createUser: 3001,
    metadata: JSON.stringify({ featured: true })
  },
  {
    id: 2,
    activityId: 1,
    activityTitle: '2024年度数字化转型优秀案例征集',
    title: '数据中台建设实践',
    summary: '构建统一数据中台，实现数据资源整合和共享',
    content: '为解决数据孤岛问题，我们建设了统一的数据中台...',
    submitterName: '李华',
    submitterDepartment: '数据部',
    submitterContact: '13800138002',
    submitterEmail: '<EMAIL>',
    categoryId: 12,
    tags: ['数据中台', '数据整合', '共享'],
    coverImage: '/images/case2-cover.jpg',
    images: ['/images/case2-1.jpg'],
    attachments: JSON.stringify([
      { name: '技术方案.docx', url: '/files/case2-tech.docx', size: 1024000, type: 'docx' }
    ]),
    status: CaseSubmissionStatus.REVIEWING,
    submitTime: generateDate(-5),
    updateTime: generateDate(-2),
    reviewTime: undefined,
    reviewerId: undefined,
    reviewerName: undefined,
    reviewComments: undefined,
    reviewScore: undefined,
    isQualified: false,
    viewCount: 23,
    downloadCount: 5,
    organizationId: 1002,
    regionId: 1,
    createTime: generateDate(-7),
    createUser: 3002,
    metadata: { priority: 'high' }
  },
  {
    id: 3,
    activityId: 2,
    activityTitle: '绿色发展实践案例征集活动',
    title: '节能减排技术改造案例',
    summary: '通过技术改造实现节能减排，降低运营成本',
    content: '我单位通过实施节能减排技术改造项目...',
    submitterName: '王强',
    submitterDepartment: '工程部',
    submitterContact: '13800138003',
    submitterEmail: '<EMAIL>',
    categoryId: 2,
    tags: ['节能减排', '技术改造', '成本控制'],
    coverImage: '/images/case3-cover.jpg',
    images: [],
    attachments: JSON.stringify([
      { name: '改造前后对比.pdf', url: '/files/case3-compare.pdf', size: 3072000, type: 'pdf' }
    ]),
    status: CaseSubmissionStatus.REJECTED,
    submitTime: generateDate(-12),
    updateTime: generateDate(-8),
    reviewTime: generateDate(-8),
    reviewerId: 2002,
    reviewerName: '审核员B',
    reviewComments: '案例材料不够完整，建议补充相关数据',
    reviewScore: 65.0,
    isQualified: false,
    viewCount: 18,
    downloadCount: 3,
    organizationId: 1003,
    regionId: 1,
    createTime: generateDate(-15),
    createUser: 3003,
    metadata: JSON.stringify({ needRevision: true })
  }
];

// ==================== 模拟案例预审数据 ====================

export const mockReviews: CaseReview[] = [
  {
    id: 1,
    activityId: 1,
    activityTitle: '2024年度数字化转型优秀案例征集',
    submissionId: 1,
    submissionTitle: '智慧办公平台建设案例',
    submitterName: '张明',
    reviewerId: 2001,
    reviewerName: '审核员A',
    reviewResult: CaseReviewResult.PASS,
    score: 92.5,
    comments: '案例内容详实，技术方案先进，具有很好的推广价值。建议作为优秀案例进行推广。',
    suggestions: '可以进一步补充成本效益分析',
    reviewTime: generateDate(-3),
    status: CaseReviewStatus.COMPLETED,
    isPass: true,
    nextStep: '推荐参评优秀案例',
    attachments: JSON.stringify([]),
    createTime: generateDate(-3),
    updateTime: generateDate(-3),
    metadata: JSON.stringify({ score_breakdown: { innovation: 95, practicality: 90, effect: 92 } })
  },
  {
    id: 2,
    activityId: 1,
    activityTitle: '2024年度数字化转型优秀案例征集',
    submissionId: 3,
    submissionTitle: '节能减排技术改造案例',
    submitterName: '王强',
    reviewerId: 2002,
    reviewerName: '审核员B',
    reviewResult: CaseReviewResult.REJECT,
    score: 65.0,
    comments: '案例材料不够完整，缺少关键数据支撑，效果描述不够具体。',
    suggestions: '1. 补充改造前后的具体数据对比\n2. 提供第三方验证报告\n3. 完善成本效益分析',
    reviewTime: generateDate(-8),
    status: CaseReviewStatus.COMPLETED,
    isPass: false,
    nextStep: '建议修改后重新提交',
    attachments: JSON.stringify([]),
    createTime: generateDate(-8),
    updateTime: generateDate(-8),
    metadata: JSON.stringify({ rejection_reason: 'insufficient_data' })
  }
];

// ==================== 模拟统计数据 ====================

export const mockStatistics: CaseCollectionStatistics = {
  totalActivities: 15,
  activeActivities: 8,
  endedActivities: 5,
  totalSubmissions: 127,
  pendingReviews: 23,
  approvedSubmissions: 78,
  rejectedSubmissions: 26,
  averageScore: 82.3,
  submissionsByActivity: {
    1: 35,
    2: 12,
    3: 0,
    4: 28,
    5: 52
  },
  submissionsByStatus: {
    [CaseSubmissionStatus.DRAFT]: 8,
    [CaseSubmissionStatus.SUBMITTED]: 15,
    [CaseSubmissionStatus.REVIEWING]: 23,
    [CaseSubmissionStatus.APPROVED]: 78,
    [CaseSubmissionStatus.REJECTED]: 26,
    [CaseSubmissionStatus.WITHDRAWN]: 3
  },
  reviewsByResult: {
    [CaseReviewResult.PASS]: 78,
    [CaseReviewResult.REJECT]: 26,
    [CaseReviewResult.REVISE]: 12
  },
  recentActivities: mockActivities.slice(0, 3) || [],
  popularActivities: mockActivities.slice(0, 2) || [],
  topSubmitters: [
    {
      submitterName: '张明',
      submitterDepartment: '信息中心',
      totalSubmissions: 5,
      approvedSubmissions: 4,
      averageScore: 88.5,
      successRate: 80
    },
    {
      submitterName: '李华',
      submitterDepartment: '数据部',
      totalSubmissions: 3,
      approvedSubmissions: 3,
      averageScore: 91.2,
      successRate: 100
    }
  ]
};

// ==================== API响应包装函数 ====================

/**
 * 包装API响应
 */
export function wrapApiResponse<T>(data: T, success: boolean = true, message: string = 'success'): ApiResponse<T> {
  return {
    code: success ? 0 : 500,
    message,
    data,
    success,
    timestamp: new Date().toISOString()
  };
}

/**
 * 包装分页响应
 */
export function wrapPageResult<T>(list: T[], total: number, page: number = 1, pageSize: number = 10): PageResult<T> {
  return {
    list,
    total,
    page,
    pageSize,
    totalPages: Math.ceil(total / pageSize)
  };
}

// ==================== 模拟API延迟 ====================

/**
 * 模拟API延迟
 */
export function mockDelay(ms: number = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}
