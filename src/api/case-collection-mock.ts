import type {
  CaseCollectionActivity,
  CaseSubmission,
  CaseReview,
  CaseCategory,
  CaseCollectionActivityQueryParams,
  CaseSubmissionQueryParams,
  CaseReviewQueryParams,
  PageResult,
  ApiResponse
} from '@/types/case-collection';

import {
  mockActivities,
  mockSubmissions,
  mockReviews,
  mockCategories,
  mockStatistics,
  wrapApiResponse,
  wrapPageResult,
  mockDelay
} from '@/mock/case-collection';

// ==================== 模拟案例征集活动管理 API ====================

/**
 * 模拟创建案例征集活动
 */
export async function mockCreateActivity(data: Partial<CaseCollectionActivity>): Promise<ApiResponse<CaseCollectionActivity>> {
  await mockDelay();
  
  const newActivity: CaseCollectionActivity = {
    id: Math.max(...mockActivities.map(a => a.id)) + 1,
    title: data.title || '',
    description: data.description || '',
    content: data.content || '',
    theme: data.theme || '',
    rules: data.rules || '',
    startTime: data.startTime || new Date().toISOString().slice(0, 19).replace('T', ' '),
    endTime: data.endTime || new Date().toISOString().slice(0, 19).replace('T', ' '),
    submitDeadline: data.submitDeadline || new Date().toISOString().slice(0, 19).replace('T', ' '),
    maxSubmissions: data.maxSubmissions || 100,
    allowedFileTypes: data.allowedFileTypes || ['pdf', 'doc', 'docx'],
    maxFileSize: data.maxFileSize || 10,
    awards: data.awards || [],
    totalPrize: data.totalPrize || 0,
    contactInfo: data.contactInfo || '',
    requirements: data.requirements || [],
    tags: data.tags || [],
    coverImage: data.coverImage || '',
    images: data.images || [],
    attachments: data.attachments || [],
    status: data.status || 'draft',
    priority: data.priority || 2,
    currentSubmissions: 0,
    viewCount: 0,
    isPublic: data.isPublic !== undefined ? data.isPublic : true,
    organizerId: data.organizerId || 1001,
    organizerName: data.organizerName || '系统用户',
    regionId: data.regionId || 1,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    createUser: data.createUser || 1001,
    createUserName: data.createUserName || '系统用户',
    publishTime: data.publishTime,
    metadata: data.metadata || ''
  };

  mockActivities.push(newActivity);
  return wrapApiResponse(newActivity);
}

/**
 * 模拟更新案例征集活动
 */
export async function mockUpdateActivity(id: number, data: Partial<CaseCollectionActivity>): Promise<ApiResponse<CaseCollectionActivity>> {
  await mockDelay();
  
  const index = mockActivities.findIndex(a => a.id === id);
  if (index === -1) {
    return wrapApiResponse(null as any, false, '活动不存在');
  }

  const updatedActivity = {
    ...mockActivities[index],
    ...data,
    id,
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  };

  mockActivities[index] = updatedActivity;
  return wrapApiResponse(updatedActivity);
}

/**
 * 模拟删除案例征集活动
 */
export async function mockDeleteActivity(id: number): Promise<ApiResponse<boolean>> {
  await mockDelay();
  
  const index = mockActivities.findIndex(a => a.id === id);
  if (index === -1) {
    return wrapApiResponse(false, false, '活动不存在');
  }

  mockActivities.splice(index, 1);
  return wrapApiResponse(true);
}

/**
 * 模拟查询活动详情
 */
export async function mockGetActivityDetail(id: number): Promise<ApiResponse<CaseCollectionActivity>> {
  await mockDelay();
  
  const activity = mockActivities.find(a => a.id === id);
  if (!activity) {
    return wrapApiResponse(null as any, false, '活动不存在');
  }

  // 模拟增加浏览次数
  activity.viewCount = (activity.viewCount || 0) + 1;
  
  return wrapApiResponse(activity);
}

/**
 * 模拟分页查询活动列表
 */
export async function mockGetActivityList(params?: CaseCollectionActivityQueryParams): Promise<ApiResponse<PageResult<CaseCollectionActivity>>> {
  await mockDelay();
  
  let filteredActivities = [...mockActivities];

  // 应用筛选条件
  if (params?.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredActivities = filteredActivities.filter(a => 
      a.title.toLowerCase().includes(keyword) || 
      a.description.toLowerCase().includes(keyword)
    );
  }

  if (params?.status) {
    filteredActivities = filteredActivities.filter(a => a.status === params.status);
  }

  if (params?.priority) {
    filteredActivities = filteredActivities.filter(a => a.priority === params.priority);
  }

  // 排序
  filteredActivities.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

  // 分页
  const page = params?.page || 1;
  const pageSize = params?.pageSize || 10;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedList = filteredActivities.slice(startIndex, endIndex);

  const pageResult = wrapPageResult(paginatedList, filteredActivities.length, page, pageSize);
  return wrapApiResponse(pageResult);
}

// ==================== 模拟案例提交管理 API ====================

/**
 * 模拟提交案例
 */
export async function mockSubmitCase(data: Partial<CaseSubmission>): Promise<ApiResponse<CaseSubmission>> {
  await mockDelay();
  
  const activity = mockActivities.find(a => a.id === data.activityId);
  if (!activity) {
    return wrapApiResponse(null as any, false, '活动不存在');
  }

  const newSubmission: CaseSubmission = {
    id: Math.max(...mockSubmissions.map(s => s.id)) + 1,
    activityId: data.activityId || 0,
    activityTitle: activity.title,
    title: data.title || '',
    summary: data.summary || '',
    content: data.content || '',
    submitterName: data.submitterName || '',
    submitterDepartment: data.submitterDepartment || '',
    submitterContact: data.submitterContact || '',
    submitterEmail: data.submitterEmail || '',
    categoryId: data.categoryId,
    tags: data.tags || [],
    coverImage: data.coverImage || '',
    images: data.images || [],
    attachments: data.attachments || '',
    status: data.status || 'draft',
    submitTime: data.status === 'submitted' ? new Date().toISOString().slice(0, 19).replace('T', ' ') : undefined,
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    reviewTime: undefined,
    reviewerId: undefined,
    reviewerName: undefined,
    reviewComments: undefined,
    reviewScore: undefined,
    isQualified: false,
    viewCount: 0,
    downloadCount: 0,
    organizationId: data.organizationId || 1001,
    regionId: data.regionId || 1,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    createUser: data.createUser || 1001,
    metadata: data.metadata || ''
  };

  mockSubmissions.push(newSubmission);

  // 如果是提交状态，更新活动的提交数量
  if (data.status === 'submitted') {
    activity.currentSubmissions = (activity.currentSubmissions || 0) + 1;
  }

  return wrapApiResponse(newSubmission);
}

/**
 * 模拟更新案例提交
 */
export async function mockUpdateSubmission(id: number, data: Partial<CaseSubmission>): Promise<ApiResponse<CaseSubmission>> {
  await mockDelay();
  
  const index = mockSubmissions.findIndex(s => s.id === id);
  if (index === -1) {
    return wrapApiResponse(null as any, false, '案例提交不存在');
  }

  const oldStatus = mockSubmissions[index].status;
  const updatedSubmission = {
    ...mockSubmissions[index],
    ...data,
    id,
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
  };

  // 如果状态从草稿变为提交，设置提交时间并更新活动提交数量
  if (oldStatus === 'draft' && data.status === 'submitted') {
    updatedSubmission.submitTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
    const activity = mockActivities.find(a => a.id === updatedSubmission.activityId);
    if (activity) {
      activity.currentSubmissions = (activity.currentSubmissions || 0) + 1;
    }
  }

  mockSubmissions[index] = updatedSubmission;
  return wrapApiResponse(updatedSubmission);
}

/**
 * 模拟分页查询案例提交列表
 */
export async function mockGetSubmissionList(params?: CaseSubmissionQueryParams): Promise<ApiResponse<PageResult<CaseSubmission>>> {
  await mockDelay();
  
  let filteredSubmissions = [...mockSubmissions];

  // 应用筛选条件
  if (params?.activityId) {
    filteredSubmissions = filteredSubmissions.filter(s => s.activityId === params.activityId);
  }

  if (params?.keyword) {
    const keyword = params.keyword.toLowerCase();
    filteredSubmissions = filteredSubmissions.filter(s => 
      s.title.toLowerCase().includes(keyword) || 
      s.summary.toLowerCase().includes(keyword)
    );
  }

  if (params?.status) {
    filteredSubmissions = filteredSubmissions.filter(s => s.status === params.status);
  }

  if (params?.submitterName) {
    filteredSubmissions = filteredSubmissions.filter(s => 
      s.submitterName.includes(params.submitterName!)
    );
  }

  // 排序
  filteredSubmissions.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

  // 分页
  const page = params?.page || 1;
  const pageSize = params?.pageSize || 10;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedList = filteredSubmissions.slice(startIndex, endIndex);

  const pageResult = wrapPageResult(paginatedList, filteredSubmissions.length, page, pageSize);
  return wrapApiResponse(pageResult);
}

// ==================== 模拟案例预审管理 API ====================

/**
 * 模拟创建案例预审
 */
export async function mockCreateReview(data: Partial<CaseReview>): Promise<ApiResponse<CaseReview>> {
  await mockDelay();
  
  const submission = mockSubmissions.find(s => s.id === data.submissionId);
  if (!submission) {
    return wrapApiResponse(null as any, false, '案例提交不存在');
  }

  const newReview: CaseReview = {
    id: Math.max(...mockReviews.map(r => r.id)) + 1,
    activityId: submission.activityId,
    activityTitle: submission.activityTitle,
    submissionId: data.submissionId || 0,
    submissionTitle: submission.title,
    submitterName: submission.submitterName,
    reviewerId: data.reviewerId || 2001,
    reviewerName: data.reviewerName || '审核员',
    reviewResult: data.reviewResult || 1,
    score: data.score || 0,
    comments: data.comments || '',
    suggestions: data.suggestions || '',
    reviewTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    status: 'completed',
    isPass: data.reviewResult === 1,
    nextStep: data.reviewResult === 1 ? '推荐参评' : '建议修改',
    attachments: data.attachments || '',
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    metadata: data.metadata || ''
  };

  mockReviews.push(newReview);

  // 更新案例提交状态
  const newStatus = data.reviewResult === 1 ? 'approved' : 
                   data.reviewResult === 2 ? 'rejected' : 'submitted';
  submission.status = newStatus;
  submission.reviewTime = newReview.reviewTime;
  submission.reviewerId = newReview.reviewerId;
  submission.reviewerName = newReview.reviewerName;
  submission.reviewComments = newReview.comments;
  submission.reviewScore = newReview.score;
  submission.isQualified = newReview.isPass;
  submission.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

  return wrapApiResponse(newReview);
}

// ==================== 模拟分类管理 API ====================

/**
 * 模拟获取所有分类
 */
export async function mockGetAllCategories(): Promise<ApiResponse<CaseCategory[]>> {
  await mockDelay();
  return wrapApiResponse(mockCategories);
}

/**
 * 模拟创建案例分类
 */
export async function mockCreateCategory(data: Partial<CaseCategory>): Promise<ApiResponse<CaseCategory>> {
  await mockDelay();
  
  const newCategory: CaseCategory = {
    id: Math.max(...mockCategories.map(c => c.id)) + 1,
    name: data.name || '',
    description: data.description || '',
    parentId: data.parentId,
    level: data.parentId ? 2 : 1,
    path: data.parentId ? `/1/${Math.max(...mockCategories.map(c => c.id)) + 1}` : `/${Math.max(...mockCategories.map(c => c.id)) + 1}`,
    sortOrder: data.sortOrder || 0,
    icon: data.icon || '',
    color: data.color || '#1890ff',
    status: data.status || 1,
    usageCount: 0,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    createUser: data.createUser || 1001,
    children: []
  };

  mockCategories.push(newCategory);
  return wrapApiResponse(newCategory);
}

// ==================== 模拟统计查询 API ====================

/**
 * 模拟获取活动统计数据
 */
export async function mockGetActivityStatistics(): Promise<ApiResponse<any>> {
  await mockDelay();
  return wrapApiResponse(mockStatistics);
}

/**
 * 模拟获取案例提交统计数据
 */
export async function mockGetSubmissionStatistics(activityId: number): Promise<ApiResponse<any>> {
  await mockDelay();

  const activitySubmissions = mockSubmissions.filter(s => s.activityId === activityId);
  const submissionsWithScore = activitySubmissions.filter(s => s.reviewScore && s.reviewScore > 0);

  const stats = {
    total: activitySubmissions.length,
    approved: activitySubmissions.filter(s => s.status === 'approved').length,
    rejected: activitySubmissions.filter(s => s.status === 'rejected').length,
    reviewing: activitySubmissions.filter(s => s.status === 'reviewing').length,
    averageScore: submissionsWithScore.length > 0
      ? Number((submissionsWithScore.reduce((sum, s) => sum + (s.reviewScore || 0), 0) / submissionsWithScore.length).toFixed(2))
      : 0
  };

  return wrapApiResponse(stats);
}

// ==================== 模拟文件管理 API ====================

/**
 * 模拟文件上传
 */
export async function mockUploadFile(file: File): Promise<ApiResponse<{ url: string; name: string; size: number }>> {
  await mockDelay(1000); // 模拟上传时间
  
  const fileInfo = {
    url: `/uploads/${Date.now()}_${file.name}`,
    name: file.name,
    size: file.size
  };

  return wrapApiResponse(fileInfo);
}

// ==================== 模拟审核管理 API ====================

/**
 * 模拟获取审核历史记录列表
 */
export async function mockGetReviewHistory(params?: {
  page?: number;
  pageSize?: number;
  activityId?: number;
  reviewResult?: number;
  reviewerName?: string;
  dateRange?: [string, string];
  keyword?: string;
}): Promise<ApiResponse<PageResult<any>>> {
  await mockDelay();

  // 模拟审核历史数据
  const mockHistoryData = [
    {
      id: 1,
      submissionId: 101,
      submissionTitle: '数字化转型案例分析',
      activityId: 1,
      activityTitle: '2024年优秀案例征集',
      reviewerName: '张三',
      reviewTime: '2024-01-15 14:30:00',
      reviewResult: 1, // 1-通过, 2-驳回, 3-需修改
      score: 85,
      comments: '案例内容详实，具有很好的参考价值',
      suggestions: '',
      submitterName: '李四',
      department: '信息技术部'
    },
    {
      id: 2,
      submissionId: 102,
      submissionTitle: '绿色办公实践案例',
      activityId: 1,
      activityTitle: '2024年优秀案例征集',
      reviewerName: '王五',
      reviewTime: '2024-01-14 16:45:00',
      reviewResult: 2,
      score: 45,
      comments: '案例缺乏具体数据支撑',
      suggestions: '建议补充量化指标和实施效果数据',
      submitterName: '赵六',
      department: '行政管理部'
    },
    {
      id: 3,
      submissionId: 103,
      submissionTitle: '创新服务模式探索',
      activityId: 2,
      activityTitle: '服务创新案例征集',
      reviewerName: '张三',
      reviewTime: '2024-01-13 10:20:00',
      reviewResult: 3,
      score: 70,
      comments: '整体思路清晰，但需要完善实施细节',
      suggestions: '建议增加具体的实施步骤和时间安排',
      submitterName: '孙七',
      department: '客户服务部'
    },
    {
      id: 4,
      submissionId: 104,
      submissionTitle: '智能客服系统建设',
      activityId: 1,
      activityTitle: '2024年优秀案例征集',
      reviewerName: '李八',
      reviewTime: '2024-01-12 09:15:00',
      reviewResult: 1,
      score: 92,
      comments: '技术方案先进，实施效果显著',
      suggestions: '',
      submitterName: '周九',
      department: '技术研发部'
    },
    {
      id: 5,
      submissionId: 105,
      submissionTitle: '供应链优化实践',
      activityId: 2,
      activityTitle: '服务创新案例征集',
      reviewerName: '王五',
      reviewTime: '2024-01-11 15:20:00',
      reviewResult: 1,
      score: 88,
      comments: '优化效果明显，具有推广价值',
      suggestions: '',
      submitterName: '吴十',
      department: '供应链管理部'
    }
  ];

  const page = params?.page || 1;
  const pageSize = params?.pageSize || 10;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  // 简单筛选逻辑
  let filteredData = mockHistoryData;
  if (params?.activityId) {
    filteredData = filteredData.filter(item => item.activityId === params.activityId);
  }
  if (params?.reviewResult) {
    filteredData = filteredData.filter(item => item.reviewResult === params.reviewResult);
  }
  if (params?.reviewerName) {
    filteredData = filteredData.filter(item => item.reviewerName.includes(params.reviewerName!));
  }
  if (params?.keyword) {
    filteredData = filteredData.filter(item =>
      item.submissionTitle.includes(params.keyword!) ||
      item.comments.includes(params.keyword!)
    );
  }

  const paginatedList = filteredData.slice(startIndex, endIndex);

  return wrapApiResponse({
    list: paginatedList,
    total: filteredData.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredData.length / pageSize)
  });
}

/**
 * 模拟获取审核统计数据
 */
export async function mockGetReviewStatistics(params?: {
  activityId?: number;
  dateRange?: [string, string];
}): Promise<ApiResponse<any>> {
  await mockDelay();

  // 模拟审核统计数据
  const mockStatistics = {
    totalReviews: 156,
    todayReviews: 8,
    pendingReviews: 23,
    approvedCount: 89,
    rejectedCount: 34,
    needRevisionCount: 33,
    approvalRate: 57.1,
    averageScore: 76.8,
    averageReviewTime: 2.5, // 小时
    reviewerStats: [
      { reviewerName: '张三', reviewCount: 45, averageScore: 78.2 },
      { reviewerName: '王五', reviewCount: 38, averageScore: 75.6 },
      { reviewerName: '李七', reviewCount: 32, averageScore: 77.9 }
    ],
    monthlyTrend: [
      { month: '2024-01', reviews: 42, approvalRate: 55.2 },
      { month: '2024-02', reviews: 38, approvalRate: 58.7 },
      { month: '2024-03', reviews: 45, approvalRate: 62.1 },
      { month: '2024-04', reviews: 31, approvalRate: 54.8 }
    ],
    scoreDistribution: [
      { range: '90-100', count: 23 },
      { range: '80-89', count: 34 },
      { range: '70-79', count: 28 },
      { range: '60-69', count: 19 },
      { range: '0-59', count: 52 }
    ]
  };

  return wrapApiResponse(mockStatistics);
}

/**
 * 模拟获取当前审核设置
 */
export async function mockGetReviewSettings(): Promise<ApiResponse<any>> {
  await mockDelay();

  // 模拟审核设置数据
  const mockSettings = {
    id: 1,
    autoAssignReviewer: true,
    maxReviewTime: 72, // 小时
    requireDoubleReview: false,
    passingScore: 60,
    excellentScore: 85,
    notificationEnabled: true,
    emailNotification: true,
    smsNotification: false,
    reviewRules: {
      allowSelfReview: false,
      requireExpertReview: true,
      maxReviewsPerDay: 20
    },
    scoringCriteria: [
      { name: '内容质量', weight: 40, description: '案例内容的完整性和准确性' },
      { name: '创新性', weight: 25, description: '案例的创新程度和独特性' },
      { name: '实用性', weight: 20, description: '案例的实际应用价值' },
      { name: '表达清晰度', weight: 15, description: '案例描述的清晰度和逻辑性' }
    ],
    updateTime: '2024-01-10 09:30:00',
    updatedBy: '系统管理员'
  };

  return wrapApiResponse(mockSettings);
}

/**
 * 模拟更新审核设置
 */
export async function mockUpdateReviewSettings(data: any): Promise<ApiResponse<boolean>> {
  await mockDelay();

  // 模拟更新审核设置
  console.log('更新审核设置:', data);
  return wrapApiResponse(true, true, '审核设置更新成功');
}

/**
 * 模拟获取审核模板列表
 */
export async function mockGetReviewTemplates(): Promise<ApiResponse<any[]>> {
  await mockDelay();

  // 模拟审核模板数据
  const mockTemplates = [
    {
      id: 1,
      name: '通过模板',
      type: 'approve',
      reviewResult: 1,
      score: 80,
      comments: '案例内容完整，质量良好，符合征集要求，予以通过。',
      suggestions: '',
      isDefault: true,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      usageCount: 45
    },
    {
      id: 2,
      name: '驳回-材料不完整',
      type: 'reject_incomplete',
      reviewResult: 2,
      score: 30,
      comments: '提交的案例材料不完整，缺少关键信息，无法进行有效评估。',
      suggestions: '请补充完整的案例背景、实施过程、效果评估等关键信息后重新提交。',
      isDefault: true,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      usageCount: 23
    },
    {
      id: 3,
      name: '驳回-质量不达标',
      type: 'reject_quality',
      reviewResult: 2,
      score: 40,
      comments: '案例质量不符合征集标准，内容缺乏深度和创新性。',
      suggestions: '建议重新梳理案例内容，突出创新点和实际效果，提升案例质量。',
      isDefault: true,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      usageCount: 18
    },
    {
      id: 4,
      name: '修改-格式问题',
      type: 'revise_format',
      reviewResult: 3,
      score: 65,
      comments: '案例内容基本符合要求，但格式需要调整。',
      suggestions: '请按照征集要求调整案例格式，包括标题、段落结构、图表规范等。',
      isDefault: true,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      usageCount: 12
    },
    {
      id: 5,
      name: '修改-内容问题',
      type: 'revise_content',
      reviewResult: 3,
      score: 70,
      comments: '案例整体框架良好，但部分内容需要完善。',
      suggestions: '建议补充具体的数据支撑，完善实施效果描述，增强案例说服力。',
      isDefault: true,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      usageCount: 15
    }
  ];

  return wrapApiResponse(mockTemplates);
}

/**
 * 模拟保存审核模板
 */
export async function mockSaveReviewTemplate(data: {
  id?: number;
  name: string;
  type: string;
  reviewResult: number;
  score: number;
  comments: string;
  suggestions?: string;
}): Promise<ApiResponse<any>> {
  await mockDelay();

  // 模拟保存审核模板
  const template = {
    id: data.id || Date.now(),
    ...data,
    isDefault: false,
    createTime: data.id ? undefined : new Date().toISOString().slice(0, 19).replace('T', ' '),
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    usageCount: 0
  };

  return wrapApiResponse(template, true, data.id ? '模板更新成功' : '模板创建成功');
}

/**
 * 模拟删除审核模板
 */
export async function mockDeleteReviewTemplate(id: number): Promise<ApiResponse<boolean>> {
  await mockDelay();

  // 模拟删除审核模板
  console.log('删除审核模板:', id);
  return wrapApiResponse(true, true, '模板删除成功');
}

/**
 * 模拟导出审核历史数据
 */
export async function mockExportReviewHistory(params?: {
  activityId?: number;
  reviewResult?: number;
  reviewerName?: string;
  dateRange?: [string, string];
  keyword?: string;
}): Promise<Blob> {
  await mockDelay(1000); // 模拟导出时间

  // 模拟导出Excel
  console.log('导出审核历史:', params);
  const blob = new Blob(['审核历史数据导出'], { type: 'application/vnd.ms-excel' });
  return blob;
}
