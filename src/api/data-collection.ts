/**
 * 数据收集模块API接口
 * 包含数据源管理、数据同步、监控统计等功能
 */

import axios from '@/utils/request'

// ============================= 类型定义 =============================

/** 数据源类型 */
export type DataSourceType = 'database' | 'api' | 'file' | 'manual' | 'message_queue' | 'ftp' | 'sftp'

/** 数据收集状态 */
export type DataCollectionStatus = 'collecting' | 'processing' | 'completed' | 'failed' | 'stopped' | 'pending'

/** 连接状态 */
export type ConnectionStatus = 'online' | 'offline' | 'connecting' | 'timeout' | 'error'

/** 同步类型 */
export type SyncType = 'full' | 'incremental' | 'real_time'

/** 数据源配置 */
export interface DataSourceConfig {
  // 数据库配置
  host?: string
  port?: number
  database?: string
  username?: string
  password?: string
  query?: string
  driverType?: 'mysql' | 'postgresql' | 'oracle' | 'sqlserver' | 'sqlite'
  
  // API配置
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  params?: Record<string, any>
  requestBody?: any
  authType?: 'none' | 'basic' | 'bearer' | 'apikey' | 'oauth2'
  authConfig?: Record<string, any>
  
  // 文件配置
  filePath?: string
  filePattern?: string  // 文件名匹配模式
  fileType?: 'csv' | 'excel' | 'json' | 'xml' | 'txt' | 'log'
  encoding?: 'utf-8' | 'gbk' | 'gb2312' | 'big5'
  delimiter?: string    // CSV分隔符
  hasHeader?: boolean   // 是否包含表头
  
  // 消息队列配置
  mqType?: 'rabbitmq' | 'kafka' | 'rocketmq' | 'activemq'
  brokerUrl?: string
  topic?: string
  queue?: string
  consumerGroup?: string
  
  // FTP/SFTP配置
  ftpHost?: string
  ftpPort?: number
  ftpUsername?: string
  ftpPassword?: string
  ftpPath?: string
  isPassive?: boolean
  
  // 通用配置
  timeout?: number      // 超时时间(ms)
  retryCount?: number   // 重试次数
  retryInterval?: number // 重试间隔(ms)
  batchSize?: number    // 批处理大小
  enableSsl?: boolean   // 启用SSL
  
  [key: string]: any
}

/** 数据源信息 */
export interface DataSource {
  id: string
  name: string
  description?: string
  type: DataSourceType
  category?: string      // 数据源分类
  config: DataSourceConfig
  isEnabled: boolean
  connectionStatus: ConnectionStatus
  lastConnectionTime?: string
  lastSyncTime?: string
  nextSyncTime?: string
  syncStatus: DataCollectionStatus
  syncType: SyncType
  syncFrequency?: number  // 同步频率(分钟)
  enableSchedule: boolean // 启用定时同步
  scheduleConfig?: {
    cron?: string        // Cron表达式
    startTime?: string   // 开始时间
    endTime?: string     // 结束时间
    timezone?: string    // 时区
  }
  
  // 统计信息
  totalRecords: number
  successRecords: number
  failedRecords: number
  lastRecordCount: number
  avgSyncDuration: number // 平均同步时长(秒)
  successRate: number     // 成功率(%)
  
  // 数据质量评分
  dataQualityScore: number
  qualityIssues?: Array<{
    type: string
    description: string
    count: number
    severity: 'high' | 'medium' | 'low'
  }>
  
  // 元数据
  tags?: string[]
  owner?: string
  ownerDept?: string
  createTime: string
  updateTime: string
  createdBy: string
  updatedBy?: string
  
  // 扩展信息
  metadata?: Record<string, any>
  errorMessage?: string
  lastErrorTime?: string
  
  // 运行时状态
  isRunning?: boolean
  isSwitching?: boolean  // 是否正在切换状态
}

/** 同步历史记录 */
export interface SyncHistory {
  id: string
  dataSourceId: string
  dataSourceName: string
  syncType: SyncType
  status: DataCollectionStatus
  startTime: string
  endTime?: string
  duration?: number      // 持续时间(秒)
  recordCount: number    // 处理记录数
  successCount: number   // 成功记录数
  failedCount: number    // 失败记录数
  errorMessage?: string
  errorDetails?: any
  
  // 性能指标
  throughput?: number    // 吞吐量(records/s)
  avgResponseTime?: number // 平均响应时间(ms)
  
  // 数据质量指标
  qualityScore?: number
  duplicateCount?: number
  invalidCount?: number
  missingCount?: number
  
  createTime: string
}

/** 数据流监控信息 */
export interface DataFlowMonitor {
  id: string
  dataSourceId: string
  dataSourceName: string
  timestamp: string
  
  // 实时指标
  currentThroughput: number    // 当前吞吐量
  avgThroughput: number        // 平均吞吐量
  peakThroughput: number       // 峰值吞吐量
  currentLatency: number       // 当前延迟(ms)
  avgLatency: number          // 平均延迟(ms)
  errorRate: number           // 错误率(%)
  
  // 资源使用情况
  cpuUsage?: number           // CPU使用率(%)
  memoryUsage?: number        // 内存使用率(%)
  diskUsage?: number          // 磁盘使用率(%)
  networkIO?: {              // 网络IO
    inbound: number          // 入站流量(KB/s)
    outbound: number         // 出站流量(KB/s)
  }
  
  // 队列状态
  queueDepth?: number         // 队列深度
  pendingTasks?: number       // 待处理任务数
  activeTasks?: number        // 活跃任务数
  
  // 状态信息
  status: DataCollectionStatus
  healthStatus: 'healthy' | 'warning' | 'critical'
  alerts?: Array<{
    level: 'info' | 'warn' | 'error'
    message: string
    timestamp: string
  }>
}

/** 数据统计信息 */
export interface DataCollectionStats {
  // 数据源统计
  totalSources: number
  onlineSources: number
  offlineSources: number
  activeSources: number      // 活跃数据源数
  enabledSources: number     // 启用数据源数
  
  // 同步统计
  todaySyncCount: number     // 今日同步次数
  todaySyncVolume: number    // 今日同步数据量
  avgSyncDuration: number    // 平均同步时长
  totalSyncCount: number     // 总同步次数
  totalDataVolume: number    // 总数据量
  
  // 质量统计
  qualityScore: number       // 平均质量评分
  qualityTrend: Array<{     // 质量趋势
    date: string
    score: number
    issues: number
  }>
  
  // 性能统计
  avgThroughput: number      // 平均吞吐量
  peakThroughput: number     // 峰值吞吐量
  successRate: number        // 整体成功率
  
  // 错误统计
  errorCount: number         // 错误总数
  criticalErrors: number     // 严重错误数
  errorTrend: Array<{       // 错误趋势
    date: string
    count: number
    type: string
  }>
  
  // 分类统计
  sourceTypeStats: Array<{   // 按类型统计
    type: DataSourceType
    count: number
    activeCount: number
    successRate: number
  }>
  
  // 部门统计
  departmentStats?: Array<{  // 按部门统计
    department: string
    sourceCount: number
    dataVolume: number
    qualityScore: number
  }>
}

/** 数据预览信息 */
export interface DataPreview {
  dataSourceId: string
  dataSourceName: string
  totalCount: number
  sampleData: Array<Record<string, any>>
  schema: Array<{
    field: string
    type: string
    nullable: boolean
    description?: string
  }>
  lastUpdateTime: string
}

/** 查询参数 */
export interface DataSourceQueryParams {
  keyword?: string           // 关键词搜索
  type?: DataSourceType     // 数据源类型
  category?: string         // 数据源分类
  status?: DataCollectionStatus // 同步状态
  connectionStatus?: ConnectionStatus // 连接状态
  owner?: string            // 所有者
  department?: string       // 所属部门
  tags?: string[]          // 标签
  isEnabled?: boolean      // 是否启用
  enableSchedule?: boolean // 是否启用定时同步
  qualityScoreRange?: [number, number] // 质量评分范围
  createTimeRange?: [string, string]   // 创建时间范围
  lastSyncTimeRange?: [string, string] // 最后同步时间范围
  
  // 分页参数
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/** 同步历史查询参数 */
export interface SyncHistoryQueryParams {
  dataSourceId?: string
  dataSourceIds?: string[]
  syncType?: SyncType
  status?: DataCollectionStatus
  timeRange?: [string, string]
  durationRange?: [number, number]  // 持续时间范围
  recordCountRange?: [number, number] // 记录数范围
  
  // 分页参数
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/** API响应类型 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

/** 分页响应类型 */
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

/** 操作结果类型 */
export interface OperationResult {
  success: boolean
  message: string
  data?: any
  errors?: Array<{
    field?: string
    message: string
    code?: string
  }>
}

/** 批量操作结果 */
export interface BatchOperationResult {
  total: number
  success: number
  failed: number
  results: Array<{
    id: string
    success: boolean
    message: string
    data?: any
  }>
}

// ============================= 静态数据 =============================

/** 静态数据源列表 - 降级使用 */
const STATIC_DATA_SOURCES: DataSource[] = [
  {
    id: 'ds_001',
    name: '培育对象基础信息库',
    description: '存储培育对象的基础信息，包括企业名称、联系方式、所属行业等',
    type: 'database',
    category: '基础数据',
    config: {
      host: '*************',
      port: 3306,
      database: 'cultivation_basic',
      username: 'readonly',
      driverType: 'mysql',
      query: 'SELECT * FROM cultivation_objects WHERE status = "active"',
      timeout: 30000,
      retryCount: 3
    },
    isEnabled: true,
    connectionStatus: 'online',
    lastConnectionTime: '2025-01-20 14:30:00',
    lastSyncTime: '2025-01-20 14:00:00',
    nextSyncTime: '2025-01-20 15:00:00',
    syncStatus: 'completed',
    syncType: 'incremental',
    syncFrequency: 60,
    enableSchedule: true,
    scheduleConfig: {
      cron: '0 */1 * * * *',
      timezone: 'Asia/Shanghai'
    },
    totalRecords: 15820,
    successRecords: 15680,
    failedRecords: 140,
    lastRecordCount: 1580,
    avgSyncDuration: 45.6,
    successRate: 99.1,
    dataQualityScore: 94.5,
    tags: ['基础数据', '企业信息'],
    owner: '数据管理员',
    ownerDept: '信息化部',
    createTime: '2024-12-01 10:00:00',
    updateTime: '2025-01-20 14:30:00',
    createdBy: 'admin',
    updatedBy: 'admin'
  },
  {
    id: 'ds_002',
    name: '评分系统API接口',
    description: '通过API获取培育对象的实时评分数据',
    type: 'api',
    category: '业务数据',
    config: {
      url: 'https://api.score.gov.cn/v1/scores',
      method: 'GET',
      headers: {
        'Authorization': 'Bearer token123',
        'Content-Type': 'application/json'
      },
      authType: 'bearer',
      timeout: 15000,
      retryCount: 3
    },
    isEnabled: true,
    connectionStatus: 'online',
    lastConnectionTime: '2025-01-20 14:25:00',
    lastSyncTime: '2025-01-20 14:20:00',
    nextSyncTime: '2025-01-20 14:50:00',
    syncStatus: 'completed',
    syncType: 'real_time',
    syncFrequency: 30,
    enableSchedule: true,
    scheduleConfig: {
      cron: '0 */30 * * * *',
      timezone: 'Asia/Shanghai'
    },
    totalRecords: 8960,
    successRecords: 8935,
    failedRecords: 25,
    lastRecordCount: 896,
    avgSyncDuration: 12.3,
    successRate: 99.7,
    dataQualityScore: 97.2,
    tags: ['业务数据', '评分数据'],
    owner: '业务管理员',
    ownerDept: '业务部',
    createTime: '2024-12-05 09:00:00',
    updateTime: '2025-01-20 14:25:00',
    createdBy: 'business_admin',
    updatedBy: 'business_admin'
  },
  {
    id: 'ds_003',
    name: '文档资料文件库',
    description: '定期扫描并导入文档资料，支持多种文件格式',
    type: 'file',
    category: '文档数据',
    config: {
      filePath: '/data/documents',
      filePattern: '*.{pdf,doc,docx,excel,xls,xlsx}',
      fileType: 'excel',
      encoding: 'utf-8',
      hasHeader: true,
      timeout: 60000
    },
    isEnabled: false,
    connectionStatus: 'offline',
    lastConnectionTime: '2025-01-19 18:00:00',
    lastSyncTime: '2025-01-19 18:00:00',
    nextSyncTime: '2025-01-21 08:00:00',
    syncStatus: 'failed',
    syncType: 'full',
    syncFrequency: 1440, // 每天一次
    enableSchedule: false,
    scheduleConfig: {
      cron: '0 0 8 * * *',
      timezone: 'Asia/Shanghai'
    },
    totalRecords: 2340,
    successRecords: 2180,
    failedRecords: 160,
    lastRecordCount: 0,
    avgSyncDuration: 180.5,
    successRate: 93.2,
    dataQualityScore: 85.3,
    errorMessage: '文件路径不存在或无访问权限',
    lastErrorTime: '2025-01-19 18:00:00',
    tags: ['文档数据', '材料管理'],
    owner: '档案管理员',
    ownerDept: '办公室',
    createTime: '2024-12-10 14:00:00',
    updateTime: '2025-01-19 18:00:00',
    createdBy: 'archive_admin',
    updatedBy: 'archive_admin'
  },
  {
    id: 'ds_004',
    name: '消息队列监听器',
    description: '监听业务系统消息队列，实时获取数据变更通知',
    type: 'message_queue',
    category: '实时数据',
    config: {
      mqType: 'rabbitmq',
      brokerUrl: 'amqp://mq.internal.com:5672',
      queue: 'data.changes',
      consumerGroup: 'data_collector',
      timeout: 10000
    },
    isEnabled: true,
    connectionStatus: 'online',
    lastConnectionTime: '2025-01-20 14:30:00',
    lastSyncTime: '2025-01-20 14:30:00',
    nextSyncTime: '实时监听',
    syncStatus: 'collecting',
    syncType: 'real_time',
    enableSchedule: false,
    totalRecords: 45680,
    successRecords: 45523,
    failedRecords: 157,
    lastRecordCount: 234,
    avgSyncDuration: 0.8,
    successRate: 99.7,
    dataQualityScore: 96.8,
    tags: ['实时数据', '消息队列'],
    owner: '系统管理员',
    ownerDept: '信息化部',
    createTime: '2024-12-15 16:00:00',
    updateTime: '2025-01-20 14:30:00',
    createdBy: 'sys_admin',
    updatedBy: 'sys_admin'
  }
]

/** 静态统计数据 - 降级使用 */
const STATIC_STATS: DataCollectionStats = {
  totalSources: 12,
  onlineSources: 9,
  offlineSources: 3,
  activeSources: 8,
  enabledSources: 10,
  todaySyncCount: 156,
  todaySyncVolume: 89650,
  avgSyncDuration: 56.8,
  totalSyncCount: 45670,
  totalDataVolume: 25680000,
  qualityScore: 93.7,
  qualityTrend: [
    { date: '01-14', score: 91.2, issues: 15 },
    { date: '01-15', score: 92.8, issues: 12 },
    { date: '01-16', score: 94.1, issues: 8 },
    { date: '01-17', score: 93.5, issues: 10 },
    { date: '01-18', score: 95.2, issues: 6 },
    { date: '01-19', score: 94.8, issues: 7 },
    { date: '01-20', score: 93.7, issues: 9 }
  ],
  avgThroughput: 1250.5,
  peakThroughput: 3680.2,
  successRate: 96.8,
  errorCount: 1568,
  criticalErrors: 23,
  errorTrend: [
    { date: '01-14', count: 28, type: '连接超时' },
    { date: '01-15', count: 15, type: '数据格式错误' },
    { date: '01-16', count: 12, type: '权限不足' },
    { date: '01-17', count: 19, type: '连接超时' },
    { date: '01-18', count: 8, type: '数据重复' },
    { date: '01-19', count: 22, type: '网络异常' },
    { date: '01-20', count: 16, type: '数据格式错误' }
  ],
  sourceTypeStats: [
    { type: 'database', count: 5, activeCount: 4, successRate: 97.2 },
    { type: 'api', count: 3, activeCount: 3, successRate: 98.5 },
    { type: 'file', count: 2, activeCount: 1, successRate: 89.3 },
    { type: 'message_queue', count: 1, activeCount: 1, successRate: 99.1 },
    { type: 'ftp', count: 1, activeCount: 0, successRate: 85.6 }
  ]
}

// ============================= API 接口 =============================

/**
 * 数据收集API类
 */
export class DataCollectionAPI {
  private static useStatic = false // 静态数据降级开关

  /**
   * 启用静态数据模式（降级机制）
   */
  static enableStaticMode() {
    DataCollectionAPI.useStatic = true
  }

  /**
   * 禁用静态数据模式
   */
  static disableStaticMode() {
    DataCollectionAPI.useStatic = false
  }

  /**
   * 通用请求处理，包含降级机制
   */
  private static async safeRequest<T>(apiCall: () => Promise<T>, fallback: T): Promise<T> {
    if (DataCollectionAPI.useStatic) {
      return fallback
    }

    try {
      return await apiCall()
    } catch (error) {
      console.warn('API请求失败，使用静态数据降级:', error)
      DataCollectionAPI.useStatic = true
      return fallback
    }
  }

  // ==================== 数据源管理 ====================

  /**
   * 获取数据源列表
   */
  static async getDataSources(params: DataSourceQueryParams = {}): Promise<PageResponse<DataSource>> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.get<ApiResponse<PageResponse<DataSource>>>('/api/data-collection/sources', {
          params
        })
        return response.data.data
      },
      {
        list: STATIC_DATA_SOURCES.slice(0, params.pageSize || 10),
        total: STATIC_DATA_SOURCES.length,
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        totalPages: Math.ceil(STATIC_DATA_SOURCES.length / (params.pageSize || 10))
      }
    )
  }

  /**
   * 获取数据源详情
   */
  static async getDataSource(id: string): Promise<DataSource | null> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.get<ApiResponse<DataSource>>(`/api/data-collection/sources/${id}`)
        return response.data.data
      },
      STATIC_DATA_SOURCES.find(ds => ds.id === id) || null
    )
  }

  /**
   * 创建数据源
   */
  static async createDataSource(dataSource: Omit<DataSource, 'id' | 'createTime' | 'updateTime' | 'createdBy'>): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<OperationResult>>('/api/data-collection/sources', dataSource)
        return response.data.data
      },
      { success: true, message: '数据源创建成功' }
    )
  }

  /**
   * 更新数据源
   */
  static async updateDataSource(id: string, dataSource: Partial<DataSource>): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.put<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}`, dataSource)
        return response.data.data
      },
      { success: true, message: '数据源更新成功' }
    )
  }

  /**
   * 删除数据源
   */
  static async deleteDataSource(id: string): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.delete<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}`)
        return response.data.data
      },
      { success: true, message: '数据源删除成功' }
    )
  }

  /**
   * 批量删除数据源
   */
  static async batchDeleteDataSources(ids: string[]): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.delete<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/batch', {
          data: { ids }
        })
        return response.data.data
      },
      {
        total: ids.length,
        success: ids.length,
        failed: 0,
        results: ids.map(id => ({ id, success: true, message: '删除成功' }))
      }
    )
  }

  /**
   * 启用/禁用数据源
   */
  static async toggleDataSource(id: string, enabled: boolean): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.patch<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}/toggle`, {
          enabled
        })
        return response.data.data
      },
      { success: true, message: `数据源已${enabled ? '启用' : '禁用'}` }
    )
  }

  // ==================== 连接和同步管理 ====================

  /**
   * 测试数据源连接
   */
  static async testConnection(id: string): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}/test-connection`)
        return response.data.data
      },
      { success: true, message: '连接测试成功', data: { latency: 125, status: 'online' } }
    )
  }

  /**
   * 批量测试连接
   */
  static async batchTestConnections(ids?: string[]): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/test-connections', {
          ids
        })
        return response.data.data
      },
      {
        total: ids?.length || STATIC_DATA_SOURCES.length,
        success: (ids?.length || STATIC_DATA_SOURCES.length) - 1,
        failed: 1,
        results: (ids || STATIC_DATA_SOURCES.map(ds => ds.id)).map((id, index) => ({
          id,
          success: index !== 2, // 模拟第3个失败
          message: index !== 2 ? '连接成功' : '连接超时',
          data: index !== 2 ? { latency: Math.floor(Math.random() * 200) + 50 } : null
        }))
      }
    )
  }

  /**
   * 启动数据同步
   */
  static async startSync(id: string, syncType: SyncType = 'incremental'): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}/sync`, {
          syncType
        })
        return response.data.data
      },
      { success: true, message: '数据同步已启动', data: { syncId: `sync_${Date.now()}` } }
    )
  }

  /**
   * 停止数据同步
   */
  static async stopSync(id: string): Promise<OperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<OperationResult>>(`/api/data-collection/sources/${id}/stop-sync`)
        return response.data.data
      },
      { success: true, message: '数据同步已停止' }
    )
  }

  /**
   * 全量同步所有数据源
   */
  static async syncAll(): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/sync-all')
        return response.data.data
      },
      {
        total: STATIC_DATA_SOURCES.length,
        success: STATIC_DATA_SOURCES.length - 1,
        failed: 1,
        results: STATIC_DATA_SOURCES.map((ds, index) => ({
          id: ds.id,
          success: index !== 2, // 模拟第3个失败
          message: index !== 2 ? '同步启动成功' : '数据源离线，无法启动同步',
          data: index !== 2 ? { syncId: `sync_${Date.now()}_${index}` } : null
        }))
      }
    )
  }

  // ==================== 统计和监控 ====================

  /**
   * 获取数据收集统计信息
   */
  static async getStats(): Promise<DataCollectionStats> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.get<ApiResponse<DataCollectionStats>>('/api/data-collection/stats')
        return response.data.data
      },
      STATIC_STATS
    )
  }

  /**
   * 获取同步历史记录
   */
  static async getSyncHistory(params: SyncHistoryQueryParams = {}): Promise<PageResponse<SyncHistory>> {
    const staticHistory: SyncHistory[] = [
      {
        id: 'sync_001',
        dataSourceId: 'ds_001',
        dataSourceName: '培育对象基础信息库',
        syncType: 'incremental',
        status: 'completed',
        startTime: '2025-01-20 14:00:00',
        endTime: '2025-01-20 14:01:30',
        duration: 90,
        recordCount: 1580,
        successCount: 1567,
        failedCount: 13,
        throughput: 17.5,
        avgResponseTime: 45,
        qualityScore: 94.8,
        duplicateCount: 3,
        createTime: '2025-01-20 14:00:00'
      },
      {
        id: 'sync_002',
        dataSourceId: 'ds_002',
        dataSourceName: '评分系统API接口',
        syncType: 'real_time',
        status: 'completed',
        startTime: '2025-01-20 14:20:00',
        endTime: '2025-01-20 14:20:12',
        duration: 12,
        recordCount: 896,
        successCount: 896,
        failedCount: 0,
        throughput: 74.7,
        avgResponseTime: 125,
        qualityScore: 97.2,
        createTime: '2025-01-20 14:20:00'
      },
      {
        id: 'sync_003',
        dataSourceId: 'ds_003',
        dataSourceName: '文档资料文件库',
        syncType: 'full',
        status: 'failed',
        startTime: '2025-01-19 18:00:00',
        endTime: '2025-01-19 18:03:00',
        duration: 180,
        recordCount: 0,
        successCount: 0,
        failedCount: 0,
        errorMessage: '文件路径不存在',
        createTime: '2025-01-19 18:00:00'
      }
    ]

    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.get<ApiResponse<PageResponse<SyncHistory>>>('/api/data-collection/sync-history', {
          params
        })
        return response.data.data
      },
      {
        list: staticHistory.slice(0, params.pageSize || 10),
        total: staticHistory.length,
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        totalPages: Math.ceil(staticHistory.length / (params.pageSize || 10))
      }
    )
  }

  /**
   * 获取数据流监控信息
   */
  static async getDataFlowMonitor(dataSourceId?: string): Promise<DataFlowMonitor[]> {
    const staticMonitorData: DataFlowMonitor[] = [
      {
        id: 'monitor_001',
        dataSourceId: 'ds_001',
        dataSourceName: '培育对象基础信息库',
        timestamp: new Date().toISOString(),
        currentThroughput: 156.8,
        avgThroughput: 142.3,
        peakThroughput: 285.6,
        currentLatency: 45,
        avgLatency: 52,
        errorRate: 0.9,
        cpuUsage: 65.2,
        memoryUsage: 72.8,
        diskUsage: 45.6,
        networkIO: { inbound: 1250.6, outbound: 890.3 },
        queueDepth: 15,
        pendingTasks: 3,
        activeTasks: 2,
        status: 'completed',
        healthStatus: 'healthy'
      },
      {
        id: 'monitor_002',
        dataSourceId: 'ds_002',
        dataSourceName: '评分系统API接口',
        timestamp: new Date().toISOString(),
        currentThroughput: 89.4,
        avgThroughput: 95.7,
        peakThroughput: 156.8,
        currentLatency: 125,
        avgLatency: 118,
        errorRate: 0.3,
        cpuUsage: 42.1,
        memoryUsage: 38.9,
        networkIO: { inbound: 680.2, outbound: 420.5 },
        queueDepth: 8,
        pendingTasks: 1,
        activeTasks: 1,
        status: 'collecting',
        healthStatus: 'healthy'
      }
    ]

    return DataCollectionAPI.safeRequest(
      async () => {
        const url = dataSourceId 
          ? `/api/data-collection/monitor/${dataSourceId}`
          : '/api/data-collection/monitor'
        const response = await axios.get<ApiResponse<DataFlowMonitor[]>>(url)
        return response.data.data
      },
      dataSourceId 
        ? staticMonitorData.filter(m => m.dataSourceId === dataSourceId)
        : staticMonitorData
    )
  }

  /**
   * 获取数据预览
   */
  static async getDataPreview(dataSourceId: string, limit: number = 100): Promise<DataPreview> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.get<ApiResponse<DataPreview>>(`/api/data-collection/sources/${dataSourceId}/preview`, {
          params: { limit }
        })
        return response.data.data
      },
      {
        dataSourceId,
        dataSourceName: STATIC_DATA_SOURCES.find(ds => ds.id === dataSourceId)?.name || '未知数据源',
        totalCount: 15820,
        sampleData: Array.from({ length: Math.min(limit, 20) }, (_, i) => ({
          id: `obj_${String(i + 1).padStart(3, '0')}`,
          name: `培育对象${i + 1}`,
          type: ['重点培育', '一般培育', '观察培育'][i % 3],
          score: Math.floor(Math.random() * 40) + 60,
          status: ['正常', '预警', '异常'][Math.floor(Math.random() * 3)],
          department: ['经开区', '高新区', '渝中区', '江北区', '南岸区'][i % 5],
          createTime: new Date(2024, 0, 1 + i * 3).toISOString().slice(0, 19).replace('T', ' '),
          updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
        })),
        schema: [
          { field: 'id', type: 'string', nullable: false, description: '对象ID' },
          { field: 'name', type: 'string', nullable: false, description: '对象名称' },
          { field: 'type', type: 'string', nullable: false, description: '培育类型' },
          { field: 'score', type: 'number', nullable: true, description: '评分' },
          { field: 'status', type: 'string', nullable: false, description: '状态' },
          { field: 'department', type: 'string', nullable: true, description: '所属部门' },
          { field: 'createTime', type: 'datetime', nullable: false, description: '创建时间' },
          { field: 'updateTime', type: 'datetime', nullable: false, description: '更新时间' }
        ],
        lastUpdateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      }
    )
  }

  // ==================== 批量操作 ====================

  /**
   * 批量启用/禁用数据源
   */
  static async batchToggleDataSources(ids: string[], enabled: boolean): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.patch<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/batch-toggle', {
          ids,
          enabled
        })
        return response.data.data
      },
      {
        total: ids.length,
        success: ids.length,
        failed: 0,
        results: ids.map(id => ({ 
          id, 
          success: true, 
          message: `数据源已${enabled ? '启用' : '禁用'}` 
        }))
      }
    )
  }

  /**
   * 批量同步数据源
   */
  static async batchSyncDataSources(ids: string[], syncType: SyncType = 'incremental'): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/batch-sync', {
          ids,
          syncType
        })
        return response.data.data
      },
      {
        total: ids.length,
        success: ids.length - Math.floor(ids.length * 0.1), // 模拟90%成功率
        failed: Math.floor(ids.length * 0.1),
        results: ids.map((id, index) => ({
          id,
          success: index % 10 !== 0, // 每10个中有1个失败
          message: index % 10 !== 0 ? '同步启动成功' : '数据源离线，无法启动同步',
          data: index % 10 !== 0 ? { syncId: `sync_${Date.now()}_${index}` } : null
        }))
      }
    )
  }

  // ==================== 高级功能 ====================

  /**
   * 导出数据源配置
   */
  static async exportDataSources(ids?: string[]): Promise<Blob> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const response = await axios.post('/api/data-collection/sources/export', 
          { ids },
          { responseType: 'blob' }
        )
        return response.data
      },
      new Blob([JSON.stringify(STATIC_DATA_SOURCES, null, 2)], { type: 'application/json' })
    )
  }

  /**
   * 导入数据源配置
   */
  static async importDataSources(file: File): Promise<BatchOperationResult> {
    return DataCollectionAPI.safeRequest(
      async () => {
        const formData = new FormData()
        formData.append('file', file)
        const response = await axios.post<ApiResponse<BatchOperationResult>>('/api/data-collection/sources/import', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        return response.data.data
      },
      {
        total: 5,
        success: 4,
        failed: 1,
        results: [
          { id: 'new_001', success: true, message: '数据源导入成功' },
          { id: 'new_002', success: true, message: '数据源导入成功' },
          { id: 'new_003', success: false, message: '配置格式错误' },
          { id: 'new_004', success: true, message: '数据源导入成功' },
          { id: 'new_005', success: true, message: '数据源导入成功' }
        ]
      }
    )
  }

  /**
   * 获取数据源类型选项
   */
  static getDataSourceTypeOptions(): Array<{ label: string; value: DataSourceType; description: string }> {
    return [
      { label: '数据库', value: 'database', description: '连接各种关系型数据库获取数据' },
      { label: 'API接口', value: 'api', description: '通过RESTful API获取数据' },
      { label: '文件系统', value: 'file', description: '从文件系统读取各种格式的文件' },
      { label: '手动录入', value: 'manual', description: '手动录入数据' },
      { label: '消息队列', value: 'message_queue', description: '从消息队列实时获取数据' },
      { label: 'FTP服务器', value: 'ftp', description: '从FTP服务器获取文件数据' },
      { label: 'SFTP服务器', value: 'sftp', description: '从SFTP服务器安全获取文件数据' }
    ]
  }

  /**
   * 获取同步类型选项
   */
  static getSyncTypeOptions(): Array<{ label: string; value: SyncType; description: string }> {
    return [
      { label: '增量同步', value: 'incremental', description: '只同步新增或变更的数据' },
      { label: '全量同步', value: 'full', description: '同步所有数据' },
      { label: '实时同步', value: 'real_time', description: '实时监听并同步数据变更' }
    ]
  }

  /**
   * 获取数据库驱动类型选项
   */
  static getDatabaseDriverOptions(): Array<{ label: string; value: string }> {
    return [
      { label: 'MySQL', value: 'mysql' },
      { label: 'PostgreSQL', value: 'postgresql' },
      { label: 'Oracle', value: 'oracle' },
      { label: 'SQL Server', value: 'sqlserver' },
      { label: 'SQLite', value: 'sqlite' }
    ]
  }

  /**
   * 获取文件类型选项
   */
  static getFileTypeOptions(): Array<{ label: string; value: string }> {
    return [
      { label: 'CSV文件', value: 'csv' },
      { label: 'Excel文件', value: 'excel' },
      { label: 'JSON文件', value: 'json' },
      { label: 'XML文件', value: 'xml' },
      { label: '文本文件', value: 'txt' },
      { label: '日志文件', value: 'log' }
    ]
  }
}

// ============================= 默认导出 =============================

export default DataCollectionAPI

// ============================= 便捷函数 =============================

/** 获取数据源列表 */
export const getDataSources = DataCollectionAPI.getDataSources

/** 获取数据源详情 */
export const getDataSource = DataCollectionAPI.getDataSource

/** 创建数据源 */
export const createDataSource = DataCollectionAPI.createDataSource

/** 更新数据源 */
export const updateDataSource = DataCollectionAPI.updateDataSource

/** 删除数据源 */
export const deleteDataSource = DataCollectionAPI.deleteDataSource

/** 测试连接 */
export const testConnection = DataCollectionAPI.testConnection

/** 启动同步 */
export const startSync = DataCollectionAPI.startSync

/** 停止同步 */
export const stopSync = DataCollectionAPI.stopSync

/** 获取统计信息 */
export const getStats = DataCollectionAPI.getStats

/** 获取同步历史 */
export const getSyncHistory = DataCollectionAPI.getSyncHistory

/** 获取数据流监控 */
export const getDataFlowMonitor = DataCollectionAPI.getDataFlowMonitor

/** 获取数据预览 */
export const getDataPreview = DataCollectionAPI.getDataPreview