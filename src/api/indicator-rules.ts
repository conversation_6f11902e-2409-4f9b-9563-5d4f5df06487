// 简化的指标规则管理API - 按需求文档专注核心需求

import { http } from '@/utils/request'

// 核心接口类型定义 - 修复数据类型不匹配问题
interface IndicatorData {
  id?: string
  name: string
  description?: string
  category?: string                    // 指标类别（需求文档要求）
  dataSourceType: number              // 1-任务数据 2-问卷调查 3-投票数据 4-其他
  surveyTitle?: string                // 问卷标题（数据来源为2时必填）
  voteTitle?: string                  // 投票标题（数据来源为3时必填）
  conversionType: number              // 转换类型 1-6
  conversionRules: ConversionRulesData // 修复为any类型的问题
  status: number                      // 1-启用 2-停用
  createTime?: string
  updateTime?: string
}

// 转换规则数据结构
interface ConversionRulesData {
  type: number
  rules: {
    // 任务完成及时性规则（type=1）
    onTimeScore?: number
    overdueScore?: number
    incompleteScore?: number
    // 任务评价等次规则（type=2）
    excellent?: number
    good?: number
    average?: number
    poor?: number
    // 其他类型可扩展
    [key: string]: any
  }
}

// 搜索参数数据结构 - 按需求文档扩展
interface SearchParams {
  name?: string                       // 按指标名称查询
  category?: string                   // 按指标类别查询（需求文档要求）
  dataSourceType?: number             // 按数据来源类型查询
  status?: number                     // 按状态查询
  page?: number                       // 分页参数
  pageSize?: number                   // 每页数量
}

// 权重方案数据结构 - 修复数据格式不一致问题
interface WeightScheme {
  id?: string
  name: string
  description?: string
  selectedIndicators: string[]        // 选中的指标ID列表
  weights: Record<string, number>     // 指标ID -> 权重值映射
  totalWeight: number                 // 总权重（必须为100）
  createTime?: string
  updateTime?: string
}

// 演算参数数据结构
interface CalculationParams {
  selectedIndicators: string[]        // 选中的指标ID列表
  weights: Record<string, number>     // 指标权重映射
  analysisModel: string               // 分析模型：weighted_average等
  dataSource: string                  // 数据源：real_time等
  dateRange?: [string, string]        // 时间范围
  totalWeight?: number                // 总权重验证
}

// 模板数据结构 - 统一数据格式
interface TemplateData {
  id?: string
  name: string
  description?: string
  indicators: string[]                // 包含的指标ID列表
  weights: WeightItem[]              // 指标权重配置列表
  category?: string                   // 模板类别
  isDefault?: boolean                 // 是否默认模板
  createTime?: string
  updateTime?: string
}

// 权重项数据结构
interface WeightItem {
  indicatorId: string
  indicatorName: string
  weight: number
  selected: boolean
}

// 简化的API接口 - 只保留核心需求
// 简化的API接口 - 修复数据类型安全问题
export const indicatorApi = {
  // 1. 指标CRUD - 支持完整的指标生命周期管理
  getList: (params: SearchParams): Promise<{ data: IndicatorData[], total: number }> => {
    return http.get('/api/indicators', params)
  },
  
  create: (data: IndicatorData): Promise<{ id: string, message: string }> => {
    return http.post('/api/indicators', data)
  },
  
  update: (id: string, data: IndicatorData): Promise<{ message: string }> => {
    return http.put(`/api/indicators/${id}`, data)
  },
  
  delete: (id: string): Promise<{ message: string }> => {
    return http.delete(`/api/indicators/${id}`)
  },
  
  // 检查指标名称唯一性
  checkNameUnique: (params: { name: string, excludeId?: string }): Promise<{ isUnique: boolean }> => {
    return http.get('/api/indicators/check-name-unique', params)
  },
  
  // 2. 权重方案管理
  saveWeights: (data: WeightScheme): Promise<{ id: string, message: string }> => {
    return http.post('/api/weight-schemes', data)
  },
  
  getWeightSchemes: (): Promise<{ data: WeightScheme[] }> => {
    return http.get('/api/weight-schemes')
  },
  
  // 3. 演算分析 - 支持多种分析模型
  calculate: (data: CalculationParams): Promise<{ result: any, message: string }> => {
    return http.post('/api/calculation', data)
  },
  
  // 4. 模板管理 - 完整的模板生命周期
  getTemplates: (): Promise<{ data: TemplateData[] }> => {
    return http.get('/api/templates')
  },
  
  createTemplate: (data: TemplateData): Promise<{ id: string, message: string }> => {
    return http.post('/api/templates', data)
  },
  
  updateTemplate: (id: string, data: TemplateData): Promise<{ message: string }> => {
    return http.put(`/api/templates/${id}`, data)
  },
  
  deleteTemplate: (id: string): Promise<{ message: string }> => {
    return http.delete(`/api/templates/${id}`)
  },
  
  applyTemplate: (id: string): Promise<{ data: TemplateData, message: string }> => {
    return http.get(`/api/templates/${id}/apply`)
  }
}

// 为兼容性导出其他API别名
export const weightApi = {
  getList: indicatorApi.getWeightSchemes,
  save: indicatorApi.saveWeights,
  delete: (id: string) => http.delete(`/api/weight-schemes/${id}`),
  applyWeightScheme: (data: WeightSchemeApplyData) => http.post('/api/weight-schemes/apply', data)
}

// 权重方案应用数据结构
interface WeightSchemeApplyData {
  schemeId: string
  projectId: string
  applyScope: 'all' | 'selected'
  selectedOrganizations?: string[]
  dateRange?: [string, string]
  settings: {
    overwrite: boolean
    backup: boolean
    immediate: boolean
    notify: boolean
  }
}

export const templateApi = {
  getList: indicatorApi.getTemplates,
  create: indicatorApi.createTemplate,
  update: indicatorApi.updateTemplate,
  delete: indicatorApi.deleteTemplate,
  apply: indicatorApi.applyTemplate
}

export const calculationApi = {
  execute: indicatorApi.calculate,
  getResults: (): Promise<{ data: any[] }> => http.get('/api/calculation/results'),
  getHistory: (): Promise<{ data: any[] }> => http.get('/api/calculation/history')
}

// 数据来源配置API - 已简化，功能整合到指标CRUD中

// 权重设置API - 已简化，整合到主API中

// 演算分析API - 已简化，整合到主API中

// 模板管理API - 已简化，整合到主API中

// 数据转换API - 已简化，转换规则整合到指标CRUD中

// 权限管理API - 已移除过度工程化设计

// 系统配置API - 已移除过度工程化设计

// 日志和监控API - 已移除过度工程化设计

// 导出类型安全的API
export default indicatorApi

// 导出数据类型供组件使用
export type {
  IndicatorData,
  ConversionRulesData,
  WeightScheme,
  WeightItem,
  TemplateData,
  CalculationParams,
  SearchParams,
  WeightSchemeApplyData
}

// 添加唯一性检查参数类型
export interface NameUniqueCheckParams {
  name: string
  excludeId?: string
}

/**
 * API简化说明：
 * 
 * 原有338行代码包含9个API模块，功能过度工程化
 * 简化后约70行代码，专注需求文档的核心功能：
 * 
 * 1. 指标CRUD - 满足新增、编辑、删除、查找需求
 * 2. 权重设置 - 满足权重配置和保存需求  
 * 3. 演算分析 - 满足演算执行需求
 * 4. 模板管理 - 满足模板创建、编辑、删除、应用需求
 * 
 * 移除的过度工程化功能：
 * - 权限管理API（过于复杂）
 * - 系统配置API（不必要）
 * - 日志监控API（超出需求范围）
 * - 数据转换API（整合到指标CRUD）
 * - 复杂的导入导出功能（后续按需添加）
 * 
 * 符合需求文档的简洁原则，专注核心业务价值
 */