import request from '@/utils/request';
import type {
  CaseCollectionActivity,
  CaseSubmission,
  CaseReview,
  CaseCategory,
  CaseCollectionActivityQueryParams,
  CaseSubmissionQueryParams,
  CaseReviewQueryParams,
  CaseCategoryQueryParams,
  CaseCollectionStatistics,
  ActivityProgress,
  PageResult,
  ApiResponse
} from '@/types/case-collection';

// ==================== 案例征集活动管理 API ====================

/**
 * 创建案例征集活动
 */
export function createActivity(data: Partial<CaseCollectionActivity>) {
  return request.post<ApiResponse<CaseCollectionActivity>>('/api/case-collection/activities', data);
}

/**
 * 更新案例征集活动
 */
export function updateActivity(id: number, data: Partial<CaseCollectionActivity>) {
  return request.put<ApiResponse<CaseCollectionActivity>>(`/api/case-collection/activities/${id}`, data);
}

/**
 * 删除案例征集活动
 */
export function deleteActivity(id: number) {
  return request.delete<ApiResponse<boolean>>(`/api/case-collection/activities/${id}`);
}

/**
 * 根据ID查询活动详情
 */
export function getActivityDetail(id: number) {
  return request.get<ApiResponse<CaseCollectionActivity>>(`/api/case-collection/activities/${id}`);
}

/**
 * 分页查询活动列表
 */
export function getActivityList(params?: CaseCollectionActivityQueryParams) {
  return request.get<ApiResponse<PageResult<CaseCollectionActivity>>>('/api/case-collection/activities', { params });
}

/**
 * 获取活动进度信息
 */
export function getActivityProgress(id: number) {
  return request.get<ApiResponse<ActivityProgress>>(`/api/case-collection/activities/${id}/progress`);
}

/**
 * 发布活动
 */
export function publishActivity(id: number) {
  return request.post<ApiResponse<boolean>>(`/api/case-collection/activities/${id}/publish`);
}

/**
 * 取消活动
 */
export function cancelActivity(id: number) {
  return request.post<ApiResponse<boolean>>(`/api/case-collection/activities/${id}/cancel`);
}

// ==================== 案例提交管理 API ====================

/**
 * 提交案例
 */
export function submitCase(data: Partial<CaseSubmission>) {
  return request.post<ApiResponse<CaseSubmission>>('/api/case-collection/submissions', data);
}

/**
 * 更新案例提交
 */
export function updateSubmission(id: number, data: Partial<CaseSubmission>) {
  return request.put<ApiResponse<CaseSubmission>>(`/api/case-collection/submissions/${id}`, data);
}

/**
 * 删除案例提交
 */
export function deleteSubmission(id: number) {
  return request.delete<ApiResponse<boolean>>(`/api/case-collection/submissions/${id}`);
}

/**
 * 根据ID查询案例详情
 */
export function getSubmissionDetail(id: number) {
  return request.get<ApiResponse<CaseSubmission>>(`/api/case-collection/submissions/${id}`);
}

/**
 * 分页查询案例提交列表
 */
export function getSubmissionList(params?: CaseSubmissionQueryParams) {
  return request.get<ApiResponse<PageResult<CaseSubmission>>>('/api/case-collection/submissions', { params });
}

/**
 * 撤回案例提交
 */
export function withdrawSubmission(id: number) {
  return request.post<ApiResponse<boolean>>(`/api/case-collection/submissions/${id}/withdraw`);
}

/**
 * 重新提交案例
 */
export function resubmitCase(id: number, data: Partial<CaseSubmission>) {
  return request.post<ApiResponse<CaseSubmission>>(`/api/case-collection/submissions/${id}/resubmit`, data);
}

// ==================== 案例预审管理 API ====================

/**
 * 创建案例预审
 */
export function createReview(data: Partial<CaseReview>) {
  return request.post<ApiResponse<CaseReview>>('/api/case-collection/reviews', data);
}

/**
 * 更新案例预审
 */
export function updateReview(id: number, data: Partial<CaseReview>) {
  return request.put<ApiResponse<CaseReview>>(`/api/case-collection/reviews/${id}`, data);
}

/**
 * 删除案例预审
 */
export function deleteReview(id: number) {
  return request.delete<ApiResponse<boolean>>(`/api/case-collection/reviews/${id}`);
}

/**
 * 根据ID查询预审详情
 */
export function getReviewDetail(id: number) {
  return request.get<ApiResponse<CaseReview>>(`/api/case-collection/reviews/${id}`);
}

/**
 * 分页查询预审列表
 */
export function getReviewList(params?: CaseReviewQueryParams) {
  return request.get<ApiResponse<PageResult<CaseReview>>>('/api/case-collection/reviews', { params });
}

/**
 * 根据提交ID查询预审记录
 */
export function getReviewsBySubmissionId(submissionId: number) {
  return request.get<ApiResponse<CaseReview[]>>(`/api/case-collection/submissions/${submissionId}/reviews`);
}

/**
 * 批量审核案例
 */
export function batchReviewCases(data: {
  submissionIds: number[];
  reviewResult: number;
  score: number;
  comments: string;
  suggestions?: string;
}) {
  return request.post<ApiResponse<boolean>>('/api/case-collection/reviews/batch', data);
}

// ==================== 案例分类管理 API ====================

/**
 * 获取所有分类（树形结构）
 */
export function getAllCategories() {
  return request.get<ApiResponse<CaseCategory[]>>('/api/case-collection/categories');
}

/**
 * 创建案例分类
 */
export function createCategory(data: Partial<CaseCategory>) {
  return request.post<ApiResponse<CaseCategory>>('/api/case-collection/categories', data);
}

/**
 * 更新案例分类
 */
export function updateCategory(id: number, data: Partial<CaseCategory>) {
  return request.put<ApiResponse<CaseCategory>>(`/api/case-collection/categories/${id}`, data);
}

/**
 * 删除案例分类
 */
export function deleteCategory(id: number) {
  return request.delete<ApiResponse<boolean>>(`/api/case-collection/categories/${id}`);
}

/**
 * 根据ID查询分类详情
 */
export function getCategoryDetail(id: number) {
  return request.get<ApiResponse<CaseCategory>>(`/api/case-collection/categories/${id}`);
}

/**
 * 根据父ID查询子分类
 */
export function getCategoriesByParentId(parentId: number) {
  return request.get<ApiResponse<CaseCategory[]>>(`/api/case-collection/categories/children/${parentId}`);
}

/**
 * 更新分类排序
 */
export function updateCategorySort(data: { id: number; sortOrder: number }[]) {
  return request.post<ApiResponse<boolean>>('/api/case-collection/categories/sort', data);
}

/**
 * 移动分类位置
 */
export function moveCategory(data: {
  dragId: number;
  targetId: number;
  position: string;
}) {
  return request.post<ApiResponse<boolean>>('/api/case-collection/categories/move', data);
}

// ==================== 统计查询 API ====================

/**
 * 获取活动统计数据
 */
export function getActivityStatistics(organizerId?: number) {
  const params = organizerId ? { organizerId } : undefined;
  return request.get<ApiResponse<CaseCollectionStatistics>>('/api/case-collection/activities/statistics', { params });
}

/**
 * 获取案例提交统计数据
 */
export function getSubmissionStatistics(activityId: number) {
  return request.get<ApiResponse<any>>(`/api/case-collection/activities/${activityId}/submissions/statistics`);
}

/**
 * 获取审核历史记录列表
 */
export function getReviewHistory(params?: {
  page?: number;
  pageSize?: number;
  activityId?: number;
  reviewResult?: number;
  reviewerName?: string;
  dateRange?: [string, string];
  keyword?: string;
}) {
  return request.get<ApiResponse<PageResult<any>>>('/api/case-collection/reviews/history', { params });
}

/**
 * 获取审核统计数据
 */
export function getReviewStatistics(params?: {
  activityId?: number;
  dateRange?: [string, string];
}) {
  return request.get<ApiResponse<any>>('/api/case-collection/reviews/statistics', { params });
}

/**
 * 获取当前审核设置
 */
export function getReviewSettings() {
  return request.get<ApiResponse<any>>('/api/case-collection/reviews/settings');
}

/**
 * 更新审核设置
 */
export function updateReviewSettings(data: any) {
  return request.put<ApiResponse<boolean>>('/api/case-collection/reviews/settings', data);
}

/**
 * 获取审核模板列表
 */
export function getReviewTemplates() {
  return request.get<ApiResponse<any[]>>('/api/case-collection/reviews/templates');
}

/**
 * 保存审核模板
 */
export function saveReviewTemplate(data: {
  id?: number;
  name: string;
  type: string;
  reviewResult: number;
  score: number;
  comments: string;
  suggestions?: string;
}) {
  if (data.id) {
    return request.put<ApiResponse<any>>(`/api/case-collection/reviews/templates/${data.id}`, data);
  } else {
    return request.post<ApiResponse<any>>('/api/case-collection/reviews/templates', data);
  }
}

/**
 * 删除审核模板
 */
export function deleteReviewTemplate(id: number) {
  return request.delete<ApiResponse<boolean>>(`/api/case-collection/reviews/templates/${id}`);
}

/**
 * 导出审核历史数据
 */
export function exportReviewHistory(params?: {
  activityId?: number;
  reviewResult?: number;
  reviewerName?: string;
  dateRange?: [string, string];
  keyword?: string;
}) {
  return request.get<Blob>('/api/case-collection/reviews/history/export', {
    params,
    responseType: 'blob'
  });
}

/**
 * 获取分类使用统计
 */
export function getCategoryStatistics() {
  return request.get<ApiResponse<any>>('/api/case-collection/categories/statistics');
}

// ==================== 文件管理 API ====================

/**
 * 上传文件
 */
export function uploadFile(file: File, type: 'image' | 'document' | 'video' = 'document') {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  
  return request.post<ApiResponse<{ url: string; name: string; size: number }>>('/api/case-collection/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除文件
 */
export function deleteFile(url: string) {
  return request.delete<ApiResponse<boolean>>('/api/case-collection/files', { url });
}

/**
 * 下载文件
 */
export function downloadFile(url: string, filename?: string) {
  return request.get(`/api/case-collection/files/download`, { 
    params: { url, filename },
    responseType: 'blob'
  });
}

// ==================== 导出功能 API ====================

/**
 * 导出活动列表
 */
export function exportActivityList(params?: CaseCollectionActivityQueryParams) {
  return request.get('/api/case-collection/activities/export', { 
    params,
    responseType: 'blob'
  });
}

/**
 * 导出案例提交列表
 */
export function exportSubmissionList(params?: CaseSubmissionQueryParams) {
  return request.get('/api/case-collection/submissions/export', { 
    params,
    responseType: 'blob'
  });
}

/**
 * 导出审核报告
 */
export function exportReviewReport(activityId: number) {
  return request.get(`/api/case-collection/activities/${activityId}/review-report`, { 
    responseType: 'blob'
  });
}
