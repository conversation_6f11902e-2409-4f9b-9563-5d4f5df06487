import request from '@/utils/request';
import type { HonorApply<PERSON><PERSON>, HonorRecord, HonorStatus } from '@/types/honor';

// 搜索参数接口
export interface HonorSearchParams {
  applicant?: string;
  department?: string;
  type?: number;
  status?: HonorStatus;
  startTime?: string;
  endTime?: string;
}

export function fetchHonorList(params?: HonorSearchParams) {
  return request.get<HonorRecord[]>('/api/honor/list', { params });
}
export function submitHonorApply(data: HonorApplyForm) {
  return request.post('/api/honor/apply', data);
}

export function updateHonorApply(data: HonorApplyForm) {
  console.log('🚀 ~-----------------data:', data);
  return request.post('/api/honor/update', data);
}
export function auditHonor(id: number, status: HonorStatus, opinion: string) {
  return request.post('/api/honor/audit', { id, status, opinion });
}
export function deleteHonor(id: number) {
  return request.delete(`/api/honor/${id}`);
} 