import request from '@/utils/request'

/**
 * 上传文件
 * @param {object} upfile
 */
export const uploadFile = (params: any) => {
	return request.post(`https://mfjg-pc.aidangqun.com/owsz/acceptance/file/file/upload`, params)
}
export const uploadVideoFile = (params: any) => {
	return request.post(`https://mfjg-pc.aidangqun.com/owsz/acceptance/file/file/upload/video`, params)
}

export const uploadVideoResult = (params: any) => {
	return request.get(`https://mfjg-pc.aidangqun.com/owsz/acceptance/file/file/upload/video/result?task_id=${params}`)
}
