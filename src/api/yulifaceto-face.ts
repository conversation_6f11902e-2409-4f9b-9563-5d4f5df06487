// 渝理面对面系统API接口

import type {
  DocumentRecord,
  DocumentTemplate,
  UserPermission,
  StatisticsData,
  DownloadRule,
  DocumentSearchParams,
  TemplateSearchParams,
  DepartmentStatistics,
  MonthlyStatistics,
  SpeakerStatistics
} from '@/types/yulifaceto-face'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟纪实数据
const mockDocuments: DocumentRecord[] = [
  {
    id: 1,
    title: '学习贯彻党的二十大精神专题宣讲',
    speaker: '张书记',
    content: '深入学习贯彻党的二十大精神，全面理解新时代新征程的使命任务...',
    lectureTime: '2025-06-15 14:30:00',
    location: '市委会议室',
    contactInfo: '13800138001',
    summary: '党的二十大精神学习宣讲活动',
    coverImage: '/images/lecture1.jpg',
    status: 2,
    enableDownload: true,
    downloadFormat: 'pdf',
    publishTime: '2025-06-16 09:00:00',
    createTime: '2025-06-15 10:00:00',
    updateTime: '2025-06-16 09:00:00',
    creator: '周海军',
    templateId: 1,
    viewCount: 156,
    downloadCount: 23
  },
  {
    id: 2,
    title: '新时代党建工作创新实践',
    speaker: '杜佳佳',
    content: '结合新时代特点，探索党建工作的创新路径和实践方法...',
    lectureTime: '2025-06-20 09:00:00',
    location: '党员活动中心',
    contactInfo: '13800138002',
    summary: '党建工作创新实践分享',
    coverImage: '/images/lecture2.jpg',
    status: 2,
    enableDownload: true,
    downloadFormat: 'doc',
    publishTime: '2025-06-21 08:30:00',
    createTime: '2025-06-19 16:00:00',
    updateTime: '2025-06-21 08:30:00',
    creator: '王东',
    templateId: 2,
    viewCount: 89,
    downloadCount: 15
  },
  {
    id: 3,
    title: '基层治理现代化路径探索',
    speaker: '王处长',
    content: '分析基层治理面临的挑战，探讨现代化治理的有效路径...',
    lectureTime: '2025-06-25 15:00:00',
    location: '区政府会议厅',
    contactInfo: '13800138003',
    summary: '基层治理现代化专题讲座',
    status: 1,
    enableDownload: false,
    downloadFormat: 'doc',
    createTime: '2025-06-24 11:00:00',
    updateTime: '2025-06-25 10:00:00',
    creator: '杜佳佳',
    templateId: 1,
    viewCount: 0,
    downloadCount: 0
  },
  {
    id: 4,
    title: '数字化转型与政务服务创新',
    speaker: '赵局长',
    content: '探讨数字化转型背景下政务服务的创新模式和实践经验...',
    lectureTime: '2024-02-01 10:00:00',
    location: '政务服务中心',
    contactInfo: '13800138004',
    summary: '数字化政务服务创新讲座',
    coverImage: '/images/lecture4.jpg',
    status: 2,
    enableDownload: true,
    downloadFormat: 'docx',
    publishTime: '2024-02-02 09:00:00',
    createTime: '2025-06-30 14:00:00',
    updateTime: '2024-02-02 09:00:00',
    creator: '孙建',
    templateId: 3,
    viewCount: 234,
    downloadCount: 45
  },
  {
    id: 5,
    title: '青年干部成长成才路径',
    speaker: '孙科长',
    content: '分享青年干部在新时代的成长路径和能力提升方法...',
    lectureTime: '2024-02-10 14:00:00',
    location: '青年干部培训中心',
    contactInfo: '13800138005',
    summary: '青年干部培养专题讲座',
    status: 1,
    enableDownload: true,
    downloadFormat: 'txt',
    createTime: '2024-02-08 09:00:00',
    updateTime: '2024-02-09 16:00:00',
    creator: '黄鑫',
    templateId: 2,
    viewCount: 0,
    downloadCount: 0
  }
]

// 模拟模板数据
const mockTemplates: DocumentTemplate[] = [
  {
    id: 1,
    name: '标准宣讲模板',
    description: '适用于一般性宣讲活动的标准模板',
    layout: {
      id: 1,
      name: '标准布局',
      structure: 1,
      textPosition: [{ x: 0, y: 0, width: 100, height: 60 }],
      imagePosition: [{ x: 0, y: 60, width: 100, height: 40 }],
      videoPosition: []
    },
    style: {
      id: 1,
      name: '标准样式',
      colors: {
        primary: '#1890ff',
        secondary: '#f0f0f0',
        background: '#ffffff',
        text: '#333333',
        accent: '#52c41a'
      },
      fonts: {
        titleFont: 'Microsoft YaHei',
        contentFont: 'Microsoft YaHei',
        titleSize: 24,
        contentSize: 14,
        lineHeight: 1.6
      },
      spacing: {
        margin: 20,
        padding: 16,
        lineSpacing: 1.6,
        paragraphSpacing: 12
      },
      borders: {
        width: 1,
        style: 'solid',
        color: '#d9d9d9',
        radius: 4
      }
    },
    placeholders: [
      {
        id: 1,
        name: '宣讲主题',
        type: 1,
        position: { x: 0, y: 0, width: 100, height: 10 },
        required: true,
        validation: { required: true, minLength: 5, maxLength: 50, message: '主题长度应在5-50字符之间' }
      },
      {
        id: 2,
        name: '主讲人',
        type: 1,
        position: { x: 0, y: 10, width: 50, height: 5 },
        required: true,
        validation: { required: true, maxLength: 20, message: '主讲人姓名不能超过20字符' }
      }
    ],
    isShared: true,
    creator: '系统管理员',
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-01 10:00:00',
    usageCount: 15,
    category: 1
  },
  {
    id: 2,
    name: '会议记录模板',
    description: '适用于会议纪实记录的专用模板',
    layout: {
      id: 2,
      name: '会议布局',
      structure: 2,
      textPosition: [
        { x: 0, y: 0, width: 60, height: 100 },
        { x: 60, y: 0, width: 40, height: 100 }
      ],
      imagePosition: [{ x: 60, y: 50, width: 40, height: 30 }],
      videoPosition: []
    },
    style: {
      id: 2,
      name: '会议样式',
      colors: {
        primary: '#722ed1',
        secondary: '#f9f0ff',
        background: '#ffffff',
        text: '#262626',
        accent: '#13c2c2'
      },
      fonts: {
        titleFont: 'SimHei',
        contentFont: 'SimSun',
        titleSize: 20,
        contentSize: 12,
        lineHeight: 1.8
      },
      spacing: {
        margin: 16,
        padding: 12,
        lineSpacing: 1.8,
        paragraphSpacing: 10
      },
      borders: {
        width: 2,
        style: 'solid',
        color: '#722ed1',
        radius: 6
      }
    },
    placeholders: [
      {
        id: 3,
        name: '会议主题',
        type: 1,
        position: { x: 0, y: 0, width: 60, height: 8 },
        required: true,
        validation: { required: true, minLength: 3, maxLength: 40, message: '会议主题长度应在3-40字符之间' }
      },
      {
        id: 4,
        name: '会议时间',
        type: 4,
        position: { x: 0, y: 8, width: 30, height: 5 },
        required: true,
        validation: { required: true, message: '请选择会议时间' }
      }
    ],
    isShared: true,
    creator: '周海军',
    createTime: '2025-06-05 14:00:00',
    updateTime: '2025-06-10 16:00:00',
    usageCount: 8,
    category: 2
  },
  {
    id: 3,
    name: '活动宣传模板',
    description: '适用于各类活动宣传的模板',
    layout: {
      id: 3,
      name: '活动布局',
      structure: 3,
      textPosition: [
        { x: 0, y: 0, width: 33, height: 100 },
        { x: 33, y: 0, width: 34, height: 100 },
        { x: 67, y: 0, width: 33, height: 100 }
      ],
      imagePosition: [
        { x: 0, y: 70, width: 33, height: 30 },
        { x: 67, y: 70, width: 33, height: 30 }
      ],
      videoPosition: [{ x: 33, y: 50, width: 34, height: 30 }]
    },
    style: {
      id: 3,
      name: '活动样式',
      colors: {
        primary: '#fa541c',
        secondary: '#fff2e8',
        background: '#ffffff',
        text: '#434343',
        accent: '#faad14'
      },
      fonts: {
        titleFont: 'Microsoft YaHei',
        contentFont: 'Microsoft YaHei',
        titleSize: 22,
        contentSize: 13,
        lineHeight: 1.7
      },
      spacing: {
        margin: 18,
        padding: 14,
        lineSpacing: 1.7,
        paragraphSpacing: 11
      },
      borders: {
        width: 1,
        style: 'dashed',
        color: '#fa541c',
        radius: 8
      }
    },
    placeholders: [
      {
        id: 5,
        name: '活动标题',
        type: 1,
        position: { x: 0, y: 0, width: 100, height: 12 },
        required: true,
        validation: { required: true, minLength: 4, maxLength: 60, message: '活动标题长度应在4-60字符之间' }
      },
      {
        id: 6,
        name: '活动图片',
        type: 2,
        position: { x: 0, y: 70, width: 33, height: 30 },
        required: false,
        validation: { required: false, message: '请上传活动图片' }
      }
    ],
    isShared: false,
    creator: '王东',
    createTime: '2025-06-12 11:00:00',
    updateTime: '2025-06-15 09:00:00',
    usageCount: 12,
    category: 3
  }
]

// 模拟统计数据
const mockStatistics: StatisticsData = {
  totalDocuments: 5,
  publishedDocuments: 3,
  draftDocuments: 2,
  totalSpeakers: 5,
  totalTemplates: 3,
  completionRate: 85.5,
  lectureCount: 12,
  departmentStats: [
    {
      departmentName: '市委办公室',
      totalDocuments: 2,
      publishedDocuments: 2,
      completionRate: 100,
      lectureCount: 4,
      activeSpeakers: 2
    },
    {
      departmentName: '市政府办公室',
      totalDocuments: 2,
      publishedDocuments: 1,
      completionRate: 75,
      lectureCount: 3,
      activeSpeakers: 2
    },
    {
      departmentName: '组织部',
      totalDocuments: 1,
      publishedDocuments: 0,
      completionRate: 60,
      lectureCount: 1,
      activeSpeakers: 1
    }
  ],
  monthlyStats: [
    {
      month: '2025-06',
      documentCount: 3,
      lectureCount: 8,
      speakerCount: 3,
      completionRate: 88.5
    },
    {
      month: '2024-02',
      documentCount: 2,
      lectureCount: 4,
      speakerCount: 2,
      completionRate: 82.0
    }
  ],
  speakerStats: [
    {
      speakerName: '张书记',
      department: '市委办公室',
      lectureCount: 3,
      documentCount: 1,
      totalViews: 156,
      avgRating: 4.8
    },
    {
      speakerName: '杜佳佳',
      department: '市委办公室',
      lectureCount: 2,
      documentCount: 1,
      totalViews: 89,
      avgRating: 4.6
    },
    {
      speakerName: '赵局长',
      department: '市政府办公室',
      lectureCount: 4,
      documentCount: 1,
      totalViews: 234,
      avgRating: 4.9
    }
  ]
}

// API接口实现
export const yulifacetoFaceApi = {
  // 获取统计数据
  async getStatistics(): Promise<StatisticsData> {
    await delay(500)
    return mockStatistics
  },

  // 获取纪实列表
  async getDocumentList(params?: DocumentSearchParams): Promise<{
    list: DocumentRecord[]
    total: number
  }> {
    await delay(400)
    let filteredList = [...mockDocuments]

    // 模拟筛选逻辑
    if (params?.title) {
      filteredList = filteredList.filter(item => 
        item.title.includes(params.title!)
      )
    }
    if (params?.speaker) {
      filteredList = filteredList.filter(item => 
        item.speaker.includes(params.speaker!)
      )
    }
    if (params?.status) {
      filteredList = filteredList.filter(item => item.status === params.status)
    }
    if (params?.location) {
      filteredList = filteredList.filter(item => 
        item.location.includes(params.location!)
      )
    }

    return {
      list: filteredList,
      total: filteredList.length
    }
  },

  // 获取模板列表
  async getTemplateList(params?: TemplateSearchParams): Promise<{
    list: DocumentTemplate[]
    total: number
  }> {
    await delay(300)
    let filteredList = [...mockTemplates]

    // 模拟筛选逻辑
    if (params?.name) {
      filteredList = filteredList.filter(item => 
        item.name.includes(params.name!)
      )
    }
    if (params?.category) {
      filteredList = filteredList.filter(item => item.category === params.category)
    }
    if (params?.isShared !== undefined) {
      filteredList = filteredList.filter(item => item.isShared === params.isShared)
    }

    return {
      list: filteredList,
      total: filteredList.length
    }
  },

  // 创建纪实
  async createDocument(data: Partial<DocumentRecord>): Promise<boolean> {
    await delay(800)
    console.log('创建纪实:', data)
    return true
  },

  // 更新纪实
  async updateDocument(id: number, data: Partial<DocumentRecord>): Promise<boolean> {
    await delay(600)
    console.log('更新纪实:', id, data)
    return true
  },

  // 删除纪实
  async deleteDocument(id: number): Promise<boolean> {
    await delay(500)
    console.log('删除纪实:', id)
    return true
  },

  // 发布纪实
  async publishDocument(id: number): Promise<boolean> {
    await delay(700)
    console.log('发布纪实:', id)
    return true
  },

  // 创建模板
  async createTemplate(data: Partial<DocumentTemplate>): Promise<boolean> {
    await delay(800)
    console.log('创建模板:', data)
    return true
  },

  // 更新模板
  async updateTemplate(id: number, data: Partial<DocumentTemplate>): Promise<boolean> {
    await delay(600)
    console.log('更新模板:', id, data)
    return true
  },

  // 删除模板
  async deleteTemplate(id: number): Promise<boolean> {
    await delay(500)
    console.log('删除模板:', id)
    return true
  },

  // 应用模板
  async applyTemplate(templateId: number, documentData: Partial<DocumentRecord>): Promise<boolean> {
    await delay(600)
    console.log('应用模板:', templateId, documentData)
    return true
  },

  // 恢复纪实
  async restoreDocument(id: number): Promise<boolean> {
    await delay(500)
    console.log('恢复纪实:', id)
    return true
  },

  // 下载纪实
  async downloadDocument(id: number): Promise<boolean> {
    await delay(400)
    console.log('下载纪实:', id)
    return true
  }
}
