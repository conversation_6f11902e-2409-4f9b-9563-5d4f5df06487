import { http } from '@/utils/request'
const apiUrl1 = import.meta.env.VITE_API_NEW_URL
const getMap = (params?: string) => {
    return import(`./json/${params}.json`)
    // return http.get(`https://ows-cdn.obs.cn-north-4.myhuaweicloud.com/public/${params}.json`)
}
const getData = (params?: Object) => {
    const apiUrl = import.meta.env.MODE === 'production' ? location.origin + '/jsapi/api' : import.meta.env.VITE_API_URL
    return null //http.post(`${apiUrl}/view/get`, params)
}

export { getMap, getData, }
