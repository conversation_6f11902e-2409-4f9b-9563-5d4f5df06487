// 统计分析模块API接口
import request from '@/utils/request'
import { 
  <PERSON>LLBACK_CORE_METRICS,
  FALLBACK_MONTHLY_STATISTICS,
  FALLBACK_DEPARTMENT_STATISTICS,
  FALLBACK_SPEAKER_STATISTICS,
  FALLBACK_TREND_ANALYSIS,
  FALLBACK_USER_BEHAVIOR_ANALYSIS,
  FALLBACK_REAL_TIME_DATA,
  createFallbackDataSource,
  createAPIDataSource,
  type DataSourceInfo
} from '@/data/static-fallback-data'

// 统计分析相关类型定义
export interface CoreMetrics {
  totalDocuments: number
  publishedDocuments: number
  completionRate: number
  totalSpeakers: number
  totalDepartments: number
  averageViews: number
  totalDownloads: number
  userEngagement: number
}

export interface MonthlyStatistics {
  month: string
  documentCount: number
  publishCount: number
  viewCount: number
  downloadCount: number
}

export interface DepartmentStatistics {
  department: string
  documentCount: number
  publishRate: number
  totalViews: number
  avgScore: number
}

export interface SpeakerStatistics {
  speaker: string
  documentCount: number
  totalViews: number
  avgRating: number
  department: string
}

export interface TrendAnalysis {
  date: string
  documentCount: number
  viewCount: number
  downloadCount: number
  userCount: number
}

export interface UserBehaviorAnalysis {
  readingPatterns: {
    peakHours: string[]
    avgReadingTime: number
    bounceRate: number
    completionRate: number
  }
  deviceAnalysis: {
    [key: string]: {
      count: number
      percentage: number
    }
  }
  sourceAnalysis: {
    [key: string]: {
      count: number
      percentage: number
    }
  }
}

export interface CustomQueryParams {
  startDate?: string
  endDate?: string
  departments?: string[]
  speakers?: string[]
  metrics?: string[]
}

export interface ExportParams {
  format?: 'excel' | 'pdf' | 'csv'
  reportType?: string
  timeRange?: string
  includeCharts?: boolean
}

export interface RealTimeData {
  currentOnlineUsers: number
  todayViews: number
  todayDownloads: number
  recentActivities: Array<{
    time: string
    activity: string
    type: string
  }>
  systemStatus: {
    serverLoad: number
    responseTime: number
    errorRate: string
  }
  timestamp: string
}

// 带数据源信息的返回类型
export interface ApiResponseWithSource<T> {
  data: T
  dataSource: DataSourceInfo
}

// ================================
// 统计分析API接口函数
// ================================

/**
 * 获取核心指标数据
 */
export const getCoreMetrics = async (): Promise<ApiResponseWithSource<CoreMetrics>> => {
  try {
    const response = await request.get('/api/statistics-analysis/core-metrics')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取核心指标失败，使用静态数据:', error)
    return {
      data: FALLBACK_CORE_METRICS,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取月度统计数据
 */
export const getMonthlyStatistics = async (params?: {
  year?: string
  limit?: number
}): Promise<ApiResponseWithSource<{
  data: MonthlyStatistics[]
  total: number
}>> => {
  try {
    const response = await request.get('/api/statistics-analysis/monthly', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取月度统计数据失败，使用静态数据:', error)
    let filteredData = [...FALLBACK_MONTHLY_STATISTICS]
    
    // 根据参数筛选静态数据
    if (params?.year) {
      filteredData = filteredData.filter(item => item.month.startsWith(params.year!))
    }
    if (params?.limit) {
      filteredData = filteredData.slice(0, params.limit)
    }
    
    return {
      data: {
        data: filteredData,
        total: filteredData.length
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取部门统计数据
 */
export const getDepartmentStatistics = async (params?: {
  sortBy?: string
  order?: 'asc' | 'desc'
  limit?: number
}): Promise<ApiResponseWithSource<{
  data: DepartmentStatistics[]
  total: number
}>> => {
  try {
    const response = await request.get('/api/statistics-analysis/department', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取部门统计数据失败，使用静态数据:', error)
    let data = [...FALLBACK_DEPARTMENT_STATISTICS]
    
    // 处理参数筛选
    if (params?.sortBy && data.length > 0) {
      data.sort((a, b) => {
        const aValue = a[params.sortBy as keyof DepartmentStatistics] as number
        const bValue = b[params.sortBy as keyof DepartmentStatistics] as number
        return params.order === 'desc' ? bValue - aValue : aValue - bValue
      })
    }
    if (params?.limit) {
      data = data.slice(0, params.limit)
    }
    
    return {
      data: {
        data,
        total: data.length
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取主讲人统计数据
 */
export const getSpeakerStatistics = async (params?: {
  department?: string
  sortBy?: string
  order?: 'asc' | 'desc'
  limit?: number
}): Promise<ApiResponseWithSource<{
  data: SpeakerStatistics[]
  total: number
}>> => {
  try {
    const response = await request.get('/api/statistics-analysis/speaker', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取主讲人统计数据失败，使用静态数据:', error)
    let data = [...FALLBACK_SPEAKER_STATISTICS]
    
    // 处理参数筛选
    if (params?.department) {
      data = data.filter(item => item.department === params.department)
    }
    if (params?.sortBy && data.length > 0) {
      data.sort((a, b) => {
        const aValue = a[params.sortBy as keyof SpeakerStatistics] as number
        const bValue = b[params.sortBy as keyof SpeakerStatistics] as number
        return params.order === 'desc' ? bValue - aValue : aValue - bValue
      })
    }
    if (params?.limit) {
      data = data.slice(0, params.limit)
    }
    
    return {
      data: {
        data,
        total: data.length
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取趋势分析数据
 */
export const getTrendAnalysis = async (params?: {
  type?: 'daily' | 'weekly' | 'yearly'
  period?: number
}): Promise<ApiResponseWithSource<{
  data: TrendAnalysis[]
  type: string
}>> => {
  try {
    const response = await request.get('/api/statistics-analysis/trend', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取趋势分析数据失败，使用静态数据:', error)
    let data = [...FALLBACK_TREND_ANALYSIS]
    
    // 根据类型筛选数据
    const type = params?.type || 'daily'
    if (params?.period) {
      data = data.slice(0, params.period)
    }
    
    return {
      data: {
        data,
        type
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取用户行为分析
 */
export const getUserBehaviorAnalysis = async (params?: {
  type?: string
}): Promise<ApiResponseWithSource<UserBehaviorAnalysis>> => {
  try {
    const response = await request.get('/api/statistics-analysis/user-behavior', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取用户行为分析失败，使用静态数据:', error)
    return {
      data: FALLBACK_USER_BEHAVIOR_ANALYSIS,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取综合统计数据
 */
export const getComprehensiveStatistics = async (params?: {
  timeRange?: string
  dimension?: string
}): Promise<ApiResponseWithSource<any>> => {
  try {
    const response = await request.get('/api/statistics-analysis/comprehensive', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取综合统计数据失败，使用静态数据:', error)
    const fallbackData = {
      coreMetrics: FALLBACK_CORE_METRICS,
      monthlyStats: FALLBACK_MONTHLY_STATISTICS.slice(0, 6),
      departmentStats: FALLBACK_DEPARTMENT_STATISTICS.slice(0, 5),
      speakerStats: FALLBACK_SPEAKER_STATISTICS.slice(0, 5),
      trendData: FALLBACK_TREND_ANALYSIS.slice(0, 10)
    }
    return {
      data: fallbackData,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 自定义统计查询
 */
export const customStatisticsQuery = async (params: CustomQueryParams): Promise<ApiResponseWithSource<any>> => {
  try {
    const response = await request.post('/api/statistics-analysis/custom-query', params)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('自定义统计查询失败，使用静态数据:', error)
    // 根据查询参数返回合适的静态数据
    let fallbackData: any = {
      totalRecords: 0,
      data: []
    }
    
    if (params.metrics?.includes('documents')) {
      fallbackData.data.push(...FALLBACK_MONTHLY_STATISTICS.slice(0, 5))
    }
    if (params.metrics?.includes('departments')) {
      fallbackData.data.push(...FALLBACK_DEPARTMENT_STATISTICS.slice(0, 3))
    }
    if (params.metrics?.includes('speakers')) {
      fallbackData.data.push(...FALLBACK_SPEAKER_STATISTICS.slice(0, 3))
    }
    
    fallbackData.totalRecords = fallbackData.data.length
    
    return {
      data: fallbackData,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 导出统计报表
 */
export const exportStatisticsReport = async (params: ExportParams): Promise<ApiResponseWithSource<{
  downloadUrl: string
  fileName: string
  fileSize: string
  recordCount: number
}>> => {
  try {
    const response = await request.post('/api/statistics-analysis/export', params)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('导出统计报表失败，生成本地报表:', error)
    // 生成模拟的导出结果
    const format = params.format || 'excel'
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const fallbackExport = {
      downloadUrl: `#fallback-export-${timestamp}`,
      fileName: `统计报表_${timestamp}.${format === 'excel' ? 'xlsx' : format}`,
      fileSize: '2.5MB',
      recordCount: FALLBACK_MONTHLY_STATISTICS.length + FALLBACK_DEPARTMENT_STATISTICS.length
    }
    
    return {
      data: fallbackExport,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取实时统计数据
 */
export const getRealTimeStatistics = async (): Promise<ApiResponseWithSource<RealTimeData>> => {
  try {
    const response = await request.get('/api/statistics-analysis/real-time')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取实时统计数据失败，使用静态数据:', error)
    return {
      data: {
        ...FALLBACK_REAL_TIME_DATA,
        timestamp: new Date().toISOString() // 更新时间戳
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 更新统计设置
 */
export const updateStatisticsSettings = async (settings: {
  refreshInterval?: number
  defaultTimeRange?: string
  displayMetrics?: string[]
  alertThresholds?: Record<string, number>
}): Promise<any> => {
  try {
    const response = await request.put('/api/statistics-analysis/settings', settings)
    return response.data
  } catch (error) {
    console.error('更新统计设置失败:', error)
    throw new Error('更新统计设置失败')
  }
}

/**
 * 获取仪表板配置
 */
export const getDashboardConfig = async (): Promise<any> => {
  try {
    const response = await request.get('/api/statistics-analysis/dashboard-config')
    return response.data
  } catch (error) {
    console.error('获取仪表板配置失败:', error)
    throw new Error('获取仪表板配置失败')
  }
}

// ================================
// 兼容性导出 - 提供更友好的API名称
// ================================

// 统计分析API对象
export const statisticsAnalysisApi = {
  // 基础数据获取
  getCoreMetrics,
  getMonthlyStatistics,
  getDepartmentStatistics,
  getSpeakerStatistics,
  getTrendAnalysis,
  getUserBehaviorAnalysis,
  getComprehensiveStatistics,
  
  // 高级功能
  customQuery: customStatisticsQuery,
  exportReport: exportStatisticsReport,
  getRealTimeData: getRealTimeStatistics,
  
  // 配置管理
  updateSettings: updateStatisticsSettings,
  getDashboardConfig
}

// 默认导出
export default statisticsAnalysisApi