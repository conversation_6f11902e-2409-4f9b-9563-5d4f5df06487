// 统计分析模块API接口
import request from '@/utils/request'
import { 
  FALLBACK_HEALTH_CHECK_STATISTICS,
  FALLBACK_HEALTH_CHECK_RECORDS,
  createFallbackDataSource,
  createAPIDataSource,
  type DataSourceInfo
} from '@/data/static-fallback-data'

// 数据体检统计分析相关类型定义
export interface HealthCheckCoreMetrics {
  totalExceptions: number
  fixedExceptions: number
  completenessRate: number
  accuracyRate: number
  remediationRate: number
  totalChecks: number
  completedChecks: number
  failedChecks: number
}

export interface HealthCheckDepartmentStats {
  unitId: string
  unitName: string
  exceptionCount: number
  totalChecks: number
  completenessRate: number
  accuracyRate: number
  overallScore: number
  trend: number
  lastCheckTime: string
}

export interface HealthCheckExceptionType {
  type: number
  typeName: string
  count: number
  avgSeverity: number
  percentage: number
  description: string
}

export interface HealthCheckTrendData {
  date: string
  exceptions: number
  checks: number
  fixedCount: number
  newCount: number
}

export interface HealthCheckRealTimeData {
  currentActiveChecks: number
  todayExceptions: number
  todayFixedExceptions: number
  recentActivities: Array<{
    time: string
    activity: string
    type: 'exception' | 'fix' | 'check' | 'alert'
    severity: 'high' | 'medium' | 'low'
  }>
  systemStatus: {
    checkEngineLoad: number
    avgResponseTime: number
    errorRate: string
    lastUpdateTime: string
  }
  timestamp: string
}

export interface ExportParams {
  format?: 'excel' | 'pdf' | 'csv'
  reportType?: 'summary' | 'detailed' | 'trend' | 'department'
  timeRange?: string
  includeDepartments?: string[]
  includeCharts?: boolean
}

// 带数据源信息的返回类型
export interface ApiResponseWithSource<T> {
  data: T
  dataSource: DataSourceInfo
}

// ================================
// 统计分析API接口函数
// ================================

/**
 * 获取数据体检核心指标
 */
export const getCoreMetrics = async (): Promise<ApiResponseWithSource<HealthCheckCoreMetrics>> => {
  try {
    const response = await request.get('/api/health-check/statistics/core-metrics')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取数据体检核心指标失败，使用静态数据:', error)
    const fallbackData: HealthCheckCoreMetrics = {
      totalExceptions: FALLBACK_HEALTH_CHECK_STATISTICS.totalExceptions,
      fixedExceptions: FALLBACK_HEALTH_CHECK_STATISTICS.fixedExceptions,
      completenessRate: Math.round((FALLBACK_HEALTH_CHECK_STATISTICS.completedChecks / FALLBACK_HEALTH_CHECK_STATISTICS.totalChecks) * 100 * 10) / 10, // 93.1%
      accuracyRate: Math.round(((FALLBACK_HEALTH_CHECK_STATISTICS.totalChecks - FALLBACK_HEALTH_CHECK_STATISTICS.failedChecks) / FALLBACK_HEALTH_CHECK_STATISTICS.totalChecks) * 100 * 10) / 10, // 93.1%
      remediationRate: Math.round((FALLBACK_HEALTH_CHECK_STATISTICS.fixedExceptions / FALLBACK_HEALTH_CHECK_STATISTICS.totalExceptions) * 100 * 10) / 10, // 57.1%
      totalChecks: FALLBACK_HEALTH_CHECK_STATISTICS.totalChecks,
      completedChecks: FALLBACK_HEALTH_CHECK_STATISTICS.completedChecks,
      failedChecks: FALLBACK_HEALTH_CHECK_STATISTICS.failedChecks
    }
    return {
      data: fallbackData,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}


/**
 * 获取部门数据体检统计
 */
export const getDepartmentStatistics = async (params?: {
  sortBy?: string
  order?: 'asc' | 'desc'
  limit?: number
}): Promise<ApiResponseWithSource<{
  data: HealthCheckDepartmentStats[]
  total: number
}>> => {
  try {
    const response = await request.get('/api/health-check/statistics/departments', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取部门数据体检统计失败，使用静态数据:', error)
    // 构建部门数据体检静态数据
    const departments = ['办公室', '组织部', '宣传部', '政法委', '统战部', '纪检委', '机关工委', '老干部局', '党校', '档案局']
    let data: HealthCheckDepartmentStats[] = departments.map((dept, index) => ({
      unitId: `dept_${index + 1}`,
      unitName: dept,
      exceptionCount: Math.floor(Math.random() * 15) + 8, // 8-22 个异常
      totalChecks: Math.floor(Math.random() * 50) + 100, // 100-150 次检查
      completenessRate: Math.floor(Math.random() * 20) + 80, // 80-100% 完整性
      accuracyRate: Math.floor(Math.random() * 10) + 90, // 90-100% 准确性
      overallScore: Math.floor(Math.random() * 20) + 80, // 80-100 分
      trend: (Math.random() - 0.5) * 10, // -5% ~ +5% 趋势
      lastCheckTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }))
    
    // 处理排序
    if (params?.sortBy && data.length > 0) {
      data.sort((a, b) => {
        const aValue = a[params.sortBy as keyof HealthCheckDepartmentStats] as number
        const bValue = b[params.sortBy as keyof HealthCheckDepartmentStats] as number
        return params.order === 'desc' ? bValue - aValue : aValue - bValue
      })
    }
    if (params?.limit) {
      data = data.slice(0, params.limit)
    }
    
    return {
      data: {
        data,
        total: data.length
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}


/**
 * 获取数据体检趋势分析
 */
export const getTrendAnalysis = async (params?: {
  type?: 'daily' | 'weekly' | 'monthly'
  period?: number
}): Promise<ApiResponseWithSource<{
  data: HealthCheckTrendData[]
  type: string
}>> => {
  try {
    const response = await request.get('/api/health-check/statistics/trend', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取数据体检趋势分析失败，使用静态数据:', error)
    // 使用FALLBACK_HEALTH_CHECK_STATISTICS中的exceptionTrend数据，确保数据结构匹配
    const exceptionTrendData = FALLBACK_HEALTH_CHECK_STATISTICS.exceptionTrend
    let data: HealthCheckTrendData[] = exceptionTrendData.map((item, index) => ({
      date: item.date,
      exceptions: item.count,
      checks: Math.floor(item.count * (4.5 + Math.sin(index) * 0.5)), // 基于异常数量合理计算检查次数
      fixedCount: Math.floor(item.count * 0.65), // 65% 修复率
      newCount: index > 0 ? Math.max(0, item.count - exceptionTrendData[index-1].count) : Math.floor(item.count * 0.8) // 新增数量
    }))
    
    const type = params?.type || 'daily'
    if (params?.period && params.period < data.length) {
      data = data.slice(-params.period) // 获取最近的数据
    }
    
    return {
      data: {
        data,
        type
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取异常类型统计
 */
export const getExceptionTypeStatistics = async (params?: {
  sortBy?: 'count' | 'severity'
  order?: 'asc' | 'desc'
}): Promise<ApiResponseWithSource<{
  data: HealthCheckExceptionType[]
  total: number
}>> => {
  try {
    const response = await request.get('/api/health-check/statistics/exception-types', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取异常类型统计失败，使用静态数据:', error)
    // 基于FALLBACK_HEALTH_CHECK_STATISTICS中的checkTypeStats数据，转换为异常类型统计
    const checkTypes = FALLBACK_HEALTH_CHECK_STATISTICS.checkTypeStats
    const totalExceptions = FALLBACK_HEALTH_CHECK_STATISTICS.totalExceptions // 使用真实总数156而不是checkTypeStats的部分和
    
    let data: HealthCheckExceptionType[] = [
      { 
        type: 1, 
        typeName: '数据缺失', 
        count: checkTypes[0]?.exceptionCount || 12, 
        avgSeverity: 3.2, 
        percentage: Math.round(((checkTypes[0]?.exceptionCount || 12) / totalExceptions) * 100 * 10) / 10, 
        description: '必填字段为空或缺失关键信息' 
      },
      { 
        type: 2, 
        typeName: '格式错误', 
        count: checkTypes[1]?.exceptionCount || 8, 
        avgSeverity: 2.1, 
        percentage: Math.round(((checkTypes[1]?.exceptionCount || 8) / totalExceptions) * 100 * 10) / 10, 
        description: '数据格式不符合规范要求' 
      },
      { 
        type: 3, 
        typeName: '逻辑异常', 
        count: checkTypes[2]?.exceptionCount || 15, 
        avgSeverity: 4.1, 
        percentage: Math.round(((checkTypes[2]?.exceptionCount || 15) / totalExceptions) * 100 * 10) / 10, 
        description: '数据逻辑关系不合理或矛盾' 
      },
      { 
        type: 4, 
        typeName: '重复记录', 
        count: checkTypes[3]?.exceptionCount || 9, 
        avgSeverity: 2.8, 
        percentage: Math.round(((checkTypes[3]?.exceptionCount || 9) / totalExceptions) * 100 * 10) / 10, 
        description: '存在重复的数据记录' 
      },
      { 
        type: 5, 
        typeName: '关联异常', 
        count: totalExceptions - checkTypes.reduce((sum, item) => sum + item.exceptionCount, 0), // 计算剩余异常数量
        avgSeverity: 3.5, 
        percentage: Math.round(((totalExceptions - checkTypes.reduce((sum, item) => sum + item.exceptionCount, 0)) / totalExceptions) * 100 * 10) / 10, 
        description: '数据关联关系异常或断裂' 
      }
    ]
    
    // 处理排序
    if (params?.sortBy) {
      data.sort((a, b) => {
        const aValue = params.sortBy === 'count' ? a.count : a.avgSeverity
        const bValue = params.sortBy === 'count' ? b.count : b.avgSeverity
        return params.order === 'desc' ? bValue - aValue : aValue - bValue
      })
    }
    
    return {
      data: {
        data,
        total: data.length
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}



/**
 * 导出数据体检统计报表
 */
export const exportStatisticsReport = async (params: ExportParams): Promise<ApiResponseWithSource<{
  downloadUrl: string
  fileName: string
  fileSize: string
  recordCount: number
}>> => {
  try {
    const response = await request.post('/api/health-check/statistics/export', params)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('导出数据体检统计报表失败，生成本地报表:', error)
    const format = params.format || 'excel'
    const reportType = params.reportType || 'summary'
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const fallbackExport = {
      downloadUrl: `#health-check-export-${timestamp}`,
      fileName: `数据体检统计报表_${reportType}_${timestamp}.${format === 'excel' ? 'xlsx' : format}`,
      fileSize: '1.8MB',
      recordCount: FALLBACK_HEALTH_CHECK_STATISTICS.totalChecks
    }
    
    return {
      data: fallbackExport,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取实时数据体检状态
 */
export const getRealTimeStatistics = async (): Promise<ApiResponseWithSource<HealthCheckRealTimeData>> => {
  try {
    const response = await request.get('/api/health-check/statistics/real-time')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取实时数据体检状态失败，使用静态数据:', error)
    const now = new Date()
    const fallbackRealTimeData: HealthCheckRealTimeData = {
      currentActiveChecks: 3,
      todayExceptions: 47,
      todayFixedExceptions: 23,
      recentActivities: [
        { time: now.toTimeString().slice(0, 8), activity: '检测到组织部数据缺失异常', type: 'exception', severity: 'medium' },
        { time: new Date(now.getTime() - 120000).toTimeString().slice(0, 8), activity: '修复了办公室格式错误问题', type: 'fix', severity: 'low' },
        { time: new Date(now.getTime() - 300000).toTimeString().slice(0, 8), activity: '完成宣传部数据体检', type: 'check', severity: 'low' },
        { time: new Date(now.getTime() - 480000).toTimeString().slice(0, 8), activity: '发现政法委高优先级异常', type: 'alert', severity: 'high' },
        { time: new Date(now.getTime() - 600000).toTimeString().slice(0, 8), activity: '完成纪检委数据体检', type: 'check', severity: 'low' }
      ],
      systemStatus: {
        checkEngineLoad: 34.5,
        avgResponseTime: 89,
        errorRate: '0.03%',
        lastUpdateTime: now.toISOString().slice(0, 19).replace('T', ' ')
      },
      timestamp: now.toISOString()
    }
    
    return {
      data: fallbackRealTimeData,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 更新数据体检统计设置
 */
export const updateStatisticsSettings = async (settings: {
  refreshInterval?: number
  defaultTimeRange?: string
  displayMetrics?: string[]
  alertThresholds?: Record<string, number>
  enableRealTimeMonitoring?: boolean
  autoFixEnabled?: boolean
}): Promise<any> => {
  try {
    const response = await request.put('/api/health-check/statistics/settings', settings)
    return response.data
  } catch (error) {
    console.error('更新数据体检统计设置失败:', error)
    throw new Error('更新数据体检统计设置失败')
  }
}

/**
 * 获取数据体检统计仪表板配置
 */
export const getDashboardConfig = async (): Promise<any> => {
  try {
    const response = await request.get('/api/health-check/statistics/dashboard-config')
    return response.data
  } catch (error) {
    console.error('获取数据体检统计仪表板配置失败:', error)
    throw new Error('获取数据体检统计仪表板配置失败')
  }
}

// ================================
// 数据体检统计分析API对象 - 提供更友好的API名称
// ================================

// 数据体检统计分析API对象
export const healthCheckStatisticsApi = {
  // 核心数据获取
  getCoreMetrics,
  getDepartmentStatistics,
  getExceptionTypeStatistics,
  getTrendAnalysis,
  getRealTimeData: getRealTimeStatistics,
  
  // 导出和配置功能
  exportReport: exportStatisticsReport,
  updateSettings: updateStatisticsSettings,
  getDashboardConfig
}

// 兼容性导出 - 保持原有名称以免破坏现有代码
export const statisticsAnalysisApi = healthCheckStatisticsApi

// 默认导出
export default healthCheckStatisticsApi