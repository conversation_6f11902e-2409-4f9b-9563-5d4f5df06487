// 定时自动体检模块API接口定义
import request from '@/utils/request'
import { 
  createFallbackDataSource,
  createAPIDataSource,
  type DataSourceInfo
} from '@/data/static-fallback-data'

// 带数据源信息的返回类型
export interface ApiResponseWithSource<T> {
  data: T
  dataSource: DataSourceInfo
}

// 定时任务接口类型定义
export interface ScheduledTask {
  id: string
  taskName: string
  taskType: number
  checkTypes: number[]
  cronExpression?: string
  cronDescription?: string
  intervalMinutes?: number
  dailyTime?: string
  isEnabled: boolean
  status: number // 1-待执行 2-执行中 3-已完成 4-执行失败
  lastExecuteTime?: string
  nextExecuteTime?: string
  executionCount?: number
  successCount?: number
  failedCount?: number
  avgDuration?: number
  createTime: string
  updateTime: string
  creator: string
  description?: string
  exceptionThreshold?: number
  notificationEnabled?: boolean
  notificationMethods?: string[]
  notificationRecipients?: string[]
}

export interface ScheduledTaskSearchParams {
  taskName?: string
  taskType?: number
  isEnabled?: boolean
  status?: number
  page?: number
  pageSize?: number
}

export interface ExecutionLog {
  id: string
  taskId: string
  level: 'info' | 'warning' | 'error' | 'success'
  timestamp: string
  message: string
  details?: string
}

// ================================
// 静态降级数据
// ================================

const FALLBACK_SCHEDULED_TASKS: ScheduledTask[] = [
  {
    id: '1',
    taskName: '每日数据质量体检',
    taskType: 1,
    checkTypes: [1, 2, 3, 4],
    cronExpression: '0 2 * * *',
    cronDescription: '每天凌晨2点执行',
    dailyTime: '02:00',
    isEnabled: true,
    status: 3,
    lastExecuteTime: '2025-01-18 02:00:00',
    nextExecuteTime: '2025-01-19 02:00:00',
    executionCount: 18,
    successCount: 17,
    failedCount: 1,
    avgDuration: 285,
    createTime: '2025-01-01 00:00:00',
    updateTime: '2025-01-18 02:00:00',
    creator: '系统管理员',
    description: '每日凌晨2点执行全面数据质量体检',
    exceptionThreshold: 5,
    notificationEnabled: true,
    notificationMethods: ['email', 'system'],
    notificationRecipients: ['admin', 'manager']
  },
  {
    id: '2', 
    taskName: '用户信息完整性检查',
    taskType: 4,
    checkTypes: [4],
    intervalMinutes: 240,
    cronExpression: '0 */4 * * *',
    cronDescription: '每4小时执行一次',
    isEnabled: true,
    status: 1,
    nextExecuteTime: '2025-01-18 16:00:00',
    lastExecuteTime: '2025-01-18 12:00:00',
    executionCount: 6,
    successCount: 6,
    failedCount: 0,
    avgDuration: 120,
    createTime: '2025-01-02 15:30:00',
    updateTime: '2025-01-18 12:00:00',
    creator: '数据管理员',
    description: '每4小时检查用户信息完整性',
    exceptionThreshold: 15,
    notificationEnabled: false,
    notificationMethods: [],
    notificationRecipients: []
  }
]

const FALLBACK_EXECUTION_LOGS: ExecutionLog[] = [
  {
    id: '1',
    taskId: '1',
    level: 'info',
    timestamp: '2025-01-18 02:00:00',
    message: '开始执行每日数据质量体检任务'
  },
  {
    id: '2',
    taskId: '1', 
    level: 'success',
    timestamp: '2025-01-18 02:04:45',
    message: '数据质量体检执行完成，发现5个异常项',
    details: JSON.stringify({
      totalChecked: 1256,
      exceptions: 5,
      duration: '4分45秒'
    })
  },
  {
    id: '3',
    taskId: '2',
    level: 'info',
    timestamp: '2025-01-18 12:00:00', 
    message: '开始执行用户信息完整性检查'
  },
  {
    id: '4',
    taskId: '2',
    level: 'success',
    timestamp: '2025-01-18 12:02:00',
    message: '用户信息完整性检查完成，未发现异常',
    details: JSON.stringify({
      totalChecked: 856,
      exceptions: 0,
      duration: '2分钟'
    })
  }
]

// ================================
// API接口函数
// ================================

/**
 * 获取定时任务列表
 */
export const fetchScheduledTasks = async (params?: ScheduledTaskSearchParams): Promise<ApiResponseWithSource<{
  data: ScheduledTask[]
  total: number
  page: number
  pageSize: number
}>> => {
  try {
    const response = await request.get('/api/data-inspection/scheduled-tasks', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取定时任务列表失败，使用静态数据:', error)
    
    // 模拟分页处理
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    let filteredTasks = [...FALLBACK_SCHEDULED_TASKS]
    
    // 根据参数筛选
    if (params?.taskName) {
      filteredTasks = filteredTasks.filter(task => task.taskName.includes(params.taskName!))
    }
    if (params?.taskType) {
      filteredTasks = filteredTasks.filter(task => task.taskType === params.taskType)
    }
    if (params?.isEnabled !== undefined) {
      filteredTasks = filteredTasks.filter(task => task.isEnabled === params.isEnabled)
    }
    if (params?.status) {
      filteredTasks = filteredTasks.filter(task => task.status === params.status)
    }
    
    // 分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedTasks = filteredTasks.slice(startIndex, endIndex)
    
    return {
      data: {
        data: paginatedTasks,
        total: filteredTasks.length,
        page,
        pageSize
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 创建定时任务
 */
export const createScheduledTask = async (taskData: Partial<ScheduledTask>): Promise<ApiResponseWithSource<{
  id: string
}>> => {
  try {
    const response = await request.post('/api/data-inspection/scheduled-tasks', taskData)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('创建定时任务失败，使用静态响应:', error)
    
    // 模拟成功响应
    const newId = `task_${Date.now()}`
    return {
      data: { id: newId },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 更新定时任务
 */
export const updateScheduledTask = async (id: string, taskData: Partial<ScheduledTask>): Promise<ApiResponseWithSource<boolean>> => {
  try {
    const response = await request.put(`/api/data-inspection/scheduled-tasks/${id}`, taskData)
    return {
      data: response.data.success || true,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('更新定时任务失败，使用静态响应:', error)
    return {
      data: true,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 删除定时任务
 */
export const deleteScheduledTask = async (id: string): Promise<ApiResponseWithSource<boolean>> => {
  try {
    const response = await request.delete(`/api/data-inspection/scheduled-tasks/${id}`)
    return {
      data: response.data.success || true,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('删除定时任务失败，使用静态响应:', error)
    return {
      data: true,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 启用/禁用定时任务
 */
export const toggleScheduledTask = async (id: string, enabled: boolean): Promise<ApiResponseWithSource<boolean>> => {
  try {
    const response = await request.put(`/api/data-inspection/scheduled-tasks/${id}/toggle`, { enabled })
    return {
      data: response.data.success || true,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('切换任务状态失败，使用静态响应:', error)
    return {
      data: true,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 立即执行定时任务
 */
export const executeScheduledTask = async (id: string): Promise<ApiResponseWithSource<{
  taskId: string
  executeTime: string
  status: string
  message: string
}>> => {
  try {
    const response = await request.post(`/api/data-inspection/scheduled-tasks/${id}/execute`)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('执行定时任务失败，使用静态响应:', error)
    
    // 模拟执行结果
    const mockResult = {
      taskId: id,
      executeTime: new Date().toISOString(),
      status: 'success',
      message: '任务执行成功'
    }
    
    return {
      data: mockResult,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 批量执行所有启用的定时任务
 */
export const executeAllScheduledTasks = async (): Promise<ApiResponseWithSource<{
  batchId: string
  executeTime: string
  totalTasks: number
  successCount: number
  failedCount: number
  summary: string
}>> => {
  try {
    const response = await request.post('/api/data-inspection/scheduled-tasks/batch-execute')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('批量执行任务失败，使用静态响应:', error)
    
    // 模拟批量执行结果
    const mockResult = {
      batchId: `batch_${Date.now()}`,
      executeTime: new Date().toISOString(),
      totalTasks: 2,
      successCount: 2,
      failedCount: 0,
      summary: '批量执行完成，共执行2个任务，全部成功'
    }
    
    return {
      data: mockResult,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取任务执行日志
 */
export const fetchTaskExecutionLogs = async (taskId: string): Promise<ApiResponseWithSource<ExecutionLog[]>> => {
  try {
    const response = await request.get(`/api/data-inspection/scheduled-tasks/${taskId}/logs`)
    return {
      data: response.data.data || response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取执行日志失败，使用静态数据:', error)
    
    // 过滤特定任务的日志
    const filteredLogs = FALLBACK_EXECUTION_LOGS.filter(log => log.taskId === taskId)
    
    return {
      data: filteredLogs,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取定时任务统计信息
 */
export const fetchScheduledTasksStatistics = async (): Promise<ApiResponseWithSource<{
  totalTasks: number
  enabledTasks: number
  todayExecutions: number
  totalExceptions: number
}>> => {
  try {
    const response = await request.get('/api/data-inspection/scheduled-tasks/statistics')
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取统计信息失败，使用静态数据:', error)
    
    // 基于静态数据计算统计信息
    const stats = {
      totalTasks: FALLBACK_SCHEDULED_TASKS.length,
      enabledTasks: FALLBACK_SCHEDULED_TASKS.filter(task => task.isEnabled).length,
      todayExecutions: 3, // 模拟今日执行次数
      totalExceptions: 5 // 模拟总异常数
    }
    
    return {
      data: stats,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

// ================================
// 兼容性导出
// ================================

export const scheduledCheckApi = {
  fetchTasks: fetchScheduledTasks,
  createTask: createScheduledTask,
  updateTask: updateScheduledTask,
  deleteTask: deleteScheduledTask,
  toggleTask: toggleScheduledTask,
  executeTask: executeScheduledTask,
  executeAllTasks: executeAllScheduledTasks,
  fetchLogs: fetchTaskExecutionLogs,
  fetchStatistics: fetchScheduledTasksStatistics
}

export default scheduledCheckApi