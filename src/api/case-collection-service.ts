import type {
  CaseCollectionActivity,
  CaseSubmission,
  CaseReview,
  CaseCategory,
  CaseCollectionActivityQueryParams,
  CaseSubmissionQueryParams,
  CaseReviewQueryParams,
  CaseCategoryQueryParams,
  CaseCollectionStatistics,
  ActivityProgress,
  PageResult,
  ApiResponse
} from '@/types/case-collection';

// 真实API接口
import * as realApi from './case-collection';

// 模拟API接口
import * as mockApi from './case-collection-mock';

// 环境配置
const isDevelopment = import.meta.env.DEV;
const useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true' || isDevelopment;

/**
 * API服务配置类
 * 根据环境变量自动切换真实API和模拟API
 */
class CaseCollectionService {
  
  // ==================== 案例征集活动管理 ====================

  /**
   * 创建案例征集活动
   */
  async createActivity(data: Partial<CaseCollectionActivity>): Promise<ApiResponse<CaseCollectionActivity>> {
    if (useMockData) {
      return mockApi.mockCreateActivity(data);
    }
    const response = await realApi.createActivity(data);
    return response.data;
  }

  /**
   * 更新案例征集活动
   */
  async updateActivity(id: number, data: Partial<CaseCollectionActivity>): Promise<ApiResponse<CaseCollectionActivity>> {
    if (useMockData) {
      return mockApi.mockUpdateActivity(id, data);
    }
    const response = await realApi.updateActivity(id, data);
    return response.data;
  }

  /**
   * 删除案例征集活动
   */
  async deleteActivity(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockDeleteActivity(id);
    }
    const response = await realApi.deleteActivity(id);
    return response.data;
  }

  /**
   * 根据ID查询活动详情
   */
  async getActivityDetail(id: number): Promise<ApiResponse<CaseCollectionActivity>> {
    if (useMockData) {
      return mockApi.mockGetActivityDetail(id);
    }
    const response = await realApi.getActivityDetail(id);
    return response.data;
  }

  /**
   * 分页查询活动列表
   */
  async getActivityList(params?: CaseCollectionActivityQueryParams): Promise<ApiResponse<PageResult<CaseCollectionActivity>>> {
    if (useMockData) {
      return mockApi.mockGetActivityList(params);
    }
    const response = await realApi.getActivityList(params);
    return response.data;
  }

  /**
   * 获取活动进度信息
   */
  async getActivityProgress(id: number): Promise<ApiResponse<ActivityProgress>> {
    if (useMockData) {
      // 模拟进度数据
      const mockProgress: ActivityProgress = {
        activityId: id,
        activityTitle: '模拟活动',
        totalSubmissions: 35,
        pendingReviews: 8,
        approvedSubmissions: 22,
        rejectedSubmissions: 5,
        completionRate: 75,
        averageScore: 85.6,
        remainingDays: 15,
        isActive: true
      };
      return {
        code: 0,
        message: 'success',
        data: mockProgress,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.getActivityProgress(id);
    return response.data;
  }

  /**
   * 发布活动
   */
  async publishActivity(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockUpdateActivity(id, { status: 'published', publishTime: new Date().toISOString().slice(0, 19).replace('T', ' ') })
        .then(() => ({ code: 0, message: 'success', data: true, success: true, timestamp: new Date().toISOString() }));
    }
    const response = await realApi.publishActivity(id);
    return response.data;
  }

  /**
   * 取消活动
   */
  async cancelActivity(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockUpdateActivity(id, { status: 'cancelled' })
        .then(() => ({ code: 0, message: 'success', data: true, success: true, timestamp: new Date().toISOString() }));
    }
    const response = await realApi.cancelActivity(id);
    return response.data;
  }

  // ==================== 案例提交管理 ====================

  /**
   * 提交案例
   */
  async submitCase(data: Partial<CaseSubmission>): Promise<ApiResponse<CaseSubmission>> {
    if (useMockData) {
      return mockApi.mockSubmitCase(data);
    }
    const response = await realApi.submitCase(data);
    return response.data;
  }

  /**
   * 更新案例提交
   */
  async updateSubmission(id: number, data: Partial<CaseSubmission>): Promise<ApiResponse<CaseSubmission>> {
    if (useMockData) {
      return mockApi.mockUpdateSubmission(id, data);
    }
    const response = await realApi.updateSubmission(id, data);
    return response.data;
  }

  /**
   * 删除案例提交
   */
  async deleteSubmission(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockDeleteActivity(id); // 复用删除逻辑
    }
    const response = await realApi.deleteSubmission(id);
    return response.data;
  }

  /**
   * 根据ID查询案例详情
   */
  async getSubmissionDetail(id: number): Promise<ApiResponse<CaseSubmission>> {
    if (useMockData) {
      const submission = (await mockApi.mockGetSubmissionList()).data.list.find(s => s.id === id);
      return {
        code: submission ? 0 : 404,
        message: submission ? 'success' : '案例不存在',
        data: submission!,
        success: !!submission,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.getSubmissionDetail(id);
    return response.data;
  }

  /**
   * 分页查询案例提交列表
   */
  async getSubmissionList(params?: CaseSubmissionQueryParams): Promise<ApiResponse<PageResult<CaseSubmission>>> {
    if (useMockData) {
      return mockApi.mockGetSubmissionList(params);
    }
    const response = await realApi.getSubmissionList(params);
    return response.data;
  }

  /**
   * 撤回案例提交
   */
  async withdrawSubmission(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockUpdateSubmission(id, { status: 'withdrawn' })
        .then(() => ({ code: 0, message: 'success', data: true, success: true, timestamp: new Date().toISOString() }));
    }
    const response = await realApi.withdrawSubmission(id);
    return response.data;
  }

  /**
   * 重新提交案例
   */
  async resubmitCase(id: number, data: Partial<CaseSubmission>): Promise<ApiResponse<CaseSubmission>> {
    if (useMockData) {
      return mockApi.mockUpdateSubmission(id, { ...data, status: 'submitted', submitTime: new Date().toISOString().slice(0, 19).replace('T', ' ') });
    }
    const response = await realApi.resubmitCase(id, data);
    return response.data;
  }

  // ==================== 案例预审管理 ====================

  /**
   * 创建案例预审
   */
  async createReview(data: Partial<CaseReview>): Promise<ApiResponse<CaseReview>> {
    if (useMockData) {
      return mockApi.mockCreateReview(data);
    }
    const response = await realApi.createReview(data);
    return response.data;
  }

  /**
   * 更新案例预审
   */
  async updateReview(id: number, data: Partial<CaseReview>): Promise<ApiResponse<CaseReview>> {
    if (useMockData) {
      // 模拟更新审核
      return {
        code: 0,
        message: 'success',
        data: { id, ...data } as CaseReview,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.updateReview(id, data);
    return response.data;
  }

  /**
   * 分页查询预审列表
   */
  async getReviewList(params?: CaseReviewQueryParams): Promise<ApiResponse<PageResult<CaseReview>>> {
    if (useMockData) {
      // 使用模拟数据
      const { mockReviews, wrapApiResponse, wrapPageResult } = await import('@/mock/case-collection');
      const page = params?.page || 1;
      const pageSize = params?.pageSize || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedList = mockReviews.slice(startIndex, endIndex);
      const pageResult = wrapPageResult(paginatedList, mockReviews.length, page, pageSize);
      return wrapApiResponse(pageResult);
    }
    const response = await realApi.getReviewList(params);
    return response.data;
  }

  /**
   * 根据提交ID查询预审记录
   */
  async getReviewsBySubmissionId(submissionId: number): Promise<ApiResponse<CaseReview[]>> {
    if (useMockData) {
      const { mockReviews, wrapApiResponse } = await import('@/mock/case-collection');
      const reviews = mockReviews.filter(r => r.submissionId === submissionId);
      return wrapApiResponse(reviews);
    }
    const response = await realApi.getReviewsBySubmissionId(submissionId);
    return response.data;
  }

  /**
   * 批量审核案例
   */
  async batchReviewCases(data: {
    submissionIds: number[];
    reviewResult: number;
    score: number;
    comments: string;
    suggestions?: string;
  }): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      // 模拟批量审核
      for (const submissionId of data.submissionIds) {
        await mockApi.mockCreateReview({
          submissionId,
          reviewResult: data.reviewResult,
          score: data.score,
          comments: data.comments,
          suggestions: data.suggestions
        });
      }
      return {
        code: 0,
        message: 'success',
        data: true,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.batchReviewCases(data);
    return response.data;
  }

  /**
   * 获取审核历史记录列表
   */
  async getReviewHistory(params?: {
    page?: number;
    pageSize?: number;
    activityId?: number;
    reviewResult?: number;
    reviewerName?: string;
    dateRange?: [string, string];
    keyword?: string;
  }): Promise<ApiResponse<PageResult<any>>> {
    if (useMockData) {
      return mockApi.mockGetReviewHistory(params);
    }
    const response = await realApi.getReviewHistory(params);
    return response.data;
  }

  /**
   * 获取审核统计数据
   */
  async getReviewStatistics(params?: {
    activityId?: number;
    dateRange?: [string, string];
  }): Promise<ApiResponse<any>> {
    if (useMockData) {
      return mockApi.mockGetReviewStatistics(params);
    }
    const response = await realApi.getReviewStatistics(params);
    return response.data;
  }

  /**
   * 获取当前审核设置
   */
  async getReviewSettings(): Promise<ApiResponse<any>> {
    if (useMockData) {
      return mockApi.mockGetReviewSettings();
    }
    const response = await realApi.getReviewSettings();
    return response.data;
  }

  /**
   * 更新审核设置
   */
  async updateReviewSettings(data: any): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockUpdateReviewSettings(data);
    }
    const response = await realApi.updateReviewSettings(data);
    return response.data;
  }

  /**
   * 获取审核模板列表
   */
  async getReviewTemplates(): Promise<ApiResponse<any[]>> {
    if (useMockData) {
      return mockApi.mockGetReviewTemplates();
    }
    const response = await realApi.getReviewTemplates();
    return response.data;
  }

  /**
   * 保存审核模板
   */
  async saveReviewTemplate(data: {
    id?: number;
    name: string;
    type: string;
    reviewResult: number;
    score: number;
    comments: string;
    suggestions?: string;
  }): Promise<ApiResponse<any>> {
    if (useMockData) {
      return mockApi.mockSaveReviewTemplate(data);
    }
    const response = await realApi.saveReviewTemplate(data);
    return response.data;
  }

  /**
   * 删除审核模板
   */
  async deleteReviewTemplate(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return mockApi.mockDeleteReviewTemplate(id);
    }
    const response = await realApi.deleteReviewTemplate(id);
    return response.data;
  }

  // ==================== 案例分类管理 ====================

  /**
   * 获取所有分类（树形结构）
   */
  async getAllCategories(): Promise<ApiResponse<CaseCategory[]>> {
    if (useMockData) {
      return mockApi.mockGetAllCategories();
    }
    const response = await realApi.getAllCategories();
    return response.data;
  }

  /**
   * 创建案例分类
   */
  async createCategory(data: Partial<CaseCategory>): Promise<ApiResponse<CaseCategory>> {
    if (useMockData) {
      return mockApi.mockCreateCategory(data);
    }
    const response = await realApi.createCategory(data);
    return response.data;
  }

  /**
   * 更新案例分类
   */
  async updateCategory(id: number, data: Partial<CaseCategory>): Promise<ApiResponse<CaseCategory>> {
    if (useMockData) {
      return {
        code: 0,
        message: 'success',
        data: { id, ...data } as CaseCategory,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.updateCategory(id, data);
    return response.data;
  }

  /**
   * 删除案例分类
   */
  async deleteCategory(id: number): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return {
        code: 0,
        message: 'success',
        data: true,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.deleteCategory(id);
    return response.data;
  }

  // ==================== 统计查询 ====================

  /**
   * 获取活动统计数据
   */
  async getActivityStatistics(organizerId?: number): Promise<ApiResponse<CaseCollectionStatistics>> {
    if (useMockData) {
      return mockApi.mockGetActivityStatistics();
    }
    const response = await realApi.getActivityStatistics(organizerId);
    return response.data;
  }

  /**
   * 获取案例提交统计数据
   */
  async getSubmissionStatistics(activityId: number): Promise<ApiResponse<any>> {
    if (useMockData) {
      return mockApi.mockGetSubmissionStatistics(activityId);
    }
    const response = await realApi.getSubmissionStatistics(activityId);
    return response.data;
  }

  // ==================== 文件管理 ====================

  /**
   * 上传文件
   */
  async uploadFile(file: File, type: 'image' | 'document' | 'video' = 'document'): Promise<ApiResponse<{ url: string; name: string; size: number }>> {
    if (useMockData) {
      return mockApi.mockUploadFile(file);
    }
    const response = await realApi.uploadFile(file, type);
    return response.data;
  }

  /**
   * 删除文件
   */
  async deleteFile(url: string): Promise<ApiResponse<boolean>> {
    if (useMockData) {
      return {
        code: 0,
        message: 'success',
        data: true,
        success: true,
        timestamp: new Date().toISOString()
      };
    }
    const response = await realApi.deleteFile(url);
    return response.data;
  }

  /**
   * 下载文件
   */
  async downloadFile(url: string, filename?: string): Promise<Blob> {
    if (useMockData) {
      // 模拟文件下载
      const blob = new Blob(['模拟文件内容'], { type: 'application/octet-stream' });
      return blob;
    }
    const response = await realApi.downloadFile(url, filename);
    return response.data;
  }

  // ==================== 导出功能 ====================

  /**
   * 导出活动列表
   */
  async exportActivityList(params?: CaseCollectionActivityQueryParams): Promise<Blob> {
    if (useMockData) {
      // 模拟导出Excel
      const blob = new Blob(['活动列表导出数据'], { type: 'application/vnd.ms-excel' });
      return blob;
    }
    const response = await realApi.exportActivityList(params);
    return response.data;
  }

  /**
   * 导出案例提交列表
   */
  async exportSubmissionList(params?: CaseSubmissionQueryParams): Promise<Blob> {
    if (useMockData) {
      // 模拟导出Excel
      const blob = new Blob(['案例提交列表导出数据'], { type: 'application/vnd.ms-excel' });
      return blob;
    }
    const response = await realApi.exportSubmissionList(params);
    return response.data;
  }

  /**
   * 导出审核报告
   */
  async exportReviewReport(activityId: number): Promise<Blob> {
    if (useMockData) {
      // 模拟导出PDF
      const blob = new Blob(['审核报告导出数据'], { type: 'application/pdf' });
      return blob;
    }
    const response = await realApi.exportReviewReport(activityId);
    return response.data;
  }

  /**
   * 导出审核历史数据
   */
  async exportReviewHistory(params?: {
    activityId?: number;
    reviewResult?: number;
    reviewerName?: string;
    dateRange?: [string, string];
    keyword?: string;
  }): Promise<Blob> {
    if (useMockData) {
      return mockApi.mockExportReviewHistory(params);
    }
    const response = await realApi.exportReviewHistory(params);
    return response.data;
  }
}

// 导出单例实例
export const caseCollectionService = new CaseCollectionService();

// 导出类型和接口
export type { CaseCollectionService };

// 默认导出
export default caseCollectionService;
