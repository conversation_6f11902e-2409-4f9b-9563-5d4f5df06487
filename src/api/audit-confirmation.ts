// 审核确认系统API接口 - 严格按照需求文档实现
import request from '@/utils/request'
import type {
  CultivationObject,
  ProjectInfo,
  AuditRecord,
  AuditProcessNode,
  NotificationTemplate,
  AppealRecord,
  AuditSearchParams,
  AuditStatistics,
  AuditStatus,
  AuditResult
} from '@/types/audit-confirmation'

// 模拟API延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟数据
// ================================
// Mock数据定义
// ================================

const mockProjects: ProjectInfo[] = [
  {
    id: 1,
    name: '2025年度优秀党支部培育项目',
    description: '培育和选树一批优秀党支部典型',
    startTime: '2025-06-01',
    endTime: '2025-06-25',
    status: 1,
    cultivationObjects: [],
    totalCount: 3,
    auditedCount: 2,
    passedCount: 1,
    rejectedCount: 1
  },
  {
    id: 2,
    name: '党员先锋模范培育计划',
    description: '选树党员先锋模范，发挥示范引领作用',
    startTime: '2025-03-01',
    endTime: '2025-05-30',
    status: 1,
    cultivationObjects: [],
    totalCount: 2,
    auditedCount: 1,
    passedCount: 1,
    rejectedCount: 0
  }
]

// Mock培育对象数据
const mockCultivationObjects: CultivationObject[] = [
  {
    id: 1,
    name: '市委办公室党支部',
    projectId: 1,
    projectName: '2025年度优秀党支部培育项目',
    organizationName: '市委办公室',
    contactPerson: '周海军',
    contactPhone: '13800138001',
    status: 1,
    score: 85,
    submitTime: '2025-06-15 10:30:00',
    indicators: [
      {
        id: 1,
        indicatorName: '组织建设',
        indicatorCode: 'ORG_BUILD',
        weight: 30,
        score: 26,
        maxScore: 30,
        description: '党支部组织建设情况',
        details: '支部班子健全，制度完善'
      },
      {
        id: 2,
        indicatorName: '活动开展',
        indicatorCode: 'ACTIVITY',
        weight: 25,
        score: 22,
        maxScore: 25,
        description: '党支部活动开展情况',
        details: '定期开展主题党日活动'
      },
      {
        id: 3,
        indicatorName: '制度建设',
        indicatorCode: 'SYSTEM',
        weight: 20,
        score: 18,
        maxScore: 20,
        description: '党支部制度建设情况',
        details: '制度体系完善，执行到位'
      },
      {
        id: 4,
        indicatorName: '作用发挥',
        indicatorCode: 'EFFECT',
        weight: 25,
        score: 19,
        maxScore: 25,
        description: '党支部作用发挥情况',
        details: '战斗堡垒作用明显'
      }
    ]
  },
  {
    id: 2,
    name: '教育局机关党支部',
    projectId: 1,
    projectName: '2025年度优秀党支部培育项目',
    organizationName: '市教育局',
    contactPerson: '王东',
    contactPhone: '13800138002',
    status: 2,
    score: 92,
    submitTime: '2025-05-20 14:20:00',
    auditTime: '2025-06-01 09:15:00',
    auditor: '王审核',
    auditComments: '各项指标表现优秀，建议通过',
    indicators: [
      {
        id: 5,
        indicatorName: '组织建设',
        indicatorCode: 'ORG_BUILD',
        weight: 30,
        score: 28,
        maxScore: 30,
        description: '党支部组织建设情况',
        details: '组织架构完善，运行规范'
      },
      {
        id: 6,
        indicatorName: '活动开展',
        indicatorCode: 'ACTIVITY',
        weight: 25,
        score: 24,
        maxScore: 25,
        description: '党支部活动开展情况',
        details: '活动形式多样，效果显著'
      },
      {
        id: 7,
        indicatorName: '制度建设',
        indicatorCode: 'SYSTEM',
        weight: 20,
        score: 19,
        maxScore: 20,
        description: '党支部制度建设情况',
        details: '制度健全，落实有力'
      },
      {
        id: 8,
        indicatorName: '作用发挥',
        indicatorCode: 'EFFECT',
        weight: 25,
        score: 21,
        maxScore: 25,
        description: '党支部作用发挥情况',
        details: '引领作用突出'
      }
    ]
  },
  {
    id: 3,
    name: '卫健委党支部',
    projectId: 1,
    projectName: '2025年度优秀党支部培育项目',
    organizationName: '市卫健委',
    contactPerson: '杜佳佳',
    contactPhone: '13800138003',
    status: 3,
    score: 68,
    submitTime: '2025-05-25 16:45:00',
    auditTime: '2025-06-05 14:30:00',
    auditor: '李审核',
    auditComments: '部分指标需要改进，建议退回完善',
    indicators: [
      {
        id: 9,
        indicatorName: '组织建设',
        indicatorCode: 'ORG_BUILD',
        weight: 30,
        score: 20,
        maxScore: 30,
        description: '党支部组织建设情况',
        details: '组织架构基本完善'
      },
      {
        id: 10,
        indicatorName: '活动开展',
        indicatorCode: 'ACTIVITY',
        weight: 25,
        score: 18,
        maxScore: 25,
        description: '党支部活动开展情况',
        details: '活动开展较少'
      },
      {
        id: 11,
        indicatorName: '制度建设',
        indicatorCode: 'SYSTEM',
        weight: 20,
        score: 15,
        maxScore: 20,
        description: '党支部制度建设情况',
        details: '制度有待完善'
      },
      {
        id: 12,
        indicatorName: '作用发挥',
        indicatorCode: 'EFFECT',
        weight: 25,
        score: 15,
        maxScore: 25,
        description: '党支部作用发挥情况',
        details: '作用发挥不够明显'
      }
    ]
  },
  {
    id: 4,
    name: '财政局党支部',
    projectId: 2,
    projectName: '党员先锋模范培育计划',
    organizationName: '市财政局',
    contactPerson: '孙建',
    contactPhone: '13800138004',
    status: 1,
    score: 78,
    submitTime: '2025-03-10 11:20:00',
    indicators: [
      {
        id: 13,
        indicatorName: '先锋模范',
        indicatorCode: 'MODEL',
        weight: 40,
        score: 32,
        maxScore: 40,
        description: '党员先锋模范作用',
        details: '党员模范作用较好'
      },
      {
        id: 14,
        indicatorName: '服务群众',
        indicatorCode: 'SERVICE',
        weight: 30,
        score: 24,
        maxScore: 30,
        description: '服务群众情况',
        details: '服务意识较强'
      },
      {
        id: 15,
        indicatorName: '创新实践',
        indicatorCode: 'INNOVATION',
        weight: 30,
        score: 22,
        maxScore: 30,
        description: '创新实践能力',
        details: '创新意识有待提升'
      }
    ]
  },
  {
    id: 5,
    name: '人社局党支部',
    projectId: 2,
    projectName: '党员先锋模范培育计划',
    organizationName: '市人社局',
    contactPerson: '黄鑫',
    contactPhone: '13800138005',
    status: 2,
    score: 95,
    submitTime: '2025-03-15 09:30:00',
    auditTime: '2025-03-25 15:20:00',
    auditor: '张审核',
    auditComments: '表现突出，各项指标优秀',
    indicators: [
      {
        id: 16,
        indicatorName: '先锋模范',
        indicatorCode: 'MODEL',
        weight: 40,
        score: 38,
        maxScore: 40,
        description: '党员先锋模范作用',
        details: '党员模范作用突出'
      },
      {
        id: 17,
        indicatorName: '服务群众',
        indicatorCode: 'SERVICE',
        weight: 30,
        score: 29,
        maxScore: 30,
        description: '服务群众情况',
        details: '服务质量优秀'
      },
      {
        id: 18,
        indicatorName: '创新实践',
        indicatorCode: 'INNOVATION',
        weight: 30,
        score: 28,
        maxScore: 30,
        description: '创新实践能力',
        details: '创新成果显著'
      }
    ]
  }
]

// Mock审核记录数据
const mockAuditRecords: AuditRecord[] = [
  {
    id: 1,
    cultivationObjectId: 2,
    cultivationObjectName: '教育局机关党支部',
    projectName: '2025年度优秀党支部培育项目',
    auditType: 1,
    auditor: '王审核',
    auditTime: '2025-02-01 09:15:00',
    auditResult: 1,
    originalScore: 90,
    finalScore: 92,
    comments: '各项指标表现优秀，建议通过',
    suggestions: '继续保持良好态势'
  },
  {
    id: 2,
    cultivationObjectId: 3,
    cultivationObjectName: '卫健委党支部',
    projectName: '2025年度优秀党支部培育项目',
    auditType: 1,
    auditor: '李审核',
    auditTime: '2025-02-05 14:30:00',
    auditResult: 2,
    originalScore: 72,
    finalScore: 68,
    comments: '部分指标需要改进，建议退回完善',
    suggestions: '加强组织建设和活动开展，完善制度体系'
  },
  {
    id: 3,
    cultivationObjectId: 5,
    cultivationObjectName: '人社局党支部',
    projectName: '党员先锋模范培育计划',
    auditType: 1,
    auditor: '张审核',
    auditTime: '2025-03-25 15:20:00',
    auditResult: 1,
    originalScore: 93,
    finalScore: 95,
    comments: '表现突出，各项指标优秀',
    suggestions: '继续发挥先锋模范作用'
  },
  {
    id: 4,
    cultivationObjectId: 2,
    cultivationObjectName: '教育局机关党支部',
    projectName: '2025年度优秀党支部培育项目',
    auditType: 2,
    auditor: '赵审核',
    auditTime: '2025-02-10 10:45:00',
    auditResult: 1,
    originalScore: 92,
    finalScore: 94,
    comments: '复审通过，表现持续优秀',
    suggestions: '可作为典型案例推广'
  },
  {
    id: 5,
    cultivationObjectId: 1,
    cultivationObjectName: '市委办公室党支部',
    projectName: '2025年度优秀党支部培育项目',
    auditType: 1,
    auditor: '王审核',
    auditTime: '2025-06-28 16:00:00',
    auditResult: 3,
    originalScore: 85,
    finalScore: 85,
    comments: '整体表现良好，但需补充部分材料',
    suggestions: '请补充活动开展的详细记录和效果评估'
  }
]

// ================================
// 一、审核流程配置API - 严格按照需求文档实现
// ================================

// 获取用户审核权限
export const getUserAuditPermissions = (): Promise<{
  canViewAudit: boolean          // 查看审核权限
  canAuditScore: boolean         // 评分审核权限  
  canModifyScore: boolean        // 修改评分权限
  canConfigProcess: boolean      // 配置流程权限
  canHandleAppeal: boolean       // 处理申诉权限
  canViewAllProjects: boolean    // 查看所有项目权限
  allowedProjects: number[]      // 允许访问的项目ID列表
}> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/permissions')
  return Promise.resolve({
    canViewAudit: true,
    canAuditScore: true, 
    canModifyScore: true,
    canConfigProcess: true,
    canHandleAppeal: true,
    canViewAllProjects: true,
    allowedProjects: [1, 2]
  })
}

// 配置审核流程节点
export const configAuditProcessNodes = (nodes: Array<{
  nodeName: string
  nodeOrder: number
  nodeType: number              // 1-审核节点, 2-评分节点, 3-确认节点
  auditors: string[]            // 审核人员列表
  auditStandard: string         // 审核标准
  auditCycle: number           // 审核周期（天）
  isRequired: boolean          // 是否必需
  nextNodeId?: number          // 下一节点ID
}>): Promise<boolean> => {
  // Mock实现 - 实际应调用 request.post('/api/audit-confirmation/process/config', { nodes })
  console.log('配置审核流程节点:', nodes)
  return Promise.resolve(true)
}

// 修改审核流程节点
export const updateAuditProcessNode = (nodeId: number, nodeData: {
  nodeName?: string
  auditors?: string[]
  auditStandard?: string 
  auditCycle?: number
  isRequired?: boolean
  nextNodeId?: number
}): Promise<boolean> => {
  // Mock实现 - 实际应调用 request.put(`/api/audit-confirmation/process/nodes/${nodeId}`, nodeData)
  console.log('修改审核流程节点:', nodeId, nodeData)
  return Promise.resolve(true)
}

// 设置审核标准
export const setAuditStandards = (standards: Array<{
  nodeId: number
  standardContent: string       // 审核标准内容
  scoreThreshold: number       // 评分阈值
  checkpoints: string[]        // 检查要点
  rejectReasons: string[]      // 常见退回原因
}>): Promise<boolean> => {
  console.log('设置审核标准:', standards)
  return Promise.resolve(true)
}

// 设置审核周期
export const setAuditCycles = (cycles: Array<{
  nodeId: number
  cycleInDays: number          // 审核周期天数
  reminderBeforeDays: number   // 提前提醒天数
  escalationAfterDays: number  // 超期升级天数
}>): Promise<boolean> => {
  console.log('设置审核周期:', cycles)
  return Promise.resolve(true)
}

// ================================
// 二、评分结果审核API - 严格按照需求文档实现
// ================================

// 获取项目列表（基于用户权限）
export const getProjectListByPermission = (): Promise<ProjectInfo[]> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/projects')
  return Promise.resolve(mockProjects)
}

// 获取项目的培育对象列表
export const getProjectCultivationObjects = (projectId: number): Promise<CultivationObject[]> => {
  // Mock实现 - 实际应调用 request.get(`/api/audit-confirmation/projects/${projectId}/cultivation-objects`)
  const filteredObjects = mockCultivationObjects.filter(obj => obj.projectId === projectId)
  return Promise.resolve(filteredObjects)
}

// 返回培育对象审核状态
export const getCultivationObjectAuditStatus = (objectId: number): Promise<{
  id: number
  currentStatus: AuditStatus    // 当前审核状态
  currentNode: string          // 当前审核节点
  nextNode?: string           // 下一审核节点
  auditProgress: Array<{      // 审核进度
    nodeName: string
    status: 'pending' | 'processing' | 'completed' | 'rejected'
    auditor?: string
    auditTime?: string
    comments?: string
  }>
  canAudit: boolean           // 当前用户是否可审核
}> => {
  const mockStatus = {
    id: objectId,
    currentStatus: 1 as AuditStatus,
    currentNode: '初审',
    nextNode: '复审',
    auditProgress: [
      {
        nodeName: '初审',
        status: 'processing' as const,
        auditor: '王审核',
        comments: '正在审核中'
      },
      {
        nodeName: '复审', 
        status: 'pending' as const
      }
    ],
    canAudit: true
  }
  return Promise.resolve(mockStatus)
}

// 按审核状态筛选培育对象
export const filterCultivationObjectsByStatus = (status: AuditStatus, projectId?: number): Promise<CultivationObject[]> => {
  let filtered = [...mockCultivationObjects]
  if (projectId) {
    filtered = filtered.filter(obj => obj.projectId === projectId)
  }
  filtered = filtered.filter(obj => obj.status === status)
  return Promise.resolve(filtered)
}

// 按培育对象名称搜索
export const searchCultivationObjectsByName = (name: string, projectId?: number): Promise<CultivationObject[]> => {
  let filtered = [...mockCultivationObjects]
  if (projectId) {
    filtered = filtered.filter(obj => obj.projectId === projectId)
  }
  filtered = filtered.filter(obj => obj.name.includes(name) || obj.organizationName.includes(name))
  return Promise.resolve(filtered)
}

// 获取培育对象评分结果
export const getCultivationObjectScoreResult = (objectId: number): Promise<{
  objectInfo: CultivationObject
  totalScore: number
  maxPossibleScore: number
  scoreBreakdown: Array<{
    categoryName: string
    categoryScore: number
    categoryMaxScore: number
    indicators: Array<{
      indicatorName: string
      indicatorCode: string
      weight: number
      score: number
      maxScore: number
      scoringMethod: string
      dataSource: string
      lastUpdated: string
    }>
  }>
  autoEvaluationComments: string
  riskWarnings: string[]
}> => {
  const objectInfo = mockCultivationObjects.find(obj => obj.id === objectId)
  if (!objectInfo) {
    throw new Error('培育对象不存在')
  }
  
  const mockResult = {
    objectInfo,
    totalScore: objectInfo.score || 0,
    maxPossibleScore: 100,
    scoreBreakdown: [
      {
        categoryName: '组织建设',
        categoryScore: 26,
        categoryMaxScore: 30,
        indicators: objectInfo.indicators.filter(ind => ind.indicatorCode === 'ORG_BUILD')
      },
      {
        categoryName: '活动开展',
        categoryScore: 22, 
        categoryMaxScore: 25,
        indicators: objectInfo.indicators.filter(ind => ind.indicatorCode === 'ACTIVITY')
      }
    ],
    autoEvaluationComments: '系统自动评价：该培育对象在组织建设方面表现良好，建议继续保持',
    riskWarnings: ['部分指标数据更新不及时', '活动开展频次有待提升']
  }
  return Promise.resolve(mockResult)
}

// 查看培育对象指标结果
export const getCultivationObjectIndicatorResults = (objectId: number): Promise<Array<{
  indicatorId: number
  indicatorName: string
  indicatorCode: string
  categoryName: string
  weight: number
  rawScore: number              // 原始评分
  adjustedScore: number         // 调整后评分
  maxScore: number
  scoringReason: string         // 评分依据
  dataSource: string           // 数据来源
  lastCalculated: string       // 最后计算时间
  hasManualAdjustment: boolean // 是否有人工调整
  adjustmentHistory: Array<{   // 调整历史
    adjustTime: string
    adjuster: string
    fromScore: number
    toScore: number
    reason: string
  }>
}>> => {
  const objectInfo = mockCultivationObjects.find(obj => obj.id === objectId)
  if (!objectInfo) {
    return Promise.resolve([])
  }
  
  const mockResults = objectInfo.indicators.map(indicator => ({
    indicatorId: indicator.id,
    indicatorName: indicator.indicatorName,
    indicatorCode: indicator.indicatorCode,
    categoryName: '党建工作',
    weight: indicator.weight,
    rawScore: indicator.score,
    adjustedScore: indicator.score,
    maxScore: indicator.maxScore,
    scoringReason: indicator.details || '根据系统规则自动计算',
    dataSource: '系统数据',
    lastCalculated: '2025-01-18 10:00:00',
    hasManualAdjustment: false,
    adjustmentHistory: []
  }))
  
  return Promise.resolve(mockResults)
}

// 查看培育对象指标详情
export const getCultivationObjectIndicatorDetails = (objectId: number, indicatorId: number): Promise<{
  indicatorInfo: {
    id: number
    name: string
    code: string
    description: string
    calculationMethod: string
    dataSourceConfig: any
    weightInCategory: number
  }
  scoreDetails: {
    rawData: any                 // 原始数据
    calculationSteps: string[]   // 计算步骤
    intermediateResults: any[]   // 中间结果
    finalScore: number          // 最终评分
    scoreExplanation: string    // 评分说明
  }
  dataQuality: {
    completeness: number        // 数据完整度
    timeliness: number         // 数据时效性
    accuracy: number           // 数据准确性
    issues: string[]           // 数据问题
  }
  relatedEvidence: Array<{    // 相关证据材料
    type: string
    name: string
    url: string
    uploadTime: string
  }>
}> => {
  const mockDetails = {
    indicatorInfo: {
      id: indicatorId,
      name: '党组织设置完整性',
      code: 'ORG_COMPLETENESS',
      description: '检查党组织设置是否完整规范',
      calculationMethod: '根据组织架构完整度和人员配置情况计算',
      dataSourceConfig: {
        type: 'database',
        tables: ['party_organization', 'party_members'],
        updateFrequency: 'daily'
      },
      weightInCategory: 30
    },
    scoreDetails: {
      rawData: {
        totalMembers: 25,
        leaderCount: 3,
        committeeCount: 5
      },
      calculationSteps: [
        '1. 统计党员总数：25人',
        '2. 检查领导班子配置：3人',
        '3. 验证委员会设置：5人',
        '4. 计算完整度得分：(3+5)/8*100 = 100%'
      ],
      intermediateResults: [
        { step: '领导班子完整度', value: 100 },
        { step: '委员会完整度', value: 100 }
      ],
      finalScore: 30,
      scoreExplanation: '党组织设置完整，符合标准要求'
    },
    dataQuality: {
      completeness: 95,
      timeliness: 98,
      accuracy: 92,
      issues: ['部分人员联系方式未更新']
    },
    relatedEvidence: [
      {
        type: '组织架构图',
        name: '党支部组织架构2025.pdf',
        url: '/files/evidence/org-structure-2025.pdf',
        uploadTime: '2025-01-15 14:30:00'
      }
    ]
  }
  return Promise.resolve(mockDetails)
}

// ================================
// 三、审核操作与反馈API - 严格按照需求文档实现
// ================================

// 选择评分审核结果
export const selectAuditResult = (objectId: number, result: {
  auditResult: AuditResult      // 1-通过, 2-不通过, 3-待补充
  overallComments: string       // 总体审核意见
  nextAction: string           // 下一步行动
}):
Promise<boolean> => {
  console.log('选择评分审核结果:', objectId, result)
  return Promise.resolve(true)
}

// 填写评分审核意见
export const writeAuditComments = (objectId: number, comments: {
  indicatorComments: Array<{   // 指标级意见
    indicatorId: number
    comment: string
    suggestion: string
  }>
  overallComment: string       // 总体意见
  improvementSuggestions: string[] // 改进建议
  commendations: string[]      // 表扬内容
  concerns: string[]           // 关注点
}): Promise<boolean> => {
  console.log('填写评分审核意见:', objectId, comments)
  return Promise.resolve(true)
}

// 修改评分结果
export const modifyScoreResult = (objectId: number, modifications: Array<{
  indicatorId: number
  originalScore: number
  newScore: number
  modificationReason: string    // 修改原因
  evidenceUrls?: string[]      // 支撑证据
}>): Promise<boolean> => {
  console.log('修改评分结果:', objectId, modifications)
  return Promise.resolve(true)
}

// 修改指标结果
export const modifyIndicatorResult = (objectId: number, indicatorId: number, modification: {
  newScore: number
  modificationReason: string
  newWeighting?: number        // 新权重（如果需要）
  dataSourceAdjustment?: any   // 数据源调整
  calculationAdjustment?: any  // 计算方式调整
  evidenceAttachments?: string[] // 证据附件
}): Promise<boolean> => {
  console.log('修改指标结果:', objectId, indicatorId, modification)
  return Promise.resolve(true)
}

// 记录评分结果历史数据
export const recordScoreHistory = (objectId: number, changes: Array<{
  changeType: 'score_update' | 'indicator_update' | 'weight_update'
  changedField: string
  originalValue: any
  newValue: any
  changeReason: string
  operator: string
  changeTime: string
  approvalRequired: boolean
  approver?: string
  approvalTime?: string
}>): Promise<boolean> => {
  console.log('记录评分结果历史数据:', objectId, changes)
  return Promise.resolve(true)
}

// 记录指标结果历史数据
export const recordIndicatorHistory = (objectId: number, indicatorId: number, changes: Array<{
  fieldName: string
  originalValue: any
  newValue: any
  changeReason: string
  operator: string
  changeTime: string
  dataVersion: string          // 数据版本
  calculationVersion: string   // 计算版本
}>): Promise<boolean> => {
  console.log('记录指标结果历史数据:', objectId, indicatorId, changes)
  return Promise.resolve(true)
}

// 展示评分结果历史数据
export const getScoreHistoryData = (objectId: number): Promise<Array<{
  changeId: number
  changeTime: string
  operator: string
  changeType: string
  changes: Array<{
    field: string
    from: any
    to: any
    reason: string
  }>
  totalScoreChange: {
    from: number
    to: number
  }
  auditTrail: {
    reviewer?: string
    reviewTime?: string
    reviewStatus: 'pending' | 'approved' | 'rejected'
    reviewComments?: string
  }
}>> => {
  const mockHistory = [
    {
      changeId: 1,
      changeTime: '2025-01-18 14:30:00',
      operator: '张审核',
      changeType: '评分调整',
      changes: [
        {
          field: '组织建设评分',
          from: 25,
          to: 28,
          reason: '重新核实组织架构，发现遗漏加分项'
        }
      ],
      totalScoreChange: {
        from: 85,
        to: 88
      },
      auditTrail: {
        reviewer: '王主任',
        reviewTime: '2025-01-18 15:00:00',
        reviewStatus: 'approved',
        reviewComments: '调整合理，同意修改'
      }
    }
  ]
  return Promise.resolve(mockHistory)
}

// 展示指标结果历史数据
export const getIndicatorHistoryData = (objectId: number, indicatorId: number): Promise<Array<{
  versionId: number
  changeTime: string
  operator: string
  versionType: 'data_update' | 'manual_adjustment' | 'rule_change'
  changes: {
    score: { from: number; to: number }
    weight: { from: number; to: number }
    dataSource: { from: string; to: string }
    calculation: { from: string; to: string }
  }
  changeReasons: string[]
  evidence: string[]
  reviewStatus: 'auto_approved' | 'pending_review' | 'approved' | 'rejected'
}>> => {
  const mockIndicatorHistory = [
    {
      versionId: 1,
      changeTime: '2025-01-18 14:30:00',
      operator: '张审核',
      versionType: 'manual_adjustment' as const,
      changes: {
        score: { from: 25, to: 28 },
        weight: { from: 30, to: 30 },
        dataSource: { from: '自动计算', to: '人工核实' },
        calculation: { from: 'AUTO_CALC', to: 'MANUAL_VERIFY' }
      },
      changeReasons: ['发现数据源遗漏', '需要人工核实'],
      evidence: ['核实报告.pdf', '补充材料.docx'],
      reviewStatus: 'approved'
    }
  ]
  return Promise.resolve(mockIndicatorHistory)
}

// 设置审核意见通知模板
export const setAuditNotificationTemplate = (template: {
  templateId?: number
  templateName: string
  templateType: 'audit_pass' | 'audit_reject' | 'audit_pending' | 'score_change'
  subject: string
  content: string
  variables: string[]           // 可用变量列表
  recipientRules: {
    includeApplicant: boolean   // 包含申报人
    includeAuditor: boolean     // 包含审核人
    includeManager: boolean     // 包含管理员
    customRecipients: string[]  // 自定义收件人
  }
  sendConditions: {
    onStatusChange: boolean
    onScoreChange: boolean
    onCommentUpdate: boolean
  }
  isEnabled: boolean
}): Promise<{ templateId: number }> => {
  console.log('设置审核意见通知模板:', template)
  return Promise.resolve({ templateId: Date.now() })
}

// 提交审核结果
export const submitAuditResult = (submission: {
  objectId: number
  auditResult: AuditResult
  auditComments: string
  scoreModifications?: Array<{
    indicatorId: number
    newScore: number
    reason: string
  }>
  nextNodeAction?: {
    assignToNextNode: boolean
    nextAuditor?: string
    estimatedCompletionTime?: string
  }
  notificationSettings: {
    notifyApplicant: boolean
    notifyManager: boolean
    notifyNextAuditor: boolean
    customMessage?: string
  }
}): Promise<{
  success: boolean
  submissionId: number
  nextSteps: string[]
  notifications: Array<{
    recipient: string
    type: string
    status: 'sent' | 'pending' | 'failed'
  }>
}> => {
  console.log('提交审核结果:', submission)
  const mockResponse = {
    success: true,
    submissionId: Date.now(),
    nextSteps: [
      '等待上级审核确认',
      '系统将自动推送通知给相关人员',
      '如有问题可通过申诉流程反馈'
    ],
    notifications: [
      {
        recipient: '申报人',
        type: 'audit_result',
        status: 'sent' as const
      },
      {
        recipient: '项目管理员',
        type: 'audit_complete',
        status: 'sent' as const
      }
    ]
  }
  return Promise.resolve(mockResponse)
}

// 设置审核意见通知人员
export const setAuditNotificationRecipients = (config: {
  projectId?: number
  notificationType: 'audit_result' | 'score_change' | 'appeal' | 'system'
  recipients: Array<{
    recipientType: 'role' | 'user' | 'department'
    recipientId: string
    recipientName: string
    contactMethod: 'email' | 'sms' | 'system' | 'all'
    priority: 'high' | 'normal' | 'low'
  }>
  conditions: {
    triggerEvents: string[]      // 触发事件
    filterRules?: any           // 过滤规则
  }
}): Promise<boolean> => {
  console.log('设置审核意见通知人员:', config)
  return Promise.resolve(true)
}

// 推送审核结果给相关人员
export const pushAuditResultToRecipients = (pushData: {
  objectId: number
  auditResult: AuditResult
  recipients: string[]
  messageTemplate: {
    title: string
    content: string
    attachments?: string[]
  }
  sendOptions: {
    immediate: boolean
    sendMethod: 'email' | 'sms' | 'system' | 'all'
    retryOnFailure: boolean
  }
}): Promise<{
  totalSent: number
  successCount: number
  failedCount: number
  details: Array<{
    recipient: string
    status: 'success' | 'failed'
    errorMessage?: string
    sentTime?: string
  }>
}> => {
  console.log('推送审核结果给相关人员:', pushData)
  const mockPushResult = {
    totalSent: pushData.recipients.length,
    successCount: pushData.recipients.length - 1,
    failedCount: 1,
    details: pushData.recipients.map((recipient, index) => ({
      recipient,
      status: index === 0 ? 'failed' as const : 'success' as const,
      errorMessage: index === 0 ? '邮件地址无效' : undefined,
      sentTime: index === 0 ? undefined : new Date().toISOString()
    }))
  }
  return Promise.resolve(mockPushResult)
}

// ================================
// 四、动态评分机制API - 严格按照需求文档实现
// ================================

// 获取最新评分规则
export const getLatestScoringRules = (projectId?: number): Promise<{
  ruleVersion: string
  updateTime: string
  rules: Array<{
    ruleId: string
    ruleName: string
    ruleType: 'calculation' | 'weighting' | 'threshold' | 'bonus'
    ruleContent: any
    applicableScopes: string[]   // 适用范围
    effectiveDate: string
    isActive: boolean
  }>
  changes: Array<{              // 与上一版本的变化
    changeType: 'added' | 'modified' | 'removed'
    ruleId: string
    description: string
    impact: string
  }>
}> => {
  const mockRules = {
    ruleVersion: 'v2.1.0',
    updateTime: '2025-01-18 12:00:00',
    rules: [
      {
        ruleId: 'ORG_BUILD_001',
        ruleName: '党组织建设评分规则',
        ruleType: 'calculation' as const,
        ruleContent: {
          baseScore: 60,
          bonusRules: [
            { condition: 'hasSecretary', bonus: 10 },
            { condition: 'regularMeetings', bonus: 15 },
            { condition: 'completeStructure', bonus: 15 }
          ]
        },
        applicableScopes: ['党支部', '党委'],
        effectiveDate: '2025-01-15 00:00:00',
        isActive: true
      }
    ],
    changes: [
      {
        changeType: 'modified' as const,
        ruleId: 'ORG_BUILD_001',
        description: '调整了党组织建设的基础分值从50分提升到60分',
        impact: '所有培育对象的组织建设得分将普遍提升10分'
      }
    ]
  }
  return Promise.resolve(mockRules)
}

// 实时更新评分数据
export const updateScoringDataRealtime = (updateConfig: {
  projectId?: number
  objectIds?: number[]          // 指定更新的培育对象
  ruleVersion: string          // 使用的规则版本
  updateScope: 'all' | 'changed_rules_only' | 'specified_objects'
  forceRecalculation: boolean  // 是否强制重新计算
  preserveManualAdjustments: boolean // 是否保留人工调整
}): Promise<{
  taskId: number
  affectedObjects: number
  estimatedDuration: number     // 预计完成时间（秒）
  updateProgress: {
    phase: 'started' | 'calculating' | 'updating' | 'validating' | 'completed'
    progress: number            // 0-100
    currentObject?: string
    completedObjects: number
    errors: Array<{
      objectId: number
      error: string
      severity: 'warning' | 'error'
    }>
  }
}> => {
  console.log('实时更新评分数据:', updateConfig)
  const mockUpdateResult = {
    taskId: Date.now(),
    affectedObjects: 5,
    estimatedDuration: 300,
    updateProgress: {
      phase: 'started' as const,
      progress: 0,
      completedObjects: 0,
      errors: []
    }
  }
  return Promise.resolve(mockUpdateResult)
}

// 获取更新任务状态
export const getUpdateTaskStatus = (taskId: number): Promise<{
  taskId: number
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  startTime: string
  endTime?: string
  results?: {
    totalProcessed: number
    successCount: number
    failureCount: number
    warningCount: number
    scoreChanges: Array<{
      objectId: number
      objectName: string
      oldScore: number
      newScore: number
      changeDelta: number
      changedIndicators: string[]
    }>
  }
  errors?: Array<{
    objectId: number
    error: string
    resolution?: string
  }>
}> => {
  const mockTaskStatus = {
    taskId,
    status: 'completed' as const,
    progress: 100,
    startTime: '2025-01-18 14:00:00',
    endTime: '2025-01-18 14:05:00',
    results: {
      totalProcessed: 5,
      successCount: 4,
      failureCount: 0,
      warningCount: 1,
      scoreChanges: [
        {
          objectId: 1,
          objectName: '市委办公室党支部',
          oldScore: 85,
          newScore: 88,
          changeDelta: 3,
          changedIndicators: ['组织建设']
        }
      ]
    }
  }
  return Promise.resolve(mockTaskStatus)
}

// ================================
// 五、申诉处理流程API - 严格按照需求文档实现
// ================================

// 获取用户申诉操作权限
export const getUserAppealPermissions = (): Promise<{
  canSubmitAppeal: boolean     // 可以提交申诉
  canViewAppealStatus: boolean // 可以查看申诉状态
  canProcessAppeal: boolean    // 可以处理申诉
  canConfigAppealProcess: boolean // 可以配置申诉流程
  allowedAppealTypes: string[] // 允许的申诉类型
}> => {
  return Promise.resolve({
    canSubmitAppeal: true,
    canViewAppealStatus: true,
    canProcessAppeal: true,
    canConfigAppealProcess: true,
    allowedAppealTypes: ['score_dispute', 'process_violation', 'data_error', 'other']
  })
}

// 培育对象填写申诉申请
export const submitAppealApplication = (appeal: {
  cultivationObjectId: number
  appealType: 'score_dispute' | 'process_violation' | 'data_error' | 'other'
  appealReason: string
  appealContent: string
  disputedItems: Array<{       // 争议项目
    itemType: 'indicator' | 'score' | 'process'
    itemId: string
    itemName: string
    currentValue: any
    expectedValue: any
    reason: string
  }>
  evidence: Array<{            // 证据材料
    evidenceType: 'document' | 'image' | 'video' | 'other'
    fileName: string
    fileUrl: string
    description: string
  }>
  requestedAction: string      // 期望的处理结果
  urgency: 'low' | 'normal' | 'high' | 'urgent'
}): Promise<{
  appealId: number
  submissionTime: string
  expectedProcessTime: string
  appealNumber: string         // 申诉编号
  nextSteps: string[]
}> => {
  console.log('提交申诉申请:', appeal)
  const mockAppealSubmission = {
    appealId: Date.now(),
    submissionTime: new Date().toISOString(),
    expectedProcessTime: '2025-01-25 18:00:00',
    appealNumber: `AP${Date.now().toString().slice(-6)}`,
    nextSteps: [
      '系统已受理您的申诉申请',
      '将在1个工作日内分配处理人员',
      '请保持联系方式畅通',
      '可通过申诉编号查询处理进度'
    ]
  }
  return Promise.resolve(mockAppealSubmission)
}

// 配置申诉处理流程
export const configAppealProcessFlow = (flowConfig: {
  flowName: string
  appealTypes: string[]        // 适用的申诉类型
  processNodes: Array<{
    nodeId: string
    nodeName: string
    nodeType: 'auto_assign' | 'manual_process' | 'review' | 'decision'
    nodeOrder: number
    processorRoles: string[]   // 处理人角色
    processorUsers?: string[]  // 指定处理人
    timeLimit: number          // 处理时限（小时）
    escalationRules: {
      escalateAfterHours: number
      escalateTo: string[]     // 升级对象
    }
    autoRules?: any           // 自动处理规则
    nextNodeConditions: Array<{
      condition: string
      nextNodeId: string
    }>
  }>
  notificationSettings: {
    notifyOnSubmit: boolean
    notifyOnAssign: boolean
    notifyOnDecision: boolean
    notifyOnEscalation: boolean
  }
}): Promise<{ flowId: string }> => {
  console.log('配置申诉处理流程:', flowConfig)
  return Promise.resolve({ flowId: `FLOW_${Date.now()}` })
}

// 获取申诉处理流程
export const getAppealProcessFlow = (appealType?: string): Promise<{
  flows: Array<{
    flowId: string
    flowName: string
    appealTypes: string[]
    isActive: boolean
    nodes: Array<{
      nodeId: string
      nodeName: string
      nodeOrder: number
      avgProcessTime: number   // 平均处理时间
      successRate: number      // 处理成功率
    }>
    statistics: {
      totalProcessed: number
      avgCompletionTime: number
      satisfactionRate: number
    }
  }>
}> => {
  const mockFlows = {
    flows: [
      {
        flowId: 'FLOW_DEFAULT',
        flowName: '标准申诉处理流程',
        appealTypes: ['score_dispute', 'process_violation', 'data_error'],
        isActive: true,
        nodes: [
          {
            nodeId: 'NODE_1',
            nodeName: '初步审查',
            nodeOrder: 1,
            avgProcessTime: 4,
            successRate: 95
          },
          {
            nodeId: 'NODE_2', 
            nodeName: '详细调查',
            nodeOrder: 2,
            avgProcessTime: 24,
            successRate: 88
          },
          {
            nodeId: 'NODE_3',
            nodeName: '决策审批',
            nodeOrder: 3,
            avgProcessTime: 8,
            successRate: 92
          }
        ],
        statistics: {
          totalProcessed: 156,
          avgCompletionTime: 36,
          satisfactionRate: 89
        }
      }
    ]
  }
  return Promise.resolve(mockFlows)
}

// 获取申诉处理人
export const getAppealProcessors = (appealId: number): Promise<{
  currentProcessor: {
    processorId: string
    processorName: string
    processorRole: string
    assignTime: string
    deadline: string
  }
  processingHistory: Array<{
    nodeId: string
    nodeName: string
    processorId: string
    processorName: string
    startTime: string
    endTime?: string
    action: string
    comments?: string
    attachments?: string[]
  }>
  nextProcessors: Array<{     // 后续处理人
    nodeId: string
    possibleProcessors: Array<{
      processorId: string
      processorName: string
      workload: number         // 当前工作量
      avgProcessTime: number   // 平均处理时间
    }>
  }>
}> => {
  const mockProcessors = {
    currentProcessor: {
      processorId: 'USER_001',
      processorName: '张处理员',
      processorRole: '申诉调查员',
      assignTime: '2025-01-18 10:00:00',
      deadline: '2025-01-19 18:00:00'
    },
    processingHistory: [
      {
        nodeId: 'NODE_1',
        nodeName: '初步审查',
        processorId: 'USER_002',
        processorName: '李审查员',
        startTime: '2025-01-18 09:00:00',
        endTime: '2025-01-18 10:00:00',
        action: '通过初审',
        comments: '申诉材料齐全，转入详细调查'
      }
    ],
    nextProcessors: [
      {
        nodeId: 'NODE_3',
        possibleProcessors: [
          {
            processorId: 'USER_003',
            processorName: '王主管',
            workload: 3,
            avgProcessTime: 6
          }
        ]
      }
    ]
  }
  return Promise.resolve(mockProcessors)
}

// 申诉处理
export const processAppeal = (appealId: number, processing: {
  action: 'accept' | 'reject' | 'transfer' | 'investigate' | 'escalate'
  decision?: {
    decisionType: 'score_adjustment' | 'process_correction' | 'data_fix' | 'no_action'
    decisionContent: string
    adjustments?: Array<{      // 如果是调整评分
      objectId: number
      indicatorId?: number
      oldValue: any
      newValue: any
      reason: string
    }>
  }
  processingComments: string
  nextAction?: {
    transferTo?: string        // 转移给谁
    escalateTo?: string       // 升级给谁
    investigationPlan?: string // 调查计划
    deadline?: string         // 处理期限
  }
  evidence?: Array<{          // 处理过程中的证据
    evidenceType: string
    fileName: string
    fileUrl: string
    description: string
  }>
}): Promise<{
  success: boolean
  processingResult: {
    newStatus: string
    nextProcessor?: string
    estimatedCompletionTime?: string
    affectedObjects?: number[]
    notificationsSent: Array<{
      recipient: string
      type: string
      status: 'sent' | 'failed'
    }>
  }
}> => {
  console.log('处理申诉:', appealId, processing)
  const mockProcessingResult = {
    success: true,
    processingResult: {
      newStatus: '处理完成',
      affectedObjects: [1],
      notificationsSent: [
        {
          recipient: '申诉人',
          type: 'appeal_decision',
          status: 'sent' as const
        }
      ]
    }
  }
  return Promise.resolve(mockProcessingResult)
}

// 设置申诉通知模板
export const setAppealNotificationTemplate = (template: {
  templateId?: number
  templateName: string
  templateType: 'appeal_submitted' | 'appeal_assigned' | 'appeal_processed' | 'appeal_decided'
  subject: string
  content: string
  variables: string[]          // 可用变量
  recipientRules: {
    includeAppellant: boolean  // 包含申诉人
    includeProcessor: boolean  // 包含处理人
    includeManager: boolean    // 包含管理员
    includeRelevantUsers: boolean // 包含相关用户
  }
  triggerConditions: {
    appealTypes: string[]      // 适用申诉类型
    urgencyLevels: string[]    // 适用紧急程度
    statusChanges: string[]    // 触发的状态变化
  }
}): Promise<{ templateId: number }> => {
  console.log('设置申诉通知模板:', template)
  return Promise.resolve({ templateId: Date.now() })
}

// 设置申诉处理通知模板
export const setAppealProcessNotificationTemplate = (template: {
  templateId?: number
  templateName: string
  processNodeId: string       // 关联的流程节点
  notificationTiming: 'on_assign' | 'before_deadline' | 'on_complete' | 'on_escalate'
  timingOffset?: number       // 提前通知时间（小时）
  subject: string
  content: string
  recipientSelection: {
    processorTypes: string[]   // 处理人类型
    managementLevels: string[] // 管理层级
    customRecipients: string[] // 自定义收件人
  }
}): Promise<{ templateId: number }> => {
  console.log('设置申诉处理通知模板:', template)
  return Promise.resolve({ templateId: Date.now() })
}

// 推送申诉消息
export const pushAppealNotification = (notification: {
  appealId: number
  notificationType: 'appeal_submitted' | 'appeal_assigned' | 'appeal_processed'
  recipients: Array<{
    recipientId: string
    recipientType: 'user' | 'role' | 'department'
    contactMethod: 'email' | 'sms' | 'system'
  }>
  messageContent: {
    subject: string
    body: string
    attachments?: string[]
    urgency: 'low' | 'normal' | 'high'
  }
  sendOptions: {
    sendImmediately: boolean
    scheduleTime?: string
    retryOnFailure: boolean
    maxRetries?: number
  }
}): Promise<{
  notificationId: number
  sentCount: number
  failedCount: number
  details: Array<{
    recipientId: string
    status: 'sent' | 'failed' | 'pending'
    sentTime?: string
    errorMessage?: string
  }>
}> => {
  console.log('推送申诉消息:', notification)
  return Promise.resolve({
    notificationId: Date.now(),
    sentCount: notification.recipients.length,
    failedCount: 0,
    details: notification.recipients.map(recipient => ({
      recipientId: recipient.recipientId,
      status: 'sent' as const,
      sentTime: new Date().toISOString()
    }))
  })
}

// 推送申诉处理消息
export const pushAppealProcessNotification = (notification: {
  appealId: number
  processNodeId: string
  notificationType: 'process_start' | 'process_reminder' | 'process_complete' | 'process_escalate'
  targetRecipients: Array<{
    recipientId: string
    recipientRole: string
    notificationPreferences: {
      email: boolean
      sms: boolean
      system: boolean
    }
  }>
  messageData: {
    appealNumber: string
    currentStatus: string
    deadline?: string
    actionRequired: string
    priority: 'low' | 'normal' | 'high' | 'urgent'
  }
}): Promise<{
  batchId: number
  totalRecipients: number
  successfulSends: number
  failedSends: number
  sendResults: Array<{
    recipientId: string
    methods: Array<{
      method: 'email' | 'sms' | 'system'
      status: 'success' | 'failed'
      timestamp: string
      errorCode?: string
    }>
  }>
}> => {
  console.log('推送申诉处理消息:', notification)
  return Promise.resolve({
    batchId: Date.now(),
    totalRecipients: notification.targetRecipients.length,
    successfulSends: notification.targetRecipients.length,
    failedSends: 0,
    sendResults: notification.targetRecipients.map(recipient => ({
      recipientId: recipient.recipientId,
      methods: [
        {
          method: 'system' as const,
          status: 'success' as const,
          timestamp: new Date().toISOString()
        }
      ]
    }))
  })
}

// ================================
// 六、统计和监控API
// ================================

// 获取统计数据
export const getAuditStatistics = (): Promise<AuditStatistics> => {
  const mockStatistics: AuditStatistics = {
    totalProjects: 2,
    totalCultivationObjects: 55,
    pendingAudit: 15,
    auditCompleted: 35,
    auditRejected: 5,
    totalAppeals: 3,
    avgScore: 87.5,
    auditTrend: [
      { date: '2025-01-14', pending: 20, completed: 15, rejected: 2 },
      { date: '2025-01-15', pending: 18, completed: 20, rejected: 3 },
      { date: '2025-01-16', pending: 15, completed: 25, rejected: 1 },
      { date: '2025-01-17', pending: 12, completed: 28, rejected: 2 },
      { date: '2025-01-18', pending: 15, completed: 35, rejected: 5 }
    ],
    scoreDistribution: [
      { range: '90-100', count: 12 },
      { range: '80-89', count: 18 },
      { range: '70-79', count: 8 },
      { range: '60-69', count: 3 },
      { range: '<60', count: 2 }
    ]
  }
  return Promise.resolve(mockStatistics)
}

// 获取项目列表
export const getProjectList = (): Promise<ProjectInfo[]> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/projects')
  return Promise.resolve(mockProjects)
}

// 获取培育对象列表（支持筛选和搜索）
export const getCultivationObjectList = (params?: AuditSearchParams): Promise<{
  list: CultivationObject[]
  total: number
}> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/cultivation-objects', { params })
  let filteredList = [...mockCultivationObjects]

  // 应用筛选条件
  if (params?.projectId) {
    filteredList = filteredList.filter(item => item.projectId === params.projectId)
  }
  if (params?.status) {
    filteredList = filteredList.filter(item => item.status === params.status)
  }
  if (params?.cultivationObjectName) {
    filteredList = filteredList.filter(item => 
      item.name.includes(params.cultivationObjectName!) ||
      item.organizationName.includes(params.cultivationObjectName!)
    )
  }
  if (params?.auditor) {
    filteredList = filteredList.filter(item => item.auditor === params.auditor)
  }
  if (params?.dateRange) {
    // 简化的日期筛选逻辑
    const [startDate, endDate] = params.dateRange
    filteredList = filteredList.filter(item => {
      const itemDate = item.submitTime.split(' ')[0]
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return Promise.resolve({
    list: filteredList,
    total: filteredList.length
  })
}

// 获取审核记录列表
export const getAuditRecordList = (params?: AuditSearchParams): Promise<{
  list: AuditRecord[]
  total: number
}> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/audit-records', { params })
  let filteredList = [...mockAuditRecords]
  
  // 应用筛选条件（类似培育对象列表的筛选逻辑）
  if (params?.projectId) {
    filteredList = filteredList.filter(record => {
      const obj = mockCultivationObjects.find(obj => obj.id === record.cultivationObjectId)
      return obj?.projectId === params.projectId
    })
  }
  
  return Promise.resolve({
    list: filteredList,
    total: filteredList.length
  })
}

// 获取审核流程节点
export const getProcessNodes = (): Promise<AuditProcessNode[]> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/process/nodes')
  const mockNodes: AuditProcessNode[] = [
    {
      id: 1,
      nodeName: '初审',
      nodeOrder: 1,
      nodeType: 1,
      auditors: ['张审核', '李审核'],
      auditStandard: '基础资格审查，确认申报材料齐全完整',
      auditCycle: 3,
      isRequired: true,
      nextNodeId: 2,
      isEnabled: true
    },
    {
      id: 2,
      nodeName: '复审',
      nodeOrder: 2,
      nodeType: 2,
      auditors: ['王审核', '赵审核'],
      auditStandard: '详细评分审核，逐项核实指标得分合理性',
      auditCycle: 5,
      isRequired: true,
      nextNodeId: 3,
      isEnabled: true
    },
    {
      id: 3,
      nodeName: '终审',
      nodeOrder: 3,
      nodeType: 3,
      auditors: ['局长', '副局长'],
      auditStandard: '最终确认审核，确保评分公平公正',
      auditCycle: 2,
      isRequired: true,
      isEnabled: true
    }
  ]
  return Promise.resolve(mockNodes)
}

// 获取通知模板
export const getNotificationTemplates = (): Promise<NotificationTemplate[]> => {
  // Mock实现 - 实际应调用 request.get('/api/audit-confirmation/notification-templates')
  const mockTemplates: NotificationTemplate[] = [
    {
      id: 1,
      templateName: '审核通过通知',
      templateType: 1,
      title: '审核结果通知',
      content: '您好，您的申报"{{objectName}}"已通过审核，总分：{{score}}分。请及时查看详细结果。',
      recipients: ['申报人', '联系人'],
      isEnabled: true,
      createTime: '2025-01-01 10:00:00',
      updateTime: '2025-01-01 10:00:00'
    },
    {
      id: 2,
      templateName: '审核退回通知',
      templateType: 1,
      title: '审核结果通知',
      content: '您好，您的申报"{{objectName}}"需要补充完善，请根据审核意见进行修改后重新提交。',
      recipients: ['申报人', '联系人'],
      isEnabled: true,
      createTime: '2025-01-01 10:00:00',
      updateTime: '2025-01-01 10:00:00'
    },
    {
      id: 3,
      templateName: '申诉受理通知',
      templateType: 3,
      title: '申诉处理通知',
      content: '您的申诉申请{{appealNumber}}已受理，我们将在{{processTime}}个工作日内处理完成。',
      recipients: ['申诉人'],
      isEnabled: true,
      createTime: '2025-01-01 10:00:00',
      updateTime: '2025-01-01 10:00:00'
    }
  ]
  return Promise.resolve(mockTemplates)
}

// ================================
// 兼容性API - 保持与现有组件的兼容
// ================================

// 批量审核（简化版，兼容现有调用）
export const batchAudit = (data: {
  cultivationObjectIds: number[]
  auditResult: number
  comments: string
}): Promise<boolean> => {
  console.log('批量审核:', data)
  return Promise.resolve(true)
}

// 修改评分（简化版，兼容现有调用）
export const updateScore = (data: {
  cultivationObjectId: number
  indicatorId: number
  score: number
  comments?: string
}): Promise<boolean> => {
  console.log('修改评分:', data)
  return Promise.resolve(true)
}

// 组合API对象（保持向后兼容）
export const auditConfirmationApi = {
  // 统计数据
  getStatistics: getAuditStatistics,
  
  // 基础数据获取
  getProjectList,
  getCultivationObjectList,
  getAuditRecordList,
  getProcessNodes,
  getNotificationTemplates,
  
  // 审核操作
  submitAuditResult,
  updateScore,
  batchAudit,
  
  // 权限相关
  getUserPermissions: getUserAuditPermissions,
  
  // 流程配置
  configProcessNodes: configAuditProcessNodes,
  updateProcessNode: updateAuditProcessNode,
  
  // 评分相关
  getScoreResult: getCultivationObjectScoreResult,
  getIndicatorResults: getCultivationObjectIndicatorResults,
  getIndicatorDetails: getCultivationObjectIndicatorDetails,
  
  // 申诉相关
  submitAppeal: submitAppealApplication,
  processAppeal,
  getAppealPermissions: getUserAppealPermissions
}
