// 智能入围审核系统API接口定义（静态页面版本）
import type { 
  ApplicationRecord, 
  ReviewRecord, 
  ShortlistRecord, 
  AppealRecord,
  FeedbackReport,
  ApplicationSearchParams,
  ReviewSearchParams,
  ShortlistSearchParams,
  AppealSearchParams,
  FeedbackSearchParams, 
  ReviewStatistics,
  ReviewProcess,
  PreReviewConfig,
  ScoringCriteria
} from '@/types/review';

// 模拟数据 - 申报记录
const mockApplicationRecords: ApplicationRecord[] = [
  {
    id: 1,
    applicantName: '市委组织部',
    applicantType: 1,
    projectName: '红岩先锋·模范机关',
    projectType: 1,
    contactPerson: '陈胜',
    contactPhone: '13800138001',
    contactEmail: '<EMAIL>',
    status: 3,
    applicationDate: '2025-06-15',
    submitTime: '2025-06-21',
    updateTime: '2025-06-16 14:30:00',
    preReviewResult: '预审通过，符合申报条件',
    preReviewScore: 0,
    reviewComments: '项目创新性强，技术方案可行',
    attachments: ['项目计划书.pdf', '技术方案.docx', '预算表.xlsx'],
    operator: '系统自动',
    isQualified: true,
    description: '基于人工智能的智能制造技术研发，提升生产效率'
  },
  {
    id: 2,
    applicantName: '市委统战部',
    applicantType: 1,
    projectName: '红岩先锋·模范机关',
    projectType: 2,
    contactPerson: '孙建',
    contactPhone: '13800138002',
    contactEmail: '<EMAIL>',
    status: 3,
    applicationDate: '2025-06-16',
    submitTime: '2025-06-21',
    updateTime: '2025-06-17 16:00:00',
    preReviewResult: '预审通过',
    preReviewScore: 0,
    reviewComments: '文化价值突出，数字化方案合理',
    attachments: ['项目申请书.pdf', '文化价值评估.docx'],
    operator: '王审核员',
    isQualified: true,
    description: '运用数字技术保护和传承传统文化遗产'
  },
  {
    id: 3,
    applicantName: '市委统战部',
    applicantType: 1,
    projectName: '红岩先锋·模范机关',
    projectType: 3,
    contactPerson: '杜佳佳',
    contactPhone: '13800138003',
    contactEmail: '<EMAIL>',
    status: 3,
    applicationDate: '2025-06-17',
    submitTime: '2025-06-21',
    updateTime: '2025-06-17 14:00:00',
    preReviewResult: '',
    preReviewScore: 0,
    operator: '系统自动',
    isQualified: true,
    description: '为偏远地区提供优质教育资源和师资培训'
  }
];

// 模拟数据 - 审核记录
const mockReviewRecords: ReviewRecord[] = [
  {
    id: 1,
    applicationId: 1,
    reviewType: 1,
    reviewerName: '智能预审系统',
    reviewTime: '2025-06-15 10:00:00',
    reviewResult: 1,
    score: 85,
    comments: '申报材料完整，符合基本条件',
    suggestions: '建议补充技术创新点的详细说明',
    isPass: true,
    nextStep: '进入人工审核阶段'
  },
  {
    id: 2,
    applicationId: 1,
    reviewType: 2,
    reviewerName: '王审核员',
    reviewTime: '2025-06-16 14:30:00',
    reviewResult: 1,
    score: 88,
    comments: '项目技术方案可行，创新性强，建议通过',
    isPass: true,
    nextStep: '进入入围名单'
  },
  {
    id: 3,
    applicationId: 2,
    reviewType: 1,
    reviewerName: '智能预审系统',
    reviewTime: '2025-06-16 11:00:00',
    reviewResult: 1,
    score: 78,
    comments: '文化价值突出，申报材料基本符合要求',
    isPass: true,
    nextStep: '进入人工审核阶段'
  }
];

// 模拟数据 - 入围名单
const mockShortlistRecords: ShortlistRecord[] = [
  {
    id: 1,
    applicationId: 1,
    applicantName: '市委办公厅',
    projectName: '红岩先锋·模范机关',
    finalScore: 88,
    ranking: 1,
    status: 3,
    publicTime: '2025-06-18 09:00:00',
    comments: '综合评分第一名',
    createTime: '2025-06-17 16:00:00'
  },
  {
    id: 2,
    applicationId: 2,
    applicantName: '市人大',
    projectName: '红岩先锋·模范机关',
    finalScore: 82,
    ranking: 2,
    status: 2,
    comments: '文化价值突出',
    createTime: '2025-06-17 16:30:00'
  }
];

// 模拟数据 - 申诉记录
const mockAppealRecords: AppealRecord[] = [
  {
    id: 1,
    applicationId: 3,
    applicantName: '市委网信办',
    appealReason: '预审结果有误',
    appealContent: '我们认为预审过程中对我们的公益性质评估有误，请求重新审核',
    appealTime: '2025-06-18 10:00:00',
    status: 2,
    appealAttachments: ['补充材料.pdf', '公益证明.docx'],
    createTime: '2025-06-18 10:00:00',
    updateTime: '2025-06-18 10:00:00'
  }
];

// 模拟数据 - 反馈报告
const mockFeedbackReports: FeedbackReport[] = [
  {
    id: 1,
    applicationId: 1,
    applicantName: '市委办公厅',
    reportType: 2,
    reportContent: '您的项目在本次评审中表现优秀',
    evaluationDetails: '技术创新性：优秀（25分）\n市场前景：良好（20分）\n团队实力：优秀（23分）\n实施方案：良好（20分）',
    suggestions: '建议进一步完善产业化方案，加强市场推广策略',
    decisionBasis: '基于专家评审意见和智能评分系统综合评定',
    generateTime: '2025-06-17 17:00:00',
    isConfirmed: true,
    confirmTime: '2025-06-18 09:30:00'
  }
];

// 模拟数据 - 统计信息
const mockStatistics: ReviewStatistics = {
  totalApplications: 156,
  submittedApplications: 142,
  preReviewPassed: 98,
  finalReviewPassed: 45,
  totalShortlisted: 30,
  totalAppeals: 8,
  appealSuccess: 3,
  applicationTypeStats: [
    { type: 1, count: 65, passRate: 0.72 },
    { type: 2, count: 38, passRate: 0.68 },
    { type: 3, count: 28, passRate: 0.64 },
    { type: 4, count: 11, passRate: 0.55 }
  ],
  projectTypeStats: [
    { type: 1, count: 45, avgScore: 82.5 },
    { type: 2, count: 32, avgScore: 78.3 },
    { type: 3, count: 28, avgScore: 75.8 },
    { type: 4, count: 22, avgScore: 73.2 },
    { type: 5, count: 15, avgScore: 70.1 }
  ],
  reviewTrend: [
    { date: '2025-06-15', submitted: 12, reviewed: 8, passed: 6 },
    { date: '2025-06-16', submitted: 15, reviewed: 12, passed: 9 },
    { date: '2025-06-17', submitted: 18, reviewed: 15, passed: 11 },
    { date: '2025-06-18', submitted: 10, reviewed: 8, passed: 5 },
    { date: '2025-06-19', submitted: 8, reviewed: 6, passed: 4 }
  ],
  scoreDistribution: [
    { range: '90-100', count: 8 },
    { range: '80-89', count: 15 },
    { range: '70-79', count: 22 },
    { range: '60-69', count: 18 },
    { range: '60以下', count: 12 }
  ]
};

// API函数定义（返回模拟数据）

// 获取申报记录列表
export function fetchApplicationList(params?: ApplicationSearchParams): Promise<{ data: ApplicationRecord[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockApplicationRecords];
      
      if (params?.applicantName) {
        filteredData = filteredData.filter(item => 
          item.applicantName.includes(params.applicantName!)
        );
      }
      
      if (params?.projectName) {
        filteredData = filteredData.filter(item => 
          item.projectName.includes(params.projectName!)
        );
      }
      
      if (params?.applicantType) {
        filteredData = filteredData.filter(item => 
          item.applicantType === params.applicantType
        );
      }
      
      if (params?.status) {
        filteredData = filteredData.filter(item => 
          item.status === params.status
        );
      }
      
      resolve({ data: filteredData });
    }, 500);
  });
}

// 获取审核记录列表
export function fetchReviewList(params?: ReviewSearchParams): Promise<{ data: ReviewRecord[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockReviewRecords];
      
      if (params?.reviewType) {
        filteredData = filteredData.filter(item => 
          item.reviewType === params.reviewType
        );
      }
      
      if (params?.reviewResult) {
        filteredData = filteredData.filter(item => 
          item.reviewResult === params.reviewResult
        );
      }
      
      resolve({ data: filteredData });
    }, 500);
  });
}

// 获取入围名单列表
export function fetchShortlistList(params?: ShortlistSearchParams): Promise<{ data: ShortlistRecord[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockShortlistRecords];
      
      if (params?.applicantName) {
        filteredData = filteredData.filter(item => 
          item.applicantName.includes(params.applicantName!)
        );
      }
      
      if (params?.status) {
        filteredData = filteredData.filter(item => 
          item.status === params.status
        );
      }
      
      resolve({ data: filteredData });
    }, 500);
  });
}

// 获取申诉记录列表
export function fetchAppealList(params?: AppealSearchParams): Promise<{ data: AppealRecord[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockAppealRecords];
      
      if (params?.applicantName) {
        filteredData = filteredData.filter(item => 
          item.applicantName.includes(params.applicantName!)
        );
      }
      
      if (params?.status) {
        filteredData = filteredData.filter(item => 
          item.status === params.status
        );
      }
      
      resolve({ data: filteredData });
    }, 500);
  });
}

// 获取反馈报告列表
export function fetchFeedbackList(params?: FeedbackSearchParams): Promise<{ data: FeedbackReport[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredData = [...mockFeedbackReports];

      if (params?.applicantName) {
        filteredData = filteredData.filter(item =>
          item.applicantName.includes(params.applicantName!)
        );
      }

      if (params?.reportType) {
        filteredData = filteredData.filter(item =>
          item.reportType === params.reportType
        );
      }

      resolve({ data: filteredData });
    }, 500);
  });
}

// 获取统计数据
export function fetchReviewStatistics(): Promise<{ data: ReviewStatistics }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data: mockStatistics });
    }, 300);
  });
}

// 提交申报
export function submitApplication(data: Partial<ApplicationRecord>): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('提交申报:', data);
      resolve({ data: true });
    }, 800);
  });
}

// 更新申报
export function updateApplication(data: Partial<ApplicationRecord>): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('更新申报:', data);
      resolve({ data: true });
    }, 800);
  });
}

// 删除申报
export function deleteApplication(id: number): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('删除申报:', id);
      resolve({ data: true });
    }, 500);
  });
}

// 智能预审
export function executePreReview(applicationId: number): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('执行智能预审:', applicationId);
      resolve({ data: true });
    }, 2000);
  });
}

// 人工审核
export function submitReview(data: Partial<ReviewRecord>): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('提交审核意见:', data);
      resolve({ data: true });
    }, 1000);
  });
}

// 生成入围名单
export function generateShortlist(criteria: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('生成入围名单:', criteria);
      resolve({ data: true });
    }, 1500);
  });
}

// 公示入围名单
export function publishShortlist(shortlistIds: number[]): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('公示入围名单:', shortlistIds);
      resolve({ data: true });
    }, 800);
  });
}

// 提交申诉
export function submitAppeal(data: Partial<AppealRecord>): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('提交申诉:', data);
      resolve({ data: true });
    }, 800);
  });
}

// 处理申诉
export function processAppeal(appealId: number, decision: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('处理申诉:', appealId, decision);
      resolve({ data: true });
    }, 1000);
  });
}

// 生成反馈报告
export function generateFeedbackReport(applicationId: number): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('生成反馈报告:', applicationId);
      resolve({ data: true });
    }, 1200);
  });
}

// 确认反馈报告
export function confirmFeedbackReport(reportId: number): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('确认反馈报告:', reportId);
      resolve({ data: true });
    }, 500);
  });
}

// 获取审核流程状态
export function fetchReviewProcess(applicationId: number): Promise<{ data: ReviewProcess }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockProcess: ReviewProcess = {
        applicationId,
        currentStep: 3,
        totalSteps: 5,
        steps: [
          {
            stepName: '申报提交',
            status: 'completed',
            completedTime: '2025-06-15 09:00:00',
            operator: '申报人',
            comments: '申报材料已提交'
          },
          {
            stepName: '智能预审',
            status: 'completed',
            completedTime: '2025-06-15 10:00:00',
            operator: '智能预审系统',
            comments: '预审通过，符合基本条件'
          },
          {
            stepName: '人工审核',
            status: 'current',
            operator: '王审核员',
            comments: '正在进行人工审核'
          },
          {
            stepName: '入围名单生成',
            status: 'pending'
          },
          {
            stepName: '结果公示',
            status: 'pending'
          }
        ]
      };
      resolve({ data: mockProcess });
    }, 400);
  });
}

// 导出入围名单
export function exportShortlist(format: 'excel' | 'pdf' = 'excel'): Promise<{ data: string }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('导出入围名单:', format);
      resolve({ data: 'export_url_placeholder' });
    }, 1000);
  });
}

// 批量操作申报状态
export function batchUpdateApplicationStatus(ids: number[], status: number): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('批量更新申报状态:', ids, status);
      resolve({ data: true });
    }, 1000);
  });
}

// 获取预审配置
export function fetchPreReviewConfig(applicantType?: number): Promise<{ data: PreReviewConfig[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockConfigs: PreReviewConfig[] = [
        {
          id: 1,
          configName: '企业单位预审标准',
          applicantType: 1,
          projectType: 1,
          criteria: [
            { name: '注册资本', weight: 0.2, minValue: 100, maxValue: 10000, required: true },
            { name: '成立年限', weight: 0.15, minValue: 2, maxValue: 50, required: true },
            { name: '技术人员比例', weight: 0.25, minValue: 0.3, maxValue: 1, required: true }
          ],
          passThreshold: 70,
          isEnabled: true,
          createTime: '2025-06-01 00:00:00',
          updateTime: '2025-06-01 00:00:00'
        }
      ];
      resolve({ data: mockConfigs });
    }, 500);
  });
}

// 保存预审配置
export function savePreReviewConfig(data: Partial<PreReviewConfig>): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('保存预审配置:', data);
      resolve({ data: true });
    }, 800);
  });
}

// ============ 审核确认模块新增API ============

// 培育对象列表管理API
export function fetchCultivationObjectList(params?: any): Promise<{ data: any[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          objectName: '党员服务中心数字化改革项目',
          projectType: '党建项目',
          applicantName: '市委组织部',
          auditStatus: 'under_review',
          systemScore: 85.5,
          reviewScore: 88.2,
          submitTime: '2025-01-05 14:30:00',
          reviewerName: '张审核员',
          reviewTime: '2025-01-06 10:20:00'
        }
      ];
      resolve({ data: mockData });
    }, 500);
  });
}

// 获取培育对象详情
export function fetchCultivationObjectDetail(id: string): Promise<{ data: any }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ 
        data: {
          id,
          objectName: '党员服务中心数字化改革项目',
          systemScore: 85.5,
          indicators: []
        }
      });
    }, 500);
  });
}

// 审核流程配置API
export function fetchReviewProcessConfig(): Promise<{ data: any }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          processNodes: [],
          auditCriteria: [],
          notificationConfig: {}
        }
      });
    }, 500);
  });
}

// 保存审核流程配置
export function saveReviewProcessConfig(data: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('保存审核流程配置:', data);
      resolve({ data: true });
    }, 1000);
  });
}

// 申诉管理API - 使用现有的fetchAppealList函数

// 提交申诉
export function submitAppealRequest(data: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('提交申诉:', data);
      resolve({ data: true });
    }, 800);
  });
}

// 处理申诉
export function handleAppealRequest(appealId: string, data: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('处理申诉:', appealId, data);
      resolve({ data: true });
    }, 1000);
  });
}

// 通知模板管理API
export function fetchNotificationTemplates(): Promise<{ data: any[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockTemplates = [
        {
          id: '1',
          name: '审核通过通知',
          type: 'review',
          trigger: 'review_complete',
          title: '您的申报项目审核通过',
          content: '恭喜您的项目通过审核...',
          enabled: true
        }
      ];
      resolve({ data: mockTemplates });
    }, 500);
  });
}

// 保存通知模板
export function saveNotificationTemplate(data: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('保存通知模板:', data);
      resolve({ data: true });
    }, 800);
  });
}

// 再次评分API
export function fetchRescoreObjects(params?: any): Promise<{ data: any[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          name: '党员服务中心数字化改革项目',
          applicantName: '市委组织部',
          originalScore: 85.5,
          estimatedScore: 88.2,
          rescoreStatus: 'pending',
          rescoreReason: '创新程度评分标准调整'
        }
      ];
      resolve({ data: mockData });
    }, 500);
  });
}

// 执行批量重评
export function executeBatchRescore(data: any): Promise<{ data: boolean }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('执行批量重评:', data);
      resolve({ data: true });
    }, 3000);
  });
}

// 获取评分规则
export function fetchScoringRules(): Promise<{ data: any[] }> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockRules = [
        {
          id: '1',
          indicatorName: '创新程度',
          weight: 30,
          scoringMethod: 'range',
          ranges: [
            { min: 90, max: 100, score: 100 },
            { min: 80, max: 89, score: 85 }
          ],
          enabled: true
        }
      ];
      resolve({ data: mockRules });
    }, 500);
  });
}
