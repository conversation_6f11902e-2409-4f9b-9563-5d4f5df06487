import { apiWrapper } from '@/utils/api-wrapper'
import type {
	SensitiveWord,
	WordCategory,
	WordLevel,
	FilterPolicy,
	SensitiveWordSearchParams,
	PolicySearchParams,
	SensitiveWordStatistics,
	DetectionResult,
	ImportTask,
} from '@/types/sensitive-words'

/**
 * 敏感词管理API服务
 */

// 敏感词相关API
export const sensitiveWordsApi = {
	/**
	 * 查询敏感词列表
	 */
	getSensitiveWords: (params?: SensitiveWordSearchParams) => {
		return apiWrapper.get<SensitiveWord[]>('/api/sensitive-words/words', { params })
	},

	/**
	 * 根据ID查询敏感词
	 */
	getSensitiveWordById: (id: number) => {
		return apiWrapper.get<SensitiveWord>(`/api/sensitive-words/words/${id}`)
	},

	/**
	 * 创建敏感词
	 */
	createSensitiveWord: (data: Omit<SensitiveWord, 'id'>) => {
		return apiWrapper.post<SensitiveWord>('/api/sensitive-words/words', data)
	},

	/**
	 * 更新敏感词
	 */
	updateSensitiveWord: (id: number, data: Partial<SensitiveWord>) => {
		return apiWrapper.put<SensitiveWord>(`/api/sensitive-words/words/${id}`, data)
	},

	/**
	 * 删除敏感词
	 */
	deleteSensitiveWord: (id: number) => {
		return apiWrapper.delete(`/api/sensitive-words/words/${id}`)
	},

	/**
	 * 批量删除敏感词
	 */
	batchDeleteSensitiveWords: (ids: number[]) => {
		return apiWrapper.delete('/api/sensitive-words/words/batch', { data: ids })
	},

	/**
	 * 批量导入敏感词
	 */
	importSensitiveWords: (file: File) => {
		const formData = new FormData()
		formData.append('file', file)
		return apiWrapper.post<{ successCount: number; failCount: number }>('/api/sensitive-words/words/import', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
	},

	/**
	 * 导出敏感词
	 */
	exportSensitiveWords: (params?: SensitiveWordSearchParams & { format?: string }) => {
		return apiWrapper.get('/api/sensitive-words/words/export', {
			params,
			responseType: 'blob',
		})
	},

	/**
	 * 文本内容检测
	 */
	detectTextContent: (content: string) => {
		return apiWrapper.post<DetectionResult>('/api/sensitive-words/detect/text', { content })
	},

	/**
	 * 华为云文本内容检测
	 */
	detectTextContentByHuawei: async (content: string) => {
		const baseUrl = 'https://captcha.aidangqun.com'
		const response = await fetch(`${baseUrl}/text/v3/verify`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				content: content,
			}),
		})

		if (!response.ok) {
			throw new Error(`华为云API请求失败: ${response.status}`)
		}

		const result = await response.json()
		return result
	},

	/**
	 * 图片内容检测（本地API）
	 */
	detectImageContent: (file: File) => {
		const formData = new FormData()
		formData.append('image', file)
		return apiWrapper.post<DetectionResult>('/api/sensitive-words/detect/image', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
	},

	/**
	 * 华为云图像内容检测
	 */
	detectImageContentByHuawei: async (file: File) => {
		const baseUrl = 'https://captcha.aidangqun.com'
		const formData = new FormData()
		formData.append('file', file)
		console.log(file, formData, 'file')
		console.log('FormData file entry:', formData.get('file'))
		return apiWrapper.post<any>(`${baseUrl}/image/v3/verify`, formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
	},
	/**
	 * 文件上传到OBS对象存储
	 * @param file 要上传的文件
	 * @param generateThumbnail 是否为视频生成缩略图，默认true
	 * @param getDuration 是否获取视频时长信息，默认true
	 */
	uploadFileToObs: async (file: File, generateThumbnail: boolean = true, getDuration: boolean = true) => {
		const formData = new FormData()
		formData.append('file', file)
		formData.append('generateThumbnail', generateThumbnail.toString())
		formData.append('getDuration', getDuration.toString())
		console.log('FormData file entry:', formData.get('file'))
		// 调试信息 - FormData内容无法直接打印，检查文件和FormData是否存在
		// console.log('FormData keys:', Array.from(formData.keys()))
		const baseUrl = 'https://captcha.aidangqun.com'
		return apiWrapper.post<any>(`${baseUrl}/api/obs/files/upload`, formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
	},

	/**
	 * 创建视频内容审核任务
	 * @param videoUrl 视频URL地址
	 */
	createVideoVerifyTask: async (videoUrl: object) => {
		const baseUrl = 'https://captcha.aidangqun.com'
		return apiWrapper.post<any>(`${baseUrl}/video/v3/verify`, videoUrl)
	},

	/**
	 * 查询视频内容审核结果
	 * @param jobId 视频审核任务ID
	 */
	getVideoVerifyResult: async (jobId: string) => {
		const baseUrl = 'https://captcha.aidangqun.com'
		return apiWrapper.get<any>(`${baseUrl}/video/v3/getResult/${jobId}`)
	},

	/**
	 * 华为云检测OBS中的内容（基于文件URL）
	 * @param fileUrl OBS文件访问URL
	 * @param contentType 内容类型：image, video, audio, text
	 */
	detectObsContentByHuawei: async (fileUrl: string, contentType: 'image' | 'video' | 'audio' | 'text') => {
		const baseUrl = import.meta.env.VITE_APP_BASE_API || 'https://mfjg-pc.aidangqun.com/owsz/acceptance'

		let endpoint = ''
		switch (contentType) {
			case 'image':
				endpoint = '/image/v3/verify'
				break
			case 'video':
				endpoint = '/video/v3/verify'
				break
			case 'audio':
				endpoint = '/audio/v3/verify'
				break
			case 'text':
				endpoint = '/text/v3/verify'
				break
			default:
				throw new Error(`不支持的内容类型: ${contentType}`)
		}

		const response = await fetch(`${baseUrl}${endpoint}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				url: fileUrl,
			}),
		})

		if (!response.ok) {
			throw new Error(`华为云${contentType}检测API请求失败: ${response.status}`)
		}

		const result = await response.json()
		return result
	},

	/**
	 * 视频内容检测
	 */
	detectVideoContent: (file: File) => {
		const formData = new FormData()
		formData.append('video', file)
		return apiWrapper.post<DetectionResult>('/api/sensitive-words/detect/video', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		})
	},
}

// 敏感词分类相关API
export const categoryApi = {
	/**
	 * 查询分类列表
	 */
	getCategories: () => {
		return apiWrapper.get<WordCategory[]>('/api/sensitive-words/categories')
	},

	/**
	 * 查询分类树
	 */
	getCategoryTree: () => {
		return apiWrapper.get<WordCategory[]>('/api/sensitive-words/categories/tree')
	},

	/**
	 * 根据ID查询分类
	 */
	getCategoryById: (id: number) => {
		return apiWrapper.get<WordCategory>(`/api/sensitive-words/categories/${id}`)
	},

	/**
	 * 创建分类
	 */
	createCategory: (data: Omit<WordCategory, 'id'>) => {
		return apiWrapper.post<WordCategory>('/api/sensitive-words/categories', data)
	},

	/**
	 * 更新分类
	 */
	updateCategory: (id: number, data: Partial<WordCategory>) => {
		return apiWrapper.put<WordCategory>(`/api/sensitive-words/categories/${id}`, data)
	},

	/**
	 * 删除分类
	 */
	deleteCategory: (id: number) => {
		return apiWrapper.delete(`/api/sensitive-words/categories/${id}`)
	},
}

// 敏感词级别相关API
export const levelApi = {
	/**
	 * 查询级别列表
	 */
	getLevels: () => {
		return apiWrapper.get<WordLevel[]>('/api/sensitive-words/levels')
	},

	/**
	 * 根据ID查询级别
	 */
	getLevelById: (id: number) => {
		return apiWrapper.get<WordLevel>(`/api/sensitive-words/levels/${id}`)
	},

	/**
	 * 创建级别
	 */
	createLevel: (data: Omit<WordLevel, 'id'>) => {
		return apiWrapper.post<WordLevel>('/api/sensitive-words/levels', data)
	},

	/**
	 * 更新级别
	 */
	updateLevel: (id: number, data: Partial<WordLevel>) => {
		return apiWrapper.put<WordLevel>(`/api/sensitive-words/levels/${id}`, data)
	},

	/**
	 * 删除级别
	 */
	deleteLevel: (id: number) => {
		return apiWrapper.delete(`/api/sensitive-words/levels/${id}`)
	},
}

// 过滤策略相关API
export const policyApi = {
	/**
	 * 查询策略列表
	 */
	getPolicies: (params?: PolicySearchParams) => {
		return apiWrapper.get<FilterPolicy[]>('/api/sensitive-words/policies', { params })
	},

	/**
	 * 根据ID查询策略
	 */
	getPolicyById: (id: number) => {
		return apiWrapper.get<FilterPolicy>(`/api/sensitive-words/policies/${id}`)
	},

	/**
	 * 创建策略
	 */
	createPolicy: (data: Omit<FilterPolicy, 'id'>) => {
		return apiWrapper.post<FilterPolicy>('/api/sensitive-words/policies', data)
	},

	/**
	 * 更新策略
	 */
	updatePolicy: (id: number, data: Partial<FilterPolicy>) => {
		return apiWrapper.put<FilterPolicy>(`/api/sensitive-words/policies/${id}`, data)
	},

	/**
	 * 删除策略
	 */
	deletePolicy: (id: number) => {
		return apiWrapper.delete(`/api/sensitive-words/policies/${id}`)
	},

	/**
	 * 启用/禁用策略
	 */
	togglePolicyStatus: (id: number, isEnabled: boolean) => {
		return apiWrapper.patch(`/api/sensitive-words/policies/${id}/status`, { isEnabled })
	},
}

// 统计相关API
export const statisticsApi = {
	/**
	 * 获取统计信息
	 */
	getStatistics: () => {
		return apiWrapper.get<SensitiveWordStatistics>('/api/sensitive-words/statistics')
	},

	/**
	 * 获取分类统计
	 */
	getCategoryStats: () => {
		return apiWrapper.get('/api/sensitive-words/statistics/categories')
	},

	/**
	 * 获取级别统计
	 */
	getLevelStats: () => {
		return apiWrapper.get('/api/sensitive-words/statistics/levels')
	},
}

// 系统配置相关API
export const configApi = {
	/**
	 * 获取系统配置
	 */
	getConfig: () => {
		return apiWrapper.get('/api/sensitive-words/config')
	},

	/**
	 * 更新系统配置
	 */
	updateConfig: (config: Record<string, any>) => {
		return apiWrapper.put('/api/sensitive-words/config', config)
	},

	/**
	 * 重置系统配置
	 */
	resetConfig: () => {
		return apiWrapper.post('/api/sensitive-words/config/reset')
	},
}

// 工具类API
export const utilsApi = {
	/**
	 * 文本预处理
	 */
	preprocessText: (text: string) => {
		return apiWrapper.post('/api/sensitive-words/utils/preprocess', { text })
	},

	/**
	 * 敏感词测试
	 */
	testWords: (words: string[], content: string) => {
		return apiWrapper.post('/api/sensitive-words/utils/test', { words, content })
	},

	/**
	 * 获取建议替换词
	 */
	getSuggestions: (word: string) => {
		return apiWrapper.get('/api/sensitive-words/utils/suggestions', { params: { word } })
	},

	/**
	 * 验证敏感词格式
	 */
	validateWords: (words: string[]) => {
		return apiWrapper.post('/api/sensitive-words/utils/validate', { words })
	},
}

// 日志相关API
export const logApi = {
	/**
	 * 获取检测日志
	 */
	getDetectionLogs: (params?: any) => {
		return apiWrapper.get('/api/sensitive-words/logs/detection', { params })
	},

	/**
	 * 获取操作日志
	 */
	getOperationLogs: (params?: any) => {
		return apiWrapper.get('/api/sensitive-words/logs/operation', { params })
	},

	/**
	 * 清理过期日志
	 */
	cleanupLogs: (days: number) => {
		return apiWrapper.delete('/api/sensitive-words/logs/cleanup', { data: { days } })
	},
}

// 导出所有API
export default {
	sensitiveWords: sensitiveWordsApi,
	category: categoryApi,
	level: levelApi,
	policy: policyApi,
	statistics: statisticsApi,
	config: configApi,
	utils: utilsApi,
	log: logApi,
}
