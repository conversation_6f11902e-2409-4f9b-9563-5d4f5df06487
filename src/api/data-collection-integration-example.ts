/**
 * 数据收集API集成示例
 * 展示如何在Vue组件中使用数据收集API
 */

import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import DataCollectionAPI, {
  getDataSources,
  getStats,
  testConnection,
  startSync,
  stopSync,
  getSyncHistory,
  getDataFlowMonitor,
  type DataSource,
  type DataSourceQueryParams,
  type DataCollectionStats,
  type SyncHistory,
  type DataFlowMonitor
} from './data-collection'

// 导入Mock服务用于开发环境
import MockDataCollectionService from '@/mock/data-collection'

/**
 * 数据收集Composable
 * 封装数据收集相关的逻辑，供Vue组件使用
 */
export function useDataCollection() {
  // ==================== 响应式状态 ====================
  
  /** 加载状态 */
  const loading = ref(false)
  
  /** 数据源列表 */
  const dataSourceList = ref<DataSource[]>([])
  
  /** 统计信息 */
  const statistics = reactive<DataCollectionStats>({
    totalSources: 0,
    onlineSources: 0,
    offlineSources: 0,
    activeSources: 0,
    enabledSources: 0,
    todaySyncCount: 0,
    todaySyncVolume: 0,
    avgSyncDuration: 0,
    totalSyncCount: 0,
    totalDataVolume: 0,
    qualityScore: 0,
    qualityTrend: [],
    avgThroughput: 0,
    peakThroughput: 0,
    successRate: 0,
    errorCount: 0,
    criticalErrors: 0,
    errorTrend: [],
    sourceTypeStats: []
  })
  
  /** 同步历史 */
  const syncHistoryList = ref<SyncHistory[]>([])
  
  /** 数据流监控 */
  const dataFlowMonitors = ref<DataFlowMonitor[]>([])
  
  /** 分页配置 */
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`
  })
  
  /** 搜索和筛选参数 */
  const searchParams = reactive<DataSourceQueryParams>({
    keyword: '',
    type: undefined,
    status: undefined,
    connectionStatus: undefined,
    isEnabled: undefined,
    page: 1,
    pageSize: 10
  })

  // ==================== 计算属性 ====================
  
  /** 在线数据源数量 */
  const onlineSourcesCount = computed(() => 
    dataSourceList.value.filter(ds => ds.connectionStatus === 'online').length
  )
  
  /** 启用数据源数量 */
  const enabledSourcesCount = computed(() => 
    dataSourceList.value.filter(ds => ds.isEnabled).length
  )
  
  /** 平均成功率 */
  const avgSuccessRate = computed(() => {
    if (dataSourceList.value.length === 0) return 0
    const total = dataSourceList.value.reduce((sum, ds) => sum + ds.successRate, 0)
    return Number((total / dataSourceList.value.length).toFixed(1))
  })

  // ==================== API调用方法 ====================
  
  /**
   * 加载数据源列表
   */
  const loadDataSources = async (showLoading = true) => {
    if (showLoading) loading.value = true
    
    try {
      const params = {
        ...searchParams,
        page: pagination.current,
        pageSize: pagination.pageSize
      }
      
      const result = await getDataSources(params)
      
      dataSourceList.value = result.list
      pagination.total = result.total
      pagination.current = result.page
      
      message.success(`加载完成，共 ${result.total} 条数据源`)
    } catch (error) {
      console.error('加载数据源失败:', error)
      message.error('加载数据源失败，请检查网络连接')
    } finally {
      if (showLoading) loading.value = false
    }
  }
  
  /**
   * 加载统计信息
   */
  const loadStatistics = async () => {
    try {
      const stats = await getStats()
      Object.assign(statistics, stats)
    } catch (error) {
      console.error('加载统计信息失败:', error)
      message.error('加载统计信息失败')
    }
  }
  
  /**
   * 测试数据源连接
   */
  const testDataSourceConnection = async (dataSourceId: string) => {
    const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
    if (!dataSource) {
      message.error('数据源不存在')
      return
    }
    
    // 设置测试中状态
    dataSource.isSwitching = true
    
    try {
      const result = await testConnection(dataSourceId)
      
      if (result.success) {
        dataSource.connectionStatus = 'online'
        dataSource.lastConnectionTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        message.success(`${dataSource.name} 连接测试成功`)
      } else {
        dataSource.connectionStatus = 'error'
        dataSource.errorMessage = result.message
        message.error(`${dataSource.name} 连接测试失败: ${result.message}`)
      }
    } catch (error) {
      console.error('连接测试失败:', error)
      dataSource.connectionStatus = 'error'
      message.error(`${dataSource.name} 连接测试异常`)
    } finally {
      dataSource.isSwitching = false
    }
  }
  
  /**
   * 启动数据同步
   */
  const startDataSync = async (dataSourceId: string, syncType: 'full' | 'incremental' | 'real_time' = 'incremental') => {
    const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
    if (!dataSource) {
      message.error('数据源不存在')
      return
    }
    
    if (!dataSource.isEnabled) {
      message.error('数据源未启用，无法启动同步')
      return
    }
    
    if (dataSource.connectionStatus !== 'online') {
      message.error('数据源连接异常，无法启动同步')
      return
    }
    
    dataSource.isSwitching = true
    
    try {
      const result = await startSync(dataSourceId, syncType)
      
      if (result.success) {
        dataSource.syncStatus = 'collecting'
        dataSource.lastSyncTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        message.success(`${dataSource.name} 数据同步已启动`)
      } else {
        message.error(`启动同步失败: ${result.message}`)
      }
    } catch (error) {
      console.error('启动同步失败:', error)
      message.error(`${dataSource.name} 启动同步异常`)
    } finally {
      dataSource.isSwitching = false
    }
  }
  
  /**
   * 停止数据同步
   */
  const stopDataSync = async (dataSourceId: string) => {
    const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
    if (!dataSource) {
      message.error('数据源不存在')
      return
    }
    
    dataSource.isSwitching = true
    
    try {
      const result = await stopSync(dataSourceId)
      
      if (result.success) {
        dataSource.syncStatus = 'stopped'
        message.success(`${dataSource.name} 数据同步已停止`)
      } else {
        message.error(`停止同步失败: ${result.message}`)
      }
    } catch (error) {
      console.error('停止同步失败:', error)
      message.error(`${dataSource.name} 停止同步异常`)
    } finally {
      dataSource.isSwitching = false
    }
  }
  
  /**
   * 切换数据源启用状态
   */
  const toggleDataSource = async (dataSourceId: string, enabled: boolean) => {
    const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
    if (!dataSource) {
      message.error('数据源不存在')
      return
    }
    
    dataSource.isSwitching = true
    
    try {
      const result = await DataCollectionAPI.toggleDataSource(dataSourceId, enabled)
      
      if (result.success) {
        dataSource.isEnabled = enabled
        if (!enabled) {
          dataSource.syncStatus = 'stopped'
          dataSource.connectionStatus = 'offline'
        }
        message.success(`${dataSource.name} 已${enabled ? '启用' : '禁用'}`)
      } else {
        message.error(`操作失败: ${result.message}`)
      }
    } catch (error) {
      console.error('切换状态失败:', error)
      message.error(`${dataSource.name} 状态切换异常`)
    } finally {
      dataSource.isSwitching = false
    }
  }
  
  /**
   * 删除数据源
   */
  const deleteDataSource = async (dataSourceId: string) => {
    const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
    if (!dataSource) {
      message.error('数据源不存在')
      return
    }
    
    try {
      const result = await DataCollectionAPI.deleteDataSource(dataSourceId)
      
      if (result.success) {
        const index = dataSourceList.value.findIndex(ds => ds.id === dataSourceId)
        if (index > -1) {
          dataSourceList.value.splice(index, 1)
        }
        pagination.total -= 1
        message.success(`${dataSource.name} 删除成功`)
      } else {
        message.error(`删除失败: ${result.message}`)
      }
    } catch (error) {
      console.error('删除数据源失败:', error)
      message.error(`${dataSource.name} 删除异常`)
    }
  }
  
  /**
   * 批量操作
   */
  const batchOperateDataSources = async (
    ids: string[], 
    operation: 'enable' | 'disable' | 'delete' | 'sync',
    syncType?: 'full' | 'incremental' | 'real_time'
  ) => {
    if (ids.length === 0) {
      message.warning('请选择要操作的数据源')
      return
    }
    
    loading.value = true
    
    try {
      let result
      
      switch (operation) {
        case 'enable':
          result = await DataCollectionAPI.batchToggleDataSources(ids, true)
          break
        case 'disable':
          result = await DataCollectionAPI.batchToggleDataSources(ids, false)
          break
        case 'delete':
          result = await DataCollectionAPI.batchDeleteDataSources(ids)
          break
        case 'sync':
          result = await DataCollectionAPI.batchSyncDataSources(ids, syncType)
          break
      }
      
      if (result) {
        message.success(`批量操作完成: 成功 ${result.success} 个，失败 ${result.failed} 个`)
        
        // 刷新数据
        if (operation === 'delete') {
          loadDataSources(false)
        } else {
          // 更新本地状态
          ids.forEach(id => {
            const dataSource = dataSourceList.value.find(ds => ds.id === id)
            if (dataSource) {
              switch (operation) {
                case 'enable':
                  dataSource.isEnabled = true
                  break
                case 'disable':
                  dataSource.isEnabled = false
                  dataSource.syncStatus = 'stopped'
                  break
                case 'sync':
                  dataSource.syncStatus = 'collecting'
                  dataSource.lastSyncTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
                  break
              }
            }
          })
        }
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      message.error('批量操作异常，请重试')
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 加载同步历史
   */
  const loadSyncHistory = async (dataSourceId?: string) => {
    try {
      const result = await getSyncHistory({
        dataSourceId,
        page: 1,
        pageSize: 50,
        sortBy: 'createTime',
        sortOrder: 'desc'
      })
      
      syncHistoryList.value = result.list
    } catch (error) {
      console.error('加载同步历史失败:', error)
      message.error('加载同步历史失败')
    }
  }
  
  /**
   * 加载数据流监控
   */
  const loadDataFlowMonitor = async (dataSourceId?: string) => {
    try {
      const monitors = await getDataFlowMonitor(dataSourceId)
      dataFlowMonitors.value = monitors
    } catch (error) {
      console.error('加载数据流监控失败:', error)
      message.error('加载数据流监控失败')
    }
  }
  
  /**
   * 搜索和筛选
   */
  const handleSearch = () => {
    pagination.current = 1
    loadDataSources()
  }
  
  /**
   * 重置搜索条件
   */
  const resetSearch = () => {
    Object.assign(searchParams, {
      keyword: '',
      type: undefined,
      status: undefined,
      connectionStatus: undefined,
      isEnabled: undefined,
      page: 1,
      pageSize: 10
    })
    pagination.current = 1
    loadDataSources()
  }
  
  /**
   * 处理分页变化
   */
  const handlePaginationChange = (page: number, pageSize: number) => {
    pagination.current = page
    pagination.pageSize = pageSize
    searchParams.page = page
    searchParams.pageSize = pageSize
    loadDataSources()
  }
  
  /**
   * 全量刷新数据
   */
  const refreshData = async () => {
    await Promise.all([
      loadDataSources(),
      loadStatistics(),
      loadSyncHistory(),
      loadDataFlowMonitor()
    ])
    message.success('数据已刷新')
  }

  // ==================== 开发环境Mock配置 ====================
  
  /**
   * 启用Mock模式（开发环境）
   */
  const enableMockMode = () => {
    if (import.meta.env.DEV) {
      DataCollectionAPI.enableStaticMode()
      message.info('已启用Mock模式，使用静态数据')
    }
  }
  
  /**
   * 禁用Mock模式
   */
  const disableMockMode = () => {
    if (import.meta.env.DEV) {
      DataCollectionAPI.disableStaticMode()
      message.info('已禁用Mock模式，使用真实API')
    }
  }

  // ==================== 生命周期钩子 ====================
  
  /**
   * 初始化数据
   */
  const initializeData = async () => {
    await Promise.all([
      loadDataSources(),
      loadStatistics()
    ])
  }

  // ==================== 返回接口 ====================
  
  return {
    // 状态
    loading,
    dataSourceList,
    statistics,
    syncHistoryList,
    dataFlowMonitors,
    pagination,
    searchParams,
    
    // 计算属性
    onlineSourcesCount,
    enabledSourcesCount,
    avgSuccessRate,
    
    // 方法
    loadDataSources,
    loadStatistics,
    testDataSourceConnection,
    startDataSync,
    stopDataSync,
    toggleDataSource,
    deleteDataSource,
    batchOperateDataSources,
    loadSyncHistory,
    loadDataFlowMonitor,
    handleSearch,
    resetSearch,
    handlePaginationChange,
    refreshData,
    initializeData,
    
    // 开发工具
    enableMockMode,
    disableMockMode
  }
}

/**
 * Vue组件使用示例
 * 展示如何在Vue组件中使用数据收集API
 */
export const DataCollectionComponentExample = {
  name: 'DataCollectionExample',
  setup() {
    // 使用Composable
    const {
      loading,
      dataSourceList,
      statistics,
      pagination,
      searchParams,
      onlineSourcesCount,
      enabledSourcesCount,
      avgSuccessRate,
      loadDataSources,
      loadStatistics,
      testDataSourceConnection,
      startDataSync,
      stopDataSync,
      toggleDataSource,
      deleteDataSource,
      handleSearch,
      resetSearch,
      refreshData,
      initializeData,
      enableMockMode
    } = useDataCollection()
    
    // 组件挂载时初始化数据
    onMounted(async () => {
      // 开发环境启用Mock模式
      if (import.meta.env.DEV) {
        enableMockMode()
      }
      
      await initializeData()
    })
    
    // 组件方法
    const handleStartSync = async (dataSource: DataSource) => {
      await startDataSync(dataSource.id, 'incremental')
    }
    
    const handleStopSync = async (dataSource: DataSource) => {
      await stopDataSync(dataSource.id)
    }
    
    const handleTestConnection = async (dataSource: DataSource) => {
      await testDataSourceConnection(dataSource.id)
    }
    
    const handleToggleEnable = async (dataSource: DataSource, enabled: boolean) => {
      await toggleDataSource(dataSource.id, enabled)
    }
    
    const handleDelete = async (dataSource: DataSource) => {
      if (confirm(`确定要删除数据源 "${dataSource.name}" 吗？`)) {
        await deleteDataSource(dataSource.id)
      }
    }
    
    return {
      // 响应式状态
      loading,
      dataSourceList,
      statistics,
      pagination,
      searchParams,
      
      // 计算属性
      onlineSourcesCount,
      enabledSourcesCount,
      avgSuccessRate,
      
      // 方法
      loadDataSources,
      loadStatistics,
      handleStartSync,
      handleStopSync,
      handleTestConnection,
      handleToggleEnable,
      handleDelete,
      handleSearch,
      resetSearch,
      refreshData
    }
  }
}

/**
 * 使用说明和最佳实践
 */
export const USAGE_GUIDE = {
  // 基本用法
  basic: `
    // 1. 导入Composable
    import { useDataCollection } from '@/api/data-collection-integration-example'
    
    // 2. 在setup函数中使用
    export default {
      setup() {
        const {
          loading,
          dataSourceList,
          statistics,
          loadDataSources,
          testDataSourceConnection
        } = useDataCollection()
        
        // 3. 在组件挂载时加载数据
        onMounted(() => {
          loadDataSources()
        })
        
        return {
          loading,
          dataSourceList,
          statistics,
          testDataSourceConnection
        }
      }
    }
  `,
  
  // 错误处理
  errorHandling: `
    // API调用已内置错误处理，会自动显示错误消息
    // 可以通过try-catch捕获特殊错误
    
    const handleCustomOperation = async () => {
      try {
        await someOperation()
      } catch (error) {
        // 自定义错误处理逻辑
        console.error('操作失败:', error)
        // 可以显示自定义错误信息或执行其他操作
      }
    }
  `,
  
  // 性能优化
  performance: `
    // 1. 使用computed属性计算衍生数据
    const filteredSources = computed(() => 
      dataSourceList.value.filter(ds => ds.isEnabled)
    )
    
    // 2. 避免频繁调用API，使用防抖
    import { debounce } from 'lodash-es'
    
    const debouncedSearch = debounce(() => {
      handleSearch()
    }, 300)
    
    // 3. 批量操作减少API调用次数
    const selectedIds = ref<string[]>([])
    
    const handleBatchEnable = () => {
      batchOperateDataSources(selectedIds.value, 'enable')
    }
  `,
  
  // 开发调试
  development: `
    // 开发环境使用Mock数据
    if (import.meta.env.DEV) {
      enableMockMode()
    }
    
    // 调试API调用
    DataCollectionAPI.enableStaticMode() // 强制使用静态数据
    DataCollectionAPI.disableStaticMode() // 恢复API调用
    
    // 重置Mock数据
    MockDataCollectionService.resetMockData()
  `
}

export default {
  useDataCollection,
  DataCollectionComponentExample,
  USAGE_GUIDE
}