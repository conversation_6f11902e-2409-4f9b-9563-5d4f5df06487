/**
 * 地图配置管理服务
 * 统一管理地图API密钥和配置
 */

export interface MapConfig {
    key: string
    securityCode: string
    version: string
    plugins: string[]
}

export interface MapOptions {
    zoom: number
    center: [number, number]
    viewMode: string
    features: string[]
    mapStyle: string
}

/**
 * 获取高德地图配置
 */
export function getAmapConfig(): MapConfig {
    const key = import.meta.env.VITE_AMAP_KEY
    const securityCode = import.meta.env.VITE_AMAP_SECURITY_CODE

    if (!key || !securityCode) {
        throw new Error('地图API配置缺失，请检查环境变量 VITE_AMAP_KEY 和 VITE_AMAP_SECURITY_CODE')
    }

    return {
        key,
        securityCode,
        version: '2.0',
        plugins: [
            'AMap.PlaceSearch',
            'AMap.Marker',
            'AMap.InfoWindow',
            'AMap.Driving',
            'AMap.Walking',
            'AMap.Transfer'
        ]
    }
}

/**
 * 获取默认地图选项
 */
export function getDefaultMapOptions(): MapOptions {
    return {
        zoom: 11,
        center: [106.550483, 29.563707], // 重庆市中心
        viewMode: '2D',
        features: ['bg', 'road', 'building', 'point'],
        mapStyle: 'amap://styles/normal'
    }
}

/**
 * 验证地图配置
 */
export function validateMapConfig(config: MapConfig): boolean {
    return !!(config.key && config.securityCode && config.version)
}

/**
 * 设置地图安全配置
 */
export function setMapSecurity(securityCode: string): void {
    if (typeof window !== 'undefined') {
        (window as any)._AMapSecurityConfig = {
            securityJsCode: securityCode
        }
    }
}