/**
 * 案例征集系统权限配置
 * 定义系统中各个功能模块的权限控制
 */

export interface Permission {
  code: string;
  name: string;
  description: string;
  module: string;
}

export interface PermissionGroup {
  name: string;
  permissions: Permission[];
}

/**
 * 案例征集系统权限定义
 */
export const CASE_COLLECTION_PERMISSIONS = {
  // 数据看板权限
  DASHBOARD: {
    VIEW: 'case_collection:dashboard:view',
    EXPORT: 'case_collection:dashboard:export'
  },

  // 活动管理权限
  ACTIVITY: {
    VIEW: 'case_collection:activity:view',
    CREATE: 'case_collection:activity:create',
    EDIT: 'case_collection:activity:edit',
    DELETE: 'case_collection:activity:delete',
    PUBLISH: 'case_collection:activity:publish',
    MANAGE: 'case_collection:activity:manage'
  },

  // 案例提交权限
  SUBMISSION: {
    VIEW: 'case_collection:submission:view',
    CREATE: 'case_collection:submission:create',
    EDIT: 'case_collection:submission:edit',
    DELETE: 'case_collection:submission:delete',
    VIEW_ALL: 'case_collection:submission:view_all'
  },

  // 审核管理权限
  REVIEW: {
    VIEW: 'case_collection:review:view',
    APPROVE: 'case_collection:review:approve',
    REJECT: 'case_collection:review:reject',
    BATCH: 'case_collection:review:batch',
    SETTINGS: 'case_collection:review:settings'
  },

  // 分类管理权限
  CATEGORY: {
    VIEW: 'case_collection:category:view',
    CREATE: 'case_collection:category:create',
    EDIT: 'case_collection:category:edit',
    DELETE: 'case_collection:category:delete',
    MANAGE: 'case_collection:category:manage'
  },

  // 系统管理权限
  SYSTEM: {
    CONFIG: 'case_collection:system:config',
    USER_MANAGE: 'case_collection:system:user_manage',
    LOG_VIEW: 'case_collection:system:log_view'
  }
};

/**
 * 权限组配置
 */
export const PERMISSION_GROUPS: PermissionGroup[] = [
  {
    name: '数据看板',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
        name: '查看看板',
        description: '查看案例征集数据看板',
        module: 'dashboard'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.DASHBOARD.EXPORT,
        name: '导出数据',
        description: '导出看板数据和报告',
        module: 'dashboard'
      }
    ]
  },
  {
    name: '活动管理',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW,
        name: '查看活动',
        description: '查看案例征集活动列表和详情',
        module: 'activity'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.CREATE,
        name: '创建活动',
        description: '创建新的案例征集活动',
        module: 'activity'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.EDIT,
        name: '编辑活动',
        description: '编辑案例征集活动信息',
        module: 'activity'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.DELETE,
        name: '删除活动',
        description: '删除案例征集活动',
        module: 'activity'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.PUBLISH,
        name: '发布活动',
        description: '发布和停止案例征集活动',
        module: 'activity'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.ACTIVITY.MANAGE,
        name: '管理活动',
        description: '全面管理案例征集活动',
        module: 'activity'
      }
    ]
  },
  {
    name: '案例提交',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW,
        name: '查看提交',
        description: '查看自己的案例提交',
        module: 'submission'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SUBMISSION.CREATE,
        name: '提交案例',
        description: '提交新的案例',
        module: 'submission'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SUBMISSION.EDIT,
        name: '编辑提交',
        description: '编辑自己的案例提交',
        module: 'submission'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SUBMISSION.DELETE,
        name: '删除提交',
        description: '删除自己的案例提交',
        module: 'submission'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL,
        name: '查看所有提交',
        description: '查看所有用户的案例提交',
        module: 'submission'
      }
    ]
  },
  {
    name: '审核管理',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW,
        name: '查看审核',
        description: '查看案例审核列表和详情',
        module: 'review'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.REVIEW.APPROVE,
        name: '审核通过',
        description: '审核通过案例提交',
        module: 'review'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.REVIEW.REJECT,
        name: '审核驳回',
        description: '审核驳回案例提交',
        module: 'review'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.REVIEW.BATCH,
        name: '批量审核',
        description: '批量审核案例提交',
        module: 'review'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.REVIEW.SETTINGS,
        name: '审核设置',
        description: '配置审核规则和流程',
        module: 'review'
      }
    ]
  },
  {
    name: '分类管理',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW,
        name: '查看分类',
        description: '查看案例分类列表',
        module: 'category'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.CATEGORY.CREATE,
        name: '创建分类',
        description: '创建新的案例分类',
        module: 'category'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.CATEGORY.EDIT,
        name: '编辑分类',
        description: '编辑案例分类信息',
        module: 'category'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.CATEGORY.DELETE,
        name: '删除分类',
        description: '删除案例分类',
        module: 'category'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.CATEGORY.MANAGE,
        name: '管理分类',
        description: '全面管理案例分类体系',
        module: 'category'
      }
    ]
  },
  {
    name: '系统管理',
    permissions: [
      {
        code: CASE_COLLECTION_PERMISSIONS.SYSTEM.CONFIG,
        name: '系统配置',
        description: '配置案例征集系统参数',
        module: 'system'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SYSTEM.USER_MANAGE,
        name: '用户管理',
        description: '管理系统用户和权限',
        module: 'system'
      },
      {
        code: CASE_COLLECTION_PERMISSIONS.SYSTEM.LOG_VIEW,
        name: '日志查看',
        description: '查看系统操作日志',
        module: 'system'
      }
    ]
  }
];

/**
 * 角色权限预设
 */
export const ROLE_PERMISSIONS = {
  // 系统管理员
  ADMIN: Object.values(CASE_COLLECTION_PERMISSIONS).flatMap(group => Object.values(group)),
  
  // 活动管理员
  ACTIVITY_MANAGER: [
    CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
    CASE_COLLECTION_PERMISSIONS.DASHBOARD.EXPORT,
    ...Object.values(CASE_COLLECTION_PERMISSIONS.ACTIVITY),
    CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL,
    ...Object.values(CASE_COLLECTION_PERMISSIONS.REVIEW),
    ...Object.values(CASE_COLLECTION_PERMISSIONS.CATEGORY)
  ],
  
  // 审核员
  REVIEWER: [
    CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
    CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW,
    CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL,
    ...Object.values(CASE_COLLECTION_PERMISSIONS.REVIEW).filter(p => p !== CASE_COLLECTION_PERMISSIONS.REVIEW.SETTINGS),
    CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW
  ],
  
  // 普通用户
  USER: [
    CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
    CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW,
    ...Object.values(CASE_COLLECTION_PERMISSIONS.SUBMISSION).filter(p => p !== CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL),
    CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW
  ]
};

/**
 * 检查用户是否有指定权限
 * @param userPermissions 用户权限列表
 * @param requiredPermission 需要的权限
 * @returns 是否有权限
 */
export function hasPermission(userPermissions: string[], requiredPermission: string): boolean {
  return userPermissions.includes(requiredPermission);
}

/**
 * 检查用户是否有任一权限
 * @param userPermissions 用户权限列表
 * @param requiredPermissions 需要的权限列表
 * @returns 是否有任一权限
 */
export function hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.some(permission => userPermissions.includes(permission));
}

/**
 * 检查用户是否有所有权限
 * @param userPermissions 用户权限列表
 * @param requiredPermissions 需要的权限列表
 * @returns 是否有所有权限
 */
export function hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.every(permission => userPermissions.includes(permission));
}
