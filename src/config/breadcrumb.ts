/**
 * 面包屑导航配置
 * 用于生成页面的面包屑导航
 */

export interface BreadcrumbItem {
  title: string;
  path?: string;
  icon?: string;
}

export interface BreadcrumbConfig {
  [key: string]: BreadcrumbItem[];
}

/**
 * 案例征集系统面包屑配置
 */
export const caseCollectionBreadcrumbs: BreadcrumbConfig = {
  // 数据看板
  '/case-collection': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '数据看板' }
  ],
  '/case-collection/dashboard': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '数据看板' }
  ],

  // 活动管理
  '/case-collection/activities': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '活动管理' }
  ],
  '/case-collection/activities/list': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '活动管理', path: '/case-collection/activities' },
    { title: '活动列表' }
  ],
  '/case-collection/activities/create': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '活动管理', path: '/case-collection/activities' },
    { title: '创建活动' }
  ],
  '/case-collection/activities/:id': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '活动管理', path: '/case-collection/activities' },
    { title: '活动详情' }
  ],
  '/case-collection/activities/:id/edit': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '活动管理', path: '/case-collection/activities' },
    { title: '编辑活动' }
  ],

  // 案例提交
  '/case-collection/submit': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例提交' }
  ],
  '/case-collection/submit/form': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例提交', path: '/case-collection/submit' },
    { title: '提交案例' }
  ],

  // 提交管理
  '/case-collection/submissions': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '我的提交' }
  ],
  '/case-collection/submissions/:id': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '我的提交', path: '/case-collection/submissions' },
    { title: '提交详情' }
  ],

  // 案例预审
  '/case-collection/review': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审' }
  ],
  '/case-collection/review/list': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审', path: '/case-collection/review' },
    { title: '审核列表' }
  ],
  '/case-collection/review/detail/:id': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审', path: '/case-collection/review' },
    { title: '审核详情' }
  ],
  '/case-collection/review/batch': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审', path: '/case-collection/review' },
    { title: '批量审核' }
  ],
  '/case-collection/review/history': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审', path: '/case-collection/review' },
    { title: '审核历史' }
  ],
  '/case-collection/review/settings': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '案例预审', path: '/case-collection/review' },
    { title: '审核设置' }
  ],

  // 分类管理
  '/case-collection/categories': [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' },
    { title: '分类管理' }
  ]
};

/**
 * 获取指定路径的面包屑配置
 * @param path 当前路径
 * @param params 路由参数（用于替换动态路由参数）
 * @returns 面包屑配置数组
 */
export function getBreadcrumbConfig(path: string, params?: Record<string, string>): BreadcrumbItem[] {
  // 直接匹配
  if (caseCollectionBreadcrumbs[path]) {
    return caseCollectionBreadcrumbs[path];
  }

  // 动态路由匹配
  for (const [pattern, config] of Object.entries(caseCollectionBreadcrumbs)) {
    if (pattern.includes(':')) {
      const regex = new RegExp('^' + pattern.replace(/:[^/]+/g, '[^/]+') + '$');
      if (regex.test(path)) {
        // 替换动态参数
        let processedConfig = config;
        if (params) {
          processedConfig = config.map(item => {
            if (item.path && item.path.includes(':')) {
              let processedPath = item.path;
              Object.entries(params).forEach(([key, value]) => {
                processedPath = processedPath.replace(`:${key}`, value);
              });
              return { ...item, path: processedPath };
            }
            return item;
          });
        }
        return processedConfig;
      }
    }
  }

  // 默认面包屑
  return [
    { title: '首页', path: '/home', icon: 'home' },
    { title: '案例征集', path: '/case-collection' }
  ];
}

/**
 * 生成面包屑导航的工具函数
 * @param path 当前路径
 * @param params 路由参数
 * @returns 面包屑配置
 */
export function generateBreadcrumb(path: string, params?: Record<string, string>) {
  return getBreadcrumbConfig(path, params);
}
