<template>
  <div class="case-list-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>案例列表</h2>
        <p>浏览和管理所有案例，支持多种展示模式和高级搜索筛选功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="navigateToCreate">
            <template #icon><plus-outlined /></template>
            新建案例
          </a-button>
          <a-button @click="handleExport">
            <template #icon><export-outlined /></template>
            批量导出
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <SearchFilter 
        :loading="loading"
        @search="handleSearch"
        @reset="handleSearchReset"
      />
    </div>

    <!-- 展示模式切换和统计信息 -->
    <div class="display-control-section">
      <a-card>
        <div class="control-header">
          <div class="display-info">
            <span class="total-count">共 {{ filteredCaseList.length }} 个案例</span>
            <a-divider type="vertical" />
            <span class="filter-info">
              <template v-if="hasActiveFilters">
                已筛选 {{ filteredCaseList.length }} / {{ mockCaseList.length }} 个案例
              </template>
              <template v-else>
                显示全部案例
              </template>
            </span>
          </div>
          
          <div class="display-controls">
            <a-space>
              <span>展示模式：</span>
              <a-radio-group v-model:value="displayMode" button-style="solid" size="small">
                <a-radio-button value="list">
                  <template #icon><unordered-list-outlined /></template>
                  列表
                </a-radio-button>
                <a-radio-button value="card">
                  <template #icon><appstore-outlined /></template>
                  卡片
                </a-radio-button>
              </a-radio-group>
              
              <a-divider type="vertical" />
              
              <span>排序：</span>
              <a-select v-model:value="sortBy" style="width: 120px" size="small" @change="handleSortChange">
                <a-select-option value="createTime">创建时间</a-select-option>
                <a-select-option value="publishTime">发布时间</a-select-option>
                <a-select-option value="viewCount">浏览量</a-select-option>
                <a-select-option value="title">标题</a-select-option>
              </a-select>
              
              <a-select v-model:value="sortOrder" style="width: 80px" size="small" @change="handleSortChange">
                <a-select-option value="desc">降序</a-select-option>
                <a-select-option value="asc">升序</a-select-option>
              </a-select>
            </a-space>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 数据展示区域 -->
    <div class="data-display-section">
      <a-card v-if="displayMode === 'list'">
        <!-- 列表模式 - 表格展示 -->
        <a-table
          :dataSource="paginatedCaseList"
          :loading="loading"
          rowKey="id"
          :pagination="false"
         
        >
          <a-table-column title="序号" width="80" fixed="left">
            <template #default="{ index }">
              {{ (currentPage - 1) * pageSize + index + 1 }}
            </template>
          </a-table-column>
          
          <a-table-column title="案例标题" dataIndex="title" width="300" fixed="left">
            <template #default="{ record }">
              <div class="case-title-cell">
                <h4 class="title-text" @click="handleView(record)">{{ record.title }}</h4>
                <p class="summary-text">{{ record.summary }}</p>
              </div>
            </template>
          </a-table-column>
          
          <a-table-column title="分类" dataIndex="category" width="120">
            <template #default="{ record }">
              <a-tag color="blue">{{ getCategoryText(record.category) }}</a-tag>
            </template>
          </a-table-column>
          
          <a-table-column title="标签" dataIndex="tags" width="200">
            <template #default="{ record }">
              <div class="tags-cell">
                <a-tag v-for="tag in getTagsDisplay(record.tags)" :key="tag.id" :color="tag.color" size="small">
                  {{ tag.name }}
                </a-tag>
              </div>
            </template>
          </a-table-column>
          
          <a-table-column title="难度" dataIndex="difficulty" width="100">
            <template #default="{ record }">
              <a-tag :color="getDifficultyColor(record.difficulty)">
                {{ getDifficultyText(record.difficulty) }}
              </a-tag>
            </template>
          </a-table-column>
          
          <a-table-column title="状态" dataIndex="status" width="100">
            <template #default="{ record }">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </a-table-column>
          
          <a-table-column title="来源" dataIndex="source" width="150" />
          
          <a-table-column title="浏览量" dataIndex="viewCount" width="100" sorter>
            <template #default="{ record }">
              {{ record.viewCount || 0 }}
            </template>
          </a-table-column>
          
          <a-table-column title="发布时间" dataIndex="publishTime" width="120">
            <template #default="{ record }">
              {{ formatTime(record.publishTime || record.createTime) }}
            </template>
          </a-table-column>
          
          <a-table-column title="操作" key="action" width="300" fixed="right">
            <template #default="{ record }">
              <a-space>
                <a-button type="link" size="small" @click="handleView(record)">
                  <template #icon><eye-outlined /></template>
                  查看
                </a-button>
                <a-button type="link" size="small" @click="handleEdit(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-popconfirm title="确定删除这个案例吗？" @confirm="handleDelete(record)">
                  <a-button type="link" size="small" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </a-table-column>
        </a-table>
      </a-card>

      <!-- 卡片模式 - 网格展示 -->
      <div v-else class="card-grid-container">
        <a-row :gutter="[16, 16]">
          <a-col 
            v-for="caseItem in paginatedCaseList" 
            :key="caseItem.id"
            :xs="24" 
            :sm="12" 
            :md="8" 
            :lg="6"
          >
            <CaseCard 
              mode="card"
              :case-data="caseItem"
              :show-actions="true"
              @view="handleView"
              @edit="handleEdit"
              @delete="handleDelete"
            />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-section">
      <a-card>
        <div class="pagination-wrapper">
          <a-pagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="filteredCaseList.length"
            :show-size-changer="true"
            :show-quick-jumper="true"
            :page-size-options="['12', '24', '48', '96']"
            :show-total="(total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`"
            @change="handlePageChange"
            @show-size-change="handlePageSizeChange"
          />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  ExportOutlined,
  ReloadOutlined,
  UnorderedListOutlined,
  AppstoreOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import SearchFilter from './components/SearchFilter.vue'
import CaseCard from './components/CaseCard.vue'
import type { CaseEntity } from '@/types/case'
import {
  CaseStatusMap,
  CaseStatusColorMap,
  DifficultyLevelMap,
  DifficultyLevelColorMap
} from '@/types/case'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const displayMode = ref<'list' | 'card'>('list')
const currentPage = ref(1)
const pageSize = ref(24)
const sortBy = ref('createTime')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 搜索参数
const searchParams = reactive({
  keyword: '',
  category: undefined,
  tags: [],
  difficulty: undefined,
  status: undefined,
  industry: undefined,
  dateRange: null,
  source: '',
  viewCountMin: undefined,
  viewCountMax: undefined
})

// 模拟案例数据
const mockCaseList = ref<CaseEntity[]>([])

// 计算属性
const hasActiveFilters = computed(() => {
  return Object.values(searchParams).some(value => {
    if (Array.isArray(value)) return value.length > 0
    return value !== undefined && value !== '' && value !== null
  })
})

const filteredCaseList = computed(() => {
  let filtered = [...mockCaseList.value]
  
  // 关键词搜索
  if (searchParams.keyword) {
    const keyword = searchParams.keyword.toLowerCase()
    filtered = filtered.filter(item => 
      item.title.toLowerCase().includes(keyword) ||
      item.summary.toLowerCase().includes(keyword) ||
      item.content.toLowerCase().includes(keyword)
    )
  }
  
  // 分类筛选
  if (searchParams.category) {
    filtered = filtered.filter(item => item.category === searchParams.category)
  }
  
  // 标签筛选
  if (searchParams.tags && searchParams.tags.length > 0) {
    filtered = filtered.filter(item => 
      searchParams.tags.some(tagId => item.tags.includes(tagId))
    )
  }
  
  // 难度筛选
  if (searchParams.difficulty) {
    filtered = filtered.filter(item => item.difficulty === searchParams.difficulty)
  }
  
  // 状态筛选
  if (searchParams.status) {
    filtered = filtered.filter(item => item.status === searchParams.status)
  }
  
  // 来源筛选
  if (searchParams.source) {
    filtered = filtered.filter(item => 
      item.source.toLowerCase().includes(searchParams.source.toLowerCase())
    )
  }
  
  // 浏览量范围筛选
  if (searchParams.viewCountMin !== undefined) {
    filtered = filtered.filter(item => (item.viewCount || 0) >= searchParams.viewCountMin!)
  }
  if (searchParams.viewCountMax !== undefined) {
    filtered = filtered.filter(item => (item.viewCount || 0) <= searchParams.viewCountMax!)
  }
  
  // 排序
  filtered.sort((a, b) => {
    let aValue: any, bValue: any
    
    switch (sortBy.value) {
      case 'title':
        aValue = a.title
        bValue = b.title
        break
      case 'publishTime':
        aValue = a.publishTime || a.createTime
        bValue = b.publishTime || b.createTime
        break
      case 'viewCount':
        aValue = a.viewCount || 0
        bValue = b.viewCount || 0
        break
      default:
        aValue = a.createTime
        bValue = b.createTime
    }
    
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })
  
  return filtered
})

const paginatedCaseList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCaseList.value.slice(start, end)
})

// 工具函数
function getCategoryText(categoryId?: number): string {
  const categoryMap: Record<number, string> = {
    1: '党建工作',
    2: '组织建设', 
    3: '思想教育',
    4: '制度建设',
    5: '作风建设'
  }
  return categoryId ? categoryMap[categoryId] || '未知' : '未分类'
}

function getTagsDisplay(tagIds: number[]) {
  const tagMap: Record<number, { id: number, name: string, color: string }> = {
    1: { id: 1, name: '创新实践', color: 'blue' },
    2: { id: 2, name: '服务民生', color: 'green' },
    3: { id: 3, name: '基层治理', color: 'orange' },
    4: { id: 4, name: '乡村振兴', color: 'purple' },
    5: { id: 5, name: '数字化', color: 'cyan' }
  }
  return tagIds.slice(0, 3).map(id => tagMap[id]).filter(Boolean)
}

function getDifficultyText(difficulty?: number): string {
  return difficulty ? DifficultyLevelMap[difficulty as keyof typeof DifficultyLevelMap] || '未知' : '简单'
}

function getDifficultyColor(difficulty?: number): string {
  return difficulty ? DifficultyLevelColorMap[difficulty as keyof typeof DifficultyLevelColorMap] || 'default' : 'green'
}

function getStatusText(status?: number): string {
  return status ? CaseStatusMap[status as keyof typeof CaseStatusMap] || '未知' : '草稿'
}

function getStatusColor(status?: number): string {
  return status ? CaseStatusColorMap[status as keyof typeof CaseStatusColorMap] || 'default' : 'orange'
}

function formatTime(time?: string): string {
  return time ? dayjs(time).format('YYYY-MM-DD') : ''
}

// 事件处理函数
function navigateToCreate() {
  router.push('/case-promotion/edit')
}


function handleExport() {
  message.info('批量导出功能开发中...')
}

function refreshData() {
  loading.value = true
  setTimeout(() => {
    loadMockData()
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

function handleSearch(params: any) {
  Object.assign(searchParams, params)
  currentPage.value = 1 // 重置到第一页
}

function handleSearchReset() {
  Object.keys(searchParams).forEach(key => {
    if (key === 'tags') {
      searchParams[key as keyof typeof searchParams] = [] as any
    } else {
      searchParams[key as keyof typeof searchParams] = undefined as any
    }
  })
  currentPage.value = 1
}

function handleSortChange() {
  // 排序变化时重新计算，computed会自动响应
}

function handleView(caseData: CaseEntity) {
  router.push(`/case-promotion/detail/${caseData.id}`)
}

function handleEdit(caseData: CaseEntity) {
  router.push(`/case-promotion/edit/${caseData.id}`)
}

function handleDelete(caseData: CaseEntity) {
  // 模拟删除操作
  const index = mockCaseList.value.findIndex(item => item.id === caseData.id)
  if (index > -1) {
    mockCaseList.value.splice(index, 1)
    message.success('案例删除成功')
  }
}

function handlePageChange(page: number) {
  currentPage.value = page
}

function handlePageSizeChange(current: number, size: number) {
  pageSize.value = size
  currentPage.value = 1
}

// 加载模拟数据
function loadMockData() {
  // 生成模拟案例数据
  const mockData: CaseEntity[] = []
  for (let i = 1; i <= 50; i++) {
    mockData.push({
      id: i,
      title: `优秀党建案例${i}：${['数字化党建平台建设', '基层组织建设创新', '党员教育管理', '乡村振兴实践', '社区治理创新'][i % 5]}`,
      summary: `这是第${i}个案例的摘要描述，展示了党建工作的创新实践和显著成效...`,
      content: `详细内容${i}...`,
      source: ['市委组织部', '县委组织部', '街道党工委', '社区党委', '企业党委'][i % 5],
      category: (i % 5) + 1,
      tags: [((i % 3) + 1), ((i % 2) + 3)],
      difficulty: (i % 3) + 1,
      industry: (i % 5) + 1,
      status: (i % 2) + 1,
      coverImage: i % 3 === 0 ? `https://via.placeholder.com/300x200/1890ff/ffffff?text=案例${i}` : undefined,
      images: [],
      attachments: [],
      publishTime: dayjs().subtract(i, 'day').toISOString(),
      createTime: dayjs().subtract(i + 5, 'day').toISOString(),
      updateTime: dayjs().subtract(i, 'day').toISOString(),
      createUser: 1,
      organizationId: 1,
      regionId: 1,
      viewCount: Math.floor(Math.random() * 500) + 10,
      downloadCount: Math.floor(Math.random() * 50)
    })
  }
  mockCaseList.value = mockData
}

// 页面初始化
onMounted(() => {
  loadMockData()
})
</script>

<style lang="scss" scoped>
.case-list-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
      }
    }
  }

  .search-filter-section {
    margin-bottom: 16px;
  }

  .display-control-section {
    margin-bottom: 16px;

    .control-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .display-info {
        display: flex;
        align-items: center;
        color: #666;

        .total-count {
          font-weight: 500;
          color: #1890ff;
        }

        .filter-info {
          font-size: 14px;
        }
      }

      .display-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        span {
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .data-display-section {
    margin-bottom: 16px;

    // 表格模式样式
    .case-title-cell {
      .title-text {
        margin: 0 0 4px 0;
        color: #1890ff;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }

      .summary-text {
        margin: 0;
        color: #999;
        font-size: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .tags-cell {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    // 卡片模式样式
    .card-grid-container {
      background: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .pagination-section {
    .pagination-wrapper {
      display: flex;
      justify-content: center;
      padding: 16px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-list-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }

        .ant-btn {
          flex: 1;
          height: 36px;
        }
      }
    }

    .display-control-section {
      .control-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .display-controls {
          width: 100%;
          justify-content: space-between;
        }
      }
    }

    .data-display-section {
      :deep(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

// 分页样式优化
:deep(.ant-pagination) {
  .ant-pagination-item-active {
    border-color: #1890ff;

    a {
      color: #1890ff;
    }
  }
}
</style>
