<template>
  <div class="case-library-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>案例库管理</h2>
        <p>创建和管理案例库，配置访问权限和用户角色分配</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建案例库
          </a-button>
          <a-button @click="handleBatchOperation" :disabled="selectedRowKeys.length === 0">
            <template #icon><setting-outlined /></template>
            批量操作
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportLibraries">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 案例库统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="案例库总数"
              :value="mockLibraryList.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <database-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="公开案例库"
              :value="getLibraryCountByAccess('public')"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <global-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="私有案例库"
              :value="getLibraryCountByAccess('private')"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <lock-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="案例总数"
              :value="getTotalCaseCount()"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="案例库名称" class="form-item-full">
                <a-input
                  v-model:value="searchForm.name"
                  placeholder="请输入案例库名称"
                  allow-clear
                  @press-enter="handleSearch"
                >
                  <template #prefix><search-outlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="访问级别" class="form-item-full">
                <a-select v-model:value="searchForm.accessLevel" placeholder="请选择访问级别" allow-clear>
                  <a-select-option value="public">公开</a-select-option>
                  <a-select-option value="internal">内部</a-select-option>
                  <a-select-option value="private">私有</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="启用状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">已启用</a-select-option>
                  <a-select-option :value="false">已禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 案例库列表表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>案例库列表</span>
            <span class="record-count">共 {{ filteredLibraryList.length }} 个案例库</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredLibraryList"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1200 }"
          row-key="id"
          :row-selection="rowSelection"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="library-name-cell">
                <a @click="viewLibraryDetail(record)">{{ record.name }}</a>
                <p class="library-description">{{ record.description }}</p>
              </div>
            </template>
            <template v-else-if="column.key === 'accessLevel'">
              <a-tag :color="getAccessLevelColor(record.accessLevel)">
                {{ getAccessLevelText(record.accessLevel) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'caseCount'">
              <a-badge :count="record.caseCount" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>
            <template v-else-if="column.key === 'userCount'">
              <span>{{ record.userCount }} 人</span>
            </template>
            <template v-else-if="column.key === 'isEnabled'">
              <a-switch
                v-model:checked="record.isEnabled"
                @change="toggleLibraryStatus(record)"
                :loading="record.updating"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editLibrary(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="managePermissions(record)">
                  <template #icon><team-outlined /></template>
                  权限管理
                </a-button>
                <a-button type="link" size="small" @click="viewLibraryDetail(record)">
                  <template #icon><eye-outlined /></template>
                  查看详情
                </a-button>
                <a-popconfirm
                  title="确定删除这个案例库吗？删除后库中的案例也会被移除。"
                  @confirm="deleteLibrary(record)"
                >
                  <a-button type="link" size="small" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 案例库表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="案例库名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入案例库名称" />
        </a-form-item>

        <a-form-item label="案例库描述" name="description">
          <a-textarea
            v-model:value="formData.description"
            placeholder="请输入案例库描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="访问级别" name="accessLevel">
          <a-select v-model:value="formData.accessLevel" placeholder="请选择访问级别">
            <a-select-option value="public">
              <div class="access-option">
                <global-outlined />
                <span>公开 - 所有用户可访问</span>
              </div>
            </a-select-option>
            <a-select-option value="internal">
              <div class="access-option">
                <team-outlined />
                <span>内部 - 组织内部用户可访问</span>
              </div>
            </a-select-option>
            <a-select-option value="private">
              <div class="access-option">
                <lock-outlined />
                <span>私有 - 仅指定用户可访问</span>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="案例库类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择案例库类型">
            <a-select-option value="general">通用案例库</a-select-option>
            <a-select-option value="special">专题案例库</a-select-option>
            <a-select-option value="department">部门案例库</a-select-option>
            <a-select-option value="project">项目案例库</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="排序权重" name="sortOrder">
          <a-input-number
            v-model:value="formData.sortOrder"
            placeholder="数值越小排序越靠前"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="启用状态" name="isEnabled">
          <a-switch v-model:checked="formData.isEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ formData.isEnabled ? '启用' : '禁用' }}
          </span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权限管理弹窗 -->
    <a-modal
      v-model:open="permissionModalVisible"
      title="权限管理"
      width="1000px"
      @ok="handlePermissionSubmit"
      @cancel="permissionModalVisible = false"
      :confirm-loading="permissionSubmitting"
    >
      <div class="permission-management" v-if="currentLibrary">
        <div class="permission-header">
          <h3>{{ currentLibrary.name }} - 权限配置</h3>
          <a-tag :color="getAccessLevelColor(currentLibrary.accessLevel)">
            {{ getAccessLevelText(currentLibrary.accessLevel) }}
          </a-tag>
        </div>

        <a-tabs v-model:activeKey="permissionTab">
          <a-tab-pane key="users" tab="用户权限">
            <div class="user-permission-section">
              <div class="section-header">
                <h4>用户分配</h4>
                <a-button type="primary" size="small" @click="showUserSelector">
                  <template #icon><plus-outlined /></template>
                  添加用户
                </a-button>
              </div>

              <a-transfer
                v-model:target-keys="selectedUserKeys"
                :data-source="userDataSource"
                :titles="['可选用户', '已分配用户']"
                :render="item => item.title"
                :list-style="{ width: '300px', height: '300px' }"
                @change="handleUserTransferChange"
              />
            </div>
          </a-tab-pane>

          <a-tab-pane key="roles" tab="角色权限">
            <div class="role-permission-section">
              <div class="section-header">
                <h4>角色配置</h4>
                <a-button type="primary" size="small" @click="showRoleSelector">
                  <template #icon><plus-outlined /></template>
                  添加角色
                </a-button>
              </div>

              <a-table
                :columns="roleColumns"
                :data-source="libraryRoles"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'permissions'">
                    <a-checkbox-group v-model:value="record.permissions">
                      <a-checkbox value="read">查看</a-checkbox>
                      <a-checkbox value="create">创建</a-checkbox>
                      <a-checkbox value="edit">编辑</a-checkbox>
                      <a-checkbox value="delete">删除</a-checkbox>
                      <a-checkbox value="manage">管理</a-checkbox>
                    </a-checkbox-group>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-button type="link" size="small" danger @click="removeRole(record)">
                      移除
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 批量操作弹窗 -->
    <a-modal
      v-model:open="batchModalVisible"
      title="批量操作"
      width="500px"
      @ok="handleBatchSubmit"
      @cancel="batchModalVisible = false"
      :confirm-loading="batchSubmitting"
    >
      <div class="batch-operation-content">
        <p>已选择 {{ selectedRowKeys.length }} 个案例库</p>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="操作类型">
            <a-radio-group v-model:value="batchOperation">
              <a-radio value="enable">批量启用</a-radio>
              <a-radio value="disable">批量禁用</a-radio>
              <a-radio value="changeAccess">批量修改访问级别</a-radio>
              <a-radio value="delete">批量删除</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="batchOperation === 'changeAccess'" label="新访问级别">
            <a-select v-model:value="batchNewAccessLevel" placeholder="请选择新的访问级别">
              <a-select-option value="public">公开</a-select-option>
              <a-select-option value="internal">内部</a-select-option>
              <a-select-option value="private">私有</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import {
  PlusOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  LockOutlined,
  FileTextOutlined,
  SearchOutlined,
  EditOutlined,
  TeamOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 案例库接口定义
interface LibraryItem {
  id: number
  name: string
  description: string
  accessLevel: 'public' | 'internal' | 'private'
  type: string
  caseCount: number
  userCount: number
  sortOrder: number
  isEnabled: boolean
  createTime: string
  updateTime: string
  updating?: boolean
}

// 表单数据接口
interface LibraryForm {
  id?: number
  name: string
  description: string
  accessLevel: 'public' | 'internal' | 'private'
  type: string
  sortOrder: number
  isEnabled: boolean
}

// 用户数据接口
interface UserItem {
  key: string
  title: string
  description: string
}

// 角色数据接口
interface RoleItem {
  id: number
  name: string
  permissions: string[]
}

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const permissionModalVisible = ref(false)
const batchModalVisible = ref(false)
const submitting = ref(false)
const permissionSubmitting = ref(false)
const batchSubmitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const batchOperation = ref('enable')
const batchNewAccessLevel = ref<string>()
const permissionTab = ref('users')
const currentLibrary = ref<LibraryItem | null>(null)

// 表单引用
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  name: '',
  accessLevel: undefined as string | undefined,
  isEnabled: undefined as boolean | undefined
})

// 表单数据
const formData = reactive<LibraryForm>({
  name: '',
  description: '',
  accessLevel: 'public',
  type: 'general',
  sortOrder: 0,
  isEnabled: true
})

// 权限管理相关数据
const selectedUserKeys = ref<string[]>([])
const userDataSource = ref<UserItem[]>([])
const libraryRoles = ref<RoleItem[]>([])

// 模拟案例库数据
const mockLibraryList = ref<LibraryItem[]>([])

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑案例库' : '新建案例库'
})

const filteredLibraryList = computed(() => {
  let filtered = [...mockLibraryList.value]

  // 应用搜索筛选
  if (searchForm.name) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }

  if (searchForm.accessLevel) {
    filtered = filtered.filter(item => item.accessLevel === searchForm.accessLevel)
  }

  if (searchForm.isEnabled !== undefined) {
    filtered = filtered.filter(item => item.isEnabled === searchForm.isEnabled)
  }

  return filtered
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例库名称',
    dataIndex: 'name',
    key: 'name',
    width: 300,
    fixed: 'left'
  },
  {
    title: '访问级别',
    dataIndex: 'accessLevel',
    key: 'accessLevel',
    width: 120,
    align: 'center'
  },
  {
    title: '案例数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 120,
    align: 'center',
    sorter: (a: LibraryItem, b: LibraryItem) => a.caseCount - b.caseCount
  },
  {
    title: '用户数量',
    dataIndex: 'userCount',
    key: 'userCount',
    width: 120,
    align: 'center'
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    key: 'sortOrder',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    fixed: 'right'
  }
]

// 角色表格列定义
const roleColumns = [
  {
    title: '角色名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '权限配置',
    dataIndex: 'permissions',
    key: 'permissions'
  },
  {
    title: '操作',
    key: 'action',
    width: 80
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入案例库名称', trigger: 'blur' },
    { max: 50, message: '案例库名称不能超过50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入案例库描述', trigger: 'blur' },
    { max: 200, message: '描述不能超过200个字符', trigger: 'blur' }
  ],
  accessLevel: [
    { required: true, message: '请选择访问级别', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择案例库类型', trigger: 'change' }
  ]
}

// 工具函数
function getLibraryCountByAccess(accessLevel: string): number {
  return mockLibraryList.value.filter(item => item.accessLevel === accessLevel).length
}

function getTotalCaseCount(): number {
  return mockLibraryList.value.reduce((total, item) => total + item.caseCount, 0)
}

function getAccessLevelText(accessLevel: string): string {
  const accessMap: Record<string, string> = {
    public: '公开',
    internal: '内部',
    private: '私有'
  }
  return accessMap[accessLevel] || '未知'
}

function getAccessLevelColor(accessLevel: string): string {
  const colorMap: Record<string, string> = {
    public: 'green',
    internal: 'blue',
    private: 'orange'
  }
  return colorMap[accessLevel] || 'default'
}

// 事件处理函数
function handleSearch() {
  // 搜索逻辑已在计算属性中实现
}

function resetSearch() {
  Object.assign(searchForm, {
    name: '',
    accessLevel: undefined,
    isEnabled: undefined
  })
}

function showCreateModal() {
  Object.assign(formData, {
    id: undefined,
    name: '',
    description: '',
    accessLevel: 'public',
    type: 'general',
    sortOrder: 0,
    isEnabled: true
  })
  modalVisible.value = true
}

function editLibrary(library: LibraryItem) {
  Object.assign(formData, {
    id: library.id,
    name: library.name,
    description: library.description,
    accessLevel: library.accessLevel,
    type: library.type,
    sortOrder: library.sortOrder,
    isEnabled: library.isEnabled
  })
  modalVisible.value = true
}

function handleSubmit() {
  formRef.value?.validate().then(() => {
    submitting.value = true

    // 模拟提交操作
    setTimeout(() => {
      submitting.value = false
      modalVisible.value = false

      const action = formData.id ? '更新' : '创建'
      message.success(`案例库${action}成功`)

      // 刷新数据
      loadMockData()
    }, 1000)
  })
}

function handleCancel() {
  modalVisible.value = false
}

function toggleLibraryStatus(library: LibraryItem) {
  library.updating = true

  // 模拟状态切换
  setTimeout(() => {
    library.updating = false
    message.success(`案例库${library.isEnabled ? '启用' : '禁用'}成功`)
  }, 500)
}

function managePermissions(library: LibraryItem) {
  currentLibrary.value = library

  // 生成模拟用户数据
  userDataSource.value = Array.from({ length: 20 }, (_, index) => ({
    key: `user_${index + 1}`,
    title: `用户${index + 1}`,
    description: `user${index + 1}@example.com`
  }))

  // 生成模拟角色数据
  libraryRoles.value = [
    { id: 1, name: '管理员', permissions: ['read', 'create', 'edit', 'delete', 'manage'] },
    { id: 2, name: '编辑者', permissions: ['read', 'create', 'edit'] },
    { id: 3, name: '查看者', permissions: ['read'] }
  ]

  // 模拟已分配用户
  selectedUserKeys.value = ['user_1', 'user_2', 'user_3']

  permissionModalVisible.value = true
}

function viewLibraryDetail(library: LibraryItem) {
  message.info(`查看案例库详情：${library.name}`)
}

function deleteLibrary(library: LibraryItem) {
  message.success('案例库删除成功')
  loadMockData()
}

function handleBatchOperation() {
  batchModalVisible.value = true
}

function handleBatchSubmit() {
  batchSubmitting.value = true

  setTimeout(() => {
    batchSubmitting.value = false
    batchModalVisible.value = false

    const operationMap: Record<string, string> = {
      enable: '启用',
      disable: '禁用',
      changeAccess: '修改访问级别',
      delete: '删除'
    }

    message.success(`批量${operationMap[batchOperation.value]}成功`)
    selectedRowKeys.value = []
    loadMockData()
  }, 1000)
}

function refreshData() {
  loading.value = true
  setTimeout(() => {
    loadMockData()
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

function exportLibraries() {
  message.info('导出功能开发中...')
}

function showUserSelector() {
  message.info('添加用户功能开发中...')
}

function showRoleSelector() {
  message.info('添加角色功能开发中...')
}

function handleUserTransferChange(targetKeys: string[]) {
  selectedUserKeys.value = targetKeys
}

function removeRole(role: RoleItem) {
  const index = libraryRoles.value.findIndex(item => item.id === role.id)
  if (index > -1) {
    libraryRoles.value.splice(index, 1)
    message.success('角色移除成功')
  }
}

function handlePermissionSubmit() {
  permissionSubmitting.value = true

  setTimeout(() => {
    permissionSubmitting.value = false
    permissionModalVisible.value = false
    message.success('权限配置保存成功')
  }, 1000)
}

// 加载模拟数据
function loadMockData() {
  // 生成模拟案例库数据
  const mockData: LibraryItem[] = []
  const libraryNames = [
    '党建工作案例库', '组织建设案例库', '思想教育案例库', '制度建设案例库', '作风建设案例库',
    '创新实践案例库', '服务民生案例库', '基层治理案例库', '乡村振兴案例库', '数字化案例库'
  ]

  const accessLevels: ('public' | 'internal' | 'private')[] = ['public', 'internal', 'private']
  const types = ['general', 'special', 'department', 'project']

  libraryNames.forEach((name, index) => {
    mockData.push({
      id: index + 1,
      name: name,
      description: `${name}的详细描述，包含该库的主要用途和特色功能。`,
      accessLevel: accessLevels[index % 3],
      type: types[index % 4],
      caseCount: Math.floor(Math.random() * 100) + 10,
      userCount: Math.floor(Math.random() * 50) + 5,
      sortOrder: index,
      isEnabled: Math.random() > 0.2,
      createTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toLocaleDateString(),
      updateTime: new Date(Date.now() - index * 12 * 60 * 60 * 1000).toLocaleDateString()
    })
  })

  mockLibraryList.value = mockData
}

// 页面初始化
onMounted(() => {
  loadMockData()
})
</script>

<style lang="scss" scoped>
.case-library-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .search-filter-section,
  .table-section {
    margin-bottom: 16px;

    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filter-section {
    .search-form {
      .form-item-full {
        margin-bottom: 16px;

        :deep(.ant-form-item-label) {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;

        :deep(.ant-form-item-control-input) {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .library-name-cell {
      a {
        color: #1890ff;
        font-weight: 500;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      .library-description {
        margin: 4px 0 0 0;
        color: #999;
        font-size: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  // 表单弹窗样式
  .access-option {
    display: flex;
    align-items: center;
    gap: 8px;

    .anticon {
      color: #1890ff;
    }
  }

  // 权限管理弹窗样式
  .permission-management {
    .permission-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      h3 {
        margin: 0;
        color: #333;
      }
    }

    .user-permission-section,
    .role-permission-section {
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h4 {
          margin: 0;
          color: #333;
        }
      }
    }
  }

  // 批量操作弹窗样式
  .batch-operation-content {
    p {
      margin-bottom: 16px;
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-library-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }

        .ant-btn {
          flex: 1;
          height: 36px;
        }
      }
    }

    .search-filter-section {
      .search-form {
        .search-actions {
          :deep(.ant-form-item-control-input) {
            justify-content: flex-start;
          }
        }
      }
    }

    .table-section {
      :deep(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }

    .permission-management {
      .permission-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }
    }
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

// 统计卡片样式优化
:deep(.ant-statistic) {
  .ant-statistic-title {
    color: #666;
    font-weight: 500;
  }

  .ant-statistic-content {
    .ant-statistic-content-value {
      font-weight: 600;
    }
  }
}

// 表单样式优化
:deep(.ant-form) {
  .ant-form-item-label {
    font-weight: 500;
  }

  .ant-input,
  .ant-select,
  .ant-textarea {
    border-radius: 6px;
  }
}

// 开关样式优化
:deep(.ant-switch) {
  &.ant-switch-checked {
    background-color: #52c41a;
  }
}

// 徽章样式优化
:deep(.ant-badge) {
  .ant-badge-count {
    box-shadow: 0 0 0 1px #fff;
  }
}

// 穿梭框样式优化
:deep(.ant-transfer) {
  .ant-transfer-list {
    border-radius: 6px;
  }

  .ant-transfer-list-header {
    background-color: #fafafa;
    border-radius: 6px 6px 0 0;
  }
}
</style>