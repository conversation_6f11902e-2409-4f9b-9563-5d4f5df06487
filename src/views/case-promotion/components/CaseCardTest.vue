<template>
  <div class="case-card-test">
    <h2>案例卡片组件测试</h2>
    
    <div class="test-section">
      <h3>列表模式</h3>
      <div class="list-container">
        <CaseCard 
          mode="list" 
          :case-data="mockCaseData" 
          :show-actions="true"
          @view="handleView"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </div>
    </div>
    
    <div class="test-section">
      <h3>卡片模式</h3>
      <div class="card-container">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <CaseCard 
              mode="card" 
              :case-data="mockCaseData" 
              :show-actions="true"
              @view="handleView"
              @edit="handleEdit"
              @delete="handleDelete"
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <CaseCard 
              mode="card" 
              :case-data="mockCaseDataNoCover" 
              :show-actions="false"
              @view="handleView"
              @edit="handleEdit"
              @delete="handleDelete"
            />
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import CaseCard from './CaseCard.vue'
import type { CaseEntity } from '@/types/case'

// 模拟数据
const mockCaseData: CaseEntity = {
  id: 1,
  title: '基层党建创新实践案例：数字化党建平台建设',
  summary: '通过建设数字化党建平台，实现党员管理、组织生活、学习教育的全流程数字化，提升基层党建工作效率和质量。',
  content: '详细内容...',
  source: '某市委组织部',
  category: 1,
  tags: [1, 2, 3],
  difficulty: 2,
  industry: 1,
  status: 2,
  coverImage: 'https://via.placeholder.com/300x200/1890ff/ffffff?text=案例封面',
  images: [],
  attachments: [],
  publishTime: '2025-06-15T10:30:00Z',
  createTime: '2025-06-10T09:00:00Z',
  updateTime: '2025-06-15T10:30:00Z',
  createUser: 1,
  organizationId: 1,
  regionId: 1,
  viewCount: 156,
  downloadCount: 23
}

const mockCaseDataNoCover: CaseEntity = {
  ...mockCaseData,
  id: 2,
  title: '农村党支部引领乡村振兴典型案例',
  summary: '发挥农村党支部战斗堡垒作用，带领村民发展特色产业，实现乡村振兴。',
  coverImage: undefined,
  viewCount: 89
}

// 事件处理
function handleView(caseData: CaseEntity) {
  message.info(`查看案例：${caseData.title}`)
}

function handleEdit(caseData: CaseEntity) {
  message.info(`编辑案例：${caseData.title}`)
}

function handleDelete(caseData: CaseEntity) {
  message.warning(`删除案例：${caseData.title}`)
}
</script>

<style lang="scss" scoped>
.case-card-test {
  padding: 24px;
  
  .test-section {
    margin-bottom: 32px;
    
    h3 {
      margin-bottom: 16px;
      color: #1890ff;
    }
  }
  
  .list-container {
    max-width: 800px;
  }
  
  .card-container {
    // 卡片容器样式
  }
}
</style>
