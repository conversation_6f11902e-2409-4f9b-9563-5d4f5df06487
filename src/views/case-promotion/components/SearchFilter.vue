<template>
  <div class="search-filter-wrapper">
    <a-card>
      <a-form :model="searchForm" class="search-form">
        <a-row :gutter="[16, 16]" style="width: 100%">
          <!-- 关键词搜索 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="关键词" class="form-item-full">
              <a-input 
                v-model:value="searchForm.keyword" 
                placeholder="请输入案例标题或内容关键词" 
                allow-clear
                @input="handleKeywordChange"
              >
                <template #prefix>
                  <search-outlined />
                </template>
              </a-input>
            </a-form-item>
          </a-col>

          <!-- 分类筛选 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="分类" class="form-item-full">
              <a-select 
                v-model:value="searchForm.category" 
                placeholder="请选择案例分类" 
                allow-clear
                @change="handleFilterChange"
              >
                <a-select-option v-for="item in categoryOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 标签筛选 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="标签" class="form-item-full">
              <a-select 
                v-model:value="searchForm.tags" 
                mode="multiple"
                placeholder="请选择案例标签" 
                allow-clear
                :max-tag-count="2"
                @change="handleFilterChange"
              >
                <a-select-option v-for="item in tagOptions" :key="item.value" :value="item.value">
                  <a-tag :color="item.color" size="small">{{ item.label }}</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 难度等级筛选 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="难度等级" class="form-item-full">
              <a-select 
                v-model:value="searchForm.difficulty" 
                placeholder="请选择难度等级" 
                allow-clear
                @change="handleFilterChange"
              >
                <a-select-option v-for="item in difficultyOptions" :key="item.value" :value="item.value">
                  <a-tag :color="item.color" size="small">{{ item.label }}</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 发布状态筛选 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="发布状态" class="form-item-full">
              <a-select 
                v-model:value="searchForm.status" 
                placeholder="请选择发布状态" 
                allow-clear
                @change="handleFilterChange"
              >
                <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                  <a-tag :color="item.color" size="small">{{ item.label }}</a-tag>
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 行业筛选 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="行业" class="form-item-full">
              <a-select 
                v-model:value="searchForm.industry" 
                placeholder="请选择行业类型" 
                allow-clear
                @change="handleFilterChange"
              >
                <a-select-option v-for="item in industryOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>

          <!-- 时间范围筛选 -->
          <a-col :xs="24" :sm="24" :md="16" :lg="12">
            <a-form-item label="发布时间" class="form-item-full">
              <a-range-picker 
                v-model:value="searchForm.dateRange" 
                format="YYYY-MM-DD" 
                style="width: 100%"
                :placeholder="['开始日期', '结束日期']"
                @change="handleFilterChange"
              />
            </a-form-item>
          </a-col>

          <!-- 搜索操作按钮 -->
          <a-col :xs="24" :sm="24" :md="8" :lg="12">
            <a-form-item class="form-item-full search-actions">
              <a-space>
                <a-button type="primary" @click="handleSearch" :loading="loading">
                  <template #icon><search-outlined /></template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
                <a-button type="link" @click="toggleAdvanced">
                  <template #icon><filter-outlined /></template>
                  {{ showAdvanced ? '收起' : '高级筛选' }}
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 高级筛选区域 -->
        <div v-show="showAdvanced" class="advanced-filter">
          <a-divider>高级筛选</a-divider>
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="来源单位" class="form-item-full">
                <a-input 
                  v-model:value="searchForm.source" 
                  placeholder="请输入来源单位" 
                  allow-clear
                  @change="handleFilterChange"
                />
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="浏览量范围" class="form-item-full">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.viewCountMin" 
                    placeholder="最小值" 
                    style="width: 50%"
                    :min="0"
                    @change="handleFilterChange"
                  />
                  <a-input-number 
                    v-model:value="searchForm.viewCountMax" 
                    placeholder="最大值" 
                    style="width: 50%"
                    :min="0"
                    @change="handleFilterChange"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="排序方式" class="form-item-full">
                <a-select 
                  v-model:value="searchForm.sortBy" 
                  placeholder="请选择排序方式" 
                  @change="handleFilterChange"
                >
                  <a-select-option value="createTime">创建时间</a-select-option>
                  <a-select-option value="publishTime">发布时间</a-select-option>
                  <a-select-option value="viewCount">浏览量</a-select-option>
                  <a-select-option value="downloadCount">下载量</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="排序顺序" class="form-item-full">
                <a-select 
                  v-model:value="searchForm.sortOrder" 
                  placeholder="请选择排序顺序"
                  @change="handleFilterChange"
                >
                  <a-select-option value="desc">降序</a-select-option>
                  <a-select-option value="asc">升序</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import {
  SearchOutlined,
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons-vue'
import type { Dayjs } from 'dayjs'

// 简单的防抖函数实现
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout | null = null
  return ((...args: any[]) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }) as T
}

// 搜索表单接口
interface SearchForm {
  keyword?: string
  category?: number
  tags?: number[]
  difficulty?: number
  status?: number
  industry?: number
  dateRange?: [Dayjs, Dayjs] | null
  source?: string
  viewCountMin?: number
  viewCountMax?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// Props定义
interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits定义
const emit = defineEmits<{
  search: [searchParams: SearchForm]
  reset: []
}>()

// 响应式数据
const showAdvanced = ref(false)
const searchForm = reactive<SearchForm>({
  keyword: '',
  category: undefined,
  tags: [],
  difficulty: undefined,
  status: undefined,
  industry: undefined,
  dateRange: null,
  source: '',
  viewCountMin: undefined,
  viewCountMax: undefined,
  sortBy: 'createTime',
  sortOrder: 'desc'
})

// 选项数据（模拟数据，实际项目中应该从API获取）
const categoryOptions = ref([
  { label: '党建工作', value: 1 },
  { label: '组织建设', value: 2 },
  { label: '思想教育', value: 3 },
  { label: '制度建设', value: 4 },
  { label: '作风建设', value: 5 }
])

const tagOptions = ref([
  { label: '创新实践', value: 1, color: 'blue' },
  { label: '服务民生', value: 2, color: 'green' },
  { label: '基层治理', value: 3, color: 'orange' },
  { label: '乡村振兴', value: 4, color: 'purple' },
  { label: '数字化', value: 5, color: 'cyan' }
])

const difficultyOptions = ref([
  { label: '简单', value: 1, color: 'green' },
  { label: '中等', value: 2, color: 'orange' },
  { label: '困难', value: 3, color: 'red' }
])

const statusOptions = ref([
  { label: '草稿', value: 1, color: 'orange' },
  { label: '已发布', value: 2, color: 'green' }
])

const industryOptions = ref([
  { label: '政府机关', value: 1 },
  { label: '事业单位', value: 2 },
  { label: '国有企业', value: 3 },
  { label: '民营企业', value: 4 },
  { label: '社会组织', value: 5 }
])

// 防抖搜索处理
const debouncedSearch = debounce(() => {
  emit('search', { ...searchForm })
}, 300)

// 事件处理函数
function handleKeywordChange() {
  debouncedSearch()
}

function handleFilterChange() {
  emit('search', { ...searchForm })
}

function handleSearch() {
  emit('search', { ...searchForm })
}

function handleReset() {
  // 重置表单
  Object.keys(searchForm).forEach(key => {
    if (key === 'tags') {
      searchForm[key as keyof SearchForm] = [] as any
    } else if (key === 'sortBy') {
      searchForm[key as keyof SearchForm] = 'createTime' as any
    } else if (key === 'sortOrder') {
      searchForm[key as keyof SearchForm] = 'desc' as any
    } else {
      searchForm[key as keyof SearchForm] = undefined as any
    }
  })
  
  // 收起高级筛选
  showAdvanced.value = false
  
  // 触发重置事件
  emit('reset')
  emit('search', { ...searchForm })
}

function toggleAdvanced() {
  showAdvanced.value = !showAdvanced.value
}

// 监听表单变化（可选，用于实时搜索）
watch(
  () => searchForm,
  () => {
    // 可以在这里添加实时搜索逻辑
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.search-filter-wrapper {
  margin-bottom: 16px;

  .search-form {
    .form-item-full {
      margin-bottom: 16px;

      :deep(.ant-form-item-label) {
        font-weight: 500;
      }
    }

    .search-actions {
      display: flex;
      align-items: flex-end;

      :deep(.ant-form-item-control-input) {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .advanced-filter {
    margin-top: 16px;
    padding-top: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    padding: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .search-filter-wrapper {
    .search-form {
      .search-actions {
        :deep(.ant-form-item-control-input) {
          justify-content: flex-start;
        }
      }
    }
  }
}
</style>
