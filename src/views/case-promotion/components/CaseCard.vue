<template>
  <div class="case-card-wrapper">
    <!-- 列表模式 -->
    <a-card 
      v-if="mode === 'list'" 
      class="case-card case-card-list" 
      :hoverable="true"
      size="small"
    >
      <div class="list-content">
        <div class="list-main">
          <div class="case-header">
            <h4 class="case-title" @click="handleView">
              {{ caseData.title }}
            </h4>
            <div class="case-meta">
              <a-tag :color="getStatusColor(caseData.status)">
                {{ getStatusText(caseData.status) }}
              </a-tag>
              <a-tag color="blue">
                {{ getDifficultyText(caseData.difficulty) }}
              </a-tag>
            </div>
          </div>
          
          <div class="case-summary">
            {{ caseData.summary }}
          </div>
          
          <div class="case-tags">
            <a-tag 
              v-for="tag in displayTags" 
              :key="tag.id" 
              :color="tag.color"
              size="small"
            >
              {{ tag.name }}
            </a-tag>
          </div>
        </div>
        
        <div class="list-side">
          <div class="case-info">
            <div class="info-item">
              <span class="label">来源：</span>
              <span class="value">{{ caseData.source }}</span>
            </div>
            <div class="info-item">
              <span class="label">发布时间：</span>
              <span class="value">{{ formatTime(caseData.publishTime || caseData.createTime) }}</span>
            </div>
            <div class="info-item" v-if="caseData.viewCount">
              <span class="label">浏览：</span>
              <span class="value">{{ caseData.viewCount }}次</span>
            </div>
          </div>
          
          <div class="case-actions" v-if="showActions">
            <a-space>
              <a-button type="link" size="small" @click="handleView">
                <template #icon><eye-outlined /></template>
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit">
                <template #icon><edit-outlined /></template>
                编辑
              </a-button>
              <a-button type="link" size="small" danger @click="handleDelete">
                <template #icon><delete-outlined /></template>
                删除
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 卡片模式 -->
    <a-card 
      v-else 
      class="case-card case-card-grid" 
      :hoverable="true"
      :cover="caseData.coverImage"
    >
      <template #cover v-if="caseData.coverImage">
        <div class="card-cover">
          <img :src="caseData.coverImage" :alt="caseData.title" />
          <div class="cover-overlay">
            <a-button type="primary" ghost @click="handleView">
              <template #icon><eye-outlined /></template>
              查看详情
            </a-button>
          </div>
        </div>
      </template>
      
      <template #cover v-else>
        <div class="card-cover-placeholder">
          <file-text-outlined />
          <span>暂无封面</span>
        </div>
      </template>

      <div class="card-content">
        <div class="case-header">
          <h4 class="case-title" @click="handleView">
            {{ caseData.title }}
          </h4>
          <div class="case-meta">
            <a-tag :color="getStatusColor(caseData.status)" size="small">
              {{ getStatusText(caseData.status) }}
            </a-tag>
            <a-tag color="blue" size="small">
              {{ getDifficultyText(caseData.difficulty) }}
            </a-tag>
          </div>
        </div>
        
        <div class="case-summary">
          {{ caseData.summary }}
        </div>
        
        <div class="case-tags">
          <a-tag 
            v-for="tag in displayTags" 
            :key="tag.id" 
            :color="tag.color"
            size="small"
          >
            {{ tag.name }}
          </a-tag>
        </div>
        
        <div class="case-footer">
          <div class="case-info">
            <span class="info-item">
              <user-outlined />
              {{ caseData.source }}
            </span>
            <span class="info-item">
              <clock-circle-outlined />
              {{ formatTime(caseData.publishTime || caseData.createTime) }}
            </span>
            <span class="info-item" v-if="caseData.viewCount">
              <eye-outlined />
              {{ caseData.viewCount }}
            </span>
          </div>
          
          <div class="case-actions" v-if="showActions">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit">
                <template #icon><edit-outlined /></template>
              </a-button>
              <a-button type="link" size="small" danger @click="handleDelete">
                <template #icon><delete-outlined /></template>
              </a-button>
            </a-space>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  EyeOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  FileTextOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import type { CaseEntity } from '@/types/case'
import { 
  CaseStatusMap, 
  CaseStatusColorMap, 
  DifficultyLevelMap,
  DifficultyLevelColorMap 
} from '@/types/case'
import dayjs from 'dayjs'

// Props定义
interface Props {
  mode?: 'list' | 'card'
  caseData: CaseEntity
  showActions?: boolean
  maxTags?: number
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'card',
  showActions: true,
  maxTags: 3
})

// Emits定义
const emit = defineEmits<{
  view: [caseData: CaseEntity]
  edit: [caseData: CaseEntity]
  delete: [caseData: CaseEntity]
}>()

// 计算属性
const displayTags = computed(() => {
  // 这里应该根据tags数组获取标签信息，暂时使用模拟数据
  const mockTags = [
    { id: 1, name: '党建工作', color: 'blue' },
    { id: 2, name: '创新实践', color: 'green' },
    { id: 3, name: '服务民生', color: 'orange' }
  ]
  return mockTags.slice(0, props.maxTags)
})

// 工具函数
function getStatusText(status?: number): string {
  return status ? CaseStatusMap[status as keyof typeof CaseStatusMap] || '未知' : '草稿'
}

function getStatusColor(status?: number): string {
  return status ? CaseStatusColorMap[status as keyof typeof CaseStatusColorMap] || 'default' : 'orange'
}

function getDifficultyText(difficulty?: number): string {
  return difficulty ? DifficultyLevelMap[difficulty as keyof typeof DifficultyLevelMap] || '未知' : '简单'
}

function formatTime(time?: string): string {
  return time ? dayjs(time).format('YYYY-MM-DD') : ''
}

// 事件处理
function handleView() {
  emit('view', props.caseData)
}

function handleEdit() {
  emit('edit', props.caseData)
}

function handleDelete() {
  emit('delete', props.caseData)
}
</script>

<style lang="scss" scoped>
.case-card-wrapper {
  height: 100%;
}

.case-card {
  height: 100%;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 列表模式样式
.case-card-list {
  .list-content {
    display: flex;
    gap: 16px;
    
    .list-main {
      flex: 1;
      min-width: 0;
    }
    
    .list-side {
      width: 200px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
  
  .case-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
    
    .case-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
      cursor: pointer;
      flex: 1;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .case-meta {
      margin-left: 12px;
      display: flex;
      gap: 4px;
    }
  }
  
  .case-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .case-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .case-info {
    .info-item {
      display: block;
      font-size: 12px;
      color: #999;
      margin-bottom: 4px;
      
      .label {
        font-weight: 500;
      }
    }
  }
  
  .case-actions {
    margin-top: 8px;
  }
}

// 卡片模式样式
.case-card-grid {
  .card-cover {
    position: relative;
    height: 160px;
    overflow: hidden;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    .cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      img {
        transform: scale(1.05);
      }
      
      .cover-overlay {
        opacity: 1;
      }
    }
  }
  
  .card-cover-placeholder {
    height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #999;
    
    .anticon {
      font-size: 32px;
      margin-bottom: 8px;
    }
  }
  
  .card-content {
    padding: 16px 0 0;
  }
  
  .case-header {
    margin-bottom: 12px;
    
    .case-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1890ff;
      cursor: pointer;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    .case-meta {
      display: flex;
      gap: 4px;
    }
  }
  
  .case-summary {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .case-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 16px;
  }
  
  .case-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .case-info {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #999;
        
        .anticon {
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-card-list {
    .list-content {
      flex-direction: column;
      
      .list-side {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }
    }
    
    .case-info {
      display: flex;
      gap: 12px;
      
      .info-item {
        margin-bottom: 0 !important;
      }
    }
  }
}
</style>
