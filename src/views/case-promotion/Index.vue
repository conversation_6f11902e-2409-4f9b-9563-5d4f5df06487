<template>
  <div class="case-promotion-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-card>
        <div class="welcome-content">
          <h1>案例推广管理系统</h1>
          <p>优秀党建案例的收集、整理、推广和分享平台，支持多维度分类、智能检索和可视化展示</p>
        </div>
      </a-card>
    </div>

    <!-- 数据统计概览 -->
    <div class="statistics-section">
      <a-card title="数据概览">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <a-statistic
              title="总案例数"
              :value="statistics.totalCases"
              suffix="个"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <book-outlined />
              </template>
            </a-statistic>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-statistic
              title="已发布"
              :value="statistics.publishedCases"
              suffix="个"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-statistic
              title="分类数量"
              :value="statistics.categoryCount"
              suffix="个"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <folder-outlined />
              </template>
            </a-statistic>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-statistic
              title="标签数量"
              :value="statistics.tagCount"
              suffix="个"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <tag-outlined />
              </template>
            </a-statistic>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <a-card title="快速操作">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8">
            <a-button type="primary" size="large" block @click="navigateTo('/case-promotion/edit')">
              <template #icon><plus-outlined /></template>
              新建案例
            </a-button>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8">
            <a-button size="large" block @click="navigateTo('/case-promotion/list')">
              <template #icon><eye-outlined /></template>
              浏览案例
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/case-promotion/list')">
            <template #cover>
              <div class="module-icon">
                <unordered-list-outlined />
              </div>
            </template>
            <a-card-meta
              title="案例列表"
              description="浏览和管理所有案例，支持多种展示模式和高级搜索筛选功能"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/case-promotion/library')">
            <template #cover>
              <div class="module-icon">
                <database-outlined />
              </div>
            </template>
            <a-card-meta
              title="案例库管理"
              description="创建和管理案例库，配置访问权限和用户角色分配"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/case-promotion/category')">
            <template #cover>
              <div class="module-icon">
                <folder-open-outlined />
              </div>
            </template>
            <a-card-meta
              title="分类管理"
              description="管理案例分类体系，支持多维度分类和层级结构管理"
            />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/case-promotion/tag')">
            <template #cover>
              <div class="module-icon">
                <tags-outlined />
              </div>
            </template>
            <a-card-meta
              title="标签管理"
              description="创建和管理案例标签，支持标签颜色配置和使用统计"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BookOutlined,
  CheckCircleOutlined,
  FolderOutlined,
  TagOutlined,
  PlusOutlined,
  UploadOutlined,
  EyeOutlined,
  UnorderedListOutlined,
  DatabaseOutlined,
  FolderOpenOutlined,
  TagsOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 统计数据
const statistics = ref({
  totalCases: 156,
  publishedCases: 128,
  categoryCount: 24,
  tagCount: 45
})

// 导航方法
function navigateTo(path: string) {
  router.push(path)
}

// 页面初始化
onMounted(() => {
  console.log('案例推广管理系统已加载')
  // 这里可以添加数据加载逻辑
  loadStatistics()
})

// 加载统计数据
function loadStatistics() {
  // 模拟数据加载
  // 实际项目中这里会调用API获取真实数据
  console.log('统计数据已加载:', statistics.value)
}
</script>

<style lang="scss" scoped>
.case-promotion-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .welcome-section {
    margin-bottom: 32px;

    .welcome-content {
      text-align: center;
      padding: 40px 20px;

      h1 {
        color: #1890ff;
        font-size: 32px;
        margin-bottom: 16px;
      }

      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .statistics-section {
    margin-bottom: 32px;

    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .quick-actions-section {
    margin-bottom: 32px;

    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ant-btn {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .modules-section {
    .module-card {
      height: 100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .module-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px;
        font-size: 48px;
        color: #1890ff;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-promotion-page {
    padding: 16px;

    .welcome-content {
      padding: 24px 16px;

      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .module-icon {
      height: 80px !important;
      font-size: 36px !important;
    }

    .quick-actions-section {
      .ant-btn {
        height: 40px;
        font-size: 14px;
      }
    }
  }
}
</style>
