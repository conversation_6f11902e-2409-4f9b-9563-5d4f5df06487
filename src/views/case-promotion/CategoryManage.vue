<template>
  <div class="category-manage-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>分类管理</h2>
        <p>管理案例分类体系，支持多维度分类和层级结构管理</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建分类
          </a-button>
          <a-button @click="handleBatchOperation" :disabled="selectedRowKeys.length === 0">
            <template #icon><setting-outlined /></template>
            批量操作
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportCategories">
            <template #icon><export-outlined /></template>
            导出分类
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 分类类型标签页 -->
    <div class="category-tabs-section">
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="1" tab="类型分类">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="案例类型分类" 
                  description="按照案例的性质和特点进行分类，如党建工作、组织建设、思想教育等。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="行业分类">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="行业领域分类" 
                  description="按照案例所属的行业领域进行分类，如政府机关、事业单位、国有企业等。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="3" tab="难度分类">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="实施难度分类" 
                  description="按照案例实施的复杂程度和难度进行分类，便于用户选择适合的案例。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="4" tab="解决方案分类">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="解决方案分类" 
                  description="按照案例提供的解决方案类型进行分类，如制度创新、技术应用、管理优化等。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="分类名称" class="form-item-full">
                <a-input v-model:value="searchForm.name" placeholder="请输入分类名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="启用状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">已启用</a-select-option>
                  <a-select-option :value="false">已禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="层级" class="form-item-full">
                <a-select v-model:value="searchForm.level" placeholder="请选择层级" allow-clear>
                  <a-select-option :value="1">一级分类</a-select-option>
                  <a-select-option :value="2">二级分类</a-select-option>
                  <a-select-option :value="3">三级分类</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 分类树形表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>{{ getTabTitle() }}分类列表</span>
            <span class="record-count">共 {{ filteredCategoryList.length }} 个分类</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredCategoryList"
          :loading="loading"
          :pagination="false"
          :scroll="{ x: 1000 }"
          row-key="id"
          :row-selection="rowSelection"
          :default-expand-all-rows="true"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="category-name-cell">
                <span class="category-icon">
                  <folder-outlined v-if="record.children && record.children.length > 0" />
                  <file-outlined v-else />
                </span>
                <span class="category-text">{{ record.name }}</span>
                <a-tag v-if="record.level === 1" color="blue" size="small">一级</a-tag>
                <a-tag v-else-if="record.level === 2" color="green" size="small">二级</a-tag>
                <a-tag v-else-if="record.level === 3" color="orange" size="small">三级</a-tag>
              </div>
            </template>
            <template v-else-if="column.key === 'isEnabled'">
              <a-switch 
                v-model:checked="record.isEnabled" 
                @change="toggleCategoryStatus(record)"
                :loading="record.updating"
              />
            </template>
            <template v-else-if="column.key === 'caseCount'">
              <a-badge :count="record.caseCount" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showCreateModal(record)">
                  <template #icon><plus-outlined /></template>
                  添加子分类
                </a-button>
                <a-button type="link" size="small" @click="editCategory(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="moveCategory(record)">
                  <template #icon><drag-outlined /></template>
                  移动
                </a-button>
                <a-popconfirm 
                  title="确定删除这个分类吗？删除后子分类也会被删除。" 
                  @confirm="deleteCategory(record)"
                >
                  <a-button type="link" size="small" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 分类表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="分类编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入分类编码（英文字母和数字）" />
        </a-form-item>

        <a-form-item label="上级分类" name="parentId">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="parentCategoryOptions"
            placeholder="请选择上级分类（留空为顶级分类）"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>

        <a-form-item label="分类描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入分类描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="排序权重" name="sortOrder">
          <a-input-number 
            v-model:value="formData.sortOrder" 
            placeholder="数值越小排序越靠前"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="启用状态" name="isEnabled">
          <a-switch v-model:checked="formData.isEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ formData.isEnabled ? '启用' : '禁用' }}
          </span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量操作弹窗 -->
    <a-modal
      v-model:open="batchModalVisible"
      title="批量操作"
      width="500px"
      @ok="handleBatchSubmit"
      @cancel="batchModalVisible = false"
      :confirm-loading="batchSubmitting"
    >
      <div class="batch-operation-content">
        <p>已选择 {{ selectedRowKeys.length }} 个分类</p>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="操作类型">
            <a-radio-group v-model:value="batchOperation">
              <a-radio value="enable">批量启用</a-radio>
              <a-radio value="disable">批量禁用</a-radio>
              <a-radio value="delete">批量删除</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import {
  PlusOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined,
  FolderOutlined,
  FileOutlined,
  EditOutlined,
  DragOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 分类接口定义
interface CategoryItem {
  id: number
  name: string
  code: string
  parentId?: number
  level: number
  description?: string
  sortOrder: number
  isEnabled: boolean
  caseCount: number
  children?: CategoryItem[]
  updating?: boolean
}

// 表单数据接口
interface CategoryForm {
  id?: number
  name: string
  code: string
  parentId?: number
  description?: string
  sortOrder: number
  isEnabled: boolean
}

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const batchModalVisible = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const activeTab = ref('1')
const selectedRowKeys = ref<number[]>([])
const batchOperation = ref('enable')

// 表单引用
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  name: '',
  isEnabled: undefined as boolean | undefined,
  level: undefined as number | undefined
})

// 表单数据
const formData = reactive<CategoryForm>({
  name: '',
  code: '',
  parentId: undefined,
  description: '',
  sortOrder: 0,
  isEnabled: true
})

// 模拟分类数据
const mockCategoryList = ref<CategoryItem[]>([])

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑分类' : '新建分类'
})

const filteredCategoryList = computed(() => {
  let filtered = mockCategoryList.value.filter(item => {
    // 根据当前标签页筛选分类类型
    const tabTypeMap: Record<string, number> = {
      '1': 1, // 类型分类
      '2': 2, // 行业分类
      '3': 3, // 难度分类
      '4': 4  // 解决方案分类
    }
    return item.type === tabTypeMap[activeTab.value]
  })

  // 应用搜索筛选
  if (searchForm.name) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }
  
  if (searchForm.isEnabled !== undefined) {
    filtered = filtered.filter(item => item.isEnabled === searchForm.isEnabled)
  }
  
  if (searchForm.level) {
    filtered = filtered.filter(item => item.level === searchForm.level)
  }

  return filtered
})

const parentCategoryOptions = computed(() => {
  // 构建父分类选项树
  const buildTree = (items: CategoryItem[], parentId?: number): any[] => {
    return items
      .filter(item => item.parentId === parentId)
      .map(item => ({
        title: item.name,
        value: item.id,
        key: item.id,
        children: buildTree(items, item.id)
      }))
  }
  
  return buildTree(filteredCategoryList.value)
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 300,
    fixed: 'left'
  },
  {
    title: '分类编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '案例数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 100,
    align: 'center'
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    key: 'sortOrder',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { max: 50, message: '分类名称不能超过50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '编码只能包含字母、数字和下划线', trigger: 'blur' },
    { max: 20, message: '编码不能超过20个字符', trigger: 'blur' }
  ]
}

// 工具函数
function getTabTitle(): string {
  const tabTitleMap: Record<string, string> = {
    '1': '类型',
    '2': '行业',
    '3': '难度',
    '4': '解决方案'
  }
  return tabTitleMap[activeTab.value] || ''
}

// 事件处理函数
function handleTabChange(key: string) {
  activeTab.value = key
  resetSearch()
}

function handleSearch() {
  // 搜索逻辑已在计算属性中实现
}

function resetSearch() {
  Object.assign(searchForm, {
    name: '',
    isEnabled: undefined,
    level: undefined
  })
}

function showCreateModal(parentCategory?: CategoryItem) {
  Object.assign(formData, {
    id: undefined,
    name: '',
    code: '',
    parentId: parentCategory?.id,
    description: '',
    sortOrder: 0,
    isEnabled: true
  })
  modalVisible.value = true
}

function editCategory(category: CategoryItem) {
  Object.assign(formData, {
    id: category.id,
    name: category.name,
    code: category.code,
    parentId: category.parentId,
    description: category.description,
    sortOrder: category.sortOrder,
    isEnabled: category.isEnabled
  })
  modalVisible.value = true
}

function handleSubmit() {
  formRef.value?.validate().then(() => {
    submitting.value = true
    
    // 模拟提交操作
    setTimeout(() => {
      submitting.value = false
      modalVisible.value = false
      
      const action = formData.id ? '更新' : '创建'
      message.success(`分类${action}成功`)
      
      // 刷新数据
      loadMockData()
    }, 1000)
  })
}

function handleCancel() {
  modalVisible.value = false
}

function toggleCategoryStatus(category: CategoryItem) {
  category.updating = true
  
  // 模拟状态切换
  setTimeout(() => {
    category.updating = false
    message.success(`分类${category.isEnabled ? '启用' : '禁用'}成功`)
  }, 500)
}

function moveCategory(category: CategoryItem) {
  message.info('拖拽排序功能开发中...')
}

function deleteCategory(category: CategoryItem) {
  message.success('分类删除成功')
  loadMockData()
}

function handleBatchOperation() {
  batchModalVisible.value = true
}

function handleBatchSubmit() {
  batchSubmitting.value = true
  
  setTimeout(() => {
    batchSubmitting.value = false
    batchModalVisible.value = false
    
    const operationMap: Record<string, string> = {
      enable: '启用',
      disable: '禁用',
      delete: '删除'
    }
    
    message.success(`批量${operationMap[batchOperation.value]}成功`)
    selectedRowKeys.value = []
    loadMockData()
  }, 1000)
}

function refreshData() {
  loading.value = true
  setTimeout(() => {
    loadMockData()
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

function exportCategories() {
  message.info('导出功能开发中...')
}

// 加载模拟数据
function loadMockData() {
  // 生成模拟分类数据
  const mockData: CategoryItem[] = []
  
  // 为每个标签页生成数据
  for (let type = 1; type <= 4; type++) {
    // 一级分类
    for (let i = 1; i <= 3; i++) {
      const parentId = type * 100 + i
      mockData.push({
        id: parentId,
        name: `${getTypeText(type)}分类${i}`,
        code: `TYPE_${type}_${i}`,
        type: type,
        level: 1,
        description: `${getTypeText(type)}分类${i}的描述`,
        sortOrder: i,
        isEnabled: true,
        caseCount: Math.floor(Math.random() * 20) + 5,
        children: []
      })
      
      // 二级分类
      for (let j = 1; j <= 2; j++) {
        const childId = parentId * 10 + j
        const childCategory: CategoryItem = {
          id: childId,
          name: `${getTypeText(type)}子分类${i}-${j}`,
          code: `TYPE_${type}_${i}_${j}`,
          type: type,
          parentId: parentId,
          level: 2,
          description: `${getTypeText(type)}子分类${i}-${j}的描述`,
          sortOrder: j,
          isEnabled: Math.random() > 0.3,
          caseCount: Math.floor(Math.random() * 10) + 1
        }
        
        mockData.push(childCategory)
        
        // 找到父分类并添加子分类
        const parent = mockData.find(item => item.id === parentId)
        if (parent) {
          if (!parent.children) parent.children = []
          parent.children.push(childCategory)
        }
      }
    }
  }
  
  mockCategoryList.value = mockData
}

function getTypeText(type: number): string {
  const typeMap: Record<number, string> = {
    1: '类型',
    2: '行业', 
    3: '难度',
    4: '解决方案'
  }
  return typeMap[type] || '未知'
}

// 页面初始化
onMounted(() => {
  loadMockData()
})
</script>

<style lang="scss" scoped>
.category-manage-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
      }
    }
  }

  .category-tabs-section,
  .search-filter-section,
  .table-section {
    margin-bottom: 16px;

    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .category-tabs-section {
    .tab-content {
      .tab-description {
        margin-bottom: 16px;
      }
    }
  }

  .search-filter-section {
    .search-form {
      .form-item-full {
        margin-bottom: 16px;

        :deep(.ant-form-item-label) {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;

        :deep(.ant-form-item-control-input) {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .category-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .category-icon {
        color: #1890ff;
        font-size: 16px;
      }

      .category-text {
        font-weight: 500;
      }
    }
  }

  .batch-operation-content {
    p {
      margin-bottom: 16px;
      color: #666;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .category-manage-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }

        .ant-btn {
          flex: 1;
          height: 36px;
        }
      }
    }

    .search-filter-section {
      .search-form {
        .search-actions {
          :deep(.ant-form-item-control-input) {
            justify-content: flex-start;
          }
        }
      }
    }

    .table-section {
      :deep(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }

  .ant-table-row-expand-icon {
    color: #1890ff;
  }
}

// 标签页样式优化
:deep(.ant-tabs) {
  .ant-tabs-tab {
    font-weight: 500;
  }

  .ant-tabs-tab-active {
    .ant-tabs-tab-btn {
      color: #1890ff;
    }
  }
}

// 表单样式优化
:deep(.ant-form) {
  .ant-form-item-label {
    font-weight: 500;
  }

  .ant-input,
  .ant-select,
  .ant-textarea {
    border-radius: 6px;
  }
}

// 开关样式优化
:deep(.ant-switch) {
  &.ant-switch-checked {
    background-color: #52c41a;
  }
}

// 徽章样式优化
:deep(.ant-badge) {
  .ant-badge-count {
    box-shadow: 0 0 0 1px #fff;
  }
}
</style>
