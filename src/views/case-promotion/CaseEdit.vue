<template>
  <div class="case-edit-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-nav">
        <a-button @click="goBack" type="text">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <a-divider type="vertical" />
        <h2 class="page-title">{{ isEditMode ? '编辑案例' : '新建案例' }}</h2>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-button @click="handleSaveDraft" :loading="savingDraft">
            <template #icon><save-outlined /></template>
            保存草稿
          </a-button>
          <a-button @click="handlePreview">
            <template #icon><eye-outlined /></template>
            预览
          </a-button>
          <a-button type="primary" @click="handlePublish" :loading="publishing">
            <template #icon><send-outlined /></template>
            {{ isEditMode ? '更新发布' : '发布案例' }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        @finish="onSubmit"
      >
        <!-- 基本信息 -->
        <a-card title="基本信息" class="form-section">
          <a-form-item label="案例标题" name="title" required>
            <a-input 
              v-model:value="formData.title" 
              placeholder="请输入案例标题"
              :maxlength="100"
              show-count
            />
          </a-form-item>

          <a-form-item label="案例摘要" name="summary" required>
            <a-textarea 
              v-model:value="formData.summary" 
              placeholder="请输入案例摘要，简要描述案例的核心内容"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <a-form-item label="案例分类" name="category" required>
            <a-select v-model:value="formData.category" placeholder="请选择案例分类">
              <a-select-option v-for="item in categoryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="相关标签" name="tags">
            <a-select 
              v-model:value="formData.tags" 
              mode="multiple"
              placeholder="请选择相关标签"
              :max-tag-count="5"
            >
              <a-select-option v-for="item in tagOptions" :key="item.value" :value="item.value">
                <a-tag :color="item.color" size="small">{{ item.label }}</a-tag>
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="难度等级" name="difficulty" required>
            <a-select v-model:value="formData.difficulty" placeholder="请选择难度等级">
              <a-select-option v-for="item in difficultyOptions" :key="item.value" :value="item.value">
                <a-tag :color="item.color" size="small">{{ item.label }}</a-tag>
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="行业类型" name="industry">
            <a-select v-model:value="formData.industry" placeholder="请选择行业类型">
              <a-select-option v-for="item in industryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="来源单位" name="source" required>
            <a-input 
              v-model:value="formData.source" 
              placeholder="请输入来源单位名称"
              :maxlength="100"
            />
          </a-form-item>
        </a-card>

        <!-- 案例内容 -->
        <a-card title="案例内容" class="form-section">
          <a-form-item label="详细内容" name="content" required>
            <a-textarea 
              v-model:value="formData.content" 
              placeholder="请输入案例的详细内容，包括背景、做法、成效等"
              :rows="12"
              :maxlength="10000"
              show-count
            />
          </a-form-item>
        </a-card>

        <!-- 媒体文件 -->
        <a-card title="媒体文件" class="form-section">
          <a-form-item label="封面图片" name="coverImage">
            <div class="cover-upload-area">
              <FileUpload
                v-model="coverImageFiles"
                :multiple="false"
                :max-size="5"
                :max-count="1"
                accept="image/*"
                show-dragger
                @change="handleCoverImageChange"
                @error="handleUploadError"
              />
              <div class="upload-tips">
                <p>建议尺寸：800x400px，支持JPG、PNG格式，文件大小不超过5MB</p>
              </div>
            </div>
          </a-form-item>

          <a-form-item label="相关图片" name="images">
            <div class="images-upload-area">
              <FileUpload
                v-model="imageFiles"
                :multiple="true"
                :max-size="5"
                :max-count="10"
                accept="image/*"
                show-dragger
                @change="handleImagesChange"
                @error="handleUploadError"
              />
              <div class="upload-tips">
                <p>最多上传10张图片，每张图片不超过5MB</p>
              </div>
            </div>
          </a-form-item>

          <a-form-item label="相关附件" name="attachments">
            <div class="attachments-upload-area">
              <FileUpload
                v-model="attachmentFiles"
                :multiple="true"
                :max-size="50"
                :max-count="5"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                show-dragger
                @change="handleAttachmentsChange"
                @error="handleUploadError"
              />
              <div class="upload-tips">
                <p>支持PDF、Word、Excel、PPT格式，最多5个文件，每个文件不超过50MB</p>
              </div>
            </div>
          </a-form-item>
        </a-card>

        <!-- 发布设置 -->
        <a-card title="发布设置" class="form-section">
          <a-form-item label="发布状态" name="status">
            <a-radio-group v-model:value="formData.status">
              <a-radio :value="1">保存为草稿</a-radio>
              <a-radio :value="2">立即发布</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="发布时间" name="publishTime" v-if="formData.status === 2">
            <a-date-picker
              v-model:value="publishTime"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择发布时间（留空为立即发布）"
              style="width: 100%"
            />
          </a-form-item>
        </a-card>

        <!-- 表单操作按钮 -->
        <div class="form-actions">
          <a-space size="large">
            <a-button size="large" @click="goBack">取消</a-button>
            <a-button size="large" @click="handleSaveDraft" :loading="savingDraft">
              保存草稿
            </a-button>
            <a-button type="primary" size="large" html-type="submit" :loading="publishing">
              {{ isEditMode ? '更新案例' : '发布案例' }}
            </a-button>
          </a-space>
        </div>
      </a-form>
    </div>

    <!-- 预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="案例预览"
      width="80%"
      :footer="null"
      class="preview-modal"
    >
      <div class="preview-content">
        <div class="preview-header">
          <h1>{{ formData.title }}</h1>
          <div class="preview-meta">
            <a-space wrap>
              <a-tag :color="getStatusColor(formData.status)">
                {{ getStatusText(formData.status) }}
              </a-tag>
              <a-tag :color="getDifficultyColor(formData.difficulty)">
                {{ getDifficultyText(formData.difficulty) }}
              </a-tag>
              <span>来源：{{ formData.source }}</span>
            </a-space>
          </div>
        </div>
        
        <div class="preview-summary">
          <h3>案例摘要</h3>
          <p>{{ formData.summary }}</p>
        </div>
        
        <div class="preview-content-text">
          <h3>详细内容</h3>
          <div class="content-text" v-html="formatContent(formData.content)"></div>
        </div>
        
        <div class="preview-tags" v-if="formData.tags && formData.tags.length > 0">
          <h3>相关标签</h3>
          <a-space wrap>
            <a-tag v-for="tagId in formData.tags" :key="tagId" :color="getTagColor(tagId)">
              {{ getTagText(tagId) }}
            </a-tag>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  EyeOutlined,
  SendOutlined
} from '@ant-design/icons-vue'
import FileUpload from '@/components/file-upload/index.vue'
import type { CaseForm } from '@/types/case'
import {
  CaseStatusMap,
  CaseStatusColorMap,
  DifficultyLevelMap,
  DifficultyLevelColorMap
} from '@/types/case'

const route = useRoute()
const router = useRouter()

// 表单引用
const formRef = ref<FormInstance>()

// 响应式数据
const loading = ref(false)
const savingDraft = ref(false)
const publishing = ref(false)
const previewVisible = ref(false)

// 判断是否为编辑模式
const isEditMode = computed(() => !!route.params.id)

// 发布时间
const publishTime = ref<Dayjs | null>(null)

// 文件上传相关
const coverImageFiles = ref<any[]>([])
const imageFiles = ref<any[]>([])
const attachmentFiles = ref<any[]>([])

// 表单数据
const formData = reactive<CaseForm>({
  title: '',
  summary: '',
  content: '',
  source: '',
  category: undefined,
  tags: [],
  difficulty: undefined,
  industry: undefined,
  status: 1,
  coverImage: '',
  images: [],
  attachments: []
})

// 选项数据
const categoryOptions = ref([
  { label: '党建工作', value: 1 },
  { label: '组织建设', value: 2 },
  { label: '思想教育', value: 3 },
  { label: '制度建设', value: 4 },
  { label: '作风建设', value: 5 }
])

const tagOptions = ref([
  { label: '创新实践', value: 1, color: 'blue' },
  { label: '服务民生', value: 2, color: 'green' },
  { label: '基层治理', value: 3, color: 'orange' },
  { label: '乡村振兴', value: 4, color: 'purple' },
  { label: '数字化', value: 5, color: 'cyan' }
])

const difficultyOptions = ref([
  { label: '简单', value: 1, color: 'green' },
  { label: '中等', value: 2, color: 'orange' },
  { label: '困难', value: 3, color: 'red' }
])

const industryOptions = ref([
  { label: '政府机关', value: 1 },
  { label: '事业单位', value: 2 },
  { label: '国有企业', value: 3 },
  { label: '民营企业', value: 4 },
  { label: '社会组织', value: 5 }
])

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入案例标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应在5-100个字符之间', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入案例摘要', trigger: 'blur' },
    { min: 10, max: 500, message: '摘要长度应在10-500个字符之间', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入案例内容', trigger: 'blur' },
    { min: 50, max: 10000, message: '内容长度应在50-10000个字符之间', trigger: 'blur' }
  ],
  source: [
    { required: true, message: '请输入来源单位', trigger: 'blur' },
    { max: 100, message: '来源单位名称不能超过100个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择案例分类', trigger: 'change' }
  ],
  difficulty: [
    { required: true, message: '请选择难度等级', trigger: 'change' }
  ]
}

// 工具函数
function getStatusText(status?: number): string {
  return status ? CaseStatusMap[status as keyof typeof CaseStatusMap] || '未知' : '草稿'
}

function getStatusColor(status?: number): string {
  return status ? CaseStatusColorMap[status as keyof typeof CaseStatusColorMap] || 'default' : 'orange'
}

function getDifficultyText(difficulty?: number): string {
  return difficulty ? DifficultyLevelMap[difficulty as keyof typeof DifficultyLevelMap] || '未知' : '简单'
}

function getDifficultyColor(difficulty?: number): string {
  return difficulty ? DifficultyLevelColorMap[difficulty as keyof typeof DifficultyLevelColorMap] || 'default' : 'green'
}

function getTagText(tagId: number): string {
  const tag = tagOptions.value.find(t => t.value === tagId)
  return tag ? tag.label : '未知标签'
}

function getTagColor(tagId: number): string {
  const tag = tagOptions.value.find(t => t.value === tagId)
  return tag ? tag.color : 'default'
}

function formatContent(content: string): string {
  return content.replace(/\n/g, '<br>')
}

// 事件处理函数
function goBack() {
  router.back()
}

function handleCoverImageChange(files: any[]) {
  formData.coverImage = files.length > 0 ? files[0].url || files[0].response?.url : ''
}

function handleImagesChange(files: any[]) {
  formData.images = files.map(file => file.url || file.response?.url).filter(Boolean)
}

function handleAttachmentsChange(files: any[]) {
  formData.attachments = files.map(file => file.url || file.response?.url).filter(Boolean)
}

function handleUploadError(error: any) {
  message.error('文件上传失败：' + error.message)
}

function handleSaveDraft() {
  formData.status = 1
  savingDraft.value = true
  
  // 模拟保存操作
  setTimeout(() => {
    savingDraft.value = false
    message.success('草稿保存成功')
  }, 1000)
}

function handlePreview() {
  if (!formData.title || !formData.summary || !formData.content) {
    message.warning('请先填写基本信息后再预览')
    return
  }
  previewVisible.value = true
}

function handlePublish() {
  formRef.value?.submit()
}

function onSubmit() {
  publishing.value = true
  
  // 处理发布时间
  if (formData.status === 2 && publishTime.value) {
    formData.publishTime = publishTime.value.toISOString()
  }
  
  // 模拟提交操作
  setTimeout(() => {
    publishing.value = false
    const action = isEditMode.value ? '更新' : '发布'
    message.success(`案例${action}成功`)
    
    // 跳转到案例列表
    setTimeout(() => {
      router.push('/case-promotion/list')
    }, 1000)
  }, 1500)
}

// 加载案例数据（编辑模式）
function loadCaseData() {
  if (!isEditMode.value) return
  
  const caseId = route.params.id as string
  loading.value = true
  
  // 模拟数据加载
  setTimeout(() => {
    // 填充表单数据
    Object.assign(formData, {
      title: `案例${caseId}：数字化党建平台建设`,
      summary: '通过建设数字化党建平台，实现党员管理、组织生活、学习教育的全流程数字化。',
      content: '详细的案例内容...',
      source: '市委组织部',
      category: 1,
      tags: [1, 2, 5],
      difficulty: 2,
      industry: 1,
      status: 2
    })
    
    loading.value = false
  }, 1000)
}

// 页面初始化
onMounted(() => {
  loadCaseData()
})
</script>

<style lang="scss" scoped>
.case-edit-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-nav {
      display: flex;
      align-items: center;
      gap: 12px;

      .page-title {
        margin: 0;
        color: #1890ff;
        font-size: 20px;
        font-weight: 600;
      }
    }

    .header-actions {
      .ant-btn {
        height: 36px;
      }
    }
  }

  .form-content {
    .form-section {
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.ant-card-head) {
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;

        .ant-card-head-title {
          color: #333;
          font-weight: 600;
        }
      }
    }

    .upload-tips {
      margin-top: 8px;

      p {
        margin: 0;
        color: #999;
        font-size: 12px;
      }
    }

    .cover-upload-area,
    .images-upload-area,
    .attachments-upload-area {
      .upload-tips {
        margin-top: 12px;
        padding: 8px 12px;
        background-color: #f6f8ff;
        border: 1px solid #d9e5ff;
        border-radius: 4px;
      }
    }

    .form-actions {
      display: flex;
      justify-content: center;
      padding: 32px 0;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 预览弹窗样式
.preview-modal {
  :deep(.ant-modal-body) {
    max-height: 70vh;
    overflow-y: auto;
  }

  .preview-content {
    .preview-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      h1 {
        margin: 0 0 12px 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      .preview-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #666;
      }
    }

    .preview-summary,
    .preview-content-text,
    .preview-tags {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 16px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
        line-height: 1.6;
      }
    }

    .preview-content-text {
      .content-text {
        color: #333;
        line-height: 1.8;

        :deep(br) {
          margin-bottom: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-edit-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;
      padding: 16px;

      .header-nav {
        width: 100%;
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }

        .ant-btn {
          flex: 1;
          height: 36px;
        }
      }
    }

    .form-content {
      :deep(.ant-form) {
        .ant-form-item {
          .ant-form-item-label {
            text-align: left;
          }
        }
      }

      .form-actions {
        .ant-space {
          width: 100%;
          justify-content: space-between;

          .ant-btn {
            flex: 1;
          }
        }
      }
    }
  }

  .preview-modal {
    :deep(.ant-modal) {
      width: 95% !important;
      margin: 10px;
    }

    .preview-content {
      .preview-header {
        h1 {
          font-size: 20px !important;
        }
      }
    }
  }
}

// 表单样式优化
:deep(.ant-form) {
  .ant-form-item-label {
    font-weight: 500;
  }

  .ant-input,
  .ant-select,
  .ant-textarea {
    border-radius: 6px;
  }

  .ant-input:focus,
  .ant-select:focus,
  .ant-textarea:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 文件上传组件样式
:deep(.file-upload-component) {
  .upload-dragger {
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
    transition: border-color 0.3s ease;

    &:hover {
      border-color: #1890ff;
    }
  }

  .file-list {
    margin-top: 16px;

    .file-item {
      border-radius: 6px;
      border: 1px solid #f0f0f0;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }
    }
  }
}
</style>
