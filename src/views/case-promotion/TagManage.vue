<template>
  <div class="tag-manage-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>标签管理</h2>
        <p>创建和管理案例标签，支持标签颜色配置和使用统计</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建标签
          </a-button>
          <a-button @click="handleBatchOperation" :disabled="selectedRowKeys.length === 0">
            <template #icon><setting-outlined /></template>
            批量操作
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportTags">
            <template #icon><export-outlined /></template>
            导出标签
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="6" :lg="6">
              <a-form-item label="标签名称" class="form-item-full">
                <a-input 
                  v-model:value="searchForm.name" 
                  placeholder="请输入标签名称" 
                  allow-clear
                  @press-enter="handleSearch"
                >
                  <template #prefix><search-outlined /></template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="标签类型" class="form-item-full">
                <a-select v-model:value="searchForm.type" placeholder="请选择类型" allow-clear>
                  <a-select-option v-for="item in tagTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="启用状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">已启用</a-select-option>
                  <a-select-option :value="false">已禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="6">
              <a-form-item label="使用次数" class="form-item-full">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.usageCountMin" 
                    placeholder="最小值" 
                    style="width: 50%"
                    :min="0"
                  />
                  <a-input-number 
                    v-model:value="searchForm.usageCountMax" 
                    placeholder="最大值" 
                    style="width: 50%"
                    :min="0"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="24" :lg="4">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 标签列表表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>标签列表</span>
            <span class="record-count">共 {{ filteredTagList.length }} 个标签</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredTagList"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1000 }"
          row-key="id"
          :row-selection="rowSelection"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="tag-name-cell">
                <a-tag :color="record.color" size="large">
                  {{ record.name }}
                </a-tag>
              </div>
            </template>
            <template v-else-if="column.key === 'color'">
              <div class="color-display">
                <div class="color-block" :style="{ backgroundColor: record.color }"></div>
                <span class="color-text">{{ record.color }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag color="blue" size="small">{{ getTagTypeText(record.type) }}</a-tag>
            </template>
            <template v-else-if="column.key === 'usageCount'">
              <a-badge :count="record.usageCount" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>
            <template v-else-if="column.key === 'isEnabled'">
              <a-switch 
                v-model:checked="record.isEnabled" 
                @change="toggleTagStatus(record)"
                :loading="record.updating"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editTag(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="viewTagUsage(record)">
                  <template #icon><bar-chart-outlined /></template>
                  使用统计
                </a-button>
                <a-popconfirm 
                  title="确定删除这个标签吗？删除后相关案例的标签关联也会被移除。" 
                  @confirm="deleteTag(record)"
                >
                  <a-button type="link" size="small" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 标签表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="标签名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入标签名称" />
        </a-form-item>

        <a-form-item label="标签类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择标签类型">
            <a-select-option v-for="item in tagTypeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="标签颜色" name="color">
          <div class="color-picker-container">
            <a-input v-model:value="formData.color" placeholder="请选择或输入颜色值" style="width: 200px;">
              <template #addonAfter>
                <input 
                  type="color" 
                  v-model="formData.color" 
                  class="color-picker-input"
                  @change="handleColorChange"
                />
              </template>
            </a-input>
            <div class="color-presets">
              <span class="preset-label">预设颜色：</span>
              <div 
                v-for="color in presetColors" 
                :key="color"
                class="preset-color"
                :style="{ backgroundColor: color }"
                @click="selectPresetColor(color)"
              ></div>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="标签预览" name="preview">
          <div class="tag-preview">
            <a-tag :color="formData.color" size="large">
              {{ formData.name || '标签预览' }}
            </a-tag>
          </div>
        </a-form-item>

        <a-form-item label="标签描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入标签描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="排序权重" name="sortOrder">
          <a-input-number 
            v-model:value="formData.sortOrder" 
            placeholder="数值越小排序越靠前"
            :min="0"
            :max="9999"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="启用状态" name="isEnabled">
          <a-switch v-model:checked="formData.isEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ formData.isEnabled ? '启用' : '禁用' }}
          </span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量操作弹窗 -->
    <a-modal
      v-model:open="batchModalVisible"
      title="批量操作"
      width="500px"
      @ok="handleBatchSubmit"
      @cancel="batchModalVisible = false"
      :confirm-loading="batchSubmitting"
    >
      <div class="batch-operation-content">
        <p>已选择 {{ selectedRowKeys.length }} 个标签</p>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="操作类型">
            <a-radio-group v-model:value="batchOperation">
              <a-radio value="enable">批量启用</a-radio>
              <a-radio value="disable">批量禁用</a-radio>
              <a-radio value="delete">批量删除</a-radio>
              <a-radio value="changeType">批量修改类型</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="batchOperation === 'changeType'" label="新类型">
            <a-select v-model:value="batchNewType" placeholder="请选择新的标签类型">
              <a-select-option v-for="item in tagTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 使用统计弹窗 -->
    <a-modal
      v-model:open="usageModalVisible"
      title="标签使用统计"
      width="800px"
      :footer="null"
    >
      <div class="usage-statistics" v-if="currentTag">
        <div class="usage-header">
          <a-tag :color="currentTag.color" size="large">{{ currentTag.name }}</a-tag>
          <span class="usage-count">总使用次数：{{ currentTag.usageCount }}</span>
        </div>
        
        <a-divider />
        
        <div class="usage-details">
          <h4>使用详情</h4>
          <a-list
            :data-source="usageDetails"
            :pagination="{ pageSize: 5 }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a @click="navigateToCase(item.caseId)">{{ item.caseTitle }}</a>
                  </template>
                  <template #description>
                    <span>分类：{{ item.categoryName }} | 发布时间：{{ item.publishTime }}</span>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { FormInstance, TableColumnsType } from 'ant-design-vue'
import {
  PlusOutlined,
  SettingOutlined,
  ReloadOutlined,
  ExportOutlined,
  SearchOutlined,
  EditOutlined,
  BarChartOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

// 标签接口定义
interface TagItem {
  id: number
  name: string
  type: number
  color: string
  description?: string
  sortOrder: number
  isEnabled: boolean
  usageCount: number
  createTime: string
  updateTime: string
  updating?: boolean
}

// 表单数据接口
interface TagForm {
  id?: number
  name: string
  type?: number
  color: string
  description?: string
  sortOrder: number
  isEnabled: boolean
}

// 使用详情接口
interface UsageDetail {
  caseId: number
  caseTitle: string
  categoryName: string
  publishTime: string
}

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const batchModalVisible = ref(false)
const usageModalVisible = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const batchOperation = ref('enable')
const batchNewType = ref<number>()
const currentTag = ref<TagItem | null>(null)

// 表单引用
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  name: '',
  type: undefined as number | undefined,
  isEnabled: undefined as boolean | undefined,
  usageCountMin: undefined as number | undefined,
  usageCountMax: undefined as number | undefined
})

// 表单数据
const formData = reactive<TagForm>({
  name: '',
  type: undefined,
  color: '#1890ff',
  description: '',
  sortOrder: 0,
  isEnabled: true
})

// 模拟标签数据
const mockTagList = ref<TagItem[]>([])
const usageDetails = ref<UsageDetail[]>([])

// 标签类型选项
const tagTypeOptions = ref([
  { label: '功能标签', value: 1 },
  { label: '行业标签', value: 2 },
  { label: '特色标签', value: 3 },
  { label: '技术标签', value: 4 }
])

// 预设颜色
const presetColors = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
]

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑标签' : '新建标签'
})

const filteredTagList = computed(() => {
  let filtered = [...mockTagList.value]
  
  // 应用搜索筛选
  if (searchForm.name) {
    filtered = filtered.filter(item => 
      item.name.toLowerCase().includes(searchForm.name.toLowerCase())
    )
  }
  
  if (searchForm.type) {
    filtered = filtered.filter(item => item.type === searchForm.type)
  }
  
  if (searchForm.isEnabled !== undefined) {
    filtered = filtered.filter(item => item.isEnabled === searchForm.isEnabled)
  }
  
  if (searchForm.usageCountMin !== undefined) {
    filtered = filtered.filter(item => item.usageCount >= searchForm.usageCountMin!)
  }
  
  if (searchForm.usageCountMax !== undefined) {
    filtered = filtered.filter(item => item.usageCount <= searchForm.usageCountMax!)
  }
  
  return filtered
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '标签名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '标签颜色',
    dataIndex: 'color',
    key: 'color',
    width: 150,
    align: 'center'
  },
  {
    title: '标签类型',
    dataIndex: 'type',
    key: 'type',
    width: 120,
    align: 'center'
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: 120,
    align: 'center',
    sorter: (a: TagItem, b: TagItem) => a.usageCount - b.usageCount
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    key: 'sortOrder',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 分页配置
const paginationConfig = {
  pageSize: 20,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { max: 20, message: '标签名称不能超过20个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择标签类型', trigger: 'change' }
  ],
  color: [
    { required: true, message: '请选择标签颜色', trigger: 'blur' },
    { pattern: /^#[0-9a-fA-F]{6}$/, message: '请输入有效的颜色值', trigger: 'blur' }
  ]
}

// 工具函数
function getTagTypeText(type: number): string {
  const typeOption = tagTypeOptions.value.find(item => item.value === type)
  return typeOption ? typeOption.label : '未知类型'
}

// 事件处理函数
function handleSearch() {
  // 搜索逻辑已在计算属性中实现
}

function resetSearch() {
  Object.assign(searchForm, {
    name: '',
    type: undefined,
    isEnabled: undefined,
    usageCountMin: undefined,
    usageCountMax: undefined
  })
}

function showCreateModal() {
  Object.assign(formData, {
    id: undefined,
    name: '',
    type: undefined,
    color: '#1890ff',
    description: '',
    sortOrder: 0,
    isEnabled: true
  })
  modalVisible.value = true
}

function editTag(tag: TagItem) {
  Object.assign(formData, {
    id: tag.id,
    name: tag.name,
    type: tag.type,
    color: tag.color,
    description: tag.description,
    sortOrder: tag.sortOrder,
    isEnabled: tag.isEnabled
  })
  modalVisible.value = true
}

function handleSubmit() {
  formRef.value?.validate().then(() => {
    submitting.value = true
    
    // 模拟提交操作
    setTimeout(() => {
      submitting.value = false
      modalVisible.value = false
      
      const action = formData.id ? '更新' : '创建'
      message.success(`标签${action}成功`)
      
      // 刷新数据
      loadMockData()
    }, 1000)
  })
}

function handleCancel() {
  modalVisible.value = false
}

function handleColorChange() {
  // 颜色变化处理
}

function selectPresetColor(color: string) {
  formData.color = color
}

function toggleTagStatus(tag: TagItem) {
  tag.updating = true
  
  // 模拟状态切换
  setTimeout(() => {
    tag.updating = false
    message.success(`标签${tag.isEnabled ? '启用' : '禁用'}成功`)
  }, 500)
}

function viewTagUsage(tag: TagItem) {
  currentTag.value = tag
  
  // 生成模拟使用详情
  usageDetails.value = Array.from({ length: tag.usageCount }, (_, index) => ({
    caseId: index + 1,
    caseTitle: `案例${index + 1}：使用了${tag.name}标签的案例`,
    categoryName: ['党建工作', '组织建设', '思想教育'][index % 3],
    publishTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toLocaleDateString()
  }))
  
  usageModalVisible.value = true
}

function navigateToCase(caseId: number) {
  router.push(`/case-promotion/detail/${caseId}`)
}

function deleteTag(tag: TagItem) {
  message.success('标签删除成功')
  loadMockData()
}

function handleBatchOperation() {
  batchModalVisible.value = true
}

function handleBatchSubmit() {
  batchSubmitting.value = true
  
  setTimeout(() => {
    batchSubmitting.value = false
    batchModalVisible.value = false
    
    const operationMap: Record<string, string> = {
      enable: '启用',
      disable: '禁用',
      delete: '删除',
      changeType: '修改类型'
    }
    
    message.success(`批量${operationMap[batchOperation.value]}成功`)
    selectedRowKeys.value = []
    loadMockData()
  }, 1000)
}

function refreshData() {
  loading.value = true
  setTimeout(() => {
    loadMockData()
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

function exportTags() {
  message.info('导出功能开发中...')
}

// 加载模拟数据
function loadMockData() {
  // 生成模拟标签数据
  const mockData: TagItem[] = []
  const tagNames = [
    '创新实践', '服务民生', '基层治理', '乡村振兴', '数字化',
    '党建引领', '组织建设', '思想教育', '制度创新', '作风建设',
    '科技应用', '管理优化', '文化建设', '人才培养', '社会治理'
  ]
  
  tagNames.forEach((name, index) => {
    mockData.push({
      id: index + 1,
      name: name,
      type: (index % 4) + 1,
      color: presetColors[index % presetColors.length],
      description: `${name}标签的描述信息`,
      sortOrder: index,
      isEnabled: Math.random() > 0.2,
      usageCount: Math.floor(Math.random() * 50) + 1,
      createTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toLocaleDateString(),
      updateTime: new Date(Date.now() - index * 12 * 60 * 60 * 1000).toLocaleDateString()
    })
  })
  
  mockTagList.value = mockData
}

// 页面初始化
onMounted(() => {
  loadMockData()
})
</script>

<style lang="scss" scoped>
.tag-manage-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        height: 40px;
      }
    }
  }

  .search-filter-section,
  .table-section {
    margin-bottom: 16px;

    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filter-section {
    .search-form {
      .form-item-full {
        margin-bottom: 16px;

        :deep(.ant-form-item-label) {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;

        :deep(.ant-form-item-control-input) {
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }

  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .tag-name-cell {
      display: flex;
      align-items: center;
    }

    .color-display {
      display: flex;
      align-items: center;
      gap: 8px;

      .color-block {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
      }

      .color-text {
        font-family: monospace;
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 表单弹窗样式
  .color-picker-container {
    .color-picker-input {
      width: 40px;
      height: 32px;
      border: none;
      cursor: pointer;
    }

    .color-presets {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;

      .preset-label {
        color: #666;
        font-size: 14px;
      }

      .preset-color {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }

  .tag-preview {
    padding: 8px 0;
  }

  // 批量操作弹窗样式
  .batch-operation-content {
    p {
      margin-bottom: 16px;
      color: #666;
    }
  }

  // 使用统计弹窗样式
  .usage-statistics {
    .usage-header {
      display: flex;
      align-items: center;
      gap: 16px;

      .usage-count {
        color: #666;
        font-size: 14px;
      }
    }

    .usage-details {
      h4 {
        margin-bottom: 16px;
        color: #333;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tag-manage-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }

        .ant-btn {
          flex: 1;
          height: 36px;
        }
      }
    }

    .search-filter-section {
      .search-form {
        .search-actions {
          :deep(.ant-form-item-control-input) {
            justify-content: flex-start;
          }
        }
      }
    }

    .table-section {
      :deep(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }

    .color-picker-container {
      .color-presets {
        flex-wrap: wrap;
      }
    }
  }
}

// 表格样式优化
:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

// 表单样式优化
:deep(.ant-form) {
  .ant-form-item-label {
    font-weight: 500;
  }

  .ant-input,
  .ant-select,
  .ant-textarea {
    border-radius: 6px;
  }
}

// 开关样式优化
:deep(.ant-switch) {
  &.ant-switch-checked {
    background-color: #52c41a;
  }
}

// 徽章样式优化
:deep(.ant-badge) {
  .ant-badge-count {
    box-shadow: 0 0 0 1px #fff;
  }
}

// 标签样式优化
:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
}
</style>
