<template>
  <div class="case-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-nav">
        <a-button @click="goBack" type="text">
          <template #icon><arrow-left-outlined /></template>
          返回列表
        </a-button>
        <a-divider type="vertical" />
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/case-promotion">案例推广</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link to="/case-promotion/list">案例列表</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>案例详情</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      
      <div class="header-actions">
        <a-space>
          <a-button @click="handleShare">
            <template #icon><share-alt-outlined /></template>
            分享
          </a-button>
          <a-button @click="handleEdit">
            <template #icon><edit-outlined /></template>
            编辑
          </a-button>
          <a-popconfirm title="确定删除这个案例吗？" @confirm="handleDelete">
            <a-button danger>
              <template #icon><delete-outlined /></template>
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中...">
        <div style="height: 400px;"></div>
      </a-spin>
    </div>

    <!-- 案例不存在 -->
    <div v-else-if="!caseData" class="not-found-container">
      <a-result
        status="404"
        title="案例不存在"
        sub-title="抱歉，您访问的案例不存在或已被删除。"
      >
        <template #extra>
          <a-button type="primary" @click="goBack">返回列表</a-button>
        </template>
      </a-result>
    </div>

    <!-- 案例详情内容 -->
    <div v-else class="case-detail-content">
      <!-- 案例标题和基本信息 -->
      <div class="case-header-section">
        <a-card>
          <div class="case-title-area">
            <h1 class="case-title">{{ caseData.title }}</h1>
            <div class="case-meta">
              <a-space wrap>
                <a-tag :color="getStatusColor(caseData.status)" size="large">
                  {{ getStatusText(caseData.status) }}
                </a-tag>
                <a-tag :color="getDifficultyColor(caseData.difficulty)" size="large">
                  {{ getDifficultyText(caseData.difficulty) }}
                </a-tag>
                <span class="meta-item">
                  <user-outlined />
                  {{ caseData.source }}
                </span>
                <span class="meta-item">
                  <calendar-outlined />
                  {{ formatTime(caseData.publishTime || caseData.createTime) }}
                </span>
                <span class="meta-item">
                  <eye-outlined />
                  {{ caseData.viewCount || 0 }} 次浏览
                </span>
                <span class="meta-item" v-if="caseData.downloadCount">
                  <download-outlined />
                  {{ caseData.downloadCount }} 次下载
                </span>
              </a-space>
            </div>
          </div>
          
          <div class="case-summary">
            <h3>案例摘要</h3>
            <p>{{ caseData.summary }}</p>
          </div>
          
          <div class="case-tags" v-if="displayTags.length > 0">
            <h3>相关标签</h3>
            <a-space wrap>
              <a-tag v-for="tag in displayTags" :key="tag.id" :color="tag.color">
                {{ tag.name }}
              </a-tag>
            </a-space>
          </div>
        </a-card>
      </div>

      <!-- 案例基本信息 -->
      <div class="case-info-section">
        <a-card title="基本信息">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="案例分类">
              <a-tag color="blue">{{ getCategoryText(caseData.category) }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="行业类型">
              {{ getIndustryText(caseData.industry) }}
            </a-descriptions-item>
            <a-descriptions-item label="难度等级">
              <a-tag :color="getDifficultyColor(caseData.difficulty)">
                {{ getDifficultyText(caseData.difficulty) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="发布状态">
              <a-tag :color="getStatusColor(caseData.status)">
                {{ getStatusText(caseData.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="来源单位">
              {{ caseData.source }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatTime(caseData.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="发布时间" v-if="caseData.publishTime">
              {{ formatTime(caseData.publishTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="最后更新">
              {{ formatTime(caseData.updateTime) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <!-- 案例内容 -->
      <div class="case-content-section">
        <a-card title="案例内容">
          <div class="content-text" v-html="formatContent(caseData.content)"></div>
        </a-card>
      </div>

      <!-- 图片展示 -->
      <div v-if="caseData.images && caseData.images.length > 0" class="case-images-section">
        <a-card title="相关图片">
          <div class="images-gallery">
            <a-image-preview-group>
              <div class="image-grid">
                <div v-for="(image, index) in caseData.images" :key="index" class="image-item">
                  <a-image
                    :src="image"
                    :alt="`案例图片${index + 1}`"
                    :preview="{ mask: '预览' }"
                  />
                </div>
              </div>
            </a-image-preview-group>
          </div>
        </a-card>
      </div>

      <!-- 封面图片 -->
      <div v-if="caseData.coverImage" class="case-cover-section">
        <a-card title="封面图片">
          <div class="cover-image-container">
            <a-image
              :src="caseData.coverImage"
              alt="案例封面"
              :preview="{ mask: '预览封面' }"
            />
          </div>
        </a-card>
      </div>

      <!-- 附件列表 -->
      <div v-if="caseData.attachments && caseData.attachments.length > 0" class="case-attachments-section">
        <a-card title="相关附件">
          <div class="attachments-list">
            <div v-for="(attachment, index) in caseData.attachments" :key="index" class="attachment-item">
              <div class="attachment-info">
                <file-outlined class="attachment-icon" />
                <span class="attachment-name">附件{{ index + 1 }}.pdf</span>
                <span class="attachment-size">2.5MB</span>
              </div>
              <div class="attachment-actions">
                <a-button type="link" size="small" @click="handlePreviewAttachment(attachment)">
                  <template #icon><eye-outlined /></template>
                  预览
                </a-button>
                <a-button type="link" size="small" @click="handleDownloadAttachment(attachment)">
                  <template #icon><download-outlined /></template>
                  下载
                </a-button>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 相关案例推荐 -->
      <div class="related-cases-section">
        <a-card title="相关案例推荐">
          <a-row :gutter="[16, 16]">
            <a-col v-for="relatedCase in relatedCases" :key="relatedCase.id" :xs="24" :sm="12" :md="8">
              <div class="related-case-item" @click="navigateToCase(relatedCase.id)">
                <div class="related-case-cover">
                  <img v-if="relatedCase.coverImage" :src="relatedCase.coverImage" :alt="relatedCase.title" />
                  <div v-else class="no-cover">
                    <file-text-outlined />
                  </div>
                </div>
                <div class="related-case-info">
                  <h4 class="related-case-title">{{ relatedCase.title }}</h4>
                  <p class="related-case-summary">{{ relatedCase.summary }}</p>
                  <div class="related-case-meta">
                    <a-tag :color="getStatusColor(relatedCase.status)" size="small">
                      {{ getStatusText(relatedCase.status) }}
                    </a-tag>
                    <span class="view-count">{{ relatedCase.viewCount || 0 }} 浏览</span>
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </div>
    </div>

    <!-- 回到顶部 -->
    <a-back-top :visibility-height="300" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  ShareAltOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  CalendarOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import type { CaseEntity } from '@/types/case'
import {
  CaseStatusMap,
  CaseStatusColorMap,
  DifficultyLevelMap,
  DifficultyLevelColorMap
} from '@/types/case'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const caseData = ref<CaseEntity | null>(null)
const relatedCases = ref<CaseEntity[]>([])

// 计算属性
const displayTags = computed(() => {
  if (!caseData.value?.tags) return []
  
  // 模拟标签数据
  const tagMap: Record<number, { id: number, name: string, color: string }> = {
    1: { id: 1, name: '创新实践', color: 'blue' },
    2: { id: 2, name: '服务民生', color: 'green' },
    3: { id: 3, name: '基层治理', color: 'orange' },
    4: { id: 4, name: '乡村振兴', color: 'purple' },
    5: { id: 5, name: '数字化', color: 'cyan' }
  }
  
  return caseData.value.tags.map(id => tagMap[id]).filter(Boolean)
})

// 工具函数
function getCategoryText(categoryId?: number): string {
  const categoryMap: Record<number, string> = {
    1: '党建工作',
    2: '组织建设',
    3: '思想教育',
    4: '制度建设',
    5: '作风建设'
  }
  return categoryId ? categoryMap[categoryId] || '未知' : '未分类'
}

function getIndustryText(industryId?: number): string {
  const industryMap: Record<number, string> = {
    1: '政府机关',
    2: '事业单位',
    3: '国有企业',
    4: '民营企业',
    5: '社会组织'
  }
  return industryId ? industryMap[industryId] || '未知' : '未知'
}

function getDifficultyText(difficulty?: number): string {
  return difficulty ? DifficultyLevelMap[difficulty as keyof typeof DifficultyLevelMap] || '未知' : '简单'
}

function getDifficultyColor(difficulty?: number): string {
  return difficulty ? DifficultyLevelColorMap[difficulty as keyof typeof DifficultyLevelColorMap] || 'default' : 'green'
}

function getStatusText(status?: number): string {
  return status ? CaseStatusMap[status as keyof typeof CaseStatusMap] || '未知' : '草稿'
}

function getStatusColor(status?: number): string {
  return status ? CaseStatusColorMap[status as keyof typeof CaseStatusColorMap] || 'default' : 'orange'
}

function formatTime(time?: string): string {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : ''
}

function formatContent(content: string): string {
  // 简单的内容格式化，将换行符转换为HTML
  return content.replace(/\n/g, '<br>')
}

// 事件处理函数
function goBack() {
  router.back()
}

function handleShare() {
  message.info('分享功能开发中...')
}

function handleEdit() {
  if (caseData.value) {
    router.push(`/case-promotion/edit/${caseData.value.id}`)
  }
}

function handleDelete() {
  message.success('案例删除成功')
  setTimeout(() => {
    router.push('/case-promotion/list')
  }, 1000)
}

function handlePreviewAttachment(attachment: string) {
  message.info('附件预览功能开发中...')
}

function handleDownloadAttachment(attachment: string) {
  message.info('附件下载功能开发中...')
}

function navigateToCase(caseId: number) {
  router.push(`/case-promotion/detail/${caseId}`)
}

// 加载案例数据
function loadCaseData() {
  const caseId = route.params.id as string
  
  // 模拟数据加载
  setTimeout(() => {
    // 生成模拟案例数据
    const mockCase: CaseEntity = {
      id: parseInt(caseId),
      title: `优秀党建案例${caseId}：数字化党建平台建设与实践`,
      summary: '通过建设数字化党建平台，实现党员管理、组织生活、学习教育的全流程数字化，提升基层党建工作效率和质量，为新时代党建工作提供了有力支撑。',
      content: `一、背景介绍\n\n随着信息技术的快速发展，传统的党建工作模式面临新的挑战。为了适应新时代党建工作的需要，我们积极探索数字化党建新模式。\n\n二、主要做法\n\n1. 建设统一的数字化党建平台\n- 整合党员信息管理系统\n- 开发在线学习教育模块\n- 构建组织生活数字化流程\n\n2. 创新党建工作方式\n- 推行线上线下相结合的组织生活\n- 建立党员积分管理制度\n- 开展网络党课和在线讨论\n\n3. 强化数据分析应用\n- 建立党建工作数据库\n- 开发党建工作分析报告\n- 实现精准化党建管理\n\n三、取得成效\n\n1. 提升了党建工作效率\n2. 增强了党员参与积极性\n3. 实现了党建工作规范化\n4. 为决策提供了数据支撑\n\n四、经验启示\n\n数字化党建是新时代党建工作的重要发展方向，需要在实践中不断完善和创新。`,
      source: '市委组织部',
      category: 1,
      tags: [1, 2, 5],
      difficulty: 2,
      industry: 1,
      status: 2,
      // coverImage: 'https://via.placeholder.com/800x400/1890ff/ffffff?text=数字化党建平台',
      // images: [
      //   'https://via.placeholder.com/600x400/52c41a/ffffff?text=平台界面1',
      //   'https://via.placeholder.com/600x400/faad14/ffffff?text=平台界面2',
      //   'https://via.placeholder.com/600x400/722ed1/ffffff?text=数据分析'
      // ],
      attachments: [
        'attachment1.pdf',
        'attachment2.docx'
      ],
      publishTime: '2025-06-15T10:30:00Z',
      createTime: '2025-06-10T09:00:00Z',
      updateTime: '2025-06-15T10:30:00Z',
      createUser: 1,
      organizationId: 1,
      regionId: 1,
      viewCount: 256,
      downloadCount: 45
    }
    
    caseData.value = mockCase
    
    // 生成相关案例
    relatedCases.value = [
      {
        ...mockCase,
        id: parseInt(caseId) + 1,
        title: '基层党组织建设创新实践',
        summary: '通过创新基层党组织建设模式，提升组织凝聚力和战斗力。',
        viewCount: 189
      },
      {
        ...mockCase,
        id: parseInt(caseId) + 2,
        title: '党员教育管理数字化探索',
        summary: '运用数字化手段创新党员教育管理方式，提升教育效果。',
        viewCount: 167
      },
      {
        ...mockCase,
        id: parseInt(caseId) + 3,
        title: '智慧党建平台应用实践',
        summary: '建设智慧党建平台，实现党建工作智能化管理。',
        viewCount: 203
      }
    ]
    
    loading.value = false
  }, 1000)
}

// 页面初始化
onMounted(() => {
  loadCaseData()
})
</script>

<style lang="scss" scoped>
.case-detail-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: white;
    padding: 16px 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-nav {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-actions {
      .ant-btn {
        height: 36px;
      }
    }
  }

  .loading-container,
  .not-found-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .case-detail-content {
    .case-header-section {
      margin-bottom: 24px;

      .case-title-area {
        margin-bottom: 24px;

        .case-title {
          margin: 0 0 16px 0;
          color: #1890ff;
          font-size: 28px;
          font-weight: 600;
          line-height: 1.3;
        }

        .case-meta {
          .meta-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 14px;

            .anticon {
              color: #999;
            }
          }
        }
      }

      .case-summary {
        margin-bottom: 24px;

        h3 {
          margin: 0 0 12px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #666;
          font-size: 15px;
          line-height: 1.6;
        }
      }

      .case-tags {
        h3 {
          margin: 0 0 12px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }
      }
    }

    .case-info-section,
    .case-content-section,
    .case-images-section,
    .case-cover-section,
    .case-attachments-section,
    .related-cases-section {
      margin-bottom: 24px;

      .ant-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .case-content-section {
      .content-text {
        color: #333;
        font-size: 15px;
        line-height: 1.8;

        :deep(br) {
          margin-bottom: 8px;
        }
      }
    }

    .case-images-section {
      .images-gallery {
        .image-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;

          .image-item {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            :deep(.ant-image) {
              width: 100%;
              height: 150px;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
          }
        }
      }
    }

    .case-cover-section {
      .cover-image-container {
        text-align: center;

        :deep(.ant-image) {
          max-width: 100%;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }

    .case-attachments-section {
      .attachments-list {
        .attachment-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          margin-bottom: 8px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f6f8ff;
          }

          .attachment-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .attachment-icon {
              font-size: 20px;
              color: #1890ff;
            }

            .attachment-name {
              font-weight: 500;
              color: #333;
            }

            .attachment-size {
              color: #999;
              font-size: 12px;
            }
          }

          .attachment-actions {
            display: flex;
            gap: 8px;
          }
        }
      }
    }

    .related-cases-section {
      .related-case-item {
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .related-case-cover {
          height: 120px;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .no-cover {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: #999;
            font-size: 32px;
          }
        }

        .related-case-info {
          padding: 16px;
          flex: 1;
          display: flex;
          flex-direction: column;

          .related-case-title {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
            font-weight: 500;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .related-case-summary {
            margin: 0 0 12px 0;
            color: #666;
            font-size: 12px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            flex: 1;
          }

          .related-case-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .view-count {
              color: #999;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .case-detail-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;
      padding: 16px;

      .header-nav {
        width: 100%;
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: space-between;
        }
      }
    }

    .case-detail-content {
      .case-header-section {
        .case-title {
          font-size: 22px !important;
        }

        .case-meta {
          .ant-space {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      }

      .case-images-section {
        .image-grid {
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        }
      }

      .case-attachments-section {
        .attachment-item {
          flex-direction: column;
          gap: 12px;
          align-items: flex-start;

          .attachment-actions {
            width: 100%;
            justify-content: flex-end;
          }
        }
      }
    }
  }
}

// 描述列表样式优化
:deep(.ant-descriptions) {
  .ant-descriptions-item-label {
    font-weight: 500;
    color: #333;
  }

  .ant-descriptions-item-content {
    color: #666;
  }
}

// 面包屑样式
:deep(.ant-breadcrumb) {
  .ant-breadcrumb-link {
    color: #666;

    &:hover {
      color: #1890ff;
    }
  }
}
</style>
