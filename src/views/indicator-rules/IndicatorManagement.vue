<template>
  <div class="indicator-management">
    <a-card title="指标管理">
      <template #extra>
        <a-button type="primary" @click="showCreateModal">
          <plus-outlined />
          新建指标
        </a-button>
      </template>
      
      <!-- 搜索筛选 -->
      <div class="search-form">
        <a-form layout="inline" @finish="handleSearch">
          <a-form-item>
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="指标名称关键词"
              @pressEnter="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-select v-model:value="searchForm.dataSourceType" placeholder="数据来源类型" style="width: 150px;">
              <a-select-option :value="1">任务数据</a-select-option>
              <a-select-option :value="2">问卷调查数据</a-select-option>
              <a-select-option :value="3">投票数据</a-select-option>
              <a-select-option :value="4">其他数据资源</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-select v-model:value="searchForm.status" placeholder="指标状态" style="width: 120px;">
              <a-select-option :value="1">启用</a-select-option>
              <a-select-option :value="2">停用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button style="margin-left: 8px;" @click="resetSearch">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 指标列表 -->
      <a-table 
        :columns="columns" 
        :data-source="indicators"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dataSourceType'">
            <a-tag :color="getDataSourceColor(record.dataSourceType)">
              {{ getDataSourceText(record.dataSourceType) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'conversionType'">
            {{ getConversionTypeText(record.conversionType) }}
          </template>
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '启用' : '停用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">编辑</a-button>
              <a-button type="link" @click="showDetailModal(record)">详情</a-button>
              <a-popconfirm 
                title="确定删除这个指标吗？" 
                @confirm="handleDelete(record.id)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑指标模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form 
        :model="formData" 
        :label-col="{ span: 6 }" 
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <a-form-item label="指标名称" name="name" :rules="nameValidationRules">
          <a-input v-model:value="formData.name" placeholder="请输入指标名称" @blur="checkNameUnique" />
        </a-form-item>
        
        <a-form-item label="指标描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入指标描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="数据来源类型" name="dataSourceType" :rules="[{ required: true, message: '请选择数据来源类型' }]">
          <a-select v-model:value="formData.dataSourceType" placeholder="请选择数据来源类型">
            <a-select-option :value="1">任务数据</a-select-option>
            <a-select-option :value="2">问卷调查数据</a-select-option>
            <a-select-option :value="3">投票数据</a-select-option>
            <a-select-option :value="4">其他数据资源</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item v-if="formData.dataSourceType === 2" label="问卷标题" name="surveyTitle">
          <a-input v-model:value="formData.surveyTitle" placeholder="请输入问卷标题" />
        </a-form-item>
        
        <a-form-item v-if="formData.dataSourceType === 3" label="投票标题" name="voteTitle">
          <a-input v-model:value="formData.voteTitle" placeholder="请输入投票标题" />
        </a-form-item>
        
        <!-- 数据源详细配置 -->
        <a-form-item label="数据源详细配置" name="dataSourceConfig">
          <DataSourceConfig 
            :data-source-type="formData.dataSourceType" 
            v-model="formData.dataSourceConfig"
            @change="handleDataSourceConfigChange"
          />
        </a-form-item>
        
        <a-form-item label="转换类型" name="conversionType" :rules="[{ required: true, message: '请选择转换类型' }]">
          <a-select v-model:value="formData.conversionType" placeholder="请选择转换类型">
            <a-select-option :value="1">任务完成及时性</a-select-option>
            <a-select-option :value="2">任务评价等次</a-select-option>
            <a-select-option :value="3">排名数据</a-select-option>
            <a-select-option :value="4">活动参与率</a-select-option>
            <a-select-option :value="5">调查问卷结果</a-select-option>
            <a-select-option :value="6">投票结果</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="指标状态" name="status" :rules="[{ required: true, message: '请选择指标状态' }]">
          <a-radio-group v-model:value="formData.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="2">停用</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <!-- 转换规则配置 -->
        <a-form-item label="转换规则配置" name="conversionRules">
          <ConversionRulesConfig 
            :conversion-type="formData.conversionType" 
            v-model="formData.conversionRules"
            @change="handleConversionRulesChange"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 指标详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="指标详情"
      :width="800"
      :footer="null"
    >
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="指标名称">{{ detailData.name }}</a-descriptions-item>
        <a-descriptions-item label="指标状态">
          <a-tag :color="detailData.status === 1 ? 'green' : 'red'">
            {{ detailData.status === 1 ? '启用' : '停用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="数据来源类型">
          <a-tag :color="getDataSourceColor(detailData.dataSourceType)">
            {{ getDataSourceText(detailData.dataSourceType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="转换类型">{{ getConversionTypeText(detailData.conversionType) }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ detailData.createTime }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ detailData.updateTime }}</a-descriptions-item>
        <a-descriptions-item label="指标描述" :span="2">{{ detailData.description || '暂无描述' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { indicatorApi } from '@/api/indicator-rules'
import ConversionRulesConfig from '@/components/indicator-rules/ConversionRulesConfig.vue'
import DataSourceConfig from '@/components/indicator-rules/DataSourceConfig.vue'
import type { Indicator, IndicatorForm, IndicatorQuery } from '@/types/indicator-rules'

// 响应式数据
const loading = ref(false)
const indicators = ref<Indicator[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive<IndicatorQuery>({
  name: '',
  dataSourceType: undefined,
  status: undefined,
  page: 1,
  pageSize: 10
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表单数据
const formData = reactive<IndicatorForm>({
  name: '',
  description: '',
  dataSourceType: 1,
  surveyTitle: '',
  voteTitle: '',
  conversionType: 1,
  conversionRules: {},
  dataSourceConfig: {},
  status: 1
})

// 指标名称唯一性验证
const nameValidationRules = [
  { required: true, message: '请输入指标名称' },
  { validator: validateNameUnique, trigger: 'blur' }
]

const nameCheckLoading = ref(false)
const isNameUnique = ref(true)

// 详情数据
const detailData = ref<Partial<Indicator>>({})

// 表格列配置
const columns = [
  {
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数据来源类型',
    dataIndex: 'dataSourceType',
    key: 'dataSourceType',
  },
  {
    title: '转换类型',
    dataIndex: 'conversionType',
    key: 'conversionType',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]

// 计算属性
const modalTitle = computed(() => isEdit.value ? '编辑指标' : '新建指标')

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.current,
      pageSize: pagination.pageSize
    }
    const response = await indicatorApi.getIndicatorList(params)
    indicators.value = response.data
    pagination.total = response.total
  } catch (error) {
    console.error('加载指标数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    dataSourceType: undefined,
    status: undefined,
    page: 1,
    pageSize: 10
  })
  handleSearch()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  isEdit.value = false
  resetFormData()
  modalVisible.value = true
}

const showEditModal = (record: Indicator) => {
  isEdit.value = true
  Object.assign(formData, record)
  modalVisible.value = true
}

const showDetailModal = (record: Indicator) => {
  detailData.value = record
  detailModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await indicatorApi.updateIndicator(detailData.value.id!, formData)
      message.success('更新指标成功')
    } else {
      await indicatorApi.createIndicator(formData)
      message.success('创建指标成功')
    }
    
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败')
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

const handleDelete = async (id: string) => {
  try {
    await indicatorApi.deleteIndicator(id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    dataSourceType: 1,
    surveyTitle: '',
    voteTitle: '',
    conversionType: 1,
    conversionRules: {},
    dataSourceConfig: {},
    status: 1
  })
  isNameUnique.value = true
}

// 检查指标名称唯一性
const checkNameUnique = async () => {
  if (!formData.name || formData.name.trim() === '') {
    return
  }
  
  try {
    nameCheckLoading.value = true
    const result = await indicatorApi.checkNameUnique({
      name: formData.name.trim(),
      excludeId: isEdit.value ? detailData.value.id : undefined
    })
    isNameUnique.value = result.isUnique
    
    if (!result.isUnique) {
      message.warning(`指标名称"${formData.name}"已存在，请使用其他名称`)
    }
  } catch (error) {
    console.error('检查指标名称唯一性失败:', error)
  } finally {
    nameCheckLoading.value = false
  }
}

// 自定义验证器：指标名称唯一性
async function validateNameUnique(rule: any, value: string) {
  if (!value || value.trim() === '') {
    return Promise.resolve()
  }
  
  // 如果是编辑模式且名称未变化，则跳过验证
  if (isEdit.value && value.trim() === detailData.value.name) {
    return Promise.resolve()
  }
  
  try {
    const result = await indicatorApi.checkNameUnique({
      name: value.trim(),
      excludeId: isEdit.value ? detailData.value.id : undefined
    })
    
    if (!result.isUnique) {
      return Promise.reject(new Error(`指标名称"${value}"已存在，请使用其他名称`))
    }
    
    return Promise.resolve()
  } catch (error) {
    console.error('验证指标名称唯一性失败:', error)
    return Promise.reject(new Error('验证指标名称唯一性失败，请重试'))
  }
}

// 转换规则变化处理
const handleConversionRulesChange = (rules: any) => {
  console.log('转换规则已更新:', rules)
}

// 数据源配置变化处理
const handleDataSourceConfigChange = (config: any) => {
  console.log('数据源配置已更新:', config)
  // 根据数据源配置自动填充相关字段
  if (formData.dataSourceType === 2 && config.surveyId) {
    // 从问卷配置中获取标题
    formData.surveyTitle = getSurveyTitleById(config.surveyId)
  } else if (formData.dataSourceType === 3 && config.voteId) {
    // 从投票配置中获取标题
    formData.voteTitle = getVoteTitleById(config.voteId)
  }
}

// 根据问卷ID获取标题
const getSurveyTitleById = (surveyId: string) => {
  // 这里应该从数据源配置组件获取的问卷列表中查找
  const surveyMap: Record<string, string> = {
    '1': '党建工作满意度调查',
    '2': '干部作风建设评议',
    '3': '组织生活质量评估'
  }
  return surveyMap[surveyId] || ''
}

// 根据投票ID获取标题
const getVoteTitleById = (voteId: string) => {
  // 这里应该从数据源配置组件获取的投票列表中查找
  const voteMap: Record<string, string> = {
    '1': '最佳党支部评选',
    '2': '优秀党员推荐',
    '3': '工作改进建议征集'
  }
  return voteMap[voteId] || ''
}

// 辅助方法
const getDataSourceText = (type: number) => {
  const map: Record<number, string> = {
    1: '任务数据',
    2: '问卷调查数据',
    3: '投票数据',
    4: '其他数据资源'
  }
  return map[type] || '未知'
}

const getDataSourceColor = (type: number) => {
  const map: Record<number, string> = {
    1: 'blue',
    2: 'green',
    3: 'orange',
    4: 'purple'
  }
  return map[type] || 'default'
}

const getConversionTypeText = (type: number) => {
  const map: Record<number, string> = {
    1: '任务完成及时性',
    2: '任务评价等次',
    3: '排名数据',
    4: '活动参与率',
    5: '调查问卷结果',
    6: '投票结果'
  }
  return map[type] || '未知'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.indicator-management {
  padding: 24px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>