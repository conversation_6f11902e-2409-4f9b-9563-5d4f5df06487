<template>
  <div class="calculation-analysis">
    <a-card title="演算分析">
      <a-row :gutter="24">
        <!-- 左侧：演算配置 -->
        <a-col :span="8">
          <a-card title="演算配置" size="small">
            <a-form layout="vertical">
              <a-form-item label="权重方案">
                <a-select v-model:value="selectedScheme" placeholder="选择权重方案">
                  <a-select-option v-for="scheme in weightSchemes" :key="scheme.id" :value="scheme.id">
                    {{ scheme.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item label="分析模型">
                <a-select v-model:value="analysisModel" placeholder="选择分析模型">
                  <a-select-option value="weighted_sum">加权求和</a-select-option>
                  <a-select-option value="hierarchical">层次分析</a-select-option>
                  <a-select-option value="fuzzy">模糊评价</a-select-option>
                </a-select>
              </a-form-item>
              
              <a-form-item>
                <a-button type="primary" block @click="executeCalculation" :loading="calculating">
                  开始演算
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
        
        <!-- 右侧：演算结果 -->
        <a-col :span="16">
          <a-card title="演算结果" size="small">
            <template #extra v-if="calculationResult">
              <a-space>
                <a-button @click="exportResults" size="small">
                  <download-outlined />
                  导出结果
                </a-button>
                <a-button @click="generateReport" size="small" type="primary">
                  <file-text-outlined />
                  生成报告
                </a-button>
              </a-space>
            </template>
            
            <div v-if="calculationResult">
              <!-- 结果概览 -->
              <div class="result-overview" style="margin-bottom: 16px;">
                <a-row :gutter="16">
                  <a-col :span="6">
                    <a-statistic 
                      title="参与组织" 
                      :value="calculationResult.results.length" 
                      suffix="个"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="平均分" 
                      :value="averageScore" 
                      :precision="2"
                      suffix="分"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="最高分" 
                      :value="maxScore" 
                      :precision="2"
                      suffix="分"
                      :value-style="{ color: '#3f8600' }"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-statistic 
                      title="最低分" 
                      :value="minScore" 
                      :precision="2"
                      suffix="分"
                      :value-style="{ color: '#cf1322' }"
                    />
                  </a-col>
                </a-row>
              </div>
              
              <!-- 结果表格 -->
              <a-table 
                :columns="enhancedResultColumns" 
                :data-source="calculationResult.results"
                :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
                row-key="organizationId"
                size="small"
                :scroll="{ x: 1200 }"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'rank'">
                    <a-tag :color="getRankColor(index + 1)">
                      第{{ index + 1 }}名
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'totalScore'">
                    <div class="score-cell">
                      <a-tag :color="getScoreColor(record.totalScore)" size="large">
                        {{ record.totalScore.toFixed(2) }}分
                      </a-tag>
                      <a-progress 
                        :percent="record.totalScore" 
                        size="small" 
                        :show-info="false"
                        :stroke-color="getScoreColor(record.totalScore)"
                      />
                    </div>
                  </template>
                  <template v-else-if="column.key === 'level'">
                    <a-tag :color="getLevelColor(getScoreLevel(record.totalScore))">
                      {{ getScoreLevel(record.totalScore) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="showDetailModal(record)">
                        详细分析
                      </a-button>
                      <a-button type="link" size="small" @click="showTrendModal(record)">
                        趋势图表
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
            <div v-else class="empty-result">
              <a-empty description="暂无演算结果">
                <template #description>
                  <span>请选择权重方案和分析模型，然后点击“开始演算”</span>
                </template>
              </a-empty>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { DownloadOutlined, FileTextOutlined } from '@ant-design/icons-vue'
import { calculationApi, weightApi } from '@/api/indicator-rules'
import type { WeightScheme, CalculationResult } from '@/types/indicator-rules'

// 响应式数据
const weightSchemes = ref<WeightScheme[]>([])
const selectedScheme = ref<string>('')
const analysisModel = ref<string>('weighted_sum')
const calculating = ref(false)
const calculationResult = ref<CalculationResult | null>(null)

// 增强的表格列配置
const enhancedResultColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    fixed: 'left',
  },
  {
    title: '组织名称',
    dataIndex: 'organizationName',
    key: 'organizationName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '总分',
    dataIndex: 'totalScore',
    key: 'totalScore',
    width: 180,
    sorter: (a: any, b: any) => a.totalScore - b.totalScore,
    sortOrder: 'descend',
  },
  {
    title: '评分等级',
    key: 'level',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
]

// 计算统计数据
const averageScore = computed(() => {
  if (!calculationResult.value?.results.length) return 0
  const total = calculationResult.value.results.reduce((sum, item) => sum + item.totalScore, 0)
  return total / calculationResult.value.results.length
})

const maxScore = computed(() => {
  if (!calculationResult.value?.results.length) return 0
  return Math.max(...calculationResult.value.results.map(item => item.totalScore))
})

const minScore = computed(() => {
  if (!calculationResult.value?.results.length) return 0
  return Math.min(...calculationResult.value.results.map(item => item.totalScore))
})

// 方法
const loadData = async () => {
  try {
    const schemes = await weightApi.getWeightSchemes()
    weightSchemes.value = schemes
  } catch (error) {
    console.error('加载权重方案失败:', error)
    message.error('加载数据失败')
  }
}

const executeCalculation = async () => {
  if (!selectedScheme.value) {
    message.warning('请选择权重方案')
    return
  }
  
  try {
    calculating.value = true
    const result = await calculationApi.executeCalculation({
      schemeId: selectedScheme.value,
      selectedIndicators: [],
      weights: {},
      analysisModel: analysisModel.value
    })
    calculationResult.value = result
    message.success('演算完成')
  } catch (error) {
    console.error('演算失败:', error)
    message.error('演算失败')
  } finally {
    calculating.value = false
  }
}

const getScoreColor = (score: number) => {
  if (score >= 95) return 'green'
  if (score >= 85) return 'blue'
  if (score >= 70) return 'orange'
  if (score >= 60) return 'volcano'
  return 'red'
}

const getRankColor = (rank: number) => {
  if (rank === 1) return 'gold'
  if (rank === 2) return 'silver'
  if (rank === 3) return 'bronze'
  if (rank <= 5) return 'blue'
  if (rank <= 10) return 'green'
  return 'default'
}

const getScoreLevel = (score: number) => {
  if (score >= 95) return '优秀'
  if (score >= 85) return '良好'
  if (score >= 70) return '合格'
  if (score >= 60) return '待改进'
  return '不合格'
}

const getLevelColor = (level: string) => {
  const map: Record<string, string> = {
    '优秀': 'green',
    '良好': 'blue',
    '合格': 'cyan',
    '待改进': 'orange',
    '不合格': 'red'
  }
  return map[level] || 'default'
}

// 显示详细分析
const showDetailModal = (record: any) => {
  console.log('查看详细分析:', record)
  message.info('详细分析功能开发中...')
}

// 显示趋势图表
const showTrendModal = (record: any) => {
  console.log('查看趋势图表:', record)
  message.info('趋势图表功能开发中...')
}

// 导出结果
const exportResults = async () => {
  if (!calculationResult.value) {
    message.warning('暂无结果数据可导出')
    return
  }
  
  try {
    // 模拟导出功能
    const csvContent = generateCSV(calculationResult.value)
    downloadFile(csvContent, '演算结果.csv', 'text/csv')
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 生成报告
const generateReport = async () => {
  if (!calculationResult.value) {
    message.warning('暂无结果数据可生成报告')
    return
  }
  
  try {
    message.loading('正在生成决策支持报告...', 2)
    // 模拟报告生成
    setTimeout(() => {
      const reportContent = generateReportContent(calculationResult.value!)
      downloadFile(reportContent, '演算分析报告.html', 'text/html')
      message.success('决策支持报告生成成功')
    }, 2000)
  } catch (error) {
    console.error('生成报告失败:', error)
    message.error('生成报告失败')
  }
}

// 生成CSV内容
const generateCSV = (result: CalculationResult) => {
  const headers = ['排名', '组织名称', '总分', '评分等级']
  const rows = result.results
    .sort((a, b) => b.totalScore - a.totalScore)
    .map((item, index) => [
      index + 1,
      item.organizationName,
      item.totalScore.toFixed(2),
      getScoreLevel(item.totalScore)
    ])
  
  const csvContent = [headers, ...rows]
    .map(row => row.join(','))
    .join('\n')
  
  return '\ufeff' + csvContent // 添加BOM支持中文
}

// 生成报告内容
const generateReportContent = (result: CalculationResult) => {
  const sortedResults = result.results.sort((a, b) => b.totalScore - a.totalScore)
  const excellentCount = sortedResults.filter(r => r.totalScore >= 95).length
  const goodCount = sortedResults.filter(r => r.totalScore >= 85 && r.totalScore < 95).length
  const passCount = sortedResults.filter(r => r.totalScore >= 70 && r.totalScore < 85).length
  const improveCount = sortedResults.filter(r => r.totalScore >= 60 && r.totalScore < 70).length
  const failCount = sortedResults.filter(r => r.totalScore < 60).length
  
  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>演算分析报告</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .header { text-align: center; margin-bottom: 30px; }
    .summary { margin: 20px 0; }
    .chart { margin: 20px 0; }
    .results-table { width: 100%; border-collapse: collapse; }
    .results-table th, .results-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
    .excellent { background-color: #f6ffed; }
    .good { background-color: #e6f7ff; }
    .pass { background-color: #fff7e6; }
    .improve { background-color: #fff1f0; }
  </style>
</head>
<body>
  <div class="header">
    <h1>演算分析报告</h1>
    <p>生成时间：${new Date().toLocaleString()}</p>
  </div>
  
  <div class="summary">
    <h2>统计概览</h2>
    <ul>
      <li>参与组织数：${result.results.length}个</li>
      <li>平均得分：${averageScore.value.toFixed(2)}分</li>
      <li>最高得分：${maxScore.value.toFixed(2)}分</li>
      <li>最低得分：${minScore.value.toFixed(2)}分</li>
    </ul>
    
    <h3>等级分布</h3>
    <ul>
      <li>优秀(95+分)：${excellentCount}个组织</li>
      <li>良好(85-94分)：${goodCount}个组织</li>
      <li>合格(70-84分)：${passCount}个组织</li>
      <li>待改进(60-69分)：${improveCount}个组织</li>
      <li>不合格(<60分)：${failCount}个组织</li>
    </ul>
  </div>
  
  <div class="results">
    <h2>详细结果</h2>
    <table class="results-table">
      <thead>
        <tr>
          <th>排名</th>
          <th>组织名称</th>
          <th>总分</th>
          <th>等级</th>
        </tr>
      </thead>
      <tbody>
        ${sortedResults.map((item, index) => `
        <tr class="${getScoreLevel(item.totalScore).toLowerCase()}">
          <td>${index + 1}</td>
          <td>${item.organizationName}</td>
          <td>${item.totalScore.toFixed(2)}</td>
          <td>${getScoreLevel(item.totalScore)}</td>
        </tr>
        `).join('')}
      </tbody>
    </table>
  </div>
</body>
</html>
  `
}

// 下载文件
const downloadFile = (content: string, filename: string, mimeType: string) => {
  const blob = new Blob([content], { type: mimeType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.click()
  window.URL.revokeObjectURL(url)
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.calculation-analysis {
  padding: 24px;
}

.empty-result {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>