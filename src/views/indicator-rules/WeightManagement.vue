<template>
  <div class="weight-management">
    <a-card title="权重设置">
      <template #extra>
        <a-button type="primary" @click="showCreateModal">
          <plus-outlined />
          新建权重方案
        </a-button>
      </template>

      <!-- 权重方案列表 -->
      <a-table 
        :columns="columns" 
        :data-source="weightSchemes"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'totalWeight'">
            <a-tag :color="record.totalWeight === 100 ? 'green' : 'orange'">
              {{ record.totalWeight }}%
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">编辑</a-button>
              <a-button type="link" @click="showDetailModal(record)">详情</a-button>
              <a-button type="link" @click="applyScheme(record)" :disabled="record.totalWeight !== 100">
                应用方案
              </a-button>
              <a-popconfirm 
                title="确定删除这个方案吗？" 
                @confirm="handleDelete(record.id)"
              >
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑权重方案模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="1000"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form 
        :model="formData" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 20 }"
        ref="formRef"
      >
        <a-form-item label="方案名称" name="name" :rules="[{ required: true, message: '请输入方案名称' }]">
          <a-input v-model:value="formData.name" placeholder="请输入权重方案名称" />
        </a-form-item>
        
        <a-form-item label="方案描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入方案描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="指标权重" required>
          <div class="weight-config">
            <div class="total-weight">
              总权重：
              <a-tag :color="totalWeight === 100 ? 'green' : 'red'">
                {{ totalWeight }}%
              </a-tag>
              <span v-if="totalWeight !== 100" style="color: red; margin-left: 8px;">
                权重总和必须为100%
              </span>
            </div>
            
            <a-table 
              :columns="indicatorColumns" 
              :data-source="indicatorWeights"
              :pagination="false"
              row-key="indicatorId"
              size="small"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'selected'">
                  <a-checkbox 
                    v-model:checked="record.selected" 
                    @change="handleIndicatorSelect(record)"
                  />
                </template>
                <template v-if="column.key === 'weight'">
                  <a-input-number
                    v-model:value="record.weight"
                    :min="0"
                    :max="100"
                    :step="0.1"
                    :precision="1"
                    :disabled="!record.selected"
                    @change="updateTotalWeight"
                    style="width: 100%"
                  />
                </template>
              </template>
            </a-table>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 权重方案详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="权重方案详情"
      :width="800"
      :footer="null"
    >
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="方案名称">{{ detailData.name }}</a-descriptions-item>
        <a-descriptions-item label="总权重">
          <a-tag :color="detailData.totalWeight === 100 ? 'green' : 'orange'">
            {{ detailData.totalWeight }}%
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ detailData.createTime }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ detailData.updateTime }}</a-descriptions-item>
        <a-descriptions-item label="方案描述" :span="2">{{ detailData.description || '暂无描述' }}</a-descriptions-item>
      </a-descriptions>
      
      <a-divider>指标权重分配</a-divider>
      <a-table 
        :columns="detailIndicatorColumns" 
        :data-source="detailData.indicators || []"
        :pagination="false"
        row-key="indicatorId"
        size="small"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { weightApi, indicatorApi } from '@/api/indicator-rules'
import type { WeightScheme, IndicatorWeight, WeightForm } from '@/types/indicator-rules'

// 响应式数据
const loading = ref(false)
const weightSchemes = ref<WeightScheme[]>([])
const indicatorWeights = ref<IndicatorWeight[]>([])
const modalVisible = ref(false)
const detailModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive<WeightForm>({
  name: '',
  description: '',
  selectedIndicators: [],
  weights: {}
})

// 详情数据
const detailData = ref<Partial<WeightScheme>>({})

// 计算总权重
const totalWeight = computed(() => {
  return indicatorWeights.value
    .filter(item => item.selected)
    .reduce((sum, item) => sum + (item.weight || 0), 0)
})

// 表格列配置
const columns = [
  {
    title: '方案名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '总权重',
    dataIndex: 'totalWeight',
    key: 'totalWeight',
    width: 120,
  },
  {
    title: '指标数量',
    key: 'indicatorCount',
    render: (record: WeightScheme) => record.indicators?.length || 0,
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
  },
]

const indicatorColumns = [
  {
    title: '选择',
    key: 'selected',
    width: 60,
  },
  {
    title: '指标名称',
    dataIndex: 'indicatorName',
    key: 'indicatorName',
  },
  {
    title: '权重(%)',
    key: 'weight',
    width: 120,
  },
]

const detailIndicatorColumns = [
  {
    title: '指标名称',
    dataIndex: 'indicatorName',
    key: 'indicatorName',
  },
  {
    title: '权重(%)',
    dataIndex: 'weight',
    key: 'weight',
    width: 120,
  },
]

// 计算属性
const modalTitle = computed(() => isEdit.value ? '编辑权重方案' : '新建权重方案')

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const [schemesResponse, weightsResponse] = await Promise.all([
      weightApi.getWeightSchemes(),
      weightApi.getIndicatorWeights()
    ])
    weightSchemes.value = schemesResponse
    indicatorWeights.value = weightsResponse
  } catch (error) {
    console.error('加载权重数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateModal = () => {
  isEdit.value = false
  resetFormData()
  modalVisible.value = true
}

const showEditModal = (record: WeightScheme) => {
  isEdit.value = true
  Object.assign(formData, {
    name: record.name,
    description: record.description,
    selectedIndicators: record.indicators?.map(i => i.indicatorId) || [],
    weights: record.indicators?.reduce((acc, i) => {
      acc[i.indicatorId] = i.weight
      return acc
    }, {} as Record<string, number>) || {}
  })
  
  // 更新指标选择状态
  indicatorWeights.value.forEach(item => {
    item.selected = formData.selectedIndicators.includes(item.indicatorId)
    item.weight = formData.weights[item.indicatorId] || 0
  })
  
  modalVisible.value = true
}

const showDetailModal = (record: WeightScheme) => {
  detailData.value = record
  detailModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (totalWeight.value !== 100) {
      message.error('权重总和必须为100%')
      return
    }
    
    const selectedIndicators = indicatorWeights.value.filter(item => item.selected)
    const submitData = {
      ...formData,
      selectedIndicators: selectedIndicators.map(i => i.indicatorId),
      weights: selectedIndicators.reduce((acc, i) => {
        acc[i.indicatorId] = i.weight
        return acc
      }, {} as Record<string, number>)
    }
    
    if (isEdit.value) {
      await weightApi.updateWeightScheme(detailData.value.id!, submitData)
      message.success('更新权重方案成功')
    } else {
      await weightApi.createWeightScheme(submitData)
      message.success('创建权重方案成功')
    }
    
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败')
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

const handleDelete = async (id: string) => {
  try {
    await weightApi.deleteWeightScheme(id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

const showApplyModal = (record: WeightScheme) => {
  applySchemeData.value = record
  applyModalVisible.value = true
  resetApplyForm()
}

const handleApplyScheme = async () => {
  if (!applyForm.value.projectId) {
    message.warning('请选择目标项目')
    return
  }
  
  if (applyForm.value.applyScope === 'selected' && applyForm.value.selectedOrganizations.length === 0) {
    message.warning('请选择要应用的组织')
    return
  }
  
  try {
    const applyData = {
      schemeId: applySchemeData.value.id,
      projectId: applyForm.value.projectId,
      applyScope: applyForm.value.applyScope,
      selectedOrganizations: applyForm.value.selectedOrganizations,
      dateRange: applyForm.value.dateRange,
      settings: {
        overwrite: applyForm.value.applySettings.includes('overwrite'),
        backup: applyForm.value.applySettings.includes('backup'),
        immediate: applyForm.value.applySettings.includes('immediate'),
        notify: applyForm.value.applySettings.includes('notify')
      }
    }
    
    await weightApi.applyWeightScheme(applyData)
    message.success(`成功将权重方案"${applySchemeData.value.name}"应用到项目"${selectedProject.value?.name}"`)
    applyModalVisible.value = false
    
    if (applyForm.value.applySettings.includes('immediate')) {
      message.info('权重方案已应用，系统正在后台执行演算，请稍后查看结果')
    }
  } catch (error) {
    console.error('应用方案失败:', error)
    message.error('应用方案失败')
  }
}

const handleApplyCancel = () => {
  applyModalVisible.value = false
  resetApplyForm()
}

const resetApplyForm = () => {
  applyForm.value = {
    projectId: '',
    applyScope: 'all',
    selectedOrganizations: [],
    dateRange: [],
    applySettings: ['backup', 'notify']
  }
}

const filterProjectOption = (input: string, option: any) => {
  return option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const getProjectStatusColor = (status: string) => {
  const map: Record<string, string> = {
    '进行中': 'green',
    '已完成': 'blue',
    '筹备中': 'orange',
    '已暂停': 'red'
  }
  return map[status] || 'default'
}

const disabledDate = (current: any) => {
  // 禁用未来日期
  return current && current > new Date()
}

const handleIndicatorSelect = (record: IndicatorWeight) => {
  if (!record.selected) {
    record.weight = 0
  }
  updateTotalWeight()
}

const updateTotalWeight = () => {
  // 强制更新计算属性
  indicatorWeights.value = [...indicatorWeights.value]
}

const resetFormData = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    selectedIndicators: [],
    weights: {}
  })
  
  // 重置指标选择状态
  indicatorWeights.value.forEach(item => {
    item.selected = false
    item.weight = 0
  })
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.weight-management {
  padding: 24px;
}

.weight-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
}

.total-weight {
  margin-bottom: 16px;
  font-weight: 500;
}
</style>