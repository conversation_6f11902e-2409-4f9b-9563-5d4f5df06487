<template>
  <div class="template-management">
    <a-card title="模板管理">
      <template #extra>
        <a-space>
          <a-button @click="showImportModal">
            <upload-outlined />
            导入模板
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <plus-outlined />
            新建模板
          </a-button>
        </a-space>
      </template>

      <!-- 模板列表 -->
      <a-table 
        :columns="columns" 
        :data-source="templates"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'isDefault'">
            <a-tag :color="record.isDefault ? 'blue' : 'default'">
              {{ record.isDefault ? '默认' : '自定义' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'indicatorCount'">
            {{ record.indicators?.length || 0 }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">编辑</a-button>
              <a-button type="link" @click="applyTemplate(record)">应用</a-button>
              <a-button type="link" @click="exportTemplate(record)">导出</a-button>
              <a-button type="link" @click="duplicateTemplate(record)">复制</a-button>
              <a-popconfirm 
                title="确定删除这个模板吗？" 
                @confirm="handleDelete(record.id)"
                :disabled="record.isDefault"
              >
                <a-button type="link" danger :disabled="record.isDefault">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模板模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="800"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form 
        :model="formData" 
        :label-col="{ span: 6 }" 
        :wrapper-col="{ span: 18 }"
        ref="formRef"
      >
        <a-form-item label="模板名称" name="name" :rules="[{ required: true, message: '请输入模板名称' }]">
          <a-input v-model:value="formData.name" placeholder="请输入模板名称" />
        </a-form-item>
        
        <a-form-item label="模板描述" name="description">
          <a-textarea v-model:value="formData.description" placeholder="请输入模板描述" :rows="3" />
        </a-form-item>
        
        <a-form-item label="模板分类" name="category">
          <a-select v-model:value="formData.category" placeholder="请选择模板分类">
            <a-select-option value="standard">标准模板</a-select-option>
            <a-select-option value="custom">自定义模板</a-select-option>
            <a-select-option value="industry">行业模板</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="默认模板">
          <a-switch v-model:checked="formData.isDefault" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入模板模态框 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入模板"
      @ok="handleImport"
      @cancel="handleImportCancel"
    >
      <a-upload-dragger
        v-model:fileList="fileList"
        :before-upload="beforeUpload"
        accept=".json,.xlsx,.xls"
      >
        <p class="ant-upload-drag-icon">
          <inbox-outlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">支持单个文件上传，支持 JSON、Excel 格式</p>
      </a-upload-dragger>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, UploadOutlined, InboxOutlined } from '@ant-design/icons-vue'
import { templateApi } from '@/api/indicator-rules'
import type { IndicatorTemplate, TemplateForm } from '@/types/indicator-rules'

// 响应式数据
const loading = ref(false)
const templates = ref<IndicatorTemplate[]>([])
const modalVisible = ref(false)
const importModalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const fileList = ref([])

// 表单数据
const formData = ref<TemplateForm>({
  name: '',
  description: '',
  selectedIndicators: [],
  weights: {},
  category: 'custom',
  isDefault: false
})

// 详情数据
const detailData = ref<Partial<IndicatorTemplate>>({})

// 表格列配置
const columns = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '模板分类',
    dataIndex: 'category',
    key: 'category',
  },
  {
    title: '指标数量',
    key: 'indicatorCount',
    width: 100,
  },
  {
    title: '类型',
    key: 'isDefault',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 350,
  },
]

// 计算属性
const modalTitle = computed(() => isEdit.value ? '编辑模板' : '新建模板')

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const response = await templateApi.getTemplateList()
    templates.value = response
  } catch (error) {
    console.error('加载模板数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const showCreateModal = () => {
  isEdit.value = false
  resetFormData()
  modalVisible.value = true
}

const showEditModal = (record: IndicatorTemplate) => {
  isEdit.value = true
  formData.value = {
    name: record.name,
    description: record.description,
    selectedIndicators: record.indicators || [],
    weights: record.weights?.reduce((acc, w) => {
      acc[w.indicatorId] = w.weight
      return acc
    }, {} as Record<string, number>) || {},
    category: record.category,
    isDefault: record.isDefault
  }
  detailData.value = record
  modalVisible.value = true
}

const showImportModal = () => {
  importModalVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await templateApi.updateTemplate(detailData.value.id!, formData.value)
      message.success('更新模板成功')
    } else {
      await templateApi.createTemplate(formData.value)
      message.success('创建模板成功')
    }
    
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败')
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetFormData()
}

const handleDelete = async (id: string) => {
  try {
    await templateApi.deleteTemplate(id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

const applyTemplate = async (record: IndicatorTemplate) => {
  try {
    await templateApi.applyTemplate(record.id)
    message.success('应用模板成功')
  } catch (error) {
    console.error('应用模板失败:', error)
    message.error('应用模板失败')
  }
}

const exportTemplate = async (record: IndicatorTemplate) => {
  try {
    const response = await templateApi.exportTemplate(record.id)
    // 创建下载链接
    const link = document.createElement('a')
    link.href = response.downloadUrl
    link.download = `${record.name}.json`
    link.click()
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

const duplicateTemplate = async (record: IndicatorTemplate) => {
  try {
    await templateApi.duplicateTemplate(record.id, `${record.name}_副本`)
    message.success('复制成功')
    loadData()
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

const beforeUpload = (file: File) => {
  const isValidType = file.type === 'application/json' || 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  
  if (!isValidType) {
    message.error('只能上传 JSON 或 Excel 文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  
  return false // 阻止自动上传
}

const handleImport = async () => {
  if (fileList.value.length === 0) {
    message.warning('请选择要导入的文件')
    return
  }
  
  try {
    const file = fileList.value[0].originFileObj || fileList.value[0]
    await templateApi.importTemplate(file)
    message.success('导入成功')
    importModalVisible.value = false
    fileList.value = []
    loadData()
  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败')
  }
}

const handleImportCancel = () => {
  importModalVisible.value = false
  fileList.value = []
}

const resetFormData = () => {
  formData.value = {
    name: '',
    description: '',
    selectedIndicators: [],
    weights: {},
    category: 'custom',
    isDefault: false
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.template-management {
  padding: 24px;
}
</style>