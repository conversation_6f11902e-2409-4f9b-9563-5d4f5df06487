<template>
  <div class="cultivation-object-list-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>培育对象审核管理</h2>
        <p>根据权限查看和管理培育对象的审核状态</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="exportData" :loading="exporting">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="培育对象名称">
              <a-input
                v-model:value="searchParams.objectName"
                placeholder="请输入培育对象名称"
                allow-clear
                @pressEnter="handleSearch"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="审核状态">
              <a-select
                v-model:value="searchParams.auditStatus"
                placeholder="请选择审核状态"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="pending">待审核</a-select-option>
                <a-select-option value="under_review">审核中</a-select-option>
                <a-select-option value="approved">已通过</a-select-option>
                <a-select-option value="rejected">已退回</a-select-option>
                <a-select-option value="need_supplement">待补充材料</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="项目类型">
              <a-select
                v-model:value="searchParams.projectType"
                placeholder="请选择项目类型"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="party_building">党建项目</a-select-option>
                <a-select-option value="innovation">创新项目</a-select-option>
                <a-select-option value="service">服务项目</a-select-option>
                <a-select-option value="culture">文化项目</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="申报单位">
              <a-input
                v-model:value="searchParams.applicantName"
                placeholder="请输入申报单位"
                allow-clear
                @pressEnter="handleSearch"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right;">
            <a-space>
              <a-button @click="resetSearch">重置</a-button>
              <a-button type="primary" @click="handleSearch" :loading="loading">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="总计"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="待审核"
              :value="statistics.pending"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="审核中"
              :value="statistics.underReview"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="已完成"
              :value="statistics.completed"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 培育对象列表 -->
    <div class="list-section">
      <a-card>
        <template #title>
          <div class="table-title">
            <span>培育对象列表</span>
            <a-tag color="blue">共 {{ pagination.total }} 条记录</a-tag>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button size="small" @click="toggleView">
              <template #icon>
                <appstore-outlined v-if="viewMode === 'table'" />
                <bars-outlined v-else />
              </template>
              {{ viewMode === 'table' ? '卡片视图' : '表格视图' }}
            </a-button>
          </a-space>
        </template>

        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :columns="columns"
          :data-source="objectList"
          :loading="loading"
          :pagination="pagination"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'objectName'">
              <div class="object-name-cell">
                <div class="name">{{ record.objectName }}</div>
                <div class="sub-info">{{ record.projectType }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'auditStatus'">
              <a-tag :color="getStatusColor(record.auditStatus)">
                {{ getStatusText(record.auditStatus) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'systemScore'">
              <div class="score-cell">
                <div class="score-value">{{ record.systemScore }}</div>
                <div class="score-bar">
                  <a-progress :percent="record.systemScore" size="small" :show-info="false" />
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'reviewScore'">
              <div class="score-cell" v-if="record.reviewScore">
                <div class="score-value" :class="getScoreChangeClass(record)">
                  {{ record.reviewScore }}
                </div>
                <div class="score-change">
                  {{ getScoreChange(record) }}
                </div>
              </div>
              <span v-else class="no-score">未评分</span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  查看详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="startReview(record)"
                  :disabled="!canReview(record)"
                >
                  开始审核
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewHistory(record)">
                        <history-outlined />
                        查看历史
                      </a-menu-item>
                      <a-menu-item @click="downloadMaterials(record)">
                        <download-outlined />
                        下载材料
                      </a-menu-item>
                      <a-menu-item v-if="record.auditStatus === 'rejected'" @click="requestRescore(record)">
                        <redo-outlined />
                        申请重评
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8" v-for="object in objectList" :key="object.id">
              <a-card class="object-card" :class="['status-' + object.auditStatus]">
                <template #title>
                  <div class="card-title">
                    <div class="object-name">{{ object.objectName }}</div>
                    <a-tag :color="getStatusColor(object.auditStatus)" size="small">
                      {{ getStatusText(object.auditStatus) }}
                    </a-tag>
                  </div>
                </template>
                <template #extra>
                  <a-dropdown>
                    <a-button type="text" size="small">
                      <ellipsis-outlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="viewDetail(object)">查看详情</a-menu-item>
                        <a-menu-item @click="startReview(object)" :disabled="!canReview(object)">开始审核</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </template>
                
                <div class="card-content">
                  <div class="info-row">
                    <span class="label">申报单位：</span>
                    <span class="value">{{ object.applicantName }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">项目类型：</span>
                    <span class="value">{{ object.projectType }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">提交时间：</span>
                    <span class="value">{{ object.submitTime }}</span>
                  </div>
                  <div class="score-section">
                    <div class="score-item">
                      <span class="score-label">系统评分</span>
                      <span class="score-value">{{ object.systemScore }}</span>
                    </div>
                    <div class="score-item" v-if="object.reviewScore">
                      <span class="score-label">审核评分</span>
                      <span class="score-value" :class="getScoreChangeClass(object)">
                        {{ object.reviewScore }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <template #actions>
                  <a-button type="link" @click="viewDetail(object)">查看详情</a-button>
                  <a-button type="link" @click="startReview(object)" :disabled="!canReview(object)">
                    开始审核
                  </a-button>
                </template>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 历史记录弹窗 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="审核历史记录"
      width="800px"
      :footer="null"
    >
      <a-timeline v-if="selectedObject">
        <a-timeline-item
          v-for="record in auditHistory"
          :key="record.id"
          :color="getHistoryColor(record.action)"
        >
          <div class="history-item">
            <div class="history-header">
              <span class="action">{{ record.action }}</span>
              <span class="time">{{ record.timestamp }}</span>
            </div>
            <div class="history-content">{{ record.content }}</div>
            <div class="history-user">操作人：{{ record.operator }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ExportOutlined,
  ReloadOutlined,
  SearchOutlined,
  AppstoreOutlined,
  BarsOutlined,
  HistoryOutlined,
  DownloadOutlined,
  RedoOutlined,
  DownOutlined,
  EllipsisOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface CultivationObject {
  id: string
  objectName: string
  projectType: string
  applicantName: string
  auditStatus: 'pending' | 'under_review' | 'approved' | 'rejected' | 'need_supplement'
  systemScore: number
  reviewScore?: number
  submitTime: string
  reviewerName?: string
  reviewTime?: string
}

interface SearchParams {
  objectName?: string
  auditStatus?: string
  projectType?: string
  applicantName?: string
}

interface AuditHistoryRecord {
  id: string
  action: string
  content: string
  timestamp: string
  operator: string
}

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const historyModalVisible = ref(false)
const selectedObject = ref<CultivationObject | null>(null)

const searchParams = ref<SearchParams>({})

const objectList = ref<CultivationObject[]>([
  {
    id: '1',
    objectName: '党员服务中心数字化改革项目',
    projectType: '党建项目',
    applicantName: '市委组织部',
    auditStatus: 'under_review',
    systemScore: 85.5,
    reviewScore: 88.2,
    submitTime: '2025-01-05 14:30:00',
    reviewerName: '张审核员',
    reviewTime: '2025-01-06 10:20:00'
  },
  {
    id: '2',
    objectName: '智慧党建平台建设',
    projectType: '党建项目',
    applicantName: '市委宣传部',
    auditStatus: 'pending',
    systemScore: 78.3,
    submitTime: '2025-01-05 16:15:00'
  },
  {
    id: '3',
    objectName: '基层治理创新实践',
    projectType: '创新项目',
    applicantName: '市委政法委',
    auditStatus: 'approved',
    systemScore: 92.1,
    reviewScore: 91.8,
    submitTime: '2025-01-04 09:45:00',
    reviewerName: '李审核员',
    reviewTime: '2025-01-05 14:30:00'
  }
])

const auditHistory = ref<AuditHistoryRecord[]>([
  {
    id: '1',
    action: '提交申请',
    content: '培育对象提交了申报材料',
    timestamp: '2025-01-05 14:30:00',
    operator: '市委组织部'
  },
  {
    id: '2',
    action: '系统评分',
    content: '系统自动完成初步评分：85.5分',
    timestamp: '2025-01-05 14:35:00',
    operator: '系统'
  },
  {
    id: '3',
    action: '分配审核员',
    content: '系统自动分配审核员：张审核员',
    timestamp: '2025-01-06 09:00:00',
    operator: '系统'
  }
])

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 统计数据
const statistics = computed(() => ({
  total: objectList.value.length,
  pending: objectList.value.filter(obj => obj.auditStatus === 'pending').length,
  underReview: objectList.value.filter(obj => obj.auditStatus === 'under_review').length,
  completed: objectList.value.filter(obj => ['approved', 'rejected'].includes(obj.auditStatus)).length
}))

// 表格列定义
const columns = [
  {
    title: '培育对象',
    key: 'objectName',
    width: 220,
    fixed: 'left'
  },
  {
    title: '申报单位',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 150
  },
  {
    title: '审核状态',
    key: 'auditStatus',
    width: 100
  },
  {
    title: '系统评分',
    key: 'systemScore',
    width: 120,
    sorter: true
  },
  {
    title: '审核评分',
    key: 'reviewScore',
    width: 120,
    sorter: true
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 150,
    sorter: true
  },
  {
    title: '审核人员',
    dataIndex: 'reviewerName',
    key: 'reviewerName',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right'
  }
]

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    // 这里应该调用API进行搜索
    console.log('搜索参数:', searchParams.value)
    loading.value = false
  }, 1000)
}

const resetSearch = () => {
  searchParams.value = {}
  handleSearch()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.value = { ...pagination.value, ...pag }
  console.log('表格变化:', { pagination: pag, filters, sorter })
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    message.success('数据刷新成功')
    loading.value = false
  }, 1000)
}

const exportData = () => {
  exporting.value = true
  setTimeout(() => {
    message.success('数据导出成功')
    exporting.value = false
  }, 2000)
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'table' ? 'card' : 'table'
}

const viewDetail = (object: CultivationObject) => {
  router.push(`/review/detail/${object.id}`)
}

const startReview = (object: CultivationObject) => {
  if (!canReview(object)) {
    message.warning('该对象当前状态不能开始审核')
    return
  }
  router.push(`/review/score-detail/${object.id}`)
}

const canReview = (object: CultivationObject) => {
  return ['pending', 'under_review', 'need_supplement'].includes(object.auditStatus)
}

const viewHistory = (object: CultivationObject) => {
  selectedObject.value = object
  historyModalVisible.value = true
}

const downloadMaterials = (object: CultivationObject) => {
  message.info(`开始下载 ${object.objectName} 的申报材料`)
}

const requestRescore = (object: CultivationObject) => {
  message.info(`申请对 ${object.objectName} 进行重新评分`)
}

// 样式计算方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'orange',
    under_review: 'blue',
    approved: 'green',
    rejected: 'red',
    need_supplement: 'purple'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待审核',
    under_review: '审核中',
    approved: '已通过',
    rejected: '已退回',
    need_supplement: '待补充材料'
  }
  return textMap[status] || '未知'
}

const getScoreChangeClass = (object: CultivationObject) => {
  if (!object.reviewScore) return ''
  if (object.reviewScore > object.systemScore) return 'score-increase'
  if (object.reviewScore < object.systemScore) return 'score-decrease'
  return 'score-unchanged'
}

const getScoreChange = (object: CultivationObject) => {
  if (!object.reviewScore) return ''
  const diff = object.reviewScore - object.systemScore
  if (diff > 0) return `(+${diff.toFixed(1)})`
  if (diff < 0) return `(${diff.toFixed(1)})`
  return '(无变化)'
}

const getHistoryColor = (action: string) => {
  const colorMap: Record<string, string> = {
    '提交申请': 'blue',
    '系统评分': 'cyan',
    '分配审核员': 'purple',
    '开始审核': 'orange',
    '审核通过': 'green',
    '审核退回': 'red'
  }
  return colorMap[action] || 'gray'
}

// 生命周期
onMounted(() => {
  handleSearch()
})
</script>

<style scoped lang="scss">
.cultivation-object-list-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.search-section {
  margin-bottom: 24px;
}

.stats-section {
  margin-bottom: 24px;
}

.list-section {
  .table-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .object-name-cell {
    .name {
      font-weight: 600;
      color: #262626;
      margin-bottom: 4px;
    }

    .sub-info {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .score-cell {
    .score-value {
      font-weight: 600;
      margin-bottom: 4px;

      &.score-increase {
        color: #52c41a;
      }

      &.score-decrease {
        color: #f5222d;
      }

      &.score-unchanged {
        color: #1890ff;
      }
    }

    .score-bar {
      width: 80px;
    }

    .score-change {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .no-score {
    color: #d9d9d9;
    font-style: italic;
  }
}

.card-view {
  .object-card {
    height: 280px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.status-pending {
      border-left: 4px solid #faad14;
    }

    &.status-under_review {
      border-left: 4px solid #1890ff;
    }

    &.status-approved {
      border-left: 4px solid #52c41a;
    }

    &.status-rejected {
      border-left: 4px solid #f5222d;
    }

    &.status-need_supplement {
      border-left: 4px solid #722ed1;
    }

    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .object-name {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .card-content {
      .info-row {
        display: flex;
        margin-bottom: 8px;

        .label {
          color: #8c8c8c;
          min-width: 80px;
          font-size: 12px;
        }

        .value {
          color: #262626;
          font-size: 12px;
          flex: 1;
        }
      }

      .score-section {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        padding: 12px;
        background: #f9f9f9;
        border-radius: 6px;

        .score-item {
          text-align: center;

          .score-label {
            display: block;
            color: #8c8c8c;
            font-size: 12px;
            margin-bottom: 4px;
          }

          .score-value {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #1890ff;

            &.score-increase {
              color: #52c41a;
            }

            &.score-decrease {
              color: #f5222d;
            }
          }
        }
      }
    }
  }
}

.history-item {
  .history-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;

    .action {
      font-weight: 600;
      color: #262626;
    }

    .time {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .history-content {
    color: #595959;
    margin-bottom: 4px;
  }

  .history-user {
    color: #8c8c8c;
    font-size: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cultivation-object-list-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .stats-section {
      .ant-col {
        margin-bottom: 12px;
      }
    }
  }
}
</style>