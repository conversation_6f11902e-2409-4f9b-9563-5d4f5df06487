<template>
  <div class="audit-confirmation-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>审核确认</h2>
        <p>培育对象评分结果审核确认管理，支持流程配置、人工校正、历史追溯等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="goToProjectAudit">
            <template #icon><audit-outlined /></template>
            项目审核
          </a-button>
          <a-button @click="goToScoreManagement">
            <template #icon><bar-chart-outlined /></template>
            评分管理
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待审核"
              :value="auditConfirmationStore.pendingAuditCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已审核"
              :value="auditConfirmationStore.auditedCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已退回"
              :value="auditConfirmationStore.rejectedCount"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均得分"
              :value="auditConfirmationStore.avgScore"
              :precision="1"
              suffix="分"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 项目概览区域 -->
    <div class="project-overview-section">
      <a-card title="项目概览" :loading="auditConfirmationStore.loading">
        <template #extra>
          <a-button type="link" @click="goToProjectAudit">查看全部</a-button>
        </template>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :md="12" v-for="project in auditConfirmationStore.projectList" :key="project.id">
            <a-card size="small" class="project-card">
              <template #title>
                <div class="project-title">
                  <span>{{ project.name }}</span>
                  <a-tag :color="getProjectStatusColor(project.status)">
                    {{ getProjectStatusText(project.status) }}
                  </a-tag>
                </div>
              </template>
              <div class="project-stats">
                <div class="stat-item">
                  <span class="label">总数:</span>
                  <span class="value">{{ project.totalCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">已审核:</span>
                  <span class="value success">{{ project.auditedCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">通过:</span>
                  <span class="value success">{{ project.passedCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="label">退回:</span>
                  <span class="value error">{{ project.rejectedCount }}</span>
                </div>
              </div>
              <div class="project-progress">
                <a-progress 
                  :percent="Math.round((project.auditedCount / project.totalCount) * 100)"
                  :stroke-color="getProgressColor(project.auditedCount / project.totalCount)"
                  size="small"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 最新审核动态 -->
    <div class="recent-activities-section">
      <a-card title="最新审核动态" :loading="auditConfirmationStore.loading">
        <template #extra>
          <a-button type="link" @click="goToAuditHistory">查看全部</a-button>
        </template>
        <a-list
          :data-source="recentAuditRecords"
          :locale="{ emptyText: '暂无审核记录' }"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <div class="activity-title">
                    <span>{{ item.cultivationObjectName }}</span>
                    <a-tag :color="getAuditResultColor(item.auditResult)" size="small">
                      {{ getAuditResultText(item.auditResult) }}
                    </a-tag>
                  </div>
                </template>
                <template #description>
                  <div class="activity-desc">
                    <div>项目: {{ item.projectName }}</div>
                    <div>审核人: {{ item.auditor }} | 时间: {{ item.auditTime }}</div>
                    <div v-if="item.comments">意见: {{ item.comments }}</div>
                  </div>
                </template>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getAuditResultColor(item.auditResult) }">
                    <template #icon>
                      <audit-outlined />
                    </template>
                  </a-avatar>
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <a-card title="快速操作">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToProjectAudit">
              <div class="action-content">
                <audit-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">项目审核</div>
                  <div class="action-desc">审核培育对象评分结果</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToScoreManagement">
              <div class="action-content">
                <bar-chart-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">评分管理</div>
                  <div class="action-desc">查看和修改评分结果</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToAuditHistory">
              <div class="action-content">
                <history-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">审核历史</div>
                  <div class="action-desc">查看历史审核记录</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="showProcessConfig">
              <div class="action-content">
                <setting-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">流程配置</div>
                  <div class="action-desc">配置审核流程节点</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAuditConfirmationStore } from '@/store/modules/audit-confirmation'
import {
  ProjectStatusTextMap,
  ProjectStatusColorMap,
  AuditResultTextMap,
  AuditResultColorMap
} from '@/types/audit-confirmation'
import {
  AuditOutlined,
  BarChartOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined,
  HistoryOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const auditConfirmationStore = useAuditConfirmationStore()

// 最新审核记录（取前5条）
const recentAuditRecords = computed(() => {
  return auditConfirmationStore.auditRecordList.slice(0, 5)
})

// 方法定义
function getProjectStatusText(status: number) {
  return ProjectStatusTextMap[status as keyof typeof ProjectStatusTextMap] || '未知'
}

function getProjectStatusColor(status: number) {
  return ProjectStatusColorMap[status as keyof typeof ProjectStatusColorMap] || 'default'
}

function getAuditResultText(result: number) {
  return AuditResultTextMap[result as keyof typeof AuditResultTextMap] || '未知'
}

function getAuditResultColor(result: number) {
  return AuditResultColorMap[result as keyof typeof AuditResultColorMap] || 'default'
}

function getProgressColor(percent: number) {
  if (percent >= 0.8) return '#52c41a'
  if (percent >= 0.6) return '#1890ff'
  if (percent >= 0.4) return '#faad14'
  return '#ff4d4f'
}

// 页面跳转
function goToProjectAudit() {
  router.push('/audit-confirmation/project-audit')
}

function goToScoreManagement() {
  router.push('/audit-confirmation/score-management')
}

function goToAuditHistory() {
  router.push('/audit-confirmation/audit-history')
}

function showProcessConfig() {
  message.info('流程配置功能开发中...')
}

// 刷新数据
async function refreshData() {
  try {
    await Promise.all([
      auditConfirmationStore.fetchStatistics(),
      auditConfirmationStore.fetchProjectList(),
      auditConfirmationStore.fetchCultivationObjectList(),
      auditConfirmationStore.fetchAuditRecordList()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  }
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.audit-confirmation-index {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .project-overview-section,
  .recent-activities-section,
  .quick-actions-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .project-card {
    .project-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .project-stats {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .label {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .value {
          font-size: 16px;
          font-weight: 600;

          &.success {
            color: #52c41a;
          }

          &.error {
            color: #ff4d4f;
          }
        }
      }
    }

    .project-progress {
      margin-top: 8px;
    }
  }

  .activity-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .activity-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;
    }
  }

  .action-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .action-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .action-icon {
        font-size: 24px;
        color: #1890ff;
      }

      .action-text {
        .action-title {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .action-desc {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .audit-confirmation-index {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .project-stats {
      flex-wrap: wrap;
      gap: 8px;
    }

    .action-content {
      flex-direction: column;
      text-align: center;
      gap: 8px;
    }
  }
}
</style>
