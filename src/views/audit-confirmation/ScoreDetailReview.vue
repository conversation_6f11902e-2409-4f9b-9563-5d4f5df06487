<template>
  <div class="score-detail-review-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button type="text" @click="goBack" class="back-btn">
          <template #icon><arrow-left-outlined /></template>
          返回列表
        </a-button>
        <div class="header-title">
          <h2>评分详情审核</h2>
          <p>{{ objectInfo.objectName }} - {{ objectInfo.projectType }}</p>
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="saveAsDraft" :loading="draftSaving">
            <template #icon><save-outlined /></template>
            保存草稿
          </a-button>
          <a-button type="primary" @click="submitReview" :loading="submitting" :disabled="!canSubmit">
            <template #icon><check-outlined /></template>
            提交审核
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 对象信息卡片 -->
    <div class="object-info-section">
      <a-card title="培育对象信息">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="对象名称">{{ objectInfo.objectName }}</a-descriptions-item>
          <a-descriptions-item label="项目类型">{{ objectInfo.projectType }}</a-descriptions-item>
          <a-descriptions-item label="申报单位">{{ objectInfo.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="当前状态">
            <a-tag :color="getStatusColor(objectInfo.status)">{{ getStatusText(objectInfo.status) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="系统评分">
            <span class="system-score">{{ objectInfo.systemScore }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="审核评分">
            <span class="review-score" :class="getScoreClass()">{{ currentReviewScore }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="提交时间" :span="3">{{ objectInfo.submitTime }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 评分审核区域 -->
    <div class="review-section">
      <a-row :gutter="24">
        <!-- 左侧指标详情 -->
        <a-col :xs="24" :lg="16">
          <a-card title="指标评分详情" class="indicators-card">
            <template #extra>
              <a-space>
                <span class="score-summary">
                  总分：<span class="total-score">{{ totalScore.toFixed(1) }}</span>
                </span>
                <a-button size="small" @click="resetAllScores">
                  <template #icon><redo-outlined /></template>
                  重置所有
                </a-button>
              </a-space>
            </template>

            <div class="indicators-list">
              <div v-for="indicator in indicators" :key="indicator.id" class="indicator-item">
                <a-card size="small" :class="['indicator-card', { 'modified': indicator.modified }]">
                  <template #title>
                    <div class="indicator-header">
                      <div class="indicator-name">
                        {{ indicator.name }}
                        <a-tag v-if="indicator.modified" color="orange" size="small">已修改</a-tag>
                      </div>
                      <div class="indicator-weight">权重: {{ (indicator.weight * 100).toFixed(0) }}%</div>
                    </div>
                  </template>
                  
                  <div class="indicator-content">
                    <div class="score-section">
                      <div class="score-row">
                        <span class="score-label">系统评分：</span>
                        <span class="system-score">{{ indicator.systemScore }}</span>
                      </div>
                      <div class="score-row">
                        <span class="score-label">审核评分：</span>
                        <a-input-number
                          v-model:value="indicator.reviewScore"
                          :min="0"
                          :max="100"
                          :step="0.1"
                          :precision="1"
                          @change="onScoreChange(indicator)"
                          style="width: 120px"
                        />
                        <span class="score-diff" :class="getScoreDiffClass(indicator)">
                          {{ getScoreDiff(indicator) }}
                        </span>
                      </div>
                    </div>
                    
                    <div class="indicator-description">
                      <div class="description-label">评分标准：</div>
                      <div class="description-content">{{ indicator.description }}</div>
                    </div>
                    
                    <div class="evidence-section" v-if="indicator.evidences.length > 0">
                      <div class="evidence-label">支撑材料：</div>
                      <div class="evidence-list">
                        <a-space wrap>
                          <a-button 
                            v-for="evidence in indicator.evidences" 
                            :key="evidence.id"
                            size="small" 
                            type="link" 
                            @click="previewEvidence(evidence)"
                          >
                            <template #icon><file-text-outlined /></template>
                            {{ evidence.name }}
                          </a-button>
                        </a-space>
                      </div>
                    </div>
                    
                    <div class="comment-section">
                      <div class="comment-label">审核意见：</div>
                      <a-textarea
                        v-model:value="indicator.reviewComment"
                        placeholder="请填写对该指标的审核意见..."
                        :rows="3"
                        @change="onCommentChange(indicator)"
                      />
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 右侧审核操作 -->
        <a-col :xs="24" :lg="8">
          <a-affix :offset-top="80">
            <a-card title="审核操作" class="review-actions-card">
              <!-- 审核结果选择 -->
              <div class="review-result-section">
                <div class="section-title">审核结果</div>
                <a-radio-group v-model:value="reviewForm.result" @change="onResultChange">
                  <a-radio value="approved" class="result-option">
                    <check-circle-outlined style="color: #52c41a;" />
                    通过
                  </a-radio>
                  <a-radio value="rejected" class="result-option">
                    <close-circle-outlined style="color: #f5222d;" />
                    退回
                  </a-radio>
                  <a-radio value="pending" class="result-option">
                    <clock-circle-outlined style="color: #faad14;" />
                    待补充材料
                  </a-radio>
                </a-radio-group>
              </div>

              <!-- 总体审核意见 -->
              <div class="overall-comment-section">
                <div class="section-title">总体审核意见</div>
                <a-textarea
                  v-model:value="reviewForm.overallComment"
                  placeholder="请填写总体审核意见..."
                  :rows="4"
                />
              </div>

              <!-- 快速操作 -->
              <div class="quick-actions-section">
                <div class="section-title">快速操作</div>
                <a-space direction="vertical" style="width: 100%;">
                  <a-button block size="small" @click="applySystemScores">
                    <template #icon><robot-outlined /></template>
                    采用系统评分
                  </a-button>
                  <a-button block size="small" @click="showHistoryModal">
                    <template #icon><history-outlined /></template>
                    查看历史记录
                  </a-button>
                  <a-button block size="small" @click="showSimilarCases">
                    <template #icon><search-outlined /></template>
                    查看相似案例
                  </a-button>
                </a-space>
              </div>

              <!-- 评分统计 -->
              <div class="score-stats-section">
                <div class="section-title">评分统计</div>
                <div class="stats-item">
                  <span class="stats-label">已修改指标：</span>
                  <span class="stats-value">{{ modifiedIndicatorsCount }}/{{ indicators.length }}</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">平均得分率：</span>
                  <span class="stats-value">{{ averageScoreRate }}%</span>
                </div>
                <div class="stats-item">
                  <span class="stats-label">评分变化：</span>
                  <span class="stats-value" :class="getTotalScoreChangeClass()">
                    {{ getTotalScoreChange() }}
                  </span>
                </div>
              </div>

              <!-- 审核进度 -->
              <div class="review-progress-section">
                <div class="section-title">审核进度</div>
                <a-progress :percent="reviewProgress" :status="getProgressStatus()" />
                <div class="progress-text">{{ getProgressText() }}</div>
              </div>
            </a-card>
          </a-affix>
        </a-col>
      </a-row>
    </div>

    <!-- 历史记录对话框 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="审核历史记录"
      width="800px"
      :footer="null"
    >
      <a-timeline>
        <a-timeline-item v-for="record in historyRecords" :key="record.id" :color="getHistoryItemColor(record.action)">
          <div class="history-item">
            <div class="history-header">
              <span class="history-action">{{ record.action }}</span>
              <span class="history-time">{{ record.timestamp }}</span>
            </div>
            <div class="history-content">{{ record.content }}</div>
            <div class="history-user">操作人：{{ record.user }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-modal>

    <!-- 相似案例对话框 -->
    <a-modal
      v-model:open="similarCasesModalVisible"
      title="相似案例参考"
      width="1000px"
      :footer="null"
    >
      <a-table
        :columns="similarCasesColumns"
        :data-source="similarCases"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'similarity'">
            <a-progress :percent="record.similarity" size="small" />
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-button type="link" size="small" @click="viewCaseDetail(record)">查看详情</a-button>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 材料预览对话框 -->
    <a-modal
      v-model:open="evidenceModalVisible"
      title="材料预览"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedEvidence">
        <div class="evidence-header">
          <h4>{{ selectedEvidence.name }}</h4>
          <a-space>
            <a-tag>{{ selectedEvidence.type }}</a-tag>
            <a-tag>{{ selectedEvidence.size }}</a-tag>
            <a-button type="link" @click="downloadEvidence(selectedEvidence)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
          </a-space>
        </div>
        <div class="evidence-content">
          <div v-if="selectedEvidence.type === 'image'" class="evidence-image">
            <img :src="selectedEvidence.url" style="max-width: 100%; height: auto;" />
          </div>
          <div v-else-if="selectedEvidence.type === 'document'" class="evidence-document">
            <iframe :src="selectedEvidence.url" style="width: 100%; height: 500px; border: none;"></iframe>
          </div>
          <div v-else class="evidence-other">
            <a-result
              status="info"
              title="无法预览该类型文件"
              sub-title="请点击下载按钮获取文件"
            />
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  CheckOutlined,
  RedoOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  RobotOutlined,
  HistoryOutlined,
  SearchOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface ObjectInfo {
  id: string
  objectName: string
  projectType: string
  applicantName: string
  status: string
  systemScore: number
  submitTime: string
}

interface Indicator {
  id: string
  name: string
  weight: number
  systemScore: number
  reviewScore: number
  description: string
  reviewComment: string
  evidences: Evidence[]
  modified: boolean
}

interface Evidence {
  id: string
  name: string
  type: string
  size: string
  url: string
}

interface ReviewForm {
  result: 'approved' | 'rejected' | 'pending' | undefined
  overallComment: string
}

interface HistoryRecord {
  id: string
  action: string
  content: string
  timestamp: string
  user: string
}

interface SimilarCase {
  id: string
  name: string
  type: string
  score: number
  similarity: number
  reviewer: string
  reviewTime: string
}

// 路由和参数
const route = useRoute()
const router = useRouter()
const objectId = route.params.id as string

// 响应式数据
const objectInfo = ref<ObjectInfo>({
  id: '1',
  objectName: '党员服务中心数字化改革项目',
  projectType: '党建项目',
  applicantName: '市委组织部',
  status: 'under_review',
  systemScore: 85.5,
  submitTime: '2025-01-05 14:30:00'
})

const indicators = ref<Indicator[]>([
  {
    id: '1',
    name: '创新程度',
    weight: 0.3,
    systemScore: 80,
    reviewScore: 80,
    description: '评价项目在理念、技术、方法等方面的创新性和先进性',
    reviewComment: '',
    evidences: [
      { id: '1', name: '技术方案书.pdf', type: 'document', size: '2.3MB', url: '/mock/document1.pdf' },
      { id: '2', name: '系统架构图.png', type: 'image', size: '1.2MB', url: '/mock/image1.png' }
    ],
    modified: false
  },
  {
    id: '2',
    name: '实施效果',
    weight: 0.4,
    systemScore: 88,
    reviewScore: 88,
    description: '评价项目实施后在提升工作效率、服务质量等方面的实际效果',
    reviewComment: '',
    evidences: [
      { id: '3', name: '效果评估报告.doc', type: 'document', size: '1.8MB', url: '/mock/document2.doc' },
      { id: '4', name: '用户反馈统计.xlsx', type: 'spreadsheet', size: '0.5MB', url: '/mock/spreadsheet1.xlsx' }
    ],
    modified: false
  },
  {
    id: '3',
    name: '可推广性',
    weight: 0.3,
    systemScore: 87,
    reviewScore: 87,
    description: '评价项目经验做法在同类单位中的推广应用潜力',
    reviewComment: '',
    evidences: [
      { id: '5', name: '推广方案.pdf', type: 'document', size: '1.5MB', url: '/mock/document3.pdf' }
    ],
    modified: false
  }
])

const reviewForm = ref<ReviewForm>({
  result: undefined,
  overallComment: ''
})

const historyRecords = ref<HistoryRecord[]>([
  {
    id: '1',
    action: '开始审核',
    content: '系统自动分配审核任务',
    timestamp: '2025-01-06 09:00:00',
    user: '系统'
  },
  {
    id: '2',
    action: '材料补充',
    content: '申报单位补充了技术方案书和效果评估报告',
    timestamp: '2025-01-05 16:30:00',
    user: '市委组织部'
  }
])

const similarCases = ref<SimilarCase[]>([
  {
    id: '1',
    name: '智慧党建平台项目',
    type: '党建项目',
    score: 89.2,
    similarity: 85,
    reviewer: '张审核员',
    reviewTime: '2024-12-15'
  },
  {
    id: '2',
    name: '数字化党员管理系统',
    type: '党建项目',
    score: 82.7,
    similarity: 78,
    reviewer: '李审核员',
    reviewTime: '2024-11-20'
  }
])

const draftSaving = ref(false)
const submitting = ref(false)
const historyModalVisible = ref(false)
const similarCasesModalVisible = ref(false)
const evidenceModalVisible = ref(false)
const selectedEvidence = ref<Evidence | null>(null)

// 计算属性
const totalScore = computed(() => {
  return indicators.value.reduce((sum, indicator) => {
    return sum + indicator.reviewScore * indicator.weight
  }, 0)
})

const currentReviewScore = computed(() => {
  return totalScore.value.toFixed(1)
})

const modifiedIndicatorsCount = computed(() => {
  return indicators.value.filter(indicator => indicator.modified).length
})

const averageScoreRate = computed(() => {
  const average = indicators.value.reduce((sum, indicator) => sum + indicator.reviewScore, 0) / indicators.value.length
  return Math.round(average)
})

const reviewProgress = computed(() => {
  let progress = 0
  
  // 指标评分完成度
  const scoredIndicators = indicators.value.filter(ind => ind.reviewScore > 0).length
  progress += (scoredIndicators / indicators.value.length) * 40
  
  // 审核意见填写完成度
  const commentedIndicators = indicators.value.filter(ind => ind.reviewComment.trim()).length
  progress += (commentedIndicators / indicators.value.length) * 30
  
  // 审核结果选择
  if (reviewForm.value.result) progress += 20
  
  // 总体意见填写
  if (reviewForm.value.overallComment.trim()) progress += 10
  
  return Math.round(progress)
})

const canSubmit = computed(() => {
  return reviewForm.value.result && reviewForm.value.overallComment.trim() && reviewProgress.value >= 80
})

// 表格列定义
const similarCasesColumns = [
  { title: '案例名称', dataIndex: 'name', key: 'name' },
  { title: '项目类型', dataIndex: 'type', key: 'type' },
  { title: '评分', dataIndex: 'score', key: 'score' },
  { title: '相似度', key: 'similarity' },
  { title: '审核人', dataIndex: 'reviewer', key: 'reviewer' },
  { title: '审核时间', dataIndex: 'reviewTime', key: 'reviewTime' },
  { title: '操作', key: 'actions' }
]

// 方法定义
const goBack = () => {
  router.go(-1)
}

const onScoreChange = (indicator: Indicator) => {
  indicator.modified = indicator.reviewScore !== indicator.systemScore
}

const onCommentChange = (indicator: Indicator) => {
  indicator.modified = true
}

const onResultChange = () => {
  // 审核结果变更时的处理
}

const saveAsDraft = async () => {
  draftSaving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('草稿保存成功')
  } finally {
    draftSaving.value = false
  }
}

const submitReview = async () => {
  if (!canSubmit.value) {
    message.warning('请完善审核信息后再提交')
    return
  }
  
  submitting.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('审核结果提交成功')
    router.push('/review/audit')
  } finally {
    submitting.value = false
  }
}

const resetAllScores = () => {
  indicators.value.forEach(indicator => {
    indicator.reviewScore = indicator.systemScore
    indicator.modified = false
    indicator.reviewComment = ''
  })
  message.info('已重置所有评分')
}

const applySystemScores = () => {
  indicators.value.forEach(indicator => {
    indicator.reviewScore = indicator.systemScore
    indicator.modified = false
  })
  message.info('已采用系统评分')
}

const showHistoryModal = () => {
  historyModalVisible.value = true
}

const showSimilarCases = () => {
  similarCasesModalVisible.value = true
}

const previewEvidence = (evidence: Evidence) => {
  selectedEvidence.value = evidence
  evidenceModalVisible.value = true
}

const downloadEvidence = (evidence: Evidence) => {
  message.info(`开始下载 ${evidence.name}`)
}

const viewCaseDetail = (caseRecord: SimilarCase) => {
  message.info(`查看案例详情: ${caseRecord.name}`)
}

// 样式计算方法
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'orange',
    under_review: 'blue',
    approved: 'green',
    rejected: 'red'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待审核',
    under_review: '审核中',
    approved: '已通过',
    rejected: '已退回'
  }
  return textMap[status] || '未知'
}

const getScoreClass = () => {
  const current = parseFloat(currentReviewScore.value)
  const system = objectInfo.value.systemScore
  if (current > system) return 'score-increase'
  if (current < system) return 'score-decrease'
  return 'score-unchanged'
}

const getScoreDiffClass = (indicator: Indicator) => {
  if (indicator.reviewScore > indicator.systemScore) return 'score-increase'
  if (indicator.reviewScore < indicator.systemScore) return 'score-decrease'
  return 'score-unchanged'
}

const getScoreDiff = (indicator: Indicator) => {
  const diff = indicator.reviewScore - indicator.systemScore
  if (diff > 0) return `(+${diff.toFixed(1)})`
  if (diff < 0) return `(${diff.toFixed(1)})`
  return '(无变化)'
}

const getTotalScoreChangeClass = () => {
  const current = totalScore.value
  const system = objectInfo.value.systemScore
  if (current > system) return 'score-increase'
  if (current < system) return 'score-decrease'
  return 'score-unchanged'
}

const getTotalScoreChange = () => {
  const diff = totalScore.value - objectInfo.value.systemScore
  if (diff > 0) return `+${diff.toFixed(1)}`
  if (diff < 0) return diff.toFixed(1)
  return '无变化'
}

const getProgressStatus = () => {
  if (reviewProgress.value >= 80) return 'success'
  if (reviewProgress.value >= 50) return 'active'
  return 'normal'
}

const getProgressText = () => {
  if (reviewProgress.value >= 80) return '审核信息已完善，可以提交'
  if (reviewProgress.value >= 50) return '审核进行中...'
  return '请完善审核信息'
}

const getHistoryItemColor = (action: string) => {
  const colorMap: Record<string, string> = {
    '开始审核': 'blue',
    '材料补充': 'orange',
    '审核通过': 'green',
    '审核退回': 'red'
  }
  return colorMap[action] || 'gray'
}

// 生命周期
onMounted(() => {
  // 根据objectId加载数据
  console.log('加载对象ID:', objectId)
})
</script>

<style scoped lang="scss">
.score-detail-review-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      padding: 4px 8px;
      
      &:hover {
        background: #f5f5f5;
      }
    }

    .header-title {
      h2 {
        margin: 0 0 4px 0;
        color: #262626;
        font-size: 20px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
}

.object-info-section {
  margin-bottom: 24px;
}

.review-section {
  .indicators-card {
    .total-score {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }
  }
}

.indicators-list {
  .indicator-item {
    margin-bottom: 24px;

    .indicator-card {
      border-left: 4px solid #d9d9d9;
      transition: all 0.3s;

      &.modified {
        border-left-color: #1890ff;
        background: #f6ffed;
      }

      .indicator-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .indicator-name {
          font-size: 16px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .indicator-weight {
          color: #8c8c8c;
          font-size: 14px;
        }
      }

      .indicator-content {
        .score-section {
          margin-bottom: 16px;

          .score-row {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .score-label {
              min-width: 80px;
              color: #595959;
            }

            .system-score {
              color: #8c8c8c;
              font-weight: 500;
            }

            .score-diff {
              font-size: 12px;
              margin-left: 8px;

              &.score-increase {
                color: #52c41a;
              }

              &.score-decrease {
                color: #f5222d;
              }

              &.score-unchanged {
                color: #8c8c8c;
              }
            }
          }
        }

        .indicator-description {
          margin-bottom: 16px;

          .description-label {
            color: #595959;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .description-content {
            color: #8c8c8c;
            font-size: 14px;
            line-height: 1.6;
          }
        }

        .evidence-section {
          margin-bottom: 16px;

          .evidence-label {
            color: #595959;
            font-weight: 500;
            margin-bottom: 8px;
          }
        }

        .comment-section {
          .comment-label {
            color: #595959;
            font-weight: 500;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}

.review-actions-card {
  .section-title {
    color: #262626;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .review-result-section {
    margin-bottom: 24px;

    .result-option {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
    }
  }

  .overall-comment-section {
    margin-bottom: 24px;
  }

  .quick-actions-section {
    margin-bottom: 24px;
  }

  .score-stats-section {
    margin-bottom: 24px;

    .stats-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .stats-label {
        color: #8c8c8c;
      }

      .stats-value {
        font-weight: 500;

        &.score-increase {
          color: #52c41a;
        }

        &.score-decrease {
          color: #f5222d;
        }

        &.score-unchanged {
          color: #1890ff;
        }
      }
    }
  }

  .review-progress-section {
    .progress-text {
      text-align: center;
      margin-top: 8px;
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

.system-score {
  color: #8c8c8c;
  font-weight: 500;
}

.review-score {
  font-size: 18px;
  font-weight: 600;

  &.score-increase {
    color: #52c41a;
  }

  &.score-decrease {
    color: #f5222d;
  }

  &.score-unchanged {
    color: #1890ff;
  }
}

.history-item {
  .history-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;

    .history-action {
      font-weight: 600;
      color: #262626;
    }

    .history-time {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .history-content {
    color: #595959;
    margin-bottom: 4px;
  }

  .history-user {
    color: #8c8c8c;
    font-size: 12px;
  }
}

.evidence-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;

  h4 {
    margin: 0;
    color: #262626;
  }
}

.evidence-image, 
.evidence-document, 
.evidence-other {
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .score-detail-review-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .review-section {
      .ant-col {
        margin-bottom: 24px;
      }
    }
  }
}
</style>