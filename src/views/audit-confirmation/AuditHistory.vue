<template>
  <div class="audit-history-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>审核历史</h2>
        <p>培育对象审核历史记录查看，支持历史追溯、数据对比、轨迹可视化等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showTimelineView">
            <template #icon><clock-circle-outlined /></template>
            时间轴视图
          </a-button>
          <a-button @click="exportHistoryData">
            <template #icon><export-outlined /></template>
            导出历史
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 历史统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总审核次数"
              :value="auditConfirmationStore.auditRecordList.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <audit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="通过审核"
              :value="getAuditResultCount(1)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="未通过审核"
              :value="getAuditResultCount(2)"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待补充材料"
              :value="getAuditResultCount(3)"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="培育对象" class="form-item-full">
                <a-input v-model:value="searchForm.cultivationObjectName" placeholder="请输入培育对象名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核类型" class="form-item-full">
                <a-select v-model:value="searchForm.auditType" placeholder="请选择审核类型" allow-clear>
                  <a-select-option v-for="item in AuditTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核结果" class="form-item-full">
                <a-select v-model:value="searchForm.auditResult" placeholder="请选择审核结果" allow-clear>
                  <a-select-option v-for="item in AuditResultOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核人" class="form-item-full">
                <a-input v-model:value="searchForm.auditor" placeholder="请输入审核人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="审核时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>审核历史记录</span>
            <span class="record-count">共 {{ filteredAuditRecordList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterAuditResult" placeholder="结果筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in AuditResultOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button size="small" @click="showCompareModal" :disabled="selectedRowKeys.length !== 2">
              对比记录
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredAuditRecordList"
          :loading="auditConfirmationStore.loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1600 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'auditType'">
              <a-tag :color="getAuditTypeColor(record.auditType)">
                {{ getAuditTypeText(record.auditType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'auditResult'">
              <a-tag :color="getAuditResultColor(record.auditResult)">
                {{ getAuditResultText(record.auditResult) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'scoreChange'">
              <div class="score-change">
                <span v-if="record.originalScore" class="original-score">{{ record.originalScore }}</span>
                <arrow-right-outlined v-if="record.originalScore && record.finalScore" class="arrow" />
                <span v-if="record.finalScore" class="final-score">{{ record.finalScore }}</span>
                <span v-if="!record.originalScore && !record.finalScore">-</span>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showHistoryDetail(record)">查看详情</a-button>
                <a-button type="link" size="small" @click="viewRelatedRecords(record)">相关记录</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 审核历史详情弹窗 -->
    <a-modal 
      title="审核历史详情" 
      :visible="historyDetailVisible" 
      @cancel="historyDetailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentAuditRecord">
        <a-descriptions-item label="培育对象名称">{{ currentAuditRecord.cultivationObjectName }}</a-descriptions-item>
        <a-descriptions-item label="所属项目">{{ currentAuditRecord.projectName }}</a-descriptions-item>
        <a-descriptions-item label="审核类型">
          <a-tag :color="getAuditTypeColor(currentAuditRecord.auditType)">
            {{ getAuditTypeText(currentAuditRecord.auditType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="审核人">{{ currentAuditRecord.auditor }}</a-descriptions-item>
        <a-descriptions-item label="审核时间">{{ currentAuditRecord.auditTime }}</a-descriptions-item>
        <a-descriptions-item label="审核结果">
          <a-tag :color="getAuditResultColor(currentAuditRecord.auditResult)">
            {{ getAuditResultText(currentAuditRecord.auditResult) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="评分变化">
          <div class="score-change-detail">
            <span v-if="currentAuditRecord.originalScore">原始得分: {{ currentAuditRecord.originalScore }}分</span>
            <span v-if="currentAuditRecord.finalScore">最终得分: {{ currentAuditRecord.finalScore }}分</span>
            <span v-if="currentAuditRecord.originalScore && currentAuditRecord.finalScore">
              变化: {{ currentAuditRecord.finalScore - currentAuditRecord.originalScore > 0 ? '+' : '' }}{{ currentAuditRecord.finalScore - currentAuditRecord.originalScore }}分
            </span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="审核意见">
          {{ currentAuditRecord.comments || '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="修改建议" v-if="currentAuditRecord.suggestions">
          {{ currentAuditRecord.suggestions }}
        </a-descriptions-item>
        <a-descriptions-item label="附件" v-if="currentAuditRecord.attachments && currentAuditRecord.attachments.length > 0">
          <a-space>
            <a-tag v-for="(attachment, index) in currentAuditRecord.attachments" :key="index">
              {{ attachment }}
            </a-tag>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 时间轴视图弹窗 -->
    <a-modal 
      title="审核时间轴" 
      :visible="timelineVisible" 
      @cancel="timelineVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="timeline-container">
        <a-timeline>
          <a-timeline-item 
            v-for="record in sortedAuditRecords" 
            :key="record.id"
            :color="getTimelineColor(record.auditResult)"
          >
            <template #dot>
              <audit-outlined :style="{ fontSize: '16px' }" />
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="object-name">{{ record.cultivationObjectName }}</span>
                <span class="audit-time">{{ record.auditTime }}</span>
              </div>
              <div class="timeline-body">
                <div class="audit-info">
                  <a-tag :color="getAuditTypeColor(record.auditType)" size="small">
                    {{ getAuditTypeText(record.auditType) }}
                  </a-tag>
                  <a-tag :color="getAuditResultColor(record.auditResult)" size="small">
                    {{ getAuditResultText(record.auditResult) }}
                  </a-tag>
                  <span class="auditor">审核人: {{ record.auditor }}</span>
                </div>
                <div class="audit-comments" v-if="record.comments">
                  {{ record.comments }}
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>

    <!-- 记录对比弹窗 -->
    <a-modal 
      title="记录对比" 
      :visible="compareVisible" 
      @cancel="compareVisible = false" 
      :footer="null" 
      width="1000px"
    >
      <div class="compare-container" v-if="compareRecords.length === 2">
        <a-row :gutter="16">
          <a-col :span="12" v-for="(record, index) in compareRecords" :key="record.id">
            <a-card :title="`记录 ${index + 1}`" size="small">
              <a-descriptions bordered :column="1" size="small">
                <a-descriptions-item label="培育对象">{{ record.cultivationObjectName }}</a-descriptions-item>
                <a-descriptions-item label="审核类型">
                  <a-tag :color="getAuditTypeColor(record.auditType)">
                    {{ getAuditTypeText(record.auditType) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="审核结果">
                  <a-tag :color="getAuditResultColor(record.auditResult)">
                    {{ getAuditResultText(record.auditResult) }}
                  </a-tag>
                </a-descriptions-item>
                <a-descriptions-item label="审核人">{{ record.auditor }}</a-descriptions-item>
                <a-descriptions-item label="审核时间">{{ record.auditTime }}</a-descriptions-item>
                <a-descriptions-item label="得分变化">
                  {{ record.originalScore || 0 }} → {{ record.finalScore || 0 }}
                </a-descriptions-item>
                <a-descriptions-item label="审核意见">{{ record.comments || '无' }}</a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useAuditConfirmationStore } from '@/store/modules/audit-confirmation'
import type { AuditRecord, AuditSearchParams } from '@/types/audit-confirmation'
import {
  AuditTypeTextMap,
  AuditResultTextMap,
  AuditResultColorMap
} from '@/types/audit-confirmation'
import {
  ClockCircleOutlined,
  ExportOutlined,
  ReloadOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  ArrowRightOutlined
} from '@ant-design/icons-vue'

// 使用store
const auditConfirmationStore = useAuditConfirmationStore()

// 审核类型选项
const AuditTypeOptions = [
  { label: '初审', value: 1 },
  { label: '复审', value: 2 },
  { label: '终审', value: 3 }
]

// 审核结果选项
const AuditResultOptions = [
  { label: '通过', value: 1 },
  { label: '不通过', value: 2 },
  { label: '待补充', value: 3 }
]

// 响应式数据
const searchForm = ref<AuditSearchParams & { auditType?: number; auditResult?: number }>({
  cultivationObjectName: '',
  auditType: undefined,
  auditResult: undefined,
  auditor: '',
  dateRange: undefined
})

const historyDetailVisible = ref(false)
const timelineVisible = ref(false)
const compareVisible = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterAuditResult = ref<number | undefined>(undefined)

// 当前选中的审核记录
const currentAuditRecord = ref<AuditRecord | null>(null)

// 对比记录
const compareRecords = ref<AuditRecord[]>([])

// 计算属性
const filteredAuditRecordList = computed(() => {
  let filtered = [...auditConfirmationStore.auditRecordList]

  if (filterAuditResult.value !== undefined) {
    filtered = filtered.filter(item => item.auditResult === filterAuditResult.value)
  }

  return filtered
})

const sortedAuditRecords = computed(() => {
  return [...auditConfirmationStore.auditRecordList].sort((a, b) => 
    new Date(b.auditTime).getTime() - new Date(a.auditTime).getTime()
  )
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '培育对象名称',
    dataIndex: 'cultivationObjectName',
    key: 'cultivationObjectName',
    width: 200
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '审核类型',
    key: 'auditType',
    width: 100,
    align: 'center'
  },
  {
    title: '审核人',
    dataIndex: 'auditor',
    key: 'auditor',
    width: 100
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    key: 'auditTime',
    width: 180
  },
  {
    title: '审核结果',
    key: 'auditResult',
    width: 120,
    align: 'center'
  },
  {
    title: '评分变化',
    key: 'scoreChange',
    width: 120,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: AuditRecord) {
  return filteredAuditRecordList.value.findIndex(item => item.id === record.id) + 1
}

function getAuditTypeText(type: number) {
  return AuditTypeTextMap[type as keyof typeof AuditTypeTextMap] || '未知'
}

function getAuditTypeColor(type: number) {
  const colors = { 1: 'blue', 2: 'green', 3: 'purple' }
  return colors[type as keyof typeof colors] || 'default'
}

function getAuditResultText(result: number) {
  return AuditResultTextMap[result as keyof typeof AuditResultTextMap] || '未知'
}

function getAuditResultColor(result: number) {
  return AuditResultColorMap[result as keyof typeof AuditResultColorMap] || 'default'
}

function getTimelineColor(result: number) {
  const colors = { 1: 'green', 2: 'red', 3: 'orange' }
  return colors[result as keyof typeof colors] || 'blue'
}

function getAuditResultCount(result: number) {
  return auditConfirmationStore.auditRecordList.filter(item => item.auditResult === result).length
}

async function refreshData() {
  await auditConfirmationStore.fetchAuditRecordList()
  message.success('数据刷新成功')
}

function showHistoryDetail(record: AuditRecord) {
  currentAuditRecord.value = record
  historyDetailVisible.value = true
}

function viewRelatedRecords(record: AuditRecord) {
  message.info(`查看 ${record.cultivationObjectName} 的相关记录`)
  // 这里可以筛选显示同一培育对象的所有审核记录
}

function showTimelineView() {
  timelineVisible.value = true
}

function showCompareModal() {
  if (selectedRowKeys.value.length !== 2) {
    message.warning('请选择两条记录进行对比')
    return
  }
  
  compareRecords.value = auditConfirmationStore.auditRecordList.filter(
    record => selectedRowKeys.value.includes(record.id)
  )
  compareVisible.value = true
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

function exportHistoryData() {
  message.info('历史数据导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await auditConfirmationStore.fetchAuditRecordList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    cultivationObjectName: '',
    auditType: undefined,
    auditResult: undefined,
    auditor: '',
    dateRange: undefined
  }
  auditConfirmationStore.fetchAuditRecordList()
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.audit-history-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .score-change {
      display: flex;
      align-items: center;
      gap: 4px;

      .original-score {
        color: #666;
      }

      .arrow {
        color: #1890ff;
      }

      .final-score {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .score-change-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;

    span {
      font-size: 12px;
    }
  }

  .timeline-container {
    max-height: 500px;
    overflow-y: auto;

    .timeline-content {
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .object-name {
          font-weight: 600;
          color: #333;
        }

        .audit-time {
          color: #666;
          font-size: 12px;
        }
      }

      .timeline-body {
        .audit-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .auditor {
            color: #666;
            font-size: 12px;
          }
        }

        .audit-comments {
          color: #333;
          font-size: 13px;
          line-height: 1.4;
          background: #f5f5f5;
          padding: 8px;
          border-radius: 4px;
        }
      }
    }
  }

  .compare-container {
    .ant-card {
      height: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .audit-history-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .score-change {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .timeline-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .audit-info {
      flex-wrap: wrap;
    }

    .compare-container {
      .ant-row {
        flex-direction: column;
      }

      .ant-col {
        width: 100% !important;
        margin-bottom: 16px;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.audit-history-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .score-change {
      display: flex;
      align-items: center;
      gap: 4px;

      .original-score {
        color: #666;
      }

      .arrow {
        color: #1890ff;
      }

      .final-score {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .score-change-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;

    span {
      font-size: 12px;
    }
  }

  .timeline-container {
    max-height: 500px;
    overflow-y: auto;

    .timeline-content {
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .object-name {
          font-weight: 600;
          color: #333;
        }

        .audit-time {
          color: #666;
          font-size: 12px;
        }
      }

      .timeline-body {
        .audit-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .auditor {
            color: #666;
            font-size: 12px;
          }
        }

        .audit-comments {
          color: #333;
          font-size: 13px;
          line-height: 1.4;
          background: #f5f5f5;
          padding: 8px;
          border-radius: 4px;
        }
      }
    }
  }

  .compare-container {
    .ant-card {
      height: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .audit-history-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .score-change {
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
    }

    .timeline-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .audit-info {
      flex-wrap: wrap;
    }

    .compare-container {
      .ant-row {
        flex-direction: column;
      }

      .ant-col {
        width: 100% !important;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
