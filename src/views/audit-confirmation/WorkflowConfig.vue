<template>
  <div class="workflow-config-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>审核流程配置</h2>
        <p>配置审核流程节点、审核标准、审核周期等，支持自定义审核流程</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建流程
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportConfig">
            <template #icon><export-outlined /></template>
            导出配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 流程配置列表 -->
    <div class="workflow-list-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>审核流程列表</span>
            <span class="record-count">共 {{ workflowList.length }} 条配置</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="workflowList"
          :loading="loading"
          :pagination="paginationConfig"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="record.isActive ? 'green' : 'default'">
                {{ record.isActive ? '启用' : '停用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'nodeCount'">
              <a-badge :count="record.nodes.length" />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button type="link" size="small" @click="editWorkflow(record)">编辑</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="toggleStatus(record)"
                  :style="{ color: record.isActive ? '#ff4d4f' : '#52c41a' }"
                >
                  {{ record.isActive ? '停用' : '启用' }}
                </a-button>
                <a-popconfirm
                  title="确定要删除这个流程配置吗？"
                  @confirm="deleteWorkflow(record.id)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 流程配置表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="900px"
      :mask-closable="false"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-divider orientation="left">基本信息</a-divider>
        <a-form-item label="流程名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入流程名称" />
        </a-form-item>
        <a-form-item label="流程描述" name="description">
          <a-textarea v-model:value="form.description" :rows="2" placeholder="请输入流程描述" />
        </a-form-item>
        <a-form-item label="审核标准" name="reviewCriteria">
          <a-textarea v-model:value="form.reviewCriteria" :rows="3" placeholder="请输入审核标准和要求" />
        </a-form-item>
        <a-form-item label="审核周期" name="reviewCycle">
          <a-input-number 
            v-model:value="form.reviewCycle" 
            :min="1" 
            :max="30" 
            addon-after="天"
            placeholder="请输入审核周期"
          />
        </a-form-item>

        <a-divider orientation="left">流程节点配置</a-divider>
        <div class="workflow-nodes">
          <div v-for="(node, index) in form.nodes" :key="index" class="node-item">
            <a-card size="small">
              <template #title>
                <span>节点 {{ index + 1 }}</span>
                <a-button 
                  v-if="form.nodes.length > 1" 
                  type="link" 
                  size="small" 
                  danger 
                  @click="removeNode(index)"
                >
                  删除
                </a-button>
              </template>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item :name="['nodes', index, 'name']" label="节点名称">
                    <a-input v-model:value="node.name" placeholder="请输入节点名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['nodes', index, 'type']" label="节点类型">
                    <a-select v-model:value="node.type" placeholder="请选择节点类型">
                      <a-select-option value="start">开始节点</a-select-option>
                      <a-select-option value="review">审核节点</a-select-option>
                      <a-select-option value="approve">审批节点</a-select-option>
                      <a-select-option value="end">结束节点</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['nodes', index, 'reviewer']" label="审核人员">
                    <a-select v-model:value="node.reviewer" placeholder="请选择审核人员" mode="multiple">
                      <a-select-option value="admin">系统管理员</a-select-option>
                      <a-select-option value="reviewer1">审核员1</a-select-option>
                      <a-select-option value="reviewer2">审核员2</a-select-option>
                      <a-select-option value="manager">部门经理</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :name="['nodes', index, 'timeout']" label="超时时间">
                    <a-input-number 
                      v-model:value="node.timeout" 
                      :min="1" 
                      :max="72" 
                      addon-after="小时"
                      placeholder="超时时间"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </div>
          <a-button @click="addNode" type="dashed" style="width: 100%; margin-top: 16px">
            <template #icon><plus-outlined /></template>
            添加流程节点
          </a-button>
        </div>

        <a-form-item :wrapper-col="{ offset: 4, span: 18 }" style="margin-top: 24px">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button @click="previewWorkflow">预览流程</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 流程详情弹窗 -->
    <a-modal 
      title="审核流程详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentWorkflow">
        <a-descriptions-item label="流程名称">{{ currentWorkflow.name }}</a-descriptions-item>
        <a-descriptions-item label="流程描述">{{ currentWorkflow.description }}</a-descriptions-item>
        <a-descriptions-item label="审核标准">{{ currentWorkflow.reviewCriteria }}</a-descriptions-item>
        <a-descriptions-item label="审核周期">{{ currentWorkflow.reviewCycle }}天</a-descriptions-item>
        <a-descriptions-item label="启用状态">
          <a-tag :color="currentWorkflow.isActive ? 'green' : 'default'">
            {{ currentWorkflow.isActive ? '启用' : '停用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentWorkflow.createTime }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ currentWorkflow.updateTime }}</a-descriptions-item>
      </a-descriptions>
      
      <a-divider>流程节点</a-divider>
      <div class="workflow-preview">
        <div v-for="(node, index) in currentWorkflow?.nodes || []" :key="index" class="preview-node">
          <div class="node-info">
            <div class="node-title">{{ node.name }}</div>
            <div class="node-meta">
              <a-tag :color="getNodeTypeColor(node.type)">{{ getNodeTypeName(node.type) }}</a-tag>
              <span>审核人：{{ (node.reviewer || []).join(', ') }}</span>
              <span>超时：{{ node.timeout }}小时</span>
            </div>
          </div>
          <div v-if="index < (currentWorkflow?.nodes.length || 0) - 1" class="node-arrow">↓</div>
        </div>
      </div>
    </a-modal>

    <!-- 流程预览弹窗 -->
    <a-modal 
      title="流程预览" 
      :visible="previewVisible" 
      @cancel="previewVisible = false" 
      :footer="null" 
      width="700px"
    >
      <div class="workflow-preview">
        <div v-for="(node, index) in form.nodes" :key="index" class="preview-node">
          <div class="node-info">
            <div class="node-title">{{ node.name || `节点${index + 1}` }}</div>
            <div class="node-meta">
              <a-tag :color="getNodeTypeColor(node.type)">{{ getNodeTypeName(node.type) }}</a-tag>
              <span v-if="node.reviewer && node.reviewer.length">
                审核人：{{ node.reviewer.join(', ') }}
              </span>
              <span v-if="node.timeout">超时：{{ node.timeout }}小时</span>
            </div>
          </div>
          <div v-if="index < form.nodes.length - 1" class="node-arrow">↓</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

// 工作流节点接口
interface WorkflowNode {
  name: string
  type: 'start' | 'review' | 'approve' | 'end'
  reviewer: string[]
  timeout: number
}

// 工作流配置接口
interface WorkflowConfig {
  id?: string
  name: string
  description: string
  reviewCriteria: string
  reviewCycle: number
  nodes: WorkflowNode[]
  isActive: boolean
  createTime?: string
  updateTime?: string
}

// 响应式数据
const loading = ref(false)
const formModalVisible = ref(false)
const detailVisible = ref(false)
const previewVisible = ref(false)
const submitting = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentWorkflow = ref<WorkflowConfig | null>(null)

// 工作流列表数据
const workflowList = ref<WorkflowConfig[]>([
  {
    id: '1',
    name: '标准审核流程',
    description: '适用于一般申报项目的标准审核流程',
    reviewCriteria: '1. 资格审查：申报单位资质、项目符合性\n2. 材料审核：申报材料完整性、真实性\n3. 专业评审：项目技术方案、创新性评估',
    reviewCycle: 7,
    nodes: [
      { name: '申报提交', type: 'start', reviewer: [], timeout: 0 },
      { name: '资格预审', type: 'review', reviewer: ['admin'], timeout: 24 },
      { name: '专业评审', type: 'review', reviewer: ['reviewer1', 'reviewer2'], timeout: 48 },
      { name: '最终审批', type: 'approve', reviewer: ['manager'], timeout: 24 },
      { name: '结果确认', type: 'end', reviewer: [], timeout: 0 }
    ],
    isActive: true,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-03 15:30:00'
  },
  {
    id: '2',
    name: '快速审核流程',
    description: '适用于紧急或简单项目的快速审核流程',
    reviewCriteria: '1. 基础资格审查\n2. 材料完整性检查\n3. 快速评审确认',
    reviewCycle: 3,
    nodes: [
      { name: '申报提交', type: 'start', reviewer: [], timeout: 0 },
      { name: '快速预审', type: 'review', reviewer: ['admin'], timeout: 12 },
      { name: '直接审批', type: 'approve', reviewer: ['manager'], timeout: 24 },
      { name: '结果确认', type: 'end', reviewer: [], timeout: 0 }
    ],
    isActive: false,
    createTime: '2025-01-02 14:00:00',
    updateTime: '2025-01-02 14:00:00'
  }
])

// 表单数据
const form = ref<WorkflowConfig>({
  name: '',
  description: '',
  reviewCriteria: '',
  reviewCycle: 7,
  nodes: [
    { name: '申报提交', type: 'start', reviewer: [], timeout: 0 }
  ],
  isActive: true
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
    { min: 2, max: 50, message: '流程名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入流程描述', trigger: 'blur' }
  ],
  reviewCriteria: [
    { required: true, message: '请输入审核标准', trigger: 'blur' }
  ],
  reviewCycle: [
    { required: true, message: '请输入审核周期', trigger: 'blur' },
    { type: 'number', min: 1, max: 30, message: '审核周期应在1-30天之间', trigger: 'blur' }
  ]
}

// 计算属性
const modalTitle = computed(() => {
  return formMode.value === 'create' ? '新建审核流程' : '编辑审核流程'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '创建流程' : '更新流程'
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '流程名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '流程描述',
    dataIndex: 'description',
    key: 'description',
    width: 250,
    ellipsis: true
  },
  {
    title: '审核周期',
    dataIndex: 'reviewCycle',
    key: 'reviewCycle',
    width: 100,
    align: 'center',
    customRender: ({ text }) => `${text}天`
  },
  {
    title: '节点数量',
    key: 'nodeCount',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: WorkflowConfig) {
  return workflowList.value.findIndex(item => item.id === record.id) + 1
}

function getNodeTypeName(type: string) {
  const map = {
    start: '开始节点',
    review: '审核节点', 
    approve: '审批节点',
    end: '结束节点'
  }
  return map[type] || '未知'
}

function getNodeTypeColor(type: string) {
  const map = {
    start: 'blue',
    review: 'orange',
    approve: 'green', 
    end: 'purple'
  }
  return map[type] || 'default'
}

function refreshData() {
  message.success('数据刷新成功')
}

function exportConfig() {
  message.info('配置导出功能开发中...')
}

function showCreateModal() {
  formMode.value = 'create'
  form.value = {
    name: '',
    description: '',
    reviewCriteria: '',
    reviewCycle: 7,
    nodes: [
      { name: '申报提交', type: 'start', reviewer: [], timeout: 0 }
    ],
    isActive: true
  }
  formModalVisible.value = true
}

function editWorkflow(record: WorkflowConfig) {
  formMode.value = 'edit'
  form.value = { ...record, nodes: [...record.nodes] }
  currentWorkflow.value = record
  formModalVisible.value = true
}

function showDetail(record: WorkflowConfig) {
  currentWorkflow.value = record
  detailVisible.value = true
}

function toggleStatus(record: WorkflowConfig) {
  record.isActive = !record.isActive
  record.updateTime = new Date().toLocaleString()
  message.success(`流程已${record.isActive ? '启用' : '停用'}`)
}

function deleteWorkflow(id: string) {
  const index = workflowList.value.findIndex(item => item.id === id)
  if (index > -1) {
    workflowList.value.splice(index, 1)
    message.success('删除成功')
  }
}

function addNode() {
  form.value.nodes.push({
    name: '',
    type: 'review',
    reviewer: [],
    timeout: 24
  })
}

function removeNode(index: number) {
  if (form.value.nodes.length > 1) {
    form.value.nodes.splice(index, 1)
  }
}

function previewWorkflow() {
  previewVisible.value = true
}

async function onSubmit() {
  try {
    submitting.value = true
    
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      const newWorkflow = {
        ...form.value,
        id: Date.now().toString(),
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      }
      workflowList.value.unshift(newWorkflow)
      message.success('审核流程创建成功')
    } else {
      const index = workflowList.value.findIndex(item => item.id === currentWorkflow.value?.id)
      if (index > -1) {
        workflowList.value[index] = {
          ...form.value,
          id: currentWorkflow.value!.id,
          createTime: currentWorkflow.value!.createTime,
          updateTime: new Date().toLocaleString()
        }
        message.success('审核流程更新成功')
      }
    }
    
    formModalVisible.value = false
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据加载
})
</script>

<style lang="scss" scoped>
.workflow-config-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .workflow-list-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .workflow-nodes {
    .node-item {
      margin-bottom: 16px;
    }
  }

  .workflow-preview {
    .preview-node {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;

      .node-info {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 2px solid #e9ecef;
        width: 100%;
        text-align: center;

        .node-title {
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 8px;
          color: #333;
        }

        .node-meta {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;
          font-size: 12px;
          color: #666;

          span {
            white-space: nowrap;
          }
        }
      }

      .node-arrow {
        font-size: 20px;
        color: #1890ff;
        margin: 8px 0;
        font-weight: bold;
      }

      &:last-child .node-arrow {
        display: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workflow-config-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
</style>