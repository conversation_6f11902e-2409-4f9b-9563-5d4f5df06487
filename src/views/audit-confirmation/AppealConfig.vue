<template>
  <div class="appeal-config-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>申诉流程配置</h2>
        <p>配置申诉处理流程、自动分配规则、通知模板等，确保申诉处理的规范性和效率</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建配置
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="testAutoAssign">
            <template #icon><experiment-outlined /></template>
            测试分配
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 配置概览卡片 -->
    <div class="overview-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="overview-card">
            <a-statistic
              title="配置总数"
              :value="currentConfigList.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <setting-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="overview-card">
            <a-statistic
              title="启用配置"
              :value="activeConfigCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8">
          <a-card class="overview-card">
            <a-statistic
              title="处理人员"
              :value="totalProcessors"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <team-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 标签页配置 -->
    <div class="config-tabs-section">
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="workflow" tab="流程配置">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="申诉处理流程配置" 
                  description="配置申诉提交后的处理流程，包括自动分配规则、处理时限、升级机制等。"
                  type="info" 
                  show-icon 
                  style="margin-bottom: 16px"
                />
              </div>
              
              <!-- 流程配置表格 -->
              <a-table
                :columns="workflowColumns"
                :data-source="workflowConfigs"
                :pagination="{ pageSize: 5 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-switch 
                      v-model:checked="record.isActive" 
                      @change="toggleConfigStatus(record)"
                    />
                  </template>
                  <template v-else-if="column.key === 'processorCount'">
                    <a-badge :count="record.processors.length" />
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="editWorkflowConfig(record)">编辑</a-button>
                      <a-button type="link" size="small" @click="previewWorkflow(record)">预览</a-button>
                      <a-popconfirm
                        title="确定要删除这个配置吗？"
                        @confirm="deleteWorkflowConfig(record.id)"
                      >
                        <a-button type="link" size="small" danger>删除</a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>

          <a-tab-pane key="assignment" tab="自动分配">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="自动分配规则配置" 
                  description="根据申诉类型、申诉原因、申诉人等条件，自动分配给相应的处理人员，提高处理效率。"
                  type="info" 
                  show-icon 
                  style="margin-bottom: 16px"
                />
              </div>

              <!-- 分配规则表格 -->
              <a-table
                :columns="assignmentColumns"
                :data-source="assignmentRules"
                :pagination="{ pageSize: 8 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'condition'">
                    <a-tag color="blue">{{ record.conditionType }}</a-tag>
                    <span>{{ record.conditionValue }}</span>
                  </template>
                  <template v-else-if="column.key === 'processor'">
                    <a-avatar-group size="small">
                      <a-avatar v-for="processor in record.assignedProcessors" :key="processor">
                        {{ processor.charAt(0) }}
                      </a-avatar>
                    </a-avatar-group>
                  </template>
                  <template v-else-if="column.key === 'priority'">
                    <a-tag :color="getPriorityColor(record.priority)">
                      {{ getPriorityText(record.priority) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="editAssignmentRule(record)">编辑</a-button>
                      <a-button type="link" size="small" @click="testAssignmentRule(record)">测试</a-button>
                      <a-popconfirm
                        title="确定要删除这个规则吗？"
                        @confirm="deleteAssignmentRule(record.id)"
                      >
                        <a-button type="link" size="small" danger>删除</a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>

          <a-tab-pane key="notification" tab="通知模板">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="通知模板配置" 
                  description="配置申诉提交、处理状态变更等环节的通知模板，支持邮件、短信、系统内通知等多种方式。"
                  type="info" 
                  show-icon 
                  style="margin-bottom: 16px"
                />
              </div>

              <!-- 通知模板表格 -->
              <a-table
                :columns="notificationColumns"
                :data-source="notificationTemplates"
                :pagination="{ pageSize: 6 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'type'">
                    <a-tag :color="getTemplateTypeColor(record.type)">
                      {{ getTemplateTypeName(record.type) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'channels'">
                    <a-tag v-for="channel in record.channels" :key="channel" size="small">
                      {{ channel }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button type="link" size="small" @click="editNotificationTemplate(record)">编辑</a-button>
                      <a-button type="link" size="small" @click="previewTemplate(record)">预览</a-button>
                      <a-button type="link" size="small" @click="testTemplate(record)">测试发送</a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 配置表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
      :mask-closable="false"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <!-- 流程配置表单 -->
        <div v-if="activeTab === 'workflow'">
          <a-form-item label="流程名称" name="name">
            <a-input v-model:value="form.name" placeholder="请输入流程名称" />
          </a-form-item>
          <a-form-item label="流程描述" name="description">
            <a-textarea v-model:value="form.description" :rows="2" placeholder="请输入流程描述" />
          </a-form-item>
          <a-form-item label="处理时限" name="timeLimit">
            <a-input-number 
              v-model:value="form.timeLimit" 
              :min="1" 
              :max="720" 
              addon-after="小时"
              placeholder="处理时限"
            />
          </a-form-item>
          <a-form-item label="处理人员" name="processors">
            <a-select 
              v-model:value="form.processors" 
              mode="multiple" 
              placeholder="请选择处理人员"
              :options="processorOptions"
            />
          </a-form-item>
          <a-form-item label="自动分配" name="autoAssign">
            <a-switch v-model:checked="form.autoAssign" />
            <span style="margin-left: 8px; color: #666;">
              {{ form.autoAssign ? '开启自动分配' : '手动分配' }}
            </span>
          </a-form-item>
        </div>

        <!-- 分配规则表单 -->
        <div v-if="activeTab === 'assignment'">
          <a-form-item label="规则名称" name="ruleName">
            <a-input v-model:value="form.ruleName" placeholder="请输入规则名称" />
          </a-form-item>
          <a-form-item label="条件类型" name="conditionType">
            <a-select v-model:value="form.conditionType" placeholder="请选择条件类型">
              <a-select-option value="appealReason">申诉原因</a-select-option>
              <a-select-option value="applicantType">申请人类型</a-select-option>
              <a-select-option value="urgencyLevel">紧急程度</a-select-option>
              <a-select-option value="appealAmount">申诉金额</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="条件值" name="conditionValue">
            <a-input v-model:value="form.conditionValue" placeholder="请输入条件值" />
          </a-form-item>
          <a-form-item label="分配给" name="assignedProcessors">
            <a-select 
              v-model:value="form.assignedProcessors" 
              mode="multiple" 
              placeholder="请选择处理人员"
              :options="processorOptions"
            />
          </a-form-item>
          <a-form-item label="优先级" name="priority">
            <a-select v-model:value="form.priority" placeholder="请选择优先级">
              <a-select-option :value="1">高</a-select-option>
              <a-select-option :value="2">中</a-select-option>
              <a-select-option :value="3">低</a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <!-- 通知模板表单 -->
        <div v-if="activeTab === 'notification'">
          <a-form-item label="模板名称" name="templateName">
            <a-input v-model:value="form.templateName" placeholder="请输入模板名称" />
          </a-form-item>
          <a-form-item label="模板类型" name="type">
            <a-select v-model:value="form.type" placeholder="请选择模板类型">
              <a-select-option value="submit">申诉提交</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="completed">处理完成</a-select-option>
              <a-select-option value="rejected">申诉驳回</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="通知渠道" name="channels">
            <a-checkbox-group v-model:value="form.channels">
              <a-checkbox value="email">邮件</a-checkbox>
              <a-checkbox value="sms">短信</a-checkbox>
              <a-checkbox value="system">系统通知</a-checkbox>
              <a-checkbox value="wechat">微信</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item label="模板标题" name="subject">
            <a-input v-model:value="form.subject" placeholder="请输入模板标题" />
          </a-form-item>
          <a-form-item label="模板内容" name="content">
            <a-textarea 
              v-model:value="form.content" 
              :rows="6" 
              placeholder="请输入模板内容，支持变量：{{applicantName}}, {{appealReason}}, {{processTime}}等"
            />
          </a-form-item>
        </div>

        <a-form-item :wrapper-col="{ offset: 4, span: 18 }" style="margin-top: 24px">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button v-if="activeTab === 'notification'" @click="previewCurrentTemplate">预览模板</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 模板预览弹窗 -->
    <a-modal 
      title="模板预览" 
      :visible="previewVisible" 
      @cancel="previewVisible = false" 
      :footer="null" 
      width="600px"
    >
      <div class="template-preview">
        <h4>{{ previewData.subject }}</h4>
        <div class="preview-content" v-html="previewData.content"></div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  ExperimentOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  TeamOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface WorkflowConfig {
  id: string
  name: string
  description: string
  timeLimit: number
  processors: string[]
  autoAssign: boolean
  isActive: boolean
}

interface AssignmentRule {
  id: string
  ruleName: string
  conditionType: string
  conditionValue: string
  assignedProcessors: string[]
  priority: number
}

interface NotificationTemplate {
  id: string
  templateName: string
  type: string
  channels: string[]
  subject: string
  content: string
}

// 响应式数据
const activeTab = ref('workflow')
const loading = ref(false)
const formModalVisible = ref(false)
const previewVisible = ref(false)
const submitting = ref(false)
const formMode = ref<'create' | 'edit'>('create')

// 表单数据
const form = ref<any>({})
const formRef = ref()

// 预览数据
const previewData = ref({
  subject: '',
  content: ''
})

// 处理人员选项
const processorOptions = [
  { label: '张三 - 高级审理员', value: 'zhangsan' },
  { label: '李四 - 审理专员', value: 'lisi' },
  { label: '王五 - 审理助理', value: 'wangwu' },
  { label: '赵六 - 部门主管', value: 'zhaoliu' }
]

// 模拟数据
const workflowConfigs = ref<WorkflowConfig[]>([
  {
    id: '1',
    name: '标准申诉处理流程',
    description: '适用于一般申诉案件的标准处理流程',
    timeLimit: 72,
    processors: ['zhangsan', 'lisi'],
    autoAssign: true,
    isActive: true
  },
  {
    id: '2',
    name: '紧急申诉处理流程',
    description: '适用于紧急申诉案件的快速处理流程',
    timeLimit: 24,
    processors: ['zhaoliu'],
    autoAssign: true,
    isActive: false
  }
])

const assignmentRules = ref<AssignmentRule[]>([
  {
    id: '1',
    ruleName: '评分有误申诉',
    conditionType: '申诉原因',
    conditionValue: '评分有误',
    assignedProcessors: ['zhangsan'],
    priority: 1
  },
  {
    id: '2',
    ruleName: '程序不当申诉',
    conditionType: '申诉原因',
    conditionValue: '程序不当',
    assignedProcessors: ['lisi', 'zhaoliu'],
    priority: 2
  }
])

const notificationTemplates = ref<NotificationTemplate[]>([
  {
    id: '1',
    templateName: '申诉提交确认',
    type: 'submit',
    channels: ['email', 'system'],
    subject: '申诉提交确认通知',
    content: '您好 {{applicantName}}，您的申诉已成功提交，我们将在72小时内处理。'
  },
  {
    id: '2',
    templateName: '申诉处理完成',
    type: 'completed',
    channels: ['email', 'sms', 'system'],
    subject: '申诉处理结果通知',
    content: '您好 {{applicantName}}，您的申诉处理已完成，处理结果：{{result}}。'
  }
])

// 计算属性
const currentConfigList = computed(() => {
  switch (activeTab.value) {
    case 'workflow': return workflowConfigs.value
    case 'assignment': return assignmentRules.value  
    case 'notification': return notificationTemplates.value
    default: return workflowConfigs.value
  }
})

const activeConfigCount = computed(() => workflowConfigs.value.filter(c => c.isActive).length)
const totalProcessors = computed(() => {
  const allProcessors = workflowConfigs.value.flatMap(c => c.processors)
  return new Set(allProcessors).size
})

const modalTitle = computed(() => {
  const typeMap = {
    workflow: '流程配置',
    assignment: '分配规则', 
    notification: '通知模板'
  }
  return `${formMode.value === 'create' ? '新建' : '编辑'}${typeMap[activeTab.value]}`
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '创建' : '更新'
})

// 表格列定义
const workflowColumns = [
  { title: '流程名称', dataIndex: 'name', key: 'name' },
  { title: '处理时限', dataIndex: 'timeLimit', key: 'timeLimit', customRender: ({ text }) => `${text}小时` },
  { title: '处理人数', key: 'processorCount', align: 'center' },
  { title: '自动分配', dataIndex: 'autoAssign', key: 'autoAssign', customRender: ({ text }) => text ? '是' : '否' },
  { title: '启用状态', key: 'status', align: 'center' },
  { title: '操作', key: 'action', align: 'center', width: 200 }
]

const assignmentColumns = [
  { title: '规则名称', dataIndex: 'ruleName', key: 'ruleName' },
  { title: '分配条件', key: 'condition' },
  { title: '处理人员', key: 'processor', align: 'center' },
  { title: '优先级', key: 'priority', align: 'center' },
  { title: '操作', key: 'action', align: 'center', width: 180 }
]

const notificationColumns = [
  { title: '模板名称', dataIndex: 'templateName', key: 'templateName' },
  { title: '模板类型', key: 'type', align: 'center' },
  { title: '通知渠道', key: 'channels' },
  { title: '模板标题', dataIndex: 'subject', key: 'subject', ellipsis: true },
  { title: '操作', key: 'action', align: 'center', width: 200 }
]

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入流程名称', trigger: 'blur' }],
  ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }]
}

// 方法定义
function handleTabChange(key: string) {
  activeTab.value = key
}

function showCreateModal() {
  formMode.value = 'create'
  resetForm()
  formModalVisible.value = true
}

function resetForm() {
  if (activeTab.value === 'workflow') {
    form.value = {
      name: '',
      description: '',
      timeLimit: 72,
      processors: [],
      autoAssign: true,
      isActive: true
    }
  } else if (activeTab.value === 'assignment') {
    form.value = {
      ruleName: '',
      conditionType: '',
      conditionValue: '',
      assignedProcessors: [],
      priority: 2
    }
  } else if (activeTab.value === 'notification') {
    form.value = {
      templateName: '',
      type: '',
      channels: [],
      subject: '',
      content: ''
    }
  }
}

function getPriorityText(priority: number) {
  const map = { 1: '高', 2: '中', 3: '低' }
  return map[priority] || '未知'
}

function getPriorityColor(priority: number) {
  const map = { 1: 'red', 2: 'orange', 3: 'green' }
  return map[priority] || 'default'
}

function getTemplateTypeName(type: string) {
  const map = {
    submit: '申诉提交',
    processing: '处理中',
    completed: '处理完成',
    rejected: '申诉驳回'
  }
  return map[type] || '未知'
}

function getTemplateTypeColor(type: string) {
  const map = {
    submit: 'blue',
    processing: 'orange', 
    completed: 'green',
    rejected: 'red'
  }
  return map[type] || 'default'
}

function refreshData() {
  message.success('数据刷新成功')
}

function testAutoAssign() {
  message.info('自动分配测试功能开发中...')
}

function toggleConfigStatus(record: WorkflowConfig) {
  record.isActive = !record.isActive
  message.success(`配置已${record.isActive ? '启用' : '停用'}`)
}

async function onSubmit() {
  try {
    submitting.value = true
    await formRef.value.validate()
    
    if (activeTab.value === 'workflow') {
      if (formMode.value === 'create') {
        const newConfig = { ...form.value, id: Date.now().toString() }
        workflowConfigs.value.push(newConfig)
      }
    } else if (activeTab.value === 'assignment') {
      if (formMode.value === 'create') {
        const newRule = { ...form.value, id: Date.now().toString() }
        assignmentRules.value.push(newRule)
      }
    } else if (activeTab.value === 'notification') {
      if (formMode.value === 'create') {
        const newTemplate = { ...form.value, id: Date.now().toString() }
        notificationTemplates.value.push(newTemplate)
      }
    }
    
    message.success(`${activeTab.value === 'workflow' ? '流程配置' : activeTab.value === 'assignment' ? '分配规则' : '通知模板'}${formMode.value === 'create' ? '创建' : '更新'}成功`)
    formModalVisible.value = false
  } catch (error) {
    message.error('操作失败，请检查输入')
  } finally {
    submitting.value = false
  }
}

function previewCurrentTemplate() {
  previewData.value = {
    subject: form.value.subject || '模板标题',
    content: (form.value.content || '模板内容').replace(/\{\{(\w+)\}\}/g, '<span style="background: #e6f7ff; padding: 2px 4px; border-radius: 3px;">[$1]</span>')
  }
  previewVisible.value = true
}

// 其他操作方法
function editWorkflowConfig(record: WorkflowConfig) {
  formMode.value = 'edit'
  form.value = { ...record }
  formModalVisible.value = true
}

function editAssignmentRule(record: AssignmentRule) {
  formMode.value = 'edit'
  form.value = { ...record }
  formModalVisible.value = true
}

function editNotificationTemplate(record: NotificationTemplate) {
  formMode.value = 'edit'
  form.value = { ...record }
  formModalVisible.value = true
}

function deleteWorkflowConfig(id: string) {
  const index = workflowConfigs.value.findIndex(item => item.id === id)
  if (index > -1) {
    workflowConfigs.value.splice(index, 1)
    message.success('删除成功')
  }
}

function deleteAssignmentRule(id: string) {
  const index = assignmentRules.value.findIndex(item => item.id === id)
  if (index > -1) {
    assignmentRules.value.splice(index, 1)
    message.success('删除成功')
  }
}

function previewWorkflow(record: WorkflowConfig) {
  message.info(`预览流程：${record.name}`)
}

function testAssignmentRule(record: AssignmentRule) {
  message.info(`测试分配规则：${record.ruleName}`)
}

function previewTemplate(record: NotificationTemplate) {
  previewData.value = {
    subject: record.subject,
    content: record.content.replace(/\{\{(\w+)\}\}/g, '<span style="background: #e6f7ff; padding: 2px 4px; border-radius: 3px;">[$1]</span>')
  }
  previewVisible.value = true
}

function testTemplate(record: NotificationTemplate) {
  message.info(`测试发送模板：${record.templateName}`)
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.appeal-config-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .overview-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .config-tabs-section {
    .tab-content {
      .tab-description {
        margin-bottom: 16px;
      }
    }
  }

  .template-preview {
    h4 {
      color: #1890ff;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 8px;
      margin-bottom: 16px;
    }

    .preview-content {
      padding: 16px;
      background: #fafafa;
      border-radius: 4px;
      line-height: 1.6;
      color: #333;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .appeal-config-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
</style>