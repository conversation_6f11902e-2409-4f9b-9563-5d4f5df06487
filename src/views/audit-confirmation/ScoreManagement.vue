<template>
  <div class="score-management-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>评分结果管理</h2>
        <p>培育对象评分结果查看和管理，支持评分修改、指标详情查看、历史追溯等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="batchUpdateScore" :disabled="selectedRowKeys.length === 0">
            <template #icon><edit-outlined /></template>
            批量修改
          </a-button>
          <a-button @click="exportScoreData">
            <template #icon><export-outlined /></template>
            导出评分
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 评分统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均得分"
              :value="auditConfirmationStore.avgScore"
              :precision="1"
              suffix="分"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="优秀(≥90分)"
              :value="getScoreRangeCount(90, 100)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="良好(80-89分)"
              :value="getScoreRangeCount(80, 89)"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <like-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待提升(<80分)"
              :value="getScoreRangeCount(0, 79)"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="项目名称" class="form-item-full">
                <a-select v-model:value="searchForm.projectId" placeholder="请选择项目" allow-clear>
                  <a-select-option v-for="project in auditConfirmationStore.projectList" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="培育对象" class="form-item-full">
                <a-input v-model:value="searchForm.cultivationObjectName" placeholder="请输入培育对象名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="得分范围" class="form-item-full">
                <a-select v-model:value="searchForm.scoreRange" placeholder="请选择得分范围" allow-clear>
                  <a-select-option value="90-100">优秀(90-100分)</a-select-option>
                  <a-select-option value="80-89">良好(80-89分)</a-select-option>
                  <a-select-option value="70-79">中等(70-79分)</a-select-option>
                  <a-select-option value="60-69">及格(60-69分)</a-select-option>
                  <a-select-option value="0-59">不及格(<60分)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择审核状态" allow-clear>
                  <a-select-option v-for="item in AuditStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="提交时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>评分结果列表</span>
            <span class="record-count">共 {{ filteredCultivationObjectList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button size="small" @click="showScoreDistribution">
              <template #icon><bar-chart-outlined /></template>
              得分分布
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredCultivationObjectList"
          :loading="auditConfirmationStore.loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1500 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'score'">
              <div class="score-cell">
                <a-badge 
                  :count="record.score || 0" 
                  :number-style="{ backgroundColor: getScoreColor(record.score || 0) }"
                />
                <a-progress 
                  :percent="record.score || 0" 
                  :stroke-color="getScoreColor(record.score || 0)"
                  size="small"
                  :show-info="false"
                  style="margin-left: 8px; width: 60px;"
                />
              </div>
            </template>
            <template v-else-if="column.key === 'scoreLevel'">
              <a-tag :color="getScoreLevelColor(record.score || 0)">
                {{ getScoreLevel(record.score || 0) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showScoreDetail(record)">查看详情</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="showEditScoreModal(record)"
                  :disabled="record.status === 1"
                >
                  修改评分
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewScoreHistory(record)"
                >
                  评分历史
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 评分详情弹窗 -->
    <a-modal 
      title="评分详情" 
      :visible="scoreDetailVisible" 
      @cancel="scoreDetailVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="score-detail" v-if="currentCultivationObject">
        <a-descriptions bordered :column="2" size="small">
          <a-descriptions-item label="培育对象" :span="2">{{ currentCultivationObject.name }}</a-descriptions-item>
          <a-descriptions-item label="所属项目">{{ currentCultivationObject.projectName }}</a-descriptions-item>
          <a-descriptions-item label="总得分">{{ currentCultivationObject.score || 0 }}分</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>指标详情</a-divider>
        
        <div class="indicators-detail">
          <div v-for="indicator in currentCultivationObject.indicators" :key="indicator.id" class="indicator-item">
            <div class="indicator-header">
              <span class="indicator-name">{{ indicator.indicatorName }}</span>
              <span class="indicator-code">{{ indicator.indicatorCode }}</span>
              <span class="indicator-weight">权重: {{ indicator.weight }}%</span>
            </div>
            <div class="indicator-score">
              <a-progress 
                :percent="Math.round((indicator.score / indicator.maxScore) * 100)"
                :stroke-color="getScoreColor(indicator.score)"
              />
              <span class="score-text">{{ indicator.score }}/{{ indicator.maxScore }}分</span>
            </div>
            <div class="indicator-desc">{{ indicator.description }}</div>
            <div v-if="indicator.details" class="indicator-details">详情: {{ indicator.details }}</div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 修改评分弹窗 -->
    <a-modal 
      title="修改评分" 
      :visible="editScoreVisible" 
      @cancel="editScoreVisible = false" 
      @ok="submitScoreUpdate"
      :confirm-loading="submitting"
      width="700px"
    >
      <div class="edit-score-form" v-if="currentCultivationObject">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="培育对象">{{ currentCultivationObject.name }}</a-descriptions-item>
          <a-descriptions-item label="当前总分">{{ currentCultivationObject.score || 0 }}分</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>指标评分</a-divider>
        
        <div class="indicators-edit">
          <div v-for="(indicator, index) in editIndicators" :key="indicator.id" class="indicator-edit-item">
            <div class="indicator-info">
              <span class="name">{{ indicator.indicatorName }}</span>
              <span class="weight">权重: {{ indicator.weight }}%</span>
              <span class="max-score">满分: {{ indicator.maxScore }}</span>
            </div>
            <div class="score-input">
              <a-input-number 
                v-model:value="indicator.score" 
                :min="0" 
                :max="indicator.maxScore" 
                :placeholder="`请输入得分(0-${indicator.maxScore})`"
                style="width: 150px"
              />
            </div>
            <div class="indicator-desc">{{ indicator.description }}</div>
          </div>
        </div>
        
        <a-divider>修改说明</a-divider>
        
        <a-form-item label="修改原因">
          <a-textarea 
            v-model:value="editScoreForm.comments" 
            :rows="3" 
            placeholder="请输入修改原因"
          />
        </a-form-item>
        
        <div class="total-score-preview">
          <a-statistic title="修改后总分" :value="calculateNewTotalScore()" suffix="分" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useAuditConfirmationStore } from '@/store/modules/audit-confirmation'
import type { CultivationObject, IndicatorResult, AuditSearchParams } from '@/types/audit-confirmation'
import {
  AuditStatusOptions,
  AuditStatusTextMap,
  AuditStatusColorMap
} from '@/types/audit-confirmation'
import {
  EditOutlined,
  ExportOutlined,
  ReloadOutlined,
  TrophyOutlined,
  StarOutlined,
  LikeOutlined,
  WarningOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

// 使用store
const auditConfirmationStore = useAuditConfirmationStore()

// 响应式数据
const searchForm = ref<AuditSearchParams & { scoreRange?: string }>({
  projectId: undefined,
  cultivationObjectName: '',
  status: undefined,
  scoreRange: undefined,
  dateRange: undefined
})

const scoreDetailVisible = ref(false)
const editScoreVisible = ref(false)
const submitting = ref(false)
const selectedRowKeys = ref<number[]>([])

// 当前选中的培育对象
const currentCultivationObject = ref<CultivationObject | null>(null)

// 编辑评分表单数据
const editScoreForm = ref({
  comments: ''
})

// 编辑中的指标数据
const editIndicators = ref<IndicatorResult[]>([])

// 计算属性
const filteredCultivationObjectList = computed(() => {
  let filtered = [...auditConfirmationStore.cultivationObjectList]

  // 根据得分范围筛选
  if (searchForm.value.scoreRange) {
    const [min, max] = searchForm.value.scoreRange.split('-').map(Number)
    filtered = filtered.filter(item => {
      const score = item.score || 0
      return score >= min && score <= max
    })
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '培育对象名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '申报单位',
    dataIndex: 'organizationName',
    key: 'organizationName',
    width: 160
  },
  {
    title: '得分',
    key: 'score',
    width: 150,
    align: 'center'
  },
  {
    title: '等级',
    key: 'scoreLevel',
    width: 100,
    align: 'center'
  },
  {
    title: '审核状态',
    key: 'status',
    width: 120,
    align: 'center'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: CultivationObject) {
  return filteredCultivationObjectList.value.findIndex(item => item.id === record.id) + 1
}

function getStatusText(status: number) {
  return AuditStatusTextMap[status as keyof typeof AuditStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return AuditStatusColorMap[status as keyof typeof AuditStatusColorMap] || 'default'
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#ff4d4f'
}

function getScoreLevel(score: number) {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '不及格'
}

function getScoreLevelColor(score: number) {
  if (score >= 90) return 'success'
  if (score >= 80) return 'processing'
  if (score >= 70) return 'warning'
  if (score >= 60) return 'default'
  return 'error'
}

function getScoreRangeCount(min: number, max: number) {
  return auditConfirmationStore.cultivationObjectList.filter(item => {
    const score = item.score || 0
    return score >= min && score <= max
  }).length
}

function calculateNewTotalScore() {
  return editIndicators.value.reduce((total, indicator) => total + (indicator.score || 0), 0)
}

async function refreshData() {
  await Promise.all([
    auditConfirmationStore.fetchProjectList(),
    auditConfirmationStore.fetchCultivationObjectList()
  ])
  message.success('数据刷新成功')
}

function showScoreDetail(record: CultivationObject) {
  currentCultivationObject.value = record
  scoreDetailVisible.value = true
}

function showEditScoreModal(record: CultivationObject) {
  currentCultivationObject.value = record
  editIndicators.value = JSON.parse(JSON.stringify(record.indicators)) // 深拷贝
  editScoreForm.value.comments = ''
  editScoreVisible.value = true
}

function viewScoreHistory(record: CultivationObject) {
  message.info(`查看 ${record.name} 的评分历史`)
  // 这里可以跳转到评分历史页面
}

function showScoreDistribution() {
  message.info('得分分布图功能开发中...')
}

async function submitScoreUpdate() {
  try {
    submitting.value = true

    if (!editScoreForm.value.comments.trim()) {
      message.error('请输入修改原因')
      return
    }

    // 模拟批量更新指标评分
    for (const indicator of editIndicators.value) {
      const updateData = {
        cultivationObjectId: currentCultivationObject.value!.id,
        indicatorId: indicator.id,
        score: indicator.score,
        comments: editScoreForm.value.comments
      }

      await auditConfirmationStore.updateScore(updateData)
    }

    message.success('评分修改成功')
    editScoreVisible.value = false
    await refreshData()
  } catch (error) {
    message.error('评分修改失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchUpdateScore() {
  message.info('批量修改评分功能开发中...')
}

function exportScoreData() {
  message.info('评分数据导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await auditConfirmationStore.fetchCultivationObjectList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    projectId: undefined,
    cultivationObjectName: '',
    status: undefined,
    scoreRange: undefined,
    dateRange: undefined
  }
  auditConfirmationStore.fetchCultivationObjectList()
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.score-management-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .score-cell {
      display: flex;
      align-items: center;
    }
  }

  .score-detail {
    .indicators-detail {
      .indicator-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;

        .indicator-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .indicator-name {
            font-weight: 600;
            color: #333;
          }

          .indicator-code {
            color: #666;
            font-size: 12px;
          }

          .indicator-weight {
            color: #1890ff;
            font-size: 12px;
          }
        }

        .indicator-score {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .score-text {
            margin-left: 12px;
            font-weight: 600;
            color: #333;
          }
        }

        .indicator-desc {
          color: #666;
          font-size: 12px;
          margin-bottom: 4px;
        }

        .indicator-details {
          color: #999;
          font-size: 11px;
        }
      }
    }
  }

  .edit-score-form {
    .indicators-edit {
      .indicator-edit-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;

        .indicator-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .name {
            font-weight: 600;
            color: #333;
          }

          .weight {
            color: #1890ff;
            font-size: 12px;
          }

          .max-score {
            color: #666;
            font-size: 12px;
          }
        }

        .score-input {
          margin-bottom: 8px;
        }

        .indicator-desc {
          color: #666;
          font-size: 12px;
        }
      }
    }

    .total-score-preview {
      text-align: center;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 6px;
      margin-top: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .score-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .score-cell {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .indicator-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .indicator-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
}
</style>
