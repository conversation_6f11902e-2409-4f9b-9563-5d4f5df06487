<template>
  <div class="project-audit-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>项目审核管理</h2>
        <p>培育对象评分结果审核管理，支持单个审核、批量审核、审核意见填写等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="batchAudit" :disabled="selectedRowKeys.length === 0">
            <template #icon><check-outlined /></template>
            批量审核
          </a-button>
          <a-button @click="exportAuditData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 审核统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待审核"
              :value="auditConfirmationStore.pendingAuditCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已审核"
              :value="auditConfirmationStore.auditedCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已退回"
              :value="auditConfirmationStore.rejectedCount"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均得分"
              :value="auditConfirmationStore.avgScore"
              :precision="1"
              suffix="分"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="项目名称" class="form-item-full">
                <a-select v-model:value="searchForm.projectId" placeholder="请选择项目" allow-clear>
                  <a-select-option v-for="project in auditConfirmationStore.projectList" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="培育对象" class="form-item-full">
                <a-input v-model:value="searchForm.cultivationObjectName" placeholder="请输入培育对象名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择审核状态" allow-clear>
                  <a-select-option v-for="item in AuditStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核人" class="form-item-full">
                <a-input v-model:value="searchForm.auditor" placeholder="请输入审核人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="提交时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>培育对象列表</span>
            <span class="record-count">共 {{ auditConfirmationStore.cultivationObjectList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterStatus" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in AuditStatusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredCultivationObjectList"
          :loading="auditConfirmationStore.loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'score'">
              <span v-if="record.score">
                <a-badge 
                  :count="record.score" 
                  :number-style="{ backgroundColor: getScoreColor(record.score) }"
                />
              </span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 1" 
                  type="link" 
                  size="small" 
                  @click="showAuditModal(record)"
                >
                  审核
                </a-button>
                <a-button 
                  v-if="record.status !== 1" 
                  type="link" 
                  size="small" 
                  @click="showScoreModal(record)"
                >
                  查看评分
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewAuditHistory(record)"
                >
                  审核历史
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 培育对象详情弹窗 -->
    <a-modal 
      title="培育对象详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentCultivationObject">
        <a-descriptions-item label="培育对象名称">{{ currentCultivationObject.name }}</a-descriptions-item>
        <a-descriptions-item label="所属项目">{{ currentCultivationObject.projectName }}</a-descriptions-item>
        <a-descriptions-item label="申报单位">{{ currentCultivationObject.organizationName }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ currentCultivationObject.contactPerson }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ currentCultivationObject.contactPhone }}</a-descriptions-item>
        <a-descriptions-item label="审核状态">
          <a-tag :color="getStatusColor(currentCultivationObject.status)">
            {{ getStatusText(currentCultivationObject.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="提交时间">{{ currentCultivationObject.submitTime }}</a-descriptions-item>
        <a-descriptions-item label="当前得分">
          {{ currentCultivationObject.score || '未评分' }}
        </a-descriptions-item>
        <a-descriptions-item label="审核时间">
          {{ currentCultivationObject.auditTime || '未审核' }}
        </a-descriptions-item>
        <a-descriptions-item label="审核人">
          {{ currentCultivationObject.auditor || '未分配' }}
        </a-descriptions-item>
        <a-descriptions-item label="审核意见">
          {{ currentCultivationObject.auditComments || '暂无意见' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 审核弹窗 -->
    <a-modal 
      title="审核确认" 
      :visible="auditVisible" 
      @cancel="auditVisible = false" 
      @ok="submitAudit"
      :confirm-loading="submitting"
      width="600px"
    >
      <div class="audit-form" v-if="currentCultivationObject">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="培育对象">{{ currentCultivationObject.name }}</a-descriptions-item>
          <a-descriptions-item label="所属项目">{{ currentCultivationObject.projectName }}</a-descriptions-item>
          <a-descriptions-item label="当前得分">{{ currentCultivationObject.score || '未评分' }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审核意见</a-divider>
        
        <a-form :model="auditForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="审核结果" required>
            <a-radio-group v-model:value="auditForm.auditResult">
              <a-radio :value="1">通过</a-radio>
              <a-radio :value="2">不通过</a-radio>
              <a-radio :value="3">待补充</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="调整得分" v-if="auditForm.auditResult === 1">
            <a-input-number 
              v-model:value="auditForm.score" 
              :min="0" 
              :max="100" 
              placeholder="请输入调整后得分"
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="审核意见" required>
            <a-textarea 
              v-model:value="auditForm.comments" 
              :rows="4" 
              placeholder="请输入审核意见"
            />
          </a-form-item>
          <a-form-item label="修改建议" v-if="auditForm.auditResult !== 1">
            <a-textarea 
              v-model:value="auditForm.suggestions" 
              :rows="3" 
              placeholder="请输入修改建议"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAuditConfirmationStore } from '@/store/modules/audit-confirmation'
import type { CultivationObject, AuditSearchParams } from '@/types/audit-confirmation'
import {
  AuditStatusOptions,
  AuditStatusTextMap,
  AuditStatusColorMap
} from '@/types/audit-confirmation'
import {
  CheckOutlined,
  ExportOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'

// 使用store
const router = useRouter()
const auditConfirmationStore = useAuditConfirmationStore()

// 响应式数据
const searchForm = ref<AuditSearchParams>({
  projectId: undefined,
  cultivationObjectName: '',
  status: undefined,
  auditor: '',
  dateRange: undefined
})

const detailVisible = ref(false)
const auditVisible = ref(false)
const submitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterStatus = ref<number | undefined>(undefined)

// 当前选中的培育对象
const currentCultivationObject = ref<CultivationObject | null>(null)

// 审核表单数据
const auditForm = ref({
  auditResult: 1,
  score: 0,
  comments: '',
  suggestions: ''
})

// 计算属性
const filteredCultivationObjectList = computed(() => {
  let filtered = [...auditConfirmationStore.cultivationObjectList]

  if (filterStatus.value !== undefined) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '培育对象名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '所属项目',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '申报单位',
    dataIndex: 'organizationName',
    key: 'organizationName',
    width: 160
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    key: 'contactPerson',
    width: 100
  },
  {
    title: '审核状态',
    key: 'status',
    width: 120,
    align: 'center'
  },
  {
    title: '得分',
    key: 'score',
    width: 100,
    align: 'center'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 240,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: CultivationObject) {
  return filteredCultivationObjectList.value.findIndex(item => item.id === record.id) + 1
}

function getStatusText(status: number) {
  return AuditStatusTextMap[status as keyof typeof AuditStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return AuditStatusColorMap[status as keyof typeof AuditStatusColorMap] || 'default'
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#ff4d4f'
}

async function refreshData() {
  await Promise.all([
    auditConfirmationStore.fetchProjectList(),
    auditConfirmationStore.fetchCultivationObjectList()
  ])
  message.success('数据刷新成功')
}

function showDetail(record: CultivationObject) {
  // 跳转到培育对象详情页面
  router.push({ 
    name: 'CultivationObjectDetail', 
    params: { id: record.id.toString() }
  })
}

function showAuditModal(record: CultivationObject) {
  currentCultivationObject.value = record
  auditForm.value = {
    auditResult: 1,
    score: record.score || 0,
    comments: '',
    suggestions: ''
  }
  auditVisible.value = true
}

function showScoreModal(record: CultivationObject) {
  message.info(`查看 ${record.name} 的详细评分`)
  // 这里可以跳转到评分管理页面或打开评分详情弹窗
}

function viewAuditHistory(record: CultivationObject) {
  message.info(`查看 ${record.name} 的审核历史`)
  // 这里可以跳转到审核历史页面
}

async function submitAudit() {
  try {
    submitting.value = true

    if (!auditForm.value.comments.trim()) {
      message.error('请输入审核意见')
      return
    }

    const auditData = {
      cultivationObjectId: currentCultivationObject.value!.id,
      auditResult: auditForm.value.auditResult,
      score: auditForm.value.score,
      comments: auditForm.value.comments,
      suggestions: auditForm.value.suggestions
    }

    const success = await auditConfirmationStore.submitAuditResult(auditData)
    if (success) {
      message.success('审核提交成功')
      auditVisible.value = false
      await refreshData()
    } else {
      message.error('审核提交失败')
    }
  } catch (error) {
    message.error('审核提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchAudit() {
  try {
    message.info('批量审核功能开发中...')
    // 实际实现中可以打开批量审核弹窗
  } catch (error) {
    message.error('批量审核失败')
  }
}

function exportAuditData() {
  message.info('数据导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await auditConfirmationStore.fetchCultivationObjectList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    projectId: undefined,
    cultivationObjectName: '',
    status: undefined,
    auditor: '',
    dateRange: undefined
  }
  auditConfirmationStore.fetchCultivationObjectList()
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.project-audit-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .audit-form {
    .ant-descriptions {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-audit-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
