<template>
  <a-modal
    :open="open"
    :title="`指标详情 - ${indicator?.indicatorName || ''}`"
    width="800px"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="indicator-detail-modal-content">
      <a-spin :spinning="loading">
        <!-- 指标基本信息 -->
        <div class="indicator-basic-info" v-if="indicatorDetail">
          <a-card size="small" title="基本信息">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="指标名称">
                {{ indicatorDetail.indicatorInfo.name }}
              </a-descriptions-item>
              <a-descriptions-item label="指标代码">
                <a-tag>{{ indicatorDetail.indicatorInfo.code }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="指标描述" :span="2">
                {{ indicatorDetail.indicatorInfo.description }}
              </a-descriptions-item>
              <a-descriptions-item label="计算方法" :span="2">
                {{ indicatorDetail.indicatorInfo.calculationMethod }}
              </a-descriptions-item>
              <a-descriptions-item label="分类权重">
                {{ indicatorDetail.indicatorInfo.weightInCategory }}%
              </a-descriptions-item>
              <a-descriptions-item label="数据源配置">
                <a-tag color="blue">{{ indicatorDetail.indicatorInfo.dataSourceConfig.type }}</a-tag>
                <span v-if="indicatorDetail.indicatorInfo.dataSourceConfig.updateFrequency">
                  更新频率: {{ indicatorDetail.indicatorInfo.dataSourceConfig.updateFrequency }}
                </span>
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </div>

        <!-- 评分详情 -->
        <div class="score-details-section" v-if="indicatorDetail">
          <a-card size="small" title="评分详情">
            <a-row :gutter="16">
              <a-col :span="12">
                <div class="score-summary">
                  <a-statistic
                    title="最终评分"
                    :value="indicatorDetail.scoreDetails.finalScore"
                    :precision="1"
                    suffix="分"
                    :value-style="{ color: getScoreColor(indicatorDetail.scoreDetails.finalScore) }"
                  />
                </div>
              </a-col>
              <a-col :span="12">
                <div class="score-explanation">
                  <h4>评分说明</h4>
                  <p>{{ indicatorDetail.scoreDetails.scoreExplanation }}</p>
                </div>
              </a-col>
            </a-row>

            <!-- 原始数据 -->
            <a-divider>原始数据</a-divider>
            <div class="raw-data-section">
              <a-descriptions :column="3" size="small">
                <a-descriptions-item
                  v-for="(value, key) in indicatorDetail.scoreDetails.rawData"
                  :key="key"
                  :label="formatDataKey(String(key))"
                >
                  {{ formatDataValue(value) }}
                </a-descriptions-item>
              </a-descriptions>
            </div>

            <!-- 计算步骤 -->
            <a-divider>计算步骤</a-divider>
            <div class="calculation-steps">
              <a-steps
                direction="vertical"
                size="small"
                :current="indicatorDetail.scoreDetails.calculationSteps.length"
              >
                <a-step
                  v-for="(step, index) in indicatorDetail.scoreDetails.calculationSteps"
                  :key="index"
                  :title="`步骤 ${index + 1}`"
                  :description="step"
                  status="finish"
                />
              </a-steps>
            </div>

            <!-- 中间结果 -->
            <a-divider>中间结果</a-divider>
            <div class="intermediate-results">
              <a-table
                :columns="intermediateResultsColumns"
                :data-source="indicatorDetail.scoreDetails.intermediateResults"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'value'">
                    <span class="result-value">{{ record.value }}</span>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </div>

        <!-- 数据质量 -->
        <div class="data-quality-section" v-if="indicatorDetail">
          <a-card size="small" title="数据质量">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="quality-metric">
                  <a-progress
                    type="circle"
                    :percent="indicatorDetail.dataQuality.completeness"
                    :size="80"
                    :stroke-color="getQualityColor(indicatorDetail.dataQuality.completeness)"
                  />
                  <div class="metric-label">完整度</div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="quality-metric">
                  <a-progress
                    type="circle"
                    :percent="indicatorDetail.dataQuality.timeliness"
                    :size="80"
                    :stroke-color="getQualityColor(indicatorDetail.dataQuality.timeliness)"
                  />
                  <div class="metric-label">时效性</div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="quality-metric">
                  <a-progress
                    type="circle"
                    :percent="indicatorDetail.dataQuality.accuracy"
                    :size="80"
                    :stroke-color="getQualityColor(indicatorDetail.dataQuality.accuracy)"
                  />
                  <div class="metric-label">准确性</div>
                </div>
              </a-col>
            </a-row>

            <!-- 数据问题 -->
            <a-divider>数据问题</a-divider>
            <div class="data-issues">
              <div v-if="indicatorDetail.dataQuality.issues.length === 0" class="no-issues">
                <a-result
                  status="success"
                  title="数据质量良好"
                  sub-title="当前没有发现数据质量问题"
                />
              </div>
              <div v-else class="issues-list">
                <a-alert
                  v-for="(issue, index) in indicatorDetail.dataQuality.issues"
                  :key="index"
                  :message="issue"
                  type="warning"
                  show-icon
                  style="margin-bottom: 8px;"
                />
              </div>
            </div>
          </a-card>
        </div>

        <!-- 相关证据 -->
        <div class="related-evidence-section" v-if="indicatorDetail?.relatedEvidence?.length">
          <a-card size="small" title="相关证据">
            <a-list
              :data-source="indicatorDetail.relatedEvidence"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a :href="item.url" target="_blank">
                        <file-text-outlined />
                        {{ item.name }}
                      </a>
                    </template>
                    <template #description>
                      <div class="evidence-meta">
                        <a-tag size="small">{{ item.type }}</a-tag>
                        <span class="upload-time">{{ item.uploadTime }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </div>

        <!-- 操作历史 -->
        <div class="operation-history-section">
          <a-card size="small" title="操作历史">
            <a-button type="link" @click="showHistory" style="padding: 0;">
              <history-outlined />
              查看完整操作历史
            </a-button>
          </a-card>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { getCultivationObjectIndicatorDetails } from '@/api/audit-confirmation'
import {
  FileTextOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  open: boolean
  indicator: any
  cultivationObjectId: number
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const indicatorDetail = ref<any>(null)

// 表格列定义
const intermediateResultsColumns = [
  {
    title: '步骤',
    dataIndex: 'step',
    key: 'step',
    width: 200
  },
  {
    title: '结果值',
    dataIndex: 'value',
    key: 'value'
  }
]

// 方法
const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getQualityColor = (quality: number) => {
  if (quality >= 90) return '#52c41a'
  if (quality >= 80) return '#1890ff'
  if (quality >= 70) return '#faad14'
  return '#ff4d4f'
}

const formatDataKey = (key: string) => {
  // 将驼峰命名转换为中文描述
  const keyMap: Record<string, string> = {
    'totalMembers': '总成员数',
    'leaderCount': '领导班子数量',
    'committeeCount': '委员会数量',
    'meetingFrequency': '会议频次',
    'activityCount': '活动次数'
  }
  return keyMap[key] || key
}

const formatDataValue = (value: any) => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return String(value)
}

const showHistory = () => {
  // 触发父组件显示历史记录
  message.info('查看操作历史功能')
}

const handleCancel = () => {
  emit('update:open', false)
}

const fetchIndicatorDetail = async () => {
  if (!props.indicator || !props.cultivationObjectId) return

  try {
    loading.value = true
    indicatorDetail.value = await getCultivationObjectIndicatorDetails(
      props.cultivationObjectId,
      props.indicator.id
    )
  } catch (error) {
    message.error('获取指标详情失败')
    console.error('获取指标详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听属性变化
watch([() => props.open, () => props.indicator], ([newOpen, newIndicator]) => {
  if (newOpen && newIndicator) {
    fetchIndicatorDetail()
  }
})
</script>

<style lang="scss" scoped>
.indicator-detail-modal-content {
  .indicator-basic-info,
  .score-details-section,
  .data-quality-section,
  .related-evidence-section,
  .operation-history-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .score-details-section {
    .score-summary {
      text-align: center;
      padding: 16px;
      background: #f6f8fa;
      border-radius: 6px;
    }

    .score-explanation {
      h4 {
        margin-bottom: 8px;
        color: #333;
      }

      p {
        margin: 0;
        color: #666;
        line-height: 1.6;
      }
    }

    .raw-data-section {
      background: #f9f9f9;
      padding: 12px;
      border-radius: 4px;
    }

    .calculation-steps {
      max-height: 300px;
      overflow-y: auto;
    }

    .intermediate-results {
      .result-value {
        font-weight: 600;
        color: #1890ff;
      }
    }
  }

  .data-quality-section {
    .quality-metric {
      text-align: center;
      padding: 16px;

      .metric-label {
        margin-top: 8px;
        color: #666;
        font-size: 14px;
      }
    }

    .no-issues {
      text-align: center;
      padding: 20px;
    }

    .issues-list {
      max-height: 200px;
      overflow-y: auto;
    }
  }

  .related-evidence-section {
    .evidence-meta {
      display: flex;
      align-items: center;
      gap: 12px;

      .upload-time {
        color: #999;
        font-size: 12px;
      }
    }
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: 600;
  }

  :deep(.ant-steps-vertical > .ant-steps-item .ant-steps-item-content) {
    min-height: auto;
    padding-bottom: 12px;
  }

  :deep(.ant-steps-vertical > .ant-steps-item:not(:last-child) .ant-steps-item-tail) {
    padding: 6px 0 12px;
  }
}
</style>