<template>
  <a-modal
    :open="open"
    title="评分修改"
    width="900px"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirm-loading="loading"
  >
    <div class="score-modify-modal-content">
      <!-- 培育对象信息 -->
      <div class="object-info-section">
        <a-alert
          message="评分修改提醒"
          description="修改评分将记录详细的历史数据，包括修改原因、修改人、修改时间等信息，请谨慎操作。"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        />
        <a-card size="small" title="培育对象信息">
          <a-descriptions :column="3" size="small">
            <a-descriptions-item label="名称">
              {{ cultivationObject?.name }}
            </a-descriptions-item>
            <a-descriptions-item label="当前总分">
              <span class="current-score">{{ currentTotalScore }} 分</span>
            </a-descriptions-item>
            <a-descriptions-item label="修改后总分">
              <span class="new-score">{{ newTotalScore }} 分</span>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <!-- 指标评分修改 -->
      <div class="indicators-modify-section">
        <a-card title="指标评分修改">
          <div class="indicators-list">
            <div
              v-for="indicator in indicatorList"
              :key="indicator.indicatorId"
              class="indicator-modify-item"
            >
              <div class="indicator-header">
                <div class="indicator-info">
                  <h4>{{ indicator.indicatorName }}</h4>
                  <p>{{ indicator.description || '暂无描述' }}</p>
                  <div class="indicator-meta">
                    <a-tag>权重: {{ indicator.weight }}%</a-tag>
                    <a-tag>数据来源: {{ indicator.dataSource }}</a-tag>
                    <a-tag :color="getQualityColor(indicator.dataQuality?.accuracy)">
                      准确性: {{ indicator.dataQuality?.accuracy || 0 }}%
                    </a-tag>
                  </div>
                </div>
                <div class="score-modify-controls">
                  <div class="score-input-group">
                    <label>原始评分:</label>
                    <a-input-number
                      :value="indicator.rawScore"
                      :min="0"
                      :max="indicator.maxScore"
                      :precision="1"
                      disabled
                      style="width: 100px;"
                    />
                    <span class="score-separator">→</span>
                    <label>修改后:</label>
                    <a-input-number
                      v-model:value="getModification(indicator.indicatorId).newScore"
                      :min="0"
                      :max="indicator.maxScore"
                      :precision="1"
                      style="width: 100px;"
                      @change="(value) => updateScore(indicator.indicatorId, value)"
                    />
                    <span class="max-score">/ {{ indicator.maxScore }}</span>
                  </div>
                  <div class="score-change-display">
                    <span 
                      :class="['score-change', getChangeClass(indicator.indicatorId)]"
                    >
                      {{ getScoreChange(indicator.indicatorId) }}
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- 修改原因 -->
              <div class="modification-reason">
                <a-form-item 
                  :label="`修改原因 (${indicator.indicatorName})`"
                  :name="`reason_${indicator.indicatorId}`"
                  :rules="[{ required: isModified(indicator.indicatorId), message: '请填写修改原因', trigger: 'blur' }]"
                >
                  <a-textarea
                    v-model:value="getModification(indicator.indicatorId).reason"
                    placeholder="请详细说明修改此指标评分的原因..."
                    :rows="2"
                    :disabled="!isModified(indicator.indicatorId)"
                  />
                </a-form-item>
              </div>

              <!-- 证据附件 -->
              <div class="evidence-section" v-if="isModified(indicator.indicatorId)">
                <a-form-item :label="`支撑证据 (${indicator.indicatorName})`">
                  <a-upload
                    v-model:file-list="getModification(indicator.indicatorId).evidenceFiles"
                    :before-upload="() => false"
                    list-type="text"
                    :multiple="true"
                  >
                    <a-button>
                      <upload-outlined /> 上传证据文件
                    </a-button>
                  </a-upload>
                  <div class="evidence-note">
                    <a-typography-text type="secondary" style="font-size: 12px;">
                      支持上传文档、图片等证据材料，用于支撑评分修改的合理性
                    </a-typography-text>
                  </div>
                </a-form-item>
              </div>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 批量操作 -->
      <div class="batch-operations-section">
        <a-card title="批量操作">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-button block @click="batchIncrease">
                <plus-outlined /> 批量加分
              </a-button>
            </a-col>
            <a-col :span="8">
              <a-button block @click="batchDecrease">
                <minus-outlined /> 批量减分
              </a-button>
            </a-col>
            <a-col :span="8">
              <a-button block @click="resetAllModifications" danger>
                <undo-outlined /> 重置所有修改
              </a-button>
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 修改汇总 -->
      <div class="modification-summary" v-if="hasModifications">
        <a-card title="修改汇总" size="small">
          <a-list
            :data-source="modificationSummary"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <span>{{ item.indicatorName }}</span>
                  </template>
                  <template #description>
                    <div class="summary-item">
                      <span class="score-change">
                        {{ item.originalScore }} → {{ item.newScore }} 分
                        <span :class="['change-delta', getChangeClass(item.indicatorId)]">
                          ({{ getScoreChange(item.indicatorId) }})
                        </span>
                      </span>
                      <span class="reason">{{ item.reason }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </div>
    </div>

    <!-- 批量操作模态框 -->
    <a-modal
      v-model:open="batchModalVisible"
      :title="batchModalTitle"
      @ok="handleBatchOperation"
      width="400px"
    >
      <a-form layout="vertical">
        <a-form-item label="调整分数">
          <a-input-number
            v-model:value="batchValue"
            :precision="1"
            :min="0.1"
            :max="10"
            style="width: 100%;"
            addon-after="分"
          />
        </a-form-item>
        <a-form-item label="调整原因">
          <a-textarea
            v-model:value="batchReason"
            placeholder="请说明批量调整的原因..."
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { CultivationObject } from '@/types/audit-confirmation'
import {
  PlusOutlined,
  MinusOutlined,
  UndoOutlined,
  UploadOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  open: boolean
  cultivationObject: CultivationObject | null
  indicators: any[]
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'submit', data: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const batchModalVisible = ref(false)
const batchModalTitle = ref('')
const batchOperationType = ref<'increase' | 'decrease'>('increase')
const batchValue = ref(1)
const batchReason = ref('')

// 指标列表
const indicatorList = ref<any[]>([])

// 修改记录
const modifications = reactive<Record<number, {
  newScore: number
  originalScore: number
  reason: string
  evidenceFiles: any[]
}>>({})

// 计算属性
const currentTotalScore = computed(() => {
  return indicatorList.value.reduce((sum, indicator) => sum + indicator.rawScore, 0)
})

const newTotalScore = computed(() => {
  return indicatorList.value.reduce((sum, indicator) => {
    const modification = modifications[indicator.indicatorId]
    return sum + (modification ? modification.newScore : indicator.rawScore)
  }, 0)
})

const hasModifications = computed(() => {
  return Object.keys(modifications).some(key => {
    const modification = modifications[Number(key)]
    return modification && modification.newScore !== modification.originalScore
  })
})

const modificationSummary = computed(() => {
  return Object.entries(modifications)
    .filter(([_, modification]) => modification.newScore !== modification.originalScore)
    .map(([indicatorId, modification]) => {
      const indicator = indicatorList.value.find(i => i.indicatorId === Number(indicatorId))
      return {
        indicatorId: Number(indicatorId),
        indicatorName: indicator?.indicatorName || '',
        originalScore: modification.originalScore,
        newScore: modification.newScore,
        reason: modification.reason
      }
    })
})

// 方法
const getModification = (indicatorId: number) => {
  if (!modifications[indicatorId]) {
    const indicator = indicatorList.value.find(i => i.indicatorId === indicatorId)
    modifications[indicatorId] = {
      newScore: indicator?.rawScore || 0,
      originalScore: indicator?.rawScore || 0,
      reason: '',
      evidenceFiles: []
    }
  }
  return modifications[indicatorId]
}

const updateScore = (indicatorId: number, newScore: number) => {
  if (modifications[indicatorId]) {
    modifications[indicatorId].newScore = newScore || 0
  }
}

const isModified = (indicatorId: number) => {
  const modification = modifications[indicatorId]
  return modification && modification.newScore !== modification.originalScore
}

const getScoreChange = (indicatorId: number) => {
  const modification = modifications[indicatorId]
  if (!modification) return '+0.0'
  
  const change = modification.newScore - modification.originalScore
  return change >= 0 ? `+${change.toFixed(1)}` : change.toFixed(1)
}

const getChangeClass = (indicatorId: number) => {
  const modification = modifications[indicatorId]
  if (!modification) return ''
  
  const change = modification.newScore - modification.originalScore
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return ''
}

const getQualityColor = (accuracy: number) => {
  if (accuracy >= 90) return 'green'
  if (accuracy >= 80) return 'blue'
  if (accuracy >= 70) return 'orange'
  return 'red'
}

const batchIncrease = () => {
  batchModalTitle.value = '批量加分'
  batchOperationType.value = 'increase'
  batchValue.value = 1
  batchReason.value = ''
  batchModalVisible.value = true
}

const batchDecrease = () => {
  batchModalTitle.value = '批量减分'
  batchOperationType.value = 'decrease'
  batchValue.value = 1
  batchReason.value = ''
  batchModalVisible.value = true
}

const handleBatchOperation = () => {
  if (!batchReason.value.trim()) {
    message.error('请填写批量操作原因')
    return
  }

  indicatorList.value.forEach(indicator => {
    const modification = getModification(indicator.indicatorId)
    const change = batchOperationType.value === 'increase' ? batchValue.value : -batchValue.value
    const newScore = Math.max(0, Math.min(indicator.maxScore, modification.newScore + change))
    
    modification.newScore = newScore
    if (!modification.reason) {
      modification.reason = `批量${batchOperationType.value === 'increase' ? '加' : '减'}分: ${batchReason.value}`
    }
  })

  batchModalVisible.value = false
  message.success('批量操作完成')
}

const resetAllModifications = () => {
  indicatorList.value.forEach(indicator => {
    const modification = getModification(indicator.indicatorId)
    modification.newScore = modification.originalScore
    modification.reason = ''
    modification.evidenceFiles = []
  })
  message.success('已重置所有修改')
}

const handleCancel = () => {
  emit('update:open', false)
}

const handleSubmit = async () => {
  // 验证修改项是否都有原因
  const invalidModifications = modificationSummary.value.filter(item => !item.reason.trim())
  if (invalidModifications.length > 0) {
    message.error('请为所有修改项填写修改原因')
    return
  }

  try {
    loading.value = true
    
    const submitData = {
      cultivationObjectId: props.cultivationObject?.id,
      modifications: modificationSummary.value.map(item => ({
        indicatorId: item.indicatorId,
        originalScore: item.originalScore,
        newScore: item.newScore,
        reason: item.reason,
        evidenceUrls: modifications[item.indicatorId]?.evidenceFiles?.map(f => f.url) || []
      }))
    }
    
    emit('submit', submitData)
    emit('update:open', false)
    
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听属性变化，更新指标列表
const initializeIndicators = () => {
  if (props.indicators && props.indicators.length > 0) {
    indicatorList.value = props.indicators.map(indicator => ({
      ...indicator,
      maxScore: indicator.maxScore || 100
    }))
    
    // 初始化修改记录
    indicatorList.value.forEach(indicator => {
      getModification(indicator.indicatorId)
    })
  }
}

// 监听props变化
watch(() => [props.open, props.indicators], ([newOpen, newIndicators]) => {
  if (newOpen && newIndicators) {
    initializeIndicators()
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.score-modify-modal-content {
  .object-info-section {
    margin-bottom: 16px;

    .current-score {
      font-weight: 600;
      color: #666;
    }

    .new-score {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .indicators-modify-section {
    margin-bottom: 16px;

    .indicator-modify-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .indicator-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;

        .indicator-info {
          flex: 1;
          margin-right: 24px;

          h4 {
            margin: 0 0 8px 0;
            color: #333;
          }

          p {
            margin: 0 0 8px 0;
            color: #666;
            font-size: 13px;
          }

          .indicator-meta {
            .ant-tag {
              margin-right: 8px;
              margin-bottom: 4px;
            }
          }
        }

        .score-modify-controls {
          flex: 0 0 auto;
          text-align: right;

          .score-input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            label {
              font-size: 12px;
              color: #666;
              white-space: nowrap;
            }

            .score-separator {
              color: #1890ff;
              font-weight: bold;
            }

            .max-score {
              color: #666;
              font-size: 12px;
            }
          }

          .score-change-display {
            .score-change {
              font-weight: 600;
              font-size: 14px;

              &.positive {
                color: #52c41a;
              }

              &.negative {
                color: #ff4d4f;
              }
            }
          }
        }
      }

      .modification-reason {
        margin-bottom: 12px;
      }

      .evidence-section {
        .evidence-note {
          margin-top: 8px;
        }
      }
    }
  }

  .batch-operations-section {
    margin-bottom: 16px;
  }

  .modification-summary {
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 16px;

      .score-change {
        font-weight: 600;

        .change-delta {
          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #ff4d4f;
          }
        }
      }

      .reason {
        color: #666;
        font-size: 12px;
        flex: 1;
        text-align: right;
      }
    }
  }

  :deep(.ant-form-item-label) {
    font-weight: 600;
  }

  :deep(.ant-upload-list) {
    max-height: 100px;
    overflow-y: auto;
  }
}
</style>