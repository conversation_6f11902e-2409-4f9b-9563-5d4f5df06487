<template>
  <a-modal
    :open="open"
    title="历史记录"
    width="1000px"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="history-modal-content">
      <!-- 历史类型切换 -->
      <div class="history-tabs">
        <a-tabs v-model:activeKey="activeTab" @change="onTabChange">
          <a-tab-pane key="score" tab="评分历史">
            <div class="score-history-section">
              <!-- 历史数据图表 -->
              <div class="history-chart-section">
                <a-card size="small" title="评分变化趋势">
                  <div class="chart-container" id="scoreHistoryChart" style="height: 300px;"></div>
                </a-card>
              </div>

              <!-- 详细历史记录 -->
              <div class="history-records-section">
                <a-card size="small" title="详细记录" :loading="loading">
                  <a-timeline>
                    <a-timeline-item
                      v-for="record in scoreHistory"
                      :key="record.changeId"
                      :color="getTimelineColor(record.changeType)"
                    >
                      <div class="history-record-item">
                        <div class="record-header">
                          <div class="record-title">
                            <span class="change-type">{{ record.changeType }}</span>
                            <span class="change-time">{{ record.changeTime }}</span>
                            <a-tag :color="getStatusColor(record.auditTrail?.reviewStatus)">
                              {{ getStatusText(record.auditTrail?.reviewStatus) }}
                            </a-tag>
                          </div>
                          <div class="record-operator">
                            <a-avatar size="small">{{ getOperatorInitial(record.operator) }}</a-avatar>
                            <span>{{ record.operator }}</span>
                          </div>
                        </div>

                        <div class="record-content">
                          <!-- 总分变化 -->
                          <div class="total-score-change">
                            <strong>总分变化：</strong>
                            <span class="score-from">{{ record.totalScoreChange.from }}</span>
                            <arrow-right-outlined style="margin: 0 8px; color: #1890ff;" />
                            <span class="score-to">{{ record.totalScoreChange.to }}</span>
                            <span 
                              :class="['score-delta', getScoreChangeClass(record.totalScoreChange)]"
                            >
                              ({{ getScoreDelta(record.totalScoreChange) }})
                            </span>
                          </div>

                          <!-- 具体变更 -->
                          <div class="detailed-changes">
                            <a-collapse size="small" ghost>
                              <a-collapse-panel header="查看详细变更" key="details">
                                <div class="changes-list">
                                  <div
                                    v-for="(change, index) in record.changes"
                                    :key="index"
                                    class="change-item"
                                  >
                                    <div class="change-field">{{ change.field }}</div>
                                    <div class="change-values">
                                      <span class="from-value">{{ formatValue(change.from) }}</span>
                                      <arrow-right-outlined style="margin: 0 8px; color: #999;" />
                                      <span class="to-value">{{ formatValue(change.to) }}</span>
                                    </div>
                                    <div class="change-reason">{{ change.reason }}</div>
                                  </div>
                                </div>
                              </a-collapse-panel>
                            </a-collapse>
                          </div>

                          <!-- 审核轨迹 -->
                          <div class="audit-trail" v-if="record.auditTrail?.reviewer">
                            <a-divider />
                            <div class="trail-info">
                              <strong>审核信息：</strong>
                              <span>{{ record.auditTrail.reviewer }}</span>
                              <span>{{ record.auditTrail.reviewTime }}</span>
                              <div class="review-comments" v-if="record.auditTrail.reviewComments">
                                <em>"{{ record.auditTrail.reviewComments }}"</em>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </a-timeline-item>
                  </a-timeline>

                  <!-- 空状态 -->
                  <a-empty v-if="!loading && scoreHistory.length === 0" description="暂无历史记录" />
                </a-card>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="indicator" tab="指标历史">
            <div class="indicator-history-section">
              <!-- 指标选择 -->
              <div class="indicator-selector">
                <a-card size="small" title="选择指标">
                  <a-select
                    v-model:value="selectedIndicatorId"
                    placeholder="请选择要查看历史的指标"
                    style="width: 100%;"
                    @change="onIndicatorChange"
                  >
                    <a-select-option
                      v-for="indicator in availableIndicators"
                      :key="indicator.id"
                      :value="indicator.id"
                    >
                      {{ indicator.indicatorName }}
                    </a-select-option>
                  </a-select>
                </a-card>
              </div>

              <!-- 指标历史详情 -->
              <div class="indicator-history-detail" v-if="selectedIndicatorId">
                <a-card size="small" title="指标变更历史" :loading="loading">
                  <div class="indicator-versions">
                    <a-table
                      :columns="indicatorHistoryColumns"
                      :data-source="indicatorHistory"
                      :pagination="false"
                      size="small"
                    >
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'versionType'">
                          <a-tag :color="getVersionTypeColor(record.versionType)">
                            {{ getVersionTypeText(record.versionType) }}
                          </a-tag>
                        </template>
                        <template v-if="column.key === 'changes'">
                          <div class="version-changes">
                            <div v-if="record.changes.score.from !== record.changes.score.to" class="change-item">
                              <strong>评分:</strong>
                              {{ record.changes.score.from }} → {{ record.changes.score.to }}
                            </div>
                            <div v-if="record.changes.weight.from !== record.changes.weight.to" class="change-item">
                              <strong>权重:</strong>
                              {{ record.changes.weight.from }} → {{ record.changes.weight.to }}
                            </div>
                            <div v-if="record.changes.dataSource.from !== record.changes.dataSource.to" class="change-item">
                              <strong>数据源:</strong>
                              {{ record.changes.dataSource.from }} → {{ record.changes.dataSource.to }}
                            </div>
                          </div>
                        </template>
                        <template v-if="column.key === 'reviewStatus'">
                          <a-tag :color="getReviewStatusColor(record.reviewStatus)">
                            {{ getReviewStatusText(record.reviewStatus) }}
                          </a-tag>
                        </template>
                        <template v-if="column.key === 'actions'">
                          <a-space size="small">
                            <a-button type="link" size="small" @click="viewVersionDetail(record)">
                              查看详情
                            </a-button>
                            <a-button type="link" size="small" @click="compareVersions(record)" v-if="canCompare">
                              对比
                            </a-button>
                          </a-space>
                        </template>
                      </template>
                    </a-table>
                  </div>
                </a-card>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="audit" tab="审核历史">
            <div class="audit-history-section">
              <a-card title="审核记录" :loading="loading">
                <a-list
                  :data-source="auditHistory"
                  item-layout="vertical"
                  size="large"
                >
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <template #actions>
                        <span>{{ item.auditTime }}</span>
                      </template>
                      <template #extra>
                        <a-tag :color="getAuditResultColor(item.auditResult)">
                          {{ getAuditResultText(item.auditResult) }}
                        </a-tag>
                      </template>
                      <a-list-item-meta>
                        <template #title>
                          <div class="audit-title">
                            <span>{{ getAuditTypeText(item.auditType) }}</span>
                            <span class="auditor">审核人: {{ item.auditor }}</span>
                          </div>
                        </template>
                        <template #description>
                          <div class="audit-content">
                            <p><strong>审核意见:</strong> {{ item.comments }}</p>
                            <p v-if="item.suggestions"><strong>建议:</strong> {{ item.suggestions }}</p>
                            <div v-if="item.originalScore !== item.finalScore" class="score-adjustment">
                              <strong>评分调整:</strong>
                              {{ item.originalScore }} → {{ item.finalScore }} 分
                            </div>
                          </div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </a-card>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>

    <!-- 版本详情模态框 -->
    <a-modal
      v-model:open="versionDetailVisible"
      title="版本详情"
      width="600px"
      :footer="null"
    >
      <div class="version-detail" v-if="currentVersion">
        <a-descriptions title="版本信息" :column="2" bordered>
          <a-descriptions-item label="版本ID">{{ currentVersion.versionId }}</a-descriptions-item>
          <a-descriptions-item label="变更时间">{{ currentVersion.changeTime }}</a-descriptions-item>
          <a-descriptions-item label="操作人">{{ currentVersion.operator }}</a-descriptions-item>
          <a-descriptions-item label="变更类型">
            <a-tag :color="getVersionTypeColor(currentVersion.versionType)">
              {{ getVersionTypeText(currentVersion.versionType) }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <div class="version-changes-detail">
          <h4>详细变更</h4>
          <pre>{{ JSON.stringify(currentVersion.changes, null, 2) }}</pre>
        </div>
        
        <div class="change-reasons">
          <h4>变更原因</h4>
          <a-list
            :data-source="currentVersion.changeReasons"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>{{ item }}</a-list-item>
            </template>
          </a-list>
        </div>
        
        <div class="evidence-files" v-if="currentVersion.evidence?.length">
          <h4>相关证据</h4>
          <a-list
            :data-source="currentVersion.evidence"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a :href="item" target="_blank">{{ item }}</a>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  getScoreHistoryData,
  getIndicatorHistoryData,
  getCultivationObjectIndicatorResults,
  getAuditRecordList
} from '@/api/audit-confirmation'
import {
  AuditTypeTextMap,
  AuditResultTextMap,
  AuditResultColorMap
} from '@/types/audit-confirmation'
import { ArrowRightOutlined } from '@ant-design/icons-vue'

// Props
interface Props {
  open: boolean
  cultivationObjectId: number
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const activeTab = ref('score')
const scoreHistory = ref<any[]>([])
const indicatorHistory = ref<any[]>([])
const auditHistory = ref<any[]>([])
const availableIndicators = ref<any[]>([])
const selectedIndicatorId = ref<number>()
const versionDetailVisible = ref(false)
const currentVersion = ref<any>(null)
const canCompare = ref(false)

// 表格列定义
const indicatorHistoryColumns = [
  {
    title: '版本ID',
    dataIndex: 'versionId',
    key: 'versionId',
    width: 80
  },
  {
    title: '变更时间',
    dataIndex: 'changeTime',
    key: 'changeTime',
    width: 150
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '变更类型',
    dataIndex: 'versionType',
    key: 'versionType',
    width: 120
  },
  {
    title: '变更内容',
    dataIndex: 'changes',
    key: 'changes'
  },
  {
    title: '审核状态',
    dataIndex: 'reviewStatus',
    key: 'reviewStatus',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 方法
const getTimelineColor = (changeType: string) => {
  const colorMap: Record<string, string> = {
    '评分调整': 'blue',
    '权重修改': 'green',
    '数据更新': 'orange',
    '规则变更': 'purple'
  }
  return colorMap[changeType] || 'gray'
}

const getStatusColor = (status?: string) => {
  const colorMap: Record<string, string> = {
    'pending': 'processing',
    'approved': 'success',
    'rejected': 'error'
  }
  return colorMap[status || ''] || 'default'
}

const getStatusText = (status?: string) => {
  const textMap: Record<string, string> = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return textMap[status || ''] || '未知'
}

const getOperatorInitial = (operator: string) => {
  return operator ? operator.charAt(0).toUpperCase() : '?'
}

const getScoreChangeClass = (scoreChange: any) => {
  const delta = scoreChange.to - scoreChange.from
  if (delta > 0) return 'positive'
  if (delta < 0) return 'negative'
  return 'neutral'
}

const getScoreDelta = (scoreChange: any) => {
  const delta = scoreChange.to - scoreChange.from
  return delta >= 0 ? `+${delta}` : `${delta}`
}

const formatValue = (value: any) => {
  if (typeof value === 'number') {
    return value.toFixed(1)
  }
  return String(value)
}

const getVersionTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'data_update': 'blue',
    'manual_adjustment': 'orange',
    'rule_change': 'purple'
  }
  return colorMap[type] || 'default'
}

const getVersionTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    'data_update': '数据更新',
    'manual_adjustment': '人工调整',
    'rule_change': '规则变更'
  }
  return textMap[type] || type
}

const getReviewStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'auto_approved': 'green',
    'pending_review': 'processing',
    'approved': 'success',
    'rejected': 'error'
  }
  return colorMap[status] || 'default'
}

const getReviewStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'auto_approved': '自动通过',
    'pending_review': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return textMap[status] || status
}

const getAuditTypeText = (type: number) => {
  return AuditTypeTextMap[type as keyof typeof AuditTypeTextMap] || '未知'
}

const getAuditResultText = (result: number) => {
  return AuditResultTextMap[result as keyof typeof AuditResultTextMap] || '未知'
}

const getAuditResultColor = (result: number) => {
  return AuditResultColorMap[result as keyof typeof AuditResultColorMap] || 'default'
}

const onTabChange = (key: string) => {
  activeTab.value = key
  if (key === 'score' && scoreHistory.value.length === 0) {
    fetchScoreHistory()
  } else if (key === 'indicator' && availableIndicators.value.length === 0) {
    fetchAvailableIndicators()
  } else if (key === 'audit' && auditHistory.value.length === 0) {
    fetchAuditHistory()
  }
}

const onIndicatorChange = (indicatorId: number) => {
  selectedIndicatorId.value = indicatorId
  fetchIndicatorHistory()
}

const viewVersionDetail = (record: any) => {
  currentVersion.value = record
  versionDetailVisible.value = true
}

const compareVersions = (record: any) => {
  // TODO: 实现版本对比功能
  message.info('版本对比功能开发中...')
}

const fetchScoreHistory = async () => {
  try {
    loading.value = true
    scoreHistory.value = await getScoreHistoryData(props.cultivationObjectId)
  } catch (error) {
    message.error('获取评分历史失败')
  } finally {
    loading.value = false
  }
}

const fetchAvailableIndicators = async () => {
  try {
    const indicators = await getCultivationObjectIndicatorResults(props.cultivationObjectId)
    availableIndicators.value = indicators.map(item => ({
      id: item.indicatorId,
      indicatorName: item.indicatorName
    }))
  } catch (error) {
    message.error('获取指标列表失败')
  }
}

const fetchIndicatorHistory = async () => {
  if (!selectedIndicatorId.value) return
  
  try {
    loading.value = true
    indicatorHistory.value = await getIndicatorHistoryData(
      props.cultivationObjectId,
      selectedIndicatorId.value
    )
  } catch (error) {
    message.error('获取指标历史失败')
  } finally {
    loading.value = false
  }
}

const fetchAuditHistory = async () => {
  try {
    loading.value = true
    const result = await getAuditRecordList()
    auditHistory.value = result.list.filter(record => 
      record.cultivationObjectId === props.cultivationObjectId
    )
  } catch (error) {
    message.error('获取审核历史失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('update:open', false)
}

// 监听弹窗打开状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    // 默认加载评分历史
    if (activeTab.value === 'score') {
      fetchScoreHistory()
    }
  }
})

// 初始化图表（如果需要）
const initChart = () => {
  // TODO: 使用 ECharts 或其他图表库渲染评分趋势图
}

onMounted(() => {
  if (props.open && props.cultivationObjectId) {
    fetchScoreHistory()
  }
})
</script>

<style lang="scss" scoped>
.history-modal-content {
  .history-tabs {
    .score-history-section {
      .history-chart-section {
        margin-bottom: 16px;
      }

      .history-records-section {
        .history-record-item {
          .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .record-title {
              display: flex;
              align-items: center;
              gap: 12px;

              .change-type {
                font-weight: 600;
                color: #333;
              }

              .change-time {
                color: #666;
                font-size: 13px;
              }
            }

            .record-operator {
              display: flex;
              align-items: center;
              gap: 8px;
              color: #666;
              font-size: 13px;
            }
          }

          .record-content {
            .total-score-change {
              margin-bottom: 12px;
              padding: 8px 12px;
              background: #f6f8fa;
              border-radius: 4px;

              .score-from, .score-to {
                font-weight: 600;
              }

              .score-delta {
                font-weight: 600;
                margin-left: 8px;

                &.positive {
                  color: #52c41a;
                }

                &.negative {
                  color: #ff4d4f;
                }

                &.neutral {
                  color: #666;
                }
              }
            }

            .detailed-changes {
              margin-bottom: 12px;

              .changes-list {
                .change-item {
                  display: flex;
                  align-items: center;
                  gap: 16px;
                  padding: 8px 0;
                  border-bottom: 1px solid #f0f0f0;

                  &:last-child {
                    border-bottom: none;
                  }

                  .change-field {
                    flex: 0 0 120px;
                    font-weight: 600;
                    color: #333;
                  }

                  .change-values {
                    flex: 1;
                    display: flex;
                    align-items: center;

                    .from-value {
                      color: #666;
                    }

                    .to-value {
                      color: #1890ff;
                      font-weight: 600;
                    }
                  }

                  .change-reason {
                    flex: 1;
                    color: #666;
                    font-size: 12px;
                  }
                }
              }
            }

            .audit-trail {
              .trail-info {
                color: #666;
                font-size: 13px;

                span {
                  margin-right: 12px;
                }

                .review-comments {
                  margin-top: 8px;
                  color: #333;
                }
              }
            }
          }
        }
      }
    }

    .indicator-history-section {
      .indicator-selector {
        margin-bottom: 16px;
      }

      .indicator-history-detail {
        .version-changes {
          .change-item {
            margin-bottom: 4px;
            font-size: 12px;
          }
        }
      }
    }

    .audit-history-section {
      .audit-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .auditor {
          color: #666;
          font-size: 13px;
          font-weight: normal;
        }
      }

      .audit-content {
        p {
          margin-bottom: 8px;
        }

        .score-adjustment {
          color: #1890ff;
          font-weight: 600;
        }
      }
    }
  }
}

.version-detail {
  .version-changes-detail {
    pre {
      background: #f6f8fa;
      padding: 12px;
      border-radius: 4px;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}

:deep(.ant-timeline-item-content) {
  min-height: auto;
}

:deep(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
  padding: 8px 0;
}
</style>