<template>
  <a-modal
    :open="open"
    title="审核确认"
    width="800px"
    @cancel="handleCancel"
    @ok="handleSubmit"
    :confirm-loading="loading"
  >
    <div class="audit-modal-content">
      <!-- 培育对象基本信息 -->
      <div class="object-info-section">
        <a-card size="small" title="培育对象信息">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="名称">
              {{ cultivationObject?.name }}
            </a-descriptions-item>
            <a-descriptions-item label="当前状态">
              <a-tag :color="getStatusColor(cultivationObject?.status)">
                {{ getStatusText(cultivationObject?.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="当前评分">
              <span class="score-value">{{ cultivationObject?.score || 0 }} 分</span>
            </a-descriptions-item>
            <a-descriptions-item label="提交时间">
              {{ cultivationObject?.submitTime }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </div>

      <!-- 审核表单 -->
      <div class="audit-form-section">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
        >
          <!-- 审核结果选择 -->
          <a-form-item label="审核结果" name="auditResult" required>
            <a-radio-group v-model:value="formData.auditResult" @change="onAuditResultChange">
              <a-radio :value="1">
                <span style="color: #52c41a;">
                  <check-circle-outlined /> 通过
                </span>
              </a-radio>
              <a-radio :value="2">
                <span style="color: #ff4d4f;">
                  <close-circle-outlined /> 不通过
                </span>
              </a-radio>
              <a-radio :value="3">
                <span style="color: #faad14;">
                  <exclamation-circle-outlined /> 待补充
                </span>
              </a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 总体审核意见 -->
          <a-form-item label="总体审核意见" name="overallComments" required>
            <a-textarea
              v-model:value="formData.overallComments"
              :rows="4"
              placeholder="请填写总体审核意见..."
              show-count
              :max-length="500"
            />
          </a-form-item>

          <!-- 指标级审核意见 -->
          <a-form-item label="指标审核意见">
            <div class="indicator-comments-section">
              <a-collapse v-if="scoreResult?.scoreBreakdown?.length">
                <a-collapse-panel 
                  v-for="category in scoreResult.scoreBreakdown" 
                  :key="category.categoryName"
                  :header="category.categoryName"
                >
                  <div
                    v-for="indicator in category.indicators"
                    :key="indicator.indicatorCode"
                    class="indicator-comment-item"
                  >
                    <div class="indicator-header">
                      <span class="indicator-name">{{ indicator.indicatorName }}</span>
                      <span class="indicator-score">{{ indicator.score }}/{{ indicator.maxScore }}分</span>
                    </div>
                    <div class="comment-inputs">
                      <a-input
                        v-model:value="getIndicatorComment(indicator.id).comment"
                        placeholder="针对此指标的具体意见..."
                        style="margin-bottom: 8px;"
                      />
                      <a-input
                        v-model:value="getIndicatorComment(indicator.id).suggestion"
                        placeholder="改进建议..."
                        addon-before="建议"
                      />
                    </div>
                  </div>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </a-form-item>

          <!-- 改进建议 -->
          <a-form-item label="改进建议" v-show="formData.auditResult !== 1">
            <div class="improvement-suggestions">
              <a-input
                v-for="(suggestion, index) in formData.improvementSuggestions"
                :key="index"
                v-model:value="formData.improvementSuggestions[index]"
                placeholder="请输入具体的改进建议..."
                style="margin-bottom: 8px;"
                suffix=""
              >
                <template #suffix>
                  <a-button 
                    type="text" 
                    size="small" 
                    danger 
                    @click="removeSuggestion(index)"
                    v-if="formData.improvementSuggestions.length > 1"
                  >
                    <delete-outlined />
                  </a-button>
                </template>
              </a-input>
              <a-button type="dashed" block @click="addSuggestion">
                <plus-outlined /> 添加建议
              </a-button>
            </div>
          </a-form-item>

          <!-- 表扬内容 -->
          <a-form-item label="表扬内容" v-show="formData.auditResult === 1">
            <div class="commendations">
              <a-input
                v-for="(commendation, index) in formData.commendations"
                :key="index"
                v-model:value="formData.commendations[index]"
                placeholder="请输入表扬内容..."
                style="margin-bottom: 8px;"
              >
                <template #suffix>
                  <a-button 
                    type="text" 
                    size="small" 
                    danger 
                    @click="removeCommendation(index)"
                    v-if="formData.commendations.length > 1"
                  >
                    <delete-outlined />
                  </a-button>
                </template>
              </a-input>
              <a-button type="dashed" block @click="addCommendation">
                <plus-outlined /> 添加表扬
              </a-button>
            </div>
          </a-form-item>

          <!-- 关注点 -->
          <a-form-item label="重点关注">
            <div class="concerns">
              <a-input
                v-for="(concern, index) in formData.concerns"
                :key="index"
                v-model:value="formData.concerns[index]"
                placeholder="请输入需要重点关注的内容..."
                style="margin-bottom: 8px;"
              >
                <template #suffix>
                  <a-button 
                    type="text" 
                    size="small" 
                    danger 
                    @click="removeConcern(index)"
                    v-if="formData.concerns.length > 1"
                  >
                    <delete-outlined />
                  </a-button>
                </template>
              </a-input>
              <a-button type="dashed" block @click="addConcern">
                <plus-outlined /> 添加关注点
              </a-button>
            </div>
          </a-form-item>

          <!-- 下一步行动 -->
          <a-form-item label="下一步行动" name="nextAction">
            <a-textarea
              v-model:value="formData.nextAction"
              :rows="2"
              placeholder="请描述审核完成后的下一步行动..."
            />
          </a-form-item>

          <!-- 通知设置 -->
          <a-form-item label="通知设置">
            <a-checkbox-group v-model:value="formData.notificationSettings">
              <a-checkbox value="notifyApplicant">通知申报人</a-checkbox>
              <a-checkbox value="notifyManager">通知项目管理员</a-checkbox>
              <a-checkbox value="notifyNextAuditor">通知下级审核人</a-checkbox>
            </a-checkbox-group>
            <a-input
              v-if="formData.notificationSettings.length > 0"
              v-model:value="formData.customNotificationMessage"
              placeholder="自定义通知消息（可选）..."
              style="margin-top: 8px;"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  AuditStatusTextMap,
  AuditStatusColorMap,
  type CultivationObject
} from '@/types/audit-confirmation'
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  open: boolean
  cultivationObject: CultivationObject | null
  scoreResult: any
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'submit', data: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  auditResult: 1, // 1-通过, 2-不通过, 3-待补充
  overallComments: '',
  nextAction: '',
  indicatorComments: [] as Array<{
    indicatorId: number
    comment: string
    suggestion: string
  }>,
  improvementSuggestions: [''],
  commendations: [''],
  concerns: [''],
  notificationSettings: ['notifyApplicant'],
  customNotificationMessage: ''
})

// 表单验证规则
const formRules = {
  auditResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  overallComments: [
    { required: true, message: '请填写总体审核意见', trigger: 'blur' },
    { min: 10, message: '审核意见至少10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const getStatusText = (status: number) => {
  return AuditStatusTextMap[status as keyof typeof AuditStatusTextMap] || '未知'
}

const getStatusColor = (status: number) => {
  return AuditStatusColorMap[status as keyof typeof AuditStatusColorMap] || 'default'
}

// 方法
const getIndicatorComment = (indicatorId: number) => {
  let comment = formData.indicatorComments.find(c => c.indicatorId === indicatorId)
  if (!comment) {
    comment = { indicatorId, comment: '', suggestion: '' }
    formData.indicatorComments.push(comment)
  }
  return comment
}

const onAuditResultChange = () => {
  // 根据审核结果调整表单显示内容
  if (formData.auditResult === 1) {
    // 通过时，显示表扬内容
    if (formData.commendations.length === 0) {
      formData.commendations = ['']
    }
  } else {
    // 不通过或待补充时，显示改进建议
    if (formData.improvementSuggestions.length === 0) {
      formData.improvementSuggestions = ['']
    }
  }
}

const addSuggestion = () => {
  formData.improvementSuggestions.push('')
}

const removeSuggestion = (index: number) => {
  formData.improvementSuggestions.splice(index, 1)
}

const addCommendation = () => {
  formData.commendations.push('')
}

const removeCommendation = (index: number) => {
  formData.commendations.splice(index, 1)
}

const addConcern = () => {
  formData.concerns.push('')
}

const removeConcern = (index: number) => {
  formData.concerns.splice(index, 1)
}

const handleCancel = () => {
  emit('update:open', false)
  resetForm()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    loading.value = true
    
    // 整理提交数据
    const submitData = {
      objectId: props.cultivationObject?.id,
      auditResult: formData.auditResult,
      overallComments: formData.overallComments,
      nextAction: formData.nextAction,
      indicatorComments: formData.indicatorComments.filter(c => c.comment || c.suggestion),
      improvementSuggestions: formData.improvementSuggestions.filter(s => s.trim()),
      commendations: formData.commendations.filter(c => c.trim()),
      concerns: formData.concerns.filter(c => c.trim()),
      notificationSettings: {
        notifyApplicant: formData.notificationSettings.includes('notifyApplicant'),
        notifyManager: formData.notificationSettings.includes('notifyManager'),
        notifyNextAuditor: formData.notificationSettings.includes('notifyNextAuditor'),
        customMessage: formData.customNotificationMessage
      }
    }
    
    emit('submit', submitData)
    emit('update:open', false)
    resetForm()
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  formData.auditResult = 1
  formData.overallComments = ''
  formData.nextAction = ''
  formData.indicatorComments = []
  formData.improvementSuggestions = ['']
  formData.commendations = ['']
  formData.concerns = ['']
  formData.notificationSettings = ['notifyApplicant']
  formData.customNotificationMessage = ''
  
  formRef.value?.resetFields()
}

// 监听弹窗打开状态
watch(() => props.open, (newOpen) => {
  if (newOpen) {
    // 弹窗打开时，初始化表单数据
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.audit-modal-content {
  .object-info-section {
    margin-bottom: 24px;
  }

  .score-value {
    font-weight: 600;
    color: #1890ff;
  }

  .indicator-comments-section {
    .indicator-comment-item {
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .indicator-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .indicator-name {
          font-weight: 600;
          color: #333;
        }

        .indicator-score {
          color: #1890ff;
          font-weight: 600;
        }
      }

      .comment-inputs {
        .ant-input {
          margin-bottom: 8px;
        }
      }
    }
  }

  .improvement-suggestions,
  .commendations,
  .concerns {
    .ant-input {
      margin-bottom: 8px;
    }
  }

  :deep(.ant-form-item-label) {
    font-weight: 600;
  }

  :deep(.ant-radio-wrapper) {
    display: flex;
    align-items: center;
    margin-right: 24px;
    
    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }

  :deep(.ant-checkbox-group) {
    .ant-checkbox-wrapper {
      margin-right: 16px;
      margin-bottom: 8px;
    }
  }
}
</style>