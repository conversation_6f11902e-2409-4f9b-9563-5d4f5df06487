<template>
  <div class="review-process-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>审核流程配置</h2>
        <p>管理员可以自定义审核流程节点，包括审核人员、审核标准、审核周期等</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="previewProcess">
            <template #icon><eye-outlined /></template>
            预览流程
          </a-button>
          <a-button @click="resetToDefault">
            <template #icon><redo-outlined /></template>
            恢复默认
          </a-button>
          <a-button type="primary" @click="saveProcessConfig" :loading="saving">
            <template #icon><save-outlined /></template>
            保存配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 流程配置区域 -->
    <div class="config-section">
      <a-row :gutter="24">
        <!-- 左侧流程节点配置 -->
        <a-col :xs="24" :lg="16">
          <a-card title="流程节点配置" class="process-nodes-card">
            <template #extra>
              <a-button type="primary" size="small" @click="addProcessNode">
                <template #icon><plus-outlined /></template>
                添加节点
              </a-button>
            </template>

            <div class="process-nodes-container">
              <div class="process-flow-chart">
                <div v-for="(node, index) in processNodes" :key="node.id" class="process-node-wrapper">
                  <div class="process-node" :class="['node-' + node.type]">
                    <div class="node-header">
                      <div class="node-title">
                        <div class="node-icon">
                          <component :is="getNodeIcon(node.type)" />
                        </div>
                        <div class="node-name">{{ node.name }}</div>
                      </div>
                      <div class="node-actions">
                        <a-button type="text" size="small" @click="editNode(node)">
                          <edit-outlined />
                        </a-button>
                        <a-button type="text" size="small" danger @click="deleteNode(node.id)">
                          <delete-outlined />
                        </a-button>
                      </div>
                    </div>
                    
                    <div class="node-content">
                      <div class="node-info">
                        <div class="info-item">
                          <span class="label">处理时限：</span>
                          <span class="value">{{ node.timeLimit }}天</span>
                        </div>
                        <div class="info-item">
                          <span class="label">审核人员：</span>
                          <span class="value">{{ getReviewersText(node.reviewers) }}</span>
                        </div>
                        <div class="info-item" v-if="node.autoAssign">
                          <span class="label">自动分配：</span>
                          <a-tag color="green" size="small">是</a-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 连接线 -->
                  <div v-if="index < processNodes.length - 1" class="process-connector">
                    <div class="connector-line"></div>
                    <div class="connector-arrow">
                      <down-outlined />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 空状态 -->
              <a-empty v-if="processNodes.length === 0" description="暂无流程节点，请添加节点" />
            </div>
          </a-card>
        </a-col>
        
        <!-- 右侧配置面板 -->
        <a-col :xs="24" :lg="8">
          <a-affix :offset-top="80">
            <div class="config-panel">
              <!-- 流程基本信息 -->
              <a-card title="流程基本信息" size="small" class="config-card">
                <a-form layout="vertical" :model="processConfig">
                  <a-form-item label="流程名称" name="name">
                    <a-input v-model:value="processConfig.name" placeholder="请输入流程名称" />
                  </a-form-item>
                  <a-form-item label="适用项目类型" name="projectTypes">
                    <a-select
                      v-model:value="processConfig.projectTypes"
                      mode="multiple"
                      placeholder="选择适用的项目类型"
                    >
                      <a-select-option value="party_building">党建项目</a-select-option>
                      <a-select-option value="innovation">创新项目</a-select-option>
                      <a-select-option value="service">服务项目</a-select-option>
                      <a-select-option value="culture">文化项目</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item label="流程描述" name="description">
                    <a-textarea
                      v-model:value="processConfig.description"
                      placeholder="请输入流程描述"
                      :rows="3"
                    />
                  </a-form-item>
                  <a-form-item label="是否启用" name="enabled">
                    <a-switch v-model:checked="processConfig.enabled" />
                  </a-form-item>
                </a-form>
              </a-card>

              <!-- 审核标准配置 -->
              <a-card title="审核标准配置" size="small" class="config-card">
                <div class="criteria-list">
                  <div v-for="(criteria, index) in auditCriteria" :key="index" class="criteria-item">
                    <div class="criteria-header">
                      <span class="criteria-name">{{ criteria.name }}</span>
                      <a-button type="text" size="small" @click="editCriteria(index)">
                        <edit-outlined />
                      </a-button>
                    </div>
                    <div class="criteria-content">
                      <div class="criteria-desc">{{ criteria.description }}</div>
                      <div class="criteria-weight">权重：{{ criteria.weight }}%</div>
                    </div>
                  </div>
                </div>
                <a-button type="dashed" block @click="addAuditCriteria">
                  <plus-outlined />
                  添加审核标准
                </a-button>
              </a-card>

              <!-- 通知配置 -->
              <a-card title="通知配置" size="small" class="config-card">
                <a-form layout="vertical" :model="notificationConfig">
                  <a-form-item label="审核开始通知">
                    <a-switch v-model:checked="notificationConfig.onStart" />
                  </a-form-item>
                  <a-form-item label="审核完成通知">
                    <a-switch v-model:checked="notificationConfig.onComplete" />
                  </a-form-item>
                  <a-form-item label="超时提醒">
                    <a-switch v-model:checked="notificationConfig.onTimeout" />
                  </a-form-item>
                  <a-form-item label="提醒提前时间" v-if="notificationConfig.onTimeout">
                    <a-input-number
                      v-model:value="notificationConfig.reminderHours"
                      :min="1"
                      :max="72"
                      addon-after="小时"
                    />
                  </a-form-item>
                </a-form>
              </a-card>
            </div>
          </a-affix>
        </a-col>
      </a-row>
    </div>

    <!-- 节点编辑弹窗 -->
    <a-modal
      v-model:open="nodeModalVisible"
      :title="editingNode ? '编辑节点' : '添加节点'"
      width="600px"
      @ok="saveNode"
      @cancel="cancelEditNode"
    >
      <a-form ref="nodeFormRef" :model="nodeForm" layout="vertical">
        <a-form-item label="节点名称" name="name" :rules="[{ required: true, message: '请输入节点名称' }]">
          <a-input v-model:value="nodeForm.name" placeholder="请输入节点名称" />
        </a-form-item>
        
        <a-form-item label="节点类型" name="type" :rules="[{ required: true, message: '请选择节点类型' }]">
          <a-select v-model:value="nodeForm.type" placeholder="请选择节点类型">
            <a-select-option value="submit">提交申请</a-select-option>
            <a-select-option value="auto_review">智能预审</a-select-option>
            <a-select-option value="manual_review">人工审核</a-select-option>
            <a-select-option value="leader_review">领导审核</a-select-option>
            <a-select-option value="public">结果公示</a-select-option>
            <a-select-option value="complete">流程结束</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="处理时限" name="timeLimit" :rules="[{ required: true, message: '请输入处理时限' }]">
          <a-input-number
            v-model:value="nodeForm.timeLimit"
            :min="1"
            :max="30"
            addon-after="天"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="审核人员" name="reviewers">
          <a-select
            v-model:value="nodeForm.reviewers"
            mode="multiple"
            placeholder="选择审核人员"
            :options="reviewerOptions"
          />
        </a-form-item>
        
        <a-form-item label="自动分配审核员" name="autoAssign">
          <a-switch v-model:checked="nodeForm.autoAssign" />
          <div class="form-help-text">开启后系统将自动分配可用的审核人员</div>
        </a-form-item>
        
        <a-form-item label="节点描述" name="description">
          <a-textarea v-model:value="nodeForm.description" placeholder="请输入节点描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 审核标准编辑弹窗 -->
    <a-modal
      v-model:open="criteriaModalVisible"
      :title="editingCriteriaIndex !== -1 ? '编辑审核标准' : '添加审核标准'"
      width="500px"
      @ok="saveCriteria"
      @cancel="cancelEditCriteria"
    >
      <a-form ref="criteriaFormRef" :model="criteriaForm" layout="vertical">
        <a-form-item label="标准名称" name="name" :rules="[{ required: true, message: '请输入标准名称' }]">
          <a-input v-model:value="criteriaForm.name" placeholder="请输入标准名称" />
        </a-form-item>
        <a-form-item label="标准描述" name="description">
          <a-textarea v-model:value="criteriaForm.description" placeholder="请输入标准描述" :rows="3" />
        </a-form-item>
        <a-form-item label="权重" name="weight" :rules="[{ required: true, message: '请输入权重' }]">
          <a-input-number
            v-model:value="criteriaForm.weight"
            :min="1"
            :max="100"
            addon-after="%"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 流程预览弹窗 -->
    <a-modal
      v-model:open="previewModalVisible"
      title="流程预览"
      width="800px"
      :footer="null"
    >
      <div class="process-preview">
        <a-steps direction="vertical" :current="0">
          <a-step v-for="(node, index) in processNodes" :key="node.id">
            <template #title>
              <div class="step-title">
                {{ node.name }}
                <a-tag :color="getNodeTypeColor(node.type)" size="small">
                  {{ getNodeTypeText(node.type) }}
                </a-tag>
              </div>
            </template>
            <template #description>
              <div class="step-description">
                <p>{{ node.description }}</p>
                <div class="step-details">
                  <span class="detail-item">处理时限：{{ node.timeLimit }}天</span>
                  <span class="detail-item">审核人员：{{ getReviewersText(node.reviewers) }}</span>
                </div>
              </div>
            </template>
          </a-step>
        </a-steps>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import {
  EyeOutlined,
  RedoOutlined,
  SaveOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownOutlined,
  FileTextOutlined,
  RobotOutlined,
  UserOutlined,
  CrownOutlined,
  NotificationOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface ProcessNode {
  id: string
  name: string
  type: 'submit' | 'auto_review' | 'manual_review' | 'leader_review' | 'public' | 'complete'
  timeLimit: number
  reviewers: string[]
  autoAssign: boolean
  description: string
}

interface AuditCriteria {
  name: string
  description: string
  weight: number
}

interface ProcessConfig {
  name: string
  projectTypes: string[]
  description: string
  enabled: boolean
}

interface NotificationConfig {
  onStart: boolean
  onComplete: boolean
  onTimeout: boolean
  reminderHours: number
}

// 响应式数据
const saving = ref(false)
const nodeModalVisible = ref(false)
const criteriaModalVisible = ref(false)
const previewModalVisible = ref(false)
const editingNode = ref<ProcessNode | null>(null)
const editingCriteriaIndex = ref(-1)

// 流程配置
const processConfig = reactive<ProcessConfig>({
  name: '标准审核流程',
  projectTypes: ['party_building'],
  description: '用于党建项目的标准审核流程',
  enabled: true
})

// 通知配置
const notificationConfig = reactive<NotificationConfig>({
  onStart: true,
  onComplete: true,
  onTimeout: true,
  reminderHours: 24
})

// 流程节点
const processNodes = ref<ProcessNode[]>([
  {
    id: '1',
    name: '申请提交',
    type: 'submit',
    timeLimit: 1,
    reviewers: [],
    autoAssign: false,
    description: '培育对象提交申报材料'
  },
  {
    id: '2',
    name: '智能预审',
    type: 'auto_review',
    timeLimit: 1,
    reviewers: [],
    autoAssign: true,
    description: '系统自动进行初步审核评分'
  },
  {
    id: '3',
    name: '人工审核',
    type: 'manual_review',
    timeLimit: 5,
    reviewers: ['reviewer1', 'reviewer2'],
    autoAssign: true,
    description: '审核人员进行详细审核'
  },
  {
    id: '4',
    name: '结果公示',
    type: 'public',
    timeLimit: 3,
    reviewers: [],
    autoAssign: false,
    description: '审核结果公示阶段'
  }
])

// 审核标准
const auditCriteria = ref<AuditCriteria[]>([
  {
    name: '创新程度',
    description: '评价项目在理念、技术、方法等方面的创新性',
    weight: 30
  },
  {
    name: '实施效果',
    description: '评价项目实施后的实际效果和影响',
    weight: 40
  },
  {
    name: '可推广性',
    description: '评价项目经验做法的推广应用潜力',
    weight: 30
  }
])

// 表单数据
const nodeForm = ref<Partial<ProcessNode>>({})
const criteriaForm = ref<Partial<AuditCriteria>>({})

// 审核人员选项
const reviewerOptions = ref([
  { label: '张审核员', value: 'reviewer1' },
  { label: '李审核员', value: 'reviewer2' },
  { label: '王审核员', value: 'reviewer3' },
  { label: '陈审核员', value: 'reviewer4' }
])

// 方法定义
const addProcessNode = () => {
  editingNode.value = null
  nodeForm.value = {
    name: '',
    type: 'manual_review',
    timeLimit: 3,
    reviewers: [],
    autoAssign: false,
    description: ''
  }
  nodeModalVisible.value = true
}

const editNode = (node: ProcessNode) => {
  editingNode.value = node
  nodeForm.value = { ...node }
  nodeModalVisible.value = true
}

const saveNode = () => {
  if (!nodeForm.value.name || !nodeForm.value.type) {
    message.warning('请填写完整的节点信息')
    return
  }

  if (editingNode.value) {
    // 编辑节点
    const index = processNodes.value.findIndex(n => n.id === editingNode.value!.id)
    if (index !== -1) {
      processNodes.value[index] = { ...editingNode.value, ...nodeForm.value } as ProcessNode
    }
  } else {
    // 添加节点
    const newNode: ProcessNode = {
      id: Date.now().toString(),
      name: nodeForm.value.name!,
      type: nodeForm.value.type!,
      timeLimit: nodeForm.value.timeLimit || 3,
      reviewers: nodeForm.value.reviewers || [],
      autoAssign: nodeForm.value.autoAssign || false,
      description: nodeForm.value.description || ''
    }
    processNodes.value.push(newNode)
  }

  nodeModalVisible.value = false
  message.success('节点保存成功')
}

const cancelEditNode = () => {
  nodeModalVisible.value = false
  editingNode.value = null
  nodeForm.value = {}
}

const deleteNode = (nodeId: string) => {
  const index = processNodes.value.findIndex(n => n.id === nodeId)
  if (index !== -1) {
    processNodes.value.splice(index, 1)
    message.success('节点删除成功')
  }
}

const addAuditCriteria = () => {
  editingCriteriaIndex.value = -1
  criteriaForm.value = {
    name: '',
    description: '',
    weight: 0
  }
  criteriaModalVisible.value = true
}

const editCriteria = (index: number) => {
  editingCriteriaIndex.value = index
  criteriaForm.value = { ...auditCriteria.value[index] }
  criteriaModalVisible.value = true
}

const saveCriteria = () => {
  if (!criteriaForm.value.name || !criteriaForm.value.weight) {
    message.warning('请填写完整的标准信息')
    return
  }

  if (editingCriteriaIndex.value !== -1) {
    // 编辑标准
    auditCriteria.value[editingCriteriaIndex.value] = { ...criteriaForm.value } as AuditCriteria
  } else {
    // 添加标准
    auditCriteria.value.push({ ...criteriaForm.value } as AuditCriteria)
  }

  criteriaModalVisible.value = false
  message.success('审核标准保存成功')
}

const cancelEditCriteria = () => {
  criteriaModalVisible.value = false
  editingCriteriaIndex.value = -1
  criteriaForm.value = {}
}

const previewProcess = () => {
  previewModalVisible.value = true
}

const resetToDefault = () => {
  processNodes.value = [
    {
      id: '1',
      name: '申请提交',
      type: 'submit',
      timeLimit: 1,
      reviewers: [],
      autoAssign: false,
      description: '培育对象提交申报材料'
    },
    {
      id: '2',
      name: '智能预审',
      type: 'auto_review',
      timeLimit: 1,
      reviewers: [],
      autoAssign: true,
      description: '系统自动进行初步审核评分'
    },
    {
      id: '3',
      name: '人工审核',
      type: 'manual_review',
      timeLimit: 5,
      reviewers: ['reviewer1', 'reviewer2'],
      autoAssign: true,
      description: '审核人员进行详细审核'
    }
  ]
  message.info('已恢复为默认流程配置')
}

const saveProcessConfig = () => {
  if (!processConfig.name.trim()) {
    message.warning('请填写流程名称')
    return
  }

  if (processNodes.value.length === 0) {
    message.warning('请至少添加一个流程节点')
    return
  }

  saving.value = true
  setTimeout(() => {
    saving.value = false
    message.success('流程配置保存成功')
  }, 1000)
}

// 工具方法
const getNodeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    submit: FileTextOutlined,
    auto_review: RobotOutlined,
    manual_review: UserOutlined,
    leader_review: CrownOutlined,
    public: NotificationOutlined,
    complete: CheckCircleOutlined
  }
  return iconMap[type] || UserOutlined
}

const getNodeTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    submit: 'blue',
    auto_review: 'cyan',
    manual_review: 'orange',
    leader_review: 'purple',
    public: 'green',
    complete: 'gray'
  }
  return colorMap[type] || 'default'
}

const getNodeTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    submit: '提交节点',
    auto_review: '自动节点',
    manual_review: '人工节点',
    leader_review: '领导节点',
    public: '公示节点',
    complete: '结束节点'
  }
  return textMap[type] || '未知'
}

const getReviewersText = (reviewers: string[]) => {
  if (reviewers.length === 0) return '系统自动'
  const reviewerNames = reviewers.map(id => {
    const reviewer = reviewerOptions.value.find(r => r.value === id)
    return reviewer?.label || id
  })
  return reviewerNames.join('、')
}
</script>

<style scoped lang="scss">
.review-process-config-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.config-section {
  .process-nodes-card {
    min-height: 600px;
  }

  .process-nodes-container {
    .process-flow-chart {
      .process-node-wrapper {
        margin-bottom: 24px;

        .process-node {
          border: 2px solid #d9d9d9;
          border-radius: 8px;
          background: white;
          transition: all 0.3s;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
          }

          &.node-submit {
            border-color: #1890ff;
          }

          &.node-auto_review {
            border-color: #13c2c2;
          }

          &.node-manual_review {
            border-color: #fa8c16;
          }

          &.node-leader_review {
            border-color: #722ed1;
          }

          &.node-public {
            border-color: #52c41a;
          }

          &.node-complete {
            border-color: #8c8c8c;
          }

          .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;

            .node-title {
              display: flex;
              align-items: center;
              gap: 12px;

              .node-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                background: #f5f5f5;
                border-radius: 6px;
                color: #1890ff;
              }

              .node-name {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }
            }

            .node-actions {
              display: flex;
              gap: 4px;
            }
          }

          .node-content {
            padding: 16px;

            .node-info {
              .info-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                .label {
                  color: #8c8c8c;
                  min-width: 80px;
                  font-size: 14px;
                }

                .value {
                  color: #262626;
                  font-size: 14px;
                }
              }
            }
          }
        }

        .process-connector {
          display: flex;
          flex-direction: column;
          align-items: center;
          height: 40px;
          position: relative;

          .connector-line {
            width: 2px;
            height: 24px;
            background: #d9d9d9;
          }

          .connector-arrow {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
            border: 2px solid #d9d9d9;
            border-radius: 50%;
            color: #8c8c8c;
          }
        }
      }
    }
  }
}

.config-panel {
  .config-card {
    margin-bottom: 16px;
  }

  .criteria-list {
    margin-bottom: 16px;

    .criteria-item {
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fafafa;

      .criteria-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .criteria-name {
          font-weight: 600;
          color: #262626;
        }
      }

      .criteria-content {
        .criteria-desc {
          color: #595959;
          font-size: 12px;
          margin-bottom: 4px;
        }

        .criteria-weight {
          color: #1890ff;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }
}

.form-help-text {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

.process-preview {
  .step-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
  }

  .step-description {
    p {
      color: #595959;
      margin-bottom: 8px;
    }

    .step-details {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .detail-item {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-process-config-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .config-section {
      .ant-col {
        margin-bottom: 24px;
      }
    }
  }
}
</style>