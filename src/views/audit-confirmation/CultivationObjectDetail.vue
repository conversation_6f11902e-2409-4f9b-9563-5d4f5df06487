<template>
  <div class="cultivation-object-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button type="text" @click="goBack">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <a-divider type="vertical" />
        <div class="header-title">
          <h2>{{ objectInfo?.name || '培育对象详情' }}</h2>
          <p>查看培育对象评分结果、指标详情及审核历史</p>
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showAuditModal" v-if="canAudit">
            <template #icon><audit-outlined /></template>
            审核
          </a-button>
          <a-button @click="showScoreModifyModal" v-if="canModifyScore">
            <template #icon><edit-outlined /></template>
            修改评分
          </a-button>
          <a-button @click="showHistoryModal">
            <template #icon><history-outlined /></template>
            查看历史
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="basic-info-section">
      <a-card title="基本信息" :loading="loading">
        <a-descriptions :column="3" bordered>
          <a-descriptions-item label="培育对象名称">
            {{ objectInfo?.name }}
          </a-descriptions-item>
          <a-descriptions-item label="所属项目">
            {{ objectInfo?.projectName }}
          </a-descriptions-item>
          <a-descriptions-item label="组织机构">
            {{ objectInfo?.organizationName }}
          </a-descriptions-item>
          <a-descriptions-item label="联系人">
            {{ objectInfo?.contactPerson }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ objectInfo?.contactPhone }}
          </a-descriptions-item>
          <a-descriptions-item label="审核状态">
            <a-tag :color="getStatusColor(objectInfo?.status)">
              {{ getStatusText(objectInfo?.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="提交时间">
            {{ objectInfo?.submitTime }}
          </a-descriptions-item>
          <a-descriptions-item label="审核时间" v-if="objectInfo?.auditTime">
            {{ objectInfo?.auditTime }}
          </a-descriptions-item>
          <a-descriptions-item label="审核人" v-if="objectInfo?.auditor">
            {{ objectInfo?.auditor }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 评分结果概览 -->
    <div class="score-overview-section">
      <a-card title="评分结果概览" :loading="loading">
        <template #extra>
          <a-statistic 
            title="总分" 
            :value="scoreResult?.totalScore || 0" 
            :precision="1"
            suffix="分"
            :value-style="{ color: getScoreColor(scoreResult?.totalScore || 0) }"
          />
        </template>
        
        <!-- 评分详情 -->
        <div class="score-breakdown" v-if="scoreResult">
          <a-row :gutter="[16, 16]">
            <a-col :span="24" v-for="category in scoreResult.scoreBreakdown" :key="category.categoryName">
              <a-card size="small">
                <template #title>
                  <div class="category-title">
                    <span>{{ category.categoryName }}</span>
                    <span class="category-score">
                      {{ category.categoryScore }} / {{ category.categoryMaxScore }} 分
                    </span>
                  </div>
                </template>
                
                <!-- 指标列表 -->
                <div class="indicators-list">
                  <div 
                    v-for="indicator in category.indicators" 
                    :key="indicator.indicatorCode"
                    class="indicator-item"
                  >
                    <div class="indicator-info">
                      <div class="indicator-name">{{ indicator.indicatorName }}</div>
                      <div class="indicator-details">
                        <span class="indicator-score">
                          {{ indicator.score }} / {{ indicator.maxScore }} 分
                        </span>
                        <span class="indicator-weight">权重: {{ indicator.weight }}%</span>
                      </div>
                    </div>
                    <div class="indicator-progress">
                      <a-progress 
                        :percent="Math.round((indicator.score / indicator.maxScore) * 100)"
                        :stroke-color="getProgressColor(indicator.score / indicator.maxScore)"
                        size="small"
                        :show-info="false"
                      />
                    </div>
                    <div class="indicator-actions">
                      <a-button type="link" size="small" @click="showIndicatorDetail(indicator)">
                        查看详情
                      </a-button>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 系统评价和风险提示 -->
        <div class="auto-evaluation" v-if="scoreResult">
          <a-divider>系统自动评价</a-divider>
          <div class="evaluation-content">
            <p>{{ scoreResult.autoEvaluationComments }}</p>
          </div>
          
          <a-divider>风险提示</a-divider>
          <div class="risk-warnings">
            <a-alert
              v-for="(warning, index) in scoreResult.riskWarnings"
              :key="index"
              :message="warning"
              type="warning"
              show-icon
              style="margin-bottom: 8px"
            />
          </div>
        </div>
      </a-card>
    </div>

    <!-- 审核历史 -->
    <div class="audit-history-section">
      <a-card title="审核历史" :loading="loading">
        <a-timeline>
          <a-timeline-item 
            v-for="record in auditHistory" 
            :key="record.id"
            :color="getAuditResultColor(record.auditResult)"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="audit-type">{{ getAuditTypeText(record.auditType) }}</span>
                <span class="audit-time">{{ record.auditTime }}</span>
                <a-tag :color="getAuditResultColor(record.auditResult)" size="small">
                  {{ getAuditResultText(record.auditResult) }}
                </a-tag>
              </div>
              <div class="timeline-body">
                <p><strong>审核人：</strong>{{ record.auditor }}</p>
                <p><strong>审核意见：</strong>{{ record.comments }}</p>
                <p v-if="record.suggestions"><strong>建议：</strong>{{ record.suggestions }}</p>
                <div v-if="record.originalScore !== record.finalScore" class="score-change">
                  <strong>评分调整：</strong>
                  {{ record.originalScore }} → {{ record.finalScore }} 分
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>

    <!-- 审核模态框 -->
    <AuditModal
      v-model:open="auditModalVisible"
      :cultivation-object="objectInfo"
      :score-result="scoreResult"
      @submit="handleAuditSubmit"
    />

    <!-- 评分修改模态框 -->
    <ScoreModifyModal
      v-model:open="scoreModifyModalVisible"
      :cultivation-object="objectInfo"
      :indicators="indicatorResults"
      @submit="handleScoreModify"
    />

    <!-- 指标详情模态框 -->
    <IndicatorDetailModal
      v-model:open="indicatorDetailModalVisible"
      :indicator="currentIndicator"
      :cultivation-object-id="objectId"
    />

    <!-- 历史记录模态框 -->
    <HistoryModal
      v-model:open="historyModalVisible"
      :cultivation-object-id="objectId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useAuditConfirmationStore } from '@/store/modules/audit-confirmation'
import {
  getCultivationObjectScoreResult,
  getCultivationObjectIndicatorResults,
  getAuditRecordList
} from '@/api/audit-confirmation'
import {
  AuditStatusTextMap,
  AuditStatusColorMap,
  AuditTypeTextMap,
  AuditResultTextMap,
  AuditResultColorMap
} from '@/types/audit-confirmation'
import type {
  CultivationObject,
  IndicatorResult,
  AuditRecord
} from '@/types/audit-confirmation'
import {
  ArrowLeftOutlined,
  AuditOutlined,
  EditOutlined,
  HistoryOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import AuditModal from './components/AuditModal.vue'
import ScoreModifyModal from './components/ScoreModifyModal.vue'
import IndicatorDetailModal from './components/IndicatorDetailModal.vue'
import HistoryModal from './components/HistoryModal.vue'

const router = useRouter()
const route = useRoute()
const auditConfirmationStore = useAuditConfirmationStore()

// 响应式数据
const loading = ref(false)
const objectId = ref(Number(route.params.id))
const objectInfo = ref<CultivationObject | null>(null)
const scoreResult = ref<any>(null)
const indicatorResults = ref<any[]>([])
const auditHistory = ref<AuditRecord[]>([])

// 模态框状态
const auditModalVisible = ref(false)
const scoreModifyModalVisible = ref(false)
const indicatorDetailModalVisible = ref(false)
const historyModalVisible = ref(false)
const currentIndicator = ref<any>(null)

// 权限控制
const canAudit = ref(true) // 从权限API获取
const canModifyScore = ref(true) // 从权限API获取

// 计算属性
const getStatusText = (status: number) => {
  return AuditStatusTextMap[status as keyof typeof AuditStatusTextMap] || '未知'
}

const getStatusColor = (status: number) => {
  return AuditStatusColorMap[status as keyof typeof AuditStatusColorMap] || 'default'
}

const getAuditTypeText = (type: number) => {
  return AuditTypeTextMap[type as keyof typeof AuditTypeTextMap] || '未知'
}

const getAuditResultText = (result: number) => {
  return AuditResultTextMap[result as keyof typeof AuditResultTextMap] || '未知'
}

const getAuditResultColor = (result: number) => {
  return AuditResultColorMap[result as keyof typeof AuditResultColorMap] || 'default'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getProgressColor = (percent: number) => {
  if (percent >= 0.9) return '#52c41a'
  if (percent >= 0.8) return '#1890ff'
  if (percent >= 0.7) return '#faad14'
  return '#ff4d4f'
}

// 方法
const goBack = () => {
  router.back()
}

const showAuditModal = () => {
  auditModalVisible.value = true
}

const showScoreModifyModal = () => {
  scoreModifyModalVisible.value = true
}

const showHistoryModal = () => {
  historyModalVisible.value = true
}

const showIndicatorDetail = (indicator: any) => {
  currentIndicator.value = indicator
  indicatorDetailModalVisible.value = true
}

const handleAuditSubmit = async (data: any) => {
  try {
    loading.value = true
    const success = await auditConfirmationStore.submitAuditResult(data)
    if (success) {
      message.success('审核提交成功')
      await refreshData()
    } else {
      message.error('审核提交失败')
    }
  } catch (error) {
    message.error('审核提交失败')
  } finally {
    loading.value = false
  }
}

const handleScoreModify = async (data: any) => {
  try {
    loading.value = true
    // 处理批量修改
    for (const item of data.modifications) {
      await auditConfirmationStore.updateScore({
        cultivationObjectId: objectId.value,
        indicatorId: item.indicatorId,
        score: item.newScore,
        comments: item.reason
      })
    }
    message.success('评分修改成功')
    await refreshData()
  } catch (error) {
    message.error('评分修改失败')
  } finally {
    loading.value = false
  }
}

const refreshData = async () => {
  try {
    loading.value = true
    await Promise.all([
      fetchObjectInfo(),
      fetchScoreResult(),
      fetchIndicatorResults(),
      fetchAuditHistory()
    ])
  } catch (error) {
    message.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

const fetchObjectInfo = async () => {
  // 从store中获取对象信息
  const objects = auditConfirmationStore.cultivationObjectList
  objectInfo.value = objects.find(obj => obj.id === objectId.value) || null
  
  if (!objectInfo.value) {
    // 如果store中没有，重新获取
    await auditConfirmationStore.fetchCultivationObjectList()
    const refreshedObjects = auditConfirmationStore.cultivationObjectList
    objectInfo.value = refreshedObjects.find(obj => obj.id === objectId.value) || null
  }
}

const fetchScoreResult = async () => {
  try {
    scoreResult.value = await getCultivationObjectScoreResult(objectId.value)
  } catch (error) {
    console.error('获取评分结果失败:', error)
  }
}

const fetchIndicatorResults = async () => {
  try {
    indicatorResults.value = await getCultivationObjectIndicatorResults(objectId.value)
  } catch (error) {
    console.error('获取指标结果失败:', error)
  }
}

const fetchAuditHistory = async () => {
  try {
    const result = await getAuditRecordList()
    auditHistory.value = result.list.filter(record => 
      record.cultivationObjectId === objectId.value
    )
  } catch (error) {
    console.error('获取审核历史失败:', error)
  }
}

// 监听路由参数变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    objectId.value = Number(newId)
    refreshData()
  }
})

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.cultivation-object-detail {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .header-title {
        h2 {
          margin: 0;
          color: #1890ff;
          font-size: 24px;
          font-weight: 600;
        }

        p {
          margin: 4px 0 0 0;
          color: #666;
          font-size: 14px;
        }
      }
    }
  }

  .basic-info-section,
  .score-overview-section,
  .audit-history-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .category-title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .category-score {
      font-weight: 600;
      color: #1890ff;
    }
  }

  .indicators-list {
    .indicator-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .indicator-info {
        flex: 1;
        min-width: 0;

        .indicator-name {
          font-weight: 600;
          margin-bottom: 4px;
          color: #333;
        }

        .indicator-details {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #666;

          .indicator-score {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }

      .indicator-progress {
        flex: 0 0 200px;
        margin: 0 16px;
      }

      .indicator-actions {
        flex: 0 0 auto;
      }
    }
  }

  .auto-evaluation {
    margin-top: 24px;

    .evaluation-content {
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 6px;
      padding: 12px;
      color: #389e0d;
    }

    .risk-warnings {
      margin-top: 12px;
    }
  }

  .timeline-content {
    .timeline-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      .audit-type {
        font-weight: 600;
        color: #333;
      }

      .audit-time {
        color: #666;
        font-size: 12px;
      }
    }

    .timeline-body {
      color: #666;
      font-size: 14px;

      p {
        margin: 4px 0;
      }

      .score-change {
        color: #1890ff;
        font-weight: 600;
        margin-top: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cultivation-object-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .indicator-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .indicator-progress {
        flex: none;
        width: 100%;
        margin: 8px 0;
      }
    }
  }
}
</style>