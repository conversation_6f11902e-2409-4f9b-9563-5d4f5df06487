<template>
  <div class="appeal-management-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>申诉管理</h2>
        <p>培育对象可以对评分结果进行申诉，管理员配置申诉处理流程并进行申诉处理</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="showAppealProcessConfig">
            <template #icon><setting-outlined /></template>
            申诉流程配置
          </a-button>
          <a-button @click="exportAppealData" :loading="exporting">
            <template #icon><export-outlined /></template>
            导出申诉数据
          </a-button>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="总申诉数"
              :value="appealStats.total"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="待处理"
              :value="appealStats.pending"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="处理中"
              :value="appealStats.processing"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="已完成"
              :value="appealStats.completed"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-section">
      <a-card>
        <a-row :gutter="16">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="申请人">
              <a-input
                v-model:value="searchParams.applicantName"
                placeholder="请输入申请人名称"
                allow-clear
                @pressEnter="handleSearch"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="申诉状态">
              <a-select
                v-model:value="searchParams.status"
                placeholder="请选择申诉状态"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="submitted">已提交</a-select-option>
                <a-select-option value="processing">审理中</a-select-option>
                <a-select-option value="accepted">已受理</a-select-option>
                <a-select-option value="rejected">已驳回</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-form-item label="申诉类型">
              <a-select
                v-model:value="searchParams.appealType"
                placeholder="请选择申诉类型"
                allow-clear
                @change="handleSearch"
              >
                <a-select-option value="score_dispute">评分争议</a-select-option>
                <a-select-option value="process_issue">流程问题</a-select-option>
                <a-select-option value="material_error">材料错误</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div style="display: flex; gap: 8px; padding-top: 30px;">
              <a-button @click="resetSearch">重置</a-button>
              <a-button type="primary" @click="handleSearch" :loading="loading">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 申诉列表 -->
    <div class="list-section">
      <a-card>
        <template #title>
          <div class="table-title">
            <span>申诉列表</span>
            <a-tag color="blue">共 {{ pagination.total }} 条记录</a-tag>
          </div>
        </template>
        <template #extra>
          <a-button type="primary" @click="showSubmitAppeal">
            <template #icon><plus-outlined /></template>
            提交申诉
          </a-button>
        </template>

        <a-table
          :columns="columns"
          :data-source="appealList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'applicantInfo'">
              <div class="applicant-info">
                <div class="name">{{ record.applicantName }}</div>
                <div class="project">{{ record.projectName }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'appealType'">
              <a-tag :color="getAppealTypeColor(record.appealType)">
                {{ getAppealTypeText(record.appealType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)" size="small">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'timeRemaining'">
              <div class="time-remaining" :class="getTimeRemainingClass(record.timeRemaining)">
                {{ record.timeRemaining }}天
              </div>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewAppealDetail(record)">
                  查看详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="handleAppeal(record)"
                  :disabled="!canHandle(record)"
                >
                  处理申诉
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="viewAppealHistory(record)">
                        <history-outlined />
                        查看历史
                      </a-menu-item>
                      <a-menu-item @click="downloadAppealMaterials(record)">
                        <download-outlined />
                        下载材料
                      </a-menu-item>
                      <a-menu-item v-if="record.status === 'submitted'" @click="assignProcessor(record)">
                        <user-add-outlined />
                        分配处理人
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 提交申诉弹窗 -->
    <a-modal
      v-model:open="submitAppealModalVisible"
      title="提交申诉"
      width="700px"
      @ok="submitAppeal"
      @cancel="cancelSubmitAppeal"
      :confirm-loading="submitting"
    >
      <a-form ref="appealFormRef" :model="appealForm" layout="vertical">
        <a-form-item label="培育对象" name="objectId" :rules="[{ required: true, message: '请选择培育对象' }]">
          <a-select v-model:value="appealForm.objectId" placeholder="请选择培育对象">
            <a-select-option value="1">党员服务中心数字化改革项目</a-select-option>
            <a-select-option value="2">智慧党建平台建设</a-select-option>
            <a-select-option value="3">基层治理创新实践</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="申诉类型" name="appealType" :rules="[{ required: true, message: '请选择申诉类型' }]">
          <a-select v-model:value="appealForm.appealType" placeholder="请选择申诉类型">
            <a-select-option value="score_dispute">评分争议</a-select-option>
            <a-select-option value="process_issue">流程问题</a-select-option>
            <a-select-option value="material_error">材料错误</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="申诉原因" name="reason" :rules="[{ required: true, message: '请输入申诉原因' }]">
          <a-input v-model:value="appealForm.reason" placeholder="请简要说明申诉原因" />
        </a-form-item>
        
        <a-form-item label="申诉内容" name="content" :rules="[{ required: true, message: '请输入申诉内容' }]">
          <a-textarea
            v-model:value="appealForm.content"
            placeholder="请详细说明申诉内容，包括争议点和期望处理方式"
            :rows="5"
            :maxlength="1000"
            show-count
          />
        </a-form-item>
        
        <a-form-item label="申诉材料" name="attachments">
          <a-upload
            v-model:file-list="appealForm.attachments"
            :before-upload="beforeUpload"
            multiple
          >
            <a-button>
              <upload-outlined />
              上传申诉材料
            </a-button>
          </a-upload>
          <div class="upload-tip">支持上传PDF、DOC、图片等格式文件，单个文件不超过10MB</div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 处理申诉弹窗 -->
    <a-modal
      v-model:open="handleAppealModalVisible"
      title="处理申诉"
      width="800px"
      @ok="saveAppealHandling"
      @cancel="cancelHandleAppeal"
      :confirm-loading="handling"
    >
      <div v-if="selectedAppeal">
        <!-- 申诉信息展示 -->
        <div class="appeal-info-section">
          <h4>申诉信息</h4>
          <a-descriptions :column="2" bordered size="small">
            <a-descriptions-item label="申请人">{{ selectedAppeal.applicantName }}</a-descriptions-item>
            <a-descriptions-item label="申诉类型">
              <a-tag :color="getAppealTypeColor(selectedAppeal.appealType)">
                {{ getAppealTypeText(selectedAppeal.appealType) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="申诉原因">{{ selectedAppeal.reason }}</a-descriptions-item>
            <a-descriptions-item label="申诉时间">{{ selectedAppeal.submitTime }}</a-descriptions-item>
            <a-descriptions-item label="申诉内容" :span="2">{{ selectedAppeal.content }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 处理表单 -->
        <div class="handling-form-section">
          <h4>处理意见</h4>
          <a-form :model="handlingForm" layout="vertical">
            <a-form-item label="处理结果" name="result" :rules="[{ required: true, message: '请选择处理结果' }]">
              <a-radio-group v-model:value="handlingForm.result">
                <a-radio value="accepted">
                  <check-circle-outlined style="color: #52c41a;" />
                  受理申诉
                </a-radio>
                <a-radio value="rejected">
                  <close-circle-outlined style="color: #f5222d;" />
                  驳回申诉
                </a-radio>
                <a-radio value="partial">
                  <info-circle-outlined style="color: #faad14;" />
                  部分支持
                </a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item label="处理意见" name="comments" :rules="[{ required: true, message: '请输入处理意见' }]">
              <a-textarea
                v-model:value="handlingForm.comments"
                placeholder="请输入详细的处理意见和决策依据"
                :rows="4"
                :maxlength="500"
                show-count
              />
            </a-form-item>
            
            <a-form-item label="后续措施" name="nextSteps">
              <a-textarea
                v-model:value="handlingForm.nextSteps"
                placeholder="如需要，请说明后续处理措施"
                :rows="3"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>

    <!-- 申诉流程配置弹窗 -->
    <a-modal
      v-model:open="processConfigModalVisible"
      title="申诉处理流程配置"
      width="900px"
      @ok="saveProcessConfig"
      @cancel="cancelProcessConfig"
    >
      <div class="process-config-section">
        <h4>申诉处理流程</h4>
        <div class="process-steps">
          <a-steps direction="vertical" :current="0">
            <a-step v-for="(step, index) in appealProcessSteps" :key="index">
              <template #title>
                <div class="step-title">
                  {{ step.name }}
                  <a-button type="text" size="small" @click="editProcessStep(index)">
                    <edit-outlined />
                  </a-button>
                </div>
              </template>
              <template #description>
                <div class="step-description">
                  <p>{{ step.description }}</p>
                  <div class="step-details">
                    <span>处理时限：{{ step.timeLimit }}天</span>
                    <span>处理人员：{{ step.processors.join('、') }}</span>
                  </div>
                </div>
              </template>
            </a-step>
          </a-steps>
        </div>
        
        <div class="notification-config">
          <h4>通知配置</h4>
          <a-form layout="vertical">
            <a-form-item label="申诉提交通知">
              <a-switch v-model:checked="notificationSettings.onSubmit" />
              <span class="config-desc">申诉提交时通知相关人员</span>
            </a-form-item>
            <a-form-item label="处理进展通知">
              <a-switch v-model:checked="notificationSettings.onProgress" />
              <span class="config-desc">处理进展更新时通知申请人</span>
            </a-form-item>
            <a-form-item label="处理完成通知">
              <a-switch v-model:checked="notificationSettings.onComplete" />
              <span class="config-desc">申诉处理完成时通知申请人</span>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>

    <!-- 详情查看弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      :title="'申诉详情 - ' + (selectedAppeal?.applicantName || '')"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedAppeal" class="appeal-detail">
        <a-tabs>
          <a-tab-pane key="info" tab="申诉信息">
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="培育对象">{{ selectedAppeal.projectName }}</a-descriptions-item>
              <a-descriptions-item label="申请人">{{ selectedAppeal.applicantName }}</a-descriptions-item>
              <a-descriptions-item label="申诉类型">
                <a-tag :color="getAppealTypeColor(selectedAppeal.appealType)">
                  {{ getAppealTypeText(selectedAppeal.appealType) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="申诉原因">{{ selectedAppeal.reason }}</a-descriptions-item>
              <a-descriptions-item label="申诉内容">{{ selectedAppeal.content }}</a-descriptions-item>
              <a-descriptions-item label="申诉时间">{{ selectedAppeal.submitTime }}</a-descriptions-item>
              <a-descriptions-item label="当前状态">
                <a-tag :color="getStatusColor(selectedAppeal.status)">
                  {{ getStatusText(selectedAppeal.status) }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          
          <a-tab-pane key="materials" tab="申诉材料">
            <div class="materials-list">
              <div v-for="material in appealMaterials" :key="material.id" class="material-item">
                <div class="material-info">
                  <file-text-outlined />
                  <span class="name">{{ material.name }}</span>
                  <span class="size">{{ material.size }}</span>
                </div>
                <div class="material-actions">
                  <a-button type="link" size="small" @click="previewMaterial(material)">预览</a-button>
                  <a-button type="link" size="small" @click="downloadMaterial(material)">下载</a-button>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="history" tab="处理历史">
            <a-timeline>
              <a-timeline-item v-for="record in appealHistory" :key="record.id" :color="getHistoryColor(record.action)">
                <div class="history-item">
                  <div class="history-header">
                    <span class="action">{{ record.action }}</span>
                    <span class="time">{{ record.timestamp }}</span>
                  </div>
                  <div class="history-content">{{ record.content }}</div>
                  <div class="history-user">处理人：{{ record.processor }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SettingOutlined,
  ExportOutlined,
  ReloadOutlined,
  SearchOutlined,
  PlusOutlined,
  HistoryOutlined,
  DownloadOutlined,
  UserAddOutlined,
  DownOutlined,
  UploadOutlined,
  EditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface AppealRecord {
  id: string
  applicantName: string
  projectName: string
  appealType: 'score_dispute' | 'process_issue' | 'material_error' | 'other'
  reason: string
  content: string
  status: 'submitted' | 'processing' | 'accepted' | 'rejected' | 'completed'
  priority: 'high' | 'medium' | 'low'
  submitTime: string
  timeRemaining: number
  processor?: string
  processTime?: string
}

interface SearchParams {
  applicantName?: string
  status?: string
  appealType?: string
}

interface AppealForm {
  objectId?: string
  appealType?: string
  reason?: string
  content?: string
  attachments?: any[]
}

interface HandlingForm {
  result?: string
  comments?: string
  nextSteps?: string
}

// 响应式数据
const loading = ref(false)
const exporting = ref(false)
const submitting = ref(false)
const handling = ref(false)

const submitAppealModalVisible = ref(false)
const handleAppealModalVisible = ref(false)
const processConfigModalVisible = ref(false)
const detailModalVisible = ref(false)

const selectedAppeal = ref<AppealRecord | null>(null)
const searchParams = ref<SearchParams>({})
const appealForm = ref<AppealForm>({})
const handlingForm = ref<HandlingForm>({})

// 申诉列表数据
const appealList = ref<AppealRecord[]>([
  {
    id: '1',
    applicantName: '市委组织部',
    projectName: '党员服务中心数字化改革项目',
    appealType: 'score_dispute',
    reason: '评分标准不明确',
    content: '我们认为在创新程度评分上存在主观判断过重的问题，希望能够重新评估。',
    status: 'processing',
    priority: 'high',
    submitTime: '2025-01-06 10:30:00',
    timeRemaining: 5,
    processor: '张处理员'
  },
  {
    id: '2',
    applicantName: '市委宣传部',
    projectName: '智慧党建平台建设',
    appealType: 'process_issue',
    reason: '审核流程超时',
    content: '申报已超过规定的审核时限，但至今未收到审核结果。',
    status: 'submitted',
    priority: 'medium',
    submitTime: '2025-01-05 16:20:00',
    timeRemaining: 3
  }
])

const appealStats = computed(() => ({
  total: appealList.value.length,
  pending: appealList.value.filter(a => a.status === 'submitted').length,
  processing: appealList.value.filter(a => a.status === 'processing').length,
  completed: appealList.value.filter(a => ['accepted', 'rejected', 'completed'].includes(a.status)).length
}))

// 申诉流程步骤
const appealProcessSteps = ref([
  {
    name: '申诉提交',
    description: '申请人提交申诉材料',
    timeLimit: 1,
    processors: ['系统自动']
  },
  {
    name: '初步审查',
    description: '对申诉材料进行形式审查',
    timeLimit: 3,
    processors: ['初审员']
  },
  {
    name: '申诉处理',
    description: '详细审理申诉内容并作出决定',
    timeLimit: 7,
    processors: ['申诉处理员', '复审员']
  },
  {
    name: '结果反馈',
    description: '将处理结果反馈给申请人',
    timeLimit: 1,
    processors: ['系统自动']
  }
])

// 通知配置
const notificationSettings = reactive({
  onSubmit: true,
  onProgress: true,
  onComplete: true
})

// 申诉材料数据
const appealMaterials = ref([
  { id: '1', name: '申诉申请书.pdf', size: '2.3MB' },
  { id: '2', name: '补充证明材料.doc', size: '1.8MB' }
])

// 申诉历史数据
const appealHistory = ref([
  {
    id: '1',
    action: '提交申诉',
    content: '申请人提交了申诉申请',
    timestamp: '2025-01-06 10:30:00',
    processor: '市委组织部'
  },
  {
    id: '2',
    action: '分配处理人',
    content: '系统自动分配处理人：张处理员',
    timestamp: '2025-01-06 11:00:00',
    processor: '系统'
  }
])

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 2,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列定义
const columns = [
  {
    title: '申请信息',
    key: 'applicantInfo',
    width: 200,
    fixed: 'left'
  },
  {
    title: '申诉类型',
    key: 'appealType',
    width: 100
  },
  {
    title: '申诉状态',
    key: 'status',
    width: 100
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80
  },
  {
    title: '剩余时间',
    key: 'timeRemaining',
    width: 100
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 150,
    sorter: true
  },
  {
    title: '处理人',
    dataIndex: 'processor',
    key: 'processor',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 方法定义
const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    console.log('搜索参数:', searchParams.value)
    loading.value = false
  }, 1000)
}

const resetSearch = () => {
  searchParams.value = {}
  handleSearch()
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    message.success('数据刷新成功')
    loading.value = false
  }, 1000)
}

const exportAppealData = () => {
  exporting.value = true
  setTimeout(() => {
    message.success('申诉数据导出成功')
    exporting.value = false
  }, 2000)
}

const handleTableChange = (pag: any) => {
  pagination.value = { ...pagination.value, ...pag }
}

const showSubmitAppeal = () => {
  appealForm.value = {}
  submitAppealModalVisible.value = true
}

const submitAppeal = () => {
  if (!appealForm.value.objectId || !appealForm.value.appealType || !appealForm.value.reason) {
    message.warning('请填写完整的申诉信息')
    return
  }

  submitting.value = true
  setTimeout(() => {
    submitting.value = false
    submitAppealModalVisible.value = false
    message.success('申诉提交成功')
    // 刷新列表
    refreshData()
  }, 1500)
}

const cancelSubmitAppeal = () => {
  submitAppealModalVisible.value = false
  appealForm.value = {}
}

const viewAppealDetail = (record: AppealRecord) => {
  selectedAppeal.value = record
  detailModalVisible.value = true
}

const handleAppeal = (record: AppealRecord) => {
  if (!canHandle(record)) {
    message.warning('该申诉当前状态不能处理')
    return
  }
  selectedAppeal.value = record
  handlingForm.value = {}
  handleAppealModalVisible.value = true
}

const canHandle = (record: AppealRecord) => {
  return ['submitted', 'processing'].includes(record.status)
}

const saveAppealHandling = () => {
  if (!handlingForm.value.result || !handlingForm.value.comments) {
    message.warning('请填写完整的处理意见')
    return
  }

  handling.value = true
  setTimeout(() => {
    handling.value = false
    handleAppealModalVisible.value = false
    message.success('申诉处理完成')
    // 更新状态
    if (selectedAppeal.value) {
      selectedAppeal.value.status = 'completed'
    }
  }, 1500)
}

const cancelHandleAppeal = () => {
  handleAppealModalVisible.value = false
  handlingForm.value = {}
}

const showAppealProcessConfig = () => {
  processConfigModalVisible.value = true
}

const saveProcessConfig = () => {
  message.success('申诉流程配置保存成功')
  processConfigModalVisible.value = false
}

const cancelProcessConfig = () => {
  processConfigModalVisible.value = false
}

const beforeUpload = () => {
  return false // 阻止自动上传
}

// 样式计算方法
const getAppealTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    score_dispute: 'red',
    process_issue: 'orange',
    material_error: 'blue',
    other: 'gray'
  }
  return colorMap[type] || 'default'
}

const getAppealTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    score_dispute: '评分争议',
    process_issue: '流程问题',
    material_error: '材料错误',
    other: '其他'
  }
  return textMap[type] || '未知'
}

const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    submitted: 'orange',
    processing: 'blue',
    accepted: 'green',
    rejected: 'red',
    completed: 'cyan'
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    processing: '审理中',
    accepted: '已受理',
    rejected: '已驳回',
    completed: '已完成'
  }
  return textMap[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority] || '未知'
}

const getTimeRemainingClass = (days: number) => {
  if (days <= 1) return 'urgent'
  if (days <= 3) return 'warning'
  return 'normal'
}

const getHistoryColor = (action: string) => {
  const colorMap: Record<string, string> = {
    '提交申诉': 'blue',
    '分配处理人': 'cyan',
    '开始处理': 'orange',
    '申诉受理': 'green',
    '申诉驳回': 'red'
  }
  return colorMap[action] || 'gray'
}

// 其他方法
const viewAppealHistory = (record: AppealRecord) => {
  selectedAppeal.value = record
  // 这里可以加载特定申诉的历史记录
  detailModalVisible.value = true
}

const downloadAppealMaterials = (record: AppealRecord) => {
  message.info(`开始下载 ${record.applicantName} 的申诉材料`)
}

const assignProcessor = (record: AppealRecord) => {
  message.info(`为申诉 ${record.id} 分配处理人`)
}

const editProcessStep = (index: number) => {
  message.info(`编辑流程步骤 ${index + 1}`)
}

const previewMaterial = (material: any) => {
  message.info(`预览材料: ${material.name}`)
}

const downloadMaterial = (material: any) => {
  message.info(`下载材料: ${material.name}`)
}
</script>

<style scoped lang="scss">
.appeal-management-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.stats-section {
  margin-bottom: 24px;
}

.search-section {
  margin-bottom: 24px;
}

.list-section {
  .table-title {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .applicant-info {
    .name {
      font-weight: 600;
      color: #262626;
      margin-bottom: 4px;
    }

    .project {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .time-remaining {
    font-weight: 600;

    &.urgent {
      color: #f5222d;
    }

    &.warning {
      color: #faad14;
    }

    &.normal {
      color: #52c41a;
    }
  }
}

.appeal-info-section {
  margin-bottom: 24px;

  h4 {
    margin-bottom: 16px;
    color: #262626;
    font-weight: 600;
  }
}

.handling-form-section {
  h4 {
    margin-bottom: 16px;
    color: #262626;
    font-weight: 600;
  }
}

.process-config-section {
  h4 {
    margin: 0 0 16px 0;
    color: #262626;
    font-weight: 600;
  }

  .process-steps {
    margin-bottom: 32px;

    .step-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }

    .step-description {
      p {
        color: #595959;
        margin-bottom: 8px;
      }

      .step-details {
        display: flex;
        gap: 16px;
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }

  .notification-config {
    .config-desc {
      margin-left: 8px;
      color: #8c8c8c;
      font-size: 12px;
    }
  }
}

.appeal-detail {
  .materials-list {
    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      margin-bottom: 8px;

      .material-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .name {
          font-weight: 600;
          color: #262626;
        }

        .size {
          color: #8c8c8c;
          font-size: 12px;
        }
      }

      .material-actions {
        display: flex;
        gap: 8px;
      }
    }
  }

  .history-item {
    .history-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .action {
        font-weight: 600;
        color: #262626;
      }

      .time {
        color: #8c8c8c;
        font-size: 12px;
      }
    }

    .history-content {
      color: #595959;
      margin-bottom: 4px;
    }

    .history-user {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
}

.upload-tip {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .appeal-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .stats-section {
      .ant-col {
        margin-bottom: 12px;
      }
    }
  }
}
</style>