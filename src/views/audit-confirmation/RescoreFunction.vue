<template>
  <div class="rescore-function-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>再次评分管理</h2>
        <p>对因指标评分机制设置不合理而导致的评分结果不合理，可以调整评分机制后重新生成评分数据</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="viewRescoreHistory">
            <template #icon><history-outlined /></template>
            重评历史
          </a-button>
          <a-button @click="batchRescore" :loading="batchRescoring">
            <template #icon><thunderbolt-outlined /></template>
            批量重评
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <a-row :gutter="16">
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="待重评对象"
              :value="rescoreStats.pending"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="重评中"
              :value="rescoreStats.processing"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="已完成"
              :value="rescoreStats.completed"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :xs="12" :sm="6">
          <a-card>
            <a-statistic
              title="本月重评次数"
              :value="rescoreStats.monthlyCount"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-row :gutter="24">
      <!-- 左侧：评分机制调整 -->
      <a-col :xs="24" :lg="12">
        <a-card title="评分机制调整" class="scoring-config-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="resetScoringRules">
                <template #icon><redo-outlined /></template>
                重置规则
              </a-button>
              <a-button type="primary" size="small" @click="saveScoringRules" :loading="saving">
                <template #icon><save-outlined /></template>
                保存规则
              </a-button>
            </a-space>
          </template>

          <div class="scoring-rules-section">
            <h4>当前评分规则</h4>
            <div class="rules-list">
              <div v-for="(rule, index) in scoringRules" :key="rule.id" class="rule-item">
                <div class="rule-header">
                  <div class="rule-name">
                    <span class="indicator-name">{{ rule.indicatorName }}</span>
                    <a-tag :color="getRuleStatusColor(rule.status)" size="small">
                      {{ getRuleStatusText(rule.status) }}
                    </a-tag>
                  </div>
                  <div class="rule-actions">
                    <a-button type="text" size="small" @click="editRule(rule)">
                      <edit-outlined />
                    </a-button>
                    <a-switch 
                      v-model:checked="rule.enabled" 
                      size="small"
                      @change="onRuleEnabledChange(rule)"
                    />
                  </div>
                </div>
                
                <div class="rule-content">
                  <div class="rule-info">
                    <div class="info-item">
                      <span class="label">权重：</span>
                      <span class="value">{{ rule.weight }}%</span>
                    </div>
                    <div class="info-item">
                      <span class="label">评分方式：</span>
                      <span class="value">{{ getScoringMethodText(rule.scoringMethod) }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">最后修改：</span>
                      <span class="value">{{ rule.lastModified }}</span>
                    </div>
                  </div>
                  
                  <!-- 评分规则详情 -->
                  <div class="rule-details" v-if="rule.scoringMethod === 'range'">
                    <div class="range-rules">
                      <div v-for="(range, ridx) in rule.ranges" :key="ridx" class="range-item">
                        <span class="range-desc">{{ range.min }}-{{ range.max }}分：{{ range.score }}分</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="rule-details" v-else-if="rule.scoringMethod === 'formula'">
                    <div class="formula-rule">
                      <span class="formula">公式：{{ rule.formula }}</span>
                    </div>
                  </div>
                </div>

                <div class="rule-impact" v-if="rule.impactPreview">
                  <div class="impact-title">影响预览：</div>
                  <div class="impact-stats">
                    <span class="impact-item">
                      <arrow-up-outlined style="color: #52c41a;" />
                      评分上升：{{ rule.impactPreview.increase }} 个对象
                    </span>
                    <span class="impact-item">
                      <arrow-down-outlined style="color: #f5222d;" />
                      评分下降：{{ rule.impactPreview.decrease }} 个对象
                    </span>
                    <span class="impact-item">
                      <minus-outlined style="color: #8c8c8c;" />
                      无变化：{{ rule.impactPreview.unchanged }} 个对象
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加新规则 -->
            <a-button type="dashed" block @click="addNewRule">
              <plus-outlined />
              添加新的评分规则
            </a-button>
          </div>
        </a-card>
      </a-col>

      <!-- 右侧：重评对象列表 -->
      <a-col :xs="24" :lg="12">
        <a-card title="重评对象列表" class="rescore-objects-card">
          <template #extra>
            <a-space>
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索培育对象"
                style="width: 200px;"
                @search="searchObjects"
              />
              <a-button @click="selectAllObjects">
                全选
              </a-button>
            </a-space>
          </template>

          <div class="objects-list">
            <div v-for="object in rescoreObjects" :key="object.id" class="object-item">
              <div class="object-header">
                <div class="object-select">
                  <a-checkbox 
                    v-model:checked="object.selected"
                    @change="onObjectSelectionChange"
                  />
                </div>
                <div class="object-info">
                  <div class="object-name">{{ object.name }}</div>
                  <div class="object-meta">
                    <span class="applicant">{{ object.applicantName }}</span>
                    <a-tag :color="getObjectStatusColor(object.rescoreStatus)" size="small">
                      {{ getObjectStatusText(object.rescoreStatus) }}
                    </a-tag>
                  </div>
                </div>
                <div class="object-actions">
                  <a-button type="link" size="small" @click="viewObjectDetail(object)">
                    详情
                  </a-button>
                  <a-dropdown>
                    <a-button type="text" size="small">
                      <ellipsis-outlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item @click="rescoreObject(object)">
                          <thunderbolt-outlined />
                          单独重评
                        </a-menu-item>
                        <a-menu-item @click="viewRescoreHistory(object)">
                          <history-outlined />
                          重评历史
                        </a-menu-item>
                        <a-menu-item @click="compareScores(object)">
                          <diff-outlined />
                          评分对比
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>

              <div class="score-comparison">
                <div class="score-item old-score">
                  <div class="score-label">原评分</div>
                  <div class="score-value">{{ object.originalScore }}</div>
                </div>
                <div class="score-arrow">
                  <arrow-right-outlined />
                </div>
                <div class="score-item new-score">
                  <div class="score-label">预估新评分</div>
                  <div class="score-value" :class="getScoreChangeClass(object)">
                    {{ object.estimatedScore || '待计算' }}
                  </div>
                </div>
                <div class="score-change" v-if="object.estimatedScore">
                  <span :class="getScoreChangeClass(object)">
                    {{ getScoreChange(object) }}
                  </span>
                </div>
              </div>

              <div class="rescore-reason" v-if="object.rescoreReason">
                <div class="reason-label">重评原因：</div>
                <div class="reason-content">{{ object.rescoreReason }}</div>
              </div>

              <div class="last-rescore-time" v-if="object.lastRescoreTime">
                <span class="time-label">上次重评：</span>
                <span class="time-value">{{ object.lastRescoreTime }}</span>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <a-empty v-if="rescoreObjects.length === 0" description="暂无需要重评的对象" />
        </a-card>
      </a-col>
    </a-row>

    <!-- 批量重评确认弹窗 -->
    <a-modal
      v-model:open="batchRescoreModalVisible"
      title="批量重评确认"
      width="600px"
      @ok="confirmBatchRescore"
      @cancel="cancelBatchRescore"
      :confirm-loading="batchRescoring"
    >
      <div class="batch-rescore-content">
        <div class="selection-summary">
          <h4>重评对象摘要</h4>
          <div class="summary-stats">
            <div class="stat-item">
              <span class="label">选中对象：</span>
              <span class="value">{{ selectedObjectsCount }} 个</span>
            </div>
            <div class="stat-item">
              <span class="label">预计耗时：</span>
              <span class="value">{{ estimatedTime }} 分钟</span>
            </div>
          </div>
        </div>

        <div class="rescore-options">
          <h4>重评选项</h4>
          <a-form layout="vertical">
            <a-form-item label="重评模式">
              <a-radio-group v-model:value="rescoreOptions.mode">
                <a-radio value="incremental">增量重评（仅重评变更的指标）</a-radio>
                <a-radio value="full">完整重评（重新计算所有指标）</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <a-form-item label="重评后处理">
              <a-checkbox-group v-model:value="rescoreOptions.postActions">
                <a-checkbox value="notify_applicant">通知申请人</a-checkbox>
                <a-checkbox value="notify_reviewer">通知审核人员</a-checkbox>
                <a-checkbox value="update_ranking">更新排名</a-checkbox>
                <a-checkbox value="log_changes">记录变更日志</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            
            <a-form-item label="重评原因">
              <a-textarea
                v-model:value="rescoreOptions.reason"
                placeholder="请说明重评的原因..."
                :rows="3"
              />
            </a-form-item>
          </a-form>
        </div>

        <div class="selected-objects">
          <h4>选中的重评对象</h4>
          <div class="objects-preview">
            <div v-for="object in selectedObjects" :key="object.id" class="object-preview">
              <span class="name">{{ object.name }}</span>
              <span class="current-score">当前：{{ object.originalScore }}分</span>
              <span class="estimated-score" :class="getScoreChangeClass(object)">
                预估：{{ object.estimatedScore || '待计算' }}分
              </span>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 评分规则编辑弹窗 -->
    <a-modal
      v-model:open="ruleEditModalVisible"
      title="编辑评分规则"
      width="700px"
      @ok="saveRule"
      @cancel="cancelEditRule"
    >
      <div v-if="editingRule">
        <a-form ref="ruleFormRef" :model="editingRule" layout="vertical">
          <a-form-item label="指标名称" name="indicatorName" :rules="[{ required: true, message: '请输入指标名称' }]">
            <a-input v-model:value="editingRule.indicatorName" placeholder="请输入指标名称" />
          </a-form-item>
          
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="权重" name="weight" :rules="[{ required: true, message: '请输入权重' }]">
                <a-input-number
                  v-model:value="editingRule.weight"
                  :min="0"
                  :max="100"
                  addon-after="%"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="评分方式" name="scoringMethod" :rules="[{ required: true, message: '请选择评分方式' }]">
                <a-select v-model:value="editingRule.scoringMethod" placeholder="请选择评分方式">
                  <a-select-option value="direct">直接评分</a-select-option>
                  <a-select-option value="range">区间评分</a-select-option>
                  <a-select-option value="formula">公式计算</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <!-- 区间评分配置 -->
          <div v-if="editingRule.scoringMethod === 'range'" class="range-config">
            <a-form-item label="评分区间">
              <div class="ranges-list">
                <div v-for="(range, index) in editingRule.ranges" :key="index" class="range-config-item">
                  <a-input-number v-model:value="range.min" placeholder="最小值" style="width: 80px;" />
                  <span style="margin: 0 8px;">-</span>
                  <a-input-number v-model:value="range.max" placeholder="最大值" style="width: 80px;" />
                  <span style="margin: 0 8px;">→</span>
                  <a-input-number v-model:value="range.score" placeholder="得分" style="width: 80px;" />
                  <a-button type="text" danger @click="removeRange(index)" style="margin-left: 8px;">
                    <delete-outlined />
                  </a-button>
                </div>
              </div>
              <a-button type="dashed" @click="addRange" style="width: 100%; margin-top: 8px;">
                <plus-outlined />
                添加区间
              </a-button>
            </a-form-item>
          </div>
          
          <!-- 公式计算配置 -->
          <div v-if="editingRule.scoringMethod === 'formula'" class="formula-config">
            <a-form-item label="计算公式" name="formula">
              <a-textarea
                v-model:value="editingRule.formula"
                placeholder="请输入计算公式，支持基本数学运算符和函数"
                :rows="3"
              />
              <div class="formula-help">
                支持变量：value（原始值）、max（最大值）、min（最小值）<br>
                示例：(value / max) * 100
              </div>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-modal>

    <!-- 重评历史弹窗 -->
    <a-modal
      v-model:open="historyModalVisible"
      title="重评历史记录"
      width="800px"
      :footer="null"
    >
      <div class="rescore-history">
        <a-timeline>
          <a-timeline-item v-for="record in rescoreHistory" :key="record.id" :color="getHistoryColor(record.type)">
            <div class="history-item">
              <div class="history-header">
                <span class="history-type">{{ getHistoryTypeText(record.type) }}</span>
                <span class="history-time">{{ record.time }}</span>
              </div>
              <div class="history-content">{{ record.content }}</div>
              <div class="history-details" v-if="record.details">
                <div class="detail-item" v-for="(detail, key) in record.details" :key="key">
                  <span class="detail-label">{{ key }}：</span>
                  <span class="detail-value">{{ detail }}</span>
                </div>
              </div>
              <div class="history-user">操作人：{{ record.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  HistoryOutlined,
  ThunderboltOutlined,
  RedoOutlined,
  SaveOutlined,
  EditOutlined,
  PlusOutlined,
  EllipsisOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  ArrowRightOutlined,
  DeleteOutlined,
  DiffOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface ScoringRule {
  id: string
  indicatorName: string
  weight: number
  scoringMethod: 'direct' | 'range' | 'formula'
  ranges?: Array<{ min: number; max: number; score: number }>
  formula?: string
  status: 'active' | 'modified' | 'new'
  enabled: boolean
  lastModified: string
  impactPreview?: {
    increase: number
    decrease: number
    unchanged: number
  }
}

interface RescoreObject {
  id: string
  name: string
  applicantName: string
  originalScore: number
  estimatedScore?: number
  rescoreStatus: 'pending' | 'processing' | 'completed' | 'failed'
  rescoreReason?: string
  lastRescoreTime?: string
  selected: boolean
}

interface RescoreOptions {
  mode: 'incremental' | 'full'
  postActions: string[]
  reason: string
}

interface HistoryRecord {
  id: string
  type: 'rule_change' | 'batch_rescore' | 'single_rescore' | 'rollback'
  content: string
  time: string
  operator: string
  details?: Record<string, any>
}

// 响应式数据
const saving = ref(false)
const batchRescoring = ref(false)
const batchRescoreModalVisible = ref(false)
const ruleEditModalVisible = ref(false)
const historyModalVisible = ref(false)
const searchKeyword = ref('')

const editingRule = ref<ScoringRule | null>(null)

// 评分规则数据
const scoringRules = ref<ScoringRule[]>([
  {
    id: '1',
    indicatorName: '创新程度',
    weight: 30,
    scoringMethod: 'range',
    ranges: [
      { min: 90, max: 100, score: 100 },
      { min: 80, max: 89, score: 85 },
      { min: 70, max: 79, score: 70 },
      { min: 60, max: 69, score: 60 }
    ],
    status: 'modified',
    enabled: true,
    lastModified: '2025-01-06 10:30:00',
    impactPreview: {
      increase: 5,
      decrease: 2,
      unchanged: 8
    }
  },
  {
    id: '2',
    indicatorName: '实施效果',
    weight: 40,
    scoringMethod: 'formula',
    formula: '(value / 100) * 100',
    status: 'active',
    enabled: true,
    lastModified: '2025-01-05 16:20:00',
    impactPreview: {
      increase: 3,
      decrease: 4,
      unchanged: 8
    }
  },
  {
    id: '3',
    indicatorName: '可推广性',
    weight: 30,
    scoringMethod: 'direct',
    status: 'active',
    enabled: true,
    lastModified: '2025-01-04 09:15:00',
    impactPreview: {
      increase: 0,
      decrease: 0,
      unchanged: 15
    }
  }
])

// 重评对象数据
const rescoreObjects = ref<RescoreObject[]>([
  {
    id: '1',
    name: '党员服务中心数字化改革项目',
    applicantName: '市委组织部',
    originalScore: 85.5,
    estimatedScore: 88.2,
    rescoreStatus: 'pending',
    rescoreReason: '创新程度评分标准调整',
    selected: false
  },
  {
    id: '2',
    name: '智慧党建平台建设',
    applicantName: '市委宣传部',
    originalScore: 78.3,
    estimatedScore: 76.8,
    rescoreStatus: 'pending',
    rescoreReason: '实施效果评分公式优化',
    selected: false
  },
  {
    id: '3',
    name: '基层治理创新实践',
    applicantName: '市委政法委',
    originalScore: 92.1,
    estimatedScore: 91.5,
    rescoreStatus: 'completed',
    lastRescoreTime: '2025-01-05 14:30:00',
    selected: false
  }
])

// 重评选项
const rescoreOptions = reactive<RescoreOptions>({
  mode: 'incremental',
  postActions: ['notify_applicant', 'log_changes'],
  reason: ''
})

// 重评历史数据
const rescoreHistory = ref<HistoryRecord[]>([
  {
    id: '1',
    type: 'rule_change',
    content: '修改了创新程度指标的评分区间设置',
    time: '2025-01-06 10:30:00',
    operator: '张管理员',
    details: {
      '修改内容': '调整了80-89分区间的得分从80分提升到85分',
      '影响对象': '5个培育对象'
    }
  },
  {
    id: '2',
    type: 'batch_rescore',
    content: '执行批量重评操作',
    time: '2025-01-05 16:20:00',
    operator: '李管理员',
    details: {
      '重评对象': '15个培育对象',
      '重评模式': '完整重评',
      '耗时': '8分钟'
    }
  }
])

// 统计数据
const rescoreStats = computed(() => ({
  pending: rescoreObjects.value.filter(obj => obj.rescoreStatus === 'pending').length,
  processing: rescoreObjects.value.filter(obj => obj.rescoreStatus === 'processing').length,
  completed: rescoreObjects.value.filter(obj => obj.rescoreStatus === 'completed').length,
  monthlyCount: 25
}))

// 选中的对象
const selectedObjects = computed(() => 
  rescoreObjects.value.filter(obj => obj.selected)
)

const selectedObjectsCount = computed(() => selectedObjects.value.length)

const estimatedTime = computed(() => {
  const baseTime = selectedObjectsCount.value * 0.5 // 每个对象0.5分钟
  return Math.ceil(baseTime)
})

// 方法定义
const saveScoringRules = () => {
  saving.value = true
  setTimeout(() => {
    saving.value = false
    message.success('评分规则保存成功')
    // 重新计算预估评分
    calculateEstimatedScores()
  }, 1500)
}

const resetScoringRules = () => {
  // 重置到默认规则
  message.info('评分规则已重置为默认设置')
}

const addNewRule = () => {
  editingRule.value = {
    id: Date.now().toString(),
    indicatorName: '',
    weight: 10,
    scoringMethod: 'direct',
    status: 'new',
    enabled: true,
    lastModified: new Date().toLocaleString()
  }
  ruleEditModalVisible.value = true
}

const editRule = (rule: ScoringRule) => {
  editingRule.value = { ...rule }
  ruleEditModalVisible.value = true
}

const saveRule = () => {
  if (!editingRule.value?.indicatorName || !editingRule.value?.weight) {
    message.warning('请填写完整的规则信息')
    return
  }

  const existingIndex = scoringRules.value.findIndex(r => r.id === editingRule.value!.id)
  if (existingIndex !== -1) {
    scoringRules.value[existingIndex] = { ...editingRule.value, status: 'modified' }
  } else {
    scoringRules.value.push({ ...editingRule.value })
  }

  ruleEditModalVisible.value = false
  message.success('评分规则保存成功')
}

const cancelEditRule = () => {
  ruleEditModalVisible.value = false
  editingRule.value = null
}

const addRange = () => {
  if (!editingRule.value?.ranges) {
    editingRule.value!.ranges = []
  }
  editingRule.value.ranges.push({ min: 0, max: 100, score: 0 })
}

const removeRange = (index: number) => {
  editingRule.value?.ranges?.splice(index, 1)
}

const onRuleEnabledChange = (rule: ScoringRule) => {
  rule.status = 'modified'
  message.success(`规则 ${rule.indicatorName} 已${rule.enabled ? '启用' : '禁用'}`)
}

const selectAllObjects = () => {
  const allSelected = rescoreObjects.value.every(obj => obj.selected)
  rescoreObjects.value.forEach(obj => {
    obj.selected = !allSelected
  })
}

const onObjectSelectionChange = () => {
  // 对象选择变化的处理
}

const searchObjects = () => {
  // 搜索对象的实现
  message.info(`搜索关键词：${searchKeyword.value}`)
}

const batchRescore = () => {
  const selectedCount = selectedObjectsCount.value
  if (selectedCount === 0) {
    message.warning('请选择需要重评的对象')
    return
  }
  batchRescoreModalVisible.value = true
}

const confirmBatchRescore = () => {
  batchRescoring.value = true
  
  setTimeout(() => {
    batchRescoring.value = false
    batchRescoreModalVisible.value = false
    
    // 更新重评状态
    selectedObjects.value.forEach(obj => {
      obj.rescoreStatus = 'completed'
      obj.lastRescoreTime = new Date().toLocaleString()
      obj.selected = false
      
      // 模拟评分变化
      if (obj.estimatedScore) {
        obj.originalScore = obj.estimatedScore
      }
    })
    
    message.success(`成功完成 ${selectedObjectsCount.value} 个对象的重评`)
  }, 3000)
}

const cancelBatchRescore = () => {
  batchRescoreModalVisible.value = false
}

const rescoreObject = (object: RescoreObject) => {
  object.rescoreStatus = 'processing'
  
  setTimeout(() => {
    object.rescoreStatus = 'completed'
    object.lastRescoreTime = new Date().toLocaleString()
    if (object.estimatedScore) {
      object.originalScore = object.estimatedScore
    }
    message.success(`${object.name} 重评完成`)
  }, 2000)
}

const viewObjectDetail = (object: RescoreObject) => {
  message.info(`查看 ${object.name} 的详细信息`)
}

const viewRescoreHistory = (object?: RescoreObject) => {
  if (object) {
    message.info(`查看 ${object.name} 的重评历史`)
  }
  historyModalVisible.value = true
}

const compareScores = (object: RescoreObject) => {
  message.info(`对比 ${object.name} 的评分变化`)
}

const calculateEstimatedScores = () => {
  rescoreObjects.value.forEach(obj => {
    // 这里应该根据新的评分规则计算预估分数
    // 现在只是模拟一个随机变化
    const change = (Math.random() - 0.5) * 10
    obj.estimatedScore = Math.round((obj.originalScore + change) * 10) / 10
  })
}

// 样式计算方法
const getRuleStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    active: 'green',
    modified: 'orange',
    new: 'blue'
  }
  return colorMap[status] || 'default'
}

const getRuleStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '正常',
    modified: '已修改',
    new: '新增'
  }
  return textMap[status] || '未知'
}

const getScoringMethodText = (method: string) => {
  const textMap: Record<string, string> = {
    direct: '直接评分',
    range: '区间评分',
    formula: '公式计算'
  }
  return textMap[method] || '未知'
}

const getObjectStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

const getObjectStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待重评',
    processing: '重评中',
    completed: '已完成',
    failed: '失败'
  }
  return textMap[status] || '未知'
}

const getScoreChangeClass = (object: RescoreObject) => {
  if (!object.estimatedScore) return ''
  if (object.estimatedScore > object.originalScore) return 'score-increase'
  if (object.estimatedScore < object.originalScore) return 'score-decrease'
  return 'score-unchanged'
}

const getScoreChange = (object: RescoreObject) => {
  if (!object.estimatedScore) return ''
  const diff = object.estimatedScore - object.originalScore
  if (diff > 0) return `+${diff.toFixed(1)}`
  if (diff < 0) return diff.toFixed(1)
  return '0.0'
}

const getHistoryColor = (type: string) => {
  const colorMap: Record<string, string> = {
    rule_change: 'blue',
    batch_rescore: 'green',
    single_rescore: 'cyan',
    rollback: 'red'
  }
  return colorMap[type] || 'gray'
}

const getHistoryTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    rule_change: '规则变更',
    batch_rescore: '批量重评',
    single_rescore: '单独重评',
    rollback: '回滚操作'
  }
  return textMap[type] || '未知'
}

// 初始化预估评分
calculateEstimatedScores()
</script>

<style scoped lang="scss">
.rescore-function-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.stats-section {
  margin-bottom: 24px;
}

.scoring-config-card,
.rescore-objects-card {
  height: calc(100vh - 320px);
  overflow-y: auto;
}

.scoring-rules-section {
  .rules-list {
    margin-bottom: 24px;

    .rule-item {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      background: white;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      .rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .rule-name {
          display: flex;
          align-items: center;
          gap: 8px;

          .indicator-name {
            font-weight: 600;
            color: #262626;
          }
        }

        .rule-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .rule-content {
        .rule-info {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          margin-bottom: 12px;

          .info-item {
            display: flex;
            align-items: center;

            .label {
              color: #8c8c8c;
              font-size: 12px;
              margin-right: 4px;
            }

            .value {
              color: #262626;
              font-size: 12px;
              font-weight: 500;
            }
          }
        }

        .rule-details {
          margin-bottom: 12px;
          padding: 8px;
          background: #f9f9f9;
          border-radius: 4px;

          .range-rules {
            .range-item {
              margin-bottom: 4px;
              font-size: 12px;
              color: #595959;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .formula-rule {
            .formula {
              font-family: monospace;
              font-size: 12px;
              color: #1890ff;
            }
          }
        }
      }

      .rule-impact {
        border-top: 1px solid #f0f0f0;
        padding-top: 12px;

        .impact-title {
          font-size: 12px;
          color: #8c8c8c;
          margin-bottom: 8px;
        }

        .impact-stats {
          display: flex;
          gap: 16px;

          .impact-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #595959;
          }
        }
      }
    }
  }
}

.objects-list {
  .object-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background: white;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
    }

    .object-header {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 12px;

      .object-select {
        margin-top: 4px;
      }

      .object-info {
        flex: 1;

        .object-name {
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .object-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .applicant {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }

      .object-actions {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }

    .score-comparison {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 6px;

      .score-item {
        text-align: center;

        .score-label {
          font-size: 12px;
          color: #8c8c8c;
          margin-bottom: 4px;
        }

        .score-value {
          font-size: 16px;
          font-weight: 600;
          color: #262626;

          &.score-increase {
            color: #52c41a;
          }

          &.score-decrease {
            color: #f5222d;
          }

          &.score-unchanged {
            color: #1890ff;
          }
        }
      }

      .score-arrow {
        color: #8c8c8c;
        font-size: 16px;
      }

      .score-change {
        font-size: 12px;
        font-weight: 600;

        &.score-increase {
          color: #52c41a;
        }

        &.score-decrease {
          color: #f5222d;
        }

        &.score-unchanged {
          color: #8c8c8c;
        }
      }
    }

    .rescore-reason {
      margin-bottom: 8px;

      .reason-label {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .reason-content {
        font-size: 12px;
        color: #595959;
        line-height: 1.4;
      }
    }

    .last-rescore-time {
      font-size: 12px;

      .time-label {
        color: #8c8c8c;
      }

      .time-value {
        color: #262626;
        margin-left: 4px;
      }
    }
  }
}

.batch-rescore-content {
  .selection-summary,
  .rescore-options,
  .selected-objects {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      color: #262626;
      font-weight: 600;
    }
  }

  .summary-stats {
    display: flex;
    gap: 24px;

    .stat-item {
      .label {
        color: #8c8c8c;
        font-size: 14px;
      }

      .value {
        color: #262626;
        font-weight: 600;
        margin-left: 8px;
      }
    }
  }

  .objects-preview {
    max-height: 200px;
    overflow-y: auto;

    .object-preview {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      margin-bottom: 8px;
      background: #fafafa;

      .name {
        font-weight: 600;
        color: #262626;
        flex: 1;
      }

      .current-score,
      .estimated-score {
        font-size: 12px;
        margin-left: 12px;
      }

      .current-score {
        color: #8c8c8c;
      }

      .estimated-score {
        font-weight: 600;

        &.score-increase {
          color: #52c41a;
        }

        &.score-decrease {
          color: #f5222d;
        }

        &.score-unchanged {
          color: #1890ff;
        }
      }
    }
  }
}

.range-config-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.formula-help {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.rescore-history {
  .history-item {
    .history-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .history-type {
        font-weight: 600;
        color: #262626;
      }

      .history-time {
        color: #8c8c8c;
        font-size: 12px;
      }
    }

    .history-content {
      color: #595959;
      margin-bottom: 8px;
    }

    .history-details {
      margin-bottom: 8px;
      padding: 8px;
      background: #f9f9f9;
      border-radius: 4px;

      .detail-item {
        display: flex;
        margin-bottom: 4px;
        font-size: 12px;

        .detail-label {
          color: #8c8c8c;
          min-width: 80px;
        }

        .detail-value {
          color: #262626;
          flex: 1;
        }
      }
    }

    .history-user {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .rescore-function-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .stats-section {
      .ant-col {
        margin-bottom: 12px;
      }
    }

    .ant-col {
      margin-bottom: 24px;
    }

    .scoring-config-card,
    .rescore-objects-card {
      height: auto;
      min-height: 400px;
    }

    .score-comparison {
      flex-direction: column !important;
      align-items: stretch !important;

      .score-arrow {
        transform: rotate(90deg);
        align-self: center;
      }
    }
  }
}
</style>