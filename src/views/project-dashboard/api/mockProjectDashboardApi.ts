// 项目看板模拟API服务
import { ref } from 'vue'

// 模拟延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟项目数据
const mockProjects = [
  {
    id: 1,
    name: '政治要件闭环落实情况',
    stage: 'selection',
    status: 'inProgress',
    department: '渝中区政府办',
    startDate: '2024-01-15',
    endDate: '2024-06-30',
    progress: 75,
    description: '确保政治要件闭环落实，提升政治执行力',
    applyingUnits: 15,
    cultivationUnits: 8,
    benchmarkUnits: 3,
    milestones: [
      { id: 1, name: '需求分析', description: '完成需求调研和分析', date: '2024-02-01', isCompleted: true },
      { id: 2, name: '方案设计', description: '制定实施方案', date: '2024-03-01', isCompleted: true },
      { id: 3, name: '试点实施', description: '在试点单位实施', date: '2024-04-15', isCompleted: false }
    ]
  },
  {
    id: 2,
    name: '重大事项请示报告上报情况',
    stage: 'cultivation',
    status: 'inProgress',
    department: '江北区政府办',
    startDate: '2024-02-01',
    endDate: '2024-07-31',
    progress: 60,
    description: '规范重大事项请示报告制度，提升决策科学性',
    applyingUnits: 12,
    cultivationUnits: 6,
    benchmarkUnits: 2,
    milestones: [
      { id: 4, name: '制度梳理', description: '梳理现有制度', date: '2024-02-15', isCompleted: true },
      { id: 5, name: '流程优化', description: '优化报告流程', date: '2024-03-30', isCompleted: true },
      { id: 6, name: '系统建设', description: '建设信息系统', date: '2024-05-30', isCompleted: false }
    ]
  },
  {
    id: 3,
    name: '党内法规制度建设上报情况',
    stage: 'establishment',
    status: 'completed',
    department: '南岸区政府办',
    startDate: '2024-01-01',
    endDate: '2024-05-31',
    progress: 100,
    description: '完善党内法规制度体系，提升制度执行力',
    applyingUnits: 18,
    cultivationUnits: 10,
    benchmarkUnits: 5,
    milestones: [
      { id: 7, name: '制度调研', description: '调研制度建设现状', date: '2024-01-15', isCompleted: true },
      { id: 8, name: '制度完善', description: '完善制度体系', date: '2024-03-15', isCompleted: true },
      { id: 9, name: '培训推广', description: '开展培训推广', date: '2024-05-15', isCompleted: true }
    ]
  },
  {
    id: 4,
    name: '问题管控考核结果',
    stage: 'promotion',
    status: 'inProgress',
    department: '九龙坡区政府办',
    startDate: '2024-03-01',
    endDate: '2024-08-31',
    progress: 45,
    description: '建立问题管控考核机制，提升问题解决效率',
    applyingUnits: 14,
    cultivationUnits: 7,
    benchmarkUnits: 3,
    milestones: [
      { id: 10, name: '考核体系设计', description: '设计考核指标体系', date: '2024-03-15', isCompleted: true },
      { id: 11, name: '试点考核', description: '开展试点考核', date: '2024-05-15', isCompleted: false },
      { id: 12, name: '全面推广', description: '全面推广考核机制', date: '2024-07-31', isCompleted: false }
    ]
  }
]

// 模拟指标数据
const mockIndicators = [
  { id: 1, name: '政治要件闭环落实情况', source: '市级部署', completedUnits: 15, completionRate: 75, details: '党建类指标，确保政治要件闭环落实' },
  { id: 2, name: '重大事项请示报告上报情况', source: '市级部署', completedUnits: 12, completionRate: 60, details: '党建类指标，规范重大事项请示报告' },
  { id: 3, name: '党内法规制度建设上报情况', source: '市级部署', completedUnits: 18, completionRate: 90, details: '党建类指标，完善党内法规制度' },
  { id: 4, name: '问题管控考核结果', source: '市级部署', completedUnits: 14, completionRate: 70, details: '党建类指标，建立问题管控机制' },
  { id: 5, name: '组织生活纪实完成情况', source: '市级部署', completedUnits: 16, completionRate: 80, details: '党建类指标，规范组织生活' },
  { id: 6, name: '职能绩效考核结果', source: '区级制定', completedUnits: 10, completionRate: 50, details: '绩效考核类指标' },
  { id: 7, name: '常态化"三服务"机制考核结果', source: '区级制定', completedUnits: 8, completionRate: 40, details: '绩效考核类指标' },
  { id: 8, name: '政务效能考核结果', source: '区级制定', completedUnits: 12, completionRate: 60, details: '绩效考核类指标' },
  { id: 9, name: '"三个一批"改革项目推进情况', source: '市级部署', completedUnits: 9, completionRate: 45, details: '改革创新类指标' },
  { id: 10, name: '最佳实践案例情况', source: '市级部署', completedUnits: 11, completionRate: 55, details: '改革创新类指标' }
]

// 模拟时间线数据
const mockTimelineData = {
  totalProjects: 48,
  activeProjects: 32,
  upcomingDeadlines: 8,
  delayedProjects: 3,
  projects: mockProjects.map(project => ({
    ...project,
    isDelayed: project.progress < 50 && new Date(project.endDate) < new Date(),
    isCritical: project.progress < 30
  }))
}

// 模拟单位完成情况数据
const mockUnitCompletionData = [
  { id: 1, unitName: '渝中区政府办', isCompleted: true, score: 95 },
  { id: 2, unitName: '江北区政府办', isCompleted: true, score: 88 },
  { id: 3, unitName: '南岸区政府办', isCompleted: false, score: 65 },
  { id: 4, unitName: '九龙坡区政府办', isCompleted: true, score: 92 },
  { id: 5, unitName: '沙坪坝区政府办', isCompleted: false, score: 60 },
  { id: 6, unitName: '大渡口区政府办', isCompleted: true, score: 85 },
  { id: 7, unitName: '巴南区政府办', isCompleted: true, score: 78 },
  { id: 8, unitName: '北碚区政府办', isCompleted: false, score: 55 }
]

// API接口类
class MockProjectDashboardApi {
  // 获取项目列表
  async getProjectList(params: { stage?: string; district?: string; page?: number; pageSize?: number }) {
    await delay(500) // 模拟网络延迟
    
    let filteredProjects = [...mockProjects]
    
    // 按阶段过滤
    if (params.stage) {
      filteredProjects = filteredProjects.filter(p => p.stage === params.stage)
    }
    
    // 按区县过滤
    if (params.district) {
      filteredProjects = filteredProjects.filter(p => p.department.includes(params.district))
    }
    
    // 分页处理
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      code: 200,
      message: '获取成功',
      data: filteredProjects.slice(start, end),
      total: filteredProjects.length
    }
  }

  // 获取项目详情
  async getProjectDetail(id: number) {
    await delay(300)
    
    const project = mockProjects.find(p => p.id === id)
    if (!project) {
      return {
        code: 404,
        message: '项目不存在',
        data: null
      }
    }
    
    return {
      code: 200,
      message: '获取成功',
      data: project
    }
  }

  // 获取阶段统计数据
  async getStageStatistics() {
    await delay(400)
    
    const stages = ['selection', 'cultivation', 'establishment', 'promotion']
    const statistics = stages.map(stage => {
      const stageProjects = mockProjects.filter(p => p.stage === stage)
      const departments = new Set(stageProjects.map(p => p.department))
      const completedProjects = stageProjects.filter(p => p.status === 'completed')
      
      return {
        stage,
        projectCount: stageProjects.length,
        unitCount: departments.size,
        completionRate: stageProjects.length > 0 
          ? Math.round((completedProjects.length / stageProjects.length) * 100) 
          : 0
      }
    })
    
    return {
      code: 200,
      message: '获取成功',
      data: statistics
    }
  }

  // 获取指标列表
  async getIndicatorsList(params: { page?: number; pageSize?: number }) {
    await delay(600)
    
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        total: 48,
        dataTotal: 256,
        list: mockIndicators.slice(start, end)
      }
    }
  }

  // 获取指标详情
  async getIndicatorDetail(id: number) {
    await delay(300)
    
    const indicator = mockIndicators.find(i => i.id === id)
    if (!indicator) {
      return {
        code: 404,
        message: '指标不存在',
        data: null
      }
    }
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        ...indicator,
        unitCompletions: mockUnitCompletionData
      }
    }
  }

  // 获取时间线数据
  async getTimelineData(projectId?: number) {
    await delay(800)
    
    return {
      code: 200,
      message: '获取成功',
      data: mockTimelineData
    }
  }

  // 创建项目
  async createProject(projectData: any) {
    await delay(1000)
    
    const newProject = {
      id: mockProjects.length + 1,
      ...projectData,
      status: 'pending',
      progress: 0,
      milestones: []
    }
    
    mockProjects.push(newProject)
    
    return {
      code: 200,
      message: '创建成功',
      data: newProject
    }
  }

  // 更新项目
  async updateProject(id: number, projectData: any) {
    await delay(800)
    
    const index = mockProjects.findIndex(p => p.id === id)
    if (index === -1) {
      return {
        code: 404,
        message: '项目不存在',
        data: null
      }
    }
    
    mockProjects[index] = { ...mockProjects[index], ...projectData }
    
    return {
      code: 200,
      message: '更新成功',
      data: mockProjects[index]
    }
  }

  // 删除项目
  async deleteProject(id: number) {
    await delay(500)
    
    const index = mockProjects.findIndex(p => p.id === id)
    if (index === -1) {
      return {
        code: 404,
        message: '项目不存在',
        data: null
      }
    }
    
    mockProjects.splice(index, 1)
    
    return {
      code: 200,
      message: '删除成功',
      data: null
    }
  }

  // 导出数据
  async exportData(type: 'pdf' | 'excel', params: any) {
    await delay(2000) // 模拟导出处理时间
    
    return {
      code: 200,
      message: '导出成功',
      data: {
        downloadUrl: `https://example.com/export/${type}/${Date.now()}.${type}`,
        fileName: `项目看板数据_${new Date().toLocaleDateString()}.${type}`
      }
    }
  }

  // 获取竞赛数据
  async getCompetitionData(params: { stage?: string; district?: string }) {
    await delay(600)
    
    const mockCompetitionData = [
      { id: 1, name: '政务服务效能提升竞赛', participants: 25, status: 'active', startDate: '2024-01-01', endDate: '2024-06-30' },
      { id: 2, name: '党建工作创新竞赛', participants: 18, status: 'completed', startDate: '2024-02-01', endDate: '2024-05-31' },
      { id: 3, name: '数字化转型竞赛', participants: 32, status: 'active', startDate: '2024-03-01', endDate: '2024-08-31' }
    ]
    
    return {
      code: 200,
      message: '获取成功',
      data: mockCompetitionData
    }
  }

  // 获取动态数据
  async getDynamicsData(params: { stage?: string; district?: string }) {
    await delay(500)
    
    const mockDynamicsData = [
      { id: 1, title: '渝中区政务服务改革取得新突破', content: '通过数字化转型，政务服务效率提升30%', publishTime: '2024-03-15', type: 'news' },
      { id: 2, title: '江北区党建工作创新实践', content: '探索"互联网+党建"新模式，获得市级表彰', publishTime: '2024-03-10', type: 'achievement' },
      { id: 3, title: '南岸区制度建设成效显著', content: '完善内控制度体系，风险防控能力大幅提升', publishTime: '2024-03-08', type: 'progress' }
    ]
    
    return {
      code: 200,
      message: '获取成功',
      data: mockDynamicsData
    }
  }

  // 获取成果数据
  async getAchievementsData(params: { stage?: string; district?: string }) {
    await delay(700)
    
    const mockAchievementsData = [
      { id: 1, title: '政务服务"一网通办"', description: '实现90%以上事项网上办理', level: 'national', awardTime: '2024-02-20' },
      { id: 2, title: '党建工作数字化平台', description: '创新党建工作管理模式', level: 'municipal', awardTime: '2024-01-15' },
      { id: 3, title: '制度建设标准化体系', description: '建立完善的制度管理体系', level: 'district', awardTime: '2024-03-01' }
    ]
    
    return {
      code: 200,
      message: '获取成功',
      data: mockAchievementsData
    }
  }

  // 获取督办跟踪数据
  async getSupervisionData(params: { stage?: string; district?: string }) {
    await delay(600)
    
    const mockSupervisionData = [
      { id: 1, taskName: '政务服务改革推进', responsible: '渝中区政府办', deadline: '2024-04-30', status: 'inProgress', progress: 75 },
      { id: 2, taskName: '党建制度完善', responsible: '江北区政府办', deadline: '2024-03-31', status: 'completed', progress: 100 },
      { id: 3, taskName: '数字化转型实施', responsible: '南岸区政府办', deadline: '2024-05-31', status: 'delayed', progress: 45 }
    ]
    
    return {
      code: 200,
      message: '获取成功',
      data: mockSupervisionData
    }
  }
}

// 导出API实例
export const mockProjectDashboardApi = new MockProjectDashboardApi()
export default mockProjectDashboardApi