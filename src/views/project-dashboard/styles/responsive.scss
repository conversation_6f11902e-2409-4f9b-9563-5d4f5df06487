// 选育树推项目看板 - 响应式样式
// Source: 基于选育树推项目看板.md需求文档生成

// 断点定义
$breakpoints: (
    xs: 480px,
    sm: 576px,
    md: 768px,
    lg: 992px,
    xl: 1200px,
    xxl: 1600px
);

// 媒体查询混合器
@mixin respond-to($breakpoint) {
    @if map-has-key($breakpoints, $breakpoint) {
        @media (min-width: map-get($breakpoints, $breakpoint)) {
            @content;
        }
    }

    @else {
        @warn "Unknown breakpoint: #{$breakpoint}";
    }
}

@mixin respond-below($breakpoint) {
    @if map-has-key($breakpoints, $breakpoint) {
        @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
            @content;
        }
    }

    @else {
        @warn "Unknown breakpoint: #{$breakpoint}";
    }
}

// 容器响应式
.container-responsive {
    width: 100%;
    margin: 0 auto;
    padding: 0 16px;

    @include respond-to(sm) {
        max-width: 540px;
    }

    @include respond-to(md) {
        max-width: 720px;
    }

    @include respond-to(lg) {
        max-width: 960px;
        padding: 0 24px;
    }

    @include respond-to(xl) {
        max-width: 1140px;
    }

    @include respond-to(xxl) {
        max-width: 1600px;
    }
}

// 网格系统响应式
.grid-responsive {
    display: grid;
    gap: 16px;

    // 默认单列
    grid-template-columns: 1fr;

    // 小屏幕及以上：2列
    @include respond-to(sm) {
        grid-template-columns: repeat(2, 1fr);
    }

    // 中等屏幕及以上：3列
    @include respond-to(md) {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    // 大屏幕及以上：4列
    @include respond-to(lg) {
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
    }

    // 超大屏幕：6列
    @include respond-to(xxl) {
        grid-template-columns: repeat(6, 1fr);
    }
}

// 文字响应式
.text-responsive {
    font-size: 14px;
    line-height: 1.5;

    @include respond-to(sm) {
        font-size: 15px;
    }

    @include respond-to(md) {
        font-size: 16px;
        line-height: 1.6;
    }

    @include respond-to(lg) {
        font-size: 16px;
        line-height: 1.7;
    }
}

// 标题响应式
.title-responsive {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.3;

    @include respond-to(sm) {
        font-size: 20px;
    }

    @include respond-to(md) {
        font-size: 22px;
    }

    @include respond-to(lg) {
        font-size: 24px;
        line-height: 1.4;
    }

    @include respond-to(xl) {
        font-size: 26px;
    }
}

// 间距响应式
.spacing-responsive {
    padding: 12px;

    @include respond-to(sm) {
        padding: 16px;
    }

    @include respond-to(md) {
        padding: 20px;
    }

    @include respond-to(lg) {
        padding: 24px;
    }

    @include respond-to(xl) {
        padding: 32px;
    }
}

// 卡片响应式
.card-responsive {
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    @include respond-to(md) {
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    @include respond-to(lg) {
        border-radius: 10px;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }
}

// 按钮响应式
.button-responsive {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;

    @include respond-to(sm) {
        padding: 10px 20px;
    }

    @include respond-to(md) {
        padding: 12px 24px;
        font-size: 15px;
        border-radius: 6px;
    }

    @include respond-to(lg) {
        padding: 14px 28px;
        font-size: 16px;
        border-radius: 8px;
    }
}

// 表格响应式
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    table {
        min-width: 600px;

        @include respond-to(md) {
            min-width: 800px;
        }

        @include respond-to(lg) {
            min-width: 1000px;
        }
    }

    // 移动端表格样式调整
    @include respond-below(md) {
        table {
            font-size: 12px;

            th,
            td {
                padding: 8px 4px;
                white-space: nowrap;
            }
        }
    }
}

// 导航响应式
.nav-responsive {
    display: flex;
    flex-direction: column;
    gap: 8px;

    @include respond-to(md) {
        flex-direction: row;
        gap: 16px;
    }

    @include respond-to(lg) {
        gap: 24px;
    }
}

// 侧边栏响应式
.sidebar-responsive {
    width: 100%;
    position: static;

    @include respond-to(lg) {
        width: 280px;
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        overflow-y: auto;
    }

    @include respond-to(xl) {
        width: 320px;
    }
}

// 主内容区响应式
.main-content-responsive {
    width: 100%;
    padding: 16px;

    @include respond-to(lg) {
        margin-left: 280px;
        padding: 24px;
    }

    @include respond-to(xl) {
        margin-left: 320px;
        padding: 32px;
    }
}

// 图表响应式
.chart-responsive {
    width: 100%;
    height: 200px;

    @include respond-to(sm) {
        height: 250px;
    }

    @include respond-to(md) {
        height: 300px;
    }

    @include respond-to(lg) {
        height: 350px;
    }

    @include respond-to(xl) {
        height: 400px;
    }
}

// 模态框响应式
.modal-responsive {
    .ant-modal {
        width: 90% !important;
        max-width: 520px;

        @include respond-to(sm) {
            width: 80% !important;
        }

        @include respond-to(md) {
            width: 70% !important;
            max-width: 720px;
        }

        @include respond-to(lg) {
            width: 60% !important;
            max-width: 920px;
        }

        @include respond-to(xl) {
            width: 50% !important;
            max-width: 1200px;
        }
    }
}

// 表单响应式
.form-responsive {
    .ant-form-item {
        margin-bottom: 16px;

        @include respond-to(md) {
            margin-bottom: 20px;
        }

        @include respond-to(lg) {
            margin-bottom: 24px;
        }
    }

    .ant-form-item-label {
        font-size: 14px;

        @include respond-to(md) {
            font-size: 15px;
        }

        @include respond-to(lg) {
            font-size: 16px;
        }
    }
}

// 工具类 - 显示/隐藏
.show-xs {
    display: block;

    @include respond-to(sm) {
        display: none;
    }
}

.show-sm {
    display: none;

    @include respond-to(sm) {
        display: block;
    }

    @include respond-to(md) {
        display: none;
    }
}

.show-md {
    display: none;

    @include respond-to(md) {
        display: block;
    }

    @include respond-to(lg) {
        display: none;
    }
}

.show-lg {
    display: none;

    @include respond-to(lg) {
        display: block;
    }

    @include respond-to(xl) {
        display: none;
    }
}

.show-xl {
    display: none;

    @include respond-to(xl) {
        display: block;
    }
}

.hide-xs {
    display: none;

    @include respond-to(sm) {
        display: block;
    }
}

.hide-sm {
    display: block;

    @include respond-below(sm) {
        display: none;
    }

    @include respond-to(md) {
        display: block;
    }
}

.hide-md {
    display: block;

    @include respond-below(md) {
        display: none;
    }

    @include respond-to(lg) {
        display: block;
    }
}

.hide-lg {
    display: block;

    @include respond-below(lg) {
        display: none;
    }

    @include respond-to(xl) {
        display: block;
    }
}

.hide-xl {
    display: block;

    @include respond-below(xl) {
        display: none;
    }
}

// 打印样式
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    // 打印时的基本样式调整
    * {
        color: #000 !important;
        background: #fff !important;
        box-shadow: none !important;
    }

    .container-responsive {
        max-width: none !important;
        padding: 0 !important;
    }

    .card-responsive {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}