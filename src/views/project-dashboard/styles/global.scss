// 选育树推项目看板 - 全局样式
// Source: 基于选育树推项目看板.md需求文档生成

// 颜色变量
:root {
    // 主题色
    --primary-color: #1890ff;
    --primary-light: #40a9ff;
    --primary-dark: #096dd9;

    // 功能色
    --success-color: #52c41a;
    --warning-color: #faad14;
    --error-color: #ff4d4f;
    --info-color: #1890ff;

    // 中性色
    --text-primary: #262626;
    --text-secondary: #595959;
    --text-tertiary: #8c8c8c;
    --text-disabled: #bfbfbf;

    // 背景色
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f5f5f5;
    --bg-disabled: #f5f5f5;

    // 边框色
    --border-color: #d9d9d9;
    --border-light: #f0f0f0;

    // 阴影
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.12);

    // 圆角
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;

    // 间距
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

// 全局重置
* {
    box-sizing: border-box;
}

// 项目看板容器
.project-dashboard {
    min-height: 100vh;
    background: var(--bg-secondary);

    // 页面标题
    &__header {
        background: var(--bg-primary);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--spacing-lg);

        h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .subtitle {
            margin-top: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 14px;
        }
    }

    // 内容区域
    &__content {
        padding: 0 var(--spacing-lg) var(--spacing-lg);
    }

    // 网格布局
    &__grid {
        display: grid;
        gap: var(--spacing-lg);

        &--2-cols {
            grid-template-columns: 1fr 1fr;
        }

        &--3-cols {
            grid-template-columns: repeat(3, 1fr);
        }

        &--4-cols {
            grid-template-columns: repeat(4, 1fr);
        }

        // 响应式
        @media (max-width: 1200px) {
            &--4-cols {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {

            &--2-cols,
            &--3-cols,
            &--4-cols {
                grid-template-columns: 1fr;
            }
        }
    }
}

// 卡片组件
.dashboard-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: var(--shadow-medium);
    }

    &__header {
        padding: var(--spacing-md) var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;

        .title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .extra {
            color: var(--text-secondary);
            font-size: 14px;
        }
    }

    &__body {
        padding: var(--spacing-lg);
    }

    &__footer {
        padding: var(--spacing-md) var(--spacing-lg);
        border-top: 1px solid var(--border-light);
        background: var(--bg-tertiary);
    }
}

// 统计数字
.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;

    &--primary {
        color: var(--primary-color);
    }

    &--success {
        color: var(--success-color);
    }

    &--warning {
        color: var(--warning-color);
    }

    &--error {
        color: var(--error-color);
    }
}

// 趋势指示器
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;

    &--up {
        color: var(--success-color);
    }

    &--down {
        color: var(--error-color);
    }

    &--stable {
        color: var(--text-secondary);
    }
}

// 进度条
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    overflow: hidden;

    &__fill {
        height: 100%;
        background: var(--primary-color);
        transition: width 0.3s ease;

        &--success {
            background: var(--success-color);
        }

        &--warning {
            background: var(--warning-color);
        }

        &--error {
            background: var(--error-color);
        }
    }
}

// 标签
.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 500;

    &--primary {
        background: rgba(24, 144, 255, 0.1);
        color: var(--primary-color);
    }

    &--success {
        background: rgba(82, 196, 26, 0.1);
        color: var(--success-color);
    }

    &--warning {
        background: rgba(250, 173, 20, 0.1);
        color: var(--warning-color);
    }

    &--error {
        background: rgba(255, 77, 79, 0.1);
        color: var(--error-color);
    }

    &--default {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
    }
}

// 状态指示器
.status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &--active {
        background: var(--success-color);
    }

    &--inactive {
        background: var(--text-disabled);
    }

    &--warning {
        background: var(--warning-color);
    }

    &--error {
        background: var(--error-color);
    }
}

// 加载状态
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

// 空状态
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);

    &__icon {
        font-size: 48px;
        margin-bottom: var(--spacing-md);
        opacity: 0.5;
    }

    &__text {
        font-size: 14px;
    }
}

// 工具提示
.tooltip {
    position: relative;
    cursor: help;

    &:hover::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-sm);
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
    }
}

// 响应式工具类
.hidden-mobile {
    @media (max-width: 768px) {
        display: none !important;
    }
}

.hidden-desktop {
    @media (min-width: 769px) {
        display: none !important;
    }
}

// 文本工具类
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// 间距工具类
.mt-0 {
    margin-top: 0 !important;
}

.mt-1 {
    margin-top: var(--spacing-xs) !important;
}

.mt-2 {
    margin-top: var(--spacing-sm) !important;
}

.mt-3 {
    margin-top: var(--spacing-md) !important;
}

.mt-4 {
    margin-top: var(--spacing-lg) !important;
}

.mt-5 {
    margin-top: var(--spacing-xl) !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}

.mb-1 {
    margin-bottom: var(--spacing-xs) !important;
}

.mb-2 {
    margin-bottom: var(--spacing-sm) !important;
}

.mb-3 {
    margin-bottom: var(--spacing-md) !important;
}

.mb-4 {
    margin-bottom: var(--spacing-lg) !important;
}

.mb-5 {
    margin-bottom: var(--spacing-xl) !important;
}

.ml-0 {
    margin-left: 0 !important;
}

.ml-1 {
    margin-left: var(--spacing-xs) !important;
}

.ml-2 {
    margin-left: var(--spacing-sm) !important;
}

.ml-3 {
    margin-left: var(--spacing-md) !important;
}

.ml-4 {
    margin-left: var(--spacing-lg) !important;
}

.ml-5 {
    margin-left: var(--spacing-xl) !important;
}

.mr-0 {
    margin-right: 0 !important;
}

.mr-1 {
    margin-right: var(--spacing-xs) !important;
}

.mr-2 {
    margin-right: var(--spacing-sm) !important;
}

.mr-3 {
    margin-right: var(--spacing-md) !important;
}

.mr-4 {
    margin-right: var(--spacing-lg) !important;
}

.mr-5 {
    margin-right: var(--spacing-xl) !important;
}

// 动画
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

// 滚动条样式
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}