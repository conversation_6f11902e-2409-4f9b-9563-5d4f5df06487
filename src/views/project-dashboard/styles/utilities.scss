// 选育树推项目看板 - 工具样式
// Source: 基于选育树推项目看板.md需求文档生成

// 布局工具类
.d-flex {
    display: flex !important;
}

.d-inline-flex {
    display: inline-flex !important;
}

.d-block {
    display: block !important;
}

.d-inline-block {
    display: inline-block !important;
}

.d-none {
    display: none !important;
}

.d-grid {
    display: grid !important;
}

// Flex 方向
.flex-row {
    flex-direction: row !important;
}

.flex-column {
    flex-direction: column !important;
}

.flex-row-reverse {
    flex-direction: row-reverse !important;
}

.flex-column-reverse {
    flex-direction: column-reverse !important;
}

// Flex 换行
.flex-wrap {
    flex-wrap: wrap !important;
}

.flex-nowrap {
    flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
    flex-wrap: wrap-reverse !important;
}

// Justify content
.justify-content-start {
    justify-content: flex-start !important;
}

.justify-content-end {
    justify-content: flex-end !important;
}

.justify-content-center {
    justify-content: center !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.justify-content-around {
    justify-content: space-around !important;
}

.justify-content-evenly {
    justify-content: space-evenly !important;
}

// Align items
.align-items-start {
    align-items: flex-start !important;
}

.align-items-end {
    align-items: flex-end !important;
}

.align-items-center {
    align-items: center !important;
}

.align-items-baseline {
    align-items: baseline !important;
}

.align-items-stretch {
    align-items: stretch !important;
}

// Align self
.align-self-start {
    align-self: flex-start !important;
}

.align-self-end {
    align-self: flex-end !important;
}

.align-self-center {
    align-self: center !important;
}

.align-self-baseline {
    align-self: baseline !important;
}

.align-self-stretch {
    align-self: stretch !important;
}

// Flex grow/shrink
.flex-fill {
    flex: 1 1 auto !important;
}

.flex-grow-0 {
    flex-grow: 0 !important;
}

.flex-grow-1 {
    flex-grow: 1 !important;
}

.flex-shrink-0 {
    flex-shrink: 0 !important;
}

.flex-shrink-1 {
    flex-shrink: 1 !important;
}

// 位置工具类
.position-static {
    position: static !important;
}

.position-relative {
    position: relative !important;
}

.position-absolute {
    position: absolute !important;
}

.position-fixed {
    position: fixed !important;
}

.position-sticky {
    position: sticky !important;
}

// 浮动
.float-left {
    float: left !important;
}

.float-right {
    float: right !important;
}

.float-none {
    float: none !important;
}

// 清除浮动
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

// 宽度工具类
.w-25 {
    width: 25% !important;
}

.w-50 {
    width: 50% !important;
}

.w-75 {
    width: 75% !important;
}

.w-100 {
    width: 100% !important;
}

.w-auto {
    width: auto !important;
}

// 高度工具类
.h-25 {
    height: 25% !important;
}

.h-50 {
    height: 50% !important;
}

.h-75 {
    height: 75% !important;
}

.h-100 {
    height: 100% !important;
}

.h-auto {
    height: auto !important;
}

// 最大宽度/高度
.mw-100 {
    max-width: 100% !important;
}

.mh-100 {
    max-height: 100% !important;
}

// 最小宽度/高度
.min-vw-100 {
    min-width: 100vw !important;
}

.min-vh-100 {
    min-height: 100vh !important;
}

.vw-100 {
    width: 100vw !important;
}

.vh-100 {
    height: 100vh !important;
}

// 边距工具类 (0-5)
@for $i from 0 through 5 {
    .m-#{$i} {
        margin: #{$i * 0.25}rem !important;
    }

    .mt-#{$i} {
        margin-top: #{$i * 0.25}rem !important;
    }

    .mr-#{$i} {
        margin-right: #{$i * 0.25}rem !important;
    }

    .mb-#{$i} {
        margin-bottom: #{$i * 0.25}rem !important;
    }

    .ml-#{$i} {
        margin-left: #{$i * 0.25}rem !important;
    }

    .mx-#{$i} {
        margin-left: #{$i * 0.25}rem !important;
        margin-right: #{$i * 0.25}rem !important;
    }

    .my-#{$i} {
        margin-top: #{$i * 0.25}rem !important;
        margin-bottom: #{$i * 0.25}rem !important;
    }

    .p-#{$i} {
        padding: #{$i * 0.25}rem !important;
    }

    .pt-#{$i} {
        padding-top: #{$i * 0.25}rem !important;
    }

    .pr-#{$i} {
        padding-right: #{$i * 0.25}rem !important;
    }

    .pb-#{$i} {
        padding-bottom: #{$i * 0.25}rem !important;
    }

    .pl-#{$i} {
        padding-left: #{$i * 0.25}rem !important;
    }

    .px-#{$i} {
        padding-left: #{$i * 0.25}rem !important;
        padding-right: #{$i * 0.25}rem !important;
    }

    .py-#{$i} {
        padding-top: #{$i * 0.25}rem !important;
        padding-bottom: #{$i * 0.25}rem !important;
    }
}

// 负边距
@for $i from 1 through 5 {
    .m-n#{$i} {
        margin: #{-$i * 0.25}rem !important;
    }

    .mt-n#{$i} {
        margin-top: #{-$i * 0.25}rem !important;
    }

    .mr-n#{$i} {
        margin-right: #{-$i * 0.25}rem !important;
    }

    .mb-n#{$i} {
        margin-bottom: #{-$i * 0.25}rem !important;
    }

    .ml-n#{$i} {
        margin-left: #{-$i * 0.25}rem !important;
    }
}

// 自动边距
.m-auto {
    margin: auto !important;
}

.mt-auto {
    margin-top: auto !important;
}

.mr-auto {
    margin-right: auto !important;
}

.mb-auto {
    margin-bottom: auto !important;
}

.ml-auto {
    margin-left: auto !important;
}

.mx-auto {
    margin-left: auto !important;
    margin-right: auto !important;
}

.my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
}

// 文本工具类
.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.text-center {
    text-align: center !important;
}

.text-justify {
    text-align: justify !important;
}

.text-nowrap {
    white-space: nowrap !important;
}

.text-wrap {
    white-space: normal !important;
}

.text-break {
    word-wrap: break-word !important;
    word-break: break-word !important;
}

.text-lowercase {
    text-transform: lowercase !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}

.text-capitalize {
    text-transform: capitalize !important;
}

// 文本装饰
.text-decoration-none {
    text-decoration: none !important;
}

.text-decoration-underline {
    text-decoration: underline !important;
}

.text-decoration-line-through {
    text-decoration: line-through !important;
}

// 字体重量
.font-weight-light {
    font-weight: 300 !important;
}

.font-weight-normal {
    font-weight: 400 !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.font-weight-bolder {
    font-weight: bolder !important;
}

// 字体样式
.font-italic {
    font-style: italic !important;
}

.font-normal {
    font-style: normal !important;
}

// 颜色工具类
.text-primary {
    color: #1890ff !important;
}

.text-secondary {
    color: #6c757d !important;
}

.text-success {
    color: #52c41a !important;
}

.text-danger {
    color: #ff4d4f !important;
}

.text-warning {
    color: #faad14 !important;
}

.text-info {
    color: #1890ff !important;
}

.text-light {
    color: #f8f9fa !important;
}

.text-dark {
    color: #343a40 !important;
}

.text-muted {
    color: #6c757d !important;
}

.text-white {
    color: #fff !important;
}

// 背景颜色
.bg-primary {
    background-color: #1890ff !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

.bg-success {
    background-color: #52c41a !important;
}

.bg-danger {
    background-color: #ff4d4f !important;
}

.bg-warning {
    background-color: #faad14 !important;
}

.bg-info {
    background-color: #1890ff !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.bg-dark {
    background-color: #343a40 !important;
}

.bg-white {
    background-color: #fff !important;
}

.bg-transparent {
    background-color: transparent !important;
}

// 边框工具类
.border {
    border: 1px solid #dee2e6 !important;
}

.border-top {
    border-top: 1px solid #dee2e6 !important;
}

.border-right {
    border-right: 1px solid #dee2e6 !important;
}

.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}

.border-left {
    border-left: 1px solid #dee2e6 !important;
}

.border-0 {
    border: 0 !important;
}

.border-top-0 {
    border-top: 0 !important;
}

.border-right-0 {
    border-right: 0 !important;
}

.border-bottom-0 {
    border-bottom: 0 !important;
}

.border-left-0 {
    border-left: 0 !important;
}

// 边框颜色
.border-primary {
    border-color: #1890ff !important;
}

.border-secondary {
    border-color: #6c757d !important;
}

.border-success {
    border-color: #52c41a !important;
}

.border-danger {
    border-color: #ff4d4f !important;
}

.border-warning {
    border-color: #faad14 !important;
}

.border-info {
    border-color: #1890ff !important;
}

.border-light {
    border-color: #f8f9fa !important;
}

.border-dark {
    border-color: #343a40 !important;
}

.border-white {
    border-color: #fff !important;
}

// 圆角
.rounded {
    border-radius: 0.25rem !important;
}

.rounded-sm {
    border-radius: 0.125rem !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

.rounded-pill {
    border-radius: 50rem !important;
}

.rounded-0 {
    border-radius: 0 !important;
}

.rounded-top {
    border-top-left-radius: 0.25rem !important;
    border-top-right-radius: 0.25rem !important;
}

.rounded-right {
    border-top-right-radius: 0.25rem !important;
    border-bottom-right-radius: 0.25rem !important;
}

.rounded-bottom {
    border-bottom-right-radius: 0.25rem !important;
    border-bottom-left-radius: 0.25rem !important;
}

.rounded-left {
    border-top-left-radius: 0.25rem !important;
    border-bottom-left-radius: 0.25rem !important;
}

// 阴影
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
    box-shadow: none !important;
}

// 溢出
.overflow-auto {
    overflow: auto !important;
}

.overflow-hidden {
    overflow: hidden !important;
}

.overflow-visible {
    overflow: visible !important;
}

.overflow-scroll {
    overflow: scroll !important;
}

// 垂直对齐
.align-baseline {
    vertical-align: baseline !important;
}

.align-top {
    vertical-align: top !important;
}

.align-middle {
    vertical-align: middle !important;
}

.align-bottom {
    vertical-align: bottom !important;
}

.align-text-bottom {
    vertical-align: text-bottom !important;
}

.align-text-top {
    vertical-align: text-top !important;
}

// 可见性
.visible {
    visibility: visible !important;
}

.invisible {
    visibility: hidden !important;
}

// 用户选择
.user-select-all {
    user-select: all !important;
}

.user-select-auto {
    user-select: auto !important;
}

.user-select-none {
    user-select: none !important;
}

// 指针事件
.pe-none {
    pointer-events: none !important;
}

.pe-auto {
    pointer-events: auto !important;
}

// 光标
.cursor-pointer {
    cursor: pointer !important;
}

.cursor-default {
    cursor: default !important;
}

.cursor-not-allowed {
    cursor: not-allowed !important;
}

.cursor-help {
    cursor: help !important;
}

// Z-index
.z-index-0 {
    z-index: 0 !important;
}

.z-index-1 {
    z-index: 1 !important;
}

.z-index-2 {
    z-index: 2 !important;
}

.z-index-3 {
    z-index: 3 !important;
}

.z-index-auto {
    z-index: auto !important;
}

// 过渡效果
.transition-all {
    transition: all 0.3s ease !important;
}

.transition-none {
    transition: none !important;
}

// 变换
.transform-none {
    transform: none !important;
}

.rotate-90 {
    transform: rotate(90deg) !important;
}

.rotate-180 {
    transform: rotate(180deg) !important;
}

.rotate-270 {
    transform: rotate(270deg) !important;
}

// 透明度
.opacity-0 {
    opacity: 0 !important;
}

.opacity-25 {
    opacity: 0.25 !important;
}

.opacity-50 {
    opacity: 0.5 !important;
}

.opacity-75 {
    opacity: 0.75 !important;
}

.opacity-100 {
    opacity: 1 !important;
}

// 滤镜
.blur {
    filter: blur(5px) !important;
}

.grayscale {
    filter: grayscale(100%) !important;
}

// 自定义工具类
.full-height {
    height: 100vh !important;
}

.full-width {
    width: 100vw !important;
}

.center-absolute {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.center-fixed {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

// 滚动条样式
.scrollbar-thin {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

.scrollbar-hidden {
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none;
    }
}