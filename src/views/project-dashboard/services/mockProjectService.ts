// 选育树推项目看板 - 模拟数据服务
// Source: 基于选育树推项目看板.md需求文档生成

import type {
    Project,
    ProjectStatistics,
    RankingsData,
    IndicatorAnalysisData,
    SupervisionData,
    OrganizationalInsights,
    InnovationTrackingData,
    IntelligentMonitoringData,
    StageManagementData,
    ProjectAnalyticsData,
    TimelineData,
    ApiResponse
} from '../types'

import {
    ProjectStage,
    ProjectStatus,
    ProjectPriority
} from '../types'

// 生成随机ID
const generateId = () => Math.random().toString(36).substr(2, 9)

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// 生成模拟项目数据
const generateMockProjects = (count: number): Project[] => {
    const projects: Project[] = []
    const stages = Object.values(ProjectStage)
    const statuses = Object.values(ProjectStatus)
    const priorities = Object.values(ProjectPriority)
    const departments = ['党委办公室', '组织部', '宣传部', '统战部', '纪委监委', '政法委', '机关工委']
    const managers = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

    // 真实的选育树推项目名称
    const projectNames = [
        '机关党建示范点创建项目',
        '优秀党员先进事迹宣传项目',
        '基层党组织标准化建设项目',
        '党员教育培训创新项目',
        '机关作风建设提升项目',
        '党务工作者能力提升项目',
        '党建品牌创建示范项目',
        '党员志愿服务品牌项目',
        '机关文化建设创新项目',
        '党建工作数字化转型项目',
        '青年党员成长培养项目',
        '党建引领业务融合项目',
        '机关党建联建共建项目',
        '党员干部廉政教育项目',
        '基层治理创新实践项目',
        '党建工作考核评价项目',
        '机关党建阵地建设项目',
        '党员先锋模范作用发挥项目',
        '党建工作制度创新项目',
        '机关党建文化品牌项目'
    ]

    for (let i = 0; i < count; i++) {
        const startDate = generateRandomDate(new Date(2024, 0, 1), new Date(2024, 11, 31))
        const endDate = new Date(startDate.getTime() + Math.random() * 180 * 24 * 60 * 60 * 1000)
        const progress = Math.floor(Math.random() * 101)
        const isDelayed = Math.random() > 0.8
        const isCritical = Math.random() > 0.9

        projects.push({
            id: generateId(),
            name: projectNames[i % projectNames.length] + (i >= projectNames.length ? ` (${Math.floor(i / projectNames.length) + 1})` : ''),
            description: `该项目旨在通过创新实践，提升机关党建工作质量和效果，打造具有示范引领作用的党建品牌，推动党建工作与业务工作深度融合。`,
            stage: stages[Math.floor(Math.random() * stages.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            progress,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            expectedEndDate: endDate.toISOString(),
            manager: managers[Math.floor(Math.random() * managers.length)],
            department: departments[Math.floor(Math.random() * departments.length)],
            budget: Math.floor(Math.random() * 1000000) + 100000,
            actualCost: Math.floor(Math.random() * 800000) + 80000,
            participants: Math.floor(Math.random() * 50) + 10,
            beneficiaries: Math.floor(Math.random() * 500) + 100,
            tags: ['党建创新', '示范引领', '品牌建设', '融合发展', '数字化转型'].slice(0, Math.floor(Math.random() * 3) + 1),
            isDelayed,
            isCritical,
            milestones: [
                {
                    id: generateId(),
                    name: '项目启动',
                    description: '项目正式启动，成立项目组',
                    date: startDate.toISOString(),
                    isCompleted: true,
                    isCritical: false,
                    completedAt: startDate.toISOString(),
                    completedBy: managers[Math.floor(Math.random() * managers.length)]
                },
                {
                    id: generateId(),
                    name: '方案制定',
                    description: '制定详细实施方案',
                    date: new Date(startDate.getTime() + 15 * 24 * 60 * 60 * 1000).toISOString(),
                    isCompleted: progress > 20,
                    isCritical: true
                },
                {
                    id: generateId(),
                    name: '中期检查',
                    description: '项目中期进度检查和评估',
                    date: new Date(startDate.getTime() + 60 * 24 * 60 * 60 * 1000).toISOString(),
                    isCompleted: progress > 50,
                    isCritical: true
                },
                {
                    id: generateId(),
                    name: '成果展示',
                    description: '项目成果展示和经验分享',
                    date: new Date(startDate.getTime() + 120 * 24 * 60 * 60 * 1000).toISOString(),
                    isCompleted: progress > 80,
                    isCritical: false
                },
                {
                    id: generateId(),
                    name: '项目验收',
                    description: '项目最终验收和总结',
                    date: endDate.toISOString(),
                    isCompleted: progress === 100,
                    isCritical: true
                }
            ],
            dependencies: [],
            metrics: {
                qualityScore: Math.floor(Math.random() * 40) + 60,
                riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as 'low' | 'medium' | 'high',
                resourceUtilization: Math.floor(Math.random() * 40) + 60,
                stakeholderSatisfaction: Math.floor(Math.random() * 30) + 70,
                innovationIndex: Math.floor(Math.random() * 50) + 50,
                socialImpact: Math.floor(Math.random() * 40) + 60
            },
            createdAt: startDate.toISOString(),
            updatedAt: new Date().toISOString()
        })
    }

    return projects
}

// Mock数据
const mockProjects = generateMockProjects(50)

// 项目统计概览服务
export const getProjectStatistics = async (): Promise<ApiResponse<ProjectStatistics>> => {
    await new Promise(resolve => setTimeout(resolve, 500))

    const totalProjects = mockProjects.length
    const activeProjects = mockProjects.filter(p => p.status === ProjectStatus.ACTIVE).length
    const completedProjects = mockProjects.filter(p => p.status === ProjectStatus.COMPLETED).length
    const delayedProjects = mockProjects.filter(p => p.isDelayed).length

    const stageDistribution = {
        selection: mockProjects.filter(p => p.stage === ProjectStage.SELECTION).length,
        cultivation: mockProjects.filter(p => p.stage === ProjectStage.CULTIVATION).length,
        establishment: mockProjects.filter(p => p.stage === ProjectStage.ESTABLISHMENT).length,
        promotion: mockProjects.filter(p => p.stage === ProjectStage.PROMOTION).length
    }

    const statusDistribution = {
        planning: mockProjects.filter(p => p.status === ProjectStatus.PLANNING).length,
        active: mockProjects.filter(p => p.status === ProjectStatus.ACTIVE).length,
        completed: mockProjects.filter(p => p.status === ProjectStatus.COMPLETED).length,
        paused: mockProjects.filter(p => p.status === ProjectStatus.PAUSED).length,
        delayed: mockProjects.filter(p => p.status === ProjectStatus.DELAYED).length
    }

    const monthlyTrends = Array.from({ length: 12 }, (_, i) => ({
        month: `2024-${String(i + 1).padStart(2, '0')}`,
        newProjects: Math.floor(Math.random() * 10) + 5,
        completedProjects: Math.floor(Math.random() * 8) + 3,
        totalBudget: Math.floor(Math.random() * 5000000) + 1000000,
        avgProgress: Math.floor(Math.random() * 30) + 60
    }))

    return {
        code: 200,
        message: '获取成功',
        data: {
            totalProjects,
            activeProjects,
            completedProjects,
            delayedProjects,
            totalBudget: mockProjects.reduce((sum, p) => sum + p.budget, 0),
            actualCost: mockProjects.reduce((sum, p) => sum + p.actualCost, 0),
            avgProgress: Math.floor(mockProjects.reduce((sum, p) => sum + p.progress, 0) / totalProjects),
            avgQualityScore: Math.floor(mockProjects.reduce((sum, p) => sum + p.metrics.qualityScore, 0) / totalProjects),
            stageDistribution,
            statusDistribution,
            monthlyTrends
        },
        timestamp: new Date().toISOString()
    }
}

// 排行榜数据服务
export const getRankingsData = async (): Promise<ApiResponse<RankingsData>> => {
    await new Promise(resolve => setTimeout(resolve, 300))

    const topProjects = mockProjects
        .sort((a, b) => b.metrics.qualityScore - a.metrics.qualityScore)
        .slice(0, 10)
        .map((project, index) => ({
            id: project.id,
            name: project.name,
            department: project.department,
            manager: project.manager,
            score: project.metrics.qualityScore,
            rank: index + 1,
            change: Math.floor(Math.random() * 6) - 3,
            stage: project.stage,
            progress: project.progress,
            highlights: ['创新性强', '执行效果好', '社会影响大'].slice(0, Math.floor(Math.random() * 3) + 1)
        }))

    const departments = ['党委办公室', '组织部', '宣传部', '统战部', '纪委监委']
    const topDepartments = departments.map((dept, index) => {
        const deptProjects = mockProjects.filter(p => p.department === dept)
        return {
            id: generateId(),
            name: dept,
            projectCount: deptProjects.length,
            completionRate: Math.floor(Math.random() * 30) + 70,
            avgScore: Math.floor(Math.random() * 20) + 80,
            rank: index + 1,
            change: Math.floor(Math.random() * 4) - 2
        }
    })

    const managers = ['张三', '李四', '王五', '赵六', '钱七']
    const topManagers = managers.map((manager, index) => {
        const managerProjects = mockProjects.filter(p => p.manager === manager)
        return {
            id: generateId(),
            name: manager,
            department: departments[Math.floor(Math.random() * departments.length)],
            projectCount: managerProjects.length,
            successRate: Math.floor(Math.random() * 25) + 75,
            avgScore: Math.floor(Math.random() * 15) + 85,
            rank: index + 1,
            change: Math.floor(Math.random() * 4) - 2
        }
    })

    const innovationProjects = mockProjects
        .sort((a, b) => b.metrics.innovationIndex - a.metrics.innovationIndex)
        .slice(0, 5)
        .map((project, index) => ({
            id: project.id,
            name: project.name,
            department: project.department,
            manager: project.manager,
            score: project.metrics.innovationIndex,
            rank: index + 1,
            change: Math.floor(Math.random() * 4) - 2,
            stage: project.stage,
            progress: project.progress,
            highlights: ['技术创新', '模式创新', '管理创新'].slice(0, Math.floor(Math.random() * 3) + 1)
        }))

    return {
        code: 200,
        message: '获取成功',
        data: {
            topProjects,
            topDepartments,
            topManagers,
            innovationProjects
        },
        timestamp: new Date().toISOString()
    }
}

// 指标分析数据服务
export const getIndicatorAnalysisData = async (): Promise<ApiResponse<IndicatorAnalysisData>> => {
    await new Promise(resolve => setTimeout(resolve, 400))

    return {
        code: 200,
        message: '获取成功',
        data: {
            overallHealth: 85,
            keyIndicators: [
                {
                    id: generateId(),
                    name: '项目完成率',
                    value: 78,
                    target: 80,
                    unit: '%',
                    trend: 5.2,
                    status: 'warning',
                    description: '当前项目完成率略低于目标值'
                },
                {
                    id: generateId(),
                    name: '质量评分',
                    value: 87,
                    target: 85,
                    unit: '分',
                    trend: 3.1,
                    status: 'good',
                    description: '项目质量评分超过目标值'
                }
            ],
            trendAnalysis: {
                period: '近12个月',
                metrics: [
                    {
                        name: '项目数量',
                        data: [45, 52, 48, 61, 55, 67, 73, 69, 78, 82, 85, 89],
                        dates: Array.from({ length: 12 }, (_, i) => `2024-${String(i + 1).padStart(2, '0')}`)
                    }
                ]
            },
            riskAssessment: {
                overallRisk: 'medium',
                riskFactors: [
                    {
                        id: generateId(),
                        name: '资源短缺',
                        level: 'medium',
                        impact: 7,
                        probability: 6,
                        description: '部分项目面临人力资源不足的问题',
                        mitigation: '加强人员培训，优化资源配置'
                    }
                ],
                mitigation: ['建立风险预警机制', '加强项目过程管控']
            },
            recommendations: [
                {
                    id: generateId(),
                    type: 'optimization',
                    title: '优化项目流程',
                    description: '建议简化项目审批流程，提高执行效率',
                    priority: 'high',
                    estimatedImpact: '提升效率15-20%',
                    implementationTime: '2-3个月'
                }
            ]
        },
        timestamp: new Date().toISOString()
    }
}

// 监督管理数据服务
export const getSupervisionData = async (): Promise<ApiResponse<SupervisionData>> => {
    await new Promise(resolve => setTimeout(resolve, 350))

    return {
        code: 200,
        message: '获取成功',
        data: {
            overviewStats: {
                totalAudits: 156,
                passedAudits: 142,
                failedAudits: 8,
                pendingAudits: 6,
                complianceRate: 91.0,
                avgAuditScore: 87.5
            },
            auditResults: mockProjects.slice(0, 10).map(project => ({
                id: generateId(),
                projectId: project.id,
                projectName: project.name,
                auditDate: generateRandomDate(new Date(2024, 0, 1), new Date()).toISOString(),
                auditor: '审计组',
                score: Math.floor(Math.random() * 30) + 70,
                status: ['passed', 'failed', 'pending'][Math.floor(Math.random() * 3)] as 'passed' | 'failed' | 'pending',
                findings: ['项目执行规范', '文档完整性良好', '资金使用合规'].slice(0, Math.floor(Math.random() * 3) + 1),
                recommendations: ['加强过程监控', '完善文档管理', '提升执行效率'].slice(0, Math.floor(Math.random() * 3) + 1)
            })),
            complianceMetrics: [
                {
                    id: generateId(),
                    name: '文档完整性',
                    value: 94,
                    threshold: 90,
                    status: 'compliant',
                    trend: 2.1
                }
            ],
            alerts: [
                {
                    id: generateId(),
                    type: 'compliance',
                    level: 'warning',
                    title: '项目进度预警',
                    description: '项目A进度落后于计划，需要关注',
                    projectId: mockProjects[0].id,
                    createdAt: new Date().toISOString(),
                    isRead: false
                }
            ]
        },
        timestamp: new Date().toISOString()
    }
}

// 组织洞察数据服务
export const getOrganizationalInsights = async (): Promise<ApiResponse<OrganizationalInsights>> => {
    await new Promise(resolve => setTimeout(resolve, 450))

    const departments = ['党委办公室', '组织部', '宣传部', '统战部', '纪委监委']

    return {
        code: 200,
        message: '获取成功',
        data: {
            departmentAnalysis: departments.map(dept => ({
                id: generateId(),
                name: dept,
                projectCount: mockProjects.filter(p => p.department === dept).length,
                avgProgress: Math.floor(Math.random() * 30) + 60,
                resourceUtilization: Math.floor(Math.random() * 25) + 70,
                collaborationScore: Math.floor(Math.random() * 20) + 75,
                innovationIndex: Math.floor(Math.random() * 30) + 60,
                strengths: ['执行力强', '创新能力', '团队协作'].slice(0, Math.floor(Math.random() * 3) + 1),
                improvements: ['流程优化', '技能提升', '资源配置'].slice(0, Math.floor(Math.random() * 2) + 1)
            })),
            collaborationNetwork: departments.map(dept => ({
                id: generateId(),
                name: dept,
                type: 'department' as const,
                connections: departments.filter(d => d !== dept).slice(0, Math.floor(Math.random() * 3) + 1),
                weight: Math.floor(Math.random() * 50) + 50,
                x: Math.random() * 100,
                y: Math.random() * 100
            })),
            capacityAnalysis: {
                totalCapacity: 1000,
                usedCapacity: 750,
                availableCapacity: 250,
                projectedDemand: 850,
                bottlenecks: ['人力资源', '技术支持', '预算限制'],
                recommendations: ['增加人员配置', '提升技术能力', '优化预算分配']
            },
            performanceMatrix: {
                departments: departments.map(dept => ({
                    name: dept,
                    performance: Math.floor(Math.random() * 30) + 70,
                    potential: Math.floor(Math.random() * 30) + 70,
                    quadrant: ['star', 'question-mark', 'cash-cow', 'dog'][Math.floor(Math.random() * 4)] as 'star' | 'question-mark' | 'cash-cow' | 'dog'
                }))
            }
        },
        timestamp: new Date().toISOString()
    }
}

// 创新跟踪数据服务
export const getInnovationTrackingData = async (): Promise<ApiResponse<InnovationTrackingData>> => {
    await new Promise(resolve => setTimeout(resolve, 400))

    return {
        code: 200,
        message: '获取成功',
        data: {
            innovationMetrics: [
                {
                    id: generateId(),
                    name: '创新项目数量',
                    value: 25,
                    target: 30,
                    unit: '个',
                    category: 'input',
                    trend: 15.2
                }
            ],
            innovationProjects: mockProjects.filter(p => p.metrics.innovationIndex > 70).slice(0, 8),
            technologyTrends: [
                {
                    id: generateId(),
                    name: '人工智能',
                    category: '新兴技术',
                    adoptionRate: 35,
                    maturityLevel: 65,
                    potentialImpact: 85,
                    timeline: '2-3年'
                }
            ],
            patentAnalysis: {
                totalPatents: 45,
                newPatents: 8,
                patentsByCategory: [
                    { category: '管理创新', count: 18 },
                    { category: '技术创新', count: 15 }
                ],
                patentTrends: Array.from({ length: 12 }, (_, i) => ({
                    month: `2024-${String(i + 1).padStart(2, '0')}`,
                    count: Math.floor(Math.random() * 5) + 2
                }))
            },
            collaborationMap: {
                internalCollaborations: 32,
                externalCollaborations: 18,
                partnerOrganizations: ['高校A', '企业B', '研究院C'],
                collaborationProjects: mockProjects.slice(0, 5)
            }
        },
        timestamp: new Date().toISOString()
    }
}

// 智能监控数据服务
export const getIntelligentMonitoringData = async (): Promise<ApiResponse<IntelligentMonitoringData>> => {
    await new Promise(resolve => setTimeout(resolve, 300))

    return {
        code: 200,
        message: '获取成功',
        data: {
            systemHealth: {
                overall: 92,
                components: [
                    {
                        name: '项目管理系统',
                        status: 'healthy',
                        uptime: 99.8,
                        lastCheck: new Date().toISOString()
                    }
                ]
            },
            performanceMetrics: [
                {
                    id: generateId(),
                    name: '系统响应时间',
                    value: 245,
                    unit: 'ms',
                    threshold: 300,
                    status: 'normal',
                    history: Array.from({ length: 24 }, (_, i) => ({
                        timestamp: new Date(Date.now() - (23 - i) * 3600000).toISOString(),
                        value: Math.floor(Math.random() * 100) + 200
                    }))
                }
            ],
            anomalies: [
                {
                    id: generateId(),
                    type: 'performance',
                    severity: 'medium',
                    description: '系统响应时间异常波动',
                    detectedAt: new Date(Date.now() - 3600000).toISOString(),
                    affectedComponents: ['数据分析引擎'],
                    status: 'investigating'
                }
            ],
            predictions: [
                {
                    id: generateId(),
                    type: 'performance',
                    description: '预计下周系统负载将增加20%',
                    confidence: 85,
                    timeframe: '7天',
                    impact: 'medium',
                    recommendations: ['增加服务器资源', '优化数据库查询']
                }
            ],
            alerts: [
                {
                    id: generateId(),
                    type: 'system',
                    level: 'warning',
                    title: '磁盘空间不足',
                    description: '服务器磁盘使用率超过85%',
                    source: '监控系统',
                    timestamp: new Date().toISOString(),
                    isAcknowledged: false
                }
            ]
        },
        timestamp: new Date().toISOString()
    }
}

// 阶段管理数据服务
export const getStageManagementData = async (): Promise<ApiResponse<StageManagementData>> => {
    await new Promise(resolve => setTimeout(resolve, 350))

    return {
        code: 200,
        message: '获取成功',
        data: {
            totalStages: 16,
            activeStages: 12,
            completedStages: 4,
            stages: [
                {
                    id: generateId(),
                    name: '选拔阶段-基础评估',
                    description: '对候选项目进行基础评估和筛选',
                    type: ProjectStage.SELECTION,
                    status: 'active',
                    progress: 75,
                    projectCount: 8,
                    manager: '张三',
                    startDate: '2024-01-01',
                    expectedEndDate: '2024-03-31',
                    milestones: [
                        {
                            id: generateId(),
                            name: '评估标准制定',
                            description: '制定项目评估标准',
                            date: '2024-01-15',
                            isCompleted: true,
                            isCritical: false
                        }
                    ],
                    isDelayed: false
                }
            ],
            templates: [
                {
                    id: generateId(),
                    name: '标准选育树推模板',
                    description: '适用于大部分选育树推项目的标准模板',
                    type: '标准模板',
                    stageCount: 4,
                    usageCount: 25,
                    createTime: '2024-01-01',
                    stages: [
                        {
                            name: '选拔阶段',
                            duration: 90,
                            dependencies: [],
                            milestones: ['评估完成', '筛选完成']
                        }
                    ]
                }
            ]
        },
        timestamp: new Date().toISOString()
    }
}

// 项目分析数据服务
export const getProjectAnalyticsData = async (): Promise<ApiResponse<ProjectAnalyticsData>> => {
    await new Promise(resolve => setTimeout(resolve, 400))

    return {
        code: 200,
        message: '获取成功',
        data: {
            avgCompletionTime: 85,
            completionTimeTrend: 5.2,
            successRate: 78,
            successRateTrend: 3.1,
            resourceUtilization: 72,
            resourceTrend: -2.3,
            qualityScore: 87,
            qualityTrend: 4.5,
            coverageUnits: 45,
            beneficiaries: 1250,
            socialImpact: 82,
            prediction: {
                trend: 'positive',
                period: '未来3个月',
                improvement: 12
            }
        },
        timestamp: new Date().toISOString()
    }
}

// 时间线数据服务
export const getTimelineData = async (): Promise<ApiResponse<TimelineData>> => {
    await new Promise(resolve => setTimeout(resolve, 300))

    const upcomingDeadlines = mockProjects.filter(p => {
        const endDate = new Date(p.endDate)
        const now = new Date()
        const diffDays = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
        return diffDays > 0 && diffDays <= 30
    }).length

    return {
        code: 200,
        message: '获取成功',
        data: {
            totalProjects: mockProjects.length,
            activeProjects: mockProjects.filter(p => p.status === ProjectStatus.ACTIVE).length,
            upcomingDeadlines,
            delayedProjects: mockProjects.filter(p => p.isDelayed).length,
            projects: mockProjects
        },
        timestamp: new Date().toISOString()
    }
}

// 获取项目列表服务
export const getProjectList = async (params?: any): Promise<ApiResponse<Project[]>> => {
    await new Promise(resolve => setTimeout(resolve, 200))

    let filteredProjects = [...mockProjects]

    if (params?.stage) {
        filteredProjects = filteredProjects.filter(p => p.stage === params.stage)
    }
    if (params?.status) {
        filteredProjects = filteredProjects.filter(p => p.status === params.status)
    }
    if (params?.department) {
        filteredProjects = filteredProjects.filter(p => p.department === params.department)
    }
    if (params?.keyword) {
        const keyword = params.keyword.toLowerCase()
        filteredProjects = filteredProjects.filter(p =>
            p.name.toLowerCase().includes(keyword) ||
            p.description.toLowerCase().includes(keyword)
        )
    }

    return {
        code: 200,
        message: '获取成功',
        data: filteredProjects,
        timestamp: new Date().toISOString()
    }
}

// 获取单个项目详情服务
export const getProjectDetail = async (id: string): Promise<ApiResponse<Project | null>> => {
    await new Promise(resolve => setTimeout(resolve, 200))

    const project = mockProjects.find(p => p.id === id)

    return {
        code: project ? 200 : 404,
        message: project ? '获取成功' : '项目不存在',
        data: project || null,
        timestamp: new Date().toISOString()
    }
}

// 导出所有服务
export default {
    getProjectStatistics,
    getRankingsData,
    getIndicatorAnalysisData,
    getSupervisionData,
    getOrganizationalInsights,
    getInnovationTrackingData,
    getIntelligentMonitoringData,
    getStageManagementData,
    getProjectAnalyticsData,
    getTimelineData,
    getProjectList,
    getProjectDetail
}