<template>
  <div class="project-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">选育树推项目看板</h1>
          <a-breadcrumb class="page-breadcrumb">
            <a-breadcrumb-item>
              <home-outlined />
              首页
            </a-breadcrumb-item>
            <a-breadcrumb-item>项目管理</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="header-actions">
          <a-space>
            <a-select v-model:value="selectedStage" placeholder="选择阶段" style="width: 200px" allow-clear
              @change="handleStageChange">
              <a-select-option value="">全部阶段</a-select-option>
              <a-select-option value="selection">选拔遴选</a-select-option>
              <a-select-option value="cultivation">培育阶段</a-select-option>
              <a-select-option value="establishment">树立阶段</a-select-option>
              <a-select-option value="promotion">推广阶段</a-select-option>
            </a-select>
            <a-select v-model:value="selectedDistrict" placeholder="选择区县" style="width: 200px" allow-clear
              @change="handleDistrictChange">
              <a-select-option value="">全部区县</a-select-option>
              <a-select-option v-for="district in districts" :key="district" :value="district">
                {{ district }}
              </a-select-option>
            </a-select>
            <a-button @click="refreshAllData" :loading="globalLoading">
              <template #icon><reload-outlined /></template>
              刷新数据
            </a-button>
            <a-dropdown>
              <a-button>
                <template #icon><download-outlined /></template>
                导出报告
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleExport">
                  <a-menu-item key="pdf">
                    <file-pdf-outlined />
                    导出PDF报告
                  </a-menu-item>
                  <a-menu-item key="excel">
                    <file-excel-outlined />
                    导出Excel数据
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 选育树推四阶段概览 -->
    <div class="stage-overview-section">
      <a-card title="选育树推四阶段概览" class="stage-overview-card">
        <a-row :gutter="[24, 24]">
          <a-col v-for="stage in stageOverview" :key="stage.key" :xs="24" :sm="12" :md="6">
            <div class="stage-card" :class="stage.key" @click="handleStageClick(stage)">
              <div class="stage-header">
                <div class="stage-icon">
                  <component :is="stage.icon" />
                </div>
                <div class="stage-info">
                  <div class="stage-name">{{ stage.name }}</div>
                  <div class="stage-desc">{{ stage.description }}</div>
                </div>
              </div>
              <div class="stage-stats">
                <div class="stat-item">
                  <div class="stat-value" @click.stop="showProjectDetails(stage)">{{ stage.projectCount }}</div>
                  <div class="stat-label">项目数量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ stage.unitCount }}</div>
                  <div class="stat-label">参与单位</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ stage.completionRate }}%</div>
                  <div class="stat-label">完成率</div>
                </div>
              </div>
              <div class="stage-progress">
                <a-progress :percent="stage.completionRate" :stroke-color="stage.color" :show-info="false"
                  size="small" />
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 项目阶段流程图 - 独占一行 -->
      <div class="flow-section">
        <a-card title="选育树推指标展示" class="flow-card">
          <div class="indicators-summary">
            <div class="indicator-stat">
              <div class="indicator-value">{{ indicatorsTotal }}</div>
              <div class="indicator-label">指标总数</div>
            </div>
            <div class="indicator-stat">
              <div class="indicator-value">{{ indicatorsDataTotal }}</div>
              <div class="indicator-label">指标数据资源总数</div>
            </div>
          </div>
          <a-divider />
          <div class="indicators-list">
            <a-table :dataSource="indicatorsList" :columns="indicatorsColumns" :loading="indicatorsLoading"
              :pagination="{ pageSize: 5 }" size="middle">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'action'">
                  <a @click="viewIndicatorDetail(record)">查看详情</a>
                </template>
                <template v-if="column.key === 'completionRate'">
                  <a-progress :percent="record.completionRate" size="small" />
                </template>
              </template>
            </a-table>
          </div>
        </a-card>
      </div>
    </div>

    <!-- 项目时间线 -->
    <div class="timeline-section">
      <a-card title="选育树推指标详情展示" class="timeline-card">
        <div class="project-selector" style="margin-bottom: 16px;">
          <a-select v-model:value="selectedProjectId" placeholder="选择项目名称" style="width: 300px"
            @change="handleProjectChange">
            <a-select-option v-for="project in projectOptions" :key="project.id" :value="project.id">
              {{ project.name }}
            </a-select-option>
          </a-select>
        </div>
        <ProjectTimeline :timeline-data="timelineData" :loading="timelineLoading"
          @milestone-click="handleMilestoneClick" />
      </a-card>
    </div>

    <!-- 新增：选育树推项目亮晒比拼模块 -->
    <CompetitionModule ref="competitionModuleRef" :selected-stage="selectedStage"
      :selected-district="selectedDistrict" />

    <!-- 新增：选育树推项目创建动态模块 -->
    <DynamicsModule ref="dynamicsModuleRef" :selected-stage="selectedStage" :selected-district="selectedDistrict" />

    <!-- 新增：选育树推项目创建成果模块 -->
    <AchievementsModule ref="achievementsModuleRef" :selected-stage="selectedStage"
      :selected-district="selectedDistrict" />

    <!-- 新增：选育树推项目创建跟踪督办模块 -->
    <SupervisionModule ref="supervisionModuleRef" :selected-stage="selectedStage"
      :selected-district="selectedDistrict" />

    <!-- 全局加载状态 -->
    <div v-if="globalLoading" class="global-loading">
      <a-spin size="large" tip="加载数据中..." />
    </div>

    <!-- 项目详情弹窗 -->
    <a-modal v-model:visible="projectDetailVisible" :title="selectedProject?.name" width="1000px" :footer="null">
      <div v-if="selectedProject" class="project-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="项目阶段">
            <a-tag :color="getStageColor(selectedProject.stage)">
              {{ getStageLabel(selectedProject.stage) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="项目状态">
            <a-tag :color="getStatusColor(selectedProject.status)">
              {{ getStatusLabel(selectedProject.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="负责单位">
            {{ selectedProject.department }}
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ formatDate(selectedProject.startDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="预计结束">
            {{ formatDate(selectedProject.endDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="项目进度" :span="2">
            <a-progress :percent="selectedProject.progress" />
          </a-descriptions-item>
          <a-descriptions-item label="项目描述" :span="2">
            {{ selectedProject.description }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 项目里程碑 -->
        <div class="project-milestones" style="margin-top: 24px;">
          <h4>项目里程碑</h4>
          <a-timeline>
            <a-timeline-item v-for="milestone in selectedProject.milestones" :key="milestone.id"
              :color="milestone.isCompleted ? 'green' : 'blue'">
              <div class="milestone-item">
                <div class="milestone-name">{{ milestone.name }}</div>
                <div class="milestone-desc">{{ milestone.description }}</div>
                <div class="milestone-date">{{ formatDate(milestone.date) }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>

    <!-- 项目数量详情弹窗 -->
    <a-modal v-model:visible="projectListVisible" :title="`${currentStage?.name || ''}阶段项目列表`" width="800px"
      :footer="null">
      <a-table :dataSource="stageProjectsList" :columns="projectListColumns" :loading="stageProjectsLoading"
        :pagination="{ pageSize: 5 }" size="middle">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a @click="handleProjectClick(record)">查看详情</a>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 指标详情弹窗 -->
    <a-modal v-model:visible="indicatorDetailVisible" :title="`指标详情: ${selectedIndicator?.name || ''}`" width="700px"
      :footer="null">
      <div v-if="selectedIndicator" class="indicator-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="指标名称">{{ selectedIndicator.name }}</a-descriptions-item>
          <a-descriptions-item label="指标来源">{{ selectedIndicator.source }}</a-descriptions-item>
          <a-descriptions-item label="完成指标单位数">{{ selectedIndicator.completedUnits }}</a-descriptions-item>
          <a-descriptions-item label="指标完成率">{{ selectedIndicator.completionRate }}%</a-descriptions-item>
          <a-descriptions-item label="指标详情" :span="2">{{ selectedIndicator.details }}</a-descriptions-item>
        </a-descriptions>

        <div class="unit-completion-list" style="margin-top: 24px;">
          <h4>单位完成情况</h4>
          <a-table :dataSource="indicatorUnitsList" :loading="indicatorUnitsLoading" :pagination="{ pageSize: 10 }"
            size="small">
            <a-table-column title="单位名称" dataIndex="unitName" />
            <a-table-column title="得分" dataIndex="score" />
            <a-table-column title="是否完成" dataIndex="isCompleted">
              <template #default="{ text }">
                <a-tag :color="text ? 'success' : 'error'">
                  {{ text ? '已完成' : '未完成' }}
                </a-tag>
              </template>
            </a-table-column>
          </a-table>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  HomeOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DownOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  SelectOutlined,
  TeamOutlined,
  TrophyOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'

// 导入组件
import ProjectStageFlow from './components/ProjectStageFlow.vue'
import ProjectTimeline from './components/ProjectTimeline.vue'
// 导入新增的组件
import CompetitionModule from './components/CompetitionModule.vue'
import DynamicsModule from './components/DynamicsModule.vue'
import AchievementsModule from './components/AchievementsModule.vue'
import SupervisionModule from './components/SupervisionModule.vue'

// 导入类型和服务
import type {
  Project,
  ProjectStatus,
  TimelineData
} from './types'
import { mockProjectDashboardApi } from './api/mockProjectDashboardApi'

// 响应式数据
const globalLoading = ref(false)
const timelineLoading = ref(false)
const indicatorsLoading = ref(false)
const stageProjectsLoading = ref(false)
const indicatorDetailVisible = ref(false)
const selectedIndicator = ref<any>(null)
const indicatorUnitsList = ref<any[]>([])
const indicatorUnitsLoading = ref(false)

// 组件引用
const competitionModuleRef = ref(null)
const dynamicsModuleRef = ref(null)
const achievementsModuleRef = ref(null)
const supervisionModuleRef = ref(null)

const selectedStage = ref('')
const selectedDistrict = ref('')
const projectDetailVisible = ref(false)
const projectListVisible = ref(false)

// 数据状态
const districts = ref<string[]>(['渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区'])
const selectedProject = ref<Project | null>(null)
const currentStage = ref<any>(null)
const stageProjectsList = ref<Project[]>([])

const projectsData = ref<Project[]>([])
const timelineData = ref<TimelineData | null>(null)

// 项目选择相关
const selectedProjectId = ref<number | null>(null)
const projectOptions = ref([
  { id: 1, name: '政治要件闭环落实情况' },
  { id: 2, name: '重大事项请示报告上报情况' },
  { id: 3, name: '党内法规制度建设上报情况' },
  { id: 4, name: '问题管控考核结果' },
  { id: 5, name: '组织生活纪实完成情况' },
  { id: 6, name: '未完成的组织生活纪实内容' },
  { id: 7, name: '党费收交完成情况' },
  { id: 8, name: '基层党组织换届完成情况' },
  { id: 9, name: '基层党未及时完成换届的组织' },
  { id: 10, name: '党员报到完成率' }
])

// 新增数据
const indicatorsTotal = ref(0)
const indicatorsDataTotal = ref(0)
const indicatorsList = ref<any[]>([])

// 表格列定义
const indicatorsColumns = [
  {
    title: '指标名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '指标来源',
    dataIndex: 'source',
    key: 'source',
  },
  {
    title: '完成指标单位数',
    dataIndex: 'completedUnits',
    key: 'completedUnits',
  },
  {
    title: '指标完成率',
    dataIndex: 'completionRate',
    key: 'completionRate',
  },
  {
    title: '操作',
    key: 'action',
  },
]

const projectListColumns = [
  {
    title: '项目名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '发起单位',
    dataIndex: 'department',
    key: 'department',
  },
  {
    title: '申报单位个数',
    dataIndex: 'applyingUnits',
    key: 'applyingUnits',
  },
  {
    title: '培育单位个数',
    dataIndex: 'cultivationUnits',
    key: 'cultivationUnits',
  },
  {
    title: '标杆单位个数',
    dataIndex: 'benchmarkUnits',
    key: 'benchmarkUnits',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 选育树推四阶段概览数据 - 动态数据
const stageOverview = ref([
  {
    key: 'selection',
    name: '选拔遴选',
    description: '发现和选拔优秀典型',
    icon: 'SelectOutlined',
    projectCount: 0,
    unitCount: 0,
    completionRate: 0,
    color: '#1890ff'
  },
  {
    key: 'cultivation',
    name: '培育阶段',
    description: '重点培育和指导',
    icon: 'TeamOutlined',
    projectCount: 0,
    unitCount: 0,
    completionRate: 0,
    color: '#52c41a'
  },
  {
    key: 'establishment',
    name: '树立阶段',
    description: '树立典型和标杆',
    icon: 'TrophyOutlined',
    projectCount: 0,
    unitCount: 0,
    completionRate: 0,
    color: '#faad14'
  },
  {
    key: 'promotion',
    name: '推广阶段',
    description: '推广经验和做法',
    icon: 'RocketOutlined',
    projectCount: 0,
    unitCount: 0,
    completionRate: 0,
    color: '#722ed1'
  }
])

// 更新阶段概览数据
const updateStageOverview = () => {
  if (!projectsData.value.length) return

  stageOverview.value.forEach(stage => {
    const stageProjects = projectsData.value.filter(p => p.stage === stage.key)
    const departments = new Set(stageProjects.map(p => p.department))
    const completedProjects = stageProjects.filter(p => p.status === 'completed')

    stage.projectCount = stageProjects.length
    stage.unitCount = departments.size
    stage.completionRate = stageProjects.length > 0
      ? Math.round((completedProjects.length / stageProjects.length) * 100)
      : 0
  })
}

// 数据加载方法
const loadProjectsData = async () => {
  try {
    const response = await mockProjectDashboardApi.getProjectList({
      stage: selectedStage.value,
      district: selectedDistrict.value
    })
    if (response.code === 200) {
      projectsData.value = response.data
    } else {
      message.error(response.message || '加载项目数据失败')
    }
  } catch (error) {
    message.error('加载项目数据失败')
    console.error(error)
  }
}

const loadTimelineData = async () => {
  timelineLoading.value = true
  try {
    const response = await mockProjectDashboardApi.getTimelineData(selectedProjectId.value)
    if (response.code === 200) {
      timelineData.value = response.data
    } else {
      message.error(response.message || '加载时间线数据失败')
    }
  } catch (error) {
    message.error('加载时间线数据失败')
    console.error(error)
  } finally {
    timelineLoading.value = false
  }
}

// 加载阶段统计数据
const loadStageStatistics = async () => {
  try {
    const response = await mockProjectDashboardApi.getStageStatistics()
    if (response.code === 200) {
      // 更新阶段概览数据
      response.data.forEach(stat => {
        const stage = stageOverview.value.find(s => s.key === stat.stage)
        if (stage) {
          stage.projectCount = stat.projectCount
          stage.unitCount = stat.unitCount
          stage.completionRate = stat.completionRate
        }
      })
    }
  } catch (error) {
    message.error('加载阶段统计数据失败')
    console.error(error)
  }
}

// 新增数据加载方法
const loadIndicatorsData = async () => {
  indicatorsLoading.value = true
  try {
    const response = await mockProjectDashboardApi.getIndicatorsList({ page: 1, pageSize: 10 })
    if (response.code === 200) {
      indicatorsTotal.value = response.data.total
      indicatorsDataTotal.value = response.data.dataTotal
      indicatorsList.value = response.data.list
    } else {
      message.error(response.message || '加载指标数据失败')
    }
  } catch (error) {
    message.error('加载指标数据失败')
    console.error(error)
  } finally {
    indicatorsLoading.value = false
  }
}

// 处理阶段点击
const handleStageClick = (stage) => {
  selectedStage.value = stage.key
  handleStageChange(stage.key)
}

// 处理阶段变更
const handleStageChange = (value) => {
  loadProjectsData()
  // 更新子组件数据
  refreshModulesData()
}

// 处理区县变更
const handleDistrictChange = (value) => {
  loadProjectsData()
  // 更新子组件数据
  refreshModulesData()
}

// 刷新所有模块数据
const refreshModulesData = () => {
  if (competitionModuleRef.value) competitionModuleRef.value.loadCompetitionData()
  if (dynamicsModuleRef.value) dynamicsModuleRef.value.loadDynamicsData()
  if (achievementsModuleRef.value) achievementsModuleRef.value.loadAchievementsData()
  if (supervisionModuleRef.value) supervisionModuleRef.value.loadSupervisionTrackingData()
}

// 刷新所有数据
const refreshAllData = () => {
  globalLoading.value = true

  Promise.all([
    loadProjectsData(),
    loadTimelineData(),
    loadIndicatorsData(),
    loadStageStatistics()
  ]).then(() => {
    refreshModulesData()
    message.success('数据刷新成功')
  }).catch(() => {
    message.error('部分数据刷新失败')
  }).finally(() => {
    globalLoading.value = false
  })
}

// 查看项目详情
const showProjectDetails = async (stage) => {
  currentStage.value = stage
  stageProjectsLoading.value = true
  projectListVisible.value = true

  try {
    const response = await mockProjectDashboardApi.getProjectList({
      stage: stage.key,
      district: selectedDistrict.value
    })
    if (response.code === 200) {
      stageProjectsList.value = response.data
    } else {
      message.error(response.message || '加载项目列表失败')
    }
  } catch (error) {
    message.error('加载项目列表失败')
    console.error(error)
  } finally {
    stageProjectsLoading.value = false
  }
}

// 处理项目点击
const handleProjectClick = async (project: any) => {
  try {
    const response = await mockProjectDashboardApi.getProjectDetail(project.id)
    if (response.code === 200) {
      selectedProject.value = response.data
      projectDetailVisible.value = true
    } else {
      message.error(response.message || '获取项目详情失败')
    }
  } catch (error) {
    message.error('获取项目详情失败')
    console.error(error)
  }
}

// 处理项目选择变更
const handleProjectChange = (value: number) => {
  selectedProjectId.value = value
  message.info(`已选择项目ID: ${value}`)
  loadTimelineData()
}

// 处理里程碑点击
const handleMilestoneClick = (milestone: any) => {
  message.info(`点击了里程碑: ${milestone.name}`)

  // 构造指标数据并显示详情
  const indicatorData = {
    id: milestone.id,
    name: milestone.name,
    source: '项目里程碑',
    completedUnits: Math.floor(Math.random() * 20) + 5,
    completionRate: Math.floor(Math.random() * 50) + 50,
    details: milestone.description || '里程碑指标详情描述'
  }

  viewIndicatorDetail(indicatorData)
}

// 查看指标详情
const viewIndicatorDetail = async (indicator: any) => {
  try {
    const response = await mockProjectDashboardApi.getIndicatorDetail(indicator.id)
    if (response.code === 200) {
      selectedIndicator.value = response.data
      indicatorUnitsList.value = response.data.unitCompletions || []
      indicatorDetailVisible.value = true
    } else {
      // 如果API返回失败，使用传入的指标数据
      selectedIndicator.value = indicator
      indicatorDetailVisible.value = true

      // 加载单位完成情况
      indicatorUnitsLoading.value = true
      setTimeout(() => {
        indicatorUnitsList.value = [
          { id: 1, unitName: '渝中区政府办', isCompleted: true, score: 95 },
          { id: 2, unitName: '江北区政府办', isCompleted: true, score: 88 },
          { id: 3, unitName: '南岸区政府办', isCompleted: false, score: 65 },
          { id: 4, unitName: '九龙坡区政府办', isCompleted: true, score: 92 },
          { id: 5, unitName: '沙坪坝区政府办', isCompleted: false, score: 60 },
          { id: 6, unitName: '大渡口区政府办', isCompleted: true, score: 85 },
        ]
        indicatorUnitsLoading.value = false
      }, 500)
    }
  } catch (error) {
    message.error('获取指标详情失败')
    console.error(error)
  }
}

// 处理导出
const handleExport = async ({ key }: { key: string }) => {
  try {
    const exportType = key as 'pdf' | 'excel'
    message.loading(`正在导出${exportType.toUpperCase()}报告...`, 0)

    const response = await mockProjectDashboardApi.exportData(exportType, {
      stage: selectedStage.value,
      district: selectedDistrict.value
    })

    message.destroy()

    if (response.code === 200) {
      message.success(`${exportType.toUpperCase()}报告导出成功！`)
      // 这里可以添加下载逻辑
      console.log('下载链接:', response.data.downloadUrl)
    } else {
      message.error(response.message || '导出失败')
    }
  } catch (error) {
    message.destroy()
    message.error('导出失败')
    console.error(error)
  }
}

// 获取阶段标签
const getStageLabel = (stage: string) => {
  const stageMap: Record<string, string> = {
    'selection': '选拔遴选',
    'cultivation': '培育阶段',
    'establishment': '树立阶段',
    'promotion': '推广阶段'
  }
  return stageMap[stage] || '未知阶段'
}

// 获取阶段颜色
const getStageColor = (stage: string) => {
  const colorMap: Record<string, string> = {
    'selection': '#1890ff',
    'cultivation': '#52c41a',
    'establishment': '#faad14',
    'promotion': '#722ed1'
  }
  return colorMap[stage] || '#d9d9d9'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待开始',
    'inProgress': '进行中',
    'completed': '已完成',
    'delayed': '已延期'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'pending': 'blue',
    'inProgress': 'processing',
    'completed': 'success',
    'delayed': 'warning'
  }
  return colorMap[status] || 'default'
}

// 格式化日期
const formatDate = (date: string | null | undefined) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 初始化
onMounted(() => {
  refreshAllData()
})
</script>

<style scoped lang="scss">
.project-dashboard {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 0;

  .dashboard-header {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 100;

    .header-content {
      max-width: 1600px;
      margin: 0 auto;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          line-height: 1.2;
        }

        .page-breadcrumb {
          margin: 0;
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }

  .stage-overview-section {
    max-width: 1600px;
    margin: 24px auto 0;
    padding: 0 24px;

    .stage-overview-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }

    .stage-card {
      background: white;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }

      .stage-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .stage-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 20px;
        }

        .stage-info {
          .stage-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
          }

          .stage-desc {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }

      .stage-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .stat-item {
          text-align: center;

          .stat-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
            cursor: pointer;

            &:hover {
              color: #1890ff;
            }
          }

          .stat-label {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .selection .stage-icon {
      background-color: rgba(24, 144, 255, 0.1);
      color: #1890ff;
    }

    .cultivation .stage-icon {
      background-color: rgba(82, 196, 26, 0.1);
      color: #52c41a;
    }

    .establishment .stage-icon {
      background-color: rgba(250, 173, 20, 0.1);
      color: #faad14;
    }

    .promotion .stage-icon {
      background-color: rgba(114, 46, 209, 0.1);
      color: #722ed1;
    }
  }

  .main-content {
    max-width: 1600px;
    margin: 24px auto 0;
    padding: 0 24px;
  }

  .flow-section {
    margin-bottom: 24px;

    .flow-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }

    .indicators-summary {
      display: flex;
      justify-content: space-around;
      margin-bottom: 16px;

      .indicator-stat {
        text-align: center;

        .indicator-value {
          font-size: 24px;
          font-weight: 600;
          color: #1890ff;
        }

        .indicator-label {
          font-size: 14px;
          color: #8c8c8c;
          margin-top: 8px;
        }
      }
    }
  }

  .timeline-section {
    max-width: 1600px;
    margin: 24px auto;
    padding: 0 24px;

    .timeline-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }
  }

  .global-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .project-detail {
    .milestone-item {
      .milestone-name {
        font-weight: 600;
        margin-bottom: 4px;
      }

      .milestone-desc {
        color: #666;
        margin-bottom: 4px;
      }

      .milestone-date {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>