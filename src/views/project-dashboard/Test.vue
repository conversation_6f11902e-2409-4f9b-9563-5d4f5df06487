<template>
  <div class="project-dashboard-test">
    <a-card title="选育树推项目看板测试">
      <div class="test-section">
        <h3>功能测试</h3>
        <a-space direction="vertical" style="width: 100%">
          <a-alert
            :message="testStatus.message"
            :type="testStatus.type"
            show-icon
          />
          
          <a-space>
            <a-button @click="testDataLoading" type="primary" :loading="testing">
              测试数据加载
            </a-button>
            <a-button @click="testComponentRender">
              测试组件渲染
            </a-button>
            <a-button @click="testResponsive">
              测试响应式
            </a-button>
            <a-button @click="goToDashboard" type="link">
              前往项目看板
            </a-button>
          </a-space>
          
          <a-divider />
          
          <div class="data-preview" v-if="sampleData">
            <h4>数据预览</h4>
            <a-descriptions :column="2" bordered size="small">
              <a-descriptions-item label="项目总数">
                {{ sampleData.projectOverview?.stageStats?.[0]?.count || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="指标数量">
                {{ sampleData.indicatorAnalysis?.totalIndicators || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="数据资源">
                {{ sampleData.indicatorAnalysis?.totalDataResources || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="培育单位">
                {{ sampleData.rankings?.cultivationRankings?.length || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="督办事项">
                {{ sampleData.rankings?.supervisionItems?.length || 0 }}
              </a-descriptions-item>
              <a-descriptions-item label="区县数量">
                {{ sampleData.districtViews?.length || 0 }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
          
          <div class="component-test" v-if="showComponentTest">
            <h4>组件测试</h4>
            <ProjectOverview
              :data="sampleData?.projectOverview"
              :loading="false"
              @stage-click="handleStageClick"
              @project-click="handleProjectClick"
            />
          </div>
        </a-space>
      </div>
      
      <a-divider />
      
      <div class="test-results">
        <h3>测试结果</h3>
        <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
          <a-tag :color="result.success ? 'green' : 'red'">
            {{ result.success ? '通过' : '失败' }}
          </a-tag>
          <span class="test-name">{{ result.testName }}</span>
          <span class="test-message">{{ result.message }}</span>
          <span class="test-time">{{ result.timestamp }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import ProjectOverview from './components/ProjectOverview.vue'
import type { DashboardData, ProjectStageStats, Project } from './types'
import { mockDataService } from './mock/data'

const router = useRouter()

// 响应式数据
const testing = ref(false)
const showComponentTest = ref(false)
const sampleData = ref<DashboardData | null>(null)
const testStatus = ref({
  type: 'info' as 'success' | 'info' | 'warning' | 'error',
  message: '准备进行项目看板测试...'
})

const testResults = ref<Array<{
  testName: string
  success: boolean
  message: string
  timestamp: string
}>>([])

// 方法
const addTestResult = (testName: string, success: boolean, message: string) => {
  testResults.value.unshift({
    testName,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
}

const testDataLoading = async () => {
  testing.value = true
  try {
    testStatus.value = {
      type: 'info',
      message: '正在测试数据加载...'
    }
    
    const startTime = performance.now()
    const response = await mockDataService.fetchDashboardData()
    const endTime = performance.now()
    
    if (response.code === 200 && response.data) {
      sampleData.value = response.data
      testStatus.value = {
        type: 'success',
        message: `数据加载成功，耗时 ${Math.round(endTime - startTime)}ms`
      }
      addTestResult('数据加载测试', true, `成功加载数据，耗时 ${Math.round(endTime - startTime)}ms`)
    } else {
      throw new Error('数据格式错误')
    }
  } catch (error) {
    testStatus.value = {
      type: 'error',
      message: `数据加载失败: ${error}`
    }
    addTestResult('数据加载测试', false, `加载失败: ${error}`)
  } finally {
    testing.value = false
  }
}

const testComponentRender = () => {
  if (!sampleData.value) {
    message.warning('请先测试数据加载')
    return
  }
  
  try {
    showComponentTest.value = true
    addTestResult('组件渲染测试', true, 'ProjectOverview组件渲染成功')
    message.success('组件渲染测试通过')
  } catch (error) {
    addTestResult('组件渲染测试', false, `组件渲染失败: ${error}`)
    message.error('组件渲染测试失败')
  }
}

const testResponsive = () => {
  const breakpoints = [
    { name: '大屏', width: 1600 },
    { name: '中屏', width: 1200 },
    { name: '小屏', width: 768 },
    { name: '手机', width: 480 }
  ]
  
  const currentWidth = window.innerWidth
  const currentBreakpoint = breakpoints.find(bp => currentWidth >= bp.width)?.name || '手机'
  
  addTestResult('响应式测试', true, `当前屏幕宽度: ${currentWidth}px, 断点: ${currentBreakpoint}`)
  message.success(`响应式测试完成，当前断点: ${currentBreakpoint}`)
}

const goToDashboard = () => {
  router.push('/project-dashboard')
}

const handleStageClick = (stage: ProjectStageStats) => {
  addTestResult('阶段点击测试', true, `点击了${stage.stageName}阶段`)
  message.info(`测试: 点击了${stage.stageName}阶段`)
}

const handleProjectClick = (project: Project) => {
  addTestResult('项目点击测试', true, `点击了项目: ${project.name}`)
  message.info(`测试: 点击了项目: ${project.name}`)
}

// 生命周期
onMounted(() => {
  addTestResult('初始化', true, '项目看板测试工具已启动')
})
</script>

<style scoped>
.project-dashboard-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
}

.data-preview {
  margin-top: 16px;
}

.component-test {
  margin-top: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.test-results {
  .test-result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    
    .test-name {
      font-weight: 500;
      min-width: 120px;
    }
    
    .test-message {
      flex: 1;
      color: #666;
    }
    
    .test-time {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
