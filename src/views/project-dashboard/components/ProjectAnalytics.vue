<template>
    <div class="project-analytics">
        <div class="analytics-header">
            <h3 class="analytics-title">项目分析</h3>
            <div class="analytics-controls">
                <a-space>
                    <a-select v-model:value="selectedTimeRange" style="width: 120px">
                        <a-select-option value="7d">近7天</a-select-option>
                        <a-select-option value="30d">近30天</a-select-option>
                        <a-select-option value="90d">近90天</a-select-option>
                        <a-select-option value="1y">近1年</a-select-option>
                    </a-select>
                    <a-button @click="handleRefresh">
                        <template #icon><reload-outlined /></template>
                        刷新
                    </a-button>
                    <a-button @click="handleExport">
                        <template #icon><download-outlined /></template>
                        导出
                    </a-button>
                </a-space>
            </div>
        </div>

        <div class="analytics-content" v-loading="loading">
            <a-row :gutter="[16, 16]">
                <!-- 项目趋势分析 -->
                <a-col :span="24">
                    <a-card title="项目趋势分析" class="trend-card">
                        <div class="trend-chart" ref="trendChartRef" style="height: 300px;"></div>
                    </a-card>
                </a-col>

                <!-- 阶段分布分析 -->
                <a-col :span="12">
                    <a-card title="阶段分布分析" class="stage-distribution-card">
                        <div class="stage-chart" ref="stageChartRef" style="height: 250px;"></div>
                    </a-card>
                </a-col>

                <!-- 项目状态分析 -->
                <a-col :span="12">
                    <a-card title="项目状态分析" class="status-analysis-card">
                        <div class="status-chart" ref="statusChartRef" style="height: 250px;"></div>
                    </a-card>
                </a-col>

                <!-- 绩效指标分析 -->
                <a-col :span="24">
                    <a-card title="绩效指标分析" class="performance-card">
                        <a-tabs v-model:activeKey="performanceTab">
                            <a-tab-pane key="efficiency" tab="效率指标">
                                <div class="metrics-grid">
                                    <div class="metric-item">
                                        <div class="metric-header">
                                            <span class="metric-title">平均完成时间</span>
                                            <a-tooltip title="项目从开始到完成的平均时间">
                                                <question-circle-outlined />
                                            </a-tooltip>
                                        </div>
                                        <div class="metric-value">{{ analyticsData?.avgCompletionTime || 0 }}天</div>
                                        <div class="metric-trend"
                                            :class="getTrendClass(analyticsData?.completionTimeTrend)">
                                            <arrow-up-outlined v-if="analyticsData?.completionTimeTrend > 0" />
                                            <arrow-down-outlined v-else-if="analyticsData?.completionTimeTrend < 0" />
                                            <minus-outlined v-else />
                                            {{ Math.abs(analyticsData?.completionTimeTrend || 0) }}%
                                        </div>
                                    </div>

                                    <div class="metric-item">
                                        <div class="metric-header">
                                            <span class="metric-title">项目成功率</span>
                                            <a-tooltip title="成功完成的项目占总项目的比例">
                                                <question-circle-outlined />
                                            </a-tooltip>
                                        </div>
                                        <div class="metric-value">{{ analyticsData?.successRate || 0 }}%</div>
                                        <div class="metric-trend"
                                            :class="getTrendClass(analyticsData?.successRateTrend)">
                                            <arrow-up-outlined v-if="analyticsData?.successRateTrend > 0" />
                                            <arrow-down-outlined v-else-if="analyticsData?.successRateTrend < 0" />
                                            <minus-outlined v-else />
                                            {{ Math.abs(analyticsData?.successRateTrend || 0) }}%
                                        </div>
                                    </div>

                                    <div class="metric-item">
                                        <div class="metric-header">
                                            <span class="metric-title">资源利用率</span>
                                            <a-tooltip title="项目资源的有效利用程度">
                                                <question-circle-outlined />
                                            </a-tooltip>
                                        </div>
                                        <div class="metric-value">{{ analyticsData?.resourceUtilization || 0 }}%</div>
                                        <div class="metric-trend" :class="getTrendClass(analyticsData?.resourceTrend)">
                                            <arrow-up-outlined v-if="analyticsData?.resourceTrend > 0" />
                                            <arrow-down-outlined v-else-if="analyticsData?.resourceTrend < 0" />
                                            <minus-outlined v-else />
                                            {{ Math.abs(analyticsData?.resourceTrend || 0) }}%
                                        </div>
                                    </div>

                                    <div class="metric-item">
                                        <div class="metric-header">
                                            <span class="metric-title">质量评分</span>
                                            <a-tooltip title="项目质量的综合评分">
                                                <question-circle-outlined />
                                            </a-tooltip>
                                        </div>
                                        <div class="metric-value">{{ analyticsData?.qualityScore || 0 }}分</div>
                                        <div class="metric-trend" :class="getTrendClass(analyticsData?.qualityTrend)">
                                            <arrow-up-outlined v-if="analyticsData?.qualityTrend > 0" />
                                            <arrow-down-outlined v-else-if="analyticsData?.qualityTrend < 0" />
                                            <minus-outlined v-else />
                                            {{ Math.abs(analyticsData?.qualityTrend || 0) }}%
                                        </div>
                                    </div>
                                </div>
                            </a-tab-pane>

                            <a-tab-pane key="impact" tab="影响力指标">
                                <div class="impact-metrics">
                                    <a-row :gutter="16">
                                        <a-col :span="8">
                                            <a-card class="impact-card">
                                                <a-statistic title="覆盖单位数" :value="analyticsData?.coverageUnits || 0"
                                                    suffix="个" :value-style="{ color: '#1890ff' }" />
                                            </a-card>
                                        </a-col>
                                        <a-col :span="8">
                                            <a-card class="impact-card">
                                                <a-statistic title="受益人数" :value="analyticsData?.beneficiaries || 0"
                                                    suffix="人" :value-style="{ color: '#52c41a' }" />
                                            </a-card>
                                        </a-col>
                                        <a-col :span="8">
                                            <a-card class="impact-card">
                                                <a-statistic title="社会影响力" :value="analyticsData?.socialImpact || 0"
                                                    suffix="分" :value-style="{ color: '#722ed1' }" />
                                            </a-card>
                                        </a-col>
                                    </a-row>

                                    <div class="impact-chart" ref="impactChartRef"
                                        style="height: 200px; margin-top: 20px;">
                                    </div>
                                </div>
                            </a-tab-pane>

                            <a-tab-pane key="comparison" tab="对比分析">
                                <div class="comparison-section">
                                    <div class="comparison-controls">
                                        <a-space>
                                            <span>对比维度:</span>
                                            <a-select v-model:value="comparisonDimension" style="width: 150px">
                                                <a-select-option value="stage">按阶段对比</a-select-option>
                                                <a-select-option value="department">按部门对比</a-select-option>
                                                <a-select-option value="time">按时间对比</a-select-option>
                                            </a-select>
                                        </a-space>
                                    </div>
                                    <div class="comparison-chart" ref="comparisonChartRef"
                                        style="height: 300px; margin-top: 20px;"></div>
                                </div>
                            </a-tab-pane>
                        </a-tabs>
                    </a-card>
                </a-col>

                <!-- 预测分析 -->
                <a-col :span="24">
                    <a-card title="预测分析" class="prediction-card">
                        <div class="prediction-content">
                            <div class="prediction-summary">
                                <a-alert :message="predictionSummary.title" :description="predictionSummary.description"
                                    :type="predictionSummary.type" show-icon style="margin-bottom: 20px;" />
                            </div>
                            <div class="prediction-chart" ref="predictionChartRef" style="height: 250px;"></div>
                        </div>
                    </a-card>
                </a-col>
            </a-row>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
    ReloadOutlined,
    DownloadOutlined,
    QuestionCircleOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    MinusOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    analyticsData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'refresh': []
    'export': []
}>()

// 响应式数据
const selectedTimeRange = ref('30d')
const performanceTab = ref('efficiency')
const comparisonDimension = ref('stage')

// 图表引用
const trendChartRef = ref<HTMLDivElement>()
const stageChartRef = ref<HTMLDivElement>()
const statusChartRef = ref<HTMLDivElement>()
const impactChartRef = ref<HTMLDivElement>()
const comparisonChartRef = ref<HTMLDivElement>()
const predictionChartRef = ref<HTMLDivElement>()

// 计算属性
const predictionSummary = computed(() => {
    const data = props.analyticsData?.prediction
    if (!data) return { title: '暂无预测数据', description: '', type: 'info' }

    if (data.trend === 'positive') {
        return {
            title: '项目发展趋势良好',
            description: `预计未来${data.period}内项目完成率将提升${data.improvement}%，建议继续保持当前策略。`,
            type: 'success'
        }
    } else if (data.trend === 'negative') {
        return {
            title: '项目发展存在风险',
            description: `预计未来${data.period}内可能面临${data.risk}的挑战，建议及时调整策略。`,
            type: 'warning'
        }
    } else {
        return {
            title: '项目发展趋势平稳',
            description: `预计未来${data.period}内项目将保持稳定发展，可考虑优化提升。`,
            type: 'info'
        }
    }
})

// 方法
const handleRefresh = () => {
    emit('refresh')
    message.success('数据已刷新')
}

const handleExport = () => {
    emit('export')
    message.success('正在导出分析报告...')
}

const getTrendClass = (trend: number) => {
    if (trend > 0) return 'trend-up'
    if (trend < 0) return 'trend-down'
    return 'trend-stable'
}

// 图表初始化方法（模拟）
const initTrendChart = () => {
    if (!trendChartRef.value) return
    // 这里应该使用实际的图表库如ECharts初始化趋势图
    console.log('初始化趋势图表')
}

const initStageChart = () => {
    if (!stageChartRef.value) return
    // 初始化阶段分布饼图
    console.log('初始化阶段分布图表')
}

const initStatusChart = () => {
    if (!statusChartRef.value) return
    // 初始化状态分析图表
    console.log('初始化状态分析图表')
}

const initImpactChart = () => {
    if (!impactChartRef.value) return
    // 初始化影响力图表
    console.log('初始化影响力图表')
}

const initComparisonChart = () => {
    if (!comparisonChartRef.value) return
    // 初始化对比分析图表
    console.log('初始化对比分析图表')
}

const initPredictionChart = () => {
    if (!predictionChartRef.value) return
    // 初始化预测分析图表
    console.log('初始化预测分析图表')
}

// 生命周期
onMounted(() => {
    nextTick(() => {
        initTrendChart()
        initStageChart()
        initStatusChart()
        initImpactChart()
        initComparisonChart()
        initPredictionChart()
    })
})
</script>

<style scoped lang="scss">
.project-analytics {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .analytics-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .analytics-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .analytics-content {
        flex: 1;
        overflow-y: auto;

        .trend-card,
        .stage-distribution-card,
        .status-analysis-card,
        .performance-card,
        .prediction-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            .ant-card-head {
                border-bottom: 1px solid #f0f0f0;

                .ant-card-head-title {
                    font-weight: 600;
                    color: #262626;
                }
            }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;

            .metric-item {
                padding: 20px;
                background: #fafafa;
                border-radius: 8px;
                border: 1px solid #f0f0f0;

                .metric-header {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-bottom: 12px;

                    .metric-title {
                        font-size: 14px;
                        color: #666;
                        font-weight: 500;
                    }

                    .anticon {
                        color: #999;
                        font-size: 12px;
                    }
                }

                .metric-value {
                    font-size: 24px;
                    font-weight: 600;
                    color: #262626;
                    margin-bottom: 8px;
                }

                .metric-trend {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-size: 12px;
                    font-weight: 500;

                    &.trend-up {
                        color: #52c41a;
                    }

                    &.trend-down {
                        color: #f5222d;
                    }

                    &.trend-stable {
                        color: #999;
                    }
                }
            }
        }

        .impact-metrics {
            .impact-card {
                text-align: center;
                border-radius: 8px;
            }
        }

        .comparison-section {
            .comparison-controls {
                margin-bottom: 20px;
                padding: 16px;
                background: #fafafa;
                border-radius: 8px;
                border: 1px solid #f0f0f0;
            }
        }

        .prediction-content {
            .prediction-summary {
                margin-bottom: 20px;
            }
        }
    }
}
</style>