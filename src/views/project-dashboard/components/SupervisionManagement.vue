<template>
    <div class="supervision-management">
        <div class="supervision-header">
            <h3 class="supervision-title">督办管理</h3>
            <div class="supervision-actions">
                <a-button type="primary" @click="handleBatchSupervision">
                    <template #icon><notification-outlined /></template>
                    批量督办
                </a-button>
            </div>
        </div>

        <div class="supervision-stats">
            <a-row :gutter="16">
                <a-col :span="6">
                    <a-statistic title="待办事项" :value="supervisionData?.totalItems || 0"
                        :value-style="{ color: '#1890ff' }" />
                </a-col>
                <a-col :span="6">
                    <a-statistic title="逾期事项" :value="supervisionData?.overdueItems || 0"
                        :value-style="{ color: '#f5222d' }" />
                </a-col>
                <a-col :span="6">
                    <a-statistic title="今日到期" :value="supervisionData?.todayDueItems || 0"
                        :value-style="{ color: '#faad14' }" />
                </a-col>
                <a-col :span="6">
                    <a-statistic title="完成率" :value="completionRate" suffix="%" :value-style="{ color: '#52c41a' }" />
                </a-col>
            </a-row>
        </div>

        <div class="supervision-content" v-loading="loading">
            <div class="content-filters">
                <a-space>
                    <a-select v-model:value="selectedStatus" placeholder="状态筛选" style="width: 120px"
                        @change="handleStatusChange">
                        <a-select-option value="">全部状态</a-select-option>
                        <a-select-option value="pending">待办</a-select-option>
                        <a-select-option value="in_progress">进行中</a-select-option>
                        <a-select-option value="completed">已完成</a-select-option>
                        <a-select-option value="overdue">逾期</a-select-option>
                    </a-select>

                    <a-select v-model:value="selectedPriority" placeholder="优先级" style="width: 120px"
                        @change="handlePriorityChange">
                        <a-select-option value="">全部优先级</a-select-option>
                        <a-select-option value="high">高</a-select-option>
                        <a-select-option value="medium">中</a-select-option>
                        <a-select-option value="low">低</a-select-option>
                    </a-select>

                    <a-input-search v-model:value="searchKeyword" placeholder="搜索单位或指标" style="width: 200px"
                        @search="handleSearch" />
                </a-space>
            </div>

            <div class="supervision-list">
                <div v-for="item in filteredSupervisionItems" :key="item.id" class="supervision-item"
                    :class="[item.priority, item.status]">
                    <div class="item-header">
                        <div class="item-info">
                            <span class="item-unit">{{ item.unitName }}</span>
                            <div class="item-tags">
                                <a-tag :color="getStatusColor(item.status)">{{ getStatusText(item.status) }}</a-tag>
                                <a-tag :color="getPriorityColor(item.priority)">{{ getPriorityText(item.priority)
                                    }}</a-tag>
                            </div>
                        </div>
                        <div class="item-deadline">
                            <clock-circle-outlined />
                            <span class="deadline-text">{{ formatDeadline(item.deadline) }}</span>
                        </div>
                    </div>

                    <div class="item-content">
                        <div class="item-indicator">
                            <span class="indicator-label">指标:</span>
                            <span class="indicator-name">{{ item.indicatorName }}</span>
                        </div>
                        <div class="item-description">{{ item.description }}</div>
                    </div>

                    <div class="item-progress" v-if="item.status === 'in_progress'">
                        <div class="progress-info">
                            <span class="progress-label">完成进度:</span>
                            <span class="progress-value">{{ item.progress || 0 }}%</span>
                        </div>
                        <a-progress :percent="item.progress || 0" :stroke-color="getProgressColor(item.progress || 0)"
                            size="small" />
                    </div>

                    <div class="item-actions">
                        <a-space>
                            <a-button size="small" type="primary" @click="handleSupervisionSend(item)"
                                :disabled="item.status === 'completed'">
                                <template #icon><notification-outlined /></template>
                                督办
                            </a-button>

                            <a-button size="small" @click="handleUpdateStatus(item)"
                                :disabled="item.status === 'completed'">
                                更新状态
                            </a-button>

                            <a-button size="small" type="link" @click="handleViewDetail(item)">
                                查看详情
                            </a-button>
                        </a-space>
                    </div>
                </div>

                <div v-if="!filteredSupervisionItems.length && !loading" class="empty-state">
                    <a-empty description="暂无督办事项" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
    NotificationOutlined,
    ClockCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Props定义
interface Props {
    supervisionData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'supervision-send': [item: any]
    'item-update': [item: any]
    'export': [data: any]
}>()

// 响应式数据
const selectedStatus = ref('')
const selectedPriority = ref('')
const searchKeyword = ref('')

// 计算属性
const completionRate = computed(() => {
    if (!props.supervisionData?.totalItems) return 0
    const completed = props.supervisionData.completedItems || 0
    return Math.round((completed / props.supervisionData.totalItems) * 100)
})

const filteredSupervisionItems = computed(() => {
    if (!props.supervisionData?.supervisionItems) return []

    let filtered = props.supervisionData.supervisionItems

    if (selectedStatus.value) {
        filtered = filtered.filter((item: any) => item.status === selectedStatus.value)
    }

    if (selectedPriority.value) {
        filtered = filtered.filter((item: any) => item.priority === selectedPriority.value)
    }

    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        filtered = filtered.filter((item: any) =>
            item.unitName.toLowerCase().includes(keyword) ||
            item.indicatorName.toLowerCase().includes(keyword)
        )
    }

    return filtered
})

// 方法
const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'pending': 'orange',
        'in_progress': 'blue',
        'completed': 'green',
        'overdue': 'red'
    }
    return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'pending': '待办',
        'in_progress': '进行中',
        'completed': '已完成',
        'overdue': '逾期'
    }
    return textMap[status] || status
}

const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
        'high': 'red',
        'medium': 'orange',
        'low': 'green'
    }
    return colorMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
    const textMap: Record<string, string> = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
    }
    return textMap[priority] || priority
}

const getProgressColor = (progress: number) => {
    if (progress >= 80) return '#52c41a'
    if (progress >= 60) return '#faad14'
    if (progress >= 40) return '#ff7a45'
    return '#f5222d'
}

const formatDeadline = (deadline: string) => {
    const date = new Date(deadline)
    const now = new Date()
    const diffTime = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays < 0) {
        return `逾期${Math.abs(diffDays)}天`
    } else if (diffDays === 0) {
        return '今日到期'
    } else if (diffDays <= 3) {
        return `${diffDays}天后到期`
    } else {
        return date.toLocaleDateString()
    }
}

const handleStatusChange = (status: string) => {
    selectedStatus.value = status
}

const handlePriorityChange = (priority: string) => {
    selectedPriority.value = priority
}

const handleSearch = (keyword: string) => {
    searchKeyword.value = keyword
}

const handleSupervisionSend = (item: any) => {
    emit('supervision-send', item)
}

const handleUpdateStatus = (item: any) => {
    emit('item-update', item)
}

const handleViewDetail = (item: any) => {
    message.info(`查看督办详情：${item.unitName}`)
}

const handleBatchSupervision = () => {
    const pendingItems = filteredSupervisionItems.value.filter(item => item.status === 'pending')
    if (pendingItems.length === 0) {
        message.warning('没有待督办的事项')
        return
    }
    message.success(`批量督办${pendingItems.length}个事项`)
}
</script>

<style scoped lang="scss">
.supervision-management {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .supervision-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .supervision-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .supervision-stats {
        background: #f6f8fa;
        padding: 16px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .supervision-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .content-filters {
            margin-bottom: 16px;
        }

        .supervision-list {
            flex: 1;
            overflow-y: auto;

            .supervision-item {
                padding: 16px;
                margin-bottom: 12px;
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                }

                &.high {
                    border-left: 4px solid #f5222d;
                }

                &.medium {
                    border-left: 4px solid #faad14;
                }

                &.low {
                    border-left: 4px solid #52c41a;
                }

                &.overdue {
                    background: #fff2f0;
                    border-color: #ffccc7;
                }

                .item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 12px;

                    .item-info {
                        flex: 1;

                        .item-unit {
                            display: block;
                            font-size: 16px;
                            font-weight: 500;
                            color: #262626;
                            margin-bottom: 8px;
                        }

                        .item-tags {
                            display: flex;
                            gap: 8px;
                        }
                    }

                    .item-deadline {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        color: #666;
                        font-size: 12px;

                        .deadline-text {
                            white-space: nowrap;
                        }
                    }
                }

                .item-content {
                    margin-bottom: 12px;

                    .item-indicator {
                        margin-bottom: 8px;

                        .indicator-label {
                            color: #666;
                            font-size: 12px;
                        }

                        .indicator-name {
                            color: #262626;
                            font-weight: 500;
                            margin-left: 4px;
                        }
                    }

                    .item-description {
                        color: #666;
                        font-size: 13px;
                        line-height: 1.5;
                    }
                }

                .item-progress {
                    margin-bottom: 12px;

                    .progress-info {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 4px;

                        .progress-label {
                            font-size: 12px;
                            color: #666;
                        }

                        .progress-value {
                            font-size: 12px;
                            font-weight: 500;
                            color: #262626;
                        }
                    }
                }

                .item-actions {
                    display: flex;
                    justify-content: flex-end;
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .supervision-management {
        padding: 16px;

        .supervision-content {
            .content-filters {
                .ant-space {
                    flex-direction: column;
                    width: 100%;

                    .ant-space-item {
                        width: 100%;

                        .ant-select,
                        .ant-input-search {
                            width: 100% !important;
                        }
                    }
                }
            }

            .supervision-list {
                .supervision-item {
                    padding: 12px;

                    .item-header {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 8px;
                    }

                    .item-actions {
                        justify-content: flex-start;

                        .ant-space {
                            flex-wrap: wrap;
                        }
                    }
                }
            }
        }
    }
}
</style>