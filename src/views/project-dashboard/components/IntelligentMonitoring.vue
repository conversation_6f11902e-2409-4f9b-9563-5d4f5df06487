<template>
    <div class="intelligent-monitoring">
        <div class="monitoring-header">
            <h3 class="monitoring-title">智能监控体系</h3>
            <div class="monitoring-actions">
                <a-button @click="handleRefreshMonitoring">
                    <template #icon><reload-outlined /></template>
                    刷新监控
                </a-button>
            </div>
        </div>

        <div class="monitoring-content" v-loading="loading">
            <div class="monitoring-overview">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-statistic title="监控问题" :value="monitoringData?.totalProblems || 0"
                            :value-style="{ color: '#f5222d' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="已解决" :value="monitoringData?.resolvedProblems || 0"
                            :value-style="{ color: '#52c41a' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="处理中" :value="monitoringData?.processingProblems || 0"
                            :value-style="{ color: '#faad14' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="解决率" :value="resolutionRate" suffix="%"
                            :value-style="{ color: '#1890ff' }" />
                    </a-col>
                </a-row>
            </div>

            <div class="monitoring-tabs">
                <a-tabs v-model:activeKey="activeMonitoringTab" @change="handleMonitoringTabChange">
                    <a-tab-pane key="problems" tab="问题督查闭环">
                        <div class="problems-section">
                            <div class="problems-filters">
                                <a-space>
                                    <a-select v-model:value="selectedProblemStatus" placeholder="状态筛选"
                                        style="width: 120px" @change="handleProblemStatusChange">
                                        <a-select-option value="">全部状态</a-select-option>
                                        <a-select-option value="new">新发现</a-select-option>
                                        <a-select-option value="processing">处理中</a-select-option>
                                        <a-select-option value="resolved">已解决</a-select-option>
                                        <a-select-option value="closed">已关闭</a-select-option>
                                    </a-select>

                                    <a-select v-model:value="selectedProblemLevel" placeholder="严重程度"
                                        style="width: 120px" @change="handleProblemLevelChange">
                                        <a-select-option value="">全部级别</a-select-option>
                                        <a-select-option value="critical">严重</a-select-option>
                                        <a-select-option value="high">高</a-select-option>
                                        <a-select-option value="medium">中</a-select-option>
                                        <a-select-option value="low">低</a-select-option>
                                    </a-select>
                                </a-space>
                            </div>

                            <div class="problems-list">
                                <div v-for="problem in filteredProblems" :key="problem.id" class="problem-item"
                                    :class="[problem.level, problem.status]" @click="handleProblemClick(problem)">
                                    <div class="problem-header">
                                        <div class="problem-info">
                                            <span class="problem-title">{{ problem.title }}</span>
                                            <div class="problem-tags">
                                                <a-tag :color="getProblemLevelColor(problem.level)">{{
                                                    getProblemLevelText(problem.level) }}</a-tag>
                                                <a-tag :color="getProblemStatusColor(problem.status)">{{
                                                    getProblemStatusText(problem.status) }}</a-tag>
                                            </div>
                                        </div>
                                        <div class="problem-time">
                                            <clock-circle-outlined />
                                            <span class="time-text">{{ formatTime(problem.createTime) }}</span>
                                        </div>
                                    </div>

                                    <div class="problem-content">
                                        <div class="problem-description">{{ problem.description }}</div>
                                        <div class="problem-details">
                                            <div class="detail-item">
                                                <span class="detail-label">涉及单位:</span>
                                                <span class="detail-value">{{ problem.affectedUnit }}</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">发现方式:</span>
                                                <span class="detail-value">{{ problem.discoveryMethod }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="problem-progress" v-if="problem.status === 'processing'">
                                        <div class="progress-info">
                                            <span class="progress-label">处理进度:</span>
                                            <span class="progress-value">{{ problem.progress || 0 }}%</span>
                                        </div>
                                        <a-progress :percent="problem.progress || 0"
                                            :stroke-color="getProgressColor(problem.progress || 0)" size="small" />
                                    </div>

                                    <div class="problem-actions">
                                        <a-space>
                                            <a-button size="small" type="primary"
                                                @click.stop="handleAssignProblem(problem)"
                                                v-if="problem.status === 'new'">
                                                分配处理
                                            </a-button>

                                            <a-button size="small" @click.stop="handleUpdateProblem(problem)"
                                                v-if="problem.status === 'processing'">
                                                更新进度
                                            </a-button>

                                            <a-button size="small" type="link" @click.stop="handleViewProblem(problem)">
                                                查看详情
                                            </a-button>
                                        </a-space>
                                    </div>
                                </div>

                                <div v-if="!filteredProblems.length && !loading" class="empty-state">
                                    <a-empty description="暂无问题记录" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="alerts" tab="预警提醒">
                        <div class="alerts-section">
                            <div class="alerts-list">
                                <div v-for="alert in monitoringData?.alerts || []" :key="alert.id" class="alert-item"
                                    :class="alert.level" @click="handleAlertClick(alert)">
                                    <div class="alert-header">
                                        <div class="alert-info">
                                            <span class="alert-title">{{ alert.title }}</span>
                                            <div class="alert-tags">
                                                <a-tag :color="getAlertLevelColor(alert.level)">{{
                                                    getAlertLevelText(alert.level) }}</a-tag>
                                                <a-tag :color="alert.isActive ? 'processing' : 'default'">
                                                    {{ alert.isActive ? '活跃' : '已处理' }}
                                                </a-tag>
                                            </div>
                                        </div>
                                        <div class="alert-time">
                                            <clock-circle-outlined />
                                            <span class="time-text">{{ formatTime(alert.createTime) }}</span>
                                        </div>
                                    </div>

                                    <div class="alert-content">
                                        <div class="alert-description">{{ alert.description }}</div>
                                        <div class="alert-metrics">
                                            <div class="metric-item">
                                                <span class="metric-label">触发条件:</span>
                                                <span class="metric-value">{{ alert.triggerCondition }}</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">影响范围:</span>
                                                <span class="metric-value">{{ alert.impactScope }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="alert-actions">
                                        <a-space>
                                            <a-button size="small" type="primary"
                                                @click.stop="handleProcessAlert(alert)" v-if="alert.isActive">
                                                立即处理
                                            </a-button>
                                            <a-button size="small" type="link" @click.stop="handleViewAlert(alert)">
                                                查看详情
                                            </a-button>
                                        </a-space>
                                    </div>
                                </div>

                                <div v-if="!monitoringData?.alerts?.length && !loading" class="empty-state">
                                    <a-empty description="暂无预警信息" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="reports" tab="监控报告">
                        <div class="reports-section">
                            <div class="reports-filters">
                                <a-space>
                                    <a-select v-model:value="selectedReportType" placeholder="报告类型" style="width: 150px"
                                        @change="handleReportTypeChange">
                                        <a-select-option value="">全部类型</a-select-option>
                                        <a-select-option value="daily">日报</a-select-option>
                                        <a-select-option value="weekly">周报</a-select-option>
                                        <a-select-option value="monthly">月报</a-select-option>
                                    </a-select>
                                    <a-button @click="handleGenerateReport">生成报告</a-button>
                                </a-space>
                            </div>

                            <div class="reports-list">
                                <div v-for="report in filteredReports" :key="report.id" class="report-item"
                                    @click="handleReportClick(report)">
                                    <div class="report-header">
                                        <div class="report-info">
                                            <span class="report-title">{{ report.title }}</span>
                                            <div class="report-tags">
                                                <a-tag :color="getReportTypeColor(report.type)">{{ report.type
                                                    }}</a-tag>
                                                <a-tag :color="report.isGenerated ? 'success' : 'processing'">
                                                    {{ report.isGenerated ? '已生成' : '生成中' }}
                                                </a-tag>
                                            </div>
                                        </div>
                                        <div class="report-time">
                                            <span class="time-text">{{ formatDate(report.createTime) }}</span>
                                        </div>
                                    </div>

                                    <div class="report-content">
                                        <div class="report-summary">{{ report.summary }}</div>
                                        <div class="report-stats">
                                            <div class="stat-item">
                                                <span class="stat-label">问题数量:</span>
                                                <span class="stat-value">{{ report.problemCount }}</span>
                                            </div>
                                            <div class="stat-item">
                                                <span class="stat-label">解决率:</span>
                                                <span class="stat-value">{{ report.resolutionRate }}%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="report-actions">
                                        <a-space>
                                            <a-button size="small" type="link" @click.stop="handleViewReport(report)">
                                                查看报告
                                            </a-button>
                                            <a-button size="small" type="link"
                                                @click.stop="handleDownloadReport(report)" v-if="report.isGenerated">
                                                下载报告
                                            </a-button>
                                        </a-space>
                                    </div>
                                </div>

                                <div v-if="!filteredReports.length && !loading" class="empty-state">
                                    <a-empty description="暂无监控报告" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ClockCircleOutlined, ReloadOutlined } from '@ant-design/icons-vue'

// Props定义
interface Props {
    monitoringData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'problem-click': [problem: any]
    'alert-click': [alert: any]
    'refresh': []
}>()

// 响应式数据
const activeMonitoringTab = ref('problems')
const selectedProblemStatus = ref('')
const selectedProblemLevel = ref('')
const selectedReportType = ref('')

// 计算属性
const resolutionRate = computed(() => {
    const total = props.monitoringData?.totalProblems || 0
    const resolved = props.monitoringData?.resolvedProblems || 0
    return total > 0 ? Math.round((resolved / total) * 100) : 0
})

const filteredProblems = computed(() => {
    if (!props.monitoringData?.problems) return []
    let filtered = props.monitoringData.problems

    if (selectedProblemStatus.value) {
        filtered = filtered.filter((p: any) => p.status === selectedProblemStatus.value)
    }

    if (selectedProblemLevel.value) {
        filtered = filtered.filter((p: any) => p.level === selectedProblemLevel.value)
    }

    return filtered
})

const filteredReports = computed(() => {
    if (!props.monitoringData?.reports) return []
    let filtered = props.monitoringData.reports

    if (selectedReportType.value) {
        filtered = filtered.filter((r: any) => r.type === selectedReportType.value)
    }

    return filtered
})

// 方法
const handleRefreshMonitoring = () => {
    emit('refresh')
    message.success('监控数据已刷新')
}

const handleMonitoringTabChange = (key: string) => {
    activeMonitoringTab.value = key
}

const handleProblemStatusChange = (status: string) => {
    selectedProblemStatus.value = status
}

const handleProblemLevelChange = (level: string) => {
    selectedProblemLevel.value = level
}

const handleReportTypeChange = (type: string) => {
    selectedReportType.value = type
}

const getProblemLevelColor = (level: string) => {
    const colorMap: Record<string, string> = {
        'critical': 'red',
        'high': 'orange',
        'medium': 'blue',
        'low': 'green'
    }
    return colorMap[level] || 'default'
}

const getProblemLevelText = (level: string) => {
    const textMap: Record<string, string> = {
        'critical': '严重',
        'high': '高',
        'medium': '中',
        'low': '低'
    }
    return textMap[level] || level
}

const getProblemStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'new': 'blue',
        'processing': 'processing',
        'resolved': 'success',
        'closed': 'default'
    }
    return colorMap[status] || 'default'
}

const getProblemStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'new': '新发现',
        'processing': '处理中',
        'resolved': '已解决',
        'closed': '已关闭'
    }
    return textMap[status] || status
}

const getAlertLevelColor = (level: string) => {
    const colorMap: Record<string, string> = {
        'critical': 'red',
        'high': 'orange',
        'medium': 'blue',
        'low': 'green'
    }
    return colorMap[level] || 'default'
}

const getAlertLevelText = (level: string) => {
    const textMap: Record<string, string> = {
        'critical': '严重',
        'high': '高',
        'medium': '中',
        'low': '低'
    }
    return textMap[level] || level
}

const getReportTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        'daily': 'blue',
        'weekly': 'green',
        'monthly': 'orange'
    }
    return colorMap[type] || 'default'
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString()
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}

const handleProblemClick = (problem: any) => {
    emit('problem-click', problem)
}

const handleAlertClick = (alert: any) => {
    emit('alert-click', alert)
}

const handleAssignProblem = (problem: any) => {
    message.success(`已分配问题：${problem.title}`)
}

const handleUpdateProblem = (problem: any) => {
    message.info(`更新问题进度：${problem.title}`)
}

const handleViewProblem = (problem: any) => {
    message.info(`查看问题详情：${problem.title}`)
}

const handleProcessAlert = (alert: any) => {
    message.success(`开始处理预警：${alert.title}`)
}

const handleViewAlert = (alert: any) => {
    message.info(`查看预警详情：${alert.title}`)
}

const handleGenerateReport = () => {
    message.success('正在生成监控报告...')
}

const handleReportClick = (report: any) => {
    message.info(`查看报告：${report.title}`)
}

const handleViewReport = (report: any) => {
    message.info(`查看报告详情：${report.title}`)
}

const handleDownloadReport = (report: any) => {
    message.success(`下载报告：${report.title}`)
}
</script>

<style scoped lang="scss">
.intelligent-monitoring {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .monitoring-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .monitoring-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .monitoring-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .monitoring-overview {
            background: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .monitoring-tabs {
            flex: 1;
            overflow: hidden;

            .ant-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-tabs-content-holder {
                    flex: 1;
                    overflow: hidden;

                    .ant-tabs-content {
                        height: 100%;

                        .ant-tabs-tabpane {
                            height: 100%;
                            overflow-y: auto;

                            .problems-section,
                            .alerts-section,
                            .reports-section {
                                height: 100%;

                                .problems-filters,
                                .reports-filters {
                                    margin-bottom: 16px;
                                    padding-bottom: 12px;
                                    border-bottom: 1px solid #e8e8e8;
                                }

                                .problems-list,
                                .alerts-list,
                                .reports-list {

                                    .problem-item,
                                    .alert-item,
                                    .report-item {
                                        padding: 16px;
                                        margin-bottom: 12px;
                                        background: white;
                                        border: 1px solid #e8e8e8;
                                        border-radius: 8px;
                                        cursor: pointer;
                                        transition: all 0.3s ease;

                                        &:hover {
                                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                                            border-color: #1890ff;
                                        }

                                        &.critical {
                                            border-left: 4px solid #f5222d;
                                        }

                                        &.high {
                                            border-left: 4px solid #fa8c16;
                                        }

                                        &.medium {
                                            border-left: 4px solid #1890ff;
                                        }

                                        &.low {
                                            border-left: 4px solid #52c41a;
                                        }

                                        .problem-header,
                                        .alert-header,
                                        .report-header {
                                            display: flex;
                                            justify-content: space-between;
                                            align-items: flex-start;
                                            margin-bottom: 12px;

                                            .problem-info,
                                            .alert-info,
                                            .report-info {
                                                flex: 1;

                                                .problem-title,
                                                .alert-title,
                                                .report-title {
                                                    display: block;
                                                    font-size: 16px;
                                                    font-weight: 500;
                                                    color: #262626;
                                                    margin-bottom: 8px;
                                                }

                                                .problem-tags,
                                                .alert-tags,
                                                .report-tags {
                                                    display: flex;
                                                    gap: 8px;
                                                }
                                            }

                                            .problem-time,
                                            .alert-time,
                                            .report-time {
                                                display: flex;
                                                align-items: center;
                                                gap: 4px;
                                                color: #666;
                                                font-size: 12px;

                                                .time-text {
                                                    white-space: nowrap;
                                                }
                                            }
                                        }

                                        .problem-content,
                                        .alert-content,
                                        .report-content {
                                            margin-bottom: 12px;

                                            .problem-description,
                                            .alert-description,
                                            .report-summary {
                                                color: #666;
                                                font-size: 13px;
                                                line-height: 1.5;
                                                margin-bottom: 8px;
                                            }

                                            .problem-details,
                                            .alert-metrics,
                                            .report-stats {
                                                display: flex;
                                                gap: 16px;
                                                flex-wrap: wrap;

                                                .detail-item,
                                                .metric-item,
                                                .stat-item {
                                                    font-size: 12px;

                                                    .detail-label,
                                                    .metric-label,
                                                    .stat-label {
                                                        color: #666;
                                                    }

                                                    .detail-value,
                                                    .metric-value,
                                                    .stat-value {
                                                        color: #262626;
                                                        font-weight: 500;
                                                        margin-left: 4px;
                                                    }
                                                }
                                            }
                                        }

                                        .problem-progress {
                                            margin-bottom: 12px;

                                            .progress-info {
                                                display: flex;
                                                justify-content: space-between;
                                                align-items: center;
                                                margin-bottom: 4px;
                                                font-size: 12px;

                                                .progress-label {
                                                    color: #666;
                                                }

                                                .progress-value {
                                                    color: #262626;
                                                    font-weight: 500;
                                                }
                                            }
                                        }

                                        .problem-actions,
                                        .alert-actions,
                                        .report-actions {
                                            display: flex;
                                            justify-content: flex-end;
                                        }
                                    }

                                    .empty-state {
                                        height: 200px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .intelligent-monitoring {
        padding: 16px;

        .monitoring-content {
            .monitoring-overview {
                padding: 12px;
            }

            .monitoring-tabs {
                .ant-tabs-content-holder {
                    .ant-tabs-content {
                        .ant-tabs-tabpane {

                            .problems-section,
                            .alerts-section,
                            .reports-section {

                                .problems-list,
                                .alerts-list,
                                .reports-list {

                                    .problem-item,
                                    .alert-item,
                                    .report-item {
                                        padding: 12px;

                                        .problem-header,
                                        .alert-header,
                                        .report-header {
                                            flex-direction: column;
                                            align-items: flex-start;
                                            gap: 8px;

                                            .problem-time,
                                            .alert-time,
                                            .report-time {
                                                align-self: flex-end;
                                            }
                                        }

                                        .problem-content,
                                        .alert-content,
                                        .report-content {

                                            .problem-details,
                                            .alert-metrics,
                                            .report-stats {
                                                flex-direction: column;
                                                gap: 4px;
                                            }
                                        }

                                        .problem-actions,
                                        .alert-actions,
                                        .report-actions {
                                            justify-content: flex-start;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>