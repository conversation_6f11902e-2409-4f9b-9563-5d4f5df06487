<template>
    <div class="organizational-insights">
        <div class="insights-header">
            <h3 class="insights-title">组织效能洞察</h3>
            <div class="insights-tabs">
                <a-radio-group v-model:value="activeInsightTab" @change="handleTabChange">
                    <a-radio-button value="party">党建专项</a-radio-button>
                    <a-radio-button value="admin">行政效能</a-radio-button>
                    <a-radio-button value="innovation">创新实践</a-radio-button>
                </a-radio-group>
            </div>
        </div>

        <div class="insights-content" v-loading="loading">
            <!-- 党建专项分析 -->
            <div v-if="activeInsightTab === 'party'" class="party-insights">
                <div class="metrics-overview">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-statistic title="党建指标数" :value="insightsData?.partyMetrics?.totalIndicators || 0"
                                :value-style="{ color: '#f5222d' }" />
                        </a-col>
                        <a-col :span="8">
                            <a-statistic title="平均得分" :value="insightsData?.partyMetrics?.averageScore || 0"
                                :precision="1" :value-style="{ color: '#f5222d' }" />
                        </a-col>
                        <a-col :span="8">
                            <a-statistic title="优秀单位" :value="insightsData?.partyMetrics?.excellentUnits || 0"
                                :value-style="{ color: '#52c41a' }" />
                        </a-col>
                    </a-row>
                </div>

                <div class="metrics-details">
                    <div class="detail-item" v-for="metric in insightsData?.partyMetrics?.details || []"
                        :key="metric.id">
                        <div class="metric-header">
                            <span class="metric-name">{{ metric.name }}</span>
                            <span class="metric-score">{{ metric.score }}/{{ metric.maxScore }}</span>
                        </div>
                        <div class="metric-progress">
                            <a-progress :percent="(metric.score / metric.maxScore) * 100"
                                :stroke-color="getScoreColor(metric.score, metric.maxScore)" :show-info="false" />
                        </div>
                        <div class="metric-description">{{ metric.description }}</div>
                    </div>
                </div>
            </div>

            <!-- 行政效能分析 -->
            <div v-if="activeInsightTab === 'admin'" class="admin-insights">
                <div class="efficiency-chart">
                    <div class="chart-header">
                        <h4>行政效能趋势</h4>
                    </div>
                    <div class="chart-content">
                        <div class="efficiency-items">
                            <div v-for="item in insightsData?.adminMetrics?.efficiencyTrend || []" :key="item.month"
                                class="efficiency-item">
                                <div class="item-month">{{ item.month }}</div>
                                <div class="item-bar">
                                    <div class="bar-fill"
                                        :style="{ height: `${item.efficiency}%`, backgroundColor: getEfficiencyColor(item.efficiency) }">
                                    </div>
                                </div>
                                <div class="item-value">{{ item.efficiency }}%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="performance-metrics">
                    <h4>关键绩效指标</h4>
                    <div class="metrics-grid">
                        <div v-for="kpi in insightsData?.adminMetrics?.kpis || []" :key="kpi.id" class="kpi-card"
                            @click="handleMetricClick(kpi)">
                            <div class="kpi-icon">
                                <component :is="getKpiIcon(kpi.type)" />
                            </div>
                            <div class="kpi-content">
                                <div class="kpi-name">{{ kpi.name }}</div>
                                <div class="kpi-value">{{ kpi.value }}{{ kpi.unit }}</div>
                                <div class="kpi-trend">
                                    <arrow-up-outlined v-if="kpi.trend === 'up'" class="trend-up" />
                                    <arrow-down-outlined v-else-if="kpi.trend === 'down'" class="trend-down" />
                                    <minus-outlined v-else class="trend-stable" />
                                    <span class="trend-text">{{ kpi.trendText }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 创新实践分析 -->
            <div v-if="activeInsightTab === 'innovation'" class="innovation-insights">
                <div class="innovation-summary">
                    <a-row :gutter="16">
                        <a-col :span="6">
                            <a-statistic title="创新项目" :value="insightsData?.innovationMetrics?.totalProjects || 0"
                                :value-style="{ color: '#1890ff' }" />
                        </a-col>
                        <a-col :span="6">
                            <a-statistic title="最佳实践" :value="insightsData?.innovationMetrics?.bestPractices || 0"
                                :value-style="{ color: '#52c41a' }" />
                        </a-col>
                        <a-col :span="6">
                            <a-statistic title="推广应用" :value="insightsData?.innovationMetrics?.applications || 0"
                                :value-style="{ color: '#faad14' }" />
                        </a-col>
                        <a-col :span="6">
                            <a-statistic title="创新指数" :value="insightsData?.innovationMetrics?.innovationIndex || 0"
                                :precision="2" :value-style="{ color: '#722ed1' }" />
                        </a-col>
                    </a-row>
                </div>

                <div class="innovation-categories">
                    <h4>创新类别分布</h4>
                    <div class="category-list">
                        <div v-for="category in insightsData?.innovationMetrics?.categories || []" :key="category.id"
                            class="category-item">
                            <div class="category-info">
                                <span class="category-name">{{ category.name }}</span>
                                <span class="category-count">{{ category.count }}个项目</span>
                            </div>
                            <div class="category-progress">
                                <a-progress :percent="category.percentage" :stroke-color="category.color"
                                    size="small" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="innovation-highlights">
                    <h4>创新亮点</h4>
                    <div class="highlights-list">
                        <div v-for="highlight in insightsData?.innovationMetrics?.highlights || []" :key="highlight.id"
                            class="highlight-item">
                            <div class="highlight-header">
                                <span class="highlight-title">{{ highlight.title }}</span>
                                <a-tag :color="highlight.type === 'breakthrough' ? 'red' : 'blue'">
                                    {{ highlight.type === 'breakthrough' ? '重大突破' : '优秀实践' }}
                                </a-tag>
                            </div>
                            <div class="highlight-description">{{ highlight.description }}</div>
                            <div class="highlight-impact">
                                <span class="impact-label">影响范围:</span>
                                <span class="impact-value">{{ highlight.impact }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="!insightsData && !loading" class="empty-state">
                <a-empty description="暂无洞察数据" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    MinusOutlined,
    TeamOutlined,
    FileTextOutlined,
    ClockCircleOutlined,
    TrophyOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    insightsData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'metric-click': [metric: any]
    'export': [data: any]
}>()

// 响应式数据
const activeInsightTab = ref('party')

// 方法
const handleTabChange = (e: any) => {
    activeInsightTab.value = e.target.value
}

const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100
    if (percentage >= 90) return '#52c41a'
    if (percentage >= 80) return '#faad14'
    if (percentage >= 60) return '#ff7a45'
    return '#f5222d'
}

const getEfficiencyColor = (efficiency: number) => {
    if (efficiency >= 90) return '#52c41a'
    if (efficiency >= 80) return '#faad14'
    if (efficiency >= 60) return '#ff7a45'
    return '#f5222d'
}

const getKpiIcon = (type: string) => {
    const iconMap: Record<string, any> = {
        'team': TeamOutlined,
        'document': FileTextOutlined,
        'time': ClockCircleOutlined,
        'achievement': TrophyOutlined
    }
    return iconMap[type] || FileTextOutlined
}

const handleMetricClick = (metric: any) => {
    emit('metric-click', metric)
}
</script>

<style scoped lang="scss">
.organizational-insights {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .insights-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .insights-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .insights-content {
        flex: 1;
        overflow-y: auto;

        .party-insights,
        .admin-insights,
        .innovation-insights {
            .metrics-overview {
                background: #f6f8fa;
                padding: 16px;
                border-radius: 6px;
                margin-bottom: 20px;
            }

            .metrics-details {
                .detail-item {
                    padding: 16px;
                    margin-bottom: 12px;
                    background: white;
                    border: 1px solid #e8e8e8;
                    border-radius: 8px;

                    .metric-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;

                        .metric-name {
                            font-weight: 500;
                            color: #262626;
                        }

                        .metric-score {
                            font-weight: 600;
                            color: #1890ff;
                        }
                    }

                    .metric-progress {
                        margin-bottom: 8px;
                    }

                    .metric-description {
                        font-size: 12px;
                        color: #666;
                        line-height: 1.5;
                    }
                }
            }

            .efficiency-chart {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 20px;

                .chart-header {
                    margin-bottom: 16px;

                    h4 {
                        margin: 0;
                        font-size: 16px;
                        font-weight: 500;
                        color: #262626;
                    }
                }

                .chart-content {
                    .efficiency-items {
                        display: flex;
                        align-items: end;
                        gap: 12px;
                        height: 120px;

                        .efficiency-item {
                            flex: 1;
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 4px;

                            .item-month {
                                font-size: 12px;
                                color: #666;
                            }

                            .item-bar {
                                width: 20px;
                                height: 80px;
                                background: #f0f0f0;
                                border-radius: 2px;
                                position: relative;
                                overflow: hidden;

                                .bar-fill {
                                    position: absolute;
                                    bottom: 0;
                                    left: 0;
                                    right: 0;
                                    border-radius: 2px;
                                    transition: height 0.3s ease;
                                }
                            }

                            .item-value {
                                font-size: 12px;
                                font-weight: 500;
                                color: #262626;
                            }
                        }
                    }
                }
            }

            .performance-metrics {
                h4 {
                    margin: 0 0 16px 0;
                    font-size: 16px;
                    font-weight: 500;
                    color: #262626;
                }

                .metrics-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 16px;

                    .kpi-card {
                        padding: 16px;
                        background: white;
                        border: 1px solid #e8e8e8;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover {
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                            border-color: #1890ff;
                        }

                        .kpi-icon {
                            font-size: 24px;
                            color: #1890ff;
                            margin-bottom: 12px;
                        }

                        .kpi-content {
                            .kpi-name {
                                font-size: 14px;
                                color: #666;
                                margin-bottom: 8px;
                            }

                            .kpi-value {
                                font-size: 20px;
                                font-weight: 600;
                                color: #262626;
                                margin-bottom: 4px;
                            }

                            .kpi-trend {
                                display: flex;
                                align-items: center;
                                gap: 4px;
                                font-size: 12px;

                                .trend-up {
                                    color: #52c41a;
                                }

                                .trend-down {
                                    color: #f5222d;
                                }

                                .trend-stable {
                                    color: #666;
                                }

                                .trend-text {
                                    color: #666;
                                }
                            }
                        }
                    }
                }
            }

            .innovation-summary {
                background: #f6f8fa;
                padding: 16px;
                border-radius: 6px;
                margin-bottom: 20px;
            }

            .innovation-categories {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 20px;

                h4 {
                    margin: 0 0 16px 0;
                    font-size: 16px;
                    font-weight: 500;
                    color: #262626;
                }

                .category-list {
                    .category-item {
                        padding: 12px 0;
                        border-bottom: 1px solid #f0f0f0;

                        &:last-child {
                            border-bottom: none;
                        }

                        .category-info {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 8px;

                            .category-name {
                                font-weight: 500;
                                color: #262626;
                            }

                            .category-count {
                                font-size: 12px;
                                color: #666;
                            }
                        }

                        .category-progress {
                            // 进度条样式由 ant-design-vue 提供
                        }
                    }
                }
            }

            .innovation-highlights {
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                padding: 16px;

                h4 {
                    margin: 0 0 16px 0;
                    font-size: 16px;
                    font-weight: 500;
                    color: #262626;
                }

                .highlights-list {
                    .highlight-item {
                        padding: 16px;
                        margin-bottom: 12px;
                        background: #fafafa;
                        border: 1px solid #e8e8e8;
                        border-radius: 6px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .highlight-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-start;
                            margin-bottom: 8px;

                            .highlight-title {
                                font-weight: 500;
                                color: #262626;
                                flex: 1;
                                margin-right: 12px;
                            }
                        }

                        .highlight-description {
                            color: #666;
                            font-size: 13px;
                            line-height: 1.5;
                            margin-bottom: 8px;
                        }

                        .highlight-impact {
                            font-size: 12px;

                            .impact-label {
                                color: #666;
                            }

                            .impact-value {
                                color: #262626;
                                font-weight: 500;
                                margin-left: 4px;
                            }
                        }
                    }
                }
            }
        }

        .empty-state {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .organizational-insights {
        padding: 16px;

        .insights-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .insights-title {
                font-size: 16px;
            }
        }

        .insights-content {

            .party-insights,
            .admin-insights,
            .innovation-insights {

                .metrics-overview,
                .innovation-summary {
                    padding: 12px;
                }

                .efficiency-chart,
                .innovation-categories,
                .innovation-highlights {
                    padding: 12px;
                }

                .performance-metrics {
                    .metrics-grid {
                        grid-template-columns: 1fr;
                    }
                }

                .efficiency-chart {
                    .chart-content {
                        .efficiency-items {
                            height: 100px;

                            .efficiency-item {
                                .item-bar {
                                    width: 16px;
                                    height: 60px;
                                }
                            }
                        }
                    }
                }

                .highlights-list {
                    .highlight-item {
                        padding: 12px;

                        .highlight-header {
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 8px;

                            .highlight-title {
                                margin-right: 0;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
