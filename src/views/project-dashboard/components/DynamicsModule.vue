<template>
    <div class="dynamics-section">
        <a-card title="选育树推项目创建动态" class="dynamics-card">
            <a-carousel autoplay class="dynamics-carousel">
                <div v-for="(item, index) in dynamicsData" :key="index" class="dynamics-item">
                    <div class="dynamics-content">
                        <div class="dynamics-image">
                            <img :src="item.imageUrl" :alt="item.title" />
                        </div>
                        <div class="dynamics-info">
                            <div class="dynamics-title">{{ item.title }}</div>
                            <div class="dynamics-unit">{{ item.unit }}</div>
                            <div class="dynamics-type">{{ getDynamicsTypeLabel(item.type) }}</div>
                        </div>
                    </div>
                </div>
            </a-carousel>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义属性
const props = defineProps({
    selectedStage: {
        type: String,
        default: ''
    },
    selectedDistrict: {
        type: String,
        default: ''
    }
})

// 响应式数据
const dynamicsData = ref([])

// 获取动态类型标签
const getDynamicsTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
        'case': '案例分享',
        'honor': '荣誉表彰',
        'media': '媒体报道',
        'showcase': '成果展示'
    }
    return typeMap[type] || '其他'
}

// 加载数据
const loadDynamicsData = async () => {
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 700))

        // 模拟数据
        dynamicsData.value = [
            { id: 1, title: '智慧政务服务创新案例', unit: '渝中区行政服务中心', type: 'case', imageUrl: 'https://via.placeholder.com/400x300?text=智慧政务' },
            { id: 2, title: '区级机关效能提升工程获市级表彰', unit: '江北区机关事务管理局', type: 'honor', imageUrl: 'https://via.placeholder.com/400x300?text=效能提升' },
            { id: 3, title: '基层治理数字化转型经验交流会', unit: '南岸区政务服务中心', type: 'media', imageUrl: 'https://via.placeholder.com/400x300?text=数字化转型' },
            { id: 4, title: '"互联网+政务服务"创新实践', unit: '九龙坡区行政审批局', type: 'showcase', imageUrl: 'https://via.placeholder.com/400x300?text=政务服务' },
            { id: 5, title: '一站式服务改革获中央媒体报道', unit: '沙坪坝区政务服务中心', type: 'media', imageUrl: 'https://via.placeholder.com/400x300?text=一站式服务' },
        ]
    } catch (error) {
        message.error('加载动态数据失败')
        console.error(error)
    }
}

// 暴露方法给父组件
defineExpose({
    loadDynamicsData
})

// 初始化
onMounted(() => {
    loadDynamicsData()
})
</script>

<style scoped lang="scss">
.dynamics-section {
    margin-top: 24px;

    .dynamics-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }

    .dynamics-carousel {
        height: 300px;

        .dynamics-item {
            height: 300px;
            padding: 16px;

            .dynamics-content {
                display: flex;
                height: 100%;

                .dynamics-image {
                    flex: 0 0 60%;
                    height: 100%;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        border-radius: 8px;
                    }
                }

                .dynamics-info {
                    flex: 0 0 40%;
                    padding: 16px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;

                    .dynamics-title {
                        font-size: 18px;
                        font-weight: 600;
                        margin-bottom: 16px;
                    }

                    .dynamics-unit {
                        font-size: 14px;
                        color: #666;
                        margin-bottom: 8px;
                    }

                    .dynamics-type {
                        display: inline-block;
                        padding: 2px 8px;
                        background-color: #e6f7ff;
                        color: #1890ff;
                        border-radius: 4px;
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
</style>