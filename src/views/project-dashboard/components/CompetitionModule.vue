<template>
    <div class="competition-section">
        <a-card title="选育树推项目亮晒比拼" class="competition-card">
            <!-- 项目名称Tab切换 -->
            <div class="project-tabs-container">
                <a-tabs v-model:activeKey="activeProjectId" @change="handleProjectChange" type="card">
                    <a-tab-pane v-for="project in projectOptions" :key="project.id" :tab="project.name">
                    </a-tab-pane>
                </a-tabs>
            </div>

            <!-- 比拼数据表格 -->
            <a-table :dataSource="competitionData" :columns="competitionColumns" :loading="loading"
                :pagination="{ pageSize: 10 }" size="middle">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'ranking'">
                        <a-tag :color="getRankingColor(record.ranking)">{{ record.ranking }}</a-tag>
                    </template>
                    <template v-if="column.dataIndex && column.dataIndex.startsWith('score')">
                        <span :style="{ color: getScoreColor(record[column.dataIndex]) }">{{ record[column.dataIndex]
                            }}</span>
                    </template>
                </template>
            </a-table>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义属性
const props = defineProps({
    selectedStage: {
        type: String,
        default: ''
    },
    selectedDistrict: {
        type: String,
        default: ''
    }
})

// 响应式数据
const loading = ref(false)
const competitionData = ref<any[]>([])
const activeProjectId = ref(1)

// 项目选项数据
const projectOptions = ref([
    { id: 1, name: '政治要件闭环落实情况' },
    { id: 2, name: '重大事项请示报告上报情况' },
    { id: 3, name: '党内法规制度建设上报情况' },
    { id: 4, name: '问题管控考核结果' },
    { id: 5, name: '组织生活纪实完成情况' },
    { id: 6, name: '未完成的组织生活纪实内容' },
    { id: 7, name: '党费收交完成情况' },
    { id: 8, name: '基层党组织换届完成情况' },
    { id: 9, name: '基层党未及时完成换届的组织' },
    { id: 10, name: '党员报到完成率' }
])

// 表格列定义
const competitionColumns = [
    {
        title: '排名',
        dataIndex: 'ranking',
        key: 'ranking',
        width: 80,
    },
    {
        title: '单位名称',
        dataIndex: 'unitName',
        key: 'unitName',
    },
    {
        title: '指标1得分',
        dataIndex: 'score1',
        key: 'score1',
    },
    {
        title: '指标2得分',
        dataIndex: 'score2',
        key: 'score2',
    },
    {
        title: '指标3得分',
        dataIndex: 'score3',
        key: 'score3',
    },
    {
        title: '综合得分',
        dataIndex: 'totalScore',
        key: 'totalScore',
    },
]

// 获取排名颜色
const getRankingColor = (ranking: number) => {
    if (ranking === 1) return 'gold'
    if (ranking === 2) return 'silver'
    if (ranking === 3) return '#cd7f32' // bronze
    return ''
}

// 获取分数颜色
const getScoreColor = (score: number) => {
    if (score >= 90) return '#52c41a' // 绿色
    if (score >= 80) return '#1890ff' // 蓝色
    if (score >= 70) return '#faad14' // 黄色
    return '#f5222d' // 红色
}

// 处理项目切换
const handleProjectChange = (projectId: number) => {
    activeProjectId.value = projectId
    loadCompetitionData()
}

// 加载数据
const loadCompetitionData = async () => {
    loading.value = true
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 模拟数据
        competitionData.value = [
            { id: 1, ranking: 1, unitName: '渝中区机关事务管理局', score1: 95, score2: 92, score3: 98, totalScore: 95.0 },
            { id: 2, ranking: 2, unitName: '江北区行政服务中心', score1: 94, score2: 90, score3: 96, totalScore: 93.3 },
            { id: 3, ranking: 3, unitName: '南岸区政务服务中心', score1: 92, score2: 91, score3: 93, totalScore: 92.0 },
            { id: 4, ranking: 4, unitName: '九龙坡区民政局', score1: 90, score2: 89, score3: 92, totalScore: 90.3 },
            { id: 5, ranking: 5, unitName: '沙坪坝区城市管理局', score1: 88, score2: 90, score3: 89, totalScore: 89.0 },
            { id: 6, ranking: 6, unitName: '大渡口区社会保障局', score1: 87, score2: 88, score3: 90, totalScore: 88.3 },
            { id: 7, ranking: 7, unitName: '渝北区公共资源交易中心', score1: 86, score2: 89, score3: 87, totalScore: 87.3 },
            { id: 8, ranking: 8, unitName: '巴南区行政审批局', score1: 85, score2: 87, score3: 88, totalScore: 86.7 },
            { id: 9, ranking: 9, unitName: '北碚区市场监督管理局', score1: 84, score2: 86, score3: 85, totalScore: 85.0 },
            { id: 10, ranking: 10, unitName: '渝中区税务局', score1: 83, score2: 84, score3: 86, totalScore: 84.3 },
        ]
    } catch (error) {
        message.error('加载比拼数据失败')
        console.error(error)
    } finally {
        loading.value = false
    }
}

// 暴露方法给父组件
defineExpose({
    loadCompetitionData
})

// 初始化
onMounted(() => {
    loadCompetitionData()
})
</script>

<style scoped lang="scss">
.competition-section {
    margin-top: 24px;

    .competition-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

        .project-tabs-container {
            margin-bottom: 16px;

            :deep(.ant-tabs-card) {
                .ant-tabs-tab {
                    background: #fafafa;
                    border: 1px solid #d9d9d9;

                    &.ant-tabs-tab-active {
                        background: #1890ff;
                        border-color: #1890ff;

                        .ant-tabs-tab-btn {
                            color: white;
                        }
                    }

                    &:hover:not(.ant-tabs-tab-active) {
                        background: #e6f7ff;
                        border-color: #91d5ff;
                    }
                }

                .ant-tabs-content-holder {
                    display: none;
                }
            }
        }
    }
}
</style>
