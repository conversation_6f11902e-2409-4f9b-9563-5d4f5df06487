<template>
    <div class="supervision-section">
        <a-card title="选育树推项目创建跟踪督办" class="supervision-card">
            <a-table :dataSource="supervisionTrackingData" :columns="supervisionColumns" :loading="loading"
                :pagination="{ pageSize: 5 }" size="middle">
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'status'">
                        <a-tag :color="getStatusTagColor(record.status)">{{ getStatusLabel(record.status) }}</a-tag>
                    </template>
                    <template v-if="column.key === 'action'">
                        <a-button type="primary" size="small" @click="handleSendSupervision(record)">一键督办</a-button>
                    </template>
                </template>
            </a-table>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义属性
const props = defineProps({
    selectedStage: {
        type: String,
        default: ''
    },
    selectedDistrict: {
        type: String,
        default: ''
    }
})

// 响应式数据
const loading = ref(false)
const supervisionTrackingData = ref([])

// 表格列定义
const supervisionColumns = [
    {
        title: '单位名称',
        dataIndex: 'unitName',
        key: 'unitName',
    },
    {
        title: '指标名称',
        dataIndex: 'indicatorName',
        key: 'indicatorName',
    },
    {
        title: '截止时间',
        dataIndex: 'deadline',
        key: 'deadline',
    },
    {
        title: '当前状态',
        dataIndex: 'status',
        key: 'status',
    },
    {
        title: '操作',
        key: 'action',
    },
]

// 获取状态标签颜色
const getStatusTagColor = (status: string) => {
    const statusColorMap: Record<string, string> = {
        'pending': 'blue',
        'inProgress': 'processing',
        'completed': 'success',
        'overdue': 'error'
    }
    return statusColorMap[status] || 'default'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
    const statusLabelMap: Record<string, string> = {
        'pending': '待处理',
        'inProgress': '进行中',
        'completed': '已完成',
        'overdue': '已逾期'
    }
    return statusLabelMap[status] || '未知状态'
}

// 处理督办按钮点击
const handleSendSupervision = (record: any) => {
    message.success(`已向${record.unitName}发送督办通知`)
}

// 加载数据
const loadSupervisionTrackingData = async () => {
    loading.value = true
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 800))

        // 模拟数据
        supervisionTrackingData.value = [
            { id: 1, unitName: '渝中区行政服务中心', indicatorName: '政务服务事项网办率', deadline: '2025-09-30', status: 'pending' },
            { id: 2, unitName: '江北区机关事务管理局', indicatorName: '机关效能评估指标完成', deadline: '2025-09-15', status: 'overdue' },
            { id: 3, unitName: '南岸区政务服务中心', indicatorName: '基层治理数字化覆盖率', deadline: '2025-10-10', status: 'inProgress' },
            { id: 4, unitName: '九龙坡区行政审批局', indicatorName: '政务服务"好差评"满意率', deadline: '2025-09-20', status: 'completed' },
            { id: 5, unitName: '沙坪坝区城市管理局', indicatorName: '城市管理数字化覆盖率', deadline: '2025-09-25', status: 'pending' },
        ]
    } catch (error) {
        message.error('加载督办数据失败')
        console.error(error)
    } finally {
        loading.value = false
    }
}

// 暴露方法给父组件
defineExpose({
    loadSupervisionTrackingData
})

// 初始化
onMounted(() => {
    loadSupervisionTrackingData()
})
</script>

<style scoped lang="scss">
.supervision-section {
    margin-top: 24px;

    .supervision-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }
}
</style>