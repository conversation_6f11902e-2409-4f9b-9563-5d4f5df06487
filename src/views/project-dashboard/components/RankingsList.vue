<template>
    <div class="rankings-list">
        <div class="rankings-header">
            <h3 class="rankings-title">排行榜单</h3>
            <div class="rankings-actions">
                <a-button @click="handleRefreshRankings">
                    <template #icon><reload-outlined /></template>
                    刷新排行
                </a-button>
            </div>
        </div>

        <div class="rankings-content" v-loading="loading">
            <div class="rankings-tabs">
                <a-tabs v-model:activeKey="activeRankingTab" @change="handleRankingTabChange">
                    <a-tab-pane key="units" tab="单位排行">
                        <div class="units-ranking">
                            <div class="ranking-filters">
                                <a-space>
                                    <a-select v-model:value="selectedRankingType" placeholder="排行类型"
                                        style="width: 150px" @change="handleRankingTypeChange">
                                        <a-select-option value="overall">综合排行</a-select-option>
                                        <a-select-option value="progress">进度排行</a-select-option>
                                        <a-select-option value="quality">质量排行</a-select-option>
                                        <a-select-option value="innovation">创新排行</a-select-option>
                                    </a-select>

                                    <a-select v-model:value="selectedTimeRange" placeholder="时间范围" style="width: 120px"
                                        @change="handleTimeRangeChange">
                                        <a-select-option value="month">本月</a-select-option>
                                        <a-select-option value="quarter">本季度</a-select-option>
                                        <a-select-option value="year">本年度</a-select-option>
                                    </a-select>
                                </a-space>
                            </div>

                            <div class="ranking-list">
                                <div class="ranking-podium">
                                    <div v-for="(unit, index) in topThreeUnits" :key="unit.id" class="podium-item"
                                        :class="`rank-${index + 1}`">
                                        <div class="podium-rank">
                                            <div class="rank-number">{{ index + 1 }}</div>
                                            <div class="rank-medal">
                                                <trophy-outlined v-if="index === 0" />
                                                <star-outlined v-else />
                                            </div>
                                        </div>
                                        <div class="podium-content">
                                            <div class="unit-name">{{ unit.name }}</div>
                                            <div class="unit-score">{{ unit.score }}分</div>
                                            <div class="unit-progress">
                                                <a-progress :percent="unit.progress" size="small"
                                                    :stroke-color="getRankColor(index + 1)" />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="ranking-table">
                                    <a-table :columns="rankingColumns" :data-source="filteredRankings"
                                        :pagination="false" size="small">
                                        <template #bodyCell="{ column, record, index }">
                                            <template v-if="column.key === 'rank'">
                                                <div class="rank-cell" :class="`rank-${record.rank}`">
                                                    <span class="rank-number">{{ record.rank }}</span>
                                                    <trophy-outlined v-if="record.rank <= 3" class="rank-icon" />
                                                </div>
                                            </template>
                                            <template v-if="column.key === 'progress'">
                                                <a-progress :percent="record.progress" size="small"
                                                    :stroke-color="getProgressColor(record.progress)" />
                                            </template>
                                            <template v-if="column.key === 'trend'">
                                                <div class="trend-cell">
                                                    <arrow-up-outlined v-if="record.trend > 0" class="trend-up" />
                                                    <arrow-down-outlined v-else-if="record.trend < 0"
                                                        class="trend-down" />
                                                    <minus-outlined v-else class="trend-stable" />
                                                    <span class="trend-value">{{ Math.abs(record.trend) }}</span>
                                                </div>
                                            </template>
                                        </template>
                                    </a-table>
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="projects" tab="项目排行">
                        <div class="projects-ranking">
                            <div class="ranking-filters">
                                <a-space>
                                    <a-select v-model:value="selectedProjectCategory" placeholder="项目类别"
                                        style="width: 150px" @change="handleProjectCategoryChange">
                                        <a-select-option value="">全部类别</a-select-option>
                                        <a-select-option value="selection">选拔项目</a-select-option>
                                        <a-select-option value="cultivation">培育项目</a-select-option>
                                        <a-select-option value="establishment">树立项目</a-select-option>
                                        <a-select-option value="promotion">推广项目</a-select-option>
                                    </a-select>
                                </a-space>
                            </div>

                            <div class="project-ranking-list">
                                <div v-for="project in filteredProjectRankings" :key="project.id"
                                    class="project-ranking-item" @click="handleProjectClick(project)">
                                    <div class="project-rank">
                                        <div class="rank-badge" :class="`rank-${project.rank}`">
                                            {{ project.rank }}
                                        </div>
                                    </div>

                                    <div class="project-info">
                                        <div class="project-header">
                                            <span class="project-name">{{ project.name }}</span>
                                            <div class="project-tags">
                                                <a-tag :color="getProjectCategoryColor(project.category)">
                                                    {{ getProjectCategoryText(project.category) }}
                                                </a-tag>
                                                <a-tag :color="getProjectStatusColor(project.status)">
                                                    {{ project.status }}
                                                </a-tag>
                                            </div>
                                        </div>

                                        <div class="project-metrics">
                                            <div class="metric-item">
                                                <span class="metric-label">评分:</span>
                                                <span class="metric-value">{{ project.score }}分</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">进度:</span>
                                                <span class="metric-value">{{ project.progress }}%</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">负责人:</span>
                                                <span class="metric-value">{{ project.manager }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="project-trend">
                                        <div class="trend-indicator" :class="getTrendClass(project.trend)">
                                            <arrow-up-outlined v-if="project.trend > 0" />
                                            <arrow-down-outlined v-else-if="project.trend < 0" />
                                            <minus-outlined v-else />
                                            <span class="trend-text">{{ getTrendText(project.trend) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="!filteredProjectRankings.length && !loading" class="empty-state">
                                    <a-empty description="暂无项目排行数据" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="individuals" tab="个人排行">
                        <div class="individuals-ranking">
                            <div class="ranking-filters">
                                <a-space>
                                    <a-select v-model:value="selectedIndividualType" placeholder="人员类型"
                                        style="width: 150px" @change="handleIndividualTypeChange">
                                        <a-select-option value="">全部人员</a-select-option>
                                        <a-select-option value="manager">项目经理</a-select-option>
                                        <a-select-option value="member">项目成员</a-select-option>
                                        <a-select-option value="expert">专家顾问</a-select-option>
                                    </a-select>
                                </a-space>
                            </div>

                            <div class="individual-ranking-list">
                                <div v-for="individual in filteredIndividualRankings" :key="individual.id"
                                    class="individual-ranking-item">
                                    <div class="individual-rank">
                                        <div class="rank-badge" :class="`rank-${individual.rank}`">
                                            {{ individual.rank }}
                                        </div>
                                    </div>

                                    <div class="individual-avatar">
                                        <a-avatar :size="48" :src="individual.avatar">
                                            {{ individual.name.charAt(0) }}
                                        </a-avatar>
                                    </div>

                                    <div class="individual-info">
                                        <div class="individual-name">{{ individual.name }}</div>
                                        <div class="individual-position">{{ individual.position }}</div>
                                        <div class="individual-unit">{{ individual.unit }}</div>
                                    </div>

                                    <div class="individual-metrics">
                                        <div class="metric-item">
                                            <span class="metric-label">参与项目:</span>
                                            <span class="metric-value">{{ individual.projectCount }}个</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">完成率:</span>
                                            <span class="metric-value">{{ individual.completionRate }}%</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">评价分:</span>
                                            <span class="metric-value">{{ individual.rating }}分</span>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="!filteredIndividualRankings.length && !loading" class="empty-state">
                                    <a-empty description="暂无个人排行数据" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    ReloadOutlined,
    TrophyOutlined,
    StarOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    MinusOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    rankingsData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'project-click': [project: any]
    'refresh': []
}>()

// 响应式数据
const activeRankingTab = ref('units')
const selectedRankingType = ref('overall')
const selectedTimeRange = ref('month')
const selectedProjectCategory = ref('')
const selectedIndividualType = ref('')

// 计算属性
const topThreeUnits = computed(() => {
    if (!props.rankingsData?.unitRankings) return []
    return props.rankingsData.unitRankings.slice(0, 3)
})

const filteredRankings = computed(() => {
    if (!props.rankingsData?.unitRankings) return []
    return props.rankingsData.unitRankings.slice(3) // 排除前三名，它们在podium中显示
})

const filteredProjectRankings = computed(() => {
    if (!props.rankingsData?.projectRankings) return []
    let filtered = props.rankingsData.projectRankings

    if (selectedProjectCategory.value) {
        filtered = filtered.filter((p: any) => p.category === selectedProjectCategory.value)
    }

    return filtered
})

const filteredIndividualRankings = computed(() => {
    if (!props.rankingsData?.individualRankings) return []
    let filtered = props.rankingsData.individualRankings

    if (selectedIndividualType.value) {
        filtered = filtered.filter((i: any) => i.type === selectedIndividualType.value)
    }

    return filtered
})

const rankingColumns = [
    {
        title: '排名',
        dataIndex: 'rank',
        key: 'rank',
        width: 80
    },
    {
        title: '单位名称',
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: '评分',
        dataIndex: 'score',
        key: 'score',
        width: 100
    },
    {
        title: '进度',
        dataIndex: 'progress',
        key: 'progress',
        width: 150
    },
    {
        title: '趋势',
        dataIndex: 'trend',
        key: 'trend',
        width: 80
    }
]

// 方法
const handleRefreshRankings = () => {
    emit('refresh')
    message.success('排行数据已刷新')
}

const handleRankingTabChange = (key: string) => {
    activeRankingTab.value = key
}

const handleRankingTypeChange = (type: string) => {
    selectedRankingType.value = type
}

const handleTimeRangeChange = (range: string) => {
    selectedTimeRange.value = range
}

const handleProjectCategoryChange = (category: string) => {
    selectedProjectCategory.value = category
}

const handleIndividualTypeChange = (type: string) => {
    selectedIndividualType.value = type
}

const handleProjectClick = (project: any) => {
    emit('project-click', project)
}

const getRankColor = (rank: number) => {
    const colorMap: Record<number, string> = {
        1: '#ffd700', // 金色
        2: '#c0c0c0', // 银色
        3: '#cd7f32'  // 铜色
    }
    return colorMap[rank] || '#1890ff'
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const getProjectCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
        'selection': 'blue',
        'cultivation': 'green',
        'establishment': 'orange',
        'promotion': 'purple'
    }
    return colorMap[category] || 'default'
}

const getProjectCategoryText = (category: string) => {
    const textMap: Record<string, string> = {
        'selection': '选拔',
        'cultivation': '培育',
        'establishment': '树立',
        'promotion': '推广'
    }
    return textMap[category] || category
}

const getProjectStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        '进行中': 'processing',
        '已完成': 'success',
        '已暂停': 'default',
        '计划中': 'blue'
    }
    return colorMap[status] || 'default'
}

const getTrendClass = (trend: number) => {
    if (trend > 0) return 'trend-up'
    if (trend < 0) return 'trend-down'
    return 'trend-stable'
}

const getTrendText = (trend: number) => {
    if (trend > 0) return `上升${trend}`
    if (trend < 0) return `下降${Math.abs(trend)}`
    return '持平'
}
</script>

<style scoped lang="scss">
.rankings-list {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .rankings-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .rankings-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .rankings-content {
        flex: 1;
        overflow: hidden;

        .rankings-tabs {
            height: 100%;

            .ant-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-tabs-content-holder {
                    flex: 1;
                    overflow: hidden;

                    .ant-tabs-content {
                        height: 100%;

                        .ant-tabs-tabpane {
                            height: 100%;
                            overflow-y: auto;

                            .ranking-filters {
                                margin-bottom: 16px;
                                padding-bottom: 12px;
                                border-bottom: 1px solid #e8e8e8;
                            }

                            .units-ranking {
                                .ranking-podium {
                                    display: flex;
                                    justify-content: center;
                                    gap: 20px;
                                    margin-bottom: 24px;
                                    padding: 20px;
                                    background: linear-gradient(135deg, #f6f8fa 0%, #e8f4fd 100%);
                                    border-radius: 12px;

                                    .podium-item {
                                        display: flex;
                                        flex-direction: column;
                                        align-items: center;
                                        padding: 16px;
                                        background: white;
                                        border-radius: 12px;
                                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                                        min-width: 150px;
                                        transition: all 0.3s ease;

                                        &:hover {
                                            transform: translateY(-4px);
                                            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
                                        }

                                        &.rank-1 {
                                            border: 2px solid #ffd700;
                                            background: linear-gradient(135deg, #fff9e6 0%, #fffbe6 100%);
                                        }

                                        &.rank-2 {
                                            border: 2px solid #c0c0c0;
                                            background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
                                        }

                                        &.rank-3 {
                                            border: 2px solid #cd7f32;
                                            background: linear-gradient(135deg, #fff2e6 0%, #fff7e6 100%);
                                        }

                                        .podium-rank {
                                            display: flex;
                                            flex-direction: column;
                                            align-items: center;
                                            margin-bottom: 12px;

                                            .rank-number {
                                                font-size: 24px;
                                                font-weight: bold;
                                                color: #262626;
                                            }

                                            .rank-medal {
                                                font-size: 20px;
                                                margin-top: 4px;

                                                .rank-1 & {
                                                    color: #ffd700;
                                                }

                                                .rank-2 & {
                                                    color: #c0c0c0;
                                                }

                                                .rank-3 & {
                                                    color: #cd7f32;
                                                }
                                            }
                                        }

                                        .podium-content {
                                            text-align: center;

                                            .unit-name {
                                                font-size: 14px;
                                                font-weight: 600;
                                                color: #262626;
                                                margin-bottom: 8px;
                                            }

                                            .unit-score {
                                                font-size: 16px;
                                                font-weight: bold;
                                                color: #1890ff;
                                                margin-bottom: 8px;
                                            }

                                            .unit-progress {
                                                width: 100%;
                                            }
                                        }
                                    }
                                }

                                .ranking-table {
                                    .ant-table {
                                        .rank-cell {
                                            display: flex;
                                            align-items: center;
                                            gap: 4px;

                                            .rank-number {
                                                font-weight: 600;
                                            }

                                            .rank-icon {
                                                color: #faad14;
                                            }

                                            &.rank-1,
                                            &.rank-2,
                                            &.rank-3 {
                                                .rank-number {
                                                    color: #1890ff;
                                                }
                                            }
                                        }

                                        .trend-cell {
                                            display: flex;
                                            align-items: center;
                                            gap: 4px;

                                            .trend-up {
                                                color: #52c41a;
                                            }

                                            .trend-down {
                                                color: #f5222d;
                                            }

                                            .trend-stable {
                                                color: #666;
                                            }

                                            .trend-value {
                                                font-size: 12px;
                                            }
                                        }
                                    }
                                }
                            }

                            .projects-ranking,
                            .individuals-ranking {

                                .project-ranking-list,
                                .individual-ranking-list {

                                    .project-ranking-item,
                                    .individual-ranking-item {
                                        display: flex;
                                        align-items: center;
                                        padding: 16px;
                                        margin-bottom: 12px;
                                        background: white;
                                        border: 1px solid #e8e8e8;
                                        border-radius: 8px;
                                        cursor: pointer;
                                        transition: all 0.3s ease;

                                        &:hover {
                                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                                            border-color: #1890ff;
                                        }

                                        .project-rank,
                                        .individual-rank {
                                            margin-right: 16px;

                                            .rank-badge {
                                                width: 32px;
                                                height: 32px;
                                                border-radius: 50%;
                                                display: flex;
                                                align-items: center;
                                                justify-content: center;
                                                font-weight: bold;
                                                color: white;
                                                background: #666;

                                                &.rank-1 {
                                                    background: #ffd700;
                                                    color: #262626;
                                                }

                                                &.rank-2 {
                                                    background: #c0c0c0;
                                                    color: #262626;
                                                }

                                                &.rank-3 {
                                                    background: #cd7f32;
                                                    color: white;
                                                }
                                            }
                                        }

                                        .individual-avatar {
                                            margin-right: 16px;
                                        }

                                        .project-info,
                                        .individual-info {
                                            flex: 1;

                                            .project-header {
                                                display: flex;
                                                justify-content: space-between;
                                                align-items: flex-start;
                                                margin-bottom: 8px;

                                                .project-name {
                                                    font-size: 16px;
                                                    font-weight: 500;
                                                    color: #262626;
                                                }

                                                .project-tags {
                                                    display: flex;
                                                    gap: 8px;
                                                }
                                            }

                                            .individual-name {
                                                font-size: 16px;
                                                font-weight: 500;
                                                color: #262626;
                                                margin-bottom: 4px;
                                            }

                                            .individual-position {
                                                font-size: 14px;
                                                color: #666;
                                                margin-bottom: 2px;
                                            }

                                            .individual-unit {
                                                font-size: 12px;
                                                color: #999;
                                                margin-bottom: 8px;
                                            }

                                            .project-metrics,
                                            .individual-metrics {
                                                display: flex;
                                                gap: 16px;
                                                flex-wrap: wrap;

                                                .metric-item {
                                                    font-size: 12px;

                                                    .metric-label {
                                                        color: #666;
                                                    }

                                                    .metric-value {
                                                        color: #262626;
                                                        font-weight: 500;
                                                        margin-left: 4px;
                                                    }
                                                }
                                            }
                                        }

                                        .project-trend {
                                            margin-left: 16px;

                                            .trend-indicator {
                                                display: flex;
                                                align-items: center;
                                                gap: 4px;
                                                font-size: 12px;

                                                &.trend-up {
                                                    color: #52c41a;
                                                }

                                                &.trend-down {
                                                    color: #f5222d;
                                                }

                                                &.trend-stable {
                                                    color: #666;
                                                }
                                            }
                                        }
                                    }

                                    .empty-state {
                                        height: 200px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .rankings-list {
        .rankings-content {
            .rankings-tabs {
                .units-ranking {
                    .ranking-podium {
                        flex-wrap: wrap;
                        gap: 16px;

                        .podium-item {
                            min-width: 120px;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .rankings-list {
        padding: 16px;

        .rankings-content {
            .rankings-tabs {
                .units-ranking {
                    .ranking-podium {
                        flex-direction: column;
                        align-items: center;

                        .podium-item {
                            width: 100%;
                            max-width: 200px;
                        }
                    }
                }

                .projects-ranking,
                .individuals-ranking {

                    .project-ranking-list,
                    .individual-ranking-list {

                        .project-ranking-item,
                        .individual-ranking-item {
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 12px;

                            .project-info,
                            .individual-info {
                                width: 100%;

                                .project-metrics,
                                .individual-metrics {
                                    flex-direction: column;
                                    gap: 4px;
                                }
                            }

                            .project-trend {
                                margin-left: 0;
                                align-self: flex-end;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>