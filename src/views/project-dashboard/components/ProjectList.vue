<template>
    <div class="project-list">
        <div class="list-header">
            <h3 class="list-title">项目列表</h3>
            <div class="list-actions">
                <a-button type="primary" @click="handleCreateProject">
                    <template #icon><plus-outlined /></template>
                    新建项目
                </a-button>
            </div>
        </div>

        <div class="list-content" v-loading="loading">
            <div class="list-filters">
                <a-row :gutter="16">
                    <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <a-input v-model:value="searchKeyword" placeholder="搜索项目名称" allow-clear @change="handleSearch">
                            <template #prefix><search-outlined /></template>
                        </a-input>
                    </a-col>
                    <a-col :xs="12" :sm="6" :md="4" :lg="4">
                        <a-select v-model:value="selectedStage" placeholder="项目阶段" style="width: 100%"
                            :dropdown-style="{ minWidth: '140px' }" @change="handleStageFilter">
                            <a-select-option value="">全部阶段</a-select-option>
                            <a-select-option value="selection">选拔阶段</a-select-option>
                            <a-select-option value="cultivation">培育阶段</a-select-option>
                            <a-select-option value="establishment">树立阶段</a-select-option>
                            <a-select-option value="promotion">推广阶段</a-select-option>
                        </a-select>
                    </a-col>
                    <a-col :xs="12" :sm="6" :md="4" :lg="4">
                        <a-select v-model:value="selectedStatus" placeholder="项目状态" style="width: 100%"
                            :dropdown-style="{ minWidth: '120px' }" @change="handleStatusFilter">
                            <a-select-option value="">全部状态</a-select-option>
                            <a-select-option value="planning">规划中</a-select-option>
                            <a-select-option value="executing">执行中</a-select-option>
                            <a-select-option value="completed">已完成</a-select-option>
                            <a-select-option value="paused">已暂停</a-select-option>
                        </a-select>
                    </a-col>
                    <a-col :xs="12" :sm="6" :md="4" :lg="4">
                        <a-select v-model:value="selectedPriority" placeholder="优先级" style="width: 100%"
                            :dropdown-style="{ minWidth: '120px' }" @change="handlePriorityFilter">
                            <a-select-option value="">全部优先级</a-select-option>
                            <a-select-option value="high">高</a-select-option>
                            <a-select-option value="medium">中</a-select-option>
                            <a-select-option value="low">低</a-select-option>
                        </a-select>
                    </a-col>
                    <a-col :xs="24" :sm="12" :md="8" :lg="6">
                        <a-space>
                            <a-button @click="handleResetFilters">重置筛选</a-button>
                            <a-button @click="handleExportList">
                                <template #icon><download-outlined /></template>
                                导出列表
                            </a-button>
                        </a-space>
                    </a-col>
                </a-row>
            </div>

            <div class="list-view-toggle">
                <a-radio-group v-model:value="viewMode" @change="handleViewModeChange">
                    <a-radio-button value="card">卡片视图</a-radio-button>
                    <a-radio-button value="table">表格视图</a-radio-button>
                </a-radio-group>
            </div>

            <!-- 卡片视图 -->
            <div v-if="viewMode === 'card'" class="card-view">
                <a-row :gutter="[16, 16]">
                    <a-col v-for="project in filteredProjects" :key="project.id" :span="8">
                        <a-card class="project-card" :class="getProjectCardClass(project)"
                            @click="handleProjectClick(project)">
                            <template #title>
                                <div class="card-title">
                                    <span class="project-name">{{ project.name }}</span>
                                    <div class="project-tags">
                                        <a-tag :color="getStageColor(project.stage)">
                                            {{ getStageText(project.stage) }}
                                        </a-tag>
                                        <a-tag :color="getStatusColor(project.status)">
                                            {{ getStatusText(project.status) }}
                                        </a-tag>
                                    </div>
                                </div>
                            </template>

                            <template #extra>
                                <a-dropdown>
                                    <a-button type="text" size="small">
                                        <template #icon><more-outlined /></template>
                                    </a-button>
                                    <template #overlay>
                                        <a-menu>
                                            <a-menu-item @click="handleEditProject(project)">
                                                <edit-outlined /> 编辑
                                            </a-menu-item>
                                            <a-menu-item @click="handleViewProject(project)">
                                                <eye-outlined /> 查看详情
                                            </a-menu-item>
                                            <a-menu-divider />
                                            <a-menu-item @click="handleDeleteProject(project)" danger>
                                                <delete-outlined /> 删除
                                            </a-menu-item>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                            </template>

                            <div class="card-content">
                                <div class="project-description">{{ project.description }}</div>

                                <div class="project-progress">
                                    <div class="progress-info">
                                        <span class="progress-label">进度:</span>
                                        <span class="progress-value">{{ project.progress }}%</span>
                                    </div>
                                    <a-progress :percent="project.progress" size="small"
                                        :stroke-color="getProgressColor(project.progress)" />
                                </div>

                                <div class="project-details">
                                    <div class="detail-row">
                                        <div class="detail-item">
                                            <user-outlined />
                                            <span>{{ project.manager }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <calendar-outlined />
                                            <span>{{ formatDate(project.startDate) }}</span>
                                        </div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-item">
                                            <team-outlined />
                                            <span>{{ project.teamSize }}人</span>
                                        </div>
                                        <div class="detail-item">
                                            <clock-circle-outlined />
                                            <span>{{ project.duration }}天</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="project-priority">
                                    <a-tag :color="getPriorityColor(project.priority)">
                                        {{ getPriorityText(project.priority) }}优先级
                                    </a-tag>
                                </div>
                            </div>
                        </a-card>
                    </a-col>
                </a-row>

                <div v-if="!filteredProjects.length && !loading" class="empty-state">
                    <a-empty description="暂无项目数据" />
                </div>
            </div>

            <!-- 表格视图 -->
            <div v-if="viewMode === 'table'" class="table-view">
                <a-table :columns="tableColumns" :data-source="filteredProjects" :pagination="paginationConfig"
                    :scroll="{ x: 1200 }" @change="handleTableChange">
                    <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'name'">
                            <div class="project-name-cell">
                                <span class="name-text" @click="handleProjectClick(record)">{{ record.name }}</span>
                                <div class="name-tags">
                                    <a-tag :color="getStageColor(record.stage)" size="small">
                                        {{ getStageText(record.stage) }}
                                    </a-tag>
                                </div>
                            </div>
                        </template>

                        <template v-if="column.key === 'status'">
                            <a-tag :color="getStatusColor(record.status)">
                                {{ getStatusText(record.status) }}
                            </a-tag>
                        </template>

                        <template v-if="column.key === 'progress'">
                            <div class="progress-cell">
                                <a-progress :percent="record.progress" size="small"
                                    :stroke-color="getProgressColor(record.progress)" />
                                <span class="progress-text">{{ record.progress }}%</span>
                            </div>
                        </template>

                        <template v-if="column.key === 'priority'">
                            <a-tag :color="getPriorityColor(record.priority)">
                                {{ getPriorityText(record.priority) }}
                            </a-tag>
                        </template>

                        <template v-if="column.key === 'actions'">
                            <a-space>
                                <a-button type="link" size="small" @click="handleViewProject(record)">
                                    查看
                                </a-button>
                                <a-button type="link" size="small" @click="handleEditProject(record)">
                                    编辑
                                </a-button>
                                <a-popconfirm title="确定要删除这个项目吗？" @confirm="handleDeleteProject(record)">
                                    <a-button type="link" size="small" danger>删除</a-button>
                                </a-popconfirm>
                            </a-space>
                        </template>
                    </template>
                </a-table>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    PlusOutlined,
    SearchOutlined,
    DownloadOutlined,
    MoreOutlined,
    EditOutlined,
    EyeOutlined,
    DeleteOutlined,
    UserOutlined,
    CalendarOutlined,
    TeamOutlined,
    ClockCircleOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    projectsData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'project-click': [project: any]
    'create-project': []
    'edit-project': [project: any]
    'delete-project': [project: any]
    'export': []
}>()

// 响应式数据
const searchKeyword = ref('')
const selectedStage = ref('')
const selectedStatus = ref('')
const selectedPriority = ref('')
const viewMode = ref('card')

// 分页配置
const paginationConfig = {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`
}

// 表格列配置
const tableColumns = [
    {
        title: '项目名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        fixed: 'left'
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
    },
    {
        title: '进度',
        dataIndex: 'progress',
        key: 'progress',
        width: 150
    },
    {
        title: '负责人',
        dataIndex: 'manager',
        key: 'manager',
        width: 120
    },
    {
        title: '团队规模',
        dataIndex: 'teamSize',
        key: 'teamSize',
        width: 100
    },
    {
        title: '优先级',
        dataIndex: 'priority',
        key: 'priority',
        width: 100
    },
    {
        title: '开始时间',
        dataIndex: 'startDate',
        key: 'startDate',
        width: 120
    },
    {
        title: '预计完成',
        dataIndex: 'expectedEndDate',
        key: 'expectedEndDate',
        width: 120
    },
    {
        title: '操作',
        key: 'actions',
        width: 150,
        fixed: 'right'
    }
]

// 计算属性
const filteredProjects = computed(() => {
    if (!props.projectsData?.projects) return []
    let filtered = props.projectsData.projects

    if (searchKeyword.value) {
        filtered = filtered.filter((p: any) =>
            p.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
            p.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    if (selectedStage.value) {
        filtered = filtered.filter((p: any) => p.stage === selectedStage.value)
    }

    if (selectedStatus.value) {
        filtered = filtered.filter((p: any) => p.status === selectedStatus.value)
    }

    if (selectedPriority.value) {
        filtered = filtered.filter((p: any) => p.priority === selectedPriority.value)
    }

    return filtered
})

// 方法
const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
}

const handleStageFilter = (stage: string) => {
    selectedStage.value = stage
}

const handleStatusFilter = (status: string) => {
    selectedStatus.value = status
}

const handlePriorityFilter = (priority: string) => {
    selectedPriority.value = priority
}

const handleResetFilters = () => {
    searchKeyword.value = ''
    selectedStage.value = ''
    selectedStatus.value = ''
    selectedPriority.value = ''
}

const handleViewModeChange = (mode: string) => {
    viewMode.value = mode
}

const handleCreateProject = () => {
    emit('create-project')
}

const handleProjectClick = (project: any) => {
    emit('project-click', project)
}

const handleEditProject = (project: any) => {
    emit('edit-project', project)
}

const handleViewProject = (project: any) => {
    emit('project-click', project)
}

const handleDeleteProject = (project: any) => {
    emit('delete-project', project)
    message.success(`项目 "${project.name}" 已删除`)
}

const handleExportList = () => {
    emit('export')
    message.success('正在导出项目列表...')
}

const handleTableChange = (pagination: any) => {
    paginationConfig.current = pagination.current
    paginationConfig.pageSize = pagination.pageSize
}

// 样式辅助方法
const getProjectCardClass = (project: any) => {
    return {
        'high-priority': project.priority === 'high',
        'completed': project.status === 'completed',
        'delayed': project.isDelayed
    }
}

const getStageColor = (stage: string) => {
    const colorMap: Record<string, string> = {
        'selection': 'blue',
        'cultivation': 'green',
        'establishment': 'orange',
        'promotion': 'purple'
    }
    return colorMap[stage] || 'default'
}

const getStageText = (stage: string) => {
    const textMap: Record<string, string> = {
        'selection': '选拔',
        'cultivation': '培育',
        'establishment': '树立',
        'promotion': '推广'
    }
    return textMap[stage] || stage
}

const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'planning': 'blue',
        'executing': 'processing',
        'completed': 'success',
        'paused': 'default'
    }
    return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'planning': '规划中',
        'executing': '执行中',
        'completed': '已完成',
        'paused': '已暂停'
    }
    return textMap[status] || status
}

const getPriorityColor = (priority: string) => {
    const colorMap: Record<string, string> = {
        'high': 'red',
        'medium': 'orange',
        'low': 'green'
    }
    return colorMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
    const textMap: Record<string, string> = {
        'high': '高',
        'medium': '中',
        'low': '低'
    }
    return textMap[priority] || priority
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped lang="scss">
.project-list {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding: 0 24px;
        padding-top: 16px;

        .list-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
    }

    .list-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 0 24px;

        .list-filters {
            margin-bottom: 16px;
            padding: 16px;
            background: #fafafa;
            border-radius: 6px;
            border: 1px solid #f0f0f0;

            // 优化下拉框样式
            :deep(.ant-select) {
                .ant-select-selector {
                    border-radius: 6px;
                    border-color: #d9d9d9;

                    &:hover {
                        border-color: #40a9ff;
                    }
                }

                &.ant-select-focused {
                    .ant-select-selector {
                        border-color: #40a9ff;
                        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    }
                }
            }

            // 确保下拉框有足够宽度显示内容
            :deep(.ant-select-dropdown) {
                min-width: 140px !important;

                .ant-select-item {
                    padding: 8px 12px;
                    white-space: nowrap;
                }
            }
        }

        .list-view-toggle {
            margin-bottom: 16px;
            display: flex;
            justify-content: flex-end;
        }

        .card-view {
            flex: 1;
            overflow-y: auto;

            .project-card {
                height: 280px;
                cursor: pointer;
                transition: all 0.3s ease;
                border-radius: 8px;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                &.high-priority {
                    border-left: 4px solid #f5222d;
                }

                &.completed {
                    background: #f6ffed;
                    border-color: #52c41a;
                }

                &.delayed {
                    background: #fff2f0;
                    border-color: #ff7875;
                }

                .card-title {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;

                    .project-name {
                        font-size: 16px;
                        font-weight: 600;
                        color: #262626;
                    }

                    .project-tags {
                        display: flex;
                        gap: 8px;
                    }
                }

                .card-content {
                    height: 180px;
                    display: flex;
                    flex-direction: column;
                    gap: 12px;

                    .project-description {
                        color: #666;
                        font-size: 13px;
                        line-height: 1.5;
                        overflow: hidden;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }

                    .project-progress {
                        .progress-info {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 4px;
                            font-size: 12px;

                            .progress-label {
                                color: #666;
                            }

                            .progress-value {
                                color: #262626;
                                font-weight: 500;
                            }
                        }
                    }

                    .project-details {
                        .detail-row {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 8px;

                            .detail-item {
                                display: flex;
                                align-items: center;
                                gap: 4px;
                                font-size: 12px;
                                color: #666;

                                .anticon {
                                    color: #999;
                                }
                            }
                        }
                    }

                    .project-priority {
                        margin-top: auto;
                    }
                }
            }

            .empty-state {
                height: 300px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .table-view {
            flex: 1;
            overflow: hidden;

            .ant-table {
                .project-name-cell {
                    .name-text {
                        cursor: pointer;
                        color: #1890ff;
                        font-weight: 500;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    .name-tags {
                        margin-top: 4px;
                    }
                }

                .progress-cell {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    .progress-text {
                        font-size: 12px;
                        color: #666;
                        min-width: 35px;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .project-list {
        .list-content {
            .card-view {
                .ant-row {
                    .ant-col {
                        span: 12 !important;
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .project-list {
        padding: 16px;

        .list-content {
            .list-filters {
                .ant-row {
                    .ant-col {
                        span: 24 !important;
                        margin-bottom: 8px;
                    }
                }
            }

            .card-view {
                .ant-row {
                    .ant-col {
                        span: 24 !important;
                    }
                }

                .project-card {
                    height: auto;
                    min-height: 200px;

                    .card-content {
                        height: auto;

                        .project-details {
                            .detail-row {
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 4px;
                            }
                        }
                    }
                }
            }

            .table-view {
                .ant-table {
                    .ant-table-scroll {
                        overflow-x: auto;
                    }
                }
            }
        }
    }
}
</style>