<template>
    <div class="achievements-section">
        <a-card title="选育树推项目创建成果" class="achievements-card">
            <a-list :dataSource="achievementsData" :loading="loading" item-layout="horizontal">
                <template #renderItem="{ item }">
                    <a-list-item>
                        <a-list-item-meta>
                            <template #title>{{ item.projectName }}</template>
                            <template #description>{{ item.honorTitle }}</template>
                            <template #avatar>
                                <a-avatar>{{ item.unitName.substring(0, 1) }}</a-avatar>
                            </template>
                        </a-list-item-meta>
                        <div class="achievement-unit">{{ item.unitName }}</div>
                    </a-list-item>
                </template>
            </a-list>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 定义属性
const props = defineProps({
    selectedStage: {
        type: String,
        default: ''
    },
    selectedDistrict: {
        type: String,
        default: ''
    }
})

// 响应式数据
const loading = ref(false)
const achievementsData = ref([])

// 加载数据
const loadAchievementsData = async () => {
    loading.value = true
    try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 900))

        // 模拟数据
        achievementsData.value = [
            { id: 1, unitName: '渝中区行政服务中心', projectName: '智慧政务服务创新项目', honorTitle: '市级数字政府建设标杆单位' },
            { id: 2, unitName: '江北区机关事务管理局', projectName: '机关效能提升工程', honorTitle: '市级机关效能建设示范点' },
            { id: 3, unitName: '南岸区政务服务中心', projectName: '基层治理数字化转型项目', honorTitle: '全国基层治理创新示范区' },
            { id: 4, unitName: '九龙坡区行政审批局', projectName: '"互联网+政务服务"创新实践', honorTitle: '市级政务服务创新示范单位' },
            { id: 5, unitName: '沙坪坝区政务服务中心', projectName: '一站式服务改革项目', honorTitle: '全国政务服务标准化示范点' },
            { id: 6, unitName: '大渡口区社会保障局', projectName: '社保服务一体化项目', honorTitle: '市级民生服务示范窗口' },
        ]
    } catch (error) {
        message.error('加载成果数据失败')
        console.error(error)
    } finally {
        loading.value = false
    }
}

// 暴露方法给父组件
defineExpose({
    loadAchievementsData
})

// 初始化
onMounted(() => {
    loadAchievementsData()
})
</script>

<style scoped lang="scss">
.achievements-section {
    margin-top: 24px;

    .achievements-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }

    .achievement-unit {
        color: #666;
        font-size: 14px;
    }
}
</style>