<template>
    <div class="project-timeline">
        <div class="timeline-header">
            <h3 class="timeline-title">项目时间线</h3>
            <div class="timeline-controls">
                <a-space>
                    <a-select v-model:value="selectedView" style="width: 120px">
                        <a-select-option value="all">全部项目</a-select-option>
                        <a-select-option value="active">进行中</a-select-option>
                        <a-select-option value="completed">已完成</a-select-option>
                        <a-select-option value="delayed">延期项目</a-select-option>
                    </a-select>
                    <a-select v-model:value="selectedTimeScale" style="width: 100px">
                        <a-select-option value="month">月视图</a-select-option>
                        <a-select-option value="quarter">季度</a-select-option>
                        <a-select-option value="year">年视图</a-select-option>
                    </a-select>
                    <a-button @click="handleToday">
                        <template #icon><calendar-outlined /></template>
                        今天
                    </a-button>
                </a-space>
            </div>
        </div>

        <div class="timeline-content" v-loading="loading">
            <!-- 时间轴导航 -->
            <div class="timeline-navigation">
                <div class="nav-controls">
                    <a-button @click="handlePrevPeriod">
                        <template #icon><left-outlined /></template>
                    </a-button>
                    <span class="current-period">{{ currentPeriodText }}</span>
                    <a-button @click="handleNextPeriod">
                        <template #icon><right-outlined /></template>
                    </a-button>
                </div>
                <div class="timeline-scale">
                    <div class="scale-marks">
                        <div v-for="mark in timeMarks" :key="mark.id" class="scale-mark"
                            :class="{ active: mark.isToday }" :style="{ left: mark.position + '%' }">
                            <div class="mark-line"></div>
                            <div class="mark-label">{{ mark.label }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目时间线 -->
            <div class="timeline-projects">
                <div v-for="project in filteredProjects" :key="project.id" class="project-timeline-item"
                    @click="handleProjectClick(project)">
                    <div class="project-info">
                        <div class="project-header">
                            <span class="project-name">{{ project.name }}</span>
                            <div class="project-tags">
                                <a-tag :color="getStageColor(project.stage)">{{ getStageText(project.stage) }}</a-tag>
                                <a-tag :color="getStatusColor(project.status)">{{ getStatusText(project.status)
                                    }}</a-tag>
                            </div>
                        </div>
                        <div class="project-details">
                            <span class="project-progress">进度: {{ project.progress }}%</span>
                        </div>
                    </div>

                    <div class="project-timeline-bar">
                        <div class="timeline-track">
                            <!-- 项目时间条 -->
                            <div class="project-bar" :style="getProjectBarStyle(project)"
                                :class="getProjectBarClass(project)">
                                <div class="bar-progress" :style="{ width: project.progress + '%' }"></div>
                                <div class="bar-content">
                                    <span class="bar-text">{{ project.name }}</span>
                                </div>
                            </div>

                            <!-- 里程碑标记 -->
                            <div v-for="milestone in project.milestones" :key="milestone.id" class="milestone-marker"
                                :style="{ left: getMilestonePosition(milestone.date) + '%' }"
                                :class="{ completed: milestone.isCompleted, critical: milestone.isCritical }">
                                <a-tooltip :title="milestone.name + ' - ' + formatDate(milestone.date)">
                                    <div class="milestone-dot">
                                        <check-circle-filled v-if="milestone.isCompleted" />
                                        <exclamation-circle-filled v-else-if="milestone.isCritical" />
                                        <clock-circle-outlined v-else />
                                    </div>
                                </a-tooltip>
                            </div>

                            <!-- 依赖关系线 -->
                            <div v-for="dependency in project.dependencies" :key="dependency.id" class="dependency-line"
                                :style="getDependencyLineStyle(dependency)">
                            </div>
                        </div>
                    </div>

                    <div class="project-actions">
                        <a-dropdown>
                            <a-button type="text" size="small">
                                <template #icon><more-outlined /></template>
                            </a-button>
                            <template #overlay>
                                <a-menu>
                                    <a-menu-item @click="handleViewProject(project)">
                                        <eye-outlined /> 查看详情
                                    </a-menu-item>
                                    <!-- <a-menu-item @click="handleEditProject(project)">
                                        <edit-outlined /> 编辑项目
                                    </a-menu-item>
                                    <a-menu-divider />
                                    <a-menu-item @click="handleAddMilestone(project)">
                                        <plus-outlined /> 添加里程碑
                                    </a-menu-item>
                                    <a-menu-item @click="handleSetDependency(project)">
                                        <link-outlined /> 设置依赖
                                    </a-menu-item> -->
                                </a-menu>
                            </template>
                        </a-dropdown>
                    </div>
                </div>

                <div v-if="!filteredProjects.length && !loading" class="empty-timeline">
                    <a-empty description="暂无项目数据" />
                </div>
            </div>

            <!-- 时间线统计 -->
            <div class="timeline-statistics">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-card class="stat-card">
                            <a-statistic title="总项目数" :value="timelineData?.totalProjects || 0"
                                :value-style="{ color: '#1890ff' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card">
                            <a-statistic title="进行中" :value="timelineData?.activeProjects || 0"
                                :value-style="{ color: '#52c41a' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card">
                            <a-statistic title="即将到期" :value="timelineData?.upcomingDeadlines || 0"
                                :value-style="{ color: '#faad14' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card">
                            <a-statistic title="延期项目" :value="timelineData?.delayedProjects || 0"
                                :value-style="{ color: '#f5222d' }" />
                        </a-card>
                    </a-col>
                </a-row>
            </div>
        </div>

        <!-- 项目详情弹窗 -->
        <a-modal v-model:open="showProjectModal" :title="`项目详情: ${currentProject?.name || ''}`" width="700px"
            :footer="null">
            <div v-if="currentProject">
                <div style="margin-bottom: 16px;">
                    <p>项目名称: {{ currentProject.name }}</p>
                    <p>发布时间: {{ formatDate(currentProject.startDate) }}</p>
                    <p>结束时间: {{ formatDate(currentProject.endDate) }}</p>
                    <p>项目进度: {{ currentProject.progress }}%</p>
                    <p>项目状态: {{ getStatusText(currentProject.status) }}</p>
                </div>
                <div>
                    <h4 style="margin-bottom: 12px;">项目指标列表</h4>
                    <a-table :columns="indicatorColumns" :data-source="projectIndicators" row-key="id"
                        :pagination="{ pageSize: 10 }" size="small" />
                </div>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
    CalendarOutlined,
    LeftOutlined,
    RightOutlined,
    MoreOutlined,
    EyeOutlined,
    EditOutlined,
    PlusOutlined,
    LinkOutlined,
    CheckCircleFilled,
    ExclamationCircleFilled,
    ClockCircleOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    timelineData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits([
    'project-click',
    'view-project',
    'edit-project',
    'add-milestone',
    'set-dependency',
    'milestone-click'
])

// 响应式数据
const selectedView = ref('all')
const selectedTimeScale = ref('month')
const currentDate = ref(new Date())
const showProjectModal = ref(false)
const currentProject = ref<any>(null)
const projectIndicators = ref<any[]>([])

// 计算属性
const currentPeriodText = computed(() => {
    const date = currentDate.value
    switch (selectedTimeScale.value) {
        case 'month':
            return `${date.getFullYear()}年${date.getMonth() + 1}月`
        case 'quarter':
            const quarter = Math.floor(date.getMonth() / 3) + 1
            return `${date.getFullYear()}年第${quarter}季度`
        case 'year':
            return `${date.getFullYear()}年`
        default:
            return ''
    }
})

const timeMarks = computed(() => {
    // 根据时间刻度生成时间标记
    const marks = []
    const today = new Date()
    const scale = selectedTimeScale.value

    if (scale === 'month') {
        // 生成月份的日期标记
        const daysInMonth = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 0).getDate()
        for (let i = 1; i <= daysInMonth; i += 5) {
            marks.push({
                id: i,
                label: `${i}日`,
                position: (i / daysInMonth) * 100,
                isToday: today.getDate() === i &&
                    today.getMonth() === currentDate.value.getMonth() &&
                    today.getFullYear() === currentDate.value.getFullYear()
            })
        }
    } else if (scale === 'quarter') {
        // 生成季度的月份标记
        const startMonth = Math.floor(currentDate.value.getMonth() / 3) * 3
        for (let i = 0; i < 3; i++) {
            const month = startMonth + i + 1
            marks.push({
                id: month,
                label: `${month}月`,
                position: (i / 3) * 100,
                isToday: today.getMonth() === startMonth + i &&
                    today.getFullYear() === currentDate.value.getFullYear()
            })
        }
    } else if (scale === 'year') {
        // 生成年份的季度标记
        for (let i = 1; i <= 4; i++) {
            marks.push({
                id: i,
                label: `Q${i}`,
                position: ((i - 1) / 4) * 100,
                isToday: Math.floor(today.getMonth() / 3) + 1 === i &&
                    today.getFullYear() === currentDate.value.getFullYear()
            })
        }
    }

    return marks
})

const filteredProjects = computed(() => {
    if (!props.timelineData?.projects) return []

    let filtered = props.timelineData.projects

    switch (selectedView.value) {
        case 'active':
            filtered = filtered.filter((p: any) => p.status === 'active')
            break
        case 'completed':
            filtered = filtered.filter((p: any) => p.status === 'completed')
            break
        case 'delayed':
            filtered = filtered.filter((p: any) => p.isDelayed)
            break
    }

    return filtered
})

// 方法
const handleToday = () => {
    currentDate.value = new Date()
    message.success('已跳转到今天')
}

const handlePrevPeriod = () => {
    const date = new Date(currentDate.value)
    switch (selectedTimeScale.value) {
        case 'month':
            date.setMonth(date.getMonth() - 1)
            break
        case 'quarter':
            date.setMonth(date.getMonth() - 3)
            break
        case 'year':
            date.setFullYear(date.getFullYear() - 1)
            break
    }
    currentDate.value = date
}

const handleNextPeriod = () => {
    const date = new Date(currentDate.value)
    switch (selectedTimeScale.value) {
        case 'month':
            date.setMonth(date.getMonth() + 1)
            break
        case 'quarter':
            date.setMonth(date.getMonth() + 3)
            break
        case 'year':
            date.setFullYear(date.getFullYear() + 1)
            break
    }
    currentDate.value = date
}

const handleProjectClick = (project: any) => {
    emit('project-click', project)
}

const handleViewProject = (project: any) => {
    emit('view-project', project)

    // 显示项目详情弹窗
    showProjectDetailModal(project)
}

const handleEditProject = (project: any) => {
    emit('edit-project', project)
}

const handleAddMilestone = (project: any) => {
    emit('add-milestone', project)
    message.info(`为项目 "${project.name}" 添加里程碑`)
}

const handleSetDependency = (project: any) => {
    emit('set-dependency', project)
    message.info(`设置项目 "${project.name}" 的依赖关系`)
}

// 处理里程碑点击
const handleMilestoneClick = (milestone: any) => {
    emit('milestone-click', milestone)

    // 模拟指标数据
    const indicatorData = {
        id: milestone.id,
        name: milestone.name,
        description: milestone.description || '指标详情描述',
        date: milestone.date,
        units: [
            { id: 1, unitName: '渝中区政府办', score: 95, isCompleted: true },
            { id: 2, unitName: '江北区政府办', score: 88, isCompleted: true },
            { id: 3, unitName: '南岸区政府办', score: 65, isCompleted: false },
            { id: 4, unitName: '九龙坡区政府办', score: 92, isCompleted: true },
            { id: 5, unitName: '沙坪坝区政府办', score: 60, isCompleted: false },
            { id: 6, unitName: '大渡口区政府办', score: 85, isCompleted: true },
        ]
    }

    // 显示指标详情弹窗
    // showIndicatorDetailModal(indicatorData)
}

// 指标表格列定义
const indicatorColumns = [
    {
        title: '指标名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '截止日期',
        dataIndex: 'date',
        key: 'date',
        customRender: ({ text }: { text: string }) => formatDate(text)
    },
    {
        title: '完成单位',
        dataIndex: 'completedUnits',
        key: 'completedUnits',
        customRender: ({ text, record }: { text: number, record: any }) => {
            return `${text || 0}/${record.totalUnits || 20}`
        }
    },
    {
        title: '完成率',
        dataIndex: 'completionRate',
        key: 'completionRate',
        customRender: ({ text }: { text: number }) => {
            return h('span', {
                style: {
                    color: text >= 80 ? '#52c41a' : text >= 60 ? '#faad14' : '#f5222d',
                    fontWeight: 'bold'
                }
            }, `${text || 0}%`)
        }
    }
]

// 显示项目详情弹窗
const showProjectDetailModal = (project: any) => {
    // 设置当前项目数据
    currentProject.value = project

    // 模拟项目指标数据
    projectIndicators.value = project.milestones || [
        {
            id: 1,
            name: '需求分析完成',
            date: project.startDate,
            completedUnits: 18,
            totalUnits: 20,
            completionRate: 90
        },
        {
            id: 2,
            name: '系统设计完成',
            date: project.startDate,
            completedUnits: 16,
            totalUnits: 20,
            completionRate: 80
        },
        {
            id: 3,
            name: '开发阶段完成',
            date: project.endDate,
            completedUnits: project.progress > 50 ? 12 : 8,
            totalUnits: 20,
            completionRate: project.progress > 50 ? 60 : 40
        },
        {
            id: 4,
            name: '测试阶段完成',
            date: project.endDate,
            completedUnits: project.progress > 80 ? 15 : 6,
            totalUnits: 20,
            completionRate: project.progress > 80 ? 75 : 30
        },
        {
            id: 5,
            name: '项目上线部署',
            date: project.endDate,
            completedUnits: project.status === 'completed' ? 20 : 0,
            totalUnits: 20,
            completionRate: project.status === 'completed' ? 100 : 0
        }
    ]

    // 显示弹窗
    showProjectModal.value = true
}

// 显示指标详情弹窗
const showIndicatorDetailModal = (indicator: any) => {
    // 创建表格列定义
    const columns = [
        {
            title: '单位名称',
            dataIndex: 'unitName',
            key: 'unitName',
        },
        {
            title: '得分',
            dataIndex: 'score',
            key: 'score',
        },
        {
            title: '是否完成',
            dataIndex: 'isCompleted',
            key: 'isCompleted',
            customRender: ({ text }: { text: boolean }) => {
                return h('span', {
                    style: {
                        color: text ? '#52c41a' : '#f5222d',
                        fontWeight: 'bold'
                    }
                }, text ? '已完成' : '未完成')
            }
        }
    ]

    // 使用普通的模态框而不是函数式调用
    Modal.info({
        title: `指标详情: ${indicator.name}`,
        width: 700,
        content: h('div', [
            h('div', { style: { marginBottom: '16px' } }, [
                h('p', `指标描述: ${indicator.description}`),
                h('p', `截止日期: ${formatDate(indicator.date)}`)
            ]),
            h('a-table', {
                columns: columns,
                dataSource: indicator.units,
                rowKey: 'id',
                pagination: { pageSize: 10 },
                size: 'small'
            })
        ]),
        onOk() { }
    })
}

// 样式计算方法
const getStageColor = (stage: string) => {
    const colorMap: Record<string, string> = {
        'selection': 'blue',
        'cultivation': 'green',
        'establishment': 'orange',
        'promotion': 'purple'
    }
    return colorMap[stage] || 'default'
}

const getStageText = (stage: string) => {
    const textMap: Record<string, string> = {
        'selection': '选拔',
        'cultivation': '培育',
        'establishment': '树立',
        'promotion': '推广'
    }
    return textMap[stage] || stage
}

const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'planning': 'blue',
        'active': 'processing',
        'completed': 'success',
        'paused': 'default',
        'delayed': 'error'
    }
    return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'planning': '规划中',
        'active': '进行中',
        'completed': '已完成',
        'paused': '已暂停',
        'delayed': '已延期'
    }
    return textMap[status] || status
}

const getProjectBarStyle = (project: any) => {
    const startDate = new Date(project.startDate)
    const endDate = new Date(project.endDate)
    const currentPeriodStart = getPeriodStart()
    const currentPeriodEnd = getPeriodEnd()

    const totalDuration = currentPeriodEnd.getTime() - currentPeriodStart.getTime()
    const projectStart = Math.max(startDate.getTime(), currentPeriodStart.getTime())
    const projectEnd = Math.min(endDate.getTime(), currentPeriodEnd.getTime())

    const left = ((projectStart - currentPeriodStart.getTime()) / totalDuration) * 100
    const width = ((projectEnd - projectStart) / totalDuration) * 100

    return {
        left: `${Math.max(0, left)}%`,
        width: `${Math.max(0, width)}%`
    }
}

const getProjectBarClass = (project: any) => {
    return {
        'bar-active': project.status === 'active',
        'bar-completed': project.status === 'completed',
        'bar-delayed': project.isDelayed,
        'bar-critical': project.isCritical
    }
}

const getMilestonePosition = (milestoneDate: string) => {
    const date = new Date(milestoneDate)
    const periodStart = getPeriodStart()
    const periodEnd = getPeriodEnd()

    const totalDuration = periodEnd.getTime() - periodStart.getTime()
    const milestoneTime = date.getTime() - periodStart.getTime()

    return Math.max(0, Math.min(100, (milestoneTime / totalDuration) * 100))
}

const getDependencyLineStyle = (dependency: any) => {
    // 计算依赖关系线的样式
    return {
        // 这里应该根据依赖项目的位置计算线条样式
        display: 'none' // 暂时隐藏，需要复杂的计算逻辑
    }
}

const getPeriodStart = () => {
    const date = new Date(currentDate.value)
    switch (selectedTimeScale.value) {
        case 'month':
            return new Date(date.getFullYear(), date.getMonth(), 1)
        case 'quarter':
            const quarterStart = Math.floor(date.getMonth() / 3) * 3
            return new Date(date.getFullYear(), quarterStart, 1)
        case 'year':
            return new Date(date.getFullYear(), 0, 1)
        default:
            return date
    }
}

const getPeriodEnd = () => {
    const date = new Date(currentDate.value)
    switch (selectedTimeScale.value) {
        case 'month':
            return new Date(date.getFullYear(), date.getMonth() + 1, 0)
        case 'quarter':
            const quarterStart = Math.floor(date.getMonth() / 3) * 3
            return new Date(date.getFullYear(), quarterStart + 3, 0)
        case 'year':
            return new Date(date.getFullYear(), 11, 31)
        default:
            return date
    }
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped lang="scss">
.project-timeline {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .timeline-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .timeline-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .timeline-navigation {
            margin-bottom: 20px;
            padding: 16px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e8e8e8;

            .nav-controls {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 16px;
                margin-bottom: 16px;

                .current-period {
                    font-size: 16px;
                    font-weight: 600;
                    color: #262626;
                    min-width: 120px;
                    text-align: center;
                }
            }

            .timeline-scale {
                position: relative;
                height: 40px;

                .scale-marks {
                    position: relative;
                    height: 100%;
                    border-bottom: 2px solid #e8e8e8;

                    .scale-mark {
                        position: absolute;
                        top: 0;
                        transform: translateX(-50%);

                        &.active {
                            .mark-line {
                                background: #1890ff;
                                height: 20px;
                            }

                            .mark-label {
                                color: #1890ff;
                                font-weight: 600;
                            }
                        }

                        .mark-line {
                            width: 2px;
                            height: 15px;
                            background: #d9d9d9;
                            margin: 0 auto 5px;
                        }

                        .mark-label {
                            font-size: 12px;
                            color: #666;
                            text-align: center;
                            white-space: nowrap;
                        }
                    }
                }
            }
        }

        .timeline-projects {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;

            .project-timeline-item {
                display: flex;
                align-items: center;
                padding: 16px;
                margin-bottom: 12px;
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    border-color: #1890ff;
                }

                .project-info {
                    width: 200px;
                    flex-shrink: 0;
                    padding-right: 16px;

                    .project-header {
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        margin-bottom: 8px;

                        .project-name {
                            font-size: 14px;
                            font-weight: 600;
                            color: #262626;
                            line-height: 1.4;
                        }

                        .project-tags {
                            display: flex;
                            gap: 4px;
                            flex-wrap: wrap;
                        }
                    }

                    .project-details {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                        font-size: 12px;
                        color: #666;
                    }
                }

                .project-timeline-bar {
                    flex: 1;
                    padding: 0 16px;

                    .timeline-track {
                        position: relative;
                        height: 40px;
                        background: #f5f5f5;
                        border-radius: 4px;

                        .project-bar {
                            position: absolute;
                            top: 8px;
                            height: 24px;
                            background: #1890ff;
                            border-radius: 4px;
                            overflow: hidden;
                            transition: all 0.3s ease;

                            &.bar-active {
                                background: #52c41a;
                            }

                            &.bar-completed {
                                background: #722ed1;
                            }

                            &.bar-delayed {
                                background: #f5222d;
                            }

                            &.bar-critical {
                                background: #fa8c16;
                                animation: pulse 2s infinite;
                            }

                            .bar-progress {
                                height: 100%;
                                background: rgba(255, 255, 255, 0.3);
                                transition: width 0.3s ease;
                            }

                            .bar-content {
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                bottom: 0;
                                display: flex;
                                align-items: center;
                                padding: 0 8px;

                                .bar-text {
                                    color: white;
                                    font-size: 12px;
                                    font-weight: 500;
                                    white-space: nowrap;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                }
                            }
                        }

                        .milestone-marker {
                            position: absolute;
                            top: 4px;
                            transform: translateX(-50%);
                            z-index: 10;

                            .milestone-dot {
                                width: 16px;
                                height: 16px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                background: white;
                                border: 2px solid #d9d9d9;
                                border-radius: 50%;
                                font-size: 10px;
                                color: #666;
                            }

                            &.completed {
                                .milestone-dot {
                                    border-color: #52c41a;
                                    color: #52c41a;
                                }
                            }

                            &.critical {
                                .milestone-dot {
                                    border-color: #f5222d;
                                    color: #f5222d;
                                    animation: pulse 2s infinite;
                                }
                            }
                        }

                        .dependency-line {
                            position: absolute;
                            height: 2px;
                            background: #faad14;
                            z-index: 5;
                        }
                    }
                }

                .project-actions {
                    width: 40px;
                    flex-shrink: 0;
                    display: flex;
                    justify-content: center;
                }
            }

            .empty-timeline {
                height: 200px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .timeline-statistics {
            .stat-card {
                text-align: center;
                border-radius: 8px;
            }
        }
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}
</style>