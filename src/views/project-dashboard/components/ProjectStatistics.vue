<template>
    <div class="project-statistics">
        <a-spin :spinning="loading" tip="加载统计数据中...">
            <div v-if="statistics" class="statistics-content">
                <!-- 统计卡片 -->
                <a-row :gutter="[16, 16]" class="stats-cards">
                    <a-col :xs="12" :sm="6">
                        <div class="stat-card total">
                            <div class="stat-icon">
                                <project-outlined />
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">{{ statistics.totalProjects }}</div>
                                <div class="stat-label">总项目数</div>
                            </div>
                        </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                        <div class="stat-card active">
                            <div class="stat-icon">
                                <rocket-outlined />
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">{{ statistics.activeProjects }}</div>
                                <div class="stat-label">进行中</div>
                            </div>
                        </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                        <div class="stat-card completed">
                            <div class="stat-icon">
                                <check-circle-outlined />
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">{{ statistics.completedProjects }}</div>
                                <div class="stat-label">已完成</div>
                            </div>
                        </div>
                    </a-col>
                    <a-col :xs="12" :sm="6">
                        <div class="stat-card units">
                            <div class="stat-icon">
                                <team-outlined />
                            </div>
                            <div class="stat-info">
                                <div class="stat-value">{{ statistics.participatingUnits }}</div>
                                <div class="stat-label">参与单位</div>
                            </div>
                        </div>
                    </a-col>
                </a-row>

                <!-- 图表区域 -->
                <div class="charts-section">
                    <a-row :gutter="[24, 24]">
                        <!-- 阶段分布图 -->
                        <a-col :xs="24" :lg="12">
                            <div class="chart-container">
                                <h4 class="chart-title">项目阶段分布</h4>
                                <div ref="stageChartRef" class="chart" style="height: 200px;"></div>
                            </div>
                        </a-col>

                        <!-- 完成率趋势 -->
                        <a-col :xs="24" :lg="12">
                            <div class="chart-container">
                                <h4 class="chart-title">月度完成率趋势</h4>
                                <div ref="trendChartRef" class="chart" style="height: 200px;"></div>
                            </div>
                        </a-col>
                    </a-row>
                </div>

                <!-- 详细统计表格 -->
                <div class="detailed-stats">
                    <h4 class="section-title">各阶段详细统计</h4>
                    <a-table :columns="stageColumns" :data-source="statistics.stageStats" :pagination="false"
                        size="small" @row-click="handleRowClick">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'stage'">
                                <a-tag :color="getStageColor(record.stage)">
                                    {{ getStageLabel(record.stage) }}
                                </a-tag>
                            </template>
                            <template v-if="column.key === 'progress'">
                                <a-progress :percent="record.completionRate" size="small"
                                    :stroke-color="getProgressColor(record.completionRate)" />
                            </template>
                            <template v-if="column.key === 'action'">
                                <a-button type="link" size="small" @click="handleViewDetails(record)">
                                    查看详情
                                </a-button>
                            </template>
                        </template>
                    </a-table>
                </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-state">
                <a-empty description="暂无统计数据" />
            </div>
        </a-spin>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
    ProjectOutlined,
    RocketOutlined,
    CheckCircleOutlined,
    TeamOutlined
} from '@ant-design/icons-vue'
import type { ProjectStatistics } from '../types'

// Props定义
interface Props {
    statistics?: ProjectStatistics | null
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    statistics: null,
    loading: false
})

// Emits定义
const emit = defineEmits<{
    chartClick: [data: any]
}>()

// 响应式数据
const stageChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 表格列定义
const stageColumns = [
    {
        title: '阶段',
        dataIndex: 'stage',
        key: 'stage',
        width: 120
    },
    {
        title: '项目数量',
        dataIndex: 'count',
        key: 'count',
        width: 100
    },
    {
        title: '参与单位',
        dataIndex: 'units',
        key: 'units',
        width: 100
    },
    {
        title: '完成率',
        dataIndex: 'progress',
        key: 'progress',
        width: 150
    },
    {
        title: '操作',
        key: 'action',
        width: 100
    }
]

// 初始化图表
const initCharts = async () => {
    if (!props.statistics) return

    await nextTick()

    try {
        // 这里可以集成 ECharts 或其他图表库
        // 暂时使用模拟实现
        if (stageChartRef.value) {
            stageChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">阶段分布图表</div>'
        }

        if (trendChartRef.value) {
            trendChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">趋势图表</div>'
        }
    } catch (error) {
        console.error('图表初始化失败:', error)
    }
}

// 事件处理
const handleRowClick = (record: any) => {
    emit('chartClick', { type: 'stage', data: record })
}

const handleViewDetails = (record: any) => {
    message.info(`查看${getStageLabel(record.stage)}详情`)
    emit('chartClick', { type: 'details', data: record })
}

// 工具方法
const getStageLabel = (stage: string) => {
    const stageMap = {
        selection: '选拔遴选',
        cultivation: '培育阶段',
        establishment: '树立阶段',
        promotion: '推广阶段'
    }
    return stageMap[stage as keyof typeof stageMap] || stage
}

const getStageColor = (stage: string) => {
    const colorMap = {
        selection: 'blue',
        cultivation: 'green',
        establishment: 'orange',
        promotion: 'purple'
    }
    return colorMap[stage as keyof typeof colorMap] || 'default'
}

const getProgressColor = (percent: number) => {
    if (percent >= 80) return '#52c41a'
    if (percent >= 60) return '#faad14'
    return '#ff4d4f'
}

// 生命周期
onMounted(() => {
    initCharts()
})

// 监听数据变化
watch(() => props.statistics, () => {
    initCharts()
}, { deep: true })
</script>

<style scoped lang="scss">
.project-statistics {
    .statistics-content {
        .stats-cards {
            margin-bottom: 24px;

            .stat-card {
                display: flex;
                align-items: center;
                padding: 16px;
                background: white;
                border-radius: 8px;
                border: 1px solid #f0f0f0;
                transition: all 0.3s ease;
                cursor: pointer;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                &.total {
                    border-left: 4px solid #1890ff;

                    .stat-icon {
                        color: #1890ff;
                        background: rgba(24, 144, 255, 0.1);
                    }
                }

                &.active {
                    border-left: 4px solid #52c41a;

                    .stat-icon {
                        color: #52c41a;
                        background: rgba(82, 196, 26, 0.1);
                    }
                }

                &.completed {
                    border-left: 4px solid #faad14;

                    .stat-icon {
                        color: #faad14;
                        background: rgba(250, 173, 20, 0.1);
                    }
                }

                &.units {
                    border-left: 4px solid #722ed1;

                    .stat-icon {
                        color: #722ed1;
                        background: rgba(114, 46, 209, 0.1);
                    }
                }

                .stat-icon {
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    margin-right: 12px;
                }

                .stat-info {
                    .stat-value {
                        font-size: 24px;
                        font-weight: 600;
                        color: #262626;
                        line-height: 1;
                        margin-bottom: 4px;
                    }

                    .stat-label {
                        font-size: 14px;
                        color: #666;
                    }
                }
            }
        }

        .charts-section {
            margin-bottom: 24px;

            .chart-container {
                background: white;
                border-radius: 8px;
                padding: 16px;
                border: 1px solid #f0f0f0;

                .chart-title {
                    margin: 0 0 16px 0;
                    font-size: 16px;
                    font-weight: 500;
                    color: #262626;
                }

                .chart {
                    background: #fafafa;
                    border-radius: 4px;
                    border: 1px dashed #d9d9d9;
                }
            }
        }

        .detailed-stats {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f0f0f0;

            .section-title {
                margin: 0 0 16px 0;
                font-size: 16px;
                font-weight: 500;
                color: #262626;
            }
        }
    }

    .empty-state {
        background: white;
        border-radius: 8px;
        padding: 48px;
        text-align: center;
        border: 1px solid #f0f0f0;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .project-statistics {
        .statistics-content {
            .stats-cards {
                .stat-card {
                    padding: 12px;

                    .stat-icon {
                        width: 40px;
                        height: 40px;
                        font-size: 20px;
                        margin-right: 8px;
                    }

                    .stat-info {
                        .stat-value {
                            font-size: 20px;
                        }

                        .stat-label {
                            font-size: 12px;
                        }
                    }
                }
            }

            .charts-section {
                .chart-container {
                    padding: 12px;

                    .chart {
                        height: 150px !important;
                    }
                }
            }
        }
    }
}
</style>