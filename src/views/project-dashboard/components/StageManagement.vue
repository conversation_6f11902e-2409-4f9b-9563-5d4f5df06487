<template>
    <div class="stage-management">
        <div class="management-header">
            <h3 class="management-title">阶段管理</h3>
            <div class="management-actions">
                <a-button type="primary" @click="handleCreateStage">
                    <template #icon><plus-outlined /></template>
                    新建阶段
                </a-button>
            </div>
        </div>

        <div class="management-content" v-loading="loading">
            <div class="stage-overview">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-card class="overview-card">
                            <a-statistic title="总阶段数" :value="stageData?.totalStages || 0"
                                :value-style="{ color: '#1890ff' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="overview-card">
                            <a-statistic title="活跃阶段" :value="stageData?.activeStages || 0"
                                :value-style="{ color: '#52c41a' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="overview-card">
                            <a-statistic title="完成阶段" :value="stageData?.completedStages || 0"
                                :value-style="{ color: '#722ed1' }" />
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="overview-card">
                            <a-statistic title="整体进度" :value="overallProgress" suffix="%"
                                :value-style="{ color: '#faad14' }" />
                        </a-card>
                    </a-col>
                </a-row>
            </div>

            <div class="stage-management-tabs">
                <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
                    <a-tab-pane key="stages" tab="阶段列表">
                        <div class="stages-section">
                            <div class="stages-filters">
                                <a-row :gutter="16">
                                    <a-col :span="6">
                                        <a-input v-model:value="searchKeyword" placeholder="搜索阶段名称" allow-clear>
                                            <template #prefix><search-outlined /></template>
                                        </a-input>
                                    </a-col>
                                    <a-col :span="4">
                                        <a-select v-model:value="selectedStatus" placeholder="阶段状态" style="width: 100%">
                                            <a-select-option value="">全部状态</a-select-option>
                                            <a-select-option value="planning">规划中</a-select-option>
                                            <a-select-option value="active">进行中</a-select-option>
                                            <a-select-option value="completed">已完成</a-select-option>
                                            <a-select-option value="paused">已暂停</a-select-option>
                                        </a-select>
                                    </a-col>
                                    <a-col :span="4">
                                        <a-select v-model:value="selectedType" placeholder="阶段类型" style="width: 100%">
                                            <a-select-option value="">全部类型</a-select-option>
                                            <a-select-option value="selection">选拔阶段</a-select-option>
                                            <a-select-option value="cultivation">培育阶段</a-select-option>
                                            <a-select-option value="establishment">树立阶段</a-select-option>
                                            <a-select-option value="promotion">推广阶段</a-select-option>
                                        </a-select>
                                    </a-col>
                                    <a-col :span="10">
                                        <a-space>
                                            <a-button @click="handleResetFilters">重置筛选</a-button>
                                            <a-button @click="handleBatchOperation">批量操作</a-button>
                                            <a-button @click="handleExportStages">
                                                <template #icon><download-outlined /></template>
                                                导出数据
                                            </a-button>
                                        </a-space>
                                    </a-col>
                                </a-row>
                            </div>

                            <div class="stages-list">
                                <div v-for="stage in filteredStages" :key="stage.id" class="stage-item"
                                    :class="getStageItemClass(stage)" @click="handleStageClick(stage)">
                                    <div class="stage-header">
                                        <div class="stage-info">
                                            <div class="stage-title">
                                                <span class="stage-name">{{ stage.name }}</span>
                                                <div class="stage-tags">
                                                    <a-tag :color="getStageTypeColor(stage.type)">
                                                        {{ getStageTypeText(stage.type) }}
                                                    </a-tag>
                                                    <a-tag :color="getStageStatusColor(stage.status)">
                                                        {{ getStageStatusText(stage.status) }}
                                                    </a-tag>
                                                </div>
                                            </div>
                                            <div class="stage-description">{{ stage.description }}</div>
                                        </div>

                                        <div class="stage-progress">
                                            <div class="progress-info">
                                                <span class="progress-text">{{ stage.progress }}%</span>
                                                <a-progress type="circle" :percent="stage.progress" :width="60"
                                                    :stroke-color="getProgressColor(stage.progress)" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stage-content">
                                        <div class="stage-metrics">
                                            <div class="metric-item">
                                                <span class="metric-label">项目数量:</span>
                                                <span class="metric-value">{{ stage.projectCount }}个</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">负责人:</span>
                                                <span class="metric-value">{{ stage.manager }}</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">开始时间:</span>
                                                <span class="metric-value">{{ formatDate(stage.startDate) }}</span>
                                            </div>
                                            <div class="metric-item">
                                                <span class="metric-label">预计完成:</span>
                                                <span class="metric-value">{{ formatDate(stage.expectedEndDate)
                                                }}</span>
                                            </div>
                                        </div>

                                        <div class="stage-timeline">
                                            <div class="timeline-item" v-for="milestone in stage.milestones"
                                                :key="milestone.id" :class="{ completed: milestone.isCompleted }">
                                                <div class="timeline-dot"></div>
                                                <div class="timeline-content">
                                                    <span class="milestone-name">{{ milestone.name }}</span>
                                                    <span class="milestone-date">{{ formatDate(milestone.date) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="stage-actions">
                                        <a-space>
                                            <a-button size="small" @click.stop="handleEditStage(stage)">
                                                <template #icon><edit-outlined /></template>
                                                编辑
                                            </a-button>
                                            <a-button size="small" @click.stop="handleViewStage(stage)">
                                                <template #icon><eye-outlined /></template>
                                                查看详情
                                            </a-button>
                                            <a-dropdown>
                                                <a-button size="small">
                                                    更多
                                                    <template #icon><down-outlined /></template>
                                                </a-button>
                                                <template #overlay>
                                                    <a-menu>
                                                        <a-menu-item @click="handleDuplicateStage(stage)">
                                                            <copy-outlined /> 复制阶段
                                                        </a-menu-item>
                                                        <a-menu-item @click="handleArchiveStage(stage)">
                                                            <inbox-outlined /> 归档阶段
                                                        </a-menu-item>
                                                        <a-menu-divider />
                                                        <a-menu-item @click="handleDeleteStage(stage)" danger>
                                                            <delete-outlined /> 删除阶段
                                                        </a-menu-item>
                                                    </a-menu>
                                                </template>
                                            </a-dropdown>
                                        </a-space>
                                    </div>
                                </div>

                                <div v-if="!filteredStages.length && !loading" class="empty-state">
                                    <a-empty description="暂无阶段数据" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="templates" tab="阶段模板">
                        <div class="templates-section">
                            <div class="templates-header">
                                <a-button type="primary" @click="handleCreateTemplate">
                                    <template #icon><plus-outlined /></template>
                                    新建模板
                                </a-button>
                            </div>

                            <div class="templates-grid">
                                <a-row :gutter="[16, 16]">
                                    <a-col v-for="template in stageData?.templates || []" :key="template.id" :span="8">
                                        <a-card class="template-card" @click="handleTemplateClick(template)">
                                            <template #title>
                                                <div class="template-title">
                                                    <span class="template-name">{{ template.name }}</span>
                                                    <a-tag :color="getTemplateTypeColor(template.type)">
                                                        {{ template.type }}
                                                    </a-tag>
                                                </div>
                                            </template>

                                            <template #extra>
                                                <a-dropdown>
                                                    <a-button type="text" size="small">
                                                        <template #icon><more-outlined /></template>
                                                    </a-button>
                                                    <template #overlay>
                                                        <a-menu>
                                                            <a-menu-item @click="handleUseTemplate(template)">
                                                                <play-circle-outlined /> 使用模板
                                                            </a-menu-item>
                                                            <a-menu-item @click="handleEditTemplate(template)">
                                                                <edit-outlined /> 编辑模板
                                                            </a-menu-item>
                                                            <a-menu-divider />
                                                            <a-menu-item @click="handleDeleteTemplate(template)" danger>
                                                                <delete-outlined /> 删除模板
                                                            </a-menu-item>
                                                        </a-menu>
                                                    </template>
                                                </a-dropdown>
                                            </template>

                                            <div class="template-content">
                                                <div class="template-description">{{ template.description }}</div>
                                                <div class="template-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-label">包含阶段:</span>
                                                        <span class="stat-value">{{ template.stageCount }}个</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">使用次数:</span>
                                                        <span class="stat-value">{{ template.usageCount }}次</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">创建时间:</span>
                                                        <span class="stat-value">{{ formatDate(template.createTime)
                                                        }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </a-card>
                                    </a-col>
                                </a-row>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="settings" tab="阶段配置">
                        <div class="settings-section">
                            <a-form :model="stageSettings" layout="vertical">
                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item label="默认阶段持续时间">
                                            <a-input-number v-model:value="stageSettings.defaultDuration" :min="1"
                                                :max="365" addon-after="天" style="width: 100%" />
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item label="阶段自动推进">
                                            <a-switch v-model:checked="stageSettings.autoAdvance" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>

                                <a-row :gutter="16">
                                    <a-col :span="12">
                                        <a-form-item label="进度更新频率">
                                            <a-select v-model:value="stageSettings.updateFrequency" style="width: 100%">
                                                <a-select-option value="daily">每日</a-select-option>
                                                <a-select-option value="weekly">每周</a-select-option>
                                                <a-select-option value="monthly">每月</a-select-option>
                                            </a-select>
                                        </a-form-item>
                                    </a-col>
                                    <a-col :span="12">
                                        <a-form-item label="通知提醒">
                                            <a-switch v-model:checked="stageSettings.notifications" />
                                        </a-form-item>
                                    </a-col>
                                </a-row>

                                <a-form-item>
                                    <a-space>
                                        <a-button type="primary" @click="handleSaveSettings">保存配置</a-button>
                                        <a-button @click="handleResetSettings">重置配置</a-button>
                                    </a-space>
                                </a-form-item>
                            </a-form>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    PlusOutlined,
    SearchOutlined,
    DownloadOutlined,
    EditOutlined,
    EyeOutlined,
    DownOutlined,
    CopyOutlined,
    InboxOutlined,
    DeleteOutlined,
    MoreOutlined,
    PlayCircleOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    stageData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'stage-click': [stage: any]
    'create-stage': []
    'edit-stage': [stage: any]
    'delete-stage': [stage: any]
}>()

// 响应式数据
const activeTab = ref('stages')
const searchKeyword = ref('')
const selectedStatus = ref('')
const selectedType = ref('')

const stageSettings = ref({
    defaultDuration: 30,
    autoAdvance: false,
    updateFrequency: 'weekly',
    notifications: true
})

// 计算属性
const overallProgress = computed(() => {
    if (!props.stageData?.stages) return 0
    const stages = props.stageData.stages
    const totalProgress = stages.reduce((sum: number, stage: any) => sum + stage.progress, 0)
    return Math.round(totalProgress / stages.length)
})

const filteredStages = computed(() => {
    if (!props.stageData?.stages) return []
    let filtered = props.stageData.stages

    if (searchKeyword.value) {
        filtered = filtered.filter((s: any) =>
            s.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
            s.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
        )
    }

    if (selectedStatus.value) {
        filtered = filtered.filter((s: any) => s.status === selectedStatus.value)
    }

    if (selectedType.value) {
        filtered = filtered.filter((s: any) => s.type === selectedType.value)
    }

    return filtered
})

// 方法
const handleTabChange = (key: string) => {
    activeTab.value = key
}

const handleCreateStage = () => {
    emit('create-stage')
}

const handleStageClick = (stage: any) => {
    emit('stage-click', stage)
}

const handleEditStage = (stage: any) => {
    emit('edit-stage', stage)
}

const handleViewStage = (stage: any) => {
    emit('stage-click', stage)
}

const handleDeleteStage = (stage: any) => {
    emit('delete-stage', stage)
    message.success(`阶段 "${stage.name}" 已删除`)
}

const handleDuplicateStage = (stage: any) => {
    message.success(`阶段 "${stage.name}" 已复制`)
}

const handleArchiveStage = (stage: any) => {
    message.success(`阶段 "${stage.name}" 已归档`)
}

const handleResetFilters = () => {
    searchKeyword.value = ''
    selectedStatus.value = ''
    selectedType.value = ''
}

const handleBatchOperation = () => {
    message.info('批量操作功能开发中...')
}

const handleExportStages = () => {
    message.success('正在导出阶段数据...')
}

const handleCreateTemplate = () => {
    message.info('新建模板功能开发中...')
}

const handleTemplateClick = (template: any) => {
    message.info(`查看模板: ${template.name}`)
}

const handleUseTemplate = (template: any) => {
    message.success(`已应用模板: ${template.name}`)
}

const handleEditTemplate = (template: any) => {
    message.info(`编辑模板: ${template.name}`)
}

const handleDeleteTemplate = (template: any) => {
    message.success(`模板 "${template.name}" 已删除`)
}

const handleSaveSettings = () => {
    message.success('阶段配置已保存')
}

const handleResetSettings = () => {
    stageSettings.value = {
        defaultDuration: 30,
        autoAdvance: false,
        updateFrequency: 'weekly',
        notifications: true
    }
    message.info('配置已重置')
}

// 样式辅助方法
const getStageItemClass = (stage: any) => {
    return {
        'stage-active': stage.status === 'active',
        'stage-completed': stage.status === 'completed',
        'stage-delayed': stage.isDelayed
    }
}

const getStageTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        'selection': 'blue',
        'cultivation': 'green',
        'establishment': 'orange',
        'promotion': 'purple'
    }
    return colorMap[type] || 'default'
}

const getStageTypeText = (type: string) => {
    const textMap: Record<string, string> = {
        'selection': '选拔',
        'cultivation': '培育',
        'establishment': '树立',
        'promotion': '推广'
    }
    return textMap[type] || type
}

const getStageStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'planning': 'blue',
        'active': 'processing',
        'completed': 'success',
        'paused': 'default'
    }
    return colorMap[status] || 'default'
}

const getStageStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'planning': '规划中',
        'active': '进行中',
        'completed': '已完成',
        'paused': '已暂停'
    }
    return textMap[status] || status
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const getTemplateTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        '标准模板': 'blue',
        '快速模板': 'green',
        '自定义模板': 'orange'
    }
    return colorMap[type] || 'default'
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped lang="scss">
.stage-management {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .management-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .management-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .management-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .stage-overview {
            margin-bottom: 20px;

            .overview-card {
                text-align: center;
                border-radius: 8px;
            }
        }

        .stage-management-tabs {
            flex: 1;
            overflow: hidden;

            .ant-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-tabs-content-holder {
                    flex: 1;
                    overflow: hidden;

                    .ant-tabs-content {
                        height: 100%;

                        .ant-tabs-tabpane {
                            height: 100%;
                            overflow-y: auto;

                            .stages-section {
                                .stages-filters {
                                    margin-bottom: 16px;
                                    padding: 16px;
                                    background: white;
                                    border-radius: 8px;
                                    border: 1px solid #e8e8e8;
                                }

                                .stages-list {
                                    .stage-item {
                                        padding: 20px;
                                        margin-bottom: 16px;
                                        background: white;
                                        border: 1px solid #e8e8e8;
                                        border-radius: 12px;
                                        cursor: pointer;
                                        transition: all 0.3s ease;

                                        &:hover {
                                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                                            border-color: #1890ff;
                                        }

                                        &.stage-active {
                                            border-left: 4px solid #1890ff;
                                            background: #f6ffed;
                                        }

                                        &.stage-completed {
                                            border-left: 4px solid #52c41a;
                                            background: #f6ffed;
                                        }

                                        &.stage-delayed {
                                            border-left: 4px solid #f5222d;
                                            background: #fff2f0;
                                        }

                                        .stage-header {
                                            display: flex;
                                            justify-content: space-between;
                                            align-items: flex-start;
                                            margin-bottom: 16px;

                                            .stage-info {
                                                flex: 1;

                                                .stage-title {
                                                    display: flex;
                                                    align-items: center;
                                                    gap: 12px;
                                                    margin-bottom: 8px;

                                                    .stage-name {
                                                        font-size: 18px;
                                                        font-weight: 600;
                                                        color: #262626;
                                                    }

                                                    .stage-tags {
                                                        display: flex;
                                                        gap: 8px;
                                                    }
                                                }

                                                .stage-description {
                                                    color: #666;
                                                    font-size: 14px;
                                                    line-height: 1.5;
                                                }
                                            }

                                            .stage-progress {
                                                .progress-info {
                                                    display: flex;
                                                    flex-direction: column;
                                                    align-items: center;
                                                    gap: 8px;

                                                    .progress-text {
                                                        font-size: 14px;
                                                        font-weight: 600;
                                                        color: #262626;
                                                    }
                                                }
                                            }
                                        }

                                        .stage-content {
                                            margin-bottom: 16px;

                                            .stage-metrics {
                                                display: grid;
                                                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                                                gap: 16px;
                                                margin-bottom: 16px;

                                                .metric-item {
                                                    font-size: 13px;

                                                    .metric-label {
                                                        color: #666;
                                                    }

                                                    .metric-value {
                                                        color: #262626;
                                                        font-weight: 500;
                                                        margin-left: 4px;
                                                    }
                                                }
                                            }

                                            .stage-timeline {
                                                display: flex;
                                                gap: 20px;
                                                overflow-x: auto;
                                                padding: 8px 0;

                                                .timeline-item {
                                                    display: flex;
                                                    align-items: center;
                                                    gap: 8px;
                                                    min-width: 120px;
                                                    opacity: 0.6;

                                                    &.completed {
                                                        opacity: 1;

                                                        .timeline-dot {
                                                            background: #52c41a;
                                                        }
                                                    }

                                                    .timeline-dot {
                                                        width: 8px;
                                                        height: 8px;
                                                        border-radius: 50%;
                                                        background: #d9d9d9;
                                                        flex-shrink: 0;
                                                    }

                                                    .timeline-content {
                                                        display: flex;
                                                        flex-direction: column;
                                                        gap: 2px;

                                                        .milestone-name {
                                                            font-size: 12px;
                                                            color: #262626;
                                                            font-weight: 500;
                                                        }

                                                        .milestone-date {
                                                            font-size: 11px;
                                                            color: #999;
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        .stage-actions {
                                            display: flex;
                                            justify-content: flex-end;
                                        }
                                    }

                                    .empty-state {
                                        height: 300px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    }
                                }
                            }

                            .templates-section {
                                .templates-header {
                                    margin-bottom: 20px;
                                    display: flex;
                                    justify-content: flex-end;
                                }

                                .templates-grid {
                                    .template-card {
                                        cursor: pointer;
                                        transition: all 0.3s ease;
                                        border-radius: 8px;

                                        &:hover {
                                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                                            transform: translateY(-2px);
                                        }

                                        .template-title {
                                            display: flex;
                                            align-items: center;
                                            justify-content: space-between;

                                            .template-name {
                                                font-size: 16px;
                                                font-weight: 600;
                                                color: #262626;
                                            }
                                        }

                                        .template-content {
                                            .template-description {
                                                color: #666;
                                                font-size: 13px;
                                                line-height: 1.5;
                                                margin-bottom: 12px;
                                            }

                                            .template-stats {
                                                .stat-item {
                                                    display: flex;
                                                    justify-content: space-between;
                                                    margin-bottom: 4px;
                                                    font-size: 12px;

                                                    .stat-label {
                                                        color: #666;
                                                    }

                                                    .stat-value {
                                                        color: #262626;
                                                        font-weight: 500;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            .settings-section {
                                padding: 20px;
                                background: white;
                                border-radius: 8px;
                                border: 1px solid #e8e8e8;

                                .ant-form {
                                    .ant-form-item {
                                        margin-bottom: 16px;

                                        .ant-form-item-label {
                                            font-weight: 500;
                                            color: #262626;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
