<template>
    <div class="indicator-analysis">
        <div class="analysis-header">
            <h3 class="analysis-title">指标深度分析</h3>
            <div class="analysis-stats">
                <a-row :gutter="16">
                    <a-col :span="8">
                        <a-statistic title="总指标数" :value="indicatorData?.totalIndicators || 0"
                            :value-style="{ color: '#1890ff' }" />
                    </a-col>
                    <a-col :span="8">
                        <a-statistic title="数据资源总数" :value="indicatorData?.totalDataResources || 0"
                            :value-style="{ color: '#52c41a' }" />
                    </a-col>
                    <a-col :span="8">
                        <a-statistic title="平均完成率" :value="averageCompletionRate" suffix="%"
                            :value-style="{ color: '#faad14' }" />
                    </a-col>
                </a-row>
            </div>
        </div>

        <a-divider />

        <div class="indicator-list" v-loading="loading">
            <div class="list-header">
                <span class="list-title">指标列表</span>
                <a-button size="small" @click="handleRefresh">
                    <template #icon><reload-outlined /></template>
                    刷新
                </a-button>
            </div>

            <div class="indicator-items">
                <div v-for="indicator in indicatorData?.indicators || []" :key="indicator.id" class="indicator-item"
                    @click="handleIndicatorClick(indicator)">
                    <div class="item-header">
                        <div class="item-info">
                            <span class="item-name">{{ indicator.name }}</span>
                            <a-tag :color="getSourceColor(indicator.source)">{{ indicator.source }}</a-tag>
                        </div>
                        <div class="item-rate">
                            <span class="rate-text">{{ indicator.completionRate }}%</span>
                        </div>
                    </div>

                    <div class="item-progress">
                        <a-progress :percent="indicator.completionRate"
                            :stroke-color="getProgressColor(indicator.completionRate)" :show-info="false"
                            size="small" />
                    </div>

                    <div class="item-details">
                        <div class="detail-item">
                            <span class="detail-label">完成单位数:</span>
                            <span class="detail-value">{{ indicator.completedUnits }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">总单位数:</span>
                            <span class="detail-value">{{ indicator.totalUnits }}</span>
                        </div>
                    </div>

                    <div class="item-actions">
                        <a-button size="small" type="link" @click.stop="handleDrillDown(indicator)">
                            查看详情
                        </a-button>
                        <a-button size="small" type="link" @click.stop="handleViewUnits(indicator)">
                            单位详情
                        </a-button>
                    </div>
                </div>
            </div>

            <div v-if="!indicatorData?.indicators?.length && !loading" class="empty-state">
                <a-empty description="暂无指标数据" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'

// Props定义
interface Props {
    indicatorData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'indicator-click': [indicator: any]
    'drill-down': [data: any]
}>()

// 计算属性
const averageCompletionRate = computed(() => {
    if (!props.indicatorData?.indicators?.length) return 0
    const total = props.indicatorData.indicators.reduce((sum: number, indicator: any) => sum + indicator.completionRate, 0)
    return Math.round(total / props.indicatorData.indicators.length)
})

// 方法
const getProgressColor = (rate: number) => {
    if (rate >= 90) return '#52c41a'
    if (rate >= 70) return '#faad14'
    if (rate >= 50) return '#ff7a45'
    return '#f5222d'
}

const getSourceColor = (source: string) => {
    const colorMap: Record<string, string> = {
        '党建系统': 'red',
        '政务系统': 'blue',
        '业务系统': 'green',
        '第三方': 'orange'
    }
    return colorMap[source] || 'default'
}

const handleIndicatorClick = (indicator: any) => {
    emit('indicator-click', indicator)
}

const handleDrillDown = (indicator: any) => {
    emit('drill-down', { type: 'indicator', data: indicator })
}

const handleViewUnits = (indicator: any) => {
    emit('drill-down', { type: 'units', data: indicator })
}

const handleRefresh = () => {
    // 触发父组件刷新数据
    emit('drill-down', { type: 'refresh' })
}
</script>

<style scoped lang="scss">
.indicator-analysis {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .analysis-header {
        margin-bottom: 16px;

        .analysis-title {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }

        .analysis-stats {
            background: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
        }
    }

    .indicator-list {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;

            .list-title {
                font-size: 16px;
                font-weight: 500;
                color: #262626;
            }
        }

        .indicator-items {
            flex: 1;
            overflow-y: auto;

            .indicator-item {
                padding: 16px;
                margin-bottom: 12px;
                background: #fafafa;
                border: 1px solid #e8e8e8;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    background: #f0f9ff;
                    border-color: #1890ff;
                    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
                }

                .item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 12px;

                    .item-info {
                        flex: 1;

                        .item-name {
                            display: block;
                            font-size: 14px;
                            font-weight: 500;
                            color: #262626;
                            margin-bottom: 4px;
                        }
                    }

                    .item-rate {
                        .rate-text {
                            font-size: 16px;
                            font-weight: 600;
                            color: #1890ff;
                        }
                    }
                }

                .item-progress {
                    margin-bottom: 12px;
                }

                .item-details {
                    display: flex;
                    gap: 16px;
                    margin-bottom: 12px;

                    .detail-item {
                        font-size: 12px;

                        .detail-label {
                            color: #666;
                        }

                        .detail-value {
                            color: #262626;
                            font-weight: 500;
                            margin-left: 4px;
                        }
                    }
                }

                .item-actions {
                    display: flex;
                    gap: 8px;
                    justify-content: flex-end;
                }
            }
        }

        .empty-state {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .indicator-analysis {
        padding: 16px;

        .analysis-header {
            .analysis-title {
                font-size: 16px;
            }

            .analysis-stats {
                padding: 12px;
            }
        }

        .indicator-list {
            .indicator-items {
                .indicator-item {
                    padding: 12px;

                    .item-header {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 8px;
                    }

                    .item-details {
                        flex-direction: column;
                        gap: 4px;
                    }

                    .item-actions {
                        justify-content: flex-start;
                    }
                }
            }
        }
    }
}
</style>