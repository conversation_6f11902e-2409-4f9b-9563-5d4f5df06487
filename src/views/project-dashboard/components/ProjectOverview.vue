<template>
  <div class="project-overview">
    <!-- 阶段数据可视化 -->
    <div class="stage-visualization">
      <a-card title="项目全景概览" class="overview-card">
        <div class="stage-stats">
          <a-row :gutter="[16, 16]">
            <a-col 
              v-for="stage in data?.stageStats || []" 
              :key="stage.stage"
              :xs="24" :sm="12" :md="8" :lg="4"
            >
              <div 
                class="stage-item"
                :class="stage.stage"
                @click="handleStageClick(stage)"
              >
                <div class="stage-header">
                  <div class="stage-icon">
                    <component :is="getStageIcon(stage.stage)" />
                  </div>
                  <div class="stage-title">{{ stage.stageName }}</div>
                </div>
                <div class="stage-content">
                  <div class="stage-count">{{ stage.count }}</div>
                  <div class="stage-unit">个项目</div>
                </div>
                <div class="stage-footer">
                  <div class="stage-percentage">{{ stage.percentage }}%</div>
                  <div class="stage-trend">
                    <arrow-up-outlined v-if="stage.percentage > 15" class="trend-up" />
                    <arrow-down-outlined v-else-if="stage.percentage < 10" class="trend-down" />
                    <minus-outlined v-else class="trend-stable" />
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 项目动态追踪 -->
    <div class="dynamic-tracking">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="14">
          <a-card title="项目创建动态" class="dynamics-card">
            <div class="dynamics-container">
              <div class="dynamics-scroll">
                <div 
                  v-for="dynamic in data?.recentDynamics || []"
                  :key="dynamic.id"
                  class="dynamic-item"
                  :class="dynamic.type"
                >
                  <div class="dynamic-icon">
                    <component :is="getDynamicIcon(dynamic.type)" />
                  </div>
                  <div class="dynamic-content">
                    <div class="dynamic-header">
                      <span class="dynamic-title">{{ dynamic.title }}</span>
                      <span class="dynamic-time">{{ formatTime(dynamic.publishTime) }}</span>
                    </div>
                    <div class="dynamic-body">
                      <div class="dynamic-text">{{ dynamic.content }}</div>
                      <div class="dynamic-meta">
                        <a-tag color="blue">{{ dynamic.projectName }}</a-tag>
                        <a-tag color="green">{{ dynamic.unitName }}</a-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :lg="10">
          <a-card title="项目创建成果" class="achievements-card">
            <div class="achievements-list">
              <div 
                v-for="achievement in data?.achievements || []"
                :key="achievement.id"
                class="achievement-item"
              >
                <div class="achievement-icon">
                  <trophy-outlined />
                </div>
                <div class="achievement-content">
                  <div class="achievement-title">{{ achievement.awardTitle }}</div>
                  <div class="achievement-project">{{ achievement.projectName }}</div>
                  <div class="achievement-meta">
                    <span class="achievement-unit">{{ achievement.unitName }}</span>
                    <span class="achievement-level">{{ achievement.awardLevel }}</span>
                  </div>
                  <div class="achievement-time">{{ formatDate(achievement.awardTime) }}</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 项目列表弹窗 -->
    <a-modal
      v-model:visible="projectListVisible"
      :title="`${selectedStage?.stageName}项目列表`"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedStage" class="project-list">
        <a-table
          :columns="projectColumns"
          :data-source="selectedStage.projects"
          :pagination="{ pageSize: 10 }"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a @click="handleProjectDetail(record)">{{ record.name }}</a>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'units'">
              <div class="units-info">
                <span class="unit-item">申报: {{ record.applicationUnits }}</span>
                <span class="unit-item">培育: {{ record.cultivationUnits }}</span>
                <span class="unit-item">标杆: {{ record.benchmarkUnits }}</span>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { ProjectOverviewProps, ProjectStageStats, Project } from '../types'

// Props定义
const props = withDefaults(defineProps<ProjectOverviewProps>(), {
  loading: false
})

// Emits定义
const emit = defineEmits<{
  'stage-click': [stage: ProjectStageStats]
  'project-click': [project: Project]
}>()

// 响应式数据
const projectListVisible = ref(false)
const selectedStage = ref<ProjectStageStats | null>(null)

// 表格列定义
const projectColumns = [
  { title: '项目名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '发起单位', dataIndex: 'initiatingUnit', key: 'initiatingUnit', width: 150 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '单位数量', key: 'units', width: 200 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 120 }
]

// 方法
const getStageIcon = (stage: string) => {
  const iconMap = {
    total: 'pie-chart-outlined',
    application: 'form-outlined',
    cultivation: 'rise-outlined',
    evaluation: 'bar-chart-outlined',
    completion: 'check-circle-outlined'
  }
  return iconMap[stage as keyof typeof iconMap] || 'pie-chart-outlined'
}

const getDynamicIcon = (type: string) => {
  const iconMap = {
    achievement: 'star-outlined',
    media: 'sound-outlined',
    honor: 'trophy-outlined',
    case: 'file-text-outlined'
  }
  return iconMap[type as keyof typeof iconMap] || 'star-outlined'
}

const getStatusColor = (status: string) => {
  const colorMap = {
    pending: 'orange',
    in_progress: 'blue',
    completed: 'green',
    overdue: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    pending: '待办',
    in_progress: '进行中',
    completed: '已完成',
    overdue: '逾期'
  }
  return textMap[status as keyof typeof textMap] || status
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (24 * 60 * 60 * 1000))
  
  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString()
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString()
}

// 事件处理
const handleStageClick = (stage: ProjectStageStats) => {
  selectedStage.value = stage
  projectListVisible.value = true
  emit('stage-click', stage)
}

const handleProjectDetail = (project: Project) => {
  emit('project-click', project)
}
</script>

<style scoped lang="scss">
.project-overview {
  .stage-visualization {
    margin-bottom: 20px;
    
    .overview-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .stage-stats {
      .stage-item {
        padding: 20px;
        border-radius: 8px;
        border: 2px solid transparent;
        background: #fafafa;
        cursor: pointer;
        transition: all 0.3s ease;
        height: 140px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        &.total {
          border-color: rgba(24, 144, 255, 0.3);
          background: rgba(24, 144, 255, 0.05);
          
          .stage-icon {
            color: #1890ff;
          }
        }
        
        &.application {
          border-color: rgba(82, 196, 26, 0.3);
          background: rgba(82, 196, 26, 0.05);
          
          .stage-icon {
            color: #52c41a;
          }
        }
        
        &.cultivation {
          border-color: rgba(250, 173, 20, 0.3);
          background: rgba(250, 173, 20, 0.05);
          
          .stage-icon {
            color: #faad14;
          }
        }
        
        &.evaluation {
          border-color: rgba(114, 46, 209, 0.3);
          background: rgba(114, 46, 209, 0.05);
          
          .stage-icon {
            color: #722ed1;
          }
        }
        
        &.completion {
          border-color: rgba(245, 34, 45, 0.3);
          background: rgba(245, 34, 45, 0.05);
          
          .stage-icon {
            color: #f5222d;
          }
        }
        
        .stage-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          .stage-icon {
            font-size: 20px;
            margin-right: 8px;
          }
          
          .stage-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }
        
        .stage-content {
          text-align: center;
          margin-bottom: 12px;
          
          .stage-count {
            font-size: 32px;
            font-weight: 600;
            color: #262626;
            line-height: 1;
          }
          
          .stage-unit {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
          }
        }
        
        .stage-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .stage-percentage {
            font-size: 14px;
            font-weight: 500;
            color: #666;
          }
          
          .stage-trend {
            .trend-up {
              color: #52c41a;
            }
            
            .trend-down {
              color: #f5222d;
            }
            
            .trend-stable {
              color: #faad14;
            }
          }
        }
      }
    }
  }
  
  .dynamic-tracking {
    .dynamics-card,
    .achievements-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      height: 400px;
      
      .ant-card-body {
        height: calc(100% - 57px);
        padding: 16px;
      }
    }
    
    .dynamics-container {
      height: 100%;
      
      .dynamics-scroll {
        height: 100%;
        overflow-y: auto;
        
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }
        
        .dynamic-item {
          display: flex;
          padding: 12px;
          margin-bottom: 12px;
          border-radius: 6px;
          border-left: 4px solid #e8e8e8;
          background: #fafafa;
          
          &.achievement {
            border-left-color: #52c41a;
          }
          
          &.media {
            border-left-color: #1890ff;
          }
          
          &.honor {
            border-left-color: #faad14;
          }
          
          &.case {
            border-left-color: #722ed1;
          }
          
          .dynamic-icon {
            margin-right: 12px;
            font-size: 16px;
            color: #666;
          }
          
          .dynamic-content {
            flex: 1;
            
            .dynamic-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .dynamic-title {
                font-size: 14px;
                font-weight: 500;
                color: #262626;
              }
              
              .dynamic-time {
                font-size: 12px;
                color: #999;
              }
            }
            
            .dynamic-body {
              .dynamic-text {
                font-size: 13px;
                color: #666;
                line-height: 1.5;
                margin-bottom: 8px;
              }
              
              .dynamic-meta {
                display: flex;
                gap: 8px;
              }
            }
          }
        }
      }
    }
    
    .achievements-list {
      height: 100%;
      overflow-y: auto;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: #f1f1f1;
      }
      
      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }
      
      .achievement-item {
        display: flex;
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 6px;
        background: #fafafa;
        border: 1px solid #e8e8e8;
        
        .achievement-icon {
          margin-right: 12px;
          font-size: 18px;
          color: #faad14;
        }
        
        .achievement-content {
          flex: 1;
          
          .achievement-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }
          
          .achievement-project {
            font-size: 13px;
            color: #1890ff;
            margin-bottom: 6px;
          }
          
          .achievement-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            
            .achievement-unit {
              font-size: 12px;
              color: #666;
            }
            
            .achievement-level {
              font-size: 12px;
              color: #52c41a;
              font-weight: 500;
            }
          }
          
          .achievement-time {
            font-size: 11px;
            color: #999;
          }
        }
      }
    }
  }
  
  .project-list {
    .units-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      
      .unit-item {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-overview {
    .stage-stats {
      .stage-item {
        height: 120px;
        padding: 16px;
        
        .stage-content {
          .stage-count {
            font-size: 24px;
          }
        }
      }
    }
    
    .dynamic-tracking {
      .dynamics-card,
      .achievements-card {
        height: 300px;
      }
    }
  }
}
</style>
