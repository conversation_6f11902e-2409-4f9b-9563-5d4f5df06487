<template>
    <div class="innovation-tracking">
        <div class="tracking-header">
            <h3 class="tracking-title">创新实践追踪</h3>
            <div class="tracking-filters">
                <a-select v-model:value="selectedType" placeholder="类型筛选" style="width: 150px"
                    @change="handleTypeChange">
                    <a-select-option value="">全部类型</a-select-option>
                    <a-select-option value="case">最佳实践</a-select-option>
                    <a-select-option value="activity">主题活动</a-select-option>
                    <a-select-option value="project">创新项目</a-select-option>
                </a-select>
            </div>
        </div>

        <div class="tracking-content" v-loading="loading">
            <div class="innovation-stats">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-statistic title="最佳实践案例" :value="innovationData?.bestPractices?.length || 0"
                            :value-style="{ color: '#52c41a' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="主题活动" :value="innovationData?.themeActivities?.length || 0"
                            :value-style="{ color: '#1890ff' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="创新项目" :value="innovationData?.innovationProjects?.length || 0"
                            :value-style="{ color: '#faad14' }" />
                    </a-col>
                    <a-col :span="6">
                        <a-statistic title="推广应用" :value="innovationData?.promotionCount || 0"
                            :value-style="{ color: '#722ed1' }" />
                    </a-col>
                </a-row>
            </div>

            <div class="tracking-tabs">
                <a-tabs v-model:activeKey="activeTrackingTab" @change="handleTrackingTabChange">
                    <a-tab-pane key="cases" tab="最佳实践案例">
                        <div class="cases-list">
                            <div v-for="caseItem in filteredCases" :key="caseItem.id" class="case-item"
                                @click="handleCaseClick(caseItem)">
                                <div class="case-header">
                                    <div class="case-info">
                                        <span class="case-title">{{ caseItem.title }}</span>
                                        <div class="case-tags">
                                            <a-tag :color="getCategoryColor(caseItem.category)">{{ caseItem.category
                                                }}</a-tag>
                                            <a-tag :color="getStatusColor(caseItem.status)">{{
                                                getStatusText(caseItem.status) }}</a-tag>
                                        </div>
                                    </div>
                                    <div class="case-score">
                                        <div class="score-value">{{ caseItem.score }}</div>
                                        <div class="score-label">综合评分</div>
                                    </div>
                                </div>

                                <div class="case-content">
                                    <div class="case-description">{{ caseItem.description }}</div>
                                    <div class="case-metrics">
                                        <div class="metric-item">
                                            <span class="metric-label">推广单位:</span>
                                            <span class="metric-value">{{ caseItem.promotionUnits }}个</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">应用效果:</span>
                                            <span class="metric-value">{{ caseItem.effectLevel }}</span>
                                        </div>
                                        <div class="metric-item">
                                            <span class="metric-label">创建时间:</span>
                                            <span class="metric-value">{{ formatDate(caseItem.createTime) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="case-actions">
                                    <a-button size="small" type="link" @click.stop="handleViewCase(caseItem)">
                                        查看详情
                                    </a-button>
                                    <a-button size="small" type="link" @click.stop="handlePromoteCase(caseItem)">
                                        推广应用
                                    </a-button>
                                </div>
                            </div>

                            <div v-if="!filteredCases.length && !loading" class="empty-state">
                                <a-empty description="暂无最佳实践案例" />
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="activities" tab="主题活动成果">
                        <div class="activities-list">
                            <div v-for="activity in filteredActivities" :key="activity.id" class="activity-item"
                                @click="handleActivityClick(activity)">
                                <div class="activity-header">
                                    <div class="activity-info">
                                        <span class="activity-name">{{ activity.name }}</span>
                                        <div class="activity-tags">
                                            <a-tag :color="getActivityTypeColor(activity.type)">{{ activity.type
                                                }}</a-tag>
                                            <a-tag :color="activity.isOngoing ? 'processing' : 'success'">
                                                {{ activity.isOngoing ? '进行中' : '已完成' }}
                                            </a-tag>
                                        </div>
                                    </div>
                                    <div class="activity-participation">
                                        <div class="participation-count">{{ activity.participationCount }}</div>
                                        <div class="participation-label">参与单位</div>
                                    </div>
                                </div>

                                <div class="activity-content">
                                    <div class="activity-description">{{ activity.description }}</div>
                                    <div class="activity-achievements">
                                        <h5>主要成果:</h5>
                                        <ul class="achievements-list">
                                            <li v-for="achievement in activity.achievements" :key="achievement">
                                                {{ achievement }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="activity-timeline">
                                    <div class="timeline-item">
                                        <span class="timeline-label">开始时间:</span>
                                        <span class="timeline-value">{{ formatDate(activity.startTime) }}</span>
                                    </div>
                                    <div class="timeline-item">
                                        <span class="timeline-label">结束时间:</span>
                                        <span class="timeline-value">{{ formatDate(activity.endTime) }}</span>
                                    </div>
                                </div>

                                <div class="activity-actions">
                                    <a-button size="small" type="link" @click.stop="handleViewActivity(activity)">
                                        查看详情
                                    </a-button>
                                    <a-button size="small" type="link" @click.stop="handleJoinActivity(activity)"
                                        v-if="activity.isOngoing">
                                        参与活动
                                    </a-button>
                                </div>
                            </div>

                            <div v-if="!filteredActivities.length && !loading" class="empty-state">
                                <a-empty description="暂无主题活动" />
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="projects" tab="创新项目">
                        <div class="projects-list">
                            <div v-for="project in filteredProjects" :key="project.id" class="project-item"
                                @click="handleProjectClick(project)">
                                <div class="project-header">
                                    <div class="project-info">
                                        <span class="project-name">{{ project.name }}</span>
                                        <div class="project-tags">
                                            <a-tag :color="getProjectTypeColor(project.type)">{{ project.type }}</a-tag>
                                            <a-tag :color="getProjectStatusColor(project.status)">{{
                                                getProjectStatusText(project.status) }}</a-tag>
                                        </div>
                                    </div>
                                    <div class="project-progress">
                                        <div class="progress-value">{{ project.progress }}%</div>
                                        <div class="progress-label">完成进度</div>
                                    </div>
                                </div>

                                <div class="project-content">
                                    <div class="project-description">{{ project.description }}</div>
                                    <div class="project-details">
                                        <div class="detail-item">
                                            <span class="detail-label">负责单位:</span>
                                            <span class="detail-value">{{ project.responsibleUnit }}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">预期效果:</span>
                                            <span class="detail-value">{{ project.expectedEffect }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="project-progress-bar">
                                    <a-progress :percent="project.progress"
                                        :stroke-color="getProgressColor(project.progress)" size="small" />
                                </div>

                                <div class="project-actions">
                                    <a-button size="small" type="link" @click.stop="handleViewProject(project)">
                                        查看详情
                                    </a-button>
                                    <a-button size="small" type="link" @click.stop="handleUpdateProgress(project)">
                                        更新进度
                                    </a-button>
                                </div>
                            </div>

                            <div v-if="!filteredProjects.length && !loading" class="empty-state">
                                <a-empty description="暂无创新项目" />
                            </div>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

// Props定义
interface Props {
    innovationData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'case-click': [caseItem: any]
    'activity-click': [activity: any]
    'export': [data: any]
}>()

// 响应式数据
const selectedType = ref('')
const activeTrackingTab = ref('cases')

// 计算属性
const filteredCases = computed(() => {
    if (!props.innovationData?.bestPractices) return []
    let filtered = props.innovationData.bestPractices

    if (selectedType.value === 'case') {
        return filtered
    } else if (selectedType.value && selectedType.value !== 'case') {
        return []
    }

    return filtered
})

const filteredActivities = computed(() => {
    if (!props.innovationData?.themeActivities) return []
    let filtered = props.innovationData.themeActivities

    if (selectedType.value === 'activity') {
        return filtered
    } else if (selectedType.value && selectedType.value !== 'activity') {
        return []
    }

    return filtered
})

const filteredProjects = computed(() => {
    if (!props.innovationData?.innovationProjects) return []
    let filtered = props.innovationData.innovationProjects

    if (selectedType.value === 'project') {
        return filtered
    } else if (selectedType.value && selectedType.value !== 'project') {
        return []
    }

    return filtered
})

// 方法
const handleTypeChange = (type: string) => {
    selectedType.value = type
    // 根据类型切换到对应的tab
    if (type === 'case') {
        activeTrackingTab.value = 'cases'
    } else if (type === 'activity') {
        activeTrackingTab.value = 'activities'
    } else if (type === 'project') {
        activeTrackingTab.value = 'projects'
    }
}

const handleTrackingTabChange = (key: string) => {
    activeTrackingTab.value = key
    // 清空类型筛选
    selectedType.value = ''
}

const getCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
        '党建创新': 'red',
        '管理创新': 'blue',
        '服务创新': 'green',
        '技术创新': 'orange'
    }
    return colorMap[category] || 'default'
}

const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'active': 'success',
        'pending': 'processing',
        'archived': 'default'
    }
    return colorMap[status] || 'default'
}

const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'active': '推广中',
        'pending': '待审核',
        'archived': '已归档'
    }
    return textMap[status] || status
}

const getActivityTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        '学习交流': 'blue',
        '实践活动': 'green',
        '竞赛评比': 'orange',
        '培训研讨': 'purple'
    }
    return colorMap[type] || 'default'
}

const getProjectTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
        '数字化转型': 'blue',
        '流程优化': 'green',
        '制度创新': 'orange',
        '服务提升': 'purple'
    }
    return colorMap[type] || 'default'
}

const getProjectStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'planning': 'blue',
        'executing': 'processing',
        'completed': 'success',
        'paused': 'warning'
    }
    return colorMap[status] || 'default'
}

const getProjectStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'planning': '规划中',
        'executing': '执行中',
        'completed': '已完成',
        'paused': '暂停'
    }
    return textMap[status] || status
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}

const handleCaseClick = (caseItem: any) => {
    emit('case-click', caseItem)
}

const handleActivityClick = (activity: any) => {
    emit('activity-click', activity)
}

const handleProjectClick = (project: any) => {
    message.info(`查看项目：${project.name}`)
}

const handleViewCase = (caseItem: any) => {
    message.info(`查看案例详情：${caseItem.title}`)
}

const handlePromoteCase = (caseItem: any) => {
    message.success(`推广案例：${caseItem.title}`)
}

const handleViewActivity = (activity: any) => {
    message.info(`查看活动详情：${activity.name}`)
}

const handleJoinActivity = (activity: any) => {
    message.success(`参与活动：${activity.name}`)
}

const handleViewProject = (project: any) => {
    message.info(`查看项目详情：${project.name}`)
}

const handleUpdateProgress = (project: any) => {
    message.info(`更新项目进度：${project.name}`)
}
</script>

<style scoped lang="scss">
.innovation-tracking {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .tracking-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .tracking-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .tracking-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .innovation-stats {
            background: #f6f8fa;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .tracking-tabs {
            flex: 1;
            overflow: hidden;

            .ant-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-tabs-content-holder {
                    flex: 1;
                    overflow: hidden;

                    .ant-tabs-content {
                        height: 100%;

                        .ant-tabs-tabpane {
                            height: 100%;
                            overflow-y: auto;

                            .cases-list,
                            .activities-list,
                            .projects-list {

                                .case-item,
                                .activity-item,
                                .project-item {
                                    padding: 16px;
                                    margin-bottom: 12px;
                                    background: white;
                                    border: 1px solid #e8e8e8;
                                    border-radius: 8px;
                                    cursor: pointer;
                                    transition: all 0.3s ease;

                                    &:hover {
                                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                                        border-color: #1890ff;
                                    }

                                    .case-header,
                                    .activity-header,
                                    .project-header {
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: flex-start;
                                        margin-bottom: 12px;

                                        .case-info,
                                        .activity-info,
                                        .project-info {
                                            flex: 1;

                                            .case-title,
                                            .activity-name,
                                            .project-name {
                                                display: block;
                                                font-size: 16px;
                                                font-weight: 500;
                                                color: #262626;
                                                margin-bottom: 8px;
                                            }

                                            .case-tags,
                                            .activity-tags,
                                            .project-tags {
                                                display: flex;
                                                gap: 8px;
                                            }
                                        }

                                        .case-score,
                                        .activity-participation,
                                        .project-progress {
                                            text-align: center;
                                            min-width: 80px;

                                            .score-value,
                                            .participation-count,
                                            .progress-value {
                                                font-size: 20px;
                                                font-weight: 600;
                                                color: #1890ff;
                                                display: block;
                                            }

                                            .score-label,
                                            .participation-label,
                                            .progress-label {
                                                font-size: 12px;
                                                color: #666;
                                            }
                                        }
                                    }

                                    .case-content,
                                    .activity-content,
                                    .project-content {
                                        margin-bottom: 12px;

                                        .case-description,
                                        .activity-description,
                                        .project-description {
                                            color: #666;
                                            font-size: 13px;
                                            line-height: 1.5;
                                            margin-bottom: 8px;
                                        }

                                        .case-metrics,
                                        .project-details {
                                            display: flex;
                                            gap: 16px;
                                            flex-wrap: wrap;

                                            .metric-item,
                                            .detail-item {
                                                font-size: 12px;

                                                .metric-label,
                                                .detail-label {
                                                    color: #666;
                                                }

                                                .metric-value,
                                                .detail-value {
                                                    color: #262626;
                                                    font-weight: 500;
                                                    margin-left: 4px;
                                                }
                                            }
                                        }

                                        .activity-achievements {
                                            h5 {
                                                margin: 8px 0 4px 0;
                                                font-size: 13px;
                                                font-weight: 500;
                                                color: #262626;
                                            }

                                            .achievements-list {
                                                margin: 0;
                                                padding-left: 16px;

                                                li {
                                                    font-size: 12px;
                                                    color: #666;
                                                    line-height: 1.4;
                                                    margin-bottom: 2px;
                                                }
                                            }
                                        }
                                    }

                                    .activity-timeline {
                                        display: flex;
                                        gap: 16px;
                                        margin-bottom: 12px;

                                        .timeline-item {
                                            font-size: 12px;

                                            .timeline-label {
                                                color: #666;
                                            }

                                            .timeline-value {
                                                color: #262626;
                                                font-weight: 500;
                                                margin-left: 4px;
                                            }
                                        }
                                    }

                                    .project-progress-bar {
                                        margin-bottom: 12px;
                                    }

                                    .case-actions,
                                    .activity-actions,
                                    .project-actions {
                                        display: flex;
                                        justify-content: flex-end;
                                        gap: 8px;
                                    }
                                }

                                .empty-state {
                                    height: 200px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .innovation-tracking {
        padding: 16px;

        .tracking-content {
            .innovation-stats {
                padding: 12px;
            }

            .tracking-tabs {
                .ant-tabs-content-holder {
                    .ant-tabs-content {
                        .ant-tabs-tabpane {

                            .cases-list,
                            .activities-list,
                            .projects-list {

                                .case-item,
                                .activity-item,
                                .project-item {
                                    padding: 12px;

                                    .case-header,
                                    .activity-header,
                                    .project-header {
                                        flex-direction: column;
                                        align-items: flex-start;
                                        gap: 8px;

                                        .case-score,
                                        .activity-participation,
                                        .project-progress {
                                            align-self: flex-end;
                                        }
                                    }

                                    .case-content,
                                    .activity-content,
                                    .project-content {

                                        .case-metrics,
                                        .project-details {
                                            flex-direction: column;
                                            gap: 4px;
                                        }

                                        .activity-timeline {
                                            flex-direction: column;
                                            gap: 4px;
                                        }
                                    }

                                    .case-actions,
                                    .activity-actions,
                                    .project-actions {
                                        justify-content: flex-start;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>