<template>
    <div class="project-statistics-overview">
        <div class="statistics-header">
            <h3 class="statistics-title">项目统计概览</h3>
            <div class="statistics-actions">
                <a-button @click="handleExportStatistics">
                    <template #icon><download-outlined /></template>
                    导出统计
                </a-button>
            </div>
        </div>

        <div class="statistics-content" v-loading="loading">
            <div class="overview-cards">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-card class="stat-card total-projects">
                            <a-statistic title="项目总数" :value="statisticsData?.totalProjects || 0"
                                :value-style="{ color: '#1890ff' }">
                                <template #prefix>
                                    <project-outlined />
                                </template>
                            </a-statistic>
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card active-projects">
                            <a-statistic title="进行中项目" :value="statisticsData?.activeProjects || 0"
                                :value-style="{ color: '#52c41a' }">
                                <template #prefix>
                                    <play-circle-outlined />
                                </template>
                            </a-statistic>
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card completed-projects">
                            <a-statistic title="已完成项目" :value="statisticsData?.completedProjects || 0"
                                :value-style="{ color: '#722ed1' }">
                                <template #prefix>
                                    <check-circle-outlined />
                                </template>
                            </a-statistic>
                        </a-card>
                    </a-col>
                    <a-col :span="6">
                        <a-card class="stat-card completion-rate">
                            <a-statistic title="完成率" :value="completionRate" suffix="%"
                                :value-style="{ color: '#faad14' }">
                                <template #prefix>
                                    <trophy-outlined />
                                </template>
                            </a-statistic>
                        </a-card>
                    </a-col>
                </a-row>
            </div>

            <div class="statistics-charts">
                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-card title="项目阶段分布" class="chart-card">
                            <div class="chart-container" ref="stageChartRef"></div>
                        </a-card>
                    </a-col>
                    <a-col :span="12">
                        <a-card title="项目类型分布" class="chart-card">
                            <div class="chart-container" ref="typeChartRef"></div>
                        </a-card>
                    </a-col>
                </a-row>

                <a-row :gutter="16" style="margin-top: 16px;">
                    <a-col :span="24">
                        <a-card title="项目进度趋势" class="chart-card">
                            <div class="chart-container large" ref="trendChartRef"></div>
                        </a-card>
                    </a-col>
                </a-row>
            </div>

            <div class="statistics-details">
                <a-card title="详细统计" class="details-card">
                    <a-table :columns="statisticsColumns" :data-source="statisticsTableData" :pagination="false"
                        size="small">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'progress'">
                                <a-progress :percent="record.progress" size="small"
                                    :stroke-color="getProgressColor(record.progress)" />
                            </template>
                            <template v-if="column.key === 'status'">
                                <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
                            </template>
                        </template>
                    </a-table>
                </a-card>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
    DownloadOutlined,
    ProjectOutlined,
    PlayCircleOutlined,
    CheckCircleOutlined,
    TrophyOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    statisticsData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'export': []
}>()

// 响应式数据
const stageChartRef = ref<HTMLElement>()
const typeChartRef = ref<HTMLElement>()
const trendChartRef = ref<HTMLElement>()

// 计算属性
const completionRate = computed(() => {
    const total = props.statisticsData?.totalProjects || 0
    const completed = props.statisticsData?.completedProjects || 0
    return total > 0 ? Math.round((completed / total) * 100) : 0
})

const statisticsColumns = [
    {
        title: '阶段',
        dataIndex: 'stage',
        key: 'stage',
        width: 120
    },
    {
        title: '项目数量',
        dataIndex: 'count',
        key: 'count',
        width: 100
    },
    {
        title: '占比',
        dataIndex: 'percentage',
        key: 'percentage',
        width: 80
    },
    {
        title: '平均进度',
        dataIndex: 'progress',
        key: 'progress',
        width: 150
    },
    {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
    }
]

const statisticsTableData = computed(() => {
    if (!props.statisticsData?.stageStatistics) return []
    return props.statisticsData.stageStatistics.map((item: any, index: number) => ({
        key: index,
        stage: item.stageName,
        count: item.projectCount,
        percentage: `${item.percentage}%`,
        progress: item.averageProgress,
        status: item.status
    }))
})

// 方法
const handleExportStatistics = () => {
    emit('export')
    message.success('正在导出统计数据...')
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        '正常': 'success',
        '延期': 'warning',
        '风险': 'error',
        '暂停': 'default'
    }
    return colorMap[status] || 'default'
}

const initCharts = async () => {
    await nextTick()
    // 这里可以集成图表库如 ECharts
    // 暂时使用模拟数据
    console.log('初始化图表')
}

// 生命周期
onMounted(() => {
    initCharts()
})
</script>

<style scoped lang="scss">
.project-statistics-overview {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .statistics-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .statistics-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .statistics-content {
        flex: 1;
        overflow-y: auto;

        .overview-cards {
            margin-bottom: 20px;

            .stat-card {
                height: 120px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                transition: all 0.3s ease;

                &:hover {
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    transform: translateY(-2px);
                }

                &.total-projects {
                    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
                    border-color: #1890ff;
                }

                &.active-projects {
                    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
                    border-color: #52c41a;
                }

                &.completed-projects {
                    background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
                    border-color: #722ed1;
                }

                &.completion-rate {
                    background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
                    border-color: #faad14;
                }
            }
        }

        .statistics-charts {
            margin-bottom: 20px;

            .chart-card {
                .chart-container {
                    height: 300px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #f5f5f5;
                    border-radius: 4px;
                    color: #666;

                    &.large {
                        height: 400px;
                    }

                    &::before {
                        content: '图表加载中...';
                    }
                }
            }
        }

        .statistics-details {
            .details-card {
                .ant-table {
                    .ant-table-thead > tr > th {
                        background: #fafafa;
                        font-weight: 600;
                    }

                    .ant-table-tbody > tr:hover > td {
                        background: #f5f5f5;
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .project-statistics-overview {
        .statistics-content {
            .overview-cards {
                .ant-col {
                    margin-bottom: 16px;
                }
            }

            .statistics-charts {
                .ant-col {
                    margin-bottom: 16px;
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .project-statistics-overview {
        padding: 16px;

        .statistics-content {
            .overview-cards {
                .ant-row {
                    .ant-col {
                        span: 12 !important;
                    }
                }
            }

            .statistics-charts {
                .ant-row {
                    .ant-col {
                        span: 24 !important;
                    }
                }

                .chart-container {
                    height: 250px !important;

                    &.large {
                        height: 300px !important;
                    }
                }
            }
        }
    }
}
</style>