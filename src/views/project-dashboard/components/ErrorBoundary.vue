<template>
    <div class="error-boundary">
        <div v-if="hasError" class="error-container">
            <div class="error-content">
                <div class="error-icon">
                    <exclamation-circle-outlined />
                </div>
                <div class="error-info">
                    <h3 class="error-title">{{ errorTitle }}</h3>
                    <p class="error-message">{{ errorMessage }}</p>
                    <div class="error-details" v-if="showDetails">
                        <a-collapse>
                            <a-collapse-panel key="1" header="错误详情">
                                <pre class="error-stack">{{ errorStack }}</pre>
                            </a-collapse-panel>
                        </a-collapse>
                    </div>
                </div>
            </div>
            <div class="error-actions">
                <a-space>
                    <a-button type="primary" @click="handleRetry">
                        <template #icon><reload-outlined /></template>
                        重试
                    </a-button>
                    <a-button @click="handleReset">
                        <template #icon><home-outlined /></template>
                        返回首页
                    </a-button>
                    <a-button @click="toggleDetails">
                        <template #icon><info-circle-outlined /></template>
                        {{ showDetails ? '隐藏' : '显示' }}详情
                    </a-button>
                    <a-button @click="handleReport">
                        <template #icon><bug-outlined /></template>
                        报告问题
                    </a-button>
                </a-space>
            </div>
        </div>
        <div v-else>
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onErrorCaptured, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    ExclamationCircleOutlined,
    ReloadOutlined,
    HomeOutlined,
    InfoCircleOutlined,
    BugOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    fallbackTitle?: string
    fallbackMessage?: string
    showRetry?: boolean
    showReport?: boolean
    onError?: (error: Error, errorInfo: any) => void
}

const props = withDefaults(defineProps<Props>(), {
    fallbackTitle: '页面加载出错',
    fallbackMessage: '抱歉，页面遇到了一些问题。请尝试刷新页面或联系管理员。',
    showRetry: true,
    showReport: true
})

// Emits定义
const emit = defineEmits<{
    'error': [error: Error, errorInfo: any]
    'retry': []
    'reset': []
    'report': [error: Error, errorInfo: any]
}>()

// 响应式数据
const hasError = ref(false)
const errorInstance = ref<Error | null>(null)
const errorInfo = ref<any>(null)
const showDetails = ref(false)

// 计算属性
const errorTitle = computed(() => {
    if (errorInstance.value?.name === 'ChunkLoadError') {
        return '资源加载失败'
    }
    if (errorInstance.value?.name === 'TypeError') {
        return '类型错误'
    }
    if (errorInstance.value?.name === 'ReferenceError') {
        return '引用错误'
    }
    return props.fallbackTitle
})

const errorMessage = computed(() => {
    if (errorInstance.value?.name === 'ChunkLoadError') {
        return '页面资源加载失败，可能是网络问题或版本更新导致。请尝试刷新页面。'
    }
    if (errorInstance.value?.name === 'TypeError') {
        return '页面执行过程中遇到类型错误，请联系技术支持。'
    }
    if (errorInstance.value?.name === 'ReferenceError') {
        return '页面引用了不存在的变量或方法，请联系技术支持。'
    }
    return props.fallbackMessage
})

const errorStack = computed(() => {
    return errorInstance.value?.stack || '暂无详细错误信息'
})

// 错误捕获
onErrorCaptured((error: Error, instance: any, info: string) => {
    console.error('ErrorBoundary captured error:', error, info)

    hasError.value = true
    errorInstance.value = error
    errorInfo.value = { instance, info }

    // 触发错误回调
    if (props.onError) {
        props.onError(error, { instance, info })
    }

    emit('error', error, { instance, info })

    // 阻止错误继续向上传播
    return false
})

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('Global error caught:', event.error)

    hasError.value = true
    errorInstance.value = event.error
    errorInfo.value = {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
    }

    if (props.onError) {
        props.onError(event.error, errorInfo.value)
    }

    emit('error', event.error, errorInfo.value)
})

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason)

    const error = new Error(event.reason?.message || 'Unhandled Promise Rejection')
    error.name = 'UnhandledPromiseRejection'

    hasError.value = true
    errorInstance.value = error
    errorInfo.value = { reason: event.reason }

    if (props.onError) {
        props.onError(error, errorInfo.value)
    }

    emit('error', error, errorInfo.value)

    // 阻止默认的控制台错误输出
    event.preventDefault()
})

// 方法
const handleRetry = () => {
    hasError.value = false
    errorInstance.value = null
    errorInfo.value = null
    showDetails.value = false

    emit('retry')
    message.success('正在重新加载...')

    // 延迟一下再重新渲染，给用户一个反馈
    setTimeout(() => {
        // 可以在这里添加重新加载逻辑
        window.location.reload()
    }, 500)
}

const handleReset = () => {
    emit('reset')

    // 清除错误状态
    hasError.value = false
    errorInstance.value = null
    errorInfo.value = null
    showDetails.value = false

    // 导航到首页
    window.location.href = '/'
}

const toggleDetails = () => {
    showDetails.value = !showDetails.value
}

const handleReport = () => {
    if (errorInstance.value && errorInfo.value) {
        emit('report', errorInstance.value, errorInfo.value)

        // 这里可以集成错误报告服务
        const errorReport = {
            error: {
                name: errorInstance.value.name,
                message: errorInstance.value.message,
                stack: errorInstance.value.stack
            },
            errorInfo: errorInfo.value,
            userAgent: navigator.userAgent,
            url: window.location.href,
            timestamp: new Date().toISOString()
        }

        console.log('Error report:', errorReport)

        // 模拟发送错误报告
        message.success('错误报告已发送，感谢您的反馈！')
    }
}

// 重置错误状态的方法（供外部调用）
const resetError = () => {
    hasError.value = false
    errorInstance.value = null
    errorInfo.value = null
    showDetails.value = false
}

// 手动触发错误的方法（用于测试）
const triggerError = (error: Error) => {
    hasError.value = true
    errorInstance.value = error
    errorInfo.value = { manual: true }
}

// 暴露方法给父组件
defineExpose({
    resetError,
    triggerError,
    hasError: () => hasError.value
})
</script>

<style scoped lang="scss">
.error-boundary {
    width: 100%;
    height: 100%;

    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        padding: 40px 20px;
        text-align: center;

        .error-content {
            max-width: 600px;
            margin-bottom: 32px;

            .error-icon {
                font-size: 64px;
                color: #ff4d4f;
                margin-bottom: 24px;

                .anticon {
                    display: block;
                }
            }

            .error-info {
                .error-title {
                    font-size: 24px;
                    font-weight: 600;
                    color: #262626;
                    margin-bottom: 16px;
                }

                .error-message {
                    font-size: 16px;
                    color: #666;
                    line-height: 1.6;
                    margin-bottom: 24px;
                }

                .error-details {
                    text-align: left;
                    margin-top: 24px;

                    .ant-collapse {
                        background: #fafafa;
                        border: 1px solid #e8e8e8;
                        border-radius: 8px;

                        .ant-collapse-item {
                            border-bottom: none;

                            .ant-collapse-header {
                                padding: 12px 16px;
                                font-weight: 500;
                                color: #666;
                            }

                            .ant-collapse-content {
                                .ant-collapse-content-box {
                                    padding: 16px;
                                    background: #fff;

                                    .error-stack {
                                        font-family: 'Courier New', monospace;
                                        font-size: 12px;
                                        color: #d32f2f;
                                        background: #fff5f5;
                                        padding: 12px;
                                        border-radius: 4px;
                                        border: 1px solid #ffebee;
                                        white-space: pre-wrap;
                                        word-break: break-all;
                                        max-height: 200px;
                                        overflow-y: auto;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .error-actions {
            .ant-btn {
                margin: 0 4px;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .error-boundary {
        .error-container {
            padding: 20px 16px;

            .error-content {
                .error-icon {
                    font-size: 48px;
                    margin-bottom: 16px;
                }

                .error-info {
                    .error-title {
                        font-size: 20px;
                        margin-bottom: 12px;
                    }

                    .error-message {
                        font-size: 14px;
                        margin-bottom: 20px;
                    }
                }
            }

            .error-actions {
                .ant-space {
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .ant-btn {
                    margin: 4px;
                    min-width: 80px;
                }
            }
        }
    }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
    .error-boundary {
        .error-container {
            .error-content {
                .error-info {
                    .error-title {
                        color: #fff;
                    }

                    .error-message {
                        color: #bfbfbf;
                    }

                    .error-details {
                        .ant-collapse {
                            background: #1f1f1f;
                            border-color: #434343;

                            .ant-collapse-item {
                                .ant-collapse-header {
                                    color: #bfbfbf;
                                    background: #1f1f1f;
                                }

                                .ant-collapse-content {
                                    .ant-collapse-content-box {
                                        background: #262626;

                                        .error-stack {
                                            background: #2d1b1b;
                                            border-color: #5c2e2e;
                                            color: #ff7875;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>