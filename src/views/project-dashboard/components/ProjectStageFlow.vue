<template>
    <div class="project-stage-flow">
        <div class="flow-header">
            <h3 class="flow-title">项目阶段流程</h3>
            <div class="flow-actions">
                <a-button @click="handleRefreshFlow">
                    <template #icon><reload-outlined /></template>
                    刷新流程
                </a-button>
            </div>
        </div>

        <div class="flow-content" v-loading="loading">
            <div class="stage-flow-diagram">
                <div class="flow-stages">
                    <div v-for="(stage, index) in stageFlowData?.stages || []" :key="stage.id" class="stage-item"
                        :class="[stage.status, { active: stage.isActive }]" @click="handleStageClick(stage)">

                        <div class="stage-icon">
                            <component :is="getStageIcon(stage.type)" />
                        </div>

                        <div class="stage-content">
                            <div class="stage-name">{{ stage.name }}</div>
                            <div class="stage-description">{{ stage.description }}</div>
                            <div class="stage-stats">
                                <span class="project-count">{{ stage.projectCount }}个项目</span>
                                <span class="completion-rate">完成率{{ stage.completionRate }}%</span>
                            </div>
                        </div>

                        <div class="stage-progress">
                            <a-progress type="circle" :percent="stage.completionRate" :width="60"
                                :stroke-color="getStageColor(stage.status)" />
                        </div>

                        <!-- 连接线 -->
                        <div v-if="index < (stageFlowData?.stages?.length || 0) - 1" class="stage-connector">
                            <div class="connector-line" :class="{ active: stage.isCompleted }"></div>
                            <div class="connector-arrow">
                                <right-outlined />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flow-details">
                <a-tabs v-model:activeKey="activeFlowTab" @change="handleFlowTabChange">
                    <a-tab-pane key="overview" tab="流程概览">
                        <div class="overview-section">
                            <a-row :gutter="16">
                                <a-col :span="8">
                                    <a-card class="metric-card">
                                        <a-statistic title="总阶段数" :value="stageFlowData?.totalStages || 0"
                                            :value-style="{ color: '#1890ff' }" />
                                    </a-card>
                                </a-col>
                                <a-col :span="8">
                                    <a-card class="metric-card">
                                        <a-statistic title="活跃阶段" :value="stageFlowData?.activeStages || 0"
                                            :value-style="{ color: '#52c41a' }" />
                                    </a-card>
                                </a-col>
                                <a-col :span="8">
                                    <a-card class="metric-card">
                                        <a-statistic title="整体进度" :value="overallProgress" suffix="%"
                                            :value-style="{ color: '#faad14' }" />
                                    </a-card>
                                </a-col>
                            </a-row>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="projects" tab="阶段项目">
                        <div class="projects-section">
                            <div class="projects-filters">
                                <a-space>
                                    <a-select v-model:value="selectedStage" placeholder="选择阶段" style="width: 150px"
                                        @change="handleStageFilterChange">
                                        <a-select-option value="">全部阶段</a-select-option>
                                        <a-select-option v-for="stage in stageFlowData?.stages || []" :key="stage.id"
                                            :value="stage.id">
                                            {{ stage.name }}
                                        </a-select-option>
                                    </a-select>

                                    <a-select v-model:value="selectedProjectStatus" placeholder="项目状态"
                                        style="width: 120px" @change="handleProjectStatusChange">
                                        <a-select-option value="">全部状态</a-select-option>
                                        <a-select-option value="planning">规划中</a-select-option>
                                        <a-select-option value="executing">执行中</a-select-option>
                                        <a-select-option value="completed">已完成</a-select-option>
                                        <a-select-option value="paused">已暂停</a-select-option>
                                    </a-select>
                                </a-space>
                            </div>

                            <div class="projects-list">
                                <div v-for="project in filteredProjects" :key="project.id" class="project-item"
                                    @click="handleProjectClick(project)">
                                    <div class="project-header">
                                        <div class="project-info">
                                            <span class="project-name">{{ project.name }}</span>
                                            <div class="project-tags">
                                                <a-tag :color="getProjectStatusColor(project.status)">
                                                    {{ getProjectStatusText(project.status) }}
                                                </a-tag>
                                                <a-tag :color="getStageColor(project.currentStage)">
                                                    {{ project.currentStageName }}
                                                </a-tag>
                                            </div>
                                        </div>
                                        <div class="project-progress">
                                            <span class="progress-text">{{ project.progress }}%</span>
                                            <a-progress :percent="project.progress" size="small"
                                                :stroke-color="getProgressColor(project.progress)" />
                                        </div>
                                    </div>

                                    <div class="project-content">
                                        <div class="project-description">{{ project.description }}</div>
                                        <div class="project-details">
                                            <div class="detail-item">
                                                <span class="detail-label">负责人:</span>
                                                <span class="detail-value">{{ project.manager }}</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">开始时间:</span>
                                                <span class="detail-value">{{ formatDate(project.startDate) }}</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="detail-label">预计完成:</span>
                                                <span class="detail-value">{{ formatDate(project.expectedEndDate)
                                                }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div v-if="!filteredProjects.length && !loading" class="empty-state">
                                    <a-empty description="暂无项目数据" />
                                </div>
                            </div>
                        </div>
                    </a-tab-pane>

                    <a-tab-pane key="timeline" tab="时间线">
                        <div class="timeline-section">
                            <a-timeline>
                                <a-timeline-item v-for="event in stageFlowData?.timeline || []" :key="event.id"
                                    :color="getTimelineColor(event.type)">
                                    <template #dot>
                                        <component :is="getTimelineIcon(event.type)" />
                                    </template>
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title">{{ event.title }}</span>
                                            <span class="timeline-time">{{ formatDateTime(event.time) }}</span>
                                        </div>
                                        <div class="timeline-description">{{ event.description }}</div>
                                        <div class="timeline-details" v-if="event.details">
                                            <div v-for="detail in event.details" :key="detail.key" class="detail-item">
                                                <span class="detail-label">{{ detail.label }}:</span>
                                                <span class="detail-value">{{ detail.value }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </a-timeline-item>
                            </a-timeline>
                        </div>
                    </a-tab-pane>
                </a-tabs>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    ReloadOutlined,
    RightOutlined,
    SearchOutlined,
    UserOutlined,
    ProjectOutlined,
    CheckCircleOutlined,
    ClockCircleOutlined,
    ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// Props定义
interface Props {
    stageFlowData: any
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    loading: false
})

// Emits定义
const emit = defineEmits<{
    'stage-click': [stage: any]
    'project-click': [project: any]
    'refresh': []
}>()

// 响应式数据
const activeFlowTab = ref('overview')
const selectedStage = ref('')
const selectedProjectStatus = ref('')

// 计算属性
const overallProgress = computed(() => {
    if (!props.stageFlowData?.stages) return 0
    const stages = props.stageFlowData.stages
    const totalProgress = stages.reduce((sum: number, stage: any) => sum + stage.completionRate, 0)
    return Math.round(totalProgress / stages.length)
})

const filteredProjects = computed(() => {
    if (!props.stageFlowData?.projects) return []
    let filtered = props.stageFlowData.projects

    if (selectedStage.value) {
        filtered = filtered.filter((p: any) => p.currentStage === selectedStage.value)
    }

    if (selectedProjectStatus.value) {
        filtered = filtered.filter((p: any) => p.status === selectedProjectStatus.value)
    }

    return filtered
})

// 方法
const handleRefreshFlow = () => {
    emit('refresh')
    message.success('流程数据已刷新')
}

const handleFlowTabChange = (key: string) => {
    activeFlowTab.value = key
}

const handleStageClick = (stage: any) => {
    emit('stage-click', stage)
}

const handleProjectClick = (project: any) => {
    emit('project-click', project)
}

const handleStageFilterChange = (stageId: string) => {
    selectedStage.value = stageId
}

const handleProjectStatusChange = (status: string) => {
    selectedProjectStatus.value = status
}

const getStageIcon = (type: string) => {
    const iconMap: Record<string, any> = {
        'selection': SearchOutlined,
        'cultivation': UserOutlined,
        'establishment': ProjectOutlined,
        'promotion': CheckCircleOutlined
    }
    return iconMap[type] || ProjectOutlined
}

const getStageColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'completed': '#52c41a',
        'active': '#1890ff',
        'pending': '#d9d9d9',
        'warning': '#faad14',
        'error': '#f5222d'
    }
    return colorMap[status] || '#d9d9d9'
}

const getProjectStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
        'planning': 'blue',
        'executing': 'processing',
        'completed': 'success',
        'paused': 'default'
    }
    return colorMap[status] || 'default'
}

const getProjectStatusText = (status: string) => {
    const textMap: Record<string, string> = {
        'planning': '规划中',
        'executing': '执行中',
        'completed': '已完成',
        'paused': '已暂停'
    }
    return textMap[status] || status
}

const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#52c41a'
    if (progress >= 70) return '#faad14'
    if (progress >= 50) return '#ff7a45'
    return '#f5222d'
}

const getTimelineColor = (type: string) => {
    const colorMap: Record<string, string> = {
        'milestone': 'green',
        'warning': 'orange',
        'error': 'red',
        'info': 'blue'
    }
    return colorMap[type] || 'blue'
}

const getTimelineIcon = (type: string) => {
    const iconMap: Record<string, any> = {
        'milestone': CheckCircleOutlined,
        'warning': ExclamationCircleOutlined,
        'error': ExclamationCircleOutlined,
        'info': ClockCircleOutlined
    }
    return iconMap[type] || ClockCircleOutlined
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
}

const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
}
</script>

<style scoped lang="scss">
.project-stage-flow {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .flow-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .flow-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
        }
    }

    .flow-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .stage-flow-diagram {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e8e8e8;

            .flow-stages {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;

                .stage-item {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    padding: 16px;
                    background: white;
                    border: 2px solid #e8e8e8;
                    border-radius: 12px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    min-width: 200px;

                    &:hover {
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        transform: translateY(-2px);
                    }

                    &.active {
                        border-color: #1890ff;
                        background: #f6ffed;
                    }

                    &.completed {
                        border-color: #52c41a;
                        background: #f6ffed;
                    }

                    &.warning {
                        border-color: #faad14;
                        background: #fffbe6;
                    }

                    &.error {
                        border-color: #f5222d;
                        background: #fff2f0;
                    }

                    .stage-icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 50%;
                        background: #f5f5f5;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                        color: #666;
                        margin-bottom: 12px;
                    }

                    .stage-content {
                        text-align: center;
                        margin-bottom: 12px;

                        .stage-name {
                            font-size: 16px;
                            font-weight: 600;
                            color: #262626;
                            margin-bottom: 4px;
                        }

                        .stage-description {
                            font-size: 12px;
                            color: #666;
                            margin-bottom: 8px;
                        }

                        .stage-stats {
                            display: flex;
                            flex-direction: column;
                            gap: 2px;

                            .project-count,
                            .completion-rate {
                                font-size: 11px;
                                color: #999;
                            }
                        }
                    }

                    .stage-connector {
                        position: absolute;
                        right: -40px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 80px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .connector-line {
                            width: 60px;
                            height: 2px;
                            background: #e8e8e8;
                            position: relative;

                            &.active {
                                background: #52c41a;
                            }
                        }

                        .connector-arrow {
                            position: absolute;
                            right: -8px;
                            color: #e8e8e8;
                            font-size: 16px;

                            .connector-line.active+& {
                                color: #52c41a;
                            }
                        }
                    }
                }
            }
        }

        .flow-details {
            flex: 1;
            overflow: hidden;

            .ant-tabs {
                height: 100%;
                display: flex;
                flex-direction: column;

                .ant-tabs-content-holder {
                    flex: 1;
                    overflow: hidden;

                    .ant-tabs-content {
                        height: 100%;

                        .ant-tabs-tabpane {
                            height: 100%;
                            overflow-y: auto;

                            .overview-section {
                                .metric-card {
                                    text-align: center;
                                    border-radius: 8px;
                                }
                            }

                            .projects-section {
                                .projects-filters {
                                    margin-bottom: 16px;
                                    padding-bottom: 12px;
                                    border-bottom: 1px solid #e8e8e8;
                                }

                                .projects-list {
                                    .project-item {
                                        padding: 16px;
                                        margin-bottom: 12px;
                                        background: white;
                                        border: 1px solid #e8e8e8;
                                        border-radius: 8px;
                                        cursor: pointer;
                                        transition: all 0.3s ease;

                                        &:hover {
                                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                                            border-color: #1890ff;
                                        }

                                        .project-header {
                                            display: flex;
                                            justify-content: space-between;
                                            align-items: flex-start;
                                            margin-bottom: 12px;

                                            .project-info {
                                                flex: 1;

                                                .project-name {
                                                    display: block;
                                                    font-size: 16px;
                                                    font-weight: 500;
                                                    color: #262626;
                                                    margin-bottom: 8px;
                                                }

                                                .project-tags {
                                                    display: flex;
                                                    gap: 8px;
                                                }
                                            }

                                            .project-progress {
                                                text-align: right;
                                                min-width: 120px;

                                                .progress-text {
                                                    display: block;
                                                    font-size: 12px;
                                                    color: #666;
                                                    margin-bottom: 4px;
                                                }
                                            }
                                        }

                                        .project-content {
                                            .project-description {
                                                color: #666;
                                                font-size: 13px;
                                                line-height: 1.5;
                                                margin-bottom: 8px;
                                            }

                                            .project-details {
                                                display: flex;
                                                gap: 16px;
                                                flex-wrap: wrap;

                                                .detail-item {
                                                    font-size: 12px;

                                                    .detail-label {
                                                        color: #666;
                                                    }

                                                    .detail-value {
                                                        color: #262626;
                                                        font-weight: 500;
                                                        margin-left: 4px;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    .empty-state {
                                        height: 200px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    }
                                }
                            }

                            .timeline-section {
                                .timeline-content {
                                    .timeline-header {
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: center;
                                        margin-bottom: 8px;

                                        .timeline-title {
                                            font-size: 14px;
                                            font-weight: 500;
                                            color: #262626;
                                        }

                                        .timeline-time {
                                            font-size: 12px;
                                            color: #999;
                                        }
                                    }

                                    .timeline-description {
                                        color: #666;
                                        font-size: 13px;
                                        line-height: 1.5;
                                        margin-bottom: 8px;
                                    }

                                    .timeline-details {
                                        display: flex;
                                        gap: 16px;
                                        flex-wrap: wrap;

                                        .detail-item {
                                            font-size: 12px;

                                            .detail-label {
                                                color: #666;
                                            }

                                            .detail-value {
                                                color: #262626;
                                                font-weight: 500;
                                                margin-left: 4px;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .project-stage-flow {
        .flow-content {
            .stage-flow-diagram {
                .flow-stages {
                    flex-wrap: wrap;
                    gap: 20px;
                    justify-content: center;

                    .stage-item {
                        .stage-connector {
                            display: none;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 768px) {
    .project-stage-flow {
        padding: 16px;

        .flow-content {
            .stage-flow-diagram {
                padding: 16px;

                .flow-stages {
                    .stage-item {
                        min-width: 150px;
                        padding: 12px;

                        .stage-icon {
                            width: 40px;
                            height: 40px;
                            font-size: 16px;
                        }

                        .stage-progress {
                            .ant-progress {
                                width: 50px !important;
                                height: 50px !important;
                            }
                        }
                    }
                }
            }

            .flow-details {
                .projects-section {
                    .projects-list {
                        .project-item {
                            padding: 12px;

                            .project-header {
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 8px;

                                .project-progress {
                                    align-self: flex-end;
                                }
                            }

                            .project-content {
                                .project-details {
                                    flex-direction: column;
                                    gap: 4px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>