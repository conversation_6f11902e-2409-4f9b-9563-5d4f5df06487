// 选育树推项目看板 - 类型定义
// Source: 基于选育树推项目看板.md需求文档生成

// 枚举定义
export enum ProjectStage {
  SELECTION = 'selection',        // 选拔阶段
  CULTIVATION = 'cultivation',    // 培育阶段
  ESTABLISHMENT = 'establishment', // 树立阶段
  PROMOTION = 'promotion'         // 推广阶段
}

export enum ProjectStatus {
  PLANNING = 'planning',      // 规划中
  ACTIVE = 'active',         // 进行中
  COMPLETED = 'completed',   // 已完成
  PAUSED = 'paused',        // 暂停
  CANCELLED = 'cancelled',   // 已取消
  DELAYED = 'delayed'        // 延期
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum UnitType {
  MODEL_AGENCY = 'model_agency',      // 模范机关单位
  EXCELLENT = 'excellent',            // 优秀单位
  GOOD = 'good',                     // 良好单位
  STANDARD = 'standard',             // 标准单位
  IMPROVEMENT_NEEDED = 'improvement_needed' // 需改进单位
}

// 基础接口
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 里程碑接口
export interface Milestone {
  id: string
  name: string
  description: string
  date: string
  isCompleted: boolean
  isCritical: boolean
  completedAt?: string
  completedBy?: string
}

// 项目指标接口
export interface ProjectMetrics {
  qualityScore: number
  riskLevel: 'low' | 'medium' | 'high'
  resourceUtilization: number
  stakeholderSatisfaction: number
  innovationIndex: number
  socialImpact: number
}

// 项目接口
export interface Project {
  id: string
  name: string
  description: string
  stage: ProjectStage
  status: ProjectStatus
  priority: ProjectPriority
  progress: number
  startDate: string
  endDate: string
  expectedEndDate: string
  manager: string
  department: string
  budget: number
  actualCost: number
  participants: number
  beneficiaries: number
  tags: string[]
  isDelayed: boolean
  isCritical: boolean
  milestones: Milestone[]
  dependencies: string[]
  metrics: ProjectMetrics
  createdAt: string
  updatedAt: string
}

// 项目统计数据
export interface ProjectStatistics {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  delayedProjects: number
  totalBudget: number
  actualCost: number
  avgProgress: number
  avgQualityScore: number
  stageDistribution: {
    selection: number
    cultivation: number
    establishment: number
    promotion: number
  }
  statusDistribution: {
    planning: number
    active: number
    completed: number
    paused: number
    delayed: number
  }
  monthlyTrends: Array<{
    month: string
    newProjects: number
    completedProjects: number
    totalBudget: number
    avgProgress: number
  }>
}

// 排行榜数据
export interface RankingsData {
  topProjects: Array<{
    id: string
    name: string
    department: string
    manager: string
    score: number
    rank: number
    change: number
    stage: ProjectStage
    progress: number
    highlights: string[]
  }>
  topDepartments: Array<{
    id: string
    name: string
    projectCount: number
    completionRate: number
    avgScore: number
    rank: number
    change: number
  }>
  topManagers: Array<{
    id: string
    name: string
    department: string
    projectCount: number
    successRate: number
    avgScore: number
    rank: number
    change: number
  }>
  innovationProjects: Array<{
    id: string
    name: string
    department: string
    manager: string
    score: number
    rank: number
    change: number
    stage: ProjectStage
    progress: number
    highlights: string[]
  }>
}

// 指标分析数据
export interface IndicatorAnalysisData {
  overallHealth: number
  keyIndicators: Array<{
    id: string
    name: string
    value: number
    target: number
    unit: string
    trend: number
    status: 'good' | 'warning' | 'danger'
    description: string
  }>
  trendAnalysis: {
    period: string
    metrics: Array<{
      name: string
      data: number[]
      dates: string[]
    }>
  }
  riskAssessment: {
    overallRisk: 'low' | 'medium' | 'high'
    riskFactors: Array<{
      id: string
      name: string
      level: 'low' | 'medium' | 'high'
      impact: number
      probability: number
      description: string
      mitigation: string
    }>
    mitigation: string[]
  }
  recommendations: Array<{
    id: string
    type: 'optimization' | 'resource-allocation' | 'risk-mitigation'
    title: string
    description: string
    priority: 'low' | 'medium' | 'high'
    estimatedImpact: string
    implementationTime: string
  }>
}

// 监督管理数据
export interface SupervisionData {
  overviewStats: {
    totalAudits: number
    passedAudits: number
    failedAudits: number
    pendingAudits: number
    complianceRate: number
    avgAuditScore: number
  }
  auditResults: Array<{
    id: string
    projectId: string
    projectName: string
    auditDate: string
    auditor: string
    score: number
    status: 'passed' | 'failed' | 'pending'
    findings: string[]
    recommendations: string[]
  }>
  complianceMetrics: Array<{
    id: string
    name: string
    value: number
    threshold: number
    status: 'compliant' | 'warning' | 'non-compliant'
    trend: number
  }>
  alerts: Array<{
    id: string
    type: 'compliance' | 'performance' | 'risk'
    level: 'info' | 'warning' | 'error'
    title: string
    description: string
    projectId: string
    createdAt: string
    isRead: boolean
  }>
}

// 组织洞察数据
export interface OrganizationalInsights {
  departmentAnalysis: Array<{
    id: string
    name: string
    projectCount: number
    avgProgress: number
    resourceUtilization: number
    collaborationScore: number
    innovationIndex: number
    strengths: string[]
    improvements: string[]
  }>
  collaborationNetwork: Array<{
    id: string
    name: string
    type: 'department' | 'project' | 'manager'
    connections: string[]
    weight: number
    x: number
    y: number
  }>
  capacityAnalysis: {
    totalCapacity: number
    usedCapacity: number
    availableCapacity: number
    projectedDemand: number
    bottlenecks: string[]
    recommendations: string[]
  }
  performanceMatrix: {
    departments: Array<{
      name: string
      performance: number
      potential: number
      quadrant: 'star' | 'question-mark' | 'cash-cow' | 'dog'
    }>
  }
}

// 创新跟踪数据
export interface InnovationTrackingData {
  innovationMetrics: Array<{
    id: string
    name: string
    value: number
    target: number
    unit: string
    category: 'input' | 'process' | 'output'
    trend: number
  }>
  innovationProjects: Project[]
  technologyTrends: Array<{
    id: string
    name: string
    category: string
    adoptionRate: number
    maturityLevel: number
    potentialImpact: number
    timeline: string
  }>
  patentAnalysis: {
    totalPatents: number
    newPatents: number
    patentsByCategory: Array<{
      category: string
      count: number
    }>
    patentTrends: Array<{
      month: string
      count: number
    }>
  }
  collaborationMap: {
    internalCollaborations: number
    externalCollaborations: number
    partnerOrganizations: string[]
    collaborationProjects: Project[]
  }
}

// 智能监控数据
export interface IntelligentMonitoringData {
  systemHealth: {
    overall: number
    components: Array<{
      name: string
      status: 'healthy' | 'warning' | 'error'
      uptime: number
      lastCheck: string
    }>
  }
  performanceMetrics: Array<{
    id: string
    name: string
    value: number
    unit: string
    threshold: number
    status: 'normal' | 'warning' | 'critical'
    history: Array<{
      timestamp: string
      value: number
    }>
  }>
  anomalies: Array<{
    id: string
    type: 'performance' | 'security' | 'data'
    severity: 'low' | 'medium' | 'high'
    description: string
    detectedAt: string
    affectedComponents: string[]
    status: 'new' | 'investigating' | 'resolved'
  }>
  predictions: Array<{
    id: string
    type: 'performance' | 'capacity' | 'risk'
    description: string
    confidence: number
    timeframe: string
    impact: 'low' | 'medium' | 'high'
    recommendations: string[]
  }>
  alerts: Array<{
    id: string
    type: 'system' | 'performance' | 'security'
    level: 'info' | 'warning' | 'error'
    title: string
    description: string
    source: string
    timestamp: string
    isAcknowledged: boolean
  }>
}

// 阶段管理数据
export interface StageManagementData {
  totalStages: number
  activeStages: number
  completedStages: number
  stages: Array<{
    id: string
    name: string
    description: string
    type: ProjectStage
    status: 'active' | 'completed' | 'paused'
    progress: number
    projectCount: number
    manager: string
    startDate: string
    expectedEndDate: string
    milestones: Milestone[]
    isDelayed: boolean
  }>
  templates: Array<{
    id: string
    name: string
    description: string
    type: string
    stageCount: number
    usageCount: number
    createTime: string
    stages: Array<{
      name: string
      duration: number
      dependencies: string[]
      milestones: string[]
    }>
  }>
}

// 项目分析数据
export interface ProjectAnalyticsData {
  avgCompletionTime: number
  completionTimeTrend: number
  successRate: number
  successRateTrend: number
  resourceUtilization: number
  resourceTrend: number
  qualityScore: number
  qualityTrend: number
  coverageUnits: number
  beneficiaries: number
  socialImpact: number
  prediction: {
    trend: 'positive' | 'negative' | 'stable'
    period: string
    improvement: number
  }
}

// 时间线数据
export interface TimelineData {
  totalProjects: number
  activeProjects: number
  upcomingDeadlines: number
  delayedProjects: number
  projects: Project[]
}

// 组件Props接口定义
export interface ProjectOverviewProps {
  data?: {
    stageStats: ProjectStageStats[]
    recentDynamics: Array<{
      id: string
      title: string
      content: string
      type: 'achievement' | 'media' | 'honor' | 'case'
      projectName: string
      unitName: string
      publishTime: string
    }>
    achievements: Array<{
      id: string
      awardTitle: string
      projectName: string
      unitName: string
      awardLevel: string
      awardTime: string
    }>
  }
  loading?: boolean
}

export interface ProjectStageStats {
  stage: string
  stageName: string
  count: number
  percentage: number
  projects: Project[]
}
