# 选育树推项目看板

## 项目概述

选育树推项目看板是一个综合性的项目管理和数据展示平台，用于展示和管理选育树推项目的全生命周期数据。该看板提供了项目全景概览、指标分析、组织洞察、督查系统等核心功能。

## 功能特性

### 1. 项目全景概览
- **阶段数据可视化**: 展示项目在不同阶段（申报遴选、培育、综合评定、完成评定）的分布情况
- **项目创建动态**: 实时展示项目相关的成果、媒体报道、荣誉、优秀案例等动态信息
- **项目创建成果**: 展示项目获得的各类奖项和荣誉

### 2. 指标分析
- **指标统计**: 展示总指标数、数据资源数量、完成率等关键指标
- **指标详情**: 显示各个指标的完成情况和进度
- **培育单位排名**: 根据党建效能、行政绩效、创新实践三个维度对单位进行排名

### 3. 组织洞察
- **党建效能**: 
  - 组织生活纪实完成度
  - "红岩先锋"工作成效（四强支部、六好党员）
  - 党组"第一议题"制度落实率
- **行政效能**:
  - 政务效能得分
  - 数字重庆建设考核得分
  - "三个一批"改革项目推进率
  - 节约型/无废/无烟机关创建情况
- **创新实践**:
  - 最佳实践案例数量
  - 改革创新奖获评数量
  - 植绿护绿活动开展次数
  - "三服务"机制考核得分
  - 主题教育活动负面清单合规率

### 4. 督查系统
- **督查清单**: 展示督查总数、已整改、待整改、逾期等统计信息
- **督办事项**: 显示具体的督办事项，包括优先级、截止时间、处理状态等
- **参与情况**: 展示组织活动次数、参与率、党员报到完成率等参与度数据

### 5. 区县视图
- 支持按区县筛选查看数据
- 提供区县级别的项目统计和指标分析

## 技术架构

### 前端技术栈
- **Vue 3**: 使用Composition API构建响应式用户界面
- **TypeScript**: 提供类型安全和更好的开发体验
- **Ant Design Vue**: UI组件库，提供丰富的组件和设计规范
- **SCSS**: CSS预处理器，支持嵌套、变量、混入等特性

### 组件结构
```
project-dashboard/
├── Index.vue                 # 主页面组件
├── components/               # 子组件目录
│   ├── ProjectOverview.vue   # 项目概览组件
│   └── ...                   # 其他功能组件（待扩展）
├── types/                    # 类型定义
│   └── index.ts             # 主要类型定义文件
├── mock/                     # 模拟数据
│   └── data.ts              # 模拟数据服务
└── README.md                # 项目文档
```

### 数据模型
- **Project**: 项目基本信息
- **ProjectStageStats**: 项目阶段统计
- **ProjectDynamic**: 项目动态信息
- **Indicator**: 指标信息
- **CultivationRanking**: 培育单位排名
- **SupervisionItem**: 督办事项
- **OrganizationalInsights**: 组织洞察数据
- **DistrictOverview**: 区县概览数据

## 使用说明

### 页面访问
通过路由 `/project-dashboard` 访问项目看板页面，或通过左侧菜单"选育树推项目看板"进入。

### 功能操作
1. **区县筛选**: 使用页面顶部的区县选择器筛选特定区县的数据
2. **数据刷新**: 点击"刷新数据"按钮重新加载最新数据
3. **报告导出**: 支持导出PDF报告和Excel数据（功能开发中）
4. **项目详情**: 点击项目阶段卡片查看该阶段的项目列表
5. **排名查看**: 查看培育单位的详细排名和得分情况

### 响应式设计
- 支持桌面端、平板端、移动端的响应式显示
- 在不同屏幕尺寸下自动调整布局和组件大小
- 移动端优化了交互体验和信息展示

## 开发指南

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问页面
http://localhost:3000/project-dashboard
```

### 数据接口
当前使用模拟数据服务 `MockDataService`，实际部署时需要替换为真实的API接口：

```typescript
// 获取看板主数据
MockDataService.fetchDashboardData()

// 获取区县数据
MockDataService.fetchDistrictData(districtId)
```

### 组件扩展
如需添加新的功能组件：
1. 在 `components/` 目录下创建新组件
2. 在 `types/index.ts` 中定义相关类型
3. 在 `mock/data.ts` 中添加模拟数据
4. 在主页面 `Index.vue` 中引入和使用

### 样式定制
- 使用SCSS变量定制主题色彩
- 遵循Ant Design设计规范
- 支持响应式断点自定义

## 部署说明

### 构建生产版本
```bash
npm run build
```

### 环境配置
- 开发环境：使用模拟数据
- 生产环境：需要配置真实API接口地址

### 性能优化
- 组件懒加载
- 图片资源优化
- 代码分割
- 缓存策略

## 更新日志

### v1.0.0 (2025-01-27)
- 初始版本发布
- 实现项目全景概览功能
- 实现指标分析和排名展示
- 实现组织洞察数据展示
- 实现督查系统功能
- 支持区县数据筛选
- 响应式设计适配

## 联系方式

如有问题或建议，请联系开发团队。
