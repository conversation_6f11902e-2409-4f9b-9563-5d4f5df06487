// 选育树推项目看板 - 模拟数据
// Source: 基于选育树推项目看板.md需求文档生成

import {
  ProjectStage,
  ProjectStatus,
  UnitType,
  type Project,
  type ProjectStageStats,
  type ProjectDynamic,
  type ProjectAchievement,
  type Indicator,
  type CultivationRanking,
  type SupervisionData,
  type PartyBuildingData,
  type AdminEfficiencyData,
  type InnovationTrackingData,
  type OrganizationalData,
  type ParticipationData,
  type DistrictOverview,
  type DashboardData
} from '../types'

// 生成模拟项目数据
const generateMockProjects = (count: number): Project[] => {
  const projects: Project[] = []
  const projectNames = [
    '党建引领基层治理创新项目',
    '数字化政务服务优化项目',
    '社区网格化管理提升项目',
    '机关效能建设示范项目',
    '党员教育培训创新项目',
    '基层党组织标准化建设项目',
    '政务公开透明化项目',
    '干部作风建设项目',
    '党风廉政建设项目',
    '群众满意度提升项目'
  ]

  const unitNames = [
    '市委组织部', '市政府办公室', '市纪委监委', '市委宣传部', '市发改委',
    '市财政局', '市人社局', '市住建局', '市交通局', '市教委'
  ]

  for (let i = 0; i < count; i++) {
    const stageValues = Object.values(ProjectStage)
    const statusValues = Object.values(ProjectStatus)
    const unitTypeValues = Object.values(UnitType)

    projects.push({
      id: `project-${i + 1}`,
      name: projectNames[i % projectNames.length],
      stage: stageValues[i % stageValues.length],
      status: statusValues[i % statusValues.length],
      unitType: unitTypeValues[i % unitTypeValues.length],
      initiatingUnit: unitNames[i % unitNames.length],
      applicationUnits: Math.floor(Math.random() * 20) + 5,
      cultivationUnits: Math.floor(Math.random() * 15) + 3,
      benchmarkUnits: Math.floor(Math.random() * 10) + 1,
      createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    })
  }

  return projects
}

// 阶段统计数据
const generateStageStats = (): ProjectStageStats[] => {
  const mockProjects = generateMockProjects(100)
  const stageNames = {
    [ProjectStage.SELECTION]: '选拔遴选',
    [ProjectStage.CULTIVATION]: '培育',
    [ProjectStage.ESTABLISHMENT]: '树立',
    [ProjectStage.PROMOTION]: '推广'
  }

  const stageStats: ProjectStageStats[] = []

  Object.values(ProjectStage).forEach(stage => {
    const stageProjects = mockProjects.filter(p => p.stage === stage)
    const count = stageProjects.length
    const percentage = Math.round((count / mockProjects.length) * 100)

    stageStats.push({
      stage,
      stageName: stageNames[stage],
      count,
      percentage,
      projects: stageProjects
    })
  })

  return stageStats
}

// 项目动态数据
const generateProjectDynamics = (): ProjectDynamic[] => {
  const dynamics: ProjectDynamic[] = []
  const dynamicTypes = ['achievement', 'media', 'honor', 'case']
  const titles = [
    '项目获得市级表彰',
    '媒体报道项目成效',
    '荣获创新奖项',
    '典型案例入选',
    '经验做法推广',
    '工作成果展示'
  ]

  for (let i = 0; i < 20; i++) {
    dynamics.push({
      id: `dynamic-${i + 1}`,
      type: dynamicTypes[i % dynamicTypes.length],
      title: titles[i % titles.length],
      content: `这是第${i + 1}个项目动态的详细内容描述，展示了项目在实施过程中取得的重要进展和成果。`,
      projectName: `示范项目${i + 1}`,
      unitName: `示范单位${i + 1}`,
      publishTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
    })
  }

  return dynamics
}

// 项目成果数据
const generateProjectAchievements = (): ProjectAchievement[] => {
  const achievements: ProjectAchievement[] = []
  const awardTitles = [
    '优秀党建创新项目',
    '政务服务创新奖',
    '基层治理示范奖',
    '机关效能建设先进奖',
    '党员教育创新奖'
  ]

  const awardLevels = ['国家级', '省级', '市级', '区县级']

  for (let i = 0; i < 15; i++) {
    achievements.push({
      id: `achievement-${i + 1}`,
      projectName: `示范项目${i + 1}`,
      unitName: `获奖单位${i + 1}`,
      awardTitle: awardTitles[i % awardTitles.length],
      awardLevel: awardLevels[i % awardLevels.length],
      awardTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      description: `项目${i + 1}在实施过程中表现突出，获得了${awardLevels[i % awardLevels.length]}${awardTitles[i % awardTitles.length]}荣誉。`
    })
  }

  return achievements
}

// 指标数据
const generateIndicators = (): Indicator[] => {
  return [
    {
      id: 'indicator-1',
      name: '项目完成率',
      value: 85.6,
      unit: '%',
      trend: 'up',
      change: 5.2,
      description: '本月项目完成率较上月提升5.2个百分点'
    },
    {
      id: 'indicator-2',
      name: '单位参与度',
      value: 92.3,
      unit: '%',
      trend: 'up',
      change: 3.1,
      description: '单位参与度持续提升，覆盖面不断扩大'
    },
    {
      id: 'indicator-3',
      name: '群众满意度',
      value: 88.9,
      unit: '%',
      trend: 'stable',
      change: 0.5,
      description: '群众满意度保持稳定，服务质量持续优化'
    },
    {
      id: 'indicator-4',
      name: '创新成果数',
      value: 156,
      unit: '项',
      trend: 'up',
      change: 12,
      description: '本月新增创新成果12项，创新活力显著提升'
    }
  ]
}

// 培育排行数据
const generateCultivationRankings = (): CultivationRanking[] => {
  const rankings: CultivationRanking[] = []
  const unitNames = [
    '市委组织部', '市政府办公室', '市纪委监委', '市委宣传部', '市发改委',
    '市财政局', '市人社局', '市住建局', '市交通局', '市教委'
  ]

  for (let i = 0; i < 10; i++) {
    rankings.push({
      id: `ranking-${i + 1}`,
      rank: i + 1,
      unitName: unitNames[i],
      score: 95 - i * 2,
      projectCount: 15 - i,
      completionRate: 98 - i * 1.5,
      innovationPoints: 25 - i * 2,
      change: i < 3 ? 'up' : i < 7 ? 'stable' : 'down'
    })
  }

  return rankings
}

// 监督管理数据
const generateSupervisionData = (): SupervisionData => {
  return {
    totalItems: 156,
    completedItems: 132,
    pendingItems: 18,
    overdueItems: 6,
    effectivenessRating: 4.2,
    problemControlAssessment: 3.8,
    recentSupervisions: [
      {
        id: 'supervision-1',
        projectName: '党建引领基层治理创新项目',
        unitName: '市委组织部',
        supervisionType: '定期检查',
        status: 'completed',
        findings: '项目进展良好，按计划推进',
        suggestions: '建议加强宣传推广力度',
        supervisionDate: '2024-01-15',
        supervisor: '张三'
      },
      {
        id: 'supervision-2',
        projectName: '数字化政务服务优化项目',
        unitName: '市政府办公室',
        supervisionType: '专项督查',
        status: 'pending',
        findings: '部分功能模块待完善',
        suggestions: '加快技术攻关，确保按期完成',
        supervisionDate: '2024-01-20',
        supervisor: '李四'
      }
    ]
  }
}

// 党建数据
const generatePartyBuildingData = (): PartyBuildingData => {
  return {
    totalPartyMembers: 1256,
    activePartyMembers: 1198,
    partyBranches: 45,
    partyActivities: 128,
    educationSessions: 89,
    memberSatisfaction: 94.5,
    recentActivities: [
      {
        id: 'activity-1',
        title: '学习贯彻党的二十大精神专题讲座',
        date: '2024-01-18',
        participants: 156,
        location: '市委党校',
        type: '理论学习'
      },
      {
        id: 'activity-2',
        title: '党员志愿服务活动',
        date: '2024-01-22',
        participants: 89,
        location: '社区服务中心',
        type: '实践活动'
      }
    ]
  }
}

// 行政效能数据
const generateAdminEfficiencyData = (): AdminEfficiencyData => {
  return {
    averageProcessingTime: 2.3,
    onTimeCompletionRate: 96.8,
    customerSatisfactionScore: 4.6,
    processOptimizationCount: 23,
    digitalTransformationProgress: 78.5,
    recentOptimizations: [
      {
        id: 'optimization-1',
        processName: '行政审批流程优化',
        beforeTime: 5.2,
        afterTime: 2.8,
        improvementRate: 46.2,
        implementationDate: '2024-01-10'
      },
      {
        id: 'optimization-2',
        processName: '公文处理流程简化',
        beforeTime: 3.5,
        afterTime: 1.9,
        improvementRate: 45.7,
        implementationDate: '2024-01-15'
      }
    ]
  }
}

// 创新实践数据
const generateInnovationTrackingData = (): InnovationTrackingData => {
  return {
    totalInnovations: 89,
    implementedInnovations: 67,
    pilotInnovations: 15,
    plannedInnovations: 7,
    successRate: 75.3,
    averageImplementationTime: 4.2,
    recentInnovations: [
      {
        id: 'innovation-1',
        title: '智能化办公系统',
        description: '基于AI技术的智能办公解决方案',
        status: 'implemented',
        implementationDate: '2024-01-12',
        expectedBenefit: '提升办公效率30%',
        actualBenefit: '提升办公效率35%'
      },
      {
        id: 'innovation-2',
        title: '移动端服务平台',
        description: '面向公众的移动政务服务平台',
        status: 'pilot',
        implementationDate: '2024-02-01',
        expectedBenefit: '提升服务便民度50%',
        actualBenefit: ''
      }
    ]
  }
}

// 组织数据
const generateOrganizationalData = (): OrganizationalData => {
  return {
    totalDepartments: 28,
    participatingDepartments: 26,
    participationRate: 92.9,
    averageProjectsPerDepartment: 3.2,
    topPerformingDepartments: [
      {
        id: 'dept-1',
        name: '市委组织部',
        projectCount: 8,
        completionRate: 95.5,
        innovationScore: 4.8
      },
      {
        id: 'dept-2',
        name: '市政府办公室',
        projectCount: 6,
        completionRate: 92.3,
        innovationScore: 4.6
      }
    ]
  }
}

// 参与数据
const generateParticipationData = (): ParticipationData => {
  return {
    totalParticipants: 2456,
    activeParticipants: 2198,
    participationGrowthRate: 15.6,
    averageEngagementScore: 4.3,
    participationByCategory: [
      { category: '党员干部', count: 1256, percentage: 51.1 },
      { category: '业务骨干', count: 789, percentage: 32.1 },
      { category: '技术专家', count: 411, percentage: 16.8 }
    ]
  }
}

// 区域概览数据
const generateDistrictOverview = (): DistrictOverview => {
  return {
    totalDistricts: 38,
    participatingDistricts: 35,
    coverageRate: 92.1,
    averageProjectsPerDistrict: 2.8,
    topPerformingDistricts: [
      {
        id: 'district-1',
        name: '渝中区',
        projectCount: 12,
        completionRate: 96.8,
        innovationIndex: 4.9
      },
      {
        id: 'district-2',
        name: '江北区',
        projectCount: 10,
        completionRate: 94.2,
        innovationIndex: 4.7
      }
    ]
  }
}

// 导出完整的仪表板数据
export const mockDashboardData: DashboardData = {
  stageStats: generateStageStats(),
  recentDynamics: generateProjectDynamics(),
  achievements: generateProjectAchievements(),
  indicators: generateIndicators(),
  cultivationRankings: generateCultivationRankings(),
  supervisionData: generateSupervisionData(),
  partyBuildingData: generatePartyBuildingData(),
  adminEfficiencyData: generateAdminEfficiencyData(),
  innovationTrackingData: generateInnovationTrackingData(),
  organizationalData: generateOrganizationalData(),
  participationData: generateParticipationData(),
  districtOverview: generateDistrictOverview()
}

// 导出单独的数据生成函数
export {
  generateMockProjects,
  generateStageStats,
  generateProjectDynamics,
  generateProjectAchievements,
  generateIndicators,
  generateCultivationRankings,
  generateSupervisionData,
  generatePartyBuildingData,
  generateAdminEfficiencyData,
  generateInnovationTrackingData,
  generateOrganizationalData,
  generateParticipationData,
  generateDistrictOverview
}