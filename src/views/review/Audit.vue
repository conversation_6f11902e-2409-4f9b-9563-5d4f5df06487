<template>
  <div class="review-audit-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>审核管理</h2>
        <p>智能入围审核管理，支持智能筛查、人工审核、评分排序等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="batchAudit" :disabled="selectedRowKeys.length === 0">
            <template #icon><check-outlined /></template>
            批量审核
          </a-button>
          <a-button @click="exportAuditReport">
            <template #icon><export-outlined /></template>
            导出报告
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 审核统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待审核"
              :value="3"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已通过"
              :value="getApplicationCountByStatus(7)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="预审通过"
              :value="getApplicationCountByStatus(4)"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <audit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="预审不通过"
              :value="getApplicationCountByStatus(5)"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申报单位" class="form-item-full">
                <a-input v-model:value="searchForm.applicantName" placeholder="请输入申报单位名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核类型" class="form-item-full">
                <a-select v-model:value="searchForm.reviewType" placeholder="请选择审核类型" allow-clear>
                  <a-select-option v-for="item in ReviewTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核结果" class="form-item-full">
                <a-select v-model:value="searchForm.reviewResult" placeholder="请选择审核结果" allow-clear>
                  <a-select-option v-for="item in ReviewResultOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核人" class="form-item-full">
                <a-input v-model:value="searchForm.reviewerName" placeholder="请输入审核人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="审核时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>审核记录</span>
            <span class="record-count">共 {{ applicationList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterStatus" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in ApplicationStatusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button size="small" @click="generateShortlist" :disabled="selectedRowKeys.length === 0">
              生成入围名单
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredApplicationList"
          :loading="applicationLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1600 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'applicantType'">
              <a-tag :color="getApplicantTypeColor(record.applicantType)">
                {{ getApplicantTypeName(record.applicantType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'preReviewScore'">
              <span v-if="record.preReviewScore">
                <a-badge 
                  :count="record.preReviewScore" 
                  :number-style="{ backgroundColor: getScoreColor(record.preReviewScore) }"
                />
              </span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'finalScore'">
              <span v-if="record.finalScore">
                <a-badge 
                  :count="record.finalScore" 
                  :number-style="{ backgroundColor: getScoreColor(record.finalScore) }"
                />
              </span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'isQualified'">
              <a-tag :color="record.isQualified ? 'success' : 'error'">
                {{ record.isQualified ? '符合' : '不符合' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 4 || record.status === 6" 
                  type="link" 
                  size="small" 
                  @click="showAuditModal(record)"
                >
                  审核
                </a-button>
                <a-button 
                  v-if="record.status >= 6" 
                  type="link" 
                  size="small" 
                  @click="showScoreModal(record)"
                >
                  评分
                </a-button>
                <a-button 
                  v-if="record.status >= 4" 
                  type="link" 
                  size="small" 
                  @click="viewReviewHistory(record)"
                >
                  审核历史
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 申报详情弹窗 -->
    <a-modal 
      title="申报详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentApplication">
        <a-descriptions-item label="申报单位">{{ currentApplication.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="申报类型">
          <a-tag :color="getApplicantTypeColor(currentApplication.applicantType)">
            {{ getApplicantTypeName(currentApplication.applicantType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">{{ currentApplication.projectName }}</a-descriptions-item>
        <a-descriptions-item label="联系人">{{ currentApplication.contactPerson }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ currentApplication.contactPhone }}</a-descriptions-item>
        <a-descriptions-item label="申报状态">
          <a-tag :color="getStatusColor(currentApplication.status)">
            {{ getStatusText(currentApplication.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="提交时间">{{ currentApplication.submitTime }}</a-descriptions-item>
        <a-descriptions-item label="预审得分">
          {{ currentApplication.preReviewScore || '未评分' }}
        </a-descriptions-item>
        <a-descriptions-item label="最终得分">
          {{ currentApplication.finalScore || '未评分' }}
        </a-descriptions-item>
        <a-descriptions-item label="预审结果">
          {{ currentApplication.preReviewResult || '暂无结果' }}
        </a-descriptions-item>
        <a-descriptions-item label="审核意见">
          {{ currentApplication.reviewComments || '暂无意见' }}
        </a-descriptions-item>
        <a-descriptions-item label="项目描述">
          {{ currentApplication.description || '无' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 审核弹窗 -->
    <a-modal 
      title="人工审核" 
      :visible="auditVisible" 
      @cancel="auditVisible = false" 
      @ok="submitAudit"
      :confirm-loading="submitting"
      width="600px"
    >
      <div class="audit-form" v-if="currentApplication">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="申报单位">{{ currentApplication.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="项目名称">{{ currentApplication.projectName }}</a-descriptions-item>
          <a-descriptions-item label="预审得分">{{ currentApplication.preReviewScore || '未评分' }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>审核意见</a-divider>
        
        <a-form :model="auditForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="审核结果" required>
            <a-radio-group v-model:value="auditForm.reviewResult">
              <a-radio :value="1">通过</a-radio>
              <a-radio :value="2">不通过</a-radio>
              <a-radio :value="3">待补充材料</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="评分" v-if="auditForm.reviewResult === 1">
            <a-input-number 
              v-model:value="auditForm.score" 
              :min="0" 
              :max="100" 
              placeholder="请输入评分(0-100)"
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="审核意见" required>
            <a-textarea 
              v-model:value="auditForm.comments" 
              :rows="4" 
              placeholder="请输入审核意见"
            />
          </a-form-item>
          <a-form-item label="修改建议" v-if="auditForm.reviewResult !== 1">
            <a-textarea 
              v-model:value="auditForm.suggestions" 
              :rows="3" 
              placeholder="请输入修改建议"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 评分弹窗 -->
    <a-modal 
      title="项目评分" 
      :visible="scoreVisible" 
      @cancel="scoreVisible = false" 
      @ok="submitScore"
      :confirm-loading="submitting"
      width="700px"
    >
      <div class="score-form" v-if="currentApplication">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="申报单位">{{ currentApplication.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="项目名称">{{ currentApplication.projectName }}</a-descriptions-item>
          <a-descriptions-item label="当前得分">{{ currentApplication.finalScore || '未评分' }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>评分标准</a-divider>
        
        <div class="scoring-criteria">
          <div v-for="(criteria, index) in scoringCriteria" :key="index" class="criteria-item">
            <div class="criteria-header">
              <span class="criteria-name">{{ criteria.name }}</span>
              <span class="criteria-weight">权重: {{ criteria.weight }}%</span>
              <span class="criteria-max">满分: {{ criteria.maxScore }}</span>
            </div>
            <div class="criteria-score">
              <a-input-number 
                v-model:value="criteria.score" 
                :min="0" 
                :max="criteria.maxScore" 
                :placeholder="`请输入得分(0-${criteria.maxScore})`"
                style="width: 150px"
              />
            </div>
            <div class="criteria-desc">{{ criteria.description }}</div>
          </div>
        </div>
        
        <a-divider>总分计算</a-divider>
        
        <div class="total-score">
          <a-statistic title="总分" :value="calculateTotalScore()" suffix="分" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useReviewStore } from '@/store/modules/review'
import type { ApplicationRecord, ReviewRecord, ReviewSearchParams } from '@/types/review'
import {
  ApplicantTypeTextMap,
  ApplicationStatusOptions,
  ApplicationStatusTextMap,
  ApplicationStatusColorMap,
  ReviewTypeOptions,
  ReviewResultOptions
} from '@/types/review'
import {
  CheckOutlined,
  ExportOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  AuditOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'

// 使用store
const reviewStore = useReviewStore()

// 响应式数据
const searchForm = ref<ReviewSearchParams>({
  applicantName: '',
  reviewType: undefined,
  reviewResult: undefined,
  reviewerName: '',
  dateRange: undefined
})

const detailVisible = ref(false)
const auditVisible = ref(false)
const scoreVisible = ref(false)
const submitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterStatus = ref<number | undefined>(undefined)

// 审核表单数据
const auditForm = ref({
  reviewResult: 1,
  score: 0,
  comments: '',
  suggestions: ''
})

// 评分标准数据
const scoringCriteria = ref([
  {
    name: '技术创新性',
    weight: 30,
    maxScore: 30,
    score: 0,
    description: '项目技术方案的创新程度和技术难度'
  },
  {
    name: '市场前景',
    weight: 25,
    maxScore: 25,
    score: 0,
    description: '项目市场需求和商业化前景'
  },
  {
    name: '团队实力',
    weight: 25,
    maxScore: 25,
    score: 0,
    description: '项目团队的专业能力和经验'
  },
  {
    name: '实施方案',
    weight: 20,
    maxScore: 20,
    score: 0,
    description: '项目实施计划的可行性和完整性'
  }
])

// 计算属性
const {
  applicationList,
  applicationLoading,
  currentApplication
} = reviewStore

const filteredApplicationList = computed(() => {
  let filtered = [...applicationList]

  if (filterStatus.value !== undefined) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '申报单位',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 200
  },
  {
    title: '申报类型',
    key: 'applicantType',
    width: 120,
    align: 'center'
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    key: 'contactPerson',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center'
  },
  {
    title: '预审得分',
    key: 'preReviewScore',
    width: 100,
    align: 'center'
  },
  {
    title: '最终得分',
    key: 'finalScore',
    width: 100,
    align: 'center'
  },
  {
    title: '资格审查',
    key: 'isQualified',
    width: 100,
    align: 'center'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 280,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: ApplicationRecord) {
  return filteredApplicationList.value.findIndex(item => item.id === record.id) + 1
}

function getApplicantTypeName(type: number) {
  return ApplicantTypeTextMap[type as keyof typeof ApplicantTypeTextMap] || '未知'
}

function getApplicantTypeColor(type: number) {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange', 4: 'purple' }
  return colors[type as keyof typeof colors] || 'default'
}

function getStatusText(status: number) {
  return ApplicationStatusTextMap[status as keyof typeof ApplicationStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return ApplicationStatusColorMap[status as keyof typeof ApplicationStatusColorMap] || 'default'
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#ff4d4f'
}

function getApplicationCountByStatus(status: number) {
  return reviewStore.getApplicationCountByStatus(status)
}

async function refreshData() {
  await reviewStore.fetchApplicationList()
  message.success('数据刷新成功')
}

function showDetail(record: ApplicationRecord) {
  reviewStore.setCurrentApplication(record)
  detailVisible.value = true
}

function showAuditModal(record: ApplicationRecord) {
  reviewStore.setCurrentApplication(record)
  auditForm.value = {
    reviewResult: 1,
    score: record.finalScore || 0,
    comments: '',
    suggestions: ''
  }
  auditVisible.value = true
}

function showScoreModal(record: ApplicationRecord) {
  reviewStore.setCurrentApplication(record)
  // 重置评分标准
  scoringCriteria.value.forEach(criteria => {
    criteria.score = 0
  })
  scoreVisible.value = true
}

async function submitAudit() {
  try {
    submitting.value = true

    if (!auditForm.value.comments.trim()) {
      message.error('请输入审核意见')
      return
    }

    const reviewData = {
      applicationId: currentApplication.value?.id,
      reviewType: 2, // 人工审核
      reviewerName: '当前用户', // 实际应该从用户信息获取
      reviewTime: new Date().toISOString(),
      reviewResult: auditForm.value.reviewResult,
      score: auditForm.value.score,
      comments: auditForm.value.comments,
      suggestions: auditForm.value.suggestions,
      isPass: auditForm.value.reviewResult === 1
    }

    const success = await reviewStore.submitReview(reviewData)
    if (success) {
      message.success('审核提交成功')
      auditVisible.value = false
      await refreshData()
    } else {
      message.error('审核提交失败')
    }
  } catch (error) {
    message.error('审核提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

function calculateTotalScore() {
  return scoringCriteria.value.reduce((total, criteria) => {
    return total + (criteria.score || 0)
  }, 0)
}

async function submitScore() {
  try {
    submitting.value = true

    const totalScore = calculateTotalScore()
    if (totalScore === 0) {
      message.error('请至少为一个评分项打分')
      return
    }

    // 更新申报记录的最终得分
    const updateData = {
      id: currentApplication.value?.id,
      finalScore: totalScore
    }

    const success = await reviewStore.updateApplication(updateData)
    if (success) {
      message.success('评分提交成功')
      scoreVisible.value = false
      await refreshData()
    } else {
      message.error('评分提交失败')
    }
  } catch (error) {
    message.error('评分提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

function viewReviewHistory(record: ApplicationRecord) {
  message.info(`查看 ${record.applicantName} 的审核历史`)
  // 这里可以跳转到审核历史页面或打开历史弹窗
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchAudit() {
  try {
    message.info('批量审核功能开发中...')
    // 实际实现中可以打开批量审核弹窗
  } catch (error) {
    message.error('批量审核失败')
  }
}

async function generateShortlist() {
  try {
    const criteria = {
      minScore: 70,
      maxCount: 30
    }
    const success = await reviewStore.generateShortlist(criteria)
    if (success) {
      message.success('入围名单生成成功')
      selectedRowKeys.value = []
    } else {
      message.error('入围名单生成失败')
    }
  } catch (error) {
    message.error('入围名单生成失败')
  }
}

function exportAuditReport() {
  message.info('审核报告导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await reviewStore.fetchReviewList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    applicantName: '',
    reviewType: undefined,
    reviewResult: undefined,
    reviewerName: '',
    dateRange: undefined
  }
  reviewStore.fetchApplicationList()
}

// 生命周期
onMounted(async () => {
  await reviewStore.fetchApplicationList()
})
</script>

<style lang="scss" scoped>
.review-audit-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .audit-form {
    .ant-descriptions {
      margin-bottom: 16px;
    }
  }

  .score-form {
    .ant-descriptions {
      margin-bottom: 16px;
    }

    .scoring-criteria {
      .criteria-item {
        margin-bottom: 16px;
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;

        .criteria-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .criteria-name {
            font-weight: 600;
            color: #333;
          }

          .criteria-weight {
            color: #1890ff;
            font-size: 12px;
          }

          .criteria-max {
            color: #666;
            font-size: 12px;
          }
        }

        .criteria-score {
          margin-bottom: 8px;
        }

        .criteria-desc {
          color: #666;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .total-score {
      text-align: center;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 6px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-audit-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .score-form {
      .scoring-criteria {
        .criteria-item {
          .criteria-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }
      }
    }
  }
}
</style>
