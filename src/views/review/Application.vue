<template>
  <div class="review-application-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>申报管理</h2>
        <p>智能入围审核申报管理，支持申报提交、预审结果查看、材料上传等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showApplyModal">
            <template #icon><plus-outlined /></template>
            新建申报
          </a-button>
          <a-button @click="showBatchImportModal">
            <template #icon><upload-outlined /></template>
            批量导入
          </a-button>
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            批量导出
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申报单位" class="form-item-full">
                <a-input v-model:value="searchForm.applicantName" placeholder="请输入申报单位名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="项目名称" class="form-item-full">
                <a-input v-model:value="searchForm.projectName" placeholder="请输入项目名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申报类型" class="form-item-full">
                <a-select v-model:value="searchForm.applicantType" placeholder="请选择申报类型" allow-clear>
                  <a-select-option v-for="item in ApplicantTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option v-for="item in ApplicationStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="提交时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>申报记录</span>
            <span class="record-count">共 {{ applicationList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button size="small" @click="batchPreReview" :disabled="selectedRowKeys.length === 0">
              批量预审
            </a-button>
            <a-button size="small" @click="batchDelete" :disabled="selectedRowKeys.length === 0" danger>
              批量删除
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="applicationList"
          :loading="applicationLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'applicantType'">
              <a-tag :color="getApplicantTypeColor(record.applicantType)">
                {{ getApplicantTypeName(record.applicantType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'projectType'">
              <a-tag color="blue">
                {{ getProjectTypeName(record.projectType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'preReviewScore'">
              <span v-if="record.preReviewScore">
                <a-badge 
                  :count="record.preReviewScore" 
                  :number-style="{ backgroundColor: getScoreColor(record.preReviewScore) }"
                />
              </span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'finalScore'">
              <span v-if="record.finalScore">
                <a-badge 
                  :count="record.finalScore" 
                  :number-style="{ backgroundColor: getScoreColor(record.finalScore) }"
                />
              </span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'isQualified'">
              <a-tag :color="record.isQualified ? 'success' : 'error'">
                {{ record.isQualified ? '符合' : '不符合' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 1" 
                  type="link" 
                  size="small" 
                  @click="editApplication(record)"
                >
                  编辑
                </a-button>
                <a-button 
                  v-if="record.status === 2" 
                  type="link" 
                  size="small" 
                  @click="executePreReview(record)"
                >
                  预审
                </a-button>
                <a-button 
                  v-if="record.status >= 3" 
                  type="link" 
                  size="small" 
                  @click="viewPreReviewResult(record)"
                >
                  预审结果
                </a-button>
                <a-button 
                  v-if="record.status >= 4" 
                  type="link" 
                  size="small" 
                  @click="viewReviewProcess(record)"
                >
                  流程跟踪
                </a-button>
                <a-popconfirm
                  v-if="record.status === 1"
                  title="确定要删除这条申报记录吗？"
                  @confirm="deleteApplication(record)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 申报表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-form-item label="申报单位" name="applicantName">
          <a-input v-model:value="form.applicantName" placeholder="请输入申报单位名称" />
        </a-form-item>
        <a-form-item label="申报类型" name="applicantType">
          <a-select v-model:value="form.applicantType" placeholder="请选择申报类型">
            <a-select-option v-for="item in ApplicantTypeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="项目名称" name="projectName">
          <a-input v-model:value="form.projectName" placeholder="请输入项目名称" />
        </a-form-item>
        <a-form-item label="项目类型" name="projectType">
          <a-select v-model:value="form.projectType" placeholder="请选择项目类型">
            <a-select-option v-for="item in ProjectTypeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="联系人" name="contactPerson">
          <a-input v-model:value="form.contactPerson" placeholder="请输入联系人姓名" />
        </a-form-item>
        <a-form-item label="联系电话" name="contactPhone">
          <a-input v-model:value="form.contactPhone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item label="联系邮箱" name="contactEmail">
          <a-input v-model:value="form.contactEmail" placeholder="请输入联系邮箱" />
        </a-form-item>
        <a-form-item label="项目描述" name="description">
          <a-textarea 
            v-model:value="form.description" 
            :rows="4" 
            placeholder="请输入项目详细描述"
          />
        </a-form-item>
        <a-form-item label="申报材料" name="attachments">
          <div class="upload-section">
            <a-upload-dragger
              :file-list="fileList"
              :before-upload="beforeUpload"
              @remove="handleRemove"
              multiple
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持多个文件上传，支持PDF、Word、Excel等格式
              </p>
            </a-upload-dragger>
          </div>
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button v-if="formMode === 'create'" @click="saveDraft">保存草稿</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 批量导入弹窗 -->
    <a-modal 
      title="批量导入申报记录" 
      :visible="batchImportVisible" 
      @cancel="batchImportVisible = false" 
      :footer="null" 
      width="700px"
    >
      <div class="batch-import-content">
        <a-alert 
          message="导入说明" 
          description="请先下载模板文件，按照模板格式填写数据后上传。" 
          type="info" 
          show-icon 
        />
        <a-button @click="downloadTemplate" class="download-template-button">
          <template #icon><download-outlined /></template>
          下载模板
        </a-button>
        <a-upload-dragger 
          :before-upload="handleBatchImport" 
          :show-upload-list="false" 
          accept=".xlsx,.xls"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">支持单个文件上传，仅支持Excel格式</p>
        </a-upload-dragger>
      </div>
    </a-modal>

    <!-- 申报详情弹窗 -->
    <a-modal 
      title="申报详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentApplication">
        <a-descriptions-item label="申报单位">{{ currentApplication.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="申报类型">
          <a-tag :color="getApplicantTypeColor(currentApplication.applicantType)">
            {{ getApplicantTypeName(currentApplication.applicantType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">{{ currentApplication.projectName }}</a-descriptions-item>
        <a-descriptions-item label="项目类型">
          <a-tag color="blue">{{ getProjectTypeName(currentApplication.projectType) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="联系人">{{ currentApplication.contactPerson }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ currentApplication.contactPhone }}</a-descriptions-item>
        <a-descriptions-item label="联系邮箱">{{ currentApplication.contactEmail }}</a-descriptions-item>
        <a-descriptions-item label="申报状态">
          <a-tag :color="getStatusColor(currentApplication.status)">
            {{ getStatusText(currentApplication.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="提交时间">{{ currentApplication.submitTime }}</a-descriptions-item>
        <a-descriptions-item label="预审得分">
          {{ currentApplication.preReviewScore || '未评分' }}
        </a-descriptions-item>
        <a-descriptions-item label="最终得分">
          {{ currentApplication.finalScore || '未评分' }}
        </a-descriptions-item>
        <a-descriptions-item label="资格审查">
          <a-tag :color="currentApplication.isQualified ? 'success' : 'error'">
            {{ currentApplication.isQualified ? '符合条件' : '不符合条件' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="预审结果">
          {{ currentApplication.preReviewResult || '暂无结果' }}
        </a-descriptions-item>
        <a-descriptions-item label="审核意见">
          {{ currentApplication.reviewComments || '暂无意见' }}
        </a-descriptions-item>
        <a-descriptions-item label="项目描述">
          {{ currentApplication.description || '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="申报材料">
          <div v-if="currentApplication.attachments && currentApplication.attachments.length > 0">
            <a-tag v-for="file in currentApplication.attachments" :key="file" color="blue">
              {{ file }}
            </a-tag>
          </div>
          <span v-else>暂无附件</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useReviewStore } from '@/store/modules/review'
import type { ApplicationRecord, ApplicationSearchParams } from '@/types/review'
import {
  ApplicantTypeOptions,
  ProjectTypeOptions,
  ApplicationStatusOptions,
  ApplicantTypeTextMap,
  ProjectTypeTextMap,
  ApplicationStatusTextMap,
  ApplicationStatusColorMap
} from '@/types/review'
import {
  PlusOutlined,
  UploadOutlined,
  ExportOutlined,
  ReloadOutlined,
  DownloadOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'

// 使用store
const reviewStore = useReviewStore()

// 响应式数据
const searchForm = ref<ApplicationSearchParams>({
  applicantName: '',
  projectName: '',
  applicantType: undefined,
  status: undefined,
  dateRange: undefined
})

const formModalVisible = ref(false)
const batchImportVisible = ref(false)
const detailVisible = ref(false)
const submitting = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const selectedRowKeys = ref<number[]>([])
const fileList = ref<any[]>([])

// 表单数据
const form = ref<Partial<ApplicationRecord>>({
  applicantName: '',
  applicantType: 1,
  projectName: '',
  projectType: 1,
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  description: '',
  attachments: []
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  applicantName: [
    { required: true, message: '请输入申报单位名称', trigger: 'blur' },
    { min: 2, max: 100, message: '单位名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  applicantType: [
    { required: true, message: '请选择申报类型', trigger: 'change' }
  ],
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 100, message: '项目名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  projectType: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  contactEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入项目描述', trigger: 'blur' },
    { min: 10, max: 1000, message: '项目描述长度在 10 到 1000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const {
  applicationList,
  applicationLoading,
  currentApplication
} = reviewStore

const modalTitle = computed(() => {
  return formMode.value === 'create' ? '新建申报' : '编辑申报'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '提交申报' : '更新申报'
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '申报单位',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 200
  },
  {
    title: '申报类型',
    key: 'applicantType',
    width: 120,
    align: 'center'
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '项目类型',
    key: 'projectType',
    width: 120,
    align: 'center'
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    key: 'contactPerson',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center'
  },
  {
    title: '预审得分',
    key: 'preReviewScore',
    width: 100,
    align: 'center'
  },
  {
    title: '最终得分',
    key: 'finalScore',
    width: 100,
    align: 'center'
  },
  {
    title: '资格审查',
    key: 'isQualified',
    width: 100,
    align: 'center'
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: ApplicationRecord) {
  return applicationList.findIndex(item => item.id === record.id) + 1
}

function getApplicantTypeName(type: number) {
  return ApplicantTypeTextMap[type as keyof typeof ApplicantTypeTextMap] || '未知'
}

function getApplicantTypeColor(type: number) {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange', 4: 'purple' }
  return colors[type as keyof typeof colors] || 'default'
}

function getProjectTypeName(type: number) {
  return ProjectTypeTextMap[type as keyof typeof ProjectTypeTextMap] || '未知'
}

function getStatusText(status: number) {
  return ApplicationStatusTextMap[status as keyof typeof ApplicationStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return ApplicationStatusColorMap[status as keyof typeof ApplicationStatusColorMap] || 'default'
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#ff4d4f'
}

async function refreshData() {
  await reviewStore.fetchApplicationList()
  message.success('数据刷新成功')
}

function showApplyModal() {
  formMode.value = 'create'
  form.value = {
    applicantName: '',
    applicantType: 1,
    projectName: '',
    projectType: 1,
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    description: '',
    attachments: []
  }
  fileList.value = []
  formModalVisible.value = true
}

function editApplication(record: ApplicationRecord) {
  formMode.value = 'edit'
  form.value = { ...record }
  reviewStore.setCurrentApplication(record)
  // 模拟文件列表
  fileList.value = (record.attachments || []).map((name, index) => ({
    uid: index,
    name,
    status: 'done'
  }))
  formModalVisible.value = true
}

function showDetail(record: ApplicationRecord) {
  reviewStore.setCurrentApplication(record)
  detailVisible.value = true
}

async function deleteApplication(record: ApplicationRecord) {
  try {
    const success = await reviewStore.deleteApplication(record.id!)
    if (success) {
      message.success('删除成功')
      await refreshData()
    } else {
      message.error('删除失败')
    }
  } catch (error) {
    message.error('删除失败，请重试')
  }
}

async function executePreReview(record: ApplicationRecord) {
  try {
    const success = await reviewStore.executePreReview(record.id!)
    if (success) {
      message.success('智能预审已启动，请稍后查看结果')
      await refreshData()
    } else {
      message.error('预审启动失败')
    }
  } catch (error) {
    message.error('预审失败，请重试')
  }
}

function viewPreReviewResult(record: ApplicationRecord) {
  message.info(`查看 ${record.applicantName} 的预审结果`)
  // 这里可以跳转到预审结果详情页面或打开详情弹窗
}

function viewReviewProcess(record: ApplicationRecord) {
  message.info(`查看 ${record.applicantName} 的审核流程`)
  // 这里可以跳转到流程跟踪页面或打开流程弹窗
}

async function onSubmit() {
  try {
    submitting.value = true

    // 验证表单
    await formRef.value.validate()

    // 处理附件
    form.value.attachments = fileList.value.map(file => file.name)

    let success = false
    if (formMode.value === 'create') {
      success = await reviewStore.submitApplication(form.value)
    } else {
      success = await reviewStore.updateApplication(form.value)
    }

    if (success) {
      message.success(`申报${formMode.value === 'create' ? '提交' : '更新'}成功`)
      formModalVisible.value = false
      await refreshData()
    } else {
      message.error('操作失败，请重试')
    }
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

async function saveDraft() {
  try {
    // 保存草稿逻辑
    form.value.status = 1 // 草稿状态
    const success = await reviewStore.submitApplication(form.value)
    if (success) {
      message.success('草稿保存成功')
      formModalVisible.value = false
      await refreshData()
    } else {
      message.error('保存失败')
    }
  } catch (error) {
    message.error('保存失败，请重试')
  }
}

// 文件上传处理
function beforeUpload(file: any) {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type)
  if (!isValidType) {
    message.error('只能上传PDF、Word、Excel格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }

  // 添加到文件列表
  fileList.value.push({
    uid: Date.now(),
    name: file.name,
    status: 'done',
    originFileObj: file
  })

  return false // 阻止自动上传
}

function handleRemove(file: any) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchPreReview() {
  try {
    for (const id of selectedRowKeys.value) {
      await reviewStore.executePreReview(id)
    }
    message.success('批量预审已启动')
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    message.error('批量预审失败')
  }
}

async function batchDelete() {
  try {
    for (const id of selectedRowKeys.value) {
      await reviewStore.deleteApplication(id)
    }
    message.success('批量删除成功')
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    message.error('批量删除失败')
  }
}

// 搜索和重置
async function handleSearch() {
  await reviewStore.fetchApplicationList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    applicantName: '',
    projectName: '',
    applicantType: undefined,
    status: undefined,
    dateRange: undefined
  }
  reviewStore.fetchApplicationList()
}

// 批量导入导出
function showBatchImportModal() {
  batchImportVisible.value = true
}

function downloadTemplate() {
  message.info('模板下载功能开发中...')
}

function handleBatchImport(file: any) {
  message.info('批量导入功能开发中...')
  return false
}

function exportData() {
  message.info('数据导出功能开发中...')
}

// 生命周期
onMounted(async () => {
  await reviewStore.fetchApplicationList()
})
</script>

<style lang="scss" scoped>
.review-application-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .upload-section {
    .ant-upload-dragger {
      margin-bottom: 16px;
    }
  }

  .batch-import-content {
    .download-template-button {
      margin: 16px 0;
      display: block;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-application-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
