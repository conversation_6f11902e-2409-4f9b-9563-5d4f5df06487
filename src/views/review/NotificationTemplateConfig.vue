<template>
  <div class="notification-template-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2>通知模板配置</h2>
        <p>管理审核意见和申诉处理的通知模板，设置通知人员和推送规则</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="previewTemplate">
            <template #icon><eye-outlined /></template>
            预览模板
          </a-button>
          <a-button @click="importTemplate">
            <template #icon><import-outlined /></template>
            导入模板
          </a-button>
          <a-button type="primary" @click="saveAllTemplates" :loading="saving">
            <template #icon><save-outlined /></template>
            保存配置
          </a-button>
        </a-space>
      </div>
    </div>

    <a-row :gutter="24">
      <!-- 左侧模板列表 -->
      <a-col :xs="24" :lg="8">
        <a-card title="通知模板列表" class="template-list-card">
          <template #extra>
            <a-button type="primary" size="small" @click="addNewTemplate">
              <template #icon><plus-outlined /></template>
              新增模板
            </a-button>
          </template>

          <div class="template-list">
            <div 
              v-for="template in templateList" 
              :key="template.id"
              class="template-item"
              :class="{ active: selectedTemplate?.id === template.id }"
              @click="selectTemplate(template)"
            >
              <div class="template-header">
                <div class="template-name">{{ template.name }}</div>
                <div class="template-actions">
                  <a-switch 
                    v-model:checked="template.enabled" 
                    size="small"
                    @change="onTemplateEnabledChange(template)"
                  />
                </div>
              </div>
              <div class="template-info">
                <div class="template-type">
                  <a-tag :color="getTemplateTypeColor(template.type)" size="small">
                    {{ getTemplateTypeText(template.type) }}
                  </a-tag>
                </div>
                <div class="template-trigger">
                  触发条件：{{ getTriggerText(template.trigger) }}
                </div>
                <div class="template-recipients">
                  通知对象：{{ getRecipientsText(template.recipients) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <a-empty v-if="templateList.length === 0" description="暂无通知模板" />
        </a-card>
      </a-col>

      <!-- 右侧模板编辑 -->
      <a-col :xs="24" :lg="16">
        <div v-if="selectedTemplate" class="template-editor">
          <a-card :title="'编辑模板 - ' + selectedTemplate.name" class="template-edit-card">
            <template #extra>
              <a-space>
                <a-button size="small" @click="duplicateTemplate">
                  <template #icon><copy-outlined /></template>
                  复制模板
                </a-button>
                <a-popconfirm
                  title="确定要删除这个模板吗？"
                  @confirm="deleteTemplate"
                >
                  <a-button size="small" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>

            <a-form ref="templateFormRef" :model="selectedTemplate" layout="vertical">
              <!-- 基本信息 -->
              <div class="form-section">
                <h4>基本信息</h4>
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="模板名称" name="name" :rules="[{ required: true, message: '请输入模板名称' }]">
                      <a-input v-model:value="selectedTemplate.name" placeholder="请输入模板名称" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="模板类型" name="type" :rules="[{ required: true, message: '请选择模板类型' }]">
                      <a-select v-model:value="selectedTemplate.type" placeholder="请选择模板类型">
                        <a-select-option value="review">审核通知</a-select-option>
                        <a-select-option value="appeal">申诉通知</a-select-option>
                        <a-select-option value="system">系统通知</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-form-item label="模板描述" name="description">
                  <a-textarea 
                    v-model:value="selectedTemplate.description" 
                    placeholder="请输入模板描述"
                    :rows="2" 
                  />
                </a-form-item>
              </div>

              <!-- 触发条件 -->
              <div class="form-section">
                <h4>触发条件</h4>
                <a-form-item label="触发事件" name="trigger" :rules="[{ required: true, message: '请选择触发事件' }]">
                  <a-select v-model:value="selectedTemplate.trigger" placeholder="请选择触发事件">
                    <a-select-option value="review_start">开始审核</a-select-option>
                    <a-select-option value="review_complete">审核完成</a-select-option>
                    <a-select-option value="review_reject">审核退回</a-select-option>
                    <a-select-option value="appeal_submit">申诉提交</a-select-option>
                    <a-select-option value="appeal_process">申诉处理</a-select-option>
                    <a-select-option value="appeal_complete">申诉完成</a-select-option>
                    <a-select-option value="score_change">评分变更</a-select-option>
                    <a-select-option value="deadline_remind">截止提醒</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="触发条件" name="conditions">
                  <div class="condition-builder">
                    <div v-for="(condition, index) in selectedTemplate.conditions" :key="index" class="condition-item">
                      <a-select v-model:value="condition.field" placeholder="选择字段" style="width: 150px;">
                        <a-select-option value="status">状态</a-select-option>
                        <a-select-option value="score">分数</a-select-option>
                        <a-select-option value="type">类型</a-select-option>
                      </a-select>
                      <a-select v-model:value="condition.operator" placeholder="选择操作" style="width: 100px;">
                        <a-select-option value="eq">等于</a-select-option>
                        <a-select-option value="ne">不等于</a-select-option>
                        <a-select-option value="gt">大于</a-select-option>
                        <a-select-option value="lt">小于</a-select-option>
                      </a-select>
                      <a-input v-model:value="condition.value" placeholder="输入值" style="width: 120px;" />
                      <a-button type="text" danger @click="removeCondition(index)">
                        <delete-outlined />
                      </a-button>
                    </div>
                    <a-button type="dashed" @click="addCondition">
                      <plus-outlined />
                      添加条件
                    </a-button>
                  </div>
                </a-form-item>
              </div>

              <!-- 通知内容 -->
              <div class="form-section">
                <h4>通知内容</h4>
                <a-form-item label="通知标题" name="title" :rules="[{ required: true, message: '请输入通知标题' }]">
                  <a-input v-model:value="selectedTemplate.title" placeholder="请输入通知标题" />
                  <div class="form-help">
                    支持变量：{objectName} {applicantName} {score} {status} {date} 等
                  </div>
                </a-form-item>
                <a-form-item label="通知内容" name="content" :rules="[{ required: true, message: '请输入通知内容' }]">
                  <a-textarea 
                    v-model:value="selectedTemplate.content"
                    placeholder="请输入通知内容，支持HTML和变量替换"
                    :rows="8"
                  />
                  <div class="form-help">
                    支持HTML格式和模板变量，如：{objectName}、{applicantName}、{reviewComments} 等
                  </div>
                </a-form-item>
                
                <!-- 变量提示 -->
                <div class="variable-hints">
                  <div class="variable-section">
                    <h5>常用变量</h5>
                    <div class="variable-tags">
                      <a-tag 
                        v-for="variable in commonVariables" 
                        :key="variable.key"
                        @click="insertVariable(variable.key)"
                        style="cursor: pointer; margin: 4px;"
                      >
                        {{ variable.key }} - {{ variable.desc }}
                      </a-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 通知设置 -->
              <div class="form-section">
                <h4>通知设置</h4>
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="通知方式" name="channels">
                      <a-checkbox-group v-model:value="selectedTemplate.channels">
                        <a-checkbox value="email">邮件通知</a-checkbox>
                        <a-checkbox value="sms">短信通知</a-checkbox>
                        <a-checkbox value="system">站内信</a-checkbox>
                        <a-checkbox value="wechat">微信通知</a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="优先级" name="priority">
                      <a-select v-model:value="selectedTemplate.priority" placeholder="请选择优先级">
                        <a-select-option value="low">低</a-select-option>
                        <a-select-option value="normal">普通</a-select-option>
                        <a-select-option value="high">高</a-select-option>
                        <a-select-option value="urgent">紧急</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-form-item label="通知对象" name="recipients" :rules="[{ required: true, message: '请选择通知对象' }]">
                  <a-checkbox-group v-model:value="selectedTemplate.recipients">
                    <a-checkbox value="applicant">申请人</a-checkbox>
                    <a-checkbox value="reviewer">审核人员</a-checkbox>
                    <a-checkbox value="admin">系统管理员</a-checkbox>
                    <a-checkbox value="leader">领导</a-checkbox>
                    <a-checkbox value="custom">自定义人员</a-checkbox>
                  </a-checkbox-group>
                </a-form-item>

                <!-- 自定义人员 -->
                <a-form-item 
                  v-if="selectedTemplate.recipients?.includes('custom')"
                  label="自定义通知人员" 
                  name="customRecipients"
                >
                  <a-select
                    v-model:value="selectedTemplate.customRecipients"
                    mode="multiple"
                    placeholder="选择自定义通知人员"
                  >
                    <a-select-option value="user1">张三 (<EMAIL>)</a-select-option>
                    <a-select-option value="user2">李四 (<EMAIL>)</a-select-option>
                    <a-select-option value="user3">王五 (<EMAIL>)</a-select-option>
                  </a-select>
                </a-form-item>

                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="发送延迟" name="delay">
                      <a-input-number
                        v-model:value="selectedTemplate.delay"
                        :min="0"
                        :max="1440"
                        addon-after="分钟"
                        style="width: 100%"
                      />
                      <div class="form-help">0表示立即发送</div>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="重试次数" name="retryCount">
                      <a-input-number
                        v-model:value="selectedTemplate.retryCount"
                        :min="0"
                        :max="5"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </a-form>
          </a-card>
        </div>

        <!-- 未选择模板时的提示 -->
        <a-card v-else class="empty-editor">
          <a-empty description="请选择一个模板进行编辑">
            <a-button type="primary" @click="addNewTemplate">创建新模板</a-button>
          </a-empty>
        </a-card>
      </a-col>
    </a-row>

    <!-- 预览模板弹窗 -->
    <a-modal
      v-model:open="previewModalVisible"
      title="模板预览"
      width="700px"
      :footer="null"
    >
      <div v-if="selectedTemplate" class="template-preview">
        <div class="preview-header">
          <h4>{{ selectedTemplate.title }}</h4>
          <div class="preview-meta">
            <a-tag :color="getTemplateTypeColor(selectedTemplate.type)">
              {{ getTemplateTypeText(selectedTemplate.type) }}
            </a-tag>
            <a-tag :color="getPriorityColor(selectedTemplate.priority)">
              {{ getPriorityText(selectedTemplate.priority) }}
            </a-tag>
          </div>
        </div>
        <div class="preview-content" v-html="getPreviewContent()"></div>
        <div class="preview-footer">
          <div class="preview-info">
            <div>通知方式：{{ getChannelsText(selectedTemplate.channels) }}</div>
            <div>通知对象：{{ getRecipientsText(selectedTemplate.recipients) }}</div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 新增/导入模板弹窗 -->
    <a-modal
      v-model:open="newTemplateModalVisible"
      :title="isImporting ? '导入模板' : '新增模板'"
      width="500px"
      @ok="createNewTemplate"
      @cancel="cancelNewTemplate"
    >
      <a-form ref="newTemplateFormRef" :model="newTemplateForm" layout="vertical">
        <a-form-item 
          v-if="!isImporting"
          label="模板名称" 
          name="name" 
          :rules="[{ required: true, message: '请输入模板名称' }]"
        >
          <a-input v-model:value="newTemplateForm.name" placeholder="请输入模板名称" />
        </a-form-item>
        
        <a-form-item 
          v-if="!isImporting"
          label="模板类型" 
          name="type" 
          :rules="[{ required: true, message: '请选择模板类型' }]"
        >
          <a-select v-model:value="newTemplateForm.type" placeholder="请选择模板类型">
            <a-select-option value="review">审核通知</a-select-option>
            <a-select-option value="appeal">申诉通知</a-select-option>
            <a-select-option value="system">系统通知</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 导入模板选择 -->
        <a-form-item v-if="isImporting" label="选择预置模板" name="presetTemplate">
          <a-select v-model:value="newTemplateForm.presetTemplate" placeholder="请选择要导入的模板">
            <a-select-option value="review_complete_success">审核通过通知</a-select-option>
            <a-select-option value="review_complete_reject">审核退回通知</a-select-option>
            <a-select-option value="appeal_submit_confirm">申诉提交确认</a-select-option>
            <a-select-option value="appeal_process_result">申诉处理结果</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import {
  EyeOutlined,
  ImportOutlined,
  SaveOutlined,
  PlusOutlined,
  CopyOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface NotificationTemplate {
  id: string
  name: string
  type: 'review' | 'appeal' | 'system'
  description: string
  trigger: string
  conditions: Array<{
    field: string
    operator: string
    value: string
  }>
  title: string
  content: string
  channels: string[]
  recipients: string[]
  customRecipients?: string[]
  priority: 'low' | 'normal' | 'high' | 'urgent'
  delay: number
  retryCount: number
  enabled: boolean
}

interface NewTemplateForm {
  name?: string
  type?: string
  presetTemplate?: string
}

// 响应式数据
const saving = ref(false)
const previewModalVisible = ref(false)
const newTemplateModalVisible = ref(false)
const isImporting = ref(false)
const selectedTemplate = ref<NotificationTemplate | null>(null)

const newTemplateForm = ref<NewTemplateForm>({})

// 模板列表数据
const templateList = ref<NotificationTemplate[]>([
  {
    id: '1',
    name: '审核通过通知',
    type: 'review',
    description: '审核通过后通知申请人',
    trigger: 'review_complete',
    conditions: [
      { field: 'status', operator: 'eq', value: 'approved' }
    ],
    title: '您的申报项目 "{objectName}" 审核通过',
    content: `尊敬的 {applicantName}：
    
您好！您申报的项目 "{objectName}" 已通过审核。

审核结果：
- 最终得分：{score} 分
- 审核意见：{reviewComments}
- 审核时间：{reviewTime}

如有疑问，请联系审核部门。

祝您工作顺利！`,
    channels: ['email', 'system'],
    recipients: ['applicant'],
    priority: 'normal',
    delay: 0,
    retryCount: 3,
    enabled: true
  },
  {
    id: '2',
    name: '审核退回通知',
    type: 'review',
    description: '审核不通过时通知申请人',
    trigger: 'review_reject',
    conditions: [
      { field: 'status', operator: 'eq', value: 'rejected' }
    ],
    title: '您的申报项目 "{objectName}" 需要补充材料',
    content: `尊敬的 {applicantName}：
    
您好！您申报的项目 "{objectName}" 审核后需要补充以下材料：

审核意见：{reviewComments}

请在 {deadline} 前补充相关材料并重新提交。

如有疑问，请联系审核部门。`,
    channels: ['email', 'sms', 'system'],
    recipients: ['applicant'],
    priority: 'high',
    delay: 0,
    retryCount: 5,
    enabled: true
  },
  {
    id: '3',
    name: '申诉处理结果通知',
    type: 'appeal',
    description: '申诉处理完成后通知申请人',
    trigger: 'appeal_complete',
    conditions: [],
    title: '您的申诉 "{appealTitle}" 处理完成',
    content: `尊敬的 {applicantName}：
    
您提交的申诉已处理完成。

处理结果：{appealResult}
处理意见：{appealComments}
处理时间：{processTime}

如对处理结果有异议，可在规定时间内进行复议。`,
    channels: ['email', 'system'],
    recipients: ['applicant'],
    priority: 'normal',
    delay: 0,
    retryCount: 3,
    enabled: true
  }
])

// 常用变量
const commonVariables = [
  { key: '{objectName}', desc: '培育对象名称' },
  { key: '{applicantName}', desc: '申请人姓名' },
  { key: '{score}', desc: '评分' },
  { key: '{status}', desc: '状态' },
  { key: '{reviewComments}', desc: '审核意见' },
  { key: '{date}', desc: '当前日期' },
  { key: '{deadline}', desc: '截止时间' },
  { key: '{appealTitle}', desc: '申诉标题' },
  { key: '{appealResult}', desc: '申诉结果' }
]

// 方法定义
const selectTemplate = (template: NotificationTemplate) => {
  selectedTemplate.value = { ...template }
}

const addNewTemplate = () => {
  isImporting.value = false
  newTemplateForm.value = {}
  newTemplateModalVisible.value = true
}

const importTemplate = () => {
  isImporting.value = true
  newTemplateForm.value = {}
  newTemplateModalVisible.value = true
}

const createNewTemplate = () => {
  if (isImporting.value) {
    if (!newTemplateForm.value.presetTemplate) {
      message.warning('请选择要导入的模板')
      return
    }
    // 这里可以加载预置模板
    message.success('模板导入成功')
  } else {
    if (!newTemplateForm.value.name || !newTemplateForm.value.type) {
      message.warning('请填写完整的模板信息')
      return
    }
    
    const newTemplate: NotificationTemplate = {
      id: Date.now().toString(),
      name: newTemplateForm.value.name,
      type: newTemplateForm.value.type as any,
      description: '',
      trigger: 'review_complete',
      conditions: [],
      title: '',
      content: '',
      channels: ['system'],
      recipients: ['applicant'],
      priority: 'normal',
      delay: 0,
      retryCount: 3,
      enabled: true
    }
    
    templateList.value.push(newTemplate)
    selectedTemplate.value = newTemplate
    message.success('模板创建成功')
  }
  
  newTemplateModalVisible.value = false
}

const cancelNewTemplate = () => {
  newTemplateModalVisible.value = false
  newTemplateForm.value = {}
}

const duplicateTemplate = () => {
  if (!selectedTemplate.value) return
  
  const newTemplate = {
    ...selectedTemplate.value,
    id: Date.now().toString(),
    name: selectedTemplate.value.name + ' - 副本'
  }
  
  templateList.value.push(newTemplate)
  selectedTemplate.value = newTemplate
  message.success('模板复制成功')
}

const deleteTemplate = () => {
  if (!selectedTemplate.value) return
  
  const index = templateList.value.findIndex(t => t.id === selectedTemplate.value!.id)
  if (index !== -1) {
    templateList.value.splice(index, 1)
    selectedTemplate.value = null
    message.success('模板删除成功')
  }
}

const addCondition = () => {
  if (!selectedTemplate.value) return
  
  selectedTemplate.value.conditions.push({
    field: 'status',
    operator: 'eq',
    value: ''
  })
}

const removeCondition = (index: number) => {
  if (!selectedTemplate.value) return
  selectedTemplate.value.conditions.splice(index, 1)
}

const insertVariable = (variable: string) => {
  // 这里可以实现插入变量到光标位置的逻辑
  message.success(`已插入变量 ${variable}`)
}

const previewTemplate = () => {
  if (!selectedTemplate.value) {
    message.warning('请先选择一个模板')
    return
  }
  previewModalVisible.value = true
}

const getPreviewContent = () => {
  if (!selectedTemplate.value) return ''
  
  // 模拟变量替换
  let content = selectedTemplate.value.content
  content = content.replace(/{objectName}/g, '党员服务中心数字化改革项目')
  content = content.replace(/{applicantName}/g, '市委组织部')
  content = content.replace(/{score}/g, '88.5')
  content = content.replace(/{reviewComments}/g, '项目创新性强，实施效果显著')
  content = content.replace(/{date}/g, new Date().toLocaleDateString())
  
  return content.replace(/\n/g, '<br>')
}

const onTemplateEnabledChange = (template: NotificationTemplate) => {
  message.success(`模板 ${template.name} 已${template.enabled ? '启用' : '禁用'}`)
}

const saveAllTemplates = () => {
  saving.value = true
  setTimeout(() => {
    saving.value = false
    message.success('所有模板配置保存成功')
  }, 1000)
}

// 工具方法
const getTemplateTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    review: 'blue',
    appeal: 'orange',
    system: 'green'
  }
  return colorMap[type] || 'default'
}

const getTemplateTypeText = (type: string) => {
  const textMap: Record<string, string> = {
    review: '审核通知',
    appeal: '申诉通知',
    system: '系统通知'
  }
  return textMap[type] || '未知'
}

const getTriggerText = (trigger: string) => {
  const textMap: Record<string, string> = {
    review_start: '开始审核',
    review_complete: '审核完成',
    review_reject: '审核退回',
    appeal_submit: '申诉提交',
    appeal_process: '申诉处理',
    appeal_complete: '申诉完成',
    score_change: '评分变更',
    deadline_remind: '截止提醒'
  }
  return textMap[trigger] || '未知'
}

const getRecipientsText = (recipients: string[]) => {
  const textMap: Record<string, string> = {
    applicant: '申请人',
    reviewer: '审核人员',
    admin: '管理员',
    leader: '领导',
    custom: '自定义'
  }
  return recipients.map(r => textMap[r] || r).join('、')
}

const getPriorityColor = (priority: string) => {
  const colorMap: Record<string, string> = {
    low: 'green',
    normal: 'blue',
    high: 'orange',
    urgent: 'red'
  }
  return colorMap[priority] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    low: '低优先级',
    normal: '普通',
    high: '高优先级',
    urgent: '紧急'
  }
  return textMap[priority] || '未知'
}

const getChannelsText = (channels: string[]) => {
  const textMap: Record<string, string> = {
    email: '邮件',
    sms: '短信',
    system: '站内信',
    wechat: '微信'
  }
  return channels.map(c => textMap[c] || c).join('、')
}

// 初始化
if (templateList.value.length > 0) {
  selectedTemplate.value = { ...templateList.value[0] }
}
</script>

<style scoped lang="scss">
.notification-template-config-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 20px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.template-list-card {
  height: calc(100vh - 200px);
  overflow-y: auto;

  .template-list {
    .template-item {
      padding: 16px;
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      &.active {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .template-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .template-name {
          font-weight: 600;
          color: #262626;
        }
      }

      .template-info {
        .template-type {
          margin-bottom: 8px;
        }

        .template-trigger,
        .template-recipients {
          color: #8c8c8c;
          font-size: 12px;
          margin-bottom: 4px;
        }
      }
    }
  }
}

.template-editor {
  .template-edit-card {
    min-height: calc(100vh - 200px);
  }
}

.empty-editor {
  height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  h4 {
    margin: 0 0 16px 0;
    color: #262626;
    font-weight: 600;
  }
}

.condition-builder {
  .condition-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }
}

.form-help {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

.variable-hints {
  margin-top: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;

  .variable-section {
    h5 {
      margin: 0 0 8px 0;
      color: #595959;
      font-size: 14px;
    }

    .variable-tags {
      .ant-tag {
        transition: all 0.3s;

        &:hover {
          background: #1890ff;
          color: white;
          border-color: #1890ff;
        }
      }
    }
  }
}

.template-preview {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
      margin: 0;
      color: #262626;
    }

    .preview-meta {
      display: flex;
      gap: 8px;
    }
  }

  .preview-content {
    margin-bottom: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;
    line-height: 1.6;
    color: #262626;
  }

  .preview-footer {
    .preview-info {
      color: #8c8c8c;
      font-size: 12px;

      div {
        margin-bottom: 4px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notification-template-config-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-left {
        width: 100%;
      }

      .header-actions {
        width: 100%;
        
        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .ant-col {
      margin-bottom: 24px;
    }

    .template-list-card,
    .template-edit-card {
      height: auto;
      min-height: 400px;
    }
  }
}
</style>