<template>
  <div class="review-results-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>结果管理</h2>
        <p>智能入围审核结果管理，支持入围名单生成、结果公示、反馈报告等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="generateShortlist">
            <template #icon><plus-outlined /></template>
            生成入围名单
          </a-button>
          <a-button @click="publishResults" :disabled="selectedRowKeys.length === 0">
            <template #icon><notification-outlined /></template>
            公示结果
          </a-button>
          <a-button @click="exportResults">
            <template #icon><export-outlined /></template>
            导出结果
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 结果统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="入围总数"
              :value="shortlistList.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已公示"
              :value="getShortlistCountByStatus(3)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已确认"
              :value="getShortlistCountByStatus(4)"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <crown-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="平均得分"
              :value="getAverageScore()"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申报单位" class="form-item-full">
                <a-input v-model:value="searchForm.applicantName" placeholder="请输入申报单位名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="项目名称" class="form-item-full">
                <a-input v-model:value="searchForm.projectName" placeholder="请输入项目名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option v-for="item in ShortlistStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="得分范围" class="form-item-full">
                <a-input-group compact>
                  <a-input-number 
                    v-model:value="searchForm.minScore" 
                    placeholder="最低分" 
                    :min="0" 
                    :max="100" 
                    style="width: 50%"
                  />
                  <a-input-number 
                    v-model:value="searchForm.maxScore" 
                    placeholder="最高分" 
                    :min="0" 
                    :max="100" 
                    style="width: 50%"
                  />
                </a-input-group>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="公示时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 入围名单表格 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>入围名单</span>
            <span class="record-count">共 {{ shortlistList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="sortBy" placeholder="排序方式" style="width: 120px">
              <a-select-option value="ranking">按排名</a-select-option>
              <a-select-option value="score">按得分</a-select-option>
              <a-select-option value="time">按时间</a-select-option>
            </a-select>
            <a-button size="small" @click="batchGenerateReports" :disabled="selectedRowKeys.length === 0">
              批量生成报告
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="sortedShortlistList"
          :loading="shortlistLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'ranking'">
              <a-badge 
                :count="record.ranking" 
                :number-style="{ backgroundColor: getRankingColor(record.ranking) }"
              />
            </template>
            <template v-else-if="column.key === 'finalScore'">
              <a-progress 
                :percent="record.finalScore" 
                :stroke-color="getScoreColor(record.finalScore)"
                :show-info="true"
                format="percent => `${record.finalScore}分`"
              />
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'publicTime'">
              <span v-if="record.publicTime">{{ formatTime(record.publicTime) }}</span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 2" 
                  type="link" 
                  size="small" 
                  @click="publishSingle(record)"
                >
                  公示
                </a-button>
                <a-button 
                  v-if="record.status === 3" 
                  type="link" 
                  size="small" 
                  @click="confirmResult(record)"
                >
                  确认
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="generateFeedbackReport(record)"
                >
                  生成报告
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewFeedbackReport(record)"
                >
                  查看反馈
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 详情弹窗 -->
    <a-modal 
      title="入围详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentShortlist">
        <a-descriptions-item label="排名">
          <a-badge 
            :count="currentShortlist.ranking" 
            :number-style="{ backgroundColor: getRankingColor(currentShortlist.ranking) }"
          />
        </a-descriptions-item>
        <a-descriptions-item label="申报单位">{{ currentShortlist.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="项目名称">{{ currentShortlist.projectName }}</a-descriptions-item>
        <a-descriptions-item label="最终得分">
          <a-progress 
            :percent="currentShortlist.finalScore" 
            :stroke-color="getScoreColor(currentShortlist.finalScore)"
            format="percent => `${currentShortlist.finalScore}分`"
          />
        </a-descriptions-item>
        <a-descriptions-item label="入围状态">
          <a-tag :color="getStatusColor(currentShortlist.status)">
            {{ getStatusText(currentShortlist.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="公示时间">
          {{ currentShortlist.publicTime ? formatTime(currentShortlist.publicTime) : '未公示' }}
        </a-descriptions-item>
        <a-descriptions-item label="确认时间">
          {{ currentShortlist.confirmTime ? formatTime(currentShortlist.confirmTime) : '未确认' }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatTime(currentShortlist.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ currentShortlist.comments || '无' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 生成入围名单弹窗 -->
    <a-modal 
      title="生成入围名单" 
      :visible="generateVisible" 
      @cancel="generateVisible = false" 
      @ok="confirmGenerate"
      :confirm-loading="generating"
      width="600px"
    >
      <div class="generate-form">
        <a-form :model="generateForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
          <a-form-item label="最低分数线">
            <a-input-number 
              v-model:value="generateForm.minScore" 
              :min="0" 
              :max="100" 
              placeholder="请输入最低分数线"
              style="width: 200px"
            />
            <span style="margin-left: 8px; color: #666;">分</span>
          </a-form-item>
          <a-form-item label="入围人数">
            <a-input-number 
              v-model:value="generateForm.maxCount" 
              :min="1" 
              :max="1000" 
              placeholder="请输入入围人数"
              style="width: 200px"
            />
            <span style="margin-left: 8px; color: #666;">人</span>
          </a-form-item>
          <a-form-item label="生成规则">
            <a-radio-group v-model:value="generateForm.rule">
              <a-radio :value="1">按得分排序</a-radio>
              <a-radio :value="2">按综合评价</a-radio>
              <a-radio :value="3">按项目类型</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="备注说明">
            <a-textarea 
              v-model:value="generateForm.comments" 
              :rows="3" 
              placeholder="请输入备注说明"
            />
          </a-form-item>
        </a-form>
        
        <a-alert 
          message="生成提示" 
          description="系统将根据设定的条件自动生成入围名单，请确认参数设置无误。" 
          type="info" 
          show-icon 
        />
      </div>
    </a-modal>

    <!-- 反馈报告弹窗 -->
    <a-modal 
      title="反馈报告" 
      :visible="reportVisible" 
      @cancel="reportVisible = false" 
      :footer="null" 
      width="700px"
    >
      <div class="feedback-report" v-if="currentFeedback">
        <a-descriptions bordered :column="1">
          <a-descriptions-item label="申报单位">{{ currentFeedback.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="报告类型">
            <a-tag color="blue">{{ getFeedbackTypeName(currentFeedback.reportType) }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="生成时间">{{ formatTime(currentFeedback.generateTime) }}</a-descriptions-item>
          <a-descriptions-item label="确认状态">
            <a-tag :color="currentFeedback.isConfirmed ? 'success' : 'warning'">
              {{ currentFeedback.isConfirmed ? '已确认' : '待确认' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>报告内容</a-divider>
        
        <div class="report-content">
          <h4>评估详情</h4>
          <pre class="evaluation-details">{{ currentFeedback.evaluationDetails }}</pre>
          
          <h4>改进建议</h4>
          <p class="suggestions">{{ currentFeedback.suggestions }}</p>
          
          <h4>决策依据</h4>
          <p class="decision-basis">{{ currentFeedback.decisionBasis }}</p>
          
          <h4>总体反馈</h4>
          <p class="report-summary">{{ currentFeedback.reportContent }}</p>
        </div>
        
        <div class="report-actions" style="text-align: center; margin-top: 24px;">
          <a-space>
            <a-button type="primary" @click="downloadReport">下载报告</a-button>
            <a-button @click="printReport">打印报告</a-button>
            <a-button v-if="!currentFeedback.isConfirmed" @click="confirmReport">确认报告</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useReviewStore } from '@/store/modules/review'
import type { ShortlistRecord, FeedbackReport, ShortlistSearchParams } from '@/types/review'
import {
  ShortlistStatusOptions,
  ShortlistStatusTextMap,
  ShortlistStatusColorMap,
  FeedbackTypeTextMap
} from '@/types/review'
import {
  PlusOutlined,
  NotificationOutlined,
  ExportOutlined,
  ReloadOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  CrownOutlined,
  StarOutlined
} from '@ant-design/icons-vue'

// 使用store
const reviewStore = useReviewStore()

// 响应式数据
const searchForm = ref<ShortlistSearchParams>({
  applicantName: '',
  projectName: '',
  status: undefined,
  minScore: undefined,
  maxScore: undefined,
  dateRange: undefined
})

const detailVisible = ref(false)
const generateVisible = ref(false)
const reportVisible = ref(false)
const generating = ref(false)
const selectedRowKeys = ref<number[]>([])
const sortBy = ref('ranking')
const currentShortlist = ref<ShortlistRecord | null>(null)
const currentFeedback = ref<FeedbackReport | null>(null)

// 生成入围名单表单
const generateForm = ref({
  minScore: 70,
  maxCount: 30,
  rule: 1,
  comments: ''
})

// 计算属性
const {
  shortlistList,
  shortlistLoading,
  feedbackList
} = reviewStore

const sortedShortlistList = computed(() => {
  let sorted = [...shortlistList]

  switch (sortBy.value) {
    case 'ranking':
      sorted.sort((a, b) => a.ranking - b.ranking)
      break
    case 'score':
      sorted.sort((a, b) => b.finalScore - a.finalScore)
      break
    case 'time':
      sorted.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      break
  }

  return sorted
})

// 表格列定义
const columns = [
  {
    title: '排名',
    key: 'ranking',
    width: 80,
    align: 'center'
  },
  {
    title: '申报单位',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 200
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
    width: 180
  },
  {
    title: '最终得分',
    key: 'finalScore',
    width: 150,
    align: 'center'
  },
  {
    title: '入围状态',
    key: 'status',
    width: 120,
    align: 'center'
  },
  {
    title: '公示时间',
    key: 'publicTime',
    width: 180
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getShortlistCountByStatus(status: number) {
  return shortlistList.filter(item => item.status === status).length
}

function getAverageScore() {
  if (shortlistList.length === 0) return 0
  const total = shortlistList.reduce((sum, item) => sum + item.finalScore, 0)
  return total / shortlistList.length
}

function getStatusText(status: number) {
  return ShortlistStatusTextMap[status as keyof typeof ShortlistStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return ShortlistStatusColorMap[status as keyof typeof ShortlistStatusColorMap] || 'default'
}

function getRankingColor(ranking: number) {
  if (ranking === 1) return '#ffd700' // 金色
  if (ranking === 2) return '#c0c0c0' // 银色
  if (ranking === 3) return '#cd7f32' // 铜色
  if (ranking <= 10) return '#1890ff' // 蓝色
  return '#52c41a' // 绿色
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  if (score >= 60) return '#fa8c16'
  return '#ff4d4f'
}

function getFeedbackTypeName(type: number) {
  return FeedbackTypeTextMap[type as keyof typeof FeedbackTypeTextMap] || '未知'
}

function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString('zh-CN')
}

async function refreshData() {
  await Promise.all([
    reviewStore.fetchShortlistList(),
    reviewStore.fetchFeedbackList()
  ])
  message.success('数据刷新成功')
}

function showDetail(record: ShortlistRecord) {
  currentShortlist.value = record
  detailVisible.value = true
}

function generateShortlist() {
  generateForm.value = {
    minScore: 70,
    maxCount: 30,
    rule: 1,
    comments: ''
  }
  generateVisible.value = true
}

async function confirmGenerate() {
  try {
    generating.value = true

    const criteria = {
      minScore: generateForm.value.minScore,
      maxCount: generateForm.value.maxCount,
      rule: generateForm.value.rule,
      comments: generateForm.value.comments
    }

    const success = await reviewStore.generateShortlist(criteria)
    if (success) {
      message.success('入围名单生成成功')
      generateVisible.value = false
      await refreshData()
    } else {
      message.error('入围名单生成失败')
    }
  } catch (error) {
    message.error('生成失败，请重试')
  } finally {
    generating.value = false
  }
}

async function publishSingle(record: ShortlistRecord) {
  try {
    const success = await reviewStore.publishShortlist([record.id!])
    if (success) {
      message.success('公示成功')
      await refreshData()
    } else {
      message.error('公示失败')
    }
  } catch (error) {
    message.error('公示失败，请重试')
  }
}

async function publishResults() {
  try {
    const success = await reviewStore.publishShortlist(selectedRowKeys.value)
    if (success) {
      message.success('批量公示成功')
      selectedRowKeys.value = []
      await refreshData()
    } else {
      message.error('批量公示失败')
    }
  } catch (error) {
    message.error('公示失败，请重试')
  }
}

async function confirmResult(record: ShortlistRecord) {
  try {
    // 模拟确认结果操作
    message.success(`${record.applicantName} 的入围结果已确认`)
    await refreshData()
  } catch (error) {
    message.error('确认失败，请重试')
  }
}

async function generateFeedbackReport(record: ShortlistRecord) {
  try {
    const success = await reviewStore.generateFeedbackReport(record.applicationId)
    if (success) {
      message.success('反馈报告生成成功')
      await refreshData()
    } else {
      message.error('反馈报告生成失败')
    }
  } catch (error) {
    message.error('生成失败，请重试')
  }
}

function viewFeedbackReport(record: ShortlistRecord) {
  // 查找对应的反馈报告
  const feedback = feedbackList.find(item => item.applicationId === record.applicationId)
  if (feedback) {
    currentFeedback.value = feedback
    reportVisible.value = true
  } else {
    message.warning('暂无反馈报告，请先生成')
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchGenerateReports() {
  try {
    for (const id of selectedRowKeys.value) {
      const record = shortlistList.find(item => item.id === id)
      if (record) {
        await reviewStore.generateFeedbackReport(record.applicationId)
      }
    }
    message.success('批量生成反馈报告成功')
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    message.error('批量生成失败')
  }
}

function exportResults() {
  message.info('结果导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await reviewStore.fetchShortlistList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    applicantName: '',
    projectName: '',
    status: undefined,
    minScore: undefined,
    maxScore: undefined,
    dateRange: undefined
  }
  reviewStore.fetchShortlistList()
}

// 反馈报告操作
function downloadReport() {
  message.info('报告下载功能开发中...')
}

function printReport() {
  message.info('报告打印功能开发中...')
}

async function confirmReport() {
  try {
    if (currentFeedback.value) {
      const success = await reviewStore.confirmFeedbackReport(currentFeedback.value.id!)
      if (success) {
        message.success('报告确认成功')
        reportVisible.value = false
        await refreshData()
      } else {
        message.error('报告确认失败')
      }
    }
  } catch (error) {
    message.error('确认失败，请重试')
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    reviewStore.fetchShortlistList(),
    reviewStore.fetchFeedbackList()
  ])
})
</script>

<style lang="scss" scoped>
.review-results-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .generate-form {
    .ant-alert {
      margin-top: 16px;
    }
  }

  .feedback-report {
    .report-content {
      h4 {
        margin: 16px 0 8px 0;
        color: #333;
        font-weight: 600;
      }

      .evaluation-details {
        background: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 200px;
        overflow-y: auto;
      }

      .suggestions,
      .decision-basis,
      .report-summary {
        padding: 12px;
        background: #fafafa;
        border-radius: 4px;
        line-height: 1.6;
        color: #333;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-results-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
