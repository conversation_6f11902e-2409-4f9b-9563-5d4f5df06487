<template>
  <div class="review-appeal-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>申诉管理</h2>
        <p>智能入围审核申诉管理，支持在线申诉提交、申诉处理、复审流程等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showSubmitModal">
            <template #icon><plus-outlined /></template>
            提交申诉
          </a-button>
          <a-button @click="batchProcess" :disabled="selectedRowKeys.length === 0">
            <template #icon><check-outlined /></template>
            批量处理
          </a-button>
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 申诉统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待处理"
              :value="getAppealCountByStatus(1)"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="审理中"
              :value="getAppealCountByStatus(2)"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <audit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已受理"
              :value="getAppealCountByStatus(3)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已驳回"
              :value="getAppealCountByStatus(4)"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申诉人" class="form-item-full">
                <a-input v-model:value="searchForm.applicantName" placeholder="请输入申诉人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申诉状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择申诉状态" allow-clear>
                  <a-select-option v-for="item in AppealStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="申诉原因" class="form-item-full">
                <a-input v-model:value="searchForm.appealReason" placeholder="请输入申诉原因" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审理人" class="form-item-full">
                <a-input v-model:value="searchForm.reviewerName" placeholder="请输入审理人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="申诉时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 申诉记录表格 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>申诉记录</span>
            <span class="record-count">共 {{ appealList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterStatus" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in AppealStatusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button size="small" @click="showBatchProcessModal" :disabled="selectedRowKeys.length === 0">
              批量审理
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredAppealList"
          :loading="appealLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'finalDecision'">
              <a-tag v-if="record.finalDecision" :color="getDecisionColor(record.finalDecision)">
                {{ getDecisionText(record.finalDecision) }}
              </a-tag>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'appealTime'">
              {{ formatTime(record.appealTime) }}
            </template>
            <template v-else-if="column.key === 'reviewTime'">
              <span v-if="record.reviewTime">{{ formatTime(record.reviewTime) }}</span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 1 || record.status === 2" 
                  type="link" 
                  size="small" 
                  @click="showProcessModal(record)"
                >
                  处理
                </a-button>
                <a-button 
                  v-if="record.status >= 3" 
                  type="link" 
                  size="small" 
                  @click="viewProcessHistory(record)"
                >
                  处理历史
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="downloadAppealMaterials(record)"
                >
                  下载材料
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 申诉详情弹窗 -->
    <a-modal 
      title="申诉详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentAppeal">
        <a-descriptions-item label="申诉人">{{ currentAppeal.applicantName }}</a-descriptions-item>
        <a-descriptions-item label="申诉原因">{{ currentAppeal.appealReason }}</a-descriptions-item>
        <a-descriptions-item label="申诉状态">
          <a-tag :color="getStatusColor(currentAppeal.status)">
            {{ getStatusText(currentAppeal.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="申诉时间">{{ formatTime(currentAppeal.appealTime) }}</a-descriptions-item>
        <a-descriptions-item label="审理人">
          {{ currentAppeal.reviewerName || '未分配' }}
        </a-descriptions-item>
        <a-descriptions-item label="审理时间">
          {{ currentAppeal.reviewTime ? formatTime(currentAppeal.reviewTime) : '未审理' }}
        </a-descriptions-item>
        <a-descriptions-item label="最终决定">
          <a-tag v-if="currentAppeal.finalDecision" :color="getDecisionColor(currentAppeal.finalDecision)">
            {{ getDecisionText(currentAppeal.finalDecision) }}
          </a-tag>
          <span v-else>待决定</span>
        </a-descriptions-item>
        <a-descriptions-item label="申诉内容">
          <div class="appeal-content">{{ currentAppeal.appealContent }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="审理结果">
          <div class="review-result">{{ currentAppeal.reviewResult || '暂无结果' }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="审理意见">
          <div class="review-comments">{{ currentAppeal.reviewComments || '暂无意见' }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="申诉材料">
          <div v-if="currentAppeal.appealAttachments && currentAppeal.appealAttachments.length > 0">
            <a-tag v-for="file in currentAppeal.appealAttachments" :key="file" color="blue">
              {{ file }}
            </a-tag>
          </div>
          <span v-else>暂无附件</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 提交申诉弹窗 -->
    <a-modal 
      title="提交申诉" 
      :visible="submitVisible" 
      @cancel="submitVisible = false" 
      @ok="submitAppeal"
      :confirm-loading="submitting"
      width="700px"
    >
      <a-form 
        :model="appealForm" 
        :rules="appealRules" 
        ref="appealFormRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="申诉人" name="applicantName">
          <a-input v-model:value="appealForm.applicantName" placeholder="请输入申诉人姓名" />
        </a-form-item>
        <a-form-item label="申诉原因" name="appealReason">
          <a-select v-model:value="appealForm.appealReason" placeholder="请选择申诉原因">
            <a-select-option value="评分有误">评分有误</a-select-option>
            <a-select-option value="程序不当">程序不当</a-select-option>
            <a-select-option value="材料遗漏">材料遗漏</a-select-option>
            <a-select-option value="其他原因">其他原因</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="申诉内容" name="appealContent">
          <a-textarea 
            v-model:value="appealForm.appealContent" 
            :rows="6" 
            placeholder="请详细描述申诉内容和理由"
          />
        </a-form-item>
        <a-form-item label="申诉材料" name="appealAttachments">
          <div class="upload-section">
            <a-upload-dragger
              :file-list="appealFileList"
              :before-upload="beforeUpload"
              @remove="handleRemove"
              multiple
            >
              <p class="ant-upload-drag-icon">
                <inbox-outlined />
              </p>
              <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p class="ant-upload-hint">
                支持多个文件上传，支持PDF、Word、图片等格式
              </p>
            </a-upload-dragger>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 申诉处理弹窗 -->
    <a-modal 
      title="申诉处理" 
      :visible="processVisible" 
      @cancel="processVisible = false" 
      @ok="submitProcess"
      :confirm-loading="processing"
      width="600px"
    >
      <div class="process-form" v-if="currentAppeal">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="申诉人">{{ currentAppeal.applicantName }}</a-descriptions-item>
          <a-descriptions-item label="申诉原因">{{ currentAppeal.appealReason }}</a-descriptions-item>
          <a-descriptions-item label="申诉时间">{{ formatTime(currentAppeal.appealTime) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>处理意见</a-divider>
        
        <a-form :model="processForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="处理决定" required>
            <a-radio-group v-model:value="processForm.finalDecision">
              <a-radio :value="1">维持原决定</a-radio>
              <a-radio :value="2">撤销原决定</a-radio>
              <a-radio :value="3">部分支持</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="审理结果" required>
            <a-textarea 
              v-model:value="processForm.reviewResult" 
              :rows="4" 
              placeholder="请输入审理结果"
            />
          </a-form-item>
          <a-form-item label="审理意见" required>
            <a-textarea 
              v-model:value="processForm.reviewComments" 
              :rows="3" 
              placeholder="请输入审理意见"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useReviewStore } from '@/store/modules/review'
import type { AppealRecord, AppealSearchParams } from '@/types/review'
import {
  AppealStatusOptions,
  AppealStatusTextMap,
  AppealStatusColorMap,
  AppealDecisionTextMap,
  AppealDecisionColorMap
} from '@/types/review'
import {
  PlusOutlined,
  CheckOutlined,
  ExportOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'

// 使用store
const reviewStore = useReviewStore()

// 响应式数据
const searchForm = ref<AppealSearchParams>({
  applicantName: '',
  status: undefined,
  appealReason: '',
  reviewerName: '',
  dateRange: undefined
})

const detailVisible = ref(false)
const submitVisible = ref(false)
const processVisible = ref(false)
const submitting = ref(false)
const processing = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterStatus = ref<number | undefined>(undefined)
const appealFileList = ref<any[]>([])

// 申诉表单数据
const appealForm = ref({
  applicantName: '',
  appealReason: '',
  appealContent: '',
  appealAttachments: []
})

// 处理表单数据
const processForm = ref({
  finalDecision: 1,
  reviewResult: '',
  reviewComments: ''
})

// 表单引用
const appealFormRef = ref()

// 表单验证规则
const appealRules = {
  applicantName: [
    { required: true, message: '请输入申诉人姓名', trigger: 'blur' }
  ],
  appealReason: [
    { required: true, message: '请选择申诉原因', trigger: 'change' }
  ],
  appealContent: [
    { required: true, message: '请输入申诉内容', trigger: 'blur' },
    { min: 10, max: 1000, message: '申诉内容长度在 10 到 1000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const {
  appealList,
  appealLoading,
  currentAppeal
} = reviewStore

const filteredAppealList = computed(() => {
  let filtered = [...appealList]

  if (filterStatus.value !== undefined) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '申诉人',
    dataIndex: 'applicantName',
    key: 'applicantName',
    width: 120
  },
  {
    title: '申诉原因',
    dataIndex: 'appealReason',
    key: 'appealReason',
    width: 120
  },
  {
    title: '申诉状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '申诉时间',
    key: 'appealTime',
    width: 180
  },
  {
    title: '审理人',
    dataIndex: 'reviewerName',
    key: 'reviewerName',
    width: 100
  },
  {
    title: '审理时间',
    key: 'reviewTime',
    width: 180
  },
  {
    title: '最终决定',
    key: 'finalDecision',
    width: 120,
    align: 'center'
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: AppealRecord) {
  return filteredAppealList.value.findIndex(item => item.id === record.id) + 1
}

function getAppealCountByStatus(status: number) {
  return reviewStore.getAppealCountByStatus(status)
}

function getStatusText(status: number) {
  return AppealStatusTextMap[status as keyof typeof AppealStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return AppealStatusColorMap[status as keyof typeof AppealStatusColorMap] || 'default'
}

function getDecisionText(decision: number) {
  return AppealDecisionTextMap[decision as keyof typeof AppealDecisionTextMap] || '未知'
}

function getDecisionColor(decision: number) {
  return AppealDecisionColorMap[decision as keyof typeof AppealDecisionColorMap] || 'default'
}

function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString('zh-CN')
}

async function refreshData() {
  await reviewStore.fetchAppealList()
  message.success('数据刷新成功')
}

function showDetail(record: AppealRecord) {
  reviewStore.setCurrentAppeal(record)
  detailVisible.value = true
}

function showSubmitModal() {
  appealForm.value = {
    applicantName: '',
    appealReason: '',
    appealContent: '',
    appealAttachments: []
  }
  appealFileList.value = []
  submitVisible.value = true
}

function showProcessModal(record: AppealRecord) {
  reviewStore.setCurrentAppeal(record)
  processForm.value = {
    finalDecision: 1,
    reviewResult: '',
    reviewComments: ''
  }
  processVisible.value = true
}

async function submitAppeal() {
  try {
    submitting.value = true

    // 验证表单
    await appealFormRef.value.validate()

    // 处理附件
    appealForm.value.appealAttachments = appealFileList.value.map(file => file.name)

    const success = await reviewStore.submitAppeal(appealForm.value)
    if (success) {
      message.success('申诉提交成功')
      submitVisible.value = false
      await refreshData()
    } else {
      message.error('申诉提交失败')
    }
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('提交失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

async function submitProcess() {
  try {
    processing.value = true

    if (!processForm.value.reviewResult.trim()) {
      message.error('请输入审理结果')
      return
    }

    if (!processForm.value.reviewComments.trim()) {
      message.error('请输入审理意见')
      return
    }

    const success = await reviewStore.processAppeal(currentAppeal.value?.id!, processForm.value)
    if (success) {
      message.success('申诉处理成功')
      processVisible.value = false
      await refreshData()
    } else {
      message.error('申诉处理失败')
    }
  } catch (error) {
    message.error('处理失败，请重试')
  } finally {
    processing.value = false
  }
}

// 文件上传处理
function beforeUpload(file: any) {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'].includes(file.type)
  if (!isValidType) {
    message.error('只能上传PDF、Word、图片格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }

  // 添加到文件列表
  appealFileList.value.push({
    uid: Date.now(),
    name: file.name,
    status: 'done',
    originFileObj: file
  })

  return false // 阻止自动上传
}

function handleRemove(file: any) {
  const index = appealFileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    appealFileList.value.splice(index, 1)
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchProcess() {
  try {
    message.info('批量处理功能开发中...')
    // 实际实现中可以打开批量处理弹窗
  } catch (error) {
    message.error('批量处理失败')
  }
}

function showBatchProcessModal() {
  message.info('批量审理功能开发中...')
}

function viewProcessHistory(record: AppealRecord) {
  message.info(`查看 ${record.applicantName} 的处理历史`)
  // 这里可以跳转到处理历史页面或打开历史弹窗
}

function downloadAppealMaterials(record: AppealRecord) {
  message.info(`下载 ${record.applicantName} 的申诉材料`)
  // 这里可以实现材料下载功能
}

function exportData() {
  message.info('数据导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await reviewStore.fetchAppealList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    applicantName: '',
    status: undefined,
    appealReason: '',
    reviewerName: '',
    dateRange: undefined
  }
  reviewStore.fetchAppealList()
}

// 生命周期
onMounted(async () => {
  await reviewStore.fetchAppealList()
})
</script>

<style lang="scss" scoped>
.review-appeal-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .appeal-content,
  .review-result,
  .review-comments {
    padding: 12px;
    background: #fafafa;
    border-radius: 4px;
    line-height: 1.6;
    color: #333;
    max-height: 200px;
    overflow-y: auto;
  }

  .upload-section {
    .ant-upload-dragger {
      margin-bottom: 16px;
    }
  }

  .process-form {
    .ant-descriptions {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-appeal-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
