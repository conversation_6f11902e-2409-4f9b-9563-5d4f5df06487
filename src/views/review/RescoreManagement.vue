<template>
  <div class="rescore-management-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>再次评分管理</h2>
        <p>当评分机制调整后，系统支持重新计算评分结果，确保评分的准确性和公正性</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showRuleUpdateModal" :loading="ruleUpdateLoading">
            <template #icon><setting-outlined /></template>
            更新评分规则
          </a-button>
          <a-button type="primary" @click="batchRescore" :disabled="selectedRowKeys.length === 0" :loading="rescoreLoading">
            <template #icon><calculator-outlined /></template>
            批量重新评分
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待重新评分"
              :value="pendingRescoreCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已完成重评"
              :value="completedRescoreCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="规则版本"
              :value="currentRuleVersion"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <branches-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="影响对象数"
              :value="affectedObjectsCount"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <team-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="培育对象名称" name="objectName">
            <a-input v-model:value="searchForm.objectName" placeholder="请输入培育对象名称" style="width: 200px" />
          </a-form-item>
          <a-form-item label="重评状态" name="rescoreStatus">
            <a-select v-model:value="searchForm.rescoreStatus" placeholder="请选择重评状态" style="width: 150px" allowClear>
              <a-select-option value="pending">待重评</a-select-option>
              <a-select-option value="processing">重评中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="failed">重评失败</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="项目类型" name="projectType">
            <a-select v-model:value="searchForm.projectType" placeholder="请选择项目类型" style="width: 150px" allowClear>
              <a-select-option value="party_building">党建项目</a-select-option>
              <a-select-option value="innovation">创新项目</a-select-option>
              <a-select-option value="service">服务项目</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="更新时间" name="updateTimeRange">
            <a-range-picker v-model:value="searchForm.updateTimeRange" style="width: 300px" />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearchForm">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <div class="table-title">培育对象列表</div>
            <div class="table-info">共 {{ total }} 条记录</div>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button type="link" @click="selectAll">全选</a-button>
            <a-button type="link" @click="clearSelection">取消选择</a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="handleExport">
                  <a-menu-item key="current">导出当前页</a-menu-item>
                  <a-menu-item key="selected">导出已选择</a-menu-item>
                  <a-menu-item key="all">导出全部</a-menu-item>
                </a-menu>
              </template>
              <a-button>
                <template #icon><export-outlined /></template>
                导出
                <template #suffix><down-outlined /></template>
              </a-button>
            </a-dropdown>
          </a-space>
        </template>

        <a-table
          :columns="columns"
          :data-source="dataSource"
          :row-selection="{
            type: 'checkbox',
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectionChange,
          }"
          :pagination="{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }"
          :loading="loading"
          row-key="id"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <!-- 培育对象名称 -->
            <template v-if="column.key === 'objectName'">
              <a-button type="link" @click="viewDetail(record)">{{ record.objectName }}</a-button>
            </template>
            
            <!-- 重评状态 -->
            <template v-else-if="column.key === 'rescoreStatus'">
              <a-tag :color="getRescoreStatusColor(record.rescoreStatus)">
                {{ getRescoreStatusText(record.rescoreStatus) }}
              </a-tag>
            </template>
            
            <!-- 评分变化 -->
            <template v-else-if="column.key === 'scoreChange'">
              <div class="score-change">
                <div class="score-item">
                  <span class="label">原分数：</span>
                  <span class="old-score">{{ record.oldScore }}</span>
                </div>
                <div class="score-item" v-if="record.newScore !== null">
                  <span class="label">新分数：</span>
                  <span class="new-score" :class="getScoreChangeClass(record.oldScore, record.newScore)">
                    {{ record.newScore }}
                  </span>
                  <span class="change-indicator" :class="getScoreChangeClass(record.oldScore, record.newScore)">
                    {{ getScoreChangeText(record.oldScore, record.newScore) }}
                  </span>
                </div>
                <div class="score-item" v-else>
                  <span class="label">新分数：</span>
                  <span class="pending">待计算</span>
                </div>
              </div>
            </template>
            
            <!-- 操作列 -->
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  <template #icon><eye-outlined /></template>
                  查看详情
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="singleRescore(record)" 
                  :disabled="record.rescoreStatus === 'processing'"
                  :loading="record.rescoring"
                >
                  <template #icon><calculator-outlined /></template>
                  重新评分
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewHistory(record)"
                  v-if="record.rescoreStatus === 'completed'"
                >
                  <template #icon><history-outlined /></template>
                  查看历史
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 评分规则更新对话框 -->
    <a-modal
      v-model:open="ruleUpdateModalVisible"
      title="更新评分规则"
      width="800px"
      :confirm-loading="ruleUpdateLoading"
      @ok="handleRuleUpdate"
    >
      <a-form :model="ruleUpdateForm" layout="vertical">
        <a-form-item label="规则版本" required>
          <a-input v-model:value="ruleUpdateForm.version" placeholder="请输入新的规则版本号" />
        </a-form-item>
        <a-form-item label="更新说明" required>
          <a-textarea v-model:value="ruleUpdateForm.description" placeholder="请描述本次规则更新的内容" :rows="3" />
        </a-form-item>
        <a-form-item label="影响范围">
          <a-checkbox-group v-model:value="ruleUpdateForm.affectedTypes">
            <a-row>
              <a-col :span="8"><a-checkbox value="party_building">党建项目</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="innovation">创新项目</a-checkbox></a-col>
              <a-col :span="8"><a-checkbox value="service">服务项目</a-checkbox></a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="自动重评">
          <a-switch v-model:checked="ruleUpdateForm.autoRescore" />
          <span class="form-help-text">开启后将自动为所有受影响的培育对象重新计算评分</span>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 评分详情对话框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="评分详情"
      width="1000px"
      :footer="null"
    >
      <div v-if="selectedRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="培育对象">{{ selectedRecord.objectName }}</a-descriptions-item>
          <a-descriptions-item label="项目类型">{{ selectedRecord.projectType }}</a-descriptions-item>
          <a-descriptions-item label="原评分">{{ selectedRecord.oldScore }}</a-descriptions-item>
          <a-descriptions-item label="新评分">{{ selectedRecord.newScore || '待计算' }}</a-descriptions-item>
          <a-descriptions-item label="重评状态">
            <a-tag :color="getRescoreStatusColor(selectedRecord.rescoreStatus)">
              {{ getRescoreStatusText(selectedRecord.rescoreStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="最后更新">{{ selectedRecord.updateTime }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="detail-tabs" style="margin-top: 20px;">
          <a-tabs v-model:activeKey="detailActiveTab">
            <a-tab-pane key="indicators" tab="指标详情">
              <a-table :columns="indicatorColumns" :data-source="selectedRecord.indicators" size="small" :pagination="false">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'scoreChange'">
                    <div class="indicator-score-change">
                      <div>原分：<span class="old-score">{{ record.oldScore }}</span></div>
                      <div v-if="record.newScore !== null">
                        新分：<span class="new-score" :class="getScoreChangeClass(record.oldScore, record.newScore)">{{ record.newScore }}</span>
                      </div>
                      <div v-else>新分：<span class="pending">待计算</span></div>
                    </div>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="history" tab="变更历史" v-if="selectedRecord.rescoreStatus === 'completed'">
              <a-timeline>
                <a-timeline-item v-for="item in selectedRecord.history" :key="item.id">
                  <template #dot>
                    <clock-circle-outlined style="font-size: 16px;" />
                  </template>
                  <div class="history-item">
                    <div class="history-title">{{ item.action }}</div>
                    <div class="history-detail">{{ item.detail }}</div>
                    <div class="history-time">{{ item.timestamp }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import {
  SettingOutlined,
  CalculatorOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  BranchesOutlined,
  TeamOutlined,
  SearchOutlined,
  ClearOutlined,
  ExportOutlined,
  DownOutlined,
  EyeOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface RescoreRecord {
  id: string
  objectName: string
  projectType: string
  oldScore: number
  newScore: number | null
  rescoreStatus: 'pending' | 'processing' | 'completed' | 'failed'
  updateTime: string
  indicators: IndicatorRecord[]
  history: HistoryRecord[]
  rescoring?: boolean
}

interface IndicatorRecord {
  id: string
  indicatorName: string
  weight: number
  oldScore: number
  newScore: number | null
  ruleChanged: boolean
}

interface HistoryRecord {
  id: string
  action: string
  detail: string
  timestamp: string
}

interface SearchForm {
  objectName: string
  rescoreStatus: string | undefined
  projectType: string | undefined
  updateTimeRange: [Dayjs, Dayjs] | null
}

interface RuleUpdateForm {
  version: string
  description: string
  affectedTypes: string[]
  autoRescore: boolean
}

// 响应式数据
const searchForm = ref<SearchForm>({
  objectName: '',
  rescoreStatus: undefined,
  projectType: undefined,
  updateTimeRange: null
})

const ruleUpdateForm = ref<RuleUpdateForm>({
  version: '',
  description: '',
  affectedTypes: [],
  autoRescore: true
})

const dataSource = ref<RescoreRecord[]>([])
const selectedRowKeys = ref<string[]>([])
const loading = ref(false)
const ruleUpdateLoading = ref(false)
const rescoreLoading = ref(false)
const ruleUpdateModalVisible = ref(false)
const detailModalVisible = ref(false)
const selectedRecord = ref<RescoreRecord | null>(null)
const detailActiveTab = ref('indicators')

const pagination = ref({
  current: 1,
  pageSize: 10,
})

const total = ref(0)
const currentRuleVersion = ref('v2.1.0')

// 计算属性
const pendingRescoreCount = computed(() => dataSource.value.filter(item => item.rescoreStatus === 'pending').length)
const completedRescoreCount = computed(() => dataSource.value.filter(item => item.rescoreStatus === 'completed').length)
const affectedObjectsCount = computed(() => dataSource.value.length)

// 表格列定义
const columns = [
  {
    title: '培育对象名称',
    dataIndex: 'objectName',
    key: 'objectName',
    width: 200,
  },
  {
    title: '项目类型',
    dataIndex: 'projectType',
    key: 'projectType',
    width: 120,
  },
  {
    title: '重评状态',
    dataIndex: 'rescoreStatus',
    key: 'rescoreStatus',
    width: 120,
  },
  {
    title: '评分变化',
    key: 'scoreChange',
    width: 200,
  },
  {
    title: '最后更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'actions',
    fixed: 'right',
    width: 200,
  },
]

const indicatorColumns = [
  {
    title: '指标名称',
    dataIndex: 'indicatorName',
    key: 'indicatorName',
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
    width: 80,
  },
  {
    title: '评分变化',
    key: 'scoreChange',
    width: 120,
  },
  {
    title: '规则是否变更',
    dataIndex: 'ruleChanged',
    key: 'ruleChanged',
    width: 120,
    customRender: ({ record }: { record: IndicatorRecord }) => {
      return record.ruleChanged ? '是' : '否'
    }
  },
]

// 模拟数据
const mockData: RescoreRecord[] = [
  {
    id: '1',
    objectName: '党员服务中心数字化改革项目',
    projectType: '党建项目',
    oldScore: 85.5,
    newScore: 87.2,
    rescoreStatus: 'completed',
    updateTime: '2025-01-06 14:30:00',
    indicators: [
      {
        id: '1',
        indicatorName: '创新程度',
        weight: 0.3,
        oldScore: 80,
        newScore: 85,
        ruleChanged: true
      },
      {
        id: '2',
        indicatorName: '实施效果',
        weight: 0.4,
        oldScore: 88,
        newScore: 88,
        ruleChanged: false
      },
      {
        id: '3',
        indicatorName: '可推广性',
        weight: 0.3,
        oldScore: 87,
        newScore: 89,
        ruleChanged: true
      }
    ],
    history: [
      {
        id: '1',
        action: '规则更新完成',
        detail: '评分规则已更新至 v2.1.0，影响创新程度和可推广性指标计算方式',
        timestamp: '2025-01-06 14:30:00'
      },
      {
        id: '2',
        action: '重新评分完成',
        detail: '根据新规则重新计算评分，总分从 85.5 提升至 87.2',
        timestamp: '2025-01-06 14:28:00'
      }
    ]
  },
  {
    id: '2',
    objectName: '智慧党建平台建设项目',
    projectType: '创新项目',
    oldScore: 92.3,
    newScore: null,
    rescoreStatus: 'pending',
    updateTime: '2025-01-06 10:15:00',
    indicators: [
      {
        id: '4',
        indicatorName: '技术先进性',
        weight: 0.35,
        oldScore: 95,
        newScore: null,
        ruleChanged: true
      }
    ],
    history: []
  }
]

// 方法定义
const showRuleUpdateModal = () => {
  ruleUpdateModalVisible.value = true
  ruleUpdateForm.value = {
    version: `v${(parseFloat(currentRuleVersion.value.substring(1)) + 0.1).toFixed(1)}`,
    description: '',
    affectedTypes: ['party_building', 'innovation', 'service'],
    autoRescore: true
  }
}

const handleRuleUpdate = async () => {
  ruleUpdateLoading.value = true
  try {
    // 模拟规则更新
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    currentRuleVersion.value = ruleUpdateForm.value.version
    message.success('评分规则更新成功！')
    ruleUpdateModalVisible.value = false
    
    if (ruleUpdateForm.value.autoRescore) {
      message.info('自动重评任务已启动，请稍候查看结果')
    }
    
    await loadData()
  } catch (error) {
    message.error('规则更新失败，请重试')
  } finally {
    ruleUpdateLoading.value = false
  }
}

const batchRescore = async () => {
  rescoreLoading.value = true
  try {
    // 模拟批量重评
    await new Promise(resolve => setTimeout(resolve, 3000))
    message.success(`已成功为 ${selectedRowKeys.value.length} 个培育对象重新评分`)
    selectedRowKeys.value = []
    await loadData()
  } catch (error) {
    message.error('批量重评失败，请重试')
  } finally {
    rescoreLoading.value = false
  }
}

const singleRescore = async (record: RescoreRecord) => {
  record.rescoring = true
  try {
    // 模拟单个重评
    await new Promise(resolve => setTimeout(resolve, 2000))
    record.rescoreStatus = 'completed'
    record.newScore = record.oldScore + (Math.random() - 0.5) * 10
    message.success('重新评分完成')
  } catch (error) {
    message.error('重新评分失败，请重试')
  } finally {
    record.rescoring = false
  }
}

const viewDetail = (record: RescoreRecord) => {
  selectedRecord.value = record
  detailModalVisible.value = true
  detailActiveTab.value = 'indicators'
}

const viewHistory = (record: RescoreRecord) => {
  selectedRecord.value = record
  detailModalVisible.value = true
  detailActiveTab.value = 'history'
}

const handleSearch = () => {
  pagination.value.current = 1
  loadData()
}

const resetSearchForm = () => {
  searchForm.value = {
    objectName: '',
    rescoreStatus: undefined,
    projectType: undefined,
    updateTimeRange: null
  }
  handleSearch()
}

const refreshData = () => {
  loadData()
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500))
    dataSource.value = mockData
    total.value = mockData.length
  } finally {
    loading.value = false
  }
}

const onSelectionChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

const selectAll = () => {
  selectedRowKeys.value = dataSource.value.map(item => item.id)
}

const clearSelection = () => {
  selectedRowKeys.value = []
}

const handleTableChange = (pagination: any) => {
  pagination.value.current = pagination.current
  pagination.value.pageSize = pagination.pageSize
  loadData()
}

const handleExport = ({ key }: { key: string }) => {
  message.info(`导出${key === 'current' ? '当前页' : key === 'selected' ? '已选择' : '全部'}数据`)
}

// 状态相关方法
const getRescoreStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    failed: 'red'
  }
  return colorMap[status] || 'default'
}

const getRescoreStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待重评',
    processing: '重评中',
    completed: '已完成',
    failed: '重评失败'
  }
  return textMap[status] || '未知'
}

const getScoreChangeClass = (oldScore: number, newScore: number) => {
  if (newScore > oldScore) return 'score-increase'
  if (newScore < oldScore) return 'score-decrease'
  return 'score-unchanged'
}

const getScoreChangeText = (oldScore: number, newScore: number) => {
  const diff = Math.round((newScore - oldScore) * 10) / 10
  if (diff > 0) return `(+${diff})`
  if (diff < 0) return `(${diff})`
  return '(无变化)'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.rescore-management-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-title {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.statistics-section {
  margin-bottom: 24px;

  .stat-card {
    .ant-statistic {
      .ant-statistic-title {
        font-size: 14px;
        color: #8c8c8c;
      }
      
      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}

.search-section {
  margin-bottom: 24px;
}

.table-section {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .table-info {
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}

.score-change {
  .score-item {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;

    .label {
      color: #8c8c8c;
      font-size: 12px;
    }

    .old-score {
      color: #8c8c8c;
      font-weight: 500;
    }

    .new-score {
      font-weight: 600;
      
      &.score-increase {
        color: #52c41a;
      }
      
      &.score-decrease {
        color: #f5222d;
      }
      
      &.score-unchanged {
        color: #1890ff;
      }
    }

    .change-indicator {
      font-size: 12px;
      
      &.score-increase {
        color: #52c41a;
      }
      
      &.score-decrease {
        color: #f5222d;
      }
    }

    .pending {
      color: #faad14;
      font-style: italic;
    }
  }
}

.indicator-score-change {
  font-size: 12px;
  
  .old-score {
    color: #8c8c8c;
  }
  
  .new-score {
    font-weight: 500;
    
    &.score-increase {
      color: #52c41a;
    }
    
    &.score-decrease {
      color: #f5222d;
    }
    
    &.score-unchanged {
      color: #1890ff;
    }
  }
  
  .pending {
    color: #faad14;
    font-style: italic;
  }
}

.history-item {
  .history-title {
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
  }
  
  .history-detail {
    color: #595959;
    margin-bottom: 4px;
  }
  
  .history-time {
    color: #8c8c8c;
    font-size: 12px;
  }
}

.form-help-text {
  margin-left: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .rescore-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
</style>