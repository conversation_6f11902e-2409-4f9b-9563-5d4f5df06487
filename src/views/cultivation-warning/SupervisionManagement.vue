<template>
  <div class="supervision-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>督办管理</h1>
        <p>培育对象督办管理和多维分析平台</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <plus-outlined />
            新增督办对象
          </a-button>
          <a-button @click="syncToYkz">
            <sync-outlined />
            同步渝快政
          </a-button>
          <a-button @click="exportData">
            <download-outlined />
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 督办统计概览 -->
    <div class="supervision-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="督办对象总数"
              :value="statistics.totalObjects"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="督办中"
              :value="statistics.supervisingObjects"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="已完成"
              :value="statistics.completedObjects"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="完成率"
              :value="statistics.completionRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 督办对象列表 -->
        <a-tab-pane key="list" tab="督办对象列表">
          <div class="supervision-list">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索对象名称或负责人"
                    allow-clear
                    @change="handleSearch"
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedType"
                    placeholder="对象类型"
                    allow-clear
                    @change="handleTypeFilter"
                  >
                    <a-select-option value="科技型企业">科技型企业</a-select-option>
                    <a-select-option value="制造业企业">制造业企业</a-select-option>
                    <a-select-option value="服务业企业">服务业企业</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="督办状态"
                    allow-clear
                    @change="handleStatusFilter"
                  >
                    <a-select-option value="pending">待督办</a-select-option>
                    <a-select-option value="supervising">督办中</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="overdue">已逾期</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedDepartment"
                    placeholder="负责部门"
                    allow-clear
                    @change="handleDepartmentFilter"
                  >
                    <a-select-option value="市科技局">市科技局</a-select-option>
                    <a-select-option value="市经信委">市经信委</a-select-option>
                    <a-select-option value="市发改委">市发改委</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-space>
                    <a-range-picker v-model:value="dateRange" />
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadSupervisionObjects">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 批量操作 -->
            <div class="batch-actions" v-if="selectedRowKeys.length > 0">
              <a-alert
                :message="`已选择 ${selectedRowKeys.length} 项`"
                type="info"
                show-icon
                closable
                @close="clearSelection"
              >
                <template #action>
                  <a-space>
                    <a-button size="small" @click="batchUpdateStatus">批量更新状态</a-button>
                    <a-button size="small" @click="batchExport">批量导出</a-button>
                    <a-button size="small" @click="batchSync">批量同步</a-button>
                  </a-space>
                </template>
              </a-alert>
            </div>

            <!-- 督办对象表格 -->
            <div class="supervision-table">
              <a-table
                :columns="supervisionColumns"
                :data-source="supervisionList"
                :loading="loading"
                :pagination="pagination"
                :row-selection="rowSelection"
                row-key="id"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="object-name">
                      <a @click="viewObjectDetail(record)">{{ record.name }}</a>
                      <div class="object-type">{{ record.type }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'evaluationScore'">
                    <div class="score-display">
                      <a-progress 
                        type="circle" 
                        :percent="record.evaluationScore" 
                        :width="50"
                        :stroke-color="getScoreColor(record.evaluationScore)"
                      />
                      <div class="score-level">{{ record.evaluationLevel }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'supervisionStatus'">
                    <a-badge 
                      :status="getStatusBadge(record.supervisionStatus)" 
                      :text="getStatusText(record.supervisionStatus)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'progress'">
                    <div class="progress-display">
                      <a-progress 
                        :percent="getProgressPercent(record)" 
                        size="small"
                        :stroke-color="getProgressColor(getProgressPercent(record))"
                      />
                      <div class="progress-text">
                        {{ getProgressText(record) }}
                      </div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewObjectDetail(record)">
                        详情
                      </a-button>
                      <a-button type="link" size="small" @click="editObject(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="viewProgress(record)">
                        进度
                      </a-button>
                      <a-dropdown>
                        <template #overlay>
                          <a-menu>
                            <a-menu-item @click="updateStatus(record)">更新状态</a-menu-item>
                            <a-menu-item @click="addProgressReport(record)">添加进度</a-menu-item>
                            <a-menu-item @click="syncObject(record)">同步数据</a-menu-item>
                            <a-menu-divider />
                            <a-menu-item @click="deleteObject(record)" danger>删除</a-menu-item>
                          </a-menu>
                        </template>
                        <a-button type="link" size="small">
                          更多 <down-outlined />
                        </a-button>
                      </a-dropdown>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 多维分析 -->
        <a-tab-pane key="analysis" tab="多维分析">
          <div class="multi-analysis">
            <a-row :gutter="24">
              <!-- 评分分布 -->
              <a-col :span="12">
                <a-card title="评分分布" size="small">
                  <div class="score-distribution">
                    <div class="distribution-chart">
                      <div v-for="level in evaluationDistribution" :key="level.name" class="level-item">
                        <div class="level-header">
                          <span class="level-name">{{ level.name }}</span>
                          <span class="level-count">{{ level.count }}个</span>
                        </div>
                        <div class="level-bar">
                          <div 
                            class="bar-fill" 
                            :style="{ 
                              width: `${(level.count / statistics.totalObjects) * 100}%`,
                              backgroundColor: level.color 
                            }"
                          ></div>
                        </div>
                        <div class="level-percent">
                          {{ Math.round((level.count / statistics.totalObjects) * 100) }}%
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <!-- 部门排名 -->
              <a-col :span="12">
                <a-card title="部门排名" size="small">
                  <div class="department-ranking">
                    <div class="ranking-list">
                      <div 
                        v-for="(dept, index) in statistics.topDepartments" 
                        :key="dept.departmentId"
                        class="ranking-item"
                      >
                        <div class="ranking-number">{{ index + 1 }}</div>
                        <div class="ranking-content">
                          <div class="dept-name">{{ dept.departmentName }}</div>
                          <div class="dept-stats">
                            <span>对象数：{{ dept.objectCount }}</span>
                            <span>完成率：{{ dept.completionRate }}%</span>
                            <span>平均分：{{ dept.averageScore }}</span>
                          </div>
                        </div>
                        <div class="ranking-score">
                          <a-progress 
                            type="circle" 
                            :percent="dept.completionRate" 
                            :width="40"
                            :stroke-color="getRankingColor(index)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <!-- 督办趋势 -->
            <a-row :gutter="24" style="margin-top: 24px;">
              <a-col :span="24">
                <a-card title="督办趋势" size="small">
                  <div class="supervision-trend">
                    <div class="trend-chart">
                      <div class="chart-header">
                        <span>近7天督办情况变化趋势</span>
                        <a-space>
                          <a-tag color="blue">新增对象</a-tag>
                          <a-tag color="green">完成对象</a-tag>
                          <a-tag color="red">逾期对象</a-tag>
                        </a-space>
                      </div>
                      <div class="chart-content">
                        <div class="trend-lines">
                          <div 
                            v-for="(item, index) in statistics.supervisionTrend" 
                            :key="item.date"
                            class="trend-point"
                            :style="{ left: `${(index / Math.max(1, statistics.supervisionTrend.length - 1)) * 100}%` }"
                          >
                            <div class="point-markers">
                              <div class="marker new" :style="{ height: `${item.newObjects * 10}px` }"></div>
                              <div class="marker completed" :style="{ height: `${item.completedObjects * 10}px` }"></div>
                              <div class="marker overdue" :style="{ height: `${item.overdueObjects * 10}px` }"></div>
                            </div>
                            <div class="point-label">{{ item.date.slice(-2) }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 渝快政同步 -->
        <a-tab-pane key="sync" tab="渝快政同步">
          <div class="ykz-sync">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="同步状态" size="small">
                  <div class="sync-status">
                    <a-descriptions :column="1" bordered size="small">
                      <a-descriptions-item label="同步状态">
                        <a-badge :status="syncStatus.status === 'success' ? 'success' : 'error'" />
                        <span>{{ syncStatus.status === 'success' ? '正常' : '异常' }}</span>
                      </a-descriptions-item>
                      <a-descriptions-item label="最后同步">
                        {{ syncStatus.lastSync }}
                      </a-descriptions-item>
                      <a-descriptions-item label="同步成功率">
                        {{ syncStatus.successRate }}%
                      </a-descriptions-item>
                      <a-descriptions-item label="今日同步">
                        {{ syncStatus.todaySync }} 次
                      </a-descriptions-item>
                      <a-descriptions-item label="待同步对象">
                        {{ syncStatus.pendingSync }} 个
                      </a-descriptions-item>
                    </a-descriptions>
                    
                    <div class="sync-actions" style="margin-top: 16px;">
                      <a-space>
                        <a-button type="primary" @click="startFullSync" :loading="syncing">
                          全量同步
                        </a-button>
                        <a-button @click="startIncrementalSync">
                          增量同步
                        </a-button>
                        <a-button @click="viewSyncLogs">
                          同步日志
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="同步配置" size="small">
                  <div class="sync-config">
                    <a-form layout="vertical">
                      <a-form-item label="同步模式">
                        <a-radio-group v-model:value="syncConfig.mode">
                          <a-radio value="auto">自动同步</a-radio>
                          <a-radio value="manual">手动同步</a-radio>
                        </a-radio-group>
                      </a-form-item>
                      
                      <a-form-item label="同步频率" v-if="syncConfig.mode === 'auto'">
                        <a-select v-model:value="syncConfig.frequency" placeholder="选择同步频率">
                          <a-select-option value="realtime">实时同步</a-select-option>
                          <a-select-option value="hourly">每小时</a-select-option>
                          <a-select-option value="daily">每日</a-select-option>
                          <a-select-option value="weekly">每周</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item label="同步范围">
                        <a-checkbox-group v-model:value="syncConfig.scope">
                          <a-checkbox value="basic">基础信息</a-checkbox>
                          <a-checkbox value="evaluation">评分信息</a-checkbox>
                          <a-checkbox value="progress">进度信息</a-checkbox>
                          <a-checkbox value="reports">报告信息</a-checkbox>
                        </a-checkbox-group>
                      </a-form-item>
                      
                      <a-form-item label="失败处理">
                        <a-checkbox v-model:checked="syncConfig.autoRetry">
                          失败自动重试
                        </a-checkbox>
                      </a-form-item>
                      
                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="saveSyncConfig">保存配置</a-button>
                          <a-button @click="resetSyncConfig">重置</a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <!-- 同步记录 -->
            <a-card title="同步记录" size="small" style="margin-top: 24px;">
              <a-table
                :columns="syncRecordColumns"
                :data-source="syncRecords"
                :pagination="{ pageSize: 10 }"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="record.status === 'success' ? 'success' : 'error'" 
                      :text="record.status === 'success' ? '成功' : '失败'"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewSyncDetail(record)">
                        详情
                      </a-button>
                      <a-button 
                        type="link" 
                        size="small" 
                        @click="retrySyncRecord(record)"
                        v-if="record.status === 'failed'"
                      >
                        重试
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 创建/编辑督办对象弹窗 -->
    <a-modal
      v-model:visible="objectModalVisible"
      :title="editingObject ? '编辑督办对象' : '新增督办对象'"
      width="800px"
      @ok="saveObject"
      @cancel="cancelObjectEdit"
    >
      <a-form :model="objectForm" layout="vertical" ref="objectFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="对象名称" name="name" :rules="[{ required: true, message: '请输入对象名称' }]">
              <a-input v-model:value="objectForm.name" placeholder="请输入对象名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="对象类型" name="type" :rules="[{ required: true, message: '请选择对象类型' }]">
              <a-select v-model:value="objectForm.type" placeholder="选择对象类型">
                <a-select-option value="科技型企业">科技型企业</a-select-option>
                <a-select-option value="制造业企业">制造业企业</a-select-option>
                <a-select-option value="服务业企业">服务业企业</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="对象描述" name="description">
          <a-textarea v-model:value="objectForm.description" placeholder="请输入对象描述" :rows="3" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="负责人" name="responsiblePerson">
              <a-input v-model:value="objectForm.responsiblePerson" placeholder="请输入负责人" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="负责部门" name="responsibleDepartment">
              <a-select v-model:value="objectForm.responsibleDepartment" placeholder="选择负责部门">
                <a-select-option value="市科技局">市科技局</a-select-option>
                <a-select-option value="市经信委">市经信委</a-select-option>
                <a-select-option value="市发改委">市发改委</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="联系电话">
              <a-input v-model:value="objectForm.contactInfo.phone" placeholder="联系电话" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="邮箱地址">
              <a-input v-model:value="objectForm.contactInfo.email" placeholder="邮箱地址" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="联系地址">
              <a-input v-model:value="objectForm.contactInfo.address" placeholder="联系地址" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="评分等级">
              <a-select v-model:value="objectForm.evaluationLevel" placeholder="选择评分等级">
                <a-select-option value="A级">A级</a-select-option>
                <a-select-option value="B级">B级</a-select-option>
                <a-select-option value="C级">C级</a-select-option>
                <a-select-option value="D级">D级</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预期完成时间">
              <a-date-picker 
                v-model:value="objectForm.expectedCompletionTime" 
                style="width: 100%"
                placeholder="选择预期完成时间"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="改进计划">
          <a-textarea v-model:value="objectForm.improvementPlan" placeholder="请输入改进计划" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SyncOutlined,
  DownloadOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type {
  SupervisionObject,
  SupervisionStatistics,
  SupervisionStatus,
  SupervisionQueryParams,
  OperationResult
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'

// 响应式数据
const loading = ref(false)
const activeTab = ref('list')

// 搜索和筛选
const searchKeyword = ref('')
const selectedType = ref<string | undefined>()
const selectedStatus = ref<SupervisionStatus | undefined>()
const selectedDepartment = ref<string | undefined>()
const dateRange = ref()

// 督办对象列表
const supervisionList = ref<SupervisionObject[]>([])
const selectedRowKeys = ref<string[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 统计数据
const statistics = reactive<SupervisionStatistics>({
  totalObjects: 0,
  pendingObjects: 0,
  supervisingObjects: 0,
  completedObjects: 0,
  overdueObjects: 0,
  averageCompletionTime: 0,
  completionRate: 0,
  supervisionTrend: [],
  topDepartments: [],
  evaluationDistribution: {}
})

// 评分分布数据
const evaluationDistribution = computed(() => [
  { name: 'A级', count: statistics.evaluationDistribution['A级'] || 0, color: '#52c41a' },
  { name: 'B级', count: statistics.evaluationDistribution['B级'] || 0, color: '#1890ff' },
  { name: 'C级', count: statistics.evaluationDistribution['C级'] || 0, color: '#faad14' },
  { name: 'D级', count: statistics.evaluationDistribution['D级'] || 0, color: '#ff4d4f' }
])

// 督办对象表格列配置
const supervisionColumns = [
  {
    title: '对象名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '负责人',
    dataIndex: 'responsiblePerson',
    key: 'responsiblePerson',
    width: 120
  },
  {
    title: '负责部门',
    dataIndex: 'responsibleDepartment',
    key: 'responsibleDepartment',
    width: 120
  },
  {
    title: '评分',
    dataIndex: 'evaluationScore',
    key: 'evaluationScore',
    width: 120
  },
  {
    title: '督办状态',
    dataIndex: 'supervisionStatus',
    key: 'supervisionStatus',
    width: 120
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 150
  },
  {
    title: '预期完成',
    dataIndex: 'expectedCompletionTime',
    key: 'expectedCompletionTime',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 渝快政同步
const syncing = ref(false)
const syncStatus = reactive({
  status: 'success',
  lastSync: '2025-06-20 10:30:00',
  successRate: 95.5,
  todaySync: 23,
  pendingSync: 5
})

const syncConfig = reactive({
  mode: 'auto',
  frequency: 'daily',
  scope: ['basic', 'evaluation', 'progress'],
  autoRetry: true
})

// 同步记录
const syncRecords = ref([
  {
    id: '1',
    syncTime: '2025-06-20 10:30:00',
    syncType: '全量同步',
    objectCount: 89,
    successCount: 85,
    failedCount: 4,
    status: 'success',
    duration: '2分35秒'
  },
  {
    id: '2',
    syncTime: '2025-06-20 08:15:00',
    syncType: '增量同步',
    objectCount: 12,
    successCount: 12,
    failedCount: 0,
    status: 'success',
    duration: '45秒'
  }
])

// 同步记录表格列配置
const syncRecordColumns = [
  {
    title: '同步时间',
    dataIndex: 'syncTime',
    key: 'syncTime'
  },
  {
    title: '同步类型',
    dataIndex: 'syncType',
    key: 'syncType'
  },
  {
    title: '对象数量',
    dataIndex: 'objectCount',
    key: 'objectCount'
  },
  {
    title: '成功/失败',
    key: 'result',
    customRender: ({ record }: any) => `${record.successCount}/${record.failedCount}`
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '耗时',
    dataIndex: 'duration',
    key: 'duration'
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 对象编辑
const objectModalVisible = ref(false)
const editingObject = ref<SupervisionObject | null>(null)
const objectFormRef = ref()

const objectForm = reactive({
  name: '',
  type: '',
  description: '',
  responsiblePerson: '',
  responsibleDepartment: '',
  contactInfo: {
    phone: '',
    email: '',
    address: ''
  },
  evaluationLevel: '',
  expectedCompletionTime: null,
  improvementPlan: ''
})

// 方法
const loadSupervisionObjects = async () => {
  loading.value = true
  try {
    const result = await MockCultivationWarningService.getSupervisionObjects({
      keyword: searchKeyword.value,
      type: selectedType.value,
      status: selectedStatus.value,
      responsibleDepartment: selectedDepartment.value,
      page: pagination.current,
      pageSize: pagination.pageSize
    })

    supervisionList.value = result.data
    pagination.total = result.total
  } catch (error) {
    console.error('加载督办对象失败:', error)
    message.error('加载督办对象失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const stats = await MockCultivationWarningService.getSupervisionStatistics()
    Object.assign(statistics, stats)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadSupervisionObjects()
}

const handleTypeFilter = () => {
  pagination.current = 1
  loadSupervisionObjects()
}

const handleStatusFilter = () => {
  pagination.current = 1
  loadSupervisionObjects()
}

const handleDepartmentFilter = () => {
  pagination.current = 1
  loadSupervisionObjects()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedType.value = undefined
  selectedStatus.value = undefined
  selectedDepartment.value = undefined
  dateRange.value = undefined
  pagination.current = 1
  loadSupervisionObjects()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadSupervisionObjects()
}

const clearSelection = () => {
  selectedRowKeys.value = []
}

// 督办对象操作
const showCreateModal = () => {
  editingObject.value = null
  resetObjectForm()
  objectModalVisible.value = true
}

const editObject = (object: SupervisionObject) => {
  editingObject.value = object
  Object.assign(objectForm, {
    name: object.name,
    type: object.type,
    description: object.description,
    responsiblePerson: object.responsiblePerson,
    responsibleDepartment: object.responsibleDepartment,
    contactInfo: { ...object.contactInfo },
    evaluationLevel: object.evaluationLevel,
    expectedCompletionTime: object.expectedCompletionTime,
    improvementPlan: object.improvementPlan
  })
  objectModalVisible.value = true
}

const saveObject = async () => {
  try {
    await objectFormRef.value.validate()

    // 模拟保存操作
    const result: OperationResult = {
      success: true,
      message: editingObject.value ? '督办对象更新成功' : '督办对象创建成功',
      timestamp: new Date().toISOString()
    }

    if (result.success) {
      message.success(result.message)
      objectModalVisible.value = false
      loadSupervisionObjects()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('保存督办对象失败:', error)
  }
}

const cancelObjectEdit = () => {
  objectModalVisible.value = false
  resetObjectForm()
}

const resetObjectForm = () => {
  Object.assign(objectForm, {
    name: '',
    type: '',
    description: '',
    responsiblePerson: '',
    responsibleDepartment: '',
    contactInfo: {
      phone: '',
      email: '',
      address: ''
    },
    evaluationLevel: '',
    expectedCompletionTime: null,
    improvementPlan: ''
  })
}

const viewObjectDetail = (object: SupervisionObject) => {
  message.info(`查看督办对象详情：${object.name}`)
}

const viewProgress = (object: SupervisionObject) => {
  message.info(`查看督办进度：${object.name}`)
}

const updateStatus = (object: SupervisionObject) => {
  message.info(`更新督办状态：${object.name}`)
}

const addProgressReport = (object: SupervisionObject) => {
  message.info(`添加进度报告：${object.name}`)
}

const syncObject = (object: SupervisionObject) => {
  message.success(`同步督办对象：${object.name}`)
}

const deleteObject = (object: SupervisionObject) => {
  message.success(`删除督办对象：${object.name}`)
  loadSupervisionObjects()
}

// 批量操作
const batchUpdateStatus = () => {
  message.success(`批量更新 ${selectedRowKeys.value.length} 个对象状态`)
  clearSelection()
}

const batchExport = () => {
  message.success(`批量导出 ${selectedRowKeys.value.length} 个对象数据`)
}

const batchSync = () => {
  message.success(`批量同步 ${selectedRowKeys.value.length} 个对象`)
  clearSelection()
}

// 渝快政同步操作
const syncToYkz = () => {
  message.success('开始同步数据到渝快政')
}

const startFullSync = () => {
  syncing.value = true
  setTimeout(() => {
    syncing.value = false
    message.success('全量同步完成')
    syncStatus.lastSync = new Date().toISOString().slice(0, 19).replace('T', ' ')
    syncStatus.todaySync += 1
  }, 3000)
}

const startIncrementalSync = () => {
  message.success('增量同步完成')
  syncStatus.lastSync = new Date().toISOString().slice(0, 19).replace('T', ' ')
  syncStatus.todaySync += 1
}

const viewSyncLogs = () => {
  message.info('查看同步日志')
}

const saveSyncConfig = () => {
  message.success('同步配置保存成功')
}

const resetSyncConfig = () => {
  Object.assign(syncConfig, {
    mode: 'auto',
    frequency: 'daily',
    scope: ['basic', 'evaluation', 'progress'],
    autoRetry: true
  })
  message.info('同步配置已重置')
}

const viewSyncDetail = (record: any) => {
  message.info(`查看同步详情：${record.syncTime}`)
}

const retrySyncRecord = (record: any) => {
  message.success(`重试同步：${record.syncTime}`)
}

// 其他操作
const exportData = () => {
  message.success('导出督办数据')
}

// 工具方法
const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getStatusBadge = (status: SupervisionStatus) => {
  const badges = {
    pending: 'warning',
    supervising: 'processing',
    completed: 'success',
    overdue: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: SupervisionStatus) => {
  const texts = {
    pending: '待督办',
    supervising: '督办中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return texts[status] || status
}

const getProgressPercent = (object: SupervisionObject) => {
  if (object.progressReports && object.progressReports.length > 0) {
    return object.progressReports[object.progressReports.length - 1].progress
  }
  return 0
}

const getProgressColor = (percent: number) => {
  if (percent >= 90) return '#52c41a'
  if (percent >= 70) return '#1890ff'
  if (percent >= 50) return '#faad14'
  return '#ff4d4f'
}

const getProgressText = (object: SupervisionObject) => {
  const percent = getProgressPercent(object)
  if (object.supervisionStatus === 'completed') return '已完成'
  if (object.supervisionStatus === 'overdue') return '已逾期'
  return `${percent}%`
}

const getRankingColor = (index: number) => {
  const colors = ['#f5222d', '#fa541c', '#faad14', '#52c41a', '#1890ff']
  return colors[index] || '#d9d9d9'
}

// 组件挂载时加载数据
onMounted(() => {
  loadSupervisionObjects()
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.supervision-management {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .supervision-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .batch-actions {
    margin-bottom: 16px;
  }

  .supervision-table {
    .object-name {
      .object-type {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }

    .score-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .score-level {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    .progress-display {
      .progress-text {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
        text-align: center;
      }
    }
  }

  .multi-analysis {
    .score-distribution {
      .distribution-chart {
        .level-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          gap: 12px;

          .level-header {
            width: 80px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .level-name {
              font-weight: 600;
              color: #262626;
            }

            .level-count {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .level-bar {
            flex: 1;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            position: relative;

            .bar-fill {
              height: 100%;
              border-radius: 10px;
              transition: width 0.3s ease;
            }
          }

          .level-percent {
            width: 50px;
            text-align: right;
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .department-ranking {
      .ranking-list {
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          gap: 16px;

          &:last-child {
            border-bottom: none;
          }

          .ranking-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #8c8c8c;

            &:nth-child(1) {
              background: #f5222d;
              color: white;
            }

            &:nth-child(2) {
              background: #fa541c;
              color: white;
            }

            &:nth-child(3) {
              background: #faad14;
              color: white;
            }
          }

          .ranking-content {
            flex: 1;

            .dept-name {
              font-weight: 600;
              color: #262626;
              margin-bottom: 4px;
            }

            .dept-stats {
              display: flex;
              gap: 16px;
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .ranking-score {
            width: 50px;
          }
        }
      }
    }

    .supervision-trend {
      .trend-chart {
        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
        }

        .chart-content {
          height: 200px;
          position: relative;
          background: linear-gradient(to bottom, #f0f2f5 0%, #ffffff 100%);
          border-radius: 6px;
          padding: 20px;

          .trend-lines {
            position: relative;
            height: 100%;

            .trend-point {
              position: absolute;
              bottom: 0;
              width: 20px;

              .point-markers {
                display: flex;
                align-items: end;
                height: 100%;
                gap: 2px;

                .marker {
                  width: 4px;
                  border-radius: 2px;
                  transition: all 0.3s ease;

                  &.new {
                    background: #1890ff;
                  }

                  &.completed {
                    background: #52c41a;
                  }

                  &.overdue {
                    background: #ff4d4f;
                  }

                  &:hover {
                    width: 6px;
                  }
                }
              }

              .point-label {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 10px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }

  .ykz-sync {
    .sync-status {
      .sync-actions {
        text-align: center;
      }
    }

    .sync-config {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .supervision-overview {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .search-filters {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .multi-analysis,
    .ykz-sync {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .score-distribution {
      .distribution-chart {
        .level-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .level-header {
            width: 100%;
          }

          .level-bar {
            width: 100%;
          }

          .level-percent {
            width: auto;
          }
        }
      }
    }

    .department-ranking {
      .ranking-list {
        .ranking-item {
          .dept-stats {
            flex-direction: column;
            gap: 4px;
          }
        }
      }
    }
  }
}
</style>
