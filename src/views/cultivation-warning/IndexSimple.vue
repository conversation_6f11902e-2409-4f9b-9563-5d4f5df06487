<template>
  <div class="cultivation-warning">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>培育过程预警</h1>
        <p>智能化培育过程监控与预警管理平台</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="预警总数" :value="stats.totalAlerts" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="严重预警" :value="stats.criticalAlerts" :value-style="{ color: '#ff4d4f' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="今日预警" :value="stats.todayAlerts" :value-style="{ color: '#52c41a' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="一般预警" :value="stats.generalAlerts" :value-style="{ color: '#1890ff' }" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToConfigManagement">
              <div class="action-icon">
                <setting-outlined />
              </div>
              <div class="action-content">
                <h3>预警配置</h3>
                <p>管理预警规则和阈值</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToDataCollection">
              <div class="action-icon">
                <database-outlined />
              </div>
              <div class="action-content">
                <h3>数据采集</h3>
                <p>监控数据采集状态</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToAlertTrigger">
              <div class="action-icon">
                <alert-outlined />
              </div>
              <div class="action-content">
                <h3>预警触发</h3>
                <p>查看预警触发情况</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToSupervision">
              <div class="action-icon">
                <eye-outlined />
              </div>
              <div class="action-content">
                <h3>督办管理</h3>
                <p>管理督办对象和进度</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 预警趋势图表 -->
    <div class="alert-trends">
      <a-card title="预警趋势" :bordered="false">
        <div class="trend-chart">
          <div class="chart-placeholder">
            <p>预警趋势图表区域</p>
            <p>显示最近7天的预警数据变化</p>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 最近预警动态 -->
    <div class="recent-alerts">
      <a-card title="最近预警动态" :bordered="false">
        <template #extra>
          <a @click="goToAlertTrigger">查看全部</a>
        </template>
        
        <div class="alert-list">
          <div v-for="alert in recentAlerts" :key="alert.id" class="alert-item">
            <div class="alert-level" :class="alert.level">
              <alert-outlined />
            </div>
            <div class="alert-content">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-description">{{ alert.description }}</div>
              <div class="alert-time">{{ alert.createTime }}</div>
            </div>
            <div class="alert-actions">
              <a-button size="small" type="primary">处理</a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  SettingOutlined,
  DatabaseOutlined,
  AlertOutlined,
  EyeOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const stats = reactive({
  totalAlerts: 156,
  criticalAlerts: 12,
  todayAlerts: 8,
  generalAlerts: 89
})

const recentAlerts = ref([
  {
    id: '1',
    title: '培育对象评分异常',
    description: '市人大',
    level: 'critical',
    createTime: '2025-06-20 10:30:00'
  },
  {
    id: '2',
    title: '数据采集延迟',
    description: '部分数据源采集出现延迟',
    level: 'major',
    createTime: '2025-06-20 09:15:00'
  },
  {
    id: '3',
    title: '督办任务逾期',
    description: '3个督办任务即将逾期',
    level: 'general',
    createTime: '2025-06-20 08:45:00'
  }
])

// 导航方法
const goToConfigManagement = () => {
  router.push('/cultivation-warning/config')
}

const goToDataCollection = () => {
  router.push('/cultivation-warning/data-collection')
}

const goToAlertTrigger = () => {
  router.push('/cultivation-warning/alert-trigger')
}

const goToSupervision = () => {
  router.push('/cultivation-warning/supervision')
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('培育过程预警首页加载完成')
})
</script>

<style lang="scss" scoped>
.cultivation-warning {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      margin-bottom: 24px;

      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    .action-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #1890ff;
      }

      .action-icon {
        font-size: 32px;
        color: #1890ff;
        margin-right: 16px;
      }

      .action-content {
        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .alert-trends {
    margin-bottom: 24px;

    .trend-chart {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-placeholder {
        text-align: center;
        color: #8c8c8c;
      }
    }
  }

  .recent-alerts {
    .alert-list {
      .alert-item {
        display: flex;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .alert-level {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 18px;
          color: white;

          &.critical {
            background-color: #ff4d4f;
          }

          &.major {
            background-color: #faad14;
          }

          &.general {
            background-color: #1890ff;
          }
        }

        .alert-content {
          flex: 1;

          .alert-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
          }

          .alert-description {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 4px;
          }

          .alert-time {
            color: #bfbfbf;
            font-size: 12px;
          }
        }

        .alert-actions {
          margin-left: 16px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      padding: 16px;

      .header-stats {
        .ant-row {
          flex-direction: column;

          .ant-col {
            width: 100%;
            margin-bottom: 16px;
          }
        }
      }
    }

    .quick-actions {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
        }
      }
    }
  }
}
</style>
