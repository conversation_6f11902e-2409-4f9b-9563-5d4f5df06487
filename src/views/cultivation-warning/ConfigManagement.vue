<template>
  <div class="config-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警配置管理</h1>
        <p>管理预警规则、模板设置和案例训练</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <plus-outlined />
            新建规则
          </a-button>
          <a-button @click="handleImport">
            <upload-outlined />
            导入规则
          </a-button>
          <a-button @click="handleExport">
            <download-outlined />
            导出规则
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 预警规则管理 -->
        <a-tab-pane key="rules" tab="预警规则管理">
          <div class="rules-management">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索规则名称或描述"
                    allow-clear
                    @change="handleSearch"
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedLevel"
                    placeholder="预警级别"
                    allow-clear
                    @change="handleLevelFilter"
                  >
                    <a-select-option value="critical">严重</a-select-option>
                    <a-select-option value="major">重要</a-select-option>
                    <a-select-option value="general">一般</a-select-option>
                    <a-select-option value="info">信息</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="启用状态"
                    allow-clear
                    @change="handleStatusFilter"
                  >
                    <a-select-option :value="true">已启用</a-select-option>
                    <a-select-option :value="false">已禁用</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-space>
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadRules">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 批量操作 -->
            <div class="batch-actions" v-if="selectedRowKeys.length > 0">
              <a-alert
                :message="`已选择 ${selectedRowKeys.length} 项`"
                type="info"
                show-icon
                closable
                @close="clearSelection"
              >
                <template #action>
                  <a-space>
                    <a-button size="small" @click="batchEnable">批量启用</a-button>
                    <a-button size="small" @click="batchDisable">批量禁用</a-button>
                    <a-button size="small" danger @click="batchDelete">批量删除</a-button>
                  </a-space>
                </template>
              </a-alert>
            </div>

            <!-- 规则列表 -->
            <div class="rules-table">
              <a-table
                :columns="rulesColumns"
                :data-source="rulesList"
                :loading="loading"
                :pagination="pagination"
                :row-selection="rowSelection"
                row-key="id"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="rule-name">
                      <a @click="viewRuleDetail(record)">{{ record.name }}</a>
                      <div class="rule-description">{{ record.description }}</div>
                    </div>
                  </template>

                  <template v-if="column.key === 'level'">
                    <a-tag :color="getLevelColor(record.level)">
                      {{ getLevelText(record.level) }}
                    </a-tag>
                  </template>

                  <template v-if="column.key === 'isEnabled'">
                    <a-switch
                      :checked="record.isEnabled"
                      :loading="record.switching"
                      @change="(checked) => toggleRuleStatus(record, checked)"
                    />
                  </template>

                  <template v-if="column.key === 'triggerCount'">
                    <a-statistic
                      :value="record.triggerCount"
                      :value-style="{ fontSize: '14px' }"
                    />
                  </template>

                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editRule(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="testRule(record)">
                        测试
                      </a-button>
                      <a-button type="link" size="small" @click="copyRule(record)">
                        复制
                      </a-button>
                      <a-popconfirm
                        title="确定要删除这个预警规则吗？"
                        @confirm="deleteRule(record)"
                      >
                        <a-button type="link" size="small" danger>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 预警模板设置 -->
        <a-tab-pane key="templates" tab="预警模板设置">
          <div class="templates-management">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-card title="模板列表" size="small">
                  <template #extra>
                    <a-button type="link" size="small" @click="createTemplate">
                      新建模板
                    </a-button>
                  </template>

                  <div class="template-list">
                    <div
                      v-for="template in templateList"
                      :key="template.id"
                      class="template-item"
                      :class="{ active: selectedTemplate?.id === template.id }"
                      @click="selectTemplate(template)"
                    >
                      <div class="template-name">{{ template.name }}</div>
                      <div class="template-type">{{ template.type }}</div>
                    </div>
                  </div>
                </a-card>
              </a-col>

              <a-col :span="16">
                <a-card title="模板配置" size="small" v-if="selectedTemplate">
                  <template #extra>
                    <a-space>
                      <a-button @click="previewTemplate">预览</a-button>
                      <a-button type="primary" @click="saveTemplate">保存</a-button>
                    </a-space>
                  </template>

                  <a-form :model="templateForm" layout="vertical">
                    <a-form-item label="模板名称" required>
                      <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
                    </a-form-item>

                    <a-form-item label="模板类型">
                      <a-select v-model:value="templateForm.type" placeholder="选择模板类型">
                        <a-select-option value="basic">基础预警模板</a-select-option>
                        <a-select-option value="deadline">关键节点到期模板</a-select-option>
                        <a-select-option value="supervision">监督报告专用模板</a-select-option>
                      </a-select>
                    </a-form-item>

                    <a-form-item label="模板内容">
                      <a-textarea
                        v-model:value="templateForm.content"
                        placeholder="请输入模板内容，支持变量：{objectName}, {score}, {level}, {time}"
                        :rows="8"
                      />
                    </a-form-item>

                    <a-form-item label="通知方式">
                      <a-checkbox-group v-model:value="templateForm.notificationMethods">
                        <a-checkbox value="system">系统内通知</a-checkbox>
                        <a-checkbox value="email">邮件</a-checkbox>
                        <a-checkbox value="sms">短信</a-checkbox>
                        <a-checkbox value="ykz">渝快政</a-checkbox>
                        <a-checkbox value="wechat">微信</a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                  </a-form>
                </a-card>

                <a-empty v-else description="请选择一个模板进行配置" />
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 案例训练 -->
        <a-tab-pane key="training" tab="案例训练">
          <div class="training-management">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="历史案例" size="small">
                  <template #extra>
                    <a-button type="link" size="small" @click="importCases">
                      导入案例
                    </a-button>
                  </template>

                  <div class="case-list">
                    <div
                      v-for="case_ in caseList"
                      :key="case_.id"
                      class="case-item"
                      @click="selectCase(case_)"
                    >
                      <div class="case-title">{{ case_.title }}</div>
                      <div class="case-meta">
                        <a-tag :color="getLevelColor(case_.level)">{{ getLevelText(case_.level) }}</a-tag>
                        <span class="case-date">{{ case_.date }}</span>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>

              <a-col :span="12">
                <a-card title="模型训练" size="small">
                  <div class="training-config">
                    <a-form layout="vertical">
                      <a-form-item label="训练数据集">
                        <a-select v-model:value="trainingConfig.dataset" placeholder="选择训练数据集">
                          <a-select-option value="all">全部案例</a-select-option>
                          <a-select-option value="recent">最近6个月</a-select-option>
                          <a-select-option value="critical">严重预警案例</a-select-option>
                        </a-select>
                      </a-form-item>

                      <a-form-item label="训练算法">
                        <a-select v-model:value="trainingConfig.algorithm" placeholder="选择训练算法">
                          <a-select-option value="decision_tree">决策树</a-select-option>
                          <a-select-option value="random_forest">随机森林</a-select-option>
                          <a-select-option value="neural_network">神经网络</a-select-option>
                        </a-select>
                      </a-form-item>

                      <a-form-item label="训练参数">
                        <a-row :gutter="16">
                          <a-col :span="12">
                            <a-input-number
                              v-model:value="trainingConfig.epochs"
                              placeholder="训练轮数"
                              :min="1"
                              :max="1000"
                              style="width: 100%"
                            />
                          </a-col>
                          <a-col :span="12">
                            <a-input-number
                              v-model:value="trainingConfig.learningRate"
                              placeholder="学习率"
                              :min="0.001"
                              :max="1"
                              :step="0.001"
                              style="width: 100%"
                            />
                          </a-col>
                        </a-row>
                      </a-form-item>

                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="startTraining" :loading="training">
                            开始训练
                          </a-button>
                          <a-button @click="validateModel">验证模型</a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>

                    <!-- 训练进度 -->
                    <div v-if="training" class="training-progress">
                      <a-progress :percent="trainingProgress" />
                      <p>训练进度：{{ trainingProgress }}%</p>
                    </div>

                    <!-- 训练结果 -->
                    <div v-if="trainingResult" class="training-result">
                      <a-descriptions title="训练结果" :column="2" bordered size="small">
                        <a-descriptions-item label="准确率">{{ trainingResult.accuracy }}%</a-descriptions-item>
                        <a-descriptions-item label="召回率">{{ trainingResult.recall }}%</a-descriptions-item>
                        <a-descriptions-item label="F1分数">{{ trainingResult.f1Score }}</a-descriptions-item>
                        <a-descriptions-item label="训练时间">{{ trainingResult.duration }}s</a-descriptions-item>
                      </a-descriptions>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 创建/编辑规则弹窗 -->
    <a-modal
      v-model:visible="ruleModalVisible"
      :title="editingRule ? '编辑预警规则' : '新建预警规则'"
      width="800px"
      @ok="saveRule"
      @cancel="cancelRuleEdit"
    >
      <a-form :model="ruleForm" layout="vertical" ref="ruleFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="规则名称" name="name" :rules="[{ required: true, message: '请输入规则名称' }]">
              <a-input v-model:value="ruleForm.name" placeholder="请输入规则名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预警级别" name="level" :rules="[{ required: true, message: '请选择预警级别' }]">
              <a-select v-model:value="ruleForm.level" placeholder="选择预警级别">
                <a-select-option value="critical">严重</a-select-option>
                <a-select-option value="major">重要</a-select-option>
                <a-select-option value="general">一般</a-select-option>
                <a-select-option value="info">信息</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="规则描述" name="description">
          <a-textarea v-model:value="ruleForm.description" placeholder="请输入规则描述" :rows="3" />
        </a-form-item>

        <a-form-item label="触发条件">
          <div class="trigger-conditions">
            <div
              v-for="(condition, index) in ruleForm.triggerConditions"
              :key="index"
              class="condition-item"
            >
              <a-row :gutter="8" align="middle">
                <a-col :span="6">
                  <a-select v-model:value="condition.field" placeholder="选择字段">
                    <a-select-option value="evaluationScore">评分</a-select-option>
                    <a-select-option value="completionRate">完成率</a-select-option>
                    <a-select-option value="dataQuality">数据质量</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="condition.operator" placeholder="操作符">
                    <a-select-option value="gt">大于</a-select-option>
                    <a-select-option value="gte">大于等于</a-select-option>
                    <a-select-option value="lt">小于</a-select-option>
                    <a-select-option value="lte">小于等于</a-select-option>
                    <a-select-option value="eq">等于</a-select-option>
                    <a-select-option value="neq">不等于</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-input-number v-model:value="condition.value" placeholder="阈值" style="width: 100%" />
                </a-col>
                <a-col :span="4">
                  <a-select v-model:value="condition.logicalOperator" placeholder="逻辑" v-if="index < ruleForm.triggerConditions.length - 1">
                    <a-select-option value="and">且</a-select-option>
                    <a-select-option value="or">或</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-space>
                    <a-button type="link" size="small" @click="addCondition" v-if="index === ruleForm.triggerConditions.length - 1">
                      <plus-outlined />
                    </a-button>
                    <a-button type="link" size="small" danger @click="removeCondition(index)" v-if="ruleForm.triggerConditions.length > 1">
                      <minus-outlined />
                    </a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="通知方式">
          <a-checkbox-group v-model:value="ruleForm.notificationMethods">
            <a-checkbox value="system">系统内通知</a-checkbox>
            <a-checkbox value="email">邮件</a-checkbox>
            <a-checkbox value="sms">短信</a-checkbox>
            <a-checkbox value="ykz">渝快政</a-checkbox>
            <a-checkbox value="wechat">微信</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="目标用户">
              <a-select v-model:value="ruleForm.targetUsers" mode="multiple" placeholder="选择目标用户">
                <a-select-option value="admin">管理员</a-select-option>
                <a-select-option value="supervisor">督办员</a-select-option>
                <a-select-option value="tech_admin">技术管理员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="冷却期(分钟)">
              <a-input-number v-model:value="ruleForm.cooldownPeriod" :min="1" :max="1440" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-checkbox v-model:checked="ruleForm.isEnabled">启用规则</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 规则测试弹窗 -->
    <a-modal
      v-model:visible="testModalVisible"
      title="预警规则测试"
      width="600px"
      @ok="runTest"
      @cancel="cancelTest"
    >
      <div class="rule-test">
        <a-form layout="vertical">
          <a-form-item label="测试数据">
            <a-textarea
              v-model:value="testData"
              placeholder="请输入JSON格式的测试数据"
              :rows="8"
            />
          </a-form-item>
        </a-form>

        <div v-if="testResult" class="test-result">
          <a-divider>测试结果</a-divider>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="是否触发">
              <a-tag :color="testResult.triggered ? 'red' : 'green'">
                {{ testResult.triggered ? '是' : '否' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="匹配条件" v-if="testResult.triggered">
              {{ testResult.matchedConditions.join(', ') }}
            </a-descriptions-item>
            <a-descriptions-item label="预警消息" v-if="testResult.triggered">
              {{ testResult.message }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  SearchOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import type {
  AlertRule,
  AlertLevel,
  NotificationMethod,
  TriggerCondition,
  OperationResult
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'

// 响应式数据
const loading = ref(false)
const activeTab = ref('rules')

// 预警规则管理
const searchKeyword = ref('')
const selectedLevel = ref<AlertLevel | undefined>()
const selectedStatus = ref<boolean | undefined>()
const rulesList = ref<AlertRule[]>([])
const selectedRowKeys = ref<string[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 规则表格列配置
const rulesColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    width: 300
  },
  {
    title: '预警级别',
    dataIndex: 'level',
    key: 'level',
    width: 100
  },
  {
    title: '启用状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 100
  },
  {
    title: '触发次数',
    dataIndex: 'triggerCount',
    key: 'triggerCount',
    width: 100
  },
  {
    title: '最后触发',
    dataIndex: 'lastTriggeredTime',
    key: 'lastTriggeredTime',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 规则编辑
const ruleModalVisible = ref(false)
const editingRule = ref<AlertRule | null>(null)
const ruleFormRef = ref()

const ruleForm = reactive({
  name: '',
  description: '',
  level: 'general' as AlertLevel,
  isEnabled: true,
  triggerConditions: [
    {
      id: '',
      field: '',
      operator: 'gt' as any,
      value: 0,
      logicalOperator: 'and' as any
    }
  ] as TriggerCondition[],
  notificationMethods: [] as NotificationMethod[],
  targetUsers: [] as string[],
  targetRoles: [] as string[],
  cooldownPeriod: 60
})

// 模板管理
const templateList = ref([
  { id: '1', name: '基础预警模板', type: '基础模板' },
  { id: '2', name: '关键节点到期模板', type: '节点模板' },
  { id: '3', name: '监督报告模板', type: '报告模板' }
])

const selectedTemplate = ref<any>(null)
const templateForm = reactive({
  name: '',
  type: 'basic',
  content: '',
  notificationMethods: [] as string[]
})

// 案例训练
const caseList = ref([
  { id: '1', title: '培育对象评分异常案例', level: 'critical' as AlertLevel, date: '2025-06-15' },
  { id: '2', title: '数据采集失败案例', level: 'major' as AlertLevel, date: '2025-06-14' },
  { id: '3', title: '督办逾期案例', level: 'general' as AlertLevel, date: '2025-06-13' }
])

const trainingConfig = reactive({
  dataset: 'all',
  algorithm: 'decision_tree',
  epochs: 100,
  learningRate: 0.01
})

const training = ref(false)
const trainingProgress = ref(0)
const trainingResult = ref<any>(null)

// 规则测试
const testModalVisible = ref(false)
const testingRule = ref<AlertRule | null>(null)
const testData = ref('')
const testResult = ref<any>(null)

// 计算属性
const filteredRules = computed(() => {
  let filtered = [...rulesList.value]

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(rule =>
      rule.name.toLowerCase().includes(keyword) ||
      rule.description.toLowerCase().includes(keyword)
    )
  }

  if (selectedLevel.value) {
    filtered = filtered.filter(rule => rule.level === selectedLevel.value)
  }

  if (selectedStatus.value !== undefined) {
    filtered = filtered.filter(rule => rule.isEnabled === selectedStatus.value)
  }

  return filtered
})

// 方法
const loadRules = async () => {
  loading.value = true
  try {
    const result = await MockCultivationWarningService.getAlertRules({
      keyword: searchKeyword.value,
      level: selectedLevel.value,
      page: pagination.current,
      pageSize: pagination.pageSize
    })

    rulesList.value = result.data
    pagination.total = result.total
  } catch (error) {
    console.error('加载预警规则失败:', error)
    message.error('加载预警规则失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadRules()
}

const handleLevelFilter = () => {
  pagination.current = 1
  loadRules()
}

const handleStatusFilter = () => {
  pagination.current = 1
  loadRules()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedLevel.value = undefined
  selectedStatus.value = undefined
  pagination.current = 1
  loadRules()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadRules()
}

const clearSelection = () => {
  selectedRowKeys.value = []
}

// 规则操作
const showCreateModal = () => {
  editingRule.value = null
  resetRuleForm()
  ruleModalVisible.value = true
}

const editRule = (rule: AlertRule) => {
  editingRule.value = rule
  Object.assign(ruleForm, {
    name: rule.name,
    description: rule.description,
    level: rule.level,
    isEnabled: rule.isEnabled,
    triggerConditions: [...rule.triggerConditions],
    notificationMethods: [...rule.notificationMethods],
    targetUsers: [...rule.targetUsers],
    targetRoles: [...rule.targetRoles],
    cooldownPeriod: rule.cooldownPeriod
  })
  ruleModalVisible.value = true
}

const saveRule = async () => {
  try {
    await ruleFormRef.value.validate()

    const ruleData = {
      ...ruleForm,
      triggerConditions: ruleForm.triggerConditions.map((condition, index) => ({
        ...condition,
        id: `condition_${index + 1}`
      }))
    }

    let result: OperationResult
    if (editingRule.value) {
      result = await MockCultivationWarningService.updateAlertRule(editingRule.value.id, ruleData)
    } else {
      result = await MockCultivationWarningService.createAlertRule(ruleData)
    }

    if (result.success) {
      message.success(result.message)
      ruleModalVisible.value = false
      loadRules()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('保存规则失败:', error)
  }
}

const cancelRuleEdit = () => {
  ruleModalVisible.value = false
  resetRuleForm()
}

const resetRuleForm = () => {
  Object.assign(ruleForm, {
    name: '',
    description: '',
    level: 'general' as AlertLevel,
    isEnabled: true,
    triggerConditions: [
      {
        id: '',
        field: '',
        operator: 'gt' as any,
        value: 0,
        logicalOperator: 'and' as any
      }
    ],
    notificationMethods: [],
    targetUsers: [],
    targetRoles: [],
    cooldownPeriod: 60
  })
}

const deleteRule = async (rule: AlertRule) => {
  try {
    const result = await MockCultivationWarningService.deleteAlertRule(rule.id)
    if (result.success) {
      message.success(result.message)
      loadRules()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('删除规则失败:', error)
    message.error('删除规则失败')
  }
}

const toggleRuleStatus = async (rule: AlertRule, checked: boolean) => {
  rule.switching = true
  try {
    const result = await MockCultivationWarningService.updateAlertRule(rule.id, { isEnabled: checked })
    if (result.success) {
      rule.isEnabled = checked
      message.success(`规则已${checked ? '启用' : '禁用'}`)
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('切换规则状态失败:', error)
    message.error('操作失败')
  } finally {
    rule.switching = false
  }
}

const copyRule = (rule: AlertRule) => {
  editingRule.value = null
  Object.assign(ruleForm, {
    name: `${rule.name} - 副本`,
    description: rule.description,
    level: rule.level,
    isEnabled: false,
    triggerConditions: [...rule.triggerConditions],
    notificationMethods: [...rule.notificationMethods],
    targetUsers: [...rule.targetUsers],
    targetRoles: [...rule.targetRoles],
    cooldownPeriod: rule.cooldownPeriod
  })
  ruleModalVisible.value = true
}

const testRule = (rule: AlertRule) => {
  testingRule.value = rule
  testData.value = JSON.stringify({
    evaluationScore: 45,
    completionRate: 0.6,
    dataQuality: 0.8
  }, null, 2)
  testResult.value = null
  testModalVisible.value = true
}

const runTest = () => {
  try {
    const data = JSON.parse(testData.value)
    // 模拟测试逻辑
    const triggered = data.evaluationScore < 60
    testResult.value = {
      triggered,
      matchedConditions: triggered ? ['评分 < 60'] : [],
      message: triggered ? '培育对象评分异常，需要立即关注' : '未触发预警条件'
    }
  } catch (error) {
    message.error('测试数据格式错误')
  }
}

const cancelTest = () => {
  testModalVisible.value = false
  testingRule.value = null
  testData.value = ''
  testResult.value = null
}

// 批量操作
const batchEnable = async () => {
  try {
    const result = await MockCultivationWarningService.batchOperateAlertRules(selectedRowKeys.value, 'enable')
    if (result.success) {
      message.success(result.message)
      clearSelection()
      loadRules()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('批量启用失败:', error)
    message.error('批量启用失败')
  }
}

const batchDisable = async () => {
  try {
    const result = await MockCultivationWarningService.batchOperateAlertRules(selectedRowKeys.value, 'disable')
    if (result.success) {
      message.success(result.message)
      clearSelection()
      loadRules()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('批量禁用失败:', error)
    message.error('批量禁用失败')
  }
}

const batchDelete = async () => {
  try {
    const result = await MockCultivationWarningService.batchOperateAlertRules(selectedRowKeys.value, 'delete')
    if (result.success) {
      message.success(result.message)
      clearSelection()
      loadRules()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('批量删除失败:', error)
    message.error('批量删除失败')
  }
}

// 触发条件操作
const addCondition = () => {
  ruleForm.triggerConditions.push({
    id: '',
    field: '',
    operator: 'gt' as any,
    value: 0,
    logicalOperator: 'and' as any
  })
}

const removeCondition = (index: number) => {
  ruleForm.triggerConditions.splice(index, 1)
}

// 模板操作
const selectTemplate = (template: any) => {
  selectedTemplate.value = template
  Object.assign(templateForm, {
    name: template.name,
    type: template.type,
    content: template.content || '',
    notificationMethods: template.notificationMethods || []
  })
}

const createTemplate = () => {
  selectedTemplate.value = { id: 'new', name: '新模板', type: '基础模板' }
  Object.assign(templateForm, {
    name: '',
    type: 'basic',
    content: '',
    notificationMethods: []
  })
}

const saveTemplate = () => {
  message.success('模板保存成功')
}

const previewTemplate = () => {
  message.info('模板预览功能')
}

// 案例训练
const selectCase = (case_: any) => {
  message.info(`选择案例：${case_.title}`)
}

const importCases = () => {
  message.info('导入案例功能')
}

const startTraining = () => {
  training.value = true
  trainingProgress.value = 0

  const interval = setInterval(() => {
    trainingProgress.value += 10
    if (trainingProgress.value >= 100) {
      clearInterval(interval)
      training.value = false
      trainingResult.value = {
        accuracy: 92.5,
        recall: 88.3,
        f1Score: 0.905,
        duration: 45
      }
      message.success('模型训练完成')
    }
  }, 500)
}

const validateModel = () => {
  message.info('模型验证功能')
}

// 导入导出
const handleImport = () => {
  message.info('导入规则功能')
}

const handleExport = () => {
  message.info('导出规则功能')
}

const viewRuleDetail = (rule: AlertRule) => {
  message.info(`查看规则详情：${rule.name}`)
}

// 工具方法
const getLevelColor = (level: AlertLevel) => {
  const colors = {
    critical: 'red',
    major: 'orange',
    general: 'blue',
    info: 'green'
  }
  return colors[level] || 'default'
}

const getLevelText = (level: AlertLevel) => {
  const texts = {
    critical: '严重',
    major: '重要',
    general: '一般',
    info: '信息'
  }
  return texts[level] || level
}

// 组件挂载时加载数据
onMounted(() => {
  loadRules()
})
</script>

<style lang="scss" scoped>
.config-management {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .search-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .batch-actions {
    margin-bottom: 16px;
  }

  .rules-table {
    .rule-name {
      .rule-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }
  }

  .templates-management {
    .template-list {
      max-height: 400px;
      overflow-y: auto;

      .template-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          background-color: #f6ffed;
        }

        &.active {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }

        .template-name {
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .template-type {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .training-management {
    .case-list {
      max-height: 400px;
      overflow-y: auto;

      .case-item {
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          background-color: #f6ffed;
        }

        .case-title {
          font-weight: 600;
          color: #262626;
          margin-bottom: 8px;
        }

        .case-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .case-date {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .training-config {
      .training-progress {
        margin-top: 16px;
        padding: 16px;
        background: #f6ffed;
        border-radius: 6px;
      }

      .training-result {
        margin-top: 16px;
      }
    }
  }

  .trigger-conditions {
    .condition-item {
      margin-bottom: 8px;
      padding: 12px;
      background: #fafafa;
      border-radius: 6px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .rule-test {
    .test-result {
      margin-top: 16px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .search-filters {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .templates-management,
    .training-management {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .trigger-conditions {
      .condition-item {
        .ant-row {
          flex-direction: column;

          .ant-col {
            width: 100%;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>