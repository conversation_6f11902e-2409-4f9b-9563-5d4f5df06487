<template>
  <div class="alert-trigger">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警触发与通知</h1>
        <p>监控预警触发情况，管理通知推送和处理流程</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary"  @click="showTriggerAlertModal">
            <plus-outlined />
            手动触发预警
          </a-button>
          <a-button>
            <sync-outlined />
            同步渝快政
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 预警统计概览 -->
    <div class="alert-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="预警总数"
              :value="statistics.totalAlerts"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="严重预警"
              :value="statistics.criticalAlerts"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日触发"
              :value="statistics.todayTriggers"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="通知成功率"
              :value="statistics.notificationSuccessRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 预警监控 -->
        <a-tab-pane key="monitoring" tab="预警监控">
          <div class="alert-monitoring">
            <!-- 预警列表 -->
            <div class="alert-table">
              <a-table
                :columns="alertColumns"
                :data-source="alertList"
                :loading="loading"
                :pagination="pagination"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'title'">
                    <div class="alert-title">
                      <a @click="viewAlertDetail(record)">{{ record.title }}</a>
                      <div class="alert-description">{{ record.description }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'level'">
                    <a-tag :color="getLevelColor(record.level)">
                      {{ getLevelText(record.level) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getStatusBadge(record.status)" 
                      :text="getStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="handleAlert(record)">
                        处理
                      </a-button>
                      <a-button type="link" size="small" @click="resendNotification(record)">
                        重发通知
                      </a-button>
                      <a-button type="link" size="small" @click="viewDetails(record)">
                        详情
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 通知管理 -->
        <a-tab-pane key="notifications" tab="通知管理">
          <div class="notification-management">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="通知渠道状态" size="small">
                  <div class="notification-channels">
                    <div v-for="channel in notificationChannels" :key="channel.id" class="channel-item">
                      <div class="channel-info">
                        <div class="channel-name">{{ channel.name }}</div>
                        <div class="channel-status" :class="channel.status">
                          {{ getChannelStatusText(channel.status) }}
                        </div>
                      </div>
                      <div class="channel-stats">
                        <span>今日发送：{{ channel.todaySent }}</span>
                        <span>成功率：{{ channel.successRate }}%</span>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="通知模板" size="small">
                  <div class="notification-templates">
                    <div v-for="template in notificationTemplates" :key="template.id" class="template-item">
                      <div class="template-header">
                        <span class="template-name">{{ template.name }}</span>
                        <a-tag :color="getLevelColor(template.level)">
                          {{ getLevelText(template.level) }}
                        </a-tag>
                      </div>
                      <div class="template-content">{{ template.content }}</div>
                      <div class="template-actions">
                        <a-button type="link" size="small" @click="editTemplate(template)">编辑</a-button>
                        <a-button type="link" size="small" @click="previewTemplate(template)">预览</a-button>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 渝快政接口 -->
        <a-tab-pane key="ykz" tab="渝快政接口">
          <div class="ykz-interface">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="接口状态" size="small">
                  <a-descriptions :column="1" bordered size="small">
                    <a-descriptions-item label="连接状态">
                      <a-badge :status="ykzStatus.connected ? 'success' : 'error'" />
                      <span>{{ ykzStatus.connected ? '已连接' : '连接失败' }}</span>
                    </a-descriptions-item>
                    <a-descriptions-item label="最后同步">
                      {{ ykzStatus.lastSync }}
                    </a-descriptions-item>
                    <a-descriptions-item label="同步成功率">
                      {{ ykzStatus.syncSuccessRate }}%
                    </a-descriptions-item>
                    <a-descriptions-item label="今日同步">
                      {{ ykzStatus.todaySync }} 次
                    </a-descriptions-item>
                  </a-descriptions>
                  
                  <div class="ykz-actions" style="margin-top: 16px;">
                    <a-space>
                      <a-button type="primary" @click="testConnection">测试连接</a-button>
                      <a-button @click="syncData">同步数据</a-button>
                      <a-button @click="viewLogs">查看日志</a-button>
                    </a-space>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="同步配置" size="small">
                  <a-form layout="vertical">
                    <a-form-item label="同步频率">
                      <a-select v-model:value="ykzConfig.frequency" placeholder="选择同步频率">
                        <a-select-option value="realtime">实时同步</a-select-option>
                        <a-select-option value="hourly">每小时</a-select-option>
                        <a-select-option value="daily">每日</a-select-option>
                      </a-select>
                    </a-form-item>
                    
                    <a-form-item label="同步内容">
                      <a-checkbox-group v-model:value="ykzConfig.syncContent">
                        <a-checkbox value="alerts">预警信息</a-checkbox>
                        <a-checkbox value="notifications">通知记录</a-checkbox>
                        <a-checkbox value="reports">处理报告</a-checkbox>
                      </a-checkbox-group>
                    </a-form-item>
                    
                    <a-form-item>
                      <a-space>
                        <a-button type="primary" @click="saveConfig">保存配置</a-button>
                        <a-button @click="resetConfig">重置</a-button>
                      </a-space>
                    </a-form-item>
                  </a-form>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
        <!-- 手动触发预警弹窗 -->
    <a-modal
      v-model:visible="triggerAlertModalVisible"
      title="手动触发预警"
      @ok="handleTriggerAlerts"
      @cancel="handleTriggerCancel"
      ok-text="一键预警"
      cancel-text="取消"
    >
      <a-form layout="vertical">
        <a-form-item label="选择预警项">
          <a-select
            v-model:value="selectedAlerts"
            mode="multiple"
            placeholder="请选择需要触发的预警项"
            :options="alertOptions"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message ,Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('monitoring')

// 统计数据
const statistics = reactive({
  totalAlerts: 156,
  criticalAlerts: 12,
  todayTriggers: 8,
  notificationSuccessRate: 96
})
// 手动触发预警弹窗相关
const triggerAlertModalVisible = ref(false)
const selectedAlerts = ref([])

// 静态预警项数据
const alertOptions = [
  { value: 'alert_001', label: '培育对象评分异常预警' },
  { value: 'alert_002', label: '数据采集延迟预警' },
  { value: 'alert_003', label: '企业资质过期预警' },
  { value: 'alert_004', label: '培育进度滞后预警' },
  { value: 'alert_005', label: '联系信息变更预警' }
]

// 预警列表
const alertList = ref([
  {
    id: 'alert_001',
    title: '培育对象评分异常',
    description: '企业评分连续下降',
    level: 'critical',
    status: 'pending',
    triggerTime: '2025-06-20 10:30:00',
    targetObject: '数据评分系统'
  },
  {
    id: 'alert_002',
    title: '数据采集延迟',
    description: '部分数据源采集出现延迟',
    level: 'major',
    status: 'processing',
    triggerTime: '2025-06-20 09:15:00',
    targetObject: '数据采集系统'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 预警表格列配置
const alertColumns = [
  {
    title: '预警标题',
    key: 'title',
    width: 250
  },
  {
    title: '预警级别',
    key: 'level',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 120
  },
  {
    title: '触发时间',
    dataIndex: 'triggerTime',
    key: 'triggerTime',
    width: 150
  },
  {
    title: '目标对象',
    dataIndex: 'targetObject',
    key: 'targetObject',
    width: 200
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 通知渠道
const notificationChannels = ref([
  {
    id: 'system',
    name: '系统内通知',
    status: 'online',
    todaySent: 45,
    successRate: 98
  },
  {
    id: 'sms',
    name: '短信通知',
    status: 'online',
    todaySent: 23,
    successRate: 95
  },
  {
    id: 'email',
    name: '邮件通知',
    status: 'online',
    todaySent: 18,
    successRate: 92
  },
  {
    id: 'ykz',
    name: '渝快政通知',
    status: 'offline',
    todaySent: 0,
    successRate: 0
  }
])

// 通知模板
const notificationTemplates = ref([
  {
    id: 'template_001',
    name: '严重预警模板',
    level: 'critical',
    content: '【严重预警】{title}，请立即处理！'
  },
  {
    id: 'template_002',
    name: '一般预警模板',
    level: 'general',
    content: '【预警提醒】{title}，请及时关注。'
  }
])

// 渝快政状态
const ykzStatus = reactive({
  connected: true,
  lastSync: '2025-06-20 10:30:00',
  syncSuccessRate: 95,
  todaySync: 12
})

// 渝快政配置
const ykzConfig = reactive({
  frequency: 'hourly',
  syncContent: ['alerts', 'notifications']
})

// 方法
const viewAlertDetail = (alert: any) => {
  message.info(`查看预警详情：${alert.title}`)
}

const handleAlert = (alert: any) => {
  message.success(`开始处理预警：${alert.title}`)
}

const resendNotification = (alert: any) => {
  message.success(`重发通知：${alert.title}`)
}

const viewDetails = (alert: any) => {
  message.info(`查看详情：${alert.title}`)
}

const editTemplate = (template: any) => {
  message.info(`编辑模板：${template.name}`)
}

const previewTemplate = (template: any) => {
  message.info(`预览模板：${template.name}`)
}

const testConnection = () => {
  message.success('渝快政连接测试成功')
}

const syncData = () => {
  message.success('开始同步数据到渝快政')
}

const viewLogs = () => {
  message.info('查看同步日志')
}

const saveConfig = () => {
  message.success('配置保存成功')
}

const resetConfig = () => {
  Object.assign(ykzConfig, {
    frequency: 'hourly',
    syncContent: ['alerts', 'notifications']
  })
  message.info('配置已重置')
}

// 工具方法
const getLevelColor = (level: string) => {
  const colors = {
    critical: 'red',
    major: 'orange',
    general: 'blue',
    info: 'green'
  }
  return colors[level] || 'default'
}

const getLevelText = (level: string) => {
  const texts = {
    critical: '严重',
    major: '重要',
    general: '一般',
    info: '信息'
  }
  return texts[level] || level
}

const getStatusBadge = (status: string) => {
  const badges = {
    pending: 'warning',
    processing: 'processing',
    completed: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getChannelStatusText = (status: string) => {
  const texts = {
    online: '在线',
    offline: '离线',
    error: '异常'
  }
  return texts[status] || status
}

// 显示手动触发预警弹窗
const showTriggerAlertModal = () => {
  selectedAlerts.value = []
  triggerAlertModalVisible.value = true
}

// 处理一键预警
const handleTriggerAlerts = () => {
  if (selectedAlerts.value.length === 0) {
    message.warning('请至少选择一个预警项')
    return
  }
  
  message.success(`成功触发 ${selectedAlerts.value.length} 个预警项`)
  triggerAlertModalVisible.value = false
}

// 处理取消
const handleTriggerCancel = () => {
  triggerAlertModalVisible.value = false
}

// 组件挂载时加载数据
onMounted(() => {
  pagination.total = alertList.value.length
})
</script>

<style lang="scss" scoped>
.alert-trigger {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .alert-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .alert-monitoring {
    .alert-title {
      .alert-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }
  }

  .notification-management {
    .notification-channels {
      .channel-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .channel-info {
          .channel-name {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
          }

          .channel-status {
            font-size: 12px;

            &.online {
              color: #52c41a;
            }

            &.offline {
              color: #ff4d4f;
            }
          }
        }

        .channel-stats {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          font-size: 12px;
          color: #8c8c8c;

          span {
            margin-bottom: 2px;
          }
        }
      }
    }

    .notification-templates {
      .template-item {
        margin-bottom: 16px;
        padding: 12px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        .template-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .template-name {
            font-weight: 600;
            color: #262626;
          }
        }

        .template-content {
          font-size: 14px;
          color: #8c8c8c;
          margin-bottom: 8px;
        }

        .template-actions {
          text-align: right;
        }
      }
    }
  }

  .ykz-interface {
    .ykz-actions {
      text-align: center;
    }
  }
}
</style>
