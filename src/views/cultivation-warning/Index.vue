<template>
  <div class="cultivation-warning">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>培育过程预警</h1>
        <p>智能化培育过程监控与预警管理平台</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="预警总数" :value="stats.totalAlerts" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="严重预警" :value="stats.criticalAlerts" :value-style="{ color: '#ff4d4f' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="今日预警" :value="stats.todayAlerts" :value-style="{ color: '#52c41a' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="处理率" :value="stats.handleRate" suffix="%" :value-style="{ color: '#1890ff' }" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToConfigManagement">
              <div class="action-icon">
                <setting-outlined />
              </div>
              <div class="action-content">
                <h3>预警配置</h3>
                <p>管理预警规则和模板</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToDataCollection">
              <div class="action-icon">
                <database-outlined />
              </div>
              <div class="action-content">
                <h3>数据采集</h3>
                <p>监控数据采集状态</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToAlertTrigger">
              <div class="action-icon">
                <alert-outlined />
              </div>
              <div class="action-content">
                <h3>预警触发</h3>
                <p>查看预警触发情况</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToSupervision">
              <div class="action-icon">
                <eye-outlined />
              </div>
              <div class="action-content">
                <h3>督办管理</h3>
                <p>管理督办对象</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 预警统计概览 -->
    <div class="warning-overview">
      <a-card title="预警统计概览" :bordered="false">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat critical">
              <div class="stat-header">
                <exclamation-circle-outlined class="stat-icon" />
                <span class="stat-title">严重预警</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ stats.criticalAlerts }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-badge">
                <a-badge 
                  :count="stats.criticalAlerts" 
                  :number-style="{ backgroundColor: '#ff4d4f' }"
                />
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat major">
              <div class="stat-header">
                <warning-outlined class="stat-icon" />
                <span class="stat-title">重要预警</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ stats.majorAlerts }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-progress">
                <a-progress 
                  :percent="getMajorPercent()"
                  stroke-color="#faad14"
                  :show-info="false"
                  size="small"
                />
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat general">
              <div class="stat-header">
                <info-circle-outlined class="stat-icon" />
                <span class="stat-title">一般预警</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ stats.generalAlerts }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-progress">
                <a-progress 
                  :percent="getGeneralPercent()"
                  stroke-color="#1890ff"
                  :show-info="false"
                  size="small"
                />
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat processing">
              <div class="stat-header">
                <sync-outlined class="stat-icon" />
                <span class="stat-title">处理中</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ stats.processingAlerts }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-trend">
                <span class="trend-value">{{ stats.handleRate }}%</span>
                <span class="trend-label">处理率</span>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 功能模块区域 -->
    <div class="feature-modules">
      <a-row :gutter="[24, 24]">
        <!-- 预警配置管理 -->
        <a-col :xs="24" :lg="12">
          <a-card title="预警配置管理" :bordered="false">
            <template #extra>
              <a @click="goToConfigManagement">查看更多</a>
            </template>
            
            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>预警规则管理</h4>
                    <p>灵活配置预警触发条件和通知方式</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>预警模板设置</h4>
                    <p>自定义预警消息模板和格式</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>案例训练</h4>
                    <p>基于历史案例优化预警算法</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToConfigManagement">
                管理配置
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 数据采集与处理 -->
        <a-col :xs="24" :lg="12">
          <a-card title="数据采集与处理" :bordered="false">
            <template #extra>
              <a @click="goToDataCollection">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>智能采集引擎</h4>
                    <p>自动化数据采集和实时监控</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>数据清洗</h4>
                    <p>智能数据清洗和质量检测</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>质量监控</h4>
                    <p>数据质量实时监控和预警</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToDataCollection">
                查看采集
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 督办管理 -->
        <a-col :xs="24" :lg="12">
          <a-card title="督办管理" :bordered="false">
            <template #extra>
              <a @click="goToSupervision">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>督办对象管理</h4>
                    <p>全面管理培育对象信息</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>多维分析</h4>
                    <p>多角度分析督办效果</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>渝快政同步</h4>
                    <p>与渝快政平台数据同步</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToSupervision">
                管理督办
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 预警报告与模板 -->
        <a-col :xs="24" :lg="12">
          <a-card title="预警报告与模板" :bordered="false">
            <template #extra>
              <a @click="goToReportTemplate">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>智能报告生成</h4>
                    <p>自动生成预警分析报告</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>模板自定义</h4>
                    <p>灵活配置报告模板格式</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>定期报告</h4>
                    <p>定时生成和推送报告</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToReportTemplate">
                生成报告
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近预警动态 -->
    <div class="recent-alerts">
      <a-card title="最近预警动态" :bordered="false">
        <template #extra>
          <a @click="goToAlertTrigger">查看全部</a>
        </template>
        
        <a-spin :spinning="loading">
          <div v-if="recentAlerts.length === 0" class="empty-state">
            <a-empty description="暂无预警动态">
              <template #image>
                <alert-outlined style="font-size: 48px; color: #d9d9d9;" />
              </template>
            </a-empty>
          </div>
          
          <div v-else class="alert-timeline">
            <a-timeline>
              <a-timeline-item
                v-for="alert in recentAlerts"
                :key="alert.id"
                :color="getAlertColor(alert.level)"
              >
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="alert-level">{{ getAlertLevelText(alert.level) }}</span>
                    <span class="alert-time">{{ alert.triggerTime }}</span>
                  </div>
                  <div class="timeline-body">
                    <h4>{{ alert.title }}</h4>
                    <p>{{ alert.content }}</p>
                    <div class="alert-meta">
                      <a-tag :color="getStatusColor(alert.status)">
                        {{ getStatusText(alert.status) }}
                      </a-tag>
                      <span v-if="alert.handlerName" class="handler">
                        处理人：{{ alert.handlerName }}
                      </span>
                    </div>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </a-spin>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SettingOutlined,
  DatabaseOutlined,
  AlertOutlined,
  EyeOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { 
  AlertStatistics, 
  AlertRecord,
  AlertLevel,
  AlertStatus
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 统计数据
const stats = reactive<AlertStatistics>({
  totalAlerts: 0,
  criticalAlerts: 0,
  majorAlerts: 0,
  generalAlerts: 0,
  infoAlerts: 0,
  pendingAlerts: 0,
  processingAlerts: 0,
  completedAlerts: 0,
  cancelledAlerts: 0,
  todayAlerts: 0,
  weekAlerts: 0,
  monthAlerts: 0,
  averageHandleTime: 0,
  handleRate: 0,
  alertTrend: [],
  topAlertRules: [],
  alertsByLevel: {} as any,
  alertsByStatus: {} as any
})

// 最近预警
const recentAlerts = ref<AlertRecord[]>([])

// 计算属性
const getMajorPercent = computed(() => {
  return stats.totalAlerts > 0 ? Math.round((stats.majorAlerts / stats.totalAlerts) * 100) : 0
})

const getGeneralPercent = computed(() => {
  return stats.totalAlerts > 0 ? Math.round((stats.generalAlerts / stats.totalAlerts) * 100) : 0
})

// 导航方法
const goToConfigManagement = () => {
  router.push('/cultivation-warning/config')
}

const goToDataCollection = () => {
  router.push('/cultivation-warning/data-collection')
}

const goToAlertTrigger = () => {
  router.push('/cultivation-warning/alert-trigger')
}

const goToSupervision = () => {
  router.push('/cultivation-warning/supervision')
}

const goToReportTemplate = () => {
  router.push('/cultivation-warning/report')
}

// 工具方法
const getAlertColor = (level: AlertLevel) => {
  const colors = {
    critical: 'red',
    major: 'orange',
    general: 'blue',
    info: 'green'
  }
  return colors[level] || 'default'
}

const getAlertLevelText = (level: AlertLevel) => {
  const texts = {
    critical: '严重预警',
    major: '重要预警',
    general: '一般预警',
    info: '信息预警'
  }
  return texts[level] || level
}

const getStatusColor = (status: AlertStatus) => {
  const colors = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    cancelled: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: AlertStatus) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 加载统计数据
    const statisticsData = await MockCultivationWarningService.getAlertStatistics()
    Object.assign(stats, statisticsData)
    
    // 加载最近预警
    const alertsResult = await MockCultivationWarningService.getAlertRecords({
      page: 1,
      pageSize: 5,
      sortBy: 'triggerTime',
      sortOrder: 'desc'
    })
    recentAlerts.value = alertsResult.data
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.cultivation-warning {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 200px;
      height: 200px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: translate(50%, -50%);
    }

    .header-content {
      position: relative;
      z-index: 1;
      margin-bottom: 24px;

      h1 {
        font-size: 32px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: white;
      }

      p {
        font-size: 16px;
        margin: 0;
        opacity: 0.9;
      }
    }

    .header-stats {
      position: relative;
      z-index: 1;

      :deep(.ant-statistic) {
        .ant-statistic-title {
          color: rgba(255, 255, 255, 0.85);
          font-size: 14px;
        }

        .ant-statistic-content {
          color: white;
          font-size: 24px;
          font-weight: 600;
        }
      }
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    .action-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;
      height: 100px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #1890ff;
      }

      .action-icon {
        font-size: 32px;
        color: #1890ff;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .action-content {
        flex: 1;

        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
  }

  .warning-overview {
    margin-bottom: 24px;

    .warning-stat {
      background: white;
      border-radius: 8px;
      padding: 20px;
      border: 1px solid #f0f0f0;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .stat-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .stat-icon {
          font-size: 20px;
          margin-right: 8px;
        }

        .stat-title {
          font-size: 14px;
          color: #8c8c8c;
        }
      }

      .stat-content {
        margin-bottom: 12px;

        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #262626;
        }

        .stat-unit {
          font-size: 14px;
          color: #8c8c8c;
          margin-left: 4px;
        }
      }

      .stat-badge {
        text-align: right;
      }

      .stat-progress {
        margin-top: 8px;
      }

      .stat-trend {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .trend-value {
          font-size: 18px;
          font-weight: 600;
          color: #1890ff;
        }

        .trend-label {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      &.critical {
        .stat-header .stat-icon {
          color: #ff4d4f;
        }
      }

      &.major {
        .stat-header .stat-icon {
          color: #faad14;
        }
      }

      &.general {
        .stat-header .stat-icon {
          color: #1890ff;
        }
      }

      &.processing {
        .stat-header .stat-icon {
          color: #52c41a;
        }
      }
    }
  }

  .feature-modules {
    margin-bottom: 24px;

    .module-content {
      .feature-list {
        margin-bottom: 20px;

        .feature-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .feature-icon {
            font-size: 16px;
            color: #52c41a;
            margin-right: 12px;
            margin-top: 2px;
            flex-shrink: 0;
          }

          .feature-text {
            flex: 1;

            h4 {
              margin: 0 0 4px 0;
              font-size: 14px;
              font-weight: 600;
              color: #262626;
            }

            p {
              margin: 0;
              font-size: 13px;
              color: #8c8c8c;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }

  .recent-alerts {
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .alert-timeline {
      .timeline-content {
        .timeline-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .alert-level {
            font-size: 12px;
            font-weight: 600;
            color: #1890ff;
          }

          .alert-time {
            font-size: 12px;
            color: #8c8c8c;
          }
        }

        .timeline-body {
          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }

          p {
            margin: 0 0 12px 0;
            font-size: 13px;
            color: #595959;
            line-height: 1.5;
          }

          .alert-meta {
            display: flex;
            align-items: center;
            gap: 12px;

            .handler {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      padding: 24px 20px;

      .header-content h1 {
        font-size: 24px;
      }

      .header-stats {
        :deep(.ant-col) {
          margin-bottom: 16px;
        }
      }
    }

    .quick-actions {
      .action-card {
        height: auto;
        min-height: 80px;
        padding: 16px;

        .action-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .action-content h3 {
          font-size: 14px;
        }

        .action-content p {
          font-size: 12px;
        }
      }
    }

    .warning-overview {
      .warning-stat {
        padding: 16px;
        margin-bottom: 16px;

        .stat-content .stat-value {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
