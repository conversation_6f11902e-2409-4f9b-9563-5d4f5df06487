<template>
  <div class="config-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警配置管理</h1>
        <p>管理预警规则、阈值设置和通知配置</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary">
            <plus-outlined />
            新增规则
          </a-button>
          <a-button>
            <download-outlined />
            导出配置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 配置统计概览 -->
    <div class="config-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="预警规则"
              :value="statistics.totalRules"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="启用规则"
              :value="statistics.enabledRules"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="通知渠道"
              :value="statistics.notificationChannels"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日触发"
              :value="statistics.todayTriggers"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 预警规则管理 -->
        <a-tab-pane key="rules" tab="预警规则管理">
          <div class="rules-management">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索规则名称"
                    allow-clear
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedLevel"
                    placeholder="预警级别"
                    allow-clear
                  >
                    <a-select-option value="critical">严重</a-select-option>
                    <a-select-option value="major">重要</a-select-option>
                    <a-select-option value="general">一般</a-select-option>
                    <a-select-option value="info">信息</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="规则状态"
                    allow-clear
                  >
                    <a-select-option :value="true">已启用</a-select-option>
                    <a-select-option :value="false">已禁用</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-space>
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadRules">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 规则列表 -->
            <div class="rules-table">
              <a-table
                :columns="rulesColumns"
                :data-source="rulesList"
                :loading="loading"
                :pagination="pagination"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="rule-name">
                      <a @click="viewRuleDetail(record)">{{ record.name }}</a>
                      <div class="rule-description">{{ record.description }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'level'">
                    <a-tag :color="getLevelColor(record.level)">
                      {{ getLevelText(record.level) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'enabled'">
                    <a-switch 
                      :checked="record.enabled" 
                      @change="(checked) => toggleRule(record, checked)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editRule(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="copyRule(record)">
                        复制
                      </a-button>
                      <a-button type="link" size="small" @click="testRule(record)">
                        测试
                      </a-button>
                      <a-button type="link" size="small" danger @click="deleteRule(record)">
                        删除
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 阈值设置 -->
        <a-tab-pane key="thresholds" tab="阈值设置">
          <div class="thresholds-config">
            <a-card title="评分阈值配置" size="small">
              <a-form layout="vertical">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="严重预警阈值">
                      <a-input-number
                        v-model:value="thresholds.critical"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="重要预警阈值">
                      <a-input-number
                        v-model:value="thresholds.major"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="一般预警阈值">
                      <a-input-number
                        v-model:value="thresholds.general"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="信息预警阈值">
                      <a-input-number
                        v-model:value="thresholds.info"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
                
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="saveThresholds">保存设置</a-button>
                    <a-button @click="resetThresholds">重置</a-button>
                  </a-space>
                </a-form-item>
              </a-form>
            </a-card>
          </div>
        </a-tab-pane>

        <!-- 通知配置 -->
        <a-tab-pane key="notifications" tab="通知配置">
          <div class="notifications-config">
            <a-card title="通知渠道配置" size="small">
              <div class="notification-channels">
                <div v-for="channel in notificationChannels" :key="channel.id" class="channel-item">
                  <div class="channel-header">
                    <div class="channel-info">
                      <h4>{{ channel.name }}</h4>
                      <p>{{ channel.description }}</p>
                    </div>
                    <a-switch 
                      :checked="channel.enabled" 
                      @change="(checked) => toggleChannel(channel, checked)"
                    />
                  </div>
                  
                  <div v-if="channel.enabled" class="channel-config">
                    <a-form layout="vertical">
                      <a-form-item v-for="config in channel.configs" :key="config.key" :label="config.label">
                        <a-input v-model:value="config.value" :placeholder="config.placeholder" />
                      </a-form-item>
                    </a-form>
                  </div>
                </div>
              </div>
              
              <div class="notification-actions">
                <a-space>
                  <a-button type="primary" @click="saveNotificationConfig">保存配置</a-button>
                  <a-button @click="testNotification">测试通知</a-button>
                </a-space>
              </div>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DownloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('rules')

// 统计数据
const statistics = reactive({
  totalRules: 15,
  enabledRules: 12,
  notificationChannels: 5,
  todayTriggers: 8
})

// 搜索和筛选
const searchKeyword = ref('')
const selectedLevel = ref()
const selectedStatus = ref()

// 规则列表
const rulesList = ref([
  {
    id: 'rule_001',
    name: '培育对象评分异常预警',
    description: '当培育对象评分低于设定阈值时触发预警',
    level: 'critical',
    enabled: true,
    triggerCount: 5,
    lastTriggered: '2025-06-20 10:30:00'
  },
  {
    id: 'rule_002',
    name: '数据采集延迟预警',
    description: '当数据采集超过预期时间时触发预警',
    level: 'major',
    enabled: true,
    triggerCount: 2,
    lastTriggered: '2025-06-20 09:15:00'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 规则表格列配置
const rulesColumns = [
  {
    title: '规则名称',
    key: 'name',
    width: 300
  },
  {
    title: '预警级别',
    key: 'level',
    width: 120
  },
  {
    title: '状态',
    key: 'enabled',
    width: 100
  },
  {
    title: '触发次数',
    dataIndex: 'triggerCount',
    key: 'triggerCount',
    width: 120
  },
  {
    title: '最后触发',
    dataIndex: 'lastTriggered',
    key: 'lastTriggered',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 阈值设置
const thresholds = reactive({
  critical: 60,
  major: 70,
  general: 80,
  info: 90
})

// 通知渠道
const notificationChannels = ref([
  {
    id: 'email',
    name: '邮件通知',
    description: '通过邮件发送预警通知',
    enabled: true,
    configs: [
      { key: 'smtp_server', label: 'SMTP服务器', value: 'smtp.example.com', placeholder: '请输入SMTP服务器地址' },
      { key: 'smtp_port', label: 'SMTP端口', value: '587', placeholder: '请输入SMTP端口' }
    ]
  },
  {
    id: 'sms',
    name: '短信通知',
    description: '通过短信发送预警通知',
    enabled: false,
    configs: [
      { key: 'api_key', label: 'API密钥', value: '', placeholder: '请输入短信服务API密钥' },
      { key: 'template_id', label: '模板ID', value: '', placeholder: '请输入短信模板ID' }
    ]
  }
])

// 方法
const loadRules = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    pagination.total = rulesList.value.length
  }, 1000)
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedLevel.value = undefined
  selectedStatus.value = undefined
  loadRules()
}

const viewRuleDetail = (rule: any) => {
  message.info(`查看规则详情：${rule.name}`)
}

const editRule = (rule: any) => {
  message.info(`编辑规则：${rule.name}`)
}

const copyRule = (rule: any) => {
  message.success(`复制规则：${rule.name}`)
}

const testRule = (rule: any) => {
  message.success(`测试规则：${rule.name}`)
}

const deleteRule = (rule: any) => {
  message.success(`删除规则：${rule.name}`)
}

const toggleRule = (rule: any, checked: boolean) => {
  rule.enabled = checked
  message.success(`规则已${checked ? '启用' : '禁用'}`)
}

const saveThresholds = () => {
  message.success('阈值设置保存成功')
}

const resetThresholds = () => {
  Object.assign(thresholds, {
    critical: 60,
    major: 70,
    general: 80,
    info: 90
  })
  message.info('阈值设置已重置')
}

const toggleChannel = (channel: any, checked: boolean) => {
  channel.enabled = checked
  message.success(`${channel.name}已${checked ? '启用' : '禁用'}`)
}

const saveNotificationConfig = () => {
  message.success('通知配置保存成功')
}

const testNotification = () => {
  message.success('测试通知发送成功')
}

// 工具方法
const getLevelColor = (level: string) => {
  const colors = {
    critical: 'red',
    major: 'orange',
    general: 'blue',
    info: 'green'
  }
  return colors[level] || 'default'
}

const getLevelText = (level: string) => {
  const texts = {
    critical: '严重',
    major: '重要',
    general: '一般',
    info: '信息'
  }
  return texts[level] || level
}

// 组件挂载时加载数据
onMounted(() => {
  loadRules()
})
</script>

<style lang="scss" scoped>
.config-management {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .config-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .rules-management {
    .rule-name {
      .rule-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }
  }

  .notifications-config {
    .notification-channels {
      .channel-item {
        margin-bottom: 24px;
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        .channel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .channel-info {
            h4 {
              margin: 0 0 4px 0;
              font-size: 16px;
              font-weight: 600;
              color: #262626;
            }

            p {
              margin: 0;
              color: #8c8c8c;
              font-size: 14px;
            }
          }
        }

        .channel-config {
          padding-top: 16px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }

    .notification-actions {
      margin-top: 24px;
      text-align: center;
    }
  }
}
</style>
