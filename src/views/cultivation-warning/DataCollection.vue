<template>
  <div class="data-collection">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>数据采集与处理</h1>
        <p>智能数据采集引擎和数据质量监控平台</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <plus-outlined />
            新建采集模型
          </a-button>
          <a-button @click="refreshData">
            <reload-outlined />
            刷新数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据采集概览 -->
    <div class="collection-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="采集模型总数"
              :value="statistics.totalModels"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="活跃模型"
              :value="statistics.activeModels"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="statistics.averageSuccessRate"
              suffix="%"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="数据质量分"
              :value="statistics.dataQualityScore"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 采集引擎配置 -->
        <a-tab-pane key="engines" tab="采集引擎配置">
          <div class="engines-management">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索模型名称"
                    allow-clear
                    @change="handleSearch"
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedSourceType"
                    placeholder="数据源类型"
                    allow-clear
                    @change="handleSourceTypeFilter"
                  >
                    <a-select-option value="database">数据库</a-select-option>
                    <a-select-option value="api">API接口</a-select-option>
                    <a-select-option value="file">文件</a-select-option>
                    <a-select-option value="manual">手动录入</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="运行状态"
                    allow-clear
                    @change="handleStatusFilter"
                  >
                    <a-select-option value="collecting">采集中</a-select-option>
                    <a-select-option value="processing">处理中</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="failed">失败</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-space>
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadModels">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 采集模型列表 -->
            <div class="models-table">
              <a-table
                :columns="modelsColumns"
                :data-source="modelsList"
                :loading="loading"
                :pagination="pagination"
                row-key="id"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="model-name">
                      <a @click="viewModelDetail(record)">{{ record.name }}</a>
                      <div class="model-description">{{ record.description }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'sourceType'">
                    <a-tag :color="getSourceTypeColor(record.sourceType)">
                      {{ getSourceTypeText(record.sourceType) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getStatusBadge(record.status)" 
                      :text="getStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'successRate'">
                    <div class="success-rate">
                      <a-progress 
                        :percent="record.successRate" 
                        size="small"
                        :stroke-color="getProgressColor(record.successRate)"
                      />
                      <span>{{ record.successRate }}%</span>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'isEnabled'">
                    <a-switch
                      :checked="record.isEnabled"
                      :loading="record.switching"
                      @change="(checked) => toggleModelStatus(record, checked)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editModel(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="runModel(record)">
                        运行
                      </a-button>
                      <a-button type="link" size="small" @click="viewLogs(record)">
                        日志
                      </a-button>
                      <a-popconfirm
                        title="确定要删除这个采集模型吗？"
                        @confirm="deleteModel(record)"
                      >
                        <a-button type="link" size="small" danger>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 数据清洗监控 -->
        <a-tab-pane key="cleaning" tab="数据清洗监控">
          <div class="cleaning-monitor">
            <a-row :gutter="24">
              <a-col :span="16">
                <a-card title="清洗进度监控" size="small">
                  <div class="cleaning-progress">
                    <div v-for="task in cleaningTasks" :key="task.id" class="task-item">
                      <div class="task-header">
                        <span class="task-name">{{ task.name }}</span>
                        <span class="task-status">{{ getStatusText(task.status) }}</span>
                      </div>
                      <div class="task-progress">
                        <a-progress 
                          :percent="task.progress" 
                          :status="task.status === 'failed' ? 'exception' : 'normal'"
                        />
                      </div>
                      <div class="task-meta">
                        <span>数据量：{{ task.dataVolume }} MB</span>
                        <span>清洗规则：{{ task.cleaningRules.length }} 个</span>
                        <span>开始时间：{{ task.startTime }}</span>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="清洗统计" size="small">
                  <div class="cleaning-stats">
                    <a-descriptions :column="1" size="small">
                      <a-descriptions-item label="总清洗任务">
                        {{ cleaningStats.totalTasks }}
                      </a-descriptions-item>
                      <a-descriptions-item label="进行中任务">
                        {{ cleaningStats.runningTasks }}
                      </a-descriptions-item>
                      <a-descriptions-item label="完成任务">
                        {{ cleaningStats.completedTasks }}
                      </a-descriptions-item>
                      <a-descriptions-item label="失败任务">
                        {{ cleaningStats.failedTasks }}
                      </a-descriptions-item>
                      <a-descriptions-item label="清洗成功率">
                        {{ cleaningStats.successRate }}%
                      </a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-card>
                
                <a-card title="异常数据处理" size="small" style="margin-top: 16px;">
                  <div class="exception-data">
                    <div v-for="exception in exceptionData" :key="exception.id" class="exception-item">
                      <div class="exception-type">{{ exception.type }}</div>
                      <div class="exception-count">{{ exception.count }} 条</div>
                      <div class="exception-action">
                        <a-button type="link" size="small" @click="handleException(exception)">
                          处理
                        </a-button>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 数据质量监控 -->
        <a-tab-pane key="quality" tab="数据质量监控">
          <div class="quality-monitor">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="数据质量指标" size="small">
                  <div class="quality-metrics">
                    <div class="metric-item">
                      <div class="metric-header">
                        <span class="metric-name">数据完整性</span>
                        <span class="metric-value">{{ qualityMetrics.completeness }}%</span>
                      </div>
                      <a-progress 
                        :percent="qualityMetrics.completeness" 
                        stroke-color="#52c41a"
                        size="small"
                      />
                    </div>
                    
                    <div class="metric-item">
                      <div class="metric-header">
                        <span class="metric-name">数据准确性</span>
                        <span class="metric-value">{{ qualityMetrics.accuracy }}%</span>
                      </div>
                      <a-progress 
                        :percent="qualityMetrics.accuracy" 
                        stroke-color="#1890ff"
                        size="small"
                      />
                    </div>
                    
                    <div class="metric-item">
                      <div class="metric-header">
                        <span class="metric-name">数据一致性</span>
                        <span class="metric-value">{{ qualityMetrics.consistency }}%</span>
                      </div>
                      <a-progress 
                        :percent="qualityMetrics.consistency" 
                        stroke-color="#faad14"
                        size="small"
                      />
                    </div>
                    
                    <div class="metric-item">
                      <div class="metric-header">
                        <span class="metric-name">数据及时性</span>
                        <span class="metric-value">{{ qualityMetrics.timeliness }}%</span>
                      </div>
                      <a-progress 
                        :percent="qualityMetrics.timeliness" 
                        stroke-color="#722ed1"
                        size="small"
                      />
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="质量趋势" size="small">
                  <div class="quality-trend">
                    <div class="trend-chart">
                      <div class="chart-header">
                        <span>近7天数据质量变化趋势</span>
                      </div>
                      <div class="chart-content">
                        <div class="trend-lines">
                          <div 
                            v-for="(item, index) in qualityTrend" 
                            :key="item.date"
                            class="trend-point"
                            :style="{ left: `${(index / Math.max(1, qualityTrend.length - 1)) * 100}%` }"
                          >
                            <div class="point-marker" :style="{ height: `${item.score}%` }"></div>
                            <div class="point-label">{{ item.date.slice(-2) }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
            
            <!-- 质量问题列表 -->
            <a-card title="质量问题" size="small" style="margin-top: 16px;">
              <a-table
                :columns="qualityIssuesColumns"
                :data-source="qualityIssues"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'severity'">
                    <a-tag :color="getSeverityColor(record.severity)">
                      {{ getSeverityText(record.severity) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getStatusBadge(record.status)" 
                      :text="getStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="fixIssue(record)">
                        修复
                      </a-button>
                      <a-button type="link" size="small" @click="ignoreIssue(record)">
                        忽略
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </div>
        </a-tab-pane>

        <!-- 数据预览 -->
        <a-tab-pane key="preview" tab="数据预览">
          <div class="data-preview">
            <div class="preview-controls">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-select
                    v-model:value="selectedDataSource"
                    placeholder="选择数据源"
                    style="width: 100%"
                    @change="loadPreviewData"
                  >
                    <a-select-option 
                      v-for="model in modelsList" 
                      :key="model.id" 
                      :value="model.id"
                    >
                      {{ model.name }}
                    </a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-range-picker
                    v-model:value="dateRange"
                    @change="loadPreviewData"
                  />
                </a-col>
                <a-col :span="4">
                  <a-input-number
                    v-model:value="previewLimit"
                    placeholder="显示条数"
                    :min="10"
                    :max="1000"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="8">
                  <a-space>
                    <a-button @click="loadPreviewData">刷新</a-button>
                    <a-button @click="exportPreviewData">导出</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>
            
            <div class="preview-table">
              <a-table
                :columns="previewColumns"
                :data-source="previewData"
                :loading="previewLoading"
                :pagination="{ pageSize: 20 }"
                :scroll="{ x: 1200 }"
                size="small"
              />
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 创建/编辑采集模型弹窗 -->
    <a-modal
      v-model:visible="modelModalVisible"
      :title="editingModel ? '编辑采集模型' : '新建采集模型'"
      width="800px"
      @ok="saveModel"
      @cancel="cancelModelEdit"
    >
      <a-form :model="modelForm" layout="vertical" ref="modelFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模型名称" name="name" :rules="[{ required: true, message: '请输入模型名称' }]">
              <a-input v-model:value="modelForm.name" placeholder="请输入模型名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="数据源类型" name="sourceType" :rules="[{ required: true, message: '请选择数据源类型' }]">
              <a-select v-model:value="modelForm.sourceType" placeholder="选择数据源类型">
                <a-select-option value="database">数据库</a-select-option>
                <a-select-option value="api">API接口</a-select-option>
                <a-select-option value="file">文件</a-select-option>
                <a-select-option value="manual">手动录入</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="模型描述" name="description">
          <a-textarea v-model:value="modelForm.description" placeholder="请输入模型描述" :rows="3" />
        </a-form-item>
        
        <!-- 数据源配置 -->
        <a-form-item label="数据源配置">
          <div class="source-config">
            <!-- 数据库配置 -->
            <div v-if="modelForm.sourceType === 'database'" class="config-section">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="主机地址">
                    <a-input v-model:value="modelForm.sourceConfig.host" placeholder="数据库主机地址" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="端口">
                    <a-input-number v-model:value="modelForm.sourceConfig.port" placeholder="端口号" style="width: 100%" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="数据库名">
                    <a-input v-model:value="modelForm.sourceConfig.database" placeholder="数据库名称" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="用户名">
                    <a-input v-model:value="modelForm.sourceConfig.username" placeholder="用户名" />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="密码">
                    <a-input-password v-model:value="modelForm.sourceConfig.password" placeholder="密码" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="查询语句">
                <a-textarea v-model:value="modelForm.sourceConfig.query" placeholder="SQL查询语句" :rows="4" />
              </a-form-item>
            </div>
            
            <!-- API配置 -->
            <div v-if="modelForm.sourceType === 'api'" class="config-section">
              <a-form-item label="API地址">
                <a-input v-model:value="modelForm.sourceConfig.url" placeholder="API接口地址" />
              </a-form-item>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="请求方法">
                    <a-select v-model:value="modelForm.sourceConfig.method" placeholder="请求方法">
                      <a-select-option value="GET">GET</a-select-option>
                      <a-select-option value="POST">POST</a-select-option>
                      <a-select-option value="PUT">PUT</a-select-option>
                      <a-select-option value="DELETE">DELETE</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="超时时间(秒)">
                    <a-input-number v-model:value="modelForm.sourceConfig.timeout" :min="1" :max="300" style="width: 100%" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            
            <!-- 文件配置 -->
            <div v-if="modelForm.sourceType === 'file'" class="config-section">
              <a-form-item label="文件路径">
                <a-input v-model:value="modelForm.sourceConfig.filePath" placeholder="文件路径" />
              </a-form-item>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="文件类型">
                    <a-select v-model:value="modelForm.sourceConfig.fileType" placeholder="文件类型">
                      <a-select-option value="csv">CSV</a-select-option>
                      <a-select-option value="excel">Excel</a-select-option>
                      <a-select-option value="json">JSON</a-select-option>
                      <a-select-option value="xml">XML</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="编码格式">
                    <a-select v-model:value="modelForm.sourceConfig.encoding" placeholder="编码格式">
                      <a-select-option value="utf-8">UTF-8</a-select-option>
                      <a-select-option value="gbk">GBK</a-select-option>
                      <a-select-option value="gb2312">GB2312</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="采集频率(分钟)">
              <a-input-number v-model:value="modelForm.collectionFrequency" :min="1" :max="1440" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="数据格式">
              <a-select v-model:value="modelForm.dataFormat" placeholder="数据格式">
                <a-select-option value="json">JSON</a-select-option>
                <a-select-option value="xml">XML</a-select-option>
                <a-select-option value="csv">CSV</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item>
          <a-checkbox v-model:checked="modelForm.isEnabled">启用模型</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import type {
  DataCollectionModel,
  DataCollectionStatistics,
  DataSourceType,
  DataCollectionStatus,
  DataCollectionQueryParams,
  OperationResult
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'

// 响应式数据
const loading = ref(false)
const activeTab = ref('engines')

// 搜索和筛选
const searchKeyword = ref('')
const selectedSourceType = ref<DataSourceType | undefined>()
const selectedStatus = ref<DataCollectionStatus | undefined>()

// 采集模型列表
const modelsList = ref<DataCollectionModel[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 统计数据
const statistics = reactive<DataCollectionStatistics>({
  totalModels: 0,
  activeModels: 0,
  inactiveModels: 0,
  collectingModels: 0,
  processingModels: 0,
  completedModels: 0,
  failedModels: 0,
  totalCollections: 0,
  successfulCollections: 0,
  failedCollections: 0,
  averageSuccessRate: 0,
  dataQualityScore: 0,
  collectionTrend: [],
  topDataSources: []
})

// 采集模型表格列配置
const modelsColumns = [
  {
    title: '模型名称',
    dataIndex: 'name',
    key: 'name',
    width: 250
  },
  {
    title: '数据源类型',
    dataIndex: 'sourceType',
    key: 'sourceType',
    width: 120
  },
  {
    title: '运行状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '成功率',
    dataIndex: 'successRate',
    key: 'successRate',
    width: 150
  },
  {
    title: '采集频率',
    dataIndex: 'collectionFrequency',
    key: 'collectionFrequency',
    width: 100
  },
  {
    title: '启用状态',
    dataIndex: 'isEnabled',
    key: 'isEnabled',
    width: 100
  },
  {
    title: '最后采集',
    dataIndex: 'lastCollectionTime',
    key: 'lastCollectionTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 数据清洗任务
const cleaningTasks = ref([
  {
    id: '1',
    name: '培育对象基础信息清洗',
    status: 'processing' as DataCollectionStatus,
    progress: 75,
    dataVolume: 128,
    cleaningRules: ['去重', '格式化', '数据验证'],
    startTime: '2025-06-20 08:00:00'
  },
  {
    id: '2',
    name: '评分数据清洗',
    status: 'completed' as DataCollectionStatus,
    progress: 100,
    dataVolume: 64,
    cleaningRules: ['异常值检测', '缺失值填充'],
    startTime: '2025-06-20 07:30:00'
  }
])

// 清洗统计
const cleaningStats = reactive({
  totalTasks: 15,
  runningTasks: 3,
  completedTasks: 10,
  failedTasks: 2,
  successRate: 86.7
})

// 异常数据
const exceptionData = ref([
  { id: '1', type: '数据格式错误', count: 23 },
  { id: '2', type: '缺失必填字段', count: 15 },
  { id: '3', type: '数据重复', count: 8 }
])

// 数据质量指标
const qualityMetrics = reactive({
  completeness: 92,
  accuracy: 88,
  consistency: 95,
  timeliness: 90
})

// 质量趋势数据
const qualityTrend = ref([
  { date: '01-14', score: 85 },
  { date: '01-15', score: 87 },
  { date: '01-16', score: 89 },
  { date: '01-17', score: 91 },
  { date: '01-18', score: 88 },
  { date: '01-19', score: 92 },
  { date: '01-20', score: 90 }
])

// 质量问题列表
const qualityIssues = ref([
  {
    id: '1',
    description: '培育对象评分数据存在异常值',
    severity: 'high',
    status: 'pending',
    affectedRecords: 156,
    detectedTime: '2025-06-20 09:15:00'
  },
  {
    id: '2',
    description: '部分记录缺少联系方式信息',
    severity: 'medium',
    status: 'processing',
    affectedRecords: 89,
    detectedTime: '2025-06-20 08:30:00'
  }
])

// 质量问题表格列配置
const qualityIssuesColumns = [
  {
    title: '问题描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '严重程度',
    dataIndex: 'severity',
    key: 'severity',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '影响记录数',
    dataIndex: 'affectedRecords',
    key: 'affectedRecords',
    width: 120
  },
  {
    title: '检测时间',
    dataIndex: 'detectedTime',
    key: 'detectedTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 数据预览
const selectedDataSource = ref<string>()
const dateRange = ref()
const previewLimit = ref(100)
const previewLoading = ref(false)
const previewData = ref([])
const previewColumns = ref([
  { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
  { title: '对象名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '评分', dataIndex: 'score', key: 'score', width: 100 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', width: 150 }
])

// 模型编辑
const modelModalVisible = ref(false)
const editingModel = ref<DataCollectionModel | null>(null)
const modelFormRef = ref()

const modelForm = reactive({
  name: '',
  description: '',
  sourceType: 'database' as DataSourceType,
  sourceConfig: {} as any,
  collectionFrequency: 60,
  dataFormat: 'json',
  isEnabled: true
})

// 方法
const loadModels = async () => {
  loading.value = true
  try {
    const result = await MockCultivationWarningService.getDataCollectionModels({
      keyword: searchKeyword.value,
      sourceType: selectedSourceType.value,
      status: selectedStatus.value,
      page: pagination.current,
      pageSize: pagination.pageSize
    })

    modelsList.value = result.data
    pagination.total = result.total
  } catch (error) {
    console.error('加载采集模型失败:', error)
    message.error('加载采集模型失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const stats = await MockCultivationWarningService.getDataCollectionStatistics()
    Object.assign(statistics, stats)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadModels()
}

const handleSourceTypeFilter = () => {
  pagination.current = 1
  loadModels()
}

const handleStatusFilter = () => {
  pagination.current = 1
  loadModels()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedSourceType.value = undefined
  selectedStatus.value = undefined
  pagination.current = 1
  loadModels()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadModels()
}

const refreshData = () => {
  loadModels()
  loadStatistics()
  message.success('数据已刷新')
}

// 模型操作
const showCreateModal = () => {
  editingModel.value = null
  resetModelForm()
  modelModalVisible.value = true
}

const editModel = (model: DataCollectionModel) => {
  editingModel.value = model
  Object.assign(modelForm, {
    name: model.name,
    description: model.description,
    sourceType: model.sourceType,
    sourceConfig: { ...model.sourceConfig },
    collectionFrequency: model.collectionFrequency,
    dataFormat: model.dataFormat,
    isEnabled: model.isEnabled
  })
  modelModalVisible.value = true
}

const saveModel = async () => {
  try {
    await modelFormRef.value.validate()

    // 模拟保存操作
    const result: OperationResult = {
      success: true,
      message: editingModel.value ? '模型更新成功' : '模型创建成功',
      timestamp: new Date().toISOString()
    }

    if (result.success) {
      message.success(result.message)
      modelModalVisible.value = false
      loadModels()
    } else {
      message.error(result.message)
    }
  } catch (error) {
    console.error('保存模型失败:', error)
  }
}

const cancelModelEdit = () => {
  modelModalVisible.value = false
  resetModelForm()
}

const resetModelForm = () => {
  Object.assign(modelForm, {
    name: '',
    description: '',
    sourceType: 'database' as DataSourceType,
    sourceConfig: {},
    collectionFrequency: 60,
    dataFormat: 'json',
    isEnabled: true
  })
}

const deleteModel = async (model: DataCollectionModel) => {
  try {
    // 模拟删除操作
    message.success('模型删除成功')
    loadModels()
  } catch (error) {
    console.error('删除模型失败:', error)
    message.error('删除模型失败')
  }
}

const toggleModelStatus = async (model: DataCollectionModel, checked: boolean) => {
  model.switching = true
  try {
    // 模拟状态切换
    await new Promise(resolve => setTimeout(resolve, 1000))
    model.isEnabled = checked
    message.success(`模型已${checked ? '启用' : '禁用'}`)
  } catch (error) {
    console.error('切换模型状态失败:', error)
    message.error('操作失败')
  } finally {
    model.switching = false
  }
}

const runModel = (model: DataCollectionModel) => {
  message.success(`开始运行模型：${model.name}`)
}

const viewLogs = (model: DataCollectionModel) => {
  message.info(`查看模型日志：${model.name}`)
}

const viewModelDetail = (model: DataCollectionModel) => {
  message.info(`查看模型详情：${model.name}`)
}

// 数据清洗操作
const handleException = (exception: any) => {
  message.info(`处理异常数据：${exception.type}`)
}

// 质量监控操作
const fixIssue = (issue: any) => {
  message.info(`修复质量问题：${issue.description}`)
}

const ignoreIssue = (issue: any) => {
  message.info(`忽略质量问题：${issue.description}`)
}

// 数据预览操作
const loadPreviewData = () => {
  if (!selectedDataSource.value) {
    message.warning('请先选择数据源')
    return
  }

  previewLoading.value = true

  // 模拟加载预览数据
  setTimeout(() => {
    previewData.value = Array.from({ length: previewLimit.value }, (_, i) => ({
      id: i + 1,
      name: `培育对象${i + 1}`,
      score: Math.floor(Math.random() * 100),
      status: ['正常', '异常', '待审核'][Math.floor(Math.random() * 3)],
      updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }))
    previewLoading.value = false
  }, 1000)
}

const exportPreviewData = () => {
  message.info('导出预览数据')
}

// 工具方法
const getSourceTypeColor = (type: DataSourceType) => {
  const colors = {
    database: 'blue',
    api: 'green',
    file: 'orange',
    manual: 'purple'
  }
  return colors[type] || 'default'
}

const getSourceTypeText = (type: DataSourceType) => {
  const texts = {
    database: '数据库',
    api: 'API接口',
    file: '文件',
    manual: '手动录入'
  }
  return texts[type] || type
}

const getStatusBadge = (status: DataCollectionStatus) => {
  const badges = {
    collecting: 'processing',
    processing: 'processing',
    completed: 'success',
    failed: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: DataCollectionStatus) => {
  const texts = {
    collecting: '采集中',
    processing: '处理中',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

const getProgressColor = (percent: number) => {
  if (percent >= 90) return '#52c41a'
  if (percent >= 70) return '#faad14'
  return '#ff4d4f'
}

const getSeverityColor = (severity: string) => {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'blue'
  }
  return colors[severity] || 'default'
}

const getSeverityText = (severity: string) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return texts[severity] || severity
}

// 组件挂载时加载数据
onMounted(() => {
  loadModels()
  loadStatistics()
})
</script>

<style lang="scss" scoped>
.data-collection {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .collection-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .models-table {
    .model-name {
      .model-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }

    .success-rate {
      display: flex;
      align-items: center;
      gap: 8px;

      span {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .cleaning-monitor {
    .cleaning-progress {
      .task-item {
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 16px;
        background: white;

        .task-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .task-name {
            font-weight: 600;
            color: #262626;
          }

          .task-status {
            font-size: 12px;
            color: #8c8c8c;
          }
        }

        .task-progress {
          margin-bottom: 12px;
        }

        .task-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }

    .cleaning-stats {
      .ant-descriptions-item-label {
        font-weight: 600;
      }
    }

    .exception-data {
      .exception-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .exception-type {
          font-size: 14px;
          color: #262626;
        }

        .exception-count {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .quality-monitor {
    .quality-metrics {
      .metric-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .metric-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .metric-name {
            font-size: 14px;
            color: #262626;
            font-weight: 600;
          }

          .metric-value {
            font-size: 16px;
            color: #1890ff;
            font-weight: 600;
          }
        }
      }
    }

    .quality-trend {
      .trend-chart {
        .chart-header {
          text-align: center;
          margin-bottom: 20px;
          font-size: 14px;
          color: #8c8c8c;
        }

        .chart-content {
          height: 200px;
          position: relative;
          background: linear-gradient(to bottom, #f0f2f5 0%, #ffffff 100%);
          border-radius: 6px;
          padding: 20px;

          .trend-lines {
            position: relative;
            height: 100%;

            .trend-point {
              position: absolute;
              bottom: 0;
              width: 2px;

              .point-marker {
                background: linear-gradient(to top, #1890ff, #52c41a);
                width: 100%;
                border-radius: 1px;
                transition: all 0.3s ease;

                &:hover {
                  width: 4px;
                  margin-left: -1px;
                }
              }

              .point-label {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 10px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }

  .data-preview {
    .preview-controls {
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
    }

    .preview-table {
      background: white;
      border-radius: 6px;
      overflow: hidden;
    }
  }

  .source-config {
    .config-section {
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .collection-overview {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .search-filters,
    .preview-controls {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .cleaning-monitor,
    .quality-monitor {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .source-config {
      .config-section {
        .ant-row {
          flex-direction: column;

          .ant-col {
            width: 100%;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}
</style>
