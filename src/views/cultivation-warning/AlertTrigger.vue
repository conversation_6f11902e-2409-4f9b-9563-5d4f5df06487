<template>
  <div class="alert-trigger">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警触发与通知</h1>
        <p>实时预警监控和智能通知推送管理</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" danger @click="showManualTrigger">
            <alert-outlined />
            手动触发预警
          </a-button>
          <a-button @click="refreshData">
            <reload-outlined />
            刷新数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 预警监控概览 -->
    <div class="alert-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日预警"
              :value="alertStats.todayAlerts"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="处理中"
              :value="alertStats.processingAlerts"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="通知成功率"
              :value="notificationStats.successRate"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="渝快政同步"
              :value="ykzStats.syncCount"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 自动预警监控 -->
        <a-tab-pane key="monitor" tab="自动预警监控">
          <div class="alert-monitor">
            <!-- 实时监控面板 -->
            <a-row :gutter="24">
              <a-col :span="16">
                <a-card title="实时预警监控" size="small">
                  <template #extra>
                    <a-space>
                      <a-select v-model:value="selectedLevel" placeholder="预警级别" style="width: 120px" allow-clear>
                        <a-select-option value="critical">严重</a-select-option>
                        <a-select-option value="major">重要</a-select-option>
                        <a-select-option value="general">一般</a-select-option>
                        <a-select-option value="info">信息</a-select-option>
                      </a-select>
                      <a-select v-model:value="selectedStatus" placeholder="处理状态" style="width: 120px" allow-clear>
                        <a-select-option value="pending">待处理</a-select-option>
                        <a-select-option value="processing">处理中</a-select-option>
                        <a-select-option value="completed">已完成</a-select-option>
                      </a-select>
                    </a-space>
                  </template>
                  
                  <div class="alert-list">
                    <div v-if="loading" class="loading-state">
                      <a-spin size="large" />
                    </div>
                    
                    <div v-else-if="filteredAlerts.length === 0" class="empty-state">
                      <a-empty description="暂无预警数据">
                        <template #image>
                          <alert-outlined style="font-size: 48px; color: #d9d9d9;" />
                        </template>
                      </a-empty>
                    </div>
                    
                    <div v-else class="alert-items">
                      <div 
                        v-for="alert in filteredAlerts" 
                        :key="alert.id"
                        class="alert-item"
                        :class="alert.level"
                        @click="handleAlertClick(alert)"
                      >
                        <div class="alert-header">
                          <div class="alert-level">
                            <a-tag :color="getLevelColor(alert.level)">
                              {{ getLevelText(alert.level) }}
                            </a-tag>
                          </div>
                          <div class="alert-status">
                            <a-tag :color="getStatusColor(alert.status)">
                              {{ getStatusText(alert.status) }}
                            </a-tag>
                          </div>
                          <div class="alert-time">
                            {{ alert.triggerTime }}
                          </div>
                        </div>
                        <div class="alert-content">
                          <div class="alert-title">{{ alert.title }}</div>
                          <div class="alert-description">{{ alert.content }}</div>
                        </div>
                        <div class="alert-footer">
                          <div class="alert-info">
                            <span class="alert-rule">规则：{{ alert.ruleName }}</span>
                            <span class="alert-handler" v-if="alert.handlerName">
                              处理人：{{ alert.handlerName }}
                            </span>
                          </div>
                          <div class="alert-actions">
                            <a-button size="small" @click.stop="handleAlertDetail(alert)">
                              详情
                            </a-button>
                            <a-button size="small" @click.stop="handleAlertProcess(alert)" v-if="alert.status === 'pending'">
                              处理
                            </a-button>
                            <a-button size="small" @click.stop="resendNotification(alert)">
                              重发通知
                            </a-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="预警统计" size="small">
                  <div class="alert-statistics">
                    <div class="stat-item">
                      <div class="stat-header">
                        <span class="stat-name">严重预警</span>
                        <span class="stat-value critical">{{ alertStats.criticalAlerts }}</span>
                      </div>
                      <a-progress 
                        :percent="getCriticalPercent()" 
                        stroke-color="#ff4d4f"
                        size="small"
                      />
                    </div>
                    
                    <div class="stat-item">
                      <div class="stat-header">
                        <span class="stat-name">重要预警</span>
                        <span class="stat-value major">{{ alertStats.majorAlerts }}</span>
                      </div>
                      <a-progress 
                        :percent="getMajorPercent()" 
                        stroke-color="#faad14"
                        size="small"
                      />
                    </div>
                    
                    <div class="stat-item">
                      <div class="stat-header">
                        <span class="stat-name">一般预警</span>
                        <span class="stat-value general">{{ alertStats.generalAlerts }}</span>
                      </div>
                      <a-progress 
                        :percent="getGeneralPercent()" 
                        stroke-color="#1890ff"
                        size="small"
                      />
                    </div>
                    
                    <div class="stat-item">
                      <div class="stat-header">
                        <span class="stat-name">处理率</span>
                        <span class="stat-value">{{ alertStats.handleRate }}%</span>
                      </div>
                      <a-progress 
                        :percent="alertStats.handleRate" 
                        stroke-color="#52c41a"
                        size="small"
                      />
                    </div>
                  </div>
                </a-card>
                
                <a-card title="预警趋势" size="small" style="margin-top: 16px;">
                  <div class="alert-trend">
                    <div class="trend-chart">
                      <div class="chart-header">
                        <span>近7天预警趋势</span>
                      </div>
                      <div class="chart-content">
                        <div class="trend-lines">
                          <div 
                            v-for="(item, index) in alertStats.alertTrend" 
                            :key="item.date"
                            class="trend-point"
                            :style="{ left: `${(index / Math.max(1, alertStats.alertTrend.length - 1)) * 100}%` }"
                          >
                            <div class="point-marker" :style="{ height: `${Math.min(item.totalCount * 5, 100)}%` }"></div>
                            <div class="point-label">{{ item.date.slice(-2) }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 通知推送配置 -->
        <a-tab-pane key="notification" tab="通知推送配置">
          <div class="notification-config">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="推送渠道配置" size="small">
                  <div class="channel-list">
                    <div 
                      v-for="channel in pushChannels" 
                      :key="channel.id"
                      class="channel-item"
                    >
                      <div class="channel-header">
                        <div class="channel-info">
                          <component :is="getChannelIcon(channel.id)" class="channel-icon" />
                          <div class="channel-details">
                            <div class="channel-name">{{ channel.name }}</div>
                            <div class="channel-description">{{ getChannelDescription(channel.id) }}</div>
                          </div>
                        </div>
                        <div class="channel-status">
                          <a-badge :status="getChannelStatusBadge(channel)" />
                          <a-switch 
                            v-model:checked="channel.enabled" 
                            size="small"
                            @change="toggleChannel(channel)"
                          />
                        </div>
                      </div>
                      
                      <div class="channel-config" v-if="channel.enabled">
                        <a-descriptions :column="2" size="small">
                          <a-descriptions-item label="配置状态">
                            <a-tag :color="channel.configured ? 'green' : 'orange'">
                              {{ channel.configured ? '已配置' : '未配置' }}
                            </a-tag>
                          </a-descriptions-item>
                          <a-descriptions-item label="最后测试">
                            {{ channel.lastTest || '未测试' }}
                          </a-descriptions-item>
                          <a-descriptions-item label="成功率">
                            {{ channel.successRate }}%
                          </a-descriptions-item>
                          <a-descriptions-item label="今日发送">
                            {{ channel.todaySent }} 条
                          </a-descriptions-item>
                        </a-descriptions>
                        
                        <div class="channel-actions">
                          <a-space>
                            <a-button size="small" @click="configureChannel(channel)">
                              配置
                            </a-button>
                            <a-button size="small" @click="testChannel(channel)">
                              测试
                            </a-button>
                            <a-button size="small" @click="viewChannelLogs(channel)">
                              日志
                            </a-button>
                          </a-space>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="通知模板配置" size="small">
                  <div class="template-config">
                    <a-form layout="vertical">
                      <a-form-item label="模板类型">
                        <a-select v-model:value="selectedTemplate" placeholder="选择通知模板">
                          <a-select-option value="critical">严重预警模板</a-select-option>
                          <a-select-option value="major">重要预警模板</a-select-option>
                          <a-select-option value="general">一般预警模板</a-select-option>
                          <a-select-option value="reminder">提醒模板</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item label="模板内容">
                        <a-textarea
                          v-model:value="templateContent"
                          placeholder="请输入模板内容，支持变量：{title}, {content}, {level}, {time}"
                          :rows="6"
                        />
                      </a-form-item>
                      
                      <a-form-item label="推送渠道">
                        <a-checkbox-group v-model:value="templateChannels">
                          <a-checkbox value="system">系统内通知</a-checkbox>
                          <a-checkbox value="sms">短信</a-checkbox>
                          <a-checkbox value="email">邮件</a-checkbox>
                          <a-checkbox value="ykz">渝快政</a-checkbox>
                          <a-checkbox value="wechat">微信</a-checkbox>
                        </a-checkbox-group>
                      </a-form-item>
                      
                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="saveTemplate">保存模板</a-button>
                          <a-button @click="previewTemplate">预览</a-button>
                          <a-button @click="testTemplate">测试发送</a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 推送历史 -->
        <a-tab-pane key="history" tab="推送历史">
          <div class="push-history">
            <!-- 搜索筛选 -->
            <div class="history-filters">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="historyKeyword"
                    placeholder="搜索通知内容"
                    allow-clear
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="historyChannel"
                    placeholder="推送渠道"
                    allow-clear
                  >
                    <a-select-option value="system">系统内通知</a-select-option>
                    <a-select-option value="sms">短信</a-select-option>
                    <a-select-option value="email">邮件</a-select-option>
                    <a-select-option value="ykz">渝快政</a-select-option>
                    <a-select-option value="wechat">微信</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="historyStatus"
                    placeholder="发送状态"
                    allow-clear
                  >
                    <a-select-option value="success">成功</a-select-option>
                    <a-select-option value="failed">失败</a-select-option>
                    <a-select-option value="pending">发送中</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-range-picker v-model:value="historyDateRange" />
                </a-col>
                <a-col :span="4">
                  <a-space>
                    <a-button @click="resetHistoryFilters">重置</a-button>
                    <a-button type="primary" @click="loadPushHistory">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 推送历史列表 -->
            <div class="history-table">
              <a-table
                :columns="historyColumns"
                :data-source="pushHistory"
                :loading="historyLoading"
                :pagination="historyPagination"
                row-key="id"
                @change="handleHistoryTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'channel'">
                    <a-tag :color="getChannelColor(record.channel)">
                      {{ getChannelText(record.channel) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getHistoryStatusBadge(record.status)" 
                      :text="getHistoryStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'content'">
                    <div class="content-preview">
                      <div class="content-title">{{ record.title }}</div>
                      <div class="content-text">{{ record.content }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewHistoryDetail(record)">
                        详情
                      </a-button>
                      <a-button 
                        type="link" 
                        size="small" 
                        @click="retryPush(record)"
                        v-if="record.status === 'failed'"
                      >
                        重试
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 渝快政接口 -->
        <a-tab-pane key="ykz" tab="渝快政接口">
          <div class="ykz-interface">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="接口状态监控" size="small">
                  <div class="interface-status">
                    <a-descriptions :column="1" bordered size="small">
                      <a-descriptions-item label="接口状态">
                        <a-badge :status="ykzStatus.status === 'online' ? 'success' : 'error'" />
                        <span>{{ ykzStatus.status === 'online' ? '在线' : '离线' }}</span>
                      </a-descriptions-item>
                      <a-descriptions-item label="最后同步">
                        {{ ykzStatus.lastSync }}
                      </a-descriptions-item>
                      <a-descriptions-item label="同步成功率">
                        {{ ykzStatus.successRate }}%
                      </a-descriptions-item>
                      <a-descriptions-item label="今日同步">
                        {{ ykzStatus.todaySync }} 次
                      </a-descriptions-item>
                      <a-descriptions-item label="响应时间">
                        {{ ykzStatus.responseTime }}ms
                      </a-descriptions-item>
                    </a-descriptions>
                    
                    <div class="interface-actions" style="margin-top: 16px;">
                      <a-space>
                        <a-button type="primary" @click="testYkzConnection">
                          测试连接
                        </a-button>
                        <a-button @click="syncToYkz">
                          立即同步
                        </a-button>
                        <a-button @click="viewYkzLogs">
                          查看日志
                        </a-button>
                      </a-space>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="同步配置" size="small">
                  <div class="sync-config">
                    <a-form layout="vertical">
                      <a-form-item label="同步频率">
                        <a-select v-model:value="ykzConfig.syncFrequency" placeholder="选择同步频率">
                          <a-select-option value="realtime">实时同步</a-select-option>
                          <a-select-option value="5min">每5分钟</a-select-option>
                          <a-select-option value="15min">每15分钟</a-select-option>
                          <a-select-option value="30min">每30分钟</a-select-option>
                          <a-select-option value="1hour">每小时</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item label="同步内容">
                        <a-checkbox-group v-model:value="ykzConfig.syncContent">
                          <a-checkbox value="alerts">预警信息</a-checkbox>
                          <a-checkbox value="supervision">督办信息</a-checkbox>
                          <a-checkbox value="reports">报告信息</a-checkbox>
                        </a-checkbox-group>
                      </a-form-item>
                      
                      <a-form-item label="接口地址">
                        <a-input v-model:value="ykzConfig.apiUrl" placeholder="渝快政接口地址" />
                      </a-form-item>
                      
                      <a-form-item label="认证密钥">
                        <a-input-password v-model:value="ykzConfig.apiKey" placeholder="API密钥" />
                      </a-form-item>
                      
                      <a-form-item>
                        <a-checkbox v-model:checked="ykzConfig.autoRetry">
                          失败自动重试
                        </a-checkbox>
                      </a-form-item>
                      
                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="saveYkzConfig">保存配置</a-button>
                          <a-button @click="resetYkzConfig">重置</a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 手动触发预警弹窗 -->
    <a-modal
      v-model:visible="manualTriggerVisible"
      title="手动触发预警"
      width="600px"
      @ok="triggerManualAlert"
      @cancel="cancelManualTrigger"
    >
      <a-form :model="manualAlertForm" layout="vertical">
        <a-form-item label="预警级别" required>
          <a-select v-model:value="manualAlertForm.level" placeholder="选择预警级别">
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="major">重要</a-select-option>
            <a-select-option value="general">一般</a-select-option>
            <a-select-option value="info">信息</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="预警标题" required>
          <a-input v-model:value="manualAlertForm.title" placeholder="请输入预警标题" />
        </a-form-item>
        
        <a-form-item label="预警内容" required>
          <a-textarea v-model:value="manualAlertForm.content" placeholder="请输入预警内容" :rows="4" />
        </a-form-item>
        
        <a-form-item label="影响对象">
          <a-select v-model:value="manualAlertForm.affectedObjects" mode="multiple" placeholder="选择影响对象">
            <a-select-option value="obj_001">重庆市某科技公司</a-select-option>
            <a-select-option value="obj_002">重庆市某制造企业</a-select-option>
            <a-select-option value="obj_003">重庆市某服务企业</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="通知方式">
          <a-checkbox-group v-model:value="manualAlertForm.notificationMethods">
            <a-checkbox value="system">系统内通知</a-checkbox>
            <a-checkbox value="sms">短信</a-checkbox>
            <a-checkbox value="email">邮件</a-checkbox>
            <a-checkbox value="ykz">渝快政</a-checkbox>
            <a-checkbox value="wechat">微信</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="目标用户">
          <a-select v-model:value="manualAlertForm.targetUsers" mode="multiple" placeholder="选择目标用户">
            <a-select-option value="admin">管理员</a-select-option>
            <a-select-option value="supervisor">督办员</a-select-option>
            <a-select-option value="tech_admin">技术管理员</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  AlertOutlined,
  ReloadOutlined,
  SearchOutlined,
  BellOutlined,
  MessageOutlined,
  MobileOutlined,
  MailOutlined,
  WechatOutlined,
  GlobalOutlined
} from '@ant-design/icons-vue'
import type {
  AlertRecord,
  AlertStatistics,
  AlertLevel,
  AlertStatus,
  NotificationMethod
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'

// 响应式数据
const loading = ref(false)
const activeTab = ref('monitor')

// 预警监控
const selectedLevel = ref<AlertLevel | undefined>()
const selectedStatus = ref<AlertStatus | undefined>()
const alertsList = ref<AlertRecord[]>([])

// 统计数据
const alertStats = reactive<AlertStatistics>({
  totalAlerts: 0,
  criticalAlerts: 0,
  majorAlerts: 0,
  generalAlerts: 0,
  infoAlerts: 0,
  pendingAlerts: 0,
  processingAlerts: 0,
  completedAlerts: 0,
  cancelledAlerts: 0,
  todayAlerts: 0,
  weekAlerts: 0,
  monthAlerts: 0,
  averageHandleTime: 0,
  handleRate: 0,
  alertTrend: [],
  topAlertRules: [],
  alertsByLevel: {} as any,
  alertsByStatus: {} as any
})

// 通知统计
const notificationStats = reactive({
  totalSent: 0,
  successCount: 0,
  failedCount: 0,
  successRate: 0
})

// 渝快政统计
const ykzStats = reactive({
  syncCount: 0,
  lastSync: '',
  status: 'online'
})

// 推送渠道配置
const pushChannels = ref([
  {
    id: 'system',
    name: '系统内通知',
    enabled: true,
    configured: true,
    lastTest: '2025-06-20 10:00:00',
    successRate: 98.5,
    todaySent: 156
  },
  {
    id: 'sms',
    name: '短信推送',
    enabled: true,
    configured: true,
    lastTest: '2025-06-20 09:30:00',
    successRate: 95.2,
    todaySent: 89
  },
  {
    id: 'email',
    name: '邮件推送',
    enabled: true,
    configured: true,
    lastTest: '2025-06-20 08:45:00',
    successRate: 92.8,
    todaySent: 67
  },
  {
    id: 'ykz',
    name: '渝快政推送',
    enabled: true,
    configured: false,
    lastTest: null,
    successRate: 0,
    todaySent: 0
  },
  {
    id: 'wechat',
    name: '微信推送',
    enabled: false,
    configured: false,
    lastTest: null,
    successRate: 0,
    todaySent: 0
  }
])

// 通知模板配置
const selectedTemplate = ref('critical')
const templateContent = ref('【{level}预警】{title}\n\n{content}\n\n触发时间：{time}')
const templateChannels = ref(['system', 'email'])

// 推送历史
const historyKeyword = ref('')
const historyChannel = ref<string | undefined>()
const historyStatus = ref<string | undefined>()
const historyDateRange = ref()
const historyLoading = ref(false)
const pushHistory = ref<any[]>([])

// 推送历史分页
const historyPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 推送历史表格列配置
const historyColumns = [
  {
    title: '通知内容',
    dataIndex: 'content',
    key: 'content',
    width: 300
  },
  {
    title: '推送渠道',
    dataIndex: 'channel',
    key: 'channel',
    width: 120
  },
  {
    title: '发送状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '目标用户',
    dataIndex: 'targetUser',
    key: 'targetUser',
    width: 120
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    key: 'sendTime',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 120
  }
]

// 渝快政接口状态
const ykzStatus = reactive({
  status: 'online',
  lastSync: '2025-06-20 10:30:00',
  successRate: 95.5,
  todaySync: 23,
  responseTime: 156
})

// 渝快政配置
const ykzConfig = reactive({
  syncFrequency: 'realtime',
  syncContent: ['alerts', 'supervision'],
  apiUrl: 'https://api.cq.gov.cn/ykz/sync',
  apiKey: '',
  autoRetry: true
})

// 手动触发预警
const manualTriggerVisible = ref(false)
const manualAlertForm = reactive({
  level: 'general' as AlertLevel,
  title: '',
  content: '',
  affectedObjects: [] as string[],
  notificationMethods: ['system'] as NotificationMethod[],
  targetUsers: [] as string[]
})

// 计算属性
const filteredAlerts = computed(() => {
  let filtered = [...alertsList.value]

  if (selectedLevel.value) {
    filtered = filtered.filter(alert => alert.level === selectedLevel.value)
  }

  if (selectedStatus.value) {
    filtered = filtered.filter(alert => alert.status === selectedStatus.value)
  }

  return filtered
})

const getCriticalPercent = computed(() => {
  return alertStats.totalAlerts > 0 ? Math.round((alertStats.criticalAlerts / alertStats.totalAlerts) * 100) : 0
})

const getMajorPercent = computed(() => {
  return alertStats.totalAlerts > 0 ? Math.round((alertStats.majorAlerts / alertStats.totalAlerts) * 100) : 0
})

const getGeneralPercent = computed(() => {
  return alertStats.totalAlerts > 0 ? Math.round((alertStats.generalAlerts / alertStats.totalAlerts) * 100) : 0
})

// 方法
const loadAlerts = async () => {
  loading.value = true
  try {
    const result = await MockCultivationWarningService.getAlertRecords({
      page: 1,
      pageSize: 20,
      sortBy: 'triggerTime',
      sortOrder: 'desc'
    })
    alertsList.value = result.data
  } catch (error) {
    console.error('加载预警记录失败:', error)
    message.error('加载预警记录失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const stats = await MockCultivationWarningService.getAlertStatistics()
    Object.assign(alertStats, stats)

    // 更新通知统计
    notificationStats.totalSent = 312
    notificationStats.successCount = 298
    notificationStats.failedCount = 14
    notificationStats.successRate = 95.5

    // 更新渝快政统计
    ykzStats.syncCount = 23
    ykzStats.lastSync = '2025-06-20 10:30:00'
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const refreshData = () => {
  loadAlerts()
  loadStatistics()
  message.success('数据已刷新')
}

// 预警操作
const handleAlertClick = (alert: AlertRecord) => {
  message.info(`查看预警：${alert.title}`)
}

const handleAlertDetail = (alert: AlertRecord) => {
  message.info(`查看预警详情：${alert.title}`)
}

const handleAlertProcess = (alert: AlertRecord) => {
  message.info(`处理预警：${alert.title}`)
}

const resendNotification = (alert: AlertRecord) => {
  message.success(`重发通知：${alert.title}`)
}

// 推送渠道操作
const getChannelIcon = (channelId: string) => {
  const icons = {
    system: BellOutlined,
    sms: MessageOutlined,
    ykz: MobileOutlined,
    email: MailOutlined,
    wechat: WechatOutlined
  }
  return icons[channelId as keyof typeof icons] || GlobalOutlined
}

const getChannelDescription = (channelId: string) => {
  const descriptions = {
    system: '系统内消息推送，用户登录后可在消息中心查看',
    sms: '短信推送，通过短信网关发送到用户手机',
    ykz: '渝快政推送，重庆市政务服务平台消息推送',
    email: '邮件推送，通过邮件服务器发送到用户邮箱',
    wechat: '微信推送，通过微信公众号或企业微信推送'
  }
  return descriptions[channelId as keyof typeof descriptions] || '第三方推送渠道'
}

const getChannelStatusBadge = (channel: any) => {
  if (!channel.enabled) return 'default'
  if (!channel.configured) return 'warning'
  return 'success'
}

const toggleChannel = (channel: any) => {
  message.success(`${channel.enabled ? '启用' : '禁用'}推送渠道：${channel.name}`)
}

const configureChannel = (channel: any) => {
  message.info(`配置推送渠道：${channel.name}`)
}

const testChannel = (channel: any) => {
  message.success(`测试推送渠道：${channel.name}`)
}

const viewChannelLogs = (channel: any) => {
  message.info(`查看推送渠道日志：${channel.name}`)
}

// 通知模板操作
const saveTemplate = () => {
  message.success('通知模板保存成功')
}

const previewTemplate = () => {
  message.info('预览通知模板')
}

const testTemplate = () => {
  message.success('测试发送通知模板')
}

// 推送历史操作
const loadPushHistory = () => {
  historyLoading.value = true

  // 模拟加载推送历史
  setTimeout(() => {
    pushHistory.value = Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      title: `预警通知${i + 1}`,
      content: `这是第${i + 1}条预警通知内容`,
      channel: ['system', 'sms', 'email', 'ykz'][Math.floor(Math.random() * 4)],
      status: ['success', 'failed', 'pending'][Math.floor(Math.random() * 3)],
      targetUser: `用户${i + 1}`,
      sendTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    }))
    historyLoading.value = false
  }, 1000)
}

const resetHistoryFilters = () => {
  historyKeyword.value = ''
  historyChannel.value = undefined
  historyStatus.value = undefined
  historyDateRange.value = undefined
  historyPagination.current = 1
  loadPushHistory()
}

const handleHistoryTableChange = (pag: any) => {
  historyPagination.current = pag.current
  historyPagination.pageSize = pag.pageSize
  loadPushHistory()
}

const viewHistoryDetail = (record: any) => {
  message.info(`查看推送详情：${record.title}`)
}

const retryPush = (record: any) => {
  message.success(`重试推送：${record.title}`)
}

// 渝快政接口操作
const testYkzConnection = () => {
  message.success('渝快政接口连接测试成功')
}

const syncToYkz = () => {
  message.success('开始同步数据到渝快政')
}

const viewYkzLogs = () => {
  message.info('查看渝快政同步日志')
}

const saveYkzConfig = () => {
  message.success('渝快政配置保存成功')
}

const resetYkzConfig = () => {
  Object.assign(ykzConfig, {
    syncFrequency: 'realtime',
    syncContent: ['alerts', 'supervision'],
    apiUrl: 'https://api.cq.gov.cn/ykz/sync',
    apiKey: '',
    autoRetry: true
  })
  message.info('渝快政配置已重置')
}

// 手动触发预警
const showManualTrigger = () => {
  manualTriggerVisible.value = true
}

const triggerManualAlert = () => {
  if (!manualAlertForm.title || !manualAlertForm.content) {
    message.error('请填写完整的预警信息')
    return
  }

  message.success('手动预警触发成功')
  manualTriggerVisible.value = false
  resetManualAlertForm()
  loadAlerts()
}

const cancelManualTrigger = () => {
  manualTriggerVisible.value = false
  resetManualAlertForm()
}

const resetManualAlertForm = () => {
  Object.assign(manualAlertForm, {
    level: 'general' as AlertLevel,
    title: '',
    content: '',
    affectedObjects: [],
    notificationMethods: ['system'],
    targetUsers: []
  })
}

// 工具方法
const getLevelColor = (level: AlertLevel) => {
  const colors = {
    critical: 'red',
    major: 'orange',
    general: 'blue',
    info: 'green'
  }
  return colors[level] || 'default'
}

const getLevelText = (level: AlertLevel) => {
  const texts = {
    critical: '严重',
    major: '重要',
    general: '一般',
    info: '信息'
  }
  return texts[level] || level
}

const getStatusColor = (status: AlertStatus) => {
  const colors = {
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    cancelled: 'default'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: AlertStatus) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getChannelColor = (channel: string) => {
  const colors: Record<string, string> = {
    system: 'blue',
    sms: 'green',
    email: 'orange',
    ykz: 'purple',
    wechat: 'cyan'
  }
  return colors[channel] || 'default'
}

const getChannelText = (channel: string) => {
  const texts: Record<string, string> = {
    system: '系统内通知',
    sms: '短信',
    email: '邮件',
    ykz: '渝快政',
    wechat: '微信'
  }
  return texts[channel] || channel
}

const getHistoryStatusBadge = (status: string) => {
  const badges: Record<string, string> = {
    success: 'success',
    failed: 'error',
    pending: 'processing'
  }
  return badges[status] || 'default'
}

const getHistoryStatusText = (status: string) => {
  const texts: Record<string, string> = {
    success: '成功',
    failed: '失败',
    pending: '发送中'
  }
  return texts[status] || status
}

// 组件挂载时加载数据
onMounted(() => {
  loadAlerts()
  loadStatistics()
  loadPushHistory()
})
</script>

<style lang="scss" scoped>
.alert-trigger {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .alert-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .alert-monitor {
    .loading-state,
    .empty-state {
      text-align: center;
      padding: 40px 20px;
    }

    .alert-items {
      max-height: 600px;
      overflow-y: auto;

      .alert-item {
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 12px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }

        &.critical {
          border-left: 4px solid #ff4d4f;
        }

        &.major {
          border-left: 4px solid #faad14;
        }

        &.general {
          border-left: 4px solid #1890ff;
        }

        &.info {
          border-left: 4px solid #52c41a;
        }

        .alert-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .alert-time {
            font-size: 12px;
            color: #8c8c8c;
          }
        }

        .alert-content {
          margin-bottom: 12px;

          .alert-title {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
          }

          .alert-description {
            font-size: 13px;
            color: #595959;
            line-height: 1.5;
          }
        }

        .alert-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .alert-info {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .alert-statistics {
      .stat-item {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .stat-name {
            font-size: 14px;
            color: #8c8c8c;
          }

          .stat-value {
            font-size: 18px;
            font-weight: 600;

            &.critical {
              color: #ff4d4f;
            }

            &.major {
              color: #faad14;
            }

            &.general {
              color: #1890ff;
            }
          }
        }
      }
    }

    .alert-trend {
      .trend-chart {
        .chart-header {
          text-align: center;
          margin-bottom: 20px;
          font-size: 14px;
          color: #8c8c8c;
        }

        .chart-content {
          height: 150px;
          position: relative;
          background: linear-gradient(to bottom, #f0f2f5 0%, #ffffff 100%);
          border-radius: 6px;
          padding: 20px;

          .trend-lines {
            position: relative;
            height: 100%;

            .trend-point {
              position: absolute;
              bottom: 0;
              width: 2px;

              .point-marker {
                background: linear-gradient(to top, #1890ff, #52c41a);
                width: 100%;
                border-radius: 1px;
                transition: all 0.3s ease;

                &:hover {
                  width: 4px;
                  margin-left: -1px;
                }
              }

              .point-label {
                position: absolute;
                bottom: -20px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 10px;
                color: #8c8c8c;
              }
            }
          }
        }
      }
    }
  }

  .notification-config {
    .channel-list {
      .channel-item {
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin-bottom: 16px;
        background: white;

        .channel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .channel-info {
            display: flex;
            align-items: center;

            .channel-icon {
              font-size: 20px;
              color: #1890ff;
              margin-right: 12px;
            }

            .channel-details {
              .channel-name {
                font-weight: 600;
                color: #262626;
                margin-bottom: 4px;
              }

              .channel-description {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }

          .channel-status {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }

        .channel-config {
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;

          .channel-actions {
            margin-top: 12px;
          }
        }
      }
    }

    .template-config {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }
  }

  .push-history {
    .history-filters {
      margin-bottom: 16px;
      padding: 16px;
      background: white;
      border-radius: 6px;
    }

    .history-table {
      background: white;
      border-radius: 6px;
      overflow: hidden;

      .content-preview {
        .content-title {
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .content-text {
          font-size: 12px;
          color: #8c8c8c;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 200px;
        }
      }
    }
  }

  .ykz-interface {
    .interface-status {
      .interface-actions {
        text-align: center;
      }
    }

    .sync-config {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .alert-overview {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .alert-monitor,
    .notification-config,
    .ykz-interface {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .history-filters {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .alert-items {
      .alert-item {
        .alert-footer {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}
</style>
