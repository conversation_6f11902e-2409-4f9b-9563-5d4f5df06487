# 培育过程预警系统 - 菜单跳转测试

## 菜单配置验证

### 1. 路由配置
已在 `model/src/router/index.ts` 中添加了培育过程预警系统的路由配置：

```typescript
{
  path: '/cultivation-warning',
  name: 'CultivationWarning',
  redirect: '/cultivation-warning/index',
  meta: { 
    title: '培育过程预警',
    description: '智能化培育过程监控与预警管理平台',
    requiresAuth: false,
    keepAlive: true,
    icon: 'alert'
  },
  children: [
    { path: 'index', name: 'CultivationWarningIndex', component: Index.vue },
    { path: 'config', name: 'CultivationWarningConfig', component: ConfigManagement.vue },
    { path: 'data-collection', name: 'CultivationWarningDataCollection', component: DataCollection.vue },
    { path: 'alert-trigger', name: 'CultivationWarningAlertTrigger', component: AlertTrigger.vue },
    { path: 'supervision', name: 'CultivationWarningSupervision', component: SupervisionManagement.vue },
    { path: 'report', name: 'CultivationWarningReport', component: ReportTemplate.vue }
  ]
}
```

### 2. 菜单配置
已在 `model/src/layouts/BasicLayout.vue` 中添加了菜单项：

```html
<a-sub-menu key="cultivation-warning">
  <template #icon>
    <alert-outlined />
  </template>
  <template #title>培育过程预警</template>
  <a-menu-item key="cultivation-warning-index">预警首页</a-menu-item>
  <a-menu-item key="cultivation-warning-config">预警配置管理</a-menu-item>
  <a-menu-item key="cultivation-warning-data-collection">数据采集与处理</a-menu-item>
  <a-menu-item key="cultivation-warning-alert-trigger">预警触发与通知</a-menu-item>
  <a-menu-item key="cultivation-warning-supervision">督办管理</a-menu-item>
  <a-menu-item key="cultivation-warning-report">预警报告与模板</a-menu-item>
</a-sub-menu>
```

### 3. 菜单路由映射
已添加菜单项与路由的映射关系：

```typescript
// 菜单项到路由的映射
const menuRouteMap = {
  'cultivation-warning-index': '/cultivation-warning/index',
  'cultivation-warning-config': '/cultivation-warning/config',
  'cultivation-warning-data-collection': '/cultivation-warning/data-collection',
  'cultivation-warning-alert-trigger': '/cultivation-warning/alert-trigger',
  'cultivation-warning-supervision': '/cultivation-warning/supervision',
  'cultivation-warning-report': '/cultivation-warning/report'
}

// 路由到菜单项的映射
const routeMenuMap = {
  '/cultivation-warning': { key: 'cultivation-warning-index', parentKey: 'cultivation-warning' },
  '/cultivation-warning/index': { key: 'cultivation-warning-index', parentKey: 'cultivation-warning' },
  '/cultivation-warning/config': { key: 'cultivation-warning-config', parentKey: 'cultivation-warning' },
  '/cultivation-warning/data-collection': { key: 'cultivation-warning-data-collection', parentKey: 'cultivation-warning' },
  '/cultivation-warning/alert-trigger': { key: 'cultivation-warning-alert-trigger', parentKey: 'cultivation-warning' },
  '/cultivation-warning/supervision': { key: 'cultivation-warning-supervision', parentKey: 'cultivation-warning' },
  '/cultivation-warning/report': { key: 'cultivation-warning-report', parentKey: 'cultivation-warning' }
}
```

### 4. 页面内部跳转
各页面内部的跳转功能已实现：

#### 首页跳转方法：
- `goToConfigManagement()` → `/cultivation-warning/config`
- `goToDataCollection()` → `/cultivation-warning/data-collection`
- `goToAlertTrigger()` → `/cultivation-warning/alert-trigger`
- `goToSupervision()` → `/cultivation-warning/supervision`
- `goToReportTemplate()` → `/cultivation-warning/report`

## 测试步骤

### 1. 菜单导航测试
1. 启动应用
2. 在左侧菜单中找到"培育过程预警"菜单项
3. 点击展开子菜单
4. 依次点击各个子菜单项，验证页面跳转是否正确

### 2. 页面内跳转测试
1. 访问预警首页 `/cultivation-warning/index`
2. 点击快速操作区域的各个功能卡片
3. 点击功能模块区域的"查看更多"和操作按钮
4. 验证跳转是否正确

### 3. 浏览器直接访问测试
直接在浏览器地址栏输入以下URL，验证页面是否正常加载：
- `/cultivation-warning/index` - 预警首页
- `/cultivation-warning/config` - 预警配置管理
- `/cultivation-warning/data-collection` - 数据采集与处理
- `/cultivation-warning/alert-trigger` - 预警触发与通知
- `/cultivation-warning/supervision` - 督办管理
- `/cultivation-warning/report` - 预警报告与模板

## 预期结果

1. **菜单展示**：左侧菜单中应显示"培育过程预警"主菜单项，带有警告图标
2. **子菜单展示**：点击主菜单后应展开6个子菜单项
3. **页面跳转**：点击任意菜单项应正确跳转到对应页面
4. **菜单高亮**：当前页面对应的菜单项应高亮显示
5. **面包屑导航**：页面顶部应显示正确的面包屑导航
6. **页面内跳转**：首页的各个跳转按钮应正确跳转到对应功能页面

## 故障排除

如果遇到跳转问题，请检查：

1. **路由配置**：确认 `router/index.ts` 中的路由配置正确
2. **组件导入**：确认各页面组件能正确导入
3. **菜单配置**：确认 `BasicLayout.vue` 中的菜单配置正确
4. **路由映射**：确认菜单项与路由的映射关系正确
5. **图标导入**：确认 `AlertOutlined` 图标已正确导入

## 系统完整性验证

培育过程预警系统现已完整集成到主系统中，包含：

✅ **8个核心页面**：全部实现并可通过菜单访问
✅ **路由配置**：完整的路由配置和嵌套路由
✅ **菜单集成**：完整的菜单结构和导航
✅ **页面跳转**：页面间的跳转功能
✅ **类型定义**：完整的TypeScript类型定义
✅ **模拟数据**：完整的模拟数据服务

系统现已可以进行完整的功能测试和演示。
