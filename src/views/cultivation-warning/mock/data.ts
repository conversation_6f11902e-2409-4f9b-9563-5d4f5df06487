// 培育过程预警模拟数据服务

import type {
  AlertRule,
  AlertRecord,
  DataCollectionModel,
  SupervisionObject,
  ReportTemplate,
  AlertStatistics,
  DataCollectionStatistics,
  SupervisionStatistics,
  AlertLevel,
  AlertStatus,
  DataCollectionStatus,
  SupervisionStatus,
  DataSourceType,
  NotificationMethod,
  ReportType,
  AlertQueryParams,
  DataCollectionQueryParams,
  SupervisionQueryParams,
  OperationResult,
  BatchOperationResult
} from '@/types/cultivation-warning'

// 模拟预警规则数据
const mockAlertRules: AlertRule[] = [
  {
    id: 'rule_001',
    name: '培育对象评分异常预警',
    description: '当培育对象评分低于60分时触发预警',
    level: 'critical' as AlertLevel,
    isEnabled: true,
    triggerConditions: [
      {
        id: 'condition_001',
        field: 'evaluationScore',
        operator: 'lt',
        value: 60
      }
    ],
    notificationMethods: ['system', 'email', 'ykz'] as NotificationMethod[],
    targetUsers: ['admin', 'supervisor'],
    targetRoles: ['管理员', '督办员'],
    cooldownPeriod: 60,
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-15 14:30:00',
    createdBy: 'admin',
    lastTriggeredTime: '2025-06-20 09:15:00',
    triggerCount: 15
  },
  {
    id: 'rule_002',
    name: '数据采集失败预警',
    description: '数据采集连续失败3次时触发预警',
    level: 'major' as AlertLevel,
    isEnabled: true,
    triggerConditions: [
      {
        id: 'condition_002',
        field: 'failureCount',
        operator: 'gte',
        value: 3
      }
    ],
    notificationMethods: ['system', 'sms'] as NotificationMethod[],
    targetUsers: ['tech_admin'],
    targetRoles: ['技术管理员'],
    cooldownPeriod: 30,
    createTime: '2025-06-02 11:00:00',
    updateTime: '2025-06-16 15:30:00',
    createdBy: 'tech_admin',
    lastTriggeredTime: '2025-06-19 16:20:00',
    triggerCount: 8
  },
  {
    id: 'rule_003',
    name: '督办逾期预警',
    description: '督办任务超过预期完成时间时触发预警',
    level: 'general' as AlertLevel,
    isEnabled: true,
    triggerConditions: [
      {
        id: 'condition_003',
        field: 'expectedCompletionTime',
        operator: 'lt',
        value: 'now'
      }
    ],
    notificationMethods: ['system', 'email'] as NotificationMethod[],
    targetUsers: ['supervisor'],
    targetRoles: ['督办员'],
    cooldownPeriod: 120,
    createTime: '2025-06-03 12:00:00',
    updateTime: '2025-06-17 16:30:00',
    createdBy: 'supervisor',
    lastTriggeredTime: '2025-06-18 10:45:00',
    triggerCount: 12
  }
]

// 模拟预警记录数据
const mockAlertRecords: AlertRecord[] = [
  {
    id: 'alert_001',
    ruleId: 'rule_001',
    ruleName: '培育对象评分异常预警',
    level: 'critical' as AlertLevel,
    status: 'processing' as AlertStatus,
    title: '培育对象"重庆市某科技公司"评分异常',
    content: '该培育对象最新评分为45分，低于预警阈值60分，需要立即关注和处理',
    triggerTime: '2025-06-20 09:15:00',
    triggerData: {
      objectId: 'obj_001',
      objectName: '重庆市某科技公司',
      currentScore: 45,
      threshold: 60,
      previousScore: 72
    },
    affectedObjects: ['obj_001'],
    notificationSent: true,
    notificationMethods: ['system', 'email', 'ykz'] as NotificationMethod[],
    handlerId: 'supervisor_001',
    handlerName: '张督办',
    handleTime: '2025-06-20 10:30:00',
    handleNotes: '已联系培育对象，了解评分下降原因，制定改进计划',
    createTime: '2025-06-20 09:15:00',
    updateTime: '2025-06-20 10:30:00'
  },
  {
    id: 'alert_002',
    ruleId: 'rule_002',
    ruleName: '数据采集失败预警',
    level: 'major' as AlertLevel,
    status: 'completed' as AlertStatus,
    title: '数据源"培育对象基础信息"采集失败',
    content: '数据源连续3次采集失败，可能存在网络或配置问题',
    triggerTime: '2025-06-19 16:20:00',
    triggerData: {
      sourceId: 'source_001',
      sourceName: '培育对象基础信息',
      failureCount: 3,
      lastError: '连接超时'
    },
    affectedObjects: ['source_001'],
    notificationSent: true,
    notificationMethods: ['system', 'sms'] as NotificationMethod[],
    handlerId: 'tech_admin_001',
    handlerName: '李技术',
    handleTime: '2025-06-19 17:00:00',
    handleNotes: '已修复网络配置问题，数据采集恢复正常',
    resolveTime: '2025-06-19 18:30:00',
    resolveNotes: '问题已解决，数据采集正常运行',
    createTime: '2025-06-19 16:20:00',
    updateTime: '2025-06-19 18:30:00'
  }
]

// 模拟数据采集模型数据
const mockDataCollectionModels: DataCollectionModel[] = [
  {
    id: 'model_001',
    name: '培育对象基础信息采集',
    description: '定期采集培育对象的基础信息和评分数据',
    sourceType: 'database' as DataSourceType,
    sourceConfig: {
      host: '*************',
      port: 3306,
      database: 'cultivation_db',
      username: 'data_collector',
      query: 'SELECT * FROM cultivation_objects WHERE update_time > ?',
      timeout: 30000
    },
    collectionFrequency: 60,
    dataFormat: 'json',
    isEnabled: true,
    lastCollectionTime: '2025-06-20 08:00:00',
    nextCollectionTime: '2025-06-20 09:00:00',
    status: 'completed' as DataCollectionStatus,
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-15 14:30:00',
    createdBy: 'admin',
    collectionCount: 480,
    successRate: 98.5
  },
  {
    id: 'model_002',
    name: '评分数据API采集',
    description: '通过API接口采集培育对象的实时评分数据',
    sourceType: 'api' as DataSourceType,
    sourceConfig: {
      url: 'https://api.cultivation.gov.cn/scores',
      method: 'GET',
      headers: {
        'Authorization': 'Bearer token123',
        'Content-Type': 'application/json'
      },
      timeout: 15000
    },
    collectionFrequency: 30,
    dataFormat: 'json',
    isEnabled: true,
    lastCollectionTime: '2025-06-20 08:30:00',
    nextCollectionTime: '2025-06-20 09:00:00',
    status: 'collecting' as DataCollectionStatus,
    createTime: '2025-06-02 11:00:00',
    updateTime: '2025-06-16 15:30:00',
    createdBy: 'tech_admin',
    collectionCount: 960,
    successRate: 95.2
  }
]

// 模拟督办对象数据
const mockSupervisionObjects: SupervisionObject[] = [
  {
    id: 'obj_001',
    name: '重庆市某科技公司',
    type: '科技型企业',
    description: '专注于人工智能技术研发的科技企业',
    responsiblePerson: '王总经理',
    responsibleDepartment: '市科技局',
    contactInfo: {
      phone: '023-12345678',
      email: '<EMAIL>',
      address: '重庆市渝北区创新大道123号'
    },
    evaluationScore: 45,
    evaluationLevel: 'C级',
    supervisionStatus: 'supervising' as SupervisionStatus,
    supervisionStartTime: '2025-06-15 09:00:00',
    expectedCompletionTime: '2024-02-15 18:00:00',
    supervisionNotes: '评分下降明显，需要重点关注和指导',
    improvementPlan: '1. 加强技术研发投入 2. 完善管理制度 3. 提升产品质量',
    progressReports: [
      {
        id: 'report_001',
        reportTime: '2025-06-18 14:00:00',
        progress: 25,
        description: '已开始制定改进计划，预计本周完成初步方案',
        reportedBy: '王总经理'
      }
    ],
    attachments: [],
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-18 14:00:00',
    createdBy: 'admin',
    lastSyncTime: '2025-06-20 08:00:00'
  },
  {
    id: 'obj_002',
    name: '重庆市某制造企业',
    type: '制造业企业',
    description: '传统制造业转型升级示范企业',
    responsiblePerson: '李厂长',
    responsibleDepartment: '市经信委',
    contactInfo: {
      phone: '023-87654321',
      email: '<EMAIL>',
      address: '重庆市九龙坡区工业园区456号'
    },
    evaluationScore: 78,
    evaluationLevel: 'B级',
    supervisionStatus: 'completed' as SupervisionStatus,
    supervisionStartTime: '2025-06-01 09:00:00',
    supervisionEndTime: '2025-06-15 17:00:00',
    expectedCompletionTime: '2025-06-20 18:00:00',
    actualCompletionTime: '2025-06-15 17:00:00',
    supervisionNotes: '企业转型升级进展良好，各项指标达标',
    improvementPlan: '1. 继续推进智能化改造 2. 加强人才培养 3. 拓展市场渠道',
    progressReports: [
      {
        id: 'report_002',
        reportTime: '2025-06-10 16:00:00',
        progress: 80,
        description: '智能化改造基本完成，生产效率提升30%',
        reportedBy: '李厂长'
      },
      {
        id: 'report_003',
        reportTime: '2025-06-15 17:00:00',
        progress: 100,
        description: '所有改进措施已完成，各项指标达到预期目标',
        reportedBy: '李厂长'
      }
    ],
    attachments: [],
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-15 17:00:00',
    createdBy: 'admin',
    lastSyncTime: '2025-06-20 08:00:00'
  }
]

// 模拟报告模板数据
const mockReportTemplates: ReportTemplate[] = [
  {
    id: 'template_001',
    name: '培育过程预警日报',
    description: '每日培育过程预警情况汇总报告',
    type: 'daily' as ReportType,
    isDefault: true,
    isEnabled: true,
    templateContent: {
      title: '培育过程预警日报',
      sections: [
        {
          id: 'section_001',
          title: '预警概况',
          content: '今日预警情况统计和分析',
          order: 1,
          isRequired: true,
          dataSource: 'alert_statistics'
        },
        {
          id: 'section_002',
          title: '重点关注',
          content: '需要重点关注的预警事项',
          order: 2,
          isRequired: true,
          dataSource: 'critical_alerts'
        }
      ],
      charts: [
        {
          id: 'chart_001',
          title: '预警级别分布',
          type: 'pie',
          dataSource: 'alert_level_distribution'
        }
      ],
      tables: [
        {
          id: 'table_001',
          title: '今日预警列表',
          dataSource: 'today_alerts',
          columns: [
            { key: 'title', title: '预警标题', dataIndex: 'title' },
            { key: 'level', title: '预警级别', dataIndex: 'level' },
            { key: 'status', title: '处理状态', dataIndex: 'status' }
          ]
        }
      ]
    },
    generateRules: [],
    outputFormat: 'pdf',
    recipients: ['<EMAIL>', '<EMAIL>'],
    scheduleConfig: {
      frequency: 'daily',
      time: '08:00',
      timezone: 'Asia/Shanghai',
      isEnabled: true
    },
    createTime: '2025-06-01 10:00:00',
    updateTime: '2025-06-15 14:30:00',
    createdBy: 'admin',
    lastGeneratedTime: '2025-06-20 08:00:00',
    generateCount: 20
  }
]

// 模拟数据服务类
export class MockCultivationWarningService {
  // 模拟网络延迟
  private static delay(ms: number = 800): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 获取预警规则列表
  static async getAlertRules(params?: AlertQueryParams): Promise<{
    data: AlertRule[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockAlertRules]
    
    // 关键词搜索
    if (params?.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredData = filteredData.filter(rule => 
        rule.name.toLowerCase().includes(keyword) ||
        rule.description.toLowerCase().includes(keyword)
      )
    }
    
    // 级别筛选
    if (params?.level) {
      filteredData = filteredData.filter(rule => rule.level === params.level)
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 获取预警记录列表
  static async getAlertRecords(params?: AlertQueryParams): Promise<{
    data: AlertRecord[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockAlertRecords]
    
    // 状态筛选
    if (params?.status) {
      filteredData = filteredData.filter(record => record.status === params.status)
    }
    
    // 级别筛选
    if (params?.level) {
      filteredData = filteredData.filter(record => record.level === params.level)
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 获取数据采集模型列表
  static async getDataCollectionModels(params?: DataCollectionQueryParams): Promise<{
    data: DataCollectionModel[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockDataCollectionModels]
    
    // 状态筛选
    if (params?.status) {
      filteredData = filteredData.filter(model => model.status === params.status)
    }
    
    // 数据源类型筛选
    if (params?.sourceType) {
      filteredData = filteredData.filter(model => model.sourceType === params.sourceType)
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 获取督办对象列表
  static async getSupervisionObjects(params?: SupervisionQueryParams): Promise<{
    data: SupervisionObject[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockSupervisionObjects]
    
    // 状态筛选
    if (params?.status) {
      filteredData = filteredData.filter(obj => obj.supervisionStatus === params.status)
    }
    
    // 部门筛选
    if (params?.responsibleDepartment) {
      filteredData = filteredData.filter(obj => obj.responsibleDepartment === params.responsibleDepartment)
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 获取报告模板列表
  static async getReportTemplates(): Promise<ReportTemplate[]> {
    await this.delay()
    return [...mockReportTemplates]
  }

  // 获取预警统计数据
  static async getAlertStatistics(): Promise<AlertStatistics> {
    await this.delay()
    
    return {
      totalAlerts: 156,
      criticalAlerts: 23,
      majorAlerts: 45,
      generalAlerts: 67,
      infoAlerts: 21,
      pendingAlerts: 34,
      processingAlerts: 56,
      completedAlerts: 58,
      cancelledAlerts: 8,
      todayAlerts: 12,
      weekAlerts: 78,
      monthAlerts: 156,
      averageHandleTime: 4.5,
      handleRate: 89.7,
      alertTrend: Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - 6 + i)
        return {
          date: date.toISOString().split('T')[0],
          totalCount: Math.floor(Math.random() * 20) + 10,
          criticalCount: Math.floor(Math.random() * 5),
          majorCount: Math.floor(Math.random() * 8),
          generalCount: Math.floor(Math.random() * 10),
          infoCount: Math.floor(Math.random() * 5),
          handledCount: Math.floor(Math.random() * 15) + 5
        }
      }),
      topAlertRules: [
        {
          ruleId: 'rule_001',
          ruleName: '培育对象评分异常预警',
          triggerCount: 15,
          level: 'critical' as AlertLevel,
          lastTriggeredTime: '2025-06-20 09:15:00'
        },
        {
          ruleId: 'rule_003',
          ruleName: '督办逾期预警',
          triggerCount: 12,
          level: 'general' as AlertLevel,
          lastTriggeredTime: '2025-06-18 10:45:00'
        }
      ],
      alertsByLevel: {
        critical: 23,
        major: 45,
        general: 67,
        info: 21
      } as Record<AlertLevel, number>,
      alertsByStatus: {
        pending: 34,
        processing: 56,
        completed: 58,
        cancelled: 8
      } as Record<AlertStatus, number>
    }
  }

  // 获取数据采集统计
  static async getDataCollectionStatistics(): Promise<DataCollectionStatistics> {
    await this.delay()
    
    return {
      totalModels: 15,
      activeModels: 12,
      inactiveModels: 3,
      collectingModels: 2,
      processingModels: 1,
      completedModels: 9,
      failedModels: 3,
      totalCollections: 1440,
      successfulCollections: 1368,
      failedCollections: 72,
      averageSuccessRate: 95.0,
      dataQualityScore: 92.5,
      collectionTrend: Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - 6 + i)
        return {
          date: date.toISOString().split('T')[0],
          totalCollections: Math.floor(Math.random() * 50) + 150,
          successfulCollections: Math.floor(Math.random() * 45) + 140,
          failedCollections: Math.floor(Math.random() * 10),
          dataVolume: Math.floor(Math.random() * 100) + 50,
          qualityScore: Math.floor(Math.random() * 20) + 80
        }
      }),
      topDataSources: [
        {
          sourceId: 'model_001',
          sourceName: '培育对象基础信息采集',
          sourceType: 'database' as DataSourceType,
          collectionCount: 480,
          successRate: 98.5,
          lastCollectionTime: '2025-06-20 08:00:00'
        }
      ]
    }
  }

  // 获取督办统计
  static async getSupervisionStatistics(): Promise<SupervisionStatistics> {
    await this.delay()
    
    return {
      totalObjects: 89,
      pendingObjects: 12,
      supervisingObjects: 34,
      completedObjects: 38,
      overdueObjects: 5,
      averageCompletionTime: 15.5,
      completionRate: 85.4,
      supervisionTrend: Array.from({ length: 7 }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - 6 + i)
        return {
          date: date.toISOString().split('T')[0],
          newObjects: Math.floor(Math.random() * 5),
          completedObjects: Math.floor(Math.random() * 8),
          overdueObjects: Math.floor(Math.random() * 2),
          averageScore: Math.floor(Math.random() * 30) + 60
        }
      }),
      topDepartments: [
        {
          departmentId: 'dept_001',
          departmentName: '市科技局',
          objectCount: 25,
          completionRate: 88.0,
          averageScore: 78.5
        },
        {
          departmentId: 'dept_002',
          departmentName: '市经信委',
          objectCount: 18,
          completionRate: 83.3,
          averageScore: 75.2
        }
      ],
      evaluationDistribution: {
        'A级': 15,
        'B级': 32,
        'C级': 28,
        'D级': 14
      }
    }
  }

  // 创建预警规则
  static async createAlertRule(data: Partial<AlertRule>): Promise<OperationResult> {
    await this.delay()
    
    const newRule: AlertRule = {
      id: `rule_${Date.now()}`,
      name: data.name!,
      description: data.description!,
      level: data.level!,
      isEnabled: data.isEnabled ?? true,
      triggerConditions: data.triggerConditions || [],
      notificationMethods: data.notificationMethods || [],
      targetUsers: data.targetUsers || [],
      targetRoles: data.targetRoles || [],
      cooldownPeriod: data.cooldownPeriod || 60,
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      createdBy: 'current_user',
      triggerCount: 0
    }
    
    mockAlertRules.push(newRule)
    
    return {
      success: true,
      message: '预警规则创建成功',
      data: newRule,
      timestamp: new Date().toISOString()
    }
  }

  // 更新预警规则
  static async updateAlertRule(id: string, data: Partial<AlertRule>): Promise<OperationResult> {
    await this.delay()
    
    const index = mockAlertRules.findIndex(rule => rule.id === id)
    if (index === -1) {
      return {
        success: false,
        message: '预警规则不存在',
        errors: ['Rule not found'],
        timestamp: new Date().toISOString()
      }
    }
    
    mockAlertRules[index] = {
      ...mockAlertRules[index],
      ...data,
      updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
    
    return {
      success: true,
      message: '预警规则更新成功',
      data: mockAlertRules[index],
      timestamp: new Date().toISOString()
    }
  }

  // 删除预警规则
  static async deleteAlertRule(id: string): Promise<OperationResult> {
    await this.delay()
    
    const index = mockAlertRules.findIndex(rule => rule.id === id)
    if (index === -1) {
      return {
        success: false,
        message: '预警规则不存在',
        errors: ['Rule not found'],
        timestamp: new Date().toISOString()
      }
    }
    
    mockAlertRules.splice(index, 1)
    
    return {
      success: true,
      message: '预警规则删除成功',
      timestamp: new Date().toISOString()
    }
  }

  // 批量操作预警规则
  static async batchOperateAlertRules(
    ids: string[], 
    operation: 'enable' | 'disable' | 'delete'
  ): Promise<BatchOperationResult> {
    await this.delay()
    
    let successCount = 0
    let failureCount = 0
    const failures: Array<{ id: string; error: string }> = []
    
    for (const id of ids) {
      const index = mockAlertRules.findIndex(rule => rule.id === id)
      if (index === -1) {
        failureCount++
        failures.push({ id, error: '规则不存在' })
        continue
      }
      
      try {
        switch (operation) {
          case 'enable':
            mockAlertRules[index].isEnabled = true
            break
          case 'disable':
            mockAlertRules[index].isEnabled = false
            break
          case 'delete':
            mockAlertRules.splice(index, 1)
            break
        }
        successCount++
      } catch (error) {
        failureCount++
        failures.push({ id, error: '操作失败' })
      }
    }
    
    return {
      success: failureCount === 0,
      message: `批量操作完成，成功${successCount}个，失败${failureCount}个`,
      totalCount: ids.length,
      successCount,
      failureCount,
      failures: failures.length > 0 ? failures : undefined,
      timestamp: new Date().toISOString()
    }
  }
}
