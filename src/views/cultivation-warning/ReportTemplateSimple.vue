<template>
  <div class="report-template">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警报告与模板</h1>
        <p>智能报告生成和模板自定义管理平台</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary">
            <plus-outlined />
            新建模板
          </a-button>
          <a-button>
            <file-text-outlined />
            生成报告
          </a-button>
          <a-button>
            <download-outlined />
            导出模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 报告统计概览 -->
    <div class="report-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="报告模板"
              :value="reportStats.totalTemplates"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日生成"
              :value="reportStats.todayGenerated"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="定期任务"
              :value="reportStats.scheduledTasks"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="reportStats.successRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 报告模板管理 -->
        <a-tab-pane key="templates" tab="报告模板管理">
          <div class="template-management">
            <!-- 模板列表 -->
            <div class="template-grid">
              <a-row :gutter="[24, 24]">
                <a-col 
                  v-for="template in templateList" 
                  :key="template.id"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <a-card 
                    class="template-card"
                    :class="{ default: template.isDefault }"
                    hoverable
                  >
                    <template #cover>
                      <div class="template-preview">
                        <file-text-outlined class="preview-icon" />
                        <div class="template-type">{{ getTypeText(template.type) }}</div>
                      </div>
                    </template>
                    
                    <template #actions>
                      <eye-outlined @click="previewTemplate(template)" />
                      <edit-outlined @click="editTemplate(template)" />
                      <copy-outlined @click="copyTemplate(template)" />
                      <delete-outlined @click="deleteTemplate(template)" />
                    </template>
                    
                    <a-card-meta 
                      :title="template.name"
                      :description="template.description"
                    />
                    
                    <div class="template-meta">
                      <div class="meta-item">
                        <span class="meta-label">格式：</span>
                        <a-tag :color="getFormatColor(template.outputFormat)">
                          {{ template.outputFormat.toUpperCase() }}
                        </a-tag>
                      </div>
                      <div class="meta-item">
                        <span class="meta-label">状态：</span>
                        <a-switch 
                          :checked="template.isEnabled" 
                          size="small"
                          @change="(checked) => toggleTemplate(template, checked)"
                        />
                      </div>
                      <div class="meta-item">
                        <span class="meta-label">生成次数：</span>
                        <span>{{ template.generateCount }}</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 智能报告生成 -->
        <a-tab-pane key="generate" tab="智能报告生成">
          <div class="report-generation">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-card title="选择模板" size="small">
                  <div class="template-selector">
                    <a-radio-group 
                      v-model:value="selectedTemplateId" 
                      class="template-radio-group"
                    >
                      <div 
                        v-for="template in enabledTemplates" 
                        :key="template.id"
                        class="template-option"
                      >
                        <a-radio :value="template.id">
                          <div class="option-content">
                            <div class="option-header">
                              <span class="option-name">{{ template.name }}</span>
                              <a-tag size="small" :color="getTypeColor(template.type)">
                                {{ getTypeText(template.type) }}
                              </a-tag>
                            </div>
                            <div class="option-description">{{ template.description }}</div>
                          </div>
                        </a-radio>
                      </div>
                    </a-radio-group>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="生成配置" size="small">
                  <div class="generation-config">
                    <a-form layout="vertical">
                      <a-form-item label="报告标题">
                        <a-input v-model:value="generationForm.title" placeholder="请输入报告标题" />
                      </a-form-item>
                      
                      <a-form-item label="时间范围">
                        <a-range-picker 
                          v-model:value="generationForm.dateRange" 
                          style="width: 100%"
                        />
                      </a-form-item>
                      
                      <a-form-item label="输出格式">
                        <a-select v-model:value="generationForm.outputFormat" placeholder="选择输出格式">
                          <a-select-option value="pdf">PDF</a-select-option>
                          <a-select-option value="excel">Excel</a-select-option>
                          <a-select-option value="word">Word</a-select-option>
                          <a-select-option value="html">HTML</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="startGeneration" :loading="generating">
                            生成报告
                          </a-button>
                          <a-button @click="previewGeneration">
                            预览
                          </a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="生成进度" size="small">
                  <div class="generation-progress">
                    <div v-if="!generating && !generationResult" class="progress-placeholder">
                      <a-empty description="请选择模板并配置参数后开始生成" />
                    </div>
                    
                    <div v-if="generating" class="progress-active">
                      <a-progress 
                        :percent="generationProgress" 
                        :status="generationProgress === 100 ? 'success' : 'active'"
                      />
                      <div class="progress-steps">
                        <div class="step-item" :class="{ active: generationStep >= 1 }">
                          <check-circle-outlined />
                          <span>数据收集</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 2 }">
                          <check-circle-outlined />
                          <span>数据分析</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 3 }">
                          <check-circle-outlined />
                          <span>报告生成</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 4 }">
                          <check-circle-outlined />
                          <span>完成</span>
                        </div>
                      </div>
                    </div>
                    
                    <div v-if="generationResult" class="progress-result">
                      <a-result
                        status="success"
                        title="报告生成成功"
                        :sub-title="`耗时 ${generationResult.duration}，文件大小 ${generationResult.fileSize}`"
                      >
                        <template #extra>
                          <a-space>
                            <a-button type="primary" @click="downloadReport">
                              下载报告
                            </a-button>
                            <a-button @click="previewReport">
                              预览报告
                            </a-button>
                          </a-space>
                        </template>
                      </a-result>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 定期报告配置 -->
        <a-tab-pane key="schedule" tab="定期报告配置">
          <div class="schedule-config">
            <div class="schedule-list">
              <div class="list-header">
                <h3>定期报告任务</h3>
                <a-button type="primary">
                  <plus-outlined />
                  新建定期任务
                </a-button>
              </div>
              
              <a-table
                :columns="scheduleColumns"
                :data-source="scheduleList"
                :pagination="false"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'template'">
                    <div class="template-info">
                      <span class="template-name">{{ record.templateName }}</span>
                      <a-tag size="small" :color="getTypeColor(record.templateType)">
                        {{ getTypeText(record.templateType) }}
                      </a-tag>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="record.enabled ? 'success' : 'default'" 
                      :text="record.enabled ? '启用' : '禁用'"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editSchedule(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="runSchedule(record)">
                        立即执行
                      </a-button>
                      <a-switch 
                        :checked="record.enabled" 
                        size="small"
                        @change="(checked) => toggleSchedule(record, checked)"
                      />
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 报告历史 -->
        <a-tab-pane key="history" tab="报告历史">
          <div class="report-history">
            <div class="history-table">
              <a-table
                :columns="historyColumns"
                :data-source="reportHistory"
                :loading="historyLoading"
                :pagination="historyPagination"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'title'">
                    <div class="report-title">
                      <a @click="previewHistoryReport(record)">{{ record.title }}</a>
                      <div class="report-template">模板：{{ record.templateName }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'type'">
                    <a-tag :color="getTypeColor(record.type)">
                      {{ getTypeText(record.type) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'format'">
                    <a-tag :color="getFormatColor(record.format)">
                      {{ record.format.toUpperCase() }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="downloadHistoryReport(record)">
                        下载
                      </a-button>
                      <a-button type="link" size="small" @click="previewHistoryReport(record)">
                        预览
                      </a-button>
                      <a-button type="link" size="small" @click="shareReport(record)">
                        分享
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  FileTextOutlined,
  DownloadOutlined,
  EyeOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('templates')

// 报告统计
const reportStats = reactive({
  totalTemplates: 8,
  todayGenerated: 12,
  scheduledTasks: 3,
  successRate: 96
})

// 模板列表
const templateList = ref([
  {
    id: 'template_001',
    name: '培育过程预警日报',
    description: '每日预警情况汇总报告',
    type: 'daily',
    outputFormat: 'pdf',
    isEnabled: true,
    isDefault: true,
    generateCount: 45
  },
  {
    id: 'template_002',
    name: '督办管理周报',
    description: '督办对象进度周报',
    type: 'weekly',
    outputFormat: 'excel',
    isEnabled: true,
    isDefault: false,
    generateCount: 12
  },
  {
    id: 'template_003',
    name: '培育效果月报',
    description: '培育效果分析月报',
    type: 'monthly',
    outputFormat: 'word',
    isEnabled: false,
    isDefault: false,
    generateCount: 3
  }
])

// 智能报告生成
const selectedTemplateId = ref()
const generating = ref(false)
const generationProgress = ref(0)
const generationStep = ref(0)
const generationResult = ref(null)

const generationForm = reactive({
  title: '',
  dateRange: null,
  outputFormat: 'pdf'
})

// 定期任务列表
const scheduleList = ref([
  {
    id: '1',
    name: '培育过程预警日报',
    templateName: '培育过程预警日报',
    templateType: 'daily',
    frequency: 'daily',
    nextRun: '2025-06-21 08:00:00',
    enabled: true,
    lastRun: '2025-06-20 08:00:00',
    runCount: 20
  },
  {
    id: '2',
    name: '督办管理周报',
    templateName: '督办管理周报',
    templateType: 'weekly',
    frequency: 'weekly',
    nextRun: '2025-06-22 09:00:00',
    enabled: true,
    lastRun: '2025-06-15 09:00:00',
    runCount: 4
  }
])

// 定期任务表格列配置
const scheduleColumns = [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '报告模板',
    key: 'template'
  },
  {
    title: '执行频率',
    dataIndex: 'frequency',
    key: 'frequency'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '下次执行',
    dataIndex: 'nextRun',
    key: 'nextRun',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 报告历史
const historyLoading = ref(false)
const reportHistory = ref([
  {
    id: 1,
    title: '培育过程预警报告_2025-06-20',
    templateName: '培育过程预警日报',
    type: 'daily',
    format: 'pdf',
    generateTime: '2025-06-20 08:00:00',
    fileSize: '2.3MB'
  },
  {
    id: 2,
    title: '督办管理周报_2025-06-15',
    templateName: '督办管理周报',
    type: 'weekly',
    format: 'excel',
    generateTime: '2025-06-15 09:00:00',
    fileSize: '1.8MB'
  }
])

// 报告历史分页
const historyPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 报告历史表格列配置
const historyColumns = [
  {
    title: '报告标题',
    key: 'title',
    width: 250
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '格式',
    key: 'format',
    width: 80
  },
  {
    title: '生成时间',
    dataIndex: 'generateTime',
    key: 'generateTime',
    width: 150
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 计算属性
const enabledTemplates = computed(() => {
  return templateList.value.filter(template => template.isEnabled)
})

// 方法
const previewTemplate = (template: any) => {
  message.info(`预览模板：${template.name}`)
}

const editTemplate = (template: any) => {
  message.info(`编辑模板：${template.name}`)
}

const copyTemplate = (template: any) => {
  message.success(`复制模板：${template.name}`)
}

const deleteTemplate = (template: any) => {
  message.success(`删除模板：${template.name}`)
}

const toggleTemplate = (template: any, checked: boolean) => {
  template.isEnabled = checked
  message.success(`模板已${checked ? '启用' : '禁用'}`)
}

const startGeneration = () => {
  if (!selectedTemplateId.value) {
    message.error('请先选择报告模板')
    return
  }

  generating.value = true
  generationProgress.value = 0
  generationStep.value = 0
  generationResult.value = null

  // 模拟生成过程
  const steps = [
    { step: 1, progress: 25, delay: 1000 },
    { step: 2, progress: 50, delay: 1500 },
    { step: 3, progress: 75, delay: 2000 },
    { step: 4, progress: 100, delay: 1000 }
  ]

  let currentIndex = 0
  const executeStep = () => {
    if (currentIndex < steps.length) {
      const { step, progress, delay } = steps[currentIndex]
      setTimeout(() => {
        generationStep.value = step
        generationProgress.value = progress
        currentIndex++
        executeStep()
      }, delay)
    } else {
      setTimeout(() => {
        generating.value = false
        generationResult.value = {
          duration: '5.2秒',
          fileSize: '2.3MB',
          fileName: `${generationForm.title}.${generationForm.outputFormat}`
        }
        message.success('报告生成完成')
      }, 500)
    }
  }

  executeStep()
}

const previewGeneration = () => {
  message.info('预览报告生成配置')
}

const downloadReport = () => {
  if (generationResult.value) {
    message.success(`下载报告：${generationResult.value.fileName}`)
  }
}

const previewReport = () => {
  message.info('预览生成的报告')
}

const editSchedule = (schedule: any) => {
  message.info(`编辑定期任务：${schedule.name}`)
}

const runSchedule = (schedule: any) => {
  message.success(`立即执行任务：${schedule.name}`)
}

const toggleSchedule = (schedule: any, checked: boolean) => {
  schedule.enabled = checked
  message.success(`任务已${checked ? '启用' : '禁用'}`)
}

const downloadHistoryReport = (record: any) => {
  message.success(`下载报告：${record.title}`)
}

const previewHistoryReport = (record: any) => {
  message.info(`预览报告：${record.title}`)
}

const shareReport = (record: any) => {
  message.info(`分享报告：${record.title}`)
}

// 工具方法
const getTypeText = (type: string) => {
  const texts = {
    daily: '日报',
    weekly: '周报',
    monthly: '月报',
    quarterly: '季报',
    annual: '年报',
    custom: '自定义'
  }
  return texts[type] || type
}

const getTypeColor = (type: string) => {
  const colors = {
    daily: 'blue',
    weekly: 'green',
    monthly: 'orange',
    quarterly: 'purple',
    annual: 'red',
    custom: 'default'
  }
  return colors[type] || 'default'
}

const getFormatColor = (format: string) => {
  const colors = {
    pdf: 'red',
    excel: 'green',
    word: 'blue',
    html: 'orange'
  }
  return colors[format] || 'default'
}

// 组件挂载时加载数据
onMounted(() => {
  historyPagination.total = reportHistory.value.length
})
</script>

<style lang="scss" scoped>
.report-template {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .report-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .template-management {
    .template-grid {
      .template-card {
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.default {
          border: 2px solid #1890ff;
        }

        .template-preview {
          height: 120px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          position: relative;

          .preview-icon {
            font-size: 32px;
            margin-bottom: 8px;
          }

          .template-type {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }

        .template-meta {
          margin-top: 12px;

          .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }

  .report-generation {
    .template-selector {
      .template-radio-group {
        width: 100%;

        .template-option {
          width: 100%;
          margin-bottom: 12px;
          padding: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f6ffed;
          }

          .option-content {
            margin-left: 8px;

            .option-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              .option-name {
                font-weight: 600;
                color: #262626;
              }
            }

            .option-description {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .generation-config {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }

    .generation-progress {
      .progress-placeholder {
        text-align: center;
        padding: 40px 20px;
      }

      .progress-active {
        .progress-steps {
          margin-top: 20px;

          .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #d9d9d9;
            transition: color 0.3s ease;

            &.active {
              color: #52c41a;
            }

            .anticon {
              margin-right: 8px;
            }
          }
        }
      }

      .progress-result {
        text-align: center;
      }
    }
  }

  .schedule-config {
    .schedule-list {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .template-info {
        .template-name {
          font-weight: 600;
          color: #262626;
          margin-right: 8px;
        }
      }
    }
  }

  .report-history {
    .history-table {
      background: white;
      border-radius: 6px;
      overflow: hidden;

      .report-title {
        .report-template {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 4px;
        }
      }
    }
  }
}
</style>
