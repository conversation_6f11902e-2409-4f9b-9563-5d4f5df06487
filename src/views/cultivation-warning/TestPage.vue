<template>
  <div class="test-page">
    <h1>培育过程预警系统测试页面</h1>
    <p>如果您能看到这个页面，说明路由配置正常。</p>
    
    <a-card title="路由测试">
      <a-space direction="vertical" style="width: 100%">
        <a-button type="primary" @click="goToIndex">
          返回首页
        </a-button>
        <a-button @click="goToConfig">
          预警配置管理
        </a-button>
        <a-button @click="goToDataCollection">
          数据采集与处理
        </a-button>
        <a-button @click="goToAlertTrigger">
          预警触发与通知
        </a-button>
        <a-button @click="goToSupervision">
          督办管理
        </a-button>
        <a-button @click="goToReport">
          预警报告与模板
        </a-button>
      </a-space>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToIndex = () => {
  router.push('/cultivation-warning/index')
}

const goToConfig = () => {
  router.push('/cultivation-warning/config')
}

const goToDataCollection = () => {
  router.push('/cultivation-warning/data-collection')
}

const goToAlertTrigger = () => {
  router.push('/cultivation-warning/alert-trigger')
}

const goToSupervision = () => {
  router.push('/cultivation-warning/supervision')
}

const goToReport = () => {
  router.push('/cultivation-warning/report')
}
</script>

<style scoped>
.test-page {
  padding: 24px;
}
</style>
