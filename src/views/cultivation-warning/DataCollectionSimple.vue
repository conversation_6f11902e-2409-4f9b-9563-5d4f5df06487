<template>
  <div class="data-collection">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>数据采集与处理</h1>
        <p>监控数据采集状态，管理数据源和处理流程</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary">
            <plus-outlined />
            新增数据源
          </a-button>
          <a-button>
            <sync-outlined />
            同步数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据采集统计概览 -->
    <div class="collection-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="数据源总数"
              :value="statistics.totalSources"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="在线数据源"
              :value="statistics.onlineSources"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日采集量"
              :value="statistics.todayCollection"
              suffix="条"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="数据质量"
              :value="statistics.dataQuality"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 数据源管理 -->
        <a-tab-pane key="sources" tab="数据源管理">
          <div class="sources-management">
            <!-- 数据源列表 -->
            <div class="sources-table">
              <a-table
                :columns="sourcesColumns"
                :data-source="sourcesList"
                :loading="loading"
                :pagination="pagination"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="source-name">
                      <a @click="viewSourceDetail(record)">{{ record.name }}</a>
                      <div class="source-description">{{ record.description }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'type'">
                    <a-tag :color="getTypeColor(record.type)">
                      {{ getTypeText(record.type) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getStatusBadge(record.status)" 
                      :text="getStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editSource(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="testConnection(record)">
                        测试连接
                      </a-button>
                      <a-button type="link" size="small" @click="syncSource(record)">
                        同步数据
                      </a-button>
                      <a-button type="link" size="small" danger @click="deleteSource(record)">
                        删除
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 采集监控 -->
        <a-tab-pane key="monitoring" tab="采集监控">
          <div class="collection-monitoring">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="实时采集状态" size="small">
                  <div class="monitoring-chart">
                    <div class="chart-placeholder">
                      <p>实时采集状态图表</p>
                      <p>显示各数据源的采集状态和速率</p>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="数据质量监控" size="small">
                  <div class="quality-metrics">
                    <div class="metric-item">
                      <span class="metric-label">完整性：</span>
                      <a-progress :percent="95" size="small" />
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">准确性：</span>
                      <a-progress :percent="92" size="small" />
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">及时性：</span>
                      <a-progress :percent="88" size="small" />
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">一致性：</span>
                      <a-progress :percent="96" size="small" />
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 数据处理 -->
        <a-tab-pane key="processing" tab="数据处理">
          <div class="data-processing">
            <a-card title="数据处理流程" size="small">
              <div class="processing-flow">
                <div class="flow-step">
                  <div class="step-icon">1</div>
                  <div class="step-content">
                    <h4>数据采集</h4>
                    <p>从各数据源采集原始数据</p>
                  </div>
                </div>
                
                <div class="flow-arrow">→</div>
                
                <div class="flow-step">
                  <div class="step-icon">2</div>
                  <div class="step-content">
                    <h4>数据清洗</h4>
                    <p>清理和标准化数据格式</p>
                  </div>
                </div>
                
                <div class="flow-arrow">→</div>
                
                <div class="flow-step">
                  <div class="step-icon">3</div>
                  <div class="step-content">
                    <h4>数据验证</h4>
                    <p>验证数据质量和完整性</p>
                  </div>
                </div>
                
                <div class="flow-arrow">→</div>
                
                <div class="flow-step">
                  <div class="step-icon">4</div>
                  <div class="step-content">
                    <h4>数据存储</h4>
                    <p>存储到数据仓库</p>
                  </div>
                </div>
              </div>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SyncOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('sources')

// 统计数据
const statistics = reactive({
  totalSources: 12,
  onlineSources: 10,
  todayCollection: 15420,
  dataQuality: 94
})

// 数据源列表
const sourcesList = ref([
  {
    id: 'source_001',
    name: '培育对象管理系统',
    description: '培育对象基础信息和评分数据',
    type: 'database',
    status: 'online',
    lastSync: '2025-06-20 10:30:00',
    recordCount: 1250
  },
  {
    id: 'source_002',
    name: '渝快政平台',
    description: '政务服务数据和审批信息',
    type: 'api',
    status: 'online',
    lastSync: '2025-06-20 10:25:00',
    recordCount: 890
  },
  {
    id: 'source_003',
    name: '第三方评估系统',
    description: '外部评估机构的评分数据',
    type: 'file',
    status: 'offline',
    lastSync: '2025-06-19 18:00:00',
    recordCount: 456
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 数据源表格列配置
const sourcesColumns = [
  {
    title: '数据源名称',
    key: 'name',
    width: 250
  },
  {
    title: '数据源类型',
    key: 'type',
    width: 120
  },
  {
    title: '连接状态',
    key: 'status',
    width: 120
  },
  {
    title: '最后同步',
    dataIndex: 'lastSync',
    key: 'lastSync',
    width: 150
  },
  {
    title: '记录数量',
    dataIndex: 'recordCount',
    key: 'recordCount',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 250
  }
]

// 方法
const viewSourceDetail = (source: any) => {
  message.info(`查看数据源详情：${source.name}`)
}

const editSource = (source: any) => {
  message.info(`编辑数据源：${source.name}`)
}

const testConnection = (source: any) => {
  message.success(`测试连接成功：${source.name}`)
}

const syncSource = (source: any) => {
  message.success(`开始同步数据：${source.name}`)
}

const deleteSource = (source: any) => {
  message.success(`删除数据源：${source.name}`)
}

// 工具方法
const getTypeColor = (type: string) => {
  const colors = {
    database: 'blue',
    api: 'green',
    file: 'orange',
    stream: 'purple'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: string) => {
  const texts = {
    database: '数据库',
    api: 'API接口',
    file: '文件',
    stream: '数据流'
  }
  return texts[type] || type
}

const getStatusBadge = (status: string) => {
  const badges = {
    online: 'success',
    offline: 'error',
    connecting: 'processing'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    online: '在线',
    offline: '离线',
    connecting: '连接中'
  }
  return texts[status] || status
}

// 组件挂载时加载数据
onMounted(() => {
  pagination.total = sourcesList.value.length
})
</script>

<style lang="scss" scoped>
.data-collection {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .collection-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .sources-management {
    .source-name {
      .source-description {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }
  }

  .collection-monitoring {
    .monitoring-chart {
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-placeholder {
        text-align: center;
        color: #8c8c8c;
      }
    }

    .quality-metrics {
      .metric-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .metric-label {
          width: 80px;
          color: #262626;
          font-weight: 500;
        }
      }
    }
  }

  .data-processing {
    .processing-flow {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .flow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .step-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #1890ff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-bottom: 12px;
        }

        .step-content {
          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }

          p {
            margin: 0;
            font-size: 12px;
            color: #8c8c8c;
            max-width: 120px;
          }
        }
      }

      .flow-arrow {
        margin: 0 20px;
        font-size: 20px;
        color: #1890ff;
        font-weight: bold;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .collection-overview {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .data-processing {
      .processing-flow {
        flex-direction: column;

        .flow-arrow {
          transform: rotate(90deg);
          margin: 20px 0;
        }
      }
    }
  }
}
</style>
