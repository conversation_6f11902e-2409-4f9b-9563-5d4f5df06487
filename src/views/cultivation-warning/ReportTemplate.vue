<template>
  <div class="report-template">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>预警报告与模板</h1>
        <p>智能报告生成和模板自定义管理平台</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="showCreateTemplate">
            <plus-outlined />
            新建模板
          </a-button>
          <a-button @click="generateReport">
            <file-text-outlined />
            生成报告
          </a-button>
          <a-button @click="exportTemplates">
            <download-outlined />
            导出模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 报告统计概览 -->
    <div class="report-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="报告模板"
              :value="reportStats.totalTemplates"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="今日生成"
              :value="reportStats.todayGenerated"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="定期任务"
              :value="reportStats.scheduledTasks"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="reportStats.successRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 报告模板管理 -->
        <a-tab-pane key="templates" tab="报告模板管理">
          <div class="template-management">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索模板名称"
                    allow-clear
                    @change="handleSearch"
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedType"
                    placeholder="报告类型"
                    allow-clear
                    @change="handleTypeFilter"
                  >
                    <a-select-option value="daily">日报</a-select-option>
                    <a-select-option value="weekly">周报</a-select-option>
                    <a-select-option value="monthly">月报</a-select-option>
                    <a-select-option value="quarterly">季报</a-select-option>
                    <a-select-option value="annual">年报</a-select-option>
                    <a-select-option value="custom">自定义</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="模板状态"
                    allow-clear
                    @change="handleStatusFilter"
                  >
                    <a-select-option :value="true">已启用</a-select-option>
                    <a-select-option :value="false">已禁用</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="8">
                  <a-space>
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadTemplates">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 模板列表 -->
            <div class="template-grid">
              <a-row :gutter="[24, 24]">
                <a-col 
                  v-for="template in templateList" 
                  :key="template.id"
                  :xs="24" :sm="12" :md="8" :lg="6"
                >
                  <a-card 
                    class="template-card"
                    :class="{ default: template.isDefault }"
                    hoverable
                  >
                    <template #cover>
                      <div class="template-preview">
                        <file-text-outlined class="preview-icon" />
                        <div class="template-type">{{ getTypeText(template.type) }}</div>
                      </div>
                    </template>
                    
                    <template #actions>
                      <a-tooltip title="预览">
                        <eye-outlined @click="previewTemplate(template)" />
                      </a-tooltip>
                      <a-tooltip title="编辑">
                        <edit-outlined @click="editTemplate(template)" />
                      </a-tooltip>
                      <a-tooltip title="复制">
                        <copy-outlined @click="copyTemplate(template)" />
                      </a-tooltip>
                      <a-tooltip title="删除">
                        <delete-outlined @click="deleteTemplate(template)" />
                      </a-tooltip>
                    </template>
                    
                    <a-card-meta 
                      :title="template.name"
                      :description="template.description"
                    />
                    
                    <div class="template-meta">
                      <div class="meta-item">
                        <span class="meta-label">格式：</span>
                        <a-tag :color="getFormatColor(template.outputFormat)">
                          {{ template.outputFormat.toUpperCase() }}
                        </a-tag>
                      </div>
                      <div class="meta-item">
                        <span class="meta-label">状态：</span>
                        <a-switch 
                          :checked="template.isEnabled" 
                          size="small"
                          @change="(checked) => toggleTemplate(template, checked)"
                        />
                      </div>
                      <div class="meta-item">
                        <span class="meta-label">生成次数：</span>
                        <span>{{ template.generateCount }}</span>
                      </div>
                      <div class="meta-item">
                        <span class="meta-label">最后生成：</span>
                        <span>{{ template.lastGeneratedTime || '未生成' }}</span>
                      </div>
                    </div>
                  </a-card>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-tab-pane>

        <!-- 智能报告生成 -->
        <a-tab-pane key="generate" tab="智能报告生成">
          <div class="report-generation">
            <a-row :gutter="24">
              <a-col :span="8">
                <a-card title="选择模板" size="small">
                  <div class="template-selector">
                    <a-radio-group 
                      v-model:value="selectedTemplateId" 
                      class="template-radio-group"
                      @change="onTemplateSelect"
                    >
                      <div 
                        v-for="template in enabledTemplates" 
                        :key="template.id"
                        class="template-option"
                      >
                        <a-radio :value="template.id">
                          <div class="option-content">
                            <div class="option-header">
                              <span class="option-name">{{ template.name }}</span>
                              <a-tag size="small" :color="getTypeColor(template.type)">
                                {{ getTypeText(template.type) }}
                              </a-tag>
                            </div>
                            <div class="option-description">{{ template.description }}</div>
                          </div>
                        </a-radio>
                      </div>
                    </a-radio-group>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="生成配置" size="small">
                  <div class="generation-config">
                    <a-form layout="vertical">
                      <a-form-item label="报告标题">
                        <a-input v-model:value="generationForm.title" placeholder="请输入报告标题" />
                      </a-form-item>
                      
                      <a-form-item label="时间范围">
                        <a-range-picker 
                          v-model:value="generationForm.dateRange" 
                          style="width: 100%"
                        />
                      </a-form-item>
                      
                      <a-form-item label="数据范围">
                        <a-checkbox-group v-model:value="generationForm.dataScope">
                          <a-checkbox value="alerts">预警数据</a-checkbox>
                          <a-checkbox value="supervision">督办数据</a-checkbox>
                          <a-checkbox value="statistics">统计数据</a-checkbox>
                          <a-checkbox value="trends">趋势数据</a-checkbox>
                        </a-checkbox-group>
                      </a-form-item>
                      
                      <a-form-item label="输出格式">
                        <a-select v-model:value="generationForm.outputFormat" placeholder="选择输出格式">
                          <a-select-option value="pdf">PDF</a-select-option>
                          <a-select-option value="excel">Excel</a-select-option>
                          <a-select-option value="word">Word</a-select-option>
                          <a-select-option value="html">HTML</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item label="接收人">
                        <a-select 
                          v-model:value="generationForm.recipients" 
                          mode="multiple" 
                          placeholder="选择接收人"
                        >
                          <a-select-option value="<EMAIL>">管理员</a-select-option>
                          <a-select-option value="<EMAIL>">督办员</a-select-option>
                          <a-select-option value="<EMAIL>">领导</a-select-option>
                        </a-select>
                      </a-form-item>
                      
                      <a-form-item>
                        <a-space>
                          <a-button type="primary" @click="startGeneration" :loading="generating">
                            生成报告
                          </a-button>
                          <a-button @click="previewGeneration">
                            预览
                          </a-button>
                        </a-space>
                      </a-form-item>
                    </a-form>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="8">
                <a-card title="生成进度" size="small">
                  <div class="generation-progress">
                    <div v-if="!generating && !generationResult" class="progress-placeholder">
                      <a-empty description="请选择模板并配置参数后开始生成" />
                    </div>
                    
                    <div v-if="generating" class="progress-active">
                      <a-progress 
                        :percent="generationProgress" 
                        :status="generationProgress === 100 ? 'success' : 'active'"
                      />
                      <div class="progress-steps">
                        <div class="step-item" :class="{ active: generationStep >= 1 }">
                          <check-circle-outlined />
                          <span>数据收集</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 2 }">
                          <check-circle-outlined />
                          <span>数据分析</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 3 }">
                          <check-circle-outlined />
                          <span>报告生成</span>
                        </div>
                        <div class="step-item" :class="{ active: generationStep >= 4 }">
                          <check-circle-outlined />
                          <span>完成</span>
                        </div>
                      </div>
                    </div>
                    
                    <div v-if="generationResult" class="progress-result">
                      <a-result
                        status="success"
                        title="报告生成成功"
                        :sub-title="`耗时 ${generationResult.duration}，文件大小 ${generationResult.fileSize}`"
                      >
                        <template #extra>
                          <a-space>
                            <a-button type="primary" @click="downloadReport">
                              下载报告
                            </a-button>
                            <a-button @click="previewReport">
                              预览报告
                            </a-button>
                            <a-button @click="sendReport">
                              发送报告
                            </a-button>
                          </a-space>
                        </template>
                      </a-result>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 定期报告配置 -->
        <a-tab-pane key="schedule" tab="定期报告配置">
          <div class="schedule-config">
            <!-- 定期任务列表 -->
            <div class="schedule-list">
              <div class="list-header">
                <h3>定期报告任务</h3>
                <a-button type="primary" @click="showCreateSchedule">
                  <plus-outlined />
                  新建定期任务
                </a-button>
              </div>
              
              <a-table
                :columns="scheduleColumns"
                :data-source="scheduleList"
                :pagination="false"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'template'">
                    <div class="template-info">
                      <span class="template-name">{{ record.templateName }}</span>
                      <a-tag size="small" :color="getTypeColor(record.templateType)">
                        {{ getTypeText(record.templateType) }}
                      </a-tag>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'schedule'">
                    <div class="schedule-info">
                      <div class="frequency">{{ getFrequencyText(record.frequency) }}</div>
                      <div class="next-run">下次执行：{{ record.nextRun }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="record.enabled ? 'success' : 'default'" 
                      :text="record.enabled ? '启用' : '禁用'"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="editSchedule(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="runSchedule(record)">
                        立即执行
                      </a-button>
                      <a-button type="link" size="small" @click="viewScheduleLogs(record)">
                        日志
                      </a-button>
                      <a-switch 
                        :checked="record.enabled" 
                        size="small"
                        @change="(checked) => toggleSchedule(record, checked)"
                      />
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 报告历史 -->
        <a-tab-pane key="history" tab="报告历史">
          <div class="report-history">
            <!-- 搜索筛选 -->
            <div class="history-filters">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="historyKeyword"
                    placeholder="搜索报告标题"
                    allow-clear
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="historyType"
                    placeholder="报告类型"
                    allow-clear
                  >
                    <a-select-option value="daily">日报</a-select-option>
                    <a-select-option value="weekly">周报</a-select-option>
                    <a-select-option value="monthly">月报</a-select-option>
                    <a-select-option value="quarterly">季报</a-select-option>
                    <a-select-option value="annual">年报</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="historyFormat"
                    placeholder="文件格式"
                    allow-clear
                  >
                    <a-select-option value="pdf">PDF</a-select-option>
                    <a-select-option value="excel">Excel</a-select-option>
                    <a-select-option value="word">Word</a-select-option>
                    <a-select-option value="html">HTML</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-range-picker v-model:value="historyDateRange" />
                </a-col>
                <a-col :span="4">
                  <a-space>
                    <a-button @click="resetHistoryFilters">重置</a-button>
                    <a-button type="primary" @click="loadReportHistory">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 报告历史列表 -->
            <div class="history-table">
              <a-table
                :columns="historyColumns"
                :data-source="reportHistory"
                :loading="historyLoading"
                :pagination="historyPagination"
                row-key="id"
                @change="handleHistoryTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'title'">
                    <div class="report-title">
                      <a @click="previewHistoryReport(record)">{{ record.title }}</a>
                      <div class="report-template">模板：{{ record.templateName }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'type'">
                    <a-tag :color="getTypeColor(record.type)">
                      {{ getTypeText(record.type) }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'format'">
                    <a-tag :color="getFormatColor(record.format)">
                      {{ record.format.toUpperCase() }}
                    </a-tag>
                  </template>
                  
                  <template v-if="column.key === 'status'">
                    <a-badge 
                      :status="getReportStatusBadge(record.status)" 
                      :text="getReportStatusText(record.status)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="downloadHistoryReport(record)">
                        下载
                      </a-button>
                      <a-button type="link" size="small" @click="previewHistoryReport(record)">
                        预览
                      </a-button>
                      <a-button type="link" size="small" @click="shareReport(record)">
                        分享
                      </a-button>
                      <a-button type="link" size="small" danger @click="deleteHistoryReport(record)">
                        删除
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 创建/编辑模板弹窗 -->
    <a-modal
      v-model:visible="templateModalVisible"
      :title="editingTemplate ? '编辑报告模板' : '新建报告模板'"
      width="1000px"
      @ok="saveTemplate"
      @cancel="cancelTemplateEdit"
    >
      <a-form :model="templateForm" layout="vertical" ref="templateFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模板名称" name="name" :rules="[{ required: true, message: '请输入模板名称' }]">
              <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="报告类型" name="type" :rules="[{ required: true, message: '请选择报告类型' }]">
              <a-select v-model:value="templateForm.type" placeholder="选择报告类型">
                <a-select-option value="daily">日报</a-select-option>
                <a-select-option value="weekly">周报</a-select-option>
                <a-select-option value="monthly">月报</a-select-option>
                <a-select-option value="quarterly">季报</a-select-option>
                <a-select-option value="annual">年报</a-select-option>
                <a-select-option value="custom">自定义</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="模板描述" name="description">
          <a-textarea v-model:value="templateForm.description" placeholder="请输入模板描述" :rows="3" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="输出格式" name="outputFormat">
              <a-select v-model:value="templateForm.outputFormat" placeholder="选择输出格式">
                <a-select-option value="pdf">PDF</a-select-option>
                <a-select-option value="excel">Excel</a-select-option>
                <a-select-option value="word">Word</a-select-option>
                <a-select-option value="html">HTML</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="接收人">
              <a-select 
                v-model:value="templateForm.recipients" 
                mode="multiple" 
                placeholder="选择默认接收人"
              >
                <a-select-option value="<EMAIL>">管理员</a-select-option>
                <a-select-option value="<EMAIL>">督办员</a-select-option>
                <a-select-option value="<EMAIL>">领导</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="模板内容">
          <a-textarea
            v-model:value="templateForm.content"
            placeholder="请输入模板内容，支持变量：{title}, {dateRange}, {statistics}, {charts}, {tables}"
            :rows="8"
          />
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="templateForm.isDefault">设为默认模板</a-checkbox>
          <a-checkbox v-model:checked="templateForm.isEnabled">启用模板</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建定期任务弹窗 -->
    <a-modal
      v-model:visible="scheduleModalVisible"
      title="创建定期报告任务"
      width="600px"
      @ok="saveSchedule"
      @cancel="cancelScheduleEdit"
    >
      <a-form :model="scheduleForm" layout="vertical">
        <a-form-item label="任务名称" required>
          <a-input v-model:value="scheduleForm.name" placeholder="请输入任务名称" />
        </a-form-item>
        
        <a-form-item label="报告模板" required>
          <a-select v-model:value="scheduleForm.templateId" placeholder="选择报告模板">
            <a-select-option 
              v-for="template in enabledTemplates" 
              :key="template.id" 
              :value="template.id"
            >
              {{ template.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="执行频率" required>
          <a-select v-model:value="scheduleForm.frequency" placeholder="选择执行频率">
            <a-select-option value="daily">每日</a-select-option>
            <a-select-option value="weekly">每周</a-select-option>
            <a-select-option value="monthly">每月</a-select-option>
            <a-select-option value="quarterly">每季度</a-select-option>
            <a-select-option value="annually">每年</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="执行时间">
          <a-time-picker v-model:value="scheduleForm.executeTime" format="HH:mm" />
        </a-form-item>
        
        <a-form-item label="接收人">
          <a-select 
            v-model:value="scheduleForm.recipients" 
            mode="multiple" 
            placeholder="选择接收人"
          >
            <a-select-option value="<EMAIL>">管理员</a-select-option>
            <a-select-option value="<EMAIL>">督办员</a-select-option>
            <a-select-option value="<EMAIL>">领导</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="scheduleForm.enabled">启用任务</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  FileTextOutlined,
  DownloadOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type {
  ReportTemplate,
  ReportType
} from '@/types/cultivation-warning'
import { MockCultivationWarningService } from './mock/data'
import { exportToExcel } from '@/utils/export'

// 响应式数据
const loading = ref(false)
const activeTab = ref('templates')

// 报告统计
const reportStats = reactive({
  totalTemplates: 0,
  todayGenerated: 0,
  scheduledTasks: 0,
  successRate: 0
})

// 模板管理
const searchKeyword = ref('')
const selectedType = ref<ReportType | undefined>()
const selectedStatus = ref<boolean | undefined>()
const templateList = ref<ReportTemplate[]>([])

// 智能报告生成
const selectedTemplateId = ref<string>()
const generating = ref(false)
const generationProgress = ref(0)
const generationStep = ref(0)
const generationResult = ref<any>(null)

const generationForm = reactive({
  title: '',
  dateRange: null,
  dataScope: ['alerts', 'supervision'],
  outputFormat: 'pdf',
  recipients: []
})

// 定期报告配置
const scheduleList = ref([
  {
    id: '1',
    name: '培育过程预警日报',
    templateId: 'template_001',
    templateName: '培育过程预警日报',
    templateType: 'daily' as ReportType,
    frequency: 'daily',
    executeTime: '08:00',
    nextRun: '2025-06-21 08:00:00',
    enabled: true,
    recipients: ['<EMAIL>'],
    lastRun: '2025-06-20 08:00:00',
    runCount: 20
  },
  {
    id: '2',
    name: '督办管理周报',
    templateId: 'template_002',
    templateName: '督办管理周报',
    templateType: 'weekly' as ReportType,
    frequency: 'weekly',
    executeTime: '09:00',
    nextRun: '2025-06-22 09:00:00',
    enabled: true,
    recipients: ['<EMAIL>'],
    lastRun: '2025-06-15 09:00:00',
    runCount: 4
  }
])

// 定期任务表格列配置
const scheduleColumns = [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '报告模板',
    key: 'template'
  },
  {
    title: '执行计划',
    key: 'schedule'
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '最后执行',
    dataIndex: 'lastRun',
    key: 'lastRun',
    width: 150
  },
  {
    title: '执行次数',
    dataIndex: 'runCount',
    key: 'runCount',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 报告历史
const historyKeyword = ref('')
const historyType = ref<ReportType | undefined>()
const historyFormat = ref<string | undefined>()
const historyDateRange = ref()
const historyLoading = ref(false)
const reportHistory = ref<any[]>([])

// 报告历史分页
const historyPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 报告历史表格列配置
const historyColumns = [
  {
    title: '报告标题',
    key: 'title',
    width: 250
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '格式',
    key: 'format',
    width: 80
  },
  {
    title: '生成时间',
    dataIndex: 'generateTime',
    key: 'generateTime',
    width: 150
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 模板编辑
const templateModalVisible = ref(false)
const editingTemplate = ref<ReportTemplate | null>(null)
const templateFormRef = ref()

const templateForm = reactive({
  name: '',
  type: 'daily' as ReportType,
  description: '',
  outputFormat: 'pdf',
  recipients: [] as string[],
  content: '',
  isDefault: false,
  isEnabled: true
})

// 定期任务编辑
const scheduleModalVisible = ref(false)
const scheduleForm = reactive({
  name: '',
  templateId: '',
  frequency: 'daily',
  executeTime: null,
  recipients: [] as string[],
  enabled: true
})

// 计算属性
const enabledTemplates = computed(() => {
  return templateList.value.filter(template => template.isEnabled)
})

// 方法
const loadTemplates = async () => {
  loading.value = true
  try {
    const templates = await MockCultivationWarningService.getReportTemplates()
    templateList.value = templates

    // 更新统计数据
    reportStats.totalTemplates = templates.length
    reportStats.todayGenerated = 12
    reportStats.scheduledTasks = scheduleList.value.filter(s => s.enabled).length
    reportStats.successRate = 96.5
  } catch (error) {
    console.error('加载报告模板失败:', error)
    message.error('加载报告模板失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadTemplates()
}

const handleTypeFilter = () => {
  loadTemplates()
}

const handleStatusFilter = () => {
  loadTemplates()
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedType.value = undefined
  selectedStatus.value = undefined
  loadTemplates()
}

// 模板操作
const showCreateTemplate = () => {
  editingTemplate.value = null
  resetTemplateForm()
  templateModalVisible.value = true
}

const editTemplate = (template: ReportTemplate) => {
  editingTemplate.value = template
  Object.assign(templateForm, {
    name: template.name,
    type: template.type,
    description: template.description,
    outputFormat: template.outputFormat,
    recipients: [...template.recipients],
    content: template.templateContent?.title || '',
    isDefault: template.isDefault,
    isEnabled: template.isEnabled
  })
  templateModalVisible.value = true
}

const saveTemplate = async () => {
  try {
    await templateFormRef.value.validate()

    message.success(editingTemplate.value ? '模板更新成功' : '模板创建成功')
    templateModalVisible.value = false
    loadTemplates()
  } catch (error) {
    console.error('保存模板失败:', error)
  }
}

const cancelTemplateEdit = () => {
  templateModalVisible.value = false
  resetTemplateForm()
}

const resetTemplateForm = () => {
  Object.assign(templateForm, {
    name: '',
    type: 'daily' as ReportType,
    description: '',
    outputFormat: 'pdf',
    recipients: [],
    content: '',
    isDefault: false,
    isEnabled: true
  })
}

const previewTemplate = (template: ReportTemplate) => {
  message.info(`预览模板：${template.name}`)
}

const copyTemplate = (template: ReportTemplate) => {
  editingTemplate.value = null
  Object.assign(templateForm, {
    name: `${template.name} - 副本`,
    type: template.type,
    description: template.description,
    outputFormat: template.outputFormat,
    recipients: [...template.recipients],
    content: template.templateContent?.title || '',
    isDefault: false,
    isEnabled: false
  })
  templateModalVisible.value = true
}

const deleteTemplate = (template: ReportTemplate) => {
  message.success(`删除模板：${template.name}`)
  loadTemplates()
}

const toggleTemplate = (template: ReportTemplate, checked: boolean) => {
  template.isEnabled = checked
  message.success(`模板已${checked ? '启用' : '禁用'}`)
}

// 报告生成操作
const onTemplateSelect = () => {
  const template = templateList.value.find(t => t.id === selectedTemplateId.value)
  if (template) {
    generationForm.title = `${template.name}_${new Date().toISOString().split('T')[0]}`
    generationForm.outputFormat = template.outputFormat
    generationForm.recipients = [...template.recipients]
  }
}

const startGeneration = () => {
  if (!selectedTemplateId.value) {
    message.error('请先选择报告模板')
    return
  }

  generating.value = true
  generationProgress.value = 0
  generationStep.value = 0
  generationResult.value = null

  // 模拟生成过程
  const steps = [
    { step: 1, progress: 25, delay: 1000 },
    { step: 2, progress: 50, delay: 1500 },
    { step: 3, progress: 75, delay: 2000 },
    { step: 4, progress: 100, delay: 1000 }
  ]

  let currentIndex = 0
  const executeStep = () => {
    if (currentIndex < steps.length) {
      const { step, progress, delay } = steps[currentIndex]
      setTimeout(() => {
        generationStep.value = step
        generationProgress.value = progress
        currentIndex++
        executeStep()
      }, delay)
    } else {
      setTimeout(() => {
        generating.value = false
        generationResult.value = {
          duration: '5.2秒',
          fileSize: '2.3MB',
          fileName: `${generationForm.title}.${generationForm.outputFormat}`
        }
        message.success('报告生成完成')
      }, 500)
    }
  }

  executeStep()
}

const previewGeneration = () => {
  message.info('预览报告生成配置')
}

const downloadReport = () => {
  if (generationResult.value) {
    message.success(`下载报告：${generationResult.value.fileName}`)
  }
}

const previewReport = () => {
  message.info('预览生成的报告')
}

const sendReport = () => {
  message.success('报告已发送给指定接收人')
}

// 定期任务操作
const showCreateSchedule = () => {
  resetScheduleForm()
  scheduleModalVisible.value = true
}

const saveSchedule = () => {
  if (!scheduleForm.name || !scheduleForm.templateId) {
    message.error('请填写完整的任务信息')
    return
  }

  message.success('定期任务创建成功')
  scheduleModalVisible.value = false

  // 添加到列表
  const template = templateList.value.find(t => t.id === scheduleForm.templateId)
  if (template) {
    scheduleList.value.push({
      id: `schedule_${Date.now()}`,
      name: scheduleForm.name,
      templateId: scheduleForm.templateId,
      templateName: template.name,
      templateType: template.type,
      frequency: scheduleForm.frequency,
      executeTime: scheduleForm.executeTime || '08:00',
      nextRun: '2025-06-21 08:00:00',
      enabled: scheduleForm.enabled,
      recipients: [...scheduleForm.recipients],
      lastRun: '',
      runCount: 0
    })
  }
}

const cancelScheduleEdit = () => {
  scheduleModalVisible.value = false
  resetScheduleForm()
}

const resetScheduleForm = () => {
  Object.assign(scheduleForm, {
    name: '',
    templateId: '',
    frequency: 'daily',
    executeTime: null,
    recipients: [],
    enabled: true
  })
}

const editSchedule = (schedule: any) => {
  message.info(`编辑定期任务：${schedule.name}`)
}

const runSchedule = (schedule: any) => {
  message.success(`立即执行任务：${schedule.name}`)
}

const viewScheduleLogs = (schedule: any) => {
  message.info(`查看任务日志：${schedule.name}`)
}

const toggleSchedule = (schedule: any, checked: boolean) => {
  schedule.enabled = checked
  message.success(`任务已${checked ? '启用' : '禁用'}`)
}

// 报告历史操作
const loadReportHistory = () => {
  historyLoading.value = true

  // 模拟加载历史数据
  setTimeout(() => {
    reportHistory.value = Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      title: `培育过程预警报告${i + 1}`,
      templateName: `模板${i % 3 + 1}`,
      type: ['daily', 'weekly', 'monthly'][i % 3] as ReportType,
      format: ['pdf', 'excel', 'word'][i % 3],
      generateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
      fileSize: `${(Math.random() * 5 + 1).toFixed(1)}MB`,
      status: ['success', 'failed', 'generating'][Math.floor(Math.random() * 3)],
      downloadCount: Math.floor(Math.random() * 20)
    }))
    historyLoading.value = false
  }, 1000)
}

const resetHistoryFilters = () => {
  historyKeyword.value = ''
  historyType.value = undefined
  historyFormat.value = undefined
  historyDateRange.value = undefined
  historyPagination.current = 1
  loadReportHistory()
}

const handleHistoryTableChange = (pag: any) => {
  historyPagination.current = pag.current
  historyPagination.pageSize = pag.pageSize
  loadReportHistory()
}

const downloadHistoryReport = (record: any) => {
  message.success(`下载报告：${record.title}`)
}

const previewHistoryReport = (record: any) => {
  message.info(`预览报告：${record.title}`)
}

const shareReport = (record: any) => {
  message.info(`分享报告：${record.title}`)
}

const deleteHistoryReport = (record: any) => {
  message.success(`删除报告：${record.title}`)
  loadReportHistory()
}

// 其他操作
const generateReport = () => {
  activeTab.value = 'generate'
}

const exportTemplates = () => {
  const exportData = templateList.value.map(template => ({
    '模板名称': template.name,
    '报告类型': getTypeText(template.type),
    '输出格式': template.outputFormat.toUpperCase(),
    '是否启用': template.isEnabled ? '是' : '否',
    '是否默认': template.isDefault ? '是' : '否',
    '生成次数': template.generateCount,
    '创建时间': template.createTime,
    '最后生成': template.lastGeneratedTime || '未生成'
  }))

  // 使用导出工具函数
  exportToExcel(exportData as any, {
    fileName: `预警报告模板_${new Date().toISOString().split('T')[0]}.xlsx`
  })

  message.success('模板数据导出成功')
}

// 工具方法
const getTypeText = (type: ReportType) => {
  const texts = {
    daily: '日报',
    weekly: '周报',
    monthly: '月报',
    quarterly: '季报',
    annual: '年报',
    custom: '自定义'
  }
  return texts[type] || type
}

const getTypeColor = (type: ReportType) => {
  const colors = {
    daily: 'blue',
    weekly: 'green',
    monthly: 'orange',
    quarterly: 'purple',
    annual: 'red',
    custom: 'default'
  }
  return colors[type] || 'default'
}

const getFormatColor = (format: string) => {
  const colors = {
    pdf: 'red',
    excel: 'green',
    word: 'blue',
    html: 'orange'
  }
  return colors[format] || 'default'
}

const getFrequencyText = (frequency: string) => {
  const texts = {
    daily: '每日',
    weekly: '每周',
    monthly: '每月',
    quarterly: '每季度',
    annually: '每年'
  }
  return texts[frequency] || frequency
}

const getReportStatusBadge = (status: string) => {
  const badges = {
    success: 'success',
    failed: 'error',
    generating: 'processing'
  }
  return badges[status] || 'default'
}

const getReportStatusText = (status: string) => {
  const texts = {
    success: '成功',
    failed: '失败',
    generating: '生成中'
  }
  return texts[status] || status
}

// 组件挂载时加载数据
onMounted(() => {
  loadTemplates()
  loadReportHistory()
})
</script>

<style lang="scss" scoped>
.report-template {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .report-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filters,
  .history-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .template-management {
    .template-grid {
      .template-card {
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.default {
          border: 2px solid #1890ff;
        }

        .template-preview {
          height: 120px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          position: relative;

          .preview-icon {
            font-size: 32px;
            margin-bottom: 8px;
          }

          .template-type {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
          }
        }

        .template-meta {
          margin-top: 12px;

          .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              color: #8c8c8c;
            }
          }
        }
      }
    }
  }

  .report-generation {
    .template-selector {
      .template-radio-group {
        width: 100%;

        .template-option {
          width: 100%;
          margin-bottom: 12px;
          padding: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f6ffed;
          }

          .option-content {
            margin-left: 8px;

            .option-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 4px;

              .option-name {
                font-weight: 600;
                color: #262626;
              }
            }

            .option-description {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }
    }

    .generation-config {
      background: white;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #f0f0f0;
    }

    .generation-progress {
      .progress-placeholder {
        text-align: center;
        padding: 40px 20px;
      }

      .progress-active {
        .progress-steps {
          margin-top: 20px;

          .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #d9d9d9;
            transition: color 0.3s ease;

            &.active {
              color: #52c41a;
            }

            .anticon {
              margin-right: 8px;
            }
          }
        }
      }

      .progress-result {
        text-align: center;
      }
    }
  }

  .schedule-config {
    .schedule-list {
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }
      }

      .template-info {
        .template-name {
          font-weight: 600;
          color: #262626;
          margin-right: 8px;
        }
      }

      .schedule-info {
        .frequency {
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .next-run {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .report-history {
    .history-table {
      background: white;
      border-radius: 6px;
      overflow: hidden;

      .report-title {
        .report-template {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 4px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .report-overview {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .search-filters,
    .history-filters {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .report-generation {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
          margin-bottom: 16px;
        }
      }
    }

    .template-grid {
      .ant-row {
        flex-direction: column;

        .ant-col {
          width: 100%;
        }
      }
    }

    .template-selector {
      .template-radio-group {
        .template-option {
          .option-content {
            .option-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 4px;
            }
          }
        }
      }
    }
  }
}
</style>
