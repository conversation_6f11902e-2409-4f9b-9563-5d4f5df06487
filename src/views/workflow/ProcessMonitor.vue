<template>
  <div class="process-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>流程监控</h2>
        <p>实时监控流程执行状态，跟踪任务进度和异常情况</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><download-outlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            启动流程
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="运行中实例"
              :value="stats.running"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <play-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已完成实例"
              :value="stats.completed"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="异常实例"
              :value="stats.failed"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <exclamation-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均处理时长"
              :value="stats.avgDuration"
              suffix="小时"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card>
        <a-form :model="filterForm" class="filter-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="流程名称">
                <a-input 
                  v-model:value="filterForm.processName" 
                  placeholder="请输入流程名称"
                  allow-clear
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="实例状态">
                <a-select 
                  v-model:value="filterForm.status" 
                  placeholder="请选择状态"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option :value="1">草稿</a-select-option>
                  <a-select-option :value="2">运行中</a-select-option>
                  <a-select-option :value="3">挂起</a-select-option>
                  <a-select-option :value="4">已完成</a-select-option>
                  <a-select-option :value="5">已终止</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="启动人">
                <a-input 
                  v-model:value="filterForm.startUser" 
                  placeholder="请输入启动人"
                  allow-clear
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="启动时间">
                <a-range-picker 
                  v-model:value="filterForm.startTimeRange"
                  style="width: 100%"
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 流程实例列表 -->
    <div class="instance-list">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>流程实例 ({{ pagination.total }})</span>
            <a-space>
              <a-button size="small" @click="refreshData">
                <template #icon><reload-outlined /></template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleBatchAction">
                    <a-menu-item key="suspend" :disabled="!selectedRowKeys.length">
                      <pause-circle-outlined />
                      批量挂起
                    </a-menu-item>
                    <a-menu-item key="resume" :disabled="!selectedRowKeys.length">
                      <play-circle-outlined />
                      批量恢复
                    </a-menu-item>
                    <a-menu-item key="terminate" :disabled="!selectedRowKeys.length" danger>
                      <stop-outlined />
                      批量终止
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="instanceList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 流程名称列 -->
          <template #processName="{ record }">
            <div class="process-name">
              <a @click="viewInstance(record)">{{ record.processDefinitionName }}</a>
              <a-tag v-if="record.businessKey" color="blue" size="small">
                {{ record.businessKey }}
              </a-tag>
            </div>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-badge 
              :status="getStatusBadge(record.status)" 
              :text="getStatusText(record.status)"
            />
          </template>

          <!-- 进度列 -->
          <template #progress="{ record }">
            <div class="progress-info">
              <a-progress 
                :percent="record.progress || 0" 
                size="small"
                :status="getProgressStatus(record.status)"
              />
              <div class="current-nodes">
                <a-tag 
                  v-for="node in record.currentNodes" 
                  :key="node" 
                  color="processing"
                  size="small"
                >
                  {{ node }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 持续时间列 -->
          <template #duration="{ record }">
            <span>{{ formatDuration(record.duration) }}</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewInstance(record)">
                查看
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(e) => handleAction(e.key, record)">
                    <a-menu-item key="diagram">
                      <apartment-outlined />
                      流程图
                    </a-menu-item>
                    <a-menu-item key="tasks">
                      <unordered-list-outlined />
                      任务列表
                    </a-menu-item>
                    <a-menu-item key="variables">
                      <database-outlined />
                      变量查看
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="suspend" 
                      :disabled="record.status !== 2"
                    >
                      <pause-circle-outlined />
                      挂起
                    </a-menu-item>
                    <a-menu-item 
                      key="resume" 
                      :disabled="record.status !== 3"
                    >
                      <play-circle-outlined />
                      恢复
                    </a-menu-item>
                    <a-menu-item key="terminate" danger>
                      <stop-outlined />
                      终止
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 启动流程模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      title="启动流程"
      width="600px"
      @ok="handleCreateSubmit"
      :confirm-loading="submitting"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        layout="vertical"
      >
        <a-form-item label="选择流程模板" name="processDefinitionId" required>
          <a-select 
            v-model:value="createForm.processDefinitionId" 
            placeholder="请选择流程模板"
            show-search
            :filter-option="filterOption"
          >
            <a-select-option 
              v-for="template in processTemplates" 
              :key="template.id" 
              :value="template.id"
            >
              {{ template.name }} (v{{ template.version }})
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="业务标识" name="businessKey">
          <a-input 
            v-model:value="createForm.businessKey" 
            placeholder="请输入业务标识（可选）"
          />
        </a-form-item>
        
        <a-form-item label="流程变量" name="variables">
          <div class="variables-editor">
            <div 
              v-for="(variable, index) in createForm.variables" 
              :key="index" 
              class="variable-item"
            >
              <a-row :gutter="8" align="middle">
                <a-col :span="8">
                  <a-input 
                    v-model:value="variable.key" 
                    placeholder="变量名"
                    size="small"
                  />
                </a-col>
                <a-col :span="12">
                  <a-input 
                    v-model:value="variable.value" 
                    placeholder="变量值"
                    size="small"
                  />
                </a-col>
                <a-col :span="4">
                  <a-button 
                    type="text" 
                    size="small" 
                    danger
                    @click="removeVariable(index)"
                  >
                    <template #icon><delete-outlined /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
            
            <a-button 
              type="dashed" 
              size="small" 
              block
              @click="addVariable"
            >
              <template #icon><plus-outlined /></template>
              添加变量
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 实例详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="流程实例详情"
      width="80%"
      :footer="null"
    >
      <div v-if="selectedInstance" class="instance-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="实例ID">
            {{ selectedInstance.id }}
          </a-descriptions-item>
          <a-descriptions-item label="流程名称">
            {{ selectedInstance.processDefinitionName }}
          </a-descriptions-item>
          <a-descriptions-item label="业务标识">
            {{ selectedInstance.businessKey || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge 
              :status="getStatusBadge(selectedInstance.status)" 
              :text="getStatusText(selectedInstance.status)"
            />
          </a-descriptions-item>
          <a-descriptions-item label="启动人">
            {{ selectedInstance.startUserId }}
          </a-descriptions-item>
          <a-descriptions-item label="启动时间">
            {{ selectedInstance.startTime }}
          </a-descriptions-item>
          <a-descriptions-item label="结束时间">
            {{ selectedInstance.endTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="持续时间">
            {{ formatDuration(selectedInstance.duration) }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>当前节点</a-divider>
        <a-space wrap>
          <a-tag 
            v-for="node in selectedInstance.currentNodes" 
            :key="node" 
            color="processing"
          >
            {{ node }}
          </a-tag>
        </a-space>
        
        <a-divider>流程变量</a-divider>
        <a-table
          :columns="variableColumns"
          :data-source="getVariableList(selectedInstance.variables)"
          :pagination="false"
          size="small"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  ReloadOutlined,
  DownloadOutlined,
  PlusOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DownOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ApartmentOutlined,
  UnorderedListOutlined,
  DatabaseOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { ProcessInstance, ProcessDefinition } from '@/types/workflow'
import dayjs, { type Dayjs } from 'dayjs'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)

// 模态框状态
const createModalVisible = ref(false)
const detailModalVisible = ref(false)

// 统计数据
const stats = reactive({
  running: 23,
  completed: 156,
  failed: 3,
  avgDuration: 4.2
})

// 筛选表单
const filterForm = reactive({
  processName: '',
  status: undefined as number | undefined,
  startUser: '',
  startTimeRange: undefined as [Dayjs, Dayjs] | undefined
})

// 创建表单
const createForm = reactive({
  processDefinitionId: '',
  businessKey: '',
  variables: [] as Array<{ key: string; value: string }>
})

const createFormRef = ref()

// 表单验证规则
const createFormRules = {
  processDefinitionId: [
    { required: true, message: '请选择流程模板', trigger: 'change' }
  ]
}

// 流程实例列表
const instanceList = ref<ProcessInstance[]>([])

// 流程模板列表
const processTemplates = ref<ProcessDefinition[]>([])

// 选中的实例
const selectedInstance = ref<ProcessInstance | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '实例ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true
  },
  {
    title: '流程名称',
    key: 'processName',
    slots: { customRender: 'processName' },
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 100
  },
  {
    title: '进度',
    key: 'progress',
    slots: { customRender: 'progress' },
    width: 200
  },
  {
    title: '启动人',
    dataIndex: 'startUserId',
    key: 'startUserId',
    width: 100
  },
  {
    title: '启动时间',
    dataIndex: 'startTime',
    key: 'startTime',
    sorter: true,
    width: 150
  },
  {
    title: '持续时间',
    key: 'duration',
    slots: { customRender: 'duration' },
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 变量表格列
const variableColumns: TableColumnsType = [
  {
    title: '变量名',
    dataIndex: 'key',
    key: 'key'
  },
  {
    title: '变量值',
    dataIndex: 'value',
    key: 'value'
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type'
  }
]

// 工具方法
const getStatusBadge = (status: number) => {
  const badges = {
    1: 'default',    // 草稿
    2: 'processing', // 运行中
    3: 'warning',    // 挂起
    4: 'success',    // 已完成
    5: 'error'       // 已终止
  }
  return badges[status as keyof typeof badges] || 'default'
}

const getStatusText = (status: number) => {
  const texts = {
    1: '草稿',
    2: '运行中',
    3: '挂起',
    4: '已完成',
    5: '已终止'
  }
  return texts[status as keyof typeof texts] || '未知'
}

const getProgressStatus = (status: number) => {
  if (status === 4) return 'success'
  if (status === 5) return 'exception'
  return 'active'
}

const formatDuration = (duration?: number) => {
  if (!duration) return '-'

  const hours = Math.floor(duration / 3600000)
  const minutes = Math.floor((duration % 3600000) / 60000)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const getVariableList = (variables: Record<string, any>) => {
  return Object.entries(variables).map(([key, value]) => ({
    key,
    value: typeof value === 'object' ? JSON.stringify(value) : String(value),
    type: typeof value
  }))
}

// 数据加载
const loadInstanceList = async () => {
  loading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟数据
    const mockData: ProcessInstance[] = [
      {
        id: 'inst_001',
        processDefinitionId: 'proc_def_001',
        processDefinitionName: '员工请假审批流程',
        businessKey: 'LEAVE_2024_001',
        status: 2,
        startTime: '2025-06-21 09:30:00',
        endTime: undefined,
        duration: 7200000, // 2小时
        startUserId: '周海军',
        variables: {
          applicant: '周海军',
          leaveType: '年假',
          days: 3,
          reason: '家庭事务'
        },
        currentNodes: ['部门经理审批'],
        progress: 60
      },
      {
        id: 'inst_002',
        processDefinitionId: 'proc_def_002',
        processDefinitionName: '采购申请流程',
        businessKey: 'PURCHASE_2024_002',
        status: 4,
        startTime: '2025-06-19 14:20:00',
        endTime: '2025-06-20 10:15:00',
        duration: 72300000, // 20小时
        startUserId: '汪有村',
        variables: {
          applicant: '汪有村',
          amount: 15000,
          supplier: 'ABC公司',
          items: ['办公用品', '电脑设备']
        },
        currentNodes: [],
        progress: 100
      },
      {
        id: 'inst_003',
        processDefinitionId: 'proc_def_001',
        processDefinitionName: '员工请假审批流程',
        businessKey: 'LEAVE_2024_003',
        status: 3,
        startTime: '2025-06-18 16:45:00',
        endTime: undefined,
        duration: 180000000, // 50小时
        startUserId: '杜佳佳',
        variables: {
          applicant: '杜佳佳',
          leaveType: '病假',
          days: 5,
          reason: '身体不适'
        },
        currentNodes: ['HR审批'],
        progress: 80
      }
    ]

    instanceList.value = mockData
    pagination.total = mockData.length

    // 更新统计数据
    stats.running = mockData.filter(i => i.status === 2).length
    stats.completed = mockData.filter(i => i.status === 4).length
    stats.failed = mockData.filter(i => i.status === 5).length

  } catch (error) {
    message.error('加载流程实例失败')
    console.error('Load instance list error:', error)
  } finally {
    loading.value = false
  }
}

const loadProcessTemplates = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟数据
    processTemplates.value = [
      {
        id: 'proc_def_001',
        name: '员工请假审批流程',
        description: '标准的员工请假审批流程',
        version: 2,
        category: 'approval',
        nodes: [],
        connections: [],
        variables: {},
        properties: {},
        createTime: '2025-06-15 10:30:00',
        updateTime: '2025-06-20 14:20:00',
        createdBy: '周海军',
        status: 2
      },
      {
        id: 'proc_def_002',
        name: '采购申请流程',
        description: '企业采购申请审批流程',
        version: 1,
        category: 'business',
        nodes: [],
        connections: [],
        variables: {},
        properties: {},
        createTime: '2025-06-10 09:15:00',
        updateTime: '2025-06-10 09:15:00',
        createdBy: '王东',
        status: 2
      }
    ]
  } catch (error) {
    console.error('Load process templates error:', error)
  }
}

// 筛选和搜索
const handleFilter = () => {
  pagination.current = 1
  loadInstanceList()
}

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.current = pag?.current || 1
  pagination.pageSize = pag?.pageSize || 10
  loadInstanceList()
}

// 刷新数据
const refreshData = () => {
  loadInstanceList()
}

// 实例操作
const viewInstance = (instance: ProcessInstance) => {
  selectedInstance.value = instance
  detailModalVisible.value = true
}

const handleAction = (action: string, instance: ProcessInstance) => {
  switch (action) {
    case 'diagram':
      router.push(`/workflow/designer/${instance.processDefinitionId}?mode=view&instanceId=${instance.id}`)
      break
    case 'tasks':
      message.info(`查看任务列表: ${instance.id}`)
      break
    case 'variables':
      viewInstance(instance)
      break
    case 'suspend':
      updateInstanceStatus(instance, 3)
      break
    case 'resume':
      updateInstanceStatus(instance, 2)
      break
    case 'terminate':
      terminateInstance(instance)
      break
  }
}

const updateInstanceStatus = async (instance: ProcessInstance, status: number) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    instance.status = status
    message.success(`实例状态更新为"${getStatusText(status)}"`)

    // 刷新统计数据
    refreshData()
  } catch (error) {
    message.error('状态更新失败')
    console.error('Update instance status error:', error)
  }
}

const terminateInstance = (instance: ProcessInstance) => {
  Modal.confirm({
    title: '确认终止',
    content: `确定要终止流程实例"${instance.id}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        instance.status = 5
        instance.endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

        message.success('流程实例已终止')
        refreshData()
      } catch (error) {
        message.error('终止流程失败')
        console.error('Terminate instance error:', error)
      }
    }
  })
}

// 批量操作
const handleBatchAction = ({ key }: { key: string }) => {
  if (!selectedRowKeys.value.length) {
    message.warning('请先选择要操作的实例')
    return
  }

  switch (key) {
    case 'suspend':
      batchUpdateStatus(3)
      break
    case 'resume':
      batchUpdateStatus(2)
      break
    case 'terminate':
      batchTerminate()
      break
  }
}

const batchUpdateStatus = async (status: number) => {
  const statusText = getStatusText(status)

  Modal.confirm({
    title: '批量操作确认',
    content: `确定要将选中的 ${selectedRowKeys.value.length} 个实例状态设为"${statusText}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        instanceList.value.forEach(instance => {
          if (selectedRowKeys.value.includes(instance.id)) {
            instance.status = status
          }
        })

        selectedRowKeys.value = []
        message.success(`批量${statusText}成功`)
        refreshData()
      } catch (error) {
        message.error(`批量${statusText}失败`)
        console.error('Batch update status error:', error)
      }
    }
  })
}

const batchTerminate = () => {
  Modal.confirm({
    title: '批量终止确认',
    content: `确定要终止选中的 ${selectedRowKeys.value.length} 个流程实例吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        instanceList.value.forEach(instance => {
          if (selectedRowKeys.value.includes(instance.id)) {
            instance.status = 5
            instance.endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        })

        selectedRowKeys.value = []
        message.success('批量终止成功')
        refreshData()
      } catch (error) {
        message.error('批量终止失败')
        console.error('Batch terminate error:', error)
      }
    }
  })
}

// 启动流程
const showCreateModal = () => {
  createForm.processDefinitionId = ''
  createForm.businessKey = ''
  createForm.variables = []
  createModalVisible.value = true
}

const handleCreateSubmit = async () => {
  try {
    await createFormRef.value.validate()
    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const selectedTemplate = processTemplates.value.find(
      t => t.id === createForm.processDefinitionId
    )

    const newInstance: ProcessInstance = {
      id: `inst_${Date.now()}`,
      processDefinitionId: createForm.processDefinitionId,
      processDefinitionName: selectedTemplate?.name || '未知流程',
      businessKey: createForm.businessKey,
      status: 2,
      startTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      endTime: undefined,
      duration: 0,
      startUserId: '当前用户',
      variables: createForm.variables.reduce((acc, v) => {
        if (v.key && v.value) {
          acc[v.key] = v.value
        }
        return acc
      }, {} as Record<string, any>),
      currentNodes: ['开始节点'],
      progress: 10
    }

    instanceList.value.unshift(newInstance)
    pagination.total++

    createModalVisible.value = false
    message.success('流程启动成功')
    refreshData()
  } catch (error) {
    console.error('Create instance error:', error)
  } finally {
    submitting.value = false
  }
}

// 变量管理
const addVariable = () => {
  createForm.variables.push({ key: '', value: '' })
}

const removeVariable = (index: number) => {
  createForm.variables.splice(index, 1)
}

// 其他功能
const exportReport = () => {
  const reportData = {
    stats,
    instances: instanceList.value,
    exportTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
  }

  const dataStr = JSON.stringify(reportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `workflow_monitor_report_${dayjs().format('YYYYMMDD_HHmmss')}.json`
  link.click()

  message.success('监控报告导出成功')
}

const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 生命周期
onMounted(() => {
  loadInstanceList()
  loadProcessTemplates()
})
</script>

<style scoped>
.process-monitor {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-overview {
  margin-bottom: 24px;
}

.stats-overview .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 24px;
}

.filter-form {
  margin-bottom: 0;
}

.instance-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.process-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.process-name a {
  font-weight: 500;
  color: #1890ff;
}

.process-name a:hover {
  color: #40a9ff;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.current-nodes {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.instance-detail {
  padding: 16px 0;
}

.variables-editor {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.variable-item {
  margin-bottom: 8px;
}

.variable-item:last-child {
  margin-bottom: 12px;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-row-selected > td) {
  background: #e6f7ff;
}

/* 进度条样式 */
:deep(.ant-progress-line) {
  margin-bottom: 4px;
}

:deep(.ant-progress-text) {
  font-size: 12px;
}

/* 状态标签样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 操作按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #333;
}

:deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .process-monitor {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    justify-content: space-between;
  }

  :deep(.ant-btn) {
    flex: 1;
  }

  /* 表格在移动端的优化 */
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }

  .progress-info {
    gap: 4px;
  }

  .current-nodes {
    gap: 2px;
  }
}

/* 动画效果 */
.process-monitor {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
:deep(.ant-spin-container) {
  min-height: 200px;
}

/* 空状态样式 */
:deep(.ant-empty) {
  padding: 40px 0;
}

:deep(.ant-empty-description) {
  color: #999;
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 8px;
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 16px;
}

:deep(.ant-dropdown-menu-item:hover) {
  background: #f5f5f5;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .process-monitor {
    background: #1f1f1f;
  }

  .page-header,
  .instance-list {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  .variables-editor {
    background: #404040;
    border-color: #595959;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #404040;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr > td) {
    background: #2f2f2f;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #404040;
  }

  :deep(.ant-descriptions-item-label) {
    color: #fff;
  }

  :deep(.ant-descriptions-item-content) {
    color: #ccc;
  }
}
</style>
