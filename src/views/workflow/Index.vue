<template>
  <div class="workflow-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>流程引擎</h1>
        <p>智能化流程设计、管理和监控平台，提升业务流程效率</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="流程模板" :value="stats.templates" suffix="个" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="运行实例" :value="stats.instances" suffix="个" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="待处理任务" :value="stats.tasks" suffix="个" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="createNewProcess">
              <div class="action-icon">
                <plus-circle-outlined />
              </div>
              <div class="action-content">
                <h3>新建流程</h3>
                <p>从零开始设计新的业务流程</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToTemplates">
              <div class="action-icon">
                <folder-outlined />
              </div>
              <div class="action-content">
                <h3>模板管理</h3>
                <p>管理和维护流程模板库</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToMonitor">
              <div class="action-icon">
                <monitor-outlined />
              </div>
              <div class="action-content">
                <h3>流程监控</h3>
                <p>实时监控流程执行状态</p>
              </div>
            </div>
          </a-col>

          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToTasks">
              <div class="action-icon">
                <unordered-list-outlined />
              </div>
              <div class="action-content">
                <h3>任务管理</h3>
                <p>处理和跟踪工作任务</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 功能模块区域 -->
    <div class="feature-modules">
      <a-row :gutter="[24, 24]">
        <!-- 流程配置模块 -->
        <a-col :xs="24" :lg="12">
          <a-card title="流程配置" :bordered="false">
            <template #extra>
              <a @click="goToDesigner">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>可视化设计</h4>
                    <p>拖拽式流程建模面板，直观易用</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>节点管理</h4>
                    <p>丰富的节点类型，灵活的属性配置</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>流转控制</h4>
                    <p>支持条件流转和规则引擎</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="createNewProcess">
                开始设计流程
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 内容管理模块 -->
        <a-col :xs="24" :lg="12">
          <a-card title="内容管理" :bordered="false">
            <template #extra>
              <a @click="goToForms">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>表单设计</h4>
                    <p>可视化表单设计器，支持多种组件</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>文档管理</h4>
                    <p>支持Word编辑和PDF预览</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>数据集管理</h4>
                    <p>结构化数据创建和批量处理</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToForms">
                管理表单内容
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <a-card title="最近活动" :bordered="false">
        <template #extra>
          <a @click="viewAllActivities">查看全部</a>
        </template>

        <a-list
          :data-source="recentActivities"
          :loading="activitiesLoading"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: item.color }">
                    {{ item.type }}
                  </a-avatar>
                </template>
                <template #title>
                  <a @click="viewActivity(item)">{{ item.title }}</a>
                </template>
                <template #description>
                  {{ item.description }} · {{ item.time }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlusCircleOutlined,
  FolderOutlined,
  MonitorOutlined,
  UnorderedListOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const activitiesLoading = ref(false)

// 统计数据
const stats = reactive({
  templates: 12,
  instances: 45,
  tasks: 8
})

// 最近活动数据
const recentActivities = ref([
  // {
  //   id: 1,
  //   type: '创建',
  //   title: '员工请假审批流程',
  //   description: '张王东创建了新的流程模板',
  //   time: '2小时前',
  //   color: '#52c41a'
  // },
  // {
  //   id: 2,
  //   type: '执行',
  //   title: '采购申请流程实例',
  //   description: '田井安启动了采购申请流程',
  //   time: '4小时前',
  //   color: '#1890ff'
  // },
  // {
  //   id: 3,
  //   type: '完成',
  //   title: '报销审批任务',
  //   description: '杜佳佳完成了报销审批任务',
  //   time: '6小时前',
  //   color: '#faad14'
  // },
  // {
  //   id: 4,
  //   type: '更新',
  //   title: '合同审批流程',
  //   description: '孙建更新了流程模板版本',
  //   time: '1天前',
  //   color: '#722ed1'
  // }
])

// 导航方法
const createNewProcess = () => {
  router.push('/workflow/designer/new')
}

const goToTemplates = () => {
  console.log('go to templates');
  
  // router.push('/workflow/template')
  router.push({ name: 'TemplateManage' }) 
}

const goToMonitor = () => {
  router.push('/workflow/monitor')
}

const goToTasks = () => {
  router.push('/workflow/task')
}

const goToDesigner = () => {
  router.push('/workflow/designer/new')
}

const goToForms = () => {
  router.push('/workflow/form/new')
}

const viewAllActivities = () => {
  message.info('活动历史功能正在开发中...')
}

const viewActivity = (activity: any) => {
  message.info(`查看活动: ${activity.title}`)
}

// 数据加载
const loadStats = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 这里可以从API加载真实的统计数据
    stats.templates = 12
    stats.instances = 45
    stats.tasks = 8
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

const loadRecentActivities = async () => {
  activitiesLoading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 这里可以从API加载真实的活动数据
  } catch (error) {
    message.error('加载最近活动失败')
    console.error('Load recent activities error:', error)
  } finally {
    activitiesLoading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.workflow-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 24px;
  color: white;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: white;
}

.header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-stats {
  min-width: 300px;
}

:deep(.header-stats .ant-statistic-title) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.header-stats .ant-statistic-content) {
  color: white;
  font-weight: 600;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.action-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.feature-modules {
  margin-bottom: 24px;
}

.module-content {
  padding: 8px 0;
}

.feature-list {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  font-size: 16px;
  color: #52c41a;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-text h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.feature-text p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.recent-activity {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片样式优化 */
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-card-extra a) {
  color: #1890ff;
  font-size: 14px;
}

:deep(.ant-card-extra a:hover) {
  color: #40a9ff;
}

/* 列表样式优化 */
:deep(.ant-list-item) {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-list-item:last-child) {
  border-bottom: none;
}

:deep(.ant-list-item-meta-title a) {
  color: #333;
  font-weight: 500;
}

:deep(.ant-list-item-meta-title a:hover) {
  color: #1890ff;
}

:deep(.ant-list-item-meta-description) {
  color: #666;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .header-stats {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .workflow-container {
    padding: 16px;
  }

  .page-header {
    padding: 24px 20px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .action-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
  }

  .feature-icon {
    margin-right: 0;
    margin-bottom: 8px;
    margin-top: 0;
  }
}

/* 动画效果 */
.workflow-container {
  animation: fadeIn 0.6s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-card {
  animation: slideInUp 0.6s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .workflow-container {
    background: #1f1f1f;
  }

  .action-card {
    background: #2f2f2f;
    border-color: #404040;
  }

  .action-content h3 {
    color: #fff;
  }

  .action-content p {
    color: #ccc;
  }

  .feature-text h4 {
    color: #fff;
  }

  .feature-text p {
    color: #ccc;
  }

  :deep(.ant-card) {
    background: #2f2f2f;
    border-color: #404040;
  }

  :deep(.ant-card-head) {
    border-color: #404040;
  }

  :deep(.ant-card-head-title) {
    color: #fff;
  }

  :deep(.ant-list-item) {
    border-color: #404040;
  }

  :deep(.ant-list-item-meta-title a) {
    color: #fff;
  }

  :deep(.ant-list-item-meta-description) {
    color: #ccc;
  }
}
</style>
