<template>
  <div class="template-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>流程模板管理</h2>
        <p>管理和维护流程模板，支持版本控制和模板复用</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建模板
          </a-button>
          <a-button @click="showImportModal">
            <template #icon><upload-outlined /></template>
            导入模板
          </a-button>
          <a-button @click="exportTemplates" :disabled="!selectedRowKeys.length">
            <template #icon><download-outlined /></template>
            导出模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="模板名称">
                <a-input 
                  v-model:value="searchForm.name" 
                  placeholder="请输入模板名称"
                  allow-clear
                  @change="handleSearch"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="模板分类">
                <a-select 
                  v-model:value="searchForm.category" 
                  placeholder="请选择分类"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="approval">审批流程</a-select-option>
                  <a-select-option value="business">业务流程</a-select-option>
                  <a-select-option value="system">系统流程</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="状态">
                <a-select 
                  v-model:value="searchForm.status" 
                  placeholder="请选择状态"
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option :value="1">草稿</a-select-option>
                  <a-select-option :value="2">激活</a-select-option>
                  <a-select-option :value="3">挂起</a-select-option>
                  <a-select-option :value="5">终止</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="创建时间">
                <a-range-picker 
                  v-model:value="searchForm.createTimeRange"
                  style="width: 100%"
                  @change="handleSearch"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 模板列表 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>模板列表 ({{ pagination.total }})</span>
            <a-space>
              <a-button size="small" @click="refreshData">
                <template #icon><reload-outlined /></template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleBatchAction">
                    <a-menu-item key="activate" :disabled="!selectedRowKeys.length">
                      <check-circle-outlined />
                      批量激活
                    </a-menu-item>
                    <a-menu-item key="suspend" :disabled="!selectedRowKeys.length">
                      <pause-circle-outlined />
                      批量挂起
                    </a-menu-item>
                    <a-menu-item key="delete" :disabled="!selectedRowKeys.length" danger>
                      <delete-outlined />
                      批量删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="templateList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 模板名称列 -->
          <template #name="{ record }">
            <div class="template-name">
              <a @click="viewTemplate(record)">{{ record.name }}</a>
              <a-tag v-if="record.isDefault" color="gold" size="small">默认</a-tag>
            </div>
          </template>

          <!-- 分类列 -->
          <template #category="{ record }">
            <a-tag :color="getCategoryColor(record.category)">
              {{ getCategoryLabel(record.category) }}
            </a-tag>
          </template>

          <!-- 版本列 -->
          <template #version="{ record }">
            <a-space>
              <span>v{{ record.version }}</span>
              <a-button 
                type="link" 
                size="small" 
                @click="showVersionHistory(record)"
              >
                历史版本
              </a-button>
            </a-space>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-badge 
              :status="getStatusBadge(record.status)" 
              :text="getStatusText(record.status)"
            />
          </template>

          <!-- 节点数量列 -->
          <template #nodeCount="{ record }">
            <a-space>
              <a-tooltip title="节点数量">
                <a-tag color="blue">{{ record.nodes?.length || 0 }}</a-tag>
              </a-tooltip>
              <a-tooltip title="连线数量">
                <a-tag color="green">{{ record.connections?.length || 0 }}</a-tag>
              </a-tooltip>
            </a-space>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="editTemplate(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="copyTemplate(record)">
                复制
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(e) => handleAction(e.key, record)">
                    <a-menu-item key="design">
                      <edit-outlined />
                      设计流程
                    </a-menu-item>
                    <a-menu-item key="preview">
                      <eye-outlined />
                      预览
                    </a-menu-item>
                    <a-menu-item key="export">
                      <download-outlined />
                      导出
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item 
                      key="activate" 
                      :disabled="record.status === 2"
                    >
                      <check-circle-outlined />
                      激活
                    </a-menu-item>
                    <a-menu-item 
                      key="suspend" 
                      :disabled="record.status !== 2"
                    >
                      <pause-circle-outlined />
                      挂起
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 创建/编辑模板模态框 -->
    <a-modal
      v-model:open="createModalVisible"
      :title="editingTemplate ? '编辑模板' : '新建模板'"
      width="600px"
      @ok="handleCreateSubmit"
      :confirm-loading="submitting"
    >
      <a-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        layout="vertical"
      >
        <a-form-item label="模板名称" name="name" required>
          <a-input 
            v-model:value="createForm.name" 
            placeholder="请输入模板名称"
          />
        </a-form-item>
        
        <a-form-item label="模板描述" name="description">
          <a-textarea 
            v-model:value="createForm.description" 
            placeholder="请输入模板描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="模板分类" name="category" required>
          <a-select 
            v-model:value="createForm.category" 
            placeholder="请选择分类"
          >
            <a-select-option value="approval">审批流程</a-select-option>
            <a-select-option value="business">业务流程</a-select-option>
            <a-select-option value="system">系统流程</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="版本号" name="version">
          <a-input-number 
            v-model:value="createForm.version" 
            :min="1"
            :disabled="!!editingTemplate"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="设为默认模板" name="isDefault">
          <a-switch v-model:checked="createForm.isDefault" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入模板模态框 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入模板"
      width="500px"
      @ok="handleImportSubmit"
      :confirm-loading="importing"
    >
      <a-upload-dragger
        v-model:file-list="importFileList"
        :before-upload="beforeUpload"
        accept=".json"
        :multiple="false"
      >
        <p class="ant-upload-drag-icon">
          <inbox-outlined />
        </p>
        <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
        <p class="ant-upload-hint">
          支持单个JSON格式的模板文件上传
        </p>
      </a-upload-dragger>
    </a-modal>

    <!-- 版本历史模态框 -->
    <a-modal
      v-model:open="versionHistoryVisible"
      title="版本历史"
      width="800px"
      :footer="null"
    >
      <a-table
        :columns="versionColumns"
        :data-source="versionHistory"
        :loading="versionLoading"
        :pagination="false"
        size="small"
      >
        <template #version="{ record }">
          <a-tag color="blue">v{{ record.version }}</a-tag>
        </template>
        
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="previewVersion(record)">
              预览
            </a-button>
            <a-button type="link" size="small" @click="restoreVersion(record)">
              恢复
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  PauseCircleOutlined,
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined
} from '@ant-design/icons-vue'
import type { ProcessDefinition } from '@/types/workflow'
import dayjs, { type Dayjs } from 'dayjs'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const importing = ref(false)
const versionLoading = ref(false)

// 模态框状态
const createModalVisible = ref(false)
const importModalVisible = ref(false)
const versionHistoryVisible = ref(false)

// 编辑状态
const editingTemplate = ref<ProcessDefinition | null>(null)

// 搜索表单
const searchForm = reactive({
  name: '',
  category: undefined as string | undefined,
  status: undefined as number | undefined,
  createTimeRange: undefined as [Dayjs, Dayjs] | undefined
})

// 创建表单
const createForm = reactive({
  name: '',
  description: '',
  category: '',
  version: 1,
  isDefault: false
})

const createFormRef = ref()

// 表单验证规则
const createFormRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '模板名称长度在2-50个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择模板分类', trigger: 'change' }
  ]
}

// 导入文件
const importFileList = ref([])

// 模板列表
const templateList = ref<ProcessDefinition[]>([])

// 版本历史
const versionHistory = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '模板名称',
    dataIndex: 'name',
    key: 'name',
    slots: { customRender: 'name' },
    sorter: true,
    width: 200
  },
  {
    title: '分类',
    dataIndex: 'category',
    key: 'category',
    slots: { customRender: 'category' },
    width: 100
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    slots: { customRender: 'version' },
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 100
  },
  {
    title: '节点/连线',
    key: 'nodeCount',
    slots: { customRender: 'nodeCount' },
    width: 120
  },
  {
    title: '创建人',
    dataIndex: 'createdBy',
    key: 'createdBy',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
    width: 150
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true,
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 200,
    fixed: 'right'
  }
]

// 版本历史表格列
const versionColumns: TableColumnsType = [
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    slots: { customRender: 'version' }
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '创建人',
    dataIndex: 'createdBy',
    key: 'createdBy'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' }
  }
]

// 工具方法
const getCategoryColor = (category: string) => {
  const colors = {
    approval: 'blue',
    business: 'green',
    system: 'orange'
  }
  return colors[category as keyof typeof colors] || 'default'
}

const getCategoryLabel = (category: string) => {
  const labels = {
    approval: '审批流程',
    business: '业务流程',
    system: '系统流程'
  }
  return labels[category as keyof typeof labels] || category
}

const getStatusBadge = (status: number) => {
  const badges = {
    1: 'default',  // 草稿
    2: 'processing', // 激活
    3: 'warning',   // 挂起
    4: 'success',   // 完成
    5: 'error'      // 终止
  }
  return badges[status as keyof typeof badges] || 'default'
}

const getStatusText = (status: number) => {
  const texts = {
    1: '草稿',
    2: '激活',
    3: '挂起',
    4: '完成',
    5: '终止'
  }
  return texts[status as keyof typeof texts] || '未知'
}

// 数据加载
const loadTemplateList = async () => {
  loading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟数据
    const mockData: ProcessDefinition[] = [
      {
        id: 'template_1',
        name: '员工请假审批流程',
        description: '标准的员工请假审批流程模板',
        version: 2,
        category: 'approval',
        nodes: [
          { id: 'start', type: 'start', name: '开始', position: { x: 100, y: 100 }, size: { width: 80, height: 80 }, properties: {} },
          { id: 'task1', type: 'task', name: '填写请假申请', position: { x: 250, y: 100 }, size: { width: 120, height: 80 }, properties: {} },
          { id: 'end', type: 'end', name: '结束', position: { x: 400, y: 100 }, size: { width: 80, height: 80 }, properties: {} }
        ],
        connections: [
          { id: 'conn1', type: 'sequence', sourceNodeId: 'start', targetNodeId: 'task1', properties: {} },
          { id: 'conn2', type: 'sequence', sourceNodeId: 'task1', targetNodeId: 'end', properties: {} }
        ],
        variables: {},
        properties: { isDefault: true },
        createTime: '2025-06-15 10:30:00',
        updateTime: '2025-06-20 14:20:00',
        createdBy: '周海军',
        status: 2
      },
      {
        id: 'template_2',
        name: '采购申请流程',
        description: '企业采购申请审批流程',
        version: 1,
        category: 'business',
        nodes: [
          { id: 'start', type: 'start', name: '开始', position: { x: 100, y: 100 }, size: { width: 80, height: 80 }, properties: {} },
          { id: 'task1', type: 'task', name: '提交采购申请', position: { x: 250, y: 100 }, size: { width: 120, height: 80 }, properties: {} }
        ],
        connections: [
          { id: 'conn1', type: 'sequence', sourceNodeId: 'start', targetNodeId: 'task1', properties: {} }
        ],
        variables: {},
        properties: {},
        createTime: '2025-06-10 09:15:00',
        updateTime: '2025-06-10 09:15:00',
        createdBy: '王东',
        status: 1
      }
    ]

    templateList.value = mockData
    pagination.total = mockData.length

  } catch (error) {
    message.error('加载模板列表失败')
    console.error('Load template list error:', error)
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadTemplateList()
}

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.current = pag?.current || 1
  pagination.pageSize = pag?.pageSize || 10
  loadTemplateList()
}

// 刷新数据
const refreshData = () => {
  loadTemplateList()
}

// 模板操作
const viewTemplate = (template: ProcessDefinition) => {
  router.push(`/workflow/designer/${template.id}?mode=view`)
}

const editTemplate = (template: ProcessDefinition) => {
  editingTemplate.value = template
  createForm.name = template.name
  createForm.description = template.description || ''
  createForm.category = template.category || ''
  createForm.version = template.version
  createForm.isDefault = !!template.properties?.isDefault
  createModalVisible.value = true
}

const copyTemplate = async (template: ProcessDefinition) => {
  try {
    const newTemplate = {
      ...template,
      id: `template_${Date.now()}`,
      name: `${template.name}_副本`,
      version: 1,
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: 1
    }

    templateList.value.unshift(newTemplate)
    message.success('模板复制成功')
  } catch (error) {
    message.error('模板复制失败')
    console.error('Copy template error:', error)
  }
}

const deleteTemplate = async (template: ProcessDefinition) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        const index = templateList.value.findIndex(t => t.id === template.id)
        if (index >= 0) {
          templateList.value.splice(index, 1)
          pagination.total--
        }

        message.success('模板删除成功')
      } catch (error) {
        message.error('模板删除失败')
        console.error('Delete template error:', error)
      }
    }
  })
}

// 操作处理
const handleAction = (action: string, template: ProcessDefinition) => {
  switch (action) {
    case 'design':
      router.push(`/workflow/designer/${template.id}`)
      break
    case 'preview':
      router.push(`/workflow/designer/${template.id}?mode=preview`)
      break
    case 'export':
      exportSingleTemplate(template)
      break
    case 'activate':
      updateTemplateStatus(template, 2)
      break
    case 'suspend':
      updateTemplateStatus(template, 3)
      break
    case 'delete':
      deleteTemplate(template)
      break
  }
}

// 批量操作
const handleBatchAction = ({ key }: { key: string }) => {
  if (!selectedRowKeys.value.length) {
    message.warning('请先选择要操作的模板')
    return
  }

  switch (key) {
    case 'activate':
      batchUpdateStatus(2)
      break
    case 'suspend':
      batchUpdateStatus(3)
      break
    case 'delete':
      batchDelete()
      break
  }
}

const batchUpdateStatus = async (status: number) => {
  const statusText = getStatusText(status)

  Modal.confirm({
    title: '批量操作确认',
    content: `确定要将选中的 ${selectedRowKeys.value.length} 个模板状态设为"${statusText}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        templateList.value.forEach(template => {
          if (selectedRowKeys.value.includes(template.id)) {
            template.status = status
            template.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        })

        selectedRowKeys.value = []
        message.success(`批量${statusText}成功`)
      } catch (error) {
        message.error(`批量${statusText}失败`)
        console.error('Batch update status error:', error)
      }
    }
  })
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个模板吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        templateList.value = templateList.value.filter(
          template => !selectedRowKeys.value.includes(template.id)
        )

        pagination.total -= selectedRowKeys.value.length
        selectedRowKeys.value = []
        message.success('批量删除成功')
      } catch (error) {
        message.error('批量删除失败')
        console.error('Batch delete error:', error)
      }
    }
  })
}

// 更新模板状态
const updateTemplateStatus = async (template: ProcessDefinition, status: number) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    template.status = status
    template.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

    message.success(`模板状态更新为"${getStatusText(status)}"`)
  } catch (error) {
    message.error('状态更新失败')
    console.error('Update template status error:', error)
  }
}

// 模态框操作
const showCreateModal = () => {
  editingTemplate.value = null
  createForm.name = ''
  createForm.description = ''
  createForm.category = ''
  createForm.version = 1
  createForm.isDefault = false
  createModalVisible.value = true
}

const handleCreateSubmit = async () => {
  try {
    await createFormRef.value.validate()
    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingTemplate.value) {
      // 编辑模式
      const template = editingTemplate.value
      template.name = createForm.name
      template.description = createForm.description
      template.category = createForm.category
      template.properties = {
        ...template.properties,
        isDefault: createForm.isDefault
      }
      template.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

      message.success('模板更新成功')
    } else {
      // 新建模式
      const newTemplate: ProcessDefinition = {
        id: `template_${Date.now()}`,
        name: createForm.name,
        description: createForm.description,
        version: createForm.version,
        category: createForm.category,
        nodes: [],
        connections: [],
        variables: {},
        properties: { isDefault: createForm.isDefault },
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        createdBy: '当前用户',
        status: 1
      }

      templateList.value.unshift(newTemplate)
      pagination.total++

      message.success('模板创建成功')
    }

    createModalVisible.value = false
  } catch (error) {
    console.error('Submit error:', error)
  } finally {
    submitting.value = false
  }
}

// 导入导出
const showImportModal = () => {
  importFileList.value = []
  importModalVisible.value = true
}

const beforeUpload = (file: File) => {
  const isJson = file.type === 'application/json' || file.name.endsWith('.json')
  if (!isJson) {
    message.error('只能上传JSON格式的文件')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB')
    return false
  }

  return false // 阻止自动上传
}

const handleImportSubmit = async () => {
  if (!importFileList.value.length) {
    message.warning('请选择要导入的文件')
    return
  }

  importing.value = true

  try {
    const file = importFileList.value[0] as any
    const text = await file.originFileObj.text()
    const templateData = JSON.parse(text)

    // 验证模板数据格式
    if (!templateData.name || !templateData.nodes || !templateData.connections) {
      throw new Error('模板文件格式不正确')
    }

    // 生成新的ID和时间戳
    const newTemplate: ProcessDefinition = {
      ...templateData,
      id: `template_${Date.now()}`,
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      createdBy: '当前用户',
      status: 1
    }

    templateList.value.unshift(newTemplate)
    pagination.total++

    importModalVisible.value = false
    message.success('模板导入成功')
  } catch (error) {
    message.error('模板导入失败：' + (error as Error).message)
    console.error('Import template error:', error)
  } finally {
    importing.value = false
  }
}

const exportTemplates = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要导出的模板')
    return
  }

  const selectedTemplates = templateList.value.filter(
    template => selectedRowKeys.value.includes(template.id)
  )

  const dataStr = JSON.stringify(selectedTemplates, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `workflow_templates_${dayjs().format('YYYYMMDD_HHmmss')}.json`
  link.click()

  message.success(`已导出 ${selectedTemplates.length} 个模板`)
}

const exportSingleTemplate = (template: ProcessDefinition) => {
  const dataStr = JSON.stringify(template, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `${template.name}_v${template.version}.json`
  link.click()

  message.success('模板导出成功')
}

// 版本历史
const showVersionHistory = async (template: ProcessDefinition) => {
  versionHistoryVisible.value = true
  versionLoading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟版本历史数据
    versionHistory.value = [
      {
        version: 2,
        description: '优化审批流程，增加自动通知功能',
        createdBy: '周海军',
        createTime: '2025-06-20 14:20:00'
      },
      {
        version: 1,
        description: '初始版本',
        createdBy: '周海军',
        createTime: '2025-06-15 10:30:00'
      }
    ]
  } catch (error) {
    message.error('加载版本历史失败')
    console.error('Load version history error:', error)
  } finally {
    versionLoading.value = false
  }
}

const previewVersion = (version: any) => {
  message.info(`预览版本 v${version.version} 功能开发中...`)
}

const restoreVersion = (version: any) => {
  Modal.confirm({
    title: '确认恢复版本',
    content: `确定要恢复到版本 v${version.version} 吗？当前版本将被覆盖。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      message.success(`已恢复到版本 v${version.version}`)
    }
  })
}

// 生命周期
onMounted(() => {
  loadTemplateList()
})
</script>

<style scoped>
.template-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.search-section {
  margin-bottom: 24px;
}

.search-form {
  margin-bottom: 0;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.template-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-name a {
  font-weight: 500;
  color: #1890ff;
}

.template-name a:hover {
  color: #40a9ff;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-row-selected > td) {
  background: #e6f7ff;
}

/* 状态标签样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 操作按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
}

/* 上传组件样式 */
:deep(.ant-upload-drag) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
}

:deep(.ant-upload-drag:hover) {
  border-color: #1890ff;
}

:deep(.ant-upload-drag-icon) {
  font-size: 48px;
  color: #1890ff;
}

:deep(.ant-upload-text) {
  font-size: 16px;
  color: #333;
  margin: 16px 0 8px 0;
}

:deep(.ant-upload-hint) {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .template-manage {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    justify-content: space-between;
  }

  :deep(.ant-btn) {
    flex: 1;
  }

  /* 表格在移动端的优化 */
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }
}

/* 动画效果 */
.template-manage {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
:deep(.ant-spin-container) {
  min-height: 200px;
}

/* 空状态样式 */
:deep(.ant-empty) {
  padding: 40px 0;
}

:deep(.ant-empty-description) {
  color: #999;
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 8px;
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 16px;
}

:deep(.ant-dropdown-menu-item:hover) {
  background: #f5f5f5;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .template-manage {
    background: #1f1f1f;
  }

  .page-header,
  .table-section {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #404040;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr > td) {
    background: #2f2f2f;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #404040;
  }
}
</style>
