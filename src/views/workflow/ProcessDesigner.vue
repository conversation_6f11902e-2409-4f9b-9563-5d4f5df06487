<template>
  <div class="process-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <a-space>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
          <a-divider type="vertical" />
          <span class="process-title">{{ processDefinition.name || '新建流程' }}</span>
        </a-space>
      </div>
      
      <div class="header-center">
        <a-space>
          <a-button @click="saveProcess" type="primary" :loading="saving">
            <template #icon><save-outlined /></template>
            保存
          </a-button>
          <a-button @click="validateProcess">
            <template #icon><check-circle-outlined /></template>
            验证
          </a-button>
          <a-button @click="previewProcess">
            <template #icon><eye-outlined /></template>
            预览
          </a-button>
        </a-space>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="showSettings">
            <template #icon><setting-outlined /></template>
            设置
          </a-button>
          <a-button @click="showHelp">
            <template #icon><question-circle-outlined /></template>
            帮助
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧节点工具栏 -->
      <div class="left-panel">
        <node-toolbar
          @add-node="addNode"
          @drag-start="onNodeDragStart"
          @drag-end="onNodeDragEnd"
        />
      </div>

      <!-- 中间画布区域 -->
      <div class="center-panel">
        <workflow-canvas
          :nodes="processDefinition.nodes"
          :connections="processDefinition.connections"
          :config="canvasConfig"
          @update:nodes="updateNodes"
          @update:connections="updateConnections"
          @node-select="onNodeSelect"
          @connection-select="onConnectionSelect"
          @drop="onCanvasDrop"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel" v-if="showPropertiesPanel">
        <node-properties
          v-if="selectedNode"
          :node="selectedNode"
          @update="updateNode"
          @close="closePropertiesPanel"
        />
        <connection-properties
          v-if="selectedConnection"
          :connection="selectedConnection"
          @update="updateConnection"
          @close="closePropertiesPanel"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="designer-footer">
      <div class="footer-left">
        <a-space>
          <span class="status-item">
            节点: {{ processDefinition.nodes.length }}
          </span>
          <span class="status-item">
            连线: {{ processDefinition.connections.length }}
          </span>
          <span class="status-item" v-if="validationResult">
            {{ validationResult.valid ? '✓ 验证通过' : '✗ 验证失败' }}
          </span>
        </a-space>
      </div>
      
      <div class="footer-right">
        <a-space>
          <span class="status-item">
            最后保存: {{ lastSaveTime || '未保存' }}
          </span>
          <span class="status-item">
            版本: {{ processDefinition.version }}
          </span>
        </a-space>
      </div>
    </div>

    <!-- 流程设置模态框 -->
    <a-modal
      v-model:open="settingsVisible"
      title="流程设置"
      width="600px"
      @ok="saveSettings"
    >
      <a-form :model="settingsForm" layout="vertical">
        <a-form-item label="流程名称" required>
          <a-input v-model:value="settingsForm.name" placeholder="请输入流程名称" />
        </a-form-item>
        
        <a-form-item label="流程描述">
          <a-textarea 
            v-model:value="settingsForm.description" 
            placeholder="请输入流程描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="流程分类">
          <a-select v-model:value="settingsForm.category" placeholder="请选择流程分类">
            <a-select-option value="approval">审批流程</a-select-option>
            <a-select-option value="business">业务流程</a-select-option>
            <a-select-option value="system">系统流程</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="版本号">
          <a-input-number 
            v-model:value="settingsForm.version" 
            :min="1"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 流程预览模态框 -->
    <a-modal
      v-model:open="previewVisible"
      title="流程预览"
      width="80%"
      :footer="null"
    >
      <div class="process-preview">
        <workflow-canvas
          :nodes="processDefinition.nodes"
          :connections="processDefinition.connections"
          :config="{ ...canvasConfig, readonly: true }"
          style="height: 500px;"
        />
      </div>
    </a-modal>

    <!-- 验证结果模态框 -->
    <a-modal
      v-model:open="validationVisible"
      title="流程验证结果"
      width="500px"
      :footer="null"
    >
      <div class="validation-result">
        <a-result
          :status="validationResult?.valid ? 'success' : 'error'"
          :title="validationResult?.valid ? '验证通过' : '验证失败'"
          :sub-title="validationResult?.message"
        >
          <template #extra v-if="validationResult?.errors?.length">
            <div class="validation-errors">
              <h4>错误详情:</h4>
              <ul>
                <li v-for="error in validationResult.errors" :key="error">
                  {{ error }}
                </li>
              </ul>
            </div>
          </template>
        </a-result>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  EyeOutlined,
  SettingOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import type {
  ProcessDefinition,
  NodeDefinition,
  ConnectionDefinition,
  NodeType,
  DesignerConfig
} from '@/types/workflow'
import WorkflowCanvas from './components/designer/WorkflowCanvas.vue'
import NodeToolbar from './components/designer/NodeToolbar.vue'
import NodeProperties from './components/designer/NodeProperties.vue'
import ConnectionProperties from './components/designer/ConnectionProperties.vue'
import dayjs from 'dayjs'

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const saving = ref(false)
const lastSaveTime = ref<string>('')
const showPropertiesPanel = ref(false)
const selectedNode = ref<NodeDefinition | null>(null)
const selectedConnection = ref<ConnectionDefinition | null>(null)

// 模态框状态
const settingsVisible = ref(false)
const previewVisible = ref(false)
const validationVisible = ref(false)

// 流程定义
const processDefinition = reactive<ProcessDefinition>({
  id: route.params.id as string || `process_${Date.now()}`,
  name: '新建流程',
  description: '',
  version: 1,
  category: 'business',
  nodes: [],
  connections: [],
  variables: {},
  properties: {},
  createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  createdBy: 'current_user',
  status: 1 // ProcessStatus.DRAFT
})

// 设置表单
const settingsForm = reactive({
  name: processDefinition.name,
  description: processDefinition.description,
  category: processDefinition.category,
  version: processDefinition.version
})

// 验证结果
interface ValidationResult {
  valid: boolean
  message: string
  errors?: string[]
}

const validationResult = ref<ValidationResult | null>(null)

// 画布配置
const canvasConfig = computed<Partial<DesignerConfig>>(() => ({
  canvasSize: { width: 2000, height: 1500 },
  theme: {
    primaryColor: '#1890ff',
    backgroundColor: '#ffffff',
    gridColor: '#e8e8e8',
    nodeColors: {
      start: '#52c41a',
      end: '#ff4d4f',
      task: '#1890ff',
      decision: '#faad14',
      parallel: '#722ed1',
      merge: '#13c2c2',
      subprocess: '#eb2f96'
    }
  }
}))

// 方法
const goBack = () => {
  router.push('/workflow')
}

const saveProcess = async () => {
  saving.value = true

  try {
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    processDefinition.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    lastSaveTime.value = dayjs().format('HH:mm:ss')

    message.success('流程保存成功')
  } catch (error) {
    message.error('流程保存失败')
    console.error('Save process error:', error)
  } finally {
    saving.value = false
  }
}

const validateProcess = () => {
  const errors: string[] = []

  // 检查是否有开始节点
  const startNodes = processDefinition.nodes.filter(n => n.type === 'start')
  if (startNodes.length === 0) {
    errors.push('流程必须包含至少一个开始节点')
  } else if (startNodes.length > 1) {
    errors.push('流程只能包含一个开始节点')
  }

  // 检查是否有结束节点
  const endNodes = processDefinition.nodes.filter(n => n.type === 'end')
  if (endNodes.length === 0) {
    errors.push('流程必须包含至少一个结束节点')
  }

  // 检查节点连接
  for (const node of processDefinition.nodes) {
    if (node.type !== 'end') {
      const outgoingConnections = processDefinition.connections.filter(c => c.sourceNodeId === node.id)
      if (outgoingConnections.length === 0) {
        errors.push(`节点"${node.name}"缺少输出连线`)
      }
    }

    if (node.type !== 'start') {
      const incomingConnections = processDefinition.connections.filter(c => c.targetNodeId === node.id)
      if (incomingConnections.length === 0) {
        errors.push(`节点"${node.name}"缺少输入连线`)
      }
    }
  }

  // 检查连线有效性
  for (const connection of processDefinition.connections) {
    const sourceNode = processDefinition.nodes.find(n => n.id === connection.sourceNodeId)
    const targetNode = processDefinition.nodes.find(n => n.id === connection.targetNodeId)

    if (!sourceNode) {
      errors.push(`连线"${connection.id}"的源节点不存在`)
    }
    if (!targetNode) {
      errors.push(`连线"${connection.id}"的目标节点不存在`)
    }
  }

  validationResult.value = {
    valid: errors.length === 0,
    message: errors.length === 0 ? '流程验证通过，可以正常使用' : `发现 ${errors.length} 个问题`,
    errors
  }

  validationVisible.value = true
}

const previewProcess = () => {
  previewVisible.value = true
}

const showSettings = () => {
  settingsForm.name = processDefinition.name
  settingsForm.description = processDefinition.description
  settingsForm.category = processDefinition.category
  settingsForm.version = processDefinition.version
  settingsVisible.value = true
}

const saveSettings = () => {
  processDefinition.name = settingsForm.name
  processDefinition.description = settingsForm.description
  processDefinition.category = settingsForm.category
  processDefinition.version = settingsForm.version
  processDefinition.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

  settingsVisible.value = false
  message.success('设置保存成功')
}

const showHelp = () => {
  message.info('帮助文档正在开发中...')
}

// 节点操作
const addNode = (nodeType: NodeType, position?: { x: number; y: number }) => {
  const defaultPosition = position || { x: 200 + Math.random() * 400, y: 150 + Math.random() * 300 }

  const newNode: NodeDefinition = {
    id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: nodeType,
    name: getDefaultNodeName(nodeType),
    description: '',
    position: defaultPosition,
    size: getDefaultNodeSize(nodeType),
    properties: {},
    priority: 1
  }

  processDefinition.nodes.push(newNode)

  // 自动选中新添加的节点
  selectedNode.value = newNode
  selectedConnection.value = null
  showPropertiesPanel.value = true
}

const getDefaultNodeName = (nodeType: NodeType): string => {
  const names = {
    start: '开始',
    end: '结束',
    task: '用户任务',
    decision: '条件判断',
    parallel: '并行分支',
    merge: '分支合并',
    subprocess: '子流程'
  }
  return names[nodeType] || '未知节点'
}

const getDefaultNodeSize = (nodeType: NodeType) => {
  const sizes = {
    start: { width: 80, height: 80 },
    end: { width: 80, height: 80 },
    task: { width: 120, height: 80 },
    decision: { width: 100, height: 100 },
    parallel: { width: 120, height: 80 },
    merge: { width: 120, height: 80 },
    subprocess: { width: 140, height: 100 }
  }
  return sizes[nodeType] || { width: 120, height: 80 }
}

const updateNodes = (nodes: NodeDefinition[]) => {
  processDefinition.nodes = nodes
}

const updateConnections = (connections: ConnectionDefinition[]) => {
  processDefinition.connections = connections
}

const updateNode = (updatedNode: NodeDefinition) => {
  const index = processDefinition.nodes.findIndex(n => n.id === updatedNode.id)
  if (index >= 0) {
    processDefinition.nodes[index] = updatedNode
    selectedNode.value = updatedNode
  }
}

const updateConnection = (updatedConnection: ConnectionDefinition) => {
  const index = processDefinition.connections.findIndex(c => c.id === updatedConnection.id)
  if (index >= 0) {
    processDefinition.connections[index] = updatedConnection
    selectedConnection.value = updatedConnection
  }
}

// 选择事件
const onNodeSelect = (node: NodeDefinition | null) => {
  selectedNode.value = node
  selectedConnection.value = null
  showPropertiesPanel.value = !!node
}

const onConnectionSelect = (connection: ConnectionDefinition | null) => {
  selectedConnection.value = connection
  selectedNode.value = null
  showPropertiesPanel.value = !!connection
}

const closePropertiesPanel = () => {
  showPropertiesPanel.value = false
  selectedNode.value = null
  selectedConnection.value = null
}

// 拖拽事件
const onNodeDragStart = (nodeType: NodeType) => {
  console.log('Node drag start:', nodeType)
}

const onNodeDragEnd = () => {
  console.log('Node drag end')
}

const onCanvasDrop = (event: DragEvent) => {
  event.preventDefault()

  try {
    const data = event.dataTransfer?.getData('application/json')
    if (data) {
      const dropData = JSON.parse(data)
      if (dropData.type === 'workflow-node') {
        // 计算拖放位置
        const rect = (event.target as HTMLElement).getBoundingClientRect()
        const position = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top
        }

        addNode(dropData.nodeType, position)
      }
    }
  } catch (error) {
    console.error('Drop error:', error)
  }
}

// 键盘快捷键
const onKeyDown = (event: KeyboardEvent) => {
  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveProcess()
  }

  // Ctrl+Z 撤销 (TODO: 实现撤销功能)
  if (event.ctrlKey && event.key === 'z') {
    event.preventDefault()
    message.info('撤销功能正在开发中...')
  }

  // Ctrl+Y 重做 (TODO: 实现重做功能)
  if (event.ctrlKey && event.key === 'y') {
    event.preventDefault()
    message.info('重做功能正在开发中...')
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', onKeyDown)

  // 如果是编辑模式，加载流程数据
  if (route.params.id && route.params.id !== 'new') {
    loadProcess(route.params.id as string)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', onKeyDown)
})

// 加载流程数据
const loadProcess = async (processId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 这里应该从API加载真实数据
    message.success('流程加载成功')
  } catch (error) {
    message.error('流程加载失败')
    console.error('Load process error:', error)
  }
}
</script>

<style scoped>
.process-designer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
}

.process-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.designer-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel {
  width: 260px;
  background: white;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  padding: 16px;
}

.center-panel {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.right-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
}

.designer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  background: white;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
}

.footer-left,
.footer-right {
  display: flex;
  align-items: center;
}

.status-item {
  padding: 0 8px;
  border-right: 1px solid #e8e8e8;
}

.status-item:last-child {
  border-right: none;
}

.process-preview {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.validation-result {
  padding: 16px;
}

.validation-errors {
  text-align: left;
  margin-top: 16px;
}

.validation-errors h4 {
  margin-bottom: 8px;
  color: #ff4d4f;
}

.validation-errors ul {
  margin: 0;
  padding-left: 20px;
}

.validation-errors li {
  margin-bottom: 4px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 220px;
  }

  .right-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .designer-header {
    flex-direction: column;
    gap: 8px;
    padding: 8px 16px;
  }

  .header-left,
  .header-center,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .left-panel {
    width: 180px;
  }

  .right-panel {
    width: 240px;
  }

  .designer-footer {
    flex-direction: column;
    gap: 4px;
    padding: 8px 16px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
  width: 4px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 动画效果 */
.designer-content {
  transition: all 0.3s ease;
}

.right-panel {
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .process-designer {
    background: #1f1f1f;
  }

  .designer-header,
  .left-panel,
  .right-panel,
  .designer-footer {
    background: #2f2f2f;
    border-color: #404040;
  }

  .process-title {
    color: #fff;
  }

  .status-item {
    color: #ccc;
    border-color: #404040;
  }
}
</style>
