<template>
  <div class="form-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <a-space>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
          <a-divider type="vertical" />
          <span class="form-title">{{ formDefinition.name || '新建表单' }}</span>
        </a-space>
      </div>
      
      <div class="header-center">
        <a-space>
          <a-button @click="saveForm" type="primary" :loading="saving">
            <template #icon><save-outlined /></template>
            保存
          </a-button>
          <a-button @click="previewForm">
            <template #icon><eye-outlined /></template>
            预览
          </a-button>
          <a-button @click="clearForm">
            <template #icon><clear-outlined /></template>
            清空
          </a-button>
        </a-space>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="showSettings">
            <template #icon><setting-outlined /></template>
            设置
          </a-button>
          <a-button @click="showHelp">
            <template #icon><question-circle-outlined /></template>
            帮助
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 左侧组件工具栏 -->
      <div class="left-panel">
        <form-component-toolbar
          @add-field="addField"
          @drag-start="onFieldDragStart"
          @drag-end="onFieldDragEnd"
        />
      </div>

      <!-- 中间设计区域 -->
      <div class="center-panel">
        <form-canvas
          :fields="formDefinition.fields"
          :layout="formDefinition.layout"
          @update:fields="updateFields"
          @field-select="onFieldSelect"
          @drop="onCanvasDrop"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="right-panel" v-if="showPropertiesPanel">
        <field-properties
          v-if="selectedField"
          :field="selectedField"
          @update="updateField"
          @close="closePropertiesPanel"
        />
        <form-properties
          v-else
          :form="formDefinition"
          @update="updateFormProperties"
          @close="closePropertiesPanel"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="designer-footer">
      <div class="footer-left">
        <a-space>
          <span class="status-item">
            字段数量: {{ formDefinition.fields.length }}
          </span>
          <span class="status-item">
            布局: {{ formDefinition.layout.columns }}列
          </span>
        </a-space>
      </div>
      
      <div class="footer-right">
        <a-space>
          <span class="status-item">
            最后保存: {{ lastSaveTime || '未保存' }}
          </span>
        </a-space>
      </div>
    </div>

    <!-- 表单设置模态框 -->
    <a-modal
      v-model:open="settingsVisible"
      title="表单设置"
      width="600px"
      @ok="saveSettings"
    >
      <a-form :model="settingsForm" layout="vertical">
        <a-form-item label="表单名称" required>
          <a-input v-model:value="settingsForm.name" placeholder="请输入表单名称" />
        </a-form-item>
        
        <a-form-item label="表单描述">
          <a-textarea 
            v-model:value="settingsForm.description" 
            placeholder="请输入表单描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="布局设置">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="列数">
                <a-input-number 
                  v-model:value="settingsForm.layout.columns" 
                  :min="1"
                  :max="4"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="标签宽度">
                <a-input-number 
                  v-model:value="settingsForm.layout.labelWidth" 
                  :min="80"
                  :max="200"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="组件尺寸">
                <a-select v-model:value="settingsForm.layout.size" style="width: 100%">
                  <a-select-option value="small">小</a-select-option>
                  <a-select-option value="default">中</a-select-option>
                  <a-select-option value="large">大</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 表单预览模态框 -->
    <a-modal
      v-model:open="previewVisible"
      title="表单预览"
      width="80%"
      :footer="null"
    >
      <div class="form-preview">
        <form-renderer
          :fields="formDefinition.fields"
          :layout="formDefinition.layout"
          :readonly="true"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  SaveOutlined,
  EyeOutlined,
  ClearOutlined,
  SettingOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue'
import type { FormDefinition, FormFieldDefinition, FormFieldType } from '@/types/workflow'
import FormComponentToolbar from './components/form-designer/FormComponentToolbar.vue'
import FormCanvas from './components/form-designer/FormCanvas.vue'
import FieldProperties from './components/form-designer/FieldProperties.vue'
import FormProperties from './components/form-designer/FormProperties.vue'
import FormRenderer from './components/form-designer/FormRenderer.vue'
import dayjs from 'dayjs'

// 路由
const router = useRouter()
const route = useRoute()

// 响应式数据
const saving = ref(false)
const lastSaveTime = ref<string>('')
const showPropertiesPanel = ref(false)
const selectedField = ref<FormFieldDefinition | null>(null)

// 模态框状态
const settingsVisible = ref(false)
const previewVisible = ref(false)

// 表单定义
const formDefinition = reactive<FormDefinition>({
  id: route.params.id as string || `form_${Date.now()}`,
  name: '新建表单',
  description: '',
  fields: [],
  layout: {
    columns: 2,
    labelWidth: 120,
    size: 'default'
  },
  properties: {},
  createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
})

// 设置表单
const settingsForm = reactive({
  name: formDefinition.name,
  description: formDefinition.description,
  layout: { ...formDefinition.layout }
})

// 方法
const goBack = () => {
  router.push('/workflow')
}

const saveForm = async () => {
  saving.value = true
  
  try {
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    formDefinition.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    lastSaveTime.value = dayjs().format('HH:mm:ss')
    
    message.success('表单保存成功')
  } catch (error) {
    message.error('表单保存失败')
    console.error('Save form error:', error)
  } finally {
    saving.value = false
  }
}

const previewForm = () => {
  previewVisible.value = true
}

const clearForm = () => {
  formDefinition.fields = []
  selectedField.value = null
  showPropertiesPanel.value = false
  message.success('表单已清空')
}

const showSettings = () => {
  settingsForm.name = formDefinition.name
  settingsForm.description = formDefinition.description
  settingsForm.layout = { ...formDefinition.layout }
  settingsVisible.value = true
}

const saveSettings = () => {
  formDefinition.name = settingsForm.name
  formDefinition.description = settingsForm.description
  formDefinition.layout = { ...settingsForm.layout }
  formDefinition.updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
  
  settingsVisible.value = false
  message.success('设置保存成功')
}

const showHelp = () => {
  message.info('帮助文档正在开发中...')
}

// 字段操作
const addField = (fieldType: FormFieldType, position?: { x: number; y: number }) => {
  const newField: FormFieldDefinition = {
    id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: `field_${formDefinition.fields.length + 1}`,
    label: getDefaultFieldLabel(fieldType),
    type: fieldType,
    required: false,
    defaultValue: undefined,
    placeholder: `请输入${getDefaultFieldLabel(fieldType)}`,
    options: fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox' 
      ? [{ label: '选项1', value: 'option1' }, { label: '选项2', value: 'option2' }] 
      : undefined,
    validation: {},
    properties: {}
  }
  
  formDefinition.fields.push(newField)
  
  // 自动选中新添加的字段
  selectedField.value = newField
  showPropertiesPanel.value = true
}

const getDefaultFieldLabel = (fieldType: FormFieldType): string => {
  const labels = {
    text: '文本输入',
    textarea: '多行文本',
    number: '数字输入',
    select: '下拉选择',
    radio: '单选按钮',
    checkbox: '多选框',
    date: '日期选择',
    datetime: '日期时间',
    file: '文件上传',
    image: '图片上传'
  }
  return labels[fieldType] || '未知字段'
}

const updateFields = (fields: FormFieldDefinition[]) => {
  formDefinition.fields = fields
}

const updateField = (updatedField: FormFieldDefinition) => {
  const index = formDefinition.fields.findIndex(f => f.id === updatedField.id)
  if (index >= 0) {
    formDefinition.fields[index] = updatedField
    selectedField.value = updatedField
  }
}

const updateFormProperties = (updatedForm: Partial<FormDefinition>) => {
  Object.assign(formDefinition, updatedForm)
}

// 选择事件
const onFieldSelect = (field: FormFieldDefinition | null) => {
  selectedField.value = field
  showPropertiesPanel.value = !!field
}

const closePropertiesPanel = () => {
  showPropertiesPanel.value = false
  selectedField.value = null
}

// 拖拽事件
const onFieldDragStart = (fieldType: FormFieldType) => {
  console.log('Field drag start:', fieldType)
}

const onFieldDragEnd = () => {
  console.log('Field drag end')
}

const onCanvasDrop = (event: DragEvent) => {
  event.preventDefault()
  
  try {
    const data = event.dataTransfer?.getData('application/json')
    if (data) {
      const dropData = JSON.parse(data)
      if (dropData.type === 'form-field') {
        addField(dropData.fieldType)
      }
    }
  } catch (error) {
    console.error('Drop error:', error)
  }
}

// 生命周期
onMounted(() => {
  // 如果是编辑模式，加载表单数据
  if (route.params.id && route.params.id !== 'new') {
    loadForm(route.params.id as string)
  }
})

// 加载表单数据
const loadForm = async (formId: string) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 这里应该从API加载真实数据
    message.success('表单加载成功')
  } catch (error) {
    message.error('表单加载失败')
    console.error('Load form error:', error)
  }
}
</script>

<style scoped>
.form-designer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
}

.form-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.designer-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-panel {
  width: 260px;
  background: white;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
  padding: 16px;
}

.center-panel {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.right-panel {
  width: 320px;
  background: white;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
}

.designer-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  background: white;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
}

.footer-left,
.footer-right {
  display: flex;
  align-items: center;
}

.status-item {
  padding: 0 8px;
  border-right: 1px solid #e8e8e8;
}

.status-item:last-child {
  border-right: none;
}

.form-preview {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 220px;
  }

  .right-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .designer-header {
    flex-direction: column;
    gap: 8px;
    padding: 8px 16px;
  }

  .header-left,
  .header-center,
  .header-right {
    width: 100%;
    justify-content: center;
  }

  .left-panel {
    width: 180px;
  }

  .right-panel {
    width: 240px;
  }

  .designer-footer {
    flex-direction: column;
    gap: 4px;
    padding: 8px 16px;
  }

  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}

/* 滚动条样式 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
  width: 4px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.left-panel::-webkit-scrollbar-thumb:hover,
.right-panel::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 动画效果 */
.designer-content {
  transition: all 0.3s ease;
}

.right-panel {
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .form-designer {
    background: #1f1f1f;
  }

  .designer-header,
  .left-panel,
  .right-panel,
  .designer-footer {
    background: #2f2f2f;
    border-color: #404040;
  }

  .form-title {
    color: #fff;
  }

  .status-item {
    color: #ccc;
    border-color: #404040;
  }

  .form-preview {
    background: #2f2f2f;
    border-color: #404040;
  }
}
</style>
