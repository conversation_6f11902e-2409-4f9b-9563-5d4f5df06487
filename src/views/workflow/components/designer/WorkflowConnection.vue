<template>
  <g 
    class="workflow-connection"
    :class="{ 'selected': selected, [`connection-${connection.type}`]: true }"
    @click="onClick"
  >
    <!-- 连线路径 -->
    <path
      :d="pathData"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      :stroke-dasharray="strokeDashArray"
      fill="none"
      class="connection-path"
      :marker-end="markerEnd"
    />
    
    <!-- 选中状态的高亮路径 -->
    <path
      v-if="selected"
      :d="pathData"
      stroke="#1890ff"
      :stroke-width="strokeWidth + 4"
      stroke-opacity="0.3"
      fill="none"
      class="connection-highlight"
    />

    <!-- 箭头标记 -->
    <defs>
      <marker
        :id="`arrow-${connection.id}`"
        viewBox="0 0 10 10"
        refX="9"
        refY="3"
        markerWidth="6"
        markerHeight="6"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <path
          d="M0,0 L0,6 L9,3 z"
          :fill="strokeColor"
        />
      </marker>
    </defs>

    <!-- 连线标签 -->
    <g v-if="connection.name" class="connection-label">
      <rect
        :x="labelPosition.x - labelSize.width / 2"
        :y="labelPosition.y - labelSize.height / 2"
        :width="labelSize.width"
        :height="labelSize.height"
        rx="4"
        ry="4"
        fill="white"
        stroke="#d9d9d9"
        stroke-width="1"
      />
      <text
        :x="labelPosition.x"
        :y="labelPosition.y"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="12"
        fill="#333333"
        class="label-text"
      >
        {{ connection.name }}
      </text>
    </g>

    <!-- 条件标签 -->
    <g v-if="connection.condition" class="condition-label">
      <rect
        :x="conditionPosition.x - conditionSize.width / 2"
        :y="conditionPosition.y - conditionSize.height / 2"
        :width="conditionSize.width"
        :height="conditionSize.height"
        rx="2"
        ry="2"
        fill="#faad14"
        stroke="#d48806"
        stroke-width="1"
      />
      <text
        :x="conditionPosition.x"
        :y="conditionPosition.y"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="10"
        fill="white"
        class="condition-text"
      >
        {{ conditionText }}
      </text>
    </g>

    <!-- 删除按钮 -->
    <g v-if="selected" class="delete-button" @click.stop="onDelete">
      <circle
        :cx="deleteButtonPosition.x"
        :cy="deleteButtonPosition.y"
        r="8"
        fill="#ff4d4f"
        stroke="white"
        stroke-width="2"
      />
      <text
        :x="deleteButtonPosition.x"
        :y="deleteButtonPosition.y"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="10"
        fill="white"
      >
        ×
      </text>
    </g>

    <!-- 控制点（用于调整连线形状） -->
    <g v-if="selected && controlPoints.length > 0" class="control-points">
      <circle
        v-for="(point, index) in controlPoints"
        :key="index"
        :cx="point.x"
        :cy="point.y"
        r="4"
        fill="#1890ff"
        stroke="white"
        stroke-width="2"
        class="control-point"
        @mousedown.stop="onControlPointMouseDown(index, $event)"
      />
    </g>
  </g>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { ConnectionDefinition, NodeDefinition, Position } from '@/types/workflow'

// Props
interface Props {
  connection: ConnectionDefinition
  nodes: Map<string, NodeDefinition>
  scale: number
  selected: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'select': [connectionId: string]
  'delete': [connectionId: string]
  'update': [connection: ConnectionDefinition]
}>()

// 响应式数据
const isDraggingControlPoint = ref(false)
const draggingPointIndex = ref(-1)

// 计算属性
const sourceNode = computed(() => props.nodes.get(props.connection.sourceNodeId))
const targetNode = computed(() => props.nodes.get(props.connection.targetNodeId))

const strokeColor = computed(() => {
  if (props.selected) return '#1890ff'
  return props.connection.type === 'condition' ? '#faad14' : '#666666'
})

const strokeWidth = computed(() => {
  return props.selected ? 3 : 2
})

const strokeDashArray = computed(() => {
  return props.connection.type === 'condition' ? '5,5' : 'none'
})

const markerEnd = computed(() => {
  return `url(#arrow-${props.connection.id})`
})

// 计算连线的起点和终点
const startPoint = computed((): Position => {
  if (!sourceNode.value) return { x: 0, y: 0 }
  
  return {
    x: sourceNode.value.position.x + sourceNode.value.size.width,
    y: sourceNode.value.position.y + sourceNode.value.size.height / 2
  }
})

const endPoint = computed((): Position => {
  if (!targetNode.value) return { x: 0, y: 0 }
  
  return {
    x: targetNode.value.position.x,
    y: targetNode.value.position.y + targetNode.value.size.height / 2
  }
})

// 计算连线路径
const pathData = computed(() => {
  const start = startPoint.value
  const end = endPoint.value
  
  if (!start || !end) return ''
  
  // 如果有自定义控制点，使用贝塞尔曲线
  if (props.connection.points && props.connection.points.length > 0) {
    let path = `M ${start.x} ${start.y}`
    
    // 添加控制点
    for (const point of props.connection.points) {
      path += ` L ${point.x} ${point.y}`
    }
    
    path += ` L ${end.x} ${end.y}`
    return path
  }
  
  // 默认使用平滑的贝塞尔曲线
  const deltaX = end.x - start.x
  const deltaY = end.y - start.y
  
  // 控制点偏移量
  const controlOffset = Math.max(50, Math.abs(deltaX) * 0.5)
  
  const cp1x = start.x + controlOffset
  const cp1y = start.y
  const cp2x = end.x - controlOffset
  const cp2y = end.y
  
  return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
})

// 计算标签位置
const labelPosition = computed((): Position => {
  const start = startPoint.value
  const end = endPoint.value
  
  return {
    x: (start.x + end.x) / 2,
    y: (start.y + end.y) / 2 - 20
  }
})

const labelSize = computed(() => {
  const text = props.connection.name || ''
  return {
    width: Math.max(60, text.length * 8 + 16),
    height: 24
  }
})

// 计算条件标签位置
const conditionPosition = computed((): Position => {
  const start = startPoint.value
  const end = endPoint.value
  
  return {
    x: (start.x + end.x) / 2,
    y: (start.y + end.y) / 2 + 20
  }
})

const conditionSize = computed(() => {
  const text = conditionText.value
  return {
    width: Math.max(40, text.length * 6 + 12),
    height: 20
  }
})

const conditionText = computed(() => {
  const condition = props.connection.condition || ''
  return condition.length > 10 ? condition.substring(0, 10) + '...' : condition
})

// 计算删除按钮位置
const deleteButtonPosition = computed((): Position => {
  const start = startPoint.value
  const end = endPoint.value
  
  return {
    x: (start.x + end.x) / 2 + 30,
    y: (start.y + end.y) / 2 - 30
  }
})

// 计算控制点
const controlPoints = computed((): Position[] => {
  return props.connection.points || []
})

// 事件处理
const onClick = (event: MouseEvent) => {
  event.stopPropagation()
  emit('select', props.connection.id)
}

const onDelete = () => {
  emit('delete', props.connection.id)
}

const onControlPointMouseDown = (index: number, event: MouseEvent) => {
  event.stopPropagation()
  isDraggingControlPoint.value = true
  draggingPointIndex.value = index
  
  document.addEventListener('mousemove', onControlPointMouseMove)
  document.addEventListener('mouseup', onControlPointMouseUp)
}

const onControlPointMouseMove = (event: MouseEvent) => {
  if (!isDraggingControlPoint.value || draggingPointIndex.value < 0) return
  
  // 这里需要根据鼠标位置计算新的控制点位置
  // 实际实现中需要考虑SVG坐标转换
  const newPosition: Position = {
    x: event.clientX / props.scale,
    y: event.clientY / props.scale
  }
  
  const updatedConnection = { ...props.connection }
  if (!updatedConnection.points) {
    updatedConnection.points = []
  }
  
  updatedConnection.points[draggingPointIndex.value] = newPosition
  emit('update', updatedConnection)
}

const onControlPointMouseUp = () => {
  isDraggingControlPoint.value = false
  draggingPointIndex.value = -1
  
  document.removeEventListener('mousemove', onControlPointMouseMove)
  document.removeEventListener('mouseup', onControlPointMouseUp)
}
</script>

<style scoped>
.workflow-connection {
  cursor: pointer;
  user-select: none;
}

.connection-path {
  transition: all 0.2s ease;
}

.connection-path:hover {
  stroke-width: 3;
}

.connection-highlight {
  pointer-events: none;
}

.connection-label,
.condition-label {
  pointer-events: none;
}

.label-text,
.condition-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
}

.delete-button {
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.delete-button:hover {
  opacity: 1;
}

.control-points {
  opacity: 0.7;
}

.control-point {
  cursor: move;
  transition: all 0.2s ease;
}

.control-point:hover {
  r: 6;
  fill: #40a9ff;
}

/* 连线类型特定样式 */
.connection-sequence .connection-path {
  stroke-dasharray: none;
}

.connection-condition .connection-path {
  stroke-dasharray: 5,5;
}
</style>
