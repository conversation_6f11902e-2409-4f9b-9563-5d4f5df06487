<template>
  <div class="workflow-canvas-container">
    <!-- 工具栏 -->
    <div class="canvas-toolbar">
      <a-space>
        <a-button-group>
          <a-button @click="zoomIn" :disabled="scale >= 2">
            <template #icon><zoom-in-outlined /></template>
          </a-button>
          <a-button @click="resetZoom">{{ Math.round(scale * 100) }}%</a-button>
          <a-button @click="zoomOut" :disabled="scale <= 0.5">
            <template #icon><zoom-out-outlined /></template>
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <a-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'">
          <template #icon><border-outlined /></template>
          网格
        </a-button>
        
        <a-button @click="toggleSnap" :type="snapToGrid ? 'primary' : 'default'">
          <template #icon><aim-outlined /></template>
          对齐
        </a-button>
        
        <a-divider type="vertical" />
        
        <a-button @click="clearCanvas" danger>
          <template #icon><clear-outlined /></template>
          清空
        </a-button>
      </a-space>
    </div>

    <!-- 画布区域 -->
    <div 
      ref="canvasContainer" 
      class="canvas-container"
      @mousedown="onCanvasMouseDown"
      @mousemove="onCanvasMouseMove"
      @mouseup="onCanvasMouseUp"
      @wheel="onCanvasWheel"
    >
      <svg
        ref="canvasSvg"
        class="workflow-canvas"
        :width="canvasSize.width"
        :height="canvasSize.height"
        :style="canvasStyle"
      >
        <!-- 网格背景 -->
        <defs v-if="showGrid">
          <pattern
            id="grid"
            :width="gridSize * scale"
            :height="gridSize * scale"
            patternUnits="userSpaceOnUse"
          >
            <path
              :d="`M ${gridSize * scale} 0 L 0 0 0 ${gridSize * scale}`"
              fill="none"
              :stroke="theme.gridColor"
              stroke-width="1"
            />
          </pattern>
        </defs>
        
        <rect
          v-if="showGrid"
          width="100%"
          height="100%"
          fill="url(#grid)"
        />

        <!-- 连线层 -->
        <g class="connections-layer">
          <workflow-connection
            v-for="connection in connections"
            :key="connection.id"
            :connection="connection"
            :nodes="nodeMap"
            :scale="scale"
            :selected="selectedConnectionId === connection.id"
            @select="selectConnection"
            @delete="deleteConnection"
          />
          
          <!-- 临时连线 -->
          <line
            v-if="tempConnection"
            :x1="tempConnection.start.x"
            :y1="tempConnection.start.y"
            :x2="tempConnection.end.x"
            :y2="tempConnection.end.y"
            stroke="#1890ff"
            stroke-width="2"
            stroke-dasharray="5,5"
          />
        </g>

        <!-- 节点层 -->
        <g class="nodes-layer">
          <workflow-node
            v-for="node in nodes"
            :key="node.id"
            :node="node"
            :scale="scale"
            :selected="selectedNodeId === node.id"
            :connecting="isConnecting"
            @select="selectNode"
            @move="moveNode"
            @delete="deleteNode"
            @connect-start="startConnection"
            @connect-end="endConnection"
          />
        </g>
      </svg>
    </div>

    <!-- 选中元素的属性面板 -->
    <div v-if="selectedNode || selectedConnection" class="properties-panel">
      <node-properties
        v-if="selectedNode"
        :node="selectedNode"
        @update="updateNode"
      />
      <connection-properties
        v-if="selectedConnection"
        :connection="selectedConnection"
        @update="updateConnection"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted } from 'vue'
import { 
  ZoomInOutlined, 
  ZoomOutOutlined, 
  BorderOutlined, 
  AimOutlined, 
  ClearOutlined 
} from '@ant-design/icons-vue'
import type { 
  NodeDefinition, 
  ConnectionDefinition, 
  Position, 
  Size,
  DesignerState,
  DesignerConfig 
} from '@/types/workflow'
import WorkflowNode from './WorkflowNode.vue'
import WorkflowConnection from './WorkflowConnection.vue'
import NodeProperties from './NodeProperties.vue'
import ConnectionProperties from './ConnectionProperties.vue'

// Props
interface Props {
  nodes: NodeDefinition[]
  connections: ConnectionDefinition[]
  config?: Partial<DesignerConfig>
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({})
})

// Emits
const emit = defineEmits<{
  'update:nodes': [nodes: NodeDefinition[]]
  'update:connections': [connections: ConnectionDefinition[]]
  'node-select': [node: NodeDefinition | null]
  'connection-select': [connection: ConnectionDefinition | null]
}>()

// 响应式数据
const canvasContainer = ref<HTMLDivElement>()
const canvasSvg = ref<SVGElement>()

// 设计器状态
const state = reactive<DesignerState>({
  selectedNodeId: undefined,
  selectedConnectionId: undefined,
  draggedNodeType: undefined,
  isConnecting: false,
  connectingFromNodeId: undefined,
  scale: 1,
  offset: { x: 0, y: 0 },
  showGrid: true,
  snapToGrid: true,
  gridSize: 20
})

// 默认配置
const defaultConfig: DesignerConfig = {
  canvasSize: { width: 2000, height: 1500 },
  nodeDefaults: {
    start: { size: { width: 80, height: 80 } },
    end: { size: { width: 80, height: 80 } },
    task: { size: { width: 120, height: 80 } },
    decision: { size: { width: 100, height: 100 } },
    parallel: { size: { width: 120, height: 80 } },
    merge: { size: { width: 120, height: 80 } },
    subprocess: { size: { width: 140, height: 100 } }
  },
  connectionDefaults: {
    properties: {}
  },
  theme: {
    primaryColor: '#1890ff',
    backgroundColor: '#ffffff',
    gridColor: '#e8e8e8',
    nodeColors: {
      start: '#52c41a',
      end: '#ff4d4f',
      task: '#1890ff',
      decision: '#faad14',
      parallel: '#722ed1',
      merge: '#13c2c2',
      subprocess: '#eb2f96'
    }
  }
}

// 计算属性
const config = computed(() => ({ ...defaultConfig, ...props.config }))
const canvasSize = computed(() => config.value.canvasSize)
const theme = computed(() => config.value.theme)
const scale = computed(() => state.scale)
const showGrid = computed(() => state.showGrid)
const snapToGrid = computed(() => state.snapToGrid)
const gridSize = computed(() => state.gridSize)
const isConnecting = computed(() => state.isConnecting)

const canvasStyle = computed(() => ({
  transform: `scale(${state.scale}) translate(${state.offset.x}px, ${state.offset.y}px)`,
  transformOrigin: '0 0'
}))

const nodeMap = computed(() => {
  const map = new Map<string, NodeDefinition>()
  props.nodes.forEach(node => map.set(node.id, node))
  return map
})

const selectedNode = computed(() => 
  state.selectedNodeId ? nodeMap.value.get(state.selectedNodeId) : undefined
)

const selectedConnection = computed(() => 
  state.selectedConnectionId ? 
    props.connections.find(c => c.id === state.selectedConnectionId) : 
    undefined
)

const selectedNodeId = computed(() => state.selectedNodeId)
const selectedConnectionId = computed(() => state.selectedConnectionId)

// 临时连线
const tempConnection = ref<{ start: Position; end: Position } | null>(null)

// 方法
const zoomIn = () => {
  if (state.scale < 2) {
    state.scale = Math.min(2, state.scale + 0.1)
  }
}

const zoomOut = () => {
  if (state.scale > 0.5) {
    state.scale = Math.max(0.5, state.scale - 0.1)
  }
}

const resetZoom = () => {
  state.scale = 1
  state.offset = { x: 0, y: 0 }
}

const toggleGrid = () => {
  state.showGrid = !state.showGrid
}

const toggleSnap = () => {
  state.snapToGrid = !state.snapToGrid
}

const clearCanvas = () => {
  emit('update:nodes', [])
  emit('update:connections', [])
  state.selectedNodeId = undefined
  state.selectedConnectionId = undefined
}

const selectNode = (nodeId: string) => {
  state.selectedNodeId = nodeId
  state.selectedConnectionId = undefined
  const node = nodeMap.value.get(nodeId)
  emit('node-select', node || null)
}

const selectConnection = (connectionId: string) => {
  state.selectedConnectionId = connectionId
  state.selectedNodeId = undefined
  const connection = props.connections.find(c => c.id === connectionId)
  emit('connection-select', connection || null)
}

const moveNode = (nodeId: string, position: Position) => {
  const nodes = [...props.nodes]
  const nodeIndex = nodes.findIndex(n => n.id === nodeId)
  if (nodeIndex >= 0) {
    nodes[nodeIndex] = { ...nodes[nodeIndex], position }
    emit('update:nodes', nodes)
  }
}

const updateNode = (updatedNode: NodeDefinition) => {
  const nodes = [...props.nodes]
  const nodeIndex = nodes.findIndex(n => n.id === updatedNode.id)
  if (nodeIndex >= 0) {
    nodes[nodeIndex] = updatedNode
    emit('update:nodes', nodes)
  }
}

const deleteNode = (nodeId: string) => {
  const nodes = props.nodes.filter(n => n.id !== nodeId)
  const connections = props.connections.filter(
    c => c.sourceNodeId !== nodeId && c.targetNodeId !== nodeId
  )
  emit('update:nodes', nodes)
  emit('update:connections', connections)
  
  if (state.selectedNodeId === nodeId) {
    state.selectedNodeId = undefined
  }
}

const updateConnection = (updatedConnection: ConnectionDefinition) => {
  const connections = [...props.connections]
  const connectionIndex = connections.findIndex(c => c.id === updatedConnection.id)
  if (connectionIndex >= 0) {
    connections[connectionIndex] = updatedConnection
    emit('update:connections', connections)
  }
}

const deleteConnection = (connectionId: string) => {
  const connections = props.connections.filter(c => c.id !== connectionId)
  emit('update:connections', connections)
  
  if (state.selectedConnectionId === connectionId) {
    state.selectedConnectionId = undefined
  }
}

const startConnection = (nodeId: string, position: Position) => {
  state.isConnecting = true
  state.connectingFromNodeId = nodeId
  tempConnection.value = { start: position, end: position }
}

const endConnection = (nodeId: string) => {
  if (state.isConnecting && state.connectingFromNodeId && state.connectingFromNodeId !== nodeId) {
    const newConnection: ConnectionDefinition = {
      id: `conn_${Date.now()}`,
      type: 'sequence',
      sourceNodeId: state.connectingFromNodeId,
      targetNodeId: nodeId,
      properties: {}
    }
    
    const connections = [...props.connections, newConnection]
    emit('update:connections', connections)
  }
  
  state.isConnecting = false
  state.connectingFromNodeId = undefined
  tempConnection.value = null
}

// 画布事件处理
const onCanvasMouseDown = (event: MouseEvent) => {
  if (event.target === canvasSvg.value) {
    state.selectedNodeId = undefined
    state.selectedConnectionId = undefined
    emit('node-select', null)
    emit('connection-select', null)
  }
}

const onCanvasMouseMove = (event: MouseEvent) => {
  if (tempConnection.value) {
    const rect = canvasSvg.value?.getBoundingClientRect()
    if (rect) {
      tempConnection.value.end = {
        x: (event.clientX - rect.left) / state.scale,
        y: (event.clientY - rect.top) / state.scale
      }
    }
  }
}

const onCanvasMouseUp = () => {
  if (state.isConnecting) {
    state.isConnecting = false
    state.connectingFromNodeId = undefined
    tempConnection.value = null
  }
}

const onCanvasWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? -0.1 : 0.1
  const newScale = Math.max(0.5, Math.min(2, state.scale + delta))
  state.scale = newScale
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', onKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', onKeyDown)
})

const onKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Delete' || event.key === 'Backspace') {
    if (state.selectedNodeId) {
      deleteNode(state.selectedNodeId)
    } else if (state.selectedConnectionId) {
      deleteConnection(state.selectedConnectionId)
    }
  }
}
</script>

<style scoped>
.workflow-canvas-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.canvas-toolbar {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.canvas-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  background: white;
}

.workflow-canvas {
  cursor: grab;
  user-select: none;
}

.workflow-canvas:active {
  cursor: grabbing;
}

.properties-panel {
  position: absolute;
  top: 80px;
  right: 16px;
  width: 300px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.nodes-layer,
.connections-layer {
  pointer-events: all;
}
</style>
