<template>
  <div class="connection-properties">
    <div class="properties-header">
      <h4>连线属性</h4>
      <a-button type="text" size="small" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="properties-content">
      <a-form :model="formData" layout="vertical" size="small">
        <!-- 基本信息 -->
        <a-form-item label="连线名称">
          <a-input 
            v-model:value="formData.name" 
            placeholder="请输入连线名称"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="连线类型">
          <a-select 
            v-model:value="formData.type" 
            style="width: 100%"
            @change="onFormChange"
          >
            <a-select-option value="sequence">顺序流转</a-select-option>
            <a-select-option value="condition">条件流转</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 条件流转配置 -->
        <template v-if="formData.type === 'condition'">
          <a-divider orientation="left">条件配置</a-divider>
          
          <a-form-item label="条件表达式" required>
            <a-textarea 
              v-model:value="formData.condition" 
              placeholder="请输入条件表达式，如：${amount > 1000}"
              :rows="3"
              @change="onFormChange"
            />
            <div class="condition-help">
              <a-typography-text type="secondary" style="font-size: 12px;">
                支持变量引用：${variableName}，运算符：>, <, >=, <=, ==, !=, &&, ||
              </a-typography-text>
            </div>
          </a-form-item>
          
          <a-form-item label="条件描述">
            <a-input 
              v-model:value="formData.conditionDescription" 
              placeholder="请输入条件描述"
              @change="onFormChange"
            />
          </a-form-item>
          
          <a-form-item label="优先级">
            <a-input-number 
              v-model:value="formData.priority" 
              :min="1"
              :max="100"
              style="width: 100%"
              placeholder="数值越大优先级越高"
              @change="onFormChange"
            />
          </a-form-item>
        </template>

        <!-- 流转参数配置 -->
        <a-divider orientation="left">流转参数</a-divider>
        
        <a-form-item label="参数传递">
          <a-switch 
            v-model:checked="formData.passVariables" 
            checked-children="传递"
            un-checked-children="不传递"
            @change="onFormChange"
          />
        </a-form-item>
        
        <template v-if="formData.passVariables">
          <a-form-item label="传递模式">
            <a-radio-group 
              v-model:value="formData.passMode" 
              @change="onFormChange"
            >
              <a-radio value="all">全部变量</a-radio>
              <a-radio value="selected">指定变量</a-radio>
              <a-radio value="mapped">变量映射</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            v-if="formData.passMode === 'selected'" 
            label="指定变量"
          >
            <a-select
              v-model:value="formData.selectedVariables"
              mode="tags"
              placeholder="请输入变量名"
              style="width: 100%"
              @change="onFormChange"
            >
            </a-select>
          </a-form-item>
          
          <a-form-item 
            v-if="formData.passMode === 'mapped'" 
            label="变量映射"
          >
            <div class="variable-mapping">
              <div 
                v-for="(mapping, index) in variableMappings" 
                :key="index" 
                class="mapping-item"
              >
                <a-row :gutter="8" align="middle">
                  <a-col :span="8">
                    <a-input 
                      v-model:value="mapping.source" 
                      placeholder="源变量"
                      size="small"
                      @change="onMappingChange"
                    />
                  </a-col>
                  <a-col :span="2" style="text-align: center;">
                    →
                  </a-col>
                  <a-col :span="10">
                    <a-input 
                      v-model:value="mapping.target" 
                      placeholder="目标变量"
                      size="small"
                      @change="onMappingChange"
                    />
                  </a-col>
                  <a-col :span="4">
                    <a-button 
                      type="text" 
                      size="small" 
                      danger
                      @click="removeMapping(index)"
                    >
                      <template #icon><delete-outlined /></template>
                    </a-button>
                  </a-col>
                </a-row>
              </div>
              
              <a-button 
                type="dashed" 
                size="small" 
                block
                @click="addMapping"
              >
                <template #icon><plus-outlined /></template>
                添加映射
              </a-button>
            </div>
          </a-form-item>
        </template>

        <!-- 执行配置 -->
        <a-divider orientation="left">执行配置</a-divider>
        
        <a-form-item label="执行监听器">
          <a-switch 
            v-model:checked="formData.hasListener" 
            checked-children="启用"
            un-checked-children="禁用"
            @change="onFormChange"
          />
        </a-form-item>
        
        <template v-if="formData.hasListener">
          <a-form-item label="监听器类型">
            <a-select 
              v-model:value="formData.listenerType" 
              style="width: 100%"
              @change="onFormChange"
            >
              <a-select-option value="script">脚本执行</a-select-option>
              <a-select-option value="service">服务调用</a-select-option>
              <a-select-option value="notification">消息通知</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="执行内容">
            <a-textarea 
              v-model:value="formData.listenerContent" 
              placeholder="请输入执行内容"
              :rows="4"
              @change="onFormChange"
            />
          </a-form-item>
        </template>

        <!-- 异常处理 -->
        <a-divider orientation="left">异常处理</a-divider>
        
        <a-form-item label="异常策略">
          <a-select 
            v-model:value="formData.errorStrategy" 
            style="width: 100%"
            @change="onFormChange"
          >
            <a-select-option value="ignore">忽略错误</a-select-option>
            <a-select-option value="retry">重试执行</a-select-option>
            <a-select-option value="rollback">回滚流程</a-select-option>
            <a-select-option value="terminate">终止流程</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item 
          v-if="formData.errorStrategy === 'retry'" 
          label="重试次数"
        >
          <a-input-number 
            v-model:value="formData.retryCount" 
            :min="1"
            :max="10"
            style="width: 100%"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item 
          v-if="formData.errorStrategy === 'retry'" 
          label="重试间隔(秒)"
        >
          <a-input-number 
            v-model:value="formData.retryInterval" 
            :min="1"
            :max="3600"
            style="width: 100%"
            @change="onFormChange"
          />
        </a-form-item>

        <!-- 自定义属性 -->
        <a-divider orientation="left">自定义属性</a-divider>
        
        <div class="custom-properties">
          <div 
            v-for="(value, key) in customProperties" 
            :key="key" 
            class="property-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="8">
                <a-input 
                  :value="key" 
                  placeholder="属性名"
                  size="small"
                  @change="(e) => onCustomPropertyKeyChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="12">
                <a-input 
                  :value="value" 
                  placeholder="属性值"
                  size="small"
                  @change="(e) => onCustomPropertyValueChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  @click="removeCustomProperty(key)"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button 
            type="dashed" 
            size="small" 
            block
            @click="addCustomProperty"
          >
            <template #icon><plus-outlined /></template>
            添加属性
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  CloseOutlined, 
  DeleteOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue'
import type { ConnectionDefinition } from '@/types/workflow'

// Props
interface Props {
  connection: ConnectionDefinition
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [connection: ConnectionDefinition]
  'close': []
}>()

// 变量映射接口
interface VariableMapping {
  source: string
  target: string
}

// 表单数据
const formData = ref<ConnectionDefinition & {
  conditionDescription?: string
  priority?: number
  passVariables?: boolean
  passMode?: 'all' | 'selected' | 'mapped'
  selectedVariables?: string[]
  hasListener?: boolean
  listenerType?: 'script' | 'service' | 'notification'
  listenerContent?: string
  errorStrategy?: 'ignore' | 'retry' | 'rollback' | 'terminate'
  retryCount?: number
  retryInterval?: number
}>({
  ...props.connection,
  passVariables: false,
  passMode: 'all',
  selectedVariables: [],
  hasListener: false,
  errorStrategy: 'ignore',
  retryCount: 3,
  retryInterval: 5
})

// 变量映射
const variableMappings = ref<VariableMapping[]>([])

// 自定义属性
const customProperties = computed(() => {
  const { properties } = formData.value
  const custom: Record<string, any> = {}
  
  // 过滤掉系统属性
  const systemKeys = [
    'conditionDescription', 'priority', 'passVariables', 'passMode', 
    'selectedVariables', 'variableMappings', 'hasListener', 'listenerType', 
    'listenerContent', 'errorStrategy', 'retryCount', 'retryInterval'
  ]
  
  Object.keys(properties).forEach(key => {
    if (!systemKeys.includes(key)) {
      custom[key] = properties[key]
    }
  })
  
  return custom
})

// 监听连线变化
watch(() => props.connection, (newConnection) => {
  formData.value = { ...newConnection }
  
  // 初始化变量映射
  const mappings = newConnection.properties.variableMappings as VariableMapping[] || []
  variableMappings.value = mappings.length > 0 ? mappings : []
}, { deep: true })

// 表单变化处理
const onFormChange = () => {
  const updatedConnection: ConnectionDefinition = {
    ...formData.value,
    properties: {
      ...formData.value.properties,
      conditionDescription: formData.value.conditionDescription,
      priority: formData.value.priority,
      passVariables: formData.value.passVariables,
      passMode: formData.value.passMode,
      selectedVariables: formData.value.selectedVariables,
      variableMappings: variableMappings.value,
      hasListener: formData.value.hasListener,
      listenerType: formData.value.listenerType,
      listenerContent: formData.value.listenerContent,
      errorStrategy: formData.value.errorStrategy,
      retryCount: formData.value.retryCount,
      retryInterval: formData.value.retryInterval
    }
  }
  
  emit('update', updatedConnection)
}

// 变量映射管理
const addMapping = () => {
  variableMappings.value.push({ source: '', target: '' })
  onMappingChange()
}

const removeMapping = (index: number) => {
  variableMappings.value.splice(index, 1)
  onMappingChange()
}

const onMappingChange = () => {
  onFormChange()
}

// 自定义属性管理
const addCustomProperty = () => {
  const key = `property_${Date.now()}`
  formData.value.properties[key] = ''
  onFormChange()
}

const removeCustomProperty = (key: string) => {
  delete formData.value.properties[key]
  onFormChange()
}

const onCustomPropertyKeyChange = (oldKey: string, newKey: string) => {
  if (newKey && newKey !== oldKey) {
    const value = formData.value.properties[oldKey]
    delete formData.value.properties[oldKey]
    formData.value.properties[newKey] = value
    onFormChange()
  }
}

const onCustomPropertyValueChange = (key: string, value: string) => {
  formData.value.properties[key] = value
  onFormChange()
}
</script>

<style scoped>
.connection-properties {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.condition-help {
  margin-top: 4px;
}

.variable-mapping {
  margin-top: 8px;
}

.mapping-item {
  margin-bottom: 8px;
}

.custom-properties {
  margin-top: 8px;
}

.property-item {
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0 12px 0;
}

:deep(.ant-divider-inner-text) {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}
</style>
