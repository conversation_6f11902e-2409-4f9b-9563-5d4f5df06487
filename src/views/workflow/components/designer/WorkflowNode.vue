<template>
  <g 
    class="workflow-node"
    :class="{ 
      'selected': selected, 
      'connecting': connecting,
      [`node-${node.type}`]: true 
    }"
    @mousedown="onMouseDown"
    @click="onClick"
  >
    <!-- 节点主体 -->
    <rect
      :x="node.position.x"
      :y="node.position.y"
      :width="node.size.width"
      :height="node.size.height"
      :rx="borderRadius"
      :ry="borderRadius"
      :fill="nodeColor"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      class="node-body"
    />
    
    <!-- 特殊形状处理 -->
    <polygon
      v-if="node.type === 'decision'"
      :points="diamondPoints"
      :fill="nodeColor"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      class="node-body"
    />
    
    <circle
      v-if="node.type === 'start' || node.type === 'end'"
      :cx="node.position.x + node.size.width / 2"
      :cy="node.position.y + node.size.height / 2"
      :r="node.size.width / 2"
      :fill="nodeColor"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      class="node-body"
    />

    <!-- 节点图标 -->
    <text
      :x="node.position.x + node.size.width / 2"
      :y="node.position.y + node.size.height / 2 - 8"
      text-anchor="middle"
      dominant-baseline="middle"
      :font-size="iconSize"
      :fill="iconColor"
      class="node-icon"
    >
      {{ nodeIcon }}
    </text>

    <!-- 节点标题 -->
    <text
      :x="node.position.x + node.size.width / 2"
      :y="node.position.y + node.size.height / 2 + 12"
      text-anchor="middle"
      dominant-baseline="middle"
      font-size="12"
      :fill="textColor"
      class="node-title"
    >
      {{ displayName }}
    </text>

    <!-- 连接点 -->
    <g class="connection-points" v-if="!connecting">
      <!-- 输入连接点 -->
      <circle
        v-if="node.type !== 'start'"
        :cx="node.position.x"
        :cy="node.position.y + node.size.height / 2"
        r="4"
        fill="#1890ff"
        stroke="white"
        stroke-width="2"
        class="connection-point input-point"
        @mousedown.stop="onConnectionStart"
      />
      
      <!-- 输出连接点 -->
      <circle
        v-if="node.type !== 'end'"
        :cx="node.position.x + node.size.width"
        :cy="node.position.y + node.size.height / 2"
        r="4"
        fill="#1890ff"
        stroke="white"
        stroke-width="2"
        class="connection-point output-point"
        @mousedown.stop="onConnectionStart"
        @mouseup.stop="onConnectionEnd"
      />
    </g>

    <!-- 选中状态的控制点 -->
    <g v-if="selected" class="selection-handles">
      <rect
        v-for="(handle, index) in selectionHandles"
        :key="index"
        :x="handle.x - 3"
        :y="handle.y - 3"
        width="6"
        height="6"
        fill="#1890ff"
        stroke="white"
        stroke-width="1"
        class="selection-handle"
      />
    </g>

    <!-- 删除按钮 -->
    <g v-if="selected" class="delete-button" @click.stop="onDelete">
      <circle
        :cx="node.position.x + node.size.width + 8"
        :cy="node.position.y - 8"
        r="8"
        fill="#ff4d4f"
        stroke="white"
        stroke-width="2"
      />
      <text
        :x="node.position.x + node.size.width + 8"
        :y="node.position.y - 8"
        text-anchor="middle"
        dominant-baseline="middle"
        font-size="10"
        fill="white"
      >
        ×
      </text>
    </g>
  </g>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { NodeDefinition, Position, NodeType } from '@/types/workflow'

// Props
interface Props {
  node: NodeDefinition
  scale: number
  selected: boolean
  connecting: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'select': [nodeId: string]
  'move': [nodeId: string, position: Position]
  'delete': [nodeId: string]
  'connect-start': [nodeId: string, position: Position]
  'connect-end': [nodeId: string]
}>()

// 拖拽状态
const isDragging = ref(false)
const dragStart = ref<Position>({ x: 0, y: 0 })
const nodeStart = ref<Position>({ x: 0, y: 0 })

// 计算属性
const nodeColor = computed(() => {
  const colors: Record<NodeType, string> = {
    start: '#52c41a',
    end: '#ff4d4f',
    task: '#1890ff',
    decision: '#faad14',
    parallel: '#722ed1',
    merge: '#13c2c2',
    subprocess: '#eb2f96'
  }
  return colors[props.node.type] || '#1890ff'
})

const strokeColor = computed(() => {
  return props.selected ? '#1890ff' : '#d9d9d9'
})

const strokeWidth = computed(() => {
  return props.selected ? 2 : 1
})

const borderRadius = computed(() => {
  return props.node.type === 'task' ? 8 : 4
})

const iconColor = computed(() => {
  return '#ffffff'
})

const textColor = computed(() => {
  return '#333333'
})

const iconSize = computed(() => {
  return Math.min(props.node.size.width, props.node.size.height) * 0.3
})

const nodeIcon = computed(() => {
  const icons: Record<NodeType, string> = {
    start: '▶',
    end: '⏹',
    task: '📋',
    decision: '❓',
    parallel: '⚡',
    merge: '🔗',
    subprocess: '📁'
  }
  return icons[props.node.type] || '📋'
})

const displayName = computed(() => {
  const maxLength = Math.floor(props.node.size.width / 8)
  return props.node.name.length > maxLength 
    ? props.node.name.substring(0, maxLength) + '...'
    : props.node.name
})

// 菱形节点的点坐标
const diamondPoints = computed(() => {
  const { x, y } = props.node.position
  const { width, height } = props.node.size
  const centerX = x + width / 2
  const centerY = y + height / 2
  
  return [
    `${centerX},${y}`,           // 上
    `${x + width},${centerY}`,   // 右
    `${centerX},${y + height}`,  // 下
    `${x},${centerY}`            // 左
  ].join(' ')
})

// 选中状态的控制点
const selectionHandles = computed(() => {
  const { x, y } = props.node.position
  const { width, height } = props.node.size
  
  return [
    { x, y },                           // 左上
    { x: x + width / 2, y },           // 上中
    { x: x + width, y },               // 右上
    { x: x + width, y: y + height / 2 }, // 右中
    { x: x + width, y: y + height },   // 右下
    { x: x + width / 2, y: y + height }, // 下中
    { x, y: y + height },              // 左下
    { x, y: y + height / 2 }           // 左中
  ]
})

// 事件处理
const onClick = (event: MouseEvent) => {
  event.stopPropagation()
  emit('select', props.node.id)
}

const onMouseDown = (event: MouseEvent) => {
  event.stopPropagation()
  
  if (event.button === 0) { // 左键
    isDragging.value = true
    dragStart.value = { x: event.clientX, y: event.clientY }
    nodeStart.value = { ...props.node.position }
    
    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
  }
}

const onMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return
  
  const deltaX = (event.clientX - dragStart.value.x) / props.scale
  const deltaY = (event.clientY - dragStart.value.y) / props.scale
  
  const newPosition: Position = {
    x: nodeStart.value.x + deltaX,
    y: nodeStart.value.y + deltaY
  }
  
  emit('move', props.node.id, newPosition)
}

const onMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
}

const onDelete = () => {
  emit('delete', props.node.id)
}

const onConnectionStart = (event: MouseEvent) => {
  event.stopPropagation()
  const position: Position = {
    x: props.node.position.x + props.node.size.width,
    y: props.node.position.y + props.node.size.height / 2
  }
  emit('connect-start', props.node.id, position)
}

const onConnectionEnd = () => {
  emit('connect-end', props.node.id)
}
</script>

<style scoped>
.workflow-node {
  cursor: pointer;
  user-select: none;
}

.workflow-node.selected .node-body {
  filter: drop-shadow(0 0 8px rgba(24, 144, 255, 0.5));
}

.workflow-node.connecting {
  cursor: crosshair;
}

.node-body {
  transition: all 0.2s ease;
}

.node-body:hover {
  filter: brightness(1.1);
}

.node-icon {
  pointer-events: none;
  font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;
}

.node-title {
  pointer-events: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
}

.connection-points {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.workflow-node:hover .connection-points {
  opacity: 1;
}

.connection-point {
  cursor: crosshair;
  transition: all 0.2s ease;
}

.connection-point:hover {
  r: 6;
  fill: #40a9ff;
}

.selection-handles {
  pointer-events: none;
}

.selection-handle {
  cursor: nw-resize;
}

.delete-button {
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.delete-button:hover {
  opacity: 1;
}

/* 节点类型特定样式 */
.node-start .node-body {
  stroke-dasharray: none;
}

.node-end .node-body {
  stroke-width: 3;
}

.node-decision .node-body {
  stroke-dasharray: 5,5;
}

.node-parallel .node-body,
.node-merge .node-body {
  stroke-width: 2;
}

.node-subprocess .node-body {
  stroke-dasharray: 10,5;
}
</style>
