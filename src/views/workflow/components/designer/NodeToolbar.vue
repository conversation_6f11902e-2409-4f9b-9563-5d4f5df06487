<template>
  <div class="node-toolbar">
    <div class="toolbar-header">
      <h4>节点工具箱</h4>
      <a-button type="text" size="small" @click="toggleCollapse">
        <template #icon>
          <up-outlined v-if="!collapsed" />
          <down-outlined v-if="collapsed" />
        </template>
      </a-button>
    </div>
    
    <div v-show="!collapsed" class="toolbar-content">
      <!-- 基础节点 -->
      <div class="node-category">
        <div class="category-title">基础节点</div>
        <div class="node-list">
          <div
            v-for="node in basicNodes"
            :key="node.type"
            class="node-item"
            :class="{ 'dragging': draggedNodeType === node.type }"
            draggable="true"
            @dragstart="onDragStart(node.type, $event)"
            @dragend="onDragEnd"
            @click="addNode(node.type)"
          >
            <div class="node-icon" :style="{ backgroundColor: node.color }">
              {{ node.icon }}
            </div>
            <div class="node-label">{{ node.label }}</div>
          </div>
        </div>
      </div>

      <!-- 流程控制节点 -->
      <div class="node-category">
        <div class="category-title">流程控制</div>
        <div class="node-list">
          <div
            v-for="node in controlNodes"
            :key="node.type"
            class="node-item"
            :class="{ 'dragging': draggedNodeType === node.type }"
            draggable="true"
            @dragstart="onDragStart(node.type, $event)"
            @dragend="onDragEnd"
            @click="addNode(node.type)"
          >
            <div class="node-icon" :style="{ backgroundColor: node.color }">
              {{ node.icon }}
            </div>
            <div class="node-label">{{ node.label }}</div>
          </div>
        </div>
      </div>

      <!-- 高级节点 -->
      <div class="node-category">
        <div class="category-title">高级节点</div>
        <div class="node-list">
          <div
            v-for="node in advancedNodes"
            :key="node.type"
            class="node-item"
            :class="{ 'dragging': draggedNodeType === node.type }"
            draggable="true"
            @dragstart="onDragStart(node.type, $event)"
            @dragend="onDragEnd"
            @click="addNode(node.type)"
          >
            <div class="node-icon" :style="{ backgroundColor: node.color }">
              {{ node.icon }}
            </div>
            <div class="node-label">{{ node.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue'
import type { NodeType } from '@/types/workflow'

// Emits
const emit = defineEmits<{
  'add-node': [nodeType: NodeType, position?: { x: number; y: number }]
  'drag-start': [nodeType: NodeType]
  'drag-end': []
}>()

// 响应式数据
const collapsed = ref(false)
const draggedNodeType = ref<NodeType | null>(null)

// 节点定义
interface NodeItem {
  type: NodeType
  label: string
  icon: string
  color: string
  description: string
}

// 基础节点
const basicNodes: NodeItem[] = [
  {
    type: 'start',
    label: '开始',
    icon: '▶',
    color: '#52c41a',
    description: '流程开始节点'
  },
  {
    type: 'end',
    label: '结束',
    icon: '⏹',
    color: '#ff4d4f',
    description: '流程结束节点'
  },
  {
    type: 'task',
    label: '任务',
    icon: '📋',
    color: '#1890ff',
    description: '用户任务节点'
  }
]

// 流程控制节点
const controlNodes: NodeItem[] = [
  {
    type: 'decision',
    label: '决策',
    icon: '❓',
    color: '#faad14',
    description: '条件判断节点'
  },
  {
    type: 'parallel',
    label: '并行',
    icon: '⚡',
    color: '#722ed1',
    description: '并行分支节点'
  },
  {
    type: 'merge',
    label: '合并',
    icon: '🔗',
    color: '#13c2c2',
    description: '分支合并节点'
  }
]

// 高级节点
const advancedNodes: NodeItem[] = [
  {
    type: 'subprocess',
    label: '子流程',
    icon: '📁',
    color: '#eb2f96',
    description: '子流程调用节点'
  }
]

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

const onDragStart = (nodeType: NodeType, event: DragEvent) => {
  draggedNodeType.value = nodeType
  
  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'workflow-node',
      nodeType
    }))
    event.dataTransfer.effectAllowed = 'copy'
  }
  
  emit('drag-start', nodeType)
}

const onDragEnd = () => {
  draggedNodeType.value = null
  emit('drag-end')
}

const addNode = (nodeType: NodeType) => {
  // 在画布中心添加节点
  const centerPosition = { x: 400, y: 300 }
  emit('add-node', nodeType, centerPosition)
}
</script>

<style scoped>
.node-toolbar {
  width: 240px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toolbar-content {
  max-height: 600px;
  overflow-y: auto;
}

.node-category {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.node-category:last-child {
  border-bottom: none;
}

.category-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.node-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
  background: white;
  user-select: none;
}

.node-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.node-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.node-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.node-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  margin-bottom: 6px;
  font-family: 'Segoe UI Emoji', 'Apple Color Emoji', sans-serif;
}

.node-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* 滚动条样式 */
.toolbar-content::-webkit-scrollbar {
  width: 4px;
}

.toolbar-content::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.toolbar-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.toolbar-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .node-toolbar {
    width: 200px;
  }
  
  .node-list {
    grid-template-columns: 1fr;
  }
  
  .node-item {
    flex-direction: row;
    justify-content: flex-start;
    padding: 8px 12px;
  }
  
  .node-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .node-label {
    font-size: 11px;
  }
}

/* 拖拽提示动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.node-item:hover .node-icon {
  animation: pulse 2s infinite;
}
</style>
