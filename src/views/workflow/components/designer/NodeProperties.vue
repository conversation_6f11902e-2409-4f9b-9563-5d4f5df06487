<template>
  <div class="node-properties">
    <div class="properties-header">
      <h4>节点属性</h4>
      <a-button type="text" size="small" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="properties-content">
      <a-form :model="formData" layout="vertical" size="small">
        <!-- 基本信息 -->
        <a-form-item label="节点名称" required>
          <a-input 
            v-model:value="formData.name" 
            placeholder="请输入节点名称"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="节点描述">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入节点描述"
            :rows="2"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="节点类型">
          <a-select 
            v-model:value="formData.type" 
            :disabled="true"
            style="width: 100%"
          >
            <a-select-option value="start">开始节点</a-select-option>
            <a-select-option value="end">结束节点</a-select-option>
            <a-select-option value="task">任务节点</a-select-option>
            <a-select-option value="decision">决策节点</a-select-option>
            <a-select-option value="parallel">并行节点</a-select-option>
            <a-select-option value="merge">合并节点</a-select-option>
            <a-select-option value="subprocess">子流程节点</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 任务节点特有属性 -->
        <template v-if="formData.type === 'task'">
          <a-divider orientation="left">任务配置</a-divider>
          
          <a-form-item label="处理人">
            <a-input 
              v-model:value="formData.assignee" 
              placeholder="请输入处理人"
              @change="onFormChange"
            />
          </a-form-item>
          
          <a-form-item label="候选用户">
            <a-select
              v-model:value="formData.candidateUsers"
              mode="tags"
              placeholder="请输入候选用户"
              style="width: 100%"
              @change="onFormChange"
            >
            </a-select>
          </a-form-item>
          
          <a-form-item label="候选组">
            <a-select
              v-model:value="formData.candidateGroups"
              mode="tags"
              placeholder="请输入候选组"
              style="width: 100%"
              @change="onFormChange"
            >
            </a-select>
          </a-form-item>
          
          <a-form-item label="优先级">
            <a-select 
              v-model:value="formData.priority" 
              placeholder="请选择优先级"
              @change="onFormChange"
            >
              <a-select-option :value="1">低</a-select-option>
              <a-select-option :value="2">中</a-select-option>
              <a-select-option :value="3">高</a-select-option>
              <a-select-option :value="4">紧急</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="截止时间">
            <a-date-picker 
              v-model:value="formData.dueDate" 
              style="width: 100%"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              @change="onFormChange"
            />
          </a-form-item>
          
          <a-form-item label="关联表单">
            <a-select 
              v-model:value="formData.formId" 
              placeholder="请选择关联表单"
              allow-clear
              @change="onFormChange"
            >
              <a-select-option 
                v-for="form in availableForms" 
                :key="form.id" 
                :value="form.id"
              >
                {{ form.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 决策节点特有属性 -->
        <template v-if="formData.type === 'decision'">
          <a-divider orientation="left">决策配置</a-divider>
          
          <a-form-item label="决策条件">
            <a-textarea 
              v-model:value="formData.decisionCondition" 
              placeholder="请输入决策条件表达式"
              :rows="3"
              @change="onFormChange"
            />
          </a-form-item>
          
          <a-form-item label="默认分支">
            <a-input 
              v-model:value="formData.defaultBranch" 
              placeholder="请输入默认分支名称"
              @change="onFormChange"
            />
          </a-form-item>
        </template>

        <!-- 子流程节点特有属性 -->
        <template v-if="formData.type === 'subprocess'">
          <a-divider orientation="left">子流程配置</a-divider>
          
          <a-form-item label="子流程定义">
            <a-select 
              v-model:value="formData.subprocessId" 
              placeholder="请选择子流程定义"
              @change="onFormChange"
            >
              <a-select-option 
                v-for="process in availableProcesses" 
                :key="process.id" 
                :value="process.id"
              >
                {{ process.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="数据传递">
            <a-switch 
              v-model:checked="formData.inheritVariables" 
              checked-children="继承"
              un-checked-children="独立"
              @change="onFormChange"
            />
          </a-form-item>
        </template>

        <!-- 位置和尺寸 -->
        <a-divider orientation="left">位置尺寸</a-divider>
        
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-item label="X坐标">
              <a-input-number 
                v-model:value="formData.position.x" 
                style="width: 100%"
                @change="onFormChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="Y坐标">
              <a-input-number 
                v-model:value="formData.position.y" 
                style="width: 100%"
                @change="onFormChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="8">
          <a-col :span="12">
            <a-form-item label="宽度">
              <a-input-number 
                v-model:value="formData.size.width" 
                :min="60"
                :max="300"
                style="width: 100%"
                @change="onFormChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="高度">
              <a-input-number 
                v-model:value="formData.size.height" 
                :min="40"
                :max="200"
                style="width: 100%"
                @change="onFormChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 自定义属性 -->
        <a-divider orientation="left">自定义属性</a-divider>
        
        <div class="custom-properties">
          <div 
            v-for="(value, key) in customProperties" 
            :key="key" 
            class="property-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="8">
                <a-input 
                  :value="key" 
                  placeholder="属性名"
                  size="small"
                  @change="(e) => onCustomPropertyKeyChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="12">
                <a-input 
                  :value="value" 
                  placeholder="属性值"
                  size="small"
                  @change="(e) => onCustomPropertyValueChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  @click="removeCustomProperty(key)"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button 
            type="dashed" 
            size="small" 
            block
            @click="addCustomProperty"
          >
            <template #icon><plus-outlined /></template>
            添加属性
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  CloseOutlined, 
  DeleteOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue'
import type { NodeDefinition } from '@/types/workflow'
import dayjs, { type Dayjs } from 'dayjs'

// Props
interface Props {
  node: NodeDefinition
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [node: NodeDefinition]
  'close': []
}>()

// 表单数据
const formData = ref<NodeDefinition & {
  decisionCondition?: string
  defaultBranch?: string
  subprocessId?: string
  inheritVariables?: boolean
  dueDate?: Dayjs
}>({
  ...props.node,
  dueDate: props.node.dueDate ? dayjs(props.node.dueDate) : undefined
})

// 可用的表单列表（模拟数据）
const availableForms = ref([
  { id: 'form1', name: '基础信息表单' },
  { id: 'form2', name: '审批表单' },
  { id: 'form3', name: '评估表单' }
])

// 可用的流程列表（模拟数据）
const availableProcesses = ref([
  { id: 'process1', name: '审批子流程' },
  { id: 'process2', name: '评估子流程' },
  { id: 'process3', name: '通知子流程' }
])

// 自定义属性
const customProperties = computed(() => {
  const { properties } = formData.value
  const custom: Record<string, any> = {}
  
  // 过滤掉系统属性
  const systemKeys = ['decisionCondition', 'defaultBranch', 'subprocessId', 'inheritVariables']
  
  Object.keys(properties).forEach(key => {
    if (!systemKeys.includes(key)) {
      custom[key] = properties[key]
    }
  })
  
  return custom
})

// 监听节点变化
watch(() => props.node, (newNode) => {
  formData.value = {
    ...newNode,
    dueDate: newNode.dueDate ? dayjs(newNode.dueDate) : undefined
  }
}, { deep: true })

// 表单变化处理
const onFormChange = () => {
  const updatedNode: NodeDefinition = {
    ...formData.value,
    dueDate: formData.value.dueDate?.format('YYYY-MM-DD HH:mm:ss'),
    properties: {
      ...formData.value.properties,
      ...(formData.value.decisionCondition && { decisionCondition: formData.value.decisionCondition }),
      ...(formData.value.defaultBranch && { defaultBranch: formData.value.defaultBranch }),
      ...(formData.value.subprocessId && { subprocessId: formData.value.subprocessId }),
      ...(formData.value.inheritVariables !== undefined && { inheritVariables: formData.value.inheritVariables })
    }
  }
  
  emit('update', updatedNode)
}

// 自定义属性管理
const addCustomProperty = () => {
  const key = `property_${Date.now()}`
  formData.value.properties[key] = ''
  onFormChange()
}

const removeCustomProperty = (key: string) => {
  delete formData.value.properties[key]
  onFormChange()
}

const onCustomPropertyKeyChange = (oldKey: string, newKey: string) => {
  if (newKey && newKey !== oldKey) {
    const value = formData.value.properties[oldKey]
    delete formData.value.properties[oldKey]
    formData.value.properties[newKey] = value
    onFormChange()
  }
}

const onCustomPropertyValueChange = (key: string, value: string) => {
  formData.value.properties[key] = value
  onFormChange()
}
</script>

<style scoped>
.node-properties {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.custom-properties {
  margin-top: 8px;
}

.property-item {
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0 12px 0;
}

:deep(.ant-divider-inner-text) {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}
</style>
