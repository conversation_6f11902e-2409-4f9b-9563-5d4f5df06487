<template>
  <div class="form-canvas">
    <!-- 画布工具栏 -->
    <div class="canvas-toolbar">
      <a-space>
        <span class="toolbar-title">表单设计</span>
        <a-divider type="vertical" />
        <a-button size="small" @click="selectAll">
          <template #icon><select-outlined /></template>
          全选
        </a-button>
        <a-button size="small" @click="clearSelection">
          <template #icon><close-outlined /></template>
          取消选择
        </a-button>
        <a-button size="small" @click="deleteSelected" :disabled="!selectedFieldId">
          <template #icon><delete-outlined /></template>
          删除
        </a-button>
      </a-space>
    </div>

    <!-- 表单预览区域 -->
    <div 
      class="canvas-container"
      @drop="onDrop"
      @dragover="onDragOver"
      @click="onCanvasClick"
    >
      <div class="form-container" :style="containerStyle">
        <div class="form-header" v-if="showFormHeader">
          <h3>{{ formTitle || '表单标题' }}</h3>
          <p v-if="formDescription">{{ formDescription }}</p>
        </div>
        
        <a-form
          :label-col="{ span: labelColSpan }"
          :wrapper-col="{ span: wrapperColSpan }"
          :size="layout.size"
          class="design-form"
        >
          <a-row :gutter="16">
            <a-col 
              v-for="(field, index) in fields" 
              :key="field.id"
              :span="getFieldSpan()"
              class="field-wrapper"
              :class="{ 'selected': selectedFieldId === field.id }"
              @click.stop="selectField(field)"
            >
              <div class="field-container">
                <!-- 字段工具栏 -->
                <div class="field-toolbar" v-if="selectedFieldId === field.id">
                  <a-space size="small">
                    <a-button type="text" size="small" @click.stop="moveFieldUp(index)">
                      <template #icon><up-outlined /></template>
                    </a-button>
                    <a-button type="text" size="small" @click.stop="moveFieldDown(index)">
                      <template #icon><down-outlined /></template>
                    </a-button>
                    <a-button type="text" size="small" @click.stop="duplicateField(field)">
                      <template #icon><copy-outlined /></template>
                    </a-button>
                    <a-button type="text" size="small" danger @click.stop="deleteField(field.id)">
                      <template #icon><delete-outlined /></template>
                    </a-button>
                  </a-space>
                </div>

                <!-- 表单项 -->
                <a-form-item
                  :label="field.label"
                  :required="field.required"
                  :help="field.validation?.message"
                  :validate-status="getValidateStatus(field)"
                >
                  <!-- 文本输入 -->
                  <a-input
                    v-if="field.type === 'text'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                  />
                  
                  <!-- 多行文本 -->
                  <a-textarea
                    v-else-if="field.type === 'textarea'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                    :rows="3"
                  />
                  
                  <!-- 数字输入 -->
                  <a-input-number
                    v-else-if="field.type === 'number'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                    style="width: 100%"
                  />
                  
                  <!-- 下拉选择 -->
                  <a-select
                    v-else-if="field.type === 'select'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                    style="width: 100%"
                  >
                    <a-select-option 
                      v-for="option in field.options" 
                      :key="option.value" 
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-select-option>
                  </a-select>
                  
                  <!-- 单选按钮 -->
                  <a-radio-group
                    v-else-if="field.type === 'radio'"
                    :disabled="true"
                    :value="field.defaultValue"
                  >
                    <a-radio 
                      v-for="option in field.options" 
                      :key="option.value" 
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-radio>
                  </a-radio-group>
                  
                  <!-- 多选框 -->
                  <a-checkbox-group
                    v-else-if="field.type === 'checkbox'"
                    :disabled="true"
                    :value="field.defaultValue"
                  >
                    <a-checkbox 
                      v-for="option in field.options" 
                      :key="option.value" 
                      :value="option.value"
                    >
                      {{ option.label }}
                    </a-checkbox>
                  </a-checkbox-group>
                  
                  <!-- 日期选择 -->
                  <a-date-picker
                    v-else-if="field.type === 'date'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                    style="width: 100%"
                  />
                  
                  <!-- 日期时间选择 -->
                  <a-date-picker
                    v-else-if="field.type === 'datetime'"
                    :placeholder="field.placeholder"
                    :disabled="true"
                    :value="field.defaultValue"
                    show-time
                    style="width: 100%"
                  />
                  
                  <!-- 文件上传 -->
                  <a-upload
                    v-else-if="field.type === 'file'"
                    :disabled="true"
                    :file-list="[]"
                  >
                    <a-button>
                      <upload-outlined />
                      {{ field.placeholder || '选择文件' }}
                    </a-button>
                  </a-upload>
                  
                  <!-- 图片上传 -->
                  <a-upload
                    v-else-if="field.type === 'image'"
                    :disabled="true"
                    :file-list="[]"
                    list-type="picture-card"
                  >
                    <div>
                      <plus-outlined />
                      <div style="margin-top: 8px">{{ field.placeholder || '上传图片' }}</div>
                    </div>
                  </a-upload>
                </a-form-item>
              </div>
            </a-col>
          </a-row>
        </a-form>
        
        <!-- 空状态 -->
        <div v-if="fields.length === 0" class="empty-state">
          <a-empty description="暂无表单字段">
            <template #image>
              <form-outlined style="font-size: 64px; color: #d9d9d9;" />
            </template>
            <p>从左侧拖拽组件到此处开始设计表单</p>
          </a-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SelectOutlined,
  CloseOutlined,
  DeleteOutlined,
  UpOutlined,
  DownOutlined,
  CopyOutlined,
  UploadOutlined,
  PlusOutlined,
  FormOutlined
} from '@ant-design/icons-vue'
import type { FormFieldDefinition } from '@/types/workflow'

// Props
interface Props {
  fields: FormFieldDefinition[]
  layout: {
    columns: number
    labelWidth?: number
    size?: 'small' | 'default' | 'large'
  }
  formTitle?: string
  formDescription?: string
}

const props = withDefaults(defineProps<Props>(), {
  formTitle: '',
  formDescription: ''
})

// Emits
const emit = defineEmits<{
  'update:fields': [fields: FormFieldDefinition[]]
  'field-select': [field: FormFieldDefinition | null]
  'drop': [event: DragEvent]
}>()

// 响应式数据
const selectedFieldId = ref<string | null>(null)
const showFormHeader = ref(true)

// 计算属性
const containerStyle = computed(() => ({
  padding: '24px',
  minHeight: '500px'
}))

const labelColSpan = computed(() => {
  const labelWidth = props.layout.labelWidth || 120
  return Math.round(labelWidth / 8) // 假设总宽度为24，每个span为8px
})

const wrapperColSpan = computed(() => {
  return 24 - labelColSpan.value
})

const getFieldSpan = () => {
  return 24 / props.layout.columns
}

const getValidateStatus = (field: FormFieldDefinition) => {
  if (field.validation?.message) {
    return 'error'
  }
  return undefined
}

// 方法
const selectField = (field: FormFieldDefinition) => {
  selectedFieldId.value = field.id
  emit('field-select', field)
}

const selectAll = () => {
  // 全选功能暂时不实现
  message.info('全选功能开发中...')
}

const clearSelection = () => {
  selectedFieldId.value = null
  emit('field-select', null)
}

const deleteSelected = () => {
  if (selectedFieldId.value) {
    deleteField(selectedFieldId.value)
  }
}

const deleteField = (fieldId: string) => {
  const newFields = props.fields.filter(f => f.id !== fieldId)
  emit('update:fields', newFields)
  
  if (selectedFieldId.value === fieldId) {
    selectedFieldId.value = null
    emit('field-select', null)
  }
  
  message.success('字段删除成功')
}

const moveFieldUp = (index: number) => {
  if (index > 0) {
    const newFields = [...props.fields]
    const temp = newFields[index]
    newFields[index] = newFields[index - 1]
    newFields[index - 1] = temp
    emit('update:fields', newFields)
  }
}

const moveFieldDown = (index: number) => {
  if (index < props.fields.length - 1) {
    const newFields = [...props.fields]
    const temp = newFields[index]
    newFields[index] = newFields[index + 1]
    newFields[index + 1] = temp
    emit('update:fields', newFields)
  }
}

const duplicateField = (field: FormFieldDefinition) => {
  const newField: FormFieldDefinition = {
    ...field,
    id: `field_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    name: `${field.name}_copy`,
    label: `${field.label}_副本`
  }
  
  const fieldIndex = props.fields.findIndex(f => f.id === field.id)
  const newFields = [...props.fields]
  newFields.splice(fieldIndex + 1, 0, newField)
  
  emit('update:fields', newFields)
  message.success('字段复制成功')
}

const onCanvasClick = () => {
  selectedFieldId.value = null
  emit('field-select', null)
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  emit('drop', event)
}

const onDragOver = (event: DragEvent) => {
  event.preventDefault()
}
</script>

<style scoped>
.form-canvas {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
}

.canvas-toolbar {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-title {
  font-weight: 600;
  color: #333;
}

.canvas-container {
  flex: 1;
  overflow: auto;
  background: #f5f5f5;
}

.form-container {
  background: white;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.form-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.form-header p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.design-form {
  padding: 0 24px 24px 24px;
}

.field-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.field-wrapper.selected {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
  border-radius: 4px;
}

.field-container {
  position: relative;
}

.field-toolbar {
  position: absolute;
  top: -32px;
  right: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #999;
}

.empty-state p {
  margin-top: 16px;
  font-size: 14px;
}

/* 表单项样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-form-item-required::before) {
  color: #ff4d4f;
}

/* 禁用状态样式 */
:deep(.ant-input[disabled]),
:deep(.ant-input-number[disabled]),
:deep(.ant-select[disabled]),
:deep(.ant-radio[disabled]),
:deep(.ant-checkbox[disabled]) {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-container {
    margin: 8px;
    padding: 16px;
  }
  
  .form-header {
    padding: 16px 16px 0 16px;
  }
  
  .design-form {
    padding: 0 16px 16px 16px;
  }
  
  .field-toolbar {
    position: static;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
  }
}

/* 动画效果 */
.field-wrapper {
  transition: all 0.2s ease;
}

.field-wrapper:hover {
  background: rgba(24, 144, 255, 0.05);
  border-radius: 4px;
}

.field-toolbar {
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
