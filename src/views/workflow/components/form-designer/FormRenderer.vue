<template>
  <div class="form-renderer">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: labelColSpan }"
      :wrapper-col="{ span: wrapperColSpan }"
      :size="layout.size"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-row :gutter="16">
        <a-col 
          v-for="field in fields" 
          :key="field.id"
          :span="getFieldSpan()"
        >
          <a-form-item
            :name="field.name"
            :label="field.label"
            :required="field.required"
          >
            <!-- 文本输入 -->
            <a-input
              v-if="field.type === 'text'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
            />
            
            <!-- 多行文本 -->
            <a-textarea
              v-else-if="field.type === 'textarea'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
              :rows="field.properties?.rows || 3"
            />
            
            <!-- 数字输入 -->
            <a-input-number
              v-else-if="field.type === 'number'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
              :min="field.validation?.min"
              :max="field.validation?.max"
              style="width: 100%"
            />
            
            <!-- 下拉选择 -->
            <a-select
              v-else-if="field.type === 'select'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
              style="width: 100%"
            >
              <a-select-option 
                v-for="option in field.options" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </a-select-option>
            </a-select>
            
            <!-- 单选按钮 -->
            <a-radio-group
              v-else-if="field.type === 'radio'"
              v-model:value="formData[field.name]"
              :disabled="readonly"
            >
              <a-radio 
                v-for="option in field.options" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </a-radio>
            </a-radio-group>
            
            <!-- 多选框 -->
            <a-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model:value="formData[field.name]"
              :disabled="readonly"
            >
              <a-checkbox 
                v-for="option in field.options" 
                :key="option.value" 
                :value="option.value"
              >
                {{ option.label }}
              </a-checkbox>
            </a-checkbox-group>
            
            <!-- 日期选择 -->
            <a-date-picker
              v-else-if="field.type === 'date'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
              style="width: 100%"
            />
            
            <!-- 日期时间选择 -->
            <a-date-picker
              v-else-if="field.type === 'datetime'"
              v-model:value="formData[field.name]"
              :placeholder="field.placeholder"
              :disabled="readonly"
              show-time
              style="width: 100%"
            />
            
            <!-- 文件上传 -->
            <a-upload
              v-else-if="field.type === 'file'"
              v-model:file-list="formData[field.name]"
              :disabled="readonly"
              :accept="field.properties?.accept"
              :max-count="field.properties?.maxCount || 1"
              :before-upload="beforeUpload"
            >
              <a-button :disabled="readonly">
                <upload-outlined />
                {{ field.placeholder || '选择文件' }}
              </a-button>
            </a-upload>
            
            <!-- 图片上传 -->
            <a-upload
              v-else-if="field.type === 'image'"
              v-model:file-list="formData[field.name]"
              :disabled="readonly"
              :accept="field.properties?.accept || 'image/*'"
              :max-count="field.properties?.maxCount || 1"
              list-type="picture-card"
              :before-upload="beforeUpload"
            >
              <div v-if="!readonly">
                <plus-outlined />
                <div style="margin-top: 8px">{{ field.placeholder || '上传图片' }}</div>
              </div>
            </a-upload>
          </a-form-item>
        </a-col>
      </a-row>
      
      <!-- 提交按钮 -->
      <a-form-item v-if="!readonly && showSubmitButton" :wrapper-col="{ offset: labelColSpan, span: wrapperColSpan }">
        <a-space>
          <a-button type="primary" html-type="submit" :loading="submitting">
            {{ submitButtonText }}
          </a-button>
          <a-button v-if="showResetButton" @click="resetForm">
            {{ resetButtonText }}
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  UploadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'
import type { FormFieldDefinition } from '@/types/workflow'

// Props
interface Props {
  fields: FormFieldDefinition[]
  layout: {
    columns: number
    labelWidth?: number
    size?: 'small' | 'default' | 'large'
  }
  readonly?: boolean
  showSubmitButton?: boolean
  showResetButton?: boolean
  submitButtonText?: string
  resetButtonText?: string
  initialData?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showSubmitButton: true,
  showResetButton: false,
  submitButtonText: '提交',
  resetButtonText: '重置',
  initialData: () => ({})
})

// Emits
const emit = defineEmits<{
  'submit': [data: Record<string, any>]
  'change': [data: Record<string, any>]
}>()

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const formData = reactive<Record<string, any>>({})

// 计算属性
const labelColSpan = computed(() => {
  const labelWidth = props.layout.labelWidth || 120
  return Math.round(labelWidth / 8) // 假设总宽度为24，每个span为8px
})

const wrapperColSpan = computed(() => {
  return 24 - labelColSpan.value
})

const getFieldSpan = () => {
  return 24 / props.layout.columns
}

// 表单验证规则
const formRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  props.fields.forEach(field => {
    const fieldRules: any[] = []
    
    // 必填验证
    if (field.required) {
      fieldRules.push({
        required: true,
        message: `请输入${field.label}`,
        trigger: ['blur', 'change']
      })
    }
    
    // 长度验证
    if (field.validation?.min || field.validation?.max) {
      if (field.type === 'text' || field.type === 'textarea') {
        fieldRules.push({
          min: field.validation.min,
          max: field.validation.max,
          message: field.validation.message || `长度应在${field.validation.min || 0}-${field.validation.max || '∞'}之间`,
          trigger: 'blur'
        })
      } else if (field.type === 'number') {
        fieldRules.push({
          type: 'number',
          min: field.validation.min,
          max: field.validation.max,
          message: field.validation.message || `数值应在${field.validation.min || '-∞'}-${field.validation.max || '∞'}之间`,
          trigger: 'blur'
        })
      }
    }
    
    // 正则验证
    if (field.validation?.pattern) {
      fieldRules.push({
        pattern: new RegExp(field.validation.pattern),
        message: field.validation.message || '格式不正确',
        trigger: 'blur'
      })
    }
    
    if (fieldRules.length > 0) {
      rules[field.name] = fieldRules
    }
  })
  
  return rules
})

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  // 设置默认值
  props.fields.forEach(field => {
    if (props.initialData[field.name] !== undefined) {
      formData[field.name] = props.initialData[field.name]
    } else if (field.defaultValue !== undefined) {
      formData[field.name] = field.defaultValue
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'checkbox':
          formData[field.name] = []
          break
        case 'file':
        case 'image':
          formData[field.name] = []
          break
        default:
          formData[field.name] = undefined
      }
    }
  })
}

// 监听字段变化
watch(() => props.fields, () => {
  initFormData()
}, { immediate: true, deep: true })

// 监听初始数据变化
watch(() => props.initialData, () => {
  initFormData()
}, { deep: true })

// 监听表单数据变化
watch(formData, (newData) => {
  emit('change', { ...newData })
}, { deep: true })

// 表单提交
const onFinish = async (values: Record<string, any>) => {
  submitting.value = true
  
  try {
    emit('submit', values)
    message.success('表单提交成功')
  } catch (error) {
    message.error('表单提交失败')
    console.error('Form submit error:', error)
  } finally {
    submitting.value = false
  }
}

const onFinishFailed = (errorInfo: any) => {
  console.log('Form validation failed:', errorInfo)
  message.error('请检查表单填写是否正确')
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
  message.success('表单已重置')
}

// 文件上传前验证
const beforeUpload = (file: File, field?: FormFieldDefinition) => {
  // 文件大小验证
  const maxSize = field?.properties?.maxSize || 10 // 默认10MB
  const isLtMaxSize = file.size / 1024 / 1024 < maxSize
  
  if (!isLtMaxSize) {
    message.error(`文件大小不能超过 ${maxSize}MB`)
    return false
  }
  
  return false // 阻止自动上传，由业务逻辑处理
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  getFieldsValue: () => formRef.value?.getFieldsValue(),
  setFieldsValue: (values: Record<string, any>) => formRef.value?.setFieldsValue(values)
})
</script>

<style scoped>
.form-renderer {
  padding: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-form-item-required::before) {
  color: #ff4d4f;
}

:deep(.ant-upload-list-picture-card .ant-upload-list-item) {
  width: 104px;
  height: 104px;
}

:deep(.ant-upload-select-picture-card) {
  width: 104px;
  height: 104px;
}
</style>
