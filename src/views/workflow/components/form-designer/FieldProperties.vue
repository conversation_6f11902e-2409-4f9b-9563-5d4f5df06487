<template>
  <div class="field-properties">
    <div class="properties-header">
      <h4>字段属性</h4>
      <a-button type="text" size="small" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="properties-content">
      <a-form :model="formData" layout="vertical" size="small">
        <!-- 基本信息 -->
        <a-form-item label="字段标识" required>
          <a-input 
            v-model:value="formData.name" 
            placeholder="请输入字段标识"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="字段标签" required>
          <a-input 
            v-model:value="formData.label" 
            placeholder="请输入字段标签"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="字段类型">
          <a-select 
            v-model:value="formData.type" 
            :disabled="true"
            style="width: 100%"
          >
            <a-select-option value="text">单行文本</a-select-option>
            <a-select-option value="textarea">多行文本</a-select-option>
            <a-select-option value="number">数字输入</a-select-option>
            <a-select-option value="select">下拉选择</a-select-option>
            <a-select-option value="radio">单选按钮</a-select-option>
            <a-select-option value="checkbox">多选框</a-select-option>
            <a-select-option value="date">日期选择</a-select-option>
            <a-select-option value="datetime">日期时间</a-select-option>
            <a-select-option value="file">文件上传</a-select-option>
            <a-select-option value="image">图片上传</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="占位符">
          <a-input 
            v-model:value="formData.placeholder" 
            placeholder="请输入占位符文本"
            @change="onFormChange"
          />
        </a-form-item>

        <a-form-item label="默认值">
          <a-input 
            v-model:value="formData.defaultValue" 
            placeholder="请输入默认值"
            @change="onFormChange"
          />
        </a-form-item>

        <a-form-item label="是否必填">
          <a-switch 
            v-model:checked="formData.required" 
            @change="onFormChange"
          />
        </a-form-item>

        <!-- 选择类型字段的选项配置 -->
        <template v-if="hasOptions">
          <a-divider orientation="left">选项配置</a-divider>
          
          <div class="options-editor">
            <div 
              v-for="(option, index) in formData.options" 
              :key="index" 
              class="option-item"
            >
              <a-row :gutter="8" align="middle">
                <a-col :span="10">
                  <a-input 
                    v-model:value="option.label" 
                    placeholder="选项标签"
                    size="small"
                    @change="onOptionsChange"
                  />
                </a-col>
                <a-col :span="10">
                  <a-input 
                    v-model:value="option.value" 
                    placeholder="选项值"
                    size="small"
                    @change="onOptionsChange"
                  />
                </a-col>
                <a-col :span="4">
                  <a-button 
                    type="text" 
                    size="small" 
                    danger
                    @click="removeOption(index)"
                  >
                    <template #icon><delete-outlined /></template>
                  </a-button>
                </a-col>
              </a-row>
            </div>
            
            <a-button 
              type="dashed" 
              size="small" 
              block
              @click="addOption"
            >
              <template #icon><plus-outlined /></template>
              添加选项
            </a-button>
          </div>
        </template>

        <!-- 验证规则 -->
        <a-divider orientation="left">验证规则</a-divider>
        
        <template v-if="formData.type === 'text' || formData.type === 'textarea'">
          <a-form-item label="最小长度">
            <a-input-number 
              v-model:value="formData.validation.min" 
              :min="0"
              style="width: 100%"
              placeholder="最小字符数"
              @change="onValidationChange"
            />
          </a-form-item>
          
          <a-form-item label="最大长度">
            <a-input-number 
              v-model:value="formData.validation.max" 
              :min="0"
              style="width: 100%"
              placeholder="最大字符数"
              @change="onValidationChange"
            />
          </a-form-item>
          
          <a-form-item label="正则表达式">
            <a-input 
              v-model:value="formData.validation.pattern" 
              placeholder="请输入正则表达式"
              @change="onValidationChange"
            />
          </a-form-item>
        </template>
        
        <template v-if="formData.type === 'number'">
          <a-form-item label="最小值">
            <a-input-number 
              v-model:value="formData.validation.min" 
              style="width: 100%"
              placeholder="最小数值"
              @change="onValidationChange"
            />
          </a-form-item>
          
          <a-form-item label="最大值">
            <a-input-number 
              v-model:value="formData.validation.max" 
              style="width: 100%"
              placeholder="最大数值"
              @change="onValidationChange"
            />
          </a-form-item>
        </template>
        
        <a-form-item label="错误提示">
          <a-input 
            v-model:value="formData.validation.message" 
            placeholder="请输入验证失败时的错误提示"
            @change="onValidationChange"
          />
        </a-form-item>

        <!-- 高级设置 -->
        <a-divider orientation="left">高级设置</a-divider>
        
        <template v-if="formData.type === 'textarea'">
          <a-form-item label="行数">
            <a-input-number 
              v-model:value="formData.properties.rows" 
              :min="1"
              :max="10"
              style="width: 100%"
              @change="onPropertiesChange"
            />
          </a-form-item>
        </template>
        
        <template v-if="formData.type === 'file' || formData.type === 'image'">
          <a-form-item label="允许的文件类型">
            <a-input 
              v-model:value="formData.properties.accept" 
              placeholder="如：.jpg,.png,.pdf"
              @change="onPropertiesChange"
            />
          </a-form-item>
          
          <a-form-item label="最大文件大小(MB)">
            <a-input-number 
              v-model:value="formData.properties.maxSize" 
              :min="1"
              :max="100"
              style="width: 100%"
              @change="onPropertiesChange"
            />
          </a-form-item>
          
          <a-form-item label="最大文件数量">
            <a-input-number 
              v-model:value="formData.properties.maxCount" 
              :min="1"
              :max="10"
              style="width: 100%"
              @change="onPropertiesChange"
            />
          </a-form-item>
        </template>

        <!-- 自定义属性 -->
        <a-divider orientation="left">自定义属性</a-divider>
        
        <div class="custom-properties">
          <div 
            v-for="(value, key) in customProperties" 
            :key="key" 
            class="property-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="8">
                <a-input 
                  :value="key" 
                  placeholder="属性名"
                  size="small"
                  @change="(e) => onCustomPropertyKeyChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="12">
                <a-input 
                  :value="value" 
                  placeholder="属性值"
                  size="small"
                  @change="(e) => onCustomPropertyValueChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  @click="removeCustomProperty(key)"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button 
            type="dashed" 
            size="small" 
            block
            @click="addCustomProperty"
          >
            <template #icon><plus-outlined /></template>
            添加属性
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  CloseOutlined, 
  DeleteOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue'
import type { FormFieldDefinition } from '@/types/workflow'

// Props
interface Props {
  field: FormFieldDefinition
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [field: FormFieldDefinition]
  'close': []
}>()

// 表单数据
const formData = ref<FormFieldDefinition>({
  ...props.field,
  validation: props.field.validation || {},
  properties: props.field.properties || {}
})

// 计算属性
const hasOptions = computed(() => {
  return ['select', 'radio', 'checkbox'].includes(formData.value.type)
})

const customProperties = computed(() => {
  const { properties } = formData.value
  const custom: Record<string, any> = {}
  
  // 过滤掉系统属性
  const systemKeys = ['rows', 'accept', 'maxSize', 'maxCount']
  
  Object.keys(properties).forEach(key => {
    if (!systemKeys.includes(key)) {
      custom[key] = properties[key]
    }
  })
  
  return custom
})

// 监听字段变化
watch(() => props.field, (newField) => {
  formData.value = {
    ...newField,
    validation: newField.validation || {},
    properties: newField.properties || {}
  }
}, { deep: true })

// 表单变化处理
const onFormChange = () => {
  emit('update', { ...formData.value })
}

const onOptionsChange = () => {
  emit('update', { ...formData.value })
}

const onValidationChange = () => {
  emit('update', { ...formData.value })
}

const onPropertiesChange = () => {
  emit('update', { ...formData.value })
}

// 选项管理
const addOption = () => {
  if (!formData.value.options) {
    formData.value.options = []
  }
  formData.value.options.push({
    label: `选项${formData.value.options.length + 1}`,
    value: `option${formData.value.options.length + 1}`
  })
  onOptionsChange()
}

const removeOption = (index: number) => {
  if (formData.value.options) {
    formData.value.options.splice(index, 1)
    onOptionsChange()
  }
}

// 自定义属性管理
const addCustomProperty = () => {
  const key = `property_${Date.now()}`
  formData.value.properties[key] = ''
  onPropertiesChange()
}

const removeCustomProperty = (key: string) => {
  delete formData.value.properties[key]
  onPropertiesChange()
}

const onCustomPropertyKeyChange = (oldKey: string, newKey: string) => {
  if (newKey && newKey !== oldKey) {
    const value = formData.value.properties[oldKey]
    delete formData.value.properties[oldKey]
    formData.value.properties[newKey] = value
    onPropertiesChange()
  }
}

const onCustomPropertyValueChange = (key: string, value: string) => {
  formData.value.properties[key] = value
  onPropertiesChange()
}
</script>

<style scoped>
.field-properties {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.options-editor {
  margin-top: 8px;
}

.option-item {
  margin-bottom: 8px;
}

.custom-properties {
  margin-top: 8px;
}

.property-item {
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0 12px 0;
}

:deep(.ant-divider-inner-text) {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}
</style>
