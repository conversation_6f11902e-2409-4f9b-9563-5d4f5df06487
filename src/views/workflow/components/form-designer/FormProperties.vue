<template>
  <div class="form-properties">
    <div class="properties-header">
      <h4>表单属性</h4>
      <a-button type="text" size="small" @click="$emit('close')">
        <template #icon><close-outlined /></template>
      </a-button>
    </div>
    
    <div class="properties-content">
      <a-form :model="formData" layout="vertical" size="small">
        <!-- 基本信息 -->
        <a-form-item label="表单名称" required>
          <a-input 
            v-model:value="formData.name" 
            placeholder="请输入表单名称"
            @change="onFormChange"
          />
        </a-form-item>
        
        <a-form-item label="表单描述">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入表单描述"
            :rows="3"
            @change="onFormChange"
          />
        </a-form-item>

        <!-- 布局设置 -->
        <a-divider orientation="left">布局设置</a-divider>
        
        <a-form-item label="列数">
          <a-input-number 
            v-model:value="formData.layout.columns" 
            :min="1"
            :max="4"
            style="width: 100%"
            @change="onLayoutChange"
          />
          <div class="form-help">
            设置表单的列数，建议1-4列
          </div>
        </a-form-item>
        
        <a-form-item label="标签宽度">
          <a-input-number 
            v-model:value="formData.layout.labelWidth" 
            :min="80"
            :max="200"
            style="width: 100%"
            @change="onLayoutChange"
          />
          <div class="form-help">
            设置表单标签的宽度，单位：像素
          </div>
        </a-form-item>
        
        <a-form-item label="组件尺寸">
          <a-select 
            v-model:value="formData.layout.size" 
            style="width: 100%"
            @change="onLayoutChange"
          >
            <a-select-option value="small">小</a-select-option>
            <a-select-option value="default">中</a-select-option>
            <a-select-option value="large">大</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 样式设置 -->
        <a-divider orientation="left">样式设置</a-divider>
        
        <a-form-item label="表单背景色">
          <a-input 
            v-model:value="formData.style.backgroundColor" 
            placeholder="#ffffff"
            @change="onStyleChange"
          >
            <template #addonAfter>
              <input 
                type="color" 
                :value="formData.style.backgroundColor || '#ffffff'"
                @change="onBackgroundColorChange"
                style="width: 30px; height: 24px; border: none; cursor: pointer;"
              />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item label="边框样式">
          <a-select 
            v-model:value="formData.style.borderStyle" 
            style="width: 100%"
            @change="onStyleChange"
          >
            <a-select-option value="none">无边框</a-select-option>
            <a-select-option value="solid">实线</a-select-option>
            <a-select-option value="dashed">虚线</a-select-option>
            <a-select-option value="dotted">点线</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="圆角大小">
          <a-input-number 
            v-model:value="formData.style.borderRadius" 
            :min="0"
            :max="20"
            style="width: 100%"
            @change="onStyleChange"
          />
          <div class="form-help">
            设置表单容器的圆角大小，单位：像素
          </div>
        </a-form-item>

        <!-- 提交设置 -->
        <a-divider orientation="left">提交设置</a-divider>
        
        <a-form-item label="提交方式">
          <a-select 
            v-model:value="formData.submit.method" 
            style="width: 100%"
            @change="onSubmitChange"
          >
            <a-select-option value="POST">POST</a-select-option>
            <a-select-option value="PUT">PUT</a-select-option>
            <a-select-option value="PATCH">PATCH</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="提交地址">
          <a-input 
            v-model:value="formData.submit.action" 
            placeholder="请输入提交地址"
            @change="onSubmitChange"
          />
        </a-form-item>
        
        <a-form-item label="提交按钮文本">
          <a-input 
            v-model:value="formData.submit.buttonText" 
            placeholder="提交"
            @change="onSubmitChange"
          />
        </a-form-item>
        
        <a-form-item label="重置按钮">
          <a-switch 
            v-model:checked="formData.submit.showReset" 
            @change="onSubmitChange"
          />
        </a-form-item>
        
        <a-form-item label="重置按钮文本" v-if="formData.submit.showReset">
          <a-input 
            v-model:value="formData.submit.resetText" 
            placeholder="重置"
            @change="onSubmitChange"
          />
        </a-form-item>

        <!-- 验证设置 -->
        <a-divider orientation="left">验证设置</a-divider>
        
        <a-form-item label="实时验证">
          <a-switch 
            v-model:checked="formData.validation.realtime" 
            @change="onValidationChange"
          />
          <div class="form-help">
            开启后，用户输入时实时进行字段验证
          </div>
        </a-form-item>
        
        <a-form-item label="提交前验证">
          <a-switch 
            v-model:checked="formData.validation.beforeSubmit" 
            @change="onValidationChange"
          />
          <div class="form-help">
            开启后，提交前会验证所有必填字段
          </div>
        </a-form-item>
        
        <a-form-item label="错误提示位置">
          <a-select 
            v-model:value="formData.validation.errorPosition" 
            style="width: 100%"
            @change="onValidationChange"
          >
            <a-select-option value="bottom">字段下方</a-select-option>
            <a-select-option value="right">字段右侧</a-select-option>
            <a-select-option value="tooltip">悬浮提示</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 自定义属性 -->
        <a-divider orientation="left">自定义属性</a-divider>
        
        <div class="custom-properties">
          <div 
            v-for="(value, key) in customProperties" 
            :key="key" 
            class="property-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="8">
                <a-input 
                  :value="key" 
                  placeholder="属性名"
                  size="small"
                  @change="(e) => onCustomPropertyKeyChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="12">
                <a-input 
                  :value="value" 
                  placeholder="属性值"
                  size="small"
                  @change="(e) => onCustomPropertyValueChange(key, e.target.value)"
                />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  size="small" 
                  danger
                  @click="removeCustomProperty(key)"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button 
            type="dashed" 
            size="small" 
            block
            @click="addCustomProperty"
          >
            <template #icon><plus-outlined /></template>
            添加属性
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  CloseOutlined, 
  DeleteOutlined, 
  PlusOutlined 
} from '@ant-design/icons-vue'
import type { FormDefinition } from '@/types/workflow'

// Props
interface Props {
  form: FormDefinition
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [form: Partial<FormDefinition>]
  'close': []
}>()

// 表单数据
const formData = ref({
  ...props.form,
  style: props.form.style || {
    backgroundColor: '#ffffff',
    borderStyle: 'solid',
    borderRadius: 4
  },
  submit: props.form.submit || {
    method: 'POST',
    action: '',
    buttonText: '提交',
    showReset: false,
    resetText: '重置'
  },
  validation: props.form.validation || {
    realtime: true,
    beforeSubmit: true,
    errorPosition: 'bottom'
  }
})

// 计算属性
const customProperties = computed(() => {
  const { properties } = formData.value
  const custom: Record<string, any> = {}
  
  // 过滤掉系统属性
  const systemKeys = ['style', 'submit', 'validation']
  
  Object.keys(properties).forEach(key => {
    if (!systemKeys.includes(key)) {
      custom[key] = properties[key]
    }
  })
  
  return custom
})

// 监听表单变化
watch(() => props.form, (newForm) => {
  formData.value = {
    ...newForm,
    style: newForm.style || formData.value.style,
    submit: newForm.submit || formData.value.submit,
    validation: newForm.validation || formData.value.validation
  }
}, { deep: true })

// 表单变化处理
const onFormChange = () => {
  emit('update', { ...formData.value })
}

const onLayoutChange = () => {
  emit('update', { ...formData.value })
}

const onStyleChange = () => {
  emit('update', { ...formData.value })
}

const onSubmitChange = () => {
  emit('update', { ...formData.value })
}

const onValidationChange = () => {
  emit('update', { ...formData.value })
}

const onBackgroundColorChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  formData.value.style.backgroundColor = target.value
  onStyleChange()
}

// 自定义属性管理
const addCustomProperty = () => {
  const key = `property_${Date.now()}`
  formData.value.properties[key] = ''
  onFormChange()
}

const removeCustomProperty = (key: string) => {
  delete formData.value.properties[key]
  onFormChange()
}

const onCustomPropertyKeyChange = (oldKey: string, newKey: string) => {
  if (newKey && newKey !== oldKey) {
    const value = formData.value.properties[oldKey]
    delete formData.value.properties[oldKey]
    formData.value.properties[newKey] = value
    onFormChange()
  }
}

const onCustomPropertyValueChange = (key: string, value: string) => {
  formData.value.properties[key] = value
  onFormChange()
}
</script>

<style scoped>
.form-properties {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.properties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.properties-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.properties-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.form-help {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.custom-properties {
  margin-top: 8px;
}

.property-item {
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 12px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 16px 0 12px 0;
}

:deep(.ant-divider-inner-text) {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

:deep(.ant-input-group-addon) {
  padding: 0 8px;
}
</style>
