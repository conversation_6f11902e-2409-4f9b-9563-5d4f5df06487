<template>
  <div class="form-component-toolbar">
    <div class="toolbar-header">
      <h4>表单组件</h4>
      <a-button type="text" size="small" @click="toggleCollapse">
        <template #icon>
          <up-outlined v-if="!collapsed" />
          <down-outlined v-if="collapsed" />
        </template>
      </a-button>
    </div>
    
    <div v-show="!collapsed" class="toolbar-content">
      <!-- 基础组件 -->
      <div class="component-category">
        <div class="category-title">基础组件</div>
        <div class="component-list">
          <div
            v-for="component in basicComponents"
            :key="component.type"
            class="component-item"
            :class="{ 'dragging': draggedFieldType === component.type }"
            draggable="true"
            @dragstart="onDragStart(component.type, $event)"
            @dragend="onDragEnd"
            @click="addComponent(component.type)"
          >
            <div class="component-icon" :style="{ backgroundColor: component.color }">
              <component :is="component.icon" />
            </div>
            <div class="component-label">{{ component.label }}</div>
          </div>
        </div>
      </div>

      <!-- 选择组件 -->
      <div class="component-category">
        <div class="category-title">选择组件</div>
        <div class="component-list">
          <div
            v-for="component in selectionComponents"
            :key="component.type"
            class="component-item"
            :class="{ 'dragging': draggedFieldType === component.type }"
            draggable="true"
            @dragstart="onDragStart(component.type, $event)"
            @dragend="onDragEnd"
            @click="addComponent(component.type)"
          >
            <div class="component-icon" :style="{ backgroundColor: component.color }">
              <component :is="component.icon" />
            </div>
            <div class="component-label">{{ component.label }}</div>
          </div>
        </div>
      </div>

      <!-- 高级组件 -->
      <div class="component-category">
        <div class="category-title">高级组件</div>
        <div class="component-list">
          <div
            v-for="component in advancedComponents"
            :key="component.type"
            class="component-item"
            :class="{ 'dragging': draggedFieldType === component.type }"
            draggable="true"
            @dragstart="onDragStart(component.type, $event)"
            @dragend="onDragEnd"
            @click="addComponent(component.type)"
          >
            <div class="component-icon" :style="{ backgroundColor: component.color }">
              <component :is="component.icon" />
            </div>
            <div class="component-label">{{ component.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  UpOutlined, 
  DownOutlined,
  EditOutlined,
  AlignLeftOutlined,
  NumberOutlined,
  DownOutlined as SelectOutlined,
  CheckCircleOutlined,
  CheckSquareOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  FileOutlined,
  PictureOutlined
} from '@ant-design/icons-vue'
import type { FormFieldType } from '@/types/workflow'

// Emits
const emit = defineEmits<{
  'add-field': [fieldType: FormFieldType, position?: { x: number; y: number }]
  'drag-start': [fieldType: FormFieldType]
  'drag-end': []
}>()

// 响应式数据
const collapsed = ref(false)
const draggedFieldType = ref<FormFieldType | null>(null)

// 组件定义
interface ComponentItem {
  type: FormFieldType
  label: string
  icon: any
  color: string
  description: string
}

// 基础组件
const basicComponents: ComponentItem[] = [
  {
    type: 'text',
    label: '单行文本',
    icon: EditOutlined,
    color: '#1890ff',
    description: '单行文本输入框'
  },
  {
    type: 'textarea',
    label: '多行文本',
    icon: AlignLeftOutlined,
    color: '#52c41a',
    description: '多行文本输入框'
  },
  {
    type: 'number',
    label: '数字输入',
    icon: NumberOutlined,
    color: '#faad14',
    description: '数字输入框'
  }
]

// 选择组件
const selectionComponents: ComponentItem[] = [
  {
    type: 'select',
    label: '下拉选择',
    icon: SelectOutlined,
    color: '#722ed1',
    description: '下拉选择框'
  },
  {
    type: 'radio',
    label: '单选按钮',
    icon: CheckCircleOutlined,
    color: '#13c2c2',
    description: '单选按钮组'
  },
  {
    type: 'checkbox',
    label: '多选框',
    icon: CheckSquareOutlined,
    color: '#eb2f96',
    description: '多选框组'
  }
]

// 高级组件
const advancedComponents: ComponentItem[] = [
  {
    type: 'date',
    label: '日期选择',
    icon: CalendarOutlined,
    color: '#f5222d',
    description: '日期选择器'
  },
  {
    type: 'datetime',
    label: '日期时间',
    icon: ClockCircleOutlined,
    color: '#fa541c',
    description: '日期时间选择器'
  },
  {
    type: 'file',
    label: '文件上传',
    icon: FileOutlined,
    color: '#a0d911',
    description: '文件上传组件'
  },
  {
    type: 'image',
    label: '图片上传',
    icon: PictureOutlined,
    color: '#2f54eb',
    description: '图片上传组件'
  }
]

// 方法
const toggleCollapse = () => {
  collapsed.value = !collapsed.value
}

const onDragStart = (fieldType: FormFieldType, event: DragEvent) => {
  draggedFieldType.value = fieldType
  
  // 设置拖拽数据
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'form-field',
      fieldType
    }))
    event.dataTransfer.effectAllowed = 'copy'
  }
  
  emit('drag-start', fieldType)
}

const onDragEnd = () => {
  draggedFieldType.value = null
  emit('drag-end')
}

const addComponent = (fieldType: FormFieldType) => {
  emit('add-field', fieldType)
}
</script>

<style scoped>
.form-component-toolbar {
  width: 240px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.toolbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.toolbar-content {
  max-height: 600px;
  overflow-y: auto;
}

.component-category {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.component-category:last-child {
  border-bottom: none;
}

.category-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.component-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
  background: white;
  user-select: none;
}

.component-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.component-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.component-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  margin-bottom: 6px;
}

.component-label {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* 滚动条样式 */
.toolbar-content::-webkit-scrollbar {
  width: 4px;
}

.toolbar-content::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.toolbar-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.toolbar-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-component-toolbar {
    width: 200px;
  }
  
  .component-list {
    grid-template-columns: 1fr;
  }
  
  .component-item {
    flex-direction: row;
    justify-content: flex-start;
    padding: 8px 12px;
  }
  
  .component-icon {
    width: 24px;
    height: 24px;
    font-size: 12px;
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .component-label {
    font-size: 11px;
  }
}

/* 拖拽提示动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.component-item:hover .component-icon {
  animation: pulse 2s infinite;
}
</style>
