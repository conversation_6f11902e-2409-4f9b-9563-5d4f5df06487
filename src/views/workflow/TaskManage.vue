<template>
  <div class="task-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>任务管理</h2>
        <p>管理和处理工作流程中的各类任务，跟踪任务进度和状态</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportTasks" :disabled="!selectedRowKeys.length">
            <template #icon><download-outlined /></template>
            导出任务
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 任务统计 -->
    <div class="task-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="待处理任务"
              :value="stats.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="进行中任务"
              :value="stats.running"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <play-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已完成任务"
              :value="stats.completed"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="超时任务"
              :value="stats.overdue"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <exclamation-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card>
        <a-form :model="filterForm" class="filter-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="任务名称">
                <a-input 
                  v-model:value="filterForm.taskName" 
                  placeholder="请输入任务名称"
                  allow-clear
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="任务状态">
                <a-select 
                  v-model:value="filterForm.status" 
                  placeholder="请选择状态"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option :value="1">待处理</a-select-option>
                  <a-select-option :value="2">进行中</a-select-option>
                  <a-select-option :value="3">已完成</a-select-option>
                  <a-select-option :value="4">失败</a-select-option>
                  <a-select-option :value="5">跳过</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="处理人">
                <a-input 
                  v-model:value="filterForm.assignee" 
                  placeholder="请输入处理人"
                  allow-clear
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="优先级">
                <a-select 
                  v-model:value="filterForm.priority" 
                  placeholder="请选择优先级"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option :value="1">低</a-select-option>
                  <a-select-option :value="2">中</a-select-option>
                  <a-select-option :value="3">高</a-select-option>
                  <a-select-option :value="4">紧急</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 任务列表 -->
    <div class="task-list">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>任务列表 ({{ pagination.total }})</span>
            <a-space>
              <a-button size="small" @click="refreshData">
                <template #icon><reload-outlined /></template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleBatchAction">
                    <a-menu-item key="claim" :disabled="!selectedRowKeys.length">
                      <user-outlined />
                      批量认领
                    </a-menu-item>
                    <a-menu-item key="complete" :disabled="!selectedRowKeys.length">
                      <check-circle-outlined />
                      批量完成
                    </a-menu-item>
                    <a-menu-item key="delegate" :disabled="!selectedRowKeys.length">
                      <team-outlined />
                      批量委派
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="taskList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 任务名称列 -->
          <template #taskName="{ record }">
            <div class="task-name">
              <a @click="viewTask(record)">{{ record.name }}</a>
              <div class="task-process">
                <a-tag color="blue" size="small">
                  {{ record.processDefinitionName }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template #status="{ record }">
            <a-badge 
              :status="getStatusBadge(record.status)" 
              :text="getStatusText(record.status)"
            />
          </template>

          <!-- 优先级列 -->
          <template #priority="{ record }">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>

          <!-- 截止时间列 -->
          <template #dueDate="{ record }">
            <span :class="{ 'overdue': isOverdue(record.dueDate) }">
              {{ record.dueDate || '-' }}
            </span>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewTask(record)">
                查看
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(e) => handleAction(e.key, record)">
                    <a-menu-item 
                      key="claim" 
                      :disabled="record.assignee || record.status !== 1"
                    >
                      <user-outlined />
                      认领
                    </a-menu-item>
                    <a-menu-item 
                      key="complete" 
                      :disabled="record.status !== 1 && record.status !== 2"
                    >
                      <check-circle-outlined />
                      完成
                    </a-menu-item>
                    <a-menu-item 
                      key="delegate" 
                      :disabled="!record.assignee"
                    >
                      <team-outlined />
                      委派
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="history">
                      <history-outlined />
                      历史记录
                    </a-menu-item>
                    <a-menu-item key="form">
                      <form-outlined />
                      表单数据
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 任务详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="任务详情"
      width="80%"
      :footer="null"
    >
      <div v-if="selectedTask" class="task-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="任务ID">
            {{ selectedTask.id }}
          </a-descriptions-item>
          <a-descriptions-item label="任务名称">
            {{ selectedTask.name }}
          </a-descriptions-item>
          <a-descriptions-item label="流程实例">
            {{ selectedTask.processInstanceId }}
          </a-descriptions-item>
          <a-descriptions-item label="流程名称">
            {{ selectedTask.processDefinitionName }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge 
              :status="getStatusBadge(selectedTask.status)" 
              :text="getStatusText(selectedTask.status)"
            />
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(selectedTask.priority)">
              {{ getPriorityText(selectedTask.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理人">
            {{ selectedTask.assignee || '未分配' }}
          </a-descriptions-item>
          <a-descriptions-item label="候选用户">
            {{ selectedTask.candidateUsers?.join(', ') || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ selectedTask.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="截止时间">
            <span :class="{ 'overdue': isOverdue(selectedTask.dueDate) }">
              {{ selectedTask.dueDate || '-' }}
            </span>
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>任务描述</a-divider>
        <p>{{ selectedTask.description || '暂无描述' }}</p>
        
        <a-divider>表单数据</a-divider>
        <a-table
          :columns="formDataColumns"
          :data-source="getFormDataList(selectedTask.formData)"
          :pagination="false"
          size="small"
        />
        
        <div class="task-actions" style="margin-top: 24px;">
          <a-space>
            <a-button 
              type="primary" 
              :disabled="selectedTask.status !== 1 && selectedTask.status !== 2"
              @click="completeTask(selectedTask)"
            >
              完成任务
            </a-button>
            <a-button 
              :disabled="selectedTask.assignee || selectedTask.status !== 1"
              @click="claimTask(selectedTask)"
            >
              认领任务
            </a-button>
            <a-button 
              :disabled="!selectedTask.assignee"
              @click="delegateTask(selectedTask)"
            >
              委派任务
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 委派任务模态框 -->
    <a-modal
      v-model:open="delegateModalVisible"
      title="委派任务"
      width="400px"
      @ok="handleDelegateSubmit"
      :confirm-loading="delegating"
    >
      <a-form :model="delegateForm" layout="vertical">
        <a-form-item label="委派给" required>
          <a-select 
            v-model:value="delegateForm.assignee" 
            placeholder="请选择委派对象"
            show-search
          >
            <a-select-option value="周海军">周海军</a-select-option>
            <a-select-option value="王东">王东</a-select-option>
            <a-select-option value="杜佳佳">杜佳佳</a-select-option>
            <a-select-option value="孙建">孙建</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="委派说明">
          <a-textarea 
            v-model:value="delegateForm.comment" 
            placeholder="请输入委派说明"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  ReloadOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DownOutlined,
  UserOutlined,
  TeamOutlined,
  HistoryOutlined,
  FormOutlined
} from '@ant-design/icons-vue'
import type { Task } from '@/types/workflow'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const delegating = ref(false)

// 模态框状态
const detailModalVisible = ref(false)
const delegateModalVisible = ref(false)

// 统计数据
const stats = reactive({
  pending: 15,
  running: 8,
  completed: 142,
  overdue: 3
})

// 筛选表单
const filterForm = reactive({
  taskName: '',
  status: undefined as number | undefined,
  assignee: '',
  priority: undefined as number | undefined
})

// 委派表单
const delegateForm = reactive({
  assignee: '',
  comment: ''
})

// 任务列表
const taskList = ref<Task[]>([])

// 选中的任务
const selectedTask = ref<Task | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '任务ID',
    dataIndex: 'id',
    key: 'id',
    width: 120,
    ellipsis: true
  },
  {
    title: '任务名称',
    key: 'taskName',
    slots: { customRender: 'taskName' },
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    slots: { customRender: 'priority' },
    width: 80
  },
  {
    title: '处理人',
    dataIndex: 'assignee',
    key: 'assignee',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
    width: 150
  },
  {
    title: '截止时间',
    key: 'dueDate',
    slots: { customRender: 'dueDate' },
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 表单数据表格列
const formDataColumns: TableColumnsType = [
  {
    title: '字段名',
    dataIndex: 'key',
    key: 'key'
  },
  {
    title: '字段值',
    dataIndex: 'value',
    key: 'value'
  }
]

// 工具方法
const getStatusBadge = (status: number) => {
  const badges = {
    1: 'warning',    // 待处理
    2: 'processing', // 进行中
    3: 'success',    // 已完成
    4: 'error',      // 失败
    5: 'default'     // 跳过
  }
  return badges[status as keyof typeof badges] || 'default'
}

const getStatusText = (status: number) => {
  const texts = {
    1: '待处理',
    2: '进行中',
    3: '已完成',
    4: '失败',
    5: '跳过'
  }
  return texts[status as keyof typeof texts] || '未知'
}

const getPriorityColor = (priority: number) => {
  const colors = {
    1: 'default',  // 低
    2: 'blue',     // 中
    3: 'orange',   // 高
    4: 'red'       // 紧急
  }
  return colors[priority as keyof typeof colors] || 'default'
}

const getPriorityText = (priority: number) => {
  const texts = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急'
  }
  return texts[priority as keyof typeof texts] || '未知'
}

const isOverdue = (dueDate?: string) => {
  if (!dueDate) return false
  return dayjs(dueDate).isBefore(dayjs())
}

const getFormDataList = (formData?: Record<string, any>) => {
  if (!formData) return []

  return Object.entries(formData).map(([key, value]) => ({
    key,
    value: typeof value === 'object' ? JSON.stringify(value) : String(value)
  }))
}

// 数据加载
const loadTaskList = async () => {
  loading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟数据
    const mockData: Task[] = [
      {
        id: 'task_001',
        processInstanceId: 'inst_001',
        processDefinitionId: 'proc_def_001',
        processDefinitionName: '员工请假审批流程',
        nodeId: 'task_node_001',
        name: '部门经理审批',
        description: '审核员工请假申请',
        assignee: '牟文婷',
        candidateUsers: ['牟文婷', '李经理'],
        candidateGroups: ['部门经理组'],
        createTime: '2025-06-20 09:30:00',
        dueDate: '2025-06-22 18:00:00',
        priority: 2,
        status: 1,
        variables: {
          applicant: '周海军',
          leaveType: '年假',
          days: 3
        },
        formData: {
          applicant: '周海军',
          leaveType: '年假',
          startDate: '2025-06-25',
          endDate: '2025-06-27',
          reason: '家庭事务'
        }
      },
      {
        id: 'task_002',
        processInstanceId: 'inst_002',
        processDefinitionId: 'proc_def_002',
        processDefinitionName: '采购申请流程',
        nodeId: 'task_node_002',
        name: '财务审核',
        description: '审核采购申请的预算和合规性',
        assignee: undefined,
        candidateUsers: ['王会计', '李会计'],
        candidateGroups: ['财务部'],
        createTime: '2025-06-19 14:20:00',
        dueDate: '2025-06-21 17:00:00',
        priority: 3,
        status: 1,
        variables: {
          applicant: '王东',
          amount: 15000,
          supplier: 'ABC公司'
        },
        formData: {
          applicant: '王东',
          department: '技术部',
          items: '办公用品',
          amount: 15000,
          supplier: 'ABC公司'
        }
      },
      {
        id: 'task_003',
        processInstanceId: 'inst_003',
        processDefinitionId: 'proc_def_001',
        processDefinitionName: '员工请假审批流程',
        nodeId: 'task_node_003',
        name: 'HR审批',
        description: '人力资源部门最终审批',
        assignee: '赵HR',
        candidateUsers: ['赵HR'],
        candidateGroups: ['人力资源部'],
        createTime: '2025-06-18 16:45:00',
        dueDate: '2025-06-19 18:00:00',
        priority: 4,
        status: 1,
        variables: {
          applicant: '杜佳佳',
          leaveType: '病假',
          days: 5
        },
        formData: {
          applicant: '杜佳佳',
          leaveType: '病假',
          startDate: '2025-06-22',
          endDate: '2025-06-26',
          reason: '身体不适',
          medicalCertificate: true
        }
      }
    ]

    taskList.value = mockData
    pagination.total = mockData.length

    // 更新统计数据
    stats.pending = mockData.filter(t => t.status === 1).length
    stats.running = mockData.filter(t => t.status === 2).length
    stats.completed = mockData.filter(t => t.status === 3).length
    stats.overdue = mockData.filter(t => t.status === 1 && isOverdue(t.dueDate)).length

  } catch (error) {
    message.error('加载任务列表失败')
    console.error('Load task list error:', error)
  } finally {
    loading.value = false
  }
}

// 筛选和搜索
const handleFilter = () => {
  pagination.current = 1
  loadTaskList()
}

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.current = pag?.current || 1
  pagination.pageSize = pag?.pageSize || 10
  loadTaskList()
}

// 刷新数据
const refreshData = () => {
  loadTaskList()
}

// 任务操作
const viewTask = (task: Task) => {
  selectedTask.value = task
  detailModalVisible.value = true
}

const handleAction = (action: string, task: Task) => {
  switch (action) {
    case 'claim':
      claimTask(task)
      break
    case 'complete':
      completeTask(task)
      break
    case 'delegate':
      showDelegateModal(task)
      break
    case 'history':
      message.info(`查看任务历史: ${task.id}`)
      break
    case 'form':
      viewTask(task)
      break
  }
}

const claimTask = async (task: Task) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    task.assignee = '当前用户'
    task.status = 2

    message.success('任务认领成功')
    refreshData()
  } catch (error) {
    message.error('任务认领失败')
    console.error('Claim task error:', error)
  }
}

const completeTask = async (task: Task) => {
  Modal.confirm({
    title: '确认完成',
    content: `确定要完成任务"${task.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        task.status = 3

        message.success('任务完成成功')
        refreshData()
      } catch (error) {
        message.error('任务完成失败')
        console.error('Complete task error:', error)
      }
    }
  })
}

const showDelegateModal = (task: Task) => {
  selectedTask.value = task
  delegateForm.assignee = ''
  delegateForm.comment = ''
  delegateModalVisible.value = true
}

const handleDelegateSubmit = async () => {
  if (!delegateForm.assignee) {
    message.warning('请选择委派对象')
    return
  }

  delegating.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (selectedTask.value) {
      selectedTask.value.assignee = delegateForm.assignee
    }

    delegateModalVisible.value = false
    message.success('任务委派成功')
    refreshData()
  } catch (error) {
    message.error('任务委派失败')
    console.error('Delegate task error:', error)
  } finally {
    delegating.value = false
  }
}

// 批量操作
const handleBatchAction = ({ key }: { key: string }) => {
  if (!selectedRowKeys.value.length) {
    message.warning('请先选择要操作的任务')
    return
  }

  switch (key) {
    case 'claim':
      batchClaim()
      break
    case 'complete':
      batchComplete()
      break
    case 'delegate':
      message.info('批量委派功能开发中...')
      break
  }
}

const batchClaim = async () => {
  Modal.confirm({
    title: '批量认领确认',
    content: `确定要认领选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        taskList.value.forEach(task => {
          if (selectedRowKeys.value.includes(task.id) && !task.assignee) {
            task.assignee = '当前用户'
            task.status = 2
          }
        })

        selectedRowKeys.value = []
        message.success('批量认领成功')
        refreshData()
      } catch (error) {
        message.error('批量认领失败')
        console.error('Batch claim error:', error)
      }
    }
  })
}

const batchComplete = async () => {
  Modal.confirm({
    title: '批量完成确认',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个任务吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        taskList.value.forEach(task => {
          if (selectedRowKeys.value.includes(task.id) && (task.status === 1 || task.status === 2)) {
            task.status = 3
          }
        })

        selectedRowKeys.value = []
        message.success('批量完成成功')
        refreshData()
      } catch (error) {
        message.error('批量完成失败')
        console.error('Batch complete error:', error)
      }
    }
  })
}

// 导出功能
const exportTasks = () => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要导出的任务')
    return
  }

  const selectedTasks = taskList.value.filter(
    task => selectedRowKeys.value.includes(task.id)
  )

  const dataStr = JSON.stringify(selectedTasks, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `tasks_${dayjs().format('YYYYMMDD_HHmmss')}.json`
  link.click()

  message.success(`已导出 ${selectedTasks.length} 个任务`)
}

// 生命周期
onMounted(() => {
  loadTaskList()
})
</script>

<style scoped>
.task-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.task-stats {
  margin-bottom: 24px;
}

.task-stats .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 24px;
}

.filter-form {
  margin-bottom: 0;
}

.task-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.task-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-name a {
  font-weight: 500;
  color: #1890ff;
}

.task-name a:hover {
  color: #40a9ff;
}

.task-process {
  font-size: 12px;
}

.task-detail {
  padding: 16px 0;
}

.task-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.overdue {
  color: #ff4d4f;
  font-weight: 500;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-row-selected > td) {
  background: #e6f7ff;
}

/* 状态标签样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 操作按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #333;
}

:deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .task-manage {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    justify-content: space-between;
  }

  :deep(.ant-btn) {
    flex: 1;
  }

  /* 表格在移动端的优化 */
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }
}

/* 动画效果 */
.task-manage {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .task-manage {
    background: #1f1f1f;
  }

  .page-header,
  .task-list {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #404040;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr > td) {
    background: #2f2f2f;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #404040;
  }

  :deep(.ant-descriptions-item-label) {
    color: #fff;
  }

  :deep(.ant-descriptions-item-content) {
    color: #ccc;
  }
}
</style>
