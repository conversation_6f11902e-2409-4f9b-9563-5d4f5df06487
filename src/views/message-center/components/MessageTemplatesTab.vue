<template>
  <div class="message-templates-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索模板名称或内容"
            style="width: 300px"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
          <a-select
            v-model:value="typeFilter"
            placeholder="消息类型"
            style="width: 120px"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option value="system">系统消息</a-select-option>
            <a-select-option value="notice">通知公告</a-select-option>
            <a-select-option value="task">任务消息</a-select-option>
            <a-select-option value="reminder">提醒消息</a-select-option>
          </a-select>
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-button @click="$emit('create')">
            <template #icon><plus-outlined /></template>
            新建模板
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 模板列表 -->
    <div class="templates-list">
      <a-table
        :columns="columns"
        :data-source="filteredTemplates"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 模板名称列 -->
        <template #name="{ record }">
          <div class="template-name">
            <div class="name-content">
              <span class="template-title">{{ record.name }}</span>
              <div class="template-meta">
                <a-tag :color="getTypeColor(record.type)" size="small">
                  {{ getTypeLabel(record.type) }}
                </a-tag>
                <span class="created-by">创建者: {{ record.createdBy }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 模板内容列 -->
        <template #content="{ record }">
          <div class="template-content">
            <div class="content-preview">
              {{ record.content.substring(0, 100) }}
              <span v-if="record.content.length > 100">...</span>
            </div>
            <div class="variables-info" v-if="record.variables?.length">
              <a-tag
                v-for="variable in record.variables.slice(0, 3)"
                :key="variable.name"
                color="blue"
                size="small"
              >
                {{ variable.label }}
              </a-tag>
              <a-tag v-if="record.variables.length > 3" color="default" size="small">
                +{{ record.variables.length - 3 }}
              </a-tag>
            </div>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-switch
            :checked="record.isEnabled"
            :loading="record.toggling"
            @change="(checked) => handleToggle(record, checked)"
          />
        </template>

        <!-- 使用次数列 -->
        <template #usageCount="{ record }">
          <a-statistic
            :value="record.usageCount || 0"
            :value-style="{ fontSize: '14px' }"
          />
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="$emit('preview', record)">
              预览
            </a-button>
            <a-button type="link" size="small" @click="$emit('edit', record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="(e) => handleAction(e.key, record)">
                  <a-menu-item key="copy">
                    <copy-outlined />
                    复制模板
                  </a-menu-item>
                  <a-menu-item key="export">
                    <download-outlined />
                    导出模板
                  </a-menu-item>
                  <a-menu-item key="usage">
                    <bar-chart-outlined />
                    使用统计
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger>
                    <delete-outlined />
                    删除模板
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownOutlined,
  CopyOutlined,
  DownloadOutlined,
  BarChartOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { MessageTemplate, MessageType } from '@/types/message'

// Props
interface Props {
  templates: MessageTemplate[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create': []
  'edit': [template: MessageTemplate]
  'delete': [template: MessageTemplate]
  'preview': [template: MessageTemplate]
}>()

// 响应式数据
const searchKeyword = ref('')
const typeFilter = ref<MessageType | undefined>(undefined)
const statusFilter = ref<boolean | undefined>(undefined)

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '模板名称',
    key: 'name',
    slots: { customRender: 'name' },
    width: 250
  },
  {
    title: '模板内容',
    key: 'content',
    slots: { customRender: 'content' },
    width: 300
  },
  {
    title: '状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 80
  },
  {
    title: '使用次数',
    key: 'usageCount',
    slots: { customRender: 'usageCount' },
    width: 100,
    sorter: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 180,
    fixed: 'right'
  }
]

// 计算属性
const filteredTemplates = computed(() => {
  let filtered = props.templates

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(template =>
      template.name.toLowerCase().includes(keyword) ||
      template.title.toLowerCase().includes(keyword) ||
      template.content.toLowerCase().includes(keyword)
    )
  }

  // 类型筛选
  if (typeFilter.value) {
    filtered = filtered.filter(template => template.type === typeFilter.value)
  }

  // 状态筛选
  if (statusFilter.value !== undefined) {
    filtered = filtered.filter(template => template.isEnabled === statusFilter.value)
  }

  return filtered
})

// 监听筛选结果更新分页
watch(filteredTemplates, (newTemplates) => {
  pagination.value.total = newTemplates.length
}, { immediate: true })

// 工具方法
const getTypeColor = (type: MessageType) => {
  const colors = {
    system: 'blue',
    notice: 'green',
    task: 'orange',
    approval: 'purple',
    reminder: 'cyan',
    warning: 'gold',
    error: 'red'
  }
  return colors[type] || 'default'
}

const getTypeLabel = (type: MessageType) => {
  const labels = {
    system: '系统消息',
    notice: '通知公告',
    task: '任务消息',
    approval: '审批消息',
    reminder: '提醒消息',
    warning: '警告消息',
    error: '错误消息'
  }
  return labels[type] || type
}

// 事件处理
const handleSearch = () => {
  pagination.value.current = 1
}

const handleFilter = () => {
  pagination.value.current = 1
}

const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.value.current = pag?.current || 1
  pagination.value.pageSize = pag?.pageSize || 10
}

const handleToggle = async (template: MessageTemplate, checked: boolean) => {
  template.toggling = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    template.isEnabled = checked
    message.success(`模板已${checked ? '启用' : '禁用'}`)
  } catch (error) {
    message.error('状态切换失败')
    console.error('Toggle template error:', error)
  } finally {
    template.toggling = false
  }
}

const handleAction = (action: string, template: MessageTemplate) => {
  switch (action) {
    case 'copy':
      copyTemplate(template)
      break
    case 'export':
      exportTemplate(template)
      break
    case 'usage':
      viewUsageStats(template)
      break
    case 'delete':
      confirmDelete(template)
      break
  }
}

const copyTemplate = (template: MessageTemplate) => {
  message.success(`复制模板: ${template.name}`)
  // 这里可以实现复制逻辑
}

const exportTemplate = (template: MessageTemplate) => {
  const dataStr = JSON.stringify(template, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `template_${template.name}_${Date.now()}.json`
  link.click()
  
  message.success('模板导出成功')
}

const viewUsageStats = (template: MessageTemplate) => {
  message.info(`查看使用统计: ${template.name}`)
  // 这里可以打开统计页面
}

const confirmDelete = (template: MessageTemplate) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      emit('delete', template)
    }
  })
}

const refreshData = () => {
  // 触发父组件刷新数据
  emit('create') // 这里复用create事件来触发刷新
}
</script>

<style scoped>
.message-templates-tab {
  padding: 16px 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.templates-list {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.template-name {
  display: flex;
  flex-direction: column;
}

.name-content {
  flex: 1;
}

.template-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.template-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.created-by {
  font-size: 12px;
  color: #999;
}

.template-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.content-preview {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.variables-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 统计数字样式 */
:deep(.ant-statistic-content) {
  font-size: 14px;
  font-weight: 500;
}

/* 开关样式 */
:deep(.ant-switch) {
  min-width: 44px;
}

/* 标签样式 */
:deep(.ant-tag) {
  margin: 2px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-left :deep(.ant-space) {
    width: 100%;
    flex-direction: column;
  }
  
  .toolbar-left :deep(.ant-input),
  .toolbar-left :deep(.ant-select) {
    width: 100% !important;
  }
}
</style>
