<template>
  <div class="priority-analysis">
    <a-spin :spinning="loading">
      <div class="analysis-content">
        <!-- 优先级概览 -->
        <div class="priority-overview">
          <a-row :gutter="[16, 16]">
            <a-col
              v-for="priority in priorityData"
              :key="priority.level"
              :xs="24" :sm="12" :md="6"
            >
              <a-card size="small">
                <div class="priority-card">
                  <div class="priority-header">
                    <div class="priority-icon" :style="{ backgroundColor: priority.color }">
                      <component :is="priority.icon" />
                    </div>
                    <div class="priority-info">
                      <div class="priority-label">{{ priority.label }}</div>
                      <div class="priority-count">{{ priority.count }}</div>
                    </div>
                  </div>
                  <div class="priority-metrics">
                    <div class="metric-item">
                      <span class="metric-label">阅读率</span>
                      <span class="metric-value">{{ priority.readRate }}%</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">响应时间</span>
                      <span class="metric-value">{{ priority.responseTime }}min</span>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 优先级趋势 -->
        <div class="priority-trend">
          <a-card title="优先级消息趋势" size="small">
            <div class="trend-chart">
              <div class="chart-legend">
                <div
                  v-for="priority in priorityData"
                  :key="priority.level"
                  class="legend-item"
                >
                  <span 
                    class="legend-color"
                    :style="{ backgroundColor: priority.color }"
                  ></span>
                  <span class="legend-label">{{ priority.label }}</span>
                </div>
              </div>
              
              <div class="trend-lines">
                <div class="chart-grid">
                  <div v-for="i in 5" :key="i" class="grid-line"></div>
                </div>
                
                <div class="trend-data">
                  <div
                    v-for="(day, dayIndex) in trendDays"
                    :key="day"
                    class="day-column"
                  >
                    <div class="day-bars">
                      <div
                        v-for="priority in priorityData"
                        :key="priority.level"
                        class="priority-bar"
                        :style="{ 
                          height: `${getTrendValue(dayIndex, priority.level)}%`,
                          backgroundColor: priority.color 
                        }"
                      ></div>
                    </div>
                    <div class="day-label">{{ day }}</div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 优先级处理效率 -->
        <div class="priority-efficiency">
          <a-card title="优先级处理效率" size="small">
            <div class="efficiency-table">
              <a-table
                :columns="efficiencyColumns"
                :data-source="efficiencyData"
                :pagination="false"
                size="small"
              >
                <template #priority="{ record }">
                  <a-tag :color="record.color">
                    {{ record.label }}
                  </a-tag>
                </template>
                
                <template #efficiency="{ record }">
                  <a-progress
                    :percent="record.efficiency"
                    size="small"
                    :stroke-color="getEfficiencyColor(record.efficiency)"
                  />
                </template>
              </a-table>
            </div>
          </a-card>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TableColumnsType } from 'ant-design-vue'
import {
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  MinusCircleOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 优先级数据
const priorityData = [
  {
    level: 4,
    label: '紧急',
    count: 45,
    readRate: 98,
    responseTime: 2,
    color: '#ff4d4f',
    icon: ExclamationCircleOutlined
  },
  {
    level: 3,
    label: '高',
    count: 128,
    readRate: 92,
    responseTime: 8,
    color: '#faad14',
    icon: WarningOutlined
  },
  {
    level: 2,
    label: '普通',
    count: 856,
    readRate: 78,
    responseTime: 25,
    color: '#1890ff',
    icon: InfoCircleOutlined
  },
  {
    level: 1,
    label: '低',
    count: 234,
    readRate: 65,
    responseTime: 60,
    color: '#52c41a',
    icon: MinusCircleOutlined
  }
]

// 趋势天数
const trendDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 效率表格列
const efficiencyColumns: TableColumnsType = [
  {
    title: '优先级',
    key: 'priority',
    slots: { customRender: 'priority' }
  },
  {
    title: '总数',
    dataIndex: 'count',
    key: 'count'
  },
  {
    title: '已处理',
    dataIndex: 'processed',
    key: 'processed'
  },
  {
    title: '平均处理时间',
    dataIndex: 'avgProcessTime',
    key: 'avgProcessTime'
  },
  {
    title: '处理效率',
    key: 'efficiency',
    slots: { customRender: 'efficiency' }
  }
]

// 效率数据
const efficiencyData = computed(() => {
  return priorityData.map(priority => ({
    ...priority,
    processed: Math.floor(priority.count * 0.85),
    avgProcessTime: `${priority.responseTime}min`,
    efficiency: Math.floor(85 + Math.random() * 15)
  }))
})

// 获取趋势值
const getTrendValue = (dayIndex: number, priorityLevel: number) => {
  // 模拟数据：紧急消息在工作日较多
  const isWorkday = dayIndex < 5
  let base = Math.random() * 30
  
  if (priorityLevel === 4) { // 紧急
    base = isWorkday ? 20 + Math.random() * 40 : 5 + Math.random() * 20
  } else if (priorityLevel === 3) { // 高
    base = isWorkday ? 30 + Math.random() * 50 : 10 + Math.random() * 30
  } else if (priorityLevel === 2) { // 普通
    base = 40 + Math.random() * 60
  } else { // 低
    base = 20 + Math.random() * 40
  }
  
  return Math.floor(base)
}

// 获取效率颜色
const getEfficiencyColor = (efficiency: number) => {
  if (efficiency >= 90) return '#52c41a'
  if (efficiency >= 80) return '#1890ff'
  if (efficiency >= 70) return '#faad14'
  return '#ff4d4f'
}
</script>

<style scoped>
.priority-analysis {
  padding: 16px 0;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.priority-overview {
  margin-bottom: 8px;
}

.priority-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.priority-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.priority-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.priority-info {
  flex: 1;
}

.priority-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 2px;
}

.priority-count {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.priority-metrics {
  display: flex;
  justify-content: space-between;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-label {
  font-size: 11px;
  color: #999;
}

.metric-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.trend-chart {
  padding: 16px 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  font-size: 12px;
  color: #666;
}

.trend-lines {
  position: relative;
  height: 120px;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.grid-line {
  height: 1px;
  background: #f0f0f0;
}

.trend-data {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.day-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.day-bars {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 100px;
}

.priority-bar {
  width: 8px;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.day-label {
  font-size: 11px;
  color: #666;
}

.efficiency-table {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .priority-metrics {
    flex-direction: column;
    gap: 8px;
  }
  
  .metric-item {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .chart-legend {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .day-bars {
    height: 80px;
  }
  
  .priority-bar {
    width: 6px;
  }
  
  .day-label {
    font-size: 10px;
  }
}
</style>
