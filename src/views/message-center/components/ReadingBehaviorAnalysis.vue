<template>
  <div class="reading-behavior-analysis">
    <a-spin :spinning="loading">
      <div class="analysis-content">
        <!-- 阅读行为概览 -->
        <div class="behavior-overview">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="平均阅读时长"
                  :value="behaviorMetrics.avgReadTime"
                  suffix="秒"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="完整阅读率"
                  :value="behaviorMetrics.fullReadRate"
                  suffix="%"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="快速浏览率"
                  :value="behaviorMetrics.quickScanRate"
                  suffix="%"
                  :value-style="{ color: '#faad14' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="重复阅读率"
                  :value="behaviorMetrics.rereadRate"
                  suffix="%"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 阅读时长分布 -->
        <div class="reading-duration">
          <a-card title="阅读时长分布" size="small">
            <div class="duration-chart">
              <div
                v-for="item in durationData"
                :key="item.range"
                class="duration-bar"
              >
                <div class="bar-container">
                  <div 
                    class="bar-fill"
                    :style="{ 
                      height: `${(item.count / maxDurationCount) * 100}%`,
                      backgroundColor: item.color 
                    }"
                  ></div>
                </div>
                <div class="bar-label">{{ item.range }}</div>
                <div class="bar-count">{{ item.count }}</div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 阅读设备分析 -->
        <div class="device-analysis">
          <a-card title="阅读设备分析" size="small">
            <div class="device-stats">
              <div
                v-for="device in deviceData"
                :key="device.type"
                class="device-item"
              >
                <div class="device-info">
                  <component :is="device.icon" class="device-icon" />
                  <span class="device-name">{{ device.name }}</span>
                </div>
                <div class="device-metrics">
                  <div class="metric">
                    <span class="metric-label">使用率</span>
                    <span class="metric-value">{{ device.usage }}%</span>
                  </div>
                  <div class="metric">
                    <span class="metric-label">平均时长</span>
                    <span class="metric-value">{{ device.avgTime }}s</span>
                  </div>
                </div>
                <div class="device-progress">
                  <a-progress
                    :percent="device.usage"
                    :stroke-color="device.color"
                    size="small"
                    :show-info="false"
                  />
                </div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 阅读时间分布 -->
        <div class="time-distribution">
          <a-card title="阅读时间分布" size="small">
            <div class="time-heatmap">
              <div class="heatmap-header">
                <div class="hour-labels">
                  <span v-for="hour in 24" :key="hour" class="hour-label">
                    {{ String(hour - 1).padStart(2, '0') }}
                  </span>
                </div>
              </div>
              <div class="heatmap-body">
                <div
                  v-for="(day, dayIndex) in weekDays"
                  :key="day"
                  class="heatmap-row"
                >
                  <div class="day-label">{{ day }}</div>
                  <div class="hour-cells">
                    <div
                      v-for="hour in 24"
                      :key="hour"
                      class="hour-cell"
                      :style="{ 
                        backgroundColor: getHeatmapColor(getHeatmapValue(dayIndex, hour - 1))
                      }"
                      :title="`${day} ${String(hour - 1).padStart(2, '0')}:00 - ${getHeatmapValue(dayIndex, hour - 1)} 次阅读`"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="heatmap-legend">
                <span class="legend-label">少</span>
                <div class="legend-colors">
                  <div
                    v-for="level in 5"
                    :key="level"
                    class="legend-color"
                    :style="{ backgroundColor: getHeatmapColor(level * 20) }"
                  ></div>
                </div>
                <span class="legend-label">多</span>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  DesktopOutlined,
  MobileOutlined,
  TabletOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 行为指标
const behaviorMetrics = {
  avgReadTime: 45,
  fullReadRate: 68,
  quickScanRate: 25,
  rereadRate: 12
}

// 阅读时长分布数据
const durationData = [
  { range: '0-10s', count: 120, color: '#ff4d4f' },
  { range: '10-30s', count: 280, color: '#faad14' },
  { range: '30-60s', count: 450, color: '#52c41a' },
  { range: '1-2m', count: 320, color: '#1890ff' },
  { range: '2-5m', count: 180, color: '#722ed1' },
  { range: '5m+', count: 80, color: '#13c2c2' }
]

const maxDurationCount = computed(() => {
  return Math.max(...durationData.map(item => item.count))
})

// 设备数据
const deviceData = [
  {
    type: 'desktop',
    name: '桌面端',
    icon: DesktopOutlined,
    usage: 65,
    avgTime: 52,
    color: '#1890ff'
  },
  {
    type: 'mobile',
    name: '移动端',
    icon: MobileOutlined,
    usage: 30,
    avgTime: 38,
    color: '#52c41a'
  },
  {
    type: 'tablet',
    name: '平板端',
    icon: TabletOutlined,
    usage: 5,
    avgTime: 45,
    color: '#faad14'
  }
]

// 星期标签
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 获取热力图数值
const getHeatmapValue = (day: number, hour: number) => {
  // 模拟数据：工作日白天阅读量较高
  const isWorkday = day < 5
  const isWorkHour = hour >= 9 && hour <= 18
  
  let base = Math.random() * 20
  if (isWorkday && isWorkHour) {
    base += Math.random() * 60
  } else if (isWorkday) {
    base += Math.random() * 30
  } else {
    base += Math.random() * 40
  }
  
  return Math.floor(base)
}

// 获取热力图颜色
const getHeatmapColor = (value: number) => {
  if (value === 0) return '#ebedf0'
  if (value < 20) return '#c6e48b'
  if (value < 40) return '#7bc96f'
  if (value < 60) return '#239a3b'
  return '#196127'
}
</script>

<style scoped>
.reading-behavior-analysis {
  padding: 16px 0;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.behavior-overview {
  margin-bottom: 8px;
}

.duration-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 120px;
  padding: 16px 0;
}

.duration-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bar-container {
  height: 80px;
  width: 20px;
  background: #f0f0f0;
  border-radius: 2px;
  display: flex;
  align-items: flex-end;
  overflow: hidden;
}

.bar-fill {
  width: 100%;
  border-radius: 2px 2px 0 0;
  transition: height 0.3s ease;
}

.bar-label {
  font-size: 11px;
  color: #666;
}

.bar-count {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.device-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.device-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 80px;
}

.device-icon {
  font-size: 16px;
  color: #1890ff;
}

.device-name {
  font-size: 13px;
  color: #333;
}

.device-metrics {
  display: flex;
  gap: 16px;
  min-width: 120px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-label {
  font-size: 11px;
  color: #999;
}

.metric-value {
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.device-progress {
  flex: 1;
}

.time-heatmap {
  padding: 16px 0;
}

.heatmap-header {
  margin-bottom: 8px;
}

.hour-labels {
  display: flex;
  margin-left: 40px;
}

.hour-label {
  flex: 1;
  text-align: center;
  font-size: 10px;
  color: #999;
}

.heatmap-body {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.heatmap-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.day-label {
  width: 32px;
  font-size: 11px;
  color: #666;
  text-align: right;
}

.hour-cells {
  display: flex;
  gap: 2px;
  flex: 1;
}

.hour-cell {
  flex: 1;
  height: 12px;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hour-cell:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.heatmap-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.legend-label {
  font-size: 11px;
  color: #999;
}

.legend-colors {
  display: flex;
  gap: 2px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .duration-chart {
    height: 100px;
    padding: 12px 0;
  }
  
  .bar-container {
    height: 60px;
    width: 16px;
  }
  
  .device-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .device-metrics {
    width: 100%;
    justify-content: space-around;
  }
  
  .hour-label {
    font-size: 9px;
  }
  
  .day-label {
    width: 28px;
    font-size: 10px;
  }
  
  .hour-cell {
    height: 10px;
  }
}
</style>
