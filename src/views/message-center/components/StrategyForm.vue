<template>
  <div class="strategy-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="策略名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入策略名称" />
      </a-form-item>
      
      <a-form-item label="策略描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入策略描述"
          :rows="3"
        />
      </a-form-item>
      
      <a-form-item label="推送渠道" name="channels" required>
        <a-checkbox-group v-model:value="formData.channels">
          <a-checkbox value="system">系统内推送</a-checkbox>
          <a-checkbox value="sms">短信推送</a-checkbox>
          <a-checkbox value="ykz">渝快政</a-checkbox>
          <a-checkbox value="email">邮件推送</a-checkbox>
        </a-checkbox-group>
      </a-form-item>
      
      <a-form-item label="推送对象" name="targets" required>
        <a-select
          v-model:value="formData.targets"
          mode="multiple"
          placeholder="请选择推送对象"
          style="width: 100%"
        >
          <a-select-option value="all">全体用户</a-select-option>
          <a-select-option value="admin">管理员</a-select-option>
          <a-select-option value="user">普通用户</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="启用状态" name="isEnabled">
        <a-switch v-model:checked="formData.isEnabled" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { PushStrategy } from '@/types/message'

// Props
interface Props {
  strategy?: PushStrategy | null
}

const props = defineProps<Props>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  channels: [] as string[],
  targets: [] as string[],
  isEnabled: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入策略名称', trigger: 'blur' }
  ],
  channels: [
    { required: true, message: '请选择推送渠道', trigger: 'change' }
  ],
  targets: [
    { required: true, message: '请选择推送对象', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.strategy, (newStrategy) => {
  if (newStrategy) {
    formData.name = newStrategy.name
    formData.description = newStrategy.description || ''
    formData.channels = [...newStrategy.channels]
    formData.targets = [...newStrategy.targets]
    formData.isEnabled = newStrategy.isEnabled
  } else {
    // 重置表单
    formData.name = ''
    formData.description = ''
    formData.channels = []
    formData.targets = []
    formData.isEnabled = true
  }
}, { immediate: true })

// 表单验证
const validate = async () => {
  return await formRef.value.validate()
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 暴露方法
defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.strategy-form {
  padding: 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
