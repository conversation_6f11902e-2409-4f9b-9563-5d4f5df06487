<template>
  <div class="push-strategies-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索策略名称或描述"
            style="width: 300px"
            allow-clear
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-button @click="$emit('create')">
            <template #icon><plus-outlined /></template>
            新建策略
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 策略列表 -->
    <div class="strategies-list">
      <a-empty v-if="!strategies.length && !loading" description="暂无推送策略">
        <a-button type="primary" @click="$emit('create')">创建第一个策略</a-button>
      </a-empty>
      
      <a-spin :spinning="loading" v-else>
        <div class="strategies-grid">
          <div
            v-for="strategy in filteredStrategies"
            :key="strategy.id"
            class="strategy-card"
          >
            <div class="card-header">
              <h4>{{ strategy.name }}</h4>
              <a-switch
                :checked="strategy.isEnabled"
                size="small"
                @change="(checked) => handleToggle(strategy, checked)"
              />
            </div>
            <div class="card-content">
              <p class="description">{{ strategy.description || '暂无描述' }}</p>
              <div class="strategy-info">
                <div class="info-item">
                  <span class="label">推送渠道:</span>
                  <div class="channels">
                    <a-tag
                      v-for="channel in strategy.channels.slice(0, 3)"
                      :key="channel"
                      size="small"
                      color="blue"
                    >
                      {{ getChannelLabel(channel) }}
                    </a-tag>
                    <a-tag v-if="strategy.channels.length > 3" size="small">
                      +{{ strategy.channels.length - 3 }}
                    </a-tag>
                  </div>
                </div>
                <div class="info-item">
                  <span class="label">推送对象:</span>
                  <span class="value">{{ strategy.targets.length }} 个</span>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <a-space>
                <a-button type="link" size="small" @click="$emit('edit', strategy)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="$emit('test', strategy)">
                  测试
                </a-button>
                <a-button type="link" size="small" danger @click="$emit('delete', strategy)">
                  删除
                </a-button>
              </a-space>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import type { PushStrategy, PushChannel } from '@/types/message'

// Props
interface Props {
  strategies: PushStrategy[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create': []
  'edit': [strategy: PushStrategy]
  'delete': [strategy: PushStrategy]
  'test': [strategy: PushStrategy]
}>()

// 响应式数据
const searchKeyword = ref('')

// 计算属性
const filteredStrategies = computed(() => {
  let filtered = props.strategies

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(strategy =>
      strategy.name.toLowerCase().includes(keyword) ||
      strategy.description?.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 工具方法
const getChannelLabel = (channel: PushChannel) => {
  const labels = {
    system: '系统内',
    sms: '短信',
    ykz: '渝快政',
    ykb: '渝快办',
    email: '邮件',
    wechat: '微信'
  }
  return labels[channel] || channel
}

const handleToggle = async (strategy: PushStrategy, checked: boolean) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    strategy.isEnabled = checked
    message.success(`策略已${checked ? '启用' : '禁用'}`)
  } catch (error) {
    message.error('状态切换失败')
    console.error('Toggle strategy error:', error)
  }
}

const refreshData = () => {
  emit('create') // 复用create事件来触发刷新
}
</script>

<style scoped>
.push-strategies-tab {
  padding: 16px 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.strategies-list {
  min-height: 300px;
}

.strategies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.strategy-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 16px;
  transition: all 0.3s ease;
}

.strategy-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.card-content {
  margin-bottom: 16px;
}

.description {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.strategy-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-size: 13px;
  color: #999;
  min-width: 60px;
}

.value {
  font-size: 13px;
  color: #333;
}

.channels {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.card-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .strategies-grid {
    grid-template-columns: 1fr;
  }
}
</style>
