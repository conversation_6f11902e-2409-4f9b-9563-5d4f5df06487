<template>
  <div class="rule-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="规则名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入规则名称" />
      </a-form-item>
      
      <a-form-item label="规则描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入规则描述"
          :rows="3"
        />
      </a-form-item>
      
      <a-form-item label="优先级" name="priority" required>
        <a-select v-model:value="formData.priority" placeholder="请选择优先级">
          <a-select-option :value="1">高优先级</a-select-option>
          <a-select-option :value="2">中优先级</a-select-option>
          <a-select-option :value="3">低优先级</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="触发条件" required>
        <div class="conditions-editor">
          <div
            v-for="(condition, index) in formData.conditions"
            :key="index"
            class="condition-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="6">
                <a-select v-model:value="condition.field" placeholder="字段">
                  <a-select-option value="type">消息类型</a-select-option>
                  <a-select-option value="priority">优先级</a-select-option>
                  <a-select-option value="category">分类</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="6">
                <a-select v-model:value="condition.operator" placeholder="操作符">
                  <a-select-option value="equals">等于</a-select-option>
                  <a-select-option value="not_equals">不等于</a-select-option>
                  <a-select-option value="contains">包含</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="8">
                <a-input v-model:value="condition.value" placeholder="值" />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  danger
                  @click="removeCondition(index)"
                  :disabled="formData.conditions.length <= 1"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button type="dashed" block @click="addCondition">
            <template #icon><plus-outlined /></template>
            添加条件
          </a-button>
        </div>
      </a-form-item>
      
      <a-form-item label="执行动作" required>
        <div class="actions-editor">
          <div
            v-for="(action, index) in formData.actions"
            :key="index"
            class="action-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="8">
                <a-select v-model:value="action.type" placeholder="动作类型">
                  <a-select-option value="push">推送消息</a-select-option>
                  <a-select-option value="email">发送邮件</a-select-option>
                  <a-select-option value="sms">发送短信</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="12">
                <a-input v-model:value="action.config.description" placeholder="动作描述" />
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  danger
                  @click="removeAction(index)"
                  :disabled="formData.actions.length <= 1"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button type="dashed" block @click="addAction">
            <template #icon><plus-outlined /></template>
            添加动作
          </a-button>
        </div>
      </a-form-item>
      
      <a-form-item label="启用状态" name="isEnabled">
        <a-switch v-model:checked="formData.isEnabled" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { MessageRule } from '@/types/message'

// Props
interface Props {
  rule?: MessageRule | null
}

const props = defineProps<Props>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  priority: 2,
  conditions: [
    { field: '', operator: '', value: '' }
  ],
  actions: [
    { type: '', config: { description: '' } }
  ],
  isEnabled: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.rule, (newRule) => {
  if (newRule) {
    formData.name = newRule.name
    formData.description = newRule.description || ''
    formData.priority = newRule.priority
    formData.conditions = [...newRule.conditions]
    formData.actions = [...newRule.actions]
    formData.isEnabled = newRule.isEnabled
  } else {
    // 重置表单
    formData.name = ''
    formData.description = ''
    formData.priority = 2
    formData.conditions = [{ field: '', operator: '', value: '' }]
    formData.actions = [{ type: '', config: { description: '' } }]
    formData.isEnabled = true
  }
}, { immediate: true })

// 条件管理
const addCondition = () => {
  formData.conditions.push({ field: '', operator: '', value: '' })
}

const removeCondition = (index: number) => {
  if (formData.conditions.length > 1) {
    formData.conditions.splice(index, 1)
  }
}

// 动作管理
const addAction = () => {
  formData.actions.push({ type: '', config: { description: '' } })
}

const removeAction = (index: number) => {
  if (formData.actions.length > 1) {
    formData.actions.splice(index, 1)
  }
}

// 表单验证
const validate = async () => {
  return await formRef.value.validate()
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 暴露方法
defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.rule-form {
  padding: 16px 0;
}

.conditions-editor,
.actions-editor {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.condition-item,
.action-item {
  margin-bottom: 12px;
}

.condition-item:last-child,
.action-item:last-child {
  margin-bottom: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
