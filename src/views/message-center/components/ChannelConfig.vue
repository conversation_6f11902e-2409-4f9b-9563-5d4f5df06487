<template>
  <div class="channel-config">
    <div class="config-header">
      <h4>{{ channel?.name }} 配置</h4>
      <a-tag :color="getChannelColor(channel?.id)">
        {{ channel?.id?.toUpperCase() }}
      </a-tag>
    </div>
    
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <!-- 系统内推送配置 -->
      <template v-if="channel?.id === 'system'">
        <a-form-item label="推送方式" name="pushMode">
          <a-radio-group v-model:value="formData.pushMode">
            <a-radio value="realtime">实时推送</a-radio>
            <a-radio value="batch">批量推送</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="消息保留时间" name="retentionDays">
          <a-input-number
            v-model:value="formData.retentionDays"
            :min="1"
            :max="365"
            addon-after="天"
            style="width: 200px"
          />
        </a-form-item>
      </template>
      
      <!-- 短信推送配置 -->
      <template v-else-if="channel?.id === 'sms'">
        <a-form-item label="短信网关" name="gateway" required>
          <a-select v-model:value="formData.gateway" placeholder="请选择短信网关">
            <a-select-option value="aliyun">阿里云短信</a-select-option>
            <a-select-option value="tencent">腾讯云短信</a-select-option>
            <a-select-option value="huawei">华为云短信</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="AccessKey" name="accessKey" required>
          <a-input v-model:value="formData.accessKey" placeholder="请输入AccessKey" />
        </a-form-item>
        
        <a-form-item label="SecretKey" name="secretKey" required>
          <a-input-password v-model:value="formData.secretKey" placeholder="请输入SecretKey" />
        </a-form-item>
        
        <a-form-item label="签名" name="signature" required>
          <a-input v-model:value="formData.signature" placeholder="请输入短信签名" />
        </a-form-item>
        
        <a-form-item label="模板ID" name="templateId">
          <a-input v-model:value="formData.templateId" placeholder="请输入短信模板ID" />
        </a-form-item>
      </template>
      
      <!-- 邮件推送配置 -->
      <template v-else-if="channel?.id === 'email'">
        <a-form-item label="SMTP服务器" name="smtpHost" required>
          <a-input v-model:value="formData.smtpHost" placeholder="请输入SMTP服务器地址" />
        </a-form-item>
        
        <a-form-item label="端口" name="smtpPort" required>
          <a-input-number
            v-model:value="formData.smtpPort"
            :min="1"
            :max="65535"
            placeholder="请输入端口号"
            style="width: 200px"
          />
        </a-form-item>
        
        <a-form-item label="用户名" name="username" required>
          <a-input v-model:value="formData.username" placeholder="请输入邮箱用户名" />
        </a-form-item>
        
        <a-form-item label="密码" name="password" required>
          <a-input-password v-model:value="formData.password" placeholder="请输入邮箱密码" />
        </a-form-item>
        
        <a-form-item label="发件人" name="fromEmail" required>
          <a-input v-model:value="formData.fromEmail" placeholder="请输入发件人邮箱" />
        </a-form-item>
        
        <a-form-item label="SSL加密" name="ssl">
          <a-switch v-model:checked="formData.ssl" />
        </a-form-item>
      </template>
      
      <!-- 渝快政配置 -->
      <template v-else-if="channel?.id === 'ykz'">
        <a-form-item label="API地址" name="apiUrl" required>
          <a-input v-model:value="formData.apiUrl" placeholder="请输入渝快政API地址" />
        </a-form-item>
        
        <a-form-item label="应用ID" name="appId" required>
          <a-input v-model:value="formData.appId" placeholder="请输入应用ID" />
        </a-form-item>
        
        <a-form-item label="应用密钥" name="appSecret" required>
          <a-input-password v-model:value="formData.appSecret" placeholder="请输入应用密钥" />
        </a-form-item>
        
        <a-form-item label="超时时间" name="timeout">
          <a-input-number
            v-model:value="formData.timeout"
            :min="1000"
            :max="60000"
            addon-after="毫秒"
            style="width: 200px"
          />
        </a-form-item>
      </template>
      
      <!-- 通用配置 -->
      <a-divider>通用配置</a-divider>
      
      <a-form-item label="重试次数" name="retryCount">
        <a-input-number
          v-model:value="formData.retryCount"
          :min="0"
          :max="10"
          style="width: 200px"
        />
      </a-form-item>
      
      <a-form-item label="重试间隔" name="retryInterval">
        <a-input-number
          v-model:value="formData.retryInterval"
          :min="1000"
          :max="300000"
          addon-after="毫秒"
          style="width: 200px"
        />
      </a-form-item>
      
      <a-form-item label="启用日志" name="enableLog">
        <a-switch v-model:checked="formData.enableLog" />
      </a-form-item>
    </a-form>
    
    <!-- 测试区域 -->
    <div class="test-section">
      <a-divider>连接测试</a-divider>
      <a-space>
        <a-button @click="testConnection" :loading="testing">
          测试连接
        </a-button>
        <a-button @click="sendTestMessage" :loading="testing">
          发送测试消息
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'

// Props
interface Props {
  channel?: any
}

const props = defineProps<Props>()

// 响应式数据
const testing = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  // 系统内推送
  pushMode: 'realtime',
  retentionDays: 30,
  
  // 短信配置
  gateway: '',
  accessKey: '',
  secretKey: '',
  signature: '',
  templateId: '',
  
  // 邮件配置
  smtpHost: '',
  smtpPort: 587,
  username: '',
  password: '',
  fromEmail: '',
  ssl: true,
  
  // 渝快政配置
  apiUrl: '',
  appId: '',
  appSecret: '',
  timeout: 30000,
  
  // 通用配置
  retryCount: 3,
  retryInterval: 5000,
  enableLog: true
})

// 表单验证规则
const formRules = reactive({
  gateway: [
    { required: true, message: '请选择短信网关', trigger: 'change' }
  ],
  accessKey: [
    { required: true, message: '请输入AccessKey', trigger: 'blur' }
  ],
  secretKey: [
    { required: true, message: '请输入SecretKey', trigger: 'blur' }
  ],
  signature: [
    { required: true, message: '请输入短信签名', trigger: 'blur' }
  ],
  smtpHost: [
    { required: true, message: '请输入SMTP服务器地址', trigger: 'blur' }
  ],
  smtpPort: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  fromEmail: [
    { required: true, message: '请输入发件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  apiUrl: [
    { required: true, message: '请输入API地址', trigger: 'blur' }
  ],
  appId: [
    { required: true, message: '请输入应用ID', trigger: 'blur' }
  ],
  appSecret: [
    { required: true, message: '请输入应用密钥', trigger: 'blur' }
  ]
})

// 监听props变化
watch(() => props.channel, (newChannel) => {
  if (newChannel?.config) {
    Object.assign(formData, newChannel.config)
  }
}, { immediate: true })

// 工具方法
const getChannelColor = (channelId?: string) => {
  const colors = {
    system: 'blue',
    sms: 'green',
    ykz: 'orange',
    email: 'purple'
  }
  return channelId ? colors[channelId as keyof typeof colors] || 'default' : 'default'
}

// 测试方法
const testConnection = async () => {
  testing.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success('连接测试成功')
  } catch (error) {
    message.error('连接测试失败')
    console.error('Test connection error:', error)
  } finally {
    testing.value = false
  }
}

const sendTestMessage = async () => {
  testing.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    message.success('测试消息发送成功')
  } catch (error) {
    message.error('测试消息发送失败')
    console.error('Send test message error:', error)
  } finally {
    testing.value = false
  }
}

// 表单验证
const validate = async () => {
  return await formRef.value.validate()
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 暴露方法
defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.channel-config {
  padding: 16px 0;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.config-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.test-section {
  margin-top: 24px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-divider) {
  margin: 24px 0 16px 0;
}
</style>
