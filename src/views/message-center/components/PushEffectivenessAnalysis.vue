<template>
  <div class="push-effectiveness-analysis">
    <a-spin :spinning="loading">
      <div class="analysis-content">
        <!-- 推送效果概览 -->
        <div class="effectiveness-overview">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="推送成功率"
                  :value="effectivenessMetrics.successRate"
                  suffix="%"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="到达率"
                  :value="effectivenessMetrics.deliveryRate"
                  suffix="%"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="点击率"
                  :value="effectivenessMetrics.clickRate"
                  suffix="%"
                  :value-style="{ color: '#faad14' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <a-card size="small">
                <a-statistic
                  title="转化率"
                  :value="effectivenessMetrics.conversionRate"
                  suffix="%"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 渠道效果对比 -->
        <div class="channel-comparison">
          <a-card title="渠道效果对比" size="small">
            <a-table
              :columns="comparisonColumns"
              :data-source="channelComparisonData"
              :pagination="false"
              size="small"
            >
              <template #channel="{ record }">
                <div class="channel-info">
                  <component :is="record.icon" class="channel-icon" />
                  <span class="channel-name">{{ record.name }}</span>
                </div>
              </template>
              
              <template #effectiveness="{ record }">
                <a-progress
                  :percent="record.effectiveness"
                  size="small"
                  :stroke-color="getEffectivenessColor(record.effectiveness)"
                />
              </template>
              
              <template #cost="{ record }">
                <span class="cost-value">¥{{ record.cost }}</span>
              </template>
            </a-table>
          </a-card>
        </div>

        <!-- 推送时间效果分析 -->
        <div class="time-effectiveness">
          <a-card title="推送时间效果分析" size="small">
            <div class="time-chart">
              <div class="chart-header">
                <div class="chart-legend">
                  <span class="legend-item">
                    <span class="legend-color" style="background: #1890ff;"></span>
                    推送量
                  </span>
                  <span class="legend-item">
                    <span class="legend-color" style="background: #52c41a;"></span>
                    阅读率
                  </span>
                </div>
              </div>
              
              <div class="chart-body">
                <div class="time-bars">
                  <div
                    v-for="hour in timeEffectivenessData"
                    :key="hour.time"
                    class="time-bar-group"
                  >
                    <div class="bar-container">
                      <div 
                        class="push-bar"
                        :style="{ height: `${(hour.pushCount / maxPushCount) * 80}px` }"
                        :title="`${hour.time} 推送量: ${hour.pushCount}`"
                      ></div>
                      <div 
                        class="read-bar"
                        :style="{ height: `${hour.readRate}px` }"
                        :title="`${hour.time} 阅读率: ${hour.readRate}%`"
                      ></div>
                    </div>
                    <div class="time-label">{{ hour.time }}</div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 推送效果建议 -->
        <div class="effectiveness-suggestions">
          <a-card title="优化建议" size="small">
            <div class="suggestions-list">
              <div
                v-for="suggestion in suggestions"
                :key="suggestion.id"
                class="suggestion-item"
              >
                <div class="suggestion-icon">
                  <component :is="suggestion.icon" />
                </div>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ suggestion.title }}</div>
                  <div class="suggestion-description">{{ suggestion.description }}</div>
                </div>
                <div class="suggestion-impact">
                  <a-tag :color="suggestion.impactColor">
                    {{ suggestion.impact }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TableColumnsType } from 'ant-design-vue'
import {
  BellOutlined,
  MessageOutlined,
  MobileOutlined,
  MailOutlined,
  BulbOutlined,
  ClockCircleOutlined,
  UserOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 效果指标
const effectivenessMetrics = {
  successRate: 96.8,
  deliveryRate: 94.2,
  clickRate: 23.5,
  conversionRate: 8.7
}

// 对比表格列
const comparisonColumns: TableColumnsType = [
  {
    title: '推送渠道',
    key: 'channel',
    slots: { customRender: 'channel' }
  },
  {
    title: '推送量',
    dataIndex: 'pushCount',
    key: 'pushCount',
    sorter: true
  },
  {
    title: '到达率',
    dataIndex: 'deliveryRate',
    key: 'deliveryRate',
    render: (value: number) => `${value}%`
  },
  {
    title: '阅读率',
    dataIndex: 'readRate',
    key: 'readRate',
    render: (value: number) => `${value}%`
  },
  {
    title: '综合效果',
    key: 'effectiveness',
    slots: { customRender: 'effectiveness' }
  },
  {
    title: '成本',
    key: 'cost',
    slots: { customRender: 'cost' }
  }
]

// 渠道对比数据
const channelComparisonData = [
  {
    id: 'system',
    name: '系统内推送',
    icon: BellOutlined,
    pushCount: 1248,
    deliveryRate: 99.2,
    readRate: 87.3,
    effectiveness: 92,
    cost: 0
  },
  {
    id: 'sms',
    name: '短信推送',
    icon: MessageOutlined,
    pushCount: 856,
    deliveryRate: 96.8,
    readRate: 78.5,
    effectiveness: 85,
    cost: 428
  },
  {
    id: 'ykz',
    name: '渝快政',
    icon: MobileOutlined,
    pushCount: 432,
    deliveryRate: 94.5,
    readRate: 82.1,
    effectiveness: 88,
    cost: 0
  },
  {
    id: 'email',
    name: '邮件推送',
    icon: MailOutlined,
    pushCount: 234,
    deliveryRate: 92.3,
    readRate: 65.8,
    effectiveness: 75,
    cost: 156
  }
]

// 时间效果数据
const timeEffectivenessData = [
  { time: '08:00', pushCount: 45, readRate: 65 },
  { time: '09:00', pushCount: 120, readRate: 85 },
  { time: '10:00', pushCount: 180, readRate: 88 },
  { time: '11:00', pushCount: 156, readRate: 82 },
  { time: '12:00', pushCount: 89, readRate: 70 },
  { time: '13:00', pushCount: 67, readRate: 68 },
  { time: '14:00', pushCount: 145, readRate: 86 },
  { time: '15:00', pushCount: 167, readRate: 89 },
  { time: '16:00', pushCount: 134, readRate: 84 },
  { time: '17:00', pushCount: 98, readRate: 78 },
  { time: '18:00', pushCount: 56, readRate: 72 },
  { time: '19:00', pushCount: 34, readRate: 65 }
]

const maxPushCount = computed(() => {
  return Math.max(...timeEffectivenessData.map(item => item.pushCount))
})

// 优化建议
const suggestions = [
  {
    id: 1,
    title: '优化推送时间',
    description: '建议在9:00-11:00和14:00-16:00时段推送，阅读率较高',
    icon: ClockCircleOutlined,
    impact: '高影响',
    impactColor: 'red'
  },
  {
    id: 2,
    title: '个性化推送内容',
    description: '根据用户行为和偏好定制消息内容，提升用户参与度',
    icon: UserOutlined,
    impact: '中影响',
    impactColor: 'orange'
  },
  {
    id: 3,
    title: '多渠道协同推送',
    description: '结合系统内推送和短信推送，提高消息到达率',
    icon: BarChartOutlined,
    impact: '中影响',
    impactColor: 'orange'
  },
  {
    id: 4,
    title: '优化消息模板',
    description: '使用更吸引人的标题和内容格式，提升点击率',
    icon: BulbOutlined,
    impact: '低影响',
    impactColor: 'green'
  }
]

// 工具方法
const getEffectivenessColor = (effectiveness: number) => {
  if (effectiveness >= 90) return '#52c41a'
  if (effectiveness >= 80) return '#1890ff'
  if (effectiveness >= 70) return '#faad14'
  return '#ff4d4f'
}
</script>

<style scoped>
.push-effectiveness-analysis {
  padding: 16px 0;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.effectiveness-overview {
  margin-bottom: 8px;
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-icon {
  font-size: 16px;
  color: #1890ff;
}

.channel-name {
  font-size: 13px;
  color: #333;
}

.cost-value {
  font-weight: 500;
  color: #333;
}

.time-chart {
  padding: 16px 0;
}

.chart-header {
  margin-bottom: 16px;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-body {
  height: 120px;
}

.time-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 100px;
}

.time-bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bar-container {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 80px;
}

.push-bar {
  width: 8px;
  background: #1890ff;
  border-radius: 2px 2px 0 0;
}

.read-bar {
  width: 8px;
  background: #52c41a;
  border-radius: 2px 2px 0 0;
}

.time-label {
  font-size: 10px;
  color: #666;
  transform: rotate(-45deg);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.suggestion-description {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.suggestion-impact {
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-legend {
    gap: 12px;
  }
  
  .time-bars {
    height: 80px;
  }
  
  .bar-container {
    height: 60px;
  }
  
  .push-bar,
  .read-bar {
    width: 6px;
  }
  
  .time-label {
    font-size: 9px;
  }
  
  .suggestion-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .suggestion-icon {
    align-self: flex-start;
  }
}
</style>
