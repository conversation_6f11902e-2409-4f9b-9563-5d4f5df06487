<template>
  <div class="channel-performance-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据" />
        </div>
        
        <div v-else class="chart-content">
          <div class="performance-list">
            <div
              v-for="channel in channelData"
              :key="channel.id"
              class="channel-item"
            >
              <div class="channel-header">
                <div class="channel-info">
                  <component :is="channel.icon" class="channel-icon" />
                  <span class="channel-name">{{ channel.name }}</span>
                </div>
                <div class="channel-status">
                  <a-badge
                    :status="channel.enabled ? 'success' : 'default'"
                    :text="channel.enabled ? '启用' : '禁用'"
                  />
                </div>
              </div>
              
              <div class="channel-metrics">
                <div class="metric-item">
                  <span class="metric-label">发送量</span>
                  <span class="metric-value">{{ channel.sentCount }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">成功率</span>
                  <span class="metric-value">{{ channel.successRate }}%</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">平均耗时</span>
                  <span class="metric-value">{{ channel.avgTime }}ms</span>
                </div>
              </div>
              
              <div class="channel-progress">
                <div class="progress-row">
                  <span class="progress-label">成功率</span>
                  <a-progress
                    :percent="channel.successRate"
                    :stroke-color="getSuccessRateColor(channel.successRate)"
                    size="small"
                  />
                </div>
                <div class="progress-row">
                  <span class="progress-label">性能</span>
                  <a-progress
                    :percent="channel.performance"
                    :stroke-color="getPerformanceColor(channel.performance)"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  BellOutlined,
  MessageOutlined,
  MobileOutlined,
  MailOutlined,
  WechatOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 模拟数据
const channelData = computed(() => [
  {
    id: 'system',
    name: '系统内推送',
    icon: BellOutlined,
    enabled: true,
    sentCount: 1248,
    successRate: 99.2,
    avgTime: 120,
    performance: 95
  },
  {
    id: 'sms',
    name: '短信推送',
    icon: MessageOutlined,
    enabled: true,
    sentCount: 856,
    successRate: 96.8,
    avgTime: 2500,
    performance: 88
  },
  {
    id: 'ykz',
    name: '渝快政',
    icon: MobileOutlined,
    enabled: false,
    sentCount: 0,
    successRate: 0,
    avgTime: 0,
    performance: 0
  },
  {
    id: 'email',
    name: '邮件推送',
    icon: MailOutlined,
    enabled: true,
    sentCount: 432,
    successRate: 94.5,
    avgTime: 3200,
    performance: 82
  },
  {
    id: 'wechat',
    name: '微信推送',
    icon: WechatOutlined,
    enabled: true,
    sentCount: 234,
    successRate: 97.8,
    avgTime: 1800,
    performance: 90
  }
])

// 工具方法
const getSuccessRateColor = (rate: number) => {
  if (rate >= 95) return '#52c41a'
  if (rate >= 90) return '#faad14'
  return '#ff4d4f'
}

const getPerformanceColor = (performance: number) => {
  if (performance >= 90) return '#52c41a'
  if (performance >= 80) return '#faad14'
  if (performance >= 70) return '#fa8c16'
  return '#ff4d4f'
}
</script>

<style scoped>
.channel-performance-chart {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
}

.empty-chart {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-content {
  height: 100%;
  overflow-y: auto;
}

.performance-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.channel-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.channel-icon {
  font-size: 16px;
  color: #1890ff;
}

.channel-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.channel-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #999;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.channel-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-label {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}

:deep(.ant-progress) {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .channel-item {
    padding: 12px;
  }
  
  .channel-metrics {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .metric-item {
    flex: 1;
    min-width: 80px;
  }
  
  .progress-row {
    gap: 8px;
  }
  
  .progress-label {
    min-width: 35px;
    font-size: 11px;
  }
}

/* 滚动条样式 */
.chart-content::-webkit-scrollbar {
  width: 4px;
}

.chart-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 2px;
}

.chart-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.chart-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
