<template>
  <div class="message-type-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <pie-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 模拟饼图 -->
          <div class="mock-pie-chart">
            <div class="pie-container">
              <div class="pie-chart">
                <div
                  v-for="(item, index) in mockData"
                  :key="item.type"
                  class="pie-slice"
                  :style="getPieSliceStyle(item, index)"
                ></div>
              </div>
              <div class="pie-center">
                <div class="center-value">{{ totalMessages }}</div>
                <div class="center-label">总消息</div>
              </div>
            </div>
            
            <div class="pie-legend">
              <div
                v-for="item in mockData"
                :key="item.type"
                class="legend-item"
              >
                <span 
                  class="legend-color"
                  :style="{ backgroundColor: item.color }"
                ></span>
                <span class="legend-label">{{ item.label }}</span>
                <span class="legend-value">{{ item.value }}</span>
                <span class="legend-percent">({{ item.percent }}%)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PieChartOutlined } from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 模拟数据
const mockData = computed(() => {
  const data = [
    { type: 'system', label: '系统消息', value: 450, color: '#1890ff' },
    { type: 'notice', label: '通知公告', value: 320, color: '#52c41a' },
    { type: 'task', label: '任务消息', value: 280, color: '#faad14' },
    { type: 'approval', label: '审批消息', value: 180, color: '#722ed1' },
    { type: 'reminder', label: '提醒消息', value: 120, color: '#13c2c2' },
    { type: 'warning', label: '警告消息', value: 80, color: '#fa8c16' },
    { type: 'error', label: '错误消息', value: 50, color: '#f5222d' }
  ]
  
  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  return data.map(item => ({
    ...item,
    percent: Math.round((item.value / total) * 100)
  }))
})

// 计算总消息数
const totalMessages = computed(() => {
  return mockData.value.reduce((sum, item) => sum + item.value, 0)
})

// 获取饼图切片样式
const getPieSliceStyle = (item: any, index: number) => {
  let startAngle = 0
  
  // 计算起始角度
  for (let i = 0; i < index; i++) {
    startAngle += (mockData.value[i].percent / 100) * 360
  }
  
  const angle = (item.percent / 100) * 360
  
  return {
    '--start-angle': `${startAngle}deg`,
    '--angle': `${angle}deg`,
    '--color': item.color,
    transform: `rotate(${startAngle}deg)`,
    background: `conic-gradient(${item.color} 0deg ${angle}deg, transparent ${angle}deg 360deg)`
  }
}
</script>

<style scoped>
.message-type-chart {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-chart {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-content {
  height: 100%;
}

.mock-pie-chart {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 24px;
}

.pie-container {
  position: relative;
  width: 150px;
  height: 150px;
  flex-shrink: 0;
}

.pie-chart {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  background: #f0f0f0;
}

.pie-slice {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 50% 100%);
}

.pie-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  background: white;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.center-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.center-label {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

.pie-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  color: #333;
}

.legend-value {
  font-weight: 500;
  color: #333;
  min-width: 30px;
  text-align: right;
}

.legend-percent {
  color: #999;
  min-width: 40px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mock-pie-chart {
    flex-direction: column;
    gap: 16px;
  }
  
  .pie-container {
    width: 120px;
    height: 120px;
    align-self: center;
  }
  
  .pie-center {
    width: 50px;
    height: 50px;
  }
  
  .center-value {
    font-size: 14px;
  }
  
  .center-label {
    font-size: 9px;
  }
  
  .pie-legend {
    max-height: none;
  }
}

/* 滚动条样式 */
.pie-legend::-webkit-scrollbar {
  width: 4px;
}

.pie-legend::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 2px;
}

.pie-legend::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.pie-legend::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
