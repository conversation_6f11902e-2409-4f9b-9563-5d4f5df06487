<template>
  <div class="message-trend-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <line-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 模拟图表 -->
          <div class="mock-chart">
            <div class="chart-header">
              <h4>消息发送趋势</h4>
              <div class="chart-legend">
                <span class="legend-item">
                  <span class="legend-color" style="background: #1890ff;"></span>
                  发送量
                </span>
                <span class="legend-item">
                  <span class="legend-color" style="background: #52c41a;"></span>
                  阅读量
                </span>
              </div>
            </div>
            
            <div class="chart-body">
              <div class="chart-bars">
                <div
                  v-for="(item, index) in mockData"
                  :key="index"
                  class="bar-group"
                >
                  <div class="bar-container">
                    <div 
                      class="bar sent-bar"
                      :style="{ height: `${item.sent / 100 * 80}px` }"
                    ></div>
                    <div 
                      class="bar read-bar"
                      :style="{ height: `${item.read / 100 * 80}px` }"
                    ></div>
                  </div>
                  <div class="bar-label">{{ item.date }}</div>
                </div>
              </div>
            </div>
            
            <div class="chart-summary">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-statistic
                    title="总发送量"
                    :value="totalSent"
                    :value-style="{ fontSize: '16px' }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="总阅读量"
                    :value="totalRead"
                    :value-style="{ fontSize: '16px' }"
                  />
                </a-col>
                <a-col :span="8">
                  <a-statistic
                    title="平均阅读率"
                    :value="avgReadRate"
                    suffix="%"
                    :value-style="{ fontSize: '16px' }"
                  />
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { LineChartOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// Props
interface Props {
  data: any[]
  loading: boolean
  period: string
}

const props = defineProps<Props>()

// 模拟数据
const mockData = computed(() => {
  const days = props.period === '7d' ? 7 : props.period === '30d' ? 30 : 90
  const data = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = dayjs().subtract(i, 'day')
    data.push({
      date: date.format('MM-DD'),
      sent: Math.floor(Math.random() * 100) + 50,
      read: Math.floor(Math.random() * 80) + 30
    })
  }
  
  return data
})

// 计算统计数据
const totalSent = computed(() => {
  return mockData.value.reduce((sum, item) => sum + item.sent, 0)
})

const totalRead = computed(() => {
  return mockData.value.reduce((sum, item) => sum + item.read, 0)
})

const avgReadRate = computed(() => {
  if (totalSent.value === 0) return 0
  return Math.round((totalRead.value / totalSent.value) * 100)
})

// 监听period变化
watch(() => props.period, () => {
  // 这里可以触发数据重新加载
  console.log('Period changed:', props.period)
})
</script>

<style scoped>
.message-trend-chart {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-chart {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-content {
  height: 100%;
}

.mock-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.chart-body {
  flex: 1;
  display: flex;
  align-items: flex-end;
  padding: 16px 0;
}

.chart-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  height: 120px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bar-container {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 80px;
}

.bar {
  width: 8px;
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.sent-bar {
  background: #1890ff;
}

.read-bar {
  background: #52c41a;
}

.bar-label {
  font-size: 10px;
  color: #999;
  transform: rotate(-45deg);
  white-space: nowrap;
}

.chart-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
  margin-bottom: 4px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .chart-legend {
    gap: 12px;
  }
  
  .bar-label {
    font-size: 8px;
  }
}
</style>
