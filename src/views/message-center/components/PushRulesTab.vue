<template>
  <div class="push-rules-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索规则名称或描述"
            style="width: 300px"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-button @click="$emit('create')">
            <template #icon><plus-outlined /></template>
            新建规则
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 规则列表 -->
    <div class="rules-list">
      <a-table
        :columns="columns"
        :data-source="filteredRules"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 规则名称列 -->
        <template #name="{ record }">
          <div class="rule-name">
            <div class="name-content">
              <span class="rule-title">{{ record.name }}</span>
              <div class="rule-meta">
                <a-tag v-if="record.priority === 1" color="red" size="small">高优先级</a-tag>
                <span class="created-by">创建者: {{ record.createdBy }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 条件列 -->
        <template #conditions="{ record }">
          <div class="conditions-list">
            <a-tag
              v-for="(condition, index) in record.conditions.slice(0, 2)"
              :key="index"
              color="blue"
              size="small"
            >
              {{ formatCondition(condition) }}
            </a-tag>
            <a-tag v-if="record.conditions.length > 2" color="default" size="small">
              +{{ record.conditions.length - 2 }}
            </a-tag>
          </div>
        </template>

        <!-- 动作列 -->
        <template #actions="{ record }">
          <div class="actions-list">
            <a-tag
              v-for="(action, index) in record.actions.slice(0, 2)"
              :key="index"
              color="green"
              size="small"
            >
              {{ formatAction(action) }}
            </a-tag>
            <a-tag v-if="record.actions.length > 2" color="default" size="small">
              +{{ record.actions.length - 2 }}
            </a-tag>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-switch
            :checked="record.isEnabled"
            :loading="record.toggling"
            @change="(checked) => handleToggle(record, checked)"
          />
        </template>

        <!-- 触发次数列 -->
        <template #triggerCount="{ record }">
          <a-statistic
            :value="record.triggerCount || 0"
            :value-style="{ fontSize: '14px' }"
          />
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="$emit('edit', record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="(e) => handleAction(e.key, record)">
                  <a-menu-item key="test">
                    <experiment-outlined />
                    测试规则
                  </a-menu-item>
                  <a-menu-item key="copy">
                    <copy-outlined />
                    复制规则
                  </a-menu-item>
                  <a-menu-item key="logs">
                    <file-text-outlined />
                    执行日志
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger>
                    <delete-outlined />
                    删除规则
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownOutlined,
  ExperimentOutlined,
  CopyOutlined,
  FileTextOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import type { MessageRule, MessageRuleCondition, MessageRuleAction } from '@/types/message'

// Props
interface Props {
  rules: MessageRule[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create': []
  'edit': [rule: MessageRule]
  'delete': [rule: MessageRule]
  'toggle': [rule: MessageRule]
  'test': [rule: MessageRule]
}>()

// 响应式数据
const searchKeyword = ref('')
const statusFilter = ref<boolean | undefined>(undefined)

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '规则名称',
    key: 'name',
    slots: { customRender: 'name' },
    width: 250
  },
  {
    title: '触发条件',
    key: 'conditions',
    slots: { customRender: 'conditions' },
    width: 200
  },
  {
    title: '执行动作',
    key: 'actions',
    slots: { customRender: 'actions' },
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 80
  },
  {
    title: '触发次数',
    key: 'triggerCount',
    slots: { customRender: 'triggerCount' },
    width: 100,
    sorter: true
  },
  {
    title: '最后触发',
    dataIndex: 'lastTriggeredTime',
    key: 'lastTriggeredTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 150,
    fixed: 'right'
  }
]

// 计算属性
const filteredRules = computed(() => {
  let filtered = props.rules

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(rule =>
      rule.name.toLowerCase().includes(keyword) ||
      rule.description?.toLowerCase().includes(keyword)
    )
  }

  // 状态筛选
  if (statusFilter.value !== undefined) {
    filtered = filtered.filter(rule => rule.isEnabled === statusFilter.value)
  }

  return filtered
})

// 监听筛选结果更新分页
watch(filteredRules, (newRules) => {
  pagination.value.total = newRules.length
}, { immediate: true })

// 工具方法
const formatCondition = (condition: MessageRuleCondition) => {
  const fieldLabels: Record<string, string> = {
    'type': '消息类型',
    'priority': '优先级',
    'category': '分类',
    'sender': '发送人'
  }

  const operatorLabels: Record<string, string> = {
    'equals': '等于',
    'not_equals': '不等于',
    'contains': '包含',
    'greater_than': '大于',
    'less_than': '小于'
  }

  const field = fieldLabels[condition.field] || condition.field
  const operator = operatorLabels[condition.operator] || condition.operator
  
  return `${field} ${operator} ${condition.value}`
}

const formatAction = (action: MessageRuleAction) => {
  const typeLabels: Record<string, string> = {
    'push': '推送消息',
    'email': '发送邮件',
    'sms': '发送短信',
    'webhook': '调用接口'
  }

  return typeLabels[action.type] || action.type
}

// 事件处理
const handleSearch = () => {
  pagination.value.current = 1
}

const handleFilter = () => {
  pagination.value.current = 1
}

const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.value.current = pag?.current || 1
  pagination.value.pageSize = pag?.pageSize || 10
}

const handleToggle = async (rule: MessageRule, checked: boolean) => {
  rule.toggling = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    rule.isEnabled = checked
    emit('toggle', rule)
  } catch (error) {
    message.error('状态切换失败')
    console.error('Toggle rule error:', error)
  } finally {
    rule.toggling = false
  }
}

const handleAction = (action: string, rule: MessageRule) => {
  switch (action) {
    case 'test':
      emit('test', rule)
      break
    case 'copy':
      copyRule(rule)
      break
    case 'logs':
      viewLogs(rule)
      break
    case 'delete':
      confirmDelete(rule)
      break
  }
}

const copyRule = (rule: MessageRule) => {
  message.success(`复制规则: ${rule.name}`)
  // 这里可以实现复制逻辑
}

const viewLogs = (rule: MessageRule) => {
  message.info(`查看执行日志: ${rule.name}`)
  // 这里可以打开日志查看页面
}

const confirmDelete = (rule: MessageRule) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除规则"${rule.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      emit('delete', rule)
    }
  })
}

const refreshData = () => {
  // 触发父组件刷新数据
  emit('create') // 这里复用create事件来触发刷新
}
</script>

<style scoped>
.push-rules-tab {
  padding: 16px 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rules-list {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rule-name {
  display: flex;
  flex-direction: column;
}

.name-content {
  flex: 1;
}

.rule-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.rule-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.created-by {
  font-size: 12px;
  color: #999;
}

.conditions-list,
.actions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 统计数字样式 */
:deep(.ant-statistic-content) {
  font-size: 14px;
  font-weight: 500;
}

/* 开关样式 */
:deep(.ant-switch) {
  min-width: 44px;
}

/* 标签样式 */
:deep(.ant-tag) {
  margin: 2px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-left :deep(.ant-space) {
    width: 100%;
    flex-direction: column;
  }
  
  .toolbar-left :deep(.ant-input),
  .toolbar-left :deep(.ant-select) {
    width: 100% !important;
  }
}
</style>
