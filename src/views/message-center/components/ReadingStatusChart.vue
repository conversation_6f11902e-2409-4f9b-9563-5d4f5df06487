<template>
  <div class="reading-status-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据" />
        </div>
        
        <div v-else class="chart-content">
          <div class="status-overview">
            <div
              v-for="item in statusData"
              :key="item.status"
              class="status-item"
            >
              <div class="status-icon" :style="{ backgroundColor: item.color }">
                <component :is="item.icon" />
              </div>
              <div class="status-info">
                <div class="status-label">{{ item.label }}</div>
                <div class="status-value">{{ item.value }}</div>
                <div class="status-percent">{{ item.percent }}%</div>
              </div>
            </div>
          </div>
          
          <div class="progress-bars">
            <div
              v-for="item in statusData"
              :key="item.status"
              class="progress-item"
            >
              <div class="progress-header">
                <span class="progress-label">{{ item.label }}</span>
                <span class="progress-value">{{ item.value }} ({{ item.percent }}%)</span>
              </div>
              <a-progress
                :percent="item.percent"
                :stroke-color="item.color"
                :show-info="false"
              />
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  EyeOutlined,
  EyeInvisibleOutlined,
  InboxOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 模拟数据
const statusData = computed(() => {
  const data = [
    { 
      status: 'read', 
      label: '已读', 
      value: 1089, 
      color: '#52c41a',
      icon: EyeOutlined
    },
    { 
      status: 'unread', 
      label: '未读', 
      value: 159, 
      color: '#ff4d4f',
      icon: EyeInvisibleOutlined
    },
    { 
      status: 'archived', 
      label: '已归档', 
      value: 89, 
      color: '#1890ff',
      icon: InboxOutlined
    },
    { 
      status: 'deleted', 
      label: '已删除', 
      value: 23, 
      color: '#d9d9d9',
      icon: DeleteOutlined
    }
  ]
  
  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  return data.map(item => ({
    ...item,
    percent: Math.round((item.value / total) * 100)
  }))
})
</script>

<style scoped>
.reading-status-chart {
  height: 300px;
}

.chart-wrapper {
  height: 100%;
}

.empty-chart {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.status-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.status-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.status-percent {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

.progress-bars {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.progress-value {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-overview {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .status-item {
    padding: 8px;
    gap: 8px;
  }
  
  .status-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .status-value {
    font-size: 14px;
  }
}
</style>
