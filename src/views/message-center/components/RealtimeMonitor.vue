<template>
  <div class="realtime-monitor">
    <div class="monitor-content">
      <div v-if="!enabled" class="monitor-disabled">
        <a-empty description="实时监控已关闭">
          <template #image>
            <monitor-outlined style="font-size: 48px; color: #d9d9d9;" />
          </template>
        </a-empty>
      </div>
      
      <div v-else class="monitor-active">
        <!-- 实时指标 -->
        <div class="realtime-metrics">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <div class="metric-card">
                <div class="metric-icon">
                  <send-outlined />
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ realtimeMetrics.currentPush }}</div>
                  <div class="metric-label">当前推送</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="8">
              <div class="metric-card">
                <div class="metric-icon">
                  <eye-outlined />
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ realtimeMetrics.currentRead }}</div>
                  <div class="metric-label">当前阅读</div>
                </div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="8">
              <div class="metric-card">
                <div class="metric-icon">
                  <user-outlined />
                </div>
                <div class="metric-info">
                  <div class="metric-value">{{ realtimeMetrics.onlineUsers }}</div>
                  <div class="metric-label">在线用户</div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 实时活动流 -->
        <div class="activity-stream">
          <div class="stream-header">
            <h4>实时活动</h4>
            <a-space>
              <a-badge :count="data.length" :overflow-count="99">
                <span>活动数量</span>
              </a-badge>
              <a-button size="small" @click="clearStream">
                清空
              </a-button>
            </a-space>
          </div>
          
          <div class="stream-content">
            <div v-if="!data.length" class="empty-stream">
              <a-empty description="暂无实时活动" />
            </div>
            
            <div v-else class="activity-list">
              <div
                v-for="activity in data.slice(0, 20)"
                :key="activity.id"
                class="activity-item"
                :class="{ 'new-activity': isNewActivity(activity) }"
              >
                <div class="activity-icon">
                  <component :is="getActivityIcon(activity.type)" />
                </div>
                <div class="activity-content">
                  <div class="activity-text">{{ activity.content }}</div>
                  <div class="activity-meta">
                    <span class="activity-type">{{ getActivityTypeLabel(activity.type) }}</span>
                    <span class="activity-time">{{ activity.timestamp }}</span>
                  </div>
                </div>
                <div class="activity-status">
                  <a-badge
                    :status="getActivityStatus(activity.type)"
                    :text="getActivityStatusText(activity.type)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时图表 -->
        <div class="realtime-chart">
          <div class="chart-header">
            <h4>实时趋势</h4>
            <a-radio-group v-model:value="chartType" size="small">
              <a-radio-button value="push">推送</a-radio-button>
              <a-radio-button value="read">阅读</a-radio-button>
              <a-radio-button value="online">在线</a-radio-button>
            </a-radio-group>
          </div>
          
          <div class="chart-content">
            <div class="trend-line">
              <div
                v-for="(point, index) in trendData"
                :key="index"
                class="trend-point"
                :style="{ 
                  left: `${(index / (trendData.length - 1)) * 100}%`,
                  bottom: `${(point / maxTrendValue) * 100}%`
                }"
              ></div>
              
              <svg class="trend-svg" viewBox="0 0 100 100" preserveAspectRatio="none">
                <polyline
                  :points="trendPoints"
                  fill="none"
                  stroke="#1890ff"
                  stroke-width="2"
                />
              </svg>
            </div>
            
            <div class="chart-labels">
              <span class="label-start">{{ chartStartTime }}</span>
              <span class="label-end">{{ chartEndTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import dayjs from 'dayjs'
import {
  MonitorOutlined,
  SendOutlined,
  EyeOutlined,
  UserOutlined,
  MessageOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  enabled: boolean
  data: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update': [data: any]
}>()

// 响应式数据
const chartType = ref('push')
const trendData = ref<number[]>([])
const maxTrendValue = ref(100)

// 实时指标
const realtimeMetrics = ref({
  currentPush: 0,
  currentRead: 0,
  onlineUsers: 0
})

// 图表时间
const chartStartTime = ref('')
const chartEndTime = ref('')

// 定时器
let metricsTimer: NodeJS.Timeout | null = null
let trendTimer: NodeJS.Timeout | null = null

// 计算属性
const trendPoints = computed(() => {
  if (trendData.value.length < 2) return ''
  
  return trendData.value
    .map((value, index) => {
      const x = (index / (trendData.value.length - 1)) * 100
      const y = 100 - (value / maxTrendValue.value) * 100
      return `${x},${y}`
    })
    .join(' ')
})

// 工具方法
const getActivityIcon = (type: string) => {
  const icons = {
    message_sent: SendOutlined,
    message_read: EyeOutlined,
    user_online: UserOutlined,
    system_alert: BellOutlined,
    push_success: CheckCircleOutlined,
    push_failed: ExclamationCircleOutlined
  }
  return icons[type as keyof typeof icons] || MessageOutlined
}

const getActivityTypeLabel = (type: string) => {
  const labels = {
    message_sent: '消息发送',
    message_read: '消息阅读',
    user_online: '用户上线',
    system_alert: '系统告警',
    push_success: '推送成功',
    push_failed: '推送失败'
  }
  return labels[type as keyof typeof labels] || type
}

const getActivityStatus = (type: string) => {
  const statuses = {
    message_sent: 'processing',
    message_read: 'success',
    user_online: 'success',
    system_alert: 'warning',
    push_success: 'success',
    push_failed: 'error'
  }
  return statuses[type as keyof typeof statuses] || 'default'
}

const getActivityStatusText = (type: string) => {
  const texts = {
    message_sent: '发送中',
    message_read: '已读',
    user_online: '在线',
    system_alert: '告警',
    push_success: '成功',
    push_failed: '失败'
  }
  return texts[type as keyof typeof texts] || '未知'
}

const isNewActivity = (activity: any) => {
  // 判断是否为新活动（最近5秒内）
  const now = dayjs()
  const activityTime = dayjs(activity.timestamp, 'HH:mm:ss')
  return now.diff(activityTime, 'second') < 5
}

const clearStream = () => {
  emit('update', { type: 'clear' })
}

// 更新实时指标
const updateMetrics = () => {
  realtimeMetrics.value = {
    currentPush: Math.floor(Math.random() * 50) + 10,
    currentRead: Math.floor(Math.random() * 80) + 20,
    onlineUsers: Math.floor(Math.random() * 200) + 800
  }
}

// 更新趋势数据
const updateTrendData = () => {
  const newValue = Math.floor(Math.random() * 100) + 20
  trendData.value.push(newValue)
  
  // 保持最近30个数据点
  if (trendData.value.length > 30) {
    trendData.value.shift()
  }
  
  // 更新最大值
  maxTrendValue.value = Math.max(...trendData.value, 100)
  
  // 更新时间标签
  chartEndTime.value = dayjs().format('HH:mm:ss')
  if (trendData.value.length >= 30) {
    chartStartTime.value = dayjs().subtract(30, 'second').format('HH:mm:ss')
  } else {
    chartStartTime.value = dayjs().subtract(trendData.value.length, 'second').format('HH:mm:ss')
  }
}

// 启动监控
const startMonitoring = () => {
  if (!props.enabled) return
  
  // 更新指标
  updateMetrics()
  metricsTimer = setInterval(updateMetrics, 2000)
  
  // 更新趋势
  updateTrendData()
  trendTimer = setInterval(updateTrendData, 1000)
}

// 停止监控
const stopMonitoring = () => {
  if (metricsTimer) {
    clearInterval(metricsTimer)
    metricsTimer = null
  }
  
  if (trendTimer) {
    clearInterval(trendTimer)
    trendTimer = null
  }
}

// 监听enabled变化
watch(() => props.enabled, (enabled) => {
  if (enabled) {
    startMonitoring()
  } else {
    stopMonitoring()
  }
})

// 生命周期
onMounted(() => {
  if (props.enabled) {
    startMonitoring()
  }
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.realtime-monitor {
  min-height: 400px;
}

.monitor-content {
  height: 100%;
}

.monitor-disabled {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.monitor-active {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.realtime-metrics {
  margin-bottom: 8px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.metric-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.metric-label {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.activity-stream {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stream-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.stream-content {
  max-height: 300px;
  overflow-y: auto;
}

.empty-stream {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.activity-item.new-activity {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  animation: newActivity 0.5s ease;
}

@keyframes newActivity {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.activity-icon {
  width: 24px;
  height: 24px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 13px;
  color: #333;
  margin-bottom: 4px;
}

.activity-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: #999;
}

.activity-status {
  flex-shrink: 0;
}

.realtime-chart {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.chart-content {
  height: 120px;
  position: relative;
}

.trend-line {
  position: relative;
  height: 100px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.trend-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.trend-point {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #1890ff;
  border-radius: 50%;
  transform: translate(-50%, 50%);
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 11px;
  color: #999;
}

/* 滚动条样式 */
.stream-content::-webkit-scrollbar {
  width: 4px;
}

.stream-content::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 2px;
}

.stream-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.stream-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-active {
    gap: 16px;
  }
  
  .metric-card {
    padding: 12px;
    gap: 8px;
  }
  
  .metric-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .metric-value {
    font-size: 16px;
  }
  
  .stream-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .activity-item {
    padding: 8px;
    gap: 8px;
  }
  
  .activity-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
</style>
