<template>
  <div class="target-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="对象名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入对象名称" />
      </a-form-item>
      
      <a-form-item label="对象类型" name="type" required>
        <a-select v-model:value="formData.type" placeholder="请选择对象类型">
          <a-select-option value="user">用户</a-select-option>
          <a-select-option value="group">用户组</a-select-option>
          <a-select-option value="role">角色</a-select-option>
          <a-select-option value="department">部门</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="目标ID" name="targetId" required>
        <a-input v-model:value="formData.targetId" placeholder="请输入目标ID" />
      </a-form-item>
      
      <a-form-item label="描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入描述"
          :rows="3"
        />
      </a-form-item>
      
      <a-form-item label="启用状态" name="isEnabled">
        <a-switch v-model:checked="formData.isEnabled" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { PushTarget } from '@/types/message'

// Props
interface Props {
  target?: PushTarget | null
}

const props = defineProps<Props>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  type: undefined,
  targetId: '',
  description: '',
  isEnabled: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入对象名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择对象类型', trigger: 'change' }
  ],
  targetId: [
    { required: true, message: '请输入目标ID', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.target, (newTarget) => {
  if (newTarget) {
    formData.name = newTarget.name
    formData.type = newTarget.type
    formData.targetId = newTarget.targetId
    formData.description = newTarget.description || ''
    formData.isEnabled = newTarget.isEnabled
  } else {
    // 重置表单
    formData.name = ''
    formData.type = undefined
    formData.targetId = ''
    formData.description = ''
    formData.isEnabled = true
  }
}, { immediate: true })

// 表单验证
const validate = async () => {
  return await formRef.value.validate()
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 暴露方法
defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.target-form {
  padding: 16px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
