<template>
  <div class="push-targets-tab">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <a-space>
          <a-input
            v-model:value="searchKeyword"
            placeholder="搜索对象名称或描述"
            style="width: 300px"
            allow-clear
            @change="handleSearch"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
          <a-select
            v-model:value="typeFilter"
            placeholder="对象类型"
            style="width: 120px"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option value="user">用户</a-select-option>
            <a-select-option value="group">用户组</a-select-option>
            <a-select-option value="role">角色</a-select-option>
            <a-select-option value="department">部门</a-select-option>
          </a-select>
          <a-select
            v-model:value="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            allow-clear
            @change="handleFilter"
          >
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-space>
          <a-button @click="$emit('create')">
            <template #icon><plus-outlined /></template>
            新建对象
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 对象列表 -->
    <div class="targets-list">
      <a-table
        :columns="columns"
        :data-source="filteredTargets"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 对象名称列 -->
        <template #name="{ record }">
          <div class="target-name">
            <div class="name-content">
              <div class="target-info">
                <component :is="getTypeIcon(record.type)" class="type-icon" />
                <span class="target-title">{{ record.name }}</span>
              </div>
              <div class="target-meta">
                <a-tag :color="getTypeColor(record.type)" size="small">
                  {{ getTypeLabel(record.type) }}
                </a-tag>
                <span class="target-id">ID: {{ record.targetId }}</span>
              </div>
            </div>
          </div>
        </template>

        <!-- 描述列 -->
        <template #description="{ record }">
          <div class="target-description">
            {{ record.description || '暂无描述' }}
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ record }">
          <a-switch
            :checked="record.isEnabled"
            :loading="record.toggling"
            @change="(checked) => handleToggle(record, checked)"
          />
        </template>

        <!-- 同步状态列 -->
        <template #syncStatus="{ record }">
          <div class="sync-status">
            <a-badge
              :status="getSyncStatusBadge(record.syncStatus)"
              :text="getSyncStatusText(record.syncStatus)"
            />
            <div class="sync-time" v-if="record.lastSyncTime">
              {{ record.lastSyncTime }}
            </div>
          </div>
        </template>

        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="$emit('edit', record)">
              编辑
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="(e) => handleAction(e.key, record)">
                  <a-menu-item key="sync">
                    <sync-outlined />
                    同步数据
                  </a-menu-item>
                  <a-menu-item key="test">
                    <experiment-outlined />
                    测试推送
                  </a-menu-item>
                  <a-menu-item key="members" :disabled="record.type === 'user'">
                    <team-outlined />
                    查看成员
                  </a-menu-item>
                  <a-menu-item key="export">
                    <download-outlined />
                    导出数据
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger>
                    <delete-outlined />
                    删除对象
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多
                <down-outlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined,
  ReloadOutlined,
  DownOutlined,
  SyncOutlined,
  ExperimentOutlined,
  TeamOutlined,
  DownloadOutlined,
  DeleteOutlined,
  UserOutlined,
  UsergroupAddOutlined,
  SafetyCertificateOutlined,
  ApartmentOutlined
} from '@ant-design/icons-vue'
import type { PushTarget } from '@/types/message'

// Props
interface Props {
  targets: PushTarget[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'create': []
  'edit': [target: PushTarget]
  'delete': [target: PushTarget]
  'sync': [target: PushTarget]
}>()

// 响应式数据
const searchKeyword = ref('')
const typeFilter = ref<string | undefined>(undefined)
const statusFilter = ref<boolean | undefined>(undefined)

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '对象名称',
    key: 'name',
    slots: { customRender: 'name' },
    width: 250
  },
  {
    title: '描述',
    key: 'description',
    slots: { customRender: 'description' },
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    slots: { customRender: 'status' },
    width: 80
  },
  {
    title: '同步状态',
    key: 'syncStatus',
    slots: { customRender: 'syncStatus' },
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    sorter: true
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 150,
    fixed: 'right'
  }
]

// 计算属性
const filteredTargets = computed(() => {
  let filtered = props.targets

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(target =>
      target.name.toLowerCase().includes(keyword) ||
      target.description?.toLowerCase().includes(keyword) ||
      target.targetId.toLowerCase().includes(keyword)
    )
  }

  // 类型筛选
  if (typeFilter.value) {
    filtered = filtered.filter(target => target.type === typeFilter.value)
  }

  // 状态筛选
  if (statusFilter.value !== undefined) {
    filtered = filtered.filter(target => target.isEnabled === statusFilter.value)
  }

  return filtered
})

// 监听筛选结果更新分页
watch(filteredTargets, (newTargets) => {
  pagination.value.total = newTargets.length
}, { immediate: true })

// 工具方法
const getTypeIcon = (type: string) => {
  const icons = {
    user: UserOutlined,
    group: UsergroupAddOutlined,
    role: SafetyCertificateOutlined,
    department: ApartmentOutlined
  }
  return icons[type as keyof typeof icons] || UserOutlined
}

const getTypeColor = (type: string) => {
  const colors = {
    user: 'blue',
    group: 'green',
    role: 'orange',
    department: 'purple'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getTypeLabel = (type: string) => {
  const labels = {
    user: '用户',
    group: '用户组',
    role: '角色',
    department: '部门'
  }
  return labels[type as keyof typeof labels] || type
}

const getSyncStatusBadge = (status?: string) => {
  const badges = {
    success: 'success',
    failed: 'error',
    syncing: 'processing',
    pending: 'warning'
  }
  return badges[status as keyof typeof badges] || 'default'
}

const getSyncStatusText = (status?: string) => {
  const texts = {
    success: '同步成功',
    failed: '同步失败',
    syncing: '同步中',
    pending: '待同步'
  }
  return texts[status as keyof typeof texts] || '未同步'
}

// 事件处理
const handleSearch = () => {
  pagination.value.current = 1
}

const handleFilter = () => {
  pagination.value.current = 1
}

const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.value.current = pag?.current || 1
  pagination.value.pageSize = pag?.pageSize || 10
}

const handleToggle = async (target: PushTarget, checked: boolean) => {
  target.toggling = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    target.isEnabled = checked
    message.success(`推送对象已${checked ? '启用' : '禁用'}`)
  } catch (error) {
    message.error('状态切换失败')
    console.error('Toggle target error:', error)
  } finally {
    target.toggling = false
  }
}

const handleAction = (action: string, target: PushTarget) => {
  switch (action) {
    case 'sync':
      emit('sync', target)
      break
    case 'test':
      testPush(target)
      break
    case 'members':
      viewMembers(target)
      break
    case 'export':
      exportTarget(target)
      break
    case 'delete':
      confirmDelete(target)
      break
  }
}

const testPush = (target: PushTarget) => {
  message.info(`测试推送: ${target.name}`)
  // 这里可以实现测试推送逻辑
}

const viewMembers = (target: PushTarget) => {
  message.info(`查看成员: ${target.name}`)
  // 这里可以打开成员列表页面
}

const exportTarget = (target: PushTarget) => {
  const dataStr = JSON.stringify(target, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `target_${target.name}_${Date.now()}.json`
  link.click()
  
  message.success('推送对象导出成功')
}

const confirmDelete = (target: PushTarget) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除推送对象"${target.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      emit('delete', target)
    }
  })
}

const refreshData = () => {
  // 触发父组件刷新数据
  emit('create') // 这里复用create事件来触发刷新
}
</script>

<style scoped>
.push-targets-tab {
  padding: 16px 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.targets-list {
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.target-name {
  display: flex;
  flex-direction: column;
}

.name-content {
  flex: 1;
}

.target-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-icon {
  font-size: 16px;
  color: #1890ff;
}

.target-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.target-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.target-id {
  font-size: 12px;
  color: #999;
}

.target-description {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.sync-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sync-time {
  font-size: 12px;
  color: #999;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

/* 开关样式 */
:deep(.ant-switch) {
  min-width: 44px;
}

/* 标签样式 */
:deep(.ant-tag) {
  margin: 2px;
  border-radius: 4px;
}

/* 状态标识样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
  }
  
  .toolbar-left :deep(.ant-space) {
    width: 100%;
    flex-direction: column;
  }
  
  .toolbar-left :deep(.ant-input),
  .toolbar-left :deep(.ant-select) {
    width: 100% !important;
  }
}
</style>
