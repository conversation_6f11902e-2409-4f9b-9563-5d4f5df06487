<template>
  <div class="user-activity-analysis">
    <a-spin :spinning="loading">
      <div class="analysis-content">
        <!-- 活跃度概览 -->
        <div class="activity-overview">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-card size="small">
                <a-statistic
                  title="活跃用户"
                  :value="activityMetrics.activeUsers"
                  :value-style="{ color: '#52c41a' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-card size="small">
                <a-statistic
                  title="平均活跃度"
                  :value="activityMetrics.avgActivity"
                  suffix="%"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-card size="small">
                <a-statistic
                  title="新增活跃"
                  :value="activityMetrics.newActive"
                  :value-style="{ color: '#faad14' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 用户活跃度排行 -->
        <div class="activity-ranking">
          <a-card title="用户活跃度排行" size="small">
            <a-table
              :columns="rankingColumns"
              :data-source="rankingData"
              :pagination="false"
              size="small"
            >
              <template #rank="{ record, index }">
                <a-badge
                  :count="index + 1"
                  :number-style="{ 
                    backgroundColor: getRankColor(index),
                    color: '#fff'
                  }"
                />
              </template>
              
              <template #activity="{ record }">
                <a-progress
                  :percent="record.activityScore"
                  size="small"
                  :stroke-color="getActivityColor(record.activityScore)"
                />
              </template>
            </a-table>
          </a-card>
        </div>

        <!-- 活跃度分布 -->
        <div class="activity-distribution">
          <a-card title="活跃度分布" size="small">
            <div class="distribution-chart">
              <div
                v-for="segment in distributionData"
                :key="segment.level"
                class="segment-item"
              >
                <div class="segment-header">
                  <span class="segment-label">{{ segment.label }}</span>
                  <span class="segment-count">{{ segment.count }}人</span>
                </div>
                <div class="segment-bar">
                  <div 
                    class="segment-fill"
                    :style="{ 
                      width: `${(segment.count / maxSegmentCount) * 100}%`,
                      backgroundColor: segment.color 
                    }"
                  ></div>
                </div>
                <div class="segment-percent">{{ segment.percent }}%</div>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { TableColumnsType } from 'ant-design-vue'

// Props
interface Props {
  data: any[]
  loading: boolean
}

const props = defineProps<Props>()

// 活跃度指标
const activityMetrics = {
  activeUsers: 1248,
  avgActivity: 73,
  newActive: 156
}

// 排行榜列定义
const rankingColumns: TableColumnsType = [
  {
    title: '排名',
    key: 'rank',
    slots: { customRender: 'rank' },
    width: 60
  },
  {
    title: '用户',
    dataIndex: 'userName',
    key: 'userName'
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department'
  },
  {
    title: '消息数',
    dataIndex: 'messageCount',
    key: 'messageCount',
    sorter: true
  },
  {
    title: '活跃度',
    key: 'activity',
    slots: { customRender: 'activity' }
  }
]

// 排行榜数据
const rankingData = [
  { userName: '周海军', department: '技术部', messageCount: 156, activityScore: 95 },
  { userName: '王东', department: '产品部', messageCount: 142, activityScore: 88 },
  { userName: '杜佳佳', department: '运营部', messageCount: 128, activityScore: 82 },
  { userName: '孙建', department: '市场部', messageCount: 115, activityScore: 78 },
  { userName: '钱七', department: '技术部', messageCount: 98, activityScore: 72 }
]

// 分布数据
const distributionData = [
  { level: 'high', label: '高活跃', count: 320, color: '#52c41a', percent: 25 },
  { level: 'medium', label: '中活跃', count: 580, color: '#1890ff', percent: 46 },
  { level: 'low', label: '低活跃', count: 280, color: '#faad14', percent: 22 },
  { level: 'inactive', label: '不活跃', count: 88, color: '#ff4d4f', percent: 7 }
]

const maxSegmentCount = computed(() => {
  return Math.max(...distributionData.map(item => item.count))
})

// 工具方法
const getRankColor = (index: number) => {
  if (index === 0) return '#ffd700'
  if (index === 1) return '#c0c0c0'
  if (index === 2) return '#cd7f32'
  return '#1890ff'
}

const getActivityColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}
</script>

<style scoped>
.user-activity-analysis {
  padding: 16px 0;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.activity-overview {
  margin-bottom: 8px;
}

.distribution-chart {
  padding: 16px 0;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.segment-item:last-child {
  margin-bottom: 0;
}

.segment-header {
  display: flex;
  flex-direction: column;
  min-width: 80px;
}

.segment-label {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.segment-count {
  font-size: 11px;
  color: #999;
}

.segment-bar {
  flex: 1;
  height: 20px;
  background: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
}

.segment-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.segment-percent {
  font-size: 12px;
  color: #666;
  min-width: 40px;
  text-align: right;
}

/* 表格样式 */
:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-badge-count) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 10px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .segment-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .segment-header {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }
  
  .segment-bar {
    height: 16px;
  }
  
  .segment-percent {
    text-align: center;
    min-width: auto;
  }
}
</style>
