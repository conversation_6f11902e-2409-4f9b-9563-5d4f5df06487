<template>
  <div class="template-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
    >
      <a-form-item label="模板名称" name="name" required>
        <a-input v-model:value="formData.name" placeholder="请输入模板名称" />
      </a-form-item>
      
      <a-form-item label="消息标题" name="title" required>
        <a-input v-model:value="formData.title" placeholder="请输入消息标题" />
      </a-form-item>
      
      <a-form-item label="消息类型" name="type" required>
        <a-select v-model:value="formData.type" placeholder="请选择消息类型">
          <a-select-option value="system">系统消息</a-select-option>
          <a-select-option value="notice">通知公告</a-select-option>
          <a-select-option value="task">任务消息</a-select-option>
          <a-select-option value="reminder">提醒消息</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="模板内容" name="content" required>
        <a-textarea 
          v-model:value="formData.content" 
          placeholder="请输入模板内容，使用{{变量名}}表示变量"
          :rows="6"
        />
        <div class="content-help">
          <a-alert
            message="变量使用说明"
            description="在内容中使用 {{变量名}} 的格式来定义变量，例如：{{userName}}、{{date}} 等"
            type="info"
            show-icon
            closable
          />
        </div>
      </a-form-item>
      
      <a-form-item label="模板变量">
        <div class="variables-editor">
          <div
            v-for="(variable, index) in formData.variables"
            :key="index"
            class="variable-item"
          >
            <a-row :gutter="8" align="middle">
              <a-col :span="6">
                <a-input v-model:value="variable.name" placeholder="变量名" />
              </a-col>
              <a-col :span="6">
                <a-input v-model:value="variable.label" placeholder="显示名称" />
              </a-col>
              <a-col :span="4">
                <a-select v-model:value="variable.type" placeholder="类型">
                  <a-select-option value="string">文本</a-select-option>
                  <a-select-option value="number">数字</a-select-option>
                  <a-select-option value="date">日期</a-select-option>
                  <a-select-option value="boolean">布尔</a-select-option>
                </a-select>
              </a-col>
              <a-col :span="4">
                <a-checkbox v-model:checked="variable.required">必填</a-checkbox>
              </a-col>
              <a-col :span="4">
                <a-button 
                  type="text" 
                  danger
                  @click="removeVariable(index)"
                >
                  <template #icon><delete-outlined /></template>
                </a-button>
              </a-col>
            </a-row>
          </div>
          
          <a-button type="dashed" block @click="addVariable">
            <template #icon><plus-outlined /></template>
            添加变量
          </a-button>
        </div>
      </a-form-item>
      
      <a-form-item label="标签">
        <a-select
          v-model:value="formData.tags"
          mode="tags"
          placeholder="请输入标签"
          style="width: 100%"
        />
      </a-form-item>
      
      <a-form-item label="启用状态" name="isEnabled">
        <a-switch v-model:checked="formData.isEnabled" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { MessageTemplate } from '@/types/message'

// Props
interface Props {
  template?: MessageTemplate | null
}

const props = defineProps<Props>()

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  title: '',
  type: undefined,
  content: '',
  variables: [] as any[],
  tags: [] as string[],
  isEnabled: true
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择消息类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入模板内容', trigger: 'blur' }
  ]
}

// 监听props变化
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    formData.name = newTemplate.name
    formData.title = newTemplate.title
    formData.type = newTemplate.type
    formData.content = newTemplate.content
    formData.variables = [...(newTemplate.variables || [])]
    formData.tags = [...(newTemplate.tags || [])]
    formData.isEnabled = newTemplate.isEnabled
  } else {
    // 重置表单
    formData.name = ''
    formData.title = ''
    formData.type = undefined
    formData.content = ''
    formData.variables = []
    formData.tags = []
    formData.isEnabled = true
  }
}, { immediate: true })

// 变量管理
const addVariable = () => {
  formData.variables.push({
    name: '',
    label: '',
    type: 'string',
    required: false
  })
}

const removeVariable = (index: number) => {
  formData.variables.splice(index, 1)
}

// 表单验证
const validate = async () => {
  return await formRef.value.validate()
}

// 获取表单数据
const getFormData = () => {
  return { ...formData }
}

// 暴露方法
defineExpose({
  validate,
  getFormData
})
</script>

<style scoped>
.template-form {
  padding: 16px 0;
}

.content-help {
  margin-top: 8px;
}

.variables-editor {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.variable-item {
  margin-bottom: 12px;
}

.variable-item:last-child {
  margin-bottom: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}
</style>
