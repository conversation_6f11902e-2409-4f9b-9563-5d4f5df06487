<template>
  <div class="push-channels-tab">
    <!-- 渠道列表 -->
    <div class="channels-list">
      <a-spin :spinning="loading">
        <div class="channels-grid">
          <div
            v-for="channel in channels"
            :key="channel.id"
            class="channel-card"
            :class="{ 'disabled': !channel.enabled }"
          >
            <div class="card-header">
              <div class="channel-info">
                <component :is="getChannelIcon(channel.id)" class="channel-icon" />
                <h4>{{ channel.name }}</h4>
              </div>
              <a-switch
                :checked="channel.enabled"
                :loading="channel.toggling"
                @change="(checked) => handleToggle(channel, checked)"
              />
            </div>
            
            <div class="card-content">
              <div class="channel-status">
                <a-badge
                  :status="getStatusBadge(channel)"
                  :text="getStatusText(channel)"
                />
              </div>
              
              <div class="channel-description">
                {{ getChannelDescription(channel.id) }}
              </div>
              
              <div class="channel-stats" v-if="channel.stats">
                <div class="stat-item">
                  <span class="stat-label">今日推送:</span>
                  <span class="stat-value">{{ channel.stats.todayCount || 0 }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功率:</span>
                  <span class="stat-value">{{ channel.stats.successRate || 0 }}%</span>
                </div>
              </div>
            </div>
            
            <div class="card-actions">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="$emit('configure', channel)"
                  :disabled="!channel.enabled"
                >
                  配置
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="$emit('test', channel)"
                  :disabled="!channel.enabled"
                >
                  测试
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="viewLogs(channel)"
                >
                  日志
                </a-button>
              </a-space>
            </div>
          </div>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import {
  BellOutlined,
  MessageOutlined,
  MobileOutlined,
  MailOutlined,
  WechatOutlined,
  GlobalOutlined
} from '@ant-design/icons-vue'

// Props
interface Props {
  channels: any[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'configure': [channel: any]
  'test': [channel: any]
  'toggle': [channel: any]
}>()

// 工具方法
const getChannelIcon = (channelId: string) => {
  const icons = {
    system: BellOutlined,
    sms: MessageOutlined,
    ykz: MobileOutlined,
    ykb: MobileOutlined,
    email: MailOutlined,
    wechat: WechatOutlined
  }
  return icons[channelId as keyof typeof icons] || GlobalOutlined
}

const getChannelDescription = (channelId: string) => {
  const descriptions = {
    system: '系统内消息推送，用户登录后可在消息中心查看',
    sms: '短信推送，通过短信网关发送到用户手机',
    ykz: '渝快政推送，重庆市政务服务平台消息推送',
    ykb: '渝快办推送，重庆市民生服务平台消息推送',
    email: '邮件推送，通过邮件服务器发送到用户邮箱',
    wechat: '微信推送，通过微信公众号或企业微信推送'
  }
  return descriptions[channelId as keyof typeof descriptions] || '第三方推送渠道'
}

const getStatusBadge = (channel: any) => {
  if (!channel.enabled) return 'default'
  if (channel.config?.configured === false) return 'warning'
  if (channel.lastError) return 'error'
  return 'success'
}

const getStatusText = (channel: any) => {
  if (!channel.enabled) return '已禁用'
  if (channel.config?.configured === false) return '未配置'
  if (channel.lastError) return '异常'
  return '正常'
}

const handleToggle = async (channel: any, checked: boolean) => {
  channel.toggling = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    channel.enabled = checked
    emit('toggle', channel)
  } catch (error) {
    message.error('状态切换失败')
    console.error('Toggle channel error:', error)
  } finally {
    channel.toggling = false
  }
}

const viewLogs = (channel: any) => {
  message.info(`查看推送日志: ${channel.name}`)
  // 这里可以打开日志查看页面
}
</script>

<style scoped>
.push-channels-tab {
  padding: 16px 0;
}

.channels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.channel-card {
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  padding: 20px;
  transition: all 0.3s ease;
}

.channel-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.channel-card.disabled {
  opacity: 0.6;
  background: #fafafa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.channel-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.channel-icon {
  font-size: 24px;
  color: #1890ff;
}

.channel-card.disabled .channel-icon {
  color: #d9d9d9;
}

.card-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.channel-card.disabled h4 {
  color: #999;
}

.card-content {
  margin-bottom: 16px;
}

.channel-status {
  margin-bottom: 12px;
}

.channel-description {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.channel-stats {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.card-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

/* 状态标识样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .channels-grid {
    grid-template-columns: 1fr;
  }
  
  .channel-stats {
    flex-direction: column;
    gap: 8px;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
  }
}
</style>
