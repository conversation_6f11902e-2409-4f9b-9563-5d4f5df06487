<template>
  <div class="template-preview">
    <div class="preview-header">
      <h4>模板预览</h4>
      <a-space>
        <a-button size="small" @click="toggleVariables">
          {{ showVariables ? '隐藏变量' : '显示变量' }}
        </a-button>
        <a-button size="small" @click="resetVariables">
          重置变量
        </a-button>
      </a-space>
    </div>
    
    <!-- 变量输入区域 -->
    <div v-if="showVariables && template?.variables?.length" class="variables-section">
      <h5>变量设置</h5>
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col
            v-for="variable in template.variables"
            :key="variable.name"
            :span="12"
          >
            <a-form-item :label="variable.label || variable.name">
              <a-input
                v-if="variable.type === 'string'"
                v-model:value="variableValues[variable.name]"
                :placeholder="`请输入${variable.label || variable.name}`"
              />
              <a-input-number
                v-else-if="variable.type === 'number'"
                v-model:value="variableValues[variable.name]"
                :placeholder="`请输入${variable.label || variable.name}`"
                style="width: 100%"
              />
              <a-date-picker
                v-else-if="variable.type === 'date'"
                v-model:value="variableValues[variable.name]"
                :placeholder="`请选择${variable.label || variable.name}`"
                style="width: 100%"
              />
              <a-switch
                v-else-if="variable.type === 'boolean'"
                v-model:checked="variableValues[variable.name]"
              />
              <a-input
                v-else
                v-model:value="variableValues[variable.name]"
                :placeholder="`请输入${variable.label || variable.name}`"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 预览区域 -->
    <div class="preview-section">
      <h5>预览效果</h5>
      <div class="preview-content">
        <div class="message-preview">
          <div class="message-header">
            <div class="message-type">
              <a-tag :color="getTypeColor(template?.type)">
                {{ getTypeLabel(template?.type) }}
              </a-tag>
            </div>
            <div class="message-time">
              {{ currentTime }}
            </div>
          </div>
          
          <div class="message-title">
            {{ renderContent(template?.title || '') }}
          </div>
          
          <div class="message-body">
            {{ renderContent(template?.content || '') }}
          </div>
          
          <div class="message-footer">
            <a-space>
              <a-button type="primary" size="small">查看详情</a-button>
              <a-button size="small">标记已读</a-button>
            </a-space>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 模板信息 -->
    <div class="template-info">
      <h5>模板信息</h5>
      <a-descriptions size="small" :column="2">
        <a-descriptions-item label="模板名称">
          {{ template?.name }}
        </a-descriptions-item>
        <a-descriptions-item label="消息类型">
          <a-tag :color="getTypeColor(template?.type)">
            {{ getTypeLabel(template?.type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="变量数量">
          {{ template?.variables?.length || 0 }}
        </a-descriptions-item>
        <a-descriptions-item label="使用次数">
          {{ template?.usageCount || 0 }}
        </a-descriptions-item>
        <a-descriptions-item label="创建时间" :span="2">
          {{ template?.createTime }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import type { MessageTemplate, MessageType } from '@/types/message'

// Props
interface Props {
  template: MessageTemplate
}

const props = defineProps<Props>()

// 响应式数据
const showVariables = ref(true)
const variableValues = reactive<Record<string, any>>({})
const currentTime = ref('')

// 计算属性
const renderedTitle = computed(() => {
  return renderContent(props.template?.title || '')
})

const renderedContent = computed(() => {
  return renderContent(props.template?.content || '')
})

// 工具方法
const getTypeColor = (type?: MessageType) => {
  const colors = {
    system: 'blue',
    notice: 'green',
    task: 'orange',
    approval: 'purple',
    reminder: 'cyan',
    warning: 'gold',
    error: 'red'
  }
  return type ? colors[type] || 'default' : 'default'
}

const getTypeLabel = (type?: MessageType) => {
  const labels = {
    system: '系统消息',
    notice: '通知公告',
    task: '任务消息',
    approval: '审批消息',
    reminder: '提醒消息',
    warning: '警告消息',
    error: '错误消息'
  }
  return type ? labels[type] || type : '未知类型'
}

const renderContent = (content: string) => {
  if (!content) return ''
  
  let rendered = content
  
  // 替换变量
  if (props.template?.variables) {
    props.template.variables.forEach(variable => {
      const value = variableValues[variable.name] || `{{${variable.name}}}`
      const regex = new RegExp(`\\{\\{${variable.name}\\}\\}`, 'g')
      rendered = rendered.replace(regex, String(value))
    })
  }
  
  return rendered
}

const toggleVariables = () => {
  showVariables.value = !showVariables.value
}

const resetVariables = () => {
  if (props.template?.variables) {
    props.template.variables.forEach(variable => {
      variableValues[variable.name] = variable.defaultValue || ''
    })
  }
}

const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 初始化
onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  
  // 初始化变量值
  if (props.template?.variables) {
    props.template.variables.forEach(variable => {
      variableValues[variable.name] = variable.defaultValue || ''
    })
  }
})
</script>

<style scoped>
.template-preview {
  padding: 16px 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.preview-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.variables-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
}

.variables-section h5 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.preview-section {
  margin-bottom: 24px;
}

.preview-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.preview-content {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
}

.message-preview {
  max-width: 400px;
  margin: 0 auto;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-title {
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.message-body {
  padding: 16px;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
}

.message-footer {
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}

.template-info h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
}
</style>
