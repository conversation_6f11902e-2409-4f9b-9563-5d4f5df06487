<template>
  <div class="message-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>消息列表</h2>
        <p>查看和管理所有消息，支持多维度筛选和批量操作</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="markAllAsRead" :disabled="!unreadCount">
            <template #icon><check-outlined /></template>
            全部已读
          </a-button>
          <a-button type="primary" @click="showSendModal">
            <template #icon><plus-outlined /></template>
            发送消息
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 高优先级消息提醒区域 -->
    <div v-if="highPriorityMessages.length > 0" class="priority-alert-section">
      <a-card class="priority-alert-card">
        <template #title>
          <div class="priority-alert-title">
            <exclamation-circle-outlined class="alert-icon pulse" />
            <span>高优先级消息提醒</span>
            <a-tag color="red" class="urgent-count">{{ highPriorityMessages.length }}</a-tag>
          </div>
        </template>
        <div class="priority-messages-list">
          <div 
            v-for="msg in highPriorityMessages.slice(0, 3)" 
            :key="msg.id"
            class="priority-message-item"
            :class="getPriorityClass(msg.priority)"
            @click="viewMessage(msg)"
          >
            <div class="priority-message-content">
              <div class="message-title-priority">
                <span class="priority-icon">{{ getPriorityIcon(msg.priority) }}</span>
                <span class="message-title-text">{{ msg.title }}</span>
                <a-tag :color="getPriorityColor(msg.priority)" size="small">
                  {{ getPriorityLabel(msg.priority) }}
                </a-tag>
              </div>
              <div class="message-meta-priority">
                <span class="sender">{{ msg.senderName }}</span>
                <span class="time">{{ msg.createTime }}</span>
              </div>
            </div>
            <div class="priority-actions">
              <a-button type="primary" size="small" @click.stop="markAsRead(msg)">
                立即处理
              </a-button>
            </div>
          </div>
          <div v-if="highPriorityMessages.length > 3" class="more-messages">
            <a @click="filterHighPriority">查看全部 {{ highPriorityMessages.length }} 条高优先级消息</a>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 消息统计 -->
    <div class="message-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="全部消息"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <message-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="未读消息"
              :value="stats.unread"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <bell-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="今日消息"
              :value="stats.today"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <calendar-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="紧急消息"
              :value="stats.urgent"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <exclamation-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card>
        <a-form :model="filterForm" class="filter-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="关键词搜索">
                <a-input 
                  v-model:value="filterForm.keyword" 
                  placeholder="搜索标题或内容"
                  allow-clear
                  @change="handleFilter"
                >
                  <template #prefix>
                    <search-outlined />
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="消息类型">
                <a-select 
                  v-model:value="filterForm.type" 
                  placeholder="请选择类型"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option value="system">系统消息</a-select-option>
                  <a-select-option value="notice">通知公告</a-select-option>
                  <a-select-option value="task">任务消息</a-select-option>
                  <a-select-option value="approval">审批消息</a-select-option>
                  <a-select-option value="reminder">提醒消息</a-select-option>
                  <a-select-option value="warning">警告消息</a-select-option>
                  <a-select-option value="error">错误消息</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="阅读状态">
                <a-select 
                  v-model:value="filterForm.status" 
                  placeholder="请选择状态"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option :value="1">未读</a-select-option>
                  <a-select-option :value="2">已读</a-select-option>
                  <a-select-option :value="3">已归档</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="优先级">
                <a-select 
                  v-model:value="filterForm.priority" 
                  placeholder="请选择优先级"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option :value="1">低</a-select-option>
                  <a-select-option :value="2">普通</a-select-option>
                  <a-select-option :value="3">高</a-select-option>
                  <a-select-option :value="4">紧急</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="消息分类">
                <a-tree-select
                  v-model:value="filterForm.categoryId"
                  :tree-data="categoryTree"
                  placeholder="请选择分类"
                  allow-clear
                  tree-default-expand-all
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="filterForm.timeRange"
                  style="width: 100%"
                  @change="handleFilter"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 消息列表 -->
    <div class="message-list-container">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>消息列表 ({{ pagination.total }})</span>
            <a-space>
              <a-button size="small" @click="refreshData">
                <template #icon><reload-outlined /></template>
                刷新
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="handleBatchAction">
                    <a-menu-item key="read" :disabled="!selectedRowKeys.length">
                      <check-outlined />
                      标记已读
                    </a-menu-item>
                    <a-menu-item key="unread" :disabled="!selectedRowKeys.length">
                      <eye-invisible-outlined />
                      标记未读
                    </a-menu-item>
                    <a-menu-item key="archive" :disabled="!selectedRowKeys.length">
                      <inbox-outlined />
                      归档
                    </a-menu-item>
                    <a-menu-item key="delete" :disabled="!selectedRowKeys.length" danger>
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button size="small">
                  批量操作
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </div>
        </template>

        <a-table
          :columns="columns"
          :data-source="messageList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <!-- 消息标题列 -->
          <template #title="{ record }">
          <div class="message-title" :class="{ 
            'unread': record && record.status === 1,
            'priority-urgent': record && record.priority === 4,
            'priority-high': record && record.priority === 3
          }">
            <div class="title-content">
              <span v-if="record && record.priority >= 3" class="priority-icon">
                {{ getPriorityIcon(record.priority) }}
              </span>
              <a @click="viewMessage(record)">{{ record?.title || '无标题' }}</a>
              <div class="message-meta">
                <a-tag :color="record?.type ? getTypeColor(record.type) : 'default'" size="small">
                  {{ record?.type ? getTypeLabel(record.type) : '未知类型' }}
                </a-tag>
                <span class="sender">{{ record?.senderName || '未知发送人' }}</span>
              </div>
            </div>
            <div class="message-indicators">
              <a-tag v-if="record && record.status === 1" color="red" size="small">未读</a-tag>
              <a-tag 
                v-if="record && record.priority === 4" 
                color="red" 
                size="small"
                class="priority-urgent"
              >
                🚨 紧急
              </a-tag>
              <a-tag 
                v-if="record && record.priority === 3" 
                color="orange" 
                size="small"
              >
                ⚠️ 高
              </a-tag>
              <a-tag v-if="record?.attachments?.length" color="blue" size="small">
                <paper-clip-outlined />
              </a-tag>
            </div>
            </div>
          </template>

  <!-- 优先级列 -->
  <template #priority="{ record }">
    <a-tag :color="record && record.priority ? getPriorityColor(record.priority) : 'default'">
      {{ record && record.priority ? getPriorityLabel(record.priority) : '未知' }}
    </a-tag>
  </template>

  <!-- 状态列 -->
  <template #status="{ record }">
    <a-badge 
      :status="record && record.status ? getStatusBadge(record.status) : 'default'" 
      :text="record && record.status ? getStatusLabel(record.status) : '未知'"
    />
  </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <a-space>
              <a-button type="link" size="small" @click="viewMessage(record)">
                查看
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(e) => handleAction(e.key, record)">
                    <a-menu-item 
                      key="read" 
                      :disabled="record.status === 2"
                    >
                      <check-outlined />
                      标记已读
                    </a-menu-item>
                    <a-menu-item 
                      key="unread" 
                      :disabled="record.status === 1"
                    >
                      <eye-invisible-outlined />
                      标记未读
                    </a-menu-item>
                    <a-menu-item key="archive" :disabled="record.status === 3">
                      <inbox-outlined />
                      归档
                    </a-menu-item>
                    <a-menu-item 
                      key="restore" 
                      :disabled="record.status !== 3 && record.status !== 4"
                    >
                      <redo-outlined />
                      恢复
                    </a-menu-item>
                    <a-menu-item key="reply" :disabled="record.type === 'system'">
                      <reply-outlined />
                      回复
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>
                      <delete-outlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 消息详情模态框 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="消息详情"
      width="80%"
      :footer="null"
    >
      <div v-if="selectedMessage" class="message-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="消息标题" :span="2">
            {{ selectedMessage.title }}
          </a-descriptions-item>
          <a-descriptions-item label="消息类型">
            <a-tag :color="selectedMessage.type ? getTypeColor(selectedMessage.type) : 'default'">
              {{ selectedMessage.type ? getTypeLabel(selectedMessage.type) : '未知' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="selectedMessage.priority ? getPriorityColor(selectedMessage.priority) : 'default'">
              {{ selectedMessage.priority ? getPriorityLabel(selectedMessage.priority) : '未知' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送人">
            {{ selectedMessage.senderName }}
          </a-descriptions-item>
          <a-descriptions-item label="接收人">
            {{ selectedMessage.receiverName }}
          </a-descriptions-item>
          <a-descriptions-item label="发送时间">
            {{ selectedMessage.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="阅读时间">
            {{ selectedMessage.readTime || '未读' }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider>消息内容</a-divider>
        <div class="message-content" v-html="selectedMessage.content"></div>
        
        <a-divider v-if="selectedMessage.attachments?.length">附件</a-divider>
        <div v-if="selectedMessage.attachments?.length" class="message-attachments">
          <a-list
            :data-source="selectedMessage.attachments"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a :href="item.url" target="_blank">{{ item.name }}</a>
                  </template>
                  <template #description>
                    {{ formatFileSize(item.size) }} · {{ item.createTime }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
        
        <div class="message-actions" style="margin-top: 24px; text-align: center;">
          <a-space>
            <a-button 
              type="primary" 
              :disabled="selectedMessage.status === 2"
              @click="markAsRead(selectedMessage)"
            >
              标记已读
            </a-button>
            <a-button 
              :disabled="selectedMessage.type === 'system'"
              @click="replyMessage(selectedMessage)"
            >
              回复
            </a-button>
            <a-button @click="archiveMessage(selectedMessage)">
              归档
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 发送消息模态框 -->
    <a-modal
      v-model:open="sendModalVisible"
      title="发送消息"
      width="600px"
      @ok="handleSendSubmit"
      :confirm-loading="sending"
    >
      <a-form
        ref="sendFormRef"
        :model="sendForm"
        :rules="sendFormRules"
        layout="vertical"
      >
        <a-form-item label="消息标题" name="title" required>
          <a-input 
            v-model:value="sendForm.title" 
            placeholder="请输入消息标题"
          />
        </a-form-item>
        
        <a-form-item label="消息类型" name="type" required>
          <a-select 
            v-model:value="sendForm.type" 
            placeholder="请选择消息类型"
          >
            <a-select-option value="system">系统消息</a-select-option>
            <a-select-option value="notice">通知公告</a-select-option>
            <a-select-option value="task">任务消息</a-select-option>
            <a-select-option value="reminder">提醒消息</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="优先级" name="priority" required>
          <a-select 
            v-model:value="sendForm.priority" 
            placeholder="请选择优先级"
          >
            <a-select-option :value="1">低</a-select-option>
            <a-select-option :value="2">普通</a-select-option>
            <a-select-option :value="3">高</a-select-option>
            <a-select-option :value="4">紧急</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="接收人" name="receiverId" required>
          <a-select 
            v-model:value="sendForm.receiverId" 
            placeholder="请选择接收人"
            show-search
            mode="multiple"
          >
            <a-select-option value="user1">周海军</a-select-option>
            <a-select-option value="user2">王东</a-select-option>
            <a-select-option value="user3">杜佳佳</a-select-option>
            <a-select-option value="all">全体用户</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="消息内容" name="content" required>
          <a-textarea 
            v-model:value="sendForm.content" 
            placeholder="请输入消息内容"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  ReloadOutlined,
  CheckOutlined,
  PlusOutlined,
  MessageOutlined,
  BellOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  DownOutlined,
  EyeInvisibleOutlined,
  InboxOutlined,
  DeleteOutlined,
  FileOutlined,
  RedoOutlined,
  PaperClipOutlined
} from '@ant-design/icons-vue'
import type { Message, MessageType, MessageStatus, MessagePriority } from '@/types/message'
import dayjs, { type Dayjs } from 'dayjs'

// 响应式数据
const loading = ref(false)
const sending = ref(false)

// 模态框状态
const detailModalVisible = ref(false)
const sendModalVisible = ref(false)

// 统计数据
const stats = reactive({
  total: 1248,
  unread: 23,
  today: 45,
  urgent: 8
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  type: undefined as MessageType | undefined,
  status: undefined as MessageStatus | undefined,
  priority: undefined as MessagePriority | undefined,
  categoryId: undefined as string | undefined,
  timeRange: undefined as [Dayjs, Dayjs] | undefined
})

// 发送表单
const sendForm = reactive({
  title: '',
  type: undefined as MessageType | undefined,
  priority: 2 as MessagePriority,
  receiverId: [] as string[],
  content: ''
})

const sendFormRef = ref()

// 表单验证规则
const sendFormRules = {
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择消息类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  receiverId: [
    { required: true, message: '请选择接收人', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ]
}

// 消息列表
const messageList = ref<Message[]>([])

// 高优先级消息列表（优先级3和4）
const highPriorityMessages = computed(() => {
  return messageList.value.filter(msg => 
    msg && msg.status === 1 && (msg.priority === 3 || msg.priority === 4)
  ).sort((a, b) => b.priority - a.priority) // 按优先级降序排列
})

// 优先级提醒设置
const prioritySettings = reactive({
  enableSound: true,
  enableNotification: true,
  enableAnimation: true,
  soundFile: '/sounds/notification.mp3'
})

// 分类树数据
const categoryTree = ref([
  {
    title: '系统通知',
    value: 'cat_1',
    children: [
      { title: '系统维护', value: 'cat_1_1' },
      { title: '功能更新', value: 'cat_1_2' }
    ]
  },
  {
    title: '业务通知',
    value: 'cat_2',
    children: [
      { title: '审批通知', value: 'cat_2_1' },
      { title: '任务通知', value: 'cat_2_2' }
    ]
  }
])

// 选中的消息
const selectedMessage = ref<Message | null>(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}))

// 计算未读数量
const unreadCount = computed(() => {
  return messageList.value.filter(msg => msg && msg.status === 1).length
})

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '消息标题',
    key: 'title',
    slots: { customRender: 'title' },
    width: 400
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    slots: { customRender: 'priority' },
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
    width: 100
  },
  {
    title: '发送时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    slots: { customRender: 'action' },
    width: 150,
    fixed: 'right'
  }
]

// 工具方法
const getTypeColor = (type: MessageType) => {
  const colors = {
    system: 'blue',
    notice: 'green',
    task: 'orange',
    approval: 'purple',
    reminder: 'cyan',
    warning: 'gold',
    error: 'red'
  }
  return colors[type] || 'default'
}

const getTypeLabel = (type: MessageType) => {
  const labels = {
    system: '系统消息',
    notice: '通知公告',
    task: '任务消息',
    approval: '审批消息',
    reminder: '提醒消息',
    warning: '警告消息',
    error: '错误消息'
  }
  return labels[type] || type
}

const getPriorityColor = (priority: MessagePriority) => {
  const colors = {
    1: 'default',  // 低
    2: 'blue',     // 普通
    3: 'orange',   // 高
    4: 'red'       // 紧急
  }
  return colors[priority] || 'default'
}

const getPriorityLabel = (priority: MessagePriority) => {
  const labels = {
    1: '低',
    2: '普通',
    3: '高',
    4: '紧急'
  }
  return labels[priority] || '未知'
}

const getStatusBadge = (status: MessageStatus) => {
  const badges = {
    1: 'error',      // 未读
    2: 'success',    // 已读
    3: 'default',    // 已归档
    4: 'warning'     // 已删除
  }
  return badges[status] || 'default'
}

const getStatusLabel = (status: MessageStatus) => {
  const labels = {
    1: '未读',
    2: '已读',
    3: '已归档',
    4: '已删除'
  }
  return labels[status] || '未知'
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

// 优先级相关工具方法
const getPriorityIcon = (priority: MessagePriority) => {
  const icons = {
    1: '📝',     // 低
    2: '📋',     // 普通
    3: '⚠️',     // 高
    4: '🚨'      // 紧急
  }
  return icons[priority] || '📝'
}

const getPriorityClass = (priority: MessagePriority) => {
  const classes = {
    1: 'priority-low',
    2: 'priority-normal', 
    3: 'priority-high',
    4: 'priority-urgent'
  }
  return classes[priority] || 'priority-normal'
}

// 筛选高优先级消息
const filterHighPriority = () => {
  filterForm.priority = undefined
  filterForm.status = 1 // 只显示未读
  // 设置优先级筛选为高和紧急
  handleFilter()
  // 滚动到消息列表
  document.querySelector('.message-list-container')?.scrollIntoView({ behavior: 'smooth' })
}

// 播放提示音
const playNotificationSound = () => {
  if (!prioritySettings.enableSound) return
  
  try {
    const audio = new Audio(prioritySettings.soundFile)
    audio.volume = 0.5
    audio.play().catch(err => {
      console.warn('无法播放提示音:', err)
    })
  } catch (error) {
    console.warn('提示音播放失败:', error)
  }
}

// 显示浏览器通知
const showBrowserNotification = (message: Message) => {
  if (!prioritySettings.enableNotification) return
  
  if ('Notification' in window) {
    if (Notification.permission === 'granted') {
      new Notification(`${getPriorityLabel(message.priority)}消息`, {
        body: message.title,
        icon: '/favicon.ico',
        tag: message.id
      })
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showBrowserNotification(message)
        }
      })
    }
  }
}

// 处理高优先级消息提醒
const handleHighPriorityAlert = (message: Message) => {
  if (message.priority >= 3 && message.status === 1) {
    // 播放提示音
    playNotificationSound()
    
    // 显示浏览器通知
    showBrowserNotification(message)
    
    // 显示弹窗提醒（仅紧急消息）
    if (message.priority === 4) {
      Modal.warning({
        title: '🚨 紧急消息提醒',
        content: `标题：${message.title}\n发送人：${message.senderName}\n时间：${message.createTime}\n\n${message.content}`,
        okText: '立即查看',
        onOk: () => viewMessage(message)
      })
    }
  }
}

// 数据加载
const loadMessageList = async () => {
  loading.value = true

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟数据
    const mockData: Message[] = [
      {
        id: 'msg_001',
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护升级，期间可能影响正常使用，请提前做好相关准备。',
        type: 'system' as MessageType,
        status: 1 as MessageStatus,
        priority: 3 as MessagePriority,
        senderId: 'admin',
        senderName: '系统管理员',
        receiverId: 'all',
        receiverName: '全体用户',
        categoryId: 'cat_1_1',
        categoryName: '系统维护',
        tags: ['系统', '维护'],
        createTime: '2025-06-20 14:30:00',
        updateTime: '2025-06-20 14:30:00',
        pushChannels: ['system', 'sms'] as any[],
        metadata: {}
      },
      {
        id: 'msg_002',
        title: '新功能上线通知',
        content: '消息中心新功能已上线，支持多渠道推送和智能分类，欢迎体验使用。',
        type: 'notice' as MessageType,
        status: 2 as MessageStatus,
        priority: 2 as MessagePriority,
        senderId: 'admin',
        senderName: '产品团队',
        receiverId: 'all',
        receiverName: '全体用户',
        categoryId: 'cat_1_2',
        categoryName: '功能更新',
        readTime: '2025-06-20 15:20:00',
        createTime: '2025-06-20 10:15:00',
        updateTime: '2025-06-20 10:15:00',
        pushChannels: ['system'] as any[],
        metadata: {}
      },
      {
        id: 'msg_003',
        title: '紧急任务分配',
        content: '请立即处理客户投诉工单#12345，优先级：紧急。',
        type: 'task' as MessageType,
        status: 1 as MessageStatus,
        priority: 4 as MessagePriority,
        senderId: 'manager1',
        senderName: '牟文婷',
        receiverId: 'user1',
        receiverName: '李小明',
        categoryId: 'cat_2_2',
        categoryName: '任务通知',
        tags: ['紧急', '任务'],
        createTime: '2025-06-20 16:45:00',
        updateTime: '2025-06-20 16:45:00',
        pushChannels: ['system', 'sms', 'ykz'] as any[],
        metadata: {}
      }
    ]

    messageList.value = mockData
    pagination.total = mockData.length

    // 更新统计数据
    stats.total = mockData.length
    stats.unread = mockData.filter(m => m.status === 1).length
    stats.today = mockData.filter(m =>
      dayjs(m.createTime).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')
    ).length
    stats.urgent = mockData.filter(m => m.priority === 4).length

    // 处理高优先级消息提醒
    mockData.forEach(msg => {
      if (msg.status === 1 && msg.priority >= 3) {
        handleHighPriorityAlert(msg)
      }
    })

  } catch (error) {
    message.error('加载消息列表失败')
    console.error('Load message list error:', error)
  } finally {
    loading.value = false
  }
}

// 筛选和搜索
const handleFilter = () => {
  pagination.current = 1
  loadMessageList()
}

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  pagination.current = pag?.current || 1
  pagination.pageSize = pag?.pageSize || 10
  loadMessageList()
}

// 刷新数据
const refreshData = () => {
  loadMessageList()
}

// 消息操作
const viewMessage = (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  selectedMessage.value = msg
  detailModalVisible.value = true

  // 如果是未读消息，自动标记为已读
  if (msg && msg.status === 1) {
    markAsRead(msg)
  }
}

const markAsRead = async (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    msg.status = 2
    msg.readTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

    // 更新统计
    stats.unread = messageList.value.filter(m => m && m.status === 1).length

    message.success('已标记为已读')
  } catch (error) {
    message.error('操作失败')
    console.error('Mark as read error:', error)
  }
}

const markAsUnread = async (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    msg.status = 1
    msg.readTime = undefined

    // 更新统计
    stats.unread = messageList.value.filter(m => m && m.status === 1).length

    message.success('已标记为未读')
  } catch (error) {
    message.error('操作失败')
    console.error('Mark as unread error:', error)
  }
}

const archiveMessage = async (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    msg.status = 3
    message.success('消息已归档')
  } catch (error) {
    message.error('归档失败')
    console.error('Archive message error:', error)
  }
}

const deleteMessage = async (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除消息"${msg.title || '未知消息'}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))

        const index = messageList.value.findIndex(m => m && m.id === msg.id)
        if (index >= 0) {
          messageList.value.splice(index, 1)
          pagination.total--
        }

        message.success('消息删除成功')
      } catch (error) {
        message.error('删除失败')
        console.error('Delete message error:', error)
      }
    }
  })
}

const restoreMessage = async (msg: Message) => {
  if (!msg) {
    message.error('消息数据异常')
    return
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 将消息状态恢复为已读
    msg.status = 2
    msg.readTime = msg.readTime || dayjs().format('YYYY-MM-DD HH:mm:ss')

    // 更新统计数据
    stats.unread = messageList.value.filter(m => m && m.status === 1).length

    message.success('消息已恢复')
  } catch (error) {
    message.error('恢复失败')
    console.error('Restore message error:', error)
  }
}

const replyMessage = (msg: Message) => {
  message.info(`回复消息: ${msg.title}`)
  // 这里可以打开回复对话框
}

const markAllAsRead = async () => {
  Modal.confirm({
    title: '确认操作',
    content: `确定要将所有未读消息标记为已读吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        messageList.value.forEach(msg => {
          if (msg.status === 1) {
            msg.status = 2
            msg.readTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
        })

        stats.unread = 0
        message.success('所有消息已标记为已读')
      } catch (error) {
        message.error('操作失败')
        console.error('Mark all as read error:', error)
      }
    }
  })
}

// 单个消息操作
const handleAction = (action: string, msg: Message) => {
  switch (action) {
    case 'read':
      markAsRead(msg)
      break
    case 'unread':
      markAsUnread(msg)
      break
    case 'archive':
      archiveMessage(msg)
      break
    case 'restore':
      restoreMessage(msg)
      break
    case 'reply':
      replyMessage(msg)
      break
    case 'delete':
      deleteMessage(msg)
      break
  }
}

// 批量操作
const handleBatchAction = ({ key }: { key: string }) => {
  if (!selectedRowKeys.value.length) {
    message.warning('请先选择要操作的消息')
    return
  }

  switch (key) {
    case 'read':
      batchMarkAsRead()
      break
    case 'unread':
      batchMarkAsUnread()
      break
    case 'archive':
      batchArchive()
      break
    case 'delete':
      batchDelete()
      break
  }
}

const batchMarkAsRead = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    messageList.value.forEach(msg => {
      if (msg && selectedRowKeys.value.includes(msg.id) && msg.status === 1) {
        msg.status = 2
        msg.readTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    })

    selectedRowKeys.value = []
    stats.unread = messageList.value.filter(m => m && m.status === 1).length
    message.success('批量标记已读成功')
  } catch (error) {
    message.error('批量操作失败')
    console.error('Batch mark as read error:', error)
  }
}

const batchMarkAsUnread = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    messageList.value.forEach(msg => {
      if (msg && selectedRowKeys.value.includes(msg.id) && msg.status === 2) {
        msg.status = 1
        msg.readTime = undefined
      }
    })

    selectedRowKeys.value = []
    stats.unread = messageList.value.filter(m => m && m.status === 1).length
    message.success('批量标记未读成功')
  } catch (error) {
    message.error('批量操作失败')
    console.error('Batch mark as unread error:', error)
  }
}

const batchArchive = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    messageList.value.forEach(msg => {
      if (msg && selectedRowKeys.value.includes(msg.id)) {
        msg.status = 3
      }
    })

    selectedRowKeys.value = []
    message.success('批量归档成功')
  } catch (error) {
    message.error('批量归档失败')
    console.error('Batch archive error:', error)
  }
}

const batchDelete = () => {
  Modal.confirm({
    title: '批量删除确认',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条消息吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        messageList.value = messageList.value.filter(
          msg => msg && !selectedRowKeys.value.includes(msg.id)
        )

        pagination.total -= selectedRowKeys.value.length
        selectedRowKeys.value = []
        message.success('批量删除成功')
      } catch (error) {
        message.error('批量删除失败')
        console.error('Batch delete error:', error)
      }
    }
  })
}

// 发送消息
const showSendModal = () => {
  sendForm.title = ''
  sendForm.type = undefined
  sendForm.priority = 2
  sendForm.receiverId = []
  sendForm.content = ''
  sendModalVisible.value = true
}

const handleSendSubmit = async () => {
  try {
    await sendFormRef.value.validate()
    sending.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      title: sendForm.title,
      content: sendForm.content,
      type: sendForm.type!,
      status: 1,
      priority: sendForm.priority,
      senderId: 'current_user',
      senderName: '当前用户',
      receiverId: sendForm.receiverId.join(','),
      receiverName: '选中用户',
      createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      pushChannels: ['system'],
      metadata: {}
    }

    messageList.value.unshift(newMessage)
    pagination.total++

    sendModalVisible.value = false
    message.success('消息发送成功')
  } catch (error) {
    console.error('Send message error:', error)
  } finally {
    sending.value = false
  }
}

// 生命周期
onMounted(() => {
  loadMessageList()
})
</script>

<style scoped>
.message-list {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.message-stats {
  margin-bottom: 24px;
}

.message-stats .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 24px;
}

.filter-form {
  margin-bottom: 0;
}

.message-list-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.message-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.message-title.unread {
  font-weight: 600;
}

.title-content {
  flex: 1;
}

.title-content a {
  color: #333;
  font-size: 14px;
  line-height: 1.4;
}

.title-content a:hover {
  color: #1890ff;
}

.message-title.unread .title-content a {
  color: #1890ff;
  font-weight: 600;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.sender {
  font-size: 12px;
  color: #999;
}

.message-indicators {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 12px;
}

.message-detail {
  padding: 16px 0;
}

.message-content {
  padding: 16px;
  background: #f9f9f9;
  border-radius: 6px;
  border-left: 4px solid #1890ff;
  line-height: 1.6;
  color: #333;
}

.message-attachments {
  background: #fafafa;
  border-radius: 6px;
  padding: 12px;
}

.message-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  font-size: 14px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-row-selected > td) {
  background: #e6f7ff;
}

/* 状态标签样式 */
:deep(.ant-badge-status-dot) {
  width: 8px;
  height: 8px;
}

/* 操作按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  line-height: 1.5;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 600;
  color: #333;
}

:deep(.ant-descriptions-item-content) {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .message-list {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    justify-content: space-between;
  }

  :deep(.ant-btn) {
    flex: 1;
  }

  /* 表格在移动端的优化 */
  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-thead > tr > th) {
    padding: 8px 4px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 4px;
  }

  .message-title {
    flex-direction: column;
    gap: 8px;
  }

  .message-indicators {
    margin-left: 0;
  }
}

/* 动画效果 */
.message-list {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
:deep(.ant-spin-container) {
  min-height: 200px;
}

/* 空状态样式 */
:deep(.ant-empty) {
  padding: 40px 0;
}

:deep(.ant-empty-description) {
  color: #999;
}

/* 标签样式优化 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 8px;
}

/* 下拉菜单样式 */
:deep(.ant-dropdown-menu) {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  padding: 8px 16px;
}

:deep(.ant-dropdown-menu-item:hover) {
  background: #f5f5f5;
}

/* 高优先级消息提醒样式 */
.priority-alert-section {
  margin-bottom: 24px;
}

.priority-alert-card {
  border: 2px solid #ff4d4f;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(255, 77, 79, 0.2);
  background: linear-gradient(135deg, #fff2f0 0%, #ffffff 100%);
}

.priority-alert-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4d4f;
  font-weight: 600;
}

.alert-icon {
  font-size: 18px;
  color: #ff4d4f;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.urgent-count {
  margin-left: auto;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.priority-messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.priority-message-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid;
}

.priority-message-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.priority-urgent {
  background: linear-gradient(135deg, #fff1f0 0%, #ffebe8 100%);
  border-left-color: #ff4d4f;
  animation: urgentGlow 3s ease-in-out infinite;
}

.priority-high {
  background: linear-gradient(135deg, #fff7e6 0%, #ffefd3 100%);
  border-left-color: #fa8c16;
}

.priority-normal {
  background: linear-gradient(135deg, #f6ffed 0%, #edf9e0 100%);
  border-left-color: #52c41a;
}

.priority-low {
  background: linear-gradient(135deg, #f0f5ff 0%, #e6f4ff 100%);
  border-left-color: #1890ff;
}

@keyframes urgentGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 77, 79, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 77, 79, 0.6);
  }
}

.priority-message-content {
  flex: 1;
}

.message-title-priority {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.priority-icon {
  font-size: 16px;
  min-width: 20px;
}

.message-title-text {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.message-meta-priority {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.priority-actions {
  margin-left: 16px;
}

.more-messages {
  text-align: center;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 6px;
  margin-top: 8px;
}

.more-messages a {
  color: #1890ff;
  font-weight: 500;
}

.more-messages a:hover {
  color: #40a9ff;
}

/* 表格行优先级样式增强 */
:deep(.ant-table-tbody > tr) {
  position: relative;
}

:deep(.ant-table-tbody > tr[data-priority="4"]) {
  background: linear-gradient(135deg, #fff1f0 0%, #ffffff 100%);
  border-left: 4px solid #ff4d4f;
}

:deep(.ant-table-tbody > tr[data-priority="4"]:hover > td) {
  background: #fff1f0;
}

:deep(.ant-table-tbody > tr[data-priority="3"]) {
  background: linear-gradient(135deg, #fff7e6 0%, #ffffff 100%);
  border-left: 4px solid #fa8c16;
}

:deep(.ant-table-tbody > tr[data-priority="3"]:hover > td) {
  background: #fff7e6;
}

/* 消息标题增强样式 */
.message-title.priority-urgent .title-content a {
  color: #ff4d4f;
  font-weight: 700;
  font-size: 15px;
}

.message-title.priority-high .title-content a {
  color: #fa8c16;
  font-weight: 600;
  font-size: 14px;
}

/* 优先级标签闪烁效果 */
.priority-urgent .ant-tag {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.6;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .message-list {
    background: #1f1f1f;
  }

  .page-header,
  .message-list-container {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .priority-alert-card {
    background: linear-gradient(135deg, #2a1f1f 0%, #2f2f2f 100%);
    border-color: #ff4d4f;
  }

  .priority-urgent {
    background: linear-gradient(135deg, #2a1f1f 0%, #331f1f 100%);
  }

  .priority-high {
    background: linear-gradient(135deg, #2a2419 0%, #332a1a 100%);
  }

  .message-title-text {
    color: #fff;
  }

  .message-meta-priority {
    color: #ccc;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  .message-content {
    background: #404040;
    color: #fff;
  }

  .message-attachments {
    background: #404040;
  }

  .title-content a {
    color: #fff;
  }

  .title-content a:hover {
    color: #40a9ff;
  }

  .message-title.unread .title-content a {
    color: #40a9ff;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #404040;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr > td) {
    background: #2f2f2f;
    color: #fff;
    border-color: #404040;
  }

  :deep(.ant-table-tbody > tr:hover > td) {
    background: #404040;
  }

  :deep(.ant-descriptions-item-label) {
    color: #fff;
  }

  :deep(.ant-descriptions-item-content) {
    color: #ccc;
  }
}
</style>
