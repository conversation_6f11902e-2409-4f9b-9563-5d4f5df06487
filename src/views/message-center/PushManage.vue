<template>
  <div class="push-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>推送管理</h2>
        <p>配置和管理消息推送规则、策略和渠道</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateRuleModal">
            <template #icon><plus-outlined /></template>
            新建规则
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 推送统计 -->
    <div class="push-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="推送规则"
              :value="stats.totalRules"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <setting-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="活跃规则"
              :value="stats.activeRules"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="今日触发"
              :value="stats.todayTriggers"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <thunderbolt-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="成功率"
              :value="stats.successRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <rise-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <div class="push-tabs">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 推送规则 -->
        <a-tab-pane key="rules" tab="推送规则">
          <push-rules-tab
            :rules="rulesList"
            :loading="rulesLoading"
            @create="showCreateRuleModal"
            @edit="editRule"
            @delete="deleteRule"
            @toggle="toggleRule"
            @test="testRule"
          />
        </a-tab-pane>

        <!-- 消息模板 -->
        <a-tab-pane key="templates" tab="消息模板">
          <message-templates-tab
            :templates="templatesList"
            :loading="templatesLoading"
            @create="showCreateTemplateModal"
            @edit="editTemplate"
            @delete="deleteTemplate"
            @preview="previewTemplate"
          />
        </a-tab-pane>

        <!-- 推送对象 -->
        <a-tab-pane key="targets" tab="推送对象">
          <push-targets-tab
            :targets="targetsList"
            :loading="targetsLoading"
            @create="showCreateTargetModal"
            @edit="editTarget"
            @delete="deleteTarget"
            @sync="syncTarget"
          />
        </a-tab-pane>

        <!-- 推送策略 -->
        <a-tab-pane key="strategies" tab="推送策略">
          <push-strategies-tab
            :strategies="strategiesList"
            :loading="strategiesLoading"
            @create="showCreateStrategyModal"
            @edit="editStrategy"
            @delete="deleteStrategy"
            @test="testStrategy"
          />
        </a-tab-pane>

        <!-- 推送渠道 -->
        <a-tab-pane key="channels" tab="推送渠道">
          <push-channels-tab
            :channels="channelsList"
            :loading="channelsLoading"
            @configure="configureChannel"
            @test="testChannel"
            @toggle="toggleChannel"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 创建/编辑规则模态框 -->
    <a-modal
      v-model:open="ruleModalVisible"
      :title="editingRule ? '编辑推送规则' : '新建推送规则'"
      width="800px"
      @ok="handleRuleSubmit"
      :confirm-loading="submitting"
    >
      <rule-form
        ref="ruleFormRef"
        :rule="editingRule"
        :templates="templatesList"
        :targets="targetsList"
        :channels="channelsList"
      />
    </a-modal>

    <!-- 创建/编辑模板模态框 -->
    <a-modal
      v-model:open="templateModalVisible"
      :title="editingTemplate ? '编辑消息模板' : '新建消息模板'"
      width="700px"
      @ok="handleTemplateSubmit"
      :confirm-loading="submitting"
    >
      <template-form
        ref="templateFormRef"
        :template="editingTemplate"
      />
    </a-modal>

    <!-- 创建/编辑推送对象模态框 -->
    <a-modal
      v-model:open="targetModalVisible"
      :title="editingTarget ? '编辑推送对象' : '新建推送对象'"
      width="600px"
      @ok="handleTargetSubmit"
      :confirm-loading="submitting"
    >
      <target-form
        ref="targetFormRef"
        :target="editingTarget"
      />
    </a-modal>

    <!-- 创建/编辑推送策略模态框 -->
    <a-modal
      v-model:open="strategyModalVisible"
      :title="editingStrategy ? '编辑推送策略' : '新建推送策略'"
      width="800px"
      @ok="handleStrategySubmit"
      :confirm-loading="submitting"
    >
      <strategy-form
        ref="strategyFormRef"
        :strategy="editingStrategy"
        :targets="targetsList"
        :channels="channelsList"
      />
    </a-modal>

    <!-- 模板预览模态框 -->
    <a-modal
      v-model:open="previewModalVisible"
      title="模板预览"
      width="600px"
      :footer="null"
    >
      <template-preview
        v-if="previewingTemplate"
        :template="previewingTemplate"
      />
    </a-modal>

    <!-- 渠道配置模态框 -->
    <a-modal
      v-model:open="channelConfigModalVisible"
      title="渠道配置"
      width="700px"
      @ok="handleChannelConfigSubmit"
      :confirm-loading="submitting"
    >
      <channel-config
        ref="channelConfigRef"
        :channel="configuringChannel"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ThunderboltOutlined,
  RiseOutlined
} from '@ant-design/icons-vue'
import type { 
  MessageRule, 
  MessageTemplate, 
  PushTarget, 
  PushStrategy,
  PushChannel 
} from '@/types/message'

// 导入子组件
import PushRulesTab from './components/PushRulesTab.vue'
import MessageTemplatesTab from './components/MessageTemplatesTab.vue'
import PushTargetsTab from './components/PushTargetsTab.vue'
import PushStrategiesTab from './components/PushStrategiesTab.vue'
import PushChannelsTab from './components/PushChannelsTab.vue'
import RuleForm from './components/RuleForm.vue'
import TemplateForm from './components/TemplateForm.vue'
import TargetForm from './components/TargetForm.vue'
import StrategyForm from './components/StrategyForm.vue'
import TemplatePreview from './components/TemplatePreview.vue'
import ChannelConfig from './components/ChannelConfig.vue'

// 响应式数据
const activeTab = ref('rules')
const submitting = ref(false)

// 模态框状态
const ruleModalVisible = ref(false)
const templateModalVisible = ref(false)
const targetModalVisible = ref(false)
const strategyModalVisible = ref(false)
const previewModalVisible = ref(false)
const channelConfigModalVisible = ref(false)

// 加载状态
const rulesLoading = ref(false)
const templatesLoading = ref(false)
const targetsLoading = ref(false)
const strategiesLoading = ref(false)
const channelsLoading = ref(false)

// 统计数据
const stats = reactive({
  totalRules: 15,
  activeRules: 12,
  todayTriggers: 234,
  successRate: 96.8
})

// 数据列表
const rulesList = ref<MessageRule[]>([])
const templatesList = ref<MessageTemplate[]>([])
const targetsList = ref<PushTarget[]>([])
const strategiesList = ref<PushStrategy[]>([])
const channelsList = ref<any[]>([])

// 编辑对象
const editingRule = ref<MessageRule | null>(null)
const editingTemplate = ref<MessageTemplate | null>(null)
const editingTarget = ref<PushTarget | null>(null)
const editingStrategy = ref<PushStrategy | null>(null)
const previewingTemplate = ref<MessageTemplate | null>(null)
const configuringChannel = ref<any>(null)

// 表单引用
const ruleFormRef = ref()
const templateFormRef = ref()
const targetFormRef = ref()
const strategyFormRef = ref()
const channelConfigRef = ref()

// 数据加载方法
const loadRules = async () => {
  rulesLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟数据
    rulesList.value = [
      {
        id: 'rule_001',
        name: '紧急消息推送规则',
        description: '当消息优先级为紧急时，自动推送到所有渠道',
        conditions: [
          { field: 'priority', operator: 'equals', value: 4 }
        ],
        actions: [
          { type: 'push', config: { channels: ['system', 'sms', 'ykz'] } }
        ],
        priority: 1,
        isEnabled: true,
        createTime: '2025-06-15 10:30:00',
        updateTime: '2025-06-20 14:20:00',
        createdBy: '周海军',
        triggerCount: 45
      }
    ]
  } catch (error) {
    message.error('加载推送规则失败')
    console.error('Load rules error:', error)
  } finally {
    rulesLoading.value = false
  }
}

const loadTemplates = async () => {
  templatesLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 600))
    
    // 模拟数据
    templatesList.value = [
      {
        id: 'tpl_001',
        name: '系统维护通知模板',
        title: '系统维护通知',
        content: '尊敬的用户，系统将于{{maintenanceTime}}进行维护，预计耗时{{duration}}，请提前做好准备。',
        type: 'system',
        variables: [
          { name: 'maintenanceTime', label: '维护时间', type: 'string', required: true },
          { name: 'duration', label: '维护时长', type: 'string', required: true }
        ],
        isEnabled: true,
        createTime: '2025-06-10 09:15:00',
        updateTime: '2025-06-15 16:30:00',
        createdBy: '王东',
        usageCount: 12
      }
    ]
  } catch (error) {
    message.error('加载消息模板失败')
    console.error('Load templates error:', error)
  } finally {
    templatesLoading.value = false
  }
}

const loadTargets = async () => {
  targetsLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据
    targetsList.value = [
      {
        id: 'target_001',
        name: '全体用户',
        type: 'group',
        targetId: 'all_users',
        isEnabled: true,
        createTime: '2025-06-01 00:00:00',
        updateTime: '2025-06-01 00:00:00',
        description: '系统中的所有用户'
      }
    ]
  } catch (error) {
    message.error('加载推送对象失败')
    console.error('Load targets error:', error)
  } finally {
    targetsLoading.value = false
  }
}

const loadStrategies = async () => {
  strategiesLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 700))
    
    // 模拟数据
    strategiesList.value = []
  } catch (error) {
    message.error('加载推送策略失败')
    console.error('Load strategies error:', error)
  } finally {
    strategiesLoading.value = false
  }
}

const loadChannels = async () => {
  channelsLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 400))
    
    // 模拟数据
    channelsList.value = [
      { id: 'system', name: '系统内推送', enabled: true, config: {} },
      { id: 'sms', name: '短信推送', enabled: true, config: {} },
      { id: 'ykz', name: '渝快政', enabled: false, config: {} },
      { id: 'email', name: '邮件推送', enabled: true, config: {} }
    ]
  } catch (error) {
    message.error('加载推送渠道失败')
    console.error('Load channels error:', error)
  } finally {
    channelsLoading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  loadRules()
  loadTemplates()
  loadTargets()
  loadStrategies()
  loadChannels()
}

// 规则操作
const showCreateRuleModal = () => {
  editingRule.value = null
  ruleModalVisible.value = true
}

const editRule = (rule: MessageRule) => {
  editingRule.value = rule
  ruleModalVisible.value = true
}

const deleteRule = (rule: MessageRule) => {
  message.success(`删除规则: ${rule.name}`)
}

const toggleRule = (rule: MessageRule) => {
  rule.isEnabled = !rule.isEnabled
  message.success(`规则已${rule.isEnabled ? '启用' : '禁用'}`)
}

const testRule = (rule: MessageRule) => {
  message.info(`测试规则: ${rule.name}`)
}

const handleRuleSubmit = async () => {
  try {
    await ruleFormRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ruleModalVisible.value = false
    message.success('推送规则保存成功')
    loadRules()
  } catch (error) {
    console.error('Save rule error:', error)
  } finally {
    submitting.value = false
  }
}

// 模板操作
const showCreateTemplateModal = () => {
  editingTemplate.value = null
  templateModalVisible.value = true
}

const editTemplate = (template: MessageTemplate) => {
  editingTemplate.value = template
  templateModalVisible.value = true
}

const deleteTemplate = (template: MessageTemplate) => {
  message.success(`删除模板: ${template.name}`)
}

const previewTemplate = (template: MessageTemplate) => {
  previewingTemplate.value = template
  previewModalVisible.value = true
}

const handleTemplateSubmit = async () => {
  try {
    await templateFormRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    templateModalVisible.value = false
    message.success('消息模板保存成功')
    loadTemplates()
  } catch (error) {
    console.error('Save template error:', error)
  } finally {
    submitting.value = false
  }
}

// 推送对象操作
const showCreateTargetModal = () => {
  editingTarget.value = null
  targetModalVisible.value = true
}

const editTarget = (target: PushTarget) => {
  editingTarget.value = target
  targetModalVisible.value = true
}

const deleteTarget = (target: PushTarget) => {
  message.success(`删除推送对象: ${target.name}`)
}

const syncTarget = (target: PushTarget) => {
  message.info(`同步推送对象: ${target.name}`)
}

const handleTargetSubmit = async () => {
  try {
    await targetFormRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    targetModalVisible.value = false
    message.success('推送对象保存成功')
    loadTargets()
  } catch (error) {
    console.error('Save target error:', error)
  } finally {
    submitting.value = false
  }
}

// 推送策略操作
const showCreateStrategyModal = () => {
  editingStrategy.value = null
  strategyModalVisible.value = true
}

const editStrategy = (strategy: PushStrategy) => {
  editingStrategy.value = strategy
  strategyModalVisible.value = true
}

const deleteStrategy = (strategy: PushStrategy) => {
  message.success(`删除推送策略: ${strategy.name}`)
}

const testStrategy = (strategy: PushStrategy) => {
  message.info(`测试推送策略: ${strategy.name}`)
}

const handleStrategySubmit = async () => {
  try {
    await strategyFormRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    strategyModalVisible.value = false
    message.success('推送策略保存成功')
    loadStrategies()
  } catch (error) {
    console.error('Save strategy error:', error)
  } finally {
    submitting.value = false
  }
}

// 渠道操作
const configureChannel = (channel: any) => {
  configuringChannel.value = channel
  channelConfigModalVisible.value = true
}

const testChannel = (channel: any) => {
  message.info(`测试推送渠道: ${channel.name}`)
}

const toggleChannel = (channel: any) => {
  channel.enabled = !channel.enabled
  message.success(`渠道已${channel.enabled ? '启用' : '禁用'}`)
}

const handleChannelConfigSubmit = async () => {
  try {
    await channelConfigRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    channelConfigModalVisible.value = false
    message.success('渠道配置保存成功')
    loadChannels()
  } catch (error) {
    console.error('Save channel config error:', error)
  } finally {
    submitting.value = false
  }
}

// 页面初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.push-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.push-stats {
  margin-bottom: 24px;
}

.push-stats .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.push-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}

/* 标签页样式 */
:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  background: #fafafa;
  border-color: #e8e8e8;
  color: #666;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background: white;
  color: #1890ff;
  border-bottom-color: white;
}

:deep(.ant-tabs-content-holder) {
  padding: 0;
}

:deep(.ant-tabs-tabpane) {
  padding: 24px;
}

/* 模态框样式 */
:deep(.ant-modal-header) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #e8e8e8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .push-manage {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    justify-content: space-between;
  }

  :deep(.ant-btn) {
    flex: 1;
  }

  :deep(.ant-tabs-tabpane) {
    padding: 16px;
  }
}

/* 动画效果 */
.push-manage {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .push-manage {
    background: #1f1f1f;
  }

  .page-header,
  .push-tabs {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
    background: #404040;
    border-color: #404040;
    color: #ccc;
  }

  :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
    background: #2f2f2f;
    color: #40a9ff;
    border-bottom-color: #2f2f2f;
  }
}
</style>
