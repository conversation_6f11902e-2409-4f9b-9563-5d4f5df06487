<template>
  <div class="category-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>消息分类管理</h1>
        <p>管理消息分类体系，支持多级分类和权限控制</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <plus-outlined />
            新建分类
          </a-button>
          <a-button @click="handleRefresh">
            <reload-outlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <a-card>
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="分类名称">
            <a-input 
              v-model:value="searchForm.name" 
              placeholder="请输入分类名称"
              allow-clear
              @press-enter="handleSearch"
            />
          </a-form-item>
          <a-form-item label="状态">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <search-outlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 分类列表 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>分类列表</span>
            <span class="record-count">共 {{ categoryList.length }} 个分类</span>
          </div>
        </template>
        
        <a-table
          :columns="columns"
          :data-source="categoryList"
          :loading="loading"
          :pagination="false"
          row-key="id"
          :default-expand-all-rows="true"
          :scroll="{ x: 1000 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="category-name-cell">
                <span class="category-icon">
                  <folder-outlined v-if="record.children && record.children.length > 0" />
                  <tag-outlined v-else />
                </span>
                <span class="category-text">{{ record.name }}</span>
              </div>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-switch 
                v-model:checked="record.isEnabled" 
                @change="handleStatusChange(record)"
                :loading="record.updating"
              />
            </template>
            
            <template v-if="column.key === 'level'">
              <a-tag :color="getLevelColor(record.level)">
                {{ getLevelText(record.level) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'messageCount'">
              <a-badge 
                :count="record.messageCount || 0" 
                :number-style="{ backgroundColor: '#52c41a' }"
              />
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleEdit(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="handleAddChild(record)">
                  添加子分类
                </a-button>
                <a-popconfirm
                  title="确定要删除这个分类吗？"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" size="small" danger>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 分类表单弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入分类名称" />
        </a-form-item>

        <a-form-item label="分类编码" name="code">
          <a-input v-model:value="formData.code" placeholder="请输入分类编码" />
        </a-form-item>

        <a-form-item label="上级分类" name="parentId">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="parentOptions"
            placeholder="请选择上级分类（留空为顶级分类）"
            allow-clear
            tree-default-expand-all
          />
        </a-form-item>

        <a-form-item label="分类描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入分类描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number 
            v-model:value="formData.sort" 
            :min="0" 
            :max="999"
            placeholder="数字越小排序越靠前"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="状态" name="isEnabled">
          <a-switch v-model:checked="formData.isEnabled" />
          <span class="form-help">启用后该分类可以被使用</span>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  FolderOutlined,
  TagOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface CategoryItem {
  id: string
  name: string
  code: string
  description?: string
  parentId?: string
  level: number
  sort: number
  isEnabled: boolean
  messageCount?: number
  createTime: string
  updateTime: string
  children?: CategoryItem[]
  updating?: boolean
}

interface CategoryForm {
  id?: string
  name: string
  code: string
  description: string
  parentId?: string
  sort: number
  isEnabled: boolean
}

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: undefined as boolean | undefined
})

// 表单数据
const formData = reactive<CategoryForm>({
  name: '',
  code: '',
  description: '',
  parentId: undefined,
  sort: 0,
  isEnabled: true
})

// 分类列表
const categoryList = ref<CategoryItem[]>([])

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑分类' : '新建分类'
})

const parentOptions = computed(() => {
  const buildOptions = (categories: CategoryItem[], level = 0): any[] => {
    return categories.map(item => ({
      title: item.name,
      value: item.id,
      key: item.id,
      disabled: formData.id === item.id, // 不能选择自己作为父级
      children: item.children ? buildOptions(item.children, level + 1) : undefined
    }))
  }
  return buildOptions(categoryList.value)
})

// 表格列配置
const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 200
  },
  {
    title: '分类编码',
    dataIndex: 'code',
    key: 'code',
    width: 150
  },
  {
    title: '层级',
    dataIndex: 'level',
    key: 'level',
    width: 80
  },
  {
    title: '消息数量',
    dataIndex: 'messageCount',
    key: 'messageCount',
    width: 100
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    key: 'status',
    width: 80
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '编码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ]
}

// 方法定义
const handleAdd = () => {
  resetForm()
  modalVisible.value = true
}

const handleEdit = (record: CategoryItem) => {
  Object.assign(formData, {
    id: record.id,
    name: record.name,
    code: record.code,
    description: record.description || '',
    parentId: record.parentId,
    sort: record.sort,
    isEnabled: record.isEnabled
  })
  modalVisible.value = true
}

const handleAddChild = (record: CategoryItem) => {
  resetForm()
  formData.parentId = record.id
  modalVisible.value = true
}

const handleDelete = async (record: CategoryItem) => {
  try {
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 500))
    message.success('删除成功')
    loadData()
  } catch (error) {
    message.error('删除失败')
  }
}

const handleStatusChange = async (record: CategoryItem) => {
  record.updating = true
  try {
    // 模拟状态切换
    await new Promise(resolve => setTimeout(resolve, 500))
    message.success(`${record.isEnabled ? '启用' : '禁用'}成功`)
  } catch (error) {
    record.isEnabled = !record.isEnabled // 回滚状态
    message.error('状态更新失败')
  } finally {
    record.updating = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    const action = formData.id ? '更新' : '创建'
    message.success(`分类${action}成功`)
    modalVisible.value = false
    loadData()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
}

const handleSearch = () => {
  loadData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.status = undefined
  loadData()
}

const handleRefresh = () => {
  loadData()
}

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    name: '',
    code: '',
    description: '',
    parentId: undefined,
    sort: 0,
    isEnabled: true
  })
}

const getLevelColor = (level: number) => {
  const colors = ['blue', 'green', 'orange', 'red', 'purple']
  return colors[level - 1] || 'default'
}

const getLevelText = (level: number) => {
  const texts = ['一级', '二级', '三级', '四级', '五级']
  return texts[level - 1] || `${level}级`
}

// 模拟数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))

    // 模拟分类数据
    categoryList.value = [
      {
        id: '1',
        name: '系统通知',
        code: 'system',
        description: '系统相关的通知消息',
        level: 1,
        sort: 1,
        isEnabled: true,
        messageCount: 156,
        createTime: '2025-06-15 10:30:00',
        updateTime: '2025-06-15 10:30:00',
        children: [
          {
            id: '1-1',
            name: '维护通知',
            code: 'system_maintenance',
            description: '系统维护相关通知',
            parentId: '1',
            level: 2,
            sort: 1,
            isEnabled: true,
            messageCount: 45,
            createTime: '2025-06-15 10:35:00',
            updateTime: '2025-06-15 10:35:00'
          },
          {
            id: '1-2',
            name: '更新通知',
            code: 'system_update',
            description: '系统更新相关通知',
            parentId: '1',
            level: 2,
            sort: 2,
            isEnabled: true,
            messageCount: 32,
            createTime: '2025-06-15 10:40:00',
            updateTime: '2025-06-15 10:40:00'
          }
        ]
      },
      {
        id: '2',
        name: '业务通知',
        code: 'business',
        description: '业务相关的通知消息',
        level: 1,
        sort: 2,
        isEnabled: true,
        messageCount: 89,
        createTime: '2025-06-15 11:00:00',
        updateTime: '2025-06-15 11:00:00',
        children: [
          {
            id: '2-1',
            name: '审核通知',
            code: 'business_audit',
            description: '审核相关通知',
            parentId: '2',
            level: 2,
            sort: 1,
            isEnabled: true,
            messageCount: 67,
            createTime: '2025-06-15 11:05:00',
            updateTime: '2025-06-15 11:05:00'
          }
        ]
      },
      {
        id: '3',
        name: '活动通知',
        code: 'activity',
        description: '活动相关的通知消息',
        level: 1,
        sort: 3,
        isEnabled: false,
        messageCount: 23,
        createTime: '2025-06-15 11:30:00',
        updateTime: '2025-06-15 11:30:00'
      }
    ]
  } catch (error) {
    message.error('加载数据失败')
    console.error('Load data error:', error)
  } finally {
    loading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.category-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .search-section {
    margin-bottom: 24px;
  }

  .table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #8c8c8c;
        font-size: 14px;
      }
    }

    .category-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .category-icon {
        color: #1890ff;
      }
    }
  }

  .form-help {
    margin-left: 8px;
    color: #8c8c8c;
    font-size: 12px;
  }
}
</style>
