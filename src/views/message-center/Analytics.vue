<template>
  <div class="message-analytics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>消息分析</h2>
        <p>消息统计分析、阅读状态跟踪和数据报告</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-range-picker
            v-model:value="dateRange"
            :presets="datePresets"
            @change="handleDateChange"
          />
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="exportReport">
            <template #icon><download-outlined /></template>
            导出报告
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="key-metrics">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="总消息数"
              :value="metrics.totalMessages"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <message-outlined />
              </template>
              <template #suffix>
                <span class="metric-trend" :class="{ 'trend-up': metrics.messageTrend > 0 }">
                  <arrow-up-outlined v-if="metrics.messageTrend > 0" />
                  <arrow-down-outlined v-else />
                  {{ Math.abs(metrics.messageTrend) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="阅读率"
              :value="metrics.readingRate"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <eye-outlined />
              </template>
              <template #suffix>
                <span class="metric-trend" :class="{ 'trend-up': metrics.readingTrend > 0 }">
                  <arrow-up-outlined v-if="metrics.readingTrend > 0" />
                  <arrow-down-outlined v-else />
                  {{ Math.abs(metrics.readingTrend) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均阅读时长"
              :value="metrics.avgReadTime"
              suffix="秒"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="推送成功率"
              :value="metrics.pushSuccessRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表分析 -->
    <div class="charts-section">
      <a-row :gutter="[24, 24]">
        <!-- 消息趋势图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="消息发送趋势" :bordered="false">
            <template #extra>
              <a-radio-group v-model:value="trendPeriod" size="small">
                <a-radio-button value="7d">7天</a-radio-button>
                <a-radio-button value="30d">30天</a-radio-button>
                <a-radio-button value="90d">90天</a-radio-button>
              </a-radio-group>
            </template>
            <div class="chart-container">
              <message-trend-chart
                :data="trendData"
                :loading="chartsLoading"
                :period="trendPeriod"
              />
            </div>
          </a-card>
        </a-col>

        <!-- 消息类型分布 -->
        <a-col :xs="24" :lg="12">
          <a-card title="消息类型分布" :bordered="false">
            <div class="chart-container">
              <message-type-chart
                :data="typeData"
                :loading="chartsLoading"
              />
            </div>
          </a-card>
        </a-col>

        <!-- 阅读状态分析 -->
        <a-col :xs="24" :lg="12">
          <a-card title="阅读状态分析" :bordered="false">
            <div class="chart-container">
              <reading-status-chart
                :data="readingData"
                :loading="chartsLoading"
              />
            </div>
          </a-card>
        </a-col>

        <!-- 推送渠道效果 -->
        <a-col :xs="24" :lg="12">
          <a-card title="推送渠道效果" :bordered="false">
            <div class="chart-container">
              <channel-performance-chart
                :data="channelData"
                :loading="chartsLoading"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细分析 -->
    <div class="detailed-analysis">
      <a-tabs v-model:activeKey="analysisTab" type="card">
        <!-- 阅读行为分析 -->
        <a-tab-pane key="reading" tab="阅读行为分析">
          <reading-behavior-analysis
            :data="readingBehaviorData"
            :loading="analysisLoading"
          />
        </a-tab-pane>

        <!-- 用户活跃度分析 -->
        <a-tab-pane key="activity" tab="用户活跃度分析">
          <user-activity-analysis
            :data="userActivityData"
            :loading="analysisLoading"
          />
        </a-tab-pane>

        <!-- 消息优先级分析 -->
        <a-tab-pane key="priority" tab="优先级分析">
          <priority-analysis
            :data="priorityData"
            :loading="analysisLoading"
          />
        </a-tab-pane>

        <!-- 推送效果分析 -->
        <a-tab-pane key="push" tab="推送效果分析">
          <push-effectiveness-analysis
            :data="pushEffectivenessData"
            :loading="analysisLoading"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 实时监控 -->
    <div class="realtime-monitor">
      <a-card title="实时监控" :bordered="false">
        <template #extra>
          <a-space>
            <a-switch
              v-model:checked="realtimeEnabled"
              checked-children="开启"
              un-checked-children="关闭"
              @change="toggleRealtime"
            />
            <a-badge :count="realtimeCount" :overflow-count="99">
              <a-button size="small" @click="clearRealtimeData">
                清空
              </a-button>
            </a-badge>
          </a-space>
        </template>
        
        <realtime-monitor
          :enabled="realtimeEnabled"
          :data="realtimeData"
          @update="handleRealtimeUpdate"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import {
  ReloadOutlined,
  DownloadOutlined,
  MessageOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue'

// 导入图表组件
import MessageTrendChart from './components/MessageTrendChart.vue'
import MessageTypeChart from './components/MessageTypeChart.vue'
import ReadingStatusChart from './components/ReadingStatusChart.vue'
import ChannelPerformanceChart from './components/ChannelPerformanceChart.vue'
import ReadingBehaviorAnalysis from './components/ReadingBehaviorAnalysis.vue'
import UserActivityAnalysis from './components/UserActivityAnalysis.vue'
import PriorityAnalysis from './components/PriorityAnalysis.vue'
import PushEffectivenessAnalysis from './components/PushEffectivenessAnalysis.vue'
import RealtimeMonitor from './components/RealtimeMonitor.vue'

// 响应式数据
const chartsLoading = ref(false)
const analysisLoading = ref(false)
const realtimeEnabled = ref(false)
const realtimeCount = ref(0)

// 时间范围
const dateRange = ref<[Dayjs, Dayjs]>([
  dayjs().subtract(7, 'day'),
  dayjs()
])

// 日期预设
const datePresets = [
  { label: '最近7天', value: [dayjs().subtract(7, 'day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'day'), dayjs()] },
  { label: '最近90天', value: [dayjs().subtract(90, 'day'), dayjs()] }
]

// 图表控制
const trendPeriod = ref('7d')
const analysisTab = ref('reading')

// 核心指标
const metrics = reactive({
  totalMessages: 12480,
  messageTrend: 12.5,
  readingRate: 87.3,
  readingTrend: 5.2,
  avgReadTime: 45,
  pushSuccessRate: 96.8
})

// 图表数据
const trendData = ref([])
const typeData = ref([])
const readingData = ref([])
const channelData = ref([])

// 分析数据
const readingBehaviorData = ref([])
const userActivityData = ref([])
const priorityData = ref([])
const pushEffectivenessData = ref([])

// 实时数据
const realtimeData = ref([])
let realtimeTimer: NodeJS.Timeout | null = null

// 数据加载
const loadMetrics = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 更新指标数据
    metrics.totalMessages = 12480
    metrics.messageTrend = 12.5
    metrics.readingRate = 87.3
    metrics.readingTrend = 5.2
    metrics.avgReadTime = 45
    metrics.pushSuccessRate = 96.8
  } catch (error) {
    message.error('加载指标数据失败')
    console.error('Load metrics error:', error)
  }
}

const loadCharts = async () => {
  chartsLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟图表数据
    trendData.value = []
    typeData.value = []
    readingData.value = []
    channelData.value = []
  } catch (error) {
    message.error('加载图表数据失败')
    console.error('Load charts error:', error)
  } finally {
    chartsLoading.value = false
  }
}

const loadAnalysis = async () => {
  analysisLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟分析数据
    readingBehaviorData.value = []
    userActivityData.value = []
    priorityData.value = []
    pushEffectivenessData.value = []
  } catch (error) {
    message.error('加载分析数据失败')
    console.error('Load analysis error:', error)
  } finally {
    analysisLoading.value = false
  }
}

// 事件处理
const handleDateChange = () => {
  refreshData()
}

const refreshData = () => {
  loadMetrics()
  loadCharts()
  loadAnalysis()
}

const exportReport = () => {
  message.success('报告导出功能开发中...')
}

const toggleRealtime = (enabled: boolean) => {
  if (enabled) {
    startRealtimeMonitor()
  } else {
    stopRealtimeMonitor()
  }
}

const startRealtimeMonitor = () => {
  realtimeTimer = setInterval(() => {
    // 模拟实时数据更新
    const newData = {
      id: Date.now(),
      type: 'message_sent',
      content: '新消息推送',
      timestamp: dayjs().format('HH:mm:ss')
    }
    
    realtimeData.value.unshift(newData)
    realtimeCount.value++
    
    // 限制数据量
    if (realtimeData.value.length > 100) {
      realtimeData.value = realtimeData.value.slice(0, 100)
    }
  }, 3000)
}

const stopRealtimeMonitor = () => {
  if (realtimeTimer) {
    clearInterval(realtimeTimer)
    realtimeTimer = null
  }
}

const clearRealtimeData = () => {
  realtimeData.value = []
  realtimeCount.value = 0
}

const handleRealtimeUpdate = (data: any) => {
  // 处理实时数据更新
  console.log('Realtime update:', data)
}

// 生命周期
onMounted(() => {
  refreshData()
})

onUnmounted(() => {
  stopRealtimeMonitor()
})
</script>

<style scoped>
.message-analytics {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.key-metrics {
  margin-bottom: 24px;
}

.key-metrics .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-trend {
  font-size: 12px;
  margin-left: 8px;
}

.metric-trend.trend-up {
  color: #52c41a;
}

.metric-trend:not(.trend-up) {
  color: #ff4d4f;
}

.charts-section {
  margin-bottom: 24px;
}

.charts-section .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-container {
  min-height: 300px;
}

.detailed-analysis {
  margin-bottom: 24px;
}

.detailed-analysis .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.realtime-monitor .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-content-prefix) {
  margin-right: 8px;
  font-size: 20px;
}

:deep(.ant-statistic-content-suffix) {
  margin-left: 8px;
  font-size: 16px;
}

/* 标签页样式 */
:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  background: #fafafa;
  border-color: #e8e8e8;
  color: #666;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background: white;
  color: #1890ff;
  border-bottom-color: white;
}

:deep(.ant-tabs-content-holder) {
  padding: 0;
}

:deep(.ant-tabs-tabpane) {
  padding: 24px;
}

/* 卡片标题样式 */
:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-card-extra) {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .message-analytics {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-title h2 {
    font-size: 20px;
  }

  .header-actions {
    align-self: stretch;
  }

  :deep(.ant-space) {
    width: 100%;
    flex-direction: column;
  }

  :deep(.ant-btn),
  :deep(.ant-picker) {
    width: 100%;
  }

  :deep(.ant-tabs-tabpane) {
    padding: 16px;
  }

  .chart-container {
    min-height: 250px;
  }
}

/* 动画效果 */
.message-analytics {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .message-analytics {
    background: #1f1f1f;
  }

  .page-header,
  .charts-section .ant-card,
  .detailed-analysis .ant-card,
  .realtime-monitor .ant-card {
    background: #2f2f2f;
    border: 1px solid #404040;
  }

  .header-title h2 {
    color: #fff;
  }

  .header-title p {
    color: #ccc;
  }

  :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
    background: #404040;
    border-color: #404040;
    color: #ccc;
  }

  :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
    background: #2f2f2f;
    color: #40a9ff;
    border-bottom-color: #2f2f2f;
  }

  :deep(.ant-card-head-title) {
    color: #fff;
  }
}
</style>
