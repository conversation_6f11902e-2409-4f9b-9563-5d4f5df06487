<template>
  <div class="template-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>模板管理</h2>
        <p>管理消息模板库，支持创建、编辑和删除消息模板</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建模板
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="总模板数"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="启用模板"
              :value="stats.enabled"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="本月使用"
              :value="stats.monthlyUsage"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <bar-chart-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="待审核"
              :value="stats.pending"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card :bordered="false">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索模板名称或内容"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-select
              v-model:value="filterType"
              placeholder="选择模板类型"
              style="width: 100%"
              allow-clear
              @change="handleFilter"
            >
              <a-select-option value="">全部类型</a-select-option>
              <a-select-option value="system">系统通知</a-select-option>
              <a-select-option value="business">业务通知</a-select-option>
              <a-select-option value="marketing">营销推广</a-select-option>
              <a-select-option value="reminder">提醒通知</a-select-option>
            </a-select>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-select
              v-model:value="filterStatus"
              placeholder="选择状态"
              style="width: 100%"
              allow-clear
              @change="handleFilter"
            >
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="1">启用</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
              <a-select-option value="2">待审核</a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <a-card :bordered="false">
        <message-templates-tab
          :templates="templatesList"
          :loading="loading"
          @create="showCreateModal"
          @edit="editTemplate"
          @delete="deleteTemplate"
          @preview="previewTemplate"
          @toggle="toggleTemplate"
        />
      </a-card>
    </div>

    <!-- 创建/编辑模板弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑模板' : '新建模板'"
      width="800px"
      :confirm-loading="submitting"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <template-form
        ref="templateFormRef"
        :template="currentTemplate"
        :is-edit="isEdit"
      />
    </a-modal>

    <!-- 模板预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="模板预览"
      width="600px"
      :footer="null"
    >
      <template-preview
        :template="previewTemplateData"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  BarChartOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import MessageTemplatesTab from './components/MessageTemplatesTab.vue'
import TemplateForm from './components/TemplateForm.vue'
import TemplatePreview from './components/TemplatePreview.vue'

// 响应式数据
const loading = ref(false)
const templatesList = ref([])
const searchKeyword = ref('')
const filterType = ref('')
const filterStatus = ref('')

// 统计数据
const stats = reactive({
  total: 0,
  enabled: 0,
  monthlyUsage: 0,
  pending: 0
})

// 弹窗相关
const modalVisible = ref(false)
const previewVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const currentTemplate = ref(null)
const previewTemplateData = ref(null)
const templateFormRef = ref()

// 生命周期
onMounted(() => {
  loadTemplates()
})

// 加载模板列表
const loadTemplates = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟数据
    const mockData = [
      {
        id: 1,
        name: '系统维护通知',
        type: 'system',
        content: '尊敬的用户，系统将于{time}进行维护，预计耗时{duration}，请提前做好准备。',
        status: 1,
        usageCount: 156,
        createTime: '2025-06-15 10:30:00',
        updateTime: '2025-06-20 14:20:00'
      },
      {
        id: 2,
        name: '会议通知模板',
        type: 'business',
        content: '会议主题：{subject}\n会议时间：{time}\n会议地点：{location}\n请准时参加。',
        status: 1,
        usageCount: 89,
        createTime: '2025-06-10 09:15:00',
        updateTime: '2025-06-18 16:45:00'
      }
    ]

    templatesList.value = mockData

    // 更新统计数据
    stats.total = mockData.length
    stats.enabled = mockData.filter(t => t.status === 1).length
    stats.monthlyUsage = mockData.reduce((sum, t) => sum + t.usageCount, 0)
    stats.pending = mockData.filter(t => t.status === 2).length

  } catch (error) {
    message.error('加载模板列表失败')
    console.error('Load templates error:', error)
  } finally {
    loading.value = false
  }
}

// 事件处理
const refreshData = () => {
  loadTemplates()
}

const handleSearch = () => {
  loadTemplates()
}

const handleFilter = () => {
  loadTemplates()
}

const showCreateModal = () => {
  isEdit.value = false
  currentTemplate.value = null
  modalVisible.value = true
}

const editTemplate = (template) => {
  isEdit.value = true
  currentTemplate.value = template
  modalVisible.value = true
}

const deleteTemplate = async (template) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    message.success('删除成功')
    loadTemplates()
  } catch (error) {
    message.error('删除失败')
  }
}

const previewTemplate = (template) => {
  previewTemplateData.value = template
  previewVisible.value = true
}

const toggleTemplate = async (template) => {
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    template.status = template.status === 1 ? 0 : 1
    message.success(template.status === 1 ? '已启用' : '已禁用')
    loadTemplates()
  } catch (error) {
    message.error('操作失败')
  }
}

const handleSubmit = async () => {
  try {
    submitting.value = true
    // 验证表单
    await templateFormRef.value.validate()
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '编辑成功' : '创建成功')
    modalVisible.value = false
    loadTemplates()
  } catch (error) {
    console.error('Submit error:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  currentTemplate.value = null
}
</script>

<style scoped lang="less">
.template-manage {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        color: #262626;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;
  }

  .filter-section {
    margin-bottom: 24px;
  }

  .template-list {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
