<template>
  <div class="message-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>消息中心</h1>
        <p>统一的消息推送、管理和监控平台</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总消息数" :value="stats.totalMessages" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="未读消息" :value="stats.unreadMessages" :value-style="{ color: '#ff4d4f' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="今日推送" :value="stats.todayPush" :value-style="{ color: '#1890ff' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="阅读率" :value="stats.readingRate" suffix="%" :value-style="{ color: '#52c41a' }" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToMessageList">
              <div class="action-icon">
                <message-outlined />
              </div>
              <div class="action-content">
                <h3>消息列表</h3>
                <p>查看和管理所有消息</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToPushManage">
              <div class="action-icon">
                <send-outlined />
              </div>
              <div class="action-content">
                <h3>推送管理</h3>
                <p>配置推送规则和策略</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToTemplateManage">
              <div class="action-icon">
                <file-text-outlined />
              </div>
              <div class="action-content">
                <h3>模板管理</h3>
                <p>管理消息模板库</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToAnalytics">
              <div class="action-icon">
                <bar-chart-outlined />
              </div>
              <div class="action-content">
                <h3>数据分析</h3>
                <p>消息统计和分析报告</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 功能模块区域 -->
    <div class="feature-modules">
      <a-row :gutter="[24, 24]">
        <!-- 消息推送服务 -->
        <a-col :xs="24" :lg="12">
          <a-card title="消息推送服务" :bordered="false">
            <template #extra>
              <a @click="goToPushManage">查看更多</a>
            </template>
            
            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>多渠道推送</h4>
                    <p>支持渝快政、短信、邮件等多种推送渠道</p>
                  </div>
                </div>
                
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>智能规则引擎</h4>
                    <p>基于条件的自动推送规则配置</p>
                  </div>
                </div>
                
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>精准推送</h4>
                    <p>用户标签定制和大数据算法驱动</p>
                  </div>
                </div>
              </div>
              
              <a-button type="primary" block @click="goToPushManage">
                管理推送服务
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 消息分类管理 -->
        <a-col :xs="24" :lg="12">
          <a-card title="消息分类管理" :bordered="false">
            <template #extra>
              <a @click="goToCategoryManage">查看更多</a>
            </template>
            
            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>多级分类体系</h4>
                    <p>支持多级分类架构和标签关联</p>
                  </div>
                </div>
                
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>智能分类推荐</h4>
                    <p>基于用户行为的智能分类推荐</p>
                  </div>
                </div>
                
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>权限控制</h4>
                    <p>分类权限设置和访问控制</p>
                  </div>
                </div>
              </div>
              
              <a-button type="primary" block @click="goToCategoryManage">
                管理消息分类
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <a-card title="最近活动" :bordered="false">
        <template #extra>
          <a @click="viewAllActivities">查看全部</a>
        </template>
        
        <a-list
          :data-source="recentActivities"
          :loading="activitiesLoading"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getActivityColor(item.type) }">
                    <component :is="getActivityIcon(item.type)" />
                  </a-avatar>
                </template>
                <template #title>
                  <a @click="viewActivity(item)">{{ item.title }}</a>
                </template>
                <template #description>
                  {{ item.description }} · {{ item.time }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MessageOutlined,
  SendOutlined,
  FileTextOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  BellOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 响应式数据
const activitiesLoading = ref(false)

// 统计数据
const stats = reactive({
  totalMessages: 1248,
  unreadMessages: 23,
  todayPush: 156,
  readingRate: 87.5
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    type: 'send',
    title: '系统维护通知',
    description: '向全体用户推送系统维护通知',
    time: '2小时前'
  },
  {
    id: 2,
    type: 'template',
    title: '新建消息模板',
    description: '创建了"会议通知"消息模板',
    time: '4小时前'
  },
  {
    id: 3,
    type: 'rule',
    title: '更新推送规则',
    description: '修改了紧急消息推送规则',
    time: '6小时前'
  },
  {
    id: 4,
    type: 'category',
    title: '新增消息分类',
    description: '添加了"项目通知"分类',
    time: '1天前'
  }
])

// 导航方法
const goToMessageList = () => {
  router.push('/message-center/list')
}

const goToPushManage = () => {
  router.push('/message-center/push')
}

const goToTemplateManage = () => {
  router.push('/message-center/template')
}

const goToAnalytics = () => {
  router.push('/message-center/analytics')
}

const goToCategoryManage = () => {
  router.push('/message-center/category')
}

const viewAllActivities = () => {
  router.push('/message-center/activity')
}

const viewActivity = (activity: any) => {
  message.info(`查看活动: ${activity.title}`)
}

// 工具方法
const getActivityColor = (type: string) => {
  const colors = {
    send: '#1890ff',
    template: '#52c41a',
    rule: '#faad14',
    category: '#722ed1'
  }
  return colors[type as keyof typeof colors] || '#d9d9d9'
}

const getActivityIcon = (type: string) => {
  const icons = {
    send: SendOutlined,
    template: FileTextOutlined,
    rule: SettingOutlined,
    category: BellOutlined
  }
  return icons[type as keyof typeof icons] || UserOutlined
}

// 数据加载
const loadStats = async () => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 这里可以从API加载真实的统计数据
    stats.totalMessages = 1248
    stats.unreadMessages = 23
    stats.todayPush = 156
    stats.readingRate = 87.5
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

const loadRecentActivities = async () => {
  activitiesLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 这里可以从API加载真实的活动数据
  } catch (error) {
    message.error('加载最近活动失败')
    console.error('Load recent activities error:', error)
  } finally {
    activitiesLoading.value = false
  }
}

// 页面初始化
onMounted(() => {
  loadStats()
  loadRecentActivities()
})
</script>

<style scoped>
.message-center {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  margin-bottom: 24px;
  color: white;
}

.header-content h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: white;
}

.header-content p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-stats {
  min-width: 400px;
}

:deep(.header-stats .ant-statistic-title) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.header-stats .ant-statistic-content) {
  color: white;
  font-weight: 600;
}

.quick-actions {
  margin-bottom: 24px;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.action-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-right: 16px;
  flex-shrink: 0;
}

.action-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.feature-modules {
  margin-bottom: 24px;
}

.module-content {
  padding: 8px 0;
}

.feature-list {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  font-size: 16px;
  color: #52c41a;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-text h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.feature-text p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.recent-activity {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 卡片样式优化 */
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.ant-card-extra a) {
  color: #1890ff;
  font-size: 14px;
}

:deep(.ant-card-extra a:hover) {
  color: #40a9ff;
}

/* 列表样式优化 */
:deep(.ant-list-item) {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-list-item:last-child) {
  border-bottom: none;
}

:deep(.ant-list-item-meta-title a) {
  color: #333;
  font-weight: 500;
}

:deep(.ant-list-item-meta-title a:hover) {
  color: #1890ff;
}

:deep(.ant-list-item-meta-description) {
  color: #666;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .header-stats {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .message-center {
    padding: 16px;
  }

  .page-header {
    padding: 24px 20px;
  }

  .header-content h1 {
    font-size: 24px;
  }

  .header-content p {
    font-size: 14px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }

  .action-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
  }

  .feature-icon {
    margin-right: 0;
    margin-bottom: 8px;
    margin-top: 0;
  }
}

/* 动画效果 */
.message-center {
  animation: fadeIn 0.6s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-card {
  animation: slideInUp 0.6s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .message-center {
    background: #1f1f1f;
  }

  .action-card {
    background: #2f2f2f;
    border-color: #404040;
  }

  .action-content h3 {
    color: #fff;
  }

  .action-content p {
    color: #ccc;
  }

  .feature-text h4 {
    color: #fff;
  }

  .feature-text p {
    color: #ccc;
  }

  :deep(.ant-card) {
    background: #2f2f2f;
    border-color: #404040;
  }

  :deep(.ant-card-head) {
    border-color: #404040;
  }

  :deep(.ant-card-head-title) {
    color: #fff;
  }

  :deep(.ant-list-item) {
    border-color: #404040;
  }

  :deep(.ant-list-item-meta-title a) {
    color: #fff;
  }

  :deep(.ant-list-item-meta-description) {
    color: #ccc;
  }
}
</style>
