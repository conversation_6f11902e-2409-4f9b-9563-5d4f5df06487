<template>
  <div class="home-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <a-card>
        <div class="welcome-content">
          <h1>欢迎使用党建管理系统</h1>
          <p>集成荣誉申报、数据体检、智能审核等功能的综合管理平台</p>
        </div>
      </a-card>
    </div>

    <!-- 功能模块导航 -->
    <div class="modules-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/honor/apply')">
            <template #cover>
              <div class="module-icon">
                <trophy-outlined />
              </div>
            </template>
            <a-card-meta title="荣誉申报" description="荣誉申报和审核管理，支持在线申报、审核流程等功能" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/health-check/dashboard')">
            <template #cover>
              <div class="module-icon">
                <dashboard-outlined />
              </div>
            </template>
            <a-card-meta title="数据体检" description="数据质量监控和体检，支持规则配置、执行监控、结果管理" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/review/application')">
            <template #cover>
              <div class="module-icon">
                <audit-outlined />
              </div>
            </template>
            <a-card-meta title="智能审核" description="智能入围审核系统，支持申报管理、审核管理、结果管理、申诉管理" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/audit-confirmation')">
            <template #cover>
              <div class="module-icon">
                <check-circle-outlined />
              </div>
            </template>
            <a-card-meta title="审核确认" description="培育对象评分结果审核确认管理，支持流程配置、人工校正、历史追溯等功能" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/indicator-rules')">
            <template #cover>
              <div class="module-icon">
                <setting-outlined />
              </div>
            </template>
            <a-card-meta title="指标规则管理" description="支持指标的全生命周期管理、权重设置、演算分析和模板管理" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/special-team')">
            <template #cover>
              <div class="module-icon">
                <team-outlined />
              </div>
            </template>
            <a-card-meta title="专班推荐" description="双层评审机制的推荐系统，支持培育对象管理、评选标准配置等功能" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/dynamics')">
            <template #cover>
              <div class="module-icon">
                <thunderbolt-outlined />
              </div>
            </template>
            <a-card-meta title="创建动态管理" description="动态内容管理系统，支持动态创建、编辑、发布和媒体文件管理" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/sensitive-words')">
            <template #cover>
              <div class="module-icon">
                <safety-outlined />
              </div>
            </template>
            <a-card-meta title="敏感词管理" description="多媒体内容检测系统，支持敏感词库管理、过滤策略配置等功能" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/case-promotion')">
            <template #cover>
              <div class="module-icon">
                <book-outlined />
              </div>
            </template>
            <a-card-meta title="案例推广" description="案例库管理和展示系统，支持案例创建、分类管理、多视图展示" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/model-agency-dashboard')">
            <template #cover>
              <div class="module-icon">
                <dashboard-outlined />
              </div>
            </template>
            <a-card-meta title="模范机关总览看板" description="数据可视化看板，展示机关创建动态、党建指数分布、考核督查数据" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/workflow')">
            <template #cover>
              <div class="module-icon">
                <apartment-outlined />
              </div>
            </template>
            <a-card-meta title="流程引擎" description="工作流管理系统，支持流程设计、监控、任务管理和模板管理" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/message-center')">
            <template #cover>
              <div class="module-icon">
                <message-outlined />
              </div>
            </template>
            <a-card-meta title="消息中心" description="消息推送和管理系统，支持多渠道推送、模板管理、统计分析" />
          </a-card>
        </a-col>

        <a-col :xs="24" :sm="12" :md="8">
          <a-card hoverable class="module-card" @click="navigateTo('/project-dashboard')">
            <template #cover>
              <div class="module-icon">
                <project-outlined />
              </div>
            </template>
            <a-card-meta title="选育树推项目看板" description="项目数据看板，展示项目阶段数据、创建动态、指标分析等信息" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快速统计 -->
    <div class="statistics-section">
      <a-card title="系统概览">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="荣誉申报" value="128" suffix="项" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="数据体检" value="45" suffix="次" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="智能审核" value="89" suffix="个" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="审核确认" value="62" suffix="项" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="指标管理" value="15" suffix="个" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="权重方案" value="8" suffix="套" />
          </a-col>
          <a-col :xs="24" :sm="12" :md="6" :lg="4">
            <a-statistic title="系统用户" value="256" suffix="人" />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  TrophyOutlined,
  DashboardOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  BookOutlined,
  ApartmentOutlined,
  MessageOutlined,
  ProjectOutlined
} from '@ant-design/icons-vue'

const router = useRouter()

function navigateTo(path: string) {
  router.push(path)
}
</script>

<style lang="scss" scoped>
.home-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .welcome-section {
    margin-bottom: 32px;

    .welcome-content {
      text-align: center;
      padding: 40px 20px;

      h1 {
        color: #1890ff;
        font-size: 32px;
        margin-bottom: 16px;
      }

      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .modules-section {
    margin-bottom: 32px;

    .module-card {
      height: 100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .module-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px;
        font-size: 48px;
        color: #1890ff;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      }
    }
  }

  .statistics-section {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home-page {
    padding: 16px;

    .welcome-content {
      padding: 24px 16px;

      h1 {
        font-size: 24px;
      }

      p {
        font-size: 14px;
      }
    }

    .module-icon {
      height: 80px !important;
      font-size: 36px !important;
    }
  }
}
</style>