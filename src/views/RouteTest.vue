<template>
  <div class="route-test-page">
    <div class="page-header">
      <h2>路由测试工具</h2>
      <p>用于测试敏感词管理模块的所有路由是否正常工作</p>
    </div>

    <div class="test-controls">
      <a-space size="middle">
        <a-button 
          type="primary" 
          :loading="testing" 
          @click="runAllTests"
        >
          <template #icon><play-circle-outlined /></template>
          运行所有测试
        </a-button>
        <a-button @click="clearResults">
          <template #icon><clear-outlined /></template>
          清空结果
        </a-button>
        <a-button @click="exportReport">
          <template #icon><download-outlined /></template>
          导出报告
        </a-button>
      </a-space>
    </div>

    <div class="test-results" v-if="testResults.length > 0">
      <a-card title="测试结果" :bordered="false">
        <template #extra>
          <a-space>
            <a-tag color="green">成功: {{ successCount }}</a-tag>
            <a-tag color="red">失败: {{ failCount }}</a-tag>
            <a-tag color="blue">总计: {{ testResults.length }}</a-tag>
          </a-space>
        </template>

        <a-table 
          :columns="columns" 
          :data-source="testResults" 
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.success ? 'green' : 'red'">
                {{ record.success ? '成功' : '失败' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button 
                  size="small" 
                  type="link" 
                  @click="testSingleRoute(record)"
                  :loading="record.testing"
                >
                  重新测试
                </a-button>
                <a-button 
                  size="small" 
                  type="link" 
                  @click="navigateToRoute(record.path)"
                >
                  访问页面
                </a-button>
              </a-space>
            </template>
            <template v-else-if="column.key === 'error'">
              <span v-if="record.error" class="error-text">{{ record.error }}</span>
              <span v-else class="success-text">无错误</span>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <div class="route-list">
      <a-card title="敏感词管理路由列表" :bordered="false">
        <a-list 
          :data-source="sensitiveWordsRoutes" 
          item-layout="horizontal"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button 
                  size="small" 
                  type="link" 
                  @click="navigateToRoute(item.path)"
                >
                  访问
                </a-button>
              </template>
              <a-list-item-meta
                :title="item.title"
                :description="`路径: ${item.path} | 名称: ${item.name}`"
              />
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  ClearOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import {
  sensitiveWordsRoutes,
  testAllSensitiveWordsRoutes,
  testSingleRoute,
  generateRouteTestReport,
  type RouteTestResult
} from '@/utils/routeTest'

const router = useRouter()

// 响应式数据
const testing = ref(false)
const testResults = ref<RouteTestResult[]>([])

// 计算属性
const successCount = computed(() => testResults.value.filter(r => r.success).length)
const failCount = computed(() => testResults.value.length - successCount.value)

// 表格列定义
const columns = [
  { title: '路由名称', dataIndex: 'title', key: 'title' },
  { title: '路径', dataIndex: 'path', key: 'path' },
  { title: '状态', key: 'status', align: 'center' },
  { title: '错误信息', key: 'error' },
  { title: '测试时间', dataIndex: 'timestamp', key: 'timestamp' },
  { title: '操作', key: 'action', align: 'center' }
]

// 方法定义
async function runAllTests() {
  testing.value = true
  try {
    message.loading('正在运行路由测试...', 0)
    const results = await testAllSensitiveWordsRoutes(router)
    testResults.value = results
    
    const successCount = results.filter(r => r.success).length
    const totalCount = results.length
    
    message.destroy()
    if (successCount === totalCount) {
      message.success(`所有路由测试通过！(${successCount}/${totalCount})`)
    } else {
      message.warning(`部分路由测试失败 (${successCount}/${totalCount})`)
    }
  } catch (error: any) {
    message.destroy()
    message.error(`测试运行失败: ${error.message}`)
  } finally {
    testing.value = false
  }
}

async function testSingleRoute(route: any) {
  route.testing = true
  try {
    const result = await testSingleRoute(router, route)
    const index = testResults.value.findIndex(r => r.path === route.path)
    if (index !== -1) {
      testResults.value[index] = result
    }
    
    if (result.success) {
      message.success(`路由 ${route.title} 测试通过`)
    } else {
      message.error(`路由 ${route.title} 测试失败: ${result.error}`)
    }
  } catch (error: any) {
    message.error(`测试失败: ${error.message}`)
  } finally {
    route.testing = false
  }
}

function navigateToRoute(path: string) {
  try {
    router.push(path).catch((error) => {
      message.error(`页面跳转失败: ${error.message}`)
    })
  } catch (error: any) {
    message.error(`页面跳转异常: ${error.message}`)
  }
}

function clearResults() {
  testResults.value = []
  message.success('测试结果已清空')
}

function exportReport() {
  if (testResults.value.length === 0) {
    message.warning('没有测试结果可导出')
    return
  }
  
  const report = generateRouteTestReport(testResults.value)
  const blob = new Blob([report], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `route-test-report-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  
  message.success('测试报告已导出')
}
</script>

<style scoped>
.route-test-page {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
}

.page-header p {
  margin: 0;
  color: #666;
}

.test-controls {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.test-results {
  margin-bottom: 24px;
}

.error-text {
  color: #ff4d4f;
}

.success-text {
  color: #52c41a;
}

.route-list .ant-list-item {
  padding: 12px 0;
}
</style>
