<template>
  <div class="special-team-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>专班推荐系统</h2>
        <p>双层评审机制的专班推荐管理系统，支持分专班初审比选和总专班综合评审</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-card :bordered="false" class="content-card">
        <a-tabs
          v-model:activeKey="activeTab"
          type="card"
          size="large"
          @change="handleTabChange"
        >
          <!-- 分专班管理 -->
          <a-tab-pane key="branch" tab="分专班管理">
            <div class="tab-content">
              <BranchTeamManagement />
            </div>
          </a-tab-pane>

          <!-- 总专班管理 -->
          <a-tab-pane key="general" tab="总专班管理">
            <div class="tab-content">
              <GeneralTeamManagement />
            </div>
          </a-tab-pane>

          <!-- 历史记录 -->
          <a-tab-pane key="history" tab="历史记录">
            <div class="tab-content">
              <HistoryManagement />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BranchTeamManagement from './components/BranchTeamManagement.vue'
import GeneralTeamManagement from './components/GeneralTeamManagement.vue'
import HistoryManagement from './components/HistoryManagement.vue'

// 响应式数据
const activeTab = ref<string>('branch')

// Tab切换处理
const handleTabChange = (key: string) => {
  activeTab.value = key
  console.log('切换到Tab:', key)
}

// 页面初始化
onMounted(() => {
  console.log('专班推荐系统已加载')
})
</script>

<style scoped>
.special-team-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  margin-bottom: 24px;
}

.header-title h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-card {
  border-radius: 8px;
}

.content-card :deep(.ant-card-body) {
  padding: 0;
}

.content-card :deep(.ant-tabs-card .ant-tabs-content) {
  padding: 24px;
  background: white;
}

.content-card :deep(.ant-tabs-card .ant-tabs-tabpane) {
  background: white;
}

.tab-content {
  min-height: 600px;
}

/* Tab样式优化 */
.content-card :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  background: #fafafa;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
}

.content-card :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background: white;
  border-bottom-color: white;
  color: #1890ff;
  font-weight: 600;
}

.content-card :deep(.ant-tabs-card > .ant-tabs-nav) {
  margin: 0;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}
</style>
