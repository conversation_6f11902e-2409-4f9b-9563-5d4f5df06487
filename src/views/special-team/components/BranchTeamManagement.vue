<template>
  <div class="branch-team-management">
    <!-- 功能导航 -->
    <div class="function-nav">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'standards' }"
            @click="setActiveFunction('standards')"
          >
            <div class="nav-content">
              <setting-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>评选标准配置</h4>
                <p>设置多维度评选标准</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'audit' }"
            @click="setActiveFunction('audit')"
          >
            <div class="nav-content">
              <audit-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>培育对象审核</h4>
                <p>审核培育对象信息</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'comparison' }"
            @click="setActiveFunction('comparison')"
          >
            <div class="nav-content">
              <trophy-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>优异对象比选</h4>
                <p>多维度比选分析</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'recommend' }"
            @click="setActiveFunction('recommend')"
          >
            <div class="nav-content">
              <send-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>推荐推送</h4>
                <p>推送至总专班</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能内容区域 -->
    <div class="function-content">
      <!-- 评选标准配置 -->
      <div v-if="activeFunction === 'standards'" class="content-section">
        <StandardsConfiguration />
      </div>

      <!-- 培育对象审核 -->
      <div v-if="activeFunction === 'audit'" class="content-section">
        <CandidateAudit />
      </div>

      <!-- 优异对象比选 -->
      <div v-if="activeFunction === 'comparison'" class="content-section">
        <ExcellentComparison />
      </div>

      <!-- 推荐推送 -->
      <div v-if="activeFunction === 'recommend'" class="content-section">
        <RecommendPush />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  SettingOutlined, 
  AuditOutlined, 
  TrophyOutlined, 
  SendOutlined 
} from '@ant-design/icons-vue'
import StandardsConfiguration from './branch/StandardsConfiguration.vue'
import CandidateAudit from './branch/CandidateAudit.vue'
import ExcellentComparison from './branch/ExcellentComparison.vue'
import RecommendPush from './branch/RecommendPush.vue'

// 响应式数据
const activeFunction = ref<string>('standards')

// 设置活动功能
const setActiveFunction = (func: string) => {
  activeFunction.value = func
}
</script>

<style scoped>
.branch-team-management {
  padding: 0;
}

.function-nav {
  margin-bottom: 24px;
}

.nav-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-card.active {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.nav-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
  flex-shrink: 0;
}

.nav-text h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.nav-text p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.nav-card.active .nav-text h4 {
  color: #1890ff;
}

.function-content {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.content-section {
  padding: 24px;
  min-height: 500px;
}
</style>
