<template>
  <div class="history-management">
    <!-- 功能导航 -->
    <div class="function-nav">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'audit' }"
            @click="setActiveFunction('audit')"
          >
            <div class="nav-content">
              <history-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>审核历史</h4>
                <p>查询审核历史记录</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'push' }"
            @click="setActiveFunction('push')"
          >
            <div class="nav-content">
              <cloud-upload-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>推送历史</h4>
                <p>推送历史管理</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'archive' }"
            @click="setActiveFunction('archive')"
          >
            <div class="nav-content">
              <file-protect-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>名单归档</h4>
                <p>名单归档与版本控制</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能内容区域 -->
    <div class="function-content">
      <!-- 审核历史 -->
      <div v-if="activeFunction === 'audit'" class="content-section">
        <AuditHistory />
      </div>

      <!-- 推送历史 -->
      <div v-if="activeFunction === 'push'" class="content-section">
        <PushHistory />
      </div>

      <!-- 名单归档 -->
      <div v-if="activeFunction === 'archive'" class="content-section">
        <ListArchive />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  HistoryOutlined, 
  CloudUploadOutlined, 
  FileProtectOutlined 
} from '@ant-design/icons-vue'
import AuditHistory from './history/AuditHistory.vue'
import PushHistory from './history/PushHistory.vue'
import ListArchive from './history/ListArchive.vue'

// 响应式数据
const activeFunction = ref<string>('audit')

// 设置活动功能
const setActiveFunction = (func: string) => {
  activeFunction.value = func
}
</script>

<style scoped>
.history-management {
  padding: 0;
}

.function-nav {
  margin-bottom: 24px;
}

.nav-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-card.active {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.nav-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
  flex-shrink: 0;
}

.nav-text h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.nav-text p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.nav-card.active .nav-text h4 {
  color: #1890ff;
}

.function-content {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.content-section {
  padding: 24px;
  min-height: 500px;
}
</style>
