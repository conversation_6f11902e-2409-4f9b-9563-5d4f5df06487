<template>
  <div class="general-team-management">
    <!-- 功能导航 -->
    <div class="function-nav">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'receive' }"
            @click="setActiveFunction('receive')"
          >
            <div class="nav-content">
              <inbox-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>推荐接收</h4>
                <p>接收分专班推荐</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'review' }"
            @click="setActiveFunction('review')"
          >
            <div class="nav-content">
              <eye-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>综合评审</h4>
                <p>多维度对比分析</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'determine' }"
            @click="setActiveFunction('determine')"
          >
            <div class="nav-content">
              <check-circle-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>名单确定</h4>
                <p>确定拟命名对象</p>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card 
            hoverable 
            class="nav-card"
            :class="{ active: activeFunction === 'feedback' }"
            @click="setActiveFunction('feedback')"
          >
            <div class="nav-content">
              <notification-outlined class="nav-icon" />
              <div class="nav-text">
                <h4>结果反馈</h4>
                <p>通知评审结果</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能内容区域 -->
    <div class="function-content">
      <!-- 推荐接收 -->
      <div v-if="activeFunction === 'receive'" class="content-section">
        <RecommendReceive />
      </div>

      <!-- 综合评审 -->
      <div v-if="activeFunction === 'review'" class="content-section">
        <ComprehensiveReview />
      </div>

      <!-- 名单确定 -->
      <div v-if="activeFunction === 'determine'" class="content-section">
        <ListDetermination />
      </div>

      <!-- 结果反馈 -->
      <div v-if="activeFunction === 'feedback'" class="content-section">
        <ResultFeedback />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { 
  InboxOutlined, 
  EyeOutlined, 
  CheckCircleOutlined, 
  NotificationOutlined 
} from '@ant-design/icons-vue'
import RecommendReceive from './general/RecommendReceive.vue'
import ComprehensiveReview from './general/ComprehensiveReview.vue'
import ListDetermination from './general/ListDetermination.vue'
import ResultFeedback from './general/ResultFeedback.vue'

// 响应式数据
const activeFunction = ref<string>('receive')

// 设置活动功能
const setActiveFunction = (func: string) => {
  activeFunction.value = func
}
</script>

<style scoped>
.general-team-management {
  padding: 0;
}

.function-nav {
  margin-bottom: 24px;
}

.nav-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.nav-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-card.active {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.nav-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.nav-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
  flex-shrink: 0;
}

.nav-text h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.nav-text p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.nav-card.active .nav-text h4 {
  color: #1890ff;
}

.function-content {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.content-section {
  padding: 24px;
  min-height: 500px;
}
</style>
