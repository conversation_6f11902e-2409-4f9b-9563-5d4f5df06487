<template>
  <div class="comprehensive-review">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>综合评审</h3>
        <p>多维度对比分析推荐对象，记录评审意见与命名理由</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="startBatchReview" :disabled="selectedRowKeys.length === 0">
            <template #icon><audit-outlined /></template>
            批量评审
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 评审进度概览 -->
    <div class="progress-section">
      <a-card title="评审进度概览" size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <div class="progress-item">
              <div class="progress-number">{{ reviewProgress.total }}</div>
              <div class="progress-label">总对象数</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="progress-item">
              <div class="progress-number pending">{{ reviewProgress.pending }}</div>
              <div class="progress-label">待评审</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="progress-item">
              <div class="progress-number reviewing">{{ reviewProgress.reviewing }}</div>
              <div class="progress-label">评审中</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="progress-item">
              <div class="progress-number completed">{{ reviewProgress.completed }}</div>
              <div class="progress-label">已完成</div>
            </div>
          </a-col>
        </a-row>
        
        <div class="progress-bar">
          <a-progress 
            :percent="Math.round((reviewProgress.completed / reviewProgress.total) * 100)"
            :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
          />
        </div>
      </a-card>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card size="small">
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="来源专班">
            <a-select 
              v-model:value="filterForm.branchTeam" 
              placeholder="请选择专班"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="branch1">组织部专班</a-select-option>
              <a-select-option value="branch2">宣传部专班</a-select-option>
              <a-select-option value="branch3">人事部专班</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="评审状态">
            <a-select 
              v-model:value="filterForm.reviewStatus" 
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="pending">待评审</a-select-option>
              <a-select-option value="reviewing">评审中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="综合得分">
            <a-input-group compact>
              <a-input-number 
                v-model:value="filterForm.minScore"
                placeholder="最低分"
                style="width: 80px"
                :min="0"
                :max="100"
              />
              <a-input
                style="width: 30px; text-align: center; pointer-events: none"
                placeholder="~"
                disabled
              />
              <a-input-number 
                v-model:value="filterForm.maxScore"
                placeholder="最高分"
                style="width: 80px"
                :min="0"
                :max="100"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleFilter">
                <template #icon><search-outlined /></template>
                筛选
              </a-button>
              <a-button @click="resetFilter">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 对象列表 -->
    <div class="candidates-section">
      <a-card title="推荐对象列表" size="small">
        <template #extra>
          <a-space>
            <span>排序：</span>
            <a-select v-model:value="sortBy" style="width: 120px" @change="handleSort">
              <a-select-option value="score">综合得分</a-select-option>
              <a-select-option value="time">推荐时间</a-select-option>
              <a-select-option value="branch">来源专班</a-select-option>
            </a-select>
          </a-space>
        </template>
        
        <a-table
          :columns="candidateColumns"
          :data-source="candidateList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
          @change="handleTableChange"
          :scroll="{ x: 1400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-button type="link" @click="viewCandidateDetail(record)">
                {{ record.name }}
              </a-button>
            </template>
            
            <template v-if="column.key === 'branchTeam'">
              <a-tag color="blue">{{ record.branchTeam }}</a-tag>
            </template>
            
            <template v-if="column.key === 'reviewStatus'">
              <a-tag :color="getReviewStatusColor(record.reviewStatus)">
                {{ getReviewStatusText(record.reviewStatus) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'scores'">
              <div class="scores-display">
                <div class="score-item">
                  <span class="label">综合：</span>
                  <span class="value">{{ record.comprehensiveScore }}分</span>
                </div>
                <div class="score-item">
                  <span class="label">评审：</span>
                  <span class="value">{{ record.reviewScore || '-' }}分</span>
                </div>
              </div>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewCandidateDetail(record)">
                  查看
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="startReview(record)"
                  :disabled="record.reviewStatus === 'completed'"
                >
                  评审
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="compareCandidate(record)"
                >
                  对比
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 候选对象详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="候选对象详情"
      width="900px"
      :footer="null"
    >
      <div v-if="currentCandidate" class="candidate-detail">
        <a-tabs>
          <a-tab-pane key="basic" tab="基本信息">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="姓名">{{ currentCandidate.name }}</a-descriptions-item>
              <a-descriptions-item label="性别">{{ currentCandidate.gender }}</a-descriptions-item>
              <a-descriptions-item label="年龄">{{ currentCandidate.age }}岁</a-descriptions-item>
              <a-descriptions-item label="职务">{{ currentCandidate.position }}</a-descriptions-item>
              <a-descriptions-item label="部门">{{ currentCandidate.department }}</a-descriptions-item>
              <a-descriptions-item label="来源专班">
                <a-tag color="blue">{{ currentCandidate.branchTeam }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="推荐时间">{{ formatDate(currentCandidate.recommendTime) }}</a-descriptions-item>
              <a-descriptions-item label="评审状态">
                <a-tag :color="getReviewStatusColor(currentCandidate.reviewStatus)">
                  {{ getReviewStatusText(currentCandidate.reviewStatus) }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </a-tab-pane>
          
          <a-tab-pane key="scores" tab="评分详情">
            <div class="scores-detail">
              <h4>原始评分</h4>
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-statistic title="综合得分" :value="currentCandidate.comprehensiveScore" suffix="分" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="成绩排名" :value="currentCandidate.gradeRank" suffix="名" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="能力评估" :value="currentCandidate.abilityScore" suffix="分" />
                </a-col>
              </a-row>
              
              <div v-if="currentCandidate.reviewScore" class="review-scores">
                <h4>评审评分</h4>
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-statistic title="评审得分" :value="currentCandidate.reviewScore" suffix="分" />
                  </a-col>
                  <a-col :span="8">
                    <a-statistic title="最终得分" :value="currentCandidate.finalScore || 0" suffix="分" />
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="review" tab="评审记录">
            <div v-if="currentCandidate.reviewComments" class="review-record">
              <div class="review-item">
                <h5>评审意见</h5>
                <div class="review-content">{{ currentCandidate.reviewComments }}</div>
                <div class="review-meta">
                  <span>评审人：{{ currentCandidate.reviewer }}</span>
                  <span>评审时间：{{ formatDate(currentCandidate.reviewTime) }}</span>
                </div>
              </div>
            </div>
            <div v-else class="empty-review">
              <a-empty description="暂无评审记录" />
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>

    <!-- 评审弹窗 -->
    <a-modal
      v-model:open="reviewVisible"
      title="综合评审"
      width="700px"
      @ok="submitReview"
      @cancel="cancelReview"
    >
      <div v-if="currentCandidate">
        <div class="review-candidate-info">
          <h4>{{ currentCandidate.name }}</h4>
          <p>{{ currentCandidate.position }} | {{ currentCandidate.department }} | 来自{{ currentCandidate.branchTeam }}</p>
          <p>原始综合得分：{{ currentCandidate.comprehensiveScore }}分</p>
        </div>
        
        <a-form ref="reviewFormRef" :model="reviewForm" layout="vertical">
          <a-form-item 
            label="评审得分" 
            name="score"
            :rules="[{ required: true, message: '请输入评审得分' }]"
          >
            <a-input-number 
              v-model:value="reviewForm.score"
              :min="0"
              :max="100"
              placeholder="请输入0-100分"
              style="width: 100%"
            />
          </a-form-item>
          
          <a-form-item label="评审维度评分">
            <div class="dimension-scores">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="专业能力" name="professionalScore">
                    <a-input-number 
                      v-model:value="reviewForm.professionalScore"
                      :min="0"
                      :max="100"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="领导能力" name="leadershipScore">
                    <a-input-number 
                      v-model:value="reviewForm.leadershipScore"
                      :min="0"
                      :max="100"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="创新能力" name="innovationScore">
                    <a-input-number 
                      v-model:value="reviewForm.innovationScore"
                      :min="0"
                      :max="100"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form-item>
          
          <a-form-item 
            label="评审意见" 
            name="comments"
            :rules="[{ required: true, message: '请输入评审意见' }]"
          >
            <a-textarea 
              v-model:value="reviewForm.comments"
              placeholder="请输入评审意见，包括优势、不足、建议等"
              :rows="4"
            />
          </a-form-item>
          
          <a-form-item 
            label="命名理由" 
            name="namingReason"
            :rules="[{ required: true, message: '请输入命名理由' }]"
          >
            <a-textarea 
              v-model:value="reviewForm.namingReason"
              placeholder="请输入推荐命名的理由"
              :rows="3"
            />
          </a-form-item>
          
          <a-form-item label="评审结论" name="conclusion">
            <a-radio-group v-model:value="reviewForm.conclusion">
              <a-radio value="recommend">推荐命名</a-radio>
              <a-radio value="conditional">有条件推荐</a-radio>
              <a-radio value="not_recommend">不推荐</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  AuditOutlined, 
  SearchOutlined 
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Candidate {
  id: number
  name: string
  gender: string
  age: number
  position: string
  department: string
  branchTeam: string
  recommendTime: string
  comprehensiveScore: number
  gradeRank: number
  abilityScore: number
  projectScore: number
  reviewStatus: 'pending' | 'reviewing' | 'completed'
  reviewScore?: number
  finalScore?: number
  reviewComments?: string
  reviewer?: string
  reviewTime?: string
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const reviewVisible = ref(false)
const selectedRowKeys = ref<number[]>([])
const currentCandidate = ref<Candidate | null>(null)
const candidateList = ref<Candidate[]>([])
const sortBy = ref('score')
const reviewFormRef = ref()

// 评审进度
const reviewProgress = reactive({
  total: 8,
  pending: 3,
  reviewing: 2,
  completed: 3
})

// 筛选表单
const filterForm = reactive({
  branchTeam: undefined as string | undefined,
  reviewStatus: undefined as string | undefined,
  minScore: undefined as number | undefined,
  maxScore: undefined as number | undefined
})

// 评审表单
const reviewForm = reactive({
  score: undefined as number | undefined,
  professionalScore: undefined as number | undefined,
  leadershipScore: undefined as number | undefined,
  innovationScore: undefined as number | undefined,
  comments: '',
  namingReason: '',
  conclusion: 'recommend' as 'recommend' | 'conditional' | 'not_recommend'
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const candidateColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    width: 100
  },
  {
    title: '职务',
    dataIndex: 'position',
    key: 'position',
    width: 120
  },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '来源专班',
    dataIndex: 'branchTeam',
    key: 'branchTeam',
    width: 120
  },
  {
    title: '评分情况',
    key: 'scores',
    width: 150
  },
  {
    title: '推荐时间',
    dataIndex: 'recommendTime',
    key: 'recommendTime',
    width: 120,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '评审状态',
    dataIndex: 'reviewStatus',
    key: 'reviewStatus',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const
  }
]

// 初始化模拟数据
const initMockData = () => {
  candidateList.value = [
    {
      id: 1,
      name: '周海军',
      gender: '男',
      age: 35,
      position: '党支部书记',
      department: '组织部',
      branchTeam: '组织部专班',
      recommendTime: '2025-06-20T14:30:00Z',
      comprehensiveScore: 95,
      gradeRank: 1,
      abilityScore: 92,
      projectScore: 88,
      reviewStatus: 'completed',
      reviewScore: 93,
      finalScore: 94,
      reviewComments: '该同志综合素质优秀，工作能力突出，具备较强的组织协调能力，建议推荐命名。',
      reviewer: '评审专家组',
      reviewTime: '2025-06-22T10:30:00Z'
    },
    {
      id: 2,
      name: '王东',
      gender: '女',
      age: 32,
      position: '副主任',
      department: '宣传部',
      branchTeam: '宣传部专班',
      recommendTime: '2025-06-18T10:15:00Z',
      comprehensiveScore: 88,
      gradeRank: 3,
      abilityScore: 85,
      projectScore: 90,
      reviewStatus: 'reviewing',
      reviewScore: undefined,
      finalScore: undefined
    },
    {
      id: 3,
      name: '杜佳佳',
      gender: '男',
      age: 38,
      position: '科长',
      department: '办公室',
      branchTeam: '组织部专班',
      recommendTime: '2025-06-20T14:30:00Z',
      comprehensiveScore: 92,
      gradeRank: 2,
      abilityScore: 89,
      projectScore: 86,
      reviewStatus: 'pending',
      reviewScore: undefined,
      finalScore: undefined
    }
  ]
  pagination.total = candidateList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD')
}

const getReviewStatusColor = (status: string) => {
  const colorMap = {
    pending: 'orange',
    reviewing: 'blue',
    completed: 'green'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getReviewStatusText = (status: string) => {
  const textMap = {
    pending: '待评审',
    reviewing: '评审中',
    completed: '已完成'
  }
  return textMap[status as keyof typeof textMap] || status
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const handleFilter = () => {
  message.info('执行筛选')
}

const resetFilter = () => {
  filterForm.branchTeam = undefined
  filterForm.reviewStatus = undefined
  filterForm.minScore = undefined
  filterForm.maxScore = undefined
  handleFilter()
}

const handleSort = () => {
  message.info(`按${sortBy.value}排序`)
}

const onSelectChange = (keys: number[]) => {
  selectedRowKeys.value = keys
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const startBatchReview = () => {
  message.info(`批量评审 ${selectedRowKeys.value.length} 个对象`)
}

const viewCandidateDetail = (record: Candidate) => {
  currentCandidate.value = record
  detailVisible.value = true
}

const startReview = (record: Candidate) => {
  currentCandidate.value = record
  reviewForm.score = record.reviewScore
  reviewForm.professionalScore = undefined
  reviewForm.leadershipScore = undefined
  reviewForm.innovationScore = undefined
  reviewForm.comments = record.reviewComments || ''
  reviewForm.namingReason = ''
  reviewForm.conclusion = 'recommend'
  reviewVisible.value = true
}

const compareCandidate = (record: Candidate) => {
  message.info(`对比分析：${record.name}`)
}

const submitReview = async () => {
  try {
    await reviewFormRef.value.validateFields()
    message.success('评审提交成功')
    reviewVisible.value = false
    refreshData()
  } catch (error) {
    console.error('评审表单验证失败:', error)
  }
}

const cancelReview = () => {
  reviewVisible.value = false
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.comprehensive-review {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.progress-section {
  margin-bottom: 16px;
}

.progress-item {
  text-align: center;
  padding: 16px 0;
}

.progress-number {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.progress-number.pending {
  color: #faad14;
}

.progress-number.reviewing {
  color: #1890ff;
}

.progress-number.completed {
  color: #52c41a;
}

.progress-label {
  font-size: 14px;
  color: #8c8c8c;
}

.progress-bar {
  margin-top: 16px;
}

.filter-section {
  margin-bottom: 16px;
}

.candidates-section {
  margin-bottom: 16px;
}

.scores-display {
  font-size: 12px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
}

.score-item .label {
  color: #666;
}

.score-item .value {
  color: #262626;
  font-weight: 500;
}

.candidate-detail {
  padding: 8px 0;
}

.scores-detail h4,
.review-scores h4 {
  margin: 16px 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.review-record {
  padding: 8px 0;
}

.review-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
}

.review-item h5 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.review-content {
  background: #f6f6f6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  line-height: 1.6;
  margin-bottom: 8px;
}

.review-meta {
  font-size: 12px;
  color: #8c8c8c;
}

.review-meta span {
  margin-right: 16px;
}

.empty-review {
  text-align: center;
  padding: 40px 0;
}

.review-candidate-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f6f6f6;
  border-radius: 6px;
}

.review-candidate-info h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.review-candidate-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.dimension-scores {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}
</style>
