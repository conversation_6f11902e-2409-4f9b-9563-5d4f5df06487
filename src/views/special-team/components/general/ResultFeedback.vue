<template>
  <div class="result-feedback">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>结果反馈</h3>
        <p>发送拟命名确认通知，发布评审结果通知至各分专班</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="createNotification">
            <template #icon><notification-outlined /></template>
            新建通知
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 通知统计 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="待发送通知"
              :value="statistics.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><clock-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="已发送通知"
              :value="statistics.sent"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><check-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="已确认通知"
              :value="statistics.confirmed"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><message-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="通知覆盖率"
              :value="Math.round((statistics.sent / (statistics.pending + statistics.sent)) * 100)"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><percentage-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <a-card title="快速操作" size="small">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="action-item" @click="sendConfirmationNotice">
              <div class="action-icon">
                <file-done-outlined />
              </div>
              <div class="action-content">
                <h4>发送拟命名确认通知</h4>
                <p>向相关部门发送拟命名对象确认通知</p>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="action-item" @click="publishResults">
              <div class="action-icon">
                <sound-outlined />
              </div>
              <div class="action-content">
                <h4>发布评审结果</h4>
                <p>向各分专班发布评审结果通知</p>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="action-item" @click="sendFeedbackRequest">
              <div class="action-icon">
                <comment-outlined />
              </div>
              <div class="action-content">
                <h4>请求反馈确认</h4>
                <p>向分专班请求反馈确认信息</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 通知列表 -->
    <div class="notifications-section">
      <a-card title="通知记录" size="small">
        <template #extra>
          <a-space>
            <a-select v-model:value="filterType" style="width: 120px" @change="filterNotifications">
              <a-select-option value="all">全部类型</a-select-option>
              <a-select-option value="confirmation">确认通知</a-select-option>
              <a-select-option value="result">结果通知</a-select-option>
              <a-select-option value="feedback">反馈请求</a-select-option>
            </a-select>
            <a-select v-model:value="filterStatus" style="width: 120px" @change="filterNotifications">
              <a-select-option value="all">全部状态</a-select-option>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="sent">已发送</a-select-option>
              <a-select-option value="confirmed">已确认</a-select-option>
            </a-select>
          </a-space>
        </template>
        
        <a-table
          :columns="notificationColumns"
          :data-source="filteredNotifications"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'recipients'">
              <a-space>
                <span>{{ record.recipientCount }}个</span>
                <a-button type="link" size="small" @click="viewRecipients(record)">
                  查看
                </a-button>
              </a-space>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewNotificationDetail(record)">
                  查看
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="editNotification(record)"
                  :disabled="record.status !== 'draft'"
                >
                  编辑
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="sendNotification(record)"
                  :disabled="record.status !== 'draft'"
                >
                  发送
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleNotificationAction(key, record)">
                      <a-menu-item key="resend" :disabled="record.status === 'draft'">重新发送</a-menu-item>
                      <a-menu-item key="recall" :disabled="record.status !== 'sent'">撤回</a-menu-item>
                      <a-menu-item key="archive">归档</a-menu-item>
                      <a-menu-item key="delete" :disabled="record.status === 'sent'">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 通知详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="通知详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentNotification" class="notification-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="通知标题">{{ currentNotification.title }}</a-descriptions-item>
          <a-descriptions-item label="通知类型">
            <a-tag :color="getTypeColor(currentNotification.type)">
              {{ getTypeText(currentNotification.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="发送时间">{{ formatDate(currentNotification.sendTime) }}</a-descriptions-item>
          <a-descriptions-item label="发送人">{{ currentNotification.sender }}</a-descriptions-item>
          <a-descriptions-item label="接收对象">{{ currentNotification.recipientCount }}个</a-descriptions-item>
          <a-descriptions-item label="通知状态">
            <a-tag :color="getStatusColor(currentNotification.status)">
              {{ getStatusText(currentNotification.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="通知内容" :span="2">
            <div class="notification-content">{{ currentNotification.content }}</div>
          </a-descriptions-item>
        </a-descriptions>
        
        <div v-if="currentNotification.attachments && currentNotification.attachments.length > 0" class="attachments-section">
          <h4>附件</h4>
          <a-list size="small" :data-source="currentNotification.attachments">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-button type="link" @click="downloadAttachment(item)">
                      <file-outlined /> {{ item.name }}
                    </a-button>
                  </template>
                  <template #description>{{ item.size }}</template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
        
        <div v-if="currentNotification.feedbacks && currentNotification.feedbacks.length > 0" class="feedbacks-section">
          <h4>反馈记录</h4>
          <div v-for="feedback in currentNotification.feedbacks" :key="feedback.id" class="feedback-item">
            <div class="feedback-header">
              <span class="feedback-from">{{ feedback.from }}</span>
              <span class="feedback-time">{{ formatDate(feedback.time) }}</span>
            </div>
            <div class="feedback-content">{{ feedback.content }}</div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 创建通知弹窗 -->
    <a-modal
      v-model:open="createVisible"
      title="创建通知"
      width="700px"
      @ok="submitNotification"
      @cancel="cancelCreate"
    >
      <a-form ref="notificationFormRef" :model="notificationForm" layout="vertical">
        <a-form-item 
          label="通知类型" 
          name="type"
          :rules="[{ required: true, message: '请选择通知类型' }]"
        >
          <a-radio-group v-model:value="notificationForm.type">
            <a-radio value="confirmation">拟命名确认通知</a-radio>
            <a-radio value="result">评审结果通知</a-radio>
            <a-radio value="feedback">反馈请求</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item 
          label="通知标题" 
          name="title"
          :rules="[{ required: true, message: '请输入通知标题' }]"
        >
          <a-input v-model:value="notificationForm.title" placeholder="请输入通知标题" />
        </a-form-item>
        
        <a-form-item 
          label="接收对象" 
          name="recipients"
          :rules="[{ required: true, message: '请选择接收对象' }]"
        >
          <a-checkbox-group v-model:value="notificationForm.recipients">
            <a-checkbox value="branch1">组织部专班</a-checkbox>
            <a-checkbox value="branch2">宣传部专班</a-checkbox>
            <a-checkbox value="branch3">人事部专班</a-checkbox>
            <a-checkbox value="leadership">领导班子</a-checkbox>
            <a-checkbox value="hr">人事部门</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item 
          label="通知内容" 
          name="content"
          :rules="[{ required: true, message: '请输入通知内容' }]"
        >
          <a-textarea 
            v-model:value="notificationForm.content"
            placeholder="请输入通知内容"
            :rows="6"
          />
        </a-form-item>
        
        <a-form-item label="优先级" name="priority">
          <a-radio-group v-model:value="notificationForm.priority">
            <a-radio value="high">高</a-radio>
            <a-radio value="medium">中</a-radio>
            <a-radio value="low">低</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="是否需要确认回复">
          <a-switch v-model:checked="notificationForm.requireConfirmation" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  NotificationOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  MessageOutlined,
  PercentageOutlined,
  FileDoneOutlined,
  SoundOutlined,
  CommentOutlined,
  FileOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Attachment {
  id: number
  name: string
  size: string
}

interface Feedback {
  id: number
  from: string
  time: string
  content: string
}

interface Notification {
  id: number
  title: string
  type: 'confirmation' | 'result' | 'feedback'
  content: string
  status: 'draft' | 'sent' | 'confirmed'
  sendTime: string
  sender: string
  recipientCount: number
  attachments?: Attachment[]
  feedbacks?: Feedback[]
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const createVisible = ref(false)
const currentNotification = ref<Notification | null>(null)
const notificationList = ref<Notification[]>([])
const filterType = ref('all')
const filterStatus = ref('all')
const notificationFormRef = ref()

// 统计数据
const statistics = reactive({
  pending: 2,
  sent: 8,
  confirmed: 5
})

// 通知表单
const notificationForm = reactive({
  type: 'confirmation' as 'confirmation' | 'result' | 'feedback',
  title: '',
  recipients: [] as string[],
  content: '',
  priority: 'medium' as 'high' | 'medium' | 'low',
  requireConfirmation: true
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性
const filteredNotifications = computed(() => {
  let list = [...notificationList.value]
  
  if (filterType.value !== 'all') {
    list = list.filter(n => n.type === filterType.value)
  }
  
  if (filterStatus.value !== 'all') {
    list = list.filter(n => n.status === filterStatus.value)
  }
  
  return list
})

// 表格列配置
const notificationColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  {
    title: '通知标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '接收对象',
    key: 'recipients',
    width: 100
  },
  {
    title: '发送时间',
    dataIndex: 'sendTime',
    key: 'sendTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '发送人',
    dataIndex: 'sender',
    key: 'sender',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const
  }
]

// 初始化模拟数据
const initMockData = () => {
  notificationList.value = [
    {
      id: 1,
      title: '2024年度优秀个人拟命名确认通知',
      type: 'confirmation',
      content: '经过综合评审，现将2024年度优秀个人拟命名名单通知如下，请各部门确认...',
      status: 'confirmed',
      sendTime: '2025-06-22T10:30:00Z',
      sender: '总专班',
      recipientCount: 3,
      attachments: [
        { id: 1, name: '拟命名名单.pdf', size: '2.5MB' }
      ],
      feedbacks: [
        { id: 1, from: '组织部专班', time: '2025-06-22T14:20:00Z', content: '已收到通知，名单确认无误。' }
      ]
    },
    {
      id: 2,
      title: '专项技能评审结果通知',
      type: 'result',
      content: '专项技能优秀对象评审已完成，现将评审结果通知各分专班...',
      status: 'sent',
      sendTime: '2025-06-20T16:45:00Z',
      sender: '总专班',
      recipientCount: 2
    },
    {
      id: 3,
      title: '反馈确认请求',
      type: 'feedback',
      content: '请各分专班对推荐对象的后续培养计划进行反馈确认...',
      status: 'draft',
      sendTime: '',
      sender: '总专班',
      recipientCount: 5
    }
  ]
  pagination.total = notificationList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getTypeColor = (type: string) => {
  const colorMap = {
    confirmation: 'green',
    result: 'blue',
    feedback: 'orange'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    confirmation: '确认通知',
    result: '结果通知',
    feedback: '反馈请求'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'orange',
    sent: 'blue',
    confirmed: 'green'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    sent: '已发送',
    confirmed: '已确认'
  }
  return textMap[status as keyof typeof textMap] || status
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const createNotification = () => {
  notificationForm.type = 'confirmation'
  notificationForm.title = ''
  notificationForm.recipients = []
  notificationForm.content = ''
  notificationForm.priority = 'medium'
  notificationForm.requireConfirmation = true
  createVisible.value = true
}

const sendConfirmationNotice = () => {
  notificationForm.type = 'confirmation'
  notificationForm.title = '拟命名对象确认通知'
  notificationForm.recipients = ['branch1', 'branch2', 'branch3', 'leadership']
  notificationForm.content = '经过综合评审，现将拟命名对象名单通知如下，请确认...'
  createVisible.value = true
}

const publishResults = () => {
  notificationForm.type = 'result'
  notificationForm.title = '评审结果通知'
  notificationForm.recipients = ['branch1', 'branch2', 'branch3']
  notificationForm.content = '评审工作已完成，现将评审结果通知各分专班...'
  createVisible.value = true
}

const sendFeedbackRequest = () => {
  notificationForm.type = 'feedback'
  notificationForm.title = '反馈确认请求'
  notificationForm.recipients = ['branch1', 'branch2', 'branch3']
  notificationForm.content = '请各分专班对相关事项进行反馈确认...'
  createVisible.value = true
}

const filterNotifications = () => {
  console.log('筛选通知:', filterType.value, filterStatus.value)
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const viewNotificationDetail = (record: Notification) => {
  currentNotification.value = record
  detailVisible.value = true
}

const viewRecipients = (record: Notification) => {
  message.info(`查看接收对象：${record.title}`)
}

const editNotification = (record: Notification) => {
  message.info(`编辑通知：${record.title}`)
}

const sendNotification = (record: Notification) => {
  message.success(`发送通知：${record.title}`)
}

const handleNotificationAction = (key: string, record: Notification) => {
  switch (key) {
    case 'resend':
      message.success(`重新发送：${record.title}`)
      break
    case 'recall':
      message.success(`撤回通知：${record.title}`)
      break
    case 'archive':
      message.success(`归档通知：${record.title}`)
      break
    case 'delete':
      message.success(`删除通知：${record.title}`)
      break
  }
}

const downloadAttachment = (attachment: Attachment) => {
  message.success(`下载附件：${attachment.name}`)
}

const submitNotification = async () => {
  try {
    await notificationFormRef.value.validateFields()
    message.success('通知创建成功')
    createVisible.value = false
    refreshData()
  } catch (error) {
    console.error('通知表单验证失败:', error)
  }
}

const cancelCreate = () => {
  createVisible.value = false
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.result-feedback {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 16px;
}

.quick-actions-section {
  margin-bottom: 16px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.action-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.action-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
  flex-shrink: 0;
}

.action-content h4 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.action-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 12px;
  line-height: 1.4;
}

.notifications-section {
  margin-bottom: 16px;
}

.notification-detail {
  padding: 8px 0;
}

.notification-content {
  background: #f6f6f6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  line-height: 1.6;
}

.attachments-section,
.feedbacks-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.attachments-section h4,
.feedbacks-section h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.feedback-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.feedback-from {
  font-weight: 500;
  color: #262626;
}

.feedback-time {
  font-size: 12px;
  color: #8c8c8c;
}

.feedback-content {
  color: #666;
  line-height: 1.5;
}
</style>
