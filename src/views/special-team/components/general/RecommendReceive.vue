<template>
  <div class="recommend-receive">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>推荐接收</h3>
        <p>查看各分专班推荐对象信息及评选标准，管理推荐信息并归档推荐情况</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="batchProcess" :disabled="selectedRowKeys.length === 0">
            <template #icon><check-circle-outlined /></template>
            批量处理
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="待处理推荐"
              :value="statistics.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><clock-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="已接收推荐"
              :value="statistics.received"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><check-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="推荐对象总数"
              :value="statistics.totalCandidates"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><user-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="分专班数量"
              :value="statistics.branchTeams"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><team-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选搜索 -->
    <div class="search-section">
      <a-card size="small">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="分专班">
            <a-select 
              v-model:value="searchForm.branchTeam" 
              placeholder="请选择分专班"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="branch1">组织部专班</a-select-option>
              <a-select-option value="branch2">宣传部专班</a-select-option>
              <a-select-option value="branch3">人事部专班</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="推荐状态">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="received">已接收</a-select-option>
              <a-select-option value="rejected">已拒绝</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="推送时间">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 推荐列表 -->
    <div class="recommend-list-section">
      <a-table
        :columns="columns"
        :data-source="recommendList"
        :loading="loading"
        :pagination="pagination"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        row-key="id"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'branchTeam'">
            <a-tag color="blue">{{ record.branchTeam }}</a-tag>
          </template>
          
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'candidates'">
            <a-space>
              <span>{{ record.candidateCount }}人</span>
              <a-button type="link" size="small" @click="viewCandidates(record)">
                查看名单
              </a-button>
            </a-space>
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">
                查看
              </a-button>
              <a-button 
                type="link" 
                size="small" 
                @click="processRecommend(record)"
                :disabled="record.status !== 'pending'"
              >
                处理
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuClick(key, record)">
                    <a-menu-item key="archive">归档</a-menu-item>
                    <a-menu-item key="export">导出</a-menu-item>
                    <a-menu-item key="history">查看历史</a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 推荐详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="推荐详情"
      width="900px"
      :footer="null"
    >
      <div v-if="currentRecommend" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="推荐标题">{{ currentRecommend.title }}</a-descriptions-item>
          <a-descriptions-item label="分专班">
            <a-tag color="blue">{{ currentRecommend.branchTeam }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推送时间">{{ formatDate(currentRecommend.pushTime) }}</a-descriptions-item>
          <a-descriptions-item label="推送人员">{{ currentRecommend.pushUser }}</a-descriptions-item>
          <a-descriptions-item label="推荐状态">
            <a-tag :color="getStatusColor(currentRecommend.status)">
              {{ getStatusText(currentRecommend.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentRecommend.priority)">
              {{ getPriorityText(currentRecommend.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推荐说明" :span="2">{{ currentRecommend.description }}</a-descriptions-item>
        </a-descriptions>
        
        <!-- 评选标准 -->
        <div class="standards-section">
          <h4>评选标准</h4>
          <a-table
            :columns="standardColumns"
            :data-source="currentRecommend.standards"
            :pagination="false"
            size="small"
          />
        </div>
        
        <!-- 推荐对象 -->
        <div class="candidates-section">
          <h4>推荐对象 ({{ currentRecommend.candidateCount }}人)</h4>
          <a-table
            :columns="candidateColumns"
            :data-source="currentRecommend.candidates"
            :pagination="false"
            size="small"
          />
        </div>
      </div>
    </a-modal>

    <!-- 处理推荐弹窗 -->
    <a-modal
      v-model:open="processVisible"
      title="处理推荐"
      width="600px"
      @ok="submitProcess"
      @cancel="cancelProcess"
    >
      <div v-if="currentRecommend">
        <div class="process-info">
          <h4>{{ currentRecommend.title }}</h4>
          <p>来自：{{ currentRecommend.branchTeam }} | 推荐对象：{{ currentRecommend.candidateCount }}人</p>
        </div>
        
        <a-form ref="processFormRef" :model="processForm" layout="vertical">
          <a-form-item 
            label="处理结果" 
            name="result"
            :rules="[{ required: true, message: '请选择处理结果' }]"
          >
            <a-radio-group v-model:value="processForm.result">
              <a-radio value="received">接收</a-radio>
              <a-radio value="rejected">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item 
            label="处理意见" 
            name="comments"
            :rules="[{ required: true, message: '请输入处理意见' }]"
          >
            <a-textarea 
              v-model:value="processForm.comments"
              placeholder="请输入处理意见"
              :rows="4"
            />
          </a-form-item>
          
          <a-form-item v-if="processForm.result === 'received'" label="后续安排">
            <a-checkbox-group v-model:value="processForm.nextSteps">
              <a-checkbox value="review">安排综合评审</a-checkbox>
              <a-checkbox value="interview">安排面试</a-checkbox>
              <a-checkbox value="investigation">安排实地调研</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 候选对象名单弹窗 -->
    <a-modal
      v-model:open="candidatesVisible"
      title="推荐对象名单"
      width="800px"
      :footer="null"
    >
      <div v-if="currentCandidates">
        <a-table
          :columns="candidateDetailColumns"
          :data-source="currentCandidates"
          :pagination="false"
          size="small"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  CheckCircleOutlined, 
  SearchOutlined,
  ClockCircleOutlined,
  UserOutlined,
  TeamOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Standard {
  dimension: string
  weight: number
  description: string
}

interface Candidate {
  id: number
  name: string
  position: string
  department: string
  comprehensiveScore: number
  gradeRank: number
  abilityScore: number
  projectScore: number
}

interface Recommend {
  id: number
  title: string
  branchTeam: string
  description: string
  candidateCount: number
  status: 'pending' | 'received' | 'rejected'
  priority: 'high' | 'medium' | 'low'
  pushTime: string
  pushUser: string
  standards: Standard[]
  candidates: Candidate[]
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const processVisible = ref(false)
const candidatesVisible = ref(false)
const selectedRowKeys = ref<number[]>([])
const currentRecommend = ref<Recommend | null>(null)
const currentCandidates = ref<Candidate[] | null>(null)
const recommendList = ref<Recommend[]>([])
const processFormRef = ref()

// 统计数据
const statistics = reactive({
  pending: 3,
  received: 8,
  totalCandidates: 25,
  branchTeams: 5
})

// 搜索表单
const searchForm = reactive({
  branchTeam: undefined as string | undefined,
  status: undefined as string | undefined,
  dateRange: null as any
})

// 处理表单
const processForm = reactive({
  result: 'received' as 'received' | 'rejected',
  comments: '',
  nextSteps: [] as string[]
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  {
    title: '推荐标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '分专班',
    dataIndex: 'branchTeam',
    key: 'branchTeam',
    width: 120
  },
  {
    title: '推荐对象',
    key: 'candidates',
    width: 120
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '推送人员',
    dataIndex: 'pushUser',
    key: 'pushUser',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right' as const
  }
]

// 评选标准列配置
const standardColumns = [
  { title: '评选维度', dataIndex: 'dimension', key: 'dimension' },
  { title: '权重', dataIndex: 'weight', key: 'weight', customRender: ({ text }: any) => `${text}%` },
  { title: '说明', dataIndex: 'description', key: 'description' }
]

// 候选对象列配置
const candidateColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '职务', dataIndex: 'position', key: 'position' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '综合得分', dataIndex: 'comprehensiveScore', key: 'comprehensiveScore', customRender: ({ text }: any) => `${text}分` }
]

// 候选对象详情列配置
const candidateDetailColumns = [
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '职务', dataIndex: 'position', key: 'position' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '综合得分', dataIndex: 'comprehensiveScore', key: 'comprehensiveScore', customRender: ({ text }: any) => `${text}分` },
  { title: '成绩排名', dataIndex: 'gradeRank', key: 'gradeRank', customRender: ({ text }: any) => `第${text}名` },
  { title: '能力评估', dataIndex: 'abilityScore', key: 'abilityScore', customRender: ({ text }: any) => `${text}分` },
  { title: '项目参与度', dataIndex: 'projectScore', key: 'projectScore', customRender: ({ text }: any) => `${text}分` }
]

// 初始化模拟数据
const initMockData = () => {
  recommendList.value = [
    {
      id: 1,
      title: '优异培育对象推荐名单（第一批）',
      branchTeam: '组织部专班',
      description: '经过多维度比选，推荐2名优异培育对象',
      candidateCount: 2,
      status: 'pending',
      priority: 'high',
      pushTime: '2025-06-20T14:30:00Z',
      pushUser: '李管理员',
      standards: [
        { dimension: '成绩要求', weight: 40, description: '年度考核成绩及历史表现' },
        { dimension: '能力指标', weight: 35, description: '专业能力和综合素质评估' },
        { dimension: '项目贡献度', weight: 25, description: '项目参与度和贡献价值' }
      ],
      candidates: [
        { id: 1, name: '陈胜', position: '党支部书记', department: '组织部', comprehensiveScore: 95, gradeRank: 1, abilityScore: 92, projectScore: 88 },
        { id: 2, name: '牟文婷', position: '科长', department: '办公室', comprehensiveScore: 92, gradeRank: 2, abilityScore: 89, projectScore: 86 }
      ]
    },
    {
      id: 2,
      title: '专项技能优秀对象推荐',
      branchTeam: '宣传部专班',
      description: '推荐专项技能突出的培育对象',
      candidateCount: 1,
      status: 'received',
      priority: 'medium',
      pushTime: '2025-06-18T10:15:00Z',
      pushUser: '王管理员',
      standards: [
        { dimension: '专业技能', weight: 50, description: '专业技能水平评估' },
        { dimension: '实践能力', weight: 30, description: '实际工作能力表现' },
        { dimension: '创新能力', weight: 20, description: '创新思维和实践能力' }
      ],
      candidates: [
        { id: 3, name: '田井安', position: '副主任', department: '宣传部', comprehensiveScore: 88, gradeRank: 3, abilityScore: 85, projectScore: 90 }
      ]
    }
  ]
  pagination.total = recommendList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getStatusColor = (status: string) => {
  const colorMap = {
    pending: 'orange',
    received: 'green',
    rejected: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    pending: '待处理',
    received: '已接收',
    rejected: '已拒绝'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[priority as keyof typeof colorMap] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const handleSearch = () => {
  message.info('执行搜索')
}

const resetSearch = () => {
  searchForm.branchTeam = undefined
  searchForm.status = undefined
  searchForm.dateRange = null
  handleSearch()
}

const onSelectChange = (keys: number[]) => {
  selectedRowKeys.value = keys
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const batchProcess = () => {
  message.info(`批量处理 ${selectedRowKeys.value.length} 个推荐`)
}

const viewDetail = (record: Recommend) => {
  currentRecommend.value = record
  detailVisible.value = true
}

const viewCandidates = (record: Recommend) => {
  currentCandidates.value = record.candidates
  candidatesVisible.value = true
}

const processRecommend = (record: Recommend) => {
  currentRecommend.value = record
  processForm.result = 'received'
  processForm.comments = ''
  processForm.nextSteps = []
  processVisible.value = true
}

const submitProcess = async () => {
  try {
    await processFormRef.value.validateFields()
    message.success('处理完成')
    processVisible.value = false
    refreshData()
  } catch (error) {
    console.error('处理表单验证失败:', error)
  }
}

const cancelProcess = () => {
  processVisible.value = false
}

const handleMenuClick = (key: string, record: Recommend) => {
  switch (key) {
    case 'archive':
      message.success(`归档推荐：${record.title}`)
      break
    case 'export':
      message.success(`导出推荐：${record.title}`)
      break
    case 'history':
      message.info(`查看历史：${record.title}`)
      break
  }
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.recommend-receive {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 16px;
}

.search-section {
  margin-bottom: 16px;
}

.recommend-list-section {
  background: white;
  border-radius: 8px;
}

.detail-content {
  padding: 8px 0;
}

.standards-section,
.candidates-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.standards-section h4,
.candidates-section h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.process-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f6f6f6;
  border-radius: 6px;
}

.process-info h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.process-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}
</style>
