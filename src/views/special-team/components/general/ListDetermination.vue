<template>
  <div class="list-determination">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>名单确定</h3>
        <p>确定拟命名对象名单，归档拟命名名单及历史记录</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="generateFinalList" :disabled="selectedCandidates.length === 0">
            <template #icon><file-text-outlined /></template>
            生成拟命名名单
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 名单生成向导 -->
    <div class="wizard-section">
      <a-card title="名单生成向导" size="small">
        <a-steps :current="currentStep" size="small">
          <a-step title="选择对象" description="选择拟命名对象" />
          <a-step title="设置信息" description="设置名单信息" />
          <a-step title="确认生成" description="确认并生成名单" />
        </a-steps>
      </a-card>
    </div>

    <!-- 步骤一：选择对象 -->
    <div v-if="currentStep === 0" class="step-content">
      <a-card title="选择拟命名对象" size="small">
        <template #extra>
          <a-space>
            <span>已选择：{{ selectedCandidates.length }}人</span>
            <a-button size="small" @click="selectAll">全选</a-button>
            <a-button size="small" @click="clearSelection">清空</a-button>
          </a-space>
        </template>
        
        <!-- 筛选条件 -->
        <div class="filter-bar">
          <a-form layout="inline" :model="filterForm">
            <a-form-item label="来源专班">
              <a-select 
                v-model:value="filterForm.branchTeam" 
                placeholder="全部"
                style="width: 120px"
                allow-clear
              >
                <a-select-option value="branch1">组织部专班</a-select-option>
                <a-select-option value="branch2">宣传部专班</a-select-option>
                <a-select-option value="branch3">人事部专班</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="评审结论">
              <a-select 
                v-model:value="filterForm.conclusion" 
                placeholder="全部"
                style="width: 120px"
                allow-clear
              >
                <a-select-option value="recommend">推荐命名</a-select-option>
                <a-select-option value="conditional">有条件推荐</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="最终得分">
              <a-input-number 
                v-model:value="filterForm.minScore"
                placeholder="最低分"
                style="width: 80px"
                :min="0"
                :max="100"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="applyFilter">筛选</a-button>
            </a-form-item>
          </a-form>
        </div>
        
        <!-- 候选对象列表 -->
        <div class="candidates-grid">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8" v-for="candidate in filteredCandidates" :key="candidate.id">
              <div 
                class="candidate-card"
                :class="{ 'selected': selectedCandidates.includes(candidate.id) }"
                @click="toggleCandidate(candidate.id)"
              >
                <div class="card-header">
                  <div class="candidate-info">
                    <h4>{{ candidate.name }}</h4>
                    <p>{{ candidate.position }} | {{ candidate.department }}</p>
                  </div>
                  <div class="selection-indicator">
                    <a-checkbox :checked="selectedCandidates.includes(candidate.id)" />
                  </div>
                </div>
                
                <div class="card-content">
                  <div class="score-info">
                    <div class="score-item">
                      <span class="label">最终得分：</span>
                      <span class="value">{{ candidate.finalScore }}分</span>
                    </div>
                    <div class="score-item">
                      <span class="label">来源专班：</span>
                      <span class="value">{{ candidate.branchTeam }}</span>
                    </div>
                  </div>
                  
                  <div class="conclusion-tag">
                    <a-tag :color="getConclusionColor(candidate.conclusion)">
                      {{ getConclusionText(candidate.conclusion) }}
                    </a-tag>
                  </div>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
        
        <div class="step-actions">
          <a-button type="primary" @click="nextStep" :disabled="selectedCandidates.length === 0">
            下一步
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 步骤二：设置信息 -->
    <div v-if="currentStep === 1" class="step-content">
      <a-card title="设置名单信息" size="small">
        <a-form ref="listFormRef" :model="listForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item 
                label="名单标题" 
                name="title"
                :rules="[{ required: true, message: '请输入名单标题' }]"
              >
                <a-input v-model:value="listForm.title" placeholder="请输入名单标题" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item 
                label="命名类型" 
                name="type"
                :rules="[{ required: true, message: '请选择命名类型' }]"
              >
                <a-select v-model:value="listForm.type" placeholder="请选择命名类型">
                  <a-select-option value="excellent">优秀个人</a-select-option>
                  <a-select-option value="advanced">先进个人</a-select-option>
                  <a-select-option value="model">模范个人</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item 
            label="名单说明" 
            name="description"
            :rules="[{ required: true, message: '请输入名单说明' }]"
          >
            <a-textarea 
              v-model:value="listForm.description"
              placeholder="请输入名单说明，包括评选过程、标准等"
              :rows="4"
            />
          </a-form-item>
          
          <a-form-item label="生效时间" name="effectiveDate">
            <a-date-picker 
              v-model:value="listForm.effectiveDate"
              style="width: 100%"
              placeholder="请选择生效时间"
            />
          </a-form-item>
          
          <a-form-item label="备注信息" name="remarks">
            <a-textarea 
              v-model:value="listForm.remarks"
              placeholder="请输入备注信息（可选）"
              :rows="2"
            />
          </a-form-item>
        </a-form>
        
        <div class="step-actions">
          <a-space>
            <a-button @click="prevStep">上一步</a-button>
            <a-button type="primary" @click="nextStep">下一步</a-button>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 步骤三：确认生成 -->
    <div v-if="currentStep === 2" class="step-content">
      <a-card title="确认生成名单" size="small">
        <div class="confirmation-content">
          <a-alert
            message="请确认以下信息"
            description="确认无误后将生成正式的拟命名对象名单"
            type="info"
            show-icon
            style="margin-bottom: 24px"
          />
          
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="名单标题">{{ listForm.title }}</a-descriptions-item>
            <a-descriptions-item label="命名类型">{{ getTypeText(listForm.type) }}</a-descriptions-item>
            <a-descriptions-item label="对象数量">{{ selectedCandidates.length }}人</a-descriptions-item>
            <a-descriptions-item label="生效时间">{{ formatDate(listForm.effectiveDate) }}</a-descriptions-item>
            <a-descriptions-item label="名单说明" :span="2">{{ listForm.description }}</a-descriptions-item>
          </a-descriptions>
          
          <div class="selected-candidates">
            <h4>拟命名对象名单</h4>
            <a-table
              :columns="confirmColumns"
              :data-source="selectedCandidateDetails"
              :pagination="false"
              size="small"
            />
          </div>
        </div>
        
        <div class="step-actions">
          <a-space>
            <a-button @click="prevStep">上一步</a-button>
            <a-button type="primary" @click="confirmGenerate" :loading="generating">
              确认生成
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 历史名单 -->
    <div class="history-section">
      <a-card title="历史名单" size="small">
        <template #extra>
          <a-space>
            <a-select v-model:value="historyFilter" style="width: 120px" @change="filterHistory">
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="published">已发布</a-select-option>
              <a-select-option value="archived">已归档</a-select-option>
            </a-select>
          </a-space>
        </template>
        
        <a-table
          :columns="historyColumns"
          :data-source="filteredHistoryList"
          :pagination="historyPagination"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewHistoryDetail(record)">
                  查看
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="downloadList(record)"
                  :disabled="record.status === 'draft'"
                >
                  下载
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleHistoryAction(key, record)">
                      <a-menu-item key="edit" :disabled="record.status !== 'draft'">编辑</a-menu-item>
                      <a-menu-item key="publish" :disabled="record.status !== 'draft'">发布</a-menu-item>
                      <a-menu-item key="archive" :disabled="record.status === 'archived'">归档</a-menu-item>
                      <a-menu-item key="delete" :disabled="record.status === 'published'">删除</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  FileTextOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Candidate {
  id: number
  name: string
  position: string
  department: string
  branchTeam: string
  finalScore: number
  conclusion: 'recommend' | 'conditional' | 'not_recommend'
}

interface HistoryList {
  id: number
  title: string
  type: string
  candidateCount: number
  status: 'draft' | 'published' | 'archived'
  createTime: string
  effectiveDate?: string
}

// 响应式数据
const currentStep = ref(0)
const selectedCandidates = ref<number[]>([])
const generating = ref(false)
const candidateList = ref<Candidate[]>([])
const historyList = ref<HistoryList[]>([])
const historyFilter = ref('all')
const listFormRef = ref()

// 筛选表单
const filterForm = reactive({
  branchTeam: undefined as string | undefined,
  conclusion: undefined as string | undefined,
  minScore: undefined as number | undefined
})

// 名单表单
const listForm = reactive({
  title: '',
  type: 'excellent' as 'excellent' | 'advanced' | 'model',
  description: '',
  effectiveDate: null as any,
  remarks: ''
})

// 分页配置
const historyPagination = reactive({
  current: 1,
  pageSize: 5,
  total: 0,
  showSizeChanger: false,
  showQuickJumper: false
})

// 计算属性
const filteredCandidates = computed(() => {
  let list = [...candidateList.value]
  
  if (filterForm.branchTeam) {
    list = list.filter(c => c.branchTeam === filterForm.branchTeam)
  }
  
  if (filterForm.conclusion) {
    list = list.filter(c => c.conclusion === filterForm.conclusion)
  }
  
  if (filterForm.minScore) {
    list = list.filter(c => c.finalScore >= filterForm.minScore!)
  }
  
  return list
})

const selectedCandidateDetails = computed(() => {
  return candidateList.value.filter(c => selectedCandidates.value.includes(c.id))
})

const filteredHistoryList = computed(() => {
  if (historyFilter.value === 'all') {
    return historyList.value
  }
  return historyList.value.filter(item => item.status === historyFilter.value)
})

// 表格列配置
const confirmColumns = [
  { title: '序号', key: 'index', customRender: ({ index }: any) => index + 1 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '职务', dataIndex: 'position', key: 'position' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '来源专班', dataIndex: 'branchTeam', key: 'branchTeam' },
  { title: '最终得分', dataIndex: 'finalScore', key: 'finalScore', customRender: ({ text }: any) => `${text}分` }
]

const historyColumns = [
  { title: '名单标题', dataIndex: 'title', key: 'title' },
  { title: '类型', dataIndex: 'type', key: 'type' },
  { title: '对象数量', dataIndex: 'candidateCount', key: 'candidateCount', customRender: ({ text }: any) => `${text}人` },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', customRender: ({ text }: any) => formatDate(text) },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '操作', key: 'action', width: 150 }
]

// 初始化模拟数据
const initMockData = () => {
  candidateList.value = [
    {
      id: 1,
      name: '周海军',
      position: '党支部书记',
      department: '组织部',
      branchTeam: '组织部专班',
      finalScore: 94,
      conclusion: 'recommend'
    },
    {
      id: 2,
      name: '王东',
      position: '副主任',
      department: '宣传部',
      branchTeam: '宣传部专班',
      finalScore: 89,
      conclusion: 'conditional'
    },
    {
      id: 3,
      name: '杜佳佳',
      position: '科长',
      department: '办公室',
      branchTeam: '组织部专班',
      finalScore: 91,
      conclusion: 'recommend'
    }
  ]

  historyList.value = [
    {
      id: 1,
      title: '2024年度优秀个人拟命名名单',
      type: '优秀个人',
      candidateCount: 5,
      status: 'published',
      createTime: '2025-06-15T10:30:00Z',
      effectiveDate: '2024-02-01T00:00:00Z'
    },
    {
      id: 2,
      title: '专项技能先进个人名单（草稿）',
      type: '先进个人',
      candidateCount: 3,
      status: 'draft',
      createTime: '2025-06-20T14:20:00Z'
    }
  ]
  historyPagination.total = historyList.value.length
}

// 工具函数
const formatDate = (dateStr: string | null) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD')
}

const getConclusionColor = (conclusion: string) => {
  const colorMap = {
    recommend: 'green',
    conditional: 'orange',
    not_recommend: 'red'
  }
  return colorMap[conclusion as keyof typeof colorMap] || 'default'
}

const getConclusionText = (conclusion: string) => {
  const textMap = {
    recommend: '推荐命名',
    conditional: '有条件推荐',
    not_recommend: '不推荐'
  }
  return textMap[conclusion as keyof typeof textMap] || conclusion
}

const getTypeText = (type: string) => {
  const textMap = {
    excellent: '优秀个人',
    advanced: '先进个人',
    model: '模范个人'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'orange',
    published: 'green',
    archived: 'default'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return textMap[status as keyof typeof textMap] || status
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const applyFilter = () => {
  message.info('应用筛选条件')
}

const toggleCandidate = (id: number) => {
  const index = selectedCandidates.value.indexOf(id)
  if (index > -1) {
    selectedCandidates.value.splice(index, 1)
  } else {
    selectedCandidates.value.push(id)
  }
}

const selectAll = () => {
  selectedCandidates.value = filteredCandidates.value.map(c => c.id)
}

const clearSelection = () => {
  selectedCandidates.value = []
}

const nextStep = async () => {
  if (currentStep.value === 1) {
    try {
      await listFormRef.value.validateFields()
    } catch (error) {
      return
    }
  }
  currentStep.value++
}

const prevStep = () => {
  currentStep.value--
}

const generateFinalList = () => {
  currentStep.value = 0
  selectedCandidates.value = []
  listForm.title = `${new Date().getFullYear()}年度拟命名对象名单`
  listForm.type = 'excellent'
  listForm.description = ''
  listForm.effectiveDate = null
  listForm.remarks = ''
}

const confirmGenerate = async () => {
  generating.value = true
  
  try {
    // 模拟生成过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success('拟命名名单生成成功')
    currentStep.value = 0
    selectedCandidates.value = []
    refreshData()
  } finally {
    generating.value = false
  }
}

const filterHistory = () => {
  console.log('筛选历史名单:', historyFilter.value)
}

const viewHistoryDetail = (record: HistoryList) => {
  message.info(`查看名单详情：${record.title}`)
}

const downloadList = (record: HistoryList) => {
  message.success(`下载名单：${record.title}`)
}

const handleHistoryAction = (key: string, record: HistoryList) => {
  switch (key) {
    case 'edit':
      message.info(`编辑名单：${record.title}`)
      break
    case 'publish':
      message.success(`发布名单：${record.title}`)
      break
    case 'archive':
      message.success(`归档名单：${record.title}`)
      break
    case 'delete':
      message.success(`删除名单：${record.title}`)
      break
  }
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.list-determination {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.wizard-section {
  margin-bottom: 24px;
}

.step-content {
  margin-bottom: 24px;
}

.filter-bar {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.candidates-grid {
  margin-bottom: 24px;
}

.candidate-card {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.candidate-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.candidate-card.selected {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.candidate-info h4 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.candidate-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.score-info {
  flex: 1;
}

.score-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 14px;
}

.score-item .label {
  color: #666;
}

.score-item .value {
  color: #262626;
  font-weight: 500;
}

.step-actions {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.confirmation-content {
  padding: 8px 0;
}

.selected-candidates {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.selected-candidates h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.history-section {
  margin-top: 24px;
}
</style>
