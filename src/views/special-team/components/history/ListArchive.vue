<template>
  <div class="list-archive">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>名单归档</h3>
        <p>名单归档与版本控制，支持历史版本查询和对比分析</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportArchive">
            <template #icon><export-outlined /></template>
            导出归档
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 归档统计 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="归档名单总数"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><file-protect-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="本年度归档"
              :value="statistics.thisYear"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><calendar-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="归档对象总数"
              :value="statistics.totalPersons"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><team-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="存储容量"
              :value="statistics.storageSize"
              suffix="GB"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix><database-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card size="small">
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="名单类型">
            <a-select 
              v-model:value="filterForm.type" 
              placeholder="请选择类型"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="excellent">优秀个人</a-select-option>
              <a-select-option value="advanced">先进个人</a-select-option>
              <a-select-option value="model">模范个人</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="归档年份">
            <a-select 
              v-model:value="filterForm.year" 
              placeholder="请选择年份"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="2024">2024年</a-select-option>
              <a-select-option value="2023">2023年</a-select-option>
              <a-select-option value="2022">2022年</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="归档状态">
            <a-select 
              v-model:value="filterForm.status" 
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="active">有效</a-select-option>
              <a-select-option value="archived">已归档</a-select-option>
              <a-select-option value="expired">已过期</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="关键词">
            <a-input 
              v-model:value="filterForm.keyword" 
              placeholder="请输入关键词"
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleFilter">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetFilter">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 归档列表 -->
    <div class="archive-list-section">
      <a-card title="归档名单" size="small">
        <template #extra>
          <a-space>
            <span>视图：</span>
            <a-radio-group v-model:value="viewMode" size="small">
              <a-radio-button value="table">表格</a-radio-button>
              <a-radio-button value="card">卡片</a-radio-button>
              <a-radio-button value="timeline">时间线</a-radio-button>
            </a-radio-group>
          </a-space>
        </template>
        
        <!-- 表格视图 -->
        <div v-if="viewMode === 'table'">
          <a-table
            :columns="archiveColumns"
            :data-source="filteredArchiveList"
            :loading="loading"
            :pagination="pagination"
            row-key="id"
            @change="handleTableChange"
            :scroll="{ x: 1200 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'title'">
                <a-button type="link" @click="viewArchiveDetail(record)">
                  {{ record.title }}
                </a-button>
              </template>
              
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
              
              <template v-if="column.key === 'version'">
                <a-space>
                  <span>v{{ record.version }}</span>
                  <a-button type="link" size="small" @click="viewVersionHistory(record)">
                    历史版本
                  </a-button>
                </a-space>
              </template>
              
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="viewArchiveDetail(record)">
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="downloadArchive(record)">
                    下载
                  </a-button>
                  <a-dropdown>
                    <template #overlay>
                      <a-menu @click="({ key }) => handleArchiveAction(key, record)">
                        <a-menu-item key="compare">版本对比</a-menu-item>
                        <a-menu-item key="restore">恢复版本</a-menu-item>
                        <a-menu-item key="backup">备份</a-menu-item>
                        <a-menu-item key="delete" :disabled="record.status === 'active'">删除</a-menu-item>
                      </a-menu>
                    </template>
                    <a-button type="link" size="small">
                      更多 <down-outlined />
                    </a-button>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
        
        <!-- 卡片视图 -->
        <div v-if="viewMode === 'card'" class="card-view">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :lg="8" v-for="archive in filteredArchiveList" :key="archive.id">
              <a-card hoverable class="archive-card">
                <template #title>
                  <div class="card-title">
                    <span>{{ archive.title }}</span>
                    <a-tag :color="getStatusColor(archive.status)">
                      {{ getStatusText(archive.status) }}
                    </a-tag>
                  </div>
                </template>
                
                <div class="card-content">
                  <div class="archive-meta">
                    <p><strong>类型：</strong>{{ getTypeText(archive.type) }}</p>
                    <p><strong>版本：</strong>v{{ archive.version }}</p>
                    <p><strong>对象数量：</strong>{{ archive.personCount }}人</p>
                    <p><strong>归档时间：</strong>{{ formatDate(archive.archiveTime) }}</p>
                  </div>
                  
                  <div class="card-actions">
                    <a-space>
                      <a-button size="small" @click="viewArchiveDetail(archive)">查看</a-button>
                      <a-button size="small" @click="downloadArchive(archive)">下载</a-button>
                    </a-space>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
        
        <!-- 时间线视图 -->
        <div v-if="viewMode === 'timeline'" class="timeline-view">
          <a-timeline mode="left">
            <a-timeline-item 
              v-for="archive in filteredArchiveList" 
              :key="archive.id"
              :color="getTimelineColor(archive.status)"
            >
              <template #label>
                <span class="timeline-date">{{ formatDate(archive.archiveTime) }}</span>
              </template>
              <div class="timeline-content">
                <div class="timeline-header">
                  <h4>{{ archive.title }}</h4>
                  <a-tag :color="getStatusColor(archive.status)">
                    {{ getStatusText(archive.status) }}
                  </a-tag>
                </div>
                <div class="timeline-meta">
                  <a-space>
                    <span>{{ getTypeText(archive.type) }}</span>
                    <span>v{{ archive.version }}</span>
                    <span>{{ archive.personCount }}人</span>
                  </a-space>
                </div>
                <div class="timeline-description">{{ archive.description }}</div>
                <div class="timeline-actions">
                  <a-space>
                    <a-button type="link" size="small" @click="viewArchiveDetail(archive)">
                      查看详情
                    </a-button>
                    <a-button type="link" size="small" @click="downloadArchive(archive)">
                      下载文件
                    </a-button>
                  </a-space>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </div>
      </a-card>
    </div>

    <!-- 归档详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="归档详情"
      width="900px"
      :footer="null"
    >
      <div v-if="currentArchive" class="archive-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="名单标题">{{ currentArchive.title }}</a-descriptions-item>
          <a-descriptions-item label="名单类型">
            <a-tag :color="getTypeColor(currentArchive.type)">
              {{ getTypeText(currentArchive.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="版本号">v{{ currentArchive.version }}</a-descriptions-item>
          <a-descriptions-item label="归档状态">
            <a-tag :color="getStatusColor(currentArchive.status)">
              {{ getStatusText(currentArchive.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="对象数量">{{ currentArchive.personCount }}人</a-descriptions-item>
          <a-descriptions-item label="文件大小">{{ currentArchive.fileSize }}</a-descriptions-item>
          <a-descriptions-item label="归档时间">{{ formatDate(currentArchive.archiveTime) }}</a-descriptions-item>
          <a-descriptions-item label="归档人员">{{ currentArchive.archiveUser }}</a-descriptions-item>
          <a-descriptions-item label="名单说明" :span="2">{{ currentArchive.description }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="archive-persons">
          <h4>名单对象</h4>
          <a-table
            :columns="personColumns"
            :data-source="currentArchive.persons"
            :pagination="false"
            size="small"
          />
        </div>
        
        <div class="archive-files">
          <h4>相关文件</h4>
          <a-list size="small" :data-source="currentArchive.files">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-button type="link" @click="downloadFile(item)">
                      <file-outlined /> {{ item.name }}
                    </a-button>
                  </template>
                  <template #description>{{ item.size }} | {{ item.type }}</template>
                </a-list-item-meta>
                <template #actions>
                  <a-button type="link" size="small" @click="previewFile(item)">预览</a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </div>
      </div>
    </a-modal>

    <!-- 版本历史弹窗 -->
    <a-modal
      v-model:open="versionVisible"
      title="版本历史"
      width="700px"
      :footer="null"
    >
      <div v-if="currentArchive" class="version-history">
        <a-timeline>
          <a-timeline-item 
            v-for="version in versionHistory" 
            :key="version.id"
            :color="version.isCurrent ? 'green' : 'blue'"
          >
            <template #dot>
              <crown-outlined v-if="version.isCurrent" />
            </template>
            <div class="version-content">
              <div class="version-header">
                <span class="version-number">v{{ version.version }}</span>
                <span class="version-time">{{ formatDate(version.createTime) }}</span>
                <a-tag v-if="version.isCurrent" color="green">当前版本</a-tag>
              </div>
              <div class="version-description">{{ version.description }}</div>
              <div class="version-meta">
                <span>创建人：{{ version.createUser }}</span>
                <span>对象数量：{{ version.personCount }}人</span>
              </div>
              <div class="version-actions">
                <a-space>
                  <a-button type="link" size="small" @click="viewVersionDetail(version)">
                    查看详情
                  </a-button>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="restoreVersion(version)"
                    :disabled="version.isCurrent"
                  >
                    恢复此版本
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  SearchOutlined,
  FileProtectOutlined,
  CalendarOutlined,
  TeamOutlined,
  DatabaseOutlined,
  FileOutlined,
  DownOutlined,
  CrownOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Person {
  id: number
  name: string
  position: string
  department: string
  score: number
}

interface ArchiveFile {
  id: number
  name: string
  size: string
  type: string
  url: string
}

interface VersionInfo {
  id: number
  version: string
  description: string
  createTime: string
  createUser: string
  personCount: number
  isCurrent: boolean
}

interface Archive {
  id: number
  title: string
  type: 'excellent' | 'advanced' | 'model'
  version: string
  status: 'active' | 'archived' | 'expired'
  personCount: number
  fileSize: string
  archiveTime: string
  archiveUser: string
  description: string
  persons: Person[]
  files: ArchiveFile[]
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const versionVisible = ref(false)
const currentArchive = ref<Archive | null>(null)
const archiveList = ref<Archive[]>([])
const versionHistory = ref<VersionInfo[]>([])
const viewMode = ref('table')

// 统计数据
const statistics = reactive({
  total: 28,
  thisYear: 12,
  totalPersons: 156,
  storageSize: 2.8
})

// 筛选表单
const filterForm = reactive({
  type: undefined as string | undefined,
  year: undefined as string | undefined,
  status: undefined as string | undefined,
  keyword: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性
const filteredArchiveList = computed(() => {
  let list = [...archiveList.value]

  if (filterForm.type) {
    list = list.filter(archive => archive.type === filterForm.type)
  }

  if (filterForm.year) {
    list = list.filter(archive =>
      dayjs(archive.archiveTime).format('YYYY') === filterForm.year
    )
  }

  if (filterForm.status) {
    list = list.filter(archive => archive.status === filterForm.status)
  }

  if (filterForm.keyword) {
    list = list.filter(archive =>
      archive.title.includes(filterForm.keyword) ||
      archive.description.includes(filterForm.keyword)
    )
  }

  // 按归档时间倒序排列
  list.sort((a, b) => new Date(b.archiveTime).getTime() - new Date(a.archiveTime).getTime())

  return list
})

// 表格列配置
const archiveColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  {
    title: '名单标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '版本',
    key: 'version',
    width: 120
  },
  {
    title: '对象数量',
    dataIndex: 'personCount',
    key: 'personCount',
    width: 100,
    customRender: ({ text }: any) => `${text}人`
  },
  {
    title: '文件大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100
  },
  {
    title: '归档时间',
    dataIndex: 'archiveTime',
    key: 'archiveTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const
  }
]

// 人员列配置
const personColumns = [
  { title: '序号', key: 'index', customRender: ({ index }: any) => index + 1 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '职务', dataIndex: 'position', key: 'position' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '得分', dataIndex: 'score', key: 'score', customRender: ({ text }: any) => `${text}分` }
]

// 初始化模拟数据
const initMockData = () => {
  archiveList.value = [
    {
      id: 1,
      title: '2024年度优秀个人拟命名名单',
      type: 'excellent',
      version: '1.2',
      status: 'active',
      personCount: 5,
      fileSize: '2.5MB',
      archiveTime: '2025-06-25T10:30:00Z',
      archiveUser: '系统管理员',
      description: '经过综合评审确定的2024年度优秀个人名单，包含各部门推荐的优秀代表。',
      persons: [
        { id: 1, name: '周海军', position: '党支部书记', department: '组织部', score: 95 },
        { id: 2, name: '王东', position: '副主任', department: '宣传部', score: 92 },
        { id: 3, name: '杜佳佳', position: '科长', department: '办公室', score: 89 }
      ],
      files: [
        { id: 1, name: '名单正式文件.pdf', size: '1.2MB', type: 'PDF文档', url: '#' },
        { id: 2, name: '评审记录.docx', size: '856KB', type: 'Word文档', url: '#' },
        { id: 3, name: '统计报表.xlsx', size: '445KB', type: 'Excel表格', url: '#' }
      ]
    },
    {
      id: 2,
      title: '2023年度先进个人名单',
      type: 'advanced',
      version: '2.0',
      status: 'archived',
      personCount: 8,
      fileSize: '3.2MB',
      archiveTime: '2023-12-20T15:45:00Z',
      archiveUser: '档案管理员',
      description: '2023年度先进个人评选结果，表彰在各项工作中表现突出的先进代表。',
      persons: [
        { id: 4, name: '孙建', position: '主任', department: '人事部', score: 88 },
        { id: 5, name: '黄鑫', position: '技术员', department: '技术部', score: 86 }
      ],
      files: [
        { id: 4, name: '先进个人名单.pdf', size: '1.8MB', type: 'PDF文档', url: '#' },
        { id: 5, name: '表彰决定.docx', size: '1.4MB', type: 'Word文档', url: '#' }
      ]
    }
  ]

  versionHistory.value = [
    {
      id: 1,
      version: '1.2',
      description: '修正了部分人员信息，更新了评分标准',
      createTime: '2025-06-25T10:30:00Z',
      createUser: '系统管理员',
      personCount: 5,
      isCurrent: true
    },
    {
      id: 2,
      version: '1.1',
      description: '增加了2名候选对象，调整了排序',
      createTime: '2025-06-22T14:20:00Z',
      createUser: '业务管理员',
      personCount: 5,
      isCurrent: false
    },
    {
      id: 3,
      version: '1.0',
      description: '初始版本，包含基础名单信息',
      createTime: '2025-06-20T09:15:00Z',
      createUser: '业务管理员',
      personCount: 3,
      isCurrent: false
    }
  ]

  pagination.total = archiveList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getTypeColor = (type: string) => {
  const colorMap = {
    excellent: 'gold',
    advanced: 'blue',
    model: 'green'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    excellent: '优秀个人',
    advanced: '先进个人',
    model: '模范个人'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getStatusColor = (status: string) => {
  const colorMap = {
    active: 'green',
    archived: 'blue',
    expired: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    active: '有效',
    archived: '已归档',
    expired: '已过期'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getTimelineColor = (status: string) => {
  const colorMap = {
    active: 'green',
    archived: 'blue',
    expired: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'blue'
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const exportArchive = () => {
  message.success('归档数据导出成功')
}

const handleFilter = () => {
  message.info('应用筛选条件')
}

const resetFilter = () => {
  filterForm.type = undefined
  filterForm.year = undefined
  filterForm.status = undefined
  filterForm.keyword = ''
  handleFilter()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const viewArchiveDetail = (record: Archive) => {
  currentArchive.value = record
  detailVisible.value = true
}

const downloadArchive = (record: Archive) => {
  message.success(`下载归档：${record.title}`)
}

const viewVersionHistory = (record: Archive) => {
  currentArchive.value = record
  versionVisible.value = true
}

const handleArchiveAction = (key: string, record: Archive) => {
  switch (key) {
    case 'compare':
      message.info(`版本对比：${record.title}`)
      break
    case 'restore':
      message.success(`恢复版本：${record.title}`)
      break
    case 'backup':
      message.success(`备份归档：${record.title}`)
      break
    case 'delete':
      message.success(`删除归档：${record.title}`)
      break
  }
}

const downloadFile = (file: ArchiveFile) => {
  message.success(`下载文件：${file.name}`)
}

const previewFile = (file: ArchiveFile) => {
  message.info(`预览文件：${file.name}`)
}

const viewVersionDetail = (version: VersionInfo) => {
  message.info(`查看版本详情：v${version.version}`)
}

const restoreVersion = (version: VersionInfo) => {
  message.success(`恢复到版本：v${version.version}`)
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.list-archive {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 16px;
}

.filter-section {
  margin-bottom: 16px;
}

.archive-list-section {
  margin-bottom: 16px;
}

.card-view {
  min-height: 400px;
}

.archive-card {
  height: 100%;
  border-radius: 8px;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.archive-meta p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.card-actions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.timeline-view {
  min-height: 400px;
}

.timeline-date {
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.timeline-meta {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.timeline-description {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

.timeline-actions {
  margin-top: 8px;
}

.archive-detail {
  padding: 8px 0;
}

.archive-persons,
.archive-files {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.archive-persons h4,
.archive-files h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.version-history {
  padding: 8px 0;
}

.version-content {
  padding-left: 8px;
}

.version-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.version-number {
  font-weight: 600;
  color: #262626;
  font-size: 16px;
}

.version-time {
  font-size: 12px;
  color: #8c8c8c;
}

.version-description {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.version-meta {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.version-meta span {
  margin-right: 16px;
}

.version-actions {
  margin-top: 8px;
}
</style>
