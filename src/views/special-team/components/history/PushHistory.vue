<template>
  <div class="push-history">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>推送历史</h3>
        <p>推送历史查询与确认反馈，推送信息归档管理</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportPushHistory">
            <template #icon><export-outlined /></template>
            导出历史
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 推送统计 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总推送次数"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><send-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="成功推送"
              :value="statistics.success"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><check-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="待确认"
              :value="statistics.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><clock-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="推送对象总数"
              :value="statistics.totalCandidates"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><user-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 时间线视图切换 -->
    <div class="view-toggle-section">
      <a-card size="small">
        <a-radio-group v-model:value="viewMode" @change="handleViewModeChange">
          <a-radio-button value="table">表格视图</a-radio-button>
          <a-radio-button value="timeline">时间线视图</a-radio-button>
          <a-radio-button value="chart">图表视图</a-radio-button>
        </a-radio-group>

        <a-divider type="vertical" />

        <a-space>
          <span>筛选：</span>
          <a-select v-model:value="statusFilter" style="width: 120px" @change="handleFilter">
            <a-select-option value="all">全部状态</a-select-option>
            <a-select-option value="pending">待确认</a-select-option>
            <a-select-option value="confirmed">已确认</a-select-option>
            <a-select-option value="rejected">已拒绝</a-select-option>
          </a-select>
          <a-range-picker
            v-model:value="dateFilter"
            style="width: 240px"
            @change="handleFilter"
          />
        </a-space>
      </a-card>
    </div>

    <!-- 表格视图 -->
    <div v-if="viewMode === 'table'" class="table-view-section">
      <a-card title="推送记录" size="small">
        <a-table
          :columns="pushColumns"
          :data-source="filteredPushList"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          :scroll="{ x: 1400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <a-button type="link" @click="viewPushDetail(record)">
                {{ record.title }}
              </a-button>
            </template>

            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>

            <template v-if="column.key === 'feedback'">
              <div v-if="record.feedback">
                <a-tooltip :title="record.feedback">
                  <a-button type="link" size="small">查看反馈</a-button>
                </a-tooltip>
              </div>
              <span v-else class="text-muted">暂无反馈</span>
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewPushDetail(record)">
                  查看
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="trackPush(record)"
                  :disabled="record.status === 'confirmed'"
                >
                  跟踪
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handlePushAction(key, record)">
                      <a-menu-item key="resend" :disabled="record.status === 'confirmed'">重新推送</a-menu-item>
                      <a-menu-item key="recall" :disabled="record.status !== 'pending'">撤回推送</a-menu-item>
                      <a-menu-item key="archive">归档记录</a-menu-item>
                      <a-menu-item key="export">导出记录</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 时间线视图 -->
    <div v-if="viewMode === 'timeline'" class="timeline-view-section">
      <a-card title="推送时间线" size="small">
        <a-timeline mode="left">
          <a-timeline-item
            v-for="record in filteredPushList"
            :key="record.id"
            :color="getTimelineColor(record.status)"
          >
            <template #label>
              <span class="timeline-date">{{ formatDate(record.pushTime) }}</span>
            </template>
            <template #dot>
              <component :is="getTimelineIcon(record.status)" />
            </template>
            <div class="timeline-content">
              <div class="timeline-header">
                <h4>{{ record.title }}</h4>
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </div>
              <div class="timeline-meta">
                <a-space>
                  <span>推送人：{{ record.pushUser }}</span>
                  <span>对象数量：{{ record.candidateCount }}人</span>
                  <span>优先级：{{ getPriorityText(record.priority) }}</span>
                </a-space>
              </div>
              <div class="timeline-description">{{ record.description }}</div>
              <div class="timeline-actions">
                <a-space>
                  <a-button type="link" size="small" @click="viewPushDetail(record)">
                    查看详情
                  </a-button>
                  <a-button type="link" size="small" @click="trackPush(record)">
                    跟踪状态
                  </a-button>
                </a-space>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </a-card>
    </div>

    <!-- 图表视图 -->
    <div v-if="viewMode === 'chart'" class="chart-view-section">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="推送状态分布" size="small">
            <div class="chart-placeholder">
              <a-empty description="推送状态饼图" />
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="推送趋势分析" size="small">
            <div class="chart-placeholder">
              <a-empty description="推送趋势折线图" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 16px">
        <a-col :span="24">
          <a-card title="推送效率分析" size="small">
            <div class="chart-placeholder">
              <a-empty description="推送效率柱状图" />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 推送详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="推送详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentPush" class="push-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="推送标题">{{ currentPush.title }}</a-descriptions-item>
          <a-descriptions-item label="推送状态">
            <a-tag :color="getStatusColor(currentPush.status)">
              {{ getStatusText(currentPush.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推送时间">{{ formatDate(currentPush.pushTime) }}</a-descriptions-item>
          <a-descriptions-item label="推送人员">{{ currentPush.pushUser }}</a-descriptions-item>
          <a-descriptions-item label="对象数量">{{ currentPush.candidateCount }}人</a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentPush.priority)">
              {{ getPriorityText(currentPush.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="期望反馈时间">{{ formatDate(currentPush.expectedFeedbackTime) }}</a-descriptions-item>
          <a-descriptions-item label="实际反馈时间">{{ formatDate(currentPush.actualFeedbackTime) }}</a-descriptions-item>
          <a-descriptions-item label="推送说明" :span="2">{{ currentPush.description }}</a-descriptions-item>
        </a-descriptions>

        <div class="push-candidates">
          <h4>推送对象列表</h4>
          <a-table
            :columns="candidateColumns"
            :data-source="currentPush.candidates"
            :pagination="false"
            size="small"
          />
        </div>

        <div v-if="currentPush.feedback" class="push-feedback">
          <h4>反馈信息</h4>
          <div class="feedback-content">
            <div class="feedback-header">
              <span>反馈时间：{{ formatDate(currentPush.actualFeedbackTime) }}</span>
              <span>反馈人：{{ currentPush.feedbackUser }}</span>
            </div>
            <div class="feedback-text">{{ currentPush.feedback }}</div>
          </div>
        </div>

        <div class="push-timeline">
          <h4>推送流程</h4>
          <a-timeline size="small">
            <a-timeline-item color="blue">
              <span>{{ formatDate(currentPush.pushTime) }} - 推送发起</span>
            </a-timeline-item>
            <a-timeline-item color="orange" v-if="currentPush.status !== 'pending'">
              <span>{{ formatDate(currentPush.actualFeedbackTime) }} - 收到反馈</span>
            </a-timeline-item>
            <a-timeline-item
              :color="currentPush.status === 'confirmed' ? 'green' : 'red'"
              v-if="currentPush.status !== 'pending'"
            >
              <span>{{ formatDate(currentPush.actualFeedbackTime) }} - {{ getStatusText(currentPush.status) }}</span>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-modal>

    <!-- 推送跟踪弹窗 -->
    <a-modal
      v-model:open="trackVisible"
      title="推送跟踪"
      width="600px"
      :footer="null"
    >
      <div v-if="currentPush" class="push-track">
        <div class="track-status">
          <a-result
            :status="getTrackStatus(currentPush.status)"
            :title="getTrackTitle(currentPush.status)"
            :sub-title="getTrackSubTitle(currentPush)"
          >
            <template #extra>
              <a-space>
                <a-button type="primary" @click="refreshTrack">刷新状态</a-button>
                <a-button @click="sendReminder" :disabled="currentPush.status !== 'pending'">
                  发送提醒
                </a-button>
              </a-space>
            </template>
          </a-result>
        </div>

        <div class="track-progress">
          <h4>处理进度</h4>
          <a-steps :current="getProgressStep(currentPush.status)" size="small">
            <a-step title="推送发起" description="推送已发送至总专班" />
            <a-step title="接收确认" description="总专班已接收推送" />
            <a-step title="处理完成" description="推送处理完成" />
          </a-steps>
        </div>

        <div v-if="currentPush.status === 'pending'" class="track-reminder">
          <h4>提醒设置</h4>
          <a-form layout="inline">
            <a-form-item label="提醒方式">
              <a-checkbox-group v-model:value="reminderMethods">
                <a-checkbox value="email">邮件</a-checkbox>
                <a-checkbox value="sms">短信</a-checkbox>
                <a-checkbox value="system">系统通知</a-checkbox>
              </a-checkbox-group>
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="sendCustomReminder">发送提醒</a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  SendOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  DownOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Candidate {
  id: number
  name: string
  position: string
  department: string
  score: number
}

interface PushRecord {
  id: number
  title: string
  description: string
  candidateCount: number
  status: 'pending' | 'confirmed' | 'rejected'
  priority: 'high' | 'medium' | 'low'
  pushTime: string
  pushUser: string
  expectedFeedbackTime: string
  actualFeedbackTime?: string
  feedback?: string
  feedbackUser?: string
  candidates: Candidate[]
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const trackVisible = ref(false)
const currentPush = ref<PushRecord | null>(null)
const pushList = ref<PushRecord[]>([])
const viewMode = ref('table')
const statusFilter = ref('all')
const dateFilter = ref(null as any)
const reminderMethods = ref(['system'])

// 统计数据
const statistics = reactive({
  total: 15,
  success: 10,
  pending: 3,
  totalCandidates: 42
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性
const filteredPushList = computed(() => {
  let list = [...pushList.value]

  if (statusFilter.value !== 'all') {
    list = list.filter(push => push.status === statusFilter.value)
  }

  // 按时间排序
  list.sort((a, b) => new Date(b.pushTime).getTime() - new Date(a.pushTime).getTime())

  return list
})

// 表格列配置
const pushColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  {
    title: '推送标题',
    dataIndex: 'title',
    key: 'title',
    width: 200
  },
  {
    title: '对象数量',
    dataIndex: 'candidateCount',
    key: 'candidateCount',
    width: 100,
    customRender: ({ text }: any) => `${text}人`
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '推送人员',
    dataIndex: 'pushUser',
    key: 'pushUser',
    width: 100
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    key: 'priority',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '反馈信息',
    key: 'feedback',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const
  }
]

// 候选对象列配置
const candidateColumns = [
  { title: '序号', key: 'index', customRender: ({ index }: any) => index + 1 },
  { title: '姓名', dataIndex: 'name', key: 'name' },
  { title: '职务', dataIndex: 'position', key: 'position' },
  { title: '部门', dataIndex: 'department', key: 'department' },
  { title: '得分', dataIndex: 'score', key: 'score', customRender: ({ text }: any) => `${text}分` }
]

// 初始化模拟数据
const initMockData = () => {
  pushList.value = [
    {
      id: 1,
      title: '第一批优异对象推送',
      description: '推送第一批优异培育对象至总专班进行综合评审',
      candidateCount: 3,
      status: 'confirmed',
      priority: 'high',
      pushTime: '2025-06-15T14:20:00Z',
      pushUser: '李管理员',
      expectedFeedbackTime: '2025-06-25T00:00:00Z',
      actualFeedbackTime: '2025-06-16T09:30:00Z',
      feedback: '已收到推荐名单，将在一周内完成综合评审。推荐对象质量较高，符合评选标准。',
      feedbackUser: '总专班负责人',
      candidates: [
        { id: 1, name: '周海军', position: '党支部书记', department: '组织部', score: 95 },
        { id: 2, name: '杜佳佳', position: '科长', department: '办公室', score: 92 },
        { id: 3, name: '孙建', position: '主任', department: '人事部', score: 89 }
      ]
    },
    {
      id: 2,
      title: '补充推荐对象推送',
      description: '补充推送1名优异培育对象',
      candidateCount: 1,
      status: 'pending',
      priority: 'medium',
      pushTime: '2025-06-18T16:45:00Z',
      pushUser: '王管理员',
      expectedFeedbackTime: '2025-06-28T00:00:00Z',
      candidates: [
        { id: 4, name: '王东', position: '副主任', department: '宣传部', score: 88 }
      ]
    },
    {
      id: 3,
      title: '专项技能推荐推送',
      description: '推送专项技能优秀对象',
      candidateCount: 2,
      status: 'rejected',
      priority: 'low',
      pushTime: '2025-06-12T11:30:00Z',
      pushUser: '张管理员',
      expectedFeedbackTime: '2025-06-22T00:00:00Z',
      actualFeedbackTime: '2025-06-14T15:20:00Z',
      feedback: '推荐对象在专项技能方面还需进一步提升，建议继续培养后再次推荐。',
      feedbackUser: '总专班评审组',
      candidates: [
        { id: 5, name: '黄鑫', position: '技术员', department: '技术部', score: 82 },
        { id: 6, name: '周黄鑫', position: '工程师', department: '工程部', score: 85 }
      ]
    }
  ]
  pagination.total = pushList.value.length
}

// 工具函数
const formatDate = (dateStr: string | undefined) => {
  if (!dateStr) return '-'
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getStatusColor = (status: string) => {
  const colorMap = {
    pending: 'orange',
    confirmed: 'green',
    rejected: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    pending: '待确认',
    confirmed: '已确认',
    rejected: '已拒绝'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[priority as keyof typeof colorMap] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

const getTimelineColor = (status: string) => {
  const colorMap = {
    pending: 'blue',
    confirmed: 'green',
    rejected: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'blue'
}

const getTimelineIcon = (status: string) => {
  const iconMap = {
    pending: ClockCircleOutlined,
    confirmed: CheckCircleOutlined,
    rejected: CloseCircleOutlined
  }
  return iconMap[status as keyof typeof iconMap] || ClockCircleOutlined
}

const getTrackStatus = (status: string) => {
  const statusMap = {
    pending: 'info',
    confirmed: 'success',
    rejected: 'error'
  }
  return statusMap[status as keyof typeof statusMap] || 'info'
}

const getTrackTitle = (status: string) => {
  const titleMap = {
    pending: '推送处理中',
    confirmed: '推送已确认',
    rejected: '推送已拒绝'
  }
  return titleMap[status as keyof typeof titleMap] || '推送处理中'
}

const getTrackSubTitle = (push: PushRecord) => {
  if (push.status === 'pending') {
    return `推送于 ${formatDate(push.pushTime)}，等待总专班确认`
  } else if (push.status === 'confirmed') {
    return `已于 ${formatDate(push.actualFeedbackTime)} 确认接收`
  } else {
    return `已于 ${formatDate(push.actualFeedbackTime)} 拒绝接收`
  }
}

const getProgressStep = (status: string) => {
  const stepMap = {
    pending: 1,
    confirmed: 2,
    rejected: 2
  }
  return stepMap[status as keyof typeof stepMap] || 0
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const exportPushHistory = () => {
  message.success('推送历史导出成功')
}

const handleViewModeChange = () => {
  console.log('视图模式变更:', viewMode.value)
}

const handleFilter = () => {
  console.log('筛选条件变更:', statusFilter.value, dateFilter.value)
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const viewPushDetail = (record: PushRecord) => {
  currentPush.value = record
  detailVisible.value = true
}

const trackPush = (record: PushRecord) => {
  currentPush.value = record
  trackVisible.value = true
}

const handlePushAction = (key: string, record: PushRecord) => {
  switch (key) {
    case 'resend':
      message.success(`重新推送：${record.title}`)
      break
    case 'recall':
      message.success(`撤回推送：${record.title}`)
      break
    case 'archive':
      message.success(`归档记录：${record.title}`)
      break
    case 'export':
      message.success(`导出记录：${record.title}`)
      break
  }
}

const refreshTrack = () => {
  message.success('状态已刷新')
}

const sendReminder = () => {
  message.success('提醒已发送')
}

const sendCustomReminder = () => {
  if (reminderMethods.value.length === 0) {
    message.warning('请选择提醒方式')
    return
  }
  message.success(`已通过 ${reminderMethods.value.join('、')} 发送提醒`)
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.push-history {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 16px;
}

.view-toggle-section {
  margin-bottom: 16px;
}

.table-view-section,
.timeline-view-section,
.chart-view-section {
  margin-bottom: 16px;
}

.text-muted {
  color: #bfbfbf;
}

.timeline-date {
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeline-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.timeline-meta {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.timeline-description {
  margin-bottom: 8px;
  color: #666;
  line-height: 1.5;
}

.timeline-actions {
  margin-top: 8px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
}

.push-detail {
  padding: 8px 0;
}

.push-candidates,
.push-feedback,
.push-timeline {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.push-candidates h4,
.push-feedback h4,
.push-timeline h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.feedback-content {
  background: #f6f6f6;
  padding: 16px;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.feedback-text {
  color: #262626;
  line-height: 1.6;
}

.push-track {
  padding: 8px 0;
}

.track-status {
  text-align: center;
  margin-bottom: 24px;
}

.track-progress,
.track-reminder {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.track-progress h4,
.track-reminder h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}
</style>