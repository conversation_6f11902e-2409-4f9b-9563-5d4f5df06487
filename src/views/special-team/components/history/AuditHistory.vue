<template>
  <div class="audit-history">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>审核历史</h3>
        <p>查询审核历史记录，支持多维度筛选和详细记录查看</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportHistory">
            <template #icon><export-outlined /></template>
            导出记录
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总审核记录"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><file-text-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="通过审核"
              :value="statistics.approved"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><check-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="拒绝审核"
              :value="statistics.rejected"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><close-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="平均得分"
              :value="statistics.averageScore"
              suffix="分"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><trophy-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 高级筛选 -->
    <div class="filter-section">
      <a-card title="筛选条件" size="small">
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="审核对象">
            <a-input 
              v-model:value="filterForm.candidateName" 
              placeholder="请输入姓名"
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item label="审核人">
            <a-select 
              v-model:value="filterForm.auditor" 
              placeholder="请选择审核人"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="user1">张审核员</a-select-option>
              <a-select-option value="user2">李审核员</a-select-option>
              <a-select-option value="user3">王审核员</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审核结果">
            <a-select 
              v-model:value="filterForm.result" 
              placeholder="请选择结果"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="approved">通过</a-select-option>
              <a-select-option value="rejected">拒绝</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审核时间">
            <a-range-picker 
              v-model:value="filterForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item label="得分范围">
            <a-input-group compact>
              <a-input-number 
                v-model:value="filterForm.minScore"
                placeholder="最低分"
                style="width: 80px"
                :min="0"
                :max="100"
              />
              <a-input
                style="width: 30px; text-align: center; pointer-events: none"
                placeholder="~"
                disabled
              />
              <a-input-number 
                v-model:value="filterForm.maxScore"
                placeholder="最高分"
                style="width: 80px"
                :min="0"
                :max="100"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleFilter">
                <template #icon><search-outlined /></template>
                筛选
              </a-button>
              <a-button @click="resetFilter">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 审核记录列表 -->
    <div class="history-list-section">
      <a-card title="审核记录" size="small">
        <template #extra>
          <a-space>
            <span>排序：</span>
            <a-select v-model:value="sortBy" style="width: 120px" @change="handleSort">
              <a-select-option value="time">审核时间</a-select-option>
              <a-select-option value="score">审核得分</a-select-option>
              <a-select-option value="name">对象姓名</a-select-option>
            </a-select>
            <a-select v-model:value="sortOrder" style="width: 80px" @change="handleSort">
              <a-select-option value="desc">降序</a-select-option>
              <a-select-option value="asc">升序</a-select-option>
            </a-select>
          </a-space>
        </template>
        
        <a-table
          :columns="historyColumns"
          :data-source="filteredHistoryList"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          :scroll="{ x: 1200 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'candidateName'">
              <a-button type="link" @click="viewCandidateDetail(record)">
                {{ record.candidateName }}
              </a-button>
            </template>
            
            <template v-if="column.key === 'result'">
              <a-tag :color="getResultColor(record.result)">
                {{ getResultText(record.result) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'score'">
              <span class="score-display">{{ record.score }}分</span>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewAuditDetail(record)">
                  查看详情
                </a-button>
                <a-button type="link" size="small" @click="viewAuditProcess(record)">
                  审核过程
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="({ key }) => handleRecordAction(key, record)">
                      <a-menu-item key="export">导出记录</a-menu-item>
                      <a-menu-item key="compare">对比分析</a-menu-item>
                      <a-menu-item key="archive">归档记录</a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 审核详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="审核详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentRecord" class="audit-detail">
        <a-descriptions :column="2" bordered>
          <!-- <a-descriptions-item label="审核对象">{{ currentRecord.candidateName }}</a-descriptions-item>
          <a-descriptions-item label="职务">{{ currentRecord.position }}</a-descriptions-item> -->
          <a-descriptions-item label="部门">{{ currentRecord.department }}</a-descriptions-item>
          <a-descriptions-item label="审核人">{{ currentRecord.auditor }}</a-descriptions-item>
          <a-descriptions-item label="审核时间">{{ formatDate(currentRecord.auditTime) }}</a-descriptions-item>
          <a-descriptions-item label="审核得分">
            <span class="score-display">{{ currentRecord.score }}分</span>
          </a-descriptions-item>
          <a-descriptions-item label="审核结果">
            <a-tag :color="getResultColor(currentRecord.result)">
              {{ getResultText(currentRecord.result) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="审核类型">{{ currentRecord.auditType }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="audit-content">
          <h4>审核意见</h4>
          <div class="audit-comments">{{ currentRecord.comments }}</div>
          
          <div v-if="currentRecord.dimensions" class="dimension-scores">
            <h4>维度评分</h4>
            <a-table
              :columns="dimensionColumns"
              :data-source="currentRecord.dimensions"
              :pagination="false"
              size="small"
            />
          </div>
          
          <div v-if="currentRecord.attachments" class="attachments">
            <h4>相关附件</h4>
            <a-list size="small" :data-source="currentRecord.attachments">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <a-button type="link" @click="downloadAttachment(item)">
                        <file-outlined /> {{ item.name }}
                      </a-button>
                    </template>
                    <template #description>{{ item.size }}</template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 审核过程弹窗 -->
    <a-modal
      v-model:open="processVisible"
      title="审核过程"
      width="700px"
      :footer="null"
    >
      <div v-if="currentRecord" class="audit-process">
        <a-timeline>
          <a-timeline-item v-for="step in auditSteps" :key="step.id" :color="step.color">
            <template #dot>
              <component :is="step.icon" />
            </template>
            <div class="step-content">
              <div class="step-header">
                <span class="step-title">{{ step.title }}</span>
                <span class="step-time">{{ formatDate(step.time) }}</span>
              </div>
              <div class="step-description">{{ step.description }}</div>
              <div v-if="step.operator" class="step-operator">操作人：{{ step.operator }}</div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>

    <!-- 候选对象详情弹窗 -->
    <a-modal
      v-model:open="candidateVisible"
      title="候选对象详情"
      width="600px"
      :footer="null"
    >
      <div v-if="currentRecord" class="candidate-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="姓名">{{ currentRecord.candidateName }}</a-descriptions-item>
          <a-descriptions-item label="性别">{{ currentRecord.gender }}</a-descriptions-item>
          <a-descriptions-item label="年龄">{{ currentRecord.age }}岁</a-descriptions-item>
          <a-descriptions-item label="职务">{{ currentRecord.position }}</a-descriptions-item>
          <a-descriptions-item label="部门">{{ currentRecord.department }}</a-descriptions-item>
          <a-descriptions-item label="入选时间">{{ formatDate(currentRecord.selectedTime) }}</a-descriptions-item>
          <a-descriptions-item label="培育结果">{{ currentRecord.cultivationResult }}</a-descriptions-item>
          <a-descriptions-item label="综合得分">{{ currentRecord.comprehensiveScore }}分</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  ExportOutlined, 
  SearchOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  TrophyOutlined,
  FileOutlined,
  DownOutlined,
  UserOutlined,
  EditOutlined,
  AuditOutlined,
  CheckOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Dimension {
  name: string
  score: number
  weight: number
}

interface Attachment {
  id: number
  name: string
  size: string
}

interface AuditStep {
  id: number
  title: string
  description: string
  time: string
  operator: string
  color: string
  icon: any
}

interface AuditRecord {
  id: number
  candidateName: string
  gender: string
  age: number
  position: string
  department: string
  auditor: string
  auditTime: string
  auditType: string
  score: number
  result: 'approved' | 'rejected'
  comments: string
  selectedTime: string
  cultivationResult: string
  comprehensiveScore: number
  dimensions?: Dimension[]
  attachments?: Attachment[]
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const processVisible = ref(false)
const candidateVisible = ref(false)
const currentRecord = ref<AuditRecord | null>(null)
const historyList = ref<AuditRecord[]>([])
const sortBy = ref('time')
const sortOrder = ref('desc')

// 统计数据
const statistics = reactive({
  total: 25,
  approved: 18,
  rejected: 7,
  averageScore: 87.5
})

// 筛选表单
const filterForm = reactive({
  candidateName: '',
  auditor: undefined as string | undefined,
  result: undefined as string | undefined,
  dateRange: null as any,
  minScore: undefined as number | undefined,
  maxScore: undefined as number | undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性
const filteredHistoryList = computed(() => {
  let list = [...historyList.value]
  
  if (filterForm.candidateName) {
    list = list.filter(record => 
      record.candidateName.includes(filterForm.candidateName)
    )
  }
  
  if (filterForm.auditor) {
    list = list.filter(record => record.auditor === filterForm.auditor)
  }
  
  if (filterForm.result) {
    list = list.filter(record => record.result === filterForm.result)
  }
  
  if (filterForm.minScore !== undefined) {
    list = list.filter(record => record.score >= filterForm.minScore!)
  }
  
  if (filterForm.maxScore !== undefined) {
    list = list.filter(record => record.score <= filterForm.maxScore!)
  }
  
  // 排序
  list.sort((a, b) => {
    let aValue: any, bValue: any
    
    switch (sortBy.value) {
      case 'time':
        aValue = new Date(a.auditTime).getTime()
        bValue = new Date(b.auditTime).getTime()
        break
      case 'score':
        aValue = a.score
        bValue = b.score
        break
      case 'name':
        aValue = a.candidateName
        bValue = b.candidateName
        break
      default:
        return 0
    }
    
    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })
  
  return list
})

// 审核步骤
const auditSteps = computed(() => [
  {
    id: 1,
    title: '提交审核申请',
    description: '候选对象信息提交至审核系统',
    time: '2025-06-15T09:00:00Z',
    operator: '系统自动',
    color: 'blue',
    icon: UserOutlined
  },
  {
    id: 2,
    title: '分配审核人员',
    description: '系统自动分配审核人员进行审核',
    time: '2025-06-15T09:30:00Z',
    operator: '系统自动',
    color: 'blue',
    icon: EditOutlined
  },
  {
    id: 3,
    title: '开始审核',
    description: '审核人员开始对候选对象进行评估',
    time: '2025-06-16T10:00:00Z',
    operator: currentRecord.value?.auditor || '张审核员',
    color: 'orange',
    icon: AuditOutlined
  },
  {
    id: 4,
    title: '完成审核',
    description: '审核完成，记录审核意见和评分',
    time: currentRecord.value?.auditTime || '2025-06-16T15:30:00Z',
    operator: currentRecord.value?.auditor || '张审核员',
    color: 'green',
    icon: CheckOutlined
  }
])

// 表格列配置
const historyColumns = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1
  },
  // {
  //   title: '审核对象',
  //   dataIndex: 'candidateName',
  //   key: 'candidateName',
  //   width: 100
  // },
  // {
  //   title: '职务',
  //   dataIndex: 'position',
  //   key: 'position',
  //   width: 120
  // },
  {
    title: '部门',
    dataIndex: 'department',
    key: 'department',
    width: 120
  },
  {
    title: '审核人',
    dataIndex: 'auditor',
    key: 'auditor',
    width: 100
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    key: 'auditTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '审核得分',
    dataIndex: 'score',
    key: 'score',
    width: 100
  },
  {
    title: '审核结果',
    dataIndex: 'result',
    key: 'result',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right' as const
  }
]

// 维度评分列配置
const dimensionColumns = [
  { title: '评分维度', dataIndex: 'name', key: 'name' },
  { title: '得分', dataIndex: 'score', key: 'score', customRender: ({ text }: any) => `${text}分` },
  { title: '权重', dataIndex: 'weight', key: 'weight', customRender: ({ text }: any) => `${text}%` }
]

// 初始化模拟数据
const initMockData = () => {
  historyList.value = [
    {
      id: 1,
      candidateName: '周海军',
      gender: '男',
      age: 35,
      position: '党支部书记',
      department: '组织部',
      auditor: '陈胜',
      auditTime: '2025-06-16T15:30:00Z',
      auditType: '综合评审',
      score: 95,
      result: 'approved',
      comments: '该同志综合素质优秀，工作能力突出，具备较强的组织协调能力，建议通过审核。',
      selectedTime: '2025-06-10T09:00:00Z',
      cultivationResult: '优秀',
      comprehensiveScore: 94,
      dimensions: [
        { name: '成绩要求', score: 95, weight: 40 },
        { name: '能力指标', score: 92, weight: 35 },
        { name: '项目贡献度', score: 88, weight: 25 }
      ],
      attachments: [
        { id: 1, name: '个人简历.pdf', size: '1.2MB' },
        { id: 2, name: '工作总结.docx', size: '856KB' }
      ]
    },
    {
      id: 2,
      candidateName: '王东',
      gender: '女',
      age: 32,
      position: '副主任',
      department: '宣传部',
      auditor: '孙建',
      auditTime: '2025-06-18T11:20:00Z',
      auditType: '专项审核',
      score: 78,
      result: 'rejected',
      comments: '该同志工作表现一般，部分指标未达到要求，建议继续培养后再次申请。',
      selectedTime: '2025-06-08T14:30:00Z',
      cultivationResult: '良好',
      comprehensiveScore: 82,
      dimensions: [
        { name: '专业技能', score: 80, weight: 50 },
        { name: '实践能力', score: 75, weight: 30 },
        { name: '创新能力', score: 78, weight: 20 }
      ]
    }
  ]
  pagination.total = historyList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getResultColor = (result: string) => {
  return result === 'approved' ? 'green' : 'red'
}

const getResultText = (result: string) => {
  return result === 'approved' ? '通过' : '拒绝'
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const exportHistory = () => {
  message.success('审核历史导出成功')
}

const handleFilter = () => {
  message.info('应用筛选条件')
}

const resetFilter = () => {
  filterForm.candidateName = ''
  filterForm.auditor = undefined
  filterForm.result = undefined
  filterForm.dateRange = null
  filterForm.minScore = undefined
  filterForm.maxScore = undefined
  handleFilter()
}

const handleSort = () => {
  console.log('排序变更:', sortBy.value, sortOrder.value)
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const viewAuditDetail = (record: AuditRecord) => {
  currentRecord.value = record
  detailVisible.value = true
}

const viewAuditProcess = (record: AuditRecord) => {
  currentRecord.value = record
  processVisible.value = true
}

const viewCandidateDetail = (record: AuditRecord) => {
  currentRecord.value = record
  candidateVisible.value = true
}

const handleRecordAction = (key: string, record: AuditRecord) => {
  switch (key) {
    case 'export':
      message.success(`导出记录：${record.candidateName}`)
      break
    case 'compare':
      message.info(`对比分析：${record.candidateName}`)
      break
    case 'archive':
      message.success(`归档记录：${record.candidateName}`)
      break
  }
}

const downloadAttachment = (attachment: Attachment) => {
  message.success(`下载附件：${attachment.name}`)
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.audit-history {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 16px;
}

.filter-section {
  margin-bottom: 16px;
}

.history-list-section {
  margin-bottom: 16px;
}

.score-display {
  font-weight: 600;
  color: #1890ff;
}

.audit-detail {
  padding: 8px 0;
}

.audit-content {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.audit-content h4 {
  margin: 16px 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.audit-content h4:first-child {
  margin-top: 0;
}

.audit-comments {
  background: #f6f6f6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  line-height: 1.6;
  margin-bottom: 16px;
}

.audit-process {
  padding: 8px 0;
}

.step-content {
  padding-left: 8px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.step-title {
  font-weight: 500;
  color: #262626;
}

.step-time {
  font-size: 12px;
  color: #8c8c8c;
}

.step-description {
  color: #666;
  margin-bottom: 4px;
  line-height: 1.5;
}

.step-operator {
  font-size: 12px;
  color: #8c8c8c;
}

.candidate-detail {
  padding: 8px 0;
}
</style>
