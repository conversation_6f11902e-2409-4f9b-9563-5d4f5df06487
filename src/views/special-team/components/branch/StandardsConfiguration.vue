<template>
  <div class="standards-configuration">
    <!-- 页面头部 -->
    <div class="section-header">
      <h3>评选标准配置</h3>
      <p>自定义评选维度，设置多维度评选标准模板</p>
      <a-button type="primary" @click="showCreateModal">
        <template #icon><plus-outlined /></template>
        新建标准
      </a-button>
    </div>

    <!-- 标准列表 -->
    <div class="standards-list">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="standard in standardsList" :key="standard.id">
          <!-- <a-card 
            hoverable 
            class="standard-card"
            :actions="[
              { key: 'edit', icon: 'EditOutlined', text: '编辑' },
              { key: 'copy', icon: 'CopyOutlined', text: '复制' },
              { key: 'delete', icon: 'DeleteOutlined', text: '删除' }
            ]"
          > -->
           <a-card 
            hoverable 
            class="standard-card"
            
          >
            <template #title>
              <div class="card-title">
                <span>{{ standard.name }}</span>
                <a-tag :color="standard.status === 'active' ? 'green' : 'default'">
                  {{ standard.status === 'active' ? '启用' : '停用' }}
                </a-tag>
              </div>
            </template>
            
            <div class="standard-content">
              <p class="description">{{ standard.description }}</p>
              
              <div class="dimensions">
                <h5>评选维度：</h5>
                <a-space wrap>
                  <a-tag v-for="dimension in standard.dimensions" :key="dimension.id" color="blue">
                    {{ dimension.name }} ({{ dimension.weight }}%)
                  </a-tag>
                </a-space>
              </div>
              
              <div class="meta-info">
                <p><small>创建时间：{{ formatDate(standard.createTime) }}</small></p>
                <p><small>最后修改：{{ formatDate(standard.updateTime) }}</small></p>
              </div>
            </div>

            <template #actions>
              <a-tooltip title="编辑">
                <edit-outlined @click="editStandard(standard)" />
              </a-tooltip>
              <a-tooltip title="复制">
                <copy-outlined @click="copyStandard(standard)" />
              </a-tooltip>
              <a-tooltip title="删除">
                <delete-outlined @click="deleteStandard(standard)" />
              </a-tooltip>
            </template>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 创建/编辑标准弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑评选标准' : '新建评选标准'"
      width="800px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="标准名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入标准名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option value="active">启用</a-select-option>
                <a-select-option value="inactive">停用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="标准描述" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入标准描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="评选维度">
          <div class="dimensions-config">
            <div v-for="(dimension, index) in formData.dimensions" :key="index" class="dimension-item">
              <a-row :gutter="8" align="middle">
                <a-col :span="8">
                  <a-input 
                    v-model:value="dimension.name" 
                    placeholder="维度名称"
                  />
                </a-col>
                <a-col :span="6">
                  <a-input-number 
                    v-model:value="dimension.weight" 
                    placeholder="权重"
                    :min="0"
                    :max="100"
                    addon-after="%"
                    style="width: 100%"
                  />
                </a-col>
                <a-col :span="8">
                  <a-select 
                    v-model:value="dimension.type" 
                    placeholder="评分类型"
                    style="width: 100%"
                  >
                    <a-select-option value="score">分数</a-select-option>
                    <a-select-option value="level">等级</a-select-option>
                    <a-select-option value="boolean">是否</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="2">
                  <a-button 
                    type="text" 
                    danger 
                    @click="removeDimension(index)"
                    :disabled="formData.dimensions.length <= 1"
                  >
                    <delete-outlined />
                  </a-button>
                </a-col>
              </a-row>
            </div>
            
            <a-button 
              type="dashed" 
              block 
              @click="addDimension"
              style="margin-top: 8px"
            >
              <plus-outlined /> 添加维度
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlusOutlined, 
  EditOutlined, 
  CopyOutlined, 
  DeleteOutlined 
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Dimension {
  id?: number
  name: string
  weight: number
  type: 'score' | 'level' | 'boolean'
}

interface Standard {
  id: number
  name: string
  description: string
  status: 'active' | 'inactive'
  dimensions: Dimension[]
  createTime: string
  updateTime: string
}

// 响应式数据
const modalVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()
const standardsList = ref<Standard[]>([])

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  description: '',
  status: 'active' as 'active' | 'inactive',
  dimensions: [
    { name: '成绩要求', weight: 40, type: 'score' as const },
    { name: '能力指标', weight: 35, type: 'score' as const },
    { name: '项目贡献度', weight: 25, type: 'score' as const }
  ] as Dimension[]
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入标准名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入标准描述', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 初始化模拟数据
const initMockData = () => {
  standardsList.value = [
    {
      id: 1,
      name: '综合评选标准',
      description: '基于成绩、能力和项目贡献度的综合评选标准',
      status: 'active',
      dimensions: [
        { id: 1, name: '成绩要求', weight: 40, type: 'score' },
        { id: 2, name: '能力指标', weight: 35, type: 'score' },
        { id: 3, name: '项目贡献度', weight: 25, type: 'score' }
      ],
      createTime: '2025-06-15T10:30:00Z',
      updateTime: '2025-06-20T14:20:00Z'
    },
    {
      id: 2,
      name: '专项技能评选标准',
      description: '专注于技能水平和实践能力的评选标准',
      status: 'active',
      dimensions: [
        { id: 4, name: '专业技能', weight: 50, type: 'score' },
        { id: 5, name: '实践能力', weight: 30, type: 'score' },
        { id: 6, name: '创新能力', weight: 20, type: 'score' }
      ],
      createTime: '2025-06-10T09:15:00Z',
      updateTime: '2025-06-18T16:45:00Z'
    }
  ]
}

// 格式化日期
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

// 显示创建弹窗
const showCreateModal = () => {
  isEdit.value = false
  resetForm()
  modalVisible.value = true
}

// 编辑标准
const editStandard = (standard: Standard) => {
  isEdit.value = true
  formData.id = standard.id
  formData.name = standard.name
  formData.description = standard.description
  formData.status = standard.status
  formData.dimensions = [...standard.dimensions]
  modalVisible.value = true
}

// 复制标准
const copyStandard = (standard: Standard) => {
  isEdit.value = false
  resetForm()
  formData.name = `${standard.name} - 副本`
  formData.description = standard.description
  formData.status = 'inactive'
  formData.dimensions = [...standard.dimensions]
  modalVisible.value = true
}

// 删除标准
const deleteStandard = (standard: Standard) => {
  message.success(`删除标准：${standard.name}`)
  // 这里应该调用删除API
}

// 添加维度
const addDimension = () => {
  formData.dimensions.push({
    name: '',
    weight: 0,
    type: 'score'
  })
}

// 移除维度
const removeDimension = (index: number) => {
  formData.dimensions.splice(index, 1)
}

// 重置表单
const resetForm = () => {
  formData.id = null
  formData.name = ''
  formData.description = ''
  formData.status = 'active'
  formData.dimensions = [
    { name: '成绩要求', weight: 40, type: 'score' },
    { name: '能力指标', weight: 35, type: 'score' },
    { name: '项目贡献度', weight: 25, type: 'score' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validateFields()
    
    // 验证权重总和
    const totalWeight = formData.dimensions.reduce((sum, dim) => sum + (dim.weight || 0), 0)
    if (totalWeight !== 100) {
      message.error('所有维度权重总和必须等于100%')
      return
    }
    
    message.success(isEdit.value ? '标准更新成功' : '标准创建成功')
    modalVisible.value = false
    // 这里应该调用保存API并刷新列表
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.standards-configuration {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section-header h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.section-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.standards-list {
  margin-top: 24px;
}

.standard-card {
  height: 100%;
  border-radius: 8px;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standard-content .description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.dimensions h5 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.meta-info {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.meta-info p {
  margin: 4px 0;
  color: #8c8c8c;
}

.dimensions-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.dimension-item {
  margin-bottom: 8px;
}

.dimension-item:last-child {
  margin-bottom: 0;
}
</style>
