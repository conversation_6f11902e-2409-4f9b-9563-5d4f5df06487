<template>
	<div class="candidate-audit">
		<!-- 页面头部 -->
		<div class="section-header">
			<div class="header-left">
				<h3>培育对象审核</h3>
				<p>查看选树库培育对象列表，支持名称/入选时间双条件检索，记录审核意见与评定得分</p>
			</div>
			<div class="header-right">
				<a-space>
					<a-button @click="refreshData">
						<template #icon><reload-outlined /></template>
						刷新
					</a-button>
					<a-button type="primary" @click="batchAudit" :disabled="selectedRowKeys.length === 0">
						<template #icon><audit-outlined /></template>
						批量审核
					</a-button>
				</a-space>
			</div>
		</div>

		<!-- 搜索筛选区域 -->
		<div class="search-section">
			<a-card size="small">
				<a-form layout="inline" :model="searchForm">
					<a-form-item label="培育对象名称">
						<a-input v-model:value="searchForm.name" placeholder="请输入培育对象名称" style="width: 200px" @press-enter="handleSearch" />
					</a-form-item>
					<a-form-item label="入选时间">
						<a-range-picker v-model:value="searchForm.dateRange" style="width: 240px" />
					</a-form-item>
					<a-form-item label="培育结果">
						<a-select v-model:value="searchForm.result" placeholder="请选择培育结果" style="width: 150px" allow-clear>
							<a-select-option value="excellent">优秀</a-select-option>
							<a-select-option value="good">良好</a-select-option>
							<a-select-option value="average">一般</a-select-option>
							<a-select-option value="poor">较差</a-select-option>
						</a-select>
					</a-form-item>
					<a-form-item label="审核状态">
						<a-select v-model:value="searchForm.auditStatus" placeholder="请选择审核状态" style="width: 120px" allow-clear>
							<a-select-option value="pending">待审核</a-select-option>
							<a-select-option value="approved">已通过</a-select-option>
							<a-select-option value="rejected">已拒绝</a-select-option>
						</a-select>
					</a-form-item>
					<a-form-item>
						<a-space>
							<a-button type="primary" @click="handleSearch">
								<template #icon><search-outlined /></template>
								搜索
							</a-button>
							<a-button @click="resetSearch">重置</a-button>
						</a-space>
					</a-form-item>
				</a-form>
			</a-card>
		</div>

		<!-- 数据表格 -->
		<div class="table-section">
			<a-table
				:columns="columns"
				:data-source="candidateList"
				:loading="loading"
				:pagination="pagination"
				:row-selection="{ selectedRowKeys, onChange: onSelectChange }"
				row-key="id"
				@change="handleTableChange"
			>
				<template #bodyCell="{ column, record }">
					<template v-if="column.key === 'name'">
						<a-button type="link" @click="viewDetail(record)">
							{{ record.name }}
						</a-button>
					</template>

					<template v-if="column.key === 'result'">
						<a-tag :color="getResultColor(record.result)">
							{{ getResultText(record.result) }}
						</a-tag>
					</template>

					<template v-if="column.key === 'auditStatus'">
						<a-tag :color="getAuditStatusColor(record.auditStatus)">
							{{ getAuditStatusText(record.auditStatus) }}
						</a-tag>
					</template>

					<template v-if="column.key === 'score'">
						<span v-if="record.score">{{ record.score }}分</span>
						<span v-else class="text-muted">未评分</span>
					</template>

					<template v-if="column.key === 'action'">
						<a-space>
							<a-button type="link" size="small" @click="viewDetail(record)"> 查看 </a-button>
							<a-button type="link" size="small" @click="auditCandidate(record)" :disabled="record.auditStatus === 'approved'"> 审核 </a-button>
						</a-space>
					</template>
				</template>
			</a-table>
		</div>

		<!-- 详情弹窗 -->
		<a-modal v-model:open="detailVisible" title="培育对象详情" width="800px" :footer="null">
			<div v-if="currentCandidate" class="detail-content">
				<a-descriptions :column="2" bordered>
					<a-descriptions-item label="姓名">{{ currentCandidate.name }}</a-descriptions-item>
					<!-- <a-descriptions-item label="性别">{{ currentCandidate.gender }}</a-descriptions-item>
					<a-descriptions-item label="年龄">{{ currentCandidate.age }}岁</a-descriptions-item>
					<a-descriptions-item label="职务">{{ currentCandidate.position }}</a-descriptions-item>
					<a-descriptions-item label="入选时间">{{ formatDate(currentCandidate.selectedTime) }}</a-descriptions-item>
					<a-descriptions-item label="培育结果">
						<a-tag :color="getResultColor(currentCandidate.result)">
							{{ getResultText(currentCandidate.result) }}
						</a-tag>
					</a-descriptions-item> -->
					<a-descriptions-item label="成绩要求" :span="2">{{ currentCandidate.gradeRequirement }}</a-descriptions-item>
					<a-descriptions-item label="能力指标" :span="2">{{ currentCandidate.abilityIndicator }}</a-descriptions-item>
					<a-descriptions-item label="项目贡献度" :span="2">{{ currentCandidate.projectContribution }}</a-descriptions-item>
				</a-descriptions>

				<div v-if="currentCandidate.auditComments" class="audit-info">
					<h4>审核信息</h4>
					<p><strong>审核得分：</strong>{{ currentCandidate.score }}分</p>
					<p><strong>审核意见：</strong></p>
					<div class="audit-comments">{{ currentCandidate.auditComments }}</div>
					<p>
						<small>审核时间：{{ formatDate(currentCandidate.auditTime) }}</small>
					</p>
				</div>
			</div>
		</a-modal>

		<!-- 审核弹窗 -->
		<a-modal v-model:open="auditVisible" title="培育对象审核" width="600px" @ok="submitAudit" @cancel="cancelAudit">
			<div v-if="currentCandidate">
				<div class="candidate-info">
					<h4>{{ currentCandidate.name }}</h4>
					<p>{{ currentCandidate.position }} | 入选时间：{{ formatDate(currentCandidate.selectedTime) }}</p>
				</div>

				<a-form ref="auditFormRef" :model="auditForm" layout="vertical">
					<a-form-item label="评定得分" name="score" :rules="[{ required: true, message: '请输入评定得分' }]">
						<a-input-number v-model:value="auditForm.score" :min="0" :max="100" placeholder="请输入0-100分" style="width: 100%" />
					</a-form-item>

					<a-form-item label="审核意见" name="comments" :rules="[{ required: true, message: '请输入审核意见' }]">
						<a-textarea v-model:value="auditForm.comments" placeholder="请输入审核意见" :rows="4" />
					</a-form-item>

					<a-form-item label="审核结果" name="result" :rules="[{ required: true, message: '请选择审核结果' }]">
						<a-radio-group v-model:value="auditForm.result">
							<a-radio value="approved">通过</a-radio>
							<a-radio value="rejected">拒绝</a-radio>
						</a-radio-group>
					</a-form-item>
				</a-form>
			</div>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, AuditOutlined, SearchOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Candidate {
	id: number
	name: string
	gender: string
	age: number
	position: string
	selectedTime: string
	result: 'excellent' | 'good' | 'average' | 'poor'
	gradeRequirement: string
	abilityIndicator: string
	projectContribution: string
	auditStatus: 'pending' | 'approved' | 'rejected'
	score?: number
	auditComments?: string
	auditTime?: string
	unit?: string
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const auditVisible = ref(false)
const selectedRowKeys = ref<number[]>([])
const currentCandidate = ref<Candidate | null>(null)
const candidateList = ref<Candidate[]>([])
const auditFormRef = ref()

// 搜索表单
const searchForm = reactive({
	name: '',
	dateRange: null as any,
	result: undefined as string | undefined,
	auditStatus: undefined as string | undefined,
})

// 审核表单
const auditForm = reactive({
	score: undefined as number | undefined,
	comments: '',
	result: 'approved' as 'approved' | 'rejected',
})

// 分页配置
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showSizeChanger: true,
	showQuickJumper: true,
	showTotal: (total: number) => `共 ${total} 条记录`,
})

// 表格列配置
const columns = [
	{
		title: '序号',
		key: 'index',
		width: 60,
		customRender: ({ index }: any) => (pagination.current - 1) * pagination.pageSize + index + 1,
	},
	{
		title: '单位',
		dataIndex: 'unit',
		key: 'unit',
		width: 100,
	},
	// {
	//   title: '性别',
	//   dataIndex: 'gender',
	//   key: 'gender',
	//   width: 60
	// },
	// {
	//   title: '年龄',
	//   dataIndex: 'age',
	//   key: 'age',
	//   width: 60
	// },
	// {
	//   title: '职务',
	//   dataIndex: 'position',
	//   key: 'position',
	//   width: 120
	// },
	{
		title: '入选时间',
		dataIndex: 'selectedTime',
		key: 'selectedTime',
		width: 120,
		customRender: ({ text }: any) => formatDate(text),
	},
	{
		title: '培育结果',
		dataIndex: 'result',
		key: 'result',
		width: 100,
	},
	{
		title: '审核状态',
		dataIndex: 'auditStatus',
		key: 'auditStatus',
		width: 100,
	},
	{
		title: '评定得分',
		dataIndex: 'score',
		key: 'score',
		width: 100,
	},
	{
		title: '操作',
		key: 'action',
		width: 120,
		fixed: 'right' as const,
	},
]

// 初始化模拟数据
const initMockData = () => {
	candidateList.value = [
		{
			id: 1,
			name: '周海军',
			unit: '市人大',
			gender: '男',
			age: 35,
			position: '党支部书记',
			selectedTime: '2025-06-15T10:30:00Z',
			result: 'excellent',
			gradeRequirement: '年度考核优秀，连续三年获得先进个人',
			abilityIndicator: '组织协调能力强，具备较强的领导力和沟通能力',
			projectContribution: '主导完成重点项目3个，为组织发展做出突出贡献',
			auditStatus: 'approved',
			score: 95,
			auditComments: '该同志表现优秀，各项指标均达到要求，建议通过审核。',
			auditTime: '2025-06-20T14:20:00Z',
		},
		{
			id: 2,
			name: '王东',
			unit: '市委网信办',
			gender: '女',
			age: 32,
			position: '副主任',
			selectedTime: '2025-06-10T09:15:00Z',
			result: 'good',
			gradeRequirement: '年度考核良好，工作表现稳定',
			abilityIndicator: '专业能力扎实，学习能力较强',
			projectContribution: '参与完成重要项目2个，工作认真负责',
			auditStatus: 'pending',
			score: undefined,
			auditComments: undefined,
			auditTime: undefined,
		},
	]
	pagination.total = candidateList.value.length
}

// 工具函数
const formatDate = (dateStr: string) => {
	return dayjs(dateStr).format('YYYY-MM-DD')
}

const getResultColor = (result: string) => {
	const colorMap = {
		excellent: 'green',
		good: 'blue',
		average: 'orange',
		poor: 'red',
	}
	return colorMap[result as keyof typeof colorMap] || 'default'
}

const getResultText = (result: string) => {
	const textMap = {
		excellent: '优秀',
		good: '良好',
		average: '一般',
		poor: '较差',
	}
	return textMap[result as keyof typeof textMap] || result
}

const getAuditStatusColor = (status: string) => {
	const colorMap = {
		pending: 'orange',
		approved: 'green',
		rejected: 'red',
	}
	return colorMap[status as keyof typeof colorMap] || 'default'
}

const getAuditStatusText = (status: string) => {
	const textMap = {
		pending: '待审核',
		approved: '已通过',
		rejected: '已拒绝',
	}
	return textMap[status as keyof typeof textMap] || status
}

// 事件处理函数
const refreshData = () => {
	message.success('数据已刷新')
	initMockData()
}

const handleSearch = () => {
	message.info('执行搜索')
	// 这里应该调用搜索API
}

const resetSearch = () => {
	searchForm.name = ''
	searchForm.dateRange = null
	searchForm.result = undefined
	searchForm.auditStatus = undefined
	handleSearch()
}

const onSelectChange = (keys: number[]) => {
	selectedRowKeys.value = keys
}

const handleTableChange = (pag: any) => {
	pagination.current = pag.current
	pagination.pageSize = pag.pageSize
	// 这里应该调用分页API
}

const viewDetail = (record: Candidate) => {
	currentCandidate.value = record
	detailVisible.value = true
}

const auditCandidate = (record: Candidate) => {
	currentCandidate.value = record
	auditForm.score = record.score
	auditForm.comments = record.auditComments || ''
	auditForm.result = 'approved'
	auditVisible.value = true
}

const batchAudit = () => {
	message.info(`批量审核 ${selectedRowKeys.value.length} 个对象`)
	// 这里应该实现批量审核逻辑
}

const submitAudit = async () => {
	try {
		await auditFormRef.value.validateFields()
		message.success('审核提交成功')
		auditVisible.value = false
		refreshData()
	} catch (error) {
		console.error('审核表单验证失败:', error)
	}
}

const cancelAudit = () => {
	auditVisible.value = false
	auditForm.score = undefined
	auditForm.comments = ''
	auditForm.result = 'approved'
}

// 页面初始化
onMounted(() => {
	initMockData()
})
</script>

<style scoped>
.candidate-audit {
	padding: 0;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16px;
	padding-bottom: 16px;
	border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
	margin: 0 0 4px 0;
	color: #262626;
	font-size: 18px;
	font-weight: 600;
}

.header-left p {
	margin: 0;
	color: #8c8c8c;
	font-size: 14px;
}

.search-section {
	margin-bottom: 16px;
}

.table-section {
	background: white;
	border-radius: 8px;
}

.detail-content .audit-info {
	margin-top: 24px;
	padding-top: 16px;
	border-top: 1px solid #f0f0f0;
}

.audit-info h4 {
	margin: 0 0 12px 0;
	color: #262626;
	font-size: 16px;
	font-weight: 600;
}

.audit-comments {
	background: #f6f6f6;
	padding: 12px;
	border-radius: 4px;
	border-left: 3px solid #1890ff;
	margin: 8px 0;
	line-height: 1.6;
}

.candidate-info {
	margin-bottom: 24px;
	padding: 16px;
	background: #f6f6f6;
	border-radius: 6px;
}

.candidate-info h4 {
	margin: 0 0 8px 0;
	color: #262626;
	font-size: 16px;
	font-weight: 600;
}

.candidate-info p {
	margin: 0;
	color: #666;
	font-size: 14px;
}

.text-muted {
	color: #bfbfbf;
}
</style>
