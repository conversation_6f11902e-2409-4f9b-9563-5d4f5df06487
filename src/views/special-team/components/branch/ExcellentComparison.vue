<template>
  <div class="excellent-comparison">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>优异对象比选</h3>
        <p>多维度比选：成绩排名/能力评估/项目参与度，标注优异培育对象并生成推荐名单</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="generateRecommendList" :disabled="excellentCandidates.length === 0">
            <template #icon><file-text-outlined /></template>
            生成推荐名单
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 比选维度选择 -->
    <div class="dimension-selector">
      <a-card size="small" title="比选维度">
        <a-checkbox-group v-model:value="selectedDimensions" @change="handleDimensionChange">
          <a-row>
            <a-col :span="8">
              <a-checkbox value="grade">成绩排名</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox value="ability">能力评估</a-checkbox>
            </a-col>
            <a-col :span="8">
              <a-checkbox value="project">项目参与度</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-card>
    </div>

    <!-- 比选结果展示 -->
    <div class="comparison-results">
      <a-row :gutter="16">
        <!-- 候选对象列表 -->
        <a-col :span="16">
          <a-card title="候选对象列表" size="small">
            <template #extra>
              <a-space>
                <span>排序方式：</span>
                <a-select v-model:value="sortBy" style="width: 120px" @change="handleSortChange">
                  <a-select-option value="comprehensive">综合得分</a-select-option>
                  <a-select-option value="grade">成绩排名</a-select-option>
                  <a-select-option value="ability">能力评估</a-select-option>
                  <a-select-option value="project">项目参与度</a-select-option>
                </a-select>
              </a-space>
            </template>
            
            <div class="candidate-list">
              <div 
                v-for="candidate in sortedCandidates" 
                :key="candidate.id"
                class="candidate-item"
                :class="{ 'excellent': candidate.isExcellent }"
                @click="toggleExcellent(candidate)"
              >
                <div class="candidate-info">
                  <div class="candidate-header">
                    <h4>{{ candidate.name }}</h4>
                    <div class="candidate-actions">
                      <a-tag v-if="candidate.isExcellent" color="gold">
                        <star-filled /> 优异对象
                      </a-tag>
                      <a-button type="link" size="small" @click.stop="viewDetail(candidate)">
                        查看详情
                      </a-button>
                    </div>
                  </div>
                  
                  <div class="candidate-scores">
                    <div class="score-item">
                      <span class="label">综合得分：</span>
                      <span class="value">{{ candidate.comprehensiveScore }}分</span>
                    </div>
                    <div class="score-item">
                      <span class="label">成绩排名：</span>
                      <span class="value">第{{ candidate.gradeRank }}名</span>
                    </div>
                    <div class="score-item">
                      <span class="label">能力评估：</span>
                      <span class="value">{{ candidate.abilityScore }}分</span>
                    </div>
                    <div class="score-item">
                      <span class="label">项目参与度：</span>
                      <span class="value">{{ candidate.projectScore }}分</span>
                    </div>
                  </div>
                  
                  <div class="candidate-meta">
                    <span>{{ candidate.position }} | {{ candidate.department }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 优异对象汇总 -->
        <a-col :span="8">
          <a-card title="优异对象汇总" size="small">
            <template #extra>
              <a-badge :count="excellentCandidates.length" :number-style="{ backgroundColor: '#52c41a' }">
                <span>已选择</span>
              </a-badge>
            </template>
            
            <div class="excellent-summary">
              <div v-if="excellentCandidates.length === 0" class="empty-state">
                <a-empty description="暂无优异对象" />
              </div>
              
              <div v-else class="excellent-list">
                <div 
                  v-for="candidate in excellentCandidates" 
                  :key="candidate.id"
                  class="excellent-item"
                >
                  <div class="item-header">
                    <span class="name">{{ candidate.name }}</span>
                    <a-button 
                      type="text" 
                      size="small" 
                      danger
                      @click="removeExcellent(candidate)"
                    >
                      <close-outlined />
                    </a-button>
                  </div>
                  <div class="item-score">
                    综合得分：{{ candidate.comprehensiveScore }}分
                  </div>
                </div>
              </div>
              
              <div v-if="excellentCandidates.length > 0" class="summary-actions">
                <a-button block @click="clearExcellent">清空选择</a-button>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 比选结果说明 -->
    <div class="comparison-notes">
      <a-card title="比选结果说明" size="small">
        <a-form layout="vertical">
          <a-form-item label="比选说明">
            <a-textarea 
              v-model:value="comparisonNotes"
              placeholder="请输入比选结果说明，包括比选标准、评选过程、结果分析等"
              :rows="4"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="saveNotes">保存说明</a-button>
              <a-button @click="clearNotes">清空</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 候选对象详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="候选对象详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentCandidate" class="detail-content">
        <a-descriptions :column="2" bordered>
          <!-- <a-descriptions-item label="姓名">{{ currentCandidate.name }}</a-descriptions-item>
          <a-descriptions-item label="职务">{{ currentCandidate.position }}</a-descriptions-item> -->
          <a-descriptions-item label="部门">{{ currentCandidate.department }}</a-descriptions-item>
          <a-descriptions-item label="综合得分">{{ currentCandidate.comprehensiveScore }}分</a-descriptions-item>
          <a-descriptions-item label="成绩排名">第{{ currentCandidate.gradeRank }}名</a-descriptions-item>
          <a-descriptions-item label="能力评估">{{ currentCandidate.abilityScore }}分</a-descriptions-item>
          <a-descriptions-item label="项目参与度">{{ currentCandidate.projectScore }}分</a-descriptions-item>
          <a-descriptions-item label="是否优异对象">
            <a-tag v-if="currentCandidate.isExcellent" color="gold">是</a-tag>
            <a-tag v-else color="default">否</a-tag>
          </a-descriptions-item>
        </a-descriptions>
        
        <div class="detail-charts">
          <h4>能力雷达图</h4>
          <div class="chart-placeholder">
            <a-empty description="图表展示区域" />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 生成推荐名单弹窗 -->
    <a-modal
      v-model:open="generateVisible"
      title="生成推荐名单"
      width="600px"
      @ok="confirmGenerate"
      @cancel="cancelGenerate"
    >
      <div class="generate-content">
        <a-alert
          message="确认生成推荐名单"
          :description="`将为 ${excellentCandidates.length} 个优异对象生成推荐名单，生成后可进行推送操作。`"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        />
        
        <a-form layout="vertical">
          <a-form-item label="名单标题">
            <a-input 
              v-model:value="listTitle"
              placeholder="请输入推荐名单标题"
            />
          </a-form-item>
          <a-form-item label="生成说明">
            <a-textarea 
              v-model:value="generateNotes"
              placeholder="请输入名单生成说明"
              :rows="3"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  FileTextOutlined,
  StarFilled,
  CloseOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface Candidate {
  id: number
  name: string
  position: string
  department: string
  comprehensiveScore: number
  gradeRank: number
  abilityScore: number
  projectScore: number
  isExcellent: boolean
}

// 响应式数据
const detailVisible = ref(false)
const generateVisible = ref(false)
const currentCandidate = ref<Candidate | null>(null)
const candidateList = ref<Candidate[]>([])
const selectedDimensions = ref(['grade', 'ability', 'project'])
const sortBy = ref('comprehensive')
const comparisonNotes = ref('')
const listTitle = ref('')
const generateNotes = ref('')

// 计算属性
const excellentCandidates = computed(() => {
  return candidateList.value.filter(candidate => candidate.isExcellent)
})

const sortedCandidates = computed(() => {
  const list = [...candidateList.value]
  
  switch (sortBy.value) {
    case 'grade':
      return list.sort((a, b) => a.gradeRank - b.gradeRank)
    case 'ability':
      return list.sort((a, b) => b.abilityScore - a.abilityScore)
    case 'project':
      return list.sort((a, b) => b.projectScore - a.projectScore)
    default:
      return list.sort((a, b) => b.comprehensiveScore - a.comprehensiveScore)
  }
})

// 初始化模拟数据
const initMockData = () => {
  candidateList.value = [
    {
      id: 1,
      name: '市委组织部',
      position: '党支部书记',
      department: '组织部',
      comprehensiveScore: 95,
      gradeRank: 1,
      abilityScore: 92,
      projectScore: 88,
      isExcellent: true
    },
    {
      id: 2,
      name: '市委宣传部',
      position: '副主任',
      department: '宣传部',
      comprehensiveScore: 88,
      gradeRank: 3,
      abilityScore: 85,
      projectScore: 90,
      isExcellent: false
    },
    {
      id: 3,
      name: '市委研究室',
      position: '科长',
      department: '办公室',
      comprehensiveScore: 92,
      gradeRank: 2,
      abilityScore: 89,
      projectScore: 86,
      isExcellent: true
    },
    {
      id: 4,
      name: '市人大',
      position: '主任',
      department: '人事部',
      comprehensiveScore: 85,
      gradeRank: 4,
      abilityScore: 82,
      projectScore: 84,
      isExcellent: false
    }
  ]
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const handleDimensionChange = (values: string[]) => {
  console.log('选择的比选维度:', values)
  // 这里可以根据选择的维度重新计算排序
}

const handleSortChange = (value: string) => {
  console.log('排序方式变更:', value)
}

const toggleExcellent = (candidate: Candidate) => {
  candidate.isExcellent = !candidate.isExcellent
  message.success(
    candidate.isExcellent 
      ? `已标注 ${candidate.name} 为优异对象` 
      : `已取消 ${candidate.name} 的优异对象标注`
  )
}

const removeExcellent = (candidate: Candidate) => {
  candidate.isExcellent = false
  message.success(`已移除 ${candidate.name}`)
}

const clearExcellent = () => {
  candidateList.value.forEach(candidate => {
    candidate.isExcellent = false
  })
  message.success('已清空所有优异对象选择')
}

const viewDetail = (candidate: Candidate) => {
  currentCandidate.value = candidate
  detailVisible.value = true
}

const saveNotes = () => {
  if (!comparisonNotes.value.trim()) {
    message.warning('请输入比选结果说明')
    return
  }
  message.success('比选结果说明已保存')
}

const clearNotes = () => {
  comparisonNotes.value = ''
}

const generateRecommendList = () => {
  if (excellentCandidates.value.length === 0) {
    message.warning('请先选择优异对象')
    return
  }
  
  listTitle.value = `优异培育对象推荐名单（${new Date().toLocaleDateString()}）`
  generateNotes.value = `经过多维度比选，共选出 ${excellentCandidates.value.length} 名优异培育对象。`
  generateVisible.value = true
}

const confirmGenerate = () => {
  if (!listTitle.value.trim()) {
    message.warning('请输入名单标题')
    return
  }
  
  message.success('推荐名单生成成功')
  generateVisible.value = false
  
  // 这里应该调用API保存推荐名单
}

const cancelGenerate = () => {
  generateVisible.value = false
  listTitle.value = ''
  generateNotes.value = ''
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.excellent-comparison {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.dimension-selector {
  margin-bottom: 16px;
}

.comparison-results {
  margin-bottom: 16px;
}

.candidate-list {
  max-height: 600px;
  overflow-y: auto;
}

.candidate-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.candidate-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.candidate-item.excellent {
  border-color: #faad14;
  background: #fffbe6;
}

.candidate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.candidate-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.candidate-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.candidate-scores {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 8px;
}

.score-item {
  display: flex;
  justify-content: space-between;
}

.score-item .label {
  color: #666;
  font-size: 14px;
}

.score-item .value {
  color: #262626;
  font-weight: 500;
}

.candidate-meta {
  color: #8c8c8c;
  font-size: 12px;
}

.excellent-summary {
  min-height: 400px;
}

.excellent-list {
  margin-bottom: 16px;
}

.excellent-item {
  border: 1px solid #faad14;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  background: #fffbe6;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.item-header .name {
  font-weight: 500;
  color: #262626;
}

.item-score {
  font-size: 12px;
  color: #666;
}

.comparison-notes {
  margin-bottom: 16px;
}

.detail-content .detail-charts {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.detail-charts h4 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
}

.generate-content {
  padding: 8px 0;
}
</style>
