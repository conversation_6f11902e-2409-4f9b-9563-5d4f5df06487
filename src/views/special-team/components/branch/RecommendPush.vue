<template>
  <div class="recommend-push">
    <!-- 页面头部 -->
    <div class="section-header">
      <div class="header-left">
        <h3>推荐推送</h3>
        <p>选择优异对象推送至总专班，记录推送时间/人员并归档推送信息</p>
      </div>
      <div class="header-right">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="createPush" :disabled="!hasRecommendList">
            <template #icon><send-outlined /></template>
            新建推送
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 推荐名单展示 -->
    <div class="recommend-list-section">
      <a-card title="待推送推荐名单" size="small">
        <div v-if="!hasRecommendList" class="empty-state">
          <a-empty description="暂无推荐名单">
            <template #image>
              <file-text-outlined style="font-size: 48px; color: #bfbfbf;" />
            </template>
            <a-button type="primary" @click="goToComparison">
              前往比选生成推荐名单
            </a-button>
          </a-empty>
        </div>
        
        <div v-else class="recommend-list-content">
          <div class="list-header">
            <h4>{{ currentRecommendList.title }}</h4>
            <div class="list-meta">
              <a-space>
                <span>生成时间：{{ formatDate(currentRecommendList.createTime) }}</span>
                <span>对象数量：{{ currentRecommendList.candidates.length }}人</span>
                <a-tag :color="getStatusColor(currentRecommendList.status)">
                  {{ getStatusText(currentRecommendList.status) }}
                </a-tag>
              </a-space>
            </div>
          </div>
          
          <div class="candidates-grid">
            <a-row :gutter="[12, 12]">
              <a-col :span="6" v-for="candidate in currentRecommendList.candidates" :key="candidate.id">
                <div class="candidate-card">
                  <div class="candidate-name">{{ candidate.name }}</div>
                  <div class="candidate-info">
                    <div>{{ candidate.position }}</div>
                    <div class="score">{{ candidate.comprehensiveScore }}分</div>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
          
          <div class="list-description">
            <h5>推荐说明：</h5>
            <p>{{ currentRecommendList.description }}</p>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 推送历史 -->
    <div class="push-history-section">
      <a-card title="推送历史" size="small">
        <template #extra>
          <a-space>
            <a-select v-model:value="historyFilter" style="width: 120px" @change="filterHistory">
              <a-select-option value="all">全部</a-select-option>
              <a-select-option value="pending">待确认</a-select-option>
              <a-select-option value="confirmed">已确认</a-select-option>
              <a-select-option value="rejected">已拒绝</a-select-option>
            </a-select>
          </a-space>
        </template>
        
        <a-table
          :columns="historyColumns"
          :data-source="filteredPushHistory"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewPushDetail(record)">
                  查看
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="cancelPush(record)"
                  :disabled="record.status !== 'pending'"
                  danger
                >
                  撤回
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 新建推送弹窗 -->
    <a-modal
      v-model:open="pushVisible"
      title="新建推送"
      width="700px"
      @ok="submitPush"
      @cancel="cancelPushModal"
    >
      <div class="push-form">
        <a-form ref="pushFormRef" :model="pushForm" layout="vertical">
          <a-form-item 
            label="推送标题" 
            name="title"
            :rules="[{ required: true, message: '请输入推送标题' }]"
          >
            <a-input v-model:value="pushForm.title" placeholder="请输入推送标题" />
          </a-form-item>
          
          <a-form-item 
            label="推送说明" 
            name="description"
            :rules="[{ required: true, message: '请输入推送说明' }]"
          >
            <a-textarea 
              v-model:value="pushForm.description"
              placeholder="请输入推送说明，包括推荐理由、评选过程等"
              :rows="4"
            />
          </a-form-item>
          
          <a-form-item label="推送对象">
            <div class="push-candidates">
              <a-checkbox-group v-model:value="pushForm.selectedCandidates" style="width: 100%">
                <a-row>
                  <a-col :span="12" v-for="candidate in currentRecommendList?.candidates" :key="candidate.id">
                    <a-checkbox :value="candidate.id" style="margin-bottom: 8px">
                      {{ candidate.name }} ({{ candidate.comprehensiveScore }}分)
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-checkbox-group>
            </div>
          </a-form-item>
          
          <a-form-item label="优先级">
            <a-radio-group v-model:value="pushForm.priority">
              <a-radio value="high">高</a-radio>
              <a-radio value="medium">中</a-radio>
              <a-radio value="low">低</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="期望反馈时间">
            <a-date-picker 
              v-model:value="pushForm.expectedFeedbackTime"
              style="width: 100%"
              placeholder="请选择期望反馈时间"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 推送详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="推送详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentPushRecord" class="push-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="推送标题">{{ currentPushRecord.title }}</a-descriptions-item>
          <a-descriptions-item label="推送状态">
            <a-tag :color="getStatusColor(currentPushRecord.status)">
              {{ getStatusText(currentPushRecord.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推送时间">{{ formatDate(currentPushRecord.pushTime) }}</a-descriptions-item>
          <a-descriptions-item label="推送人员">{{ currentPushRecord.pushUser }}</a-descriptions-item>
          <a-descriptions-item label="对象数量">{{ currentPushRecord.candidateCount }}人</a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(currentPushRecord.priority)">
              {{ getPriorityText(currentPushRecord.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="推送说明" :span="2">{{ currentPushRecord.description }}</a-descriptions-item>
        </a-descriptions>
        
        <div v-if="currentPushRecord.feedback" class="feedback-section">
          <h4>反馈信息</h4>
          <div class="feedback-content">
            <p><strong>反馈时间：</strong>{{ formatDate(currentPushRecord.feedbackTime) }}</p>
            <p><strong>反馈内容：</strong></p>
            <div class="feedback-text">{{ currentPushRecord.feedback }}</div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  ReloadOutlined, 
  SendOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 接口定义
interface Candidate {
  id: number
  name: string
  position: string
  comprehensiveScore: number
}

interface RecommendList {
  id: number
  title: string
  description: string
  candidates: Candidate[]
  status: 'draft' | 'ready' | 'pushed'
  createTime: string
}

interface PushRecord {
  id: number
  title: string
  description: string
  candidateCount: number
  status: 'pending' | 'confirmed' | 'rejected'
  priority: 'high' | 'medium' | 'low'
  pushTime: string
  pushUser: string
  expectedFeedbackTime?: string
  feedback?: string
  feedbackTime?: string
}

// 响应式数据
const pushVisible = ref(false)
const detailVisible = ref(false)
const currentRecommendList = ref<RecommendList | null>(null)
const currentPushRecord = ref<PushRecord | null>(null)
const pushHistory = ref<PushRecord[]>([])
const historyFilter = ref('all')
const pushFormRef = ref()

// 推送表单
const pushForm = reactive({
  title: '',
  description: '',
  selectedCandidates: [] as number[],
  priority: 'medium' as 'high' | 'medium' | 'low',
  expectedFeedbackTime: null as any
})

// 计算属性
const hasRecommendList = computed(() => {
  return currentRecommendList.value && currentRecommendList.value.status === 'ready'
})

const filteredPushHistory = computed(() => {
  if (historyFilter.value === 'all') {
    return pushHistory.value
  }
  return pushHistory.value.filter(record => record.status === historyFilter.value)
})

// 表格列配置
const historyColumns = [
  {
    title: '推送标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '对象数量',
    dataIndex: 'candidateCount',
    key: 'candidateCount',
    width: 100
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
    width: 150,
    customRender: ({ text }: any) => formatDate(text)
  },
  {
    title: '推送人员',
    dataIndex: 'pushUser',
    key: 'pushUser',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right' as const
  }
]

// 初始化模拟数据
const initMockData = () => {
  // 模拟推荐名单
  currentRecommendList.value = {
    id: 1,
    title: '优异培育对象推荐名单（2025-06-20）',
    description: '经过多维度比选，共选出2名优异培育对象，综合得分均在90分以上，具备突出的能力和贡献。',
    candidates: [
      { id: 1, name: '周海军', position: '党支部书记', comprehensiveScore: 95 },
      { id: 3, name: '杜佳佳', position: '科长', comprehensiveScore: 92 }
    ],
    status: 'ready',
    createTime: '2025-06-20T10:30:00Z'
  }

  // 模拟推送历史
  pushHistory.value = [
    {
      id: 1,
      title: '第一批优异对象推送',
      description: '推送第一批优异培育对象至总专班进行综合评审',
      candidateCount: 3,
      status: 'confirmed',
      priority: 'high',
      pushTime: '2025-06-15T14:20:00Z',
      pushUser: '杜佳佳',
      expectedFeedbackTime: '2025-06-25T00:00:00Z',
      feedback: '已收到推荐名单，将在一周内完成综合评审。',
      feedbackTime: '2025-06-16T09:30:00Z'
    },
    {
      id: 2,
      title: '补充推荐对象推送',
      description: '补充推送1名优异培育对象',
      candidateCount: 1,
      status: 'pending',
      priority: 'medium',
      pushTime: '2025-06-18T16:45:00Z',
      pushUser: '周海军',
      expectedFeedbackTime: '2025-06-28T00:00:00Z'
    }
  ]
}

// 工具函数
const formatDate = (dateStr: string) => {
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'default',
    ready: 'blue',
    pushed: 'green',
    pending: 'orange',
    confirmed: 'green',
    rejected: 'red'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    ready: '待推送',
    pushed: '已推送',
    pending: '待确认',
    confirmed: '已确认',
    rejected: '已拒绝'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[priority as keyof typeof colorMap] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

// 事件处理函数
const refreshData = () => {
  message.success('数据已刷新')
  initMockData()
}

const goToComparison = () => {
  message.info('跳转到优异对象比选页面')
  // 这里应该触发父组件切换到比选页面
}

const createPush = () => {
  if (!currentRecommendList.value) {
    message.warning('暂无推荐名单')
    return
  }
  
  // 初始化表单
  pushForm.title = `${currentRecommendList.value.title} - 推送`
  pushForm.description = currentRecommendList.value.description
  pushForm.selectedCandidates = currentRecommendList.value.candidates.map(c => c.id)
  pushForm.priority = 'medium'
  pushForm.expectedFeedbackTime = null
  
  pushVisible.value = true
}

const submitPush = async () => {
  try {
    await pushFormRef.value.validateFields()
    
    if (pushForm.selectedCandidates.length === 0) {
      message.warning('请选择推送对象')
      return
    }
    
    message.success('推送提交成功')
    pushVisible.value = false
    refreshData()
  } catch (error) {
    console.error('推送表单验证失败:', error)
  }
}

const cancelPushModal = () => {
  pushVisible.value = false
  // 重置表单
  pushForm.title = ''
  pushForm.description = ''
  pushForm.selectedCandidates = []
  pushForm.priority = 'medium'
  pushForm.expectedFeedbackTime = null
}

const filterHistory = () => {
  console.log('筛选推送历史:', historyFilter.value)
}

const viewPushDetail = (record: PushRecord) => {
  currentPushRecord.value = record
  detailVisible.value = true
}

const cancelPush = (record: PushRecord) => {
  message.success(`已撤回推送：${record.title}`)
  // 这里应该调用撤回API
}

// 页面初始化
onMounted(() => {
  initMockData()
})
</script>

<style scoped>
.recommend-push {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left h3 {
  margin: 0 0 4px 0;
  color: #262626;
  font-size: 18px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.recommend-list-section {
  margin-bottom: 24px;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

.recommend-list-content {
  padding: 8px 0;
}

.list-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.list-header h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.list-meta {
  color: #666;
  font-size: 14px;
}

.candidates-grid {
  margin-bottom: 16px;
}

.candidate-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  background: #fafafa;
}

.candidate-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.candidate-info {
  font-size: 12px;
  color: #666;
}

.candidate-info .score {
  color: #1890ff;
  font-weight: 500;
}

.list-description h5 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.list-description p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

.push-history-section {
  margin-bottom: 16px;
}

.push-form {
  padding: 8px 0;
}

.push-candidates {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.push-detail {
  padding: 8px 0;
}

.feedback-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.feedback-section h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.feedback-content p {
  margin: 8px 0;
  color: #666;
}

.feedback-text {
  background: #f6f6f6;
  padding: 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
  line-height: 1.6;
  color: #262626;
}
</style>
