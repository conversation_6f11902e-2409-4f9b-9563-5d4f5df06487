<template>
  <div class="system-diagnostic">
    <a-card title="系统诊断">
      <template #extra>
        <a-button type="primary" @click="showDiagnosticModal">
          <bug-outlined />
          执行诊断
        </a-button>
      </template>

      <!-- 诊断结果概览 -->
      <a-row :gutter="24" style="margin-bottom: 24px;" v-if="lastDiagnostic">
        <a-col :span="6">
          <a-statistic 
            title="总体评分" 
            :value="lastDiagnostic.overallScore" 
            suffix="/100"
            :value-style="{ color: getScoreColor(lastDiagnostic.overallScore) }"
          />
        </a-col>
        <a-col :span="6">
          <a-statistic title="检查项目" :value="lastDiagnostic.totalChecks" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="通过项目" :value="lastDiagnostic.passedChecks" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="问题项目" :value="lastDiagnostic.failedChecks + lastDiagnostic.warningChecks" />
        </a-col>
      </a-row>

      <!-- 诊断详情 -->
      <div v-if="lastDiagnostic">
        <a-collapse v-model:activeKey="activeKeys" style="margin-bottom: 24px;">
          <a-collapse-panel 
            v-for="result in lastDiagnostic.results" 
            :key="result.checkType"
            :header="getCheckTypeLabel(result.checkType)"
          >
            <template #extra>
              <a-tag :color="getStatusColor(result.status)">
                {{ getStatusText(result.status) }}
              </a-tag>
            </template>
            
            <a-list :data-source="result.details" item-layout="horizontal">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>{{ item.item }}</template>
                    <template #description>
                      <div>
                        <div><strong>结果：</strong>{{ item.result }}</div>
                        <div v-if="item.recommendation">
                          <strong>建议：</strong>{{ item.recommendation }}
                        </div>
                      </div>
                    </template>
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
          </a-collapse-panel>
        </a-collapse>
      </div>

      <!-- 诊断历史 -->
      <a-table 
        :columns="historyColumns" 
        :data-source="diagnosticHistory"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'overallScore'">
            <a-tag :color="getScoreColor(record.overallScore)">
              {{ record.overallScore }}/100
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewReport(record)">查看报告</a-button>
              <a-button type="link" @click="downloadReport(record)">下载</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 执行诊断模态框 -->
    <a-modal
      v-model:open="diagnosticModalVisible"
      title="执行系统诊断"
      @ok="handleDiagnostic"
      @cancel="handleDiagnosticCancel"
    >
      <a-form :model="diagnosticForm" layout="vertical">
        <a-form-item label="诊断模块" required>
          <a-checkbox-group v-model:value="diagnosticForm.modules">
            <a-checkbox value="indicator-rules">指标规则管理</a-checkbox>
            <a-checkbox value="health-check">数据体检</a-checkbox>
            <a-checkbox value="audit-confirmation">审核确认</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="检查类型" required>
          <a-checkbox-group v-model:value="diagnosticForm.checkTypes">
            <a-checkbox value="api_connectivity">API连通性</a-checkbox>
            <a-checkbox value="data_consistency">数据一致性</a-checkbox>
            <a-checkbox value="performance">性能检查</a-checkbox>
            <a-checkbox value="security">安全检查</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="诊断深度" required>
          <a-radio-group v-model:value="diagnosticForm.depth">
            <a-radio value="basic">基础检查</a-radio>
            <a-radio value="comprehensive">全面检查</a-radio>
            <a-radio value="deep">深度检查</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { BugOutlined } from '@ant-design/icons-vue'
import { diagnoseModuleIntegration } from '@/api/module-integration'

// 响应式数据
const loading = ref(false)
const diagnosticModalVisible = ref(false)
const activeKeys = ref(['api_connectivity'])

const lastDiagnostic = ref({
  diagnosticId: 'DIAG_1737264123456',
  overallScore: 85,
  totalChecks: 8,
  passedChecks: 6,
  warningChecks: 2,
  failedChecks: 0,
  results: [
    {
      checkType: 'api_connectivity',
      status: 'passed',
      details: [
        { item: '指标规则API连通性', result: '正常' },
        { item: '数据体检API连通性', result: '正常' },
        { item: '审核确认API连通性', result: '正常' }
      ]
    },
    {
      checkType: 'data_consistency',
      status: 'warning',
      details: [
        {
          item: '指标权重一致性',
          result: '发现3处不一致',
          recommendation: '建议执行数据同步'
        }
      ]
    },
    {
      checkType: 'performance',
      status: 'passed',
      details: [
        { item: '响应时间检查', result: '平均200ms，符合要求' },
        { item: '并发处理能力', result: '支持100并发，性能良好' }
      ]
    }
  ]
})

const diagnosticHistory = ref([
  {
    id: 1,
    diagnosticTime: '2025-01-18 14:30:00',
    overallScore: 85,
    checkTypes: ['API连通性', '数据一致性', '性能检查'],
    duration: '3分45秒',
    reportUrl: '/reports/diagnostic_1737264123456.pdf'
  },
  {
    id: 2,
    diagnosticTime: '2025-01-18 10:15:00',
    overallScore: 92,
    checkTypes: ['API连通性', '安全检查'],
    duration: '1分20秒',
    reportUrl: '/reports/diagnostic_1737260123456.pdf'
  }
])

const diagnosticForm = ref({
  modules: ['indicator-rules', 'health-check', 'audit-confirmation'],
  checkTypes: ['api_connectivity', 'data_consistency', 'performance'],
  depth: 'comprehensive'
})

// 表格列配置
const historyColumns = [
  {
    title: '诊断时间',
    dataIndex: 'diagnosticTime',
    key: 'diagnosticTime',
  },
  {
    title: '总体评分',
    key: 'overallScore',
    width: 120,
  },
  {
    title: '检查类型',
    key: 'checkTypes',
    render: (record: any) => record.checkTypes.join(', '),
  },
  {
    title: '耗时',
    dataIndex: 'duration',
    key: 'duration',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
]

// 方法
const showDiagnosticModal = () => {
  diagnosticModalVisible.value = true
}

const handleDiagnostic = async () => {
  try {
    if (diagnosticForm.value.modules.length === 0) {
      message.warning('请选择诊断模块')
      return
    }
    
    if (diagnosticForm.value.checkTypes.length === 0) {
      message.warning('请选择检查类型')
      return
    }
    
    const result = await diagnoseModuleIntegration(diagnosticForm.value)
    message.success(`诊断已启动，诊断ID: ${result.diagnosticId}`)
    diagnosticModalVisible.value = false
    loadDiagnosticHistory()
  } catch (error) {
    console.error('诊断失败:', error)
    message.error('诊断失败')
  }
}

const handleDiagnosticCancel = () => {
  diagnosticModalVisible.value = false
}

const loadDiagnosticHistory = async () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

const viewReport = (record: any) => {
  message.info('查看诊断报告功能待完善')
}

const downloadReport = (record: any) => {
  // 模拟下载报告
  const link = document.createElement('a')
  link.href = record.reportUrl
  link.download = `diagnostic_report_${record.id}.pdf`
  link.click()
  message.success('报告下载已启动')
}

const getCheckTypeLabel = (type: string) => {
  const map: Record<string, string> = {
    'api_connectivity': 'API连通性检查',
    'data_consistency': '数据一致性检查',
    'performance': '性能检查',
    'security': '安全检查'
  }
  return map[type] || type
}

const getStatusColor = (status: string) => {
  const map: Record<string, string> = {
    'passed': 'green',
    'warning': 'orange',
    'failed': 'red'
  }
  return map[status] || 'default'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'passed': '通过',
    'warning': '警告',
    'failed': '失败'
  }
  return map[status] || '未知'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

// 生命周期
onMounted(() => {
  loadDiagnosticHistory()
})
</script>

<style scoped>
.system-diagnostic {
  padding: 24px;
}
</style>