<template>
  <div class="data-sync">
    <a-card title="数据同步管理">
      <template #extra>
        <a-button type="primary" @click="showSyncModal">
          <sync-outlined />
          执行同步
        </a-button>
      </template>

      <!-- 同步状态概览 -->
      <a-row :gutter="24" style="margin-bottom: 24px;">
        <a-col :span="8">
          <a-statistic title="数据一致性" :value="95" suffix="%" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="上次同步" value="2小时前" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="同步频次" :value="12" suffix="次/小时" />
        </a-col>
      </a-row>

      <!-- 同步历史 -->
      <a-table 
        :columns="columns" 
        :data-source="syncHistory"
        :loading="loading"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'modules'">
            <a-tag v-for="module in record.modules" :key="module" style="margin: 2px;">
              {{ module }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 执行同步模态框 -->
    <a-modal
      v-model:open="syncModalVisible"
      title="执行数据同步"
      @ok="handleSync"
      @cancel="handleSyncCancel"
    >
      <a-form :model="syncForm" layout="vertical">
        <a-form-item label="同步范围" required>
          <a-radio-group v-model:value="syncForm.syncScope">
            <a-radio value="all">全部数据</a-radio>
            <a-radio value="indicators">指标数据</a-radio>
            <a-radio value="health_rules">体检规则</a-radio>
            <a-radio value="audit_results">审核结果</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="目标模块" required>
          <a-checkbox-group v-model:value="syncForm.targetModules">
            <a-checkbox value="indicator-rules">指标规则管理</a-checkbox>
            <a-checkbox value="health-check">数据体检</a-checkbox>
            <a-checkbox value="audit-confirmation">审核确认</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="syncForm.forceFull">强制全量同步</a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="syncForm.validateConsistency">验证数据一致性</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SyncOutlined } from '@ant-design/icons-vue'
import { synchronizeModuleData } from '@/api/module-integration'

// 响应式数据
const loading = ref(false)
const syncModalVisible = ref(false)
const syncHistory = ref([
  {
    id: 1,
    syncTime: '2025-01-18 14:30:00',
    syncScope: '全部数据',
    modules: ['指标规则管理', '数据体检', '审核确认'],
    status: 'success',
    syncedRecords: 156,
    duration: '2分30秒'
  },
  {
    id: 2,
    syncTime: '2025-01-18 12:30:00',
    syncScope: '指标数据',
    modules: ['指标规则管理', '数据体检'],
    status: 'partial',
    syncedRecords: 45,
    duration: '1分15秒'
  }
])

const syncForm = ref({
  syncScope: 'all',
  targetModules: ['indicator-rules', 'health-check', 'audit-confirmation'],
  forceFull: false,
  validateConsistency: true
})

// 表格列配置
const columns = [
  {
    title: '同步时间',
    dataIndex: 'syncTime',
    key: 'syncTime',
  },
  {
    title: '同步范围',
    dataIndex: 'syncScope',
    key: 'syncScope',
  },
  {
    title: '涉及模块',
    key: 'modules',
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
  },
  {
    title: '同步记录数',
    dataIndex: 'syncedRecords',
    key: 'syncedRecords',
    width: 120,
  },
  {
    title: '耗时',
    dataIndex: 'duration',
    key: 'duration',
    width: 100,
  },
]

// 方法
const showSyncModal = () => {
  syncModalVisible.value = true
}

const handleSync = async () => {
  try {
    const result = await synchronizeModuleData(syncForm.value)
    message.success('数据同步已启动')
    syncModalVisible.value = false
    loadSyncHistory()
  } catch (error) {
    console.error('同步失败:', error)
    message.error('同步失败')
  }
}

const handleSyncCancel = () => {
  syncModalVisible.value = false
}

const loadSyncHistory = async () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

const getStatusColor = (status: string) => {
  const map: Record<string, string> = {
    'success': 'green',
    'partial': 'orange',
    'failed': 'red'
  }
  return map[status] || 'default'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'success': '成功',
    'partial': '部分成功',
    'failed': '失败'
  }
  return map[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadSyncHistory()
})
</script>

<style scoped>
.data-sync {
  padding: 24px;
}
</style>