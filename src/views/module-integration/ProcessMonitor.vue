<template>
  <div class="process-monitor">
    <a-card title="流程监控">
      <template #extra>
        <a-button type="primary" @click="showExecuteModal">
          <play-circle-outlined />
          执行新流程
        </a-button>
      </template>

      <!-- 流程列表 -->
      <a-table 
        :columns="columns" 
        :data-source="processes"
        :loading="loading"
        row-key="processId"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'progress'">
            <a-progress :percent="record.progress" size="small" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewDetail(record)">详情</a-button>
              <a-button type="link" danger @click="stopProcess(record)" v-if="record.status === 'running'">
                停止
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 执行流程模态框 -->
    <a-modal
      v-model:open="executeModalVisible"
      title="执行完整评分流程"
      :width="600"
      @ok="handleExecute"
      @cancel="handleExecuteCancel"
    >
      <a-form :model="executeForm" layout="vertical">
        <a-form-item label="项目ID" required>
          <a-input-number v-model:value="executeForm.projectId" style="width: 100%" />
        </a-form-item>
        
        <a-form-item label="培育对象IDs" required>
          <a-select 
            v-model:value="executeForm.objectIds" 
            mode="multiple" 
            placeholder="选择培育对象"
            style="width: 100%"
          >
            <a-select-option :value="1">市委办公室党支部</a-select-option>
            <a-select-option :value="2">教育局机关党支部</a-select-option>
            <a-select-option :value="3">卫健委党支部</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="executeForm.useLatestRules">使用最新规则</a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="executeForm.performHealthCheck">执行数据体检</a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="executeForm.autoAudit">自动预审核</a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="executeForm.notifyOnComplete">完成后通知</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlayCircleOutlined } from '@ant-design/icons-vue'
import { executeFullScoringProcess, getProcessStatus } from '@/api/module-integration'

// 响应式数据
const loading = ref(false)
const executeModalVisible = ref(false)
const processes = ref([
  {
    processId: 'PROC_1737264123456',
    processName: '完整评分流程',
    status: 'running',
    progress: 65,
    startTime: '2025-01-18 14:00:00',
    currentStep: 3,
    totalSteps: 5
  }
])

const executeForm = ref({
  projectId: 1,
  objectIds: [],
  useLatestRules: true,
  performHealthCheck: true,
  autoAudit: false,
  notifyOnComplete: true
})

// 表格列配置
const columns = [
  {
    title: '流程ID',
    dataIndex: 'processId',
    key: 'processId',
  },
  {
    title: '流程名称',
    dataIndex: 'processName',
    key: 'processName',
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
  },
  {
    title: '进度',
    key: 'progress',
    width: 150,
  },
  {
    title: '当前步骤',
    key: 'currentStep',
    render: (record: any) => `${record.currentStep}/${record.totalSteps}`,
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
]

// 方法
const showExecuteModal = () => {
  executeModalVisible.value = true
}

const handleExecute = async () => {
  try {
    if (executeForm.value.objectIds.length === 0) {
      message.warning('请选择培育对象')
      return
    }
    
    const result = await executeFullScoringProcess(executeForm.value)
    message.success(`流程已启动，流程ID: ${result.processId}`)
    executeModalVisible.value = false
    loadProcesses()
  } catch (error) {
    console.error('执行流程失败:', error)
    message.error('执行流程失败')
  }
}

const handleExecuteCancel = () => {
  executeModalVisible.value = false
}

const loadProcesses = async () => {
  // 模拟加载流程列表
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 500)
}

const viewDetail = (record: any) => {
  console.log('查看流程详情:', record)
  message.info('查看流程详情功能待完善')
}

const stopProcess = (record: any) => {
  message.success(`已停止流程: ${record.processName}`)
}

const getStatusColor = (status: string) => {
  const map: Record<string, string> = {
    'running': 'blue',
    'completed': 'green',
    'failed': 'red'
  }
  return map[status] || 'default'
}

const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return map[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadProcesses()
})
</script>

<style scoped>
.process-monitor {
  padding: 24px;
}
</style>