<template>
  <div class="module-integration-index">
    <!-- 系统概览 -->
    <a-row :gutter="24" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card>
          <a-statistic 
            title="模块健康度" 
            :value="systemHealth.overallScore"
            suffix="%" 
            :value-style="{ color: getHealthColor(systemHealth.overallScore) }"
          />
          <div style="margin-top: 8px;">
            <a-tag :color="systemHealth.status === 'healthy' ? 'green' : systemHealth.status === 'warning' ? 'orange' : 'red'">
              {{ getHealthStatusText(systemHealth.status) }}
            </a-tag>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic 
            title="数据一致性" 
            :value="systemHealth.dataConsistency"
            suffix="%" 
            :value-style="{ color: systemHealth.dataConsistency >= 95 ? '#3f8600' : '#faad14' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic 
            title="流程成功率" 
            :value="systemHealth.processSuccess"
            suffix="%" 
            :value-style="{ color: systemHealth.processSuccess >= 90 ? '#3f8600' : '#faad14' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic 
            title="同步频次" 
            :value="systemHealth.syncFrequency"
            suffix="/小时" 
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 模块状态 -->
    <a-card title="模块状态监控" style="margin-bottom: 24px;">
      <a-row :gutter="24">
        <a-col :span="8" v-for="module in moduleStatuses" :key="module.moduleName">
          <a-card size="small" :title="module.moduleName">
            <div class="module-status">
              <div class="status-header">
                <a-badge 
                  :status="module.status === 'online' ? 'processing' : module.status === 'degraded' ? 'warning' : 'error'"
                  :text="getModuleStatusText(module.status)"
                />
                <span class="last-check">{{ formatTime(module.lastHealthCheck) }}</span>
              </div>
              
              <div class="performance-metrics">
                <div class="metric">
                  <span>响应时间</span>
                  <a-tag :color="module.performance.responseTime < 300 ? 'green' : 'orange'">
                    {{ module.performance.responseTime }}ms
                  </a-tag>
                </div>
                <div class="metric">
                  <span>错误率</span>
                  <a-tag :color="module.performance.errorRate < 1 ? 'green' : 'red'">
                    {{ module.performance.errorRate }}%
                  </a-tag>
                </div>
                <div class="metric">
                  <span>吞吐量</span>
                  <span>{{ module.performance.throughput }}/h</span>
                </div>
              </div>

              <div v-if="module.issues.length > 0" class="issues">
                <a-alert 
                  v-for="issue in module.issues" 
                  :key="issue"
                  :message="issue" 
                  type="warning" 
                  size="small"
                  style="margin-top: 8px;"
                />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 快速操作 -->
    <a-card title="快速操作">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-button 
            type="primary" 
            block 
            size="large"
            @click="$router.push('/module-integration/process-monitor')"
          >
            <play-circle-outlined />
            执行完整流程
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button 
            block 
            size="large"
            @click="$router.push('/module-integration/data-sync')"
          >
            <sync-outlined />
            数据同步
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button 
            block 
            size="large"
            @click="$router.push('/module-integration/health-monitor')"
          >
            <monitor-outlined />
            健康监控
          </a-button>
        </a-col>
        <a-col :span="6">
          <a-button 
            block 
            size="large"
            @click="$router.push('/module-integration/diagnostic')"
          >
            <bug-outlined />
            系统诊断
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 运行流程监控 -->
    <a-card title="运行中的流程" style="margin-top: 24px;" v-if="runningProcesses.length > 0">
      <a-table 
        :columns="processColumns" 
        :data-source="runningProcesses"
        :pagination="false"
        row-key="processId"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'running' ? 'blue' : record.status === 'completed' ? 'green' : 'red'">
              {{ getProcessStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'progress'">
            <a-progress :percent="record.progress" size="small" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="viewProcessDetail(record)">详情</a-button>
              <a-button type="link" danger @click="stopProcess(record)" v-if="record.status === 'running'">
                停止
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 系统建议 -->
    <a-card title="系统建议" style="margin-top: 24px;" v-if="recommendations.length > 0">
      <a-list :data-source="recommendations" item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-button type="link" @click="handleRecommendation(item)">处理</a-button>
            </template>
            <a-list-item-meta>
              <template #title>
                <a-tag :color="getPriorityColor(item.priority)">{{ item.priority.toUpperCase() }}</a-tag>
                {{ item.description }}
              </template>
              <template #description>
                <div>
                  <div><strong>建议操作：</strong>{{ item.action }}</div>
                  <div><strong>预期影响：</strong>{{ item.expectedImpact }}</div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  PlayCircleOutlined, 
  SyncOutlined, 
  MonitorOutlined, 
  BugOutlined 
} from '@ant-design/icons-vue'
import { getModuleIntegrationHealth, getProcessStatus } from '@/api/module-integration'

// 响应式数据
const systemHealth = ref({
  overallScore: 95,
  status: 'healthy' as 'healthy' | 'warning' | 'critical',
  dataConsistency: 95,
  processSuccess: 92,
  syncFrequency: 12
})

const moduleStatuses = ref([
  {
    moduleName: '指标规则管理',
    status: 'online' as 'online' | 'degraded' | 'offline',
    lastHealthCheck: new Date().toISOString(),
    issues: [],
    performance: {
      responseTime: 150,
      errorRate: 0.5,
      throughput: 1200
    }
  },
  {
    moduleName: '数据体检',
    status: 'online' as 'online' | 'degraded' | 'offline',
    lastHealthCheck: new Date().toISOString(),
    issues: [],
    performance: {
      responseTime: 280,
      errorRate: 1.2,
      throughput: 800
    }
  },
  {
    moduleName: '审核确认',
    status: 'degraded' as 'online' | 'degraded' | 'offline',
    lastHealthCheck: new Date().toISOString(),
    issues: ['审核队列积压较多'],
    performance: {
      responseTime: 450,
      errorRate: 2.1,
      throughput: 600
    }
  }
])

const runningProcesses = ref([
  {
    processId: 'PROC_1737264123456',
    processName: '完整评分流程',
    status: 'running',
    progress: 65,
    startTime: '2025-01-18 14:00:00',
    estimatedCompletion: '2025-01-18 14:15:00'
  }
])

const recommendations = ref([
  {
    priority: 'medium' as 'low' | 'medium' | 'high' | 'critical',
    description: '审核确认模块响应时间较长',
    action: '优化审核流程，增加处理能力',
    expectedImpact: '提升用户体验，减少等待时间'
  }
])

// 表格列配置
const processColumns = [
  {
    title: '流程名称',
    dataIndex: 'processName',
    key: 'processName',
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
  },
  {
    title: '进度',
    key: 'progress',
    width: 150,
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 180,
  },
  {
    title: '预计完成',
    dataIndex: 'estimatedCompletion',
    key: 'estimatedCompletion',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
  },
]

// 定时器
let healthCheckTimer: NodeJS.Timeout | null = null

// 方法
const loadSystemHealth = async () => {
  try {
    const health = await getModuleIntegrationHealth()
    systemHealth.value = {
      overallScore: health.integrationMetrics.dataConsistency,
      status: health.overallHealth,
      dataConsistency: health.integrationMetrics.dataConsistency,
      processSuccess: health.integrationMetrics.processSuccess,
      syncFrequency: health.integrationMetrics.syncFrequency
    }
    moduleStatuses.value = health.moduleStatuses
    recommendations.value = health.recommendations
  } catch (error) {
    console.error('加载系统健康状态失败:', error)
  }
}

const getHealthColor = (score: number) => {
  if (score >= 95) return '#3f8600'
  if (score >= 85) return '#faad14'
  return '#cf1322'
}

const getHealthStatusText = (status: string) => {
  const map: Record<string, string> = {
    'healthy': '健康',
    'warning': '警告',
    'critical': '严重'
  }
  return map[status] || '未知'
}

const getModuleStatusText = (status: string) => {
  const map: Record<string, string> = {
    'online': '在线',
    'degraded': '降级',
    'offline': '离线'
  }
  return map[status] || '未知'
}

const getProcessStatusText = (status: string) => {
  const map: Record<string, string> = {
    'running': '运行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return map[status] || '未知'
}

const getPriorityColor = (priority: string) => {
  const map: Record<string, string> = {
    'low': 'blue',
    'medium': 'orange',
    'high': 'red',
    'critical': 'red'
  }
  return map[priority] || 'default'
}

const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleTimeString()
}

const viewProcessDetail = (process: any) => {
  // 跳转到流程详情页面
  console.log('查看流程详情:', process)
}

const stopProcess = (process: any) => {
  message.success(`已停止流程: ${process.processName}`)
  // 实际应该调用停止流程的API
}

const handleRecommendation = (recommendation: any) => {
  message.info(`正在处理建议: ${recommendation.description}`)
  // 实际应该根据建议类型执行相应的操作
}

// 生命周期
onMounted(() => {
  loadSystemHealth()
  
  // 设置定时刷新健康状态
  healthCheckTimer = setInterval(() => {
    loadSystemHealth()
  }, 30000) // 30秒刷新一次
})

onUnmounted(() => {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer)
  }
})
</script>

<style scoped>
.module-integration-index {
  padding: 24px;
}

.module-status {
  min-height: 120px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.last-check {
  font-size: 12px;
  color: #999;
}

.performance-metrics {
  margin-bottom: 12px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.issues {
  margin-top: 8px;
}
</style>