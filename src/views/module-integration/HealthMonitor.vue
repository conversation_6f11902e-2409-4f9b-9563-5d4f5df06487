<template>
  <div class="health-monitor">
    <a-card title="系统健康监控">
      <!-- 总体健康状态 -->
      <a-row :gutter="24" style="margin-bottom: 24px;">
        <a-col :span="24">
          <div class="health-overview">
            <div class="health-score">
              <a-progress 
                type="circle" 
                :percent="overallHealth" 
                :size="120"
                :stroke-color="getHealthColor(overallHealth)"
              />
              <div class="health-status">
                <h3>系统健康度</h3>
                <a-tag :color="getHealthStatusColor(overallHealth)" size="large">
                  {{ getHealthStatusText(overallHealth) }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>

      <!-- 模块健康详情 -->
      <a-row :gutter="24">
        <a-col :span="8" v-for="module in moduleHealth" :key="module.name">
          <a-card :title="module.name" size="small">
            <div class="module-health-detail">
              <div class="health-metrics">
                <div class="metric">
                  <span>响应时间</span>
                  <a-tag :color="module.responseTime < 300 ? 'green' : 'orange'">
                    {{ module.responseTime }}ms
                  </a-tag>
                </div>
                <div class="metric">
                  <span>错误率</span>
                  <a-tag :color="module.errorRate < 1 ? 'green' : 'red'">
                    {{ module.errorRate }}%
                  </a-tag>
                </div>
                <div class="metric">
                  <span>可用性</span>
                  <a-tag :color="module.availability > 99 ? 'green' : 'orange'">
                    {{ module.availability }}%
                  </a-tag>
                </div>
                <div class="metric">
                  <span>CPU使用率</span>
                  <a-progress 
                    :percent="module.cpuUsage" 
                    size="small" 
                    :stroke-color="module.cpuUsage > 80 ? '#ff4d4f' : '#52c41a'"
                  />
                </div>
                <div class="metric">
                  <span>内存使用率</span>
                  <a-progress 
                    :percent="module.memoryUsage" 
                    size="small"
                    :stroke-color="module.memoryUsage > 80 ? '#ff4d4f' : '#52c41a'"
                  />
                </div>
              </div>
              
              <div v-if="module.alerts.length > 0" class="alerts">
                <a-alert 
                  v-for="alert in module.alerts" 
                  :key="alert"
                  :message="alert" 
                  type="warning" 
                  size="small"
                  style="margin-bottom: 4px;"
                />
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 性能趋势图表 -->
      <a-card title="性能趋势" style="margin-top: 24px;">
        <div class="performance-chart">
          <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
            性能趋势图表（实际项目中可使用 ECharts 或其他图表库）
          </div>
        </div>
      </a-card>

      <!-- 系统日志 -->
      <a-card title="系统日志" style="margin-top: 24px;">
        <a-table 
          :columns="logColumns" 
          :data-source="systemLogs"
          :pagination="{ pageSize: 5 }"
          row-key="id"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'level'">
              <a-tag :color="getLogLevelColor(record.level)">
                {{ record.level.toUpperCase() }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-card>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const overallHealth = ref(95)
const moduleHealth = ref([
  {
    name: '指标规则管理',
    responseTime: 150,
    errorRate: 0.5,
    availability: 99.9,
    cpuUsage: 25,
    memoryUsage: 60,
    alerts: []
  },
  {
    name: '数据体检',
    responseTime: 280,
    errorRate: 1.2,
    availability: 99.5,
    cpuUsage: 45,
    memoryUsage: 70,
    alerts: []
  },
  {
    name: '审核确认',
    responseTime: 450,
    errorRate: 2.1,
    availability: 98.8,
    cpuUsage: 75,
    memoryUsage: 85,
    alerts: ['响应时间过长', '内存使用率偏高']
  }
])

const systemLogs = ref([
  {
    id: 1,
    timestamp: '2025-01-18 14:30:15',
    level: 'info',
    module: '数据体检',
    message: '完成定时体检任务，发现3个异常项'
  },
  {
    id: 2,
    timestamp: '2025-01-18 14:28:45',
    level: 'warn',
    module: '审核确认',
    message: '审核队列积压，当前待处理任务15个'
  },
  {
    id: 3,
    timestamp: '2025-01-18 14:25:30',
    level: 'info',
    module: '指标规则管理',
    message: '权重方案更新成功，影响12个指标'
  },
  {
    id: 4,
    timestamp: '2025-01-18 14:20:12',
    level: 'error',
    module: '数据体检',
    message: '数据源连接超时，重试中...'
  }
])

// 表格列配置
const logColumns = [
  {
    title: '时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 180,
  },
  {
    title: '级别',
    key: 'level',
    width: 80,
  },
  {
    title: '模块',
    dataIndex: 'module',
    key: 'module',
    width: 120,
  },
  {
    title: '消息',
    dataIndex: 'message',
    key: 'message',
  },
]

// 定时器
let healthTimer: NodeJS.Timeout | null = null

// 方法
const getHealthColor = (score: number) => {
  if (score >= 95) return '#52c41a'
  if (score >= 85) return '#faad14'
  return '#ff4d4f'
}

const getHealthStatusColor = (score: number) => {
  if (score >= 95) return 'green'
  if (score >= 85) return 'orange'
  return 'red'
}

const getHealthStatusText = (score: number) => {
  if (score >= 95) return '健康'
  if (score >= 85) return '良好'
  if (score >= 70) return '警告'
  return '严重'
}

const getLogLevelColor = (level: string) => {
  const map: Record<string, string> = {
    'info': 'blue',
    'warn': 'orange',
    'error': 'red',
    'debug': 'purple'
  }
  return map[level] || 'default'
}

const refreshHealthData = () => {
  // 模拟健康数据刷新
  moduleHealth.value.forEach(module => {
    module.responseTime += Math.floor(Math.random() * 20) - 10
    module.errorRate += (Math.random() - 0.5) * 0.2
    module.cpuUsage += Math.floor(Math.random() * 10) - 5
    module.memoryUsage += Math.floor(Math.random() * 10) - 5
    
    // 确保数值在合理范围内
    module.responseTime = Math.max(100, Math.min(1000, module.responseTime))
    module.errorRate = Math.max(0, Math.min(10, module.errorRate))
    module.cpuUsage = Math.max(0, Math.min(100, module.cpuUsage))
    module.memoryUsage = Math.max(0, Math.min(100, module.memoryUsage))
  })
  
  // 计算总体健康度
  const avgHealth = moduleHealth.value.reduce((sum, module) => {
    const healthScore = 100 - (module.responseTime / 10) - (module.errorRate * 5) - (module.cpuUsage * 0.2)
    return sum + Math.max(0, Math.min(100, healthScore))
  }, 0) / moduleHealth.value.length
  
  overallHealth.value = Math.floor(avgHealth)
}

// 生命周期
onMounted(() => {
  // 设置定时刷新
  healthTimer = setInterval(refreshHealthData, 5000) // 5秒刷新一次
})

onUnmounted(() => {
  if (healthTimer) {
    clearInterval(healthTimer)
  }
})
</script>

<style scoped>
.health-monitor {
  padding: 24px;
}

.health-overview {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.health-score {
  display: flex;
  align-items: center;
  gap: 40px;
}

.health-status {
  text-align: center;
}

.health-status h3 {
  margin-bottom: 8px;
  color: #262626;
}

.module-health-detail {
  min-height: 200px;
}

.health-metrics {
  margin-bottom: 16px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.alerts {
  margin-top: 16px;
}

.performance-chart {
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}
</style>