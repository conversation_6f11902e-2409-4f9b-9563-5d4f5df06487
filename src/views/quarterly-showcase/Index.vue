<template>
  <div class="quarterly-showcase-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>季度亮晒榜单</h2>
        <p>展示各培育对象在不同项目中的表现和排名</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-dropdown>
            <a-button>
              <template #icon><export-outlined /></template>
              导出榜单
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleExportMenuClick">
                <a-menu-item key="excel">
                  <file-excel-outlined />
                  导出为Excel
                </a-menu-item>
                <a-menu-item key="csv">
                  <file-text-outlined />
                  导出为CSV
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button type="primary" @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic title="培育对象总数" :value="statistics.totalTargets" suffix="人" />
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic title="项目总数" :value="statistics.totalProjects" suffix="个" />
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic title="平均分" :value="statistics.averageScore" :precision="1" />
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic 
              title="季度增长率" 
              :value="statistics.quarterlyGrowth" 
              :precision="1" 
              suffix="%" 
              :value-style="{ color: statistics.quarterlyGrowth >= 0 ? '#3f8600' : '#cf1322' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="关键词">
                <a-input 
                  v-model:value="searchForm.keyword" 
                  placeholder="请输入姓名或项目名称" 
                  allow-clear 
                  @change="handleSearch"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="部门">
                <a-select 
                  v-model:value="searchForm.department" 
                  placeholder="请选择部门" 
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="办公室">办公室</a-select-option>
                  <a-select-option value="人事处">人事处</a-select-option>
                  <a-select-option value="财务处">财务处</a-select-option>
                  <a-select-option value="教务处">教务处</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="项目类别">
                <a-select 
                  v-model:value="searchForm.project" 
                  placeholder="请选择项目" 
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="党建工作">党建工作</a-select-option>
                  <a-select-option value="业务能力">业务能力</a-select-option>
                  <a-select-option value="创新实践">创新实践</a-select-option>
                  <a-select-option value="服务质量">服务质量</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="季度">
                <a-select 
                  v-model:value="searchForm.quarter" 
                  placeholder="请选择季度" 
                  allow-clear
                  @change="handleSearch"
                >
                  <a-select-option value="2024Q1">2024年第一季度</a-select-option>
                  <a-select-option value="2024Q2">2024年第二季度</a-select-option>
                  <a-select-option value="2024Q3">2024年第三季度</a-select-option>
                  <a-select-option value="2024Q4">2024年第四季度</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 榜单列表 -->
    <div class="ranking-list-section">
      <a-card title="排行榜单">
        <template #extra>
          <a-space>
            <span>共 {{ filteredData.length }} 条记录</span>
            <a-divider type="vertical" />
            <a-button size="small" @click="resetFilters">重置筛选</a-button>
            <a-divider type="vertical" class="mobile-divider" />
            <a-button-group size="small" class="view-toggle">
              <a-button
                :type="viewMode === 'table' ? 'primary' : 'default'"
                @click="viewMode = 'table'"
              >
                <table-outlined />
              </a-button>
              <a-button
                :type="viewMode === 'card' ? 'primary' : 'default'"
                @click="viewMode = 'card'"
              >
                <appstore-outlined />
              </a-button>
            </a-button-group>
          </a-space>
        </template>

        <!-- 表格视图 -->
        <a-table
          v-if="viewMode === 'table'"
          :columns="columns"
          :data-source="filteredData"
          :pagination="pagination"
          :loading="loading"
          :scroll="{ x: 800 }"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <!-- 排名列 -->
            <template v-if="column.key === 'rank'">
              <div class="rank-cell">
                <a-badge
                  :count="record.rank"
                  :number-style="getRankStyle(record.rank)"
                  :show-zero="false"
                />
                <span class="rank-number">{{ record.rank }}</span>
              </div>
            </template>

            <!-- 培育对象列 -->
            <template v-else-if="column.key === 'target'">
              <div class="target-info">
                <a-avatar :src="record.target.avatar" :size="32">
                  {{ record.target.name.charAt(0) }}
                </a-avatar>
                <div class="target-details">
                  <div class="name">{{ record.target.name }}</div>
                  <div class="department">{{ record.target.department }}</div>
                </div>
              </div>
            </template>

            <!-- 项目列 -->
            <template v-else-if="column.key === 'project'">
              <div class="project-info">
                <div class="project-name">{{ record.project.name }}</div>
                <a-tag size="small" color="blue">{{ record.project.category }}</a-tag>
              </div>
            </template>

            <!-- 分数列 -->
            <template v-else-if="column.key === 'score'">
              <div class="score-info">
                <div class="current-score">{{ record.score }}</div>
                <div class="trend-info">
                  <a-tag
                    :color="getTrendColor(record.trend)"
                    size="small"
                  >
                    <template #icon>
                      <arrow-up-outlined v-if="record.trend === 'up'" />
                      <arrow-down-outlined v-if="record.trend === 'down'" />
                      <minus-outlined v-if="record.trend === 'stable'" />
                    </template>
                    {{ record.trendValue }}
                  </a-tag>
                </div>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  查看详情
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 卡片视图 -->
        <div v-else-if="viewMode === 'card'" class="card-view">
          <a-row :gutter="[16, 16]">
            <a-col
              v-for="item in paginatedCardData"
              :key="item.id"
              :xs="24"
              :sm="12"
              :md="8"
              :lg="6"
            >
              <ranking-card :data="item" @view-detail="viewDetail" />
            </a-col>
          </a-row>

          <!-- 卡片视图分页 -->
          <div class="card-pagination">
            <a-pagination
              v-model:current="pagination.current"
              v-model:page-size="pagination.pageSize"
              :total="filteredData.length"
              :show-size-changer="true"
              :show-quick-jumper="true"
              :show-total="(total: number) => `共 ${total} 条记录`"
              @change="handleCardPageChange"
            />
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ExportOutlined,
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  DownOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  TableOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue'
import type {
  RankingItem,
  SearchFilters,
  Statistics
} from '@/types/quarterly-showcase'
import { exportToExcel, exportToCSV } from '@/utils/export'
import RankingCard from '@/components/RankingCard.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const searchForm = reactive<SearchFilters>({
  keyword: '',
  department: '',
  project: '',
  quarter: '2024Q4'
})

// 统计数据
const statistics = ref<Statistics>({
  totalTargets: 156,
  totalProjects: 12,
  averageScore: 87.5,
  highestScore: 98.5,
  lowestScore: 72.3,
  quarterlyGrowth: 5.2
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '排名',
    dataIndex: 'rank',
    key: 'rank',
    width: 80,
    sorter: true
  },
  {
    title: '培育对象',
    dataIndex: 'target',
    key: 'target',
    width: 200
  },
  {
    title: '项目',
    dataIndex: 'project',
    key: 'project',
    width: 180
  },
  {
    title: '分数',
    dataIndex: 'score',
    key: 'score',
    width: 120,
    sorter: true
  },
  {
    title: '季度得分',
    dataIndex: 'quarterlyScore',
    key: 'quarterlyScore',
    width: 100,
    sorter: true
  },
  {
    title: '年度得分',
    dataIndex: 'yearlyScore',
    key: 'yearlyScore',
    width: 100,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
]

// Mock数据
const mockData = ref<RankingItem[]>([])

// 过滤后的数据
const filteredData = computed(() => {
  let result = [...mockData.value]

  if (searchForm.keyword) {
    result = result.filter(item =>
      item.target.name.includes(searchForm.keyword!) ||
      item.project.name.includes(searchForm.keyword!)
    )
  }

  if (searchForm.department) {
    result = result.filter(item =>
      item.target.department === searchForm.department
    )
  }

  if (searchForm.project) {
    result = result.filter(item =>
      item.project.category === searchForm.project
    )
  }

  return result
})

// 卡片视图分页数据
const paginatedCardData = computed(() => {
  const start = (pagination.current - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  pagination.current = 1
}

const resetFilters = () => {
  Object.assign(searchForm, {
    keyword: '',
    department: '',
    project: '',
    quarter: '2024Q4'
  })
}

const handleTableChange = (pag: any, _filters: any, _sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
}

const handleCardPageChange = (page: number, pageSize: number) => {
  pagination.current = page
  pagination.pageSize = pageSize
}

const getRankStyle = (rank: number) => {
  if (rank <= 3) {
    return { backgroundColor: '#faad14', color: '#fff' }
  }
  return { backgroundColor: '#d9d9d9', color: '#666' }
}

const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up': return 'green'
    case 'down': return 'red'
    default: return 'default'
  }
}

const viewDetail = (record: RankingItem) => {
  router.push(`/quarterly-showcase/detail/${record.id}`)
}

const handleExportMenuClick = ({ key }: { key: string }) => {
  try {
    const dataToExport = filteredData.value
    if (dataToExport.length === 0) {
      message.warning('暂无数据可导出')
      return
    }

    if (key === 'excel') {
      exportToExcel(dataToExport)
      message.success('Excel文件导出成功')
    } else if (key === 'csv') {
      exportToCSV(dataToExport)
      message.success('CSV文件导出成功')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// 初始化Mock数据
const initMockData = () => {
  mockData.value = [
    {
      id: '1',
      rank: 1,
      target: {
        id: '1',
        name: '市委统战部',
        department: '办公室',
        position: '主任',
        avatar: ''
      },
      project: {
        id: '1',
        name: '党建工作示范项目',
        category: '党建工作',
        description: '深入推进党建工作标准化规范化',
        weight: 25
      },
      score: 95.5,
      quarterlyScore: 92.3,
      yearlyScore: 368.5,
      trend: 'up',
      trendValue: 2,
      lastQuarterRank: 3
    },
    {
      id: '2',
      rank: 2,
      target: {
        id: '2',
        name: '市委宣传部',
        department: '人事处',
        position: '副处长',
        avatar: ''
      },
      project: {
        id: '2',
        name: '业务能力提升项目',
        category: '业务能力',
        description: '提升业务处理能力和服务水平',
        weight: 30
      },
      score: 93.2,
      quarterlyScore: 89.7,
      yearlyScore: 356.8,
      trend: 'stable',
      trendValue: 0,
      lastQuarterRank: 2
    },
    {
      id: '3',
      rank: 3,
      target: {
        id: '3',
        name: '市委宣传部',
        department: '财务处',
        position: '科长',
        avatar: ''
      },
      project: {
        id: '3',
        name: '创新实践项目',
        category: '创新实践',
        description: '推进工作方式方法创新',
        weight: 20
      },
      score: 91.8,
      quarterlyScore: 88.5,
      yearlyScore: 345.2,
      trend: 'up',
      trendValue: 1,
      lastQuarterRank: 4
    },
    {
      id: '4',
      rank: 4,
      target: {
        id: '4',
        name: '市委宣传部',
        department: '教务处',
        position: '处长',
        avatar: ''
      },
      project: {
        id: '4',
        name: '服务质量优化项目',
        category: '服务质量',
        description: '提升服务质量和用户满意度',
        weight: 25
      },
      score: 89.6,
      quarterlyScore: 86.3,
      yearlyScore: 332.1,
      trend: 'down',
      trendValue: -1,
      lastQuarterRank: 3
    },
    {
      id: '5',
      rank: 5,
      target: {
        id: '5',
        name: '市委统战部',
        department: '办公室',
        position: '副主任',
        avatar: ''
      },
      project: {
        id: '5',
        name: '党建工作创新项目',
        category: '党建工作',
        description: '党建工作方式方法创新',
        weight: 20
      },
      score: 87.4,
      quarterlyScore: 84.1,
      yearlyScore: 318.9,
      trend: 'up',
      trendValue: 3,
      lastQuarterRank: 8
    },
    {
      id: '6',
      rank: 6,
      target: {
        id: '6',
        name: '市委直属机关工委',
        department: '人事处',
        position: '科员',
        avatar: ''
      },
      project: {
        id: '6',
        name: '业务流程优化项目',
        category: '业务能力',
        description: '优化业务流程，提高工作效率',
        weight: 25
      },
      score: 85.7,
      quarterlyScore: 82.4,
      yearlyScore: 305.6,
      trend: 'stable',
      trendValue: 0,
      lastQuarterRank: 6
    },
    {
      id: '7',
      rank: 7,
      target: {
        id: '7',
        name: '市委统战部',
        department: '财务处',
        position: '副处长',
        avatar: ''
      },
      project: {
        id: '7',
        name: '数字化转型项目',
        category: '创新实践',
        description: '推进财务数字化转型',
        weight: 30
      },
      score: 83.9,
      quarterlyScore: 80.2,
      yearlyScore: 292.3,
      trend: 'down',
      trendValue: -2,
      lastQuarterRank: 5
    },
    {
      id: '8',
      rank: 8,
      target: {
        id: '8',
        name: '市委统战部',
        department: '教务处',
        position: '副科长',
        avatar: ''
      },
      project: {
        id: '8',
        name: '教学服务提升项目',
        category: '服务质量',
        description: '提升教学服务质量',
        weight: 20
      },
      score: 82.1,
      quarterlyScore: 78.8,
      yearlyScore: 279.7,
      trend: 'up',
      trendValue: 1,
      lastQuarterRank: 9
    }
  ]

  // 更新分页总数
  pagination.total = mockData.value.length
}

onMounted(() => {
  initMockData()
})
</script>

<style scoped lang="scss">
.quarterly-showcase-page {
  padding: 24px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .header-title {
      h2 {
        margin: 0;
        color: #262626;
      }
      
      p {
        margin: 4px 0 0 0;
        color: #8c8c8c;
      }
    }
  }
  
  .statistics-section {
    margin-bottom: 24px;
  }
  
  .search-filter-section {
    margin-bottom: 24px;
    
    .search-form {
      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .ranking-list-section {
    .view-toggle {
      display: none;
    }

    .mobile-divider {
      display: none;
    }

    .rank-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .rank-number {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .target-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .target-details {
        .name {
          font-weight: 500;
          color: #262626;
        }

        .department {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }

    .project-info {
      .project-name {
        margin-bottom: 4px;
        font-weight: 500;
      }
    }

    .score-info {
      .current-score {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;
      }

      .trend-info {
        margin-top: 4px;
      }
    }

    .card-view {
      .card-pagination {
        margin-top: 24px;
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .quarterly-showcase-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-title {
        h2 {
          font-size: 20px;
        }

        p {
          font-size: 14px;
        }
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .statistics-section {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .search-filter-section {
      .search-form {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }

    .ranking-list-section {
      .view-toggle {
        display: inline-flex;
      }

      .mobile-divider {
        display: block;
      }

      .ant-table-wrapper {
        overflow-x: auto;
      }

      .target-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .target-details {
          .name {
            font-size: 14px;
          }

          .department {
            font-size: 11px;
          }
        }
      }

      .project-info {
        .project-name {
          font-size: 13px;
          margin-bottom: 2px;
        }
      }

      .score-info {
        .current-score {
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .quarterly-showcase-page {
    padding: 12px;

    .page-header {
      .header-title {
        h2 {
          font-size: 18px;
        }
      }
    }

    .statistics-section {
      .ant-card {
        .ant-statistic {
          .ant-statistic-title {
            font-size: 12px;
          }

          .ant-statistic-content {
            font-size: 18px;
          }
        }
      }
    }

    .search-filter-section {
      .ant-card {
        .ant-card-body {
          padding: 16px;
        }
      }
    }

    .ranking-list-section {
      .view-toggle {
        display: inline-flex;
      }

      .mobile-divider {
        display: block;
      }

      .ant-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 16px;
          }

          .ant-card-extra {
            .ant-space {
              flex-wrap: wrap;
              gap: 8px !important;

              .ant-space-item {
                margin-right: 0 !important;
              }
            }
          }
        }

        .ant-card-body {
          padding: 12px;
        }
      }

      .ant-table {
        font-size: 12px;

        .ant-table-thead > tr > th {
          padding: 8px 4px;
          font-size: 12px;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 4px;
        }
      }

      .card-view {
        .card-pagination {
          margin-top: 16px;

          .ant-pagination {
            .ant-pagination-options {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
