<template>
  <div class="data-display-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>数据展示</h2>
        <p>理论宣讲纪实数据展示和查询，支持多维度筛选、详情查看、数据导出等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="printData">
            <template #icon><printer-outlined /></template>
            打印
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="纪实总数"
              :value="yulifacetoFaceStore.totalDocuments"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已发布"
              :value="yulifacetoFaceStore.publishedDocuments"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总浏览量"
              :value="yulifacetoFaceStore.totalViews"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <eye-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总下载量"
              :value="yulifacetoFaceStore.totalDownloads"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <download-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="宣讲主题" class="form-item-full">
                <a-input v-model:value="searchForm.title" placeholder="请输入宣讲主题" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="主讲人" class="form-item-full">
                <a-input v-model:value="searchForm.speaker" placeholder="请输入主讲人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="宣讲地点" class="form-item-full">
                <a-input v-model:value="searchForm.location" placeholder="请输入宣讲地点" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="发布状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择发布状态" allow-clear>
                  <a-select-option v-for="item in DocumentStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="宣讲时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.lectureTimeRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据展示区域 -->
    <div class="data-display-section">
      <a-card>
        <template #title>
          <div class="display-header">
            <span>纪实数据</span>
            <span class="record-count">共 {{ filteredDocumentList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="sortField" placeholder="排序字段" style="width: 120px">
              <a-select-option value="createTime">创建时间</a-select-option>
              <a-select-option value="lectureTime">宣讲时间</a-select-option>
              <a-select-option value="viewCount">浏览量</a-select-option>
              <a-select-option value="downloadCount">下载量</a-select-option>
            </a-select>
            <a-select v-model:value="sortOrder" style="width: 80px">
              <a-select-option value="desc">降序</a-select-option>
              <a-select-option value="asc">升序</a-select-option>
            </a-select>
            <a-radio-group v-model:value="displayMode" button-style="solid" size="small">
              <a-radio-button value="card">
                <appstore-outlined />
              </a-radio-button>
              <a-radio-button value="list">
                <unordered-list-outlined />
              </a-radio-button>
            </a-radio-group>
          </a-space>
        </template>

        <!-- 卡片视图 -->
        <div v-if="displayMode === 'card'" class="card-display">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="document in sortedDocumentList" :key="document.id">
              <a-card 
                class="document-card" 
                hoverable
                @click="showDocumentDetail(document)"
              >
                <template #cover v-if="document.coverImage">
                  <div class="card-cover">
                    <img :src="document.coverImage" :alt="document.title" />
                  </div>
                </template>
                <template #actions>
                  <eye-outlined key="view" @click.stop="showDocumentDetail(document)" />
                  <download-outlined key="download" @click.stop="downloadDocument(document)" />
                  <share-alt-outlined key="share" @click.stop="shareDocument(document)" />
                </template>
                <a-card-meta>
                  <template #title>
                    <div class="card-title">
                      <span class="title-text">{{ document.title }}</span>
                      <a-tag :color="getStatusColor(document.status)" size="small">
                        {{ getStatusText(document.status) }}
                      </a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="card-desc">
                      <div class="desc-item">
                        <user-outlined />
                        <span>{{ document.speaker }}</span>
                      </div>
                      <div class="desc-item">
                        <environment-outlined />
                        <span>{{ document.location }}</span>
                      </div>
                      <div class="desc-item">
                        <clock-circle-outlined />
                        <span>{{ formatDate(document.lectureTime) }}</span>
                      </div>
                      <div class="desc-stats">
                        <span class="stat-item">
                          <eye-outlined />
                          {{ document.viewCount }}
                        </span>
                        <span class="stat-item">
                          <download-outlined />
                          {{ document.downloadCount }}
                        </span>
                      </div>
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 列表视图 -->
        <div v-else class="list-display">
          <a-table
            :columns="columns"
            :data-source="sortedDocumentList"
            :loading="yulifacetoFaceStore.loading"
            :pagination="paginationConfig"
            :scroll="{ x: 1200 }"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'index'">
                {{ getRowIndex(record) }}
              </template>
              <template v-else-if="column.key === 'title'">
                <div class="title-cell">
                  <span class="title-text" @click="showDocumentDetail(record)">{{ record.title }}</span>
                  <div class="title-meta">
                    <a-tag :color="getStatusColor(record.status)" size="small">
                      {{ getStatusText(record.status) }}
                    </a-tag>
                    <span v-if="record.summary" class="summary">{{ record.summary }}</span>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'stats'">
                <div class="stats-cell">
                  <span class="stat-item">
                    <eye-outlined />
                    {{ record.viewCount }}
                  </span>
                  <span class="stat-item">
                    <download-outlined />
                    {{ record.downloadCount }}
                  </span>
                </div>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="showDocumentDetail(record)">查看详情</a-button>
                  <a-button type="link" size="small" @click="downloadDocument(record)">下载</a-button>
                  <a-button type="link" size="small" @click="shareDocument(record)">分享</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 纪实详情弹窗 -->
    <a-modal 
      title="纪实详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="document-detail" v-if="currentDocument">
        <div class="detail-header">
          <h2>{{ currentDocument.title }}</h2>
          <div class="header-meta">
            <a-tag :color="getStatusColor(currentDocument.status)">
              {{ getStatusText(currentDocument.status) }}
            </a-tag>
            <span class="view-count">
              <eye-outlined />
              {{ currentDocument.viewCount }} 次浏览
            </span>
            <span class="download-count">
              <download-outlined />
              {{ currentDocument.downloadCount }} 次下载
            </span>
          </div>
        </div>

        <a-descriptions bordered :column="2" size="small">
          <a-descriptions-item label="主讲人">{{ currentDocument.speaker }}</a-descriptions-item>
          <a-descriptions-item label="宣讲地点">{{ currentDocument.location }}</a-descriptions-item>
          <a-descriptions-item label="宣讲时间" :span="2">{{ currentDocument.lectureTime }}</a-descriptions-item>
          <a-descriptions-item label="联系方式" v-if="currentDocument.contactInfo">{{ currentDocument.contactInfo }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ currentDocument.creator }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentDocument.createTime }}</a-descriptions-item>
          <a-descriptions-item label="发布时间" v-if="currentDocument.publishTime">{{ currentDocument.publishTime }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>简介</a-divider>
        <div class="summary-content" v-if="currentDocument.summary">
          {{ currentDocument.summary }}
        </div>
        <div v-else class="no-summary">暂无简介</div>
        
        <a-divider>宣讲内容</a-divider>
        <div class="content-preview">
          <div class="content-text">{{ currentDocument.content }}</div>
          <div v-if="currentDocument.coverImage" class="content-image">
            <img :src="currentDocument.coverImage" :alt="currentDocument.title" />
          </div>
        </div>

        <div class="detail-actions">
          <a-space>
            <a-button @click="detailVisible = false">关闭</a-button>
            <a-button type="primary" @click="downloadDocument(currentDocument)">
              <template #icon><download-outlined /></template>
              下载
            </a-button>
            <a-button @click="shareDocument(currentDocument)">
              <template #icon><share-alt-outlined /></template>
              分享
            </a-button>
            <a-button @click="printDocument(currentDocument)">
              <template #icon><printer-outlined /></template>
              打印
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useYulifacetoFaceStore } from '@/store/modules/yulifaceto-face'
import type { DocumentRecord, DocumentSearchParams } from '@/types/yulifaceto-face'
import {
  DocumentStatusOptions,
  DocumentStatusTextMap,
  DocumentStatusColorMap
} from '@/types/yulifaceto-face'
import {
  ExportOutlined,
  PrinterOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  UserOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

// 使用store
const yulifacetoFaceStore = useYulifacetoFaceStore()

// 响应式数据
const searchForm = ref<DocumentSearchParams>({
  title: '',
  speaker: '',
  location: '',
  status: undefined,
  lectureTimeRange: undefined
})

const detailVisible = ref(false)
const displayMode = ref<'card' | 'list'>('card')
const sortField = ref('createTime')
const sortOrder = ref<'asc' | 'desc'>('desc')

// 当前选中的纪实
const currentDocument = ref<DocumentRecord | null>(null)

// 计算属性
const filteredDocumentList = computed(() => {
  return yulifacetoFaceStore.documentList.filter(item => item.status === 2) // 只显示已发布的
})

const sortedDocumentList = computed(() => {
  const list = [...filteredDocumentList.value]
  return list.sort((a, b) => {
    const aValue = a[sortField.value as keyof DocumentRecord]
    const bValue = b[sortField.value as keyof DocumentRecord]
    
    if (sortOrder.value === 'desc') {
      return String(bValue).localeCompare(String(aValue))
    } else {
      return String(aValue).localeCompare(String(bValue))
    }
  })
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '宣讲主题',
    key: 'title',
    width: 250
  },
  {
    title: '主讲人',
    dataIndex: 'speaker',
    key: 'speaker',
    width: 120
  },
  {
    title: '宣讲地点',
    dataIndex: 'location',
    key: 'location',
    width: 150
  },
  {
    title: '宣讲时间',
    dataIndex: 'lectureTime',
    key: 'lectureTime',
    width: 180
  },
  {
    title: '统计',
    key: 'stats',
    width: 100,
    align: 'center'
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    key: 'publishTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 12,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: DocumentRecord) {
  return sortedDocumentList.value.findIndex(item => item.id === record.id) + 1
}

function getStatusText(status: number) {
  return DocumentStatusTextMap[status as keyof typeof DocumentStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return DocumentStatusColorMap[status as keyof typeof DocumentStatusColorMap] || 'default'
}

function formatDate(dateString: string) {
  return dayjs(dateString).format('MM-DD HH:mm')
}

async function refreshData() {
  await yulifacetoFaceStore.fetchDocumentList()
  message.success('数据刷新成功')
}

function showDocumentDetail(record: DocumentRecord) {
  currentDocument.value = record
  detailVisible.value = true
  
  // 增加浏览次数（模拟）
  record.viewCount += 1
}

function downloadDocument(record: DocumentRecord) {
  message.success(`下载纪实"${record.title}"`)
  // 增加下载次数（模拟）
  record.downloadCount += 1
}

function shareDocument(record: DocumentRecord) {
  message.success(`分享纪实"${record.title}"`)
  // 实际实现中可以打开分享弹窗
}

function printDocument(record: DocumentRecord) {
  message.success(`打印纪实"${record.title}"`)
  // 实际实现中可以调用打印功能
}

function exportData() {
  message.info('数据导出功能开发中...')
}

function printData() {
  message.info('批量打印功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await yulifacetoFaceStore.fetchDocumentList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    title: '',
    speaker: '',
    location: '',
    status: undefined,
    lectureTimeRange: undefined
  }
  yulifacetoFaceStore.fetchDocumentList()
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.data-display-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .data-display-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .display-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .card-display {
    .document-card {
      transition: all 0.3s ease;
      border-radius: 8px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .card-cover {
        height: 160px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .card-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 8px;

        .title-text {
          flex: 1;
          font-weight: 600;
          color: #333;
          cursor: pointer;
          transition: color 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .card-desc {
        .desc-item {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 6px;
          font-size: 12px;
          color: #666;

          .anticon {
            font-size: 11px;
            color: #999;
          }
        }

        .desc-stats {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #f0f0f0;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: #999;

            .anticon {
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .list-display {
    .title-cell {
      .title-text {
        font-weight: 600;
        color: #333;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: #1890ff;
        }
      }

      .title-meta {
        margin-top: 4px;
        display: flex;
        align-items: center;
        gap: 8px;

        .summary {
          color: #666;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .stats-cell {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #666;

        .anticon {
          font-size: 11px;
        }
      }
    }
  }

  .document-detail {
    .detail-header {
      text-align: center;
      margin-bottom: 24px;

      h2 {
        margin: 0 0 12px 0;
        color: #333;
        font-size: 24px;
        font-weight: 600;
      }

      .header-meta {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;

        .view-count,
        .download-count {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: #666;

          .anticon {
            font-size: 11px;
          }
        }
      }
    }

    .summary-content {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 6px;
      line-height: 1.6;
      color: #333;
    }

    .no-summary {
      text-align: center;
      color: #999;
      font-style: italic;
      padding: 16px;
    }

    .content-preview {
      .content-text {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 6px;
        line-height: 1.6;
        margin-bottom: 16px;
        white-space: pre-wrap;
        color: #333;
      }

      .content-image {
        text-align: center;

        img {
          max-width: 100%;
          max-height: 400px;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .detail-actions {
      text-align: center;
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-display-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .card-display {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .card-title {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .desc-stats {
      justify-content: space-around;
    }

    .header-meta {
      flex-direction: column;
      gap: 8px;
    }

    .detail-actions {
      .ant-space {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }
}
</style>
