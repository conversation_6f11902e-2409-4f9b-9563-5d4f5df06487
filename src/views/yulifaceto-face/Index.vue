<template>
  <div class="yulifaceto-face-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>渝理面对面</h2>
        <p>理论宣讲纪实管理系统，支持纪实录入、模板管理、数据展示、统计分析等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="goToDocumentManagement">
            <template #icon><file-text-outlined /></template>
            纪实管理
          </a-button>
          <a-button @click="goToTemplateManagement">
            <template #icon><layout-outlined /></template>
            模板管理
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="纪实总数"
              :value="yulifacetoFaceStore.totalDocuments"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已发布"
              :value="yulifacetoFaceStore.publishedDocuments"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="草稿"
              :value="yulifacetoFaceStore.draftDocuments"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <edit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="完成率"
              :value="yulifacetoFaceStore.completionRate"
              :precision="1"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 第二行统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="主讲人数"
              :value="yulifacetoFaceStore.totalSpeakers"
              :value-style="{ color: '#13c2c2' }"
            >
              <template #prefix>
                <user-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="模板数量"
              :value="yulifacetoFaceStore.totalTemplates"
              :value-style="{ color: '#eb2f96' }"
            >
              <template #prefix>
                <layout-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总浏览量"
              :value="yulifacetoFaceStore.totalViews"
              :value-style="{ color: '#fa541c' }"
            >
              <template #prefix>
                <eye-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总下载量"
              :value="yulifacetoFaceStore.totalDownloads"
              :value-style="{ color: '#2f54eb' }"
            >
              <template #prefix>
                <download-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最新纪实动态 -->
    <div class="recent-documents-section">
      <a-card title="最新纪实动态" :loading="yulifacetoFaceStore.loading">
        <template #extra>
          <a-button type="link" @click="goToDataDisplay">查看全部</a-button>
        </template>
        <a-list
          :data-source="recentDocuments"
          :locale="{ emptyText: '暂无纪实记录' }"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta>
                <template #title>
                  <div class="document-title">
                    <span>{{ item.title }}</span>
                    <a-tag :color="getStatusColor(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </a-tag>
                  </div>
                </template>
                <template #description>
                  <div class="document-desc">
                    <div>主讲人: {{ item.speaker }} | 地点: {{ item.location }}</div>
                    <div>宣讲时间: {{ item.lectureTime }}</div>
                    <div v-if="item.summary">简介: {{ item.summary }}</div>
                  </div>
                </template>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getStatusColor(item.status) }">
                    <template #icon>
                      <file-text-outlined />
                    </template>
                  </a-avatar>
                </template>
              </a-list-item-meta>
              <template #actions>
                <span>浏览: {{ item.viewCount }}</span>
                <span>下载: {{ item.downloadCount }}</span>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>

    <!-- 模板库概览 -->
    <div class="template-overview-section">
      <a-card title="模板库概览" :loading="yulifacetoFaceStore.loading">
        <template #extra>
          <a-button type="link" @click="goToTemplateManagement">查看全部</a-button>
        </template>
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :md="12" v-for="template in recentTemplates" :key="template.id">
            <a-card size="small" class="template-card" hoverable @click="previewTemplate(template)">
              <template #title>
                <div class="template-title">
                  <span>{{ template.name }}</span>
                  <a-tag :color="getTemplateCategoryColor(template.category)" size="small">
                    {{ getTemplateCategoryText(template.category) }}
                  </a-tag>
                </div>
              </template>
              <div class="template-info">
                <div class="template-desc">{{ template.description }}</div>
                <div class="template-stats">
                  <span class="stat-item">
                    <user-outlined />
                    {{ template.creator }}
                  </span>
                  <span class="stat-item">
                    <eye-outlined />
                    使用 {{ template.usageCount }} 次
                  </span>
                  <span class="stat-item" v-if="template.isShared">
                    <share-alt-outlined />
                    已共享
                  </span>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions-section">
      <a-card title="快速操作">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="createNewDocument">
              <div class="action-content">
                <plus-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">新建纪实</div>
                  <div class="action-desc">创建新的宣讲纪实</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToTemplateManagement">
              <div class="action-content">
                <layout-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">模板管理</div>
                  <div class="action-desc">创建和管理模板</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToDataDisplay">
              <div class="action-content">
                <bar-chart-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">数据展示</div>
                  <div class="action-desc">查看纪实数据</div>
                </div>
              </div>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card size="small" hoverable class="action-card" @click="goToStatisticsAnalysis">
              <div class="action-content">
                <pie-chart-outlined class="action-icon" />
                <div class="action-text">
                  <div class="action-title">统计分析</div>
                  <div class="action-desc">查看统计报表</div>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useYulifacetoFaceStore } from '@/store/modules/yulifaceto-face'
import type { DocumentTemplate } from '@/types/yulifaceto-face'
import {
  DocumentStatusTextMap,
  DocumentStatusColorMap,
  TemplateCategoryTextMap,
  TemplateCategoryColorMap
} from '@/types/yulifaceto-face'
import {
  FileTextOutlined,
  LayoutOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  EditOutlined,
  TrophyOutlined,
  UserOutlined,
  EyeOutlined,
  DownloadOutlined,
  PlusOutlined,
  BarChartOutlined,
  PieChartOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const yulifacetoFaceStore = useYulifacetoFaceStore()

// 最新纪实记录（取前5条）
const recentDocuments = computed(() => {
  return yulifacetoFaceStore.documentList
    .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
    .slice(0, 5)
})

// 最新模板（取前4个）
const recentTemplates = computed(() => {
  return yulifacetoFaceStore.templateList
    .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
    .slice(0, 4)
})

// 方法定义
function getStatusText(status: number) {
  return DocumentStatusTextMap[status as keyof typeof DocumentStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return DocumentStatusColorMap[status as keyof typeof DocumentStatusColorMap] || 'default'
}

function getTemplateCategoryText(category: number) {
  return TemplateCategoryTextMap[category as keyof typeof TemplateCategoryTextMap] || '未知'
}

function getTemplateCategoryColor(category: number) {
  return TemplateCategoryColorMap[category as keyof typeof TemplateCategoryColorMap] || 'default'
}

// 页面跳转
function goToDocumentManagement() {
  router.push('/yulifaceto-face/document-management')
}

function goToTemplateManagement() {
  router.push('/yulifaceto-face/template-management')
}

function goToDataDisplay() {
  router.push('/yulifaceto-face/data-display')
}

function goToStatisticsAnalysis() {
  router.push('/yulifaceto-face/statistics-analysis')
}

function createNewDocument() {
  router.push('/yulifaceto-face/document-management?action=create')
}

function previewTemplate(template: DocumentTemplate) {
  message.info(`预览模板: ${template.name}`)
  // 这里可以打开模板预览弹窗
}

// 刷新数据
async function refreshData() {
  try {
    await Promise.all([
      yulifacetoFaceStore.fetchStatistics(),
      yulifacetoFaceStore.fetchDocumentList(),
      yulifacetoFaceStore.fetchTemplateList()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  }
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.yulifaceto-face-index {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .recent-documents-section,
  .template-overview-section,
  .quick-actions-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .document-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .document-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.4;

    div {
      margin-bottom: 2px;
    }
  }

  .template-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .template-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .template-info {
      .template-desc {
        color: #666;
        font-size: 12px;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .template-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .stat-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          color: #999;

          .anticon {
            font-size: 10px;
          }
        }
      }
    }
  }

  .action-card {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .action-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .action-icon {
        font-size: 24px;
        color: #1890ff;
      }

      .action-text {
        .action-title {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .action-desc {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .yulifaceto-face-index {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .template-stats {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .action-content {
      flex-direction: column;
      text-align: center;
      gap: 8px;
    }
  }
}
</style>
