<template>
  <div class="document-management-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>纪实管理</h2>
        <p>理论宣讲纪实录入、编辑、发布管理，支持模板应用、内容编辑、状态管理等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建纪实
          </a-button>
          <a-button @click="batchPublish" :disabled="selectedRowKeys.length === 0">
            <template #icon><send-outlined /></template>
            批量发布
          </a-button>
          <a-button @click="exportDocuments">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 纪实统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="纪实总数"
              :value="yulifacetoFaceStore.totalDocuments"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已发布"
              :value="yulifacetoFaceStore.publishedDocuments"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="草稿"
              :value="yulifacetoFaceStore.draftDocuments"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <edit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="主讲人数"
              :value="yulifacetoFaceStore.totalSpeakers"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <user-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="宣讲主题" class="form-item-full">
                <a-input v-model:value="searchForm.title" placeholder="请输入宣讲主题" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="主讲人" class="form-item-full">
                <a-input v-model:value="searchForm.speaker" placeholder="请输入主讲人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="宣讲地点" class="form-item-full">
                <a-input v-model:value="searchForm.location" placeholder="请输入宣讲地点" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="发布状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择发布状态" allow-clear>
                  <a-select-option v-for="item in DocumentStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="创建时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>纪实列表</span>
            <span class="record-count">共 {{ filteredDocumentList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterStatus" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in DocumentStatusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredDocumentList"
          :loading="yulifacetoFaceStore.loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'title'">
              <div class="title-cell">
                <span class="title-text">{{ record.title }}</span>
                <div class="title-meta">
                  <span v-if="record.summary" class="summary">{{ record.summary }}</span>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'stats'">
              <div class="stats-cell">
                <span class="stat-item">
                  <eye-outlined />
                  {{ record.viewCount }}
                </span>
                <span class="stat-item">
                  <download-outlined />
                  {{ record.downloadCount }}
                </span>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button type="link" size="small" @click="showEditModal(record)">编辑</a-button>
                <a-button 
                  v-if="record.status === 1" 
                  type="link" 
                  size="small" 
                  @click="publishDocument(record)"
                >
                  发布
                </a-button>
                <a-button 
                  v-if="record.status === 2 && ((record as any).enableDownload !== undefined ? (record as any).enableDownload : true)" 
                  type="link" 
                  size="small" 
                  @click="downloadDocument(record)"
                >
                  下载
                </a-button>
                <a-button 
                  v-if="record.status === 0 || record.status === 3" 
                  type="link" 
                  size="small" 
                  @click="restoreDocument(record)"
                >
                  恢复
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  danger
                  @click="deleteDocument(record)"
                >
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 纪实详情弹窗 -->
    <a-modal 
      title="纪实详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="document-detail" v-if="currentDocument">
        <a-descriptions bordered :column="2" size="small">
          <a-descriptions-item label="宣讲主题" :span="2">{{ currentDocument.title }}</a-descriptions-item>
          <a-descriptions-item label="主讲人">{{ currentDocument.speaker }}</a-descriptions-item>
          <a-descriptions-item label="宣讲地点">{{ currentDocument.location }}</a-descriptions-item>
          <a-descriptions-item label="宣讲时间" :span="2">{{ currentDocument.lectureTime }}</a-descriptions-item>
          <a-descriptions-item label="联系方式" v-if="currentDocument.contactInfo">{{ currentDocument.contactInfo }}</a-descriptions-item>
          <a-descriptions-item label="发布状态">
            <a-tag :color="getStatusColor(currentDocument.status)">
              {{ getStatusText(currentDocument.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ currentDocument.createTime }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ currentDocument.updateTime }}</a-descriptions-item>
          <a-descriptions-item label="创建人">{{ currentDocument.creator }}</a-descriptions-item>
          <a-descriptions-item label="浏览次数">{{ currentDocument.viewCount }}</a-descriptions-item>
          <a-descriptions-item label="下载次数">{{ currentDocument.downloadCount }}</a-descriptions-item>
          <a-descriptions-item label="是否支持下载">
            <a-tag :color="((currentDocument as any).enableDownload !== undefined ? (currentDocument as any).enableDownload : true) ? 'green' : 'red'">
              {{ ((currentDocument as any).enableDownload !== undefined ? (currentDocument as any).enableDownload : true) ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="下载格式" v-if="((currentDocument as any).enableDownload !== undefined ? (currentDocument as any).enableDownload : true)">
            {{ getDownloadFormatText((currentDocument as any).downloadFormat || 'doc') }}
          </a-descriptions-item>
          <a-descriptions-item label="发布时间" v-if="currentDocument.publishTime">{{ currentDocument.publishTime }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>宣讲内容</a-divider>
        
        <div class="content-preview">
          <div class="content-text">{{ currentDocument.content }}</div>
          <div v-if="currentDocument.coverImage" class="content-image">
            <img :src="currentDocument.coverImage" :alt="currentDocument.title" />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 创建/编辑纪实弹窗 -->
    <a-modal 
      :title="editMode ? '编辑纪实' : '新建纪实'" 
      :visible="editVisible" 
      @cancel="editVisible = false" 
      @ok="submitDocument"
      :confirm-loading="submitting"
      width="800px"
    >
      <div class="document-form">
        <a-form :model="documentForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="宣讲主题" required>
            <a-input 
              v-model:value="documentForm.title" 
              placeholder="请输入宣讲主题"
              :maxlength="100"
              show-count
            />
          </a-form-item>
          <a-form-item label="主讲人" required>
            <a-input 
              v-model:value="documentForm.speaker" 
              placeholder="请输入主讲人姓名"
            />
          </a-form-item>
          <a-form-item label="宣讲时间" required>
            <a-date-picker 
              v-model:value="documentForm.lectureTime" 
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择宣讲时间"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="宣讲地点" required>
            <a-input 
              v-model:value="documentForm.location" 
              placeholder="请输入宣讲地点"
            />
          </a-form-item>
          <a-form-item label="联系方式">
            <a-input 
              v-model:value="documentForm.contactInfo" 
              placeholder="请输入联系方式"
            />
          </a-form-item>
          
          <!-- 人员信息部分 -->
          <a-divider orientation="left">人员信息</a-divider>
          
          <!-- 参加人员 -->
          <a-form-item label="参加人员">
            <div class="personnel-section">
              <div class="personnel-summary">
                应到{{ documentForm.totalParticipants }}人，实到{{ documentForm.actualParticipants }}人，请假{{ documentForm.leaveParticipants }}人（因公请假{{ documentForm.officialLeave }}人，因私请假{{ documentForm.personalLeave }}人），未签到{{ documentForm.absentParticipants }}人
              </div>
              <div class="personnel-actions">
                <a-button type="link" @click="showParticipantModal" class="add-personnel-btn">
                  添加参加人员
                </a-button>
                <span class="personnel-count">({{ documentForm.participants.length }}人)</span>
              </div>
              <div v-if="documentForm.participants.length > 0" class="personnel-list">
                <a-tag 
                  v-for="(participant, index) in documentForm.participants" 
                  :key="index"
                  closable
                  @close="removeParticipant(index)"
                  class="personnel-tag"
                >
                  {{ participant.name }}{{ participant.position ? `(${participant.position})` : '' }}
                </a-tag>
              </div>
            </div>
          </a-form-item>
          
          <!-- 列席人员 -->
          <a-form-item label="列席人员">
            <div class="personnel-section">
              <div class="personnel-actions">
                <a-button type="link" @click="showAttendeeModal" class="add-personnel-btn">
                  添加列席人员
                </a-button>
                <a-button type="link" @click="showExternalPersonnelModal" class="add-external-btn">
                  录入非本组织内人员
                </a-button>
                <span class="personnel-count">({{ documentForm.attendees.length }}人)</span>
              </div>
              <div v-if="documentForm.attendees.length > 0" class="personnel-list">
                <a-tag 
                  v-for="(attendee, index) in documentForm.attendees" 
                  :key="index"
                  closable
                  @close="removeAttendee(index)"
                  class="personnel-tag"
                  color="blue"
                >
                  {{ attendee.name }}{{ attendee.position ? `(${attendee.position})` : '' }}
                </a-tag>
              </div>
            </div>
          </a-form-item>
          
          <a-form-item label="简介">
            <a-textarea 
              v-model:value="documentForm.summary" 
              :rows="2" 
              placeholder="请输入简介"
              :maxlength="200"
              show-count
            />
          </a-form-item>
          <a-form-item label="宣讲内容" required>
            <a-textarea 
              v-model:value="documentForm.content" 
              :rows="8" 
              placeholder="请输入宣讲内容"
              :maxlength="5000"
              show-count
            />
          </a-form-item>
          <a-form-item label="示意图">
            <a-input 
              v-model:value="documentForm.coverImage" 
              placeholder="请输入图片URL"
            />
          </a-form-item>
          <a-form-item label="是否支持下载">
            <a-radio-group v-model:value="documentForm.enableDownload">
              <a-radio :value="true">是</a-radio>
              <a-radio :value="false">否</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="下载格式" v-if="documentForm.enableDownload">
            <a-select 
              v-model:value="documentForm.downloadFormat" 
              placeholder="请选择下载格式"
              style="width: 100%"
            >
              <a-select-option value="doc">Word文档 (.doc)</a-select-option>
              <a-select-option value="pdf">PDF文档 (.pdf)</a-select-option>
              <a-select-option value="txt">文本文档 (.txt)</a-select-option>
              <a-select-option value="docx">Word文档 (.docx)</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="发布状态">
            <a-radio-group v-model:value="documentForm.status">
              <a-radio :value="1">保存为草稿</a-radio>
              <a-radio :value="2">立即发布</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 人员选择模态框组件 -->
    <PersonnelModals
      :participant-modal-visible="participantModalVisible"
      :attendee-modal-visible="attendeeModalVisible"
      :external-personnel-modal-visible="externalPersonnelModalVisible"
      :personnel-list="mockPersonnelList"
      :initial-selected-personnel="selectedPersonnel"
      :initial-external-form="externalPersonnelForm"
      @cancel-participant="participantModalVisible = false"
      @cancel-attendee="attendeeModalVisible = false"
      @cancel-external="externalPersonnelModalVisible = false"
      @confirm-participants="confirmParticipants"
      @confirm-attendees="confirmAttendees"
      @confirm-external="confirmExternalPersonnel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useYulifacetoFaceStore } from '@/store/modules/yulifaceto-face'
import type { DocumentRecord, DocumentSearchParams } from '@/types/yulifaceto-face'
import {
  DocumentStatusOptions,
  DocumentStatusTextMap,
  DocumentStatusColorMap
} from '@/types/yulifaceto-face'
import {
  PlusOutlined,
  SendOutlined,
  ExportOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  EditOutlined,
  UserOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import PersonnelModals from './PersonnelModals.vue'

// 使用store
const yulifacetoFaceStore = useYulifacetoFaceStore()

// 响应式数据
const searchForm = ref<DocumentSearchParams>({
  title: '',
  speaker: '',
  location: '',
  status: undefined,
  dateRange: undefined
})

const detailVisible = ref(false)
const editVisible = ref(false)
const editMode = ref(false)
const submitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterStatus = ref<number | undefined>(undefined)

// 人员选择相关状态
const participantModalVisible = ref(false)
const attendeeModalVisible = ref(false)
const externalPersonnelModalVisible = ref(false)
const selectedPersonnel = ref<string[]>([])

// 外部人员表单
const externalPersonnelForm = ref({
  name: '',
  position: '',
  department: '',
  type: 'participant' as 'participant' | 'attendee'
})

// 模拟人员数据
const mockPersonnelList = ref([
  { key: '1', title: '张三 - 党委书记', name: '张三', position: '党委书记', department: '党委办公室' },
  { key: '2', title: '李四 - 副书记', name: '李四', position: '副书记', department: '党委办公室' },
  { key: '3', title: '王五 - 组织委员', name: '王五', position: '组织委员', department: '组织部' },
  { key: '4', title: '赵六 - 宣传委员', name: '赵六', position: '宣传委员', department: '宣传部' },
  { key: '5', title: '钱七 - 纪检委员', name: '钱七', position: '纪检委员', department: '纪检监察室' }
])

// 当前选中的纪实
const currentDocument = ref<DocumentRecord | null>(null)

// 纪实表单数据
const documentForm = ref({
  title: '',
  speaker: '',
  content: '',
  lectureTime: null as any,
  location: '',
  contactInfo: '',
  summary: '',
  coverImage: '',
  status: 1,
  // 下载相关字段
  enableDownload: true,
  downloadFormat: 'doc',
  // 人员统计信息
  totalParticipants: 0,
  actualParticipants: 0,
  leaveParticipants: 0,
  officialLeave: 0,
  personalLeave: 0,
  absentParticipants: 0,
  // 参加人员列表
  participants: [] as Array<{id: string, name: string, position?: string, department?: string, isExternal?: boolean}>,
  // 列席人员列表
  attendees: [] as Array<{id: string, name: string, position?: string, department?: string, isExternal?: boolean}>
})

// 计算属性
const filteredDocumentList = computed(() => {
  let filtered = [...yulifacetoFaceStore.documentList]

  if (filterStatus.value !== undefined) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '宣讲主题',
    key: 'title',
    width: 250
  },
  {
    title: '主讲人',
    dataIndex: 'speaker',
    key: 'speaker',
    width: 120
  },
  {
    title: '宣讲地点',
    dataIndex: 'location',
    key: 'location',
    width: 150
  },
  {
    title: '宣讲时间',
    dataIndex: 'lectureTime',
    key: 'lectureTime',
    width: 180
  },
  {
    title: '发布状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '统计',
    key: 'stats',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: DocumentRecord) {
  return filteredDocumentList.value.findIndex(item => item.id === record.id) + 1
}

function getStatusText(status: number) {
  return DocumentStatusTextMap[status as keyof typeof DocumentStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return DocumentStatusColorMap[status as keyof typeof DocumentStatusColorMap] || 'default'
}

function getDownloadFormatText(format: string) {
  const formatMap: Record<string, string> = {
    'doc': 'Word文档 (.doc)',
    'docx': 'Word文档 (.docx)',
    'pdf': 'PDF文档 (.pdf)',
    'txt': '文本文档 (.txt)'
  }
  return formatMap[format] || '未知格式'
}

async function refreshData() {
  await yulifacetoFaceStore.fetchDocumentList()
  message.success('数据刷新成功')
}

function showDetail(record: DocumentRecord) {
  currentDocument.value = record
  detailVisible.value = true
}

function showCreateModal() {
  editMode.value = false
  documentForm.value = {
    title: '',
    speaker: '',
    content: '',
    lectureTime: null,
    location: '',
    contactInfo: '',
    summary: '',
    coverImage: '',
    status: 1,
    enableDownload: true,
    downloadFormat: 'doc',
    totalParticipants: 0,
    actualParticipants: 0,
    leaveParticipants: 0,
    officialLeave: 0,
    personalLeave: 0,
    absentParticipants: 0,
    participants: [],
    attendees: []
  }
  editVisible.value = true
}

function showEditModal(record: DocumentRecord) {
  editMode.value = true
  currentDocument.value = record
  documentForm.value = {
    title: record.title,
    speaker: record.speaker,
    content: record.content,
    lectureTime: dayjs(record.lectureTime),
    location: record.location,
    contactInfo: record.contactInfo || '',
    summary: record.summary || '',
    coverImage: record.coverImage || '',
    status: record.status,
    enableDownload: (record as any).enableDownload !== undefined ? (record as any).enableDownload : true,
    downloadFormat: (record as any).downloadFormat || 'doc',
    totalParticipants: (record as any).totalParticipants || 0,
    actualParticipants: (record as any).actualParticipants || 0,
    leaveParticipants: (record as any).leaveParticipants || 0,
    officialLeave: (record as any).officialLeave || 0,
    personalLeave: (record as any).personalLeave || 0,
    absentParticipants: (record as any).absentParticipants || 0,
    participants: (record as any).participants || [],
    attendees: (record as any).attendees || []
  }
  editVisible.value = true
}

async function submitDocument() {
  try {
    submitting.value = true

    if (!documentForm.value.title.trim()) {
      message.error('请输入宣讲主题')
      return
    }
    if (!documentForm.value.speaker.trim()) {
      message.error('请输入主讲人')
      return
    }
    if (!documentForm.value.content.trim()) {
      message.error('请输入宣讲内容')
      return
    }
    if (!documentForm.value.lectureTime) {
      message.error('请选择宣讲时间')
      return
    }
    if (!documentForm.value.location.trim()) {
      message.error('请输入宣讲地点')
      return
    }

    const documentData = {
      ...documentForm.value,
      lectureTime: documentForm.value.lectureTime.format('YYYY-MM-DD HH:mm:ss')
    }

    let success = false
    if (editMode.value && currentDocument.value) {
      success = await yulifacetoFaceStore.updateDocument(currentDocument.value.id, documentData)
    } else {
      success = await yulifacetoFaceStore.createDocument(documentData)
    }

    if (success) {
      message.success(editMode.value ? '纪实更新成功' : '纪实创建成功')
      editVisible.value = false
      await refreshData()
    } else {
      message.error(editMode.value ? '纪实更新失败' : '纪实创建失败')
    }
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

async function publishDocument(record: DocumentRecord) {
  try {
    const success = await yulifacetoFaceStore.publishDocument(record.id)
    if (success) {
      message.success('纪实发布成功')
      await refreshData()
    } else {
      message.error('纪实发布失败')
    }
  } catch (error) {
    message.error('发布失败，请重试')
  }
}

function deleteDocument(record: DocumentRecord) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除纪实"${record.title}"吗？`,
    onOk: async () => {
      try {
        const success = await yulifacetoFaceStore.deleteDocument(record.id)
        if (success) {
          message.success('纪实删除成功')
          await refreshData()
        } else {
          message.error('纪实删除失败')
        }
      } catch (error) {
        message.error('删除失败，请重试')
      }
    }
  })
}

async function restoreDocument(record: DocumentRecord) {
  Modal.confirm({
    title: '确认恢复',
    content: `确定要恢复纪实"${record.title}"吗？恢复后将变为草稿状态。`,
    onOk: async () => {
      try {
        const success = await yulifacetoFaceStore.restoreDocument(record.id)
        if (success) {
          message.success('纪实恢复成功')
          await refreshData()
        } else {
          message.error('纪实恢复失败')
        }
      } catch (error) {
        message.error('恢复失败，请重试')
      }
    }
  })
}

async function downloadDocument(record: DocumentRecord) {
  try {
    message.loading('正在准备下载...', 1)
    
    // 检查是否允许下载
    const enableDownload = (record as any).enableDownload !== undefined ? (record as any).enableDownload : true
    if (!enableDownload) {
      message.warning('该纪实不支持下载')
      return
    }
    
    // 模拟下载逻辑 - 实际项目中应该调用后端API获取下载链接
    const success = await yulifacetoFaceStore.downloadDocument(record.id)
    
    if (success) {
      // 获取下载格式
      const downloadFormat = (record as any).downloadFormat || 'doc'
      
      // 创建下载内容
      const downloadContent = `
纪实标题：${record.title}
主讲人：${record.speaker}
宣讲时间：${record.lectureTime}
宣讲地点：${record.location}
联系方式：${record.contactInfo || '无'}

宣讲内容：
${record.content}

创建时间：${record.createTime}
发布时间：${record.publishTime || ''}
      `.trim()
      
      // 根据格式设置不同的MIME类型和文件扩展名
      let mimeType = 'text/plain;charset=utf-8'
      let fileExtension = 'txt'
      
      switch (downloadFormat) {
        case 'doc':
        case 'docx':
          mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          fileExtension = downloadFormat
          break
        case 'pdf':
          mimeType = 'application/pdf'
          fileExtension = 'pdf'
          break
        case 'txt':
        default:
          mimeType = 'text/plain;charset=utf-8'
          fileExtension = 'txt'
          break
      }
      
      const blob = new Blob([downloadContent], { type: mimeType })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${record.title}_纪实文档.${fileExtension}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success(`纪实下载成功 (${downloadFormat.toUpperCase()}格式)`)
      
      // 更新下载次数
      await refreshData()
    } else {
      message.error('纪实下载失败')
    }
  } catch (error) {
    message.error('下载失败，请重试')
  }
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作
async function batchPublish() {
  try {
    message.info('批量发布功能开发中...')
    // 实际实现中可以调用批量发布接口
  } catch (error) {
    message.error('批量发布失败')
  }
}

function exportDocuments() {
  message.info('数据导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await yulifacetoFaceStore.fetchDocumentList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    title: '',
    speaker: '',
    location: '',
    status: undefined,
    dateRange: undefined
  }
  yulifacetoFaceStore.fetchDocumentList()
}

// 人员选择相关方法
function showParticipantModal() {
  selectedPersonnel.value = documentForm.value.participants.map(p => p.id)
  participantModalVisible.value = true
}

function showAttendeeModal() {
  selectedPersonnel.value = documentForm.value.attendees.map(a => a.id)
  attendeeModalVisible.value = true
}

function showExternalPersonnelModal() {
  externalPersonnelForm.value = {
    name: '',
    position: '',
    department: '',
    type: 'participant'
  }
  externalPersonnelModalVisible.value = true
}

function confirmParticipants(selectedKeys: string[]) {
  const selectedItems = mockPersonnelList.value.filter(item => 
    selectedKeys.includes(item.key)
  )
  documentForm.value.participants = selectedItems.map(item => ({
    id: item.key,
    name: item.name,
    position: item.position,
    department: item.department,
    isExternal: false
  }))
  participantModalVisible.value = false
  updateParticipantStats()
}

function confirmAttendees(selectedKeys: string[]) {
  const selectedItems = mockPersonnelList.value.filter(item => 
    selectedKeys.includes(item.key)
  )
  documentForm.value.attendees = selectedItems.map(item => ({
    id: item.key,
    name: item.name,
    position: item.position,
    department: item.department,
    isExternal: false
  }))
  attendeeModalVisible.value = false
}

function confirmExternalPersonnel(form: {name: string, position: string, department: string, type: 'participant' | 'attendee'}) {
  if (!form.name.trim()) {
    message.error('请输入姓名')
    return
  }
  
  const externalPerson = {
    id: `external_${Date.now()}`,
    name: form.name,
    position: form.position,
    department: form.department,
    isExternal: true
  }
  
  if (form.type === 'participant') {
    documentForm.value.participants.push(externalPerson)
    updateParticipantStats()
  } else {
    documentForm.value.attendees.push(externalPerson)
  }
  
  externalPersonnelModalVisible.value = false
  message.success('人员添加成功')
}

function removeParticipant(index: number) {
  documentForm.value.participants.splice(index, 1)
  updateParticipantStats()
}

function removeAttendee(index: number) {
  documentForm.value.attendees.splice(index, 1)
}

function updateParticipantStats() {
  // 这里可以根据实际需求更新统计数据
  documentForm.value.totalParticipants = documentForm.value.participants.length
  documentForm.value.actualParticipants = documentForm.value.participants.length
  documentForm.value.leaveParticipants = 0
  documentForm.value.officialLeave = 0
  documentForm.value.personalLeave = 0
  documentForm.value.absentParticipants = 0
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.document-management-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .title-cell {
      .title-text {
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 4px;
      }

      .title-meta {
        .summary {
          color: #666;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .stats-cell {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #666;

        .anticon {
          font-size: 11px;
        }
      }
    }
  }

  .document-detail {
    .content-preview {
      .content-text {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 6px;
        line-height: 1.6;
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      .content-image {
        text-align: center;

        img {
          max-width: 100%;
          max-height: 300px;
          border-radius: 6px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .document-form {
    .ant-form-item {
      margin-bottom: 16px;
    }

    .personnel-section {
      .personnel-summary {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #666;
        line-height: 1.5;
      }

      .personnel-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .add-personnel-btn {
          color: #1890ff;
          padding: 0;
          height: auto;
          font-size: 14px;
        }

        .add-external-btn {
          color: #52c41a;
          padding: 0;
          height: auto;
          font-size: 14px;
        }

        .personnel-count {
          color: #999;
          font-size: 12px;
        }
      }

      .personnel-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .personnel-tag {
          margin: 0;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          line-height: 1.4;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .document-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .stats-cell {
      flex-direction: row;
      justify-content: space-around;
    }

    .title-cell {
      .title-text {
        font-size: 13px;
      }

      .summary {
        font-size: 11px;
      }
    }
  }
}
</style>
