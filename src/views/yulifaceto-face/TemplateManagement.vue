<template>
  <div class="template-management-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>模板管理</h2>
        <p>纪实模板创建、编辑、应用管理，支持模板设计、样式配置、共享管理等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建模板
          </a-button>
          <a-button @click="importTemplate">
            <template #icon><import-outlined /></template>
            导入模板
          </a-button>
          <a-button @click="exportTemplates" :disabled="selectedRowKeys.length === 0">
            <template #icon><export-outlined /></template>
            导出模板
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 模板统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="模板总数"
              :value="yulifacetoFaceStore.totalTemplates"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <layout-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="共享模板"
              :value="yulifacetoFaceStore.sharedTemplates"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <share-alt-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="使用次数"
              :value="getTotalUsageCount()"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <eye-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="模板分类"
              :value="getTemplateCategoryCount()"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <appstore-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="模板名称" class="form-item-full">
                <a-input v-model:value="searchForm.name" placeholder="请输入模板名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="模板分类" class="form-item-full">
                <a-select v-model:value="searchForm.category" placeholder="请选择模板分类" allow-clear>
                  <a-select-option v-for="item in TemplateCategoryOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="创建人" class="form-item-full">
                <a-input v-model:value="searchForm.creator" placeholder="请输入创建人" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="共享状态" class="form-item-full">
                <a-select v-model:value="searchForm.isShared" placeholder="请选择共享状态" allow-clear>
                  <a-select-option :value="true">已共享</a-select-option>
                  <a-select-option :value="false">未共享</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="创建时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 模板展示区域 -->
    <div class="template-grid-section">
      <a-card>
        <template #title>
          <div class="grid-header">
            <span>模板库</span>
            <span class="record-count">共 {{ filteredTemplateList.length }} 个模板</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterCategory" placeholder="分类筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in TemplateCategoryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-radio-group v-model:value="viewMode" button-style="solid" size="small">
              <a-radio-button value="grid">
                <appstore-outlined />
              </a-radio-button>
              <a-radio-button value="list">
                <unordered-list-outlined />
              </a-radio-button>
            </a-radio-group>
          </a-space>
        </template>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="template-grid">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="template in filteredTemplateList" :key="template.id">
              <a-card 
                class="template-card" 
                hoverable
                :class="{ selected: selectedRowKeys.includes(template.id) }"
                @click="selectTemplate(template)"
              >
                <template #cover>
                  <div class="template-preview">
                    <div class="preview-content" :style="getPreviewStyle(template)">
                      <div class="preview-title">{{ template.name }}</div>
                      <div class="preview-text">示例内容</div>
                    </div>
                  </div>
                </template>
                <template #actions>
                  <eye-outlined key="preview" @click.stop="previewTemplate(template)" />
                  <edit-outlined key="edit" @click.stop="showEditModal(template)" />
                  <copy-outlined key="copy" @click.stop="copyTemplate(template)" />
                  <delete-outlined key="delete" @click.stop="deleteTemplate(template)" />
                </template>
                <a-card-meta>
                  <template #title>
                    <div class="template-title">
                      <span>{{ template.name }}</span>
                      <a-tag :color="getTemplateCategoryColor(template.category)" size="small">
                        {{ getTemplateCategoryText(template.category) }}
                      </a-tag>
                    </div>
                  </template>
                  <template #description>
                    <div class="template-desc">{{ template.description }}</div>
                    <div class="template-meta">
                      <span class="meta-item">
                        <user-outlined />
                        {{ template.creator }}
                      </span>
                      <span class="meta-item">
                        <eye-outlined />
                        {{ template.usageCount }} 次使用
                      </span>
                      <span class="meta-item" v-if="template.isShared">
                        <share-alt-outlined />
                        已共享
                      </span>
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 列表视图 -->
        <div v-else class="template-list">
          <a-table
            :columns="columns"
            :data-source="filteredTemplateList"
            :loading="yulifacetoFaceStore.loading"
            :pagination="paginationConfig"
            :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'index'">
                {{ getRowIndex(record) }}
              </template>
              <template v-else-if="column.key === 'name'">
                <div class="name-cell">
                  <span class="name-text">{{ record.name }}</span>
                  <div class="name-meta">
                    <a-tag :color="getTemplateCategoryColor(record.category)" size="small">
                      {{ getTemplateCategoryText(record.category) }}
                    </a-tag>
                    <a-tag v-if="record.isShared" color="green" size="small">已共享</a-tag>
                  </div>
                </div>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" size="small" @click="previewTemplate(record)">预览</a-button>
                  <a-button type="link" size="small" @click="showEditModal(record)">编辑</a-button>
                  <a-button type="link" size="small" @click="copyTemplate(record)">复制</a-button>
                  <a-button type="link" size="small" @click="applyTemplate(record)">应用</a-button>
                  <a-button type="link" size="small" danger @click="deleteTemplate(record)">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 模板预览弹窗 -->
    <a-modal 
      title="模板预览" 
      :visible="previewVisible" 
      @cancel="previewVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="template-preview-modal" v-if="currentTemplate">
        <div class="preview-header">
          <h3>{{ currentTemplate.name }}</h3>
          <p>{{ currentTemplate.description }}</p>
        </div>
        <div class="preview-body">
          <div class="preview-canvas" :style="getCanvasStyle(currentTemplate)">
            <div class="canvas-content">
              <h2 class="preview-title">{{ currentTemplate.name }}</h2>
              <div class="preview-layout">
                <div class="layout-section" v-for="(placeholder, index) in currentTemplate.placeholders" :key="index">
                  <div class="placeholder-item" :style="getPlaceholderStyle(placeholder)">
                    <span class="placeholder-label">{{ placeholder.name }}</span>
                    <span class="placeholder-type">{{ getPlaceholderTypeText(placeholder.type) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="preview-footer">
          <a-space>
            <a-button @click="previewVisible = false">关闭</a-button>
            <a-button type="primary" @click="applyTemplate(currentTemplate)">应用模板</a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 创建/编辑模板弹窗 -->
    <a-modal 
      :title="editMode ? '编辑模板' : '新建模板'" 
      :visible="editVisible" 
      @cancel="editVisible = false" 
      @ok="submitTemplate"
      :confirm-loading="submitting"
      width="700px"
    >
      <div class="template-form">
        <a-form :model="templateForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="模板名称" required>
            <a-input 
              v-model:value="templateForm.name" 
              placeholder="请输入模板名称"
              :maxlength="50"
              show-count
            />
          </a-form-item>
          <a-form-item label="模板描述" required>
            <a-textarea 
              v-model:value="templateForm.description" 
              :rows="3" 
              placeholder="请输入模板描述"
              :maxlength="200"
              show-count
            />
          </a-form-item>
          <a-form-item label="模板分类" required>
            <a-select v-model:value="templateForm.category" placeholder="请选择模板分类">
              <a-select-option v-for="item in TemplateCategoryOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="是否共享">
            <a-switch v-model:checked="templateForm.isShared" />
            <span style="margin-left: 8px; color: #666;">共享后其他用户可以使用此模板</span>
          </a-form-item>
          
          <a-divider>样式配置</a-divider>
          
          <a-form-item label="主色调">
            <a-input v-model:value="templateForm.primaryColor" placeholder="#1890ff" />
          </a-form-item>
          <a-form-item label="背景色">
            <a-input v-model:value="templateForm.backgroundColor" placeholder="#ffffff" />
          </a-form-item>
          <a-form-item label="文字颜色">
            <a-input v-model:value="templateForm.textColor" placeholder="#333333" />
          </a-form-item>
          <a-form-item label="标题字体">
            <a-select v-model:value="templateForm.titleFont" placeholder="请选择标题字体">
              <a-select-option value="Microsoft YaHei">微软雅黑</a-select-option>
              <a-select-option value="SimHei">黑体</a-select-option>
              <a-select-option value="SimSun">宋体</a-select-option>
              <a-select-option value="KaiTi">楷体</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="标题大小">
            <a-input-number v-model:value="templateForm.titleSize" :min="12" :max="48" placeholder="24" />
            <span style="margin-left: 8px;">px</span>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useYulifacetoFaceStore } from '@/store/modules/yulifaceto-face'
import type { DocumentTemplate, TemplateSearchParams } from '@/types/yulifaceto-face'
import {
  TemplateCategoryTextMap,
  TemplateCategoryColorMap
} from '@/types/yulifaceto-face'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  ReloadOutlined,
  LayoutOutlined,
  ShareAltOutlined,
  EyeOutlined,
  AppstoreOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  UserOutlined,
  UnorderedListOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const yulifacetoFaceStore = useYulifacetoFaceStore()

// 模板分类选项
const TemplateCategoryOptions = [
  { label: '标准模板', value: 1 },
  { label: '会议模板', value: 2 },
  { label: '活动模板', value: 3 },
  { label: '自定义模板', value: 4 }
]

// 响应式数据
const searchForm = ref<TemplateSearchParams>({
  name: '',
  category: undefined,
  creator: '',
  isShared: undefined,
  dateRange: undefined
})

const previewVisible = ref(false)
const editVisible = ref(false)
const editMode = ref(false)
const submitting = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterCategory = ref<number | undefined>(undefined)
const viewMode = ref<'grid' | 'list'>('grid')

// 当前选中的模板
const currentTemplate = ref<DocumentTemplate | null>(null)

// 模板表单数据
const templateForm = ref({
  name: '',
  description: '',
  category: 1,
  isShared: false,
  primaryColor: '#1890ff',
  backgroundColor: '#ffffff',
  textColor: '#333333',
  titleFont: 'Microsoft YaHei',
  titleSize: 24
})

// 计算属性
const filteredTemplateList = computed(() => {
  let filtered = [...yulifacetoFaceStore.templateList]

  if (filterCategory.value !== undefined) {
    filtered = filtered.filter(item => item.category === filterCategory.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '模板名称',
    key: 'name',
    width: 200
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 250
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    key: 'creator',
    width: 100
  },
  {
    title: '使用次数',
    dataIndex: 'usageCount',
    key: 'usageCount',
    width: 100,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 12,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 个模板`
}

// 方法定义
function getRowIndex(record: DocumentTemplate) {
  return filteredTemplateList.value.findIndex(item => item.id === record.id) + 1
}

function getTemplateCategoryText(category: number) {
  return TemplateCategoryTextMap[category as keyof typeof TemplateCategoryTextMap] || '未知'
}

function getTemplateCategoryColor(category: number) {
  return TemplateCategoryColorMap[category as keyof typeof TemplateCategoryColorMap] || 'default'
}

function getTotalUsageCount() {
  return yulifacetoFaceStore.templateList.reduce((sum, item) => sum + item.usageCount, 0)
}

function getTemplateCategoryCount() {
  const categories = new Set(yulifacetoFaceStore.templateList.map(item => item.category))
  return categories.size
}

function getPreviewStyle(template: DocumentTemplate) {
  return {
    backgroundColor: template.style.colors.background,
    color: template.style.colors.text,
    borderColor: template.style.colors.primary
  }
}

function getCanvasStyle(template: DocumentTemplate) {
  return {
    backgroundColor: template.style.colors.background,
    color: template.style.colors.text,
    fontFamily: template.style.fonts.contentFont,
    fontSize: template.style.fonts.contentSize + 'px',
    lineHeight: template.style.fonts.lineHeight
  }
}

function getPlaceholderStyle(placeholder: any) {
  return {
    border: '2px dashed #d9d9d9',
    padding: '8px',
    margin: '4px 0',
    borderRadius: '4px',
    backgroundColor: '#fafafa'
  }
}

function getPlaceholderTypeText(type: number) {
  const typeMap = { 1: '文本', 2: '图片', 3: '视频', 4: '日期', 5: '链接' }
  return typeMap[type as keyof typeof typeMap] || '未知'
}

async function refreshData() {
  await yulifacetoFaceStore.fetchTemplateList()
  message.success('数据刷新成功')
}

function selectTemplate(template: DocumentTemplate) {
  const index = selectedRowKeys.value.indexOf(template.id)
  if (index > -1) {
    selectedRowKeys.value.splice(index, 1)
  } else {
    selectedRowKeys.value.push(template.id)
  }
}

function previewTemplate(template: DocumentTemplate) {
  currentTemplate.value = template
  previewVisible.value = true
}

function showCreateModal() {
  editMode.value = false
  templateForm.value = {
    name: '',
    description: '',
    category: 1,
    isShared: false,
    primaryColor: '#1890ff',
    backgroundColor: '#ffffff',
    textColor: '#333333',
    titleFont: 'Microsoft YaHei',
    titleSize: 24
  }
  editVisible.value = true
}

function showEditModal(template: DocumentTemplate) {
  editMode.value = true
  currentTemplate.value = template
  templateForm.value = {
    name: template.name,
    description: template.description,
    category: template.category,
    isShared: template.isShared,
    primaryColor: template.style.colors.primary,
    backgroundColor: template.style.colors.background,
    textColor: template.style.colors.text,
    titleFont: template.style.fonts.titleFont,
    titleSize: template.style.fonts.titleSize
  }
  editVisible.value = true
}

async function submitTemplate() {
  try {
    submitting.value = true

    if (!templateForm.value.name.trim()) {
      message.error('请输入模板名称')
      return
    }
    if (!templateForm.value.description.trim()) {
      message.error('请输入模板描述')
      return
    }

    const templateData = {
      name: templateForm.value.name,
      description: templateForm.value.description,
      category: templateForm.value.category,
      isShared: templateForm.value.isShared,
      style: {
        id: 1,
        name: '自定义样式',
        colors: {
          primary: templateForm.value.primaryColor,
          secondary: '#f0f0f0',
          background: templateForm.value.backgroundColor,
          text: templateForm.value.textColor,
          accent: '#52c41a'
        },
        fonts: {
          titleFont: templateForm.value.titleFont,
          contentFont: templateForm.value.titleFont,
          titleSize: templateForm.value.titleSize,
          contentSize: 14,
          lineHeight: 1.6
        },
        spacing: {
          margin: 20,
          padding: 16,
          lineSpacing: 1.6,
          paragraphSpacing: 12
        },
        borders: {
          width: 1,
          style: 'solid',
          color: '#d9d9d9',
          radius: 4
        }
      }
    }

    let success = false
    if (editMode.value && currentTemplate.value) {
      success = await yulifacetoFaceStore.updateTemplate(currentTemplate.value.id, templateData)
    } else {
      success = await yulifacetoFaceStore.createTemplate(templateData)
    }

    if (success) {
      message.success(editMode.value ? '模板更新成功' : '模板创建成功')
      editVisible.value = false
      await refreshData()
    } else {
      message.error(editMode.value ? '模板更新失败' : '模板创建失败')
    }
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

function copyTemplate(template: DocumentTemplate) {
  message.success(`模板"${template.name}"已复制`)
  // 实际实现中可以创建模板副本
}

function applyTemplate(template: DocumentTemplate) {
  message.success(`应用模板"${template.name}"`)
  router.push('/yulifaceto-face/document-management?action=create&templateId=' + template.id)
}

function deleteTemplate(template: DocumentTemplate) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？`,
    onOk: async () => {
      try {
        const success = await yulifacetoFaceStore.deleteTemplate(template.id)
        if (success) {
          message.success('模板删除成功')
          await refreshData()
        } else {
          message.error('模板删除失败')
        }
      } catch (error) {
        message.error('删除失败，请重试')
      }
    }
  })
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

function importTemplate() {
  message.info('模板导入功能开发中...')
}

function exportTemplates() {
  message.info('模板导出功能开发中...')
}

// 搜索和重置
async function handleSearch() {
  await yulifacetoFaceStore.fetchTemplateList(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    name: '',
    category: undefined,
    creator: '',
    isShared: undefined,
    dateRange: undefined
  }
  yulifacetoFaceStore.fetchTemplateList()
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.template-management-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .template-grid-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .grid-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .template-grid {
    .template-card {
      transition: all 0.3s ease;
      border-radius: 8px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      &.selected {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .template-preview {
        height: 120px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;

        .preview-content {
          text-align: center;
          color: white;
          padding: 16px;
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);

          .preview-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
          }

          .preview-text {
            font-size: 12px;
            opacity: 0.8;
          }
        }
      }

      .template-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .template-desc {
        color: #666;
        font-size: 12px;
        margin-bottom: 8px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .template-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          color: #999;

          .anticon {
            font-size: 10px;
          }
        }
      }
    }
  }

  .template-list {
    .name-cell {
      .name-text {
        font-weight: 600;
        color: #333;
        display: block;
        margin-bottom: 4px;
      }

      .name-meta {
        display: flex;
        gap: 4px;
      }
    }
  }

  .template-preview-modal {
    .preview-header {
      text-align: center;
      margin-bottom: 24px;

      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 20px;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }

    .preview-body {
      margin-bottom: 24px;

      .preview-canvas {
        border: 2px solid #d9d9d9;
        border-radius: 8px;
        padding: 24px;
        background: white;
        min-height: 400px;

        .canvas-content {
          .preview-title {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
          }

          .preview-layout {
            .layout-section {
              margin-bottom: 16px;

              .placeholder-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px;
                border: 2px dashed #d9d9d9;
                border-radius: 4px;
                background: #fafafa;

                .placeholder-label {
                  font-weight: 600;
                  color: #333;
                }

                .placeholder-type {
                  font-size: 12px;
                  color: #666;
                  background: #e6f7ff;
                  padding: 2px 8px;
                  border-radius: 12px;
                }
              }
            }
          }
        }
      }
    }

    .preview-footer {
      text-align: center;
    }
  }

  .template-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .template-management-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .template-grid {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .template-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .preview-canvas {
      padding: 16px;
    }
  }
}
</style>
