<template>
  <div class="statistics-analysis-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>统计分析</h2>
        <p>理论宣讲纪实数据统计分析，支持多维度统计、图表展示、报表导出等功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="exportReport">
            <template #icon><export-outlined /></template>
            导出报表
          </a-button>
          <a-button @click="printReport">
            <template #icon><printer-outlined /></template>
            打印报表
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 时间筛选区域 -->
    <div class="time-filter-section">
      <a-card>
        <a-form layout="inline" class="filter-form">
          <a-form-item label="统计时间">
            <a-range-picker 
              v-model:value="timeRange" 
              format="YYYY-MM-DD" 
              @change="handleTimeRangeChange"
            />
          </a-form-item>
          <a-form-item label="统计维度">
            <a-select v-model:value="statisticsDimension" style="width: 120px" @change="handleDimensionChange">
              <a-select-option value="monthly">按月统计</a-select-option>
              <a-select-option value="department">按部门统计</a-select-option>
              <a-select-option value="speaker">按主讲人统计</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="generateReport">生成报表</a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 核心指标统计 -->
    <div class="core-metrics-section">
      <a-card title="核心指标">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <a-card class="metric-card">
              <a-statistic
                title="纪实总数"
                :value="yulifacetoFaceStore.totalDocuments"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <file-text-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card class="metric-card">
              <a-statistic
                title="发布率"
                :value="yulifacetoFaceStore.completionRate"
                :precision="1"
                suffix="%"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <check-circle-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card class="metric-card">
              <a-statistic
                title="平均浏览量"
                :value="getAverageViews()"
                :precision="0"
                :value-style="{ color: '#faad14' }"
              >
                <template #prefix>
                  <eye-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <a-card class="metric-card">
              <a-statistic
                title="活跃主讲人"
                :value="yulifacetoFaceStore.totalSpeakers"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <user-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section">
      <a-row :gutter="[16, 16]">
        <!-- 月度趋势图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="月度趋势" class="chart-card">
            <div class="chart-container">
              <div class="chart-placeholder">
                <line-chart-outlined class="chart-icon" />
                <div class="chart-content">
                  <h4>月度纪实发布趋势</h4>
                  <div class="trend-data">
                    <div v-for="stat in monthlyStats" :key="stat.month" class="trend-item">
                      <span class="month">{{ stat.month }}</span>
                      <span class="count">{{ stat.documentCount }}篇</span>
                      <div class="progress-bar">
                        <div 
                          class="progress-fill" 
                          :style="{ width: (stat.documentCount / getMaxMonthlyCount()) * 100 + '%' }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 部门分布图 -->
        <a-col :xs="24" :lg="12">
          <a-card title="部门分布" class="chart-card">
            <div class="chart-container">
              <div class="chart-placeholder">
                <pie-chart-outlined class="chart-icon" />
                <div class="chart-content">
                  <h4>各部门纪实分布</h4>
                  <div class="pie-data">
                    <div v-for="dept in departmentStats" :key="dept.departmentName" class="pie-item">
                      <div class="pie-legend">
                        <span class="legend-color" :style="{ backgroundColor: getDepartmentColor(dept.departmentName) }"></span>
                        <span class="legend-label">{{ dept.departmentName }}</span>
                      </div>
                      <span class="pie-value">{{ dept.totalDocuments }}篇</span>
                      <span class="pie-percent">({{ Math.round((dept.totalDocuments / yulifacetoFaceStore.totalDocuments) * 100) }}%)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 主讲人排行 -->
        <a-col :xs="24" :lg="12">
          <a-card title="主讲人排行" class="chart-card">
            <div class="chart-container">
              <div class="chart-placeholder">
                <bar-chart-outlined class="chart-icon" />
                <div class="chart-content">
                  <h4>主讲人活跃度排行</h4>
                  <div class="ranking-data">
                    <div v-for="(speaker, index) in speakerStats" :key="speaker.speakerName" class="ranking-item">
                      <div class="rank-number">{{ index + 1 }}</div>
                      <div class="speaker-info">
                        <div class="speaker-name">{{ speaker.speakerName }}</div>
                        <div class="speaker-dept">{{ speaker.department }}</div>
                      </div>
                      <div class="speaker-stats">
                        <div class="stat-item">
                          <span class="stat-label">宣讲:</span>
                          <span class="stat-value">{{ speaker.lectureCount }}次</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">纪实:</span>
                          <span class="stat-value">{{ speaker.documentCount }}篇</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 完成率分析 -->
        <a-col :xs="24" :lg="12">
          <a-card title="完成率分析" class="chart-card">
            <div class="chart-container">
              <div class="chart-placeholder">
                <fund-outlined class="chart-icon" />
                <div class="chart-content">
                  <h4>各部门完成率对比</h4>
                  <div class="completion-data">
                    <div v-for="dept in departmentStats" :key="dept.departmentName" class="completion-item">
                      <div class="dept-name">{{ dept.departmentName }}</div>
                      <div class="completion-bar">
                        <div class="bar-bg">
                          <div 
                            class="bar-fill" 
                            :style="{ 
                              width: dept.completionRate + '%',
                              backgroundColor: getCompletionColor(dept.completionRate)
                            }"
                          ></div>
                        </div>
                        <span class="completion-rate">{{ dept.completionRate }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-section">
      <a-card title="详细数据">
        <template #extra>
          <a-radio-group v-model:value="tableView" button-style="solid" size="small">
            <a-radio-button value="department">部门统计</a-radio-button>
            <a-radio-button value="speaker">主讲人统计</a-radio-button>
            <a-radio-button value="monthly">月度统计</a-radio-button>
          </a-radio-group>
        </template>

        <!-- 部门统计表格 -->
        <a-table
          v-if="tableView === 'department'"
          :columns="departmentColumns"
          :data-source="departmentStats"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'completionRate'">
              <a-progress 
                :percent="record.completionRate" 
                size="small"
                :stroke-color="getCompletionColor(record.completionRate)"
              />
            </template>
          </template>
        </a-table>

        <!-- 主讲人统计表格 -->
        <a-table
          v-if="tableView === 'speaker'"
          :columns="speakerColumns"
          :data-source="speakerStats"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'avgRating'">
              <a-rate :value="record.avgRating" disabled allow-half />
            </template>
          </template>
        </a-table>

        <!-- 月度统计表格 -->
        <a-table
          v-if="tableView === 'monthly'"
          :columns="monthlyColumns"
          :data-source="monthlyStats"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'completionRate'">
              <a-progress 
                :percent="record.completionRate" 
                size="small"
                :stroke-color="getCompletionColor(record.completionRate)"
              />
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useYulifacetoFaceStore } from '@/store/modules/yulifaceto-face'
import type { DepartmentStatistics, MonthlyStatistics, SpeakerStatistics } from '@/types/yulifaceto-face'
import {
  ExportOutlined,
  PrinterOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  EyeOutlined,
  UserOutlined,
  LineChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  FundOutlined
} from '@ant-design/icons-vue'

// 使用store
const yulifacetoFaceStore = useYulifacetoFaceStore()

// 响应式数据
const timeRange = ref<[string, string] | null>(null)
const statisticsDimension = ref('monthly')
const tableView = ref<'department' | 'speaker' | 'monthly'>('department')

// 模拟统计数据
const departmentStats = ref<DepartmentStatistics[]>([
  {
    departmentName: '市委办公室',
    totalDocuments: 2,
    publishedDocuments: 2,
    completionRate: 100,
    lectureCount: 4,
    activeSpeakers: 2
  },
  {
    departmentName: '市政府办公室',
    totalDocuments: 2,
    publishedDocuments: 1,
    completionRate: 75,
    lectureCount: 3,
    activeSpeakers: 2
  },
  {
    departmentName: '组织部',
    totalDocuments: 1,
    publishedDocuments: 0,
    completionRate: 60,
    lectureCount: 1,
    activeSpeakers: 1
  }
])

const monthlyStats = ref<MonthlyStatistics[]>([
  {
    month: '2025-06',
    documentCount: 3,
    lectureCount: 8,
    speakerCount: 3,
    completionRate: 88.5
  },
  {
    month: '2024-02',
    documentCount: 2,
    lectureCount: 4,
    speakerCount: 2,
    completionRate: 82.0
  }
])

const speakerStats = ref<SpeakerStatistics[]>([
  {
    speakerName: '张书记',
    department: '市委办公室',
    lectureCount: 3,
    documentCount: 1,
    totalViews: 156,
    avgRating: 4.8
  },
  {
    speakerName: '赵局长',
    department: '市政府办公室',
    lectureCount: 4,
    documentCount: 1,
    totalViews: 234,
    avgRating: 4.9
  },
  {
    speakerName: '杜佳佳',
    department: '市委办公室',
    lectureCount: 2,
    documentCount: 1,
    totalViews: 89,
    avgRating: 4.6
  }
])

// 表格列定义
const departmentColumns = [
  { title: '部门名称', dataIndex: 'departmentName', key: 'departmentName' },
  { title: '纪实总数', dataIndex: 'totalDocuments', key: 'totalDocuments', align: 'center' },
  { title: '已发布', dataIndex: 'publishedDocuments', key: 'publishedDocuments', align: 'center' },
  { title: '完成率', key: 'completionRate', align: 'center', width: 150 },
  { title: '宣讲次数', dataIndex: 'lectureCount', key: 'lectureCount', align: 'center' },
  { title: '活跃主讲人', dataIndex: 'activeSpeakers', key: 'activeSpeakers', align: 'center' }
]

const speakerColumns = [
  { title: '主讲人', dataIndex: 'speakerName', key: 'speakerName' },
  { title: '所属部门', dataIndex: 'department', key: 'department' },
  { title: '宣讲次数', dataIndex: 'lectureCount', key: 'lectureCount', align: 'center' },
  { title: '纪实数量', dataIndex: 'documentCount', key: 'documentCount', align: 'center' },
  { title: '总浏览量', dataIndex: 'totalViews', key: 'totalViews', align: 'center' },
  { title: '平均评分', key: 'avgRating', align: 'center', width: 150 }
]

const monthlyColumns = [
  { title: '月份', dataIndex: 'month', key: 'month' },
  { title: '纪实数量', dataIndex: 'documentCount', key: 'documentCount', align: 'center' },
  { title: '宣讲次数', dataIndex: 'lectureCount', key: 'lectureCount', align: 'center' },
  { title: '主讲人数', dataIndex: 'speakerCount', key: 'speakerCount', align: 'center' },
  { title: '完成率', key: 'completionRate', align: 'center', width: 150 }
]

// 方法定义
function getAverageViews() {
  const totalViews = yulifacetoFaceStore.totalViews
  const totalDocs = yulifacetoFaceStore.totalDocuments
  return totalDocs > 0 ? Math.round(totalViews / totalDocs) : 0
}

function getMaxMonthlyCount() {
  return Math.max(...monthlyStats.value.map(item => item.documentCount))
}

function getDepartmentColor(deptName: string) {
  const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1']
  const index = departmentStats.value.findIndex(item => item.departmentName === deptName)
  return colors[index % colors.length]
}

function getCompletionColor(rate: number) {
  if (rate >= 90) return '#52c41a'
  if (rate >= 80) return '#1890ff'
  if (rate >= 70) return '#faad14'
  if (rate >= 60) return '#fa8c16'
  return '#f5222d'
}

async function refreshData() {
  await Promise.all([
    yulifacetoFaceStore.fetchStatistics(),
    yulifacetoFaceStore.fetchDocumentList(),
    yulifacetoFaceStore.fetchTemplateList()
  ])
  message.success('数据刷新成功')
}

function handleTimeRangeChange() {
  message.info('时间范围筛选功能开发中...')
}

function handleDimensionChange() {
  message.info('统计维度切换功能开发中...')
}

function generateReport() {
  message.success('报表生成成功')
}

function exportReport() {
  message.info('报表导出功能开发中...')
}

function printReport() {
  message.info('报表打印功能开发中...')
}

// 生命周期
onMounted(async () => {
  await refreshData()
})
</script>

<style lang="scss" scoped>
.statistics-analysis-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .time-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .filter-form {
      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }

  .core-metrics-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .metric-card {
      text-align: center;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      height: 400px;

      .chart-container {
        height: 320px;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-placeholder {
          text-align: center;
          width: 100%;

          .chart-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
          }

          .chart-content {
            h4 {
              margin: 0 0 16px 0;
              color: #333;
              font-size: 16px;
            }
          }
        }
      }
    }

    .trend-data {
      .trend-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;

        .month {
          width: 80px;
          font-size: 12px;
          color: #666;
        }

        .count {
          width: 60px;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .progress-bar {
          flex: 1;
          height: 8px;
          background: #f0f0f0;
          border-radius: 4px;
          overflow: hidden;

          .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #52c41a);
            transition: width 0.3s ease;
          }
        }
      }
    }

    .pie-data {
      .pie-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 8px;
        background: #fafafa;
        border-radius: 4px;

        .pie-legend {
          display: flex;
          align-items: center;
          gap: 8px;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
          }

          .legend-label {
            font-size: 12px;
            color: #333;
          }
        }

        .pie-value {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .pie-percent {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .ranking-data {
      .ranking-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 12px;
        background: #fafafa;
        border-radius: 6px;
        gap: 12px;

        .rank-number {
          width: 24px;
          height: 24px;
          background: #1890ff;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
        }

        .speaker-info {
          flex: 1;

          .speaker-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
          }

          .speaker-dept {
            font-size: 11px;
            color: #666;
          }
        }

        .speaker-stats {
          display: flex;
          gap: 12px;

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stat-label {
              font-size: 10px;
              color: #999;
            }

            .stat-value {
              font-size: 12px;
              font-weight: 600;
              color: #333;
            }
          }
        }
      }
    }

    .completion-data {
      .completion-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;

        .dept-name {
          width: 100px;
          font-size: 12px;
          color: #333;
        }

        .completion-bar {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 8px;

          .bar-bg {
            flex: 1;
            height: 12px;
            background: #f0f0f0;
            border-radius: 6px;
            overflow: hidden;

            .bar-fill {
              height: 100%;
              transition: width 0.3s ease;
              border-radius: 6px;
            }
          }

          .completion-rate {
            width: 40px;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            text-align: right;
          }
        }
      }
    }
  }

  .data-table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .statistics-analysis-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .filter-form {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }

    .chart-card {
      height: auto;

      .chart-container {
        height: 280px;
      }
    }

    .ranking-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .speaker-stats {
        width: 100%;
        justify-content: space-around;
      }
    }

    .completion-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .completion-bar {
        width: 100%;
      }
    }

    .trend-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;

      .progress-bar {
        width: 100%;
      }
    }
  }
}
</style>
