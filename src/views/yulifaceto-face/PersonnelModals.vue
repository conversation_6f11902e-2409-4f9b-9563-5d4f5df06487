<template>
  <div>
    <!-- 参加人员选择模态框 -->
    <a-modal
      title="选择参加人员"
      :visible="participantModalVisible"
      @cancel="$emit('cancel-participant')"
      @ok="confirmParticipants"
      width="600px"
      :ok-button-props="{ disabled: selectedParticipants.length === 0 }"
    >
      <div class="personnel-modal-content">
        <div class="search-section">
          <a-input
            v-model:value="participantSearchText"
            placeholder="搜索人员姓名或职位"
            allow-clear
            style="margin-bottom: 16px"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
        
        <div class="personnel-list">
          <a-checkbox-group v-model:value="selectedParticipants" style="width: 100%">
            <div class="personnel-grid">
              <div
                v-for="person in filteredParticipantList"
                :key="person.key"
                class="personnel-item"
              >
                <a-checkbox :value="person.key">
                  <div class="person-info">
                    <div class="person-name">{{ person.name }}</div>
                    <div class="person-details">{{ person.position }} - {{ person.department }}</div>
                  </div>
                </a-checkbox>
              </div>
            </div>
          </a-checkbox-group>
        </div>
        
        <div class="selected-count">
          已选择 {{ selectedParticipants.length }} 人
        </div>
      </div>
    </a-modal>

    <!-- 列席人员选择模态框 -->
    <a-modal
      title="选择列席人员"
      :visible="attendeeModalVisible"
      @cancel="$emit('cancel-attendee')"
      @ok="confirmAttendees"
      width="600px"
      :ok-button-props="{ disabled: selectedAttendees.length === 0 }"
    >
      <div class="personnel-modal-content">
        <div class="search-section">
          <a-input
            v-model:value="attendeeSearchText"
            placeholder="搜索人员姓名或职位"
            allow-clear
            style="margin-bottom: 16px"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
        
        <div class="personnel-list">
          <a-checkbox-group v-model:value="selectedAttendees" style="width: 100%">
            <div class="personnel-grid">
              <div
                v-for="person in filteredAttendeeList"
                :key="person.key"
                class="personnel-item"
              >
                <a-checkbox :value="person.key">
                  <div class="person-info">
                    <div class="person-name">{{ person.name }}</div>
                    <div class="person-details">{{ person.position }} - {{ person.department }}</div>
                  </div>
                </a-checkbox>
              </div>
            </div>
          </a-checkbox-group>
        </div>
        
        <div class="selected-count">
          已选择 {{ selectedAttendees.length }} 人
        </div>
      </div>
    </a-modal>

    <!-- 外部人员录入模态框 -->
    <a-modal
      title="录入非本组织内人员"
      :visible="externalPersonnelModalVisible"
      @cancel="$emit('cancel-external')"
      @ok="confirmExternalPersonnel"
      width="500px"
    >
      <div class="external-personnel-form">
        <a-form :model="externalForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="人员类型" required>
            <a-radio-group v-model:value="externalForm.type">
              <a-radio value="participant">参加人员</a-radio>
              <a-radio value="attendee">列席人员</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="姓名" required>
            <a-input
              v-model:value="externalForm.name"
              placeholder="请输入姓名"
              :maxlength="20"
            />
          </a-form-item>
          <a-form-item label="职位">
            <a-input
              v-model:value="externalForm.position"
              placeholder="请输入职位"
              :maxlength="50"
            />
          </a-form-item>
          <a-form-item label="单位/部门">
            <a-input
              v-model:value="externalForm.department"
              placeholder="请输入单位或部门"
              :maxlength="100"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'

// Props
interface Props {
  participantModalVisible: boolean
  attendeeModalVisible: boolean
  externalPersonnelModalVisible: boolean
  personnelList: Array<{
    key: string
    title: string
    name: string
    position: string
    department: string
  }>
  initialSelectedPersonnel: string[]
  initialExternalForm: {
    name: string
    position: string
    department: string
    type: 'participant' | 'attendee'
  }
}

const props = withDefaults(defineProps<Props>(), {
  participantModalVisible: false,
  attendeeModalVisible: false,
  externalPersonnelModalVisible: false,
  personnelList: () => [],
  initialSelectedPersonnel: () => [],
  initialExternalForm: () => ({
    name: '',
    position: '',
    department: '',
    type: 'participant'
  })
})

// Emits
const emit = defineEmits<{
  'cancel-participant': []
  'cancel-attendee': []
  'cancel-external': []
  'confirm-participants': [selectedKeys: string[]]
  'confirm-attendees': [selectedKeys: string[]]
  'confirm-external': [form: {
    name: string
    position: string
    department: string
    type: 'participant' | 'attendee'
  }]
}>()

// 响应式数据
const participantSearchText = ref('')
const attendeeSearchText = ref('')
const selectedParticipants = ref<string[]>([])
const selectedAttendees = ref<string[]>([])

// 外部人员表单
const externalForm = ref({
  name: '',
  position: '',
  department: '',
  type: 'participant' as 'participant' | 'attendee'
})

// 计算属性 - 过滤后的人员列表
const filteredParticipantList = computed(() => {
  if (!participantSearchText.value) {
    return props.personnelList
  }
  const searchText = participantSearchText.value.toLowerCase()
  return props.personnelList.filter(person =>
    person.name.toLowerCase().includes(searchText) ||
    person.position.toLowerCase().includes(searchText) ||
    person.department.toLowerCase().includes(searchText)
  )
})

const filteredAttendeeList = computed(() => {
  if (!attendeeSearchText.value) {
    return props.personnelList
  }
  const searchText = attendeeSearchText.value.toLowerCase()
  return props.personnelList.filter(person =>
    person.name.toLowerCase().includes(searchText) ||
    person.position.toLowerCase().includes(searchText) ||
    person.department.toLowerCase().includes(searchText)
  )
})

// 监听初始选中人员变化
watch(() => props.initialSelectedPersonnel, (newVal) => {
  selectedParticipants.value = [...newVal]
  selectedAttendees.value = [...newVal]
}, { immediate: true })

// 监听外部表单初始值变化
watch(() => props.initialExternalForm, (newVal) => {
  externalForm.value = { ...newVal }
}, { immediate: true, deep: true })

// 方法
function confirmParticipants() {
  emit('confirm-participants', selectedParticipants.value)
  // 重置搜索
  participantSearchText.value = ''
}

function confirmAttendees() {
  emit('confirm-attendees', selectedAttendees.value)
  // 重置搜索
  attendeeSearchText.value = ''
}

function confirmExternalPersonnel() {
  if (!externalForm.value.name.trim()) {
    return
  }
  emit('confirm-external', { ...externalForm.value })
  // 重置表单
  externalForm.value = {
    name: '',
    position: '',
    department: '',
    type: 'participant'
  }
}
</script>

<style lang="scss" scoped>
.personnel-modal-content {
  .search-section {
    margin-bottom: 16px;
  }

  .personnel-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 12px;

    .personnel-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 12px;

      .personnel-item {
        padding: 8px 12px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
        }

        .ant-checkbox-wrapper {
          width: 100%;
          display: flex;
          align-items: center;

          .person-info {
            margin-left: 8px;
            flex: 1;

            .person-name {
              font-weight: 500;
              color: #333;
              font-size: 14px;
              margin-bottom: 2px;
            }

            .person-details {
              color: #666;
              font-size: 12px;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }

  .selected-count {
    margin-top: 12px;
    text-align: right;
    color: #1890ff;
    font-size: 14px;
    font-weight: 500;
  }
}

.external-personnel-form {
  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-radio-group {
    .ant-radio-wrapper {
      margin-right: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .personnel-modal-content {
    .personnel-list {
      max-height: 300px;
    }
  }
}
</style>