<template>
  <div class="case-submit-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>案例提交</h1>
        <p>参与案例征集活动，分享您的优秀实践经验</p>
      </div>
    </div>

    <!-- 活动选择区域 -->
    <div class="activity-selection">
      <a-card title="选择征集活动" :bordered="false">
        <a-spin :spinning="false">
          <div v-if="activities.length === 0" class="empty-state">
            <a-empty description="暂无进行中的征集活动">
              <a-button type="primary" @click="goToActivities">
                查看所有活动
              </a-button>
            </a-empty>
          </div>
          
          <div v-else class="activities-grid">
            <div 
              v-for="activity in activities" 
              :key="activity.id"
              class="activity-card"
              :class="{ 'selected': selectedActivity?.id === activity.id }"
              @click="selectActivity(activity)"
            >
              <!-- 活动封面 -->
              <div class="activity-cover">
                <img 
                  v-if="activity.coverImage" 
                  :src="activity.coverImage" 
                  :alt="activity.title"
                />
                <div v-else class="default-cover">
                  <calendar-outlined />
                </div>
              </div>

              <!-- 活动信息 -->
              <div class="activity-info">
                <h3 class="activity-title">{{ activity.title }}</h3>
                <p class="activity-theme" v-if="activity.theme">{{ activity.theme }}</p>
                <p class="activity-description">{{ truncateText(activity.description, 100) }}</p>
                
                <!-- 活动状态 -->
                <div class="activity-status">
                  <a-tag :color="getStatusColor(activity.status)">
                    {{ getStatusText(activity.status) }}
                  </a-tag>
                  <a-tag v-if="activity.priority > 2" color="red">
                    高优先级
                  </a-tag>
                </div>

                <!-- 时间信息 -->
                <div class="activity-time">
                  <div class="time-item">
                    <clock-circle-outlined />
                    <span>截止时间：{{ formatDate(activity.submitDeadline) }}</span>
                  </div>
                  <div class="time-item">
                    <team-outlined />
                    <span>已提交：{{ activity.currentSubmissions || 0 }}/{{ activity.maxSubmissions || '∞' }}</span>
                  </div>
                </div>

                <!-- 进度条 -->
                <div class="activity-progress">
                  <a-progress 
                    :percent="getProgressPercentage(activity)" 
                    :stroke-color="getProgressColor(activity)"
                    size="small"
                    :show-info="false"
                  />
                </div>

                <!-- 选择按钮 -->
                <div class="activity-actions">
                  <a-button 
                    type="primary" 
                    block
                    :disabled="!canSubmit(activity)"
                    @click.stop="startSubmit(activity)"
                  >
                    {{ getSubmitButtonText(activity) }}
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-spin>
      </a-card>
    </div>

    <!-- 我的提交记录 -->
    <div class="my-submissions">
      <a-card title="我的提交记录" :bordered="false">
        <template #extra>
          <a-button type="link" @click="goToMySubmissions">查看全部</a-button>
        </template>
        
        <a-spin :spinning="false">
          <div v-if="mySubmissions.length === 0" class="empty-state">
            <a-empty description="暂无提交记录" />
          </div>
          
          <div v-else class="submissions-list">
            <div 
              v-for="submission in mySubmissions" 
              :key="submission.id"
              class="submission-item"
            >
              <div class="submission-info">
                <h4 class="submission-title">{{ submission.title }}</h4>
                <p class="submission-activity">活动：{{ submission.activityTitle }}</p>
                <div class="submission-meta">
                  <a-tag :color="getSubmissionStatusColor(submission.status)">
                    {{ getSubmissionStatusText(submission.status) }}
                  </a-tag>
                  <span class="meta-item">
                    <clock-circle-outlined />
                    {{ formatDate(submission.submitTime || submission.createTime) }}
                  </span>
                </div>
              </div>
              
              <div class="submission-actions">
                <a-space>
                  <a-button size="small" @click="viewSubmission(submission)">
                    <template #icon><eye-outlined /></template>
                    查看
                  </a-button>
                  <a-button 
                    v-if="canEditSubmission(submission)" 
                    size="small" 
                    @click="editSubmission(submission)"
                  >
                    <template #icon><edit-outlined /></template>
                    编辑
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </a-spin>
      </a-card>
    </div>

    <!-- 提交指南 -->
    <div class="submit-guide">
      <a-card title="提交指南" :bordered="false">
        <div class="guide-content">
          <a-steps direction="vertical" size="small">
            <a-step title="选择活动" description="选择您要参与的案例征集活动" />
            <a-step title="填写信息" description="填写案例的基本信息和详细内容" />
            <a-step title="上传文件" description="上传相关的图片、文档等支撑材料" />
            <a-step title="预览提交" description="预览案例内容并确认提交" />
          </a-steps>
          
          <div class="guide-tips">
            <h4>提交要求</h4>
            <ul>
              <li>案例内容应真实、完整，具有实际应用价值</li>
              <li>提供的材料应清晰、准确，支撑案例描述</li>
              <li>遵守活动规则和要求，按时提交</li>
              <li>保证案例的原创性和知识产权合规性</li>
            </ul>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity, CaseSubmission } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateActivity, validateSubmission } from '@/utils/data-validation';
import { usePageLoading, PageDataProcessor } from '@/utils/page-optimization';
import { formatDate } from '@/utils/date';

const router = useRouter();

// 响应式数据
const activities = ref<CaseCollectionActivity[]>([]);
const mySubmissions = ref<CaseSubmission[]>([]);
const selectedActivity = ref<CaseCollectionActivity>();

// 使用页面优化工具
const pageLoading = usePageLoading();

// 方法
const loadActiveActivities = async () => {
  await pageLoading.execute(async () => {
    const response = await caseCollectionService.getActivityList({
      status: 'active',
      page: 1,
      pageSize: 20
    });

    if (response.success && response.data) {
      const paginationData = PageDataProcessor.processPaginationData(response, validateActivity);
      activities.value = paginationData.list.filter(activity => canSubmit(activity));
    } else {
      throw new Error(response.message || '加载活动列表失败');
    }
  });
};

const loadMySubmissions = async () => {
  await pageLoading.execute(async () => {
    const response = await caseCollectionService.getSubmissionList({
      page: 1,
      pageSize: 5
    });

    if (response.success && response.data) {
      const paginationData = PageDataProcessor.processPaginationData(response, validateSubmission);
      mySubmissions.value = paginationData.list;
    } else {
      throw new Error(response.message || '加载提交记录失败');
    }
  });
};

// 工具方法
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'published': 'blue',
    'active': 'green',
    'ended': 'orange',
    'cancelled': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'active': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusTexts[status] || '未知';
};

const getSubmissionStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'submitted': 'blue',
    'reviewing': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'withdrawn': 'default'
  };
  return statusColors[status] || 'default';
};

const getSubmissionStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'withdrawn': '已撤回'
  };
  return statusTexts[status] || '未知';
};

const getProgressPercentage = (activity: CaseCollectionActivity) => {
  if (!activity.maxSubmissions || activity.maxSubmissions === 0) {
    return 0;
  }
  const current = activity.currentSubmissions || 0;
  return Math.min(Math.round((current / activity.maxSubmissions) * 100), 100);
};

const getProgressColor = (activity: CaseCollectionActivity) => {
  const percentage = getProgressPercentage(activity);
  if (percentage >= 80) return '#ff4d4f';
  if (percentage >= 50) return '#faad14';
  return '#52c41a';
};

const canSubmit = (activity: CaseCollectionActivity) => {
  if (activity.status !== 'active') return false;
  
  const now = new Date();
  const deadline = new Date(activity.submitDeadline);
  if (now > deadline) return false;
  
  if (activity.maxSubmissions && activity.currentSubmissions >= activity.maxSubmissions) {
    return false;
  }
  
  return true;
};

const getSubmitButtonText = (activity: CaseCollectionActivity) => {
  if (activity.status !== 'active') return '活动未开始';
  
  const now = new Date();
  const deadline = new Date(activity.submitDeadline);
  if (now > deadline) return '已截止';
  
  if (activity.maxSubmissions && activity.currentSubmissions >= activity.maxSubmissions) {
    return '已满额';
  }
  
  return '开始提交';
};

const canEditSubmission = (submission: CaseSubmission) => {
  return ['draft', 'rejected'].includes(submission.status);
};

// 事件处理
const selectActivity = (activity: CaseCollectionActivity) => {
  selectedActivity.value = activity;
};

const startSubmit = (activity: CaseCollectionActivity) => {
  if (!canSubmit(activity)) {
    message.warning('当前活动不可提交');
    return;
  }
  
  router.push(`/case-collection/submit/form?activityId=${activity.id}`);
};

const viewSubmission = (submission: CaseSubmission) => {
  router.push(`/case-collection/submissions/${submission.id}`);
};

const editSubmission = (submission: CaseSubmission) => {
  router.push(`/case-collection/submit/form?submissionId=${submission.id}`);
};

const goToActivities = () => {
  router.push('/case-collection/activities');
};

const goToMySubmissions = () => {
  router.push('/case-collection/submissions');
};

// 生命周期
onMounted(() => {
  loadActiveActivities();
  loadMySubmissions();
});
</script>

<style scoped lang="scss">
.case-submit-index {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 16px;
      }
    }
  }

  .activity-selection {
    margin-bottom: 24px;

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }

    .activities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 24px;

      .activity-card {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        &.selected {
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        }

        .activity-cover {
          height: 160px;
          overflow: hidden;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .default-cover {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
          }
        }

        .activity-info {
          padding: 20px;

          .activity-title {
            margin: 0 0 8px 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            line-height: 1.4;
          }

          .activity-theme {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #1890ff;
            font-weight: 500;
          }

          .activity-description {
            margin: 0 0 16px 0;
            color: #595959;
            font-size: 14px;
            line-height: 1.5;
          }

          .activity-status {
            margin-bottom: 16px;
            display: flex;
            gap: 8px;
          }

          .activity-time {
            margin-bottom: 16px;

            .time-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              font-size: 12px;
              color: #8c8c8c;

              .anticon {
                margin-right: 4px;
              }

              &:last-child {
                margin-bottom: 0;
              }
            }
          }

          .activity-progress {
            margin-bottom: 16px;
          }

          .activity-actions {
            margin-top: 16px;
          }
        }
      }
    }
  }

  .my-submissions {
    margin-bottom: 24px;

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }

    .submissions-list {
      .submission-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .submission-info {
          flex: 1;

          .submission-title {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
          }

          .submission-activity {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #8c8c8c;
          }

          .submission-meta {
            display: flex;
            align-items: center;
            gap: 12px;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #8c8c8c;

              .anticon {
                color: #1890ff;
              }
            }
          }
        }

        .submission-actions {
          flex-shrink: 0;
        }
      }
    }
  }

  .submit-guide {
    .guide-content {
      .guide-tips {
        margin-top: 24px;
        padding: 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #262626;
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            line-height: 1.5;
            color: #595959;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .case-submit-index {
    padding: 16px;

    .page-header {
      padding: 16px;

      .header-content h1 {
        font-size: 24px;
      }
    }

    .activity-selection {
      .activities-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .activity-card {
          .activity-cover {
            height: 120px;
          }

          .activity-info {
            padding: 16px;

            .activity-title {
              font-size: 16px;
            }
          }
        }
      }
    }

    .my-submissions {
      .submissions-list {
        .submission-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          padding: 12px;

          .submission-actions {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
