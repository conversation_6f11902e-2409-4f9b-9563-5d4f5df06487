<template>
  <div class="case-submit-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <a-button @click="goBack" class="back-btn">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <div class="header-info">
          <h2>{{ isEdit ? '编辑案例' : '提交案例' }}</h2>
          <p v-if="selectedActivity">活动：{{ selectedActivity.title }}</p>
        </div>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button @click="saveDraft" :loading="saving">
            <template #icon><save-outlined /></template>
            保存草稿
          </a-button>
          <a-button @click="previewCase" v-if="currentStep === 3">
            <template #icon><eye-outlined /></template>
            预览
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 步骤指示器 -->
    <div class="steps-container">
      <a-card :bordered="false">
        <a-steps :current="currentStep" class="submit-steps">
          <a-step title="基本信息" description="填写案例基本信息" />
          <a-step title="内容编辑" description="编写案例详细内容" />
          <a-step title="文件上传" description="上传相关支撑材料" />
          <a-step title="预览提交" description="预览并确认提交" />
        </a-steps>
      </a-card>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <a-spin :spinning="loading">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0" class="step-panel">
          <a-card title="基本信息" :bordered="false">
            <a-form
              ref="basicFormRef"
              :model="formData"
              :rules="basicRules"
              layout="vertical"
            >
              <a-row :gutter="24">
                <a-col :span="24">
                  <a-form-item label="案例标题" name="title" required>
                    <a-input 
                      v-model:value="formData.title" 
                      placeholder="请输入案例标题"
                      show-count
                      :maxlength="100"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="案例摘要" name="summary" required>
                    <a-textarea 
                      v-model:value="formData.summary" 
                      placeholder="请简要描述案例的核心内容和价值"
                      :rows="4"
                      show-count
                      :maxlength="500"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="案例分类" name="categoryId">
                    <a-tree-select
                      v-model:value="formData.categoryId"
                      :tree-data="categoryOptions"
                      placeholder="请选择案例分类"
                      tree-default-expand-all
                      :field-names="{ label: 'name', value: 'id', children: 'children' }"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="案例标签" name="tags">
                    <a-select 
                      v-model:value="formData.tags" 
                      mode="tags"
                      placeholder="请输入案例标签，按回车添加"
                      :max-tag-count="5"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="提交者姓名" name="submitterName" required>
                    <a-input 
                      v-model:value="formData.submitterName" 
                      placeholder="请输入提交者姓名"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="所属部门" name="submitterDepartment">
                    <a-input 
                      v-model:value="formData.submitterDepartment" 
                      placeholder="请输入所属部门"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="联系电话" name="submitterContact">
                    <a-input 
                      v-model:value="formData.submitterContact" 
                      placeholder="请输入联系电话"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="邮箱地址" name="submitterEmail">
                    <a-input 
                      v-model:value="formData.submitterEmail" 
                      placeholder="请输入邮箱地址"
                      type="email"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </div>

        <!-- 步骤2：内容编辑 -->
        <div v-show="currentStep === 1" class="step-panel">
          <a-card title="案例内容" :bordered="false">
            <a-form
              ref="contentFormRef"
              :model="formData"
              :rules="contentRules"
              layout="vertical"
            >
              <a-form-item label="案例详细内容" name="content" required>
                <div class="content-editor">
                  <a-textarea 
                    v-model:value="formData.content" 
                    placeholder="请详细描述案例的背景、实施过程、取得成效等内容"
                    :rows="20"
                    show-count
                    :maxlength="10000"
                  />
                  <div class="editor-tips">
                    <h4>内容编写建议：</h4>
                    <ul>
                      <li><strong>背景介绍：</strong>说明案例产生的背景和需要解决的问题</li>
                      <li><strong>实施过程：</strong>详细描述具体的实施步骤和方法</li>
                      <li><strong>创新点：</strong>突出案例的创新性和特色</li>
                      <li><strong>取得成效：</strong>量化描述取得的成果和效益</li>
                      <li><strong>经验总结：</strong>总结可推广的经验和启示</li>
                    </ul>
                  </div>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </div>

        <!-- 步骤3：文件上传 -->
        <div v-show="currentStep === 2" class="step-panel">
          <a-card title="文件上传" :bordered="false">
            <a-form layout="vertical">
              <a-form-item label="封面图片">
                <div class="upload-section">
                  <div
                    class="upload-placeholder large"
                    :style="generateDefaultBackground(selectedActivity?.theme || '默认', 'placeholder')"
                    @click="handleCoverImageClick"
                  >
                    <div class="placeholder-content">
                      <file-outlined style="font-size: 32px; margin-bottom: 8px;" />
                      <div>点击上传封面图片</div>
                      <div class="placeholder-tips">建议尺寸：800x400px，支持JPG、PNG格式</div>
                    </div>
                  </div>
                </div>
              </a-form-item>

              <a-form-item label="案例图片">
                <div class="upload-section">
                  <div class="upload-grid">
                    <div
                      v-for="index in 10"
                      :key="index"
                      class="upload-placeholder small"
                      :style="generateDefaultBackground(selectedActivity?.theme || '默认', 'placeholder')"
                      @click="handleImageClick(index)"
                    >
                      <div class="placeholder-content">
                        <file-outlined style="font-size: 20px; margin-bottom: 4px;" />
                        <div>图片 {{ index }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="upload-tips">
                    <p>最多上传10张图片，每张不超过5MB，支持JPG、PNG格式</p>
                  </div>
                </div>
              </a-form-item>

              <a-form-item label="支撑文档">
                <div class="upload-section">
                  <div class="upload-grid">
                    <div
                      v-for="index in 10"
                      :key="index"
                      class="upload-placeholder document"
                      :style="generateDefaultBackground(selectedActivity?.theme || '默认', 'placeholder')"
                      @click="handleDocumentClick(index)"
                    >
                      <div class="placeholder-content">
                        <file-text-outlined style="font-size: 20px; margin-bottom: 4px;" />
                        <div>文档 {{ index }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="upload-tips">
                    <p>支持PDF、Word、Excel、PPT等格式，单个文件不超过20MB</p>
                  </div>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </div>

        <!-- 步骤4：预览提交 -->
        <div v-show="currentStep === 3" class="step-panel">
          <case-preview :case-data="formData" :activity="selectedActivity" />
        </div>
      </a-spin>
    </div>

    <!-- 步骤导航 -->
    <div class="step-navigation">
      <a-card :bordered="false">
        <div class="nav-actions">
          <a-space>
            <a-button v-if="currentStep > 0" @click="prevStep">
              <template #icon><arrow-left-outlined /></template>
              上一步
            </a-button>
            <a-button v-if="currentStep < 3" type="primary" @click="nextStep">
              下一步
              <template #icon><arrow-right-outlined /></template>
            </a-button>
            <a-button v-if="currentStep === 3" type="primary" @click="submitCase" :loading="submitting">
              <template #icon><check-outlined /></template>
              {{ isEdit ? '更新案例' : '提交案例' }}
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>

    <!-- 预览模态框 -->
    <a-modal
      v-model:visible="previewVisible"
      title="案例预览"
      :width="1000"
      :footer="null"
      centered
    >
      <case-preview :case-data="formData" :activity="selectedActivity" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  SaveOutlined,
  EyeOutlined,
  CheckOutlined,
  FileOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity, CaseSubmission, CaseCategory } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateActivity, validateSubmission } from '@/utils/data-validation';
import { usePageLoading, generateDefaultBackground } from '@/utils/page-optimization';
import FileUpload from '@/components/file-upload/index.vue';
import CasePreview from './components/CasePreview.vue';

const router = useRouter();
const route = useRoute();

// 响应式数据
const currentStep = ref(0);
const previewVisible = ref(false);

// 使用页面优化工具
const pageLoading = usePageLoading();

const selectedActivity = ref<CaseCollectionActivity>();
const categoryOptions = ref<CaseCategory[]>([]);

// 表单引用
const basicFormRef = ref<FormInstance>();
const contentFormRef = ref<FormInstance>();

// 文件列表
const coverImageList = ref<any[]>([]);
const imageList = ref<any[]>([]);
const attachmentList = ref<any[]>([]);

// 计算属性
const isEdit = computed(() => !!route.query.submissionId);
const activityId = computed(() => route.query.activityId as string);
const submissionId = computed(() => route.query.submissionId as string);

// 表单数据
const formData = reactive<Partial<CaseSubmission>>({
  title: '',
  summary: '',
  content: '',
  submitterName: '',
  submitterDepartment: '',
  submitterContact: '',
  submitterEmail: '',
  categoryId: undefined,
  tags: [],
  coverImage: '',
  images: [],
  attachments: '',
  status: 'draft'
});

// 表单验证规则
const basicRules = {
  title: [
    { required: true, message: '请输入案例标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应在5-100个字符之间', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入案例摘要', trigger: 'blur' },
    { min: 10, max: 500, message: '摘要长度应在10-500个字符之间', trigger: 'blur' }
  ],
  submitterName: [
    { required: true, message: '请输入提交者姓名', trigger: 'blur' }
  ]
};

const contentRules = {
  content: [
    { required: true, message: '请输入案例详细内容', trigger: 'blur' },
    { min: 100, max: 10000, message: '内容长度应在100-10000个字符之间', trigger: 'blur' }
  ]
};

// 方法
const loadActivity = async () => {
  if (!activityId.value) return;

  try {
    const response = await caseCollectionService.getActivityDetail(Number(activityId.value));
    if (response.success) {
      selectedActivity.value = response.data;
      formData.activityId = response.data.id;
      formData.activityTitle = response.data.title;
    } else {
      message.error(response.message || '加载活动信息失败');
    }
  } catch (error) {
    console.error('加载活动信息失败:', error);
    message.error('加载活动信息失败');
  }
};

const loadSubmission = async () => {
  if (!isEdit.value || !submissionId.value) return;

  try {
    loading.value = true;
    const response = await caseCollectionService.getSubmissionDetail(Number(submissionId.value));
    if (response.success) {
      Object.assign(formData, response.data);
      
      // 处理文件列表
      if (response.data.coverImage) {
        coverImageList.value = [{ url: response.data.coverImage, name: 'cover.jpg' }];
      }
      if (response.data.images) {
        imageList.value = response.data.images.map((url: string, index: number) => ({
          url,
          name: `image_${index + 1}.jpg`
        }));
      }
      if (response.data.attachments) {
        attachmentList.value = JSON.parse(response.data.attachments);
      }
    } else {
      message.error(response.message || '加载案例信息失败');
    }
  } catch (error) {
    console.error('加载案例信息失败:', error);
    message.error('加载案例信息失败');
  } finally {
    loading.value = false;
  }
};

const loadCategories = async () => {
  try {
    const response = await caseCollectionService.getAllCategories();
    if (response.success) {
      categoryOptions.value = response.data;
    }
  } catch (error) {
    console.error('加载分类失败:', error);
  }
};

const nextStep = async () => {
  try {
    if (currentStep.value === 0) {
      await basicFormRef.value?.validate();
    } else if (currentStep.value === 1) {
      await contentFormRef.value?.validate();
    }
    
    // 自动保存草稿
    await saveDraft();
    currentStep.value++;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const prevStep = () => {
  currentStep.value--;
};

const saveDraft = async () => {
  try {
    saving.value = true;
    formData.status = 'draft';
    
    if (isEdit.value) {
      const response = await caseCollectionService.updateSubmission(Number(submissionId.value), formData);
      if (response.success) {
        message.success('保存草稿成功');
      } else {
        message.error(response.message || '保存草稿失败');
      }
    } else {
      const response = await caseCollectionService.submitCase(formData);
      if (response.success) {
        message.success('保存草稿成功');
        // 更新URL为编辑模式
        router.replace(`/case-collection/submit/form?submissionId=${response.data.id}`);
      } else {
        message.error(response.message || '保存草稿失败');
      }
    }
  } catch (error) {
    console.error('保存草稿失败:', error);
    message.error('保存草稿失败');
  } finally {
    saving.value = false;
  }
};

const submitCase = async () => {
  try {
    submitting.value = true;
    
    // 验证所有表单
    await basicFormRef.value?.validate();
    await contentFormRef.value?.validate();
    
    formData.status = 'submitted';
    formData.submitTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    if (isEdit.value) {
      const response = await caseCollectionService.updateSubmission(Number(submissionId.value), formData);
      if (response.success) {
        message.success('提交案例成功');
        router.push('/case-collection/submissions');
      } else {
        message.error(response.message || '提交案例失败');
      }
    } else {
      const response = await caseCollectionService.submitCase(formData);
      if (response.success) {
        message.success('提交案例成功');
        router.push('/case-collection/submissions');
      } else {
        message.error(response.message || '提交案例失败');
      }
    }
  } catch (error) {
    console.error('提交案例失败:', error);
    message.error('提交案例失败');
  } finally {
    submitting.value = false;
  }
};

const previewCase = () => {
  previewVisible.value = true;
};

const goBack = () => {
  router.back();
};

// 文件占位符点击处理
const handleCoverImageClick = () => {
  message.info('封面图片上传功能已使用纯色占位符替代');
  // 模拟设置封面图片
  formData.coverImage = 'placeholder-cover.jpg';
};

const handleImageClick = (index: number) => {
  message.info(`案例图片 ${index} 上传功能已使用纯色占位符替代`);
  // 模拟添加图片
  if (!formData.images) {
    formData.images = [];
  }
  if (!formData.images.includes(`placeholder-image-${index}.jpg`)) {
    formData.images.push(`placeholder-image-${index}.jpg`);
  }
};

const handleDocumentClick = (index: number) => {
  message.info(`支撑文档 ${index} 上传功能已使用纯色占位符替代`);
  // 模拟添加文档
  const mockDoc = {
    name: `document-${index}.pdf`,
    url: `placeholder-document-${index}.pdf`,
    size: 1024 * 1024,
    type: 'application/pdf'
  };

  try {
    const attachments = formData.attachments ? JSON.parse(formData.attachments) : [];
    if (!attachments.find((doc: any) => doc.name === mockDoc.name)) {
      attachments.push(mockDoc);
      formData.attachments = JSON.stringify(attachments);
    }
  } catch {
    formData.attachments = JSON.stringify([mockDoc]);
  }
};

// 监听路由变化
watch(() => route.query, () => {
  if (route.query.activityId) {
    loadActivity();
  }
  if (route.query.submissionId) {
    loadSubmission();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  loadCategories();
});
</script>

<style scoped lang="scss">
.case-submit-form {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-btn {
        flex-shrink: 0;
      }

      .header-info {
        h2 {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .steps-container {
    margin-bottom: 24px;

    .submit-steps {
      padding: 24px 0;
    }
  }

  .form-content {
    margin-bottom: 24px;

    .step-panel {
      animation: fadeIn 0.3s ease-in-out;

      .content-editor {
        .editor-tips {
          margin-top: 16px;
          padding: 16px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 4px;

          h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }

          ul {
            margin: 0;
            padding-left: 20px;

            li {
              margin-bottom: 8px;
              line-height: 1.5;
              color: #595959;

              &:last-child {
                margin-bottom: 0;
              }

              strong {
                color: #262626;
              }
            }
          }
        }
      }

      .upload-section {
        .upload-tips {
          margin-top: 8px;

          p {
            margin: 0;
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }

  .step-navigation {
    .nav-actions {
      text-align: center;
      padding: 16px 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .case-submit-form {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content {
        width: 100%;

        .header-info h2 {
          font-size: 20px;
        }
      }

      .header-actions {
        width: 100%;
      }
    }

    .steps-container {
      .submit-steps {
        padding: 16px 0;

        :deep(.ant-steps-item-title) {
          font-size: 12px;
        }

        :deep(.ant-steps-item-description) {
          display: none;
        }
      }
    }

    .upload-section {
      .upload-placeholder {
        border-radius: 8px;
        cursor: pointer;
        transition: transform 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-bottom: 8px;

        &:hover {
          transform: scale(1.02);
        }

        &.large {
          width: 100%;
          height: 120px;
        }

        &.small {
          height: 80px;
        }

        &.document {
          height: 60px;
        }

        .placeholder-content {
          text-align: center;
          font-size: 14px;
          font-weight: 500;

          .placeholder-tips {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
          }
        }
      }

      .upload-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 12px;
        margin-bottom: 8px;
      }

      .upload-tips {
        font-size: 12px;
        color: #8c8c8c;
        text-align: center;
      }
    }
  }
}
</style>
