<template>
  <div class="case-preview">
    <div class="preview-header">
      <h3>案例预览</h3>
      <p class="preview-tip">请仔细检查案例信息，确认无误后提交</p>
    </div>

    <div class="preview-content">
      <!-- 活动信息 -->
      <div class="preview-section" v-if="activity">
        <h4>征集活动</h4>
        <div class="activity-info">
          <div class="activity-title">{{ activity.title }}</div>
          <div class="activity-meta">
            <a-tag :color="getActivityStatusColor(activity.status)">
              {{ getActivityStatusText(activity.status) }}
            </a-tag>
            <span class="meta-item">
              <calendar-outlined />
              截止时间：{{ formatDate(activity.submitDeadline) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="preview-section">
        <h4>基本信息</h4>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="案例标题" :span="2">
            {{ caseData.title }}
          </a-descriptions-item>
          <a-descriptions-item label="案例摘要" :span="2">
            {{ caseData.summary }}
          </a-descriptions-item>
          <a-descriptions-item label="提交者">
            {{ caseData.submitterName }}
          </a-descriptions-item>
          <a-descriptions-item label="所属部门">
            {{ caseData.submitterDepartment || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ caseData.submitterContact || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="邮箱地址">
            {{ caseData.submitterEmail || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="案例分类" v-if="getCategoryName()">
            {{ getCategoryName() }}
          </a-descriptions-item>
          <a-descriptions-item label="案例标签" v-if="caseData.tags && caseData.tags.length">
            <a-space wrap>
              <a-tag v-for="tag in caseData.tags" :key="tag" color="blue">
                {{ tag }}
              </a-tag>
            </a-space>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 案例内容 -->
      <div class="preview-section">
        <h4>案例内容</h4>
        <div class="content-display">
          <div class="content-text">{{ caseData.content }}</div>
        </div>
      </div>

      <!-- 封面图片 -->
      <div class="preview-section" v-if="caseData.coverImage">
        <h4>封面图片</h4>
        <div class="cover-display">
          <img :src="caseData.coverImage" :alt="caseData.title" />
        </div>
      </div>

      <!-- 案例图片 -->
      <div class="preview-section" v-if="caseData.images && caseData.images.length">
        <h4>案例图片</h4>
        <div class="images-display">
          <div v-for="(image, index) in caseData.images" :key="index" class="image-item">
            <img :src="image" :alt="`案例图片${index + 1}`" @click="previewImage(image)" />
          </div>
        </div>
      </div>

      <!-- 支撑文档 -->
      <div class="preview-section" v-if="getAttachments().length">
        <h4>支撑文档</h4>
        <div class="attachments-display">
          <div v-for="attachment in getAttachments()" :key="attachment.name" class="attachment-item">
            <div class="attachment-info">
              <file-outlined />
              <span class="attachment-name">{{ attachment.name }}</span>
              <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
            </div>
            <a-button type="link" size="small" @click="previewAttachment(attachment)">
              <template #icon><eye-outlined /></template>
              预览
            </a-button>
          </div>
        </div>
      </div>

      <!-- 提交信息 -->
      <div class="preview-section">
        <h4>提交信息</h4>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="提交状态">
            <a-tag :color="getStatusColor(caseData.status)">
              {{ getStatusText(caseData.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" v-if="caseData.createTime">
            {{ formatDate(caseData.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="提交时间" v-if="caseData.submitTime">
            {{ formatDate(caseData.submitTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间" v-if="caseData.updateTime">
            {{ formatDate(caseData.updateTime) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:visible="imagePreviewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  CalendarOutlined,
  FileOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity, CaseSubmission } from '@/types/case-collection';
import { formatDate } from '@/utils/date';

interface Props {
  caseData: Partial<CaseSubmission>;
  activity?: CaseCollectionActivity;
}

const props = defineProps<Props>();

// 响应式数据
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

// 计算属性
const getAttachments = () => {
  if (!props.caseData.attachments) return [];
  try {
    return JSON.parse(props.caseData.attachments);
  } catch {
    return [];
  }
};

// 方法
const getCategoryName = () => {
  // 这里应该根据categoryId获取分类名称
  // 暂时返回ID，实际应该从分类列表中查找
  return props.caseData.categoryId ? `分类${props.caseData.categoryId}` : '';
};

const getActivityStatusColor = (status?: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'published': 'blue',
    'active': 'green',
    'ended': 'orange',
    'cancelled': 'red'
  };
  return statusColors[status || ''] || 'default';
};

const getActivityStatusText = (status?: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'active': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusTexts[status || ''] || '未知';
};

const getStatusColor = (status?: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'submitted': 'blue',
    'reviewing': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'withdrawn': 'default'
  };
  return statusColors[status || ''] || 'default';
};

const getStatusText = (status?: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'withdrawn': '已撤回'
  };
  return statusTexts[status || ''] || '未知';
};

const formatFileSize = (size?: number) => {
  if (!size) return '0B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`;
};

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl;
  imagePreviewVisible.value = true;
};

const previewAttachment = (attachment: any) => {
  // 预览附件逻辑
  window.open(attachment.url, '_blank');
};
</script>

<style scoped lang="scss">
.case-preview {
  .preview-header {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    .preview-tip {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .preview-content {
    .preview-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        padding-bottom: 8px;
        border-bottom: 2px solid #1890ff;
      }

      .activity-info {
        padding: 16px;
        background: #f0f9ff;
        border: 1px solid #91d5ff;
        border-radius: 4px;

        .activity-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 8px;
        }

        .activity-meta {
          display: flex;
          align-items: center;
          gap: 16px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: #8c8c8c;

            .anticon {
              color: #1890ff;
            }
          }
        }
      }

      .content-display {
        .content-text {
          padding: 16px;
          background: #fafafa;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          white-space: pre-wrap;
          line-height: 1.6;
          min-height: 200px;
        }
      }

      .cover-display {
        img {
          max-width: 100%;
          max-height: 300px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }

      .images-display {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;

        .image-item {
          img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;

            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }

      .attachments-display {
        .attachment-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px;
          background: #fafafa;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .attachment-info {
            display: flex;
            align-items: center;
            flex: 1;

            .anticon {
              margin-right: 8px;
              color: #1890ff;
            }

            .attachment-name {
              font-weight: 500;
              margin-right: 8px;
            }

            .attachment-size {
              color: #8c8c8c;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .case-preview {
    .preview-content {
      .preview-section {
        .images-display {
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: 12px;

          .image-item img {
            height: 120px;
          }
        }

        .attachments-display {
          .attachment-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .attachment-info {
              width: 100%;
            }
          }
        }

        :deep(.ant-descriptions) {
          .ant-descriptions-item-label {
            width: 100px;
          }
        }
      }
    }
  }
}
</style>
