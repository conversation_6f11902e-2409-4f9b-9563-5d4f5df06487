<template>
  <div class="review-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <a-button @click="goBack" class="back-btn">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <div class="header-info">
          <h1>案例审核</h1>
          <p v-if="submission">{{ submission.title }}</p>
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="downloadCase">
            <template #icon><download-outlined /></template>
            下载案例
          </a-button>
          <a-button @click="viewHistory" v-if="hasReviewHistory">
            <template #icon><history-outlined /></template>
            审核历史
          </a-button>
        </a-space>
      </div>
    </div>

    <a-spin :spinning="loading">
      <div class="detail-content">
        <a-row :gutter="24">
          <!-- 左侧：案例内容 -->
          <a-col :xs="24" :lg="16">
            <!-- 案例基本信息 -->
            <a-card title="案例信息" :bordered="false" class="case-info-card">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="案例标题" :span="2">
                  {{ submission?.title }}
                </a-descriptions-item>
                <a-descriptions-item label="案例摘要" :span="2">
                  {{ submission?.summary }}
                </a-descriptions-item>
                <a-descriptions-item label="提交者">
                  {{ submission?.submitterName }}
                </a-descriptions-item>
                <a-descriptions-item label="所属部门">
                  {{ submission?.submitterDepartment }}
                </a-descriptions-item>
                <a-descriptions-item label="联系方式">
                  {{ submission?.submitterContact }}
                </a-descriptions-item>
                <a-descriptions-item label="邮箱地址">
                  {{ submission?.submitterEmail }}
                </a-descriptions-item>
                <a-descriptions-item label="提交时间" :span="2">
                  {{ formatDate(submission?.submitTime) }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>

            <!-- 案例内容 -->
            <a-card title="案例详细内容" :bordered="false" class="case-content-card">
              <div class="content-display">
                {{ submission?.content }}
              </div>
            </a-card>

            <!-- 案例图片 -->
            <a-card v-if="submission?.images?.length" title="案例图片" :bordered="false">
              <div class="case-images">
                <div v-for="(image, index) in submission.images" :key="index" class="image-item">
                  <img :src="image" :alt="`案例图片${index + 1}`" @click="previewImage(image)" />
                </div>
              </div>
            </a-card>

            <!-- 支撑文档 -->
            <a-card v-if="getAttachments().length" title="支撑文档" :bordered="false">
              <div class="attachments-list">
                <div v-for="attachment in getAttachments()" :key="attachment.name" class="attachment-item">
                  <div class="attachment-info">
                    <file-outlined />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                  </div>
                  <a-button type="link" size="small" @click="previewAttachment(attachment)">
                    <template #icon><eye-outlined /></template>
                    预览
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>

          <!-- 右侧：审核操作 -->
          <a-col :xs="24" :lg="8">
            <!-- 当前状态 -->
            <a-card title="当前状态" :bordered="false" class="status-card">
              <div class="status-info">
                <div class="status-item">
                  <span class="status-label">审核状态：</span>
                  <a-tag :color="getStatusColor(submission?.status)">
                    {{ getStatusText(submission?.status) }}
                  </a-tag>
                </div>
                <div class="status-item" v-if="submission?.reviewScore">
                  <span class="status-label">当前评分：</span>
                  <span class="status-value">{{ submission.reviewScore }}分</span>
                </div>
                <div class="status-item" v-if="submission?.reviewerName">
                  <span class="status-label">审核员：</span>
                  <span class="status-value">{{ submission.reviewerName }}</span>
                </div>
                <div class="status-item" v-if="submission?.reviewTime">
                  <span class="status-label">审核时间：</span>
                  <span class="status-value">{{ formatDate(submission.reviewTime) }}</span>
                </div>
              </div>
            </a-card>

            <!-- 审核表单 -->
            <a-card title="审核操作" :bordered="false" class="review-form-card" v-if="canReview">
              <review-form
                ref="reviewFormRef"
                :submission="submission"
                @submit="handleReviewSubmit"
                @save-draft="handleSaveDraft"
              />
            </a-card>

            <!-- 历史审核记录 -->
            <a-card v-if="reviewHistory.length" title="审核历史" :bordered="false">
              <a-timeline>
                <a-timeline-item 
                  v-for="review in reviewHistory" 
                  :key="review.id"
                  :color="getReviewResultColor(review.reviewResult)"
                >
                  <div class="review-history-item">
                    <div class="review-header">
                      <a-tag :color="getReviewResultColor(review.reviewResult)">
                        {{ getReviewResultText(review.reviewResult) }}
                      </a-tag>
                      <span class="review-score">{{ review.score }}分</span>
                    </div>
                    <div class="review-content">
                      <p><strong>审核员：</strong>{{ review.reviewerName }}</p>
                      <p><strong>审核意见：</strong>{{ review.comments }}</p>
                      <p v-if="review.suggestions"><strong>修改建议：</strong>{{ review.suggestions }}</p>
                    </div>
                    <div class="review-time">{{ formatDate(review.reviewTime) }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-spin>

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:visible="imagePreviewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  HistoryOutlined,
  FileOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission, CaseReview } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { formatDate } from '@/utils/date';
import ReviewForm from './components/ReviewForm.vue';

const router = useRouter();
const route = useRoute();

// 响应式数据
const submission = ref<CaseSubmission>();
const reviewHistory = ref<CaseReview[]>([]);
const loading = ref(false);
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

const submissionId = computed(() => Number(route.params.id));
const canReview = computed(() => 
  submission.value && ['submitted', 'reviewing'].includes(submission.value.status)
);
const hasReviewHistory = computed(() => reviewHistory.value.length > 0);

// 方法
const loadSubmission = async () => {
  try {
    loading.value = true;
    const response = await caseCollectionService.getSubmissionDetail(submissionId.value);
    
    if (response.success) {
      submission.value = response.data;
    } else {
      message.error(response.message || '加载案例详情失败');
    }
  } catch (error) {
    console.error('加载案例详情失败:', error);
    message.error('加载案例详情失败');
  } finally {
    loading.value = false;
  }
};

const loadReviewHistory = async () => {
  try {
    const response = await caseCollectionService.getReviewsBySubmissionId(submissionId.value);
    
    if (response.success) {
      reviewHistory.value = response.data;
    }
  } catch (error) {
    console.error('加载审核历史失败:', error);
  }
};

const getAttachments = () => {
  if (!submission.value?.attachments) return [];
  try {
    return JSON.parse(submission.value.attachments);
  } catch {
    return [];
  }
};

// 工具方法
const getStatusColor = (status?: string) => {
  const statusColors: Record<string, string> = {
    'submitted': 'orange',
    'reviewing': 'blue',
    'approved': 'green',
    'rejected': 'red'
  };
  return statusColors[status || ''] || 'default';
};

const getStatusText = (status?: string) => {
  const statusTexts: Record<string, string> = {
    'submitted': '待审核',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回'
  };
  return statusTexts[status || ''] || '未知';
};

const getReviewResultColor = (result: number) => {
  const colors: Record<number, string> = {
    1: 'green',
    2: 'red',
    3: 'orange'
  };
  return colors[result] || 'default';
};

const getReviewResultText = (result: number) => {
  const texts: Record<number, string> = {
    1: '通过',
    2: '驳回',
    3: '需修改'
  };
  return texts[result] || '未知';
};

const formatFileSize = (size?: number) => {
  if (!size) return '0B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`;
};

// 事件处理
const goBack = () => {
  router.back();
};

const downloadCase = () => {
  message.info('下载功能开发中...');
};

const viewHistory = () => {
  router.push(`/case-collection/review/history/${submissionId.value}`);
};

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl;
  imagePreviewVisible.value = true;
};

const previewAttachment = (attachment: any) => {
  window.open(attachment.url, '_blank');
};

const handleReviewSubmit = async (reviewData: any) => {
  try {
    const response = await caseCollectionService.createReview({
      submissionId: submissionId.value,
      ...reviewData
    });
    
    if (response.success) {
      message.success('审核提交成功');
      loadSubmission();
      loadReviewHistory();
    } else {
      message.error(response.message || '审核提交失败');
    }
  } catch (error) {
    console.error('审核提交失败:', error);
    message.error('审核提交失败');
  }
};

const handleSaveDraft = () => {
  message.success('审核草稿已保存');
};

// 生命周期
onMounted(() => {
  loadSubmission();
  loadReviewHistory();
});
</script>

<style scoped lang="scss">
.review-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-btn {
        flex-shrink: 0;
      }

      .header-info {
        h1 {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .detail-content {
    .case-info-card,
    .case-content-card {
      margin-bottom: 24px;
    }

    .content-display {
      padding: 16px;
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      white-space: pre-wrap;
      line-height: 1.6;
      min-height: 200px;
    }

    .case-images {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .image-item {
        img {
          width: 100%;
          height: 150px;
          object-fit: cover;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .attachments-list {
      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .attachment-info {
          display: flex;
          align-items: center;
          flex: 1;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }

          .attachment-name {
            font-weight: 500;
            margin-right: 8px;
          }

          .attachment-size {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
    }

    .status-card {
      margin-bottom: 24px;

      .status-info {
        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .status-label {
            color: #8c8c8c;
            font-size: 14px;
          }

          .status-value {
            font-weight: 500;
            color: #262626;
          }
        }
      }
    }

    .review-form-card {
      margin-bottom: 24px;
    }

    .review-history-item {
      .review-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .review-score {
          font-weight: 500;
          color: #fa8c16;
        }
      }

      .review-content {
        margin-bottom: 8px;

        p {
          margin: 0 0 4px 0;
          font-size: 14px;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            color: #262626;
          }
        }
      }

      .review-time {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
}

@media (max-width: 768px) {
  .review-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content {
        width: 100%;

        .header-info h1 {
          font-size: 20px;
        }
      }

      .header-actions {
        width: 100%;
      }
    }

    .detail-content {
      .case-images {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;

        .image-item img {
          height: 120px;
        }
      }

      .attachments-list {
        .attachment-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .attachment-info {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
