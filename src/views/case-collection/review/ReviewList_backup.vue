<template>
  <div class="review-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>案例审核列表</h2>
        <p>查看和审核提交的案例，支持批量操作和筛选</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="batchReview" :disabled="!selectedRowKeys.length">
            <template #icon><team-outlined /></template>
            批量审核
          </a-button>
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 审核统计 -->
    <div class="review-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="待审核"
              :value="stats.pending"
              :value-style="{ color: '#faad14' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="今日审核"
              :value="stats.todayReviewed"
              :value-style="{ color: '#1890ff' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <check-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="通过率"
              :value="stats.approvalRate"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <rise-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均评分"
              :value="stats.averageScore"
              :precision="1"
              :value-style="{ color: '#722ed1' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="案例标题" name="keyword">
            <a-input 
              v-model:value="searchForm.keyword" 
              placeholder="请输入案例标题或关键词"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="审核状态" name="status">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="submitted">待审核</a-select-option>
              <a-select-option value="reviewing">审核中</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="征集活动" name="activityId">
            <a-select 
              v-model:value="searchForm.activityId" 
              placeholder="请选择活动"
              allow-clear
              style="width: 200px"
            >
              <a-select-option 
                v-for="activity in activities" 
                :key="activity.id" 
                :value="activity.id"
              >
                {{ activity.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="提交时间" name="dateRange">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 案例列表 -->
    <div class="cases-table">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="cases"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <!-- 使用新的插槽语法 -->
          <template #bodyCell="{ column, record }">
            <!-- 案例标题 -->
            <template v-if="column.key === 'title'">
              <div class="case-title-cell">
                <div class="title-content">
                  <a @click="viewCase(record)" class="case-link">
                    {{ record.title }}
                  </a>
                  <div class="case-summary" v-if="record.summary">
                    {{ truncateText(record.summary, 80) }}
                  </div>
                </div>
                <div class="case-cover" v-if="record.coverImage">
                  <img :src="record.coverImage" :alt="record.title" />
                </div>
              </div>
            </template>

            <!-- 提交者信息 -->
              <div class="submitter-info">
                <div class="submitter-name">{{ record.submitterName }}</div>
                <div class="submitter-dept">{{ record.submitterDepartment }}</div>
                <div class="submitter-contact">{{ record.submitterContact }}</div>
              </div>
            </template>

            <!-- 活动信息 -->
            <template v-else-if="column.key === 'activity'">
              <div class="activity-info">
                <div class="activity-title">{{ record.activityTitle }}</div>
                <div class="submit-time">
                  <clock-circle-outlined />
                  {{ formatDate(record.submitTime) }}
                </div>
              </div>
            </template>

            <!-- 审核状态 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <div v-if="record.reviewScore" class="review-score">
                评分：{{ record.reviewScore }}
              </div>
            </template>

            <!-- 审核信息 -->
            <template v-else-if="column.key === 'review'">
              <div v-if="record.reviewerName" class="review-info">
                <div>审核员：{{ record.reviewerName }}</div>
                <div v-if="record.reviewTime">时间：{{ formatDate(record.reviewTime) }}</div>
                <div v-if="record.reviewComments" class="review-comments">
                  {{ truncateText(record.reviewComments, 50) }}
                </div>
              </div>
              <div v-else class="no-review">
                <span>暂未审核</span>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="查看详情">
                  <a-button type="link" size="small" @click="viewCase(record)">
                    <template #icon><eye-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-tooltip title="开始审核" v-if="canReview(record)">
                  <a-button type="primary" size="small" @click="reviewCase(record)">
                    <template #icon><audit-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-dropdown>
                  <a-button type="link" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="downloadCase(record)">
                        <download-outlined />
                        下载案例
                      </a-menu-item>
                      <a-menu-item @click="viewReviewHistory(record)" v-if="hasReviewHistory(record)">
                        <history-outlined />
                        审核历史
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="quickApprove(record)" v-if="canReview(record)">
                        <check-outlined />
                        快速通过
                      </a-menu-item>
                      <a-menu-item @click="quickReject(record)" v-if="canReview(record)" class="danger-item">
                        <close-outlined />
                        快速驳回
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
        </a-table>
      </a-card>
    </div>

    <!-- 批量审核模态框 -->
    <a-modal
      v-model:visible="batchReviewVisible"
      title="批量审核"
      :width="600"
      @ok="handleBatchReview"
      :confirm-loading="batchReviewLoading"
    >
      <batch-review-form 
        ref="batchReviewFormRef"
        :selected-cases="selectedCases"
        @submit="handleBatchReview"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import {
  ReloadOutlined,
  TeamOutlined,
  ExportOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  RiseOutlined,
  StarOutlined,
  SearchOutlined,
  ClearOutlined,
  EyeOutlined,
  AuditOutlined,
  MoreOutlined,
  DownloadOutlined,
  HistoryOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission, CaseCollectionActivity, CaseSubmissionQueryParams } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { formatDate } from '@/utils/date';
import BatchReviewForm from './components/BatchReviewForm.vue';

const router = useRouter();

// 响应式数据
const cases = ref<CaseSubmission[]>([]);
const activities = ref<CaseCollectionActivity[]>([]);
const loading = ref(false);
const statsLoading = ref(false);
const selectedRowKeys = ref<number[]>([]);
const batchReviewVisible = ref(false);
const batchReviewLoading = ref(false);

const stats = reactive({
  pending: 0,
  todayReviewed: 0,
  approvalRate: 0,
  averageScore: 0
});

const searchForm = reactive({
  keyword: '',
  status: undefined,
  activityId: undefined,
  dateRange: undefined
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例标题',
    dataIndex: 'title',
    key: 'title',
    width: 300
  },
  {
    title: '提交者',
    key: 'submitter',
    width: 150
  },
  {
    title: '征集活动',
    key: 'activity',
    width: 200
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '审核信息',
    key: 'review',
    width: 200
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
];

// 计算属性
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: CaseSubmission) => ({
    disabled: !canReview(record)
  })
}));

const selectedCases = computed(() => 
  cases.value.filter(c => selectedRowKeys.value.includes(c.id))
);

// 方法
const loadCases = async () => {
  try {
    loading.value = true;
    
    const params: CaseSubmissionQueryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status,
      activityId: searchForm.activityId
    };

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0].format('YYYY-MM-DD');
      params.endTime = searchForm.dateRange[1].format('YYYY-MM-DD');
    }

    const response = await caseCollectionService.getSubmissionList(params);
    
    if (response.success) {
      cases.value = response.data.list;
      pagination.total = response.data.total;
    } else {
      message.error(response.message || '加载案例列表失败');
    }
  } catch (error) {
    console.error('加载案例列表失败:', error);
    message.error('加载案例列表失败');
  } finally {
    loading.value = false;
  }
};

const loadActivities = async () => {
  try {
    const response = await caseCollectionService.getActivityList({
      page: 1,
      pageSize: 100
    });
    
    if (response.success) {
      activities.value = response.data.list;
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
  }
};

const loadStats = async () => {
  try {
    statsLoading.value = true;
    // 这里应该调用统计API，暂时使用模拟数据
    stats.pending = cases.value.filter(c => c.status === 'submitted').length;
    stats.todayReviewed = 15; // 模拟数据
    stats.approvalRate = 85; // 模拟数据
    stats.averageScore = 82.5; // 模拟数据
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statsLoading.value = false;
  }
};

// 工具方法
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'submitted': 'orange',
    'reviewing': 'blue',
    'approved': 'green',
    'rejected': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'submitted': '待审核',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回'
  };
  return statusTexts[status] || '未知';
};

const canReview = (caseItem: CaseSubmission) => {
  return ['submitted', 'reviewing'].includes(caseItem.status);
};

const hasReviewHistory = (caseItem: CaseSubmission) => {
  return caseItem.reviewerName || caseItem.reviewTime;
};

// 事件处理
const handleSearch = () => {
  pagination.current = 1;
  loadCases();
};

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    activityId: undefined,
    dateRange: undefined
  });
  pagination.current = 1;
  loadCases();
};

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1;
  pagination.pageSize = pag.pageSize || 10;
  loadCases();
};

const refreshData = () => {
  loadCases();
  loadStats();
};

// 导航和操作方法
const viewCase = (caseItem: CaseSubmission) => {
  router.push(`/case-collection/submissions/${caseItem.id}`);
};

const reviewCase = (caseItem: CaseSubmission) => {
  router.push(`/case-collection/review/detail/${caseItem.id}`);
};

const batchReview = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审核的案例');
    return;
  }
  batchReviewVisible.value = true;
};

const handleBatchReview = async (reviewData: any) => {
  try {
    batchReviewLoading.value = true;
    
    const response = await caseCollectionService.batchReviewCases({
      submissionIds: selectedRowKeys.value,
      reviewResult: reviewData.reviewResult,
      score: reviewData.score,
      comments: reviewData.comments,
      suggestions: reviewData.suggestions
    });
    
    if (response.success) {
      message.success('批量审核成功');
      batchReviewVisible.value = false;
      selectedRowKeys.value = [];
      refreshData();
    } else {
      message.error(response.message || '批量审核失败');
    }
  } catch (error) {
    console.error('批量审核失败:', error);
    message.error('批量审核失败');
  } finally {
    batchReviewLoading.value = false;
  }
};

const quickApprove = (caseItem: CaseSubmission) => {
  Modal.confirm({
    title: '快速通过',
    content: `确定要通过案例"${caseItem.title}"吗？`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const response = await caseCollectionService.createReview({
          submissionId: caseItem.id,
          reviewResult: 1,
          score: 80,
          comments: '快速通过审核'
        });
        
        if (response.success) {
          message.success('审核通过');
          refreshData();
        } else {
          message.error(response.message || '审核失败');
        }
      } catch (error) {
        console.error('审核失败:', error);
        message.error('审核失败');
      }
    }
  });
};

const quickReject = (caseItem: CaseSubmission) => {
  Modal.confirm({
    title: '快速驳回',
    content: `确定要驳回案例"${caseItem.title}"吗？`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await caseCollectionService.createReview({
          submissionId: caseItem.id,
          reviewResult: 2,
          score: 60,
          comments: '不符合要求，需要修改'
        });
        
        if (response.success) {
          message.success('审核驳回');
          refreshData();
        } else {
          message.error(response.message || '审核失败');
        }
      } catch (error) {
        console.error('审核失败:', error);
        message.error('审核失败');
      }
    }
  });
};

const downloadCase = (caseItem: CaseSubmission) => {
  message.info('下载功能开发中...');
};

const viewReviewHistory = (caseItem: CaseSubmission) => {
  router.push(`/case-collection/review/history/${caseItem.id}`);
};

const exportData = () => {
  message.info('导出功能开发中...');
};

// 生命周期
onMounted(() => {
  loadCases();
  loadActivities();
  loadStats();
});
</script>

<style scoped lang="scss">
.review-list {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .review-stats {
    margin-bottom: 24px;

    .ant-statistic {
      text-align: center;
    }
  }

  .search-filters {
    margin-bottom: 24px;

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .cases-table {
    .case-title-cell {
      display: flex;
      align-items: center;
      gap: 12px;

      .title-content {
        flex: 1;

        .case-link {
          font-weight: 500;
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        .case-summary {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 4px;
          line-height: 1.4;
        }
      }

      .case-cover {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .submitter-info {
      .submitter-name {
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
      }

      .submitter-dept,
      .submitter-contact {
        font-size: 12px;
        color: #8c8c8c;
        margin-bottom: 2px;
      }
    }

    .activity-info {
      .activity-title {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }

      .submit-time {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #8c8c8c;

        .anticon {
          color: #1890ff;
        }
      }
    }

    .review-score {
      font-size: 12px;
      color: #fa8c16;
      margin-top: 4px;
    }

    .review-info {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .review-comments {
        color: #8c8c8c;
      }
    }

    .no-review {
      font-size: 12px;
      color: #8c8c8c;
    }

    .danger-item {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
      }
    }
  }
}

@media (max-width: 768px) {
  .review-list {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;
        overflow-x: auto;
      }
    }

    .search-filters {
      .ant-form {
        .ant-form-item {
          width: 100%;
          margin-bottom: 12px;

          .ant-form-item-control-input {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
