<template>
  <div class="review-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>案例审核列表</h2>
        <p>查看和审核提交的案例，支持批量操作和筛选</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="batchReview" :disabled="!selectedRowKeys.length">
            <template #icon><team-outlined /></template>
            批量审核
          </a-button>
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card :bordered="false">
            <a-statistic
              title="待审核案例"
              :value="stats.pending"
              :loading="statsLoading"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card :bordered="false">
            <a-statistic
              title="今日已审核"
              :value="stats.todayReviewed"
              :loading="statsLoading"
            >
              <template #prefix>
                <check-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card :bordered="false">
            <a-statistic
              title="通过率"
              :value="stats.approvalRate"
              suffix="%"
              :precision="1"
              :loading="statsLoading"
            >
              <template #prefix>
                <rise-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card :bordered="false">
            <a-statistic
              title="平均评分"
              :value="stats.averageScore"
              :precision="1"
              :loading="statsLoading"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="关键词">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="请输入案例标题或内容关键词"
              style="width: 200px"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="审核状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="pending">待审核</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="征集活动">
            <a-select
              v-model:value="searchForm.activityId"
              placeholder="请选择活动"
              style="width: 200px"
              allow-clear
            >
              <a-select-option
                v-for="activity in activities"
                :key="activity.id"
                :value="activity.id"
              >
                {{ activity.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="提交时间">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 案例列表 -->
    <div class="cases-table">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="cases"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <div class="case-title-cell">
                <div class="title-content">
                  <a @click="viewCase(record)" class="case-link">
                    {{ record.title }}
                  </a>
                  <div class="case-summary" v-if="record.summary">
                    {{ truncateText(record.summary, 80) }}
                  </div>
                </div>
                <div class="case-cover" v-if="record.coverImage">
                  <img :src="record.coverImage" :alt="record.title" />
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'submitter'">
              <div class="submitter-info">
                <div class="submitter-name">{{ record.submitterName }}</div>
                <div class="submitter-dept">{{ record.submitterDepartment }}</div>
                <div class="submitter-contact">{{ record.submitterContact }}</div>
              </div>
            </template>

            <template v-else-if="column.key === 'activity'">
              <div class="activity-info">
                <div class="activity-title">{{ record.activityTitle }}</div>
                <div class="submit-time">
                  <clock-circle-outlined />
                  {{ formatDate(record.submitTime) }}
                </div>
              </div>
            </template>

            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <div v-if="record.reviewScore" class="review-score">
                评分：{{ record.reviewScore }}
              </div>
            </template>

            <template v-else-if="column.key === 'review'">
              <div v-if="record.reviewerName" class="review-info">
                <div>审核员：{{ record.reviewerName }}</div>
                <div v-if="record.reviewTime">时间：{{ formatDate(record.reviewTime) }}</div>
                <div v-if="record.reviewComments" class="review-comments">
                  {{ truncateText(record.reviewComments, 50) }}
                </div>
              </div>
              <div v-else class="no-review">
                <span>暂未审核</span>
              </div>
            </template>

            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="查看详情">
                  <a-button type="link" size="small" @click="viewCase(record)">
                    <template #icon><eye-outlined /></template>
                  </a-button>
                </a-tooltip>
                
                <a-tooltip title="开始审核" v-if="canReview(record)">
                  <a-button type="primary" size="small" @click="reviewCase(record)">
                    <template #icon><audit-outlined /></template>
                  </a-button>
                </a-tooltip>
                
                <a-dropdown>
                  <a-button type="link" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="downloadCase(record)">
                        <download-outlined />
                        下载案例
                      </a-menu-item>
                      <a-menu-item @click="viewReviewHistory(record)" v-if="hasReviewHistory(record)">
                        <history-outlined />
                        审核历史
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="quickApprove(record)" v-if="canReview(record)">
                        <check-outlined />
                        快速通过
                      </a-menu-item>
                      <a-menu-item @click="quickReject(record)" v-if="canReview(record)" class="danger-item">
                        <close-outlined />
                        快速驳回
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import type { CaseSubmission, CaseSubmissionStatus } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import {
  ReloadOutlined,
  TeamOutlined,
  ExportOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  RiseOutlined,
  StarOutlined,
  SearchOutlined,
  ClearOutlined,
  EyeOutlined,
  AuditOutlined,
  MoreOutlined,
  DownloadOutlined,
  HistoryOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';

const router = useRouter();

// 响应式数据
const cases = ref<CaseSubmission[]>([]);
const activities = ref([]);
const loading = ref(false);
const statsLoading = ref(false);
const selectedRowKeys = ref<number[]>([]);

const stats = reactive({
  pending: 0,
  todayReviewed: 0,
  approvalRate: 0,
  averageScore: 0
});

const searchForm = reactive({
  keyword: '',
  status: undefined as CaseSubmissionStatus | undefined,
  activityId: undefined as number | undefined,
  dateRange: undefined as any
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例标题',
    dataIndex: 'title',
    key: 'title',
    width: 300
  },
  {
    title: '提交者',
    key: 'submitter',
    width: 150
  },
  {
    title: '征集活动',
    key: 'activity',
    width: 200
  },
  {
    title: '审核状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '审核信息',
    key: 'review',
    width: 200
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
];

// 计算属性
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  }
}));

// 方法
const formatDate = (date: string) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString();
};

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const getStatusColor = (status: string) => {
  const colors = {
    pending: 'orange',
    approved: 'green',
    rejected: 'red'
  };
  return colors[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已驳回'
  };
  return texts[status] || status;
};

const canReview = (record: any) => {
  return record.status === 'pending';
};

const hasReviewHistory = (record: any) => {
  return record.reviewHistory && record.reviewHistory.length > 0;
};

const refreshData = () => {
  loadCases();
  loadStats();
};

const loadCases = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      status: searchForm.status,
      activityId: searchForm.activityId,
      keyword: searchForm.keyword,
      startTime: searchForm.dateRange?.[0]?.format('YYYY-MM-DD'),
      endTime: searchForm.dateRange?.[1]?.format('YYYY-MM-DD')
    };

    const response = await caseCollectionService.getSubmissionList(params);
    if (response.success) {
      cases.value = response.data.list;
      pagination.total = response.data.total;
    } else {
      message.error(response.message || '加载案例列表失败');
    }
  } catch (error) {
    console.error('加载案例列表失败:', error);
    message.error('加载案例列表失败');
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  statsLoading.value = true;
  try {
    const response = await caseCollectionService.getReviewStatistics();
    if (response.success) {
      stats.pending = response.data.pendingReviews || 0;
      stats.todayReviewed = response.data.todayReviews || 0;
      stats.approvalRate = response.data.approvalRate || 0;
      stats.averageScore = response.data.averageScore || 0;
    } else {
      message.error(response.message || '加载统计数据失败');
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    message.error('加载统计数据失败');
  } finally {
    statsLoading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadCases();
};

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    activityId: undefined,
    dateRange: undefined
  });
  handleSearch();
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadCases();
};

const viewCase = (record: any) => {
  router.push(`/case-collection/submissions/${record.id}`);
};

const reviewCase = (record: any) => {
  router.push(`/case-collection/review/detail/${record.id}`);
};

const batchReview = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审核的案例');
    return;
  }
  // 实现批量审核逻辑
};

const exportData = () => {
  message.info('导出功能开发中...');
};

const downloadCase = (record: any) => {
  message.info('下载功能开发中...');
};

const viewReviewHistory = (record: any) => {
  message.info('查看审核历史功能开发中...');
};

const quickApprove = (record: any) => {
  Modal.confirm({
    title: '确认快速通过',
    content: `确定要快速通过案例"${record.title}"吗？`,
    onOk() {
      message.success('案例已通过');
      loadCases();
    }
  });
};

const quickReject = (record: any) => {
  Modal.confirm({
    title: '确认快速驳回',
    content: `确定要快速驳回案例"${record.title}"吗？`,
    onOk() {
      message.success('案例已驳回');
      loadCases();
    }
  });
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style lang="scss" scoped>
.review-list {
  padding: 24px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-content {
      h2 {
        margin: 0;
        color: #262626;
      }

      p {
        margin: 4px 0 0;
        color: #8c8c8c;
      }
    }
  }

  .stats-cards {
    margin-bottom: 24px;
  }

  .search-filters {
    margin-bottom: 24px;
  }

  .case-title-cell {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-content {
      flex: 1;

      .case-link {
        font-weight: 500;
        color: #1890ff;
        cursor: pointer;

        &:hover {
          color: #40a9ff;
        }
      }

      .case-summary {
        margin-top: 4px;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 1.4;
      }
    }

    .case-cover {
      width: 40px;
      height: 40px;
      border-radius: 4px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }

  .submitter-info {
    .submitter-name {
      font-weight: 500;
      color: #262626;
    }

    .submitter-dept,
    .submitter-contact {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }

  .activity-info {
    .activity-title {
      font-weight: 500;
      color: #262626;
    }

    .submit-time {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 4px;
    }
  }

  .review-score {
    margin-top: 4px;
    font-size: 12px;
    color: #52c41a;
  }

  .review-info {
    font-size: 12px;

    div {
      margin-bottom: 2px;
      color: #8c8c8c;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .review-comments {
      color: #262626;
      font-style: italic;
    }
  }

  .no-review {
    font-size: 12px;
    color: #bfbfbf;
  }

  .danger-item {
    color: #ff4d4f !important;
  }
}
</style>
