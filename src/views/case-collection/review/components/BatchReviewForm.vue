<template>
  <div class="batch-review-form">
    <div class="selected-cases">
      <h4>选中的案例 ({{ selectedCases.length }}个)</h4>
      <div class="cases-list">
        <div v-for="caseItem in selectedCases" :key="caseItem.id" class="case-item">
          <div class="case-info">
            <span class="case-title">{{ caseItem.title }}</span>
            <span class="case-submitter">{{ caseItem.submitterName }}</span>
          </div>
        </div>
      </div>
    </div>

    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="审核结果" name="reviewResult" required>
        <a-radio-group v-model:value="formData.reviewResult">
          <a-radio :value="1">
            <check-circle-outlined style="color: #52c41a" />
            通过
          </a-radio>
          <a-radio :value="2">
            <close-circle-outlined style="color: #ff4d4f" />
            驳回
          </a-radio>
          <a-radio :value="3">
            <exclamation-circle-outlined style="color: #faad14" />
            需修改
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="评分" name="score" required>
        <a-slider
          v-model:value="formData.score"
          :min="0"
          :max="100"
          :marks="scoreMarks"
          :tooltip-formatter="(value) => `${value}分`"
        />
        <div class="score-display">
          <span class="score-value">{{ formData.score }}分</span>
          <span class="score-level">{{ getScoreLevel(formData.score) }}</span>
        </div>
      </a-form-item>

      <a-form-item label="审核意见" name="comments" required>
        <a-textarea
          v-model:value="formData.comments"
          placeholder="请输入审核意见"
          :rows="4"
          show-count
          :maxlength="500"
        />
      </a-form-item>

      <a-form-item label="修改建议" name="suggestions" v-if="formData.reviewResult !== 1">
        <a-textarea
          v-model:value="formData.suggestions"
          placeholder="请输入修改建议（可选）"
          :rows="3"
          show-count
          :maxlength="500"
        />
      </a-form-item>

      <a-form-item label="快速模板" name="template">
        <a-select
          v-model:value="selectedTemplate"
          placeholder="选择快速模板（可选）"
          allow-clear
          @change="applyTemplate"
        >
          <a-select-option value="approve">通过模板</a-select-option>
          <a-select-option value="reject_incomplete">驳回-材料不完整</a-select-option>
          <a-select-option value="reject_quality">驳回-质量不达标</a-select-option>
          <a-select-option value="revise_format">修改-格式问题</a-select-option>
          <a-select-option value="revise_content">修改-内容问题</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>

    <div class="form-actions">
      <a-space>
        <a-button @click="resetForm">重置</a-button>
        <a-button type="primary" @click="submitForm" :loading="submitting">
          确认批量审核
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission } from '@/types/case-collection';

interface Props {
  selectedCases: CaseSubmission[];
}

interface Emits {
  (e: 'submit', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);
const selectedTemplate = ref<string>();

const formData = reactive({
  reviewResult: 1,
  score: 80,
  comments: '',
  suggestions: ''
});

// 评分标记
const scoreMarks = {
  0: '0',
  20: '20',
  40: '40',
  60: '60',
  80: '80',
  100: '100'
};

// 表单验证规则
const rules = {
  reviewResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请设置评分', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '评分范围为0-100', trigger: 'change' }
  ],
  comments: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 10, max: 500, message: '审核意见长度应在10-500个字符之间', trigger: 'blur' }
  ]
};

// 快速模板
const templates = {
  approve: {
    reviewResult: 1,
    score: 85,
    comments: '案例内容完整，具有较好的实践价值和推广意义，符合征集要求，予以通过。',
    suggestions: ''
  },
  reject_incomplete: {
    reviewResult: 2,
    score: 50,
    comments: '案例材料不完整，缺少关键信息或支撑材料，不符合征集要求。',
    suggestions: '请补充完整的案例材料，包括详细的实施过程、效果数据和相关证明文件。'
  },
  reject_quality: {
    reviewResult: 2,
    score: 45,
    comments: '案例质量不达标，内容缺乏深度，实践价值有限，不符合征集标准。',
    suggestions: '请重新梳理案例内容，突出创新点和实际效果，提供更有说服力的数据支撑。'
  },
  revise_format: {
    reviewResult: 3,
    score: 70,
    comments: '案例内容基本符合要求，但格式规范性有待改进。',
    suggestions: '请按照征集活动的格式要求调整案例结构，完善图表和数据展示。'
  },
  revise_content: {
    reviewResult: 3,
    score: 65,
    comments: '案例有一定价值，但部分内容需要进一步完善和补充。',
    suggestions: '请补充案例的背景介绍、具体实施步骤和量化效果，增强案例的完整性。'
  }
};

// 方法
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '中等';
  if (score >= 60) return '及格';
  return '不及格';
};

const applyTemplate = (templateKey: string) => {
  if (!templateKey || !templates[templateKey as keyof typeof templates]) return;
  
  const template = templates[templateKey as keyof typeof templates];
  Object.assign(formData, template);
};

const resetForm = () => {
  Object.assign(formData, {
    reviewResult: 1,
    score: 80,
    comments: '',
    suggestions: ''
  });
  selectedTemplate.value = undefined;
  formRef.value?.clearValidate();
};

const submitForm = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;
    emit('submit', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
};

// 监听审核结果变化，自动调整评分
watch(() => formData.reviewResult, (newResult) => {
  if (newResult === 1 && formData.score < 60) {
    formData.score = 80; // 通过时最低80分
  } else if (newResult === 2 && formData.score >= 60) {
    formData.score = 50; // 驳回时最高50分
  } else if (newResult === 3 && (formData.score < 60 || formData.score >= 80)) {
    formData.score = 70; // 需修改时60-79分
  }
});

// 暴露方法给父组件
defineExpose({
  resetForm,
  submitForm
});
</script>

<style scoped lang="scss">
.batch-review-form {
  .selected-cases {
    margin-bottom: 24px;
    padding: 16px;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #262626;
    }

    .cases-list {
      max-height: 120px;
      overflow-y: auto;

      .case-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .case-info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .case-title {
            font-weight: 500;
            color: #262626;
            font-size: 14px;
          }

          .case-submitter {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }

  .score-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;

    .score-value {
      font-size: 18px;
      font-weight: 600;
      color: #1890ff;
    }

    .score-level {
      font-size: 14px;
      color: #8c8c8c;
    }
  }

  .form-actions {
    margin-top: 24px;
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.ant-radio-group) {
    .ant-radio-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 8px 12px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        background: #f6ffed;
      }

      &.ant-radio-wrapper-checked {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .ant-radio {
        margin-right: 8px;
      }
    }
  }

  :deep(.ant-slider) {
    .ant-slider-track {
      background: linear-gradient(to right, #ff4d4f 0%, #faad14 50%, #52c41a 100%);
    }

    .ant-slider-handle {
      border-color: #1890ff;
    }
  }
}

@media (max-width: 768px) {
  .batch-review-form {
    .selected-cases {
      .cases-list {
        max-height: 100px;

        .case-item {
          .case-info {
            .case-title {
              font-size: 13px;
            }

            .case-submitter {
              font-size: 11px;
            }
          }
        }
      }
    }

    .score-display {
      .score-value {
        font-size: 16px;
      }

      .score-level {
        font-size: 12px;
      }
    }

    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        margin-bottom: 6px;
        padding: 6px 8px;
      }
    }
  }
}
</style>
