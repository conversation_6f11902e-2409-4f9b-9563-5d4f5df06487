<template>
  <div class="review-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="审核结果" name="reviewResult" required>
        <a-radio-group v-model:value="formData.reviewResult" @change="onResultChange">
          <a-radio :value="1" class="result-option approve">
            <check-circle-outlined />
            <span>通过</span>
          </a-radio>
          <a-radio :value="2" class="result-option reject">
            <close-circle-outlined />
            <span>驳回</span>
          </a-radio>
          <a-radio :value="3" class="result-option revise">
            <exclamation-circle-outlined />
            <span>需修改</span>
          </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-form-item label="评分" name="score" required>
        <div class="score-section">
          <a-slider
            v-model:value="formData.score"
            :min="0"
            :max="100"
            :marks="scoreMarks"
            :tooltip-formatter="(value) => `${value}分`"
            @change="onScoreChange"
          />
          <div class="score-info">
            <div class="score-display">
              <span class="score-value">{{ formData.score }}分</span>
              <span class="score-level" :class="getScoreLevelClass(formData.score)">
                {{ getScoreLevel(formData.score) }}
              </span>
            </div>
            <div class="score-tips">
              <p>评分标准：90-100优秀，80-89良好，70-79中等，60-69及格，60以下不及格</p>
            </div>
          </div>
        </div>
      </a-form-item>

      <a-form-item label="审核意见" name="comments" required>
        <a-textarea
          v-model:value="formData.comments"
          placeholder="请详细说明审核意见，包括案例的优点、不足和改进建议"
          :rows="5"
          show-count
          :maxlength="1000"
        />
      </a-form-item>

      <a-form-item label="修改建议" name="suggestions" v-if="formData.reviewResult !== 1">
        <a-textarea
          v-model:value="formData.suggestions"
          placeholder="请提供具体的修改建议，帮助提交者改进案例"
          :rows="4"
          show-count
          :maxlength="1000"
        />
      </a-form-item>

      <a-form-item label="快速模板">
        <a-select
          v-model:value="selectedTemplate"
          placeholder="选择快速模板（可选）"
          allow-clear
          @change="applyTemplate"
        >
          <a-select-option value="approve_excellent">通过-优秀案例</a-select-option>
          <a-select-option value="approve_good">通过-良好案例</a-select-option>
          <a-select-option value="reject_incomplete">驳回-材料不完整</a-select-option>
          <a-select-option value="reject_quality">驳回-质量不达标</a-select-option>
          <a-select-option value="reject_irrelevant">驳回-不符合主题</a-select-option>
          <a-select-option value="revise_format">修改-格式问题</a-select-option>
          <a-select-option value="revise_content">修改-内容完善</a-select-option>
          <a-select-option value="revise_evidence">修改-证据补充</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="审核附件">
        <file-upload
          v-model:file-list="attachmentList"
          :multiple="true"
          :max-count="5"
          :max-size="10"
          accept=".pdf,.doc,.docx,.jpg,.png"
          @change="handleAttachmentsChange"
        />
        <div class="upload-tips">
          <p>可上传审核相关的文件，如评分表、详细意见等</p>
        </div>
      </a-form-item>
    </a-form>

    <div class="form-actions">
      <a-space>
        <a-button @click="resetForm">重置</a-button>
        <a-button @click="saveDraft" :loading="saving">
          <template #icon><save-outlined /></template>
          保存草稿
        </a-button>
        <a-button type="primary" @click="submitReview" :loading="submitting">
          <template #icon><check-outlined /></template>
          提交审核
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SaveOutlined,
  CheckOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission } from '@/types/case-collection';
import FileUpload from '@/components/file-upload/index.vue';

interface Props {
  submission?: CaseSubmission;
}

interface Emits {
  (e: 'submit', data: any): void;
  (e: 'save-draft', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);
const saving = ref(false);
const selectedTemplate = ref<string>();
const attachmentList = ref<any[]>([]);

const formData = reactive({
  reviewResult: 1,
  score: 80,
  comments: '',
  suggestions: '',
  attachments: ''
});

// 评分标记
const scoreMarks = {
  0: '0',
  20: '20',
  40: '40',
  60: '及格',
  80: '良好',
  100: '优秀'
};

// 表单验证规则
const rules = {
  reviewResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请设置评分', trigger: 'change' },
    { type: 'number', min: 0, max: 100, message: '评分范围为0-100', trigger: 'change' }
  ],
  comments: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 20, max: 1000, message: '审核意见长度应在20-1000个字符之间', trigger: 'blur' }
  ]
};

// 快速模板
const templates = {
  approve_excellent: {
    reviewResult: 1,
    score: 95,
    comments: '案例内容详实，创新性强，实践效果显著，具有很高的推广价值。案例结构完整，数据翔实，表述清晰，是一个优秀的实践案例。',
    suggestions: ''
  },
  approve_good: {
    reviewResult: 1,
    score: 85,
    comments: '案例内容较为完整，具有一定的实践价值和参考意义。案例描述清晰，效果明显，符合征集要求。',
    suggestions: ''
  },
  reject_incomplete: {
    reviewResult: 2,
    score: 45,
    comments: '案例材料不完整，缺少关键信息或支撑材料。案例描述过于简单，缺乏具体的实施过程和效果数据，不符合征集要求。',
    suggestions: '请补充完整的案例材料，包括详细的背景介绍、实施过程、具体做法、取得成效和经验总结等内容。'
  },
  reject_quality: {
    reviewResult: 2,
    score: 40,
    comments: '案例质量不达标，内容缺乏深度和创新性，实践价值有限。案例描述不够具体，缺乏有说服力的数据支撑。',
    suggestions: '请重新梳理案例内容，突出创新点和特色做法，提供更详细的实施过程和量化的效果数据。'
  },
  reject_irrelevant: {
    reviewResult: 2,
    score: 35,
    comments: '案例内容与征集主题不符，偏离了活动要求的范围和重点，不符合征集标准。',
    suggestions: '请重新审视征集活动的主题和要求，提交符合主题的相关案例。'
  },
  revise_format: {
    reviewResult: 3,
    score: 75,
    comments: '案例内容基本符合要求，但格式规范性有待改进。案例结构需要调整，部分内容表述不够清晰。',
    suggestions: '请按照征集活动的格式要求调整案例结构，完善标题、摘要、正文等各部分内容，提高表述的准确性和清晰度。'
  },
  revise_content: {
    reviewResult: 3,
    score: 70,
    comments: '案例有一定价值，但部分内容需要进一步完善和补充。案例的完整性和深度还有提升空间。',
    suggestions: '请补充案例的背景介绍、具体实施步骤和量化效果，增强案例的完整性和说服力。建议添加更多的实践细节和经验总结。'
  },
  revise_evidence: {
    reviewResult: 3,
    score: 68,
    comments: '案例内容基本完整，但缺乏充分的证据支撑。需要提供更多的数据和材料来证明案例的效果和价值。',
    suggestions: '请补充相关的数据、图表、证明材料等支撑文档，增强案例的可信度和说服力。建议提供前后对比数据和第三方评价。'
  }
};

// 方法
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '中等';
  if (score >= 60) return '及格';
  return '不及格';
};

const getScoreLevelClass = (score: number) => {
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 70) return 'average';
  if (score >= 60) return 'pass';
  return 'fail';
};

const onResultChange = () => {
  // 根据审核结果自动调整评分范围
  if (formData.reviewResult === 1 && formData.score < 60) {
    formData.score = 80; // 通过时最低60分，建议80分
  } else if (formData.reviewResult === 2 && formData.score >= 60) {
    formData.score = 45; // 驳回时最高59分
  } else if (formData.reviewResult === 3 && (formData.score < 60 || formData.score >= 90)) {
    formData.score = 70; // 需修改时60-89分
  }
};

const onScoreChange = () => {
  // 根据评分自动调整审核结果
  if (formData.score >= 60 && formData.reviewResult === 2) {
    formData.reviewResult = formData.score >= 80 ? 1 : 3;
  } else if (formData.score < 60 && formData.reviewResult !== 2) {
    formData.reviewResult = 2;
  }
};

const applyTemplate = (templateKey: string) => {
  if (!templateKey || !templates[templateKey as keyof typeof templates]) return;
  
  const template = templates[templateKey as keyof typeof templates];
  Object.assign(formData, template);
};

const resetForm = () => {
  Object.assign(formData, {
    reviewResult: 1,
    score: 80,
    comments: '',
    suggestions: '',
    attachments: ''
  });
  selectedTemplate.value = undefined;
  attachmentList.value = [];
  formRef.value?.clearValidate();
};

const saveDraft = async () => {
  try {
    saving.value = true;
    emit('save-draft', { ...formData });
  } finally {
    saving.value = false;
  }
};

const submitReview = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;
    emit('submit', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    submitting.value = false;
  }
};

const handleAttachmentsChange = (fileList: any[]) => {
  formData.attachments = JSON.stringify(fileList.map(file => ({
    name: file.name,
    url: file.url || file.response?.url,
    size: file.size,
    type: file.type
  })));
};

// 监听审核结果变化，清空建议字段
watch(() => formData.reviewResult, (newResult) => {
  if (newResult === 1) {
    formData.suggestions = '';
  }
});

// 暴露方法给父组件
defineExpose({
  resetForm,
  submitReview,
  saveDraft
});
</script>

<style scoped lang="scss">
.review-form {
  :deep(.ant-radio-group) {
    width: 100%;

    .ant-radio-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 12px;
      padding: 12px;
      border: 2px solid #f0f0f0;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        background: #f6ffed;
      }

      &.ant-radio-wrapper-checked {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .ant-radio {
        margin-right: 8px;
      }

      span {
        font-weight: 500;
      }

      &.approve.ant-radio-wrapper-checked {
        border-color: #52c41a;
        background: #f6ffed;
      }

      &.reject.ant-radio-wrapper-checked {
        border-color: #ff4d4f;
        background: #fff2f0;
      }

      &.revise.ant-radio-wrapper-checked {
        border-color: #faad14;
        background: #fffbe6;
      }
    }
  }

  .score-section {
    .score-info {
      margin-top: 16px;

      .score-display {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .score-value {
          font-size: 20px;
          font-weight: 600;
          color: #1890ff;
        }

        .score-level {
          font-size: 14px;
          font-weight: 500;
          padding: 4px 8px;
          border-radius: 4px;

          &.excellent {
            background: #f6ffed;
            color: #52c41a;
          }

          &.good {
            background: #e6f7ff;
            color: #1890ff;
          }

          &.average {
            background: #fffbe6;
            color: #faad14;
          }

          &.pass {
            background: #fff7e6;
            color: #fa8c16;
          }

          &.fail {
            background: #fff2f0;
            color: #ff4d4f;
          }
        }
      }

      .score-tips {
        p {
          margin: 0;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .upload-tips {
    margin-top: 8px;

    p {
      margin: 0;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .form-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
  }

  :deep(.ant-slider) {
    .ant-slider-track {
      background: linear-gradient(to right, #ff4d4f 0%, #faad14 30%, #1890ff 60%, #52c41a 100%);
    }

    .ant-slider-handle {
      border-color: #1890ff;
    }
  }
}

@media (max-width: 768px) {
  .review-form {
    .score-section {
      .score-info {
        .score-display {
          .score-value {
            font-size: 18px;
          }

          .score-level {
            font-size: 12px;
          }
        }
      }
    }

    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        margin-bottom: 8px;
        padding: 8px;
      }
    }

    .form-actions {
      text-align: center;

      .ant-space {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
</style>
