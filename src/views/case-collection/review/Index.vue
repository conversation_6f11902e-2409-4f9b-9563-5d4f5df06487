<template>
  <div class="case-review-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>案例预审管理</h1>
        <p>对提交的案例进行预审、评分和意见反馈，确保案例质量</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportReport">
            <template #icon><export-outlined /></template>
            导出报告
          </a-button>
          <a-button type="primary" @click="goToReviewList">
            <template #icon><audit-outlined /></template>
            开始审核
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 审核统计 -->
    <div class="review-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="待审核案例"
              :value="stats.pending"
              :value-style="{ color: '#faad14' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已通过"
              :value="stats.approved"
              :value-style="{ color: '#52c41a' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已驳回"
              :value="stats.rejected"
              :value-style="{ color: '#ff4d4f' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <close-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="平均评分"
              :value="stats.averageScore"
              :precision="1"
              :value-style="{ color: '#1890ff' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <star-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToReviewList">
              <div class="action-icon">
                <audit-outlined />
              </div>
              <div class="action-content">
                <h3>案例审核</h3>
                <p>审核提交的案例并给出评分</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToBatchReview">
              <div class="action-icon">
                <team-outlined />
              </div>
              <div class="action-content">
                <h3>批量审核</h3>
                <p>批量处理多个案例的审核</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToReviewHistory">
              <div class="action-icon">
                <history-outlined />
              </div>
              <div class="action-content">
                <h3>审核历史</h3>
                <p>查看历史审核记录和统计</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToReviewSettings">
              <div class="action-icon">
                <setting-outlined />
              </div>
              <div class="action-content">
                <h3>审核设置</h3>
                <p>配置审核规则和评分标准</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 待审核案例 -->
    <div class="pending-reviews">
      <a-card title="待审核案例" :bordered="false">
        <template #extra>
          <a-button type="link" @click="goToReviewList">查看全部</a-button>
        </template>
        
        <a-spin :spinning="pendingLoading">
          <div v-if="pendingCases.length === 0" class="empty-state">
            <a-empty description="暂无待审核案例" />
          </div>
          
          <div v-else class="pending-list">
            <div 
              v-for="caseItem in pendingCases" 
              :key="caseItem.id"
              class="pending-item"
            >
              <div class="case-info">
                <h4 class="case-title">{{ caseItem.title }}</h4>
                <p class="case-activity">活动：{{ caseItem.activityTitle }}</p>
                <div class="case-meta">
                  <span class="meta-item">
                    <user-outlined />
                    {{ caseItem.submitterName }}
                  </span>
                  <span class="meta-item">
                    <clock-circle-outlined />
                    {{ formatDate(caseItem.submitTime) }}
                  </span>
                  <span class="meta-item">
                    <team-outlined />
                    {{ caseItem.submitterDepartment }}
                  </span>
                </div>
              </div>
              
              <div class="case-actions">
                <a-space>
                  <a-button size="small" @click="viewCase(caseItem)">
                    <template #icon><eye-outlined /></template>
                    查看
                  </a-button>
                  <a-button type="primary" size="small" @click="reviewCase(caseItem)">
                    <template #icon><audit-outlined /></template>
                    审核
                  </a-button>
                </a-space>
              </div>
            </div>
          </div>
        </a-spin>
      </a-card>
    </div>

    <!-- 审核进度 -->
    <div class="review-progress">
      <a-card title="审核进度" :bordered="false">
        <div class="progress-content">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="progress-item">
                <h4>总体进度</h4>
                <a-progress 
                  :percent="getOverallProgress()" 
                  :stroke-color="getProgressColor(getOverallProgress())"
                />
                <p class="progress-desc">
                  已审核 {{ stats.approved + stats.rejected }} / {{ stats.total }} 个案例
                </p>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="progress-item">
                <h4>通过率</h4>
                <a-progress 
                  :percent="getApprovalRate()" 
                  :stroke-color="getApprovalColor(getApprovalRate())"
                />
                <p class="progress-desc">
                  通过率 {{ getApprovalRate() }}%
                </p>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 最近审核记录 -->
    <div class="recent-reviews">
      <a-card title="最近审核记录" :bordered="false">
        <template #extra>
          <a-button type="link" @click="goToReviewHistory">查看全部</a-button>
        </template>
        
        <a-spin :spinning="recentLoading">
          <div v-if="recentReviews.length === 0" class="empty-state">
            <a-empty description="暂无审核记录" />
          </div>
          
          <a-timeline v-else>
            <a-timeline-item 
              v-for="review in recentReviews" 
              :key="review.id"
              :color="getReviewResultColor(review.reviewResult)"
            >
              <div class="review-timeline-item">
                <div class="review-header">
                  <span class="review-title">{{ review.submissionTitle }}</span>
                  <a-tag :color="getReviewResultColor(review.reviewResult)">
                    {{ getReviewResultText(review.reviewResult) }}
                  </a-tag>
                </div>
                <div class="review-content">
                  <p>审核员：{{ review.reviewerName }}</p>
                  <p>评分：{{ review.score }}</p>
                  <p v-if="review.comments">意见：{{ truncateText(review.comments, 100) }}</p>
                </div>
                <div class="review-time">{{ formatDate(review.reviewTime) }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-spin>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  ExportOutlined,
  AuditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StarOutlined,
  TeamOutlined,
  HistoryOutlined,
  SettingOutlined,
  UserOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission, CaseReview } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { formatDate } from '@/utils/date';

const router = useRouter();

// 响应式数据
const stats = reactive({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0,
  averageScore: 0
});

const pendingCases = ref<CaseSubmission[]>([]);
const recentReviews = ref<CaseReview[]>([]);
const statsLoading = ref(false);
const pendingLoading = ref(false);
const recentLoading = ref(false);

// 方法
const loadStats = async () => {
  try {
    statsLoading.value = true;
    const response = await caseCollectionService.getSubmissionStatistics(0); // 0表示所有活动
    
    if (response.success) {
      stats.total = response.data.total || 0;
      stats.pending = response.data.reviewing || 0;
      stats.approved = response.data.approved || 0;
      stats.rejected = response.data.rejected || 0;
      stats.averageScore = response.data.averageScore || 0;
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statsLoading.value = false;
  }
};

const loadPendingCases = async () => {
  try {
    pendingLoading.value = true;
    const response = await caseCollectionService.getSubmissionList({
      status: 'submitted',
      page: 1,
      pageSize: 5
    });
    
    if (response.success) {
      pendingCases.value = response.data.list;
    }
  } catch (error) {
    console.error('加载待审核案例失败:', error);
  } finally {
    pendingLoading.value = false;
  }
};

const loadRecentReviews = async () => {
  try {
    recentLoading.value = true;
    const response = await caseCollectionService.getReviewList({
      page: 1,
      pageSize: 5
    });
    
    if (response.success) {
      recentReviews.value = response.data.list;
    }
  } catch (error) {
    console.error('加载审核记录失败:', error);
  } finally {
    recentLoading.value = false;
  }
};

// 工具方法
const getOverallProgress = () => {
  if (stats.total === 0) return 0;
  return Math.round(((stats.approved + stats.rejected) / stats.total) * 100);
};

const getApprovalRate = () => {
  const reviewed = stats.approved + stats.rejected;
  if (reviewed === 0) return 0;
  return Math.round((stats.approved / reviewed) * 100);
};

const getProgressColor = (percent: number) => {
  if (percent >= 80) return '#52c41a';
  if (percent >= 50) return '#faad14';
  return '#1890ff';
};

const getApprovalColor = (percent: number) => {
  if (percent >= 80) return '#52c41a';
  if (percent >= 60) return '#faad14';
  return '#ff4d4f';
};

const getReviewResultColor = (result: number) => {
  const colors: Record<number, string> = {
    1: 'green',
    2: 'red',
    3: 'orange'
  };
  return colors[result] || 'default';
};

const getReviewResultText = (result: number) => {
  const texts: Record<number, string> = {
    1: '通过',
    2: '驳回',
    3: '需修改'
  };
  return texts[result] || '未知';
};

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 事件处理
const refreshData = () => {
  loadStats();
  loadPendingCases();
  loadRecentReviews();
};

const exportReport = () => {
  message.info('导出功能开发中...');
};

// 导航方法
const goToReviewList = () => {
  router.push('/case-collection/review/list');
};

const goToBatchReview = () => {
  router.push('/case-collection/review/batch');
};

const goToReviewHistory = () => {
  router.push('/case-collection/review/history');
};

const goToReviewSettings = () => {
  router.push('/case-collection/review/settings');
};

const viewCase = (caseItem: CaseSubmission) => {
  router.push(`/case-collection/submissions/${caseItem.id}`);
};

const reviewCase = (caseItem: CaseSubmission) => {
  router.push(`/case-collection/review/detail/${caseItem.id}`);
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style scoped lang="scss">
.case-review-index {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 16px;
      }
    }
  }

  .review-stats {
    margin-bottom: 24px;

    .ant-statistic {
      text-align: center;
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    .action-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fafafa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      height: 100px;

      &:hover {
        background: #e6f7ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .action-icon {
        font-size: 32px;
        color: #1890ff;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .action-content {
        flex: 1;

        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .pending-reviews {
    margin-bottom: 24px;

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }

    .pending-list {
      .pending-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .case-info {
          flex: 1;

          .case-title {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 500;
            color: #262626;
          }

          .case-activity {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #8c8c8c;
          }

          .case-meta {
            display: flex;
            gap: 16px;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 12px;
              color: #8c8c8c;

              .anticon {
                color: #1890ff;
              }
            }
          }
        }

        .case-actions {
          flex-shrink: 0;
        }
      }
    }
  }

  .review-progress {
    margin-bottom: 24px;

    .progress-content {
      .progress-item {
        h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .progress-desc {
          margin: 8px 0 0 0;
          font-size: 14px;
          color: #8c8c8c;
        }
      }
    }
  }

  .recent-reviews {
    .empty-state {
      text-align: center;
      padding: 40px 0;
    }

    .review-timeline-item {
      .review-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .review-title {
          font-weight: 500;
          color: #262626;
        }
      }

      .review-content {
        margin-bottom: 8px;

        p {
          margin: 0 0 4px 0;
          font-size: 14px;
          color: #595959;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .review-time {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
}

@media (max-width: 768px) {
  .case-review-index {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content h1 {
        font-size: 24px;
      }

      .header-actions {
        width: 100%;
      }
    }

    .quick-actions {
      .action-card {
        height: auto;
        min-height: 80px;
        padding: 16px;

        .action-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .action-content h3 {
          font-size: 14px;
        }

        .action-content p {
          font-size: 12px;
        }
      }
    }

    .pending-reviews {
      .pending-list {
        .pending-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;
          padding: 12px;

          .case-actions {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
