<template>
  <div class="batch-review">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>批量审核</h2>
        <p>选择多个案例进行批量审核，提高审核效率</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button type="primary" @click="showBatchReviewModal" :disabled="!selectedRowKeys.length">
            <template #icon><team-outlined /></template>
            批量审核 ({{ selectedRowKeys.length }})
          </a-button>
          <a-button @click="clearSelection" :disabled="!selectedRowKeys.length">
            <template #icon><clear-outlined /></template>
            清空选择
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="活动筛选">
            <a-select
              v-model:value="searchForm.activityId"
              placeholder="选择活动"
              style="width: 200px"
              allow-clear
            >
              <a-select-option v-for="activity in activities" :key="activity.id" :value="activity.id">
                {{ activity.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态筛选">
            <a-select
              v-model:value="searchForm.status"
              placeholder="选择状态"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="submitted">已提交</a-select-option>
              <a-select-option value="reviewing">审核中</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="关键词">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="搜索案例标题或提交人"
              style="width: 200px"
              allow-clear
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit" :loading="loading">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><redo-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 选择提示 -->
    <div v-if="selectedRowKeys.length > 0" class="selection-info">
      <a-alert
        :message="`已选择 ${selectedRowKeys.length} 个案例`"
        type="info"
        show-icon
        closable
        @close="clearSelection"
      >
        <template #description>
          <span>您可以对选中的案例进行批量审核操作</span>
          <a-button type="link" size="small" @click="showBatchReviewModal">
            立即审核
          </a-button>
        </template>
      </a-alert>
    </div>

    <!-- 案例列表 -->
    <div class="table-section">
      <a-card :bordered="false">
        <template #title>
          <div class="table-header">
            <span>待审核案例列表</span>
            <span class="record-count">共 {{ pagination.total }} 条记录</span>
          </div>
        </template>
        
        <a-table
          :columns="columns"
          :data-source="cases"
          :loading="loading"
          :pagination="paginationConfig"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <div class="case-title">
                <a @click="viewCase(record)">{{ record.title }}</a>
                <div class="case-summary">{{ record.summary }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'submitTime'">
              {{ formatDate(record.submitTime) }}
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewCase(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="reviewSingleCase(record)">
                  审核
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 批量审核弹窗 -->
    <a-modal
      v-model:open="batchReviewVisible"
      title="批量审核"
      width="800px"
      :footer="null"
      :mask-closable="false"
    >
      <BatchReviewForm
        :selected-cases="selectedCases"
        @submit="handleBatchReview"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  TeamOutlined,
  ClearOutlined,
  SearchOutlined,
  RedoOutlined
} from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';
import type { 
  CaseSubmission, 
  CaseCollectionActivity,
  CaseSubmissionStatus 
} from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import BatchReviewForm from './components/BatchReviewForm.vue';

const router = useRouter();

// 响应式数据
const cases = ref<CaseSubmission[]>([]);
const activities = ref<CaseCollectionActivity[]>([]);
const loading = ref(false);
const selectedRowKeys = ref<number[]>([]);
const batchReviewVisible = ref(false);

const searchForm = reactive({
  activityId: undefined as number | undefined,
  status: undefined as string | undefined,
  keyword: ''
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 计算属性
const selectedCases = computed(() => {
  return cases.value.filter(item => selectedRowKeys.value.includes(item.id));
});

const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page: number, pageSize: number) => {
    pagination.current = page;
    pagination.pageSize = pageSize;
    loadCases();
  }
}));

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例标题',
    dataIndex: 'title',
    key: 'title',
    width: 300,
    ellipsis: true
  },
  {
    title: '提交人',
    dataIndex: 'submitterName',
    key: 'submitterName',
    width: 120
  },
  {
    title: '部门',
    dataIndex: 'submitterDepartment',
    key: 'submitterDepartment',
    width: 150,
    ellipsis: true
  },
  {
    title: '所属活动',
    dataIndex: 'activityTitle',
    key: 'activityTitle',
    width: 200,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
];

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: CaseSubmission) => ({
    disabled: record.status !== 'submitted' && record.status !== 'reviewing'
  })
};

// 方法定义
const loadCases = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      activityId: searchForm.activityId,
      status: searchForm.status as CaseSubmissionStatus,
      keyword: searchForm.keyword
    };

    const response = await caseCollectionService.getSubmissionList(params);
    if (response.success) {
      cases.value = response.data.list;
      pagination.total = response.data.total;
    } else {
      message.error(response.message || '加载案例列表失败');
    }
  } catch (error) {
    console.error('加载案例列表失败:', error);
    message.error('加载案例列表失败');
  } finally {
    loading.value = false;
  }
};

const loadActivities = async () => {
  try {
    const response = await caseCollectionService.getActivityList({
      status: 'active',
      pageSize: 100
    });
    if (response.success) {
      activities.value = response.data.list;
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
  }
};

const refreshData = () => {
  loadCases();
  loadActivities();
};

const handleSearch = () => {
  pagination.current = 1;
  loadCases();
};

const handleReset = () => {
  Object.assign(searchForm, {
    activityId: undefined,
    status: undefined,
    keyword: ''
  });
  handleSearch();
};

const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadCases();
};

const clearSelection = () => {
  selectedRowKeys.value = [];
};

const showBatchReviewModal = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要审核的案例');
    return;
  }
  batchReviewVisible.value = true;
};

const handleBatchReview = async (reviewData: any) => {
  try {
    const response = await caseCollectionService.batchReviewCases({
      submissionIds: selectedRowKeys.value,
      reviewResult: reviewData.reviewResult,
      score: reviewData.score,
      comments: reviewData.comments,
      suggestions: reviewData.suggestions
    });

    if (response.success) {
      message.success(`成功审核 ${selectedRowKeys.value.length} 个案例`);
      batchReviewVisible.value = false;
      selectedRowKeys.value = [];
      refreshData();
    } else {
      message.error(response.message || '批量审核失败');
    }
  } catch (error) {
    console.error('批量审核失败:', error);
    message.error('批量审核失败');
  }
};

const viewCase = (record: CaseSubmission) => {
  router.push(`/case-collection/submissions/${record.id}`);
};

const reviewSingleCase = (record: CaseSubmission) => {
  router.push(`/case-collection/review/detail/${record.id}`);
};

// 工具函数
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'submitted': 'blue',
    'reviewing': 'processing',
    'approved': 'green',
    'rejected': 'red',
    'withdrawn': 'default'
  };
  return colorMap[status] || 'default';
};

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'withdrawn': '已撤回'
  };
  return textMap[status] || '未知';
};

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN');
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style scoped lang="less">
.batch-review {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 32px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        color: #1a1a1a;
        font-size: 32px;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 16px;
        line-height: 1.6;
      }
    }

    .header-actions {
      .ant-btn {
        height: 48px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        &.ant-btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }
        }
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
      }
    }

    .ant-form-item {
      margin-bottom: 0;

      .ant-form-item-label {
        font-weight: 600;
        color: #333;
      }
    }
  }

  .selection-info {
    margin-bottom: 24px;

    .ant-alert {
      border-radius: 12px;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      border: 1px solid rgba(102, 126, 234, 0.2);
      backdrop-filter: blur(10px);
    }
  }

  .table-section {
    .ant-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.2);
      overflow: hidden;
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .case-title {
      a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          color: #764ba2;
          text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
        }
      }

      .case-summary {
        color: #666;
        font-size: 12px;
        margin-top: 4px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
  }

  .ant-table {
    .ant-table-thead > tr > th {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      border-bottom: 2px solid rgba(102, 126, 234, 0.1);
      font-weight: 700;
      color: #333;
      font-size: 14px;
    }

    .ant-table-tbody > tr {
      transition: all 0.3s ease;

      &:hover > td {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
        transform: scale(1.001);
      }
    }

    .ant-table-row-selected > td {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%) !important;
    }
  }

  .ant-tag {
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    border: none;

    &.ant-tag-success {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      color: white;
    }

    &.ant-tag-warning {
      background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
      color: white;
    }

    &.ant-tag-error {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      color: white;
    }
  }

  .ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1.4;
    color: #667eea;
    font-weight: 500;

    &:hover {
      color: #764ba2;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .batch-review {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      padding: 24px;

      .header-content {
        h2 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
        }
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }

        .ant-btn {
          height: 40px;
          font-size: 13px;
        }
      }
    }

    .filter-section {
      .ant-card {
        .ant-card-body {
          padding: 16px;
        }
      }

      .ant-form {
        .ant-form-item {
          margin-bottom: 16px;
        }
      }
    }

    .table-section {
      .ant-card {
        .ant-card-body {
          padding: 12px;
        }
      }

      .ant-table {
        .ant-table-content {
          overflow-x: auto;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .batch-review {
    padding: 12px;

    .page-header {
      padding: 20px;
      border-radius: 16px;

      .header-content {
        h2 {
          font-size: 20px;
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .batch-review {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);

    .page-header,
    .filter-section .ant-card,
    .table-section .ant-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .selection-info .ant-alert {
      background: rgba(102, 126, 234, 0.1);
      border: 1px solid rgba(102, 126, 234, 0.2);
    }
  }
}
</style>
