<template>
  <div class="review-history">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>审核历史</h2>
        <p>查看案例审核历史记录，支持数据分析、趋势查看和详细追溯</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="showTrendChart">
            <template #icon><line-chart-outlined /></template>
            趋势分析
          </a-button>
          <a-button @click="exportHistory">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <a-row :gutter="[24, 24]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :loading="statsLoading">
            <a-statistic
              title="总审核数"
              :value="statistics.totalReviews"
              :value-style="{ color: '#1890ff', fontSize: '28px', fontWeight: '600' }"
            >
              <template #prefix>
                <audit-outlined class="stat-icon" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+12</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :loading="statsLoading">
            <a-statistic
              title="通过率"
              :value="statistics.approvalRate"
              suffix="%"
              :precision="1"
              :value-style="{ color: '#52c41a', fontSize: '28px', fontWeight: '600' }"
            >
              <template #prefix>
                <check-circle-outlined class="stat-icon" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value positive">+2.3%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :loading="statsLoading">
            <a-statistic
              title="平均分"
              :value="statistics.averageScore"
              :precision="1"
              suffix="分"
              :value-style="{ color: '#faad14', fontSize: '28px', fontWeight: '600' }"
            >
              <template #prefix>
                <star-outlined class="stat-icon" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">较昨日</span>
              <span class="trend-value negative">-1.2分</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" :loading="statsLoading">
            <a-statistic
              title="今日审核"
              :value="statistics.todayReviews"
              :value-style="{ color: '#722ed1', fontSize: '28px', fontWeight: '600' }"
            >
              <template #prefix>
                <clock-circle-outlined class="stat-icon" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <span class="trend-text">进行中</span>
              <span class="trend-value">{{ statistics.pendingReviews }}待审核</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card :bordered="false" class="filter-card">
        <a-form layout="inline" :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="活动筛选" class="form-item-full">
                <a-select
                  v-model:value="searchForm.activityId"
                  placeholder="选择活动"
                  allow-clear
                  show-search
                  :filter-option="filterOption"
                >
                  <a-select-option v-for="activity in activities" :key="activity.id" :value="activity.id">
                    {{ activity.title }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核结果" class="form-item-full">
                <a-select
                  v-model:value="searchForm.reviewResult"
                  placeholder="选择结果"
                  allow-clear
                >
                  <a-select-option :value="1">
                    <check-circle-outlined style="color: #52c41a" /> 通过
                  </a-select-option>
                  <a-select-option :value="2">
                    <close-circle-outlined style="color: #ff4d4f" /> 驳回
                  </a-select-option>
                  <a-select-option :value="3">
                    <exclamation-circle-outlined style="color: #faad14" /> 需修改
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="审核人" class="form-item-full">
                <a-input
                  v-model:value="searchForm.reviewerName"
                  placeholder="输入审核人姓名"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="关键词" class="form-item-full">
                <a-input
                  v-model:value="searchForm.keyword"
                  placeholder="搜索案例标题或评论"
                  allow-clear
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="审核时间" class="form-item-full">
                <a-range-picker
                  v-model:value="searchForm.dateRange"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                  :placeholder="['开始日期', '结束日期']"
                />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch" :loading="loading">
                    <template #icon><search-outlined /></template>
                    搜索
                  </a-button>
                  <a-button @click="handleReset">
                    <template #icon><redo-outlined /></template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 历史记录表格 -->
    <div class="table-section">
      <a-card :bordered="false" class="table-card">
        <template #title>
          <div class="table-header">
            <span class="table-title">审核历史记录</span>
            <span class="record-count">共 {{ pagination.total }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-button size="small" @click="showCompareModal" :disabled="selectedRowKeys.length !== 2">
              <template #icon><diff-outlined /></template>
              对比记录
            </a-button>
          </a-space>
        </template>
        
        <a-table
          :columns="columns"
          :data-source="historyList"
          :loading="loading"
          :pagination="paginationConfig"
          :row-selection="rowSelection"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
          class="history-table"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'submissionTitle'">
              <div class="submission-info">
                <a @click="viewSubmission(record)" class="submission-title">
                  {{ record.submissionTitle }}
                </a>
                <div class="submission-meta">
                  <span class="submitter">{{ record.submitterName }}</span>
                  <a-divider type="vertical" />
                  <span class="department">{{ record.department }}</span>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'reviewResult'">
              <a-tag :color="getResultColor(record.reviewResult)" class="result-tag">
                <template #icon>
                  <component :is="getResultIcon(record.reviewResult)" />
                </template>
                {{ getResultText(record.reviewResult) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'score'">
              <div class="score-display">
                <span class="score-value" :class="getScoreClass(record.score)">
                  {{ record.score }}分
                </span>
                <div class="score-bar">
                  <div class="score-fill" :style="{ width: record.score + '%', backgroundColor: getScoreColor(record.score) }"></div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'reviewTime'">
              <div class="time-display">
                <div class="date">{{ formatDate(record.reviewTime) }}</div>
                <div class="time">{{ formatTime(record.reviewTime) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  <template #icon><eye-outlined /></template>
                  详情
                </a-button>
                <a-button type="link" size="small" @click="viewRelated(record)">
                  <template #icon><link-outlined /></template>
                  关联
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 详情查看弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="审核详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentRecord" class="detail-content">
        <!-- 详情内容将在后续添加 -->
      </div>
    </a-modal>

    <!-- 趋势分析弹窗 -->
    <a-modal
      v-model:open="trendVisible"
      title="审核趋势分析"
      width="1000px"
      :footer="null"
    >
      <div class="trend-content">
        <!-- 趋势图表将在后续添加 -->
      </div>
    </a-modal>

    <!-- 对比弹窗 -->
    <a-modal
      v-model:open="compareVisible"
      title="记录对比"
      width="1200px"
      :footer="null"
    >
      <div class="compare-content">
        <!-- 对比内容将在后续添加 -->
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  ExportOutlined,
  LineChartOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  StarOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  RedoOutlined,
  DiffOutlined,
  EyeOutlined,
  LinkOutlined
} from '@ant-design/icons-vue';
import type { TableColumnsType } from 'ant-design-vue';
import type { 
  ReviewHistoryRecord,
  ReviewStatistics,
  CaseCollectionActivity,
  CaseReviewResult
} from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';

const router = useRouter();

// 响应式数据
const historyList = ref<ReviewHistoryRecord[]>([]);
const activities = ref<CaseCollectionActivity[]>([]);
const statistics = ref<ReviewStatistics>({
  totalReviews: 0,
  todayReviews: 0,
  pendingReviews: 0,
  approvedCount: 0,
  rejectedCount: 0,
  needRevisionCount: 0,
  approvalRate: 0,
  averageScore: 0,
  averageReviewTime: 0,
  reviewerStats: [],
  monthlyTrend: [],
  scoreDistribution: []
});

const loading = ref(false);
const statsLoading = ref(false);
const selectedRowKeys = ref<number[]>([]);
const detailVisible = ref(false);
const trendVisible = ref(false);
const compareVisible = ref(false);
const currentRecord = ref<ReviewHistoryRecord | null>(null);

const searchForm = reactive({
  activityId: undefined as number | undefined,
  reviewResult: undefined as CaseReviewResult | undefined,
  reviewerName: '',
  keyword: '',
  dateRange: undefined as [string, string] | undefined
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 计算属性
const paginationConfig = computed(() => ({
  ...pagination,
  onChange: (page: number, pageSize: number) => {
    pagination.current = page;
    pagination.pageSize = pageSize;
    loadHistoryData();
  }
}));

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例信息',
    dataIndex: 'submissionTitle',
    key: 'submissionTitle',
    width: 280,
    ellipsis: true
  },
  {
    title: '所属活动',
    dataIndex: 'activityTitle',
    key: 'activityTitle',
    width: 200,
    ellipsis: true
  },
  {
    title: '审核人',
    dataIndex: 'reviewerName',
    key: 'reviewerName',
    width: 120
  },
  {
    title: '审核结果',
    dataIndex: 'reviewResult',
    key: 'reviewResult',
    width: 120,
    align: 'center'
  },
  {
    title: '评分',
    dataIndex: 'score',
    key: 'score',
    width: 120,
    align: 'center',
    sorter: true
  },
  {
    title: '审核时间',
    dataIndex: 'reviewTime',
    key: 'reviewTime',
    width: 160,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 140,
    fixed: 'right'
  }
];

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  }
};

// 方法定义
const loadHistoryData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      activityId: searchForm.activityId,
      reviewResult: searchForm.reviewResult,
      reviewerName: searchForm.reviewerName,
      keyword: searchForm.keyword,
      dateRange: searchForm.dateRange
    };

    const response = await caseCollectionService.getReviewHistory(params);
    if (response.success) {
      historyList.value = response.data.list;
      pagination.total = response.data.total;
    } else {
      message.error(response.message || '加载审核历史失败');
    }
  } catch (error) {
    console.error('加载审核历史失败:', error);
    message.error('加载审核历史失败');
  } finally {
    loading.value = false;
  }
};

const loadStatistics = async () => {
  try {
    statsLoading.value = true;
    const response = await caseCollectionService.getReviewStatistics();
    if (response.success) {
      statistics.value = response.data;
    } else {
      message.error(response.message || '加载统计数据失败');
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    message.error('加载统计数据失败');
  } finally {
    statsLoading.value = false;
  }
};

const loadActivities = async () => {
  try {
    const response = await caseCollectionService.getActivityList({
      pageSize: 100
    });
    if (response.success) {
      activities.value = response.data.list;
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
  }
};

const refreshData = () => {
  loadHistoryData();
  loadStatistics();
  loadActivities();
};

const handleSearch = () => {
  pagination.current = 1;
  loadHistoryData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    activityId: undefined,
    reviewResult: undefined,
    reviewerName: '',
    keyword: '',
    dateRange: undefined
  });
  handleSearch();
};

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  // 处理排序逻辑
  loadHistoryData();
};

// 弹窗和操作方法
const viewDetail = (record: ReviewHistoryRecord) => {
  currentRecord.value = record;
  detailVisible.value = true;
};

const viewSubmission = (record: ReviewHistoryRecord) => {
  router.push(`/case-collection/submissions/${record.submissionId}`);
};

const viewRelated = (record: ReviewHistoryRecord) => {
  // 查看相关审核记录
  searchForm.activityId = record.activityId;
  handleSearch();
};

const showTrendChart = () => {
  trendVisible.value = true;
};

const showCompareModal = () => {
  if (selectedRowKeys.value.length !== 2) {
    message.warning('请选择两条记录进行对比');
    return;
  }
  compareVisible.value = true;
};

const exportHistory = async () => {
  try {
    message.loading('正在导出数据...', 0);
    const params = {
      activityId: searchForm.activityId,
      reviewResult: searchForm.reviewResult,
      reviewerName: searchForm.reviewerName,
      keyword: searchForm.keyword,
      dateRange: searchForm.dateRange
    };

    const blob = await caseCollectionService.exportReviewHistory(params);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `审核历史_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.destroy();
    message.success('导出成功');
  } catch (error) {
    message.destroy();
    console.error('导出失败:', error);
    message.error('导出失败');
  }
};

// 工具函数
const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const getResultColor = (result: CaseReviewResult) => {
  const colorMap = {
    1: 'success',
    2: 'error',
    3: 'warning'
  };
  return colorMap[result] || 'default';
};

const getResultText = (result: CaseReviewResult) => {
  const textMap = {
    1: '通过',
    2: '驳回',
    3: '需修改'
  };
  return textMap[result] || '未知';
};

const getResultIcon = (result: CaseReviewResult) => {
  const iconMap = {
    1: CheckCircleOutlined,
    2: CloseCircleOutlined,
    3: ExclamationCircleOutlined
  };
  return iconMap[result] || CheckCircleOutlined;
};

const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent';
  if (score >= 80) return 'score-good';
  if (score >= 70) return 'score-fair';
  if (score >= 60) return 'score-pass';
  return 'score-fail';
};

const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a';
  if (score >= 80) return '#1890ff';
  if (score >= 70) return '#faad14';
  if (score >= 60) return '#fa8c16';
  return '#ff4d4f';
};

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

const formatTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style scoped lang="less">
.review-history {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 32px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        color: #1a1a1a;
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 15px;
        line-height: 1.6;
      }
    }

    .header-actions {
      .ant-btn {
        height: 44px;
        border-radius: 12px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      border-radius: 16px;
      border: none;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
      }

      .ant-card-body {
        padding: 24px;
      }

      .stat-icon {
        font-size: 24px;
        margin-right: 8px;
      }

      .stat-trend {
        margin-top: 12px;
        display: flex;
        align-items: center;
        gap: 8px;

        .trend-text {
          color: #8c8c8c;
          font-size: 12px;
        }

        .trend-value {
          font-size: 12px;
          font-weight: 600;

          &.positive {
            color: #52c41a;
          }

          &.negative {
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    .filter-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .ant-card-body {
        padding: 24px;
      }
    }

    .search-form {
      .form-item-full {
        width: 100%;
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
          color: #333;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-start;

        .ant-btn {
          border-radius: 8px;
          font-weight: 500;
        }
      }
    }
  }

  .table-section {
    .table-card {
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        border-radius: 16px 16px 0 0;
      }

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .table-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .record-count {
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }

    .history-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        border-bottom: 2px solid #f0f0f0;
        font-weight: 600;
        color: #333;
      }

      .ant-table-tbody > tr {
        transition: all 0.3s ease;

        &:hover > td {
          background: #f8f9ff;
        }
      }

      .submission-info {
        .submission-title {
          color: #1890ff;
          text-decoration: none;
          font-weight: 500;
          font-size: 14px;

          &:hover {
            color: #40a9ff;
          }
        }

        .submission-meta {
          margin-top: 4px;
          font-size: 12px;
          color: #8c8c8c;

          .submitter {
            font-weight: 500;
          }
        }
      }

      .result-tag {
        border-radius: 8px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 4px;
      }

      .score-display {
        .score-value {
          font-weight: 600;
          font-size: 16px;

          &.score-excellent { color: #52c41a; }
          &.score-good { color: #1890ff; }
          &.score-fair { color: #faad14; }
          &.score-pass { color: #fa8c16; }
          &.score-fail { color: #ff4d4f; }
        }

        .score-bar {
          width: 60px;
          height: 4px;
          background: #f0f0f0;
          border-radius: 2px;
          margin-top: 4px;
          overflow: hidden;

          .score-fill {
            height: 100%;
            border-radius: 2px;
            transition: all 0.3s ease;
          }
        }
      }

      .time-display {
        .date {
          font-weight: 500;
          color: #333;
          font-size: 13px;
        }

        .time {
          color: #8c8c8c;
          font-size: 12px;
          margin-top: 2px;
        }
      }
    }
  }

  .ant-btn-link {
    padding: 0;
    height: auto;
    line-height: 1.4;
    border-radius: 4px;

    &:hover {
      background: rgba(24, 144, 255, 0.1);
      padding: 2px 6px;
    }
  }

  // 弹窗样式
  .detail-content {
    padding: 16px 0;
  }

  .trend-content {
    padding: 16px 0;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
  }

  .compare-content {
    padding: 16px 0;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .review-history {
    .statistics-section {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .review-history {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      padding: 24px;

      .header-content {
        h2 {
          font-size: 24px;
        }
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }

        .ant-btn {
          height: 40px;
        }
      }
    }

    .filter-section {
      .search-form {
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }

        .search-actions {
          justify-content: center;

          .ant-space {
            width: 100%;
            justify-content: center;
          }
        }
      }
    }

    .table-section {
      .table-card {
        .ant-card-extra {
          margin-top: 16px;
        }
      }

      .history-table {
        .ant-table-content {
          overflow-x: auto;
        }

        .submission-info {
          min-width: 200px;
        }

        .score-display {
          .score-bar {
            width: 40px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .review-history {
    padding: 12px;

    .page-header {
      padding: 20px;

      .header-content {
        h2 {
          font-size: 20px;
        }

        p {
          font-size: 14px;
        }
      }
    }

    .statistics-section {
      .stat-card {
        .ant-card-body {
          padding: 16px;
        }

        .ant-statistic {
          .ant-statistic-content {
            font-size: 20px !important;
          }
        }
      }
    }

    .filter-section {
      .filter-card {
        .ant-card-body {
          padding: 16px;
        }
      }
    }

    .table-section {
      .table-card {
        .ant-card-body {
          padding: 12px;
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .review-history {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);

    .page-header,
    .filter-card,
    .table-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
