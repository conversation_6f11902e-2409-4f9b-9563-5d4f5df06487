<template>
  <div class="review-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>审核设置</h2>
        <p>配置审核规则、评分标准和快速模板，优化审核流程</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="resetToDefault">
            <template #icon><undo-outlined /></template>
            恢复默认
          </a-button>
          <a-button @click="previewSettings">
            <template #icon><eye-outlined /></template>
            预览效果
          </a-button>
          <a-button type="primary" @click="saveSettings" :loading="saving">
            <template #icon><save-outlined /></template>
            保存设置
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <a-card :bordered="false" class="settings-card">
        <a-tabs v-model:activeKey="activeTab" type="card" class="settings-tabs">
          <!-- 审核规则选项卡 -->
          <a-tab-pane key="rules" tab="审核规则">
            <template #tab>
              <span>
                <setting-outlined />
                审核规则
              </span>
            </template>
            <div class="tab-content">
              <a-form :model="settings.reviewRules" layout="vertical" class="rules-form">
                <a-row :gutter="[24, 24]">
                  <a-col :xs="24" :md="12">
                    <a-card title="基础规则" size="small" class="rule-card">
                      <a-form-item label="审核时限" name="maxReviewTime">
                        <a-input-number
                          v-model:value="settings.maxReviewTime"
                          :min="1"
                          :max="168"
                          addon-after="小时"
                          style="width: 100%"
                          placeholder="设置审核时限"
                        />
                        <div class="form-help">建议设置为24-72小时</div>
                      </a-form-item>
                      
                      <a-form-item label="通过分数线" name="passingScore">
                        <a-slider
                          v-model:value="settings.passingScore"
                          :min="0"
                          :max="100"
                          :marks="scoreMarks"
                          :tooltip-formatter="(value) => `${value}分`"
                        />
                        <div class="score-display">
                          <span class="score-value">{{ settings.passingScore }}分</span>
                          <span class="score-level">{{ getScoreLevel(settings.passingScore) }}</span>
                        </div>
                      </a-form-item>

                      <a-form-item label="优秀分数线" name="excellentScore">
                        <a-slider
                          v-model:value="settings.excellentScore"
                          :min="settings.passingScore"
                          :max="100"
                          :marks="excellentMarks"
                          :tooltip-formatter="(value) => `${value}分`"
                        />
                        <div class="score-display">
                          <span class="score-value">{{ settings.excellentScore }}分</span>
                          <span class="score-level">{{ getScoreLevel(settings.excellentScore) }}</span>
                        </div>
                      </a-form-item>
                    </a-card>
                  </a-col>

                  <a-col :xs="24" :md="12">
                    <a-card title="高级规则" size="small" class="rule-card">
                      <a-form-item name="autoAssignReviewer">
                        <a-checkbox v-model:checked="settings.autoAssignReviewer">
                          自动分配审核人
                        </a-checkbox>
                        <div class="form-help">系统将根据工作量自动分配审核任务</div>
                      </a-form-item>

                      <a-form-item name="requireDoubleReview">
                        <a-checkbox v-model:checked="settings.requireDoubleReview">
                          要求双重审核
                        </a-checkbox>
                        <div class="form-help">重要案例需要两位审核人确认</div>
                      </a-form-item>

                      <a-form-item name="allowSelfReview">
                        <a-checkbox v-model:checked="settings.reviewRules.allowSelfReview">
                          允许自我审核
                        </a-checkbox>
                        <div class="form-help">提交人可以审核自己的案例</div>
                      </a-form-item>

                      <a-form-item name="requireExpertReview">
                        <a-checkbox v-model:checked="settings.reviewRules.requireExpertReview">
                          优秀案例需专家审核
                        </a-checkbox>
                        <div class="form-help">达到优秀分数线的案例需要专家确认</div>
                      </a-form-item>

                      <a-form-item label="每日审核上限" name="maxReviewsPerDay">
                        <a-input-number
                          v-model:value="settings.reviewRules.maxReviewsPerDay"
                          :min="1"
                          :max="100"
                          addon-after="个"
                          style="width: 100%"
                          placeholder="设置每日审核数量上限"
                        />
                        <div class="form-help">防止审核人员过度疲劳</div>
                      </a-form-item>
                    </a-card>
                  </a-col>

                  <a-col :xs="24">
                    <a-card title="通知设置" size="small" class="rule-card">
                      <a-row :gutter="[16, 16]">
                        <a-col :xs="24" :sm="8">
                          <a-form-item name="notificationEnabled">
                            <a-checkbox v-model:checked="settings.notificationEnabled">
                              启用通知
                            </a-checkbox>
                          </a-form-item>
                        </a-col>
                        <a-col :xs="24" :sm="8">
                          <a-form-item name="emailNotification">
                            <a-checkbox 
                              v-model:checked="settings.emailNotification"
                              :disabled="!settings.notificationEnabled"
                            >
                              邮件通知
                            </a-checkbox>
                          </a-form-item>
                        </a-col>
                        <a-col :xs="24" :sm="8">
                          <a-form-item name="smsNotification">
                            <a-checkbox 
                              v-model:checked="settings.smsNotification"
                              :disabled="!settings.notificationEnabled"
                            >
                              短信通知
                            </a-checkbox>
                          </a-form-item>
                        </a-col>
                      </a-row>
                    </a-card>
                  </a-col>
                </a-row>
              </a-form>
            </div>
          </a-tab-pane>

          <!-- 评分标准选项卡 -->
          <a-tab-pane key="scoring" tab="评分标准">
            <template #tab>
              <span>
                <star-outlined />
                评分标准
              </span>
            </template>
            <div class="tab-content">
              <div class="scoring-section">
                <a-card title="评分维度配置" class="scoring-card">
                  <template #extra>
                    <a-button type="link" @click="addScoringCriteria">
                      <template #icon><plus-outlined /></template>
                      添加维度
                    </a-button>
                  </template>
                  
                  <div class="criteria-list">
                    <div
                      v-for="(criteria, index) in settings.scoringCriteria"
                      :key="index"
                      class="criteria-item"
                    >
                      <a-card size="small" class="criteria-card">
                        <template #extra>
                          <a-button 
                            type="text" 
                            danger 
                            size="small"
                            @click="removeScoringCriteria(index)"
                            :disabled="settings.scoringCriteria.length <= 1"
                          >
                            <template #icon><delete-outlined /></template>
                          </a-button>
                        </template>
                        
                        <a-row :gutter="[16, 16]">
                          <a-col :xs="24" :sm="8">
                            <a-form-item label="维度名称" :name="['scoringCriteria', index, 'name']">
                              <a-input 
                                v-model:value="criteria.name" 
                                placeholder="如：内容质量"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :xs="24" :sm="4">
                            <a-form-item label="权重" :name="['scoringCriteria', index, 'weight']">
                              <a-input-number
                                v-model:value="criteria.weight"
                                :min="1"
                                :max="100"
                                addon-after="%"
                                style="width: 100%"
                              />
                            </a-form-item>
                          </a-col>
                          <a-col :xs="24" :sm="12">
                            <a-form-item label="描述" :name="['scoringCriteria', index, 'description']">
                              <a-input 
                                v-model:value="criteria.description" 
                                placeholder="评分维度的详细说明"
                              />
                            </a-form-item>
                          </a-col>
                        </a-row>
                      </a-card>
                    </div>
                  </div>

                  <div class="weight-summary">
                    <a-alert
                      :message="`当前权重总计：${totalWeight}%`"
                      :type="totalWeight === 100 ? 'success' : 'warning'"
                      show-icon
                    >
                      <template #description>
                        {{ totalWeight === 100 ? '权重配置正确' : '权重总和应为100%' }}
                      </template>
                    </a-alert>
                  </div>
                </a-card>
              </div>
            </div>
          </a-tab-pane>

          <!-- 快速模板选项卡 -->
          <a-tab-pane key="templates" tab="快速模板">
            <template #tab>
              <span>
                <file-text-outlined />
                快速模板
              </span>
            </template>
            <div class="tab-content">
              <div class="templates-section">
                <a-card title="审核模板管理" class="templates-card">
                  <template #extra>
                    <a-button type="primary" @click="showTemplateModal">
                      <template #icon><plus-outlined /></template>
                      新建模板
                    </a-button>
                  </template>
                  
                  <div class="templates-list">
                    <a-row :gutter="[16, 16]">
                      <a-col 
                        v-for="template in templates" 
                        :key="template.id" 
                        :xs="24" 
                        :sm="12" 
                        :lg="8"
                      >
                        <a-card 
                          size="small" 
                          class="template-card"
                          :class="{ 'default-template': template.isDefault }"
                        >
                          <template #title>
                            <div class="template-header">
                              <span class="template-name">{{ template.name }}</span>
                              <a-tag 
                                :color="getTemplateTypeColor(template.type)" 
                                size="small"
                              >
                                {{ getTemplateTypeText(template.type) }}
                              </a-tag>
                            </div>
                          </template>
                          
                          <template #extra>
                            <a-dropdown>
                              <a-button type="text" size="small">
                                <template #icon><more-outlined /></template>
                              </a-button>
                              <template #overlay>
                                <a-menu>
                                  <a-menu-item @click="editTemplate(template)">
                                    <edit-outlined /> 编辑
                                  </a-menu-item>
                                  <a-menu-item @click="copyTemplate(template)">
                                    <copy-outlined /> 复制
                                  </a-menu-item>
                                  <a-menu-divider />
                                  <a-menu-item 
                                    @click="deleteTemplate(template)"
                                    :disabled="template.isDefault"
                                    danger
                                  >
                                    <delete-outlined /> 删除
                                  </a-menu-item>
                                </a-menu>
                              </template>
                            </a-dropdown>
                          </template>

                          <div class="template-content">
                            <div class="template-result">
                              <a-tag :color="getResultColor(template.reviewResult)">
                                {{ getResultText(template.reviewResult) }}
                              </a-tag>
                              <span class="template-score">{{ template.score }}分</span>
                            </div>
                            
                            <div class="template-comments">
                              <p class="comments-text">{{ template.comments }}</p>
                              <p v-if="template.suggestions" class="suggestions-text">
                                建议：{{ template.suggestions }}
                              </p>
                            </div>

                            <div class="template-meta">
                              <span class="usage-count">使用 {{ template.usageCount || 0 }} 次</span>
                              <span class="update-time">{{ formatDate(template.updateTime) }}</span>
                            </div>
                          </div>
                        </a-card>
                      </a-col>
                    </a-row>
                  </div>
                </a-card>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 模板编辑弹窗 -->
    <a-modal
      v-model:open="templateModalVisible"
      :title="editingTemplate ? '编辑模板' : '新建模板'"
      width="600px"
      @ok="saveTemplate"
      @cancel="cancelTemplateEdit"
    >
      <!-- 模板表单内容将在后续添加 -->
    </a-modal>

    <!-- 预览弹窗 -->
    <a-modal
      v-model:open="previewVisible"
      title="设置预览"
      width="800px"
      :footer="null"
    >
      <!-- 预览内容将在后续添加 -->
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  UndoOutlined,
  EyeOutlined,
  SaveOutlined,
  SettingOutlined,
  StarOutlined,
  FileTextOutlined,
  PlusOutlined,
  DeleteOutlined,
  MoreOutlined,
  EditOutlined,
  CopyOutlined
} from '@ant-design/icons-vue';
import type { 
  ReviewSettings,
  ReviewTemplate,
  ReviewTemplateType,
  CaseReviewResult
} from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';

// 响应式数据
const activeTab = ref('rules');
const saving = ref(false);
const templateModalVisible = ref(false);
const previewVisible = ref(false);
const editingTemplate = ref<ReviewTemplate | null>(null);
const templates = ref<ReviewTemplate[]>([]);

const settings = reactive<ReviewSettings>({
  id: 1,
  autoAssignReviewer: true,
  maxReviewTime: 72,
  requireDoubleReview: false,
  passingScore: 60,
  excellentScore: 85,
  notificationEnabled: true,
  emailNotification: true,
  smsNotification: false,
  reviewRules: {
    allowSelfReview: false,
    requireExpertReview: true,
    maxReviewsPerDay: 20,
    autoRejectLowScore: false,
    autoApproveHighScore: false,
    requireReviewComments: true
  },
  scoringCriteria: [
    { name: '内容质量', weight: 40, description: '案例内容的完整性和准确性' },
    { name: '创新性', weight: 25, description: '案例的创新程度和独特性' },
    { name: '实用性', weight: 20, description: '案例的实际应用价值' },
    { name: '表达清晰度', weight: 15, description: '案例描述的清晰度和逻辑性' }
  ],
  updateTime: '',
  updatedBy: ''
});

// 计算属性
const totalWeight = computed(() => {
  return settings.scoringCriteria.reduce((sum, criteria) => sum + (criteria.weight || 0), 0);
});

const scoreMarks = computed(() => ({
  0: '0',
  20: '20',
  40: '40',
  60: '60',
  80: '80',
  100: '100'
}));

const excellentMarks = computed(() => ({
  [settings.passingScore]: settings.passingScore.toString(),
  80: '80',
  90: '90',
  100: '100'
}));

// 方法定义
const loadSettings = async () => {
  try {
    const response = await caseCollectionService.getReviewSettings();
    if (response.success) {
      Object.assign(settings, response.data);
    } else {
      message.error(response.message || '加载设置失败');
    }
  } catch (error) {
    console.error('加载设置失败:', error);
    message.error('加载设置失败');
  }
};

const loadTemplates = async () => {
  try {
    const response = await caseCollectionService.getReviewTemplates();
    if (response.success) {
      templates.value = response.data;
    } else {
      message.error(response.message || '加载模板失败');
    }
  } catch (error) {
    console.error('加载模板失败:', error);
    message.error('加载模板失败');
  }
};

const saveSettings = async () => {
  try {
    // 验证权重总和
    if (totalWeight.value !== 100) {
      message.error('评分维度权重总和必须为100%');
      activeTab.value = 'scoring';
      return;
    }

    saving.value = true;
    const response = await caseCollectionService.updateReviewSettings(settings);

    if (response.success) {
      message.success('设置保存成功');
      settings.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
      settings.updatedBy = '当前用户'; // 实际应该从用户信息获取
    } else {
      message.error(response.message || '保存设置失败');
    }
  } catch (error) {
    console.error('保存设置失败:', error);
    message.error('保存设置失败');
  } finally {
    saving.value = false;
  }
};

const resetToDefault = () => {
  Modal.confirm({
    title: '确认重置',
    content: '确定要恢复到默认设置吗？此操作不可撤销。',
    onOk: () => {
      // 重置为默认值
      Object.assign(settings, {
        autoAssignReviewer: true,
        maxReviewTime: 72,
        requireDoubleReview: false,
        passingScore: 60,
        excellentScore: 85,
        notificationEnabled: true,
        emailNotification: true,
        smsNotification: false,
        reviewRules: {
          allowSelfReview: false,
          requireExpertReview: true,
          maxReviewsPerDay: 20,
          autoRejectLowScore: false,
          autoApproveHighScore: false,
          requireReviewComments: true
        },
        scoringCriteria: [
          { name: '内容质量', weight: 40, description: '案例内容的完整性和准确性' },
          { name: '创新性', weight: 25, description: '案例的创新程度和独特性' },
          { name: '实用性', weight: 20, description: '案例的实际应用价值' },
          { name: '表达清晰度', weight: 15, description: '案例描述的清晰度和逻辑性' }
        ]
      });
      message.success('已恢复默认设置');
    }
  });
};

const previewSettings = () => {
  previewVisible.value = true;
};

// 评分标准管理
const addScoringCriteria = () => {
  settings.scoringCriteria.push({
    name: '',
    weight: 0,
    description: ''
  });
};

const removeScoringCriteria = (index: number) => {
  if (settings.scoringCriteria.length > 1) {
    settings.scoringCriteria.splice(index, 1);
  }
};

// 模板管理
const showTemplateModal = () => {
  editingTemplate.value = null;
  templateModalVisible.value = true;
};

const editTemplate = (template: ReviewTemplate) => {
  editingTemplate.value = { ...template };
  templateModalVisible.value = true;
};

const copyTemplate = (template: ReviewTemplate) => {
  const newTemplate = {
    ...template,
    id: 0,
    name: `${template.name} - 副本`,
    isDefault: false,
    createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    usageCount: 0
  };
  editingTemplate.value = newTemplate;
  templateModalVisible.value = true;
};

const deleteTemplate = (template: ReviewTemplate) => {
  if (template.isDefault) {
    message.warning('默认模板不能删除');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除模板"${template.name}"吗？此操作不可撤销。`,
    onOk: async () => {
      try {
        const response = await caseCollectionService.deleteReviewTemplate(template.id);
        if (response.success) {
          message.success('模板删除成功');
          loadTemplates();
        } else {
          message.error(response.message || '删除模板失败');
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        message.error('删除模板失败');
      }
    }
  });
};

const saveTemplate = async () => {
  if (!editingTemplate.value) return;

  try {
    const response = await caseCollectionService.saveReviewTemplate(editingTemplate.value);
    if (response.success) {
      message.success(editingTemplate.value.id ? '模板更新成功' : '模板创建成功');
      templateModalVisible.value = false;
      loadTemplates();
    } else {
      message.error(response.message || '保存模板失败');
    }
  } catch (error) {
    console.error('保存模板失败:', error);
    message.error('保存模板失败');
  }
};

const cancelTemplateEdit = () => {
  editingTemplate.value = null;
  templateModalVisible.value = false;
};

// 工具函数
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀';
  if (score >= 80) return '良好';
  if (score >= 70) return '中等';
  if (score >= 60) return '及格';
  return '不及格';
};

const getTemplateTypeColor = (type: ReviewTemplateType) => {
  const colorMap = {
    'approve': 'green',
    'reject_incomplete': 'red',
    'reject_quality': 'red',
    'revise_format': 'orange',
    'revise_content': 'orange',
    'custom': 'blue'
  };
  return colorMap[type] || 'default';
};

const getTemplateTypeText = (type: ReviewTemplateType) => {
  const textMap = {
    'approve': '通过',
    'reject_incomplete': '驳回',
    'reject_quality': '驳回',
    'revise_format': '修改',
    'revise_content': '修改',
    'custom': '自定义'
  };
  return textMap[type] || '未知';
};

const getResultColor = (result: CaseReviewResult) => {
  const colorMap = {
    1: 'success',
    2: 'error',
    3: 'warning'
  };
  return colorMap[result] || 'default';
};

const getResultText = (result: CaseReviewResult) => {
  const textMap = {
    1: '通过',
    2: '驳回',
    3: '需修改'
  };
  return textMap[result] || '未知';
};

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

// 生命周期
onMounted(() => {
  loadSettings();
  loadTemplates();
});
</script>

<style scoped lang="less">
.review-settings {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 32px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        color: #1a1a1a;
        font-size: 32px;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 16px;
        line-height: 1.6;
      }
    }

    .header-actions {
      .ant-btn {
        height: 48px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        &.ant-btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }
        }
      }
    }
  }

  .settings-content {
    .settings-card {
      border-radius: 20px;
      border: none;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      overflow: hidden;

      .ant-card-body {
        padding: 0;
      }
    }

    .settings-tabs {
      .ant-tabs-nav {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
        margin: 0;
        padding: 20px 20px 0;
        border-radius: 20px 20px 0 0;

        .ant-tabs-tab {
          border-radius: 12px 12px 0 0;
          border: none;
          background: transparent;
          margin-right: 8px;
          font-weight: 600;
          transition: all 0.3s ease;

          &.ant-tabs-tab-active {
            background: white;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

            .ant-tabs-tab-btn {
              color: #667eea;
            }
          }

          &:hover:not(.ant-tabs-tab-active) {
            background: rgba(255, 255, 255, 0.7);
          }
        }
      }

      .ant-tabs-content-holder {
        background: white;
      }
    }

    .tab-content {
      padding: 32px;
      min-height: 600px;
    }

    // 审核规则样式
    .rules-form {
      .rule-card {
        border-radius: 16px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
          transform: translateY(-2px);
        }

        .ant-card-head {
          border-bottom: 2px solid #f8f9ff;
          border-radius: 16px 16px 0 0;

          .ant-card-head-title {
            font-weight: 600;
            color: #333;
          }
        }

        .ant-card-body {
          padding: 24px;
        }
      }

      .form-help {
        color: #8c8c8c;
        font-size: 12px;
        margin-top: 4px;
        line-height: 1.4;
      }

      .score-display {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 8px;

        .score-value {
          font-weight: 600;
          font-size: 16px;
          color: #667eea;
        }

        .score-level {
          padding: 2px 8px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }

    // 评分标准样式
    .scoring-section {
      .scoring-card {
        border-radius: 16px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f0f0;

        .ant-card-head {
          border-bottom: 2px solid #f8f9ff;
          border-radius: 16px 16px 0 0;

          .ant-card-head-title {
            font-weight: 600;
            color: #333;
          }
        }
      }

      .criteria-list {
        .criteria-item {
          margin-bottom: 16px;

          .criteria-card {
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #667eea;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
            }
          }
        }
      }

      .weight-summary {
        margin-top: 24px;

        .ant-alert {
          border-radius: 12px;
          border: none;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }
      }
    }

    // 模板管理样式
    .templates-section {
      .templates-card {
        border-radius: 16px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f0f0;

        .ant-card-head {
          border-bottom: 2px solid #f8f9ff;
          border-radius: 16px 16px 0 0;

          .ant-card-head-title {
            font-weight: 600;
            color: #333;
          }
        }
      }

      .templates-list {
        .template-card {
          border-radius: 12px;
          border: 2px solid #f0f0f0;
          transition: all 0.3s ease;
          height: 100%;

          &:hover {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-4px);
          }

          &.default-template {
            border-color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);

            &::before {
              content: '默认';
              position: absolute;
              top: -1px;
              right: -1px;
              background: #52c41a;
              color: white;
              padding: 2px 8px;
              border-radius: 0 12px 0 8px;
              font-size: 10px;
              font-weight: 600;
            }
          }

          .template-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;

            .template-name {
              font-weight: 600;
              color: #333;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .template-content {
            .template-result {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 12px;

              .template-score {
                font-weight: 600;
                color: #667eea;
                font-size: 16px;
              }
            }

            .template-comments {
              margin-bottom: 16px;

              .comments-text {
                color: #333;
                font-size: 13px;
                line-height: 1.5;
                margin-bottom: 8px;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }

              .suggestions-text {
                color: #8c8c8c;
                font-size: 12px;
                line-height: 1.4;
                margin: 0;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
              }
            }

            .template-meta {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-top: 12px;
              border-top: 1px solid #f0f0f0;
              font-size: 11px;
              color: #8c8c8c;

              .usage-count {
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .review-settings {
    .settings-content {
      .tab-content {
        padding: 24px;
      }

      .templates-section {
        .templates-list {
          .ant-col {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .review-settings {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 20px;
      padding: 24px;

      .header-content {
        h2 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
        }
      }

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }

        .ant-btn {
          height: 40px;
          font-size: 13px;
        }
      }
    }

    .settings-content {
      .settings-card {
        border-radius: 16px;
      }

      .settings-tabs {
        .ant-tabs-nav {
          padding: 16px 16px 0;
          border-radius: 16px 16px 0 0;

          .ant-tabs-tab {
            margin-right: 4px;
            font-size: 13px;
          }
        }
      }

      .tab-content {
        padding: 20px 16px;
      }

      .rules-form {
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }
      }

      .scoring-section {
        .criteria-list {
          .criteria-item {
            .criteria-card {
              .ant-row {
                .ant-col {
                  margin-bottom: 12px;
                }
              }
            }
          }
        }
      }

      .templates-section {
        .templates-list {
          .template-card {
            margin-bottom: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .review-settings {
    padding: 12px;

    .page-header {
      padding: 20px;
      border-radius: 16px;

      .header-content {
        h2 {
          font-size: 20px;
        }
      }

      .header-actions {
        .ant-btn {
          height: 36px;
          font-size: 12px;
          padding: 0 12px;
        }
      }
    }

    .settings-content {
      .settings-card {
        border-radius: 12px;
      }

      .settings-tabs {
        .ant-tabs-nav {
          padding: 12px 12px 0;
          border-radius: 12px 12px 0 0;

          .ant-tabs-tab {
            font-size: 12px;
            padding: 8px 12px;
          }
        }
      }

      .tab-content {
        padding: 16px 12px;
      }

      .rules-form {
        .rule-card {
          .ant-card-body {
            padding: 16px;
          }
        }
      }

      .scoring-section {
        .criteria-list {
          .criteria-card {
            .ant-card-body {
              padding: 12px;
            }
          }
        }
      }

      .templates-section {
        .template-card {
          .ant-card-body {
            padding: 12px;
          }

          .template-content {
            .template-comments {
              .comments-text {
                -webkit-line-clamp: 3;
              }
            }
          }
        }
      }
    }
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .review-settings {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);

    .page-header,
    .settings-card {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .settings-tabs {
      .ant-tabs-nav {
        background: rgba(255, 255, 255, 0.03);
      }

      .ant-tabs-content-holder {
        background: rgba(255, 255, 255, 0.02);
      }
    }

    .rule-card,
    .scoring-card,
    .templates-card,
    .criteria-card,
    .template-card {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
    }
  }
}

// 打印样式
@media print {
  .review-settings {
    background: white;
    padding: 0;

    .page-header {
      .header-actions {
        display: none;
      }
    }

    .settings-tabs {
      .ant-tabs-nav {
        display: none;
      }
    }
  }
}
</style>
