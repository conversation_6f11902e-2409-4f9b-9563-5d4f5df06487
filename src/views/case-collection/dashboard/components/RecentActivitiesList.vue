<template>
  <div class="recent-activities-list">
    <a-spin :spinning="loading">
      <div class="list-wrapper">
        <div v-if="!data.length" class="empty-list">
          <a-empty description="暂无最新动态" />
        </div>
        
        <div v-else class="list-content">
          <a-timeline>
            <a-timeline-item
              v-for="(item, index) in data"
              :key="index"
              :color="getTimelineColor(item.type)"
            >
              <template #dot>
                <div class="timeline-dot" :class="item.type">
                  <component :is="getTypeIcon(item.type)" />
                </div>
              </template>
              
              <div class="activity-item" @click="handleViewDetail(item)">
                <div class="activity-header">
                  <div class="activity-info">
                    <h4 class="activity-title">{{ item.title }}</h4>
                    <div class="activity-meta">
                      <a-tag :color="getTypeColor(item.type)" size="small">
                        {{ getTypeText(item.type) }}
                      </a-tag>
                      <span class="activity-time">{{ formatTime(item.createTime) }}</span>
                    </div>
                  </div>
                  <div class="activity-status">
                    <a-tag :color="getStatusColor(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </a-tag>
                  </div>
                </div>
                
                <div class="activity-content">
                  <div class="activity-description">
                    {{ item.description }}
                  </div>
                  
                  <div v-if="item.details" class="activity-details">
                    <div v-for="detail in item.details" :key="detail.key" class="detail-item">
                      <span class="detail-label">{{ detail.label }}:</span>
                      <span class="detail-value">{{ detail.value }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="activity-footer">
                  <div class="activity-user">
                    <a-avatar :src="item.userAvatar" :size="24">
                      {{ item.userName?.charAt(0) }}
                    </a-avatar>
                    <span class="user-name">{{ item.userName }}</span>
                    <span class="user-role">{{ item.userRole }}</span>
                  </div>
                  
                  <div class="activity-actions">
                    <a-button type="link" size="small" @click.stop="handleViewDetail(item)">
                      <template #icon><eye-outlined /></template>
                      查看
                    </a-button>
                    <a-button 
                      v-if="item.type === 'submission'" 
                      type="link" 
                      size="small" 
                      @click.stop="handleReview(item)"
                    >
                      <template #icon><audit-outlined /></template>
                      审核
                    </a-button>
                    <a-dropdown v-if="hasMoreActions(item)">
                      <a-button type="link" size="small">
                        <template #icon><more-outlined /></template>
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="handleEdit(item)" v-if="canEdit(item)">
                            <edit-outlined />
                            编辑
                          </a-menu-item>
                          <a-menu-item @click="handleShare(item)">
                            <share-alt-outlined />
                            分享
                          </a-menu-item>
                          <a-menu-item @click="handleExport(item)">
                            <export-outlined />
                            导出
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </div>
                </div>
              </div>
            </a-timeline-item>
          </a-timeline>
          
          <!-- 加载更多 -->
          <div v-if="hasMore" class="load-more">
            <a-button @click="loadMore" :loading="loadingMore">
              加载更多
            </a-button>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  EyeOutlined,
  AuditOutlined,
  MoreOutlined,
  EditOutlined,
  ShareAltOutlined,
  ExportOutlined,
  FileTextOutlined,
  CalendarOutlined,
  UserOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';

interface ActivityDetail {
  key: string;
  label: string;
  value: string;
}

interface RecentActivity {
  id: number;
  type: 'submission' | 'activity' | 'review' | 'user';
  title: string;
  description: string;
  status: string;
  createTime: string;
  userName: string;
  userRole: string;
  userAvatar?: string;
  details?: ActivityDetail[];
}

interface Props {
  data: RecentActivity[];
  loading?: boolean;
  hasMore?: boolean;
}

interface Emits {
  (e: 'view-detail', item: RecentActivity): void;
  (e: 'load-more'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasMore: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const loadingMore = ref(false);

// 方法
const getTimelineColor = (type: string) => {
  const colors: Record<string, string> = {
    'submission': '#1890ff',
    'activity': '#52c41a',
    'review': '#faad14',
    'user': '#722ed1'
  };
  return colors[type] || '#d9d9d9';
};

const getTypeIcon = (type: string) => {
  const icons: Record<string, any> = {
    'submission': FileTextOutlined,
    'activity': CalendarOutlined,
    'review': AuditOutlined,
    'user': UserOutlined
  };
  return icons[type] || FileTextOutlined;
};

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'submission': 'blue',
    'activity': 'green',
    'review': 'orange',
    'user': 'purple'
  };
  return colors[type] || 'default';
};

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    'submission': '案例提交',
    'activity': '活动动态',
    'review': '审核动态',
    'user': '用户动态'
  };
  return texts[type] || '未知';
};

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'submitted': 'blue',
    'reviewing': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'active': 'green',
    'completed': 'default'
  };
  return colors[status] || 'default';
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'active': '进行中',
    'completed': '已完成'
  };
  return texts[status] || '未知';
};

const formatTime = (time: string) => {
  const now = new Date();
  const createTime = new Date(time);
  const diff = now.getTime() - createTime.getTime();
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  
  return createTime.toLocaleDateString();
};

const hasMoreActions = (item: RecentActivity) => {
  return canEdit(item) || true; // 总是显示分享和导出
};

const canEdit = (item: RecentActivity) => {
  return ['submission', 'activity'].includes(item.type) && 
         ['draft', 'submitted'].includes(item.status);
};

const handleViewDetail = (item: RecentActivity) => {
  emit('view-detail', item);
};

const handleReview = (item: RecentActivity) => {
  // 处理审核操作
};

const handleEdit = (item: RecentActivity) => {
  // 处理编辑操作
};

const handleShare = (item: RecentActivity) => {
  // 处理分享操作
};

const handleExport = (item: RecentActivity) => {
  // 处理导出操作
};

const loadMore = () => {
  loadingMore.value = true;
  emit('load-more');
  setTimeout(() => {
    loadingMore.value = false;
  }, 1000);
};
</script>

<style scoped lang="scss">
.recent-activities-list {
  .list-wrapper {
    min-height: 300px;

    .empty-list {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
    }

    .list-content {
      :deep(.ant-timeline) {
        .ant-timeline-item {
          padding-bottom: 20px;

          .ant-timeline-item-tail {
            border-left: 2px solid #f0f0f0;
          }

          .ant-timeline-item-head {
            background: transparent;
            border: none;
          }
        }
      }

      .timeline-dot {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: white;
        font-size: 14px;

        &.submission {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        }

        &.activity {
          background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        }

        &.review {
          background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
        }

        &.user {
          background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
        }
      }

      .activity-item {
        background: white;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 16px;
        margin-left: 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        }

        .activity-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;

          .activity-info {
            flex: 1;

            .activity-title {
              margin: 0 0 8px 0;
              font-size: 14px;
              font-weight: 600;
              color: #262626;
            }

            .activity-meta {
              display: flex;
              align-items: center;
              gap: 8px;

              .activity-time {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }

          .activity-status {
            flex-shrink: 0;
          }
        }

        .activity-content {
          margin-bottom: 12px;

          .activity-description {
            font-size: 13px;
            color: #595959;
            line-height: 1.5;
            margin-bottom: 8px;
          }

          .activity-details {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;

            .detail-item {
              font-size: 12px;

              .detail-label {
                color: #8c8c8c;
                margin-right: 4px;
              }

              .detail-value {
                color: #262626;
                font-weight: 500;
              }
            }
          }
        }

        .activity-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .activity-user {
            display: flex;
            align-items: center;
            gap: 8px;

            .user-name {
              font-size: 13px;
              color: #262626;
              font-weight: 500;
            }

            .user-role {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .activity-actions {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }

      .load-more {
        text-align: center;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}

@media (max-width: 768px) {
  .recent-activities-list {
    .list-content {
      .activity-item {
        margin-left: 8px;
        padding: 12px;

        .activity-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }

        .activity-content {
          .activity-details {
            flex-direction: column;
            gap: 4px;
          }
        }

        .activity-footer {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .activity-actions {
            align-self: flex-end;
          }
        }
      }
    }
  }
}
</style>
