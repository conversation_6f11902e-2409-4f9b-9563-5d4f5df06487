<template>
  <div class="review-status-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <bar-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 状态概览 -->
          <div class="status-overview">
            <div
              v-for="status in statusData"
              :key="status.key"
              class="status-card"
              :class="status.key"
              @click="handleStatusClick(status)"
            >
              <div class="status-icon">
                <component :is="status.icon" />
              </div>
              <div class="status-info">
                <div class="status-count">{{ status.count }}</div>
                <div class="status-label">{{ status.label }}</div>
                <div class="status-percentage">{{ getPercentage(status.count) }}%</div>
              </div>
              <div class="status-trend">
                <span class="trend-indicator" :class="getTrendClass(status.trend)">
                  <arrow-up-outlined v-if="status.trend > 0" />
                  <arrow-down-outlined v-else-if="status.trend < 0" />
                  <minus-outlined v-else />
                  {{ Math.abs(status.trend || 0) }}%
                </span>
              </div>
            </div>
          </div>
          
          <!-- 审核进度条 -->
          <div class="review-progress">
            <div class="progress-header">
              <h4>审核进度</h4>
              <span class="progress-text">
                {{ reviewedCount }}/{{ totalCount }} 已审核
              </span>
            </div>
            
            <div class="progress-bar-container">
              <div class="progress-segments">
                <div
                  v-for="status in statusData"
                  :key="status.key"
                  class="progress-segment"
                  :class="status.key"
                  :style="{ width: `${getPercentage(status.count)}%` }"
                  :title="`${status.label}: ${status.count} (${getPercentage(status.count)}%)`"
                ></div>
              </div>
              
              <div class="progress-labels">
                <div
                  v-for="status in statusData"
                  :key="status.key"
                  class="progress-label"
                  :style="{ width: `${getPercentage(status.count)}%` }"
                >
                  <span v-if="getPercentage(status.count) > 10">
                    {{ getPercentage(status.count) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 审核效率统计 -->
          <div class="efficiency-stats">
            <div class="stats-header">
              <h4>审核效率</h4>
            </div>
            
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-icon">
                  <clock-circle-outlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ averageReviewTime }}</div>
                  <div class="stat-label">平均审核时间</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <team-outlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ activeReviewers }}</div>
                  <div class="stat-label">活跃审核员</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <check-circle-outlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ dailyReviewCount }}</div>
                  <div class="stat-label">今日审核量</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon">
                  <rise-outlined />
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ approvalRate }}%</div>
                  <div class="stat-label">通过率</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 审核员排行 -->
          <div class="reviewer-ranking">
            <div class="ranking-header">
              <h4>审核员排行</h4>
              <a-button type="link" size="small" @click="viewAllReviewers">
                查看全部
              </a-button>
            </div>
            
            <div class="ranking-list">
              <div
                v-for="(reviewer, index) in topReviewers"
                :key="reviewer.id"
                class="reviewer-item"
              >
                <div class="reviewer-rank">{{ index + 1 }}</div>
                <div class="reviewer-avatar">
                  <a-avatar :src="reviewer.avatar" :size="32">
                    {{ reviewer.name.charAt(0) }}
                  </a-avatar>
                </div>
                <div class="reviewer-info">
                  <div class="reviewer-name">{{ reviewer.name }}</div>
                  <div class="reviewer-dept">{{ reviewer.department }}</div>
                </div>
                <div class="reviewer-stats">
                  <div class="review-count">{{ reviewer.reviewCount }}</div>
                  <div class="review-rate">{{ reviewer.approvalRate }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  BarChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  RiseOutlined,
  ClockCircleFilled,
  EyeFilled,
  CheckCircleFilled,
  CloseCircleFilled
} from '@ant-design/icons-vue';

interface StatusData {
  key: string;
  label: string;
  count: number;
  icon: any;
  trend?: number;
}

interface ReviewerData {
  id: number;
  name: string;
  department: string;
  avatar?: string;
  reviewCount: number;
  approvalRate: number;
}

interface Props {
  data: StatusData[];
  loading?: boolean;
}

interface Emits {
  (e: 'status-click', status: StatusData): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 模拟数据
const topReviewers = ref<ReviewerData[]>([
  { id: 1, name: '张三', department: '办公室', reviewCount: 45, approvalRate: 85 },
  { id: 2, name: '李四', department: '人事处', reviewCount: 38, approvalRate: 78 },
  { id: 3, name: '王五', department: '财务处', reviewCount: 32, approvalRate: 92 },
  { id: 4, name: '赵六', department: '技术部', reviewCount: 28, approvalRate: 88 },
  { id: 5, name: '钱七', department: '市场部', reviewCount: 25, approvalRate: 76 }
]);

// 计算属性
const statusData = computed(() => {
  const statusMap = {
    'submitted': { label: '待审核', icon: ClockCircleFilled },
    'reviewing': { label: '审核中', icon: EyeFilled },
    'approved': { label: '已通过', icon: CheckCircleFilled },
    'rejected': { label: '已驳回', icon: CloseCircleFilled }
  };
  
  return props.data.map(item => ({
    ...item,
    label: statusMap[item.key as keyof typeof statusMap]?.label || item.label,
    icon: statusMap[item.key as keyof typeof statusMap]?.icon || ClockCircleFilled
  }));
});

const totalCount = computed(() => {
  return statusData.value.reduce((sum, item) => sum + item.count, 0);
});

const reviewedCount = computed(() => {
  return statusData.value
    .filter(item => ['approved', 'rejected'].includes(item.key))
    .reduce((sum, item) => sum + item.count, 0);
});

const approvalRate = computed(() => {
  const approved = statusData.value.find(item => item.key === 'approved')?.count || 0;
  if (reviewedCount.value === 0) return 0;
  return Math.round((approved / reviewedCount.value) * 100);
});

const averageReviewTime = computed(() => {
  return '2.3天'; // 模拟数据
});

const activeReviewers = computed(() => {
  return 12; // 模拟数据
});

const dailyReviewCount = computed(() => {
  return 28; // 模拟数据
});

// 方法
const getPercentage = (count: number) => {
  if (totalCount.value === 0) return 0;
  return Math.round((count / totalCount.value) * 100);
};

const getTrendClass = (trend: number = 0) => {
  if (trend > 0) return 'trend-up';
  if (trend < 0) return 'trend-down';
  return 'trend-flat';
};

const handleStatusClick = (status: StatusData) => {
  emit('status-click', status);
};

const viewAllReviewers = () => {
  // 查看全部审核员
};
</script>

<style scoped lang="scss">
.review-status-chart {
  .chart-wrapper {
    min-height: 400px;

    .empty-chart {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }

    .chart-content {
      .status-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;

        .status-card {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;

          &.submitted {
            background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
            border: 1px solid #ffd591;

            &:hover {
              box-shadow: 0 4px 12px rgba(255, 213, 145, 0.3);
            }
          }

          &.reviewing {
            background: linear-gradient(135deg, #e6f7ff 0%, #91d5ff 100%);
            border: 1px solid #91d5ff;

            &:hover {
              box-shadow: 0 4px 12px rgba(145, 213, 255, 0.3);
            }
          }

          &.approved {
            background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
            border: 1px solid #b7eb8f;

            &:hover {
              box-shadow: 0 4px 12px rgba(183, 235, 143, 0.3);
            }
          }

          &.rejected {
            background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
            border: 1px solid #ffccc7;

            &:hover {
              box-shadow: 0 4px 12px rgba(255, 204, 199, 0.3);
            }
          }

          .status-icon {
            font-size: 24px;
            color: #1890ff;
          }

          .status-info {
            flex: 1;

            .status-count {
              font-size: 24px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 4px;
            }

            .status-label {
              font-size: 14px;
              color: #8c8c8c;
              margin-bottom: 2px;
            }

            .status-percentage {
              font-size: 12px;
              color: #1890ff;
              font-weight: 500;
            }
          }

          .status-trend {
            .trend-indicator {
              display: flex;
              align-items: center;
              gap: 2px;
              font-size: 12px;

              &.trend-up {
                color: #52c41a;
              }

              &.trend-down {
                color: #ff4d4f;
              }

              &.trend-flat {
                color: #8c8c8c;
              }
            }
          }
        }
      }

      .review-progress {
        margin-bottom: 24px;

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }

          .progress-text {
            font-size: 14px;
            color: #8c8c8c;
          }
        }

        .progress-bar-container {
          .progress-segments {
            display: flex;
            height: 24px;
            border-radius: 12px;
            overflow: hidden;
            background: #f5f5f5;

            .progress-segment {
              transition: all 0.3s ease;

              &.submitted {
                background: #faad14;
              }

              &.reviewing {
                background: #1890ff;
              }

              &.approved {
                background: #52c41a;
              }

              &.rejected {
                background: #ff4d4f;
              }
            }
          }

          .progress-labels {
            display: flex;
            margin-top: 8px;

            .progress-label {
              display: flex;
              justify-content: center;
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }

      .efficiency-stats {
        margin-bottom: 24px;

        .stats-header {
          margin-bottom: 16px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 16px;

          .stat-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #fafafa;
            border-radius: 8px;

            .stat-icon {
              font-size: 20px;
              color: #1890ff;
            }

            .stat-content {
              .stat-value {
                font-size: 18px;
                font-weight: 600;
                color: #262626;
                margin-bottom: 2px;
              }

              .stat-label {
                font-size: 12px;
                color: #8c8c8c;
              }
            }
          }
        }
      }

      .reviewer-ranking {
        .ranking-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }
        }

        .ranking-list {
          .reviewer-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              background: #f6ffed;
            }

            &:last-child {
              margin-bottom: 0;
            }

            .reviewer-rank {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #1890ff;
              color: white;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 500;
            }

            .reviewer-avatar {
              flex-shrink: 0;
            }

            .reviewer-info {
              flex: 1;

              .reviewer-name {
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 2px;
              }

              .reviewer-dept {
                font-size: 12px;
                color: #8c8c8c;
              }
            }

            .reviewer-stats {
              text-align: right;

              .review-count {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }

              .review-rate {
                font-size: 12px;
                color: #52c41a;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .review-status-chart {
    .chart-content {
      .status-overview {
        grid-template-columns: 1fr;
        gap: 12px;

        .status-card {
          padding: 12px;
          gap: 8px;

          .status-icon {
            font-size: 20px;
          }

          .status-info {
            .status-count {
              font-size: 20px;
            }
          }
        }
      }

      .efficiency-stats {
        .stats-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;

          .stat-item {
            padding: 12px;
            gap: 8px;

            .stat-icon {
              font-size: 16px;
            }

            .stat-content {
              .stat-value {
                font-size: 16px;
              }
            }
          }
        }
      }

      .reviewer-ranking {
        .ranking-list {
          .reviewer-item {
            gap: 8px;
            padding: 8px;

            .reviewer-stats {
              .review-count {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
