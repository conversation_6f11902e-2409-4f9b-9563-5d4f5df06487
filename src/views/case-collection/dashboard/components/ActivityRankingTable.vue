<template>
  <div class="activity-ranking-table">
    <a-spin :spinning="loading">
      <div class="table-wrapper">
        <div v-if="!data.length" class="empty-table">
          <a-empty description="暂无排行数据" />
        </div>
        
        <div v-else class="table-content">
          <div class="ranking-list">
            <div
              v-for="(activity, index) in data"
              :key="activity.id"
              class="ranking-item"
              :class="getRankClass(index)"
              @click="handleViewActivity(activity)"
            >
              <!-- 排名 -->
              <div class="rank-badge">
                <div class="rank-number" :class="getRankClass(index)">
                  {{ index + 1 }}
                </div>
                <div v-if="index < 3" class="rank-medal">
                  <trophy-outlined v-if="index === 0" />
                  <medal-outlined v-else />
                </div>
              </div>
              
              <!-- 活动信息 -->
              <div class="activity-info">
                <div class="activity-header">
                  <h4 class="activity-title">{{ activity.title }}</h4>
                  <a-tag :color="getStatusColor(activity.status)">
                    {{ getStatusText(activity.status) }}
                  </a-tag>
                </div>
                
                <div class="activity-meta">
                  <span class="meta-item">
                    <calendar-outlined />
                    {{ formatDateRange(activity.startTime, activity.endTime) }}
                  </span>
                  <span class="meta-item">
                    <user-outlined />
                    {{ activity.organizerName }}
                  </span>
                </div>
                
                <div class="activity-description">
                  {{ activity.description || '暂无描述' }}
                </div>
              </div>
              
              <!-- 统计数据 -->
              <div class="activity-stats">
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-value">{{ activity.submissionCount }}</div>
                    <div class="stat-label">提交数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ activity.participantCount }}</div>
                    <div class="stat-label">参与人数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ activity.approvalRate }}%</div>
                    <div class="stat-label">通过率</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ activity.score }}</div>
                    <div class="stat-label">综合评分</div>
                  </div>
                </div>
                
                <!-- 进度条 -->
                <div class="progress-section">
                  <div class="progress-info">
                    <span class="progress-label">活动进度</span>
                    <span class="progress-percentage">{{ getProgressPercentage(activity) }}%</span>
                  </div>
                  <a-progress 
                    :percent="getProgressPercentage(activity)" 
                    :stroke-color="getProgressColor(activity)"
                    :show-info="false"
                    size="small"
                  />
                </div>
              </div>
              
              <!-- 趋势指标 -->
              <div class="activity-trend">
                <div class="trend-item">
                  <span class="trend-label">热度</span>
                  <div class="trend-bar">
                    <div 
                      class="trend-fill"
                      :style="{ width: `${activity.popularity}%`, backgroundColor: '#1890ff' }"
                    ></div>
                  </div>
                  <span class="trend-value">{{ activity.popularity }}%</span>
                </div>
                
                <div class="trend-item">
                  <span class="trend-label">活跃度</span>
                  <div class="trend-bar">
                    <div 
                      class="trend-fill"
                      :style="{ width: `${activity.activity}%`, backgroundColor: '#52c41a' }"
                    ></div>
                  </div>
                  <span class="trend-value">{{ activity.activity }}%</span>
                </div>
                
                <div class="trend-item">
                  <span class="trend-label">质量</span>
                  <div class="trend-bar">
                    <div 
                      class="trend-fill"
                      :style="{ width: `${activity.quality}%`, backgroundColor: '#faad14' }"
                    ></div>
                  </div>
                  <span class="trend-value">{{ activity.quality }}%</span>
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="activity-actions">
                <a-button type="link" size="small" @click.stop="handleViewActivity(activity)">
                  <template #icon><eye-outlined /></template>
                  查看
                </a-button>
                <a-button type="link" size="small" @click.stop="handleViewSubmissions(activity)">
                  <template #icon><file-text-outlined /></template>
                  案例
                </a-button>
                <a-button type="link" size="small" @click.stop="handleViewAnalytics(activity)">
                  <template #icon><bar-chart-outlined /></template>
                  分析
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  TrophyOutlined,
  StarOutlined,
  CalendarOutlined,
  UserOutlined,
  EyeOutlined,
  FileTextOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';

interface ActivityRanking {
  id: number;
  title: string;
  description?: string;
  status: string;
  startTime: string;
  endTime: string;
  organizerName: string;
  submissionCount: number;
  participantCount: number;
  approvalRate: number;
  score: number;
  popularity: number;
  activity: number;
  quality: number;
}

interface Props {
  data: ActivityRanking[];
  loading?: boolean;
}

interface Emits {
  (e: 'view-activity', activity: ActivityRanking): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 方法
const getRankClass = (index: number) => {
  if (index === 0) return 'rank-first';
  if (index === 1) return 'rank-second';
  if (index === 2) return 'rank-third';
  return 'rank-normal';
};

const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'active': 'blue',
    'reviewing': 'orange',
    'completed': 'green',
    'cancelled': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'active': '进行中',
    'reviewing': '评审中',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusTexts[status] || '未知';
};

const getProgressPercentage = (activity: ActivityRanking) => {
  const now = new Date().getTime();
  const start = new Date(activity.startTime).getTime();
  const end = new Date(activity.endTime).getTime();
  
  if (now < start) return 0;
  if (now > end) return 100;
  
  return Math.round(((now - start) / (end - start)) * 100);
};

const getProgressColor = (activity: ActivityRanking) => {
  const progress = getProgressPercentage(activity);
  if (progress >= 80) return '#52c41a';
  if (progress >= 50) return '#faad14';
  return '#1890ff';
};

const formatDateRange = (startTime: string, endTime: string) => {
  const start = new Date(startTime).toLocaleDateString();
  const end = new Date(endTime).toLocaleDateString();
  return `${start} - ${end}`;
};

const handleViewActivity = (activity: ActivityRanking) => {
  emit('view-activity', activity);
};

const handleViewSubmissions = (activity: ActivityRanking) => {
  // 查看活动案例
};

const handleViewAnalytics = (activity: ActivityRanking) => {
  // 查看活动分析
};
</script>

<style scoped lang="scss">
.activity-ranking-table {
  .table-wrapper {
    min-height: 300px;

    .empty-table {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
    }

    .table-content {
      .ranking-list {
        .ranking-item {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          padding: 20px;
          border: 1px solid #f0f0f0;
          border-radius: 12px;
          margin-bottom: 16px;
          background: white;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
            transform: translateY(-2px);
          }

          &:last-child {
            margin-bottom: 0;
          }

          &.rank-first {
            border-color: #faad14;
            background: linear-gradient(135deg, #fff7e6 0%, #fffbe6 100%);
          }

          &.rank-second {
            border-color: #d9d9d9;
            background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
          }

          &.rank-third {
            border-color: #d48806;
            background: linear-gradient(135deg, #fff2e8 0%, #fff7e6 100%);
          }

          .rank-badge {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;

            .rank-number {
              width: 40px;
              height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 50%;
              font-size: 18px;
              font-weight: 600;
              color: white;

              &.rank-first {
                background: linear-gradient(135deg, #faad14 0%, #ffd666 100%);
              }

              &.rank-second {
                background: linear-gradient(135deg, #d9d9d9 0%, #f0f0f0 100%);
                color: #262626;
              }

              &.rank-third {
                background: linear-gradient(135deg, #d48806 0%, #faad14 100%);
              }

              &.rank-normal {
                background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
              }
            }

            .rank-medal {
              font-size: 16px;
              color: #faad14;
            }
          }

          .activity-info {
            flex: 1;
            min-width: 0;

            .activity-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 8px;

              .activity-title {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
                color: #262626;
                flex: 1;
                margin-right: 12px;
              }
            }

            .activity-meta {
              display: flex;
              gap: 16px;
              margin-bottom: 8px;
              font-size: 12px;
              color: #8c8c8c;

              .meta-item {
                display: flex;
                align-items: center;
                gap: 4px;

                .anticon {
                  color: #1890ff;
                }
              }
            }

            .activity-description {
              font-size: 13px;
              color: #8c8c8c;
              line-height: 1.5;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .activity-stats {
            flex-shrink: 0;
            width: 200px;

            .stats-grid {
              display: grid;
              grid-template-columns: repeat(2, 1fr);
              gap: 8px;
              margin-bottom: 12px;

              .stat-item {
                text-align: center;
                padding: 8px;
                background: #fafafa;
                border-radius: 6px;

                .stat-value {
                  font-size: 16px;
                  font-weight: 600;
                  color: #262626;
                  margin-bottom: 2px;
                }

                .stat-label {
                  font-size: 10px;
                  color: #8c8c8c;
                }
              }
            }

            .progress-section {
              .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;

                .progress-label {
                  font-size: 12px;
                  color: #8c8c8c;
                }

                .progress-percentage {
                  font-size: 12px;
                  font-weight: 500;
                  color: #1890ff;
                }
              }
            }
          }

          .activity-trend {
            flex-shrink: 0;
            width: 120px;

            .trend-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .trend-label {
                font-size: 10px;
                color: #8c8c8c;
                width: 30px;
                flex-shrink: 0;
              }

              .trend-bar {
                flex: 1;
                height: 6px;
                background: #f5f5f5;
                border-radius: 3px;
                overflow: hidden;

                .trend-fill {
                  height: 100%;
                  transition: width 0.3s ease;
                }
              }

              .trend-value {
                font-size: 10px;
                color: #262626;
                width: 25px;
                text-align: right;
                flex-shrink: 0;
              }
            }
          }

          .activity-actions {
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            gap: 4px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .activity-ranking-table {
    .table-content {
      .ranking-list {
        .ranking-item {
          flex-direction: column;
          gap: 12px;
          padding: 16px;

          .rank-badge {
            align-self: flex-start;
          }

          .activity-info {
            width: 100%;

            .activity-header {
              flex-direction: column;
              align-items: flex-start;
              gap: 8px;

              .activity-title {
                margin-right: 0;
              }
            }

            .activity-meta {
              flex-direction: column;
              gap: 4px;
            }
          }

          .activity-stats {
            width: 100%;

            .stats-grid {
              grid-template-columns: repeat(4, 1fr);
              gap: 6px;

              .stat-item {
                padding: 6px;

                .stat-value {
                  font-size: 14px;
                }

                .stat-label {
                  font-size: 9px;
                }
              }
            }
          }

          .activity-trend {
            width: 100%;

            .trend-item {
              .trend-label {
                width: 40px;
              }

              .trend-value {
                width: 30px;
              }
            }
          }

          .activity-actions {
            flex-direction: row;
            justify-content: center;
            gap: 8px;
          }
        }
      }
    }
  }
}
</style>
