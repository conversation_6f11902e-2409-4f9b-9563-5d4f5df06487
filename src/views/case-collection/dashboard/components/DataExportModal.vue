<template>
  <a-modal
    v-model:visible="modalVisible"
    title="数据导出"
    width="600px"
    @ok="handleExport"
    @cancel="handleCancel"
    :confirm-loading="exporting"
    ok-text="导出"
    cancel-text="取消"
  >
    <div class="data-export-modal">
      <a-form :model="exportConfig" layout="vertical">
        <!-- 导出类型 -->
        <a-form-item label="导出类型" required>
          <a-radio-group v-model:value="exportConfig.type">
            <a-radio value="activities">活动数据</a-radio>
            <a-radio value="submissions">案例数据</a-radio>
            <a-radio value="reviews">审核数据</a-radio>
            <a-radio value="statistics">统计数据</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <!-- 数据范围 -->
        <a-form-item label="数据范围" required>
          <a-select v-model:value="exportConfig.scope" placeholder="请选择数据范围">
            <a-select-option value="all">全部数据</a-select-option>
            <a-select-option value="current">当前筛选结果</a-select-option>
            <a-select-option value="custom">自定义范围</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 时间范围 -->
        <a-form-item label="时间范围" v-if="exportConfig.scope === 'custom'">
          <a-range-picker
            v-model:value="exportConfig.dateRange"
            style="width: 100%"
            :placeholder="['开始日期', '结束日期']"
          />
        </a-form-item>
        
        <!-- 活动选择 -->
        <a-form-item 
          label="选择活动" 
          v-if="exportConfig.type !== 'statistics' && exportConfig.scope !== 'all'"
        >
          <a-select
            v-model:value="exportConfig.activityIds"
            mode="multiple"
            placeholder="请选择活动（留空表示全部）"
            :options="activityOptions"
            :max-tag-count="3"
          />
        </a-form-item>
        
        <!-- 导出字段 -->
        <a-form-item label="导出字段">
          <div class="field-selection">
            <div class="field-group">
              <div class="group-header">
                <a-checkbox 
                  :indeterminate="getGroupIndeterminate('basic')"
                  :checked="getGroupChecked('basic')"
                  @change="handleGroupChange('basic', $event)"
                >
                  基础信息
                </a-checkbox>
              </div>
              <a-checkbox-group v-model:value="exportConfig.fields" class="field-list">
                <a-checkbox 
                  v-for="field in basicFields" 
                  :key="field.value" 
                  :value="field.value"
                >
                  {{ field.label }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
            
            <div class="field-group">
              <div class="group-header">
                <a-checkbox 
                  :indeterminate="getGroupIndeterminate('extended')"
                  :checked="getGroupChecked('extended')"
                  @change="handleGroupChange('extended', $event)"
                >
                  扩展信息
                </a-checkbox>
              </div>
              <a-checkbox-group v-model:value="exportConfig.fields" class="field-list">
                <a-checkbox 
                  v-for="field in extendedFields" 
                  :key="field.value" 
                  :value="field.value"
                >
                  {{ field.label }}
                </a-checkbox>
              </a-checkbox-group>
            </div>
          </div>
        </a-form-item>
        
        <!-- 导出格式 -->
        <a-form-item label="导出格式" required>
          <a-radio-group v-model:value="exportConfig.format">
            <a-radio value="excel">Excel (.xlsx)</a-radio>
            <a-radio value="csv">CSV (.csv)</a-radio>
            <a-radio value="pdf">PDF (.pdf)</a-radio>
            <a-radio value="json">JSON (.json)</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <!-- 高级选项 -->
        <a-form-item label="高级选项">
          <a-space direction="vertical" style="width: 100%">
            <a-checkbox v-model:checked="exportConfig.includeAttachments">
              包含附件文件
            </a-checkbox>
            <a-checkbox v-model:checked="exportConfig.includeImages">
              包含图片文件
            </a-checkbox>
            <a-checkbox v-model:checked="exportConfig.compressFiles">
              压缩导出文件
            </a-checkbox>
            <a-checkbox v-model:checked="exportConfig.addWatermark">
              添加水印
            </a-checkbox>
          </a-space>
        </a-form-item>
        
        <!-- 导出预览 -->
        <a-form-item label="导出预览">
          <div class="export-preview">
            <div class="preview-info">
              <div class="info-item">
                <span class="info-label">预计记录数：</span>
                <span class="info-value">{{ estimatedRecords }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">预计文件大小：</span>
                <span class="info-value">{{ estimatedSize }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">预计导出时间：</span>
                <span class="info-value">{{ estimatedTime }}</span>
              </div>
            </div>
            
            <div class="preview-warning" v-if="hasWarning">
              <a-alert
                :message="warningMessage"
                type="warning"
                show-icon
                closable
              />
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

interface ExportConfig {
  type: string;
  scope: string;
  dateRange: any[];
  activityIds: number[];
  fields: string[];
  format: string;
  includeAttachments: boolean;
  includeImages: boolean;
  compressFiles: boolean;
  addWatermark: boolean;
}

interface Props {
  visible: boolean;
  activities: any[];
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'export', config: ExportConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const exporting = ref(false);

const exportConfig = reactive<ExportConfig>({
  type: 'activities',
  scope: 'all',
  dateRange: [],
  activityIds: [],
  fields: ['id', 'title', 'createTime', 'status'],
  format: 'excel',
  includeAttachments: false,
  includeImages: false,
  compressFiles: true,
  addWatermark: false
});

// 字段选项
const basicFields = [
  { label: 'ID', value: 'id' },
  { label: '标题', value: 'title' },
  { label: '创建时间', value: 'createTime' },
  { label: '状态', value: 'status' },
  { label: '创建者', value: 'creator' }
];

const extendedFields = [
  { label: '描述', value: 'description' },
  { label: '分类', value: 'category' },
  { label: '标签', value: 'tags' },
  { label: '附件', value: 'attachments' },
  { label: '审核意见', value: 'reviewComments' }
];

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const activityOptions = computed(() => {
  return props.activities.map(activity => ({
    label: activity.title,
    value: activity.id
  }));
});

const estimatedRecords = computed(() => {
  // 模拟计算预计记录数
  let records = 0;
  switch (exportConfig.type) {
    case 'activities':
      records = 50;
      break;
    case 'submissions':
      records = 500;
      break;
    case 'reviews':
      records = 300;
      break;
    case 'statistics':
      records = 20;
      break;
  }
  
  if (exportConfig.scope === 'current') {
    records = Math.floor(records * 0.6);
  } else if (exportConfig.scope === 'custom') {
    records = Math.floor(records * 0.8);
  }
  
  return records;
});

const estimatedSize = computed(() => {
  const baseSize = estimatedRecords.value * 2; // 每条记录约2KB
  let size = baseSize;
  
  if (exportConfig.includeAttachments) size *= 10;
  if (exportConfig.includeImages) size *= 5;
  if (exportConfig.compressFiles) size *= 0.3;
  
  if (size < 1024) return `${Math.round(size)}KB`;
  if (size < 1024 * 1024) return `${Math.round(size / 1024)}MB`;
  return `${Math.round(size / (1024 * 1024))}GB`;
});

const estimatedTime = computed(() => {
  const records = estimatedRecords.value;
  let seconds = Math.ceil(records / 100); // 每秒处理100条记录
  
  if (exportConfig.includeAttachments || exportConfig.includeImages) {
    seconds *= 5;
  }
  
  if (seconds < 60) return `${seconds}秒`;
  return `${Math.ceil(seconds / 60)}分钟`;
});

const hasWarning = computed(() => {
  return estimatedRecords.value > 1000 || 
         exportConfig.includeAttachments || 
         exportConfig.includeImages;
});

const warningMessage = computed(() => {
  const warnings = [];
  
  if (estimatedRecords.value > 1000) {
    warnings.push('数据量较大，导出时间可能较长');
  }
  
  if (exportConfig.includeAttachments) {
    warnings.push('包含附件将显著增加文件大小');
  }
  
  if (exportConfig.includeImages) {
    warnings.push('包含图片将显著增加文件大小');
  }
  
  return warnings.join('；');
});

// 方法
const getGroupIndeterminate = (group: string) => {
  const fields = group === 'basic' ? basicFields : extendedFields;
  const selectedCount = fields.filter(field => 
    exportConfig.fields.includes(field.value)
  ).length;
  
  return selectedCount > 0 && selectedCount < fields.length;
};

const getGroupChecked = (group: string) => {
  const fields = group === 'basic' ? basicFields : extendedFields;
  return fields.every(field => exportConfig.fields.includes(field.value));
};

const handleGroupChange = (group: string, event: any) => {
  const fields = group === 'basic' ? basicFields : extendedFields;
  const fieldValues = fields.map(field => field.value);
  
  if (event.target.checked) {
    // 添加所有字段
    fieldValues.forEach(value => {
      if (!exportConfig.fields.includes(value)) {
        exportConfig.fields.push(value);
      }
    });
  } else {
    // 移除所有字段
    exportConfig.fields = exportConfig.fields.filter(value => 
      !fieldValues.includes(value)
    );
  }
};

const handleExport = async () => {
  if (exportConfig.fields.length === 0) {
    message.error('请至少选择一个导出字段');
    return;
  }
  
  try {
    exporting.value = true;
    emit('export', { ...exportConfig });
  } finally {
    exporting.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
};

// 监听导出类型变化，自动调整字段选择
watch(() => exportConfig.type, (newType) => {
  if (newType === 'statistics') {
    exportConfig.fields = ['metric', 'value', 'date'];
  } else {
    exportConfig.fields = ['id', 'title', 'createTime', 'status'];
  }
});
</script>

<style scoped lang="scss">
.data-export-modal {
  .field-selection {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;

    .field-group {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .group-header {
        margin-bottom: 8px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
        font-weight: 500;
      }

      .field-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;

        :deep(.ant-checkbox-wrapper) {
          margin-right: 0;
        }
      }
    }
  }

  .export-preview {
    .preview-info {
      background: #fafafa;
      padding: 12px;
      border-radius: 6px;
      margin-bottom: 12px;

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          color: #8c8c8c;
          font-size: 13px;
        }

        .info-value {
          color: #262626;
          font-weight: 500;
          font-size: 13px;
        }
      }
    }

    .preview-warning {
      margin-bottom: 0;
    }
  }
}

@media (max-width: 768px) {
  .data-export-modal {
    .field-selection {
      .field-group {
        .field-list {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
