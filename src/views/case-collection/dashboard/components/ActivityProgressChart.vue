<template>
  <div class="activity-progress-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <bar-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 活动进度列表 -->
          <div class="progress-list">
            <div
              v-for="activity in displayData"
              :key="activity.id"
              class="progress-item"
              @click="handleActivityClick(activity)"
            >
              <div class="activity-header">
                <div class="activity-info">
                  <h4 class="activity-title">{{ activity.title }}</h4>
                  <div class="activity-meta">
                    <span class="meta-item">
                      <calendar-outlined />
                      {{ formatDateRange(activity.startTime, activity.endTime) }}
                    </span>
                    <span class="meta-item">
                      <team-outlined />
                      {{ activity.participantCount }}人参与
                    </span>
                  </div>
                </div>
                <div class="activity-status">
                  <a-tag :color="getStatusColor(activity.status)">
                    {{ getStatusText(activity.status) }}
                  </a-tag>
                </div>
              </div>
              
              <div class="progress-details">
                <div class="progress-stats">
                  <div class="stat-item">
                    <span class="stat-label">总案例</span>
                    <span class="stat-value">{{ activity.totalSubmissions }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">已审核</span>
                    <span class="stat-value">{{ activity.reviewedSubmissions }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">通过率</span>
                    <span class="stat-value">{{ getApprovalRate(activity) }}%</span>
                  </div>
                </div>
                
                <div class="progress-bar-section">
                  <div class="progress-info">
                    <span class="progress-label">整体进度</span>
                    <span class="progress-percentage">{{ getOverallProgress(activity) }}%</span>
                  </div>
                  <a-progress 
                    :percent="getOverallProgress(activity)" 
                    :stroke-color="getProgressColor(activity)"
                    :show-info="false"
                    size="small"
                  />
                </div>
                
                <div class="stage-progress">
                  <div class="stage-item" v-for="stage in activity.stages" :key="stage.name">
                    <div class="stage-header">
                      <span class="stage-name">{{ stage.name }}</span>
                      <span class="stage-count">{{ stage.count }}</span>
                    </div>
                    <div class="stage-bar">
                      <div 
                        class="stage-fill"
                        :style="{ 
                          width: `${getStagePercentage(stage, activity)}%`,
                          backgroundColor: stage.color 
                        }"
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div v-if="data.length > pageSize" class="chart-pagination">
            <a-pagination
              v-model:current="currentPage"
              :total="data.length"
              :page-size="pageSize"
              :show-size-changer="false"
              :show-quick-jumper="false"
              size="small"
              @change="handlePageChange"
            />
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  BarChartOutlined,
  CalendarOutlined,
  TeamOutlined
} from '@ant-design/icons-vue';

interface ActivityProgress {
  id: number;
  title: string;
  status: string;
  startTime: string;
  endTime: string;
  participantCount: number;
  totalSubmissions: number;
  reviewedSubmissions: number;
  approvedSubmissions: number;
  stages: Array<{
    name: string;
    count: number;
    color: string;
  }>;
}

interface Props {
  data: ActivityProgress[];
  loading?: boolean;
}

interface Emits {
  (e: 'activity-click', activity: ActivityProgress): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const currentPage = ref(1);
const pageSize = ref(5);

// 计算属性
const displayData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return props.data.slice(start, end);
});

// 方法
const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'active': 'blue',
    'reviewing': 'orange',
    'completed': 'green',
    'cancelled': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'active': '进行中',
    'reviewing': '评审中',
    'completed': '已完成',
    'cancelled': '已取消'
  };
  return statusTexts[status] || '未知';
};

const getOverallProgress = (activity: ActivityProgress) => {
  if (activity.totalSubmissions === 0) return 0;
  return Math.round((activity.reviewedSubmissions / activity.totalSubmissions) * 100);
};

const getApprovalRate = (activity: ActivityProgress) => {
  if (activity.reviewedSubmissions === 0) return 0;
  return Math.round((activity.approvedSubmissions / activity.reviewedSubmissions) * 100);
};

const getProgressColor = (activity: ActivityProgress) => {
  const progress = getOverallProgress(activity);
  if (progress >= 80) return '#52c41a';
  if (progress >= 50) return '#faad14';
  return '#1890ff';
};

const getStagePercentage = (stage: any, activity: ActivityProgress) => {
  if (activity.totalSubmissions === 0) return 0;
  return Math.round((stage.count / activity.totalSubmissions) * 100);
};

const formatDateRange = (startTime: string, endTime: string) => {
  const start = new Date(startTime).toLocaleDateString();
  const end = new Date(endTime).toLocaleDateString();
  return `${start} - ${end}`;
};

const handleActivityClick = (activity: ActivityProgress) => {
  emit('activity-click', activity);
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

// 监听数据变化，重置分页
watch(() => props.data, () => {
  currentPage.value = 1;
});
</script>

<style scoped lang="scss">
.activity-progress-chart {
  .chart-wrapper {
    min-height: 300px;

    .empty-chart {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
    }

    .chart-content {
      .progress-list {
        .progress-item {
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;
          background: white;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;

            .activity-info {
              flex: 1;

              .activity-title {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }

              .activity-meta {
                display: flex;
                gap: 16px;
                font-size: 12px;
                color: #8c8c8c;

                .meta-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  .anticon {
                    color: #1890ff;
                  }
                }
              }
            }

            .activity-status {
              flex-shrink: 0;
            }
          }

          .progress-details {
            .progress-stats {
              display: flex;
              gap: 24px;
              margin-bottom: 16px;

              .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .stat-label {
                  font-size: 12px;
                  color: #8c8c8c;
                  margin-bottom: 4px;
                }

                .stat-value {
                  font-size: 18px;
                  font-weight: 600;
                  color: #262626;
                }
              }
            }

            .progress-bar-section {
              margin-bottom: 16px;

              .progress-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .progress-label {
                  font-size: 14px;
                  color: #262626;
                }

                .progress-percentage {
                  font-size: 14px;
                  font-weight: 600;
                  color: #1890ff;
                }
              }
            }

            .stage-progress {
              .stage-item {
                margin-bottom: 8px;

                &:last-child {
                  margin-bottom: 0;
                }

                .stage-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 4px;

                  .stage-name {
                    font-size: 12px;
                    color: #8c8c8c;
                  }

                  .stage-count {
                    font-size: 12px;
                    font-weight: 500;
                    color: #262626;
                  }
                }

                .stage-bar {
                  height: 6px;
                  background: #f5f5f5;
                  border-radius: 3px;
                  overflow: hidden;

                  .stage-fill {
                    height: 100%;
                    transition: width 0.3s ease;
                  }
                }
              }
            }
          }
        }
      }

      .chart-pagination {
        display: flex;
        justify-content: center;
        margin-top: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .activity-progress-chart {
    .chart-content {
      .progress-list {
        .progress-item {
          padding: 12px;

          .activity-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            .activity-meta {
              flex-direction: column;
              gap: 4px;
            }
          }

          .progress-details {
            .progress-stats {
              gap: 16px;

              .stat-item {
                .stat-value {
                  font-size: 16px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
