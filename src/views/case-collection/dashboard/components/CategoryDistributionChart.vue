<template>
  <div class="category-distribution-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <pie-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 图表头部信息 -->
          <div class="chart-header">
            <div class="chart-summary">
              <div class="summary-item">
                <span class="summary-label">总分类数</span>
                <span class="summary-value">{{ totalCategories }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">活跃分类</span>
                <span class="summary-value">{{ activeCategories }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">最热分类</span>
                <span class="summary-value">{{ topCategory?.name || '-' }}</span>
              </div>
            </div>
          </div>
          
          <!-- 饼图展示 -->
          <div class="pie-chart-section">
            <div class="pie-chart">
              <svg :width="chartSize" :height="chartSize" class="pie-svg">
                <g :transform="`translate(${chartSize/2}, ${chartSize/2})`">
                  <!-- 饼图扇形 -->
                  <path
                    v-for="(segment, index) in pieSegments"
                    :key="index"
                    :d="segment.path"
                    :fill="segment.color"
                    :stroke="'white'"
                    :stroke-width="2"
                    class="pie-segment"
                    @mouseenter="showTooltip(segment, $event)"
                    @mouseleave="hideTooltip"
                    @click="handleCategoryClick(segment.data)"
                  />
                  
                  <!-- 中心文字 -->
                  <text
                    x="0"
                    y="-10"
                    text-anchor="middle"
                    class="center-text-title"
                  >
                    案例总数
                  </text>
                  <text
                    x="0"
                    y="15"
                    text-anchor="middle"
                    class="center-text-value"
                  >
                    {{ totalCases }}
                  </text>
                </g>
              </svg>
            </div>
            
            <!-- 图例 -->
            <div class="chart-legend">
              <div
                v-for="(item, index) in chartData"
                :key="index"
                class="legend-item"
                @click="handleCategoryClick(item)"
              >
                <span 
                  class="legend-color" 
                  :style="{ backgroundColor: item.color }"
                ></span>
                <span class="legend-label">{{ item.name }}</span>
                <span class="legend-value">{{ item.count }}</span>
                <span class="legend-percentage">({{ getPercentage(item.count) }}%)</span>
              </div>
            </div>
          </div>
          
          <!-- 详细列表 -->
          <div class="category-list">
            <div class="list-header">
              <h4>分类详情</h4>
              <a-button type="link" size="small" @click="toggleSortOrder">
                <template #icon>
                  <sort-ascending-outlined v-if="sortOrder === 'asc'" />
                  <sort-descending-outlined v-else />
                </template>
                {{ sortOrder === 'asc' ? '升序' : '降序' }}
              </a-button>
            </div>
            
            <div class="category-items">
              <div
                v-for="(item, index) in sortedData"
                :key="index"
                class="category-item"
                @click="handleCategoryClick(item)"
              >
                <div class="item-rank">{{ index + 1 }}</div>
                <div class="item-color" :style="{ backgroundColor: item.color }"></div>
                <div class="item-info">
                  <div class="item-name">{{ item.name }}</div>
                  <div class="item-description">{{ item.description || '暂无描述' }}</div>
                </div>
                <div class="item-stats">
                  <div class="stat-count">{{ item.count }}</div>
                  <div class="stat-percentage">{{ getPercentage(item.count) }}%</div>
                </div>
                <div class="item-trend">
                  <span class="trend-indicator" :class="getTrendClass(item.trend)">
                    <arrow-up-outlined v-if="item.trend > 0" />
                    <arrow-down-outlined v-else-if="item.trend < 0" />
                    <minus-outlined v-else />
                    {{ Math.abs(item.trend || 0) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
    
    <!-- 悬浮提示 -->
    <div
      v-if="tooltipVisible"
      class="chart-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">{{ tooltipData?.name }}</div>
        <div class="tooltip-item">
          案例数量: {{ tooltipData?.count }}
        </div>
        <div class="tooltip-item">
          占比: {{ getPercentage(tooltipData?.count || 0) }}%
        </div>
        <div v-if="tooltipData?.description" class="tooltip-item">
          {{ tooltipData.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  PieChartOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue';

interface CategoryData {
  id: number;
  name: string;
  description?: string;
  count: number;
  color: string;
  trend?: number;
}

interface Props {
  data: CategoryData[];
  loading?: boolean;
}

interface Emits {
  (e: 'category-click', category: CategoryData): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const sortOrder = ref<'asc' | 'desc'>('desc');
const tooltipVisible = ref(false);
const tooltipX = ref(0);
const tooltipY = ref(0);
const tooltipData = ref<CategoryData | null>(null);

const chartSize = 200;
const radius = 80;

// 计算属性
const chartData = computed(() => {
  return props.data.slice(0, 8); // 最多显示8个分类
});

const totalCases = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.count, 0);
});

const totalCategories = computed(() => {
  return props.data.length;
});

const activeCategories = computed(() => {
  return props.data.filter(item => item.count > 0).length;
});

const topCategory = computed(() => {
  return props.data.reduce((max, item) => 
    item.count > (max?.count || 0) ? item : max, null as CategoryData | null
  );
});

const sortedData = computed(() => {
  const sorted = [...chartData.value];
  return sorted.sort((a, b) => {
    return sortOrder.value === 'desc' ? b.count - a.count : a.count - b.count;
  });
});

const pieSegments = computed(() => {
  let currentAngle = 0;
  const segments = [];
  
  for (const item of chartData.value) {
    const percentage = totalCases.value > 0 ? item.count / totalCases.value : 0;
    const angle = percentage * 2 * Math.PI;
    
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    const x1 = Math.cos(startAngle) * radius;
    const y1 = Math.sin(startAngle) * radius;
    const x2 = Math.cos(endAngle) * radius;
    const y2 = Math.sin(endAngle) * radius;
    
    const largeArcFlag = angle > Math.PI ? 1 : 0;
    
    const path = [
      `M 0 0`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      `Z`
    ].join(' ');
    
    segments.push({
      path,
      color: item.color,
      data: item
    });
    
    currentAngle += angle;
  }
  
  return segments;
});

// 方法
const getPercentage = (count: number) => {
  if (totalCases.value === 0) return 0;
  return Math.round((count / totalCases.value) * 100);
};

const getTrendClass = (trend: number = 0) => {
  if (trend > 0) return 'trend-up';
  if (trend < 0) return 'trend-down';
  return 'trend-flat';
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
};

const handleCategoryClick = (category: CategoryData) => {
  emit('category-click', category);
};

const showTooltip = (segment: any, event: MouseEvent) => {
  tooltipData.value = segment.data;
  tooltipX.value = event.clientX + 10;
  tooltipY.value = event.clientY - 10;
  tooltipVisible.value = true;
};

const hideTooltip = () => {
  tooltipVisible.value = false;
};
</script>

<style scoped lang="scss">
.category-distribution-chart {
  position: relative;

  .chart-wrapper {
    min-height: 400px;

    .empty-chart {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }

    .chart-content {
      .chart-header {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .chart-summary {
          display: flex;
          gap: 24px;

          .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .summary-label {
              font-size: 12px;
              color: #8c8c8c;
              margin-bottom: 4px;
            }

            .summary-value {
              font-size: 18px;
              font-weight: 600;
              color: #262626;
            }
          }
        }
      }

      .pie-chart-section {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 24px;

        .pie-chart {
          flex-shrink: 0;

          .pie-svg {
            .pie-segment {
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                opacity: 0.8;
                transform: scale(1.05);
              }
            }

            .center-text-title {
              font-size: 12px;
              fill: #8c8c8c;
            }

            .center-text-value {
              font-size: 20px;
              font-weight: 600;
              fill: #262626;
            }
          }
        }

        .chart-legend {
          flex: 1;
          max-height: 200px;
          overflow-y: auto;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: #f5f5f5;
            }

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
              flex-shrink: 0;
            }

            .legend-label {
              flex: 1;
              font-size: 14px;
              color: #262626;
            }

            .legend-value {
              font-size: 14px;
              font-weight: 500;
              color: #262626;
            }

            .legend-percentage {
              font-size: 12px;
              color: #8c8c8c;
              margin-left: 4px;
            }
          }
        }
      }

      .category-list {
        .list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }
        }

        .category-items {
          .category-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              background: #f6ffed;
            }

            &:last-child {
              margin-bottom: 0;
            }

            .item-rank {
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #f5f5f5;
              border-radius: 50%;
              font-size: 12px;
              font-weight: 500;
              color: #8c8c8c;
            }

            .item-color {
              width: 16px;
              height: 16px;
              border-radius: 4px;
              flex-shrink: 0;
            }

            .item-info {
              flex: 1;

              .item-name {
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 2px;
              }

              .item-description {
                font-size: 12px;
                color: #8c8c8c;
              }
            }

            .item-stats {
              text-align: right;

              .stat-count {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }

              .stat-percentage {
                font-size: 12px;
                color: #8c8c8c;
              }
            }

            .item-trend {
              .trend-indicator {
                display: flex;
                align-items: center;
                gap: 2px;
                font-size: 12px;

                &.trend-up {
                  color: #52c41a;
                }

                &.trend-down {
                  color: #ff4d4f;
                }

                &.trend-flat {
                  color: #8c8c8c;
                }
              }
            }
          }
        }
      }
    }
  }

  .chart-tooltip {
    position: fixed;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;

    .tooltip-content {
      .tooltip-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .tooltip-item {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .category-distribution-chart {
    .chart-content {
      .chart-header {
        .chart-summary {
          gap: 16px;
        }
      }

      .pie-chart-section {
        flex-direction: column;
        gap: 16px;

        .chart-legend {
          max-height: none;
        }
      }

      .category-list {
        .category-items {
          .category-item {
            gap: 8px;
            padding: 8px;

            .item-info {
              .item-name {
                font-size: 13px;
              }

              .item-description {
                font-size: 11px;
              }
            }

            .item-stats {
              .stat-count {
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
