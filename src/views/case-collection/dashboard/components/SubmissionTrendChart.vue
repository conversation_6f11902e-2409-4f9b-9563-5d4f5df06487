<template>
  <div class="submission-trend-chart">
    <a-spin :spinning="loading">
      <div class="chart-wrapper">
        <div v-if="!data.length" class="empty-chart">
          <a-empty description="暂无数据">
            <template #image>
              <line-chart-outlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
          </a-empty>
        </div>
        
        <div v-else class="chart-content">
          <!-- 图表头部信息 -->
          <div class="chart-header">
            <div class="chart-summary">
              <div class="summary-item">
                <span class="summary-label">总提交量</span>
                <span class="summary-value">{{ totalSubmissions }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">平均日提交</span>
                <span class="summary-value">{{ averageDaily }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">增长率</span>
                <span class="summary-value" :class="growthRateClass">{{ growthRate }}%</span>
              </div>
            </div>
            <div class="chart-legend">
              <span class="legend-item">
                <span class="legend-color" style="background: #1890ff;"></span>
                提交量
              </span>
              <span class="legend-item">
                <span class="legend-color" style="background: #52c41a;"></span>
                通过量
              </span>
            </div>
          </div>
          
          <!-- 趋势图表 -->
          <div class="trend-chart">
            <div class="chart-container">
              <div class="y-axis">
                <div v-for="tick in yAxisTicks" :key="tick" class="y-tick">
                  {{ tick }}
                </div>
              </div>
              
              <div class="chart-area">
                <div class="chart-grid">
                  <div v-for="i in 5" :key="i" class="grid-line"></div>
                </div>
                
                <div class="chart-bars">
                  <div
                    v-for="(item, index) in chartData"
                    :key="index"
                    class="bar-group"
                    @mouseenter="showTooltip(item, $event)"
                    @mouseleave="hideTooltip"
                  >
                    <div class="bar-container">
                      <div 
                        class="bar submission-bar"
                        :style="{ height: `${getBarHeight(item.submissions)}px` }"
                        :title="`提交量: ${item.submissions}`"
                      ></div>
                      <div 
                        class="bar approved-bar"
                        :style="{ height: `${getBarHeight(item.approved)}px` }"
                        :title="`通过量: ${item.approved}`"
                      ></div>
                    </div>
                    <div class="bar-label">{{ formatLabel(item.date) }}</div>
                  </div>
                </div>
                
                <!-- 趋势线 -->
                <div class="trend-line">
                  <svg class="trend-svg" :viewBox="`0 0 ${chartWidth} ${chartHeight}`">
                    <polyline
                      :points="submissionTrendPoints"
                      fill="none"
                      stroke="#1890ff"
                      stroke-width="2"
                      stroke-dasharray="5,5"
                    />
                    <polyline
                      :points="approvedTrendPoints"
                      fill="none"
                      stroke="#52c41a"
                      stroke-width="2"
                      stroke-dasharray="5,5"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数据表格 -->
          <div class="data-table">
            <a-table
              :columns="tableColumns"
              :data-source="chartData"
              :pagination="false"
              size="small"
              :scroll="{ y: 200 }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'date'">
                  {{ formatTableDate(record.date) }}
                </template>
                <template v-else-if="column.key === 'rate'">
                  <span :class="getRateClass(record.rate)">
                    {{ record.rate }}%
                  </span>
                </template>
                <template v-else-if="column.key === 'trend'">
                  <span class="trend-indicator" :class="getTrendClass(record.trend)">
                    <arrow-up-outlined v-if="record.trend > 0" />
                    <arrow-down-outlined v-else-if="record.trend < 0" />
                    <minus-outlined v-else />
                    {{ Math.abs(record.trend) }}%
                  </span>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </a-spin>
    
    <!-- 悬浮提示 -->
    <div
      v-if="tooltipVisible"
      class="chart-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">{{ tooltipData?.date }}</div>
        <div class="tooltip-item">
          <span class="tooltip-color" style="background: #1890ff;"></span>
          提交量: {{ tooltipData?.submissions }}
        </div>
        <div class="tooltip-item">
          <span class="tooltip-color" style="background: #52c41a;"></span>
          通过量: {{ tooltipData?.approved }}
        </div>
        <div class="tooltip-item">
          通过率: {{ tooltipData?.rate }}%
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  LineChartOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue';

interface TrendData {
  date: string;
  submissions: number;
  approved: number;
  rate: number;
  trend: number;
}

interface Props {
  data: TrendData[];
  type: 'daily' | 'weekly' | 'monthly';
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// 响应式数据
const tooltipVisible = ref(false);
const tooltipX = ref(0);
const tooltipY = ref(0);
const tooltipData = ref<TrendData | null>(null);

const chartWidth = 400;
const chartHeight = 200;
const maxBarHeight = 150;

// 计算属性
const chartData = computed(() => {
  return props.data.slice(-20); // 显示最近20个数据点
});

const totalSubmissions = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.submissions, 0);
});

const averageDaily = computed(() => {
  if (chartData.value.length === 0) return 0;
  return Math.round(totalSubmissions.value / chartData.value.length);
});

const growthRate = computed(() => {
  if (chartData.value.length < 2) return 0;
  const latest = chartData.value[chartData.value.length - 1];
  const previous = chartData.value[chartData.value.length - 2];
  if (previous.submissions === 0) return 0;
  return Math.round(((latest.submissions - previous.submissions) / previous.submissions) * 100);
});

const growthRateClass = computed(() => {
  if (growthRate.value > 0) return 'positive';
  if (growthRate.value < 0) return 'negative';
  return 'neutral';
});

const maxValue = computed(() => {
  const maxSubmissions = Math.max(...chartData.value.map(item => item.submissions));
  const maxApproved = Math.max(...chartData.value.map(item => item.approved));
  return Math.max(maxSubmissions, maxApproved);
});

const yAxisTicks = computed(() => {
  const max = maxValue.value;
  const step = Math.ceil(max / 5);
  return Array.from({ length: 6 }, (_, i) => max - i * step);
});

const submissionTrendPoints = computed(() => {
  return chartData.value.map((item, index) => {
    const x = (index / (chartData.value.length - 1)) * chartWidth;
    const y = chartHeight - (item.submissions / maxValue.value) * chartHeight;
    return `${x},${y}`;
  }).join(' ');
});

const approvedTrendPoints = computed(() => {
  return chartData.value.map((item, index) => {
    const x = (index / (chartData.value.length - 1)) * chartWidth;
    const y = chartHeight - (item.approved / maxValue.value) * chartHeight;
    return `${x},${y}`;
  }).join(' ');
});

const tableColumns = [
  { title: '日期', dataIndex: 'date', key: 'date', width: 100 },
  { title: '提交量', dataIndex: 'submissions', key: 'submissions', width: 80 },
  { title: '通过量', dataIndex: 'approved', key: 'approved', width: 80 },
  { title: '通过率', dataIndex: 'rate', key: 'rate', width: 80 },
  { title: '趋势', dataIndex: 'trend', key: 'trend', width: 80 }
];

// 方法
const getBarHeight = (value: number) => {
  if (maxValue.value === 0) return 0;
  return (value / maxValue.value) * maxBarHeight;
};

const formatLabel = (date: string) => {
  const d = new Date(date);
  if (props.type === 'daily') {
    return `${d.getMonth() + 1}/${d.getDate()}`;
  } else if (props.type === 'weekly') {
    return `第${Math.ceil(d.getDate() / 7)}周`;
  } else {
    return `${d.getMonth() + 1}月`;
  }
};

const formatTableDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const getRateClass = (rate: number) => {
  if (rate >= 80) return 'rate-high';
  if (rate >= 60) return 'rate-medium';
  return 'rate-low';
};

const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up';
  if (trend < 0) return 'trend-down';
  return 'trend-flat';
};

const showTooltip = (item: TrendData, event: MouseEvent) => {
  tooltipData.value = item;
  tooltipX.value = event.clientX + 10;
  tooltipY.value = event.clientY - 10;
  tooltipVisible.value = true;
};

const hideTooltip = () => {
  tooltipVisible.value = false;
};
</script>

<style scoped lang="scss">
.submission-trend-chart {
  position: relative;

  .chart-wrapper {
    min-height: 400px;

    .empty-chart {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
    }

    .chart-content {
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .chart-summary {
          display: flex;
          gap: 24px;

          .summary-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .summary-label {
              font-size: 12px;
              color: #8c8c8c;
              margin-bottom: 4px;
            }

            .summary-value {
              font-size: 18px;
              font-weight: 600;
              color: #262626;

              &.positive {
                color: #52c41a;
              }

              &.negative {
                color: #ff4d4f;
              }

              &.neutral {
                color: #8c8c8c;
              }
            }
          }
        }

        .chart-legend {
          display: flex;
          gap: 16px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #8c8c8c;

            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
            }
          }
        }
      }

      .trend-chart {
        margin-bottom: 20px;

        .chart-container {
          display: flex;
          height: 200px;

          .y-axis {
            width: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding-right: 8px;

            .y-tick {
              font-size: 10px;
              color: #8c8c8c;
              text-align: right;
            }
          }

          .chart-area {
            flex: 1;
            position: relative;

            .chart-grid {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .grid-line {
                height: 1px;
                background: #f0f0f0;
              }
            }

            .chart-bars {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              height: 150px;
              display: flex;
              align-items: flex-end;
              gap: 2px;

              .bar-group {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;

                .bar-container {
                  display: flex;
                  align-items: flex-end;
                  gap: 1px;
                  width: 100%;
                  height: 150px;

                  .bar {
                    flex: 1;
                    min-height: 2px;
                    border-radius: 2px 2px 0 0;
                    transition: all 0.3s ease;

                    &.submission-bar {
                      background: #1890ff;
                    }

                    &.approved-bar {
                      background: #52c41a;
                    }
                  }
                }

                .bar-label {
                  font-size: 10px;
                  color: #8c8c8c;
                  margin-top: 4px;
                  text-align: center;
                }

                &:hover {
                  .bar {
                    opacity: 0.8;
                  }
                }
              }
            }

            .trend-line {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 50px;
              pointer-events: none;

              .trend-svg {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }

      .data-table {
        .rate-high {
          color: #52c41a;
          font-weight: 500;
        }

        .rate-medium {
          color: #faad14;
          font-weight: 500;
        }

        .rate-low {
          color: #ff4d4f;
          font-weight: 500;
        }

        .trend-indicator {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;

          &.trend-up {
            color: #52c41a;
          }

          &.trend-down {
            color: #ff4d4f;
          }

          &.trend-flat {
            color: #8c8c8c;
          }
        }
      }
    }
  }

  .chart-tooltip {
    position: fixed;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;

    .tooltip-content {
      .tooltip-title {
        font-weight: 500;
        margin-bottom: 4px;
      }

      .tooltip-item {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }

        .tooltip-color {
          width: 8px;
          height: 8px;
          border-radius: 2px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .submission-trend-chart {
    .chart-content {
      .chart-header {
        flex-direction: column;
        gap: 16px;

        .chart-summary {
          gap: 16px;
        }
      }

      .trend-chart {
        .chart-container {
          .y-axis {
            width: 30px;
          }
        }
      }
    }
  }
}
</style>
