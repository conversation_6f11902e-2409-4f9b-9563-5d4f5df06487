<template>
  <div class="case-category-manage">
    <!-- 页面头部 -->
    <div class="page-header">
      <case-breadcrumb />
      <div class="header-content">
        <h1>案例分类管理</h1>
        <p>管理案例分类体系，支持多级分类和标签关联，为案例征集提供完整的分类架构</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="expandAll">
            <template #icon><expand-outlined /></template>
            展开全部
          </a-button>
          <a-button @click="collapseAll">
            <template #icon><compress-outlined /></template>
            收起全部
          </a-button>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建分类
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 分类统计 -->
    <div class="category-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="总分类数"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <folder-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="顶级分类"
              :value="stats.topLevel"
              :value-style="{ color: '#52c41a' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <apartment-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="关联案例"
              :value="stats.linkedCases"
              :value-style="{ color: '#faad14' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="活跃标签"
              :value="stats.activeTags"
              :value-style="{ color: '#722ed1' }"
              :loading="statsLoading"
            >
              <template #prefix>
                <tags-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="分类名称" name="keyword">
            <a-input 
              v-model:value="searchForm.keyword" 
              placeholder="请输入分类名称"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="分类状态" name="status">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="分类层级" name="level">
            <a-select 
              v-model:value="searchForm.level" 
              placeholder="请选择层级"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="1">一级分类</a-select-option>
              <a-select-option :value="2">二级分类</a-select-option>
              <a-select-option :value="3">三级分类</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 分类树形表格 -->
    <div class="category-table">
      <a-card :bordered="false">
        <template #title>
          <div class="table-header">
            <span>分类列表</span>
            <span class="record-count">共 {{ filteredCategories.length }} 个分类</span>
          </div>
        </template>
        
        <category-tree
          ref="categoryTreeRef"
          :categories="filteredCategories"
          :loading="loading"
          @create="showCreateModal"
          @edit="handleEdit"
          @delete="handleDelete"
          @status-change="handleStatusChange"
          @move="handleMove"
        />
      </a-card>
    </div>

    <!-- 分类表单弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitting"
    >
      <category-form
        ref="categoryFormRef"
        :form-data="formData"
        :parent-options="parentOptions"
        @submit="handleFormSubmit"
      />
    </a-modal>

    <!-- 标签管理弹窗 -->
    <a-modal
      v-model:visible="tagModalVisible"
      title="标签管理"
      width="800px"
      :footer="null"
    >
      <tag-management
        :category-id="selectedCategoryId"
        @close="tagModalVisible = false"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  ReloadOutlined,
  ExpandOutlined,
  CompressOutlined,
  PlusOutlined,
  FolderOutlined,
  ApartmentOutlined,
  FileTextOutlined,
  TagsOutlined,
  SearchOutlined,
  ClearOutlined
} from '@ant-design/icons-vue';
import type { CaseCategory } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import CaseBreadcrumb from '@/components/common/CaseBreadcrumb.vue';
import CategoryTree from './components/CategoryTree.vue';
import CategoryForm from './components/CategoryForm.vue';
import TagManagement from './components/TagManagement.vue';

// 响应式数据
const categories = ref<CaseCategory[]>([]);
const loading = ref(false);
const statsLoading = ref(false);
const modalVisible = ref(false);
const tagModalVisible = ref(false);
const submitting = ref(false);
const selectedCategoryId = ref<number>();

const categoryTreeRef = ref();
const categoryFormRef = ref();

const stats = reactive({
  total: 0,
  topLevel: 0,
  linkedCases: 0,
  activeTags: 0
});

const searchForm = reactive({
  keyword: '',
  status: undefined,
  level: undefined
});

const formData = reactive<Partial<CaseCategory>>({
  name: '',
  code: '',
  description: '',
  parentId: undefined,
  sort: 0,
  isEnabled: true
});

// 计算属性
const modalTitle = computed(() => {
  return formData.id ? '编辑分类' : '新建分类';
});

const filteredCategories = computed(() => {
  let result = categories.value;
  
  if (searchForm.keyword) {
    result = result.filter(cat => 
      cat.name.includes(searchForm.keyword) || 
      cat.code?.includes(searchForm.keyword)
    );
  }
  
  if (searchForm.status !== undefined) {
    result = result.filter(cat => cat.isEnabled === searchForm.status);
  }
  
  if (searchForm.level) {
    result = result.filter(cat => cat.level === searchForm.level);
  }
  
  return result;
});

const parentOptions = computed(() => {
  const buildTree = (items: CaseCategory[], parentId?: number): any[] => {
    return items
      .filter(item => item.parentId === parentId && item.id !== formData.id)
      .map(item => ({
        title: item.name,
        value: item.id,
        key: item.id,
        children: buildTree(items, item.id)
      }));
  };
  
  return buildTree(categories.value);
});

// 方法
const loadCategories = async () => {
  try {
    loading.value = true;
    const response = await caseCollectionService.getAllCategories();
    
    if (response.success) {
      categories.value = response.data;
    } else {
      message.error(response.message || '加载分类列表失败');
    }
  } catch (error) {
    console.error('加载分类列表失败:', error);
    message.error('加载分类列表失败');
  } finally {
    loading.value = false;
  }
};

const loadStats = async () => {
  try {
    statsLoading.value = true;
    // 计算统计数据
    stats.total = categories.value.length;
    stats.topLevel = categories.value.filter(cat => !cat.parentId).length;
    stats.linkedCases = categories.value.reduce((sum, cat) => sum + (cat.caseCount || 0), 0);
    stats.activeTags = 50; // 模拟数据
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statsLoading.value = false;
  }
};

const refreshData = () => {
  loadCategories();
  loadStats();
};

const expandAll = () => {
  categoryTreeRef.value?.expandAll();
};

const collapseAll = () => {
  categoryTreeRef.value?.collapseAll();
};

// 事件处理
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
};

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    level: undefined
  });
};

const showCreateModal = (parentId?: number) => {
  resetForm();
  if (parentId) {
    formData.parentId = parentId;
  }
  modalVisible.value = true;
};

const handleEdit = (category: CaseCategory) => {
  Object.assign(formData, {
    id: category.id,
    name: category.name,
    code: category.code,
    description: category.description,
    parentId: category.parentId,
    sort: category.sort,
    isEnabled: category.isEnabled
  });
  modalVisible.value = true;
};

const handleDelete = async (category: CaseCategory) => {
  try {
    const response = await caseCollectionService.deleteCategory(category.id);
    if (response.success) {
      message.success('删除分类成功');
      refreshData();
    } else {
      message.error(response.message || '删除分类失败');
    }
  } catch (error) {
    console.error('删除分类失败:', error);
    message.error('删除分类失败');
  }
};

const handleStatusChange = async (category: CaseCategory, enabled: boolean) => {
  try {
    const response = await caseCollectionService.updateCategory(category.id, {
      ...category,
      isEnabled: enabled
    });
    
    if (response.success) {
      message.success(`${enabled ? '启用' : '禁用'}分类成功`);
      refreshData();
    } else {
      message.error(response.message || '更新分类状态失败');
    }
  } catch (error) {
    console.error('更新分类状态失败:', error);
    message.error('更新分类状态失败');
  }
};

const handleMove = async (dragCategory: CaseCategory, targetCategory: CaseCategory, position: string) => {
  try {
    const response = await caseCollectionService.moveCategory({
      dragId: dragCategory.id,
      targetId: targetCategory.id,
      position
    });
    
    if (response.success) {
      message.success('移动分类成功');
      refreshData();
    } else {
      message.error(response.message || '移动分类失败');
    }
  } catch (error) {
    console.error('移动分类失败:', error);
    message.error('移动分类失败');
  }
};

const handleSubmit = () => {
  categoryFormRef.value?.submit();
};

const handleFormSubmit = async (data: any) => {
  try {
    submitting.value = true;
    
    if (data.id) {
      const response = await caseCollectionService.updateCategory(data.id, data);
      if (response.success) {
        message.success('更新分类成功');
        modalVisible.value = false;
        refreshData();
      } else {
        message.error(response.message || '更新分类失败');
      }
    } else {
      const response = await caseCollectionService.createCategory(data);
      if (response.success) {
        message.success('创建分类成功');
        modalVisible.value = false;
        refreshData();
      } else {
        message.error(response.message || '创建分类失败');
      }
    }
  } catch (error) {
    console.error('提交分类失败:', error);
    message.error('提交分类失败');
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    name: '',
    code: '',
    description: '',
    parentId: undefined,
    sort: 0,
    isEnabled: true
  });
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<style scoped lang="scss">
.case-category-manage {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 16px;
      }
    }
  }

  .category-stats {
    margin-bottom: 24px;

    .ant-statistic {
      text-align: center;
    }
  }

  .search-filters {
    margin-bottom: 24px;

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .category-table {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 768px) {
  .case-category-manage {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content h1 {
        font-size: 24px;
      }

      .header-actions {
        width: 100%;
      }
    }

    .search-filters {
      .ant-form {
        .ant-form-item {
          width: 100%;
          margin-bottom: 12px;

          .ant-form-item-control-input {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
