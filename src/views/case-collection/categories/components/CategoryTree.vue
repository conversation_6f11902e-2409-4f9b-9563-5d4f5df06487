<template>
  <div class="category-tree">
    <a-table
      :columns="columns"
      :data-source="treeData"
      :loading="loading"
      :pagination="false"
      :scroll="{ x: 1000 }"
      row-key="id"
      :default-expand-all-rows="expandAll"
      :expanded-row-keys="expandedKeys"
      @expand="handleExpand"
    >
      <!-- 分类名称 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div class="category-name-cell">
            <span class="category-icon">
              <folder-outlined v-if="record.children && record.children.length > 0" />
              <file-outlined v-else />
            </span>
            <span class="category-text">{{ record.name }}</span>
            <a-tag v-if="record.level" size="small" color="blue">
              L{{ record.level }}
            </a-tag>
          </div>
        </template>

        <!-- 分类编码 -->
        <template v-if="column.key === 'code'">
          <a-tag color="geekblue">{{ record.code }}</a-tag>
        </template>

        <!-- 案例数量 -->
        <template v-if="column.key === 'caseCount'">
          <a-badge 
            :count="record.caseCount || 0" 
            :number-style="{ backgroundColor: '#52c41a' }"
          />
        </template>

        <!-- 状态 -->
        <template v-if="column.key === 'status'">
          <a-switch 
            v-model:checked="record.isEnabled" 
            @change="(checked) => handleStatusChange(record, checked)"
            :loading="record.updating"
          />
        </template>

        <!-- 排序 -->
        <template v-if="column.key === 'sort'">
          <a-input-number
            v-model:value="record.sort"
            :min="0"
            :max="999"
            size="small"
            @change="(value) => handleSortChange(record, value)"
          />
        </template>

        <!-- 操作 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-tooltip title="添加子分类">
              <a-button type="link" size="small" @click="handleCreate(record.id)">
                <template #icon><plus-outlined /></template>
              </a-button>
            </a-tooltip>
            
            <a-tooltip title="编辑分类">
              <a-button type="link" size="small" @click="handleEdit(record)">
                <template #icon><edit-outlined /></template>
              </a-button>
            </a-tooltip>
            
            <a-tooltip title="管理标签">
              <a-button type="link" size="small" @click="handleManageTags(record)">
                <template #icon><tags-outlined /></template>
              </a-button>
            </a-tooltip>
            
            <a-dropdown>
              <a-button type="link" size="small">
                <template #icon><more-outlined /></template>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handleMove(record, 'up')" :disabled="!canMoveUp(record)">
                    <arrow-up-outlined />
                    上移
                  </a-menu-item>
                  <a-menu-item @click="handleMove(record, 'down')" :disabled="!canMoveDown(record)">
                    <arrow-down-outlined />
                    下移
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="handleCopy(record)">
                    <copy-outlined />
                    复制分类
                  </a-menu-item>
                  <a-menu-item @click="handleExport(record)">
                    <export-outlined />
                    导出分类
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item @click="handleDelete(record)" class="danger-item">
                    <delete-outlined />
                    删除分类
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Modal, message } from 'ant-design-vue';
import {
  FolderOutlined,
  FileOutlined,
  PlusOutlined,
  EditOutlined,
  TagsOutlined,
  MoreOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CopyOutlined,
  ExportOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import type { CaseCategory } from '@/types/case-collection';

interface Props {
  categories: CaseCategory[];
  loading?: boolean;
}

interface Emits {
  (e: 'create', parentId?: number): void;
  (e: 'edit', category: CaseCategory): void;
  (e: 'delete', category: CaseCategory): void;
  (e: 'status-change', category: CaseCategory, enabled: boolean): void;
  (e: 'move', category: CaseCategory, direction: string): void;
  (e: 'sort-change', category: CaseCategory, sort: number): void;
  (e: 'manage-tags', category: CaseCategory): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const expandedKeys = ref<number[]>([]);
const expandAll = ref(false);

// 表格列定义
const columns = [
  {
    title: '分类名称',
    dataIndex: 'name',
    key: 'name',
    width: 250,
    fixed: 'left'
  },
  {
    title: '分类编码',
    dataIndex: 'code',
    key: 'code',
    width: 120
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 200,
    ellipsis: true
  },
  {
    title: '案例数量',
    dataIndex: 'caseCount',
    key: 'caseCount',
    width: 100,
    align: 'center'
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    dataIndex: 'isEnabled',
    key: 'status',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ text }: any) => {
      return text ? new Date(text).toLocaleDateString() : '-';
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
];

// 计算属性
const treeData = computed(() => {
  const buildTree = (items: CaseCategory[], parentId?: number): CaseCategory[] => {
    return items
      .filter(item => item.parentId === parentId)
      .sort((a, b) => (a.sort || 0) - (b.sort || 0))
      .map(item => ({
        ...item,
        children: buildTree(items, item.id)
      }));
  };
  
  return buildTree(props.categories);
});

// 方法
const handleExpand = (expanded: boolean, record: CaseCategory) => {
  if (expanded) {
    expandedKeys.value.push(record.id);
  } else {
    const index = expandedKeys.value.indexOf(record.id);
    if (index > -1) {
      expandedKeys.value.splice(index, 1);
    }
  }
};

const expandAllNodes = () => {
  expandAll.value = true;
  expandedKeys.value = props.categories.map(cat => cat.id);
};

const collapseAllNodes = () => {
  expandAll.value = false;
  expandedKeys.value = [];
};

const canMoveUp = (record: CaseCategory) => {
  const siblings = props.categories.filter(cat => cat.parentId === record.parentId);
  const sortedSiblings = siblings.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  return sortedSiblings.indexOf(record) > 0;
};

const canMoveDown = (record: CaseCategory) => {
  const siblings = props.categories.filter(cat => cat.parentId === record.parentId);
  const sortedSiblings = siblings.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  return sortedSiblings.indexOf(record) < sortedSiblings.length - 1;
};

// 事件处理
const handleCreate = (parentId?: number) => {
  emit('create', parentId);
};

const handleEdit = (category: CaseCategory) => {
  emit('edit', category);
};

const handleDelete = (category: CaseCategory) => {
  if (category.children && category.children.length > 0) {
    message.warning('该分类下还有子分类，请先删除子分类');
    return;
  }
  
  if (category.caseCount && category.caseCount > 0) {
    message.warning('该分类下还有案例，请先移除案例');
    return;
  }

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分类"${category.name}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      emit('delete', category);
    }
  });
};

const handleStatusChange = (category: CaseCategory, enabled: boolean) => {
  emit('status-change', category, enabled);
};

const handleMove = (category: CaseCategory, direction: string) => {
  emit('move', category, direction);
};

const handleSortChange = (category: CaseCategory, sort: number) => {
  emit('sort-change', category, sort);
};

const handleManageTags = (category: CaseCategory) => {
  emit('manage-tags', category);
};

const handleCopy = (category: CaseCategory) => {
  message.info('复制功能开发中...');
};

const handleExport = (category: CaseCategory) => {
  message.info('导出功能开发中...');
};

// 监听分类数据变化，自动展开有子分类的节点
watch(() => props.categories, () => {
  if (expandAll.value) {
    expandedKeys.value = props.categories.map(cat => cat.id);
  }
}, { immediate: true });

// 暴露方法给父组件
defineExpose({
  expandAll: expandAllNodes,
  collapseAll: collapseAllNodes
});
</script>

<style scoped lang="scss">
.category-tree {
  .category-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .category-icon {
      color: #1890ff;
      font-size: 16px;
    }

    .category-text {
      font-weight: 500;
      color: #262626;
    }
  }

  .danger-item {
    color: #ff4d4f;

    &:hover {
      background-color: #fff2f0;
    }
  }

  :deep(.ant-table-tbody) {
    .ant-table-row {
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  :deep(.ant-table-thead) {
    .ant-table-cell {
      background-color: #fafafa;
      font-weight: 600;
    }
  }
}

@media (max-width: 768px) {
  .category-tree {
    :deep(.ant-table) {
      .ant-table-content {
        overflow-x: auto;
      }
    }
  }
}
</style>
