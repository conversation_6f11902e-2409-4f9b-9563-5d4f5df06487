<template>
  <div class="tag-management">
    <!-- 标签统计 -->
    <div class="tag-stats">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-statistic title="总标签数" :value="stats.total" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="已使用" :value="stats.used" />
        </a-col>
        <a-col :span="8">
          <a-statistic title="热门标签" :value="stats.popular" />
        </a-col>
      </a-row>
    </div>

    <!-- 标签操作 -->
    <div class="tag-actions">
      <a-space>
        <a-input
          v-model:value="newTagName"
          placeholder="输入新标签名称"
          @press-enter="addTag"
          style="width: 200px"
        />
        <a-button type="primary" @click="addTag" :disabled="!newTagName.trim()">
          <template #icon><plus-outlined /></template>
          添加标签
        </a-button>
        <a-button @click="refreshTags">
          <template #icon><reload-outlined /></template>
          刷新
        </a-button>
        <a-button @click="batchDelete" :disabled="selectedTags.length === 0">
          <template #icon><delete-outlined /></template>
          批量删除
        </a-button>
      </a-space>
    </div>

    <!-- 标签搜索 -->
    <div class="tag-search">
      <a-input-search
        v-model:value="searchKeyword"
        placeholder="搜索标签"
        @search="handleSearch"
        style="width: 300px"
      />
    </div>

    <!-- 标签列表 -->
    <div class="tag-list">
      <a-spin :spinning="loading">
        <div class="tag-grid">
          <div
            v-for="tag in filteredTags"
            :key="tag.id"
            class="tag-item"
            :class="{ selected: selectedTags.includes(tag.id) }"
            @click="toggleTagSelection(tag.id)"
          >
            <div class="tag-content">
              <div class="tag-header">
                <a-tag 
                  :color="tag.color || 'blue'"
                  class="tag-name"
                >
                  {{ tag.name }}
                </a-tag>
                <a-dropdown>
                  <a-button type="text" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="editTag(tag)">
                        <edit-outlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item @click="viewTagCases(tag)">
                        <eye-outlined />
                        查看案例
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteTag(tag)" class="danger-item">
                        <delete-outlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
              
              <div class="tag-info">
                <div class="tag-description">{{ tag.description || '暂无描述' }}</div>
                <div class="tag-meta">
                  <span class="case-count">
                    <file-text-outlined />
                    {{ tag.caseCount || 0 }} 个案例
                  </span>
                  <span class="usage-rate">
                    <bar-chart-outlined />
                    使用率 {{ getUsageRate(tag) }}%
                  </span>
                </div>
              </div>
              
              <div class="tag-actions-inline">
                <a-button type="link" size="small" @click.stop="editTag(tag)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click.stop="viewTagCases(tag)">
                  案例
                </a-button>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="filteredTags.length === 0" class="empty-state">
          <a-empty description="暂无标签数据" />
        </div>
      </a-spin>
    </div>

    <!-- 标签编辑弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      :title="editingTag ? '编辑标签' : '新建标签'"
      @ok="saveTag"
      @cancel="cancelEdit"
      :confirm-loading="saving"
    >
      <a-form :model="tagForm" layout="vertical">
        <a-form-item label="标签名称" required>
          <a-input v-model:value="tagForm.name" placeholder="请输入标签名称" />
        </a-form-item>
        
        <a-form-item label="标签描述">
          <a-textarea 
            v-model:value="tagForm.description" 
            placeholder="请输入标签描述"
            :rows="3"
          />
        </a-form-item>
        
        <a-form-item label="标签颜色">
          <div class="color-selector">
            <a-input v-model:value="tagForm.color" placeholder="颜色值" style="width: 200px" />
            <div class="color-presets">
              <div
                v-for="color in colorOptions"
                :key="color"
                class="color-option"
                :style="{ backgroundColor: color }"
                @click="tagForm.color = color"
              ></div>
            </div>
          </div>
        </a-form-item>
        
        <a-form-item label="标签分组">
          <a-select v-model:value="tagForm.group" placeholder="选择标签分组" allow-clear>
            <a-select-option value="技术">技术</a-select-option>
            <a-select-option value="管理">管理</a-select-option>
            <a-select-option value="服务">服务</a-select-option>
            <a-select-option value="创新">创新</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="排序权重">
          <a-input-number 
            v-model:value="tagForm.sort" 
            :min="0" 
            :max="999" 
            placeholder="排序权重"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="启用状态">
          <a-switch 
            v-model:checked="tagForm.isEnabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 案例查看弹窗 -->
    <a-modal
      v-model:visible="casesModalVisible"
      :title="`标签「${selectedTag?.name}」关联的案例`"
      width="800px"
      :footer="null"
    >
      <div class="tag-cases">
        <a-list
          :data-source="tagCases"
          :loading="casesLoading"
        >
          <template #renderItem="{ item }">
            <a-list-item>
              <a-list-item-meta
                :title="item.title"
                :description="item.summary"
              >
                <template #avatar>
                  <a-avatar :src="item.coverImage" icon="file-text" />
                </template>
              </a-list-item-meta>
              <template #actions>
                <a @click="viewCase(item)">查看</a>
              </template>
            </a-list-item>
          </template>
        </a-list>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  MoreOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';

interface Props {
  categoryId?: number;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const tags = ref<any[]>([]);
const loading = ref(false);
const saving = ref(false);
const casesLoading = ref(false);
const newTagName = ref('');
const searchKeyword = ref('');
const selectedTags = ref<number[]>([]);
const editModalVisible = ref(false);
const casesModalVisible = ref(false);
const editingTag = ref<any>(null);
const selectedTag = ref<any>(null);
const tagCases = ref<any[]>([]);

const stats = reactive({
  total: 0,
  used: 0,
  popular: 0
});

const tagForm = reactive({
  name: '',
  description: '',
  color: '#1890ff',
  group: '',
  sort: 0,
  isEnabled: true
});

// 颜色选项
const colorOptions = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
];

// 计算属性
const filteredTags = computed(() => {
  if (!searchKeyword.value) return tags.value;
  return tags.value.filter(tag => 
    tag.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    tag.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 方法
const loadTags = async () => {
  try {
    loading.value = true;
    // 模拟数据
    tags.value = [
      { id: 1, name: '创新实践', description: '创新相关的实践案例', color: '#1890ff', caseCount: 15, group: '创新' },
      { id: 2, name: '管理经验', description: '管理方面的经验总结', color: '#52c41a', caseCount: 23, group: '管理' },
      { id: 3, name: '技术应用', description: '技术应用相关案例', color: '#faad14', caseCount: 18, group: '技术' },
      { id: 4, name: '服务优化', description: '服务优化改进案例', color: '#f5222d', caseCount: 12, group: '服务' },
      { id: 5, name: '流程改进', description: '流程改进相关案例', color: '#722ed1', caseCount: 8, group: '管理' }
    ];
    
    // 更新统计
    stats.total = tags.value.length;
    stats.used = tags.value.filter(tag => tag.caseCount > 0).length;
    stats.popular = tags.value.filter(tag => tag.caseCount >= 10).length;
  } catch (error) {
    console.error('加载标签失败:', error);
    message.error('加载标签失败');
  } finally {
    loading.value = false;
  }
};

const addTag = () => {
  if (!newTagName.value.trim()) return;
  
  const newTag = {
    id: Date.now(),
    name: newTagName.value.trim(),
    description: '',
    color: '#1890ff',
    caseCount: 0,
    group: ''
  };
  
  tags.value.push(newTag);
  newTagName.value = '';
  message.success('添加标签成功');
};

const editTag = (tag: any) => {
  editingTag.value = tag;
  Object.assign(tagForm, {
    name: tag.name,
    description: tag.description || '',
    color: tag.color || '#1890ff',
    group: tag.group || '',
    sort: tag.sort || 0,
    isEnabled: tag.isEnabled !== false
  });
  editModalVisible.value = true;
};

const saveTag = () => {
  if (!tagForm.name.trim()) {
    message.error('请输入标签名称');
    return;
  }
  
  saving.value = true;
  
  setTimeout(() => {
    if (editingTag.value) {
      Object.assign(editingTag.value, tagForm);
      message.success('更新标签成功');
    } else {
      const newTag = {
        id: Date.now(),
        ...tagForm,
        caseCount: 0
      };
      tags.value.push(newTag);
      message.success('创建标签成功');
    }
    
    editModalVisible.value = false;
    saving.value = false;
  }, 1000);
};

const cancelEdit = () => {
  editModalVisible.value = false;
  editingTag.value = null;
  Object.assign(tagForm, {
    name: '',
    description: '',
    color: '#1890ff',
    group: '',
    sort: 0,
    isEnabled: true
  });
};

const deleteTag = (tag: any) => {
  if (tag.caseCount > 0) {
    message.warning('该标签下还有案例，无法删除');
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除标签"${tag.name}"吗？`,
    onOk() {
      const index = tags.value.findIndex(t => t.id === tag.id);
      if (index > -1) {
        tags.value.splice(index, 1);
        message.success('删除标签成功');
      }
    }
  });
};

const toggleTagSelection = (tagId: number) => {
  const index = selectedTags.value.indexOf(tagId);
  if (index > -1) {
    selectedTags.value.splice(index, 1);
  } else {
    selectedTags.value.push(tagId);
  }
};

const batchDelete = () => {
  const selectedTagsData = tags.value.filter(tag => selectedTags.value.includes(tag.id));
  const hasUsedTags = selectedTagsData.some(tag => tag.caseCount > 0);
  
  if (hasUsedTags) {
    message.warning('选中的标签中有已使用的标签，无法删除');
    return;
  }
  
  Modal.confirm({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedTags.value.length} 个标签吗？`,
    onOk() {
      tags.value = tags.value.filter(tag => !selectedTags.value.includes(tag.id));
      selectedTags.value = [];
      message.success('批量删除成功');
    }
  });
};

const viewTagCases = async (tag: any) => {
  selectedTag.value = tag;
  casesModalVisible.value = true;
  casesLoading.value = true;
  
  // 模拟加载案例数据
  setTimeout(() => {
    tagCases.value = [
      { id: 1, title: '案例1', summary: '案例1的摘要', coverImage: '' },
      { id: 2, title: '案例2', summary: '案例2的摘要', coverImage: '' }
    ];
    casesLoading.value = false;
  }, 1000);
};

const viewCase = (caseItem: any) => {
  message.info(`查看案例: ${caseItem.title}`);
};

const getUsageRate = (tag: any) => {
  const totalCases = 100; // 模拟总案例数
  return Math.round((tag.caseCount / totalCases) * 100);
};

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
};

const refreshTags = () => {
  loadTags();
};

// 生命周期
onMounted(() => {
  loadTags();
});
</script>

<style scoped lang="scss">
.tag-management {
  .tag-stats {
    margin-bottom: 24px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 8px;
  }

  .tag-actions {
    margin-bottom: 16px;
  }

  .tag-search {
    margin-bottom: 24px;
  }

  .tag-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;

    .tag-item {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      padding: 16px;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.selected {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .tag-content {
        .tag-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .tag-name {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .tag-info {
          margin-bottom: 12px;

          .tag-description {
            color: #8c8c8c;
            font-size: 12px;
            margin-bottom: 8px;
          }

          .tag-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #8c8c8c;

            .case-count,
            .usage-rate {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }

        .tag-actions-inline {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .color-selector {
    .color-presets {
      display: flex;
      gap: 8px;
      margin-top: 8px;

      .color-option {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        border: 2px solid transparent;

        &:hover {
          border-color: #1890ff;
        }
      }
    }
  }

  .danger-item {
    color: #ff4d4f;

    &:hover {
      background-color: #fff2f0;
    }
  }

  .tag-cases {
    max-height: 400px;
    overflow-y: auto;
  }
}

@media (max-width: 768px) {
  .tag-management {
    .tag-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    .tag-actions {
      .ant-space {
        flex-wrap: wrap;
      }
    }
  }
}
</style>
