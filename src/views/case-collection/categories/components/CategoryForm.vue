<template>
  <div class="category-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="分类名称" name="name" required>
        <a-input 
          v-model:value="formData.name" 
          placeholder="请输入分类名称"
          show-count
          :maxlength="50"
        />
      </a-form-item>

      <a-form-item label="分类编码" name="code" required>
        <a-input 
          v-model:value="formData.code" 
          placeholder="请输入分类编码（英文字母和数字）"
          show-count
          :maxlength="30"
        />
        <div class="form-tip">
          编码用于系统内部标识，只能包含字母、数字、下划线和横线
        </div>
      </a-form-item>

      <a-form-item label="上级分类" name="parentId">
        <a-tree-select
          v-model:value="formData.parentId"
          :tree-data="parentOptions"
          placeholder="请选择上级分类（留空为顶级分类）"
          allow-clear
          tree-default-expand-all
          :field-names="{ label: 'title', value: 'value', children: 'children' }"
        />
        <div class="form-tip">
          选择上级分类后，当前分类将作为其子分类
        </div>
      </a-form-item>

      <a-form-item label="分类描述" name="description">
        <a-textarea
          v-model:value="formData.description"
          placeholder="请输入分类描述"
          :rows="3"
          show-count
          :maxlength="200"
        />
      </a-form-item>

      <a-form-item label="排序权重" name="sort">
        <a-input-number
          v-model:value="formData.sort"
          :min="0"
          :max="999"
          placeholder="排序权重"
          style="width: 100%"
        />
        <div class="form-tip">
          数值越小排序越靠前，相同权重按创建时间排序
        </div>
      </a-form-item>

      <a-form-item label="分类图标" name="icon">
        <div class="icon-selector">
          <a-input 
            v-model:value="formData.icon" 
            placeholder="请选择或输入图标名称"
            style="width: calc(100% - 100px)"
          />
          <a-button @click="showIconSelector" style="width: 90px; margin-left: 10px">
            选择图标
          </a-button>
        </div>
        <div class="icon-preview" v-if="formData.icon">
          <component :is="getIconComponent(formData.icon)" />
          <span>{{ formData.icon }}</span>
        </div>
      </a-form-item>

      <a-form-item label="分类颜色" name="color">
        <div class="color-selector">
          <a-input 
            v-model:value="formData.color" 
            placeholder="请选择颜色"
            style="width: calc(100% - 100px)"
          />
          <div 
            class="color-preview" 
            :style="{ backgroundColor: formData.color || '#1890ff' }"
            @click="showColorPicker"
          ></div>
        </div>
        <div class="color-presets">
          <div 
            v-for="color in colorPresets" 
            :key="color"
            class="color-preset"
            :style="{ backgroundColor: color }"
            @click="formData.color = color"
          ></div>
        </div>
      </a-form-item>

      <a-form-item label="关联标签" name="tags">
        <a-select 
          v-model:value="formData.tags" 
          mode="tags"
          placeholder="请输入或选择标签"
          :options="tagOptions"
          :max-tag-count="10"
        />
        <div class="form-tip">
          可以输入新标签或选择已有标签，最多10个
        </div>
      </a-form-item>

      <a-form-item label="启用状态" name="isEnabled">
        <a-switch 
          v-model:checked="formData.isEnabled"
          checked-children="启用"
          un-checked-children="禁用"
        />
        <div class="form-tip">
          禁用后该分类将不会在前端显示
        </div>
      </a-form-item>

      <a-form-item label="权限设置" name="permissions">
        <a-checkbox-group v-model:value="formData.permissions">
          <a-checkbox value="view">查看权限</a-checkbox>
          <a-checkbox value="submit">提交权限</a-checkbox>
          <a-checkbox value="manage">管理权限</a-checkbox>
        </a-checkbox-group>
        <div class="form-tip">
          设置用户对该分类的操作权限
        </div>
      </a-form-item>
    </a-form>

    <!-- 图标选择器弹窗 -->
    <a-modal
      v-model:visible="iconSelectorVisible"
      title="选择图标"
      width="600px"
      @ok="handleIconSelect"
      @cancel="iconSelectorVisible = false"
    >
      <div class="icon-grid">
        <div 
          v-for="icon in iconList" 
          :key="icon"
          class="icon-item"
          :class="{ active: selectedIcon === icon }"
          @click="selectedIcon = icon"
        >
          <component :is="getIconComponent(icon)" />
          <span>{{ icon }}</span>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import * as Icons from '@ant-design/icons-vue';
import type { CaseCategory } from '@/types/case-collection';

interface Props {
  formData: Partial<CaseCategory>;
  parentOptions: any[];
}

interface Emits {
  (e: 'submit', data: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const formRef = ref<FormInstance>();
const iconSelectorVisible = ref(false);
const selectedIcon = ref('');

const formData = reactive({
  ...props.formData,
  tags: props.formData.tags || [],
  permissions: props.formData.permissions || ['view']
});

// 颜色预设
const colorPresets = [
  '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
  '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
];

// 图标列表
const iconList = [
  'FolderOutlined', 'FileOutlined', 'TagOutlined', 'StarOutlined',
  'HeartOutlined', 'LikeOutlined', 'FireOutlined', 'ThunderboltOutlined',
  'CrownOutlined', 'TrophyOutlined', 'GiftOutlined', 'BulbOutlined'
];

// 标签选项
const tagOptions = ref([
  { label: '创新实践', value: '创新实践' },
  { label: '管理经验', value: '管理经验' },
  { label: '技术应用', value: '技术应用' },
  { label: '服务优化', value: '服务优化' },
  { label: '流程改进', value: '流程改进' }
]);

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '分类名称长度在2-50个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '编码只能包含字母、数字、下划线和横线', trigger: 'blur' },
    { min: 2, max: 30, message: '编码长度在2-30个字符', trigger: 'blur' }
  ],
  sort: [
    { type: 'number', min: 0, max: 999, message: '排序权重范围为0-999', trigger: 'change' }
  ]
};

// 计算属性
const getIconComponent = (iconName: string) => {
  return Icons[iconName as keyof typeof Icons] || Icons.FileOutlined;
};

// 方法
const showIconSelector = () => {
  selectedIcon.value = formData.icon || '';
  iconSelectorVisible.value = true;
};

const handleIconSelect = () => {
  formData.icon = selectedIcon.value;
  iconSelectorVisible.value = false;
};

const showColorPicker = () => {
  message.info('颜色选择器功能开发中...');
};

const submit = async () => {
  try {
    await formRef.value?.validate();
    emit('submit', { ...formData });
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const resetForm = () => {
  formRef.value?.resetFields();
};

// 暴露方法给父组件
defineExpose({
  submit,
  resetForm
});
</script>

<style scoped lang="scss">
.category-form {
  .form-tip {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
  }

  .icon-selector {
    display: flex;
    align-items: center;
  }

  .icon-preview {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;

    .anticon {
      font-size: 16px;
      color: #1890ff;
    }

    span {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .color-selector {
    display: flex;
    align-items: center;

    .color-preview {
      width: 80px;
      height: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      margin-left: 10px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }
    }
  }

  .color-presets {
    display: flex;
    gap: 8px;
    margin-top: 8px;

    .color-preset {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      cursor: pointer;
      border: 2px solid transparent;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        transform: scale(1.1);
      }
    }
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        background: #f6ffed;
      }

      &.active {
        border-color: #1890ff;
        background: #e6f7ff;
      }

      .anticon {
        font-size: 20px;
        margin-bottom: 4px;
        color: #1890ff;
      }

      span {
        font-size: 10px;
        color: #8c8c8c;
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .category-form {
    .icon-selector {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .ant-input {
        width: 100% !important;
      }

      .ant-btn {
        width: 100% !important;
        margin-left: 0 !important;
      }
    }

    .color-selector {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .ant-input {
        width: 100% !important;
      }

      .color-preview {
        width: 100% !important;
        margin-left: 0 !important;
      }
    }

    .icon-grid {
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      gap: 8px;

      .icon-item {
        padding: 8px;

        .anticon {
          font-size: 16px;
        }

        span {
          font-size: 9px;
        }
      }
    }
  }
}
</style>
