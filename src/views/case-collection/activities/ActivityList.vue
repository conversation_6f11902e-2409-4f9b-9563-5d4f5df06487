<template>
  <div class="activity-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>案例征集活动列表</h2>
        <p>查看和管理所有案例征集活动，支持多维度筛选和批量操作</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="batchDelete" :disabled="!selectedRowKeys.length">
            <template #icon><delete-outlined /></template>
            批量删除
          </a-button>
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button type="primary" @click="createActivity">
            <template #icon><plus-outlined /></template>
            创建活动
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 活动统计 -->
    <div class="activity-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="全部活动"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <calendar-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="进行中活动"
              :value="stats.active"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <play-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已结束活动"
              :value="stats.ended"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <stop-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="案例提交数"
              :value="stats.submissions"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="活动标题" name="keyword">
            <a-input 
              v-model:value="searchForm.keyword" 
              placeholder="请输入活动标题或关键词"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="活动状态" name="status">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="published">已发布</a-select-option>
              <a-select-option value="active">进行中</a-select-option>
              <a-select-option value="ended">已结束</a-select-option>
              <a-select-option value="cancelled">已取消</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="优先级" name="priority">
            <a-select 
              v-model:value="searchForm.priority" 
              placeholder="请选择优先级"
              allow-clear
              style="width: 120px"
            >
              <a-select-option :value="1">低</a-select-option>
              <a-select-option :value="2">普通</a-select-option>
              <a-select-option :value="3">高</a-select-option>
              <a-select-option :value="4">紧急</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="创建时间" name="dateRange">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 活动列表 -->
    <div class="activity-table">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="activities"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
        >
          <!-- 使用新的插槽语法 -->
          <template #bodyCell="{ column, record }">
            <!-- 活动标题 -->
            <template v-if="column.key === 'title'">
              <div class="activity-title-cell">
                <div class="title-content">
                  <a @click="viewActivity(record)" class="activity-link">
                    {{ record.title }}
                  </a>
                  <div class="activity-theme" v-if="record.theme">
                    {{ record.theme }}
                  </div>
                </div>
                <div class="activity-cover" v-if="record.coverImage">
                  <img :src="record.coverImage" :alt="record.title" />
                </div>
              </div>
            </template>

            <!-- 活动状态 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <a-tag v-if="record.priority > 2" color="red" size="small">
                高优先级
              </a-tag>
            </template>

            <!-- 活动时间 -->
            <template v-else-if="column.key === 'time'">
              <div class="time-info">
                <div>开始：{{ formatDate(record.startTime) }}</div>
                <div>结束：{{ formatDate(record.endTime) }}</div>
                <div v-if="record.submitDeadline">
                  截止：{{ formatDate(record.submitDeadline) }}
                </div>
              </div>
            </template>

            <!-- 统计信息 -->
            <template v-else-if="column.key === 'stats'">
              <div class="stats-info">
                <div>提交：{{ record.currentSubmissions || 0 }}/{{ record.maxSubmissions || 0 }}</div>
                <div>浏览：{{ record.viewCount || 0 }}</div>
                <div>
                  <a-progress
                    :percent="getProgressPercentage(record)"
                    size="small"
                    :show-info="false"
                  />
                </div>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="查看详情">
                  <a-button type="link" size="small" @click="viewActivity(record)">
                    <template #icon><eye-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-tooltip title="编辑活动">
                  <a-button type="link" size="small" @click="editActivity(record)">
                    <template #icon><edit-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-dropdown>
                  <a-button type="link" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="copyActivity(record)">
                        <copy-outlined />
                        复制活动
                      </a-menu-item>
                      <a-menu-item @click="exportActivity(record)">
                        <export-outlined />
                        导出数据
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item @click="deleteActivity(record)" class="danger-item">
                        <delete-outlined />
                        删除活动
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import {
  ReloadOutlined,
  DeleteOutlined,
  ExportOutlined,
  PlusOutlined,
  CalendarOutlined,
  PlayCircleOutlined,
  StopOutlined,
  FileTextOutlined,
  SearchOutlined,
  ClearOutlined,
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  CopyOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity, CaseCollectionActivityQueryParams } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateActivity, validateStatistics } from '@/utils/data-validation';
import { usePageLoading, PageDataProcessor } from '@/utils/page-optimization';
import { formatDate } from '@/utils/date';

const router = useRouter();

// 响应式数据
const activities = ref<CaseCollectionActivity[]>([]);
const selectedRowKeys = ref<number[]>([]);

// 使用页面优化工具
const pageLoading = usePageLoading();

const stats = reactive({
  total: 0,
  active: 0,
  ended: 0,
  submissions: 0
});

const searchForm = reactive({
  keyword: '',
  status: undefined,
  priority: undefined,
  dateRange: undefined
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '活动标题',
    dataIndex: 'title',
    key: 'title',
    width: 300
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '活动时间',
    key: 'time',
    width: 200
  },
  {
    title: '统计信息',
    key: 'stats',
    width: 150
  },
  {
    title: '创建者',
    dataIndex: 'organizerName',
    key: 'organizerName',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ text }) => formatDate(text)
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  }
}));

// 方法
const loadActivities = async () => {
  await pageLoading.execute(async () => {
    const params: CaseCollectionActivityQueryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status,
      priority: searchForm.priority
    };

    if (searchForm.dateRange && Array.isArray(searchForm.dateRange) && searchForm.dateRange.length === 2) {
      (params as any).startTime = searchForm.dateRange[0]?.format?.('YYYY-MM-DD');
      (params as any).endTime = searchForm.dateRange[1]?.format?.('YYYY-MM-DD');
    }

    const response = await caseCollectionService.getActivityList(params);

    if (response.success && response.data) {
      // 使用数据验证工具处理活动列表
      const paginationData = PageDataProcessor.processPaginationData(response, validateActivity);
      activities.value = paginationData.list;
      pagination.total = paginationData.total;
    } else {
      throw new Error(response.message || '加载活动列表失败');
    }
  });
};

const loadStats = async () => {
  await pageLoading.execute(async () => {
    const response = await caseCollectionService.getActivityStatistics();

    if (response.success && response.data) {
      const validatedStats = validateStatistics(response.data);
      stats.total = validatedStats.totalActivities || 0;
      stats.active = validatedStats.activeActivities || 0;
      stats.ended = validatedStats.endedActivities || 0;
      stats.submissions = validatedStats.totalSubmissions || 0;
    } else {
      throw new Error('加载统计数据失败');
    }
  });
};

// 工具方法
const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'published': 'blue',
    'active': 'green',
    'ended': 'orange',
    'cancelled': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'active': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusTexts[status] || '未知';
};

const getProgressPercentage = (activity: CaseCollectionActivity) => {
  if (!activity.maxSubmissions || activity.maxSubmissions === 0) {
    return 0;
  }
  const current = activity.currentSubmissions || 0;
  return Math.min(Math.round((current / activity.maxSubmissions) * 100), 100);
};

// 事件处理
const handleSearch = () => {
  pagination.current = 1;
  loadActivities();
};

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    priority: undefined,
    dateRange: undefined
  });
  pagination.current = 1;
  loadActivities();
};

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1;
  pagination.pageSize = pag.pageSize || 10;
  loadActivities();
};

const refreshData = () => {
  loadActivities();
  loadStats();
};

// 导航方法
const createActivity = () => {
  router.push('/case-collection/activities/create');
};

const viewActivity = (activity: CaseCollectionActivity) => {
  router.push(`/case-collection/activities/${activity.id}`);
};

const editActivity = (activity: CaseCollectionActivity) => {
  router.push(`/case-collection/activities/${activity.id}/edit`);
};

// 操作方法
const deleteActivity = (activity: CaseCollectionActivity) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除活动"${activity.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await caseCollectionService.deleteActivity(activity.id);
        if (response.success) {
          message.success('删除活动成功');
          refreshData();
        } else {
          message.error(response.message || '删除活动失败');
        }
      } catch (error) {
        console.error('删除活动失败:', error);
        message.error('删除活动失败');
      }
    }
  });
};

const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的活动');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个活动吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        // 这里应该调用批量删除API
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        refreshData();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

const copyActivity = (activity: CaseCollectionActivity) => {
  // 复制活动逻辑
  message.info('复制功能开发中...');
};

const exportActivity = (activity: CaseCollectionActivity) => {
  // 导出单个活动数据
  message.info('导出功能开发中...');
};

const exportData = () => {
  // 导出所有数据
  message.info('导出功能开发中...');
};

// 生命周期
onMounted(() => {
  loadActivities();
  loadStats();
});
</script>

<style scoped lang="scss">
.activity-list {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .activity-stats {
    margin-bottom: 24px;

    .ant-statistic {
      text-align: center;
    }
  }

  .search-filters {
    margin-bottom: 24px;

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .activity-table {
    .activity-title-cell {
      display: flex;
      align-items: center;
      gap: 12px;

      .title-content {
        flex: 1;

        .activity-link {
          font-weight: 500;
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        .activity-theme {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 2px;
        }
      }

      .activity-cover {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .time-info {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .stats-info {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .danger-item {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
      }
    }
  }
}

@media (max-width: 768px) {
  .activity-list {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;
        overflow-x: auto;
      }
    }

    .search-filters {
      .ant-form {
        .ant-form-item {
          width: 100%;
          margin-bottom: 12px;

          .ant-form-item-control-input {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
