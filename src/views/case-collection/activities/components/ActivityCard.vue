<template>
  <a-card
    class="activity-card"
    :hoverable="true"
  >
    <!-- 卡片封面 -->
    <template #cover>
      <div
        class="card-cover"
        :class="{ 'default-cover': !activity.coverImage }"
        :style="!activity.coverImage ? getDefaultCoverStyle() : {}"
      >
        <img v-if="activity.coverImage" :src="activity.coverImage" :alt="activity.title" />
        <div v-else class="default-cover-content">
          <div class="default-cover-icon">
            <calendar-outlined />
          </div>
          <div class="default-cover-text">{{ activity.theme || '案例征集活动' }}</div>
        </div>
        <div class="cover-overlay">
          <a-space>
            <a-button type="primary" ghost size="small" @click="handleView">
              <template #icon><eye-outlined /></template>
              查看
            </a-button>
            <a-button ghost size="small" @click="handleEdit">
              <template #icon><edit-outlined /></template>
              编辑
            </a-button>
          </a-space>
        </div>
      </div>
    </template>

    <!-- 活动状态标签 -->
    <div class="activity-status">
      <a-tag :color="getStatusColor(activity.status)">
        {{ getStatusText(activity.status) }}
      </a-tag>
      <a-tag v-if="activity.priority && activity.priority > 2" color="red">
        高优先级
      </a-tag>
    </div>

    <!-- 活动标题 -->
    <div class="activity-title">
      <h3 @click="handleView">{{ activity.title }}</h3>
      <p class="activity-theme" v-if="activity.theme">{{ activity.theme }}</p>
    </div>

    <!-- 活动描述 -->
    <div class="activity-description">
      <p>{{ truncateText(activity.description, 100) }}</p>
    </div>

    <!-- 活动时间信息 -->
    <div class="activity-time">
      <div class="time-item">
        <clock-circle-outlined />
        <span>开始时间：{{ formatDate(activity.startTime) }}</span>
      </div>
      <div class="time-item">
        <clock-circle-outlined />
        <span>结束时间：{{ formatDate(activity.endTime) }}</span>
      </div>
      <div class="time-item" v-if="activity.submitDeadline">
        <calendar-outlined />
        <span>提交截止：{{ formatDate(activity.submitDeadline) }}</span>
      </div>
    </div>

    <!-- 活动统计信息 -->
    <div class="activity-stats">
      <a-row :gutter="8">
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ activity.currentSubmissions || 0 }}</div>
            <div class="stat-label">提交数</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ activity.viewCount || 0 }}</div>
            <div class="stat-label">浏览数</div>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="stat-item">
            <div class="stat-value">{{ getProgressPercentage() }}%</div>
            <div class="stat-label">进度</div>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 进度条 -->
    <div class="activity-progress">
      <a-progress 
        :percent="getProgressPercentage()" 
        :stroke-color="getProgressColor()"
        :show-info="false"
        size="small"
      />
    </div>

    <!-- 操作按钮 -->
    <template #actions>
      <a-tooltip title="查看详情">
        <eye-outlined @click="handleView" />
      </a-tooltip>
      <a-tooltip title="编辑活动">
        <edit-outlined @click="handleEdit" />
      </a-tooltip>
      <a-tooltip title="更多操作">
        <a-dropdown>
          <more-outlined />
          <template #overlay>
            <a-menu>
              <a-menu-item key="copy" @click="handleCopy">
                <copy-outlined />
                复制活动
              </a-menu-item>
              <a-menu-item key="export" @click="handleExport">
                <export-outlined />
                导出数据
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="delete" @click="handleDelete" class="danger-item">
                <delete-outlined />
                删除活动
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </a-tooltip>
    </template>

    <!-- 活动标签 -->
    <div class="activity-tags" v-if="activity.tags && activity.tags.length">
      <a-tag 
        v-for="tag in activity.tags.slice(0, 3)" 
        :key="tag" 
        size="small"
      >
        {{ tag }}
      </a-tag>
      <a-tag v-if="activity.tags.length > 3" size="small">
        +{{ activity.tags.length - 3 }}
      </a-tag>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Modal } from 'ant-design-vue';
import {
  EyeOutlined,
  EditOutlined,
  MoreOutlined,
  CopyOutlined,
  ExportOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity } from '@/types/case-collection';
import { formatDate } from '@/utils/date';

interface Props {
  activity: CaseCollectionActivity;
}

interface Emits {
  (e: 'view', activity: CaseCollectionActivity): void;
  (e: 'edit', activity: CaseCollectionActivity): void;
  (e: 'delete', activity: CaseCollectionActivity): void;
  (e: 'copy', activity: CaseCollectionActivity): void;
  (e: 'export', activity: CaseCollectionActivity): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性
const getProgressPercentage = () => {
  if (!props.activity.maxSubmissions || props.activity.maxSubmissions === 0) {
    return 0;
  }
  const current = props.activity.currentSubmissions || 0;
  return Math.min(Math.round((current / props.activity.maxSubmissions) * 100), 100);
};

const getProgressColor = () => {
  const percentage = getProgressPercentage();
  if (percentage >= 80) return '#52c41a';
  if (percentage >= 50) return '#faad14';
  return '#1890ff';
};

const getDefaultCoverStyle = () => {
  const theme = props.activity.theme || '';
  const gradients: Record<string, string> = {
    '数字化转型': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    '绿色发展': 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)',
    '党建创新': 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)',
    '技术创新': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    '管理创新': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    '服务创新': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    '默认': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  };

  // 根据主题选择渐变色，如果没有匹配的主题则使用默认色
  const gradient = gradients[theme] || gradients['默认'];
  return { background: gradient };
};

// 方法
const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'published': 'blue',
    'active': 'green',
    'ended': 'orange',
    'cancelled': 'red'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'active': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusTexts[status] || '未知';
};

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 事件处理
const handleView = () => {
  emit('view', props.activity);
};

const handleEdit = () => {
  emit('edit', props.activity);
};

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除活动"${props.activity.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    onOk() {
      emit('delete', props.activity);
    }
  });
};

const handleCopy = () => {
  emit('copy', props.activity);
};

const handleExport = () => {
  emit('export', props.activity);
};
</script>

<style scoped lang="scss">
.activity-card {
  height: 100%;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }

  .card-cover {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }

    &.default-cover {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .default-cover-content {
      text-align: center;
      color: white;
      z-index: 1;

      .default-cover-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.9;
      }

      .default-cover-text {
        font-size: 16px;
        font-weight: 500;
        opacity: 0.9;
      }
    }

    .cover-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      img {
        transform: scale(1.05);
      }

      .cover-overlay {
        opacity: 1;
      }
    }
  }

  .activity-status {
    margin-bottom: 12px;
    display: flex;
    gap: 8px;
  }

  .activity-title {
    margin-bottom: 12px;

    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .activity-theme {
      margin: 0;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .activity-description {
    margin-bottom: 16px;

    p {
      margin: 0;
      color: #595959;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .activity-time {
    margin-bottom: 16px;

    .time-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 12px;
      color: #8c8c8c;

      .anticon {
        margin-right: 4px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .activity-stats {
    margin-bottom: 12px;

    .stat-item {
      text-align: center;

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        line-height: 1;
      }

      .stat-label {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 2px;
      }
    }
  }

  .activity-progress {
    margin-bottom: 16px;
  }

  .activity-tags {
    margin-top: 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }

  :deep(.ant-card-actions) {
    .anticon {
      font-size: 16px;
      cursor: pointer;
      transition: color 0.3s ease;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .danger-item {
    color: #ff4d4f;

    &:hover {
      background-color: #fff2f0;
    }
  }
}

@media (max-width: 768px) {
  .activity-card {
    .card-cover {
      height: 150px;
    }

    .activity-title h3 {
      font-size: 14px;
    }

    .activity-stats .stat-item {
      .stat-value {
        font-size: 16px;
      }

      .stat-label {
        font-size: 11px;
      }
    }
  }
}
</style>
