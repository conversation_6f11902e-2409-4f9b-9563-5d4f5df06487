<template>
  <div class="activity-preview">
    <h3>活动预览</h3>
    <p class="preview-tip">请仔细检查活动信息，确认无误后提交</p>

    <div class="preview-content">
      <!-- 基本信息 -->
      <div class="preview-section">
        <h4>基本信息</h4>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="活动标题" :span="2">
            {{ activity.title }}
          </a-descriptions-item>
          <a-descriptions-item label="活动主题">
            {{ activity.theme || '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(activity.priority)">
              {{ getPriorityText(activity.priority) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="活动描述" :span="2">
            {{ activity.description }}
          </a-descriptions-item>
          <a-descriptions-item label="详细内容" :span="2" v-if="activity.content">
            <div class="content-preview">
              {{ activity.content }}
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 时间信息 -->
      <div class="preview-section">
        <h4>时间安排</h4>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="活动开始时间">
            <clock-circle-outlined />
            {{ formatDateTime(activity.startTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="活动结束时间">
            <clock-circle-outlined />
            {{ formatDateTime(activity.endTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="提交截止时间">
            <calendar-outlined />
            {{ formatDateTime(activity.submitDeadline) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 配置信息 -->
      <div class="preview-section">
        <h4>配置信息</h4>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="最大提交数量">
            {{ activity.maxSubmissions || '无限制' }}
          </a-descriptions-item>
          <a-descriptions-item label="最大文件大小">
            {{ activity.maxFileSize }}MB
          </a-descriptions-item>
          <a-descriptions-item label="允许的文件类型" :span="2">
            <a-space wrap>
              <a-tag v-for="type in activity.allowedFileTypes" :key="type">
                {{ type.toUpperCase() }}
              </a-tag>
            </a-space>
          </a-descriptions-item>
          <a-descriptions-item label="是否公开">
            <a-tag :color="activity.isPublic ? 'green' : 'orange'">
              {{ activity.isPublic ? '公开' : '私有' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 活动规则 -->
      <div class="preview-section" v-if="activity.rules">
        <h4>活动规则</h4>
        <div class="rules-content">
          {{ activity.rules }}
        </div>
      </div>

      <!-- 参与要求 -->
      <div class="preview-section" v-if="activity.requirements && activity.requirements.length">
        <h4>参与要求</h4>
        <ul class="requirements-list">
          <li v-for="requirement in activity.requirements" :key="requirement">
            {{ requirement }}
          </li>
        </ul>
      </div>

      <!-- 奖项设置 -->
      <div class="preview-section" v-if="activity.awards && activity.awards.length">
        <h4>奖项设置</h4>
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="总奖金">
            <span class="prize-amount">¥{{ formatNumber(activity.totalPrize) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="奖项设置">
            <a-space wrap>
              <a-tag v-for="award in activity.awards" :key="award" color="gold">
                {{ award }}
              </a-tag>
            </a-space>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 活动标签 -->
      <div class="preview-section" v-if="activity.tags && activity.tags.length">
        <h4>活动标签</h4>
        <a-space wrap>
          <a-tag v-for="tag in activity.tags" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </a-space>
      </div>

      <!-- 联系信息 -->
      <div class="preview-section" v-if="activity.contactInfo">
        <h4>联系信息</h4>
        <div class="contact-info">
          {{ activity.contactInfo }}
        </div>
      </div>

      <!-- 封面图片 -->
      <div class="preview-section" v-if="activity.coverImage">
        <h4>封面图片</h4>
        <div class="cover-image">
          <img :src="activity.coverImage" :alt="activity.title" />
        </div>
      </div>

      <!-- 活动图片 -->
      <div class="preview-section" v-if="activity.images && activity.images.length">
        <h4>活动图片</h4>
        <div class="activity-images">
          <div v-for="(image, index) in activity.images" :key="index" class="image-item">
            <img :src="image" :alt="`活动图片${index + 1}`" />
          </div>
        </div>
      </div>

      <!-- 活动附件 -->
      <div class="preview-section" v-if="activity.attachments">
        <h4>活动附件</h4>
        <div class="attachments-list">
          <div v-for="attachment in getAttachments()" :key="attachment.name" class="attachment-item">
            <file-outlined />
            <span class="attachment-name">{{ attachment.name }}</span>
            <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  ClockCircleOutlined,
  CalendarOutlined,
  FileOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity } from '@/types/case-collection';
import { formatDate } from '@/utils/date';

interface Props {
  activity: Partial<CaseCollectionActivity>;
}

const props = defineProps<Props>();

// 计算属性
const getAttachments = () => {
  if (!props.activity.attachments) return [];
  try {
    return JSON.parse(props.activity.attachments);
  } catch {
    return [];
  }
};

// 方法
const getPriorityColor = (priority?: number) => {
  const colors: Record<number, string> = {
    1: 'default',
    2: 'blue',
    3: 'orange',
    4: 'red'
  };
  return colors[priority || 2] || 'blue';
};

const getPriorityText = (priority?: number) => {
  const texts: Record<number, string> = {
    1: '低',
    2: '普通',
    3: '高',
    4: '紧急'
  };
  return texts[priority || 2] || '普通';
};

const formatDateTime = (dateTime: any) => {
  if (!dateTime) return '未设置';
  return formatDate(dateTime, 'YYYY-MM-DD HH:mm:ss');
};

const formatNumber = (num?: number) => {
  if (!num) return '0';
  return num.toLocaleString();
};

const formatFileSize = (size?: number) => {
  if (!size) return '0B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`;
};
</script>

<style scoped lang="scss">
.activity-preview {
  h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
  }

  .preview-tip {
    margin: 0 0 24px 0;
    color: #8c8c8c;
    font-size: 14px;
  }

  .preview-content {
    .preview-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        padding-bottom: 8px;
        border-bottom: 2px solid #1890ff;
      }

      .content-preview {
        max-height: 200px;
        overflow-y: auto;
        padding: 12px;
        background: #fafafa;
        border-radius: 4px;
        white-space: pre-wrap;
        line-height: 1.6;
      }

      .rules-content {
        padding: 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;
        white-space: pre-wrap;
        line-height: 1.6;
      }

      .requirements-list {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .prize-amount {
        font-size: 18px;
        font-weight: 600;
        color: #fa8c16;
      }

      .contact-info {
        padding: 12px;
        background: #f0f9ff;
        border: 1px solid #91d5ff;
        border-radius: 4px;
        line-height: 1.6;
      }

      .cover-image {
        img {
          max-width: 100%;
          max-height: 300px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }

      .activity-images {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;

        .image-item {
          img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .attachments-list {
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 12px;
          background: #fafafa;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }

          .attachment-name {
            flex: 1;
            font-weight: 500;
          }

          .attachment-size {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .activity-preview {
    .preview-content {
      .preview-section {
        .activity-images {
          grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
          gap: 12px;

          .image-item img {
            height: 120px;
          }
        }

        :deep(.ant-descriptions) {
          .ant-descriptions-item-label {
            width: 100px;
          }
        }
      }
    }
  }
}
</style>
