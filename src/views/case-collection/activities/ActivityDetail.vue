<template>
  <div class="activity-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <a-button @click="goBack" class="back-btn">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <div class="header-info">
          <h1>{{ activity?.title }}</h1>
          <div class="header-meta">
            <a-tag :color="getStatusColor(activity?.status)">
              {{ getStatusText(activity?.status) }}
            </a-tag>
            <a-tag v-if="activity?.priority && activity.priority > 2" color="red">
              高优先级
            </a-tag>
            <span class="meta-item">
              <user-outlined />
              {{ activity?.organizerName }}
            </span>
            <span class="meta-item">
              <clock-circle-outlined />
              {{ formatDate(activity?.createTime) }}
            </span>
            <span class="meta-item">
              <eye-outlined />
              {{ activity?.viewCount || 0 }} 次浏览
            </span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="exportData">
            <template #icon><export-outlined /></template>
            导出数据
          </a-button>
          <a-button @click="viewSubmissions">
            <template #icon><file-text-outlined /></template>
            查看提交
          </a-button>
          <a-button type="primary" @click="editActivity">
            <template #icon><edit-outlined /></template>
            编辑活动
          </a-button>
        </a-space>
      </div>
    </div>

    <a-spin :spinning="false">
      <div class="detail-content">
        <!-- 活动概览 -->
        <div class="overview-section">
          <a-row :gutter="24">
            <!-- 左侧内容 -->
            <a-col :xs="24" :lg="16">
              <!-- 封面图片 -->
              <div v-if="activity?.coverImage" class="cover-section">
                <img :src="activity.coverImage" :alt="activity.title" class="cover-image" />
              </div>

              <!-- 活动描述 -->
              <a-card title="活动描述" :bordered="false" class="description-card">
                <p class="activity-description">{{ activity?.description }}</p>
                <div v-if="activity?.content" class="activity-content">
                  <h4>详细内容</h4>
                  <div class="content-text">{{ activity.content }}</div>
                </div>
              </a-card>

              <!-- 活动规则 -->
              <a-card v-if="activity?.rules" title="活动规则" :bordered="false" class="rules-card">
                <div class="rules-content">{{ activity.rules }}</div>
              </a-card>

              <!-- 参与要求 -->
              <a-card v-if="activity?.requirements?.length" title="参与要求" :bordered="false">
                <ul class="requirements-list">
                  <li v-for="requirement in activity.requirements" :key="requirement">
                    {{ requirement }}
                  </li>
                </ul>
              </a-card>

              <!-- 活动图片 -->
              <a-card title="活动图片" :bordered="false">
                <div class="activity-images">
                  <div v-for="index in (activity?.images?.length || 3)" :key="index" class="image-item">
                    <div
                      class="image-placeholder"
                      :style="generateDefaultBackground(activity?.theme || '默认', 'placeholder')"
                      @click="handleImageClick(index)"
                    >
                      <div class="placeholder-content">
                        <file-outlined style="font-size: 24px; margin-bottom: 8px;" />
                        <div>活动图片 {{ index }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </a-card>

              <!-- 活动附件 -->
              <a-card v-if="getAttachments().length" title="活动附件" :bordered="false">
                <div class="attachments-list">
                  <div v-for="attachment in getAttachments()" :key="attachment.name" class="attachment-item">
                    <file-outlined />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                    <a-button type="link" size="small" @click="downloadAttachment(attachment)">
                      <template #icon><download-outlined /></template>
                      下载
                    </a-button>
                  </div>
                </div>
              </a-card>
            </a-col>

            <!-- 右侧信息 -->
            <a-col :xs="24" :lg="8">
              <!-- 活动统计 -->
              <a-card title="活动统计" :bordered="false" class="stats-card">
                <div class="stats-grid">
                  <div class="stat-item">
                    <div class="stat-value">{{ activity?.currentSubmissions || 0 }}</div>
                    <div class="stat-label">案例提交数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ activity?.maxSubmissions || 0 }}</div>
                    <div class="stat-label">最大提交数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ getProgressPercentage() }}%</div>
                    <div class="stat-label">完成进度</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ activity?.viewCount || 0 }}</div>
                    <div class="stat-label">浏览次数</div>
                  </div>
                </div>
                <div class="progress-section">
                  <h4>提交进度</h4>
                  <a-progress 
                    :percent="getProgressPercentage()" 
                    :stroke-color="getProgressColor()"
                  />
                </div>
              </a-card>

              <!-- 时间信息 -->
              <a-card title="时间安排" :bordered="false">
                <div class="time-info">
                  <div class="time-item">
                    <div class="time-label">
                      <play-circle-outlined />
                      活动开始
                    </div>
                    <div class="time-value">{{ formatDate(activity?.startTime) }}</div>
                  </div>
                  <div class="time-item">
                    <div class="time-label">
                      <stop-outlined />
                      活动结束
                    </div>
                    <div class="time-value">{{ formatDate(activity?.endTime) }}</div>
                  </div>
                  <div class="time-item">
                    <div class="time-label">
                      <calendar-outlined />
                      提交截止
                    </div>
                    <div class="time-value">{{ formatDate(activity?.submitDeadline) }}</div>
                  </div>
                </div>
              </a-card>

              <!-- 配置信息 -->
              <a-card title="配置信息" :bordered="false">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item label="最大文件大小">
                    {{ activity?.maxFileSize }}MB
                  </a-descriptions-item>
                  <a-descriptions-item label="允许文件类型">
                    <a-space wrap>
                      <a-tag v-for="type in activity?.allowedFileTypes" :key="type" size="small">
                        {{ type.toUpperCase() }}
                      </a-tag>
                    </a-space>
                  </a-descriptions-item>
                  <a-descriptions-item label="是否公开">
                    <a-tag :color="activity?.isPublic ? 'green' : 'orange'" size="small">
                      {{ activity?.isPublic ? '公开' : '私有' }}
                    </a-tag>
                  </a-descriptions-item>
                </a-descriptions>
              </a-card>

              <!-- 奖项设置 -->
              <a-card v-if="activity?.awards?.length || activity?.totalPrize" title="奖项设置" :bordered="false">
                <div v-if="activity.totalPrize" class="prize-info">
                  <div class="prize-label">总奖金</div>
                  <div class="prize-value">¥{{ formatNumber(activity.totalPrize) }}</div>
                </div>
                <div v-if="activity.awards?.length" class="awards-list">
                  <div class="awards-label">奖项设置</div>
                  <a-space wrap>
                    <a-tag v-for="award in activity.awards" :key="award" color="gold" size="small">
                      {{ award }}
                    </a-tag>
                  </a-space>
                </div>
              </a-card>

              <!-- 活动标签 -->
              <a-card v-if="activity?.tags?.length" title="活动标签" :bordered="false">
                <a-space wrap>
                  <a-tag v-for="tag in activity.tags" :key="tag" color="blue">
                    {{ tag }}
                  </a-tag>
                </a-space>
              </a-card>

              <!-- 联系信息 -->
              <a-card v-if="activity?.contactInfo" title="联系信息" :bordered="false">
                <div class="contact-info">{{ activity.contactInfo }}</div>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-spin>

    <!-- 图片预览 -->
    <a-modal
      v-model:visible="previewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  UserOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  ExportOutlined,
  FileTextOutlined,
  EditOutlined,
  FileOutlined,
  DownloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateActivity } from '@/utils/data-validation';
import { generateDefaultBackground, usePageLoading } from '@/utils/page-optimization';
import { formatDate } from '@/utils/date';

const router = useRouter();
const route = useRoute();

// 响应式数据
const activity = ref<CaseCollectionActivity>();
const previewVisible = ref(false);
const previewImageUrl = ref('');

// 使用页面优化工具
const pageLoading = usePageLoading();

const activityId = computed(() => Number(route.params.id));

// 方法
const loadActivity = async () => {
  await pageLoading.execute(async () => {
    const response = await caseCollectionService.getActivityDetail(activityId.value);

    if (response.success && response.data) {
      activity.value = validateActivity(response.data);
    } else {
      throw new Error(response.message || '加载活动详情失败');
    }
  });
};

const getStatusColor = (status?: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'published': 'blue',
    'active': 'green',
    'ended': 'orange',
    'cancelled': 'red'
  };
  return statusColors[status || ''] || 'default';
};

const getStatusText = (status?: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'published': '已发布',
    'active': '进行中',
    'ended': '已结束',
    'cancelled': '已取消'
  };
  return statusTexts[status || ''] || '未知';
};

const getProgressPercentage = () => {
  if (!activity.value?.maxSubmissions || activity.value.maxSubmissions === 0) {
    return 0;
  }
  const current = activity.value.currentSubmissions || 0;
  return Math.min(Math.round((current / activity.value.maxSubmissions) * 100), 100);
};

const getProgressColor = () => {
  const percentage = getProgressPercentage();
  if (percentage >= 80) return '#52c41a';
  if (percentage >= 50) return '#faad14';
  return '#1890ff';
};

const getAttachments = () => {
  if (!activity.value?.attachments) return [];
  try {
    return JSON.parse(activity.value.attachments);
  } catch {
    return [];
  }
};

const formatNumber = (num?: number) => {
  if (!num) return '0';
  return num.toLocaleString();
};

const formatFileSize = (size?: number) => {
  if (!size) return '0B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`;
};

// 事件处理
const goBack = () => {
  router.back();
};

const editActivity = () => {
  router.push(`/case-collection/activities/${activityId.value}/edit`);
};

const viewSubmissions = () => {
  router.push(`/case-collection/submissions?activityId=${activityId.value}`);
};

const exportData = () => {
  message.info('导出功能开发中...');
};

const handleImageClick = (index: number) => {
  // 图片点击处理，显示占位符信息
  // message.info(`这是活动图片 ${index} 的占位符`);
};

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl;
  previewVisible.value = true;
};

const downloadAttachment = (attachment: any) => {
  // 下载附件逻辑
  const link = document.createElement('a');
  link.href = attachment.url;
  link.download = attachment.name;
  link.click();
};

// 生命周期
onMounted(() => {
  loadActivity();
});
</script>

<style scoped lang="scss">
.activity-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-content {
      flex: 1;

      .back-btn {
        margin-bottom: 16px;
      }

      .header-info {
        h1 {
          margin: 0 0 12px 0;
          font-size: 28px;
          font-weight: 600;
          color: #262626;
          line-height: 1.2;
        }

        .header-meta {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #8c8c8c;
            font-size: 14px;

            .anticon {
              color: #1890ff;
            }
          }
        }
      }
    }

    .header-actions {
      flex-shrink: 0;
      margin-left: 24px;
    }
  }

  .detail-content {
    .cover-section {
      margin-bottom: 24px;

      .cover-image {
        width: 100%;
        max-height: 400px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .description-card {
      margin-bottom: 24px;

      .activity-description {
        font-size: 16px;
        line-height: 1.6;
        color: #262626;
        margin-bottom: 16px;
      }

      .activity-content {
        h4 {
          margin: 16px 0 8px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        .content-text {
          padding: 16px;
          background: #fafafa;
          border-radius: 4px;
          white-space: pre-wrap;
          line-height: 1.6;
        }
      }
    }

    .rules-card {
      margin-bottom: 24px;

      .rules-content {
        padding: 16px;
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;
        white-space: pre-wrap;
        line-height: 1.6;
      }
    }

    .requirements-list {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .activity-images {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .image-item {
        .image-placeholder {
          width: 100%;
          height: 150px;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;

          &:hover {
            transform: scale(1.05);
          }

          .placeholder-content {
            text-align: center;
            font-size: 14px;
            font-weight: 500;
          }
        }

        img {
          width: 100%;
          height: 150px;
          object-fit: cover;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .attachments-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 12px;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }

        .attachment-name {
          flex: 1;
          font-weight: 500;
        }

        .attachment-size {
          color: #8c8c8c;
          font-size: 12px;
          margin-right: 8px;
        }
      }
    }

    .stats-card {
      margin-bottom: 24px;

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .stat-item {
          text-align: center;
          padding: 16px;
          background: #fafafa;
          border-radius: 8px;

          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            line-height: 1;
          }

          .stat-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
          }
        }
      }

      .progress-section {
        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #262626;
        }
      }
    }

    .time-info {
      .time-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .time-label {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #8c8c8c;
          font-size: 14px;

          .anticon {
            color: #1890ff;
          }
        }

        .time-value {
          font-weight: 500;
          color: #262626;
        }
      }
    }

    .prize-info {
      margin-bottom: 16px;

      .prize-label {
        font-size: 14px;
        color: #8c8c8c;
        margin-bottom: 4px;
      }

      .prize-value {
        font-size: 20px;
        font-weight: 600;
        color: #fa8c16;
      }
    }

    .awards-list {
      .awards-label {
        font-size: 14px;
        color: #8c8c8c;
        margin-bottom: 8px;
      }
    }

    .contact-info {
      padding: 12px;
      background: #f0f9ff;
      border: 1px solid #91d5ff;
      border-radius: 4px;
      line-height: 1.6;
    }
  }
}

@media (max-width: 768px) {
  .activity-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content {
        .header-info h1 {
          font-size: 24px;
        }

        .header-meta {
          gap: 12px;

          .meta-item {
            font-size: 12px;
          }
        }
      }

      .header-actions {
        width: 100%;
        margin-left: 0;
      }
    }

    .detail-content {
      .activity-images {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;

        .image-item img {
          height: 120px;
        }
      }

      .stats-card .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .stat-item {
          padding: 12px;

          .stat-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>
