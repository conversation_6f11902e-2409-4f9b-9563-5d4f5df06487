<template>
  <div class="case-collection-activities">
    <!-- 页面头部 -->
    <div class="page-header">
      <case-breadcrumb />
      <div class="header-content">
        <h1>案例征集活动管理</h1>
        <p>统一的案例征集活动组织、管理和监控平台</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic 
              title="活动总数" 
              :value="stats.totalActivities" 
              :loading="statsLoading"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="进行中活动" 
              :value="stats.activeActivities" 
              :value-style="{ color: '#1890ff' }"
              :loading="statsLoading"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="案例提交数" 
              :value="stats.totalSubmissions" 
              :value-style="{ color: '#52c41a' }"
              :loading="statsLoading"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic 
              title="待审核案例" 
              :value="stats.pendingReviews" 
              :value-style="{ color: '#faad14' }"
              :loading="statsLoading"
            />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToActivityList">
              <div class="action-icon">
                <calendar-outlined />
              </div>
              <div class="action-content">
                <h3>活动列表</h3>
                <p>查看和管理所有征集活动</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="createActivity">
              <div class="action-icon">
                <plus-outlined />
              </div>
              <div class="action-content">
                <h3>创建活动</h3>
                <p>发起新的案例征集活动</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToSubmissions">
              <div class="action-icon">
                <file-text-outlined />
              </div>
              <div class="action-content">
                <h3>案例管理</h3>
                <p>查看和管理提交的案例</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToReviews">
              <div class="action-icon">
                <audit-outlined />
              </div>
              <div class="action-content">
                <h3>案例预审</h3>
                <p>审核和评价提交的案例</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToCategories">
              <div class="action-icon">
                <tag-outlined />
              </div>
              <div class="action-content">
                <h3>分类管理</h3>
                <p>管理案例分类和标签</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToDashboard">
              <div class="action-icon">
                <bar-chart-outlined />
              </div>
              <div class="action-content">
                <h3>数据统计</h3>
                <p>查看活动数据和分析报告</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 最新活动 -->
    <div class="recent-activities">
      <a-card title="最新活动" :bordered="false">
        <template #extra>
          <a-button type="link" @click="goToActivityList">查看全部</a-button>
        </template>
        
        <div>
          <div v-if="recentActivities.length === 0" class="empty-state">
            <a-empty description="暂无活动数据">
              <a-button type="primary" @click="createActivity">创建第一个活动</a-button>
            </a-empty>
          </div>

          <a-row v-else :gutter="[16, 16]">
            <a-col
              v-for="activity in recentActivities"
              :key="activity.id"
              :xs="24" :sm="12" :lg="8"
            >
              <activity-card
                :activity="activity"
                @view="viewActivity"
                @edit="editActivity"
                @delete="deleteActivity"
              />
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 热门活动 -->
    <div class="popular-activities">
      <a-card title="热门活动" :bordered="false">
        <template #extra>
          <a-button type="link" @click="goToActivityList">查看全部</a-button>
        </template>
        
        <div>
          <div v-if="popularActivities.length === 0" class="empty-state">
            <a-empty description="暂无热门活动数据" />
          </div>

          <a-row v-else :gutter="[16, 16]">
            <a-col
              v-for="activity in popularActivities"
              :key="activity.id"
              :xs="24" :sm="12" :lg="8"
            >
              <activity-card
                :activity="activity"
                @view="viewActivity"
                @edit="editActivity"
                @delete="deleteActivity"
              />
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useLoading } from '@/composables/useLoading';
import { feedbackSystem } from '@/utils/feedback-system';
import {
  CalendarOutlined,
  PlusOutlined,
  FileTextOutlined,
  AuditOutlined,
  TagOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity, CaseCollectionStatistics } from '@/types/case-collection';
import { CaseSubmissionStatus, CaseReviewResult } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateStatistics } from '@/utils/data-validation';
import CaseBreadcrumb from '@/components/common/CaseBreadcrumb.vue';
import ActivityCard from './components/ActivityCard.vue';

const router = useRouter();

// 加载状态管理
const { execute: executeWithLoading } = useLoading();
const statsLoading = useLoading();

// 响应式数据
const stats = ref<CaseCollectionStatistics>({
  totalActivities: 0,
  activeActivities: 0,
  endedActivities: 0,
  totalSubmissions: 0,
  pendingReviews: 0,
  approvedSubmissions: 0,
  rejectedSubmissions: 0,
  averageScore: 0,
  submissionsByActivity: {},
  submissionsByStatus: {
    [CaseSubmissionStatus.DRAFT]: 0,
    [CaseSubmissionStatus.SUBMITTED]: 0,
    [CaseSubmissionStatus.REVIEWING]: 0,
    [CaseSubmissionStatus.APPROVED]: 0,
    [CaseSubmissionStatus.REJECTED]: 0,
    [CaseSubmissionStatus.WITHDRAWN]: 0
  },
  reviewsByResult: {
    [CaseReviewResult.PASS]: 0,
    [CaseReviewResult.REJECT]: 0,
    [CaseReviewResult.REVISE]: 0
  },
  recentActivities: [],
  popularActivities: [],
  topSubmitters: []
});

const recentActivities = ref<CaseCollectionActivity[]>([]);
const popularActivities = ref<CaseCollectionActivity[]>([]);

// 数据验证和处理工具函数
const validateAndProcessStatistics = (data: any) => {
  return validateStatistics(data);
};

// 方法
const loadStatistics = async () => {
  await statsLoading.execute(async () => {
    const response = await caseCollectionService.getActivityStatistics();
    if (response.success && response.data) {
      const processedData = validateAndProcessStatistics(response.data);
      if (processedData) {
        stats.value = processedData;
        recentActivities.value = processedData.recentActivities || [];
        popularActivities.value = processedData.popularActivities || [];
        return processedData;
      } else {
        throw new Error('数据格式错误');
      }
    } else {
      throw new Error(response.message || '加载统计数据失败');
    }
  }, {
    showLoading: true,
    showError: true,
    loadingMessage: '正在加载统计数据...',
    errorMessage: '加载统计数据失败'
  });
};

// 导航方法
const goToActivityList = () => {
  router.push('/case-collection/activities/list');
};

const createActivity = () => {
  router.push('/case-collection/activities/create');
};

const goToSubmissions = () => {
  router.push('/case-collection/submissions');
};

const goToReviews = () => {
  router.push('/case-collection/review');
};

const goToCategories = () => {
  router.push('/case-collection/categories');
};

const goToDashboard = () => {
  router.push('/case-collection/dashboard');
};

// 活动操作方法
const viewActivity = (activity: CaseCollectionActivity) => {
  router.push(`/case-collection/activities/${activity.id}`);
};

const editActivity = (activity: CaseCollectionActivity) => {
  router.push(`/case-collection/activities/${activity.id}/edit`);
};

const deleteActivity = async (activity: CaseCollectionActivity) => {
  feedbackSystem.confirmDelete(
    activity.title,
    async () => {
      await executeWithLoading(async () => {
        const response = await caseCollectionService.deleteActivity(activity.id);
        if (response.success) {
          loadStatistics(); // 重新加载数据
          return response.data;
        } else {
          throw new Error(response.message || '删除活动失败');
        }
      }, {
        showLoading: true,
        showSuccess: true,
        showError: true,
        successMessage: '删除活动成功',
        errorMessage: '删除活动失败'
      });
    }
  );
};

// 生命周期
onMounted(() => {
  loadStatistics();
});
</script>

<style scoped lang="scss">
.case-collection-activities {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      margin-bottom: 24px;

      h1 {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 16px;
      }
    }

    .header-stats {
      .ant-statistic {
        text-align: center;
      }
    }
  }

  .quick-actions {
    margin-bottom: 24px;

    .action-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fafafa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      height: 100px;

      &:hover {
        background: #e6f7ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .action-icon {
        font-size: 32px;
        color: #1890ff;
        margin-right: 16px;
        flex-shrink: 0;
      }

      .action-content {
        flex: 1;

        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .recent-activities,
  .popular-activities {
    margin-bottom: 24px;

    .empty-state {
      text-align: center;
      padding: 40px 0;
    }
  }
}

@media (max-width: 768px) {
  .case-collection-activities {
    padding: 16px;

    .page-header {
      padding: 16px;

      .header-content h1 {
        font-size: 24px;
      }

      .header-stats {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }

    .quick-actions {
      .action-card {
        height: auto;
        min-height: 80px;
        padding: 16px;

        .action-icon {
          font-size: 24px;
          margin-right: 12px;
        }

        .action-content h3 {
          font-size: 14px;
        }

        .action-content p {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
