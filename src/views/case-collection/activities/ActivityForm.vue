<template>
  <div class="activity-form">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>{{ isEdit ? '编辑活动' : '创建活动' }}</h2>
        <p>{{ isEdit ? '修改案例征集活动信息' : '创建新的案例征集活动' }}</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button @click="goBack">
            <template #icon><arrow-left-outlined /></template>
            返回
          </a-button>
          <a-button @click="saveDraft" :loading="saving">
            <template #icon><save-outlined /></template>
            保存草稿
          </a-button>
          <a-button type="primary" @click="submitForm" :loading="submitting">
            <template #icon><check-outlined /></template>
            {{ isEdit ? '更新活动' : '创建活动' }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-content">
      <a-card :bordered="false">
        <a-steps :current="currentStep" class="form-steps">
          <a-step title="基本信息" description="活动基础信息设置" />
          <a-step title="详细配置" description="活动规则和要求配置" />
          <a-step title="奖项设置" description="奖项和奖金配置" />
          <a-step title="预览确认" description="预览并确认活动信息" />
        </a-steps>

        <div class="step-content">
          <!-- 步骤1：基本信息 -->
          <div v-show="currentStep === 0" class="step-panel">
            <a-form
              ref="basicFormRef"
              :model="formData"
              :rules="basicRules"
              layout="vertical"
            >
              <a-row :gutter="24">
                <a-col :span="24">
                  <a-form-item label="活动标题" name="title" required>
                    <a-input 
                      v-model:value="formData.title" 
                      placeholder="请输入活动标题"
                      show-count
                      :maxlength="100"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="活动主题" name="theme">
                    <a-input 
                      v-model:value="formData.theme" 
                      placeholder="请输入活动主题"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="优先级" name="priority">
                    <a-select v-model:value="formData.priority" placeholder="请选择优先级">
                      <a-select-option :value="1">低</a-select-option>
                      <a-select-option :value="2">普通</a-select-option>
                      <a-select-option :value="3">高</a-select-option>
                      <a-select-option :value="4">紧急</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动描述" name="description" required>
                    <a-textarea 
                      v-model:value="formData.description" 
                      placeholder="请输入活动描述"
                      :rows="4"
                      show-count
                      :maxlength="500"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动详细内容" name="content">
                    <a-textarea 
                      v-model:value="formData.content" 
                      placeholder="请输入活动详细内容"
                      :rows="6"
                      show-count
                      :maxlength="2000"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="活动开始时间" name="startTime" required>
                    <a-date-picker 
                      v-model:value="formData.startTime" 
                      show-time
                      placeholder="选择开始时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="活动结束时间" name="endTime" required>
                    <a-date-picker 
                      v-model:value="formData.endTime" 
                      show-time
                      placeholder="选择结束时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="8">
                  <a-form-item label="提交截止时间" name="submitDeadline" required>
                    <a-date-picker 
                      v-model:value="formData.submitDeadline" 
                      show-time
                      placeholder="选择截止时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="封面图片" name="coverImage">
                    <file-upload
                      v-model:file-list="coverImageList"
                      :multiple="false"
                      accept="image/*"
                      :max-size="5"
                      @change="handleCoverImageChange"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动图片" name="images">
                    <file-upload
                      v-model:file-list="imageList"
                      :multiple="true"
                      accept="image/*"
                      :max-count="5"
                      :max-size="5"
                      @change="handleImagesChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>

          <!-- 步骤2：详细配置 -->
          <div v-show="currentStep === 1" class="step-panel">
            <a-form
              ref="configFormRef"
              :model="formData"
              :rules="configRules"
              layout="vertical"
            >
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="最大提交数量" name="maxSubmissions">
                    <a-input-number 
                      v-model:value="formData.maxSubmissions" 
                      placeholder="请输入最大提交数量"
                      :min="1"
                      :max="10000"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="12">
                  <a-form-item label="最大文件大小(MB)" name="maxFileSize">
                    <a-input-number 
                      v-model:value="formData.maxFileSize" 
                      placeholder="请输入最大文件大小"
                      :min="1"
                      :max="100"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="允许的文件类型" name="allowedFileTypes">
                    <a-select 
                      v-model:value="formData.allowedFileTypes" 
                      mode="multiple"
                      placeholder="请选择允许的文件类型"
                    >
                      <a-select-option value="pdf">PDF</a-select-option>
                      <a-select-option value="doc">Word文档</a-select-option>
                      <a-select-option value="docx">Word文档(新版)</a-select-option>
                      <a-select-option value="xls">Excel表格</a-select-option>
                      <a-select-option value="xlsx">Excel表格(新版)</a-select-option>
                      <a-select-option value="ppt">PowerPoint</a-select-option>
                      <a-select-option value="pptx">PowerPoint(新版)</a-select-option>
                      <a-select-option value="jpg">JPEG图片</a-select-option>
                      <a-select-option value="png">PNG图片</a-select-option>
                      <a-select-option value="mp4">MP4视频</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动规则" name="rules">
                    <a-textarea 
                      v-model:value="formData.rules" 
                      placeholder="请输入活动规则"
                      :rows="6"
                      show-count
                      :maxlength="2000"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="参与要求" name="requirements">
                    <a-select 
                      v-model:value="formData.requirements" 
                      mode="tags"
                      placeholder="请输入参与要求，按回车添加"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动标签" name="tags">
                    <a-select 
                      v-model:value="formData.tags" 
                      mode="tags"
                      placeholder="请输入活动标签，按回车添加"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="联系信息" name="contactInfo">
                    <a-input 
                      v-model:value="formData.contactInfo" 
                      placeholder="请输入联系信息"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="活动附件" name="attachments">
                    <file-upload
                      v-model:file-list="attachmentList"
                      :multiple="true"
                      :max-count="10"
                      :max-size="20"
                      @change="handleAttachmentsChange"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item name="isPublic">
                    <a-checkbox v-model:checked="formData.isPublic">
                      公开活动（所有用户可见）
                    </a-checkbox>
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>

          <!-- 步骤3：奖项设置 -->
          <div v-show="currentStep === 2" class="step-panel">
            <a-form
              ref="awardFormRef"
              :model="formData"
              layout="vertical"
            >
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item label="总奖金(元)" name="totalPrize">
                    <a-input-number 
                      v-model:value="formData.totalPrize" 
                      placeholder="请输入总奖金"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="24">
                  <a-form-item label="奖项设置" name="awards">
                    <a-select 
                      v-model:value="formData.awards" 
                      mode="tags"
                      placeholder="请输入奖项设置，如：一等奖1名、二等奖3名"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>

          <!-- 步骤4：预览确认 -->
          <div v-show="currentStep === 3" class="step-panel">
            <activity-preview :activity="formData" />
          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="step-actions">
          <a-space>
            <a-button v-if="currentStep > 0" @click="prevStep">
              <template #icon><arrow-left-outlined /></template>
              上一步
            </a-button>
            <a-button v-if="currentStep < 3" type="primary" @click="nextStep">
              下一步
              <template #icon><arrow-right-outlined /></template>
            </a-button>
            <a-button v-if="currentStep === 3" type="primary" @click="submitForm" :loading="submitting">
              <template #icon><check-outlined /></template>
              {{ isEdit ? '更新活动' : '创建活动' }}
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  ArrowRightOutlined,
  SaveOutlined,
  CheckOutlined
} from '@ant-design/icons-vue';
import type { CaseCollectionActivity } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateActivity } from '@/utils/data-validation';
import { usePageLoading, generateDefaultBackground } from '@/utils/page-optimization';
import FileUpload from '@/components/file-upload/index.vue';
import ActivityPreview from './components/ActivityPreview.vue';

const router = useRouter();
const route = useRoute();

// 响应式数据
const currentStep = ref(0);

// 使用页面优化工具
const pageLoading = usePageLoading();

const isEdit = computed(() => !!route.params.id);
const activityId = computed(() => route.params.id as string);

// 表单引用
const basicFormRef = ref<FormInstance>();
const configFormRef = ref<FormInstance>();
const awardFormRef = ref<FormInstance>();

// 文件列表
const coverImageList = ref<any[]>([]);
const imageList = ref<any[]>([]);
const attachmentList = ref<any[]>([]);

// 表单数据
const formData = reactive<Partial<CaseCollectionActivity>>({
  title: '',
  description: '',
  content: '',
  theme: '',
  rules: '',
  startTime: undefined,
  endTime: undefined,
  submitDeadline: undefined,
  maxSubmissions: 100,
  allowedFileTypes: ['pdf', 'doc', 'docx'],
  maxFileSize: 10,
  awards: [],
  totalPrize: 0,
  contactInfo: '',
  requirements: [],
  tags: [],
  coverImage: '',
  images: [],
  attachments: [],
  status: 'draft',
  priority: 2,
  isPublic: true
});

// 表单验证规则
const basicRules = {
  title: [
    { required: true, message: '请输入活动标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度应在5-100个字符之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入活动描述', trigger: 'blur' },
    { min: 10, max: 500, message: '描述长度应在10-500个字符之间', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择活动开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择活动结束时间', trigger: 'change' }
  ],
  submitDeadline: [
    { required: true, message: '请选择提交截止时间', trigger: 'change' }
  ]
};

const configRules = {
  maxSubmissions: [
    { required: true, message: '请输入最大提交数量', trigger: 'blur' }
  ],
  maxFileSize: [
    { required: true, message: '请输入最大文件大小', trigger: 'blur' }
  ]
};

// 方法
const loadActivity = async () => {
  if (!isEdit.value) return;

  try {
    const response = await caseCollectionService.getActivityDetail(Number(activityId.value));
    if (response.success) {
      Object.assign(formData, response.data);
      // 处理文件列表
      if (response.data.coverImage) {
        coverImageList.value = [{ url: response.data.coverImage, name: 'cover.jpg' }];
      }
      if (response.data.images) {
        imageList.value = response.data.images.map((url: string, index: number) => ({
          url,
          name: `image_${index + 1}.jpg`
        }));
      }
      if (response.data.attachments) {
        attachmentList.value = JSON.parse(response.data.attachments);
      }
    } else {
      message.error(response.message || '加载活动信息失败');
    }
  } catch (error) {
    console.error('加载活动信息失败:', error);
    message.error('加载活动信息失败');
  }
};

const nextStep = async () => {
  try {
    if (currentStep.value === 0) {
      await basicFormRef.value?.validate();
    } else if (currentStep.value === 1) {
      await configFormRef.value?.validate();
    }
    currentStep.value++;
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const prevStep = () => {
  currentStep.value--;
};

const saveDraft = async () => {
  try {
    saving.value = true;
    formData.status = 'draft';
    
    if (isEdit.value) {
      const response = await caseCollectionService.updateActivity(Number(activityId.value), formData);
      if (response.success) {
        message.success('保存草稿成功');
      } else {
        message.error(response.message || '保存草稿失败');
      }
    } else {
      const response = await caseCollectionService.createActivity(formData);
      if (response.success) {
        message.success('保存草稿成功');
        router.replace(`/case-collection/activities/${response.data.id}/edit`);
      } else {
        message.error(response.message || '保存草稿失败');
      }
    }
  } catch (error) {
    console.error('保存草稿失败:', error);
    message.error('保存草稿失败');
  } finally {
    saving.value = false;
  }
};

const submitForm = async () => {
  try {
    submitting.value = true;
    
    // 验证所有表单
    await basicFormRef.value?.validate();
    await configFormRef.value?.validate();
    
    formData.status = 'published';
    
    if (isEdit.value) {
      const response = await caseCollectionService.updateActivity(Number(activityId.value), formData);
      if (response.success) {
        message.success('更新活动成功');
        router.push('/case-collection/activities');
      } else {
        message.error(response.message || '更新活动失败');
      }
    } else {
      const response = await caseCollectionService.createActivity(formData);
      if (response.success) {
        message.success('创建活动成功');
        router.push('/case-collection/activities');
      } else {
        message.error(response.message || '创建活动失败');
      }
    }
  } catch (error) {
    console.error('提交表单失败:', error);
    // message.error('提交表单失败');
  } finally {
    submitting.value = false;
  }
};

const goBack = () => {
  router.back();
};

// 文件上传处理
const handleCoverImageChange = (fileList: any[]) => {
  if (fileList.length > 0) {
    formData.coverImage = fileList[0].url || fileList[0].response?.url;
  } else {
    formData.coverImage = '';
  }
};

const handleImagesChange = (fileList: any[]) => {
  formData.images = fileList.map(file => file.url || file.response?.url).filter(Boolean);
};

const handleAttachmentsChange = (fileList: any[]) => {
  formData.attachments = JSON.stringify(fileList.map(file => ({
    name: file.name,
    url: file.url || file.response?.url,
    size: file.size,
    type: file.type
  })));
};

// 生命周期
onMounted(() => {
  if (isEdit.value) {
    loadActivity();
  }
});
</script>

<style scoped lang="scss">
.activity-form {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .form-content {
    .form-steps {
      margin-bottom: 32px;
    }

    .step-content {
      min-height: 400px;
      margin-bottom: 32px;

      .step-panel {
        animation: fadeIn 0.3s ease-in-out;
      }
    }

    .step-actions {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .activity-form {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;
      }
    }

    .form-content {
      .form-steps {
        :deep(.ant-steps-item-title) {
          font-size: 12px;
        }

        :deep(.ant-steps-item-description) {
          display: none;
        }
      }
    }
  }
}
</style>
