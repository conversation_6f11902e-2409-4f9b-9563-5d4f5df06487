<template>
  <div class="submission-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <a-button @click="goBack" class="back-btn">
          <template #icon><arrow-left-outlined /></template>
          返回
        </a-button>
        <div class="header-info">
          <h1>案例详情</h1>
          <p v-if="submission">{{ submission.title }}</p>
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="downloadCase">
            <template #icon><download-outlined /></template>
            下载案例
          </a-button>
          <a-button @click="editSubmission" v-if="canEdit">
            <template #icon><edit-outlined /></template>
            编辑案例
          </a-button>
          <a-button @click="withdrawSubmission" v-if="canWithdraw" type="primary" danger>
            <template #icon><rollback-outlined /></template>
            撤回提交
          </a-button>
        </a-space>
      </div>
    </div>

    <a-spin :spinning="pageLoading">
      <div class="detail-content">
        <a-row :gutter="24">
          <!-- 左侧：案例内容 -->
          <a-col :xs="24" :lg="16">
            <!-- 案例基本信息 -->
            <a-card title="案例信息" :bordered="false" class="case-info-card">
              <a-descriptions :column="2" bordered>
                <a-descriptions-item label="案例标题" :span="2">
                  {{ submission?.title }}
                </a-descriptions-item>
                <a-descriptions-item label="案例摘要" :span="2">
                  {{ submission?.summary }}
                </a-descriptions-item>
                <a-descriptions-item label="征集活动">
                  {{ submission?.activityTitle }}
                </a-descriptions-item>
                <a-descriptions-item label="案例分类">
                  {{ getCategoryName() }}
                </a-descriptions-item>
                <a-descriptions-item label="提交者">
                  {{ submission?.submitterName }}
                </a-descriptions-item>
                <a-descriptions-item label="所属部门">
                  {{ submission?.submitterDepartment }}
                </a-descriptions-item>
                <a-descriptions-item label="联系方式">
                  {{ submission?.submitterContact }}
                </a-descriptions-item>
                <a-descriptions-item label="邮箱地址">
                  {{ submission?.submitterEmail }}
                </a-descriptions-item>
                <a-descriptions-item label="提交时间" :span="2">
                  {{ formatDate(submission?.submitTime || submission?.createTime) }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>

            <!-- 案例内容 -->
            <a-card title="案例详细内容" :bordered="false" class="case-content-card">
              <div class="content-display">
                {{ submission?.content }}
              </div>
            </a-card>

            <!-- 案例标签 -->
            <a-card v-if="submission?.tags?.length" title="案例标签" :bordered="false">
              <a-space wrap>
                <a-tag v-for="tag in submission.tags" :key="tag" color="blue">
                  {{ tag }}
                </a-tag>
              </a-space>
            </a-card>

            <!-- 封面图片 -->
            <a-card title="封面图片" :bordered="false">
              <div class="cover-display">
                <div
                  class="image-placeholder cover"
                  :style="generateDefaultBackground(submission?.activityTitle || '默认', 'card')"
                  @click="handleCoverClick"
                >
                  <div class="placeholder-content">
                    <file-outlined style="font-size: 48px; margin-bottom: 12px;" />
                    <div class="placeholder-title">{{ submission?.title || '案例封面' }}</div>
                    <div class="placeholder-subtitle">{{ submission?.activityTitle || '案例征集活动' }}</div>
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 案例图片 -->
            <a-card title="案例图片" :bordered="false">
              <div class="case-images">
                <div v-for="index in (submission?.images?.length || 3)" :key="index" class="image-item">
                  <div
                    class="image-placeholder small"
                    :style="generateDefaultBackground(submission?.activityTitle || '默认', 'placeholder')"
                    @click="handleImageClick(index)"
                  >
                    <div class="placeholder-content">
                      <file-outlined style="font-size: 24px; margin-bottom: 8px;" />
                      <div>案例图片 {{ index }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 支撑文档 -->
            <a-card v-if="getAttachments().length" title="支撑文档" :bordered="false">
              <div class="attachments-list">
                <div v-for="attachment in getAttachments()" :key="attachment.name" class="attachment-item">
                  <div class="attachment-info">
                    <file-outlined />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                  </div>
                  <a-button type="link" size="small" @click="downloadAttachment(attachment)">
                    <template #icon><download-outlined /></template>
                    下载
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>

          <!-- 右侧：状态信息 -->
          <a-col :xs="24" :lg="8">
            <!-- 提交状态 -->
            <a-card title="提交状态" :bordered="false" class="status-card">
              <div class="status-info">
                <div class="status-item">
                  <span class="status-label">当前状态：</span>
                  <a-tag :color="getStatusColor(submission?.status)">
                    {{ getStatusText(submission?.status) }}
                  </a-tag>
                </div>
                <div class="status-item" v-if="submission?.reviewScore">
                  <span class="status-label">审核评分：</span>
                  <span class="status-value">{{ submission.reviewScore }}分</span>
                </div>
                <div class="status-item" v-if="submission?.reviewerName">
                  <span class="status-label">审核员：</span>
                  <span class="status-value">{{ submission.reviewerName }}</span>
                </div>
                <div class="status-item" v-if="submission?.reviewTime">
                  <span class="status-label">审核时间：</span>
                  <span class="status-value">{{ formatDate(submission.reviewTime) }}</span>
                </div>
              </div>
            </a-card>

            <!-- 审核意见 -->
            <a-card v-if="submission?.reviewComments" title="审核意见" :bordered="false">
              <div class="review-comments">
                <p><strong>审核意见：</strong></p>
                <div class="comments-content">{{ submission.reviewComments }}</div>
                <div v-if="submission.reviewSuggestions" class="suggestions-content">
                  <p><strong>修改建议：</strong></p>
                  <div>{{ submission.reviewSuggestions }}</div>
                </div>
              </div>
            </a-card>

            <!-- 操作历史 -->
            <a-card title="操作历史" :bordered="false">
              <a-timeline>
                <a-timeline-item color="blue">
                  <div class="timeline-item">
                    <div class="timeline-title">案例创建</div>
                    <div class="timeline-time">{{ formatDate(submission?.createTime) }}</div>
                  </div>
                </a-timeline-item>
                <a-timeline-item v-if="submission?.submitTime" color="green">
                  <div class="timeline-item">
                    <div class="timeline-title">提交审核</div>
                    <div class="timeline-time">{{ formatDate(submission.submitTime) }}</div>
                  </div>
                </a-timeline-item>
                <a-timeline-item v-if="submission?.reviewTime" :color="getReviewTimelineColor()">
                  <div class="timeline-item">
                    <div class="timeline-title">{{ getReviewTimelineTitle() }}</div>
                    <div class="timeline-time">{{ formatDate(submission.reviewTime) }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-spin>

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:visible="imagePreviewVisible"
      :footer="null"
      :width="800"
      centered
    >
      <img :src="previewImageUrl" style="width: 100%" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import {
  ArrowLeftOutlined,
  DownloadOutlined,
  EditOutlined,
  RollbackOutlined,
  FileOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateSubmission } from '@/utils/data-validation';
import { usePageLoading, generateDefaultBackground } from '@/utils/page-optimization';
import { formatDate } from '@/utils/date';

const router = useRouter();
const route = useRoute();

// 响应式数据
const submission = ref<CaseSubmission>();
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

// 使用页面优化工具
const pageLoading = usePageLoading();

const submissionId = computed(() => Number(route.params.id));
const canEdit = computed(() => 
  submission.value && ['draft', 'rejected'].includes(submission.value.status)
);
const canWithdraw = computed(() => 
  submission.value && ['submitted', 'reviewing'].includes(submission.value.status)
);

// 方法
const loadSubmission = async () => {
  await pageLoading.execute(async () => {
    const response = await caseCollectionService.getSubmissionDetail(submissionId.value);

    if (response.success && response.data) {
      submission.value = validateSubmission(response.data);
    } else {
      throw new Error(response.message || '加载案例详情失败');
    }
  });
};

const getAttachments = () => {
  if (!submission.value?.attachments) return [];
  try {
    return JSON.parse(submission.value.attachments);
  } catch {
    return [];
  }
};

const getCategoryName = () => {
  // 这里应该根据categoryId获取分类名称
  return submission.value?.categoryId ? `分类${submission.value.categoryId}` : '无';
};

// 工具方法
const getStatusColor = (status?: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'submitted': 'blue',
    'reviewing': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'withdrawn': 'default'
  };
  return statusColors[status || ''] || 'default';
};

const getStatusText = (status?: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'withdrawn': '已撤回'
  };
  return statusTexts[status || ''] || '未知';
};

const getReviewTimelineColor = () => {
  if (!submission.value?.status) return 'default';
  return submission.value.status === 'approved' ? 'green' : 'red';
};

const getReviewTimelineTitle = () => {
  if (!submission.value?.status) return '审核完成';
  return submission.value.status === 'approved' ? '审核通过' : '审核驳回';
};

const formatFileSize = (size?: number) => {
  if (!size) return '0B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  let fileSize = size;
  
  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }
  
  return `${fileSize.toFixed(1)}${units[index]}`;
};

// 事件处理
const goBack = () => {
  router.back();
};

const downloadCase = () => {
  message.info('下载功能开发中...');
};

const editSubmission = () => {
  router.push(`/case-collection/submit/form?submissionId=${submissionId.value}`);
};

const withdrawSubmission = () => {
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回案例"${submission.value?.title}"吗？撤回后可以重新编辑和提交。`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const response = await caseCollectionService.withdrawSubmission(submissionId.value);
        if (response.success) {
          message.success('撤回案例成功');
          loadSubmission();
        } else {
          message.error(response.message || '撤回案例失败');
        }
      } catch (error) {
        console.error('撤回案例失败:', error);
        message.error('撤回案例失败');
      }
    }
  });
};

const handleCoverClick = () => {
  message.info('这是案例封面的纯色背景占位符');
};

const handleImageClick = (index: number) => {
  message.info(`这是案例图片 ${index} 的纯色背景占位符`);
};

const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl;
  imagePreviewVisible.value = true;
};

const downloadAttachment = (attachment: any) => {
  const link = document.createElement('a');
  link.href = attachment.url;
  link.download = attachment.name;
  link.click();
};

// 生命周期
onMounted(() => {
  loadSubmission();
});
</script>

<style scoped lang="scss">
.submission-detail {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      display: flex;
      align-items: center;
      gap: 16px;

      .back-btn {
        flex-shrink: 0;
      }

      .header-info {
        h1 {
          margin: 0 0 4px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }

  .detail-content {
    .case-info-card,
    .case-content-card {
      margin-bottom: 24px;
    }

    .content-display {
      padding: 16px;
      background: #fafafa;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      white-space: pre-wrap;
      line-height: 1.6;
      min-height: 200px;
    }

    .cover-display {
      .image-placeholder {
        &.cover {
          width: 100%;
          height: 300px;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: scale(1.02);
          }

          .placeholder-content {
            text-align: center;

            .placeholder-title {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 4px;
            }

            .placeholder-subtitle {
              font-size: 14px;
              opacity: 0.8;
            }
          }
        }
      }

      img {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .case-images {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 16px;

      .image-item {
        .image-placeholder {
          &.small {
            width: 100%;
            height: 150px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;

            &:hover {
              transform: scale(1.05);
            }

            .placeholder-content {
              text-align: center;
              font-size: 14px;
              font-weight: 500;
            }
          }
        }

        img {
          width: 100%;
          height: 150px;
          object-fit: cover;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }

    .attachments-list {
      .attachment-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .attachment-info {
          display: flex;
          align-items: center;
          flex: 1;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }

          .attachment-name {
            font-weight: 500;
            margin-right: 8px;
          }

          .attachment-size {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
    }

    .status-card {
      margin-bottom: 24px;

      .status-info {
        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .status-label {
            color: #8c8c8c;
            font-size: 14px;
          }

          .status-value {
            font-weight: 500;
            color: #262626;
          }
        }
      }
    }

    .review-comments {
      .comments-content,
      .suggestions-content {
        padding: 12px;
        background: #fafafa;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        margin-top: 8px;
        line-height: 1.6;
      }

      .suggestions-content {
        margin-top: 16px;
      }
    }

    .timeline-item {
      .timeline-title {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }

      .timeline-time {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
}

@media (max-width: 768px) {
  .submission-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-content {
        width: 100%;

        .header-info h1 {
          font-size: 20px;
        }
      }

      .header-actions {
        width: 100%;
      }
    }

    .detail-content {
      .case-images {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 12px;

        .image-item img {
          height: 120px;
        }
      }

      .attachments-list {
        .attachment-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .attachment-info {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
