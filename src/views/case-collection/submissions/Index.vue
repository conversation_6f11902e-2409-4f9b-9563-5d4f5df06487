<template>
  <div class="submissions-index">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2>我的案例提交</h2>
        <p>查看和管理您提交的所有案例</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="batchDelete" :disabled="!selectedRowKeys.length">
            <template #icon><delete-outlined /></template>
            批量删除
          </a-button>
          <a-button type="primary" @click="goToSubmit">
            <template #icon><plus-outlined /></template>
            提交案例
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="全部提交"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="审核中"
              :value="stats.reviewing"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已通过"
              :value="stats.approved"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="草稿"
              :value="stats.draft"
              :value-style="{ color: '#8c8c8c' }"
            >
              <template #prefix>
                <edit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-filters">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch">
          <a-form-item label="案例标题" name="keyword">
            <a-input 
              v-model:value="searchForm.keyword" 
              placeholder="请输入案例标题或关键词"
              allow-clear
              style="width: 200px"
            />
          </a-form-item>
          
          <a-form-item label="提交状态" name="status">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
              style="width: 120px"
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="submitted">已提交</a-select-option>
              <a-select-option value="reviewing">审核中</a-select-option>
              <a-select-option value="approved">已通过</a-select-option>
              <a-select-option value="rejected">已驳回</a-select-option>
              <a-select-option value="withdrawn">已撤回</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="征集活动" name="activityId">
            <a-select 
              v-model:value="searchForm.activityId" 
              placeholder="请选择活动"
              allow-clear
              style="width: 200px"
            >
              <a-select-option 
                v-for="activity in activities" 
                :key="activity.id" 
                :value="activity.id"
              >
                {{ activity.title }}
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="提交时间" name="dateRange">
            <a-range-picker 
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">
                <template #icon><search-outlined /></template>
                搜索
              </a-button>
              <a-button @click="resetSearch">
                <template #icon><clear-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 案例列表 -->
    <div class="submissions-list">
      <a-card :bordered="false">
        <a-table
          :columns="columns"
          :data-source="submissions"
          :pagination="pagination"
          :row-selection="rowSelection"
          :scroll="{ x: 1200 }"
          @change="handleTableChange"
        >
          <!-- 案例标题 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <div class="submission-title-cell">
                <div class="title-content">
                  <a @click="viewSubmission(record)" class="submission-link">
                    {{ record.title }}
                  </a>
                  <div class="submission-summary" v-if="record.summary">
                    {{ truncateText(record.summary, 100) }}
                  </div>
                </div>
               <!--  <div class="submission-cover" v-if="record.coverImage">
                  <img :src="record.coverImage" :alt="record.title" />
                </div> -->
              </div>
            </template>

            <!-- 活动信息 -->
            <template v-else-if="column.key === 'activity'">
              <div class="activity-info">
                <div class="activity-title">{{ record.activityTitle }}</div>
                <div class="activity-meta">
                  <clock-circle-outlined />
                  <span>{{ formatDate(record.submitTime || record.createTime) }}</span>
                </div>
              </div>
            </template>

            <!-- 提交状态 -->
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
              <div v-if="record.reviewScore" class="review-score">
                评分：{{ record.reviewScore }}
              </div>
            </template>

            <!-- 审核信息 -->
            <template v-else-if="column.key === 'review'">
              <div v-if="record.reviewerName" class="review-info">
                <div>审核员：{{ record.reviewerName }}</div>
                <div v-if="record.reviewTime">时间：{{ formatDate(record.reviewTime) }}</div>
                <div v-if="record.reviewComments" class="review-comments">
                  {{ truncateText(record.reviewComments, 50) }}
                </div>
              </div>
              <div v-else class="no-review">
                <span>暂未审核</span>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-tooltip title="查看详情">
                  <a-button type="link" size="small" @click="viewSubmission(record)">
                    <template #icon><eye-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-tooltip title="编辑案例" v-if="canEdit(record)">
                  <a-button type="link" size="small" @click="editSubmission(record)">
                    <template #icon><edit-outlined /></template>
                  </a-button>
                </a-tooltip>

                <a-dropdown>
                  <a-button type="link" size="small">
                    <template #icon><more-outlined /></template>
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="downloadSubmission(record)">
                        <download-outlined />
                        下载案例
                      </a-menu-item>
                      <a-menu-item @click="copySubmission(record)">
                        <copy-outlined />
                        复制案例
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        @click="withdrawSubmission(record)"
                        v-if="canWithdraw(record)"
                      >
                        <rollback-outlined />
                        撤回提交
                      </a-menu-item>
                      <a-menu-item
                        @click="deleteSubmission(record)"
                        class="danger-item"
                        v-if="canDelete(record)"
                      >
                        <delete-outlined />
                        删除案例
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import {
  ReloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  EditOutlined,
  SearchOutlined,
  ClearOutlined,
  EyeOutlined,
  MoreOutlined,
  DownloadOutlined,
  CopyOutlined,
  RollbackOutlined
} from '@ant-design/icons-vue';
import type { CaseSubmission, CaseCollectionActivity, CaseSubmissionQueryParams } from '@/types/case-collection';
import { caseCollectionService } from '@/api/case-collection-service';
import { validateSubmission, validateActivity } from '@/utils/data-validation';
import { usePageLoading, PageDataProcessor } from '@/utils/page-optimization';
import { formatDate } from '@/utils/date';

const router = useRouter();

// 响应式数据
const submissions = ref<CaseSubmission[]>([]);
const activities = ref<CaseCollectionActivity[]>([]);
const selectedRowKeys = ref<number[]>([]);

// 使用页面优化工具
const pageLoading = usePageLoading();

const stats = reactive({
  total: 0,
  reviewing: 0,
  approved: 0,
  draft: 0
});

const searchForm = reactive({
  keyword: '',
  status: undefined,
  activityId: undefined,
  dateRange: undefined
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
});

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '案例标题',
    dataIndex: 'title',
    key: 'title',
    width: 300
  },
  {
    title: '征集活动',
    key: 'activity',
    width: 200
  },
  {
    title: '提交状态',
    dataIndex: 'status',
    key: 'status',
    width: 120
  },
  {
    title: '审核信息',
    key: 'review',
    width: 200
  },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    key: 'submitTime',
    width: 150,
    customRender: ({ text, record }) => formatDate(text || record.createTime)
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  }
}));

// 方法
const loadSubmissions = async () => {
  await pageLoading.execute(async () => {
    const params: CaseSubmissionQueryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status,
      activityId: searchForm.activityId
    };

    if (searchForm.dateRange && Array.isArray(searchForm.dateRange) && searchForm.dateRange.length === 2) {
      (params as any).startTime = searchForm.dateRange[0]?.format?.('YYYY-MM-DD');
      (params as any).endTime = searchForm.dateRange[1]?.format?.('YYYY-MM-DD');
    }

    const response = await caseCollectionService.getSubmissionList(params);

    if (response.success && response.data) {
      const paginationData = PageDataProcessor.processPaginationData(response, validateSubmission);
      submissions.value = paginationData.list;
      pagination.total = paginationData.total;
    } else {
      throw new Error(response.message || '加载案例列表失败');
    }
  });
};

const loadActivities = async () => {
  try {
    const response = await caseCollectionService.getActivityList({
      page: 1,
      pageSize: 100
    });
    
    if (response.success) {
      activities.value = response.data.list;
    }
  } catch (error) {
    console.error('加载活动列表失败:', error);
  }
};

const loadStats = async () => {
  try {
    statsLoading.value = true;
    // 这里应该调用统计API，暂时使用模拟数据
    stats.total = submissions.value.length;
    stats.reviewing = submissions.value.filter(s => s.status === 'reviewing').length;
    stats.approved = submissions.value.filter(s => s.status === 'approved').length;
    stats.draft = submissions.value.filter(s => s.status === 'draft').length;
  } catch (error) {
    console.error('加载统计数据失败:', error);
  } finally {
    statsLoading.value = false;
  }
};

// 工具方法
const truncateText = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const getStatusColor = (status: string) => {
  const statusColors: Record<string, string> = {
    'draft': 'default',
    'submitted': 'blue',
    'reviewing': 'orange',
    'approved': 'green',
    'rejected': 'red',
    'withdrawn': 'default'
  };
  return statusColors[status] || 'default';
};

const getStatusText = (status: string) => {
  const statusTexts: Record<string, string> = {
    'draft': '草稿',
    'submitted': '已提交',
    'reviewing': '审核中',
    'approved': '已通过',
    'rejected': '已驳回',
    'withdrawn': '已撤回'
  };
  return statusTexts[status] || '未知';
};

const canEdit = (submission: CaseSubmission) => {
  return ['draft', 'rejected'].includes(submission.status);
};

const canWithdraw = (submission: CaseSubmission) => {
  return ['submitted', 'reviewing'].includes(submission.status);
};

const canDelete = (submission: CaseSubmission) => {
  return ['draft', 'rejected', 'withdrawn'].includes(submission.status);
};

// 事件处理
const handleSearch = () => {
  pagination.current = 1;
  loadSubmissions();
};

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    activityId: undefined,
    dateRange: undefined
  });
  pagination.current = 1;
  loadSubmissions();
};

const handleTableChange: TableProps['onChange'] = (pag) => {
  pagination.current = pag.current || 1;
  pagination.pageSize = pag.pageSize || 10;
  loadSubmissions();
};

const refreshData = () => {
  loadSubmissions();
  loadStats();
};

// 导航方法
const goToSubmit = () => {
  router.push('/case-collection/submit');
};

const viewSubmission = (submission: CaseSubmission) => {
  router.push(`/case-collection/submissions/${submission.id}`);
};

const editSubmission = (submission: CaseSubmission) => {
  router.push(`/case-collection/submit/form?submissionId=${submission.id}`);
};

// 操作方法
const deleteSubmission = (submission: CaseSubmission) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除案例"${submission.title}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const response = await caseCollectionService.deleteSubmission(submission.id);
        if (response.success) {
          message.success('删除案例成功');
          refreshData();
        } else {
          message.error(response.message || '删除案例失败');
        }
      } catch (error) {
        console.error('删除案例失败:', error);
        message.error('删除案例失败');
      }
    }
  });
};

const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的案例');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个案例吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        // 这里应该调用批量删除API
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        refreshData();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

const withdrawSubmission = (submission: CaseSubmission) => {
  Modal.confirm({
    title: '确认撤回',
    content: `确定要撤回案例"${submission.title}"吗？撤回后可以重新编辑和提交。`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        const response = await caseCollectionService.withdrawSubmission(submission.id);
        if (response.success) {
          message.success('撤回案例成功');
          refreshData();
        } else {
          message.error(response.message || '撤回案例失败');
        }
      } catch (error) {
        console.error('撤回案例失败:', error);
        message.error('撤回案例失败');
      }
    }
  });
};

const copySubmission = (submission: CaseSubmission) => {
  // 复制案例逻辑
  message.info('复制功能开发中...');
};

const downloadSubmission = (submission: CaseSubmission) => {
  // 下载案例逻辑
  message.info('下载功能开发中...');
};

// 生命周期
onMounted(() => {
  loadSubmissions();
  loadActivities();
  loadStats();
});
</script>

<style scoped lang="scss">
.submissions-index {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-content {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .stats-section {
    margin-bottom: 24px;

    .ant-statistic {
      text-align: center;
    }
  }

  .search-filters {
    margin-bottom: 24px;

    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .submissions-list {
    .submission-title-cell {
      display: flex;
      align-items: center;
      gap: 12px;

      .title-content {
        flex: 1;

        .submission-link {
          font-weight: 500;
          color: #1890ff;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }

        .submission-summary {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 4px;
          line-height: 1.4;
        }
      }

      .submission-cover {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .activity-info {
      .activity-title {
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
      }

      .activity-meta {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #8c8c8c;

        .anticon {
          color: #1890ff;
        }
      }
    }

    .review-score {
      font-size: 12px;
      color: #fa8c16;
      margin-top: 4px;
    }

    .review-info {
      font-size: 12px;
      line-height: 1.4;

      div {
        margin-bottom: 2px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .review-comments {
        color: #8c8c8c;
      }
    }

    .no-review {
      font-size: 12px;
      color: #8c8c8c;
    }

    .danger-item {
      color: #ff4d4f;

      &:hover {
        background-color: #fff2f0;
      }
    }
  }
}

@media (max-width: 768px) {
  .submissions-index {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      padding: 16px;

      .header-actions {
        width: 100%;
        overflow-x: auto;
      }
    }

    .search-filters {
      .ant-form {
        .ant-form-item {
          width: 100%;
          margin-bottom: 12px;

          .ant-form-item-control-input {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
