<template>
  <div class="dynamics-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>创建动态管理</h2>
        <p>全媒体支持的动态内容创作与管理平台，支持个人动态和机关单位动态管理</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-card class="content-card" :bordered="false">
        <!-- Tab切换 -->
        <a-tabs v-model:activeKey="activeTab" type="card" @change="handleTabChange">
          <a-tab-pane key="personal" tab="个人动态">
            <div class="tab-content">
              <PersonalDynamics />
            </div>
          </a-tab-pane>
          <a-tab-pane key="organization" tab="机关单位动态">
            <div class="tab-content">
              <OrganizationDynamics />
            </div>
          </a-tab-pane>
          <a-tab-pane key="logs" tab="操作日志">
            <div class="tab-content">
              <OperationLogs />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PersonalDynamics from './components/PersonalDynamics.vue'
import OrganizationDynamics from './components/OrganizationDynamics.vue'
import OperationLogs from './components/OperationLogs.vue'

// 响应式数据
const activeTab = ref('personal')

// 事件处理函数
const handleTabChange = (key: string) => {
  console.log('切换到标签页:', key)
}
</script>

<style scoped>
.dynamics-container {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px - 70px);
}

.page-header {
  margin-bottom: 24px;
}

.header-title h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-card {
  border-radius: 8px;
}

.content-card :deep(.ant-card-body) {
  padding: 0;
}

.content-card :deep(.ant-tabs-card .ant-tabs-content) {
  padding: 24px;
  background: white;
}

.content-card :deep(.ant-tabs-card .ant-tabs-tabpane) {
  background: white;
}

.tab-content {
  min-height: 600px;
}

/* Tab样式优化 */
.content-card :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  background: #fafafa;
  border-color: #d9d9d9;
  color: #666;
  font-weight: 500;
}

.content-card :deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background: white;
  border-bottom-color: white;
  color: #1890ff;
  font-weight: 600;
}

.content-card :deep(.ant-tabs-card > .ant-tabs-nav) {
  margin: 0;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
}
</style>
