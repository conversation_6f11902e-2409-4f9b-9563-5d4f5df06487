<template>
  <div class="organization-dynamics">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            发布机关动态
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="showBatchModal">
            <template #icon><upload-outlined /></template>
            批量导入
          </a-button>
        </a-space>
      </div>
      <div class="action-right">
        <a-space>
          <a-select v-model:value="viewMode" style="width: 120px" @change="handleViewModeChange">
            <a-select-option value="list">列表视图</a-select-option>
            <a-select-option value="card">卡片视图</a-select-option>
            <a-select-option value="timeline">时间线视图</a-select-option>
          </a-select>
        </a-space>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-area">
      <a-card size="small">
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="发布单位">
            <a-select
              v-model:value="filterForm.organization"
              placeholder="请选择单位"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="org1">组织部</a-select-option>
              <a-select-option value="org2">宣传部</a-select-option>
              <a-select-option value="org3">人事部</a-select-option>
              <a-select-option value="org4">办公室</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="动态类型">
            <a-select
              v-model:value="filterForm.type"
              placeholder="请选择类型"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="notice">通知公告</a-select-option>
              <a-select-option value="activity">活动动态</a-select-option>
              <a-select-option value="meeting">会议纪要</a-select-option>
              <a-select-option value="news">新闻资讯</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="发布状态">
            <a-select
              v-model:value="filterForm.status"
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="published">已发布</a-select-option>
              <a-select-option value="archived">已归档</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="重要程度">
            <a-select
              v-model:value="filterForm.priority"
              placeholder="请选择重要程度"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="high">重要</a-select-option>
              <a-select-option value="medium">一般</a-select-option>
              <a-select-option value="low">普通</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="发布时间">
            <a-range-picker
              v-model:value="filterForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleFilter">筛选</a-button>
              <a-button @click="resetFilter">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总动态数"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><file-text-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="本月发布"
              :value="statistics.thisMonth"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><calendar-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="待审核"
              :value="statistics.pending"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix><clock-circle-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总浏览量"
              :value="statistics.totalViews"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><eye-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="list-view">
      <a-table
        :columns="columns"
        :data-source="dynamicsList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <div class="title-cell">
              <a @click="viewDetail(record)">{{ record.title }}</a>
              <a-tag v-if="record.isTop" color="red" size="small">置顶</a-tag>
              <a-tag v-if="record.isUrgent" color="orange" size="small">紧急</a-tag>
            </div>
          </template>

          <template v-else-if="column.key === 'organization'">
            <a-tag color="blue">{{ record.organization }}</a-tag>
          </template>

          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'priority'">
            <a-tag :color="getPriorityColor(record.priority)">
              {{ getPriorityText(record.priority) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'views'">
            <span class="views-count">
              <eye-outlined /> {{ record.views }}
            </span>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">查看</a-button>
              <a-button type="link" size="small" @click="editDynamic(record)">编辑</a-button>
              <a-button
                type="link"
                size="small"
                :disabled="record.status === 'published'"
                @click="publishDynamic(record)"
              >
                发布
              </a-button>
              <a-dropdown>
                <a-button type="link" size="small">
                  更多 <down-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuAction(key, record)">
                    <a-menu-item key="top" :disabled="record.isTop">置顶</a-menu-item>
                    <a-menu-item key="untop" :disabled="!record.isTop">取消置顶</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="archive">归档</a-menu-item>
                    <a-menu-item key="export">导出</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" danger>删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 卡片视图 -->
    <div v-if="viewMode === 'card'" class="card-view">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="item in dynamicsList" :key="item.id">
          <a-card hoverable class="dynamic-card">
            <template #title>
              <div class="card-title">
                <span>{{ item.title }}</span>
                <div class="title-tags">
                  <a-tag v-if="item.isTop" color="red" size="small">置顶</a-tag>
                  <a-tag v-if="item.isUrgent" color="orange" size="small">紧急</a-tag>
                </div>
              </div>
            </template>
            
            <template #extra>
              <a-dropdown>
                <a-button type="text" size="small">
                  <more-outlined />
                </a-button>
                <template #overlay>
                  <a-menu @click="({ key }) => handleMenuAction(key, item)">
                    <a-menu-item @click="viewDetail(item)">查看详情</a-menu-item>
                    <a-menu-item @click="editDynamic(item)">编辑</a-menu-item>
                    <a-menu-item @click="publishDynamic(item)" :disabled="item.status === 'published'">发布</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item key="delete" @click="deleteDynamic(item.id)" danger>删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>

            <div class="card-content">
              <div class="card-meta">
                <a-space>
                  <a-tag color="blue">{{ item.organization }}</a-tag>
                  <a-tag :color="getTypeColor(item.type)">{{ getTypeText(item.type) }}</a-tag>
                  <a-tag :color="getPriorityColor(item.priority)">{{ getPriorityText(item.priority) }}</a-tag>
                </a-space>
              </div>
              
              <p class="content-preview">{{ item.content }}</p>
              
              <div class="card-footer">
                <div class="footer-left">
                  <a-space>
                    <span><clock-circle-outlined /> {{ item.publishTime }}</span>
                    <span><eye-outlined /> {{ item.views }}</span>
                  </a-space>
                </div>
                <div class="footer-right">
                  <a-tag :color="getStatusColor(item.status)">
                    {{ getStatusText(item.status) }}
                  </a-tag>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 时间线视图 -->
    <div v-if="viewMode === 'timeline'" class="timeline-view">
      <a-timeline mode="left">
        <a-timeline-item 
          v-for="item in dynamicsList" 
          :key="item.id"
          :color="getTimelineColor(item.priority)"
        >
          <template #label>
            <span class="timeline-date">{{ item.publishTime }}</span>
          </template>
          <template #dot>
            <component :is="getTimelineIcon(item.type)" />
          </template>
          <div class="timeline-content">
            <div class="timeline-header">
              <h4>{{ item.title }}</h4>
              <div class="timeline-tags">
                <a-tag color="blue">{{ item.organization }}</a-tag>
                <a-tag :color="getTypeColor(item.type)">{{ getTypeText(item.type) }}</a-tag>
                <a-tag v-if="item.isTop" color="red" size="small">置顶</a-tag>
              </div>
            </div>
            <div class="timeline-description">{{ item.content }}</div>
            <div class="timeline-actions">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(item)">
                  查看详情
                </a-button>
                <span class="views-info">
                  <eye-outlined /> {{ item.views }}次浏览
                </span>
              </a-space>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>

    <!-- 创建/编辑动态弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="editingId ? '编辑机关动态' : '发布机关动态'"
      width="1000px"
      :confirm-loading="submitting"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="动态标题" name="title">
              <a-input v-model:value="formData.title" placeholder="请输入动态标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="发布单位" name="organization">
              <a-select v-model:value="formData.organization" placeholder="请选择发布单位">
                <a-select-option value="组织部">组织部</a-select-option>
                <a-select-option value="宣传部">宣传部</a-select-option>
                <a-select-option value="人事部">人事部</a-select-option>
                <a-select-option value="办公室">办公室</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="动态类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择类型">
                <a-select-option value="notice">通知公告</a-select-option>
                <a-select-option value="activity">活动动态</a-select-option>
                <a-select-option value="meeting">会议纪要</a-select-option>
                <a-select-option value="news">新闻资讯</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="重要程度" name="priority">
              <a-select v-model:value="formData.priority" placeholder="请选择重要程度">
                <a-select-option value="high">重要</a-select-option>
                <a-select-option value="medium">一般</a-select-option>
                <a-select-option value="low">普通</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="特殊标记">
              <a-space>
                <a-checkbox v-model:checked="formData.isTop">置顶</a-checkbox>
                <a-checkbox v-model:checked="formData.isUrgent">紧急</a-checkbox>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="动态内容" name="content">
          <a-textarea
            v-model:value="formData.content"
            placeholder="请输入动态内容"
            :rows="8"
            show-count
            :maxlength="2000"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="上传图片">
              <a-upload
                v-model:file-list="formData.images"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @preview="handlePreview"
                @remove="handleRemove"
              >
                <div v-if="formData.images.length < 9">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传图片</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="上传视频">
              <a-upload
                v-model:file-list="formData.videos"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <a-button>
                  <video-camera-outlined />
                  上传视频
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="上传附件">
              <a-upload
                v-model:file-list="formData.attachments"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <a-button>
                  <paper-clip-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  UploadOutlined,
  FileTextOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  EyeOutlined,
  MoreOutlined,
  DownOutlined,
  VideoCameraOutlined,
  PaperClipOutlined,
  NotificationOutlined,
  TeamOutlined,
  FileOutlined,
  SoundOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, UploadFile } from 'ant-design-vue'
import dayjs from 'dayjs'

// 接口类型定义
interface OrganizationDynamic {
  id: string
  title: string
  content: string
  organization: string
  type: 'notice' | 'activity' | 'meeting' | 'news'
  status: 'draft' | 'published' | 'archived'
  priority: 'high' | 'medium' | 'low'
  isTop: boolean
  isUrgent: boolean
  publishTime: string
  updateTime: string
  author: string
  views: number
  images?: UploadFile[]
  videos?: UploadFile[]
  attachments?: UploadFile[]
  version?: number
}

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const modalVisible = ref(false)
const batchVisible = ref(false)
const detailVisible = ref(false)
const previewVisible = ref(false)
const previewImage = ref('')
const editingId = ref<string | null>(null)
const currentDetail = ref<OrganizationDynamic | null>(null)
const viewMode = ref('list')

// 筛选表单
const filterForm = reactive({
  organization: undefined as string | undefined,
  type: undefined as string | undefined,
  status: undefined as string | undefined,
  priority: undefined as string | undefined,
  dateRange: undefined as any
})

// 统计数据
const statistics = reactive({
  total: 156,
  thisMonth: 28,
  pending: 12,
  totalViews: 8542
})

// 动态列表数据
const dynamicsList = ref<OrganizationDynamic[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 250,
    ellipsis: true
  },
  {
    title: '发布单位',
    dataIndex: 'organization',
    key: 'organization',
    width: 120
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '重要程度',
    dataIndex: 'priority',
    key: 'priority',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '浏览量',
    key: 'views',
    width: 100
  },
  {
    title: '发布时间',
    dataIndex: 'publishTime',
    key: 'publishTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const
  }
]

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  organization: '',
  type: 'notice' as 'notice' | 'activity' | 'meeting' | 'news',
  priority: 'medium' as 'high' | 'medium' | 'low',
  isTop: false,
  isUrgent: false,
  images: [] as UploadFile[],
  videos: [] as UploadFile[],
  attachments: [] as UploadFile[]
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入动态标题', trigger: 'blur' },
    { min: 5, max: 200, message: '标题长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入动态内容', trigger: 'blur' },
    { min: 20, max: 2000, message: '内容长度在 20 到 2000 个字符', trigger: 'blur' }
  ],
  organization: [
    { required: true, message: '请选择发布单位', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择动态类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择重要程度', trigger: 'change' }
  ]
}

const formRef = ref()

// 模拟数据
const mockData: OrganizationDynamic[] = [
  {
    id: '1',
    title: '关于召开2024年度工作总结大会的通知',
    content: '各部门：为全面总结2024年度工作成果，部署2025年重点任务，经研究决定召开2024年度工作总结大会。现将有关事项通知如下：一、会议时间：2024年1月30日（星期二）上午9:00；二、会议地点：机关大会议室；三、参会人员：各部门负责人及相关工作人员...',
    organization: '办公室',
    type: 'notice',
    status: 'published',
    priority: 'high',
    isTop: true,
    isUrgent: false,
    publishTime: '2025-06-20 09:00:00',
    updateTime: '2025-06-20 09:00:00',
    author: '办公室',
    views: 1256,
    images: [],
    videos: [],
    attachments: [
      { uid: '1', name: '会议通知.pdf', status: 'done', url: '#' }
    ],
    version: 1
  },
  {
    id: '2',
    title: '党支部开展"学习强国"主题党日活动',
    content: '为深入学习贯彻习近平新时代中国特色社会主义思想，进一步提升党员理论素养，我支部于1月18日组织开展了"学习强国"主题党日活动。活动中，全体党员集中学习了党的二十大报告重要内容，观看了专题教育片，并结合实际工作进行了深入讨论...',
    organization: '组织部',
    type: 'activity',
    status: 'published',
    priority: 'medium',
    isTop: false,
    isUrgent: false,
    publishTime: '2025-06-18 15:30:00',
    updateTime: '2025-06-18 15:30:00',
    author: '组织部',
    views: 892,
    images: [
      { uid: '2', name: 'activity1.jpg', status: 'done', url: 'https://via.placeholder.com/300x200' },
      { uid: '3', name: 'activity2.jpg', status: 'done', url: 'https://via.placeholder.com/300x200' }
    ],
    videos: [],
    attachments: [],
    version: 1
  },
  {
    id: '3',
    title: '机关安全工作专题会议纪要',
    content: '2024年1月15日下午，机关召开安全工作专题会议，会议由主要负责同志主持，各部门安全责任人参加。会议传达学习了上级关于安全工作的重要指示精神，分析了当前安全形势，部署了下一步安全工作重点任务...',
    organization: '办公室',
    type: 'meeting',
    status: 'draft',
    priority: 'medium',
    isTop: false,
    isUrgent: true,
    publishTime: '2025-06-16 10:20:00',
    updateTime: '2025-06-16 10:20:00',
    author: '办公室',
    views: 234,
    images: [],
    videos: [],
    attachments: [
      { uid: '4', name: '会议纪要.docx', status: 'done', url: '#' }
    ],
    version: 1
  }
]

// 工具函数
const getTypeColor = (type: string) => {
  const colorMap = {
    notice: 'red',
    activity: 'green',
    meeting: 'blue',
    news: 'orange'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

const getTypeText = (type: string) => {
  const textMap = {
    notice: '通知公告',
    activity: '活动动态',
    meeting: '会议纪要',
    news: '新闻资讯'
  }
  return textMap[type as keyof typeof textMap] || type
}

const getStatusColor = (status: string) => {
  const colorMap = {
    draft: 'orange',
    published: 'green',
    archived: 'default'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const textMap = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return textMap[status as keyof typeof textMap] || status
}

const getPriorityColor = (priority: string) => {
  const colorMap = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  }
  return colorMap[priority as keyof typeof colorMap] || 'default'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    high: '重要',
    medium: '一般',
    low: '普通'
  }
  return textMap[priority as keyof typeof textMap] || priority
}

const getTimelineColor = (priority: string) => {
  const colorMap = {
    high: 'red',
    medium: 'blue',
    low: 'green'
  }
  return colorMap[priority as keyof typeof colorMap] || 'blue'
}

const getTimelineIcon = (type: string) => {
  const iconMap = {
    notice: NotificationOutlined,
    activity: TeamOutlined,
    meeting: FileOutlined,
    news: SoundOutlined
  }
  return iconMap[type as keyof typeof iconMap] || FileOutlined
}

// 方法定义
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 应用筛选条件
    let filteredData = [...mockData]

    if (filterForm.organization) {
      filteredData = filteredData.filter(item =>
        item.organization === filterForm.organization
      )
    }

    if (filterForm.type) {
      filteredData = filteredData.filter(item =>
        item.type === filterForm.type
      )
    }

    if (filterForm.status) {
      filteredData = filteredData.filter(item =>
        item.status === filterForm.status
      )
    }

    if (filterForm.priority) {
      filteredData = filteredData.filter(item =>
        item.priority === filterForm.priority
      )
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      const [startDate, endDate] = filterForm.dateRange
      filteredData = filteredData.filter(item => {
        const itemDate = dayjs(item.publishTime)
        return itemDate.isAfter(startDate) && itemDate.isBefore(endDate.add(1, 'day'))
      })
    }

    // 排序：置顶优先，然后按发布时间倒序
    filteredData.sort((a, b) => {
      if (a.isTop && !b.isTop) return -1
      if (!a.isTop && b.isTop) return 1
      return new Date(b.publishTime).getTime() - new Date(a.publishTime).getTime()
    })

    // 分页处理
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize

    dynamicsList.value = filteredData.slice(start, end)
    pagination.total = filteredData.length

  } catch (error) {
    message.error('加载数据失败')
    console.error('Load data error:', error)
  } finally {
    loading.value = false
  }
}

const handleFilter = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    organization: undefined,
    type: undefined,
    status: undefined,
    priority: undefined,
    dateRange: undefined
  })
  pagination.current = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleViewModeChange = () => {
  console.log('视图模式变更:', viewMode.value)
}

const showCreateModal = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

const showBatchModal = () => {
  batchVisible.value = true
}

const editDynamic = (record: OrganizationDynamic) => {
  editingId.value = record.id
  Object.assign(formData, {
    title: record.title,
    content: record.content,
    organization: record.organization,
    type: record.type,
    priority: record.priority,
    isTop: record.isTop,
    isUrgent: record.isUrgent,
    images: record.images || [],
    videos: record.videos || [],
    attachments: record.attachments || []
  })
  modalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    content: '',
    organization: '',
    type: 'notice',
    priority: 'medium',
    isTop: false,
    isUrgent: false,
    images: [],
    videos: [],
    attachments: []
  })
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingId.value) {
      // 编辑逻辑
      const index = mockData.findIndex(item => item.id === editingId.value)
      if (index !== -1) {
        Object.assign(mockData[index], {
          ...formData,
          updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          version: (mockData[index].version || 1) + 1
        })
      }
      message.success('动态更新成功')
    } else {
      // 新建逻辑
      const newDynamic: OrganizationDynamic = {
        id: Date.now().toString(),
        ...formData,
        status: 'draft',
        publishTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        author: formData.organization,
        views: 0,
        version: 1
      }
      mockData.unshift(newDynamic)
      message.success('动态创建成功')
    }

    modalVisible.value = false
    loadData()

  } catch (error) {
    console.error('Submit error:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const viewDetail = (record: OrganizationDynamic) => {
  currentDetail.value = record
  detailVisible.value = true
}

const publishDynamic = async (record: OrganizationDynamic) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = mockData.findIndex(item => item.id === record.id)
    if (index !== -1) {
      mockData[index].status = 'published'
      mockData[index].updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }

    message.success('动态发布成功')
    loadData()
  } catch (error) {
    message.error('发布失败')
    console.error('Publish error:', error)
  } finally {
    loading.value = false
  }
}

const deleteDynamic = async (id: string) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = mockData.findIndex(item => item.id === id)
    if (index !== -1) {
      mockData.splice(index, 1)
    }

    message.success('动态删除成功')
    loadData()
  } catch (error) {
    message.error('删除失败')
    console.error('Delete error:', error)
  } finally {
    loading.value = false
  }
}

const handleMenuAction = async (key: string, record: OrganizationDynamic) => {
  switch (key) {
    case 'top':
      const topIndex = mockData.findIndex(item => item.id === record.id)
      if (topIndex !== -1) {
        mockData[topIndex].isTop = true
        message.success('置顶成功')
        loadData()
      }
      break
    case 'untop':
      const untopIndex = mockData.findIndex(item => item.id === record.id)
      if (untopIndex !== -1) {
        mockData[untopIndex].isTop = false
        message.success('取消置顶成功')
        loadData()
      }
      break
    case 'archive':
      const archiveIndex = mockData.findIndex(item => item.id === record.id)
      if (archiveIndex !== -1) {
        mockData[archiveIndex].status = 'archived'
        message.success('归档成功')
        loadData()
      }
      break
    case 'export':
      message.success(`导出动态：${record.title}`)
      break
    case 'delete':
      deleteDynamic(record.id)
      break
  }
}

const beforeUpload = (file: any) => {
  const isValidType = file.type.startsWith('image/') ||
                     file.type.startsWith('video/') ||
                     file.type.startsWith('application/')

  if (!isValidType) {
    message.error('只能上传图片、视频或文档文件!')
    return false
  }

  const isLt20M = file.size / 1024 / 1024 < 20
  if (!isLt20M) {
    message.error('文件大小不能超过 20MB!')
    return false
  }

  return false // 阻止自动上传
}

const handlePreview = (file: UploadFile) => {
  previewImage.value = file.url || file.preview || ''
  previewVisible.value = true
}

const handleRemove = (file: UploadFile) => {
  console.log('移除文件:', file)
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.organization-dynamics {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-area {
  margin-bottom: 16px;
}

.statistics-section {
  margin-bottom: 16px;
}

.list-view {
  background: white;
  border-radius: 8px;
}

.title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.views-count {
  color: #666;
  font-size: 12px;
}

.card-view {
  min-height: 400px;
}

.dynamic-card {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dynamic-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.card-title span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-tags {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-meta {
  margin-bottom: 12px;
}

.content-preview {
  flex: 1;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-view {
  min-height: 400px;
  background: white;
  border-radius: 8px;
  padding: 24px;
}

.timeline-date {
  font-size: 12px;
  color: #8c8c8c;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 16px;
}

.timeline-header h4 {
  margin: 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.timeline-tags {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.timeline-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.timeline-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.views-info {
  color: #8c8c8c;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .action-left,
  .action-right {
    width: 100%;
  }

  .filter-area :deep(.ant-form-item) {
    margin-bottom: 8px;
  }

  .timeline-header {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-tags {
    align-self: flex-start;
  }
}
</style>
