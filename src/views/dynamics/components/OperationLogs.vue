<template>
  <div class="operation-logs">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <a-space>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportLogs">
            <template #icon><export-outlined /></template>
            导出日志
          </a-button>
          <a-button @click="clearLogs" danger>
            <template #icon><delete-outlined /></template>
            清空日志
          </a-button>
        </a-space>
      </div>
      <div class="action-right">
        <a-space>
          <span>自动刷新：</span>
          <a-switch v-model:checked="autoRefresh" @change="handleAutoRefreshChange" />
        </a-space>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-area">
      <a-card size="small">
        <a-form layout="inline" :model="filterForm">
          <a-form-item label="操作类型">
            <a-select
              v-model:value="filterForm.action"
              placeholder="请选择操作类型"
              style="width: 150px"
              allow-clear
            >
              <a-select-option value="create">创建</a-select-option>
              <a-select-option value="update">更新</a-select-option>
              <a-select-option value="delete">删除</a-select-option>
              <a-select-option value="publish">发布</a-select-option>
              <a-select-option value="archive">归档</a-select-option>
              <a-select-option value="view">查看</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="操作对象">
            <a-select
              v-model:value="filterForm.objectType"
              placeholder="请选择对象类型"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="personal">个人动态</a-select-option>
              <a-select-option value="organization">机关动态</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="操作人">
            <a-input
              v-model:value="filterForm.operator"
              placeholder="请输入操作人"
              style="width: 120px"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="操作时间">
            <a-range-picker
              v-model:value="filterForm.dateRange"
              style="width: 240px"
              show-time
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleFilter">筛选</a-button>
              <a-button @click="resetFilter">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 统计信息 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="总操作数"
              :value="statistics.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix><file-text-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="今日操作"
              :value="statistics.today"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix><calendar-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="异常操作"
              :value="statistics.errors"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix><warning-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic
              title="活跃用户"
              :value="statistics.activeUsers"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix><user-outlined /></template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 日志列表 -->
    <div class="logs-list">
      <a-table
        :columns="columns"
        :data-source="logsList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-tag :color="getActionColor(record.action)">
              {{ getActionText(record.action) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'objectType'">
            <a-tag :color="getObjectTypeColor(record.objectType)">
              {{ getObjectTypeText(record.objectType) }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 'success' ? 'green' : 'red'">
              {{ record.status === 'success' ? '成功' : '失败' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'details'">
            <a-button type="link" size="small" @click="viewLogDetail(record)">
              查看详情
            </a-button>
          </template>

          <template v-else-if="column.key === 'duration'">
            <span class="duration-text">{{ record.duration }}ms</span>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="操作日志详情"
      width="700px"
      :footer="null"
    >
      <div v-if="currentLog" class="log-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="操作ID">{{ currentLog.id }}</a-descriptions-item>
          <a-descriptions-item label="操作类型">
            <a-tag :color="getActionColor(currentLog.action)">
              {{ getActionText(currentLog.action) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="操作对象">
            <a-tag :color="getObjectTypeColor(currentLog.objectType)">
              {{ getObjectTypeText(currentLog.objectType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="对象ID">{{ currentLog.objectId }}</a-descriptions-item>
          <a-descriptions-item label="操作人">{{ currentLog.operator }}</a-descriptions-item>
          <a-descriptions-item label="操作时间">{{ currentLog.operateTime }}</a-descriptions-item>
          <a-descriptions-item label="操作状态">
            <a-tag :color="currentLog.status === 'success' ? 'green' : 'red'">
              {{ currentLog.status === 'success' ? '成功' : '失败' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="耗时">{{ currentLog.duration }}ms</a-descriptions-item>
          <a-descriptions-item label="IP地址">{{ currentLog.ipAddress }}</a-descriptions-item>
          <a-descriptions-item label="用户代理">{{ currentLog.userAgent }}</a-descriptions-item>
        </a-descriptions>
        
        <div class="log-content">
          <h4>操作详情</h4>
          <div class="operation-details">
            <pre>{{ JSON.stringify(currentLog.details, null, 2) }}</pre>
          </div>
          
          <div v-if="currentLog.changes" class="changes-section">
            <h4>变更记录</h4>
            <div class="changes-content">
              <div v-for="(change, key) in currentLog.changes" :key="key" class="change-item">
                <div class="change-field">{{ key }}:</div>
                <div class="change-values">
                  <span class="old-value">{{ change.old }}</span>
                  <arrow-right-outlined />
                  <span class="new-value">{{ change.new }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="currentLog.error" class="error-section">
            <h4>错误信息</h4>
            <div class="error-content">
              <a-alert
                :message="currentLog.error.message"
                :description="currentLog.error.stack"
                type="error"
                show-icon
              />
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined,
  DeleteOutlined,
  FileTextOutlined,
  CalendarOutlined,
  WarningOutlined,
  UserOutlined,
  ArrowRightOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType } from 'ant-design-vue'
import dayjs from 'dayjs'

// 接口类型定义
interface OperationLog {
  id: string
  action: 'create' | 'update' | 'delete' | 'publish' | 'archive' | 'view'
  objectType: 'personal' | 'organization'
  objectId: string
  objectTitle: string
  operator: string
  operateTime: string
  status: 'success' | 'error'
  duration: number
  ipAddress: string
  userAgent: string
  details: any
  changes?: Record<string, { old: any; new: any }>
  error?: {
    message: string
    stack: string
  }
}

// 响应式数据
const loading = ref(false)
const detailVisible = ref(false)
const currentLog = ref<OperationLog | null>(null)
const logsList = ref<OperationLog[]>([])
const autoRefresh = ref(false)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 筛选表单
const filterForm = reactive({
  action: undefined as string | undefined,
  objectType: undefined as string | undefined,
  operator: '',
  dateRange: undefined as any
})

// 统计数据
const statistics = reactive({
  total: 1248,
  today: 156,
  errors: 8,
  activeUsers: 24
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '操作时间',
    dataIndex: 'operateTime',
    key: 'operateTime',
    width: 180,
    sorter: true
  },
  {
    title: '操作类型',
    dataIndex: 'action',
    key: 'action',
    width: 100
  },
  {
    title: '对象类型',
    dataIndex: 'objectType',
    key: 'objectType',
    width: 100
  },
  {
    title: '操作对象',
    dataIndex: 'objectTitle',
    key: 'objectTitle',
    width: 200,
    ellipsis: true
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '耗时',
    key: 'duration',
    width: 80
  },
  {
    title: 'IP地址',
    dataIndex: 'ipAddress',
    key: 'ipAddress',
    width: 120
  },
  {
    title: '操作',
    key: 'details',
    width: 100,
    fixed: 'right' as const
  }
]

// 模拟数据
const mockData: OperationLog[] = [
  {
    id: '1',
    action: 'create',
    objectType: 'personal',
    objectId: 'dyn_001',
    objectTitle: '党支部学习贯彻党的二十大精神专题会议',
    operator: '周海军',
    operateTime: '2025-06-20 14:30:25',
    status: 'success',
    duration: 245,
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    details: {
      title: '党支部学习贯彻党的二十大精神专题会议',
      content: '为深入学习贯彻党的二十大精神...',
      scope: 'public'
    }
  },
  {
    id: '2',
    action: 'update',
    objectType: 'organization',
    objectId: 'org_001',
    objectTitle: '关于召开2024年度工作总结大会的通知',
    operator: '王东',
    operateTime: '2025-06-20 11:15:42',
    status: 'success',
    duration: 156,
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    details: {
      action: 'update_content',
      changes: ['title', 'content']
    },
    changes: {
      title: {
        old: '关于召开工作总结大会的通知',
        new: '关于召开2024年度工作总结大会的通知'
      },
      priority: {
        old: 'medium',
        new: 'high'
      }
    }
  },
  {
    id: '3',
    action: 'delete',
    objectType: 'personal',
    objectId: 'dyn_002',
    objectTitle: '测试动态内容',
    operator: '杜佳佳',
    operateTime: '2025-06-20 09:45:18',
    status: 'error',
    duration: 89,
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    details: {
      reason: 'user_request'
    },
    error: {
      message: '删除失败：对象不存在或已被删除',
      stack: 'Error: Object not found\n    at deleteObject (dynamics.js:123:15)\n    at async handler (router.js:45:7)'
    }
  }
]

// 工具函数
const getActionColor = (action: string) => {
  const colorMap = {
    create: 'green',
    update: 'blue',
    delete: 'red',
    publish: 'purple',
    archive: 'orange',
    view: 'default'
  }
  return colorMap[action as keyof typeof colorMap] || 'default'
}

const getActionText = (action: string) => {
  const textMap = {
    create: '创建',
    update: '更新',
    delete: '删除',
    publish: '发布',
    archive: '归档',
    view: '查看'
  }
  return textMap[action as keyof typeof textMap] || action
}

const getObjectTypeColor = (type: string) => {
  const colorMap = {
    personal: 'cyan',
    organization: 'geekblue'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

const getObjectTypeText = (type: string) => {
  const textMap = {
    personal: '个人动态',
    organization: '机关动态'
  }
  return textMap[type as keyof typeof textMap] || type
}

// 方法定义
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300))

    // 应用筛选条件
    let filteredData = [...mockData]

    if (filterForm.action) {
      filteredData = filteredData.filter(log => log.action === filterForm.action)
    }

    if (filterForm.objectType) {
      filteredData = filteredData.filter(log => log.objectType === filterForm.objectType)
    }

    if (filterForm.operator) {
      filteredData = filteredData.filter(log =>
        log.operator.includes(filterForm.operator)
      )
    }

    if (filterForm.dateRange && filterForm.dateRange.length === 2) {
      const [startDate, endDate] = filterForm.dateRange
      filteredData = filteredData.filter(log => {
        const logDate = dayjs(log.operateTime)
        return logDate.isAfter(startDate) && logDate.isBefore(endDate.add(1, 'day'))
      })
    }

    // 按时间倒序排列
    filteredData.sort((a, b) =>
      new Date(b.operateTime).getTime() - new Date(a.operateTime).getTime()
    )

    // 分页处理
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize

    logsList.value = filteredData.slice(start, end)
    pagination.total = filteredData.length

  } catch (error) {
    message.error('加载日志失败')
    console.error('Load logs error:', error)
  } finally {
    loading.value = false
  }
}

const handleFilter = () => {
  pagination.current = 1
  loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    action: undefined,
    objectType: undefined,
    operator: '',
    dateRange: undefined
  })
  pagination.current = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleAutoRefreshChange = (checked: boolean) => {
  if (checked) {
    refreshTimer.value = setInterval(() => {
      loadData()
    }, 30000) // 30秒自动刷新
    message.success('已开启自动刷新（30秒间隔）')
  } else {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
    message.info('已关闭自动刷新')
  }
}

const viewLogDetail = (record: OperationLog) => {
  currentLog.value = record
  detailVisible.value = true
}

const exportLogs = () => {
  message.success('日志导出功能开发中...')
}

const clearLogs = () => {
  message.warning('清空日志功能需要管理员权限')
}

// 页面初始化和清理
onMounted(() => {
  loadData()
})

onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
  }
})
</script>

<style scoped>
.operation-logs {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.filter-area {
  margin-bottom: 16px;
}

.statistics-section {
  margin-bottom: 16px;
}

.logs-list {
  background: white;
  border-radius: 8px;
}

.duration-text {
  color: #666;
  font-family: 'Courier New', monospace;
}

.log-detail {
  padding: 8px 0;
}

.log-content {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.log-content h4 {
  margin: 16px 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.log-content h4:first-child {
  margin-top: 0;
}

.operation-details {
  background: #f6f6f6;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.operation-details pre {
  margin: 0;
  font-size: 12px;
  color: #262626;
  white-space: pre-wrap;
  word-break: break-all;
}

.changes-section,
.error-section {
  margin-top: 16px;
}

.changes-content {
  background: #f9f9f9;
  border-radius: 4px;
  padding: 12px;
}

.change-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.change-item:last-child {
  margin-bottom: 0;
}

.change-field {
  font-weight: 600;
  color: #262626;
  min-width: 100px;
  margin-right: 12px;
}

.change-values {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.old-value {
  background: #fff2f0;
  color: #cf1322;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.new-value {
  background: #f6ffed;
  color: #389e0d;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.error-content {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .action-left,
  .action-right {
    width: 100%;
  }

  .filter-area :deep(.ant-form-item) {
    margin-bottom: 8px;
  }

  .change-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .change-field {
    margin-bottom: 4px;
    margin-right: 0;
  }

  .change-values {
    width: 100%;
  }
}
</style>
