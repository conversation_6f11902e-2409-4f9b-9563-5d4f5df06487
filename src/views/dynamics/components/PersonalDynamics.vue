<template>
  <div class="personal-dynamics">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建动态
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
      <div class="action-right">
        <a-space>
          <a-select v-model:value="viewMode" style="width: 120px" @change="handleViewModeChange">
            <a-select-option value="list">列表视图</a-select-option>
            <a-select-option value="card">卡片视图</a-select-option>
          </a-select>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-area">
      <a-card size="small">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="标题">
            <a-input
              v-model:value="searchForm.title"
              placeholder="搜索动态标题"
              style="width: 200px"
              allow-clear
              @press-enter="handleSearch"
            >
              <template #prefix><search-outlined /></template>
            </a-input>
          </a-form-item>
          <a-form-item label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="发布状态"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="published">已发布</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="范围">
            <a-select
              v-model:value="searchForm.scope"
              placeholder="发布范围"
              style="width: 120px"
              allow-clear
            >
              <a-select-option value="private">仅自己可见</a-select-option>
              <a-select-option value="public">公开</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间">
            <a-range-picker
              v-model:value="searchForm.dateRange"
              style="width: 240px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">搜索</a-button>
              <a-button @click="resetSearch">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'" class="list-view">
      <a-table
        :columns="columns"
        :data-source="dynamicsList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'title'">
            <a @click="viewDetail(record)">{{ record.title }}</a>
          </template>

          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 'published' ? 'green' : 'orange'">
              {{ record.status === 'published' ? '已发布' : '草稿' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'scope'">
            <a-tag :color="record.scope === 'public' ? 'blue' : 'default'">
              {{ record.scope === 'public' ? '公开' : '仅自己可见' }}
            </a-tag>
          </template>

          <template v-else-if="column.key === 'hasMedia'">
            <a-space>
              <a-tag v-if="record.images?.length" color="cyan">
                <picture-outlined /> 图片
              </a-tag>
              <a-tag v-if="record.videos?.length" color="purple">
                <video-camera-outlined /> 视频
              </a-tag>
              <a-tag v-if="record.audios?.length" color="orange">
                <sound-outlined /> 音频
              </a-tag>
            </a-space>
          </template>

          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="viewDetail(record)">查看</a-button>
              <a-button type="link" size="small" @click="editDynamic(record)">编辑</a-button>
              <a-button
                type="link"
                size="small"
                :disabled="record.status === 'published'"
                @click="publishDynamic(record)"
              >
                发布
              </a-button>
              <a-popconfirm
                title="确定要删除这条动态吗？"
                @confirm="deleteDynamic(record.id)"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 卡片视图 -->
    <div v-if="viewMode === 'card'" class="card-view">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :lg="8" v-for="item in dynamicsList" :key="item.id">
          <a-card hoverable class="dynamic-card">
            <template #title>
              <div class="card-title">
                <span>{{ item.title }}</span>
                <a-tag :color="item.status === 'published' ? 'green' : 'orange'">
                  {{ item.status === 'published' ? '已发布' : '草稿' }}
                </a-tag>
              </div>
            </template>
            
            <template #extra>
              <a-dropdown>
                <a-button type="text" size="small">
                  <more-outlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="viewDetail(item)">查看详情</a-menu-item>
                    <a-menu-item @click="editDynamic(item)">编辑</a-menu-item>
                    <a-menu-item @click="publishDynamic(item)" :disabled="item.status === 'published'">发布</a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deleteDynamic(item.id)" danger>删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>

            <div class="card-content">
              <p class="content-preview">{{ item.content }}</p>
              
              <div v-if="item.images?.length || item.videos?.length || item.audios?.length" class="media-preview">
                <a-space>
                  <a-tag v-if="item.images?.length" color="cyan">
                    <picture-outlined /> {{ item.images.length }}张图片
                  </a-tag>
                  <a-tag v-if="item.videos?.length" color="purple">
                    <video-camera-outlined /> {{ item.videos.length }}个视频
                  </a-tag>
                  <a-tag v-if="item.audios?.length" color="orange">
                    <sound-outlined /> {{ item.audios.length }}个音频
                  </a-tag>
                </a-space>
              </div>
              
              <div class="card-meta">
                <a-space>
                  <span><clock-circle-outlined /> {{ item.createTime }}</span>
                  <a-tag :color="item.scope === 'public' ? 'blue' : 'default'">
                    {{ item.scope === 'public' ? '公开' : '仅自己可见' }}
                  </a-tag>
                </a-space>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 创建/编辑动态弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="editingId ? '编辑动态' : '新建动态'"
      width="900px"
      :confirm-loading="submitting"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="动态标题" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入动态标题" />
        </a-form-item>

        <a-form-item label="动态内容" name="content">
          <a-textarea
            v-model:value="formData.content"
            placeholder="请输入动态内容"
            :rows="6"
            show-count
            :maxlength="1000"
          />
        </a-form-item>

        <a-form-item label="发布范围" name="scope">
          <a-radio-group v-model:value="formData.scope">
            <a-radio value="private">仅自己可见</a-radio>
            <a-radio value="public">公开</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="上传图片">
              <a-upload
                v-model:file-list="formData.images"
                list-type="picture-card"
                :before-upload="beforeUpload"
                @preview="handlePreview"
                @remove="handleRemove"
              >
                <div v-if="formData.images.length < 9">
                  <plus-outlined />
                  <div style="margin-top: 8px">上传图片</div>
                </div>
              </a-upload>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="上传视频">
              <a-upload
                v-model:file-list="formData.videos"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <a-button>
                  <video-camera-outlined />
                  上传视频
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
          
          <a-col :span="8">
            <a-form-item label="上传音频">
              <a-upload
                v-model:file-list="formData.audios"
                :before-upload="beforeUpload"
                @remove="handleRemove"
              >
                <a-button>
                  <sound-outlined />
                  上传音频
                </a-button>
              </a-upload>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 动态详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="动态详情"
      width="800px"
      :footer="null"
    >
      <div v-if="currentDetail" class="detail-content">
        <h3>{{ currentDetail.title }}</h3>
        <div class="detail-meta">
          <a-space>
            <span>发布时间：{{ currentDetail.createTime }}</span>
            <a-tag :color="currentDetail.status === 'published' ? 'green' : 'orange'">
              {{ currentDetail.status === 'published' ? '已发布' : '草稿' }}
            </a-tag>
            <a-tag :color="currentDetail.scope === 'public' ? 'blue' : 'default'">
              {{ currentDetail.scope === 'public' ? '公开' : '仅自己可见' }}
            </a-tag>
          </a-space>
        </div>
        <div class="detail-text">
          {{ currentDetail.content }}
        </div>
        
        <!-- 媒体文件展示 -->
        <div v-if="currentDetail.images?.length" class="detail-images">
          <h4>图片</h4>
          <a-image-preview-group>
            <a-image
              v-for="(image, index) in currentDetail.images"
              :key="index"
              :src="image.url"
              :width="100"
              style="margin-right: 8px; margin-bottom: 8px"
            />
          </a-image-preview-group>
        </div>
        
        <div v-if="currentDetail.videos?.length" class="detail-videos">
          <h4>视频</h4>
          <div v-for="(video, index) in currentDetail.videos" :key="index">
            <video :src="video.url" controls style="max-width: 100%; margin-bottom: 8px"></video>
          </div>
        </div>
        
        <div v-if="currentDetail.audios?.length" class="detail-audios">
          <h4>音频</h4>
          <div v-for="(audio, index) in currentDetail.audios" :key="index">
            <audio :src="audio.url" controls style="width: 100%; margin-bottom: 8px"></audio>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 图片预览 -->
    <a-modal v-model:open="previewVisible" :footer="null">
      <img alt="preview" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  SoundOutlined,
  MoreOutlined,
  ClockCircleOutlined
} from '@ant-design/icons-vue'
import type { TableColumnsType, UploadFile } from 'ant-design-vue'
import dayjs from 'dayjs'

// 接口类型定义
interface DynamicItem {
  id: string
  title: string
  content: string
  status: 'draft' | 'published'
  scope: 'private' | 'public'
  images?: UploadFile[]
  videos?: UploadFile[]
  audios?: UploadFile[]
  createTime: string
  updateTime: string
  author: string
  version?: number
}

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const modalVisible = ref(false)
const detailVisible = ref(false)
const previewVisible = ref(false)
const previewImage = ref('')
const editingId = ref<string | null>(null)
const currentDetail = ref<DynamicItem | null>(null)
const viewMode = ref('list')

// 搜索表单
const searchForm = reactive({
  title: '',
  status: undefined as string | undefined,
  scope: undefined as string | undefined,
  dateRange: undefined as any
})

// 动态列表数据
const dynamicsList = ref<DynamicItem[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns: TableColumnsType = [
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: 200,
    ellipsis: true
  },
  {
    title: '内容预览',
    dataIndex: 'content',
    key: 'content',
    width: 300,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '发布范围',
    dataIndex: 'scope',
    key: 'scope',
    width: 120
  },
  {
    title: '媒体文件',
    key: 'hasMedia',
    width: 150
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const
  }
]

// 表单数据
const formData = reactive({
  title: '',
  content: '',
  scope: 'private' as 'private' | 'public',
  images: [] as UploadFile[],
  videos: [] as UploadFile[],
  audios: [] as UploadFile[]
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入动态标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入动态内容', trigger: 'blur' },
    { min: 10, max: 1000, message: '内容长度在 10 到 1000 个字符', trigger: 'blur' }
  ],
  scope: [
    { required: true, message: '请选择发布范围', trigger: 'change' }
  ]
}

const formRef = ref()

// 模拟数据
const mockData: DynamicItem[] = [
  {
    id: '1',
    title: '党支部学习贯彻党的二十大精神专题会议',
    content: '为深入学习贯彻党的二十大精神，进一步统一思想、凝聚共识，我支部于今日召开专题学习会议。会议深入学习了党的二十大报告的重要内容，结合实际工作进行了深入讨论。',
    status: 'published',
    scope: 'public',
    images: [
      // { uid: '1', name: 'meeting1.jpg', status: 'done', url: 'https://via.placeholder.com/300x200' },
      // { uid: '2', name: 'meeting2.jpg', status: 'done', url: 'https://via.placeholder.com/300x200' }
    ],
    videos: [],
    audios: [],
    createTime: '2025-06-15 10:30:00',
    updateTime: '2025-06-15 10:30:00',
    author: '周海军',
    version: 1
  },
  {
    id: '2',
    title: '机关党建工作创新实践分享',
    content: '结合我单位实际情况，在党建工作中积极探索创新举措，取得了良好效果。通过建立党建工作责任制，完善党建工作机制，提升了党建工作质量。',
    status: 'draft',
    scope: 'private',
    images: [],
    videos: [
      // { uid: '3', name: 'practice.mp4', status: 'done', url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' }
    ],
    audios: [],
    createTime: '2025-06-14 14:20:00',
    updateTime: '2025-06-14 14:20:00',
    author: '王东',
    version: 1
  },
  {
    id: '3',
    title: '志愿服务活动圆满结束',
    content: '我支部组织的社区志愿服务活动圆满结束，党员同志们积极参与，展现了良好的精神风貌。活动中，大家发扬不怕苦、不怕累的精神，为社区居民提供了优质服务。',
    status: 'published',
    scope: 'public',
    images: [
      // { uid: '4', name: 'volunteer1.jpg', status: 'done', url: 'https://via.placeholder.com/300x200' }
    ],
    videos: [],
    audios: [
      // { uid: '5', name: 'interview.mp3', status: 'done', url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav' }
    ],
    createTime: '2025-06-13 16:45:00',
    updateTime: '2025-06-13 16:45:00',
    author: '杜佳佳',
    version: 1
  }
]

// 方法定义
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 应用搜索筛选
    let filteredData = [...mockData]

    if (searchForm.title) {
      filteredData = filteredData.filter(item =>
        item.title.includes(searchForm.title)
      )
    }

    if (searchForm.status) {
      filteredData = filteredData.filter(item =>
        item.status === searchForm.status
      )
    }

    if (searchForm.scope) {
      filteredData = filteredData.filter(item =>
        item.scope === searchForm.scope
      )
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      const [startDate, endDate] = searchForm.dateRange
      filteredData = filteredData.filter(item => {
        const itemDate = dayjs(item.createTime)
        return itemDate.isAfter(startDate) && itemDate.isBefore(endDate.add(1, 'day'))
      })
    }

    // 分页处理
    const start = (pagination.current - 1) * pagination.pageSize
    const end = start + pagination.pageSize

    dynamicsList.value = filteredData.slice(start, end)
    pagination.total = filteredData.length

  } catch (error) {
    message.error('加载数据失败')
    console.error('Load data error:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    title: '',
    status: undefined,
    scope: undefined,
    dateRange: undefined
  })
  pagination.current = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const handleViewModeChange = () => {
  console.log('视图模式变更:', viewMode.value)
}

const showCreateModal = () => {
  editingId.value = null
  resetForm()
  modalVisible.value = true
}

const editDynamic = (record: DynamicItem) => {
  editingId.value = record.id
  Object.assign(formData, {
    title: record.title,
    content: record.content,
    scope: record.scope,
    images: record.images || [],
    videos: record.videos || [],
    audios: record.audios || []
  })
  modalVisible.value = true
}

const resetForm = () => {
  Object.assign(formData, {
    title: '',
    content: '',
    scope: 'private',
    images: [],
    videos: [],
    audios: []
  })
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingId.value) {
      // 编辑逻辑
      const index = mockData.findIndex(item => item.id === editingId.value)
      if (index !== -1) {
        Object.assign(mockData[index], {
          ...formData,
          updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          version: (mockData[index].version || 1) + 1
        })
      }
      message.success('动态更新成功')
    } else {
      // 新建逻辑
      const newDynamic: DynamicItem = {
        id: Date.now().toString(),
        ...formData,
        status: 'draft',
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        author: '当前用户',
        version: 1
      }
      mockData.unshift(newDynamic)
      message.success('动态创建成功')
    }

    modalVisible.value = false
    loadData()

  } catch (error) {
    console.error('Submit error:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  resetForm()
}

const viewDetail = (record: DynamicItem) => {
  currentDetail.value = record
  detailVisible.value = true
}

const publishDynamic = async (record: DynamicItem) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = mockData.findIndex(item => item.id === record.id)
    if (index !== -1) {
      mockData[index].status = 'published'
      mockData[index].updateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    }

    message.success('动态发布成功')
    loadData()
  } catch (error) {
    message.error('发布失败')
    console.error('Publish error:', error)
  } finally {
    loading.value = false
  }
}

const deleteDynamic = async (id: string) => {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = mockData.findIndex(item => item.id === id)
    if (index !== -1) {
      mockData.splice(index, 1)
    }

    message.success('动态删除成功')
    loadData()
  } catch (error) {
    message.error('删除失败')
    console.error('Delete error:', error)
  } finally {
    loading.value = false
  }
}

const beforeUpload = (file: any) => {
  const isValidType = file.type.startsWith('image/') ||
                     file.type.startsWith('video/') ||
                     file.type.startsWith('audio/')

  if (!isValidType) {
    message.error('只能上传图片、视频或音频文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }

  return false // 阻止自动上传
}

const handlePreview = (file: UploadFile) => {
  previewImage.value = file.url || file.preview || ''
  previewVisible.value = true
}

const handleRemove = (file: UploadFile) => {
  console.log('移除文件:', file)
}

// 页面初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.personal-dynamics {
  padding: 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-area {
  margin-bottom: 16px;
}

.list-view {
  background: white;
  border-radius: 8px;
}

.card-view {
  min-height: 400px;
}

.dynamic-card {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dynamic-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title span {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.content-preview {
  flex: 1;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.media-preview {
  margin-bottom: 12px;
}

.card-meta {
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 12px;
  color: #8c8c8c;
}

.detail-content {
  padding: 8px 0;
}

.detail-content h3 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 20px;
  font-weight: 600;
}

.detail-meta {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-text {
  color: #262626;
  line-height: 1.8;
  margin-bottom: 24px;
  white-space: pre-wrap;
}

.detail-images,
.detail-videos,
.detail-audios {
  margin-bottom: 24px;
}

.detail-images h4,
.detail-videos h4,
.detail-audios h4 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .action-left,
  .action-right {
    width: 100%;
  }

  .search-area :deep(.ant-form-item) {
    margin-bottom: 8px;
  }
}
</style>
