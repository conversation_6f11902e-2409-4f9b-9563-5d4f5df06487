<template>
  <div class="model-agency-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">模范机关总览看板</h1>
          <a-breadcrumb class="page-breadcrumb">
            <a-breadcrumb-item>
              <home-outlined />
              首页
            </a-breadcrumb-item>
            <a-breadcrumb-item>党建工作</a-breadcrumb-item>
            <a-breadcrumb-item>模范机关总览看板</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="header-actions">
          <a-space>
            <a-select v-model:value="selectedDistrict" placeholder="选择区县" style="width: 200px" allow-clear
              @change="handleDistrictChange">
              <a-select-option value="">全部区县</a-select-option>
              <a-select-option v-for="district in districts" :key="district" :value="district">
                {{ district }}
              </a-select-option>
            </a-select>
            <a-button @click="refreshAllData" :loading="globalLoading">
              <template #icon><reload-outlined /></template>
              刷新数据
            </a-button>
            <a-dropdown>
              <a-button>
                <template #icon><download-outlined /></template>
                导出报告
                <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleExport">
                  <a-menu-item key="pdf">
                    <file-pdf-outlined />
                    导出PDF报告
                  </a-menu-item>
                  <a-menu-item key="excel">
                    <file-excel-outlined />
                    导出Excel数据
                  </a-menu-item>
                  <a-menu-item key="image">
                    <picture-outlined />
                    导出图片
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <ErrorBoundary v-if="hasError" :error-type="errorType" :error-message="errorMessage" @retry="handleErrorRetry"
        @report="handleErrorReport" />
      <StatisticsOverview v-else :project-data="projectData?.municipal" :party-building-data="partyBuildingData"
        :loading="statisticsLoading" @card-click="handleStatisticsClick" @data-refresh="refreshStatisticsData"
        @location-update="handleLocationUpdate" @location-delete="handleLocationDelete" />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[16, 16]">
        <!-- 左侧地图区域 -->
        <a-col :xs="24" :lg="14" :xl="16">
          <div class="map-section">
            <MapContainer ref="mapContainerRef" :markers="mapMarkers" :viewport="mapViewport" :show-markers="true"
              :show-search="true" :show-controls="true" map-height="600px" @marker-click="handleMarkerClick"
              @map-click="handleMapClick" @place-select="handlePlaceSelect" @viewport-change="handleViewportChange"
              @data-filter="handleDataFilter" />

            <!-- 地图图例 - 浮于右上方 -->
            <div class="map-legend-overlay">
              <div class="legend-header">
                <info-circle-outlined />
                <span class="legend-title">图例</span>
              </div>
              <div class="legend-items">
                <div class="legend-item">
                  <div class="legend-color-marker" style="background-color: #00EE63;"></div>
                  <span class="legend-text">A级 (90分以上)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color-marker" style="background-color: #1A9FFF;"></div>
                  <span class="legend-text">B级 (80-90分)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color-marker" style="background-color: #F8B523;"></div>
                  <span class="legend-text">C级 (70-80分)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color-marker" style="background-color: #FF6C00;"></div>
                  <span class="legend-text">D级 (60-70分)</span>
                </div>
                <div class="legend-item">
                  <div class="legend-color-marker" style="background-color: #FF4042;"></div>
                  <span class="legend-text">E级 (60分以下)</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 右侧数据面板 -->
        <a-col :xs="24" :lg="10" :xl="8">
          <div class="data-panel">
            <!-- 数据过滤状态提示和重置按钮 -->
            <div v-if="dataFilterState.isFiltered" class="data-filter-header">
              <div class="filter-info">
                <info-circle-outlined class="filter-icon" />
                <span class="filter-text">当前显示：{{ dataFilterState.selectedUnit?.name }} 的数据</span>
              </div>
              <a-button size="small" type="primary" ghost @click="resetDataFilter">
                <template #icon><reload-outlined /></template>
                重置为全部数据
              </a-button>
            </div>

            <a-tabs v-model:activeKey="activeDataTab" @change="handleDataTabChange" :style="{ overflowY: 'auto' }">
              <a-tab-pane key="project" tab="项目数据">
                <ProjectDataOverview :municipal-data="projectData?.municipal" :district-data="projectData?.districts"
                  :selected-district="selectedDistrict" @district-select="handleDistrictChange"
                  @drill-down="handleProjectDrillDown" @export="handleDataExport" />
              </a-tab-pane>
              <a-tab-pane key="party" tab="党建指数">
                <PartyBuildingIndex :data="partyBuildingData" :loading="partyBuildingLoading"
                  @agency-click="handleAgencyClick" @data-refresh="refreshPartyBuildingData"
                  @export="handleDataExport" />
              </a-tab-pane>
              <a-tab-pane key="assessment" tab="考核督查">
                <AssessmentSupervision :municipal-data="assessmentData?.municipal"
                  :district-data="assessmentData?.districts" :selected-district="selectedDistrict"
                  @year-select="handleYearSelect" @district-select="handleDistrictChange" @export="handleDataExport" />
              </a-tab-pane>
              <a-tab-pane key="warning" tab="预警监控">
                <WarningMonitor :data="warningData" :loading="warningLoading" @warning-click="handleWarningClick"
                  @data-refresh="refreshWarningData" @export="handleDataExport" />
              </a-tab-pane>
              <a-tab-pane key="navigation" tab="导航">
                <NavigationPanel :unit-locations="unitLocationStore.unitLocations"
                  @start-navigation="handleStartNavigation" @show-route="handleShowRoute"
                  @clear-route="handleClearRoute" />
              </a-tab-pane>
            </a-tabs>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 全局加载状态 -->
    <div v-if="globalLoading" class="global-loading">
      <a-spin size="large" tip="加载数据中..." />
    </div>

    <!-- 错误提示 -->
    <a-modal v-model:visible="errorVisible" title="错误提示" :footer="null" @ok="errorVisible = false">
      <div class="error-content">
        <exclamation-circle-outlined class="error-icon" />
        <div class="error-message">{{ errorMessage }}</div>
        <div class="error-actions">
          <a-button @click="errorVisible = false">关闭</a-button>
          <a-button type="primary" @click="retryOperation">重试</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  HomeOutlined,
  ReloadOutlined,
  DownloadOutlined,
  DownOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  PictureOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import MapContainer from '@/components/map-container/index.vue'
import StatisticsOverview from './components/StatisticsOverview.vue'
import ProjectDataOverview from './components/ProjectDataOverview.vue'
import PartyBuildingIndex from './components/PartyBuildingIndex.vue'
import AssessmentSupervision from './components/AssessmentSupervision.vue'
import WarningMonitor from './components/WarningMonitor.vue'
import NavigationPanel from './components/NavigationPanel.vue'
import LoadingState from './components/LoadingState.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'
import type {
  UnitLocation,
  ProjectData,
  PartyBuildingData,
  AssessmentData,
  WarningData
} from './types'
import type { MapMarker, MapViewport } from '@/components/map-container/types'
import { mockModelAgencyApi } from './api/mockModelAgencyApi'
import { useUnitLocationStore } from '@/store/modules/unit-location'

// 响应式数据
const globalLoading = ref(false)
const statisticsLoading = ref(false)
const partyBuildingLoading = ref(false)
const warningLoading = ref(false)
const errorVisible = ref(false)
const errorMessage = ref('')
const errorType = ref<'network' | 'server' | 'permission' | 'data' | 'generic'>('generic')
const hasError = ref(false)
const selectedDistrict = ref('')
const activeDataTab = ref('project')
const selectedYear = ref(new Date().getFullYear())

// 数据联动状态
const dataFilterState = ref<{
  isFiltered: boolean
  selectedUnit: any
  originalData: {
    projectData: any
    partyBuildingData: any
    assessmentData: any
    warningData: any
  } | null
}>({
  isFiltered: false,
  selectedUnit: null,
  originalData: null
})

// 地图容器引用
const mapContainerRef = ref()

// Store 引用
const unitLocationStore = useUnitLocationStore()

// 数据状态
const districts = ref<string[]>([])
const projectData = ref<{ municipal: ProjectData; districts: ProjectData[] } | null>(null)
const partyBuildingData = ref<PartyBuildingData | null>(null)
const assessmentData = ref<{ municipal: AssessmentData; districts: AssessmentData[] } | null>(null)
const warningData = ref<WarningData | null>(null)

// 地图相关数据
const mapViewport = ref<MapViewport>({
  center: [106.550483, 29.563707], // 重庆市中心坐标
  zoom: 11
})

// 计算属性
const filteredUnitLocations = computed(() => {
  if (!selectedDistrict.value) {
    return unitLocationStore.unitLocations
  }
  return unitLocationStore.unitLocations.filter(unit => unit.district === selectedDistrict.value)
})

// 转换单位位置数据为地图标记格式
const mapMarkers = computed(() => {
  return filteredUnitLocations.value.map(unit => {
    // 转换level格式
    let markerLevel: 'A' | 'B' | 'C' | 'D' | 'E' | undefined
    if (unit.partyBuildingIndex >= 90) markerLevel = 'A'
    else if (unit.partyBuildingIndex >= 80) markerLevel = 'B'
    else if (unit.partyBuildingIndex >= 70) markerLevel = 'C'
    else if (unit.partyBuildingIndex >= 60) markerLevel = 'D'
    else markerLevel = 'E'

    return {
      id: unit.id,
      name: unit.name,
      position: [unit.longitude, unit.latitude] as [number, number],
      type: unit.unitType === 'model' ? 'model' as const : 'unit' as const,
      level: markerLevel,
      score: unit.partyBuildingIndex,
      description: unit.description,
      address: unit.address,
      contactPerson: unit.contactPerson,
      contactPhone: unit.contactPhone
    } as MapMarker
  })
})

// 数据加载方法
const loadUnitLocations = async () => {
  try {
    await unitLocationStore.fetchUnitLocations({
      district: selectedDistrict.value || undefined
    })

    // 提取区县列表
    const districtSet = new Set(unitLocationStore.unitLocations.map((unit) => unit.district))
    districts.value = Array.from(districtSet).sort()
  } catch (error) {
    handleError('加载单位位置数据失败', error)
  }
}

const loadProjectData = async () => {
  try {
    const response = await mockModelAgencyApi.fetchProjectStats({
      district: selectedDistrict.value || undefined,
      year: selectedYear.value
    })
    projectData.value = response.data
  } catch (error) {
    handleError('加载项目数据失败', error)
  }
}

const loadPartyBuildingData = async () => {
  partyBuildingLoading.value = true
  try {
    const response = await mockModelAgencyApi.fetchPartyBuildingData({
      district: selectedDistrict.value || undefined
    })
    partyBuildingData.value = response.data
  } catch (error) {
    handleError('加载党建指数数据失败', error)
  } finally {
    partyBuildingLoading.value = false
  }
}

const loadAssessmentData = async () => {
  try {
    const response = await mockModelAgencyApi.fetchAssessmentData({
      district: selectedDistrict.value || undefined,
      year: selectedYear.value
    })
    assessmentData.value = response.data
  } catch (error) {
    handleError('加载考核督查数据失败', error)
  }
}

const loadWarningData = async () => {
  warningLoading.value = true
  try {
    const response = await mockModelAgencyApi.fetchWarningData({
      district: selectedDistrict.value || undefined
    })
    warningData.value = response.data
  } catch (error) {
    handleError('加载预警监控数据失败', error)
  } finally {
    warningLoading.value = false
  }
}

// 初始化数据加载
const initializeData = async () => {
  globalLoading.value = true
  try {
    await Promise.all([
      loadUnitLocations(),
      loadProjectData(),
      loadPartyBuildingData(),
      loadAssessmentData(),
      loadWarningData()
    ])
    message.success('数据加载完成')
  } catch (error) {
    handleError('初始化数据失败', error)
  } finally {
    globalLoading.value = false
  }
}

// 刷新数据方法
const refreshAllData = async () => {
  await initializeData()
}

const refreshStatisticsData = async () => {
  statisticsLoading.value = true
  try {
    await Promise.all([loadProjectData(), loadPartyBuildingData()])
  } finally {
    statisticsLoading.value = false
  }
}

const refreshPartyBuildingData = async () => {
  await loadPartyBuildingData()
}

const refreshWarningData = async () => {
  await loadWarningData()
}

// 事件处理方法
const handleDistrictChange = (district: string) => {
  // 防止重复触发
  if (selectedDistrict.value === district) {
    return
  }

  selectedDistrict.value = district
  // 注意：这里不需要手动重新加载数据，因为 watch 监听器会自动处理
}

const handleDataTabChange = (key: string) => {
  activeDataTab.value = key
}

const handleYearSelect = (year: number) => {
  selectedYear.value = year
}

const handleUnitClick = (unit: UnitLocation) => {
  message.info(`查看单位：${unit.name}`)
  // 可以在这里实现单位详情查看逻辑
}

const handleMapSearch = (keyword: string) => {
  message.info(`搜索：${keyword}`)
  // 可以在这里实现搜索逻辑
}

// 新的地图事件处理方法
const handleMarkerClick = (marker: MapMarker) => {
  message.info(`查看单位：${marker.name}`)
  // 可以在这里实现单位详情查看逻辑
}

const handleMapClick = (position: [number, number]) => {
  console.log('地图点击位置:', position)
  // 可以在这里实现地图点击逻辑
}

const handlePlaceSelect = (data: any) => {
  message.info(`选择地点：${data.name}`)
  console.log('选择的地点数据:', data)
  // 可以在这里实现地点选择逻辑
}

const handleViewportChange = (viewport: MapViewport) => {
  mapViewport.value = viewport
  console.log('地图视图变化:', viewport)
  // 可以在这里实现视图变化逻辑
}

const handleStatisticsClick = (type: string, data: any) => {
  message.info(`查看统计：${type}`)
  // 可以在这里实现统计详情查看逻辑
}

const handleProjectDrillDown = (type: string, data: any) => {
  message.info(`项目数据钻取：${type}`)
  // 可以在这里实现项目数据钻取逻辑
}

const handleAgencyClick = (agencyId: string) => {
  message.info(`查看机构：${agencyId}`)
  // 可以在这里实现机构详情查看逻辑
}

const handleWarningClick = async (warningId: string) => {
  message.info(`查看预警：${warningId}`)

  try {
    // 可以添加更新预警状态的逻辑
    const response = await mockModelAgencyApi.updateWarningStatus(Number(warningId), 'viewed')
    if (response.data) {
      // 重新加载预警数据
      await loadWarningData()
      console.log('预警状态已更新:', response.data)
    }
  } catch (error) {
    console.error('更新预警状态失败:', error)
  }
}

const handleDataExport = async (data: any) => {
  try {
    const response = await mockModelAgencyApi.exportData(
      'excel',
      data.type || '数据导出',
      { district: selectedDistrict.value, ...data }
    )
    if (response.data) {
      message.success(`数据导出成功！文件名：${response.data.fileName}`)
    }
  } catch (error) {
    message.error('数据导出失败')
    console.error('Export error:', error)
  }
}

const handleExport = async ({ key }: { key: string }) => {
  const exportMap = {
    pdf: 'PDF报告',
    excel: 'Excel数据',
    image: '图片'
  }

  try {
    const response = await mockModelAgencyApi.exportData(
      key as 'pdf' | 'excel' | 'image',
      '模范机关总览看板',
      { district: selectedDistrict.value }
    )
    message.success(`${exportMap[key as keyof typeof exportMap]}导出成功！文件名：${response.data.fileName}`)
  } catch (error) {
    message.error(`${exportMap[key as keyof typeof exportMap]}导出失败`)
  }
}

const handleError = (title: string, error: any) => {
  console.error(title, error)
  hasError.value = true
  errorMessage.value = `${title}: ${error.message || error}`

  // 根据错误类型设置错误类型
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('网络')) {
    errorType.value = 'network'
  } else if (error.code === 'SERVER_ERROR' || error.status >= 500) {
    errorType.value = 'server'
  } else if (error.code === 'PERMISSION_ERROR' || error.status === 403) {
    errorType.value = 'permission'
  } else if (error.code === 'DATA_ERROR') {
    errorType.value = 'data'
  } else {
    errorType.value = 'generic'
  }

  errorVisible.value = true
}

const retryOperation = () => {
  hasError.value = false
  errorVisible.value = false
  refreshAllData()
}

const handleErrorRetry = () => {
  retryOperation()
}

const handleErrorReport = (reportData: any) => {
  console.log('Error report:', reportData)
  message.success('错误报告已提交')
}

// 导航相关事件处理
const handleStartNavigation = (data: any) => {
  console.log('开始导航:', data)
  message.success(`🚀 开始导航：从 ${data.startPoint} 到 ${data.endPoint}`)

  // 可以选择性地切换到地图视图，确保用户能看到导航路线
  // 这里保持当前tab，让用户可以继续使用导航功能
}

const handleShowRoute = async (data: any) => {
  console.log('显示路线:', data)

  try {
    // 通过地图容器引用调用显示路线方法
    if (mapContainerRef.value && mapContainerRef.value.showNavigationRoute) {
      await mapContainerRef.value.showNavigationRoute(
        data.startCoords,
        data.endCoords,
        data.travelMode
      )
      message.success('🗺️ 导航路线已在地图上显示')
    } else {
      // 如果地图组件还没有showNavigationRoute方法，显示提示信息
      message.info('🗺️ 导航路线规划完成，请在地图上查看')

      // 更新地图视图到路线中心点
      const centerPoint = [
        (data.startCoords[0] + data.endCoords[0]) / 2,
        (data.startCoords[1] + data.endCoords[1]) / 2
      ]

      mapViewport.value = {
        center: centerPoint as [number, number],
        zoom: 13
      }
    }
  } catch (error) {
    console.error('显示导航路线失败:', error)
    message.error('显示导航路线失败，请重试')
  }
}

const handleClearRoute = () => {
  console.log('清除导航路线')

  try {
    // 通过地图容器引用调用清除路线方法
    if (mapContainerRef.value && mapContainerRef.value.clearNavigationRoute) {
      mapContainerRef.value.clearNavigationRoute()
    }

    // 重置地图视图到默认中心位置
    mapViewport.value = {
      center: [106.550483, 29.563707], // 重庆市中心坐标
      zoom: 11
    }

    message.success('🗺️ 导航路线已清除，地图已回到中心位置')
  } catch (error) {
    console.error('清除导航路线失败:', error)
    message.error('清除导航路线失败，请重试')
  }
}

// 数据联动处理方法
const handleDataFilter = (filterData: any) => {
  console.log('数据联动事件:', filterData)

  if (filterData.type === 'marker-select') {
    // 保存原始数据（如果还没有保存）
    if (!dataFilterState.value.originalData) {
      dataFilterState.value.originalData = {
        projectData: projectData.value,
        partyBuildingData: partyBuildingData.value,
        assessmentData: assessmentData.value,
        warningData: warningData.value
      }
    }

    // 设置过滤状态
    dataFilterState.value.isFiltered = true
    dataFilterState.value.selectedUnit = filterData.data

    // 过滤各个数据面板的数据，只显示选中单位的相关数据
    filterDataByUnit(filterData.data)

    message.info(`📊 已切换到单位数据视图：${filterData.unitName}`)
  } else if (filterData.type === 'reset') {
    resetDataFilter()
  }
}

const filterDataByUnit = (selectedUnit: any) => {
  // 根据选中的单位过滤各个数据面板的数据
  console.log('开始过滤数据，选中单位:', selectedUnit)

  // 过滤项目数据 - 只显示与选中单位相关的项目
  if (dataFilterState.value.originalData?.projectData) {
    const originalData = dataFilterState.value.originalData.projectData
    const filteredProjectData = {
      municipal: {
        ...originalData.municipal,
        // 根据单位ID或名称过滤项目数据
        totalProjects: originalData.municipal.totalProjects > 0 ? Math.floor(originalData.municipal.totalProjects * 0.1) : 0,
        completedProjects: originalData.municipal.completedProjects > 0 ? Math.floor(originalData.municipal.completedProjects * 0.1) : 0,
        ongoingProjects: originalData.municipal.ongoingProjects > 0 ? Math.floor(originalData.municipal.ongoingProjects * 0.1) : 0,
        plannedProjects: originalData.municipal.plannedProjects > 0 ? Math.floor(originalData.municipal.plannedProjects * 0.1) : 0
      },
      districts: originalData.districts.filter((district: any) =>
        district.name === selectedUnit.name ||
        district.id === selectedUnit.id ||
        selectedUnit.address?.includes(district.name)
      )
    }
    projectData.value = filteredProjectData
  }

  // 过滤党建指数数据 - 只显示选中单位的党建数据
  if (dataFilterState.value.originalData?.partyBuildingData) {
    const originalData = dataFilterState.value.originalData.partyBuildingData
    const filteredPartyData = {
      ...originalData,
      // 模拟过滤：只显示选中单位的党建指数
      overallIndex: selectedUnit.score || originalData.overallIndex,
      agencies: originalData.agencies?.filter((agency: any) =>
        agency.name === selectedUnit.name ||
        agency.id === selectedUnit.id
      ) || [],
      // 更新统计数据
      totalAgencies: 1,
      excellentCount: selectedUnit.level === 'A' ? 1 : 0,
      goodCount: selectedUnit.level === 'B' ? 1 : 0,
      averageCount: selectedUnit.level === 'C' ? 1 : 0,
      belowAverageCount: ['D', 'E'].includes(selectedUnit.level) ? 1 : 0
    }
    partyBuildingData.value = filteredPartyData
  }

  // 过滤考核督查数据 - 只显示选中单位的考核数据
  if (dataFilterState.value.originalData?.assessmentData) {
    const originalData = dataFilterState.value.originalData.assessmentData
    const filteredAssessmentData = {
      municipal: {
        ...originalData.municipal,
        // 根据单位等级调整考核数据
        totalScore: selectedUnit.score || originalData.municipal.totalScore,
        completionRate: selectedUnit.level === 'A' ? 95 : selectedUnit.level === 'B' ? 85 : 75
      },
      districts: originalData.districts.filter((district: any) =>
        district.name === selectedUnit.name ||
        selectedUnit.address?.includes(district.name)
      )
    }
    assessmentData.value = filteredAssessmentData
  }

  // 过滤预警监控数据 - 只显示选中单位的预警信息
  if (dataFilterState.value.originalData?.warningData) {
    const originalData = dataFilterState.value.originalData.warningData
    const filteredWarningData = {
      ...originalData,
      // 根据单位等级显示相应的预警信息
      totalWarnings: selectedUnit.level === 'E' ? 3 : selectedUnit.level === 'D' ? 1 : 0,
      highPriorityWarnings: selectedUnit.level === 'E' ? 1 : 0,
      mediumPriorityWarnings: selectedUnit.level === 'D' ? 1 : 0,
      lowPriorityWarnings: selectedUnit.level === 'E' ? 2 : 0,
      warnings: originalData.warnings?.filter((warning: any) =>
        warning.unitId === selectedUnit.id ||
        warning.unitName === selectedUnit.name ||
        (selectedUnit.level === 'E' && warning.priority === 'high') ||
        (selectedUnit.level === 'D' && warning.priority === 'medium')
      ) || []
    }
    warningData.value = filteredWarningData
  }

  console.log('数据过滤完成')
}

const resetDataFilter = () => {
  if (dataFilterState.value.originalData) {
    // 恢复原始数据
    projectData.value = dataFilterState.value.originalData.projectData
    partyBuildingData.value = dataFilterState.value.originalData.partyBuildingData
    assessmentData.value = dataFilterState.value.originalData.assessmentData
    warningData.value = dataFilterState.value.originalData.warningData
  }

  // 重置过滤状态
  dataFilterState.value.isFiltered = false
  dataFilterState.value.selectedUnit = null
  dataFilterState.value.originalData = null

  message.success('📊 已重置为全部数据视图')
}

// 单位位置管理处理方法
const handleLocationUpdate = async (location: any) => {
  console.log('单位位置更新:', location)

  try {
    const response = await mockModelAgencyApi.updateUnitLocation(location.id, location)
    if (response.data) {
      // 更新本地数据
      await loadUnitLocations()

      // 更新地图上的标记点
      const mapContainer = document.querySelector('.map-container')
      if (mapContainer) {
        const event = new CustomEvent('location-updated', {
          detail: location
        })
        mapContainer.dispatchEvent(event)
      }

      message.success(`✅ 单位"${location.unitName || location.name}"的位置坐标已更新`)
    }
  } catch (error) {
    message.error('更新单位位置失败')
    console.error('Update location error:', error)
  }
}

const handleLocationDelete = async (locationId: string | number) => {
  console.log('删除单位位置:', locationId)

  try {
    const response = await mockModelAgencyApi.deleteUnitLocation(Number(locationId))
    if (response.data) {
      // 更新本地数据
      await loadUnitLocations()

      // 从地图上移除对应的标记点
      const mapContainer = document.querySelector('.map-container')
      if (mapContainer) {
        const event = new CustomEvent('location-deleted', {
          detail: { locationId }
        })
        mapContainer.dispatchEvent(event)
      }

      message.success('🗑️ 单位位置坐标已删除')
    }
  } catch (error) {
    message.error('删除单位位置失败')
    console.error('Delete location error:', error)
  }
}

// 生命周期
onMounted(() => {
  initializeData()
})

// 监听区县变化，更新相关数据
watch(selectedDistrict, async (newDistrict, oldDistrict) => {
  if (newDistrict !== oldDistrict) {
    console.log('District changed to:', newDistrict)

    // 重置数据过滤状态
    if (dataFilterState.value.isFiltered) {
      resetDataFilter()
    }

    // 重新加载所有相关数据
    try {
      globalLoading.value = true
      await Promise.all([
        loadUnitLocations(),
        loadProjectData(),
        loadPartyBuildingData(),
        loadAssessmentData(),
        loadWarningData()
      ])
      message.success(newDistrict ? `已切换到${newDistrict}数据` : '已切换到全部区县数据')
    } catch (error) {
      handleError('切换区县数据失败', error)
    } finally {
      globalLoading.value = false
    }
  }
})
</script>

<style scoped lang="scss">
@import './styles/global.scss';
@import './styles/responsive.scss';
@import './styles/utilities.scss';

.model-agency-dashboard {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 0;

  .dashboard-header {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 100;

    .header-content {
      max-width: 1600px;
      margin: 0 auto;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-section {
        .page-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          line-height: 1.2;
        }

        .page-breadcrumb {
          margin: 0;

          .ant-breadcrumb-link {
            color: #666;

            &:hover {
              color: #1890ff;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 12px;
      }
    }
  }

  .statistics-section {
    max-width: 1600px;
    margin: 0 auto;
    padding: 24px;
    padding-bottom: 0;
  }

  .main-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 24px;
    padding-top: 0;

    .map-section {
      height: 600px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      position: relative;

      // 地图图例浮层样式
      .map-legend-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(8px);
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 160px;
        border: 1px solid rgba(255, 255, 255, 0.2);

        .legend-header {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 8px;
          padding-bottom: 6px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);

          .anticon {
            color: #1890ff;
            font-size: 14px;
          }

          .legend-title {
            font-size: 13px;
            font-weight: 600;
            color: #262626;
          }
        }

        .legend-items {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 2px 0;

            .legend-color-marker {
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 1px solid rgba(255, 255, 255, 0.8);
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
              flex-shrink: 0;
            }

            .legend-text {
              font-size: 12px;
              color: #595959;
              line-height: 1.2;
              white-space: nowrap;
            }
          }
        }

        // 响应式调整
        @media (max-width: 768px) {
          top: 50px;
          right: 8px;
          min-width: 140px;
          padding: 10px;

          .legend-header {
            .legend-title {
              font-size: 12px;
            }
          }

          .legend-items {
            .legend-item {
              .legend-color-marker {
                width: 10px;
                height: 10px;
              }

              .legend-text {
                font-size: 11px;
              }
            }
          }
        }

        @media (max-width: 480px) {
          top: 45px;
          right: 6px;
          min-width: 120px;
          padding: 8px;

          .legend-header {
            margin-bottom: 6px;
            padding-bottom: 4px;

            .anticon {
              font-size: 12px;
            }

            .legend-title {
              font-size: 11px;
            }
          }

          .legend-items {
            gap: 4px;

            .legend-item {
              gap: 6px;

              .legend-color-marker {
                width: 8px;
                height: 8px;
              }

              .legend-text {
                font-size: 10px;
              }
            }
          }
        }
      }
    }

    .data-panel {
      height: 600px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .data-filter-header {
        padding: 12px 16px;
        background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
        border-bottom: 1px solid #d1ecf1;
        display: flex;
        justify-content: space-between;
        align-items: center;
        animation: slideDown 0.3s ease-out;

        .filter-info {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #0c5aa6;
          font-size: 14px;
          font-weight: 500;

          .filter-icon {
            font-size: 16px;
            color: #1890ff;
          }

          .filter-text {
            color: #0c5aa6;
          }
        }

        .ant-btn {
          font-size: 12px;
          height: 28px;
          padding: 0 12px;
          border-radius: 14px;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
          }
        }
      }

      .ant-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;

        .ant-tabs-nav {
          margin: 0;
          padding: 0 16px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-bottom: 1px solid #dee2e6;
          position: relative;

          .ant-tabs-nav-wrap {
            .ant-tabs-nav-list {
              .ant-tabs-tab {
                padding: 14px 20px;
                font-weight: 500;
                font-size: 14px;
                color: #6c757d;
                border: none;
                border-radius: 8px 8px 0 0;
                margin-right: 4px;
                position: relative;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                background: transparent;

                &::before {
                  content: '';
                  position: absolute;
                  bottom: 0;
                  left: 50%;
                  width: 0;
                  height: 3px;
                  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
                  border-radius: 2px 2px 0 0;
                  transform: translateX(-50%);
                  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                &:hover {
                  color: #1890ff;
                  background: rgba(24, 144, 255, 0.04);
                  transform: translateY(-1px);

                  &::before {
                    width: 60%;
                  }
                }

                &.ant-tabs-tab-active {
                  color: #1890ff;
                  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
                  border-bottom-color: transparent;
                  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
                  transform: translateY(-2px);

                  &::before {
                    width: 100%;
                    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 50%, #1890ff 100%);
                    box-shadow: 0 0 8px rgba(24, 144, 255, 0.4);
                  }

                  &::after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
                  }
                }

                .ant-tabs-tab-btn {
                  font-weight: inherit;
                  color: inherit;
                  position: relative;
                  z-index: 1;
                }
              }
            }

            .ant-tabs-ink-bar {
              display: none; // 隐藏默认的滑动条，使用自定义的
            }
          }
        }

        .ant-tabs-content-holder {
          flex: 1;
          overflow: hidden;

          .ant-tabs-content {
            height: 100%;

            .ant-tabs-tabpane {
              height: 100%;
              overflow-y: auto;
              padding: 0;

              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f1f1;
              }

              &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;

                &:hover {
                  background: #a8a8a8;
                }
              }
            }
          }
        }
      }
    }
  }

  .global-loading {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .error-content {
    text-align: center;
    padding: 20px;

    .error-icon {
      font-size: 48px;
      color: #ff4d4f;
      margin-bottom: 16px;
    }

    .error-message {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
      line-height: 1.5;
    }

    .error-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .model-agency-dashboard {
    .dashboard-header {
      .header-content {
        padding: 12px 16px;

        .title-section {
          .page-title {
            font-size: 20px;
          }
        }

        .header-actions {
          .ant-space {
            flex-wrap: wrap;
          }
        }
      }
    }

    .statistics-section,
    .main-content {
      padding: 16px;
    }

    .main-content {

      .map-section,
      .data-panel {
        height: 500px;
      }
    }
  }
}

@media (max-width: 768px) {
  .model-agency-dashboard {
    .dashboard-header {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .title-section {
          .page-title {
            font-size: 18px;
          }
        }

        .header-actions {
          width: 100%;
          justify-content: flex-start;

          .ant-select {
            width: 150px !important;
          }
        }
      }
    }

    .statistics-section,
    .main-content {
      padding: 12px;
    }

    .main-content {
      .ant-row {
        flex-direction: column;
      }

      .ant-col {
        width: 100% !important;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .map-section {
        height: 400px;
      }

      .data-panel {
        height: 500px;

        .ant-tabs {
          .ant-tabs-nav {
            padding: 0 12px;

            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  padding: 10px 14px;
                  font-size: 13px;
                  margin-right: 2px;

                  &::before {
                    height: 2px;
                  }

                  &:hover {
                    transform: translateY(-0.5px);
                  }

                  &.ant-tabs-tab-active {
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .model-agency-dashboard {
    .dashboard-header {
      .header-content {
        padding: 8px 12px;

        .title-section {
          .page-title {
            font-size: 16px;
          }

          .page-breadcrumb {
            font-size: 12px;
          }
        }

        .header-actions {
          .ant-space {
            .ant-space-item {
              .ant-btn {
                font-size: 12px;
                padding: 4px 8px;
              }

              .ant-select {
                width: 120px !important;
              }
            }
          }
        }
      }
    }

    .statistics-section,
    .main-content {
      padding: 8px;
    }

    .main-content {
      .map-section {
        height: 300px;
      }

      .data-panel {
        height: 400px;

        .ant-tabs {
          .ant-tabs-nav {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  padding: 8px 10px;
                  font-size: 12px;
                  margin-right: 1px;

                  &::before {
                    height: 2px;
                  }

                  &:hover {
                    transform: translateY(-0.5px);
                  }

                  &.ant-tabs-tab-active {
                    transform: translateY(-1px);
                    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.15);
                  }
                }
              }
            }
          }
        }
      }
    }

    .error-content {
      padding: 16px;

      .error-icon {
        font-size: 36px;
      }

      .error-message {
        font-size: 14px;
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.model-agency-dashboard {

  .statistics-section,
  .main-content {
    animation: fadeIn 0.6s ease-out;
  }

  .data-panel {
    .ant-tabs-tabpane {
      animation: fadeIn 0.3s ease-out;
    }
  }
}

// 打印样式
@media print {
  .model-agency-dashboard {
    .dashboard-header {
      .header-actions {
        display: none;
      }
    }

    .global-loading {
      display: none;
    }

    .data-panel {
      .ant-tabs-nav {
        display: none;
      }

      .ant-tabs-content-holder {
        .ant-tabs-content {
          .ant-tabs-tabpane {
            display: block !important;
            height: auto !important;
            overflow: visible !important;
          }
        }
      }
    }
  }
}
</style>
