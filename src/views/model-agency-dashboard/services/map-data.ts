// 地图数据服务
import type { OrganUnit, DistrictStats, MunicipalStats, MapSearchResult } from '../types/map'

// 模拟机关单位数据
export const mockOrganUnits: OrganUnit[] = [
    {
        id: '001',
        name: '重庆市委组织部',
        code: 'CQSWZZB',
        type: 'municipal',
        category: 'party',
        district: '渝中区',
        address: '重庆市渝中区人民路232号',
        longitude: 106.5516,
        latitude: 29.5630,
        contactPerson: '张三',
        contactPhone: '023-12345678',
        description: '负责全市组织工作',
        partyBuildingIndex: 95.5,
        partyBuildingLevel: 'A',
        isModelOrgan: true,
        projectData: {
            totalProjects: 15,
            completedProjects: 12,
            applicationUnits: 8,
            cultivationUnits: 5,
            benchmarkUnits: 2,
            applicationRatio: 53.3,
            cultivationRatio: 33.3,
            benchmarkRatio: 13.3,
            totalIndicators: 45,
            smartFilterIndicators: 38,
            dataResources: 120,
            dataSourceUnits: 25
        },
        assessmentData: {
            currentScore: 95.5,
            currentLevel: 'excellent',
            historicalScores: [
                { year: 2023, score: 95.5, level: 'excellent' },
                { year: 2022, score: 92.3, level: 'excellent' },
                { year: 2021, score: 89.8, level: 'good' }
            ],
            supervisionLists: 2,
            completedSupervision: 2,
            supervisionCompletionRate: 100
        },
        warningData: {
            warningItems: 0,
            highestWarningProject: '',
            highestWarningUnits: 0,
            totalProblems: 1,
            rectificationRate: 100,
            focusProblems: 0,
            majorProblems: 0,
            generalProblems: 1,
            rectificationInProgress: 0,
            rectificationCompleted: 1,
            overdueRectification: 0
        }
    },
    {
        id: '002',
        name: '重庆市发展和改革委员会',
        code: 'CQSFZGGWYH',
        type: 'municipal',
        category: 'government',
        district: '渝中区',
        address: '重庆市渝中区中山四路36号',
        longitude: 106.5580,
        latitude: 29.5650,
        contactPerson: '李四',
        contactPhone: '023-87654321',
        description: '负责全市发展改革工作',
        partyBuildingIndex: 88.2,
        partyBuildingLevel: 'B',
        isModelOrgan: false,
        projectData: {
            totalProjects: 12,
            completedProjects: 10,
            applicationUnits: 6,
            cultivationUnits: 4,
            benchmarkUnits: 2,
            applicationRatio: 50.0,
            cultivationRatio: 33.3,
            benchmarkRatio: 16.7,
            totalIndicators: 38,
            smartFilterIndicators: 32,
            dataResources: 95,
            dataSourceUnits: 18
        },
        assessmentData: {
            currentScore: 88.2,
            currentLevel: 'good',
            historicalScores: [
                { year: 2023, score: 88.2, level: 'good' },
                { year: 2022, score: 85.7, level: 'good' },
                { year: 2021, score: 82.4, level: 'good' }
            ],
            supervisionLists: 3,
            completedSupervision: 2,
            supervisionCompletionRate: 66.7
        },
        warningData: {
            warningItems: 2,
            highestWarningProject: '项目进度预警',
            highestWarningUnits: 1,
            totalProblems: 3,
            rectificationRate: 66.7,
            focusProblems: 1,
            majorProblems: 1,
            generalProblems: 1,
            rectificationInProgress: 1,
            rectificationCompleted: 2,
            overdueRectification: 0
        }
    },
    {
        id: '003',
        name: '江北区委组织部',
        code: 'JBQWZZB',
        type: 'district',
        category: 'party',
        district: '江北区',
        address: '重庆市江北区建新东路3号',
        longitude: 106.5740,
        latitude: 29.6060,
        contactPerson: '王五',
        contactPhone: '023-67890123',
        description: '负责江北区组织工作',
        partyBuildingIndex: 92.8,
        partyBuildingLevel: 'A',
        isModelOrgan: true,
        projectData: {
            totalProjects: 8,
            completedProjects: 7,
            applicationUnits: 4,
            cultivationUnits: 3,
            benchmarkUnits: 1,
            applicationRatio: 50.0,
            cultivationRatio: 37.5,
            benchmarkRatio: 12.5,
            totalIndicators: 28,
            smartFilterIndicators: 24,
            dataResources: 68,
            dataSourceUnits: 12
        },
        assessmentData: {
            currentScore: 92.8,
            currentLevel: 'excellent',
            historicalScores: [
                { year: 2023, score: 92.8, level: 'excellent' },
                { year: 2022, score: 90.1, level: 'excellent' },
                { year: 2021, score: 87.5, level: 'good' }
            ],
            supervisionLists: 1,
            completedSupervision: 1,
            supervisionCompletionRate: 100
        },
        warningData: {
            warningItems: 1,
            highestWarningProject: '数据质量预警',
            highestWarningUnits: 1,
            totalProblems: 2,
            rectificationRate: 50,
            focusProblems: 0,
            majorProblems: 1,
            generalProblems: 1,
            rectificationInProgress: 1,
            rectificationCompleted: 1,
            overdueRectification: 0
        }
    },
    {
        id: '004',
        name: '沙坪坝区政府办公室',
        code: 'SPBQZFBGS',
        type: 'district',
        category: 'government',
        district: '沙坪坝区',
        address: '重庆市沙坪坝区凤天大道8号',
        longitude: 106.4570,
        latitude: 29.5410,
        contactPerson: '赵六',
        contactPhone: '023-65432109',
        description: '负责沙坪坝区政府日常工作',
        partyBuildingIndex: 76.5,
        partyBuildingLevel: 'C',
        isModelOrgan: false,
        projectData: {
            totalProjects: 6,
            completedProjects: 4,
            applicationUnits: 3,
            cultivationUnits: 2,
            benchmarkUnits: 1,
            applicationRatio: 50.0,
            cultivationRatio: 33.3,
            benchmarkRatio: 16.7,
            totalIndicators: 22,
            smartFilterIndicators: 18,
            dataResources: 45,
            dataSourceUnits: 8
        },
        assessmentData: {
            currentScore: 76.5,
            currentLevel: 'average',
            historicalScores: [
                { year: 2023, score: 76.5, level: 'average' },
                { year: 2022, score: 74.2, level: 'average' },
                { year: 2021, score: 71.8, level: 'average' }
            ],
            supervisionLists: 4,
            completedSupervision: 3,
            supervisionCompletionRate: 75
        },
        warningData: {
            warningItems: 3,
            highestWarningProject: '考核指标预警',
            highestWarningUnits: 2,
            totalProblems: 5,
            rectificationRate: 60,
            focusProblems: 1,
            majorProblems: 2,
            generalProblems: 2,
            rectificationInProgress: 2,
            rectificationCompleted: 3,
            overdueRectification: 0
        }
    },
    {
        id: '005',
        name: '渝北区财政局',
        code: 'YBQCZJ',
        type: 'district',
        category: 'government',
        district: '渝北区',
        address: '重庆市渝北区双龙湖街道龙山大道298号',
        longitude: 106.6310,
        latitude: 29.7180,
        contactPerson: '孙七',
        contactPhone: '023-67123456',
        description: '负责渝北区财政管理工作',
        partyBuildingIndex: 84.3,
        partyBuildingLevel: 'B',
        isModelOrgan: false,
        projectData: {
            totalProjects: 10,
            completedProjects: 8,
            applicationUnits: 5,
            cultivationUnits: 3,
            benchmarkUnits: 2,
            applicationRatio: 50.0,
            cultivationRatio: 30.0,
            benchmarkRatio: 20.0,
            totalIndicators: 32,
            smartFilterIndicators: 28,
            dataResources: 78,
            dataSourceUnits: 15
        },
        assessmentData: {
            currentScore: 84.3,
            currentLevel: 'good',
            historicalScores: [
                { year: 2023, score: 84.3, level: 'good' },
                { year: 2022, score: 81.9, level: 'good' },
                { year: 2021, score: 79.6, level: 'average' }
            ],
            supervisionLists: 2,
            completedSupervision: 2,
            supervisionCompletionRate: 100
        },
        warningData: {
            warningItems: 1,
            highestWarningProject: '预算执行预警',
            highestWarningUnits: 1,
            totalProblems: 2,
            rectificationRate: 100,
            focusProblems: 0,
            majorProblems: 1,
            generalProblems: 1,
            rectificationInProgress: 0,
            rectificationCompleted: 2,
            overdueRectification: 0
        }
    }
]

// 模拟区县统计数据
export const mockDistrictStats: DistrictStats[] = [
    {
        districtName: '渝中区',
        districtCode: '500103',
        totalUnits: 45,
        totalPartyOrganizations: 38,
        partyBuildingStats: {
            averageIndex: 89.2,
            levelACount: 15,
            levelBCount: 18,
            levelCCount: 8,
            levelDCount: 3,
            levelECount: 1,
            modelOrganCount: 12,
            modelOrganRatio: 26.7
        },
        projectStats: {
            totalProjects: 180,
            completedProjects: 145,
            applicationUnits: 85,
            cultivationUnits: 45,
            benchmarkUnits: 15,
            applicationRatio: 47.2,
            cultivationRatio: 25.0,
            benchmarkRatio: 8.3,
            totalIndicators: 520,
            smartFilterIndicators: 445,
            dataResources: 1250,
            dataSourceUnits: 180
        },
        assessmentStats: {
            averageScore: 87.5,
            excellentCount: 18,
            goodCount: 20,
            averageCount: 5,
            poorCount: 2,
            supervisionLists: 25,
            completedSupervision: 22,
            supervisionCompletionRate: 88
        },
        warningStats: {
            totalWarningItems: 15,
            highestWarningProject: '党建指数预警',
            highestWarningUnits: 8,
            totalProblems: 28,
            rectificationRate: 85.7,
            focusProblems: 3,
            majorProblems: 8,
            generalProblems: 17
        }
    },
    {
        districtName: '江北区',
        districtCode: '500105',
        totalUnits: 52,
        totalPartyOrganizations: 44,
        partyBuildingStats: {
            averageIndex: 85.8,
            levelACount: 12,
            levelBCount: 22,
            levelCCount: 12,
            levelDCount: 4,
            levelECount: 2,
            modelOrganCount: 10,
            modelOrganRatio: 19.2
        },
        projectStats: {
            totalProjects: 165,
            completedProjects: 132,
            applicationUnits: 78,
            cultivationUnits: 42,
            benchmarkUnits: 12,
            applicationRatio: 47.3,
            cultivationRatio: 25.5,
            benchmarkRatio: 7.3,
            totalIndicators: 485,
            smartFilterIndicators: 412,
            dataResources: 1180,
            dataSourceUnits: 165
        },
        assessmentStats: {
            averageScore: 84.2,
            excellentCount: 15,
            goodCount: 25,
            averageCount: 8,
            poorCount: 4,
            supervisionLists: 32,
            completedSupervision: 28,
            supervisionCompletionRate: 87.5
        },
        warningStats: {
            totalWarningItems: 22,
            highestWarningProject: '考核指标预警',
            highestWarningUnits: 12,
            totalProblems: 38,
            rectificationRate: 78.9,
            focusProblems: 5,
            majorProblems: 12,
            generalProblems: 21
        }
    },
    {
        districtName: '沙坪坝区',
        districtCode: '500106',
        totalUnits: 48,
        totalPartyOrganizations: 41,
        partyBuildingStats: {
            averageIndex: 82.1,
            levelACount: 8,
            levelBCount: 20,
            levelCCount: 15,
            levelDCount: 4,
            levelECount: 1,
            modelOrganCount: 6,
            modelOrganRatio: 12.5
        },
        projectStats: {
            totalProjects: 145,
            completedProjects: 115,
            applicationUnits: 68,
            cultivationUnits: 38,
            benchmarkUnits: 9,
            applicationRatio: 46.9,
            cultivationRatio: 26.2,
            benchmarkRatio: 6.2,
            totalIndicators: 425,
            smartFilterIndicators: 358,
            dataResources: 980,
            dataSourceUnits: 145
        },
        assessmentStats: {
            averageScore: 81.8,
            excellentCount: 10,
            goodCount: 22,
            averageCount: 12,
            poorCount: 4,
            supervisionLists: 38,
            completedSupervision: 32,
            supervisionCompletionRate: 84.2
        },
        warningStats: {
            totalWarningItems: 28,
            highestWarningProject: '项目进度预警',
            highestWarningUnits: 15,
            totalProblems: 45,
            rectificationRate: 73.3,
            focusProblems: 8,
            majorProblems: 15,
            generalProblems: 22
        }
    }
]

// 模拟市级统计数据
export const mockMunicipalStats: MunicipalStats = {
    totalMunicipalUnits: 156,
    totalMunicipalPartyOrganizations: 132,
    municipalProjectStats: {
        totalProjects: 520,
        completedProjects: 425,
        applicationUnits: 245,
        cultivationUnits: 135,
        benchmarkUnits: 45,
        applicationRatio: 47.1,
        cultivationRatio: 26.0,
        benchmarkRatio: 8.7,
        totalIndicators: 1580,
        smartFilterIndicators: 1345,
        dataResources: 3850,
        dataSourceUnits: 520
    },
    municipalPartyBuildingStats: {
        levelACount: 42,
        levelBCount: 68,
        levelCCount: 32,
        levelDCount: 10,
        levelECount: 4
    },
    municipalAssessmentStats: {
        excellentCount: 48,
        goodCount: 72,
        averageCount: 28,
        poorCount: 8,
        supervisionLists: 125,
        completedSupervision: 108,
        supervisionCompletionRate: 86.4
    },
    municipalWarningStats: {
        totalWarningItems: 85,
        highestWarningProject: '党建指数综合预警',
        highestWarningUnits: 35
    },
    districtStats: mockDistrictStats
}

// 地图数据服务类
export class MapDataService {
    // 获取所有机关单位数据
    static async getOrganUnits(): Promise<OrganUnit[]> {
        // 模拟异步请求
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(mockOrganUnits)
            }, 500)
        })
    }

    // 根据区县获取机关单位
    static async getOrganUnitsByDistrict(district: string): Promise<OrganUnit[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const units = mockOrganUnits.filter(unit => unit.district === district)
                resolve(units)
            }, 300)
        })
    }

    // 获取区县统计数据
    static async getDistrictStats(): Promise<DistrictStats[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(mockDistrictStats)
            }, 400)
        })
    }

    // 获取单个区县统计数据
    static async getDistrictStatsByName(districtName: string): Promise<DistrictStats | null> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const stats = mockDistrictStats.find(stat => stat.districtName === districtName)
                resolve(stats || null)
            }, 300)
        })
    }

    // 获取市级统计数据
    static async getMunicipalStats(): Promise<MunicipalStats> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(mockMunicipalStats)
            }, 400)
        })
    }

    // 搜索机关单位
    static async searchUnits(keyword: string): Promise<MapSearchResult[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const results: MapSearchResult[] = []

                // 搜索单位名称
                mockOrganUnits.forEach(unit => {
                    if (unit.name.includes(keyword) || unit.address.includes(keyword)) {
                        results.push({
                            id: unit.id,
                            name: unit.name,
                            type: 'unit',
                            coordinates: [unit.longitude, unit.latitude],
                            address: unit.address,
                            district: unit.district
                        })
                    }
                })

                // 搜索区县
                mockDistrictStats.forEach(district => {
                    if (district.districtName.includes(keyword)) {
                        results.push({
                            id: district.districtCode,
                            name: district.districtName,
                            type: 'district',
                            coordinates: [106.5516, 29.5630], // 默认坐标
                            address: district.districtName
                        })
                    }
                })

                resolve(results)
            }, 200)
        })
    }

    // 根据党建指数等级获取单位
    static async getUnitsByPartyBuildingLevel(level: 'A' | 'B' | 'C' | 'D' | 'E'): Promise<OrganUnit[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const units = mockOrganUnits.filter(unit => unit.partyBuildingLevel === level)
                resolve(units)
            }, 300)
        })
    }

    // 获取模范机关单位
    static async getModelOrgans(): Promise<OrganUnit[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const units = mockOrganUnits.filter(unit => unit.isModelOrgan)
                resolve(units)
            }, 300)
        })
    }

    // 根据单位类型获取单位
    static async getUnitsByType(type: 'municipal' | 'district' | 'county'): Promise<OrganUnit[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const units = mockOrganUnits.filter(unit => unit.type === type)
                resolve(units)
            }, 300)
        })
    }

    // 获取预警单位
    static async getWarningUnits(): Promise<OrganUnit[]> {
        return new Promise((resolve) => {
            setTimeout(() => {
                const units = mockOrganUnits.filter(unit => unit.warningData.warningItems > 0)
                resolve(units)
            }, 300)
        })
    }
}