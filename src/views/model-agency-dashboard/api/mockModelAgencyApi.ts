/**
 * 模范机关总览看板模拟API服务
 * 提供所有数据交互的假接口，方便后续真实接口入场
 */

// 模拟网络延迟
const mockDelay = (min = 300, max = 2000) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min
    return new Promise(resolve => setTimeout(resolve, delay))
}

// 统一响应格式
interface ApiResponse<T = any> {
    code: number
    message: string
    data: T
    timestamp: number
}

const createResponse = <T>(data: T, message = '操作成功'): ApiResponse<T> => ({
    code: 200,
    message,
    data,
    timestamp: Date.now()
})

const createErrorResponse = (message = '操作失败', code = 500): ApiResponse<null> => ({
    code,
    message,
    data: null,
    timestamp: Date.now()
})

// 模拟数据生成器
const generateMockData = {
    // 生成单位位置数据
    unitLocations: () => {
        const districts = ['渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区', '渝北区', '巴南区', '北碚区', '綦江区']
        const unitTypes = ['government', 'enterprise', 'institution', 'model']
        const locations = []

        for (let i = 1; i <= 50; i++) {
            locations.push({
                id: i,
                name: `${districts[Math.floor(Math.random() * districts.length)]}${['机关事务管理局', '行政服务中心', '政务服务中心', '民政局', '城市管理局', '社会保障局', '公共资源交易中心', '行政审批局', '市场监督管理局', '税务局'][Math.floor(Math.random() * 10)]}`,
                district: districts[Math.floor(Math.random() * districts.length)],
                address: `重庆市${districts[Math.floor(Math.random() * districts.length)]}某某街道${i}号`,
                longitude: 106.550483 + (Math.random() - 0.5) * 0.5,
                latitude: 29.563707 + (Math.random() - 0.5) * 0.3,
                unitType: unitTypes[Math.floor(Math.random() * unitTypes.length)],
                partyBuildingIndex: Math.floor(Math.random() * 40) + 60, // 60-100分
                description: `这是一个模范机关单位，致力于党建工作和服务民生。`,
                contactPerson: `联系人${i}`,
                contactPhone: `138${String(Math.floor(Math.random() * 100000000)).padStart(8, '0')}`,
                establishedDate: new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString(),
                lastUpdated: new Date().toISOString()
            })
        }
        return locations
    },

    // 生成项目统计数据
    projectStats: () => ({
        municipal: {
            totalProjects: 156,
            completedProjects: 89,
            ongoingProjects: 45,
            plannedProjects: 22,
            totalInvestment: 2.8e8,
            completedInvestment: 1.6e8,
            completionRate: 57.1,
            averageScore: 87.3
        },
        districts: [
            { name: '渝中区', totalProjects: 18, completedProjects: 12, ongoingProjects: 4, plannedProjects: 2, completionRate: 66.7, averageScore: 89.2 },
            { name: '江北区', totalProjects: 16, completedProjects: 10, ongoingProjects: 4, plannedProjects: 2, completionRate: 62.5, averageScore: 88.1 },
            { name: '南岸区', totalProjects: 15, completedProjects: 9, ongoingProjects: 4, plannedProjects: 2, completionRate: 60.0, averageScore: 86.8 },
            { name: '九龙坡区', totalProjects: 17, completedProjects: 8, ongoingProjects: 6, plannedProjects: 3, completionRate: 47.1, averageScore: 85.4 },
            { name: '沙坪坝区', totalProjects: 14, completedProjects: 9, ongoingProjects: 3, plannedProjects: 2, completionRate: 64.3, averageScore: 87.9 }
        ]
    }),

    // 生成党建指数数据
    partyBuildingData: () => ({
        overallIndex: 87.5,
        totalAgencies: 45,
        excellentCount: 12,
        goodCount: 18,
        averageCount: 10,
        belowAverageCount: 5,
        monthlyTrend: [
            { month: '1月', index: 84.2 },
            { month: '2月', index: 85.1 },
            { month: '3月', index: 86.3 },
            { month: '4月', index: 87.0 },
            { month: '5月', index: 87.5 },
            { month: '6月', index: 88.2 }
        ],
        agencies: Array.from({ length: 45 }, (_, i) => ({
            id: i + 1,
            name: `机关单位${i + 1}`,
            index: Math.floor(Math.random() * 40) + 60,
            level: ['A', 'B', 'C', 'D', 'E'][Math.floor(Math.random() * 5)],
            district: ['渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区'][Math.floor(Math.random() * 5)],
            lastAssessment: new Date(2024, Math.floor(Math.random() * 6), Math.floor(Math.random() * 28) + 1).toISOString()
        }))
    }),

    // 生成考核督查数据
    assessmentData: () => ({
        municipal: {
            totalScore: 89.3,
            completionRate: 94.2,
            totalTasks: 128,
            completedTasks: 108,
            ongoingTasks: 15,
            overdueTasks: 5,
            averageCompletionTime: 12.5
        },
        districts: [
            { name: '渝中区', totalScore: 92.1, completionRate: 96.8, totalTasks: 15, completedTasks: 14, ongoingTasks: 1, overdueTasks: 0 },
            { name: '江北区', totalScore: 90.5, completionRate: 95.2, totalTasks: 14, completedTasks: 13, ongoingTasks: 1, overdueTasks: 0 },
            { name: '南岸区', totalScore: 88.7, completionRate: 93.1, totalTasks: 13, completedTasks: 12, ongoingTasks: 1, overdueTasks: 0 },
            { name: '九龙坡区', totalScore: 87.2, completionRate: 91.4, totalTasks: 16, completedTasks: 14, ongoingTasks: 2, overdueTasks: 0 },
            { name: '沙坪坝区', totalScore: 89.8, completionRate: 94.7, totalTasks: 12, completedTasks: 11, ongoingTasks: 1, overdueTasks: 0 }
        ]
    }),

    // 生成预警监控数据
    warningData: () => ({
        totalWarnings: 23,
        highPriorityWarnings: 5,
        mediumPriorityWarnings: 12,
        lowPriorityWarnings: 6,
        resolvedWarnings: 156,
        resolutionRate: 87.1,
        warnings: [
            {
                id: 1,
                title: '党建指数连续下降',
                description: '某单位党建指数连续3个月下降，需要重点关注',
                priority: 'high',
                unitId: 15,
                unitName: '渝中区机关事务管理局',
                district: '渝中区',
                createTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'pending'
            },
            {
                id: 2,
                title: '考核任务逾期',
                description: '有3项考核任务已逾期未完成',
                priority: 'medium',
                unitId: 23,
                unitName: '江北区行政服务中心',
                district: '江北区',
                createTime: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                status: 'processing'
            }
        ]
    })
}

// API接口实现
export const mockModelAgencyApi = {
    // 获取单位位置数据
    async fetchUnitLocations(params?: { district?: string; unitType?: string }) {
        await mockDelay()
        try {
            let locations = generateMockData.unitLocations()

            // 根据参数过滤
            if (params?.district) {
                locations = locations.filter(loc => loc.district === params.district)
            }
            if (params?.unitType) {
                locations = locations.filter(loc => loc.unitType === params.unitType)
            }

            return createResponse(locations, '获取单位位置数据成功')
        } catch (error) {
            return createErrorResponse('获取单位位置数据失败')
        }
    },

    // 获取项目统计数据
    async fetchProjectStats(params?: { district?: string; year?: number }) {
        await mockDelay()
        try {
            const data = generateMockData.projectStats()
            return createResponse(data, '获取项目统计数据成功')
        } catch (error) {
            return createErrorResponse('获取项目统计数据失败')
        }
    },

    // 获取党建指数数据
    async fetchPartyBuildingData(params?: { district?: string; dateRange?: [string, string] }) {
        await mockDelay()
        try {
            const data = generateMockData.partyBuildingData()
            return createResponse(data, '获取党建指数数据成功')
        } catch (error) {
            return createErrorResponse('获取党建指数数据失败')
        }
    },

    // 获取考核督查数据
    async fetchAssessmentData(params?: { district?: string; year?: number }) {
        await mockDelay()
        try {
            const data = generateMockData.assessmentData()
            return createResponse(data, '获取考核督查数据成功')
        } catch (error) {
            return createErrorResponse('获取考核督查数据失败')
        }
    },

    // 获取预警监控数据
    async fetchWarningData(params?: { priority?: string; status?: string; district?: string }) {
        await mockDelay()
        try {
            let data = generateMockData.warningData()

            // 根据参数过滤预警数据
            if (params?.priority) {
                data.warnings = data.warnings.filter(warning => warning.priority === params.priority)
            }
            if (params?.status) {
                data.warnings = data.warnings.filter(warning => warning.status === params.status)
            }
            if (params?.district) {
                data.warnings = data.warnings.filter(warning => warning.district === params.district)
            }

            return createResponse(data, '获取预警监控数据成功')
        } catch (error) {
            return createErrorResponse('获取预警监控数据失败')
        }
    },

    // ==================== 单位位置管理 CRUD 接口 ====================

    // 创建单位位置
    async createUnitLocation(locationData: any) {
        await mockDelay()
        try {
            const newLocation = {
                id: Date.now(),
                ...locationData,
                establishedDate: new Date().toISOString(),
                lastUpdated: new Date().toISOString(),
                // 如果没有提供经纬度，生成随机坐标（重庆市范围内）
                longitude: locationData.longitude || (106.550483 + (Math.random() - 0.5) * 0.5),
                latitude: locationData.latitude || (29.563707 + (Math.random() - 0.5) * 0.3),
                // 如果没有提供党建指数，生成随机分数
                partyBuildingIndex: locationData.partyBuildingIndex || (Math.floor(Math.random() * 40) + 60)
            }
            return createResponse(newLocation, '创建单位位置成功')
        } catch (error) {
            return createErrorResponse('创建单位位置失败')
        }
    },

    // 获取单个单位位置详情
    async getUnitLocationById(locationId: number) {
        await mockDelay()
        try {
            const locations = generateMockData.unitLocations()
            const location = locations.find(loc => loc.id === locationId)

            if (!location) {
                return createErrorResponse('单位位置不存在', 404)
            }

            return createResponse(location, '获取单位位置详情成功')
        } catch (error) {
            return createErrorResponse('获取单位位置详情失败')
        }
    },

    // 更新单位位置
    async updateUnitLocation(locationId: number, locationData: any) {
        await mockDelay()
        try {
            const updatedLocation = {
                id: locationId,
                ...locationData,
                lastUpdated: new Date().toISOString()
            }
            return createResponse(updatedLocation, '更新单位位置成功')
        } catch (error) {
            return createErrorResponse('更新单位位置失败')
        }
    },

    // 删除单位位置
    async deleteUnitLocation(locationId: number) {
        await mockDelay()
        try {
            return createResponse({ id: locationId, deleted: true }, '删除单位位置成功')
        } catch (error) {
            return createErrorResponse('删除单位位置失败')
        }
    },

    // 批量删除单位位置
    async batchDeleteUnitLocations(locationIds: number[]) {
        await mockDelay(800, 2000)
        try {
            const deletedLocations = locationIds.map(id => ({ id, deleted: true }))
            return createResponse(deletedLocations, `批量删除${locationIds.length}个单位位置成功`)
        } catch (error) {
            return createErrorResponse('批量删除单位位置失败')
        }
    },

    // 批量更新单位位置
    async batchUpdateUnitLocations(updates: Array<{ id: number; data: any }>) {
        await mockDelay(1000, 2500)
        try {
            const updatedLocations = updates.map(update => ({
                id: update.id,
                ...update.data,
                lastUpdated: new Date().toISOString()
            }))
            return createResponse(updatedLocations, `批量更新${updates.length}个单位位置成功`)
        } catch (error) {
            return createErrorResponse('批量更新单位位置失败')
        }
    },

    // 搜索单位位置
    async searchUnitLocations(params: {
        keyword?: string
        district?: string
        unitType?: string
        partyBuildingIndexRange?: [number, number]
        page?: number
        pageSize?: number
    }) {
        await mockDelay()
        try {
            let locations = generateMockData.unitLocations()

            // 关键词搜索
            if (params.keyword) {
                const keyword = params.keyword.toLowerCase()
                locations = locations.filter(loc =>
                    loc.name.toLowerCase().includes(keyword) ||
                    loc.address.toLowerCase().includes(keyword) ||
                    loc.contactPerson?.toLowerCase().includes(keyword)
                )
            }

            // 区县过滤
            if (params.district) {
                locations = locations.filter(loc => loc.district === params.district)
            }

            // 单位类型过滤
            if (params.unitType) {
                locations = locations.filter(loc => loc.unitType === params.unitType)
            }

            // 党建指数范围过滤
            if (params.partyBuildingIndexRange) {
                const [min, max] = params.partyBuildingIndexRange
                locations = locations.filter(loc =>
                    loc.partyBuildingIndex >= min && loc.partyBuildingIndex <= max
                )
            }

            // 分页处理
            const page = params.page || 1
            const pageSize = params.pageSize || 20
            const total = locations.length
            const startIndex = (page - 1) * pageSize
            const endIndex = startIndex + pageSize
            const paginatedLocations = locations.slice(startIndex, endIndex)

            const result = {
                list: paginatedLocations,
                pagination: {
                    current: page,
                    pageSize,
                    total,
                    totalPages: Math.ceil(total / pageSize)
                }
            }

            return createResponse(result, '搜索单位位置成功')
        } catch (error) {
            return createErrorResponse('搜索单位位置失败')
        }
    },

    // 导入单位位置数据
    async importUnitLocations(importData: any[]) {
        await mockDelay(2000, 4000)
        try {
            const importResults = {
                total: importData.length,
                success: Math.floor(importData.length * 0.9), // 90% 成功率
                failed: Math.ceil(importData.length * 0.1),
                duplicates: Math.floor(importData.length * 0.05), // 5% 重复
                errors: [
                    { row: 5, error: '经纬度格式不正确' },
                    { row: 12, error: '单位名称重复' }
                ]
            }
            return createResponse(importResults, '导入单位位置数据完成')
        } catch (error) {
            return createErrorResponse('导入单位位置数据失败')
        }
    },

    // 导出单位位置数据
    async exportUnitLocations(params?: {
        district?: string
        unitType?: string
        format?: 'excel' | 'csv'
    }) {
        await mockDelay(1500, 3000)
        try {
            const format = params?.format || 'excel'
            const exportResult = {
                fileName: `单位位置数据_${new Date().toISOString().split('T')[0]}.${format}`,
                fileSize: Math.floor(Math.random() * 2000) + 500, // KB
                downloadUrl: `https://mock-api.example.com/downloads/unit-locations-${Date.now()}.${format}`,
                exportTime: new Date().toISOString(),
                recordCount: Math.floor(Math.random() * 100) + 50
            }
            return createResponse(exportResult, `导出单位位置数据成功`)
        } catch (error) {
            return createErrorResponse('导出单位位置数据失败')
        }
    },

    // 获取单位位置统计信息
    async getUnitLocationStatistics(params?: { district?: string }) {
        await mockDelay()
        try {
            const stats = {
                totalCount: 156,
                districtDistribution: {
                    '渝中区': 18,
                    '江北区': 16,
                    '南岸区': 15,
                    '九龙坡区': 17,
                    '沙坪坝区': 14,
                    '大渡口区': 12,
                    '渝北区': 20,
                    '巴南区': 13,
                    '北碚区': 11,
                    '綦江区': 20
                },
                unitTypeDistribution: {
                    'government': 45,
                    'enterprise': 38,
                    'institution': 42,
                    'model': 31
                },
                partyBuildingIndexDistribution: {
                    'excellent': 42, // 90-100分
                    'good': 56,      // 80-89分
                    'average': 38,   // 70-79分
                    'poor': 20       // 60-69分
                },
                recentUpdates: 23,
                averagePartyBuildingIndex: 82.5
            }
            return createResponse(stats, '获取单位位置统计信息成功')
        } catch (error) {
            return createErrorResponse('获取单位位置统计信息失败')
        }
    },

    // 创建预警
    async createWarning(warningData: any) {
        await mockDelay()
        try {
            const newWarning = {
                id: Date.now(),
                ...warningData,
                createTime: new Date().toISOString(),
                status: 'pending'
            }
            return createResponse(newWarning, '创建预警成功')
        } catch (error) {
            return createErrorResponse('创建预警失败')
        }
    },

    // 更新预警状态
    async updateWarningStatus(warningId: number, status: string) {
        await mockDelay()
        try {
            return createResponse({ id: warningId, status }, '更新预警状态成功')
        } catch (error) {
            return createErrorResponse('更新预警状态失败')
        }
    },

    // 删除预警
    async deleteWarning(warningId: number) {
        await mockDelay()
        try {
            return createResponse({ id: warningId }, '删除预警成功')
        } catch (error) {
            return createErrorResponse('删除预警失败')
        }
    },

    // 导出数据
    async exportData(exportType: 'pdf' | 'excel' | 'image', dataType: string, params?: any) {
        await mockDelay(1000, 3000) // 导出需要更长时间
        try {
            const exportResult = {
                fileName: `模范机关总览看板_${dataType}_${new Date().toISOString().split('T')[0]}.${exportType}`,
                fileSize: Math.floor(Math.random() * 5000) + 1000, // KB
                downloadUrl: `https://mock-api.example.com/downloads/${Date.now()}.${exportType}`,
                exportTime: new Date().toISOString()
            }
            return createResponse(exportResult, `${exportType.toUpperCase()}导出成功`)
        } catch (error) {
            return createErrorResponse(`${exportType.toUpperCase()}导出失败`)
        }
    },

    // 获取导航路线
    async getNavigationRoute(startPoint: [number, number], endPoint: [number, number], travelMode: string) {
        await mockDelay(500, 1500)
        try {
            const routeData = {
                distance: Math.floor(Math.random() * 20000) + 1000, // 米
                duration: Math.floor(Math.random() * 3600) + 300, // 秒
                steps: [
                    { instruction: '从起点出发', distance: 100, duration: 60 },
                    { instruction: '直行500米', distance: 500, duration: 300 },
                    { instruction: '右转进入主干道', distance: 1200, duration: 480 },
                    { instruction: '到达目的地', distance: 50, duration: 30 }
                ],
                polyline: 'mock_polyline_data', // 实际应用中这里是路线的坐标点数组
                travelMode,
                startPoint,
                endPoint
            }
            return createResponse(routeData, '获取导航路线成功')
        } catch (error) {
            return createErrorResponse('获取导航路线失败')
        }
    },

    // 搜索地点
    async searchPlaces(keyword: string, location?: [number, number]) {
        await mockDelay(300, 800)
        try {
            const places = [
                { name: `${keyword}相关地点1`, address: '重庆市渝中区某某路1号', location: [106.550483, 29.563707] },
                { name: `${keyword}相关地点2`, address: '重庆市江北区某某街2号', location: [106.560483, 29.573707] },
                { name: `${keyword}相关地点3`, address: '重庆市南岸区某某道3号', location: [106.540483, 29.553707] }
            ]
            return createResponse(places, '搜索地点成功')
        } catch (error) {
            return createErrorResponse('搜索地点失败')
        }
    },

    // 获取统计概览数据
    async fetchStatisticsOverview(params?: { district?: string }) {
        await mockDelay()
        try {
            const data = {
                totalUnits: 156,
                modelUnits: 45,
                excellentUnits: 89,
                averageScore: 87.3,
                totalProjects: 234,
                completedProjects: 156,
                ongoingProjects: 78,
                totalInvestment: 2.8e8,
                completedInvestment: 1.6e8,
                partyBuildingIndex: 87.5,
                assessmentScore: 89.3,
                warningCount: 23,
                resolutionRate: 87.1
            }
            return createResponse(data, '获取统计概览数据成功')
        } catch (error) {
            return createErrorResponse('获取统计概览数据失败')
        }
    }
}

// 导出默认服务
export default mockModelAgencyApi