/**
 * 响应式布局样式
 * 针对不同屏幕尺寸优化布局和交互
 */

// 导入全局变量
@import './global.scss';

// 响应式容器
.responsive-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  
  @include respond-below(md) {
    padding: 0 var(--spacing-md);
  }
  
  @include respond-below(sm) {
    padding: 0 var(--spacing-sm);
  }
}

// 响应式网格系统
.responsive-grid {
  display: grid;
  gap: var(--spacing-lg);
  
  // 大屏幕 (>1200px) - 左右分栏
  @include respond-to(xl) {
    grid-template-columns: 2fr 1fr;
    grid-template-areas: 
      "main sidebar";
  }
  
  // 中等屏幕 (768px-1200px) - 上下布局
  @include respond-below(xl) {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "main"
      "sidebar";
  }
  
  // 小屏幕 (<768px) - 单列布局
  @include respond-below(md) {
    gap: var(--spacing-md);
  }
  
  .grid-main {
    grid-area: main;
  }
  
  .grid-sidebar {
    grid-area: sidebar;
  }
}

// 响应式卡片布局
.responsive-cards {
  display: grid;
  gap: var(--spacing-lg);
  
  // 超大屏幕 - 4列
  @include respond-to(xxl) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  // 大屏幕 - 3列
  @include respond-to(xl) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 中等屏幕 - 2列
  @include respond-to(md) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  // 小屏幕 - 1列
  @include respond-below(md) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

// 响应式统计卡片
.responsive-stats {
  display: grid;
  gap: var(--spacing-lg);
  
  // 大屏幕 - 4列
  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
  }
  
  // 中等屏幕 - 2列
  @include respond-to(md) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  // 小屏幕 - 1列
  @include respond-below(md) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .stat-item {
    min-height: 120px;
    
    @include respond-below(md) {
      min-height: 100px;
    }
    
    @include respond-below(sm) {
      min-height: 80px;
    }
  }
}

// 响应式表格
.responsive-table {
  .ant-table-wrapper {
    // 大屏幕正常显示
    @include respond-to(lg) {
      .ant-table-scroll {
        overflow-x: visible;
      }
    }
    
    // 中小屏幕横向滚动
    @include respond-below(lg) {
      .ant-table-scroll {
        overflow-x: auto;
      }
      
      .ant-table {
        min-width: 800px;
      }
    }
    
    // 小屏幕优化
    @include respond-below(md) {
      .ant-table {
        min-width: 600px;
        
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: var(--spacing-sm) var(--spacing-xs);
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}

// 响应式标签页
.responsive-tabs {
  .ant-tabs {
    // 大屏幕正常显示
    @include respond-to(md) {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: var(--spacing-md) var(--spacing-lg);
          font-size: var(--font-size-md);
        }
      }
    }
    
    // 小屏幕优化
    @include respond-below(md) {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: var(--spacing-sm) var(--spacing-md);
          font-size: var(--font-size-sm);
        }
      }
    }
    
    // 超小屏幕
    @include respond-below(sm) {
      .ant-tabs-nav {
        .ant-tabs-tab {
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: var(--font-size-sm);
          
          .ant-tabs-tab-btn {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 80px;
          }
        }
      }
    }
  }
}

// 响应式按钮组
.responsive-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  
  @include respond-below(md) {
    gap: var(--spacing-sm);
    
    .ant-btn {
      font-size: var(--font-size-sm);
      padding: var(--spacing-xs) var(--spacing-md);
    }
  }
  
  @include respond-below(sm) {
    flex-direction: column;
    
    .ant-btn {
      width: 100%;
    }
  }
}

// 响应式表单
.responsive-form {
  .ant-form-item {
    margin-bottom: var(--spacing-lg);
    
    @include respond-below(md) {
      margin-bottom: var(--spacing-md);
    }
  }
  
  .ant-row {
    @include respond-below(md) {
      flex-direction: column;
      
      .ant-col {
        width: 100% !important;
        margin-bottom: var(--spacing-sm);
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 响应式模态框
.responsive-modal {
  .ant-modal {
    @include respond-below(md) {
      max-width: 90vw;
      margin: var(--spacing-lg);
    }
    
    @include respond-below(sm) {
      max-width: 95vw;
      margin: var(--spacing-sm);
      
      .ant-modal-content {
        .ant-modal-header {
          padding: var(--spacing-md);
          
          .ant-modal-title {
            font-size: var(--font-size-lg);
          }
        }
        
        .ant-modal-body {
          padding: var(--spacing-md);
        }
        
        .ant-modal-footer {
          padding: var(--spacing-md);
        }
      }
    }
  }
}

// 响应式抽屉
.responsive-drawer {
  .ant-drawer {
    @include respond-below(md) {
      .ant-drawer-content-wrapper {
        width: 90vw !important;
      }
    }
    
    @include respond-below(sm) {
      .ant-drawer-content-wrapper {
        width: 100vw !important;
      }
    }
  }
}

// 响应式图表容器
.responsive-chart {
  width: 100%;
  height: 400px;
  
  @include respond-below(lg) {
    height: 350px;
  }
  
  @include respond-below(md) {
    height: 300px;
  }
  
  @include respond-below(sm) {
    height: 250px;
  }
}

// 响应式地图容器
.responsive-map {
  width: 100%;
  height: 600px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  
  @include respond-below(xl) {
    height: 500px;
  }
  
  @include respond-below(lg) {
    height: 450px;
  }
  
  @include respond-below(md) {
    height: 400px;
  }
  
  @include respond-below(sm) {
    height: 300px;
  }
}

// 响应式侧边栏
.responsive-sidebar {
  @include respond-to(xl) {
    position: sticky;
    top: var(--spacing-xl);
    height: fit-content;
  }
  
  @include respond-below(xl) {
    margin-top: var(--spacing-lg);
  }
  
  @include respond-below(md) {
    margin-top: var(--spacing-md);
  }
}

// 响应式头部
.responsive-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  
  @include respond-below(lg) {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  @include respond-below(md) {
    padding: var(--spacing-sm) var(--spacing-md);
    
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-md);
    }
    
    .header-title {
      font-size: var(--font-size-xl);
    }
    
    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }
  
  @include respond-below(sm) {
    padding: var(--spacing-xs) var(--spacing-sm);
    
    .header-title {
      font-size: var(--font-size-lg);
    }
  }
}

// 响应式面包屑
.responsive-breadcrumb {
  .ant-breadcrumb {
    @include respond-below(sm) {
      font-size: var(--font-size-sm);
      
      .ant-breadcrumb-link {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
      }
    }
  }
}

// 响应式分页
.responsive-pagination {
  .ant-pagination {
    @include respond-below(md) {
      .ant-pagination-options {
        display: none;
      }
    }
    
    @include respond-below(sm) {
      .ant-pagination-item {
        display: none;
        
        &.ant-pagination-item-active {
          display: inline-block;
        }
      }
      
      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        display: none;
      }
    }
  }
}

// 响应式工具提示
.responsive-tooltip {
  @include respond-below(sm) {
    .ant-tooltip-inner {
      font-size: var(--font-size-sm);
      padding: var(--spacing-xs) var(--spacing-sm);
    }
  }
}

// 响应式加载状态
.responsive-loading {
  .loading-container {
    min-height: 300px;
    
    @include respond-below(md) {
      min-height: 200px;
    }
    
    @include respond-below(sm) {
      min-height: 150px;
    }
    
    .ant-spin {
      .ant-spin-dot {
        @include respond-below(sm) {
          font-size: var(--font-size-lg);
        }
      }
      
      .ant-spin-text {
        @include respond-below(sm) {
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}

// 响应式空状态
.responsive-empty {
  .empty-container {
    min-height: 200px;
    
    @include respond-below(md) {
      min-height: 150px;
      padding: var(--spacing-lg);
    }
    
    @include respond-below(sm) {
      min-height: 120px;
      padding: var(--spacing-md);
    }
    
    .empty-icon {
      @include respond-below(sm) {
        font-size: 48px;
      }
    }
    
    .empty-title {
      @include respond-below(sm) {
        font-size: var(--font-size-md);
      }
    }
    
    .empty-description {
      @include respond-below(sm) {
        font-size: var(--font-size-sm);
      }
    }
  }
}
