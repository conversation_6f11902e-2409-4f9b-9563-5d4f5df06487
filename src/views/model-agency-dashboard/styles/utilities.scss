/**
 * 工具类样式
 * 提供常用的原子化CSS类
 */

@import './global.scss';

// 布局工具类
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.d-block { display: block; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.align-start { align-items: flex-start; }
.align-center { align-items: center; }
.align-end { align-items: flex-end; }
.align-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

// 定位工具类
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

// 尺寸工具类
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-50 { width: 50%; }
.w-25 { width: 25%; }
.w-75 { width: 75%; }

.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-50 { height: 50%; }

.min-w-0 { min-width: 0; }
.min-h-0 { min-height: 0; }
.max-w-full { max-width: 100%; }
.max-h-full { max-height: 100%; }

// 间距工具类
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-auto { margin-top: auto; margin-bottom: auto; }

.p-0 { padding: 0; }

// 生成间距类
@each $size, $value in (
  'xs': var(--spacing-xs),
  'sm': var(--spacing-sm),
  'md': var(--spacing-md),
  'lg': var(--spacing-lg),
  'xl': var(--spacing-xl),
  'xxl': var(--spacing-xxl)
) {
  // margin
  .m-#{$size} { margin: #{$value}; }
  .mt-#{$size} { margin-top: #{$value}; }
  .mr-#{$size} { margin-right: #{$value}; }
  .mb-#{$size} { margin-bottom: #{$value}; }
  .ml-#{$size} { margin-left: #{$value}; }
  .mx-#{$size} { margin-left: #{$value}; margin-right: #{$value}; }
  .my-#{$size} { margin-top: #{$value}; margin-bottom: #{$value}; }
  
  // padding
  .p-#{$size} { padding: #{$value}; }
  .pt-#{$size} { padding-top: #{$value}; }
  .pr-#{$size} { padding-right: #{$value}; }
  .pb-#{$size} { padding-bottom: #{$value}; }
  .pl-#{$size} { padding-left: #{$value}; }
  .px-#{$size} { padding-left: #{$value}; padding-right: #{$value}; }
  .py-#{$size} { padding-top: #{$value}; padding-bottom: #{$value}; }
}

// 文本工具类
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-uppercase { text-transform: uppercase; }
.text-lowercase { text-transform: lowercase; }
.text-capitalize { text-transform: capitalize; }

.font-normal { font-weight: normal; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: bold; }

.text-xs { font-size: var(--font-size-sm); }
.text-sm { font-size: var(--font-size-md); }
.text-base { font-size: var(--font-size-lg); }
.text-lg { font-size: var(--font-size-xl); }
.text-xl { font-size: var(--font-size-xxl); }
.text-2xl { font-size: var(--font-size-title); }

.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.75; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }
.text-secondary { color: var(--text-color-secondary); }
.text-disabled { color: var(--text-color-disabled); }

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-break {
  word-break: break-word;
  overflow-wrap: break-word;
}

// 背景工具类
.bg-transparent { background-color: transparent; }
.bg-white { background-color: var(--background-color); }
.bg-light { background-color: var(--background-color-light); }
.bg-dark { background-color: var(--background-color-dark); }
.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

// 边框工具类
.border { border: 1px solid var(--border-color); }
.border-light { border: 1px solid var(--border-color-light); }
.border-primary { border: 1px solid var(--primary-color); }
.border-success { border: 1px solid var(--success-color); }
.border-warning { border: 1px solid var(--warning-color); }
.border-error { border: 1px solid var(--error-color); }

.border-t { border-top: 1px solid var(--border-color); }
.border-r { border-right: 1px solid var(--border-color); }
.border-b { border-bottom: 1px solid var(--border-color); }
.border-l { border-left: 1px solid var(--border-color); }

.border-none { border: none; }

.rounded { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-full { border-radius: 50%; }
.rounded-none { border-radius: 0; }

// 阴影工具类
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-1); }
.shadow { box-shadow: var(--shadow-2); }
.shadow-lg { box-shadow: var(--shadow-3); }

// 溢出工具类
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }

// 可见性工具类
.visible { visibility: visible; }
.invisible { visibility: hidden; }

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// 指针事件工具类
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

// 用户选择工具类
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

// 过渡动画工具类
.transition { transition: all var(--transition-normal); }
.transition-none { transition: none; }
.transition-fast { transition: all var(--transition-fast); }
.transition-slow { transition: all var(--transition-slow); }

.transition-colors { transition: color var(--transition-normal), background-color var(--transition-normal), border-color var(--transition-normal); }
.transition-opacity { transition: opacity var(--transition-normal); }
.transition-transform { transition: transform var(--transition-normal); }

// 变换工具类
.transform { transform: translateZ(0); }
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

// 响应式工具类
@include respond-below(sm) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:text-center { text-align: center; }
  .sm\:text-left { text-align: left; }
}

@include respond-below(md) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:text-center { text-align: center; }
  .md\:text-left { text-align: left; }
}

@include respond-below(lg) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:text-center { text-align: center; }
  .lg\:text-left { text-align: left; }
}

// 打印工具类
@media print {
  .print\:hidden { display: none !important; }
  .print\:block { display: block !important; }
  .print\:text-black { color: #000 !important; }
  .print\:bg-white { background: #fff !important; }
}

// 深色模式工具类（预留）
@media (prefers-color-scheme: dark) {
  .dark\:text-white { color: #ffffff; }
  .dark\:bg-gray-800 { background-color: #1f2937; }
  .dark\:border-gray-600 { border-color: #4b5563; }
}

// 高对比度模式工具类
@media (prefers-contrast: high) {
  .high-contrast\:border-black { border-color: #000000; }
  .high-contrast\:text-black { color: #000000; }
  .high-contrast\:bg-white { background-color: #ffffff; }
}

// 减少动画模式工具类
@media (prefers-reduced-motion: reduce) {
  .motion-reduce\:transition-none { transition: none !important; }
  .motion-reduce\:animate-none { animation: none !important; }
}
