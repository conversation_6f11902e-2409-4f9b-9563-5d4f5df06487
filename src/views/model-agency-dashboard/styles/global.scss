/**
 * 模范机关总览看板 - 全局样式
 * 遵循Ant Design设计规范，提供一致的用户体验
 */

// 设计令牌 (Design Tokens)
:root {
  // 主色调
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --primary-color-light: #e6f7ff;
  
  // 功能色彩
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  // 中性色彩
  --text-color: #262626;
  --text-color-secondary: #595959;
  --text-color-disabled: #bfbfbf;
  --border-color: #d9d9d9;
  --border-color-light: #f0f0f0;
  --background-color: #ffffff;
  --background-color-light: #fafafa;
  --background-color-dark: #f5f5f5;
  
  // 阴影
  --shadow-1: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-2: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-3: 0 6px 16px rgba(0, 0, 0, 0.12);
  
  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  
  // 字体
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;
  --font-size-title: 24px;
  
  // 动画
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

// 全局重置样式
* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  color: var(--text-color);
  background-color: var(--background-color-dark);
}

// 通用工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.flex-between { display: flex; align-items: center; justify-content: space-between; }
.flex-column { display: flex; flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

// 响应式断点
$breakpoints: (
  xs: 480px,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  .ant-spin {
    .ant-spin-text {
      color: var(--text-color-secondary);
      margin-top: var(--spacing-md);
    }
  }
}

// 骨架屏样式
.skeleton-container {
  .skeleton-item {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius-sm);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 错误状态样式
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--spacing-xl);
  text-align: center;
  
  .error-icon {
    font-size: 48px;
    color: var(--error-color);
    margin-bottom: var(--spacing-lg);
  }
  
  .error-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-sm);
  }
  
  .error-message {
    font-size: var(--font-size-md);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
  }
  
  .error-actions {
    display: flex;
    gap: var(--spacing-md);
  }
}

// 空状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: var(--spacing-xl);
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    color: var(--text-color-disabled);
    margin-bottom: var(--spacing-lg);
  }
  
  .empty-title {
    font-size: var(--font-size-lg);
    color: var(--text-color-secondary);
    margin-bottom: var(--spacing-sm);
  }
  
  .empty-description {
    font-size: var(--font-size-md);
    color: var(--text-color-disabled);
  }
}

// 卡片增强样式
.enhanced-card {
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-1);
  transition: box-shadow var(--transition-normal);
  
  &:hover {
    box-shadow: var(--shadow-2);
  }
  
  .ant-card-head {
    border-bottom: 1px solid var(--border-color-light);
    
    .ant-card-head-title {
      font-weight: 600;
      color: var(--text-color);
    }
  }
  
  .ant-card-body {
    padding: var(--spacing-xl);
  }
}

// 统计卡片样式
.stat-card {
  @extend .enhanced-card;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
  }
  
  &:hover {
    &::before {
      transform: scaleX(1);
    }
  }
  
  .ant-statistic {
    .ant-statistic-title {
      font-size: var(--font-size-md);
      color: var(--text-color-secondary);
      margin-bottom: var(--spacing-sm);
      font-weight: 500;
    }
    
    .ant-statistic-content {
      font-size: var(--font-size-xxl);
      font-weight: 600;
      margin-bottom: var(--spacing-sm);
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 动画类
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

// 无障碍访问支持
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-color: #000000;
    --background-color: #ffffff;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  .enhanced-card,
  .stat-card {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
  
  .ant-btn {
    display: none;
  }
}
