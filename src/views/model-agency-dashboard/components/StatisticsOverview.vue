<template>
  <div class="statistics-overview">
    <!-- 核心统计指标 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('municipal-units')">
            <a-statistic title="市级机关单位" :value="statisticsData.municipalUnits" suffix="个"
              :value-style="{ color: '#1890ff' }">
              <template #prefix>
                <bank-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('district-units')">
            <a-statistic title="区县机关单位" :value="statisticsData.districtUnits" suffix="个"
              :value-style="{ color: '#52c41a' }">
              <template #prefix>
                <apartment-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('party-organizations')">
            <a-statistic title="党组（党委）" :value="statisticsData.partyOrganizations" suffix="个"
              :value-style="{ color: '#722ed1' }">
              <template #prefix>
                <team-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('total-projects')">
            <a-statistic title="项目总数" :value="statisticsData.totalProjects" suffix="个"
              :value-style="{ color: '#faad14' }">
              <template #prefix>
                <project-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 项目进度统计 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('completed-projects')">
            <a-statistic title="完成评选项目" :value="statisticsData.completedProjects" suffix="个"
              :value-style="{ color: '#52c41a' }">
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <a-progress :percent="statisticsData.completionRate" size="small" :show-info="false"
                stroke-color="#52c41a" />
              <span class="progress-text">完成率 {{ statisticsData.completionRate }}%</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('applicant-units')">
            <a-statistic title="申报单位" :value="statisticsData.applicantUnits" suffix="个"
              :value-style="{ color: '#1890ff' }">
              <template #prefix>
                <file-text-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <a-tag color="blue">申报率 {{ statisticsData.applicantRatio }}%</a-tag>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('cultivation-units')">
            <a-statistic title="培育单位" :value="statisticsData.cultivationUnits" suffix="个"
              :value-style="{ color: '#faad14' }">
              <template #prefix>
                <rise-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <a-tag color="orange">培育率 {{ statisticsData.cultivationRatio }}%</a-tag>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('benchmark-units')">
            <a-statistic title="标杆单位" :value="statisticsData.benchmarkUnits" suffix="个"
              :value-style="{ color: '#f5222d' }">
              <template #prefix>
                <trophy-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <a-tag color="red">标杆率 {{ statisticsData.benchmarkRatio }}%</a-tag>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 党建指数统计 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card level-excellent" @click="handleCardClick('excellent-units')">
            <a-statistic title="优秀单位" :value="statisticsData.excellentUnits" suffix="个"
              :value-style="{ color: '#52c41a' }">
              <template #prefix>
                <crown-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <span class="level-badge excellent">90分以上</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card level-good" @click="handleCardClick('good-units')">
            <a-statistic title="良好单位" :value="statisticsData.goodUnits" suffix="个" :value-style="{ color: '#1890ff' }">
              <template #prefix>
                <like-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <span class="level-badge good">80-90分</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card level-average" @click="handleCardClick('average-units')">
            <a-statistic title="一般单位" :value="statisticsData.averageUnits" suffix="个"
              :value-style="{ color: '#faad14' }">
              <template #prefix>
                <minus-circle-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <span class="level-badge average">70-80分</span>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card level-poor" @click="handleCardClick('poor-units')">
            <a-statistic title="待提升单位" :value="statisticsData.poorUnits" suffix="个" :value-style="{ color: '#f5222d' }">
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
            <div class="stat-extra">
              <span class="level-badge poor">70分以下</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据资源统计 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('total-indicators')">
            <a-statistic title="指标总数" :value="statisticsData.totalIndicators" suffix="项"
              :value-style="{ color: '#722ed1' }">
              <template #prefix>
                <bar-chart-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('smart-indicators')">
            <a-statistic title="智能筛选指标" :value="statisticsData.smartFilterIndicators" suffix="项"
              :value-style="{ color: '#13c2c2' }">
              <template #prefix>
                <filter-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('data-resources')">
            <a-statistic title="数据资源" :value="statisticsData.dataResources" suffix="个"
              :value-style="{ color: '#eb2f96' }">
              <template #prefix>
                <database-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card" @click="handleCardClick('data-source-units')">
            <a-statistic title="数据来源单位" :value="statisticsData.dataSourceUnits" suffix="个"
              :value-style="{ color: '#52c41a' }">
              <template #prefix>
                <cluster-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 单位位置管理 -->
    <div class="statistics-section">
      <UnitLocationManager :data="unitLocations" :loading="locationLoading" @location-update="handleLocationUpdate"
        @location-delete="handleLocationDelete" @data-refresh="handleLocationDataRefresh" />
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large" tip="加载统计数据中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { ProjectData, PartyBuildingData, StatisticsOverviewProps } from '../types'
import { mockDataService } from '../mock/data'
import UnitLocationManager from './UnitLocationManager.vue'

// 单位位置类型定义
interface UnitLocation {
  id: string | number
  unitName: string
  longitude: number
  latitude: number
  address?: string
  level?: string
  status?: 'active' | 'inactive'
  createdAt?: string
  updatedAt?: string
}

// Props定义
interface Props {
  projectData?: ProjectData
  partyBuildingData?: PartyBuildingData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits定义
const emit = defineEmits<{
  'card-click': [type: string, data?: any]
  'data-refresh': []
}>()

// 响应式数据
const loading = ref(props.loading)
const projectData = ref<ProjectData | null>(null)
const partyBuildingData = ref<PartyBuildingData | null>(null)

// 计算属性 - 统计数据
const statisticsData = computed(() => {
  const project = projectData.value
  const partyBuilding = partyBuildingData.value

  if (!project || !partyBuilding) {
    return {
      municipalUnits: 0,
      districtUnits: 0,
      partyOrganizations: 0,
      totalProjects: 0,
      completedProjects: 0,
      completionRate: 0,
      applicantUnits: 0,
      cultivationUnits: 0,
      benchmarkUnits: 0,
      applicantRatio: 0,
      cultivationRatio: 0,
      benchmarkRatio: 0,
      excellentUnits: 0,
      goodUnits: 0,
      averageUnits: 0,
      poorUnits: 0,
      totalIndicators: 0,
      smartFilterIndicators: 0,
      dataResources: 0,
      dataSourceUnits: 0
    }
  }

  return {
    municipalUnits: partyBuilding.municipalAgencies,
    districtUnits: partyBuilding.municipalAgencies + partyBuilding.partyGroupDepartments + partyBuilding.governmentDepartments - partyBuilding.municipalAgencies,
    partyOrganizations: partyBuilding.municipalAgencies + partyBuilding.partyGroupDepartments,
    totalProjects: project.totalProjects,
    completedProjects: project.completedProjects,
    completionRate: Math.round(project.completionRate),
    applicantUnits: project.applicantUnits,
    cultivationUnits: project.cultivationUnits,
    benchmarkUnits: project.benchmarkUnits,
    applicantRatio: Math.round(project.applicantRatio),
    cultivationRatio: Math.round(project.cultivationRatio),
    benchmarkRatio: Math.round(project.benchmarkRatio),
    excellentUnits: partyBuilding.excellentCount,
    goodUnits: partyBuilding.goodCount,
    averageUnits: partyBuilding.averageCount,
    poorUnits: partyBuilding.poorCount,
    totalIndicators: project.totalIndicators,
    smartFilterIndicators: project.smartFilterIndicators,
    dataResources: project.dataResources,
    dataSourceUnits: project.dataSourceUnits
  }
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const [projectResponse, partyBuildingResponse] = await Promise.all([
      mockDataService.fetchProjectStats(),
      mockDataService.fetchPartyBuildingData()
    ])

    projectData.value = projectResponse.data.municipal
    partyBuildingData.value = partyBuildingResponse.data
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCardClick = (type: string) => {
  const data = {
    type,
    value: statisticsData.value,
    projectData: projectData.value,
    partyBuildingData: partyBuildingData.value
  }
  emit('card-click', type, data)
}

const refreshData = () => {
  emit('data-refresh')
  loadData()
}

// 单位位置管理相关方法
const loadUnitLocations = async () => {
  locationLoading.value = true
  try {
    // 模拟API调用，实际项目中替换为真实API
    const mockLocations: UnitLocation[] = [
      {
        id: '1',
        unitName: '市委办公厅',
        longitude: 106.5516,
        latitude: 29.5630,
        address: '重庆市渝中区人民路232号',
        level: 'A',
        status: 'active',
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15'
      },
      {
        id: '2',
        unitName: '市政府办公厅',
        longitude: 106.5548,
        latitude: 29.5647,
        address: '重庆市渝中区人民路252号',
        level: 'A',
        status: 'active',
        createdAt: '2024-01-16',
        updatedAt: '2024-01-16'
      },
      {
        id: '3',
        unitName: '市发展改革委',
        longitude: 106.5580,
        latitude: 29.5680,
        address: '重庆市渝中区中山四路36号',
        level: 'B',
        status: 'active',
        createdAt: '2024-01-17',
        updatedAt: '2024-01-17'
      }
    ]

    unitLocations.value = mockLocations
  } catch (error) {
    console.error('加载单位位置数据失败:', error)
    message.error('加载单位位置数据失败')
  } finally {
    locationLoading.value = false
  }
}

const handleLocationUpdate = (location: UnitLocation) => {
  // 更新本地数据
  const index = unitLocations.value.findIndex(item => item.id === location.id)
  if (index !== -1) {
    unitLocations.value[index] = { ...location, updatedAt: new Date().toISOString().split('T')[0] }
  } else {
    // 新增数据
    unitLocations.value.push({ ...location, createdAt: new Date().toISOString().split('T')[0] })
  }

  // 发送更新事件到父组件
  emit('location-update', location)
  message.success('单位坐标更新成功')
}

const handleLocationDelete = (locationId: string | number) => {
  // 从本地数据中删除
  unitLocations.value = unitLocations.value.filter(item => item.id !== locationId)

  // 发送删除事件到父组件
  emit('location-delete', locationId)
  message.success('单位坐标删除成功')
}

const handleLocationDataRefresh = () => {
  loadUnitLocations()
}

// 生命周期
onMounted(() => {
  if (!props.projectData || !props.partyBuildingData) {
    loadData()
  }
  // 加载单位位置数据
  loadUnitLocations()
})

// 监听props变化
watch(() => props.projectData, (newValue) => {
  if (newValue) {
    projectData.value = newValue
  }
})

watch(() => props.partyBuildingData, (newValue) => {
  if (newValue) {
    partyBuildingData.value = newValue
  }
})

watch(() => props.loading, (newValue) => {
  loading.value = newValue
})

// 暴露方法给父组件
defineExpose({
  refreshData,
  loadData
})
</script>

<style scoped lang="scss">
.statistics-overview {
  position: relative;

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }

      // 等级卡片特殊样式
      &.level-excellent {
        border-left: 4px solid #52c41a;

        &:hover {
          border-left-color: #389e0d;
        }
      }

      &.level-good {
        border-left: 4px solid #1890ff;

        &:hover {
          border-left-color: #096dd9;
        }
      }

      &.level-average {
        border-left: 4px solid #faad14;

        &:hover {
          border-left-color: #d48806;
        }
      }

      &.level-poor {
        border-left: 4px solid #f5222d;

        &:hover {
          border-left-color: #cf1322;
        }
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
          margin-bottom: 8px;
        }
      }

      .stat-extra {
        margin-top: 12px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;

        .ant-progress {
          margin-bottom: 4px;
        }

        .progress-text {
          font-size: 12px;
          color: #666;
        }

        .ant-tag {
          margin: 0;
          font-size: 11px;
          border-radius: 10px;
        }

        .level-badge {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 11px;
          font-weight: 500;

          &.excellent {
            background: rgba(82, 196, 26, 0.1);
            color: #52c41a;
            border: 1px solid rgba(82, 196, 26, 0.3);
          }

          &.good {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            border: 1px solid rgba(24, 144, 255, 0.3);
          }

          &.average {
            background: rgba(250, 173, 20, 0.1);
            color: #faad14;
            border: 1px solid rgba(250, 173, 20, 0.3);
          }

          &.poor {
            background: rgba(245, 34, 45, 0.1);
            color: #f5222d;
            border: 1px solid rgba(245, 34, 45, 0.3);
          }
        }
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .statistics-overview {
    .statistics-section {
      .stat-card {
        .ant-statistic {
          .ant-statistic-content {
            font-size: 24px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .statistics-overview {
    .statistics-section {
      margin-bottom: 16px;

      .stat-card {
        margin-bottom: 8px;

        .ant-statistic {
          .ant-statistic-title {
            font-size: 13px;
          }

          .ant-statistic-content {
            font-size: 20px;
          }
        }

        .stat-extra {
          margin-top: 8px;
          padding-top: 6px;

          .progress-text {
            font-size: 11px;
          }

          .level-badge {
            font-size: 10px;
            padding: 1px 6px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .statistics-overview {
    .statistics-section {
      .stat-card {
        .ant-statistic {
          .ant-statistic-title {
            font-size: 12px;
            margin-bottom: 4px;
          }

          .ant-statistic-content {
            font-size: 18px;
          }
        }

        .stat-extra {
          margin-top: 6px;
          padding-top: 4px;

          .level-badge {
            font-size: 9px;
            padding: 1px 4px;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  50% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.statistics-overview {
  .stat-card {
    &.pulse {
      animation: pulse 2s infinite;
    }
  }
}
</style>
