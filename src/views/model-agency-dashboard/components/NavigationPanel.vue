<template>
    <div class="navigation-panel">
        <!-- 导航功能选择 -->
        <div class="navigation-header">
            <a-segmented v-model:value="activeNavMode" :options="navModeOptions" @change="handleNavModeChange"
                style="margin-bottom: 16px;" />
        </div>

        <!-- 路径导航 -->
        <div v-if="activeNavMode === 'route'" class="route-planning">
            <a-card title="路径规划" size="small" :bordered="false">
                <div class="route-form">
                    <a-form layout="vertical" :model="routeForm">
                        <a-form-item label="起点">
                            <a-auto-complete v-model:value="routeForm.startPoint" :options="startPointOptions"
                                placeholder="请选择或输入起点位置" allow-clear @search="handleStartPointSearch"
                                @select="handleStartPointSelect" @pressEnter="searchLocation('start')">
                                <template #option="{ value, label, unit }">
                                    <div class="location-option">
                                        <div class="location-name">{{ unit?.name || value }}</div>
                                        <div class="location-detail">{{ unit?.district }} · {{ unit?.address }}</div>
                                    </div>
                                </template>
                                <template #suffix>
                                    <a-button type="text" size="small" @click="getCurrentLocation('start')">
                                        <template #icon><environment-outlined /></template>
                                    </a-button>
                                </template>
                            </a-auto-complete>
                        </a-form-item>

                        <a-form-item label="终点">
                            <a-auto-complete v-model:value="routeForm.endPoint" :options="endPointOptions"
                                placeholder="请选择或输入终点位置" allow-clear @search="handleEndPointSearch"
                                @select="handleEndPointSelect" @pressEnter="searchLocation('end')">
                                <template #option="{ value, label, unit }">
                                    <div class="location-option">
                                        <div class="location-name">{{ unit?.name || value }}</div>
                                        <div class="location-detail">{{ unit?.district }} · {{ unit?.address }}</div>
                                    </div>
                                </template>
                                <template #suffix>
                                    <a-button type="text" size="small" @click="getCurrentLocation('end')">
                                        <template #icon><environment-outlined /></template>
                                    </a-button>
                                </template>
                            </a-auto-complete>
                        </a-form-item>

                        <a-form-item label="导航方式">
                            <a-radio-group v-model:value="routeForm.travelMode">
                                <a-radio value="driving">驾车</a-radio>
                                <a-radio value="walking">步行</a-radio>
                                <a-radio value="transit">公交</a-radio>
                            </a-radio-group>
                        </a-form-item>

                        <a-form-item>
                            <a-space>
                                <a-button type="primary" @click="planRoute" :loading="routeLoading">
                                    <template #icon><search-outlined /></template>
                                    规划路线
                                </a-button>
                                <!-- <a-button @click="clearRoute">
                                    <template #icon><clear-outlined /></template>
                                    清除
                                </a-button> -->
                            </a-space>
                        </a-form-item>
                    </a-form>
                </div>

                <!-- 路线结果 -->
                <div v-if="routeResult" class="route-result">
                    <a-divider>路线信息</a-divider>
                    <a-descriptions :column="1" size="small">
                        <a-descriptions-item label="总距离">
                            {{ routeResult.distance }}
                        </a-descriptions-item>
                        <a-descriptions-item label="预计时间">
                            {{ routeResult.duration }}
                        </a-descriptions-item>
                        <a-descriptions-item label="路线方案">
                            {{ routeResult.routeCount }} 条
                        </a-descriptions-item>
                    </a-descriptions>

                    <a-button type="link" size="small" @click="startNavigation">
                        <template #icon><play-circle-outlined /></template>
                        开始导航
                    </a-button>
                </div>
            </a-card>
        </div>

        <!-- 导航轨迹记录 -->
        <div v-if="activeNavMode === 'track'" class="track-recording">
            <a-card title="轨迹记录" size="small" :bordered="false">
                <div class="track-controls">
                    <a-space direction="vertical" style="width: 100%;">
                        <a-button :type="isRecording ? 'danger' : 'primary'" block @click="toggleRecording"
                            :loading="recordingLoading">
                            <template #icon>
                                <play-circle-outlined v-if="!isRecording" />
                                <pause-circle-outlined v-else />
                            </template>
                            {{ isRecording ? '停止记录' : '开始记录' }}
                        </a-button>

                        <div v-if="currentTrack" class="track-info">
                            <a-statistic-countdown title="记录时长" :value="trackStartTime + recordingDuration"
                                format="HH:mm:ss" />
                            <a-row :gutter="16" style="margin-top: 12px;">
                                <a-col span="12">
                                    <a-statistic title="距离" :value="currentTrack.distance" suffix="km" />
                                </a-col>
                                <a-col span="12">
                                    <a-statistic title="点数" :value="currentTrack.points.length" />
                                </a-col>
                            </a-row>
                        </div>
                    </a-space>
                </div>

                <!-- 轨迹历史 -->
                <a-divider>历史轨迹</a-divider>
                <div class="track-history">
                    <a-list :data-source="trackHistory" size="small" :pagination="{ pageSize: 5, size: 'small' }">
                        <template #renderItem="{ item }">
                            <a-list-item>
                                <template #actions>
                                    <a-button type="text" size="small" @click="viewTrack(item)">
                                        <template #icon><eye-outlined /></template>
                                    </a-button>
                                    <a-button type="text" size="small" @click="shareTrack(item)">
                                        <template #icon><share-alt-outlined /></template>
                                    </a-button>
                                    <a-button type="text" size="small" danger @click="deleteTrack(item.id)">
                                        <template #icon><delete-outlined /></template>
                                    </a-button>
                                </template>
                                <a-list-item-meta>
                                    <template #title>{{ item.name }}</template>
                                    <template #description>
                                        {{ item.date }} · {{ item.distance }}km · {{ item.duration }}
                                    </template>
                                </a-list-item-meta>
                            </a-list-item>
                        </template>
                    </a-list>
                </div>
            </a-card>
        </div>

        <!-- 轨迹绘制 -->
        <div v-if="activeNavMode === 'draw'" class="track-drawing">
            <a-card title="轨迹绘制" size="small" :bordered="false">
                <div class="draw-controls">
                    <a-space direction="vertical" style="width: 100%;">
                        <a-button-group style="width: 100%;">
                            <a-button :type="drawMode === 'draw' ? 'primary' : 'default'" @click="setDrawMode('draw')"
                                style="width: 50%;">
                                <template #icon><edit-outlined /></template>
                                绘制
                            </a-button>
                            <a-button :type="drawMode === 'edit' ? 'primary' : 'default'" @click="setDrawMode('edit')"
                                style="width: 50%;">
                                <template #icon><drag-outlined /></template>
                                编辑
                            </a-button>
                        </a-button-group>

                        <a-form layout="vertical" :model="drawForm">
                            <a-form-item label="轨迹名称">
                                <a-input v-model:value="drawForm.name" placeholder="请输入轨迹名称" />
                            </a-form-item>

                            <a-form-item label="轨迹颜色">
                                <a-select v-model:value="drawForm.color" style="width: 100%;">
                                    <a-select-option value="#1890ff">蓝色</a-select-option>
                                    <a-select-option value="#52c41a">绿色</a-select-option>
                                    <a-select-option value="#fa541c">橙色</a-select-option>
                                    <a-select-option value="#f5222d">红色</a-select-option>
                                    <a-select-option value="#722ed1">紫色</a-select-option>
                                </a-select>
                            </a-form-item>

                            <a-form-item label="线条宽度">
                                <a-slider v-model:value="drawForm.width" :min="2" :max="10"
                                    :marks="{ 2: '细', 6: '中', 10: '粗' }" />
                            </a-form-item>
                        </a-form>

                        <a-space style="width: 100%;">
                            <a-button @click="clearDrawing">
                                <template #icon><clear-outlined /></template>
                                清除
                            </a-button>
                            <a-button type="primary" @click="saveDrawing" :disabled="!currentDrawing">
                                <template #icon><save-outlined /></template>
                                保存
                            </a-button>
                        </a-space>
                    </a-space>
                </div>

                <!-- 绘制轨迹列表 -->
                <a-divider>已绘制轨迹</a-divider>
                <div class="drawn-tracks">
                    <a-list :data-source="drawnTracks" size="small" :pagination="{ pageSize: 5, size: 'small' }">
                        <template #renderItem="{ item }">
                            <a-list-item>
                                <template #actions>
                                    <a-button type="text" size="small" @click="editDrawnTrack(item)">
                                        <template #icon><edit-outlined /></template>
                                    </a-button>
                                    <a-button type="text" size="small" @click="exportTrack(item)">
                                        <template #icon><download-outlined /></template>
                                    </a-button>
                                    <a-button type="text" size="small" danger @click="deleteDrawnTrack(item.id)">
                                        <template #icon><delete-outlined /></template>
                                    </a-button>
                                </template>
                                <a-list-item-meta>
                                    <template #title>
                                        <span :style="{ color: item.color }">{{ item.name }}</span>
                                    </template>
                                    <template #description>
                                        {{ item.date }} · {{ item.points.length }} 个点
                                    </template>
                                </a-list-item-meta>
                            </a-list-item>
                        </template>
                    </a-list>
                </div>
            </a-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, h, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
    EnvironmentOutlined,
    SearchOutlined,
    ClearOutlined,
    PlayCircleOutlined,
    PauseCircleOutlined,
    EyeOutlined,
    ShareAltOutlined,
    DeleteOutlined,
    EditOutlined,
    DragOutlined,
    SaveOutlined,
    DownloadOutlined
} from '@ant-design/icons-vue'

// 定义单位位置类型
interface UnitLocation {
    id: string
    name: string
    longitude: number
    latitude: number
    district: string
    address: string
    partyBuildingIndex: number
    level: 'excellent' | 'good' | 'average' | 'poor'
    unitType: 'municipal' | 'district' | 'county'
    contactPerson?: string
    contactPhone?: string
    description?: string
}

// 定义组件的 props
const props = defineProps<{
    unitLocations?: UnitLocation[]
}>()

// 定义组件的 emits
const emit = defineEmits<{
    'start-navigation': [data: {
        startPoint: string
        endPoint: string
        travelMode: string
        routeData: any
    }]
    'show-route': [data: {
        startCoords: [number, number]
        endCoords: [number, number]
        travelMode: string
    }]
    'clear-route': []
}>()

// 导航模式选项
const navModeOptions = [
    { label: '路径规划', value: 'route' },
    { label: '轨迹记录', value: 'track' },
    { label: '轨迹绘制', value: 'draw' }
]

// 响应式数据
const activeNavMode = ref('route')
const routeLoading = ref(false)
const recordingLoading = ref(false)
const isRecording = ref(false)
const drawMode = ref('draw')
const trackStartTime = ref(Date.now())
const recordingDuration = ref(0)

// 路径规划表单
const routeForm = reactive({
    startPoint: '',
    endPoint: '',
    travelMode: 'driving'
})

// 路线结果
const routeResult = ref<{
    distance: string
    duration: string
    routeCount: number
} | null>(null)

// 当前轨迹
const currentTrack = ref<{
    name: string
    distance: number
    points: Array<[number, number]>
    startTime: number
} | null>(null)

// 轨迹历史
const trackHistory = ref([
    {
        id: '1',
        name: '晨跑轨迹',
        date: '2024-01-15',
        distance: '5.2',
        duration: '32分钟',
        points: []
    },
    {
        id: '2',
        name: '上班路线',
        date: '2024-01-14',
        distance: '12.8',
        duration: '45分钟',
        points: []
    }
])

// 绘制表单
const drawForm = reactive({
    name: '',
    color: '#1890ff',
    width: 4
})

// 当前绘制轨迹
const currentDrawing = ref<Array<[number, number]> | null>(null)

// 已绘制轨迹
const drawnTracks = ref([
    {
        id: '1',
        name: '巡检路线A',
        date: '2024-01-15',
        color: '#1890ff',
        width: 4,
        points: []
    },
    {
        id: '2',
        name: '应急疏散路线',
        date: '2024-01-14',
        color: '#f5222d',
        width: 6,
        points: []
    }
])

// 搜索处理方法
const handleStartPointSearch = (searchText: string) => {
    if (!searchText) {
        startPointOptions.value = []
        return
    }

    const filtered = formattedUnitLocations.value.filter(item =>
        item.searchText.includes(searchText.toLowerCase())
    )

    startPointOptions.value = filtered.slice(0, 10) // 限制显示数量
}

const handleEndPointSearch = (searchText: string) => {
    if (!searchText) {
        endPointOptions.value = []
        return
    }

    const filtered = formattedUnitLocations.value.filter(item =>
        item.searchText.includes(searchText.toLowerCase())
    )

    endPointOptions.value = filtered.slice(0, 10) // 限制显示数量
}

const handleStartPointSelect = (value: string, option: any) => {
    routeForm.startPoint = value
    // 将选中的位置添加到最近使用列表
    addToRecentLocations(value)
    message.success(`已选择起点：${value}`)
}

const handleEndPointSelect = (value: string, option: any) => {
    routeForm.endPoint = value
    // 将选中的位置添加到最近使用列表
    addToRecentLocations(value)
    message.success(`已选择终点：${value}`)
}

// 添加到最近使用位置
const addToRecentLocations = (location: string) => {
    const index = recentLocations.value.indexOf(location)
    if (index > -1) {
        recentLocations.value.splice(index, 1)
    }
    recentLocations.value.unshift(location)
    // 限制最近使用列表长度
    if (recentLocations.value.length > 10) {
        recentLocations.value = recentLocations.value.slice(0, 10)
    }
}

// 事件处理方法
const handleNavModeChange = (mode: string) => {
    activeNavMode.value = mode
    message.info(`切换到${navModeOptions.find(opt => opt.value === mode)?.label}模式`)
}

const searchLocation = (type: 'start' | 'end') => {
    const location = type === 'start' ? routeForm.startPoint : routeForm.endPoint
    if (location.trim()) {
        message.info(`搜索${type === 'start' ? '起点' : '终点'}：${location}`)
        // 这里可以调用地图搜索API
    }
}

const getCurrentLocation = (type: 'start' | 'end') => {
    message.loading('获取当前位置...', 1)
    // 模拟获取当前位置
    setTimeout(() => {
        const currentLocation = '重庆市渝中区人民路1号'
        if (type === 'start') {
            routeForm.startPoint = currentLocation
        } else {
            routeForm.endPoint = currentLocation
        }
        message.success('位置获取成功')
    }, 1000)
}

const planRoute = async () => {
    if (!routeForm.startPoint || !routeForm.endPoint) {
        message.warning('请输入起点和终点')
        return
    }

    routeLoading.value = true
    try {
        // 模拟路径规划API调用
        await new Promise(resolve => setTimeout(resolve, 1500))

        routeResult.value = {
            distance: '12.5公里',
            duration: '28分钟',
            routeCount: 3
        }

        message.success('路线规划完成')
        // 这里可以在地图上绘制路线
    } catch (error) {
        message.error('路线规划失败')
    } finally {
        routeLoading.value = false
    }
}

const clearRoute = () => {
    routeForm.startPoint = ''
    routeForm.endPoint = ''
    routeResult.value = null

    // 通知父组件清除地图上的导航路线
    emit('clear-route')

    message.info('已清除路线')
}

const startNavigation = () => {
    if (!routeResult.value) {
        message.warning('请先规划路线')
        return
    }

    // 显示导航确认对话框
    Modal.confirm({
        title: '🧭 开始导航',
        content: h('div', { style: 'text-align: center; padding: 20px;' }, [
            h('div', { style: 'margin-bottom: 16px;' }, [
                h('div', { style: 'font-size: 16px; font-weight: bold; color: #1890ff; margin-bottom: 8px;' },
                    `从 ${routeForm.startPoint} 到 ${routeForm.endPoint}`),
                h('div', { style: 'color: #666;' }, '确定要开始导航吗？')
            ]),
            h('div', { style: 'background: #f0f2f5; padding: 12px; border-radius: 6px; margin-bottom: 16px;' }, [
                h('div', { style: 'display: flex; justify-content: space-between; margin-bottom: 8px;' }, [
                    h('span', '总距离:'),
                    h('span', { style: 'font-weight: bold;' }, routeResult.value?.distance)
                ]),
                h('div', { style: 'display: flex; justify-content: space-between; margin-bottom: 8px;' }, [
                    h('span', '预计时间:'),
                    h('span', { style: 'font-weight: bold;' }, routeResult.value?.duration)
                ]),
                h('div', { style: 'display: flex; justify-content: space-between;' }, [
                    h('span', '导航方式:'),
                    h('span', { style: 'font-weight: bold;' },
                        routeForm.travelMode === 'driving' ? '🚗 驾车' :
                            routeForm.travelMode === 'walking' ? '🚶 步行' : '🚌 公交')
                ])
            ]),
            h('div', { style: 'background: #e6f7ff; padding: 8px; border-radius: 4px; font-size: 12px; color: #1890ff;' },
                '💡 导航过程中请注意交通安全，遵守交通规则')
        ]),
        okText: '开始导航',
        cancelText: '取消',
        onOk: () => {
            // 启动导航
            message.success('🚀 导航已启动！正在为您规划最佳路线...')

            // 获取起点终点坐标（模拟地理编码）
            const startCoords = getLocationCoords(routeForm.startPoint)
            const endCoords = getLocationCoords(routeForm.endPoint)

            // 触发地图显示路线
            emit('show-route', {
                startCoords,
                endCoords,
                travelMode: routeForm.travelMode
            })

            // 触发导航启动事件
            emit('start-navigation', {
                startPoint: routeForm.startPoint,
                endPoint: routeForm.endPoint,
                travelMode: routeForm.travelMode,
                routeData: routeResult.value
            })

            // 模拟导航启动过程
            setTimeout(() => {
                message.info('📍 GPS定位成功，开始语音导航')
            }, 1000)

            setTimeout(() => {
                message.info('🛣️ 已为您规划最优路线，请按照指示行驶')
            }, 2000)

            setTimeout(() => {
                message.info('🗺️ 导航路线已在地图上显示')
            }, 3000)
        },
        onCancel: () => {
            message.info('已取消导航')
        },
        width: 400,
        centered: true
    })
}

// 优化的地理编码，优先使用单位位置数据
const getLocationCoords = (address: string): [number, number] => {
    // 首先尝试从单位位置数据中查找精确匹配
    if (props.unitLocations) {
        const exactMatch = props.unitLocations.find(unit => unit.name === address)
        if (exactMatch) {
            return [exactMatch.longitude, exactMatch.latitude]
        }

        // 尝试模糊匹配
        const fuzzyMatch = props.unitLocations.find(unit =>
            unit.name.includes(address) ||
            address.includes(unit.name) ||
            unit.address.includes(address)
        )
        if (fuzzyMatch) {
            return [fuzzyMatch.longitude, fuzzyMatch.latitude]
        }
    }

    // 如果单位位置数据中没有找到，使用备用的地理编码数据
    const locationMap: Record<string, [number, number]> = {
        '重庆市渝中区人民路1号': [106.550483, 29.563707],
        '重庆市江北区': [106.574269, 29.606703],
        '重庆市南岸区': [106.644447, 29.523992],
        '重庆市渝北区': [106.631187, 29.718142],
        '重庆市沙坪坝区': [106.456878, 29.541224],
        '重庆市九龙坡区': [106.510426, 29.502677],
        '重庆市大渡口区': [106.482513, 29.484527],
        '重庆市巴南区': [106.540256, 29.402498],
        '重庆市北碚区': [106.437868, 29.825692],
        '重庆市渝中区解放碑': [106.577847, 29.559244]
    }

    // 如果找到精确匹配，返回对应坐标
    if (locationMap[address]) {
        return locationMap[address]
    }

    // 否则根据关键词匹配
    for (const [key, coords] of Object.entries(locationMap)) {
        if (address.includes(key.split('区')[0] + '区') || key.includes(address)) {
            return coords
        }
    }

    // 默认返回重庆市中心坐标
    return [106.550483, 29.563707]
}

const toggleRecording = async () => {
    if (!isRecording.value) {
        // 开始记录
        recordingLoading.value = true
        try {
            await new Promise(resolve => setTimeout(resolve, 500))
            isRecording.value = true
            trackStartTime.value = Date.now()
            currentTrack.value = {
                name: `轨迹_${new Date().toLocaleString()}`,
                distance: 0,
                points: [],
                startTime: Date.now()
            }
            message.success('开始记录轨迹')

            // 模拟轨迹记录
            const recordInterval = setInterval(() => {
                if (!isRecording.value) {
                    clearInterval(recordInterval)
                    return
                }
                if (currentTrack.value) {
                    currentTrack.value.distance += 0.1
                    currentTrack.value.points.push([
                        106.550483 + Math.random() * 0.01,
                        29.563707 + Math.random() * 0.01
                    ])
                }
            }, 2000)

        } finally {
            recordingLoading.value = false
        }
    } else {
        // 停止记录
        isRecording.value = false
        if (currentTrack.value) {
            const track = {
                id: Date.now().toString(),
                name: currentTrack.value.name,
                date: new Date().toLocaleDateString(),
                distance: currentTrack.value.distance.toFixed(1),
                duration: Math.floor((Date.now() - currentTrack.value.startTime) / 60000) + '分钟',
                points: currentTrack.value.points
            }
            trackHistory.value.unshift(track)
            currentTrack.value = null
            message.success('轨迹记录已保存')
        }
    }
}

const viewTrack = (track: any) => {
    message.info(`查看轨迹：${track.name}`)
    // 这里可以在地图上显示轨迹
}

const shareTrack = (track: any) => {
    message.success(`轨迹 "${track.name}" 分享链接已复制到剪贴板`)
    // 这里可以生成分享链接
}

const deleteTrack = (trackId: string) => {
    const index = trackHistory.value.findIndex(track => track.id === trackId)
    if (index > -1) {
        trackHistory.value.splice(index, 1)
        message.success('轨迹已删除')
    }
}

const setDrawMode = (mode: string) => {
    drawMode.value = mode
    message.info(`切换到${mode === 'draw' ? '绘制' : '编辑'}模式`)
}

const clearDrawing = () => {
    currentDrawing.value = null
    message.info('已清除绘制内容')
}

const saveDrawing = () => {
    if (!drawForm.name.trim()) {
        message.warning('请输入轨迹名称')
        return
    }

    if (!currentDrawing.value || currentDrawing.value.length === 0) {
        message.warning('请先绘制轨迹')
        return
    }

    const newTrack = {
        id: Date.now().toString(),
        name: drawForm.name,
        date: new Date().toLocaleDateString(),
        color: drawForm.color,
        width: drawForm.width,
        points: [...currentDrawing.value]
    }

    drawnTracks.value.unshift(newTrack)

    // 重置表单
    drawForm.name = ''
    drawForm.color = '#1890ff'
    drawForm.width = 4
    currentDrawing.value = null

    message.success('轨迹已保存')
}

const editDrawnTrack = (track: any) => {
    message.info(`编辑轨迹：${track.name}`)
    // 这里可以加载轨迹到编辑模式
}

const exportTrack = (track: any) => {
    message.success(`轨迹 "${track.name}" 已导出`)
    // 这里可以导出轨迹数据
}

const deleteDrawnTrack = (trackId: string) => {
    const index = drawnTracks.value.findIndex(track => track.id === trackId)
    if (index > -1) {
        drawnTracks.value.splice(index, 1)
        message.success('轨迹已删除')
    }
}

// 生命周期
onMounted(() => {
    // 初始化导航功能
})

// 监听记录状态，更新计时器
watch(isRecording, (newVal) => {
    if (newVal) {
        const timer = setInterval(() => {
            if (!isRecording.value) {
                clearInterval(timer)
                return
            }
            recordingDuration.value += 1000
        }, 1000)
    } else {
        recordingDuration.value = 0
    }
})
</script>

<style scoped lang="scss">
.navigation-panel {
    padding: 16px;
    height: 100%;
    overflow-y: auto;

    .navigation-header {
        margin-bottom: 16px;
    }

    .route-planning,
    .track-recording,
    .track-drawing {
        .ant-card {
            .ant-card-body {
                padding: 16px;
            }
        }
    }

    .route-form {
        .ant-form-item {
            margin-bottom: 16px;
        }
    }

    .route-result {
        margin-top: 16px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
    }

    .track-controls {
        margin-bottom: 16px;
    }

    .track-info {
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-top: 12px;
    }

    .track-history,
    .drawn-tracks {
        .ant-list {
            .ant-list-item {
                padding: 8px 0;

                .ant-list-item-meta {
                    .ant-list-item-meta-title {
                        font-size: 14px;
                        margin-bottom: 4px;
                    }

                    .ant-list-item-meta-description {
                        font-size: 12px;
                        color: #666;
                    }
                }
            }
        }
    }

    .draw-controls {
        .ant-form-item {
            margin-bottom: 12px;
        }
    }

    // AutoComplete 选项样式
    .location-option {
        padding: 4px 0;

        .location-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 2px;
        }

        .location-detail {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.2;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        padding: 12px;

        .ant-card {
            .ant-card-body {
                padding: 12px;
            }
        }

        .ant-segmented {
            width: 100%;
        }

        .location-option {
            .location-name {
                font-size: 13px;
            }

            .location-detail {
                font-size: 11px;
            }
        }
    }
}

// 滚动条样式
.navigation-panel {
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}
</style>