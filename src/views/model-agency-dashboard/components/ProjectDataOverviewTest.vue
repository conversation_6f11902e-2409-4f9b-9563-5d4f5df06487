<template>
  <div class="project-data-overview-test">
    <h2>ProjectDataOverview组件测试</h2>
    
    <!-- 组件展示 -->
    <div class="component-demo">
      <ProjectDataOverview 
        :municipal-data="testMunicipalData"
        :district-data="testDistrictData"
        :selected-district="selectedDistrict"
        @district-select="handleDistrictSelect"
        @drill-down="handleDrillDown"
        @export="handleExport"
      />
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="测试控制">
        <a-space direction="vertical" style="width: 100%">
          <a-space>
            <a-button @click="loadTestData" type="primary">加载测试数据</a-button>
            <a-button @click="clearData">清空数据</a-button>
            <a-button @click="randomizeData">随机化数据</a-button>
            <a-button @click="addDistrictData">添加区县数据</a-button>
          </a-space>
          
          <a-divider />
          
          <div>
            <h4>选择区县</h4>
            <a-select 
              v-model:value="selectedDistrict" 
              style="width: 200px" 
              placeholder="选择区县"
              allow-clear
            >
              <a-select-option value="">全部区县</a-select-option>
              <a-select-option 
                v-for="district in testDistrictData" 
                :key="district.regionName" 
                :value="district.regionName"
              >
                {{ district.regionName }}
              </a-select-option>
            </a-select>
          </div>
          
          <a-divider />
          
          <div>
            <h4>市级数据调整</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="项目总数">
                  <a-input-number 
                    v-model:value="testMunicipalData.totalProjects" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="完成项目">
                  <a-input-number 
                    v-model:value="testMunicipalData.completedProjects" 
                    :min="0" 
                    :max="testMunicipalData.totalProjects"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="申报单位">
                  <a-input-number 
                    v-model:value="testMunicipalData.applicantUnits" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-space>
      </a-card>
    </div>
    
    <!-- 事件日志 -->
    <div class="event-log">
      <a-card title="事件日志">
        <div class="log-container">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <a-tag :color="getLogColor(log.type)">{{ log.type }}</a-tag>
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
            <div v-if="log.data" class="log-data">
              <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <a-button @click="clearLogs" size="small" style="margin-top: 8px;">清空日志</a-button>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import ProjectDataOverview from './ProjectDataOverview.vue'
import type { ProjectData } from '../types'
import { mockDataService } from '../mock/data'

// 响应式数据
const selectedDistrict = ref('')
const eventLogs = ref<Array<{ type: string; time: string; message: string; data?: any }>>([])

const testMunicipalData = ref<ProjectData>({
  totalProjects: 156,
  completedProjects: 142,
  completionRate: 91.0,
  applicantUnits: 89,
  cultivationUnits: 67,
  benchmarkUnits: 34,
  applicantRatio: 57.1,
  cultivationRatio: 43.0,
  benchmarkRatio: 21.8,
  totalIndicators: 1247,
  smartFilterIndicators: 892,
  dataResources: 2156,
  dataSourceUnits: 78,
  regionType: 'municipal',
  regionName: '重庆市'
})

const testDistrictData = ref<ProjectData[]>([
  {
    totalProjects: 25,
    completedProjects: 22,
    completionRate: 88.0,
    applicantUnits: 15,
    cultivationUnits: 12,
    benchmarkUnits: 8,
    applicantRatio: 60.0,
    cultivationRatio: 48.0,
    benchmarkRatio: 32.0,
    totalIndicators: 180,
    smartFilterIndicators: 125,
    dataResources: 320,
    dataSourceUnits: 12,
    regionType: 'district',
    regionName: '渝中区'
  },
  {
    totalProjects: 18,
    completedProjects: 15,
    completionRate: 83.3,
    applicantUnits: 12,
    cultivationUnits: 9,
    benchmarkUnits: 5,
    applicantRatio: 66.7,
    cultivationRatio: 50.0,
    benchmarkRatio: 27.8,
    totalIndicators: 145,
    smartFilterIndicators: 98,
    dataResources: 280,
    dataSourceUnits: 10,
    regionType: 'district',
    regionName: '江北区'
  }
])

// 计算属性
const totalDistrictProjects = computed(() => {
  return testDistrictData.value.reduce((sum, district) => sum + district.totalProjects, 0)
})

// 方法
const loadTestData = async () => {
  addLog('INFO', '开始加载测试数据')
  
  try {
    const response = await mockDataService.fetchProjectStats()
    testMunicipalData.value = response.data.municipal
    testDistrictData.value = response.data.districts
    
    addLog('SUCCESS', '测试数据加载成功', { 
      municipal: response.data.municipal.totalProjects,
      districts: response.data.districts.length 
    })
  } catch (error) {
    addLog('ERROR', `数据加载失败: ${error}`)
  }
}

const clearData = () => {
  testMunicipalData.value = {
    totalProjects: 0,
    completedProjects: 0,
    completionRate: 0,
    applicantUnits: 0,
    cultivationUnits: 0,
    benchmarkUnits: 0,
    applicantRatio: 0,
    cultivationRatio: 0,
    benchmarkRatio: 0,
    totalIndicators: 0,
    smartFilterIndicators: 0,
    dataResources: 0,
    dataSourceUnits: 0,
    regionType: 'municipal',
    regionName: '重庆市'
  }
  
  testDistrictData.value = []
  selectedDistrict.value = ''
  
  addLog('INFO', '数据已清空')
}

const randomizeData = () => {
  // 随机化市级数据
  testMunicipalData.value.totalProjects = Math.floor(Math.random() * 200) + 50
  testMunicipalData.value.completedProjects = Math.floor(Math.random() * testMunicipalData.value.totalProjects)
  testMunicipalData.value.completionRate = Math.round((testMunicipalData.value.completedProjects / testMunicipalData.value.totalProjects) * 100)
  
  // 随机化区县数据
  testDistrictData.value.forEach(district => {
    district.totalProjects = Math.floor(Math.random() * 50) + 10
    district.completedProjects = Math.floor(Math.random() * district.totalProjects)
    district.completionRate = Math.round((district.completedProjects / district.totalProjects) * 100)
  })
  
  addLog('INFO', '数据已随机化')
}

const addDistrictData = () => {
  const districts = ['沙坪坝区', '九龙坡区', '南岸区', '渝北区', '巴南区']
  const randomDistrict = districts[Math.floor(Math.random() * districts.length)]
  
  const newDistrict: ProjectData = {
    totalProjects: Math.floor(Math.random() * 30) + 10,
    completedProjects: 0,
    completionRate: 0,
    applicantUnits: Math.floor(Math.random() * 15) + 5,
    cultivationUnits: Math.floor(Math.random() * 10) + 3,
    benchmarkUnits: Math.floor(Math.random() * 5) + 1,
    applicantRatio: Math.random() * 30 + 40,
    cultivationRatio: Math.random() * 25 + 30,
    benchmarkRatio: Math.random() * 20 + 10,
    totalIndicators: Math.floor(Math.random() * 100) + 80,
    smartFilterIndicators: Math.floor(Math.random() * 60) + 40,
    dataResources: Math.floor(Math.random() * 200) + 150,
    dataSourceUnits: Math.floor(Math.random() * 8) + 5,
    regionType: 'district',
    regionName: randomDistrict
  }
  
  newDistrict.completedProjects = Math.floor(Math.random() * newDistrict.totalProjects)
  newDistrict.completionRate = Math.round((newDistrict.completedProjects / newDistrict.totalProjects) * 100)
  
  testDistrictData.value.push(newDistrict)
  
  addLog('INFO', `添加了新的区县数据: ${randomDistrict}`)
}

const handleDistrictSelect = (district: string) => {
  selectedDistrict.value = district
  addLog('DISTRICT_SELECT', `选择了区县: ${district || '全部区县'}`)
}

const handleDrillDown = (type: string, data: any) => {
  addLog('DRILL_DOWN', `数据钻取: ${type}`, data)
}

const handleExport = (type: string, data: any) => {
  addLog('EXPORT', `导出数据: ${type}`, data)
}

const addLog = (type: string, message: string, data?: any) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ type, time, message, data })
  
  // 保持最多30条日志
  if (eventLogs.value.length > 30) {
    eventLogs.value = eventLogs.value.slice(0, 30)
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

const getLogColor = (type: string) => {
  const colors = {
    INFO: 'blue',
    SUCCESS: 'green',
    ERROR: 'red',
    DISTRICT_SELECT: 'purple',
    DRILL_DOWN: 'orange',
    EXPORT: 'cyan'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 监听数据变化
watch(() => testMunicipalData.value.totalProjects, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    testMunicipalData.value.completionRate = testMunicipalData.value.totalProjects > 0 
      ? Math.round((testMunicipalData.value.completedProjects / testMunicipalData.value.totalProjects) * 100)
      : 0
  }
})

watch(() => testMunicipalData.value.completedProjects, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    testMunicipalData.value.completionRate = testMunicipalData.value.totalProjects > 0 
      ? Math.round((testMunicipalData.value.completedProjects / testMunicipalData.value.totalProjects) * 100)
      : 0
  }
})

// 生命周期
onMounted(() => {
  addLog('INFO', 'ProjectDataOverview测试组件已加载')
})
</script>

<style scoped>
.project-data-overview-test {
  padding: 20px;
}

.component-demo {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.control-panel {
  margin-bottom: 24px;
}

.event-log {
  .log-container {
    max-height: 400px;
    overflow-y: auto;
    
    .log-item {
      margin-bottom: 12px;
      padding: 8px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
      
      .log-time {
        margin: 0 8px;
        color: #666;
        min-width: 80px;
        display: inline-block;
      }
      
      .log-message {
        flex: 1;
        margin-right: 8px;
      }
      
      .log-data {
        margin-top: 8px;
        padding: 8px;
        background: #f5f5f5;
        border-radius: 4px;
        
        pre {
          margin: 0;
          font-size: 11px;
          max-height: 100px;
          overflow-y: auto;
        }
      }
    }
  }
}
</style>
