<template>
    <div class="map-control-panel">
        <!-- 主控制面板 -->
        <a-card class="control-card" size="small" title="地图控制">
            <template #extra>
                <a-button type="text" size="small" @click="togglePanel">
                    <template #icon>
                        <SettingOutlined />
                    </template>
                </a-button>
            </template>

            <div class="control-content" v-show="panelVisible">
                <!-- 显示模式切换 -->
                <div class="control-section">
                    <h4>显示模式</h4>
                    <a-space direction="vertical" style="width: 100%">
                        <a-checkbox v-model:checked="showUnitMarkers" @change="onShowMarkersChange">
                            显示单位标注
                        </a-checkbox>
                        <a-checkbox v-model:checked="showModelOrgansOnly" @change="onModelOrgansOnlyChange">
                            仅显示模范机关
                        </a-checkbox>
                        <a-checkbox v-model:checked="showWarningUnits" @change="onWarningUnitsChange">
                            仅显示预警单位
                        </a-checkbox>
                    </a-space>
                </div>

                <!-- 党建指数等级筛选 -->
                <div class="control-section">
                    <h4>党建指数等级</h4>
                    <a-select v-model:value="selectedPartyBuildingLevel" placeholder="选择等级" style="width: 100%"
                        allow-clear @change="onLevelChange">
                        <a-select-option value="">全部等级</a-select-option>
                        <a-select-option value="A">A级 (90分以上)</a-select-option>
                        <a-select-option value="B">B级 (80-89分)</a-select-option>
                        <a-select-option value="C">C级 (70-79分)</a-select-option>
                        <a-select-option value="D">D级 (60-69分)</a-select-option>
                        <a-select-option value="E">E级 (60分以下)</a-select-option>
                    </a-select>
                </div>

                <!-- 快速筛选按钮 -->
                <div class="control-section">
                    <h4>快速筛选</h4>
                    <a-space wrap>
                        <a-button size="small" @click="showAllUnits">全部单位</a-button>
                        <a-button size="small" type="primary" @click="showModelOrganOnly">模范机关</a-button>
                        <a-button size="small" danger @click="showWarningOnly">预警单位</a-button>
                        <a-button size="small" @click="showExcellentUnits">优秀单位</a-button>
                    </a-space>
                </div>

                <!-- 统计信息 -->
                <div class="control-section">
                    <h4>统计信息</h4>
                    <a-row :gutter="8">
                        <a-col :span="12">
                            <a-statistic title="总单位数" :value="totalUnits" :value-style="{ fontSize: '14px' }" />
                        </a-col>
                        <a-col :span="12">
                            <a-statistic title="模范机关" :value="modelOrganCount"
                                :value-style="{ fontSize: '14px', color: '#52c41a' }" />
                        </a-col>
                    </a-row>
                    <a-row :gutter="8" style="margin-top: 8px">
                        <a-col :span="12">
                            <a-statistic title="预警单位" :value="warningUnitCount"
                                :value-style="{ fontSize: '14px', color: '#ff4d4f' }" />
                        </a-col>
                        <a-col :span="12">
                            <a-statistic title="平均指数" :value="averageIndex" :precision="1"
                                :value-style="{ fontSize: '14px', color: '#1890ff' }" />
                        </a-col>
                    </a-row>
                </div>
            </div>
        </a-card>

        <!-- 搜索面板 -->
        <a-card class="search-card" size="small" title="搜索导航">
            <div class="search-content">
                <a-input-search v-model:value="searchKeyword" placeholder="搜索单位名称或地址" @search="onSearch"
                    @change="onSearchChange" style="margin-bottom: 12px" />

                <!-- 搜索结果 -->
                <div v-if="searchResults.length > 0" class="search-results">
                    <h5>搜索结果 ({{ searchResults.length }})</h5>
                    <div class="result-list">
                        <div v-for="result in searchResults" :key="result.id" class="result-item"
                            @click="onResultClick(result)">
                            <div class="result-info">
                                <div class="result-name">{{ result.name }}</div>
                                <div class="result-address">{{ result.address }}</div>
                            </div>
                            <a-tag :color="getResultTypeColor(result.type)">
                                {{ getResultTypeText(result.type) }}
                            </a-tag>
                        </div>
                    </div>
                </div>

                <!-- 热门搜索 -->
                <div v-else class="hot-searches">
                    <h5>热门搜索</h5>
                    <a-space wrap>
                        <a-tag v-for="tag in hotSearchTags" :key="tag" @click="onHotSearchClick(tag)"
                            style="cursor: pointer">
                            {{ tag }}
                        </a-tag>
                    </a-space>
                </div>
            </div>
        </a-card>

        <!-- 图例面板 -->
        <a-card class="legend-card" size="small" title="图例说明">
            <div class="legend-content">
                <div class="legend-section">
                    <h5>党建指数等级</h5>
                    <div class="legend-items">
                        <div class="legend-item">
                            <span class="legend-marker level-a"></span>
                            <span class="legend-text">A级 (90分以上)</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker level-b"></span>
                            <span class="legend-text">B级 (80-89分)</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker level-c"></span>
                            <span class="legend-text">C级 (70-79分)</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker level-d"></span>
                            <span class="legend-text">D级 (60-69分)</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker level-e"></span>
                            <span class="legend-text">E级 (60分以下)</span>
                        </div>
                    </div>
                </div>

                <div class="legend-section">
                    <h5>特殊标识</h5>
                    <div class="legend-items">
                        <div class="legend-item">
                            <span class="legend-marker model-organ"></span>
                            <span class="legend-text">模范机关</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker warning-unit"></span>
                            <span class="legend-text">预警单位</span>
                        </div>
                    </div>
                </div>

                <div class="legend-section">
                    <h5>单位类型</h5>
                    <div class="legend-items">
                        <div class="legend-item">
                            <span class="legend-marker municipal"></span>
                            <span class="legend-text">市级机关</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-marker district"></span>
                            <span class="legend-text">区县机关</span>
                        </div>
                    </div>
                </div>
            </div>
        </a-card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import type { OrganUnit, MapSearchResult } from '../types/map'

// Props
interface Props {
    organUnits?: OrganUnit[]
    searchResults?: MapSearchResult[]
    showUnitMarkers?: boolean
    showModelOrgansOnly?: boolean
    showWarningUnits?: boolean
    selectedPartyBuildingLevel?: 'A' | 'B' | 'C' | 'D' | 'E' | ''
}

const props = withDefaults(defineProps<Props>(), {
    organUnits: () => [],
    searchResults: () => [],
    showUnitMarkers: true,
    showModelOrgansOnly: false,
    showWarningUnits: false,
    selectedPartyBuildingLevel: ''
})

// Emits
const emit = defineEmits<{
    'update:showUnitMarkers': [value: boolean]
    'update:showModelOrgansOnly': [value: boolean]
    'update:showWarningUnits': [value: boolean]
    'update:selectedPartyBuildingLevel': [value: 'A' | 'B' | 'C' | 'D' | 'E' | '']
    'search': [keyword: string]
    'result-click': [result: MapSearchResult]
    'filter-change': [filters: any]
}>()

// 响应式数据
const panelVisible = ref(true)
const searchKeyword = ref('')

// 双向绑定的响应式数据
const showUnitMarkers = ref(props.showUnitMarkers)
const showModelOrgansOnly = ref(props.showModelOrgansOnly)
const showWarningUnits = ref(props.showWarningUnits)
const selectedPartyBuildingLevel = ref(props.selectedPartyBuildingLevel)

// 热门搜索标签
const hotSearchTags = ref([
    '市委组织部',
    '发改委',
    '财政局',
    '教育局',
    '卫健委',
    '公安局',
    '模范机关',
    '渝中区',
    '江北区'
])

// 计算属性
const totalUnits = computed(() => props.organUnits.length)

const modelOrganCount = computed(() =>
    props.organUnits.filter(unit => unit.isModelOrgan).length
)

const warningUnitCount = computed(() =>
    props.organUnits.filter(unit => unit.warningData.warningItems > 0).length
)

const averageIndex = computed(() => {
    if (props.organUnits.length === 0) return 0
    const total = props.organUnits.reduce((sum, unit) => sum + unit.partyBuildingIndex, 0)
    return total / props.organUnits.length
})

// 监听props变化
watch(() => props.showUnitMarkers, (newVal) => {
    showUnitMarkers.value = newVal
})

watch(() => props.showModelOrgansOnly, (newVal) => {
    showModelOrgansOnly.value = newVal
})

watch(() => props.showWarningUnits, (newVal) => {
    showWarningUnits.value = newVal
})

watch(() => props.selectedPartyBuildingLevel, (newVal) => {
    selectedPartyBuildingLevel.value = newVal
})

// 方法
const togglePanel = () => {
    panelVisible.value = !panelVisible.value
}

const onShowMarkersChange = (checked: boolean) => {
    emit('update:showUnitMarkers', checked)
}

const onModelOrgansOnlyChange = (checked: boolean) => {
    emit('update:showModelOrgansOnly', checked)
    if (checked) {
        showWarningUnits.value = false
        emit('update:showWarningUnits', false)
    }
}

const onWarningUnitsChange = (checked: boolean) => {
    emit('update:showWarningUnits', checked)
    if (checked) {
        showModelOrgansOnly.value = false
        emit('update:showModelOrgansOnly', false)
    }
}

const onLevelChange = (value: 'A' | 'B' | 'C' | 'D' | 'E' | '') => {
    emit('update:selectedPartyBuildingLevel', value)
}

const showAllUnits = () => {
    showModelOrgansOnly.value = false
    showWarningUnits.value = false
    selectedPartyBuildingLevel.value = ''

    emit('update:showModelOrgansOnly', false)
    emit('update:showWarningUnits', false)
    emit('update:selectedPartyBuildingLevel', '')
}

const showModelOrganOnly = () => {
    showModelOrgansOnly.value = true
    showWarningUnits.value = false

    emit('update:showModelOrgansOnly', true)
    emit('update:showWarningUnits', false)
}

const showWarningOnly = () => {
    showWarningUnits.value = true
    showModelOrgansOnly.value = false

    emit('update:showWarningUnits', true)
    emit('update:showModelOrgansOnly', false)
}

const showExcellentUnits = () => {
    selectedPartyBuildingLevel.value = 'A'
    showModelOrgansOnly.value = false
    showWarningUnits.value = false

    emit('update:selectedPartyBuildingLevel', 'A')
    emit('update:showModelOrgansOnly', false)
    emit('update:showWarningUnits', false)
}

const onSearch = (value: string) => {
    emit('search', value)
}

const onSearchChange = () => {
    if (searchKeyword.value.trim()) {
        emit('search', searchKeyword.value)
    }
}

const onResultClick = (result: MapSearchResult) => {
    emit('result-click', result)
}

const onHotSearchClick = (tag: string) => {
    searchKeyword.value = tag
    emit('search', tag)
}

const getResultTypeColor = (type: string) => {
    const colorMap = {
        unit: 'blue',
        district: 'green',
        address: 'orange'
    }
    return colorMap[type as keyof typeof colorMap] || 'default'
}

const getResultTypeText = (type: string) => {
    const textMap = {
        unit: '单位',
        district: '区县',
        address: '地址'
    }
    return textMap[type as keyof typeof textMap] || type
}
</script>

<style scoped lang="scss">
.map-control-panel {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 280px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .control-card,
    .search-card,
    .legend-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(8px);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        :deep(.ant-card-head) {
            padding: 8px 12px;
            min-height: auto;
            border-bottom: 1px solid #f0f0f0;

            .ant-card-head-title {
                font-size: 14px;
                font-weight: 600;
            }
        }

        :deep(.ant-card-body) {
            padding: 12px;
        }
    }

    .control-content {
        .control-section {
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0;
            }

            h4 {
                margin: 0 0 8px 0;
                font-size: 13px;
                font-weight: 600;
                color: #333;
            }

            h5 {
                margin: 0 0 6px 0;
                font-size: 12px;
                font-weight: 500;
                color: #666;
            }
        }
    }

    .search-content {
        .search-results {
            .result-list {
                max-height: 200px;
                overflow-y: auto;

                .result-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px;
                    margin-bottom: 4px;
                    background: #f8f9fa;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s;

                    &:hover {
                        background: #e9ecef;
                    }

                    .result-info {
                        flex: 1;
                        min-width: 0;

                        .result-name {
                            font-size: 13px;
                            font-weight: 500;
                            color: #333;
                            margin-bottom: 2px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .result-address {
                            font-size: 11px;
                            color: #666;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
        }

        .hot-searches {
            h5 {
                margin: 0 0 8px 0;
                font-size: 12px;
                font-weight: 500;
                color: #666;
            }
        }
    }

    .legend-content {
        .legend-section {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }

            h5 {
                margin: 0 0 6px 0;
                font-size: 12px;
                font-weight: 500;
                color: #666;
            }

            .legend-items {
                .legend-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .legend-marker {
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        margin-right: 8px;
                        border: 1px solid #fff;
                        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);

                        &.level-a {
                            background: #52c41a;
                        }

                        &.level-b {
                            background: #1890ff;
                        }

                        &.level-c {
                            background: #faad14;
                        }

                        &.level-d {
                            background: #ff7a45;
                        }

                        &.level-e {
                            background: #ff4d4f;
                        }

                        &.model-organ {
                            background: #52c41a;
                            border: 2px solid gold;
                            animation: pulse 2s infinite;
                        }

                        &.warning-unit {
                            background: #ff4d4f;
                            border: 2px solid #ff4d4f;
                        }

                        &.municipal {
                            border-width: 2px;
                            background: #1890ff;
                        }

                        &.district {
                            border-width: 1px;
                            background: #52c41a;
                        }
                    }

                    .legend-text {
                        font-size: 11px;
                        color: #666;
                    }
                }
            }
        }
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.7);
    }

    70% {
        box-shadow: 0 0 0 6px rgba(255, 215, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .map-control-panel {
        width: 260px;
        top: 8px;
        right: 8px;
        gap: 8px;

        .control-card,
        .search-card,
        .legend-card {
            :deep(.ant-card-body) {
                padding: 8px;
            }
        }
    }
}

@media (max-width: 480px) {
    .map-control-panel {
        width: calc(100vw - 32px);
        left: 16px;
        right: 16px;
        top: 8px;
    }
}
</style>