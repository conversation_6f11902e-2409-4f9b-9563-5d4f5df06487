<template>
  <div class="project-data-overview">
    <!-- 标题和操作栏 -->
    <div class="header-section">
      <div class="title-area">
        <h3>项目创建数据概览</h3>
        <a-space>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
          <a-dropdown>
            <a-button>
              <template #icon><download-outlined /></template>
              导出数据
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleExport">
                <a-menu-item key="excel">
                  <file-excel-outlined />
                  导出Excel
                </a-menu-item>
                <a-menu-item key="pdf">
                  <file-pdf-outlined />
                  导出PDF
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 数据视图切换 -->
    <div class="data-tabs">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="municipal" tab="市级机关数据">
          <div class="municipal-data">
            <!-- 市级项目概览 -->
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="12" :md="6">
                <a-card class="data-card">
                  <a-statistic
                    title="项目总数"
                    :value="municipalData?.totalProjects || 0"
                    suffix="个"
                    :value-style="{ color: '#1890ff' }"
                  >
                    <template #prefix>
                      <project-outlined />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-card class="data-card">
                  <a-statistic
                    title="完成评选"
                    :value="municipalData?.completedProjects || 0"
                    suffix="个"
                    :value-style="{ color: '#52c41a' }"
                  >
                    <template #prefix>
                      <check-circle-outlined />
                    </template>
                  </a-statistic>
                  <div class="progress-info">
                    <a-progress 
                      :percent="municipalData?.completionRate || 0" 
                      size="small"
                      :show-info="false"
                      stroke-color="#52c41a"
                    />
                    <span class="progress-text">完成率 {{ municipalData?.completionRate || 0 }}%</span>
                  </div>
                </a-card>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-card class="data-card">
                  <a-statistic
                    title="指标总数"
                    :value="municipalData?.totalIndicators || 0"
                    suffix="项"
                    :value-style="{ color: '#722ed1' }"
                  >
                    <template #prefix>
                      <bar-chart-outlined />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-card class="data-card">
                  <a-statistic
                    title="数据资源"
                    :value="municipalData?.dataResources || 0"
                    suffix="个"
                    :value-style="{ color: '#eb2f96' }"
                  >
                    <template #prefix>
                      <database-outlined />
                    </template>
                  </a-statistic>
                </a-card>
              </a-col>
            </a-row>

            <!-- 单位类型分布 -->
            <a-card title="单位类型分布" class="chart-card" style="margin-top: 16px;">
              <a-row :gutter="[24, 24]">
                <a-col :xs="24" :sm="8">
                  <div class="unit-type-item">
                    <div class="unit-header">
                      <span class="unit-title">申报单位</span>
                      <span class="unit-count">{{ municipalData?.applicantUnits || 0 }}个</span>
                    </div>
                    <a-progress 
                      :percent="municipalData?.applicantRatio || 0"
                      stroke-color="#1890ff"
                      :show-info="false"
                    />
                    <div class="unit-footer">
                      <span class="ratio-text">占比 {{ municipalData?.applicantRatio || 0 }}%</span>
                      <a-button size="small" type="link" @click="handleDrillDown('applicant')">
                        查看详情
                      </a-button>
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <div class="unit-type-item">
                    <div class="unit-header">
                      <span class="unit-title">培育单位</span>
                      <span class="unit-count">{{ municipalData?.cultivationUnits || 0 }}个</span>
                    </div>
                    <a-progress 
                      :percent="municipalData?.cultivationRatio || 0"
                      stroke-color="#faad14"
                      :show-info="false"
                    />
                    <div class="unit-footer">
                      <span class="ratio-text">占比 {{ municipalData?.cultivationRatio || 0 }}%</span>
                      <a-button size="small" type="link" @click="handleDrillDown('cultivation')">
                        查看详情
                      </a-button>
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <div class="unit-type-item">
                    <div class="unit-header">
                      <span class="unit-title">标杆单位</span>
                      <span class="unit-count">{{ municipalData?.benchmarkUnits || 0 }}个</span>
                    </div>
                    <a-progress 
                      :percent="municipalData?.benchmarkRatio || 0"
                      stroke-color="#f5222d"
                      :show-info="false"
                    />
                    <div class="unit-footer">
                      <span class="ratio-text">占比 {{ municipalData?.benchmarkRatio || 0 }}%</span>
                      <a-button size="small" type="link" @click="handleDrillDown('benchmark')">
                        查看详情
                      </a-button>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </a-card>

            <!-- 数据资源统计 -->
            <a-card title="数据资源统计" class="chart-card" style="margin-top: 16px;">
              <a-row :gutter="[16, 16]">
                <a-col :xs="24" :sm="12">
                  <div class="resource-item">
                    <div class="resource-header">
                      <filter-outlined class="resource-icon" />
                      <span class="resource-title">智能筛选指标</span>
                    </div>
                    <div class="resource-content">
                      <span class="resource-value">{{ municipalData?.smartFilterIndicators || 0 }}</span>
                      <span class="resource-unit">项</span>
                    </div>
                    <div class="resource-progress">
                      <a-progress 
                        :percent="getIndicatorRatio(municipalData?.smartFilterIndicators, municipalData?.totalIndicators)"
                        size="small"
                        stroke-color="#13c2c2"
                        :show-info="false"
                      />
                      <span class="progress-text">
                        占总指标 {{ getIndicatorRatio(municipalData?.smartFilterIndicators, municipalData?.totalIndicators) }}%
                      </span>
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="12">
                  <div class="resource-item">
                    <div class="resource-header">
                      <cluster-outlined class="resource-icon" />
                      <span class="resource-title">数据来源单位</span>
                    </div>
                    <div class="resource-content">
                      <span class="resource-value">{{ municipalData?.dataSourceUnits || 0 }}</span>
                      <span class="resource-unit">个</span>
                    </div>
                    <div class="resource-progress">
                      <a-tag color="green">覆盖率 85%</a-tag>
                    </div>
                  </div>
                </a-col>
              </a-row>
            </a-card>
          </div>
        </a-tab-pane>

        <a-tab-pane key="districts" tab="区县机关数据">
          <div class="districts-data">
            <!-- 区县数据列表 -->
            <a-card title="各区县项目数据" class="chart-card">
              <div class="district-search">
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索区县名称"
                  style="width: 300px; margin-bottom: 16px;"
                  @search="handleSearch"
                />
              </div>
              
              <a-table
                :columns="districtColumns"
                :data-source="filteredDistrictData"
                :pagination="{ pageSize: 10, showSizeChanger: true }"
                :loading="loading"
                size="middle"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'regionName'">
                    <a-button type="link" @click="handleDistrictClick(record)">
                      {{ record.regionName }}
                    </a-button>
                  </template>
                  <template v-else-if="column.key === 'completionRate'">
                    <div class="completion-cell">
                      <a-progress 
                        :percent="record.completionRate" 
                        size="small"
                        :stroke-color="getProgressColor(record.completionRate)"
                      />
                    </div>
                  </template>
                  <template v-else-if="column.key === 'unitRatios'">
                    <a-space direction="vertical" size="small">
                      <a-tag color="blue">申报 {{ record.applicantRatio }}%</a-tag>
                      <a-tag color="orange">培育 {{ record.cultivationRatio }}%</a-tag>
                      <a-tag color="red">标杆 {{ record.benchmarkRatio }}%</a-tag>
                    </a-space>
                  </template>
                  <template v-else-if="column.key === 'actions'">
                    <a-space>
                      <a-button size="small" @click="handleDistrictDetail(record)">
                        详情
                      </a-button>
                      <a-button size="small" @click="handleDistrictExport(record)">
                        导出
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="detailTitle"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedData" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="区县名称">
            {{ selectedData.regionName }}
          </a-descriptions-item>
          <a-descriptions-item label="项目总数">
            {{ selectedData.totalProjects }}个
          </a-descriptions-item>
          <a-descriptions-item label="完成项目">
            {{ selectedData.completedProjects }}个
          </a-descriptions-item>
          <a-descriptions-item label="完成率">
            {{ selectedData.completionRate }}%
          </a-descriptions-item>
          <a-descriptions-item label="申报单位">
            {{ selectedData.applicantUnits }}个 ({{ selectedData.applicantRatio }}%)
          </a-descriptions-item>
          <a-descriptions-item label="培育单位">
            {{ selectedData.cultivationUnits }}个 ({{ selectedData.cultivationRatio }}%)
          </a-descriptions-item>
          <a-descriptions-item label="标杆单位">
            {{ selectedData.benchmarkUnits }}个 ({{ selectedData.benchmarkRatio }}%)
          </a-descriptions-item>
          <a-descriptions-item label="指标总数">
            {{ selectedData.totalIndicators }}项
          </a-descriptions-item>
          <a-descriptions-item label="数据资源">
            {{ selectedData.dataResources }}个
          </a-descriptions-item>
          <a-descriptions-item label="数据来源单位">
            {{ selectedData.dataSourceUnits }}个
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large" tip="加载项目数据中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { ProjectData, ProjectDataOverviewProps } from '../types'
import { mockDataService } from '../mock/data'

// Props定义
interface Props {
  municipalData?: ProjectData
  districtData?: ProjectData[]
  selectedDistrict?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedDistrict: ''
})

// Emits定义
const emit = defineEmits<{
  'district-select': [district: string]
  'drill-down': [type: string, data: any]
  'export': [type: string, data: any]
}>()

// 响应式数据
const loading = ref(false)
const activeTab = ref('municipal')
const searchKeyword = ref('')
const detailVisible = ref(false)
const detailTitle = ref('')
const selectedData = ref<ProjectData | null>(null)

const municipalData = ref<ProjectData | null>(null)
const districtData = ref<ProjectData[]>([])

// 表格列定义
const districtColumns = [
  {
    title: '区县名称',
    dataIndex: 'regionName',
    key: 'regionName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '项目总数',
    dataIndex: 'totalProjects',
    key: 'totalProjects',
    width: 100,
    sorter: (a: ProjectData, b: ProjectData) => a.totalProjects - b.totalProjects
  },
  {
    title: '完成项目',
    dataIndex: 'completedProjects',
    key: 'completedProjects',
    width: 100,
    sorter: (a: ProjectData, b: ProjectData) => a.completedProjects - b.completedProjects
  },
  {
    title: '完成率',
    dataIndex: 'completionRate',
    key: 'completionRate',
    width: 120
  },
  {
    title: '单位比例',
    key: 'unitRatios',
    width: 150
  },
  {
    title: '指标数',
    dataIndex: 'totalIndicators',
    key: 'totalIndicators',
    width: 100,
    sorter: (a: ProjectData, b: ProjectData) => a.totalIndicators - b.totalIndicators
  },
  {
    title: '数据资源',
    dataIndex: 'dataResources',
    key: 'dataResources',
    width: 100,
    sorter: (a: ProjectData, b: ProjectData) => a.dataResources - b.dataResources
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 计算属性
const filteredDistrictData = computed(() => {
  if (!searchKeyword.value) {
    return districtData.value
  }
  
  return districtData.value.filter(item => 
    item.regionName?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await mockDataService.fetchProjectStats()
    municipalData.value = response.data.municipal
    districtData.value = response.data.districts
  } catch (error) {
    console.error('加载项目数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleExport = ({ key }: { key: string }) => {
  const data = activeTab.value === 'municipal' ? municipalData.value : districtData.value
  emit('export', key, { type: activeTab.value, data })
}

const handleDrillDown = (type: string) => {
  emit('drill-down', type, municipalData.value)
}

const handleSearch = (value: string) => {
  searchKeyword.value = value
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  // 处理表格变化
  console.log('Table change:', pagination, filters, sorter)
}

const handleDistrictClick = (record: ProjectData) => {
  emit('district-select', record.regionName || '')
}

const handleDistrictDetail = (record: ProjectData) => {
  selectedData.value = record
  detailTitle.value = `${record.regionName} - 项目数据详情`
  detailVisible.value = true
}

const handleDistrictExport = (record: ProjectData) => {
  emit('export', 'excel', { type: 'district', data: record })
}

const getIndicatorRatio = (smart: number = 0, total: number = 0) => {
  if (total === 0) return 0
  return Math.round((smart / total) * 100)
}

const getProgressColor = (percent: number) => {
  if (percent >= 90) return '#52c41a'
  if (percent >= 70) return '#faad14'
  if (percent >= 50) return '#1890ff'
  return '#f5222d'
}

// 生命周期
onMounted(() => {
  if (!props.municipalData || !props.districtData) {
    loadData()
  }
})

// 监听props变化
watch(() => props.municipalData, (newValue) => {
  if (newValue) {
    municipalData.value = newValue
  }
})

watch(() => props.districtData, (newValue) => {
  if (newValue) {
    districtData.value = newValue
  }
})

watch(() => props.selectedDistrict, (newValue) => {
  if (newValue && activeTab.value === 'districts') {
    searchKeyword.value = newValue
  }
})
</script>

<style scoped lang="scss">
.project-data-overview {
  position: relative;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .header-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .title-area {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .data-tabs {
    padding: 20px;

    .ant-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-tabs-content-holder {
        padding: 20px;
      }
    }
  }

  .data-card {
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .ant-statistic {
      .ant-statistic-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }

    .progress-info {
      margin-top: 12px;
      padding-top: 8px;
      border-top: 1px solid #f0f0f0;

      .ant-progress {
        margin-bottom: 4px;
      }

      .progress-text {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .chart-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #e8e8e8;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .unit-type-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f6f9ff;
    }

    .unit-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .unit-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }

      .unit-count {
        font-size: 18px;
        font-weight: 600;
        color: #1890ff;
      }
    }

    .unit-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .ratio-text {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .resource-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    height: 100%;

    .resource-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .resource-icon {
        font-size: 16px;
        color: #1890ff;
        margin-right: 8px;
      }

      .resource-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .resource-content {
      margin-bottom: 12px;

      .resource-value {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
        margin-right: 4px;
      }

      .resource-unit {
        font-size: 14px;
        color: #666;
      }
    }

    .resource-progress {
      .progress-text {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        display: block;
      }
    }
  }

  .district-search {
    margin-bottom: 16px;
  }

  .completion-cell {
    .ant-progress {
      margin-bottom: 0;
    }
  }

  .detail-content {
    .ant-descriptions {
      margin-top: 16px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .project-data-overview {
    .data-card {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 20px;
        }
      }
    }

    .unit-type-item {
      .unit-header {
        .unit-count {
          font-size: 16px;
        }
      }
    }

    .resource-item {
      .resource-content {
        .resource-value {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .project-data-overview {
    .header-section {
      padding: 12px 16px;

      .title-area {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h3 {
          font-size: 16px;
        }
      }
    }

    .data-tabs {
      padding: 16px;

      .ant-tabs-content-holder {
        padding: 16px;
      }
    }

    .data-card {
      margin-bottom: 12px;

      .ant-statistic {
        .ant-statistic-title {
          font-size: 13px;
        }

        .ant-statistic-content {
          font-size: 18px;
        }
      }
    }

    .unit-type-item {
      padding: 12px;
      margin-bottom: 12px;

      .unit-header {
        .unit-title {
          font-size: 13px;
        }

        .unit-count {
          font-size: 14px;
        }
      }
    }

    .resource-item {
      padding: 12px;

      .resource-content {
        .resource-value {
          font-size: 18px;
        }
      }
    }

    // 表格在移动端的优化
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .project-data-overview {
    .header-section {
      .title-area {
        h3 {
          font-size: 14px;
        }
      }
    }

    .data-card {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }

    .unit-type-item {
      .unit-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }

    // 移动端表格滚动
    .ant-table-wrapper {
      .ant-table {
        min-width: 600px;
      }
    }
  }
}
</style>
