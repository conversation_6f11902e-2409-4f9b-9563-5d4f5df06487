<template>
  <div class="loading-state" :class="[`loading-${type}`, { 'loading-overlay': overlay }]">
    <!-- 骨架屏加载 -->
    <div v-if="type === 'skeleton'" class="skeleton-container">
      <div class="skeleton-header">
        <div class="skeleton-item skeleton-title"></div>
        <div class="skeleton-item skeleton-subtitle"></div>
      </div>
      <div class="skeleton-content">
        <div v-for="i in skeletonRows" :key="i" class="skeleton-row">
          <div class="skeleton-item skeleton-avatar"></div>
          <div class="skeleton-item skeleton-text"></div>
          <div class="skeleton-item skeleton-action"></div>
        </div>
      </div>
    </div>

    <!-- 卡片骨架屏 -->
    <div v-else-if="type === 'card'" class="skeleton-cards">
      <div v-for="i in cardCount" :key="i" class="skeleton-card">
        <div class="skeleton-item skeleton-card-header"></div>
        <div class="skeleton-item skeleton-card-content"></div>
        <div class="skeleton-item skeleton-card-footer"></div>
      </div>
    </div>

    <!-- 表格骨架屏 -->
    <div v-else-if="type === 'table'" class="skeleton-table">
      <div class="skeleton-table-header">
        <div v-for="i in tableColumns" :key="i" class="skeleton-item skeleton-th"></div>
      </div>
      <div v-for="i in tableRows" :key="i" class="skeleton-table-row">
        <div v-for="j in tableColumns" :key="j" class="skeleton-item skeleton-td"></div>
      </div>
    </div>

    <!-- 图表骨架屏 -->
    <div v-else-if="type === 'chart'" class="skeleton-chart">
      <div class="skeleton-item skeleton-chart-title"></div>
      <div class="skeleton-chart-content">
        <div class="skeleton-item skeleton-chart-area"></div>
        <div class="skeleton-chart-legend">
          <div v-for="i in 3" :key="i" class="skeleton-item skeleton-legend-item"></div>
        </div>
      </div>
    </div>

    <!-- 默认旋转加载 -->
    <div v-else class="loading-container">
      <a-spin :size="spinSize" :tip="tip">
        <div v-if="showContent" class="loading-content">
          <slot></slot>
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props定义
interface Props {
  type?: 'spin' | 'skeleton' | 'card' | 'table' | 'chart'
  overlay?: boolean
  tip?: string
  size?: 'small' | 'default' | 'large'
  skeletonRows?: number
  cardCount?: number
  tableRows?: number
  tableColumns?: number
  showContent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spin',
  overlay: false,
  tip: '加载中...',
  size: 'default',
  skeletonRows: 5,
  cardCount: 4,
  tableRows: 8,
  tableColumns: 6,
  showContent: false
})

// 计算属性
const spinSize = computed(() => {
  const sizeMap = {
    small: 'small',
    default: 'default',
    large: 'large'
  }
  return sizeMap[props.size] as 'small' | 'default' | 'large'
})
</script>

<style scoped lang="scss">
@import '../styles/global.scss';

.loading-state {
  position: relative;
  
  &.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 骨架屏样式
.skeleton-container {
  padding: var(--spacing-lg);
  
  .skeleton-header {
    margin-bottom: var(--spacing-xl);
    
    .skeleton-title {
      height: 24px;
      width: 200px;
      margin-bottom: var(--spacing-sm);
    }
    
    .skeleton-subtitle {
      height: 16px;
      width: 300px;
    }
  }
  
  .skeleton-content {
    .skeleton-row {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
      
      .skeleton-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
      
      .skeleton-text {
        flex: 1;
        height: 16px;
      }
      
      .skeleton-action {
        width: 80px;
        height: 32px;
      }
    }
  }
}

.skeleton-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  
  .skeleton-card {
    border: 1px solid var(--border-color-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    
    .skeleton-card-header {
      height: 20px;
      width: 60%;
      margin-bottom: var(--spacing-md);
    }
    
    .skeleton-card-content {
      height: 80px;
      margin-bottom: var(--spacing-md);
    }
    
    .skeleton-card-footer {
      height: 16px;
      width: 40%;
    }
  }
}

.skeleton-table {
  .skeleton-table-header {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-color-light);
    
    .skeleton-th {
      flex: 1;
      height: 20px;
    }
  }
  
  .skeleton-table-row {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
    
    .skeleton-td {
      flex: 1;
      height: 16px;
    }
  }
}

.skeleton-chart {
  padding: var(--spacing-lg);
  
  .skeleton-chart-title {
    height: 20px;
    width: 150px;
    margin-bottom: var(--spacing-lg);
  }
  
  .skeleton-chart-content {
    .skeleton-chart-area {
      height: 200px;
      margin-bottom: var(--spacing-md);
    }
    
    .skeleton-chart-legend {
      display: flex;
      justify-content: center;
      gap: var(--spacing-lg);
      
      .skeleton-legend-item {
        height: 16px;
        width: 80px;
      }
    }
  }
}

// 响应式适配
@include respond-below(md) {
  .skeleton-container {
    padding: var(--spacing-md);
    
    .skeleton-content {
      .skeleton-row {
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        
        .skeleton-avatar {
          width: 32px;
          height: 32px;
        }
        
        .skeleton-action {
          width: 60px;
          height: 28px;
        }
      }
    }
  }
  
  .skeleton-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }
  
  .skeleton-chart {
    padding: var(--spacing-md);
    
    .skeleton-chart-content {
      .skeleton-chart-area {
        height: 150px;
      }
      
      .skeleton-chart-legend {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
      }
    }
  }
}

@include respond-below(sm) {
  .skeleton-table {
    .skeleton-table-header,
    .skeleton-table-row {
      padding: var(--spacing-sm);
    }
  }
  
  .skeleton-chart {
    .skeleton-chart-content {
      .skeleton-chart-area {
        height: 120px;
      }
    }
  }
}
</style>
