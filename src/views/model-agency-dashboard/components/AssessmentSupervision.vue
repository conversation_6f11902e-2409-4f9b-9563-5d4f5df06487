<template>
  <div class="assessment-supervision">
    <!-- 标题和操作栏 -->
    <div class="header-section">
      <div class="title-area">
        <h3>考核督查数据概览</h3>
        <a-space>
          <a-select
            v-model:value="selectedYear"
            placeholder="选择年份"
            style="width: 120px"
            @change="handleYearChange"
          >
            <a-select-option 
              v-for="year in availableYears" 
              :key="year" 
              :value="year"
            >
              {{ year }}年
            </a-select-option>
          </a-select>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
          <a-button @click="handleExport">
            <template #icon><download-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据视图切换 -->
    <div class="data-tabs">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="municipal" tab="市级机关数据">
          <div class="municipal-data">
            <!-- 历年考核趋势 -->
            <a-card title="历年考核趋势" class="chart-card">
              <div class="trend-chart">
                <div class="chart-header">
                  <span class="chart-subtitle">近5年考核成绩变化趋势</span>
                </div>
                <div class="chart-content">
                  <div class="year-bars">
                    <div 
                      v-for="assessment in municipalData?.yearlyAssessments || []" 
                      :key="assessment.year"
                      class="year-bar"
                    >
                      <div class="bar-container">
                        <div 
                          class="bar-fill"
                          :class="`level-${assessment.level}`"
                          :style="{ height: `${assessment.score}%` }"
                        ></div>
                      </div>
                      <div class="bar-info">
                        <div class="bar-score">{{ assessment.score.toFixed(1) }}</div>
                        <div class="bar-level">{{ getLevelText(assessment.level) }}</div>
                        <div class="bar-year">{{ assessment.year }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 考核等次分布 -->
            <a-card title="考核等次分布" class="chart-card" style="margin-top: 16px;">
              <a-row :gutter="[16, 16]">
                <a-col :xs="24" :sm="12" :md="6">
                  <div class="level-stat excellent">
                    <div class="stat-icon">
                      <crown-outlined />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ municipalData?.levelDistribution.excellent || 0 }}</div>
                      <div class="stat-label">优秀</div>
                    </div>
                    <div class="stat-progress">
                      <a-progress 
                        :percent="getDistributionPercent('excellent')"
                        stroke-color="#52c41a"
                        :show-info="false"
                        size="small"
                      />
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <div class="level-stat good">
                    <div class="stat-icon">
                      <like-outlined />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ municipalData?.levelDistribution.good || 0 }}</div>
                      <div class="stat-label">良好</div>
                    </div>
                    <div class="stat-progress">
                      <a-progress 
                        :percent="getDistributionPercent('good')"
                        stroke-color="#1890ff"
                        :show-info="false"
                        size="small"
                      />
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <div class="level-stat average">
                    <div class="stat-icon">
                      <minus-circle-outlined />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ municipalData?.levelDistribution.average || 0 }}</div>
                      <div class="stat-label">一般</div>
                    </div>
                    <div class="stat-progress">
                      <a-progress 
                        :percent="getDistributionPercent('average')"
                        stroke-color="#faad14"
                        :show-info="false"
                        size="small"
                      />
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="12" :md="6">
                  <div class="level-stat poor">
                    <div class="stat-icon">
                      <warning-outlined />
                    </div>
                    <div class="stat-content">
                      <div class="stat-value">{{ municipalData?.levelDistribution.poor || 0 }}</div>
                      <div class="stat-label">待提升</div>
                    </div>
                    <div class="stat-progress">
                      <a-progress 
                        :percent="getDistributionPercent('poor')"
                        stroke-color="#f5222d"
                        :show-info="false"
                        size="small"
                      />
                    </div>
                  </div>
                </a-col>
              </a-row>
            </a-card>

            <!-- 督查统计 -->
            <a-card title="督查统计" class="chart-card" style="margin-top: 16px;">
              <a-row :gutter="[24, 24]">
                <a-col :xs="24" :sm="8">
                  <div class="supervision-item">
                    <div class="supervision-header">
                      <file-text-outlined class="supervision-icon" />
                      <span class="supervision-title">督查单位</span>
                    </div>
                    <div class="supervision-content">
                      <span class="supervision-value">{{ municipalData?.supervisionUnits || 0 }}</span>
                      <span class="supervision-unit">个</span>
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <div class="supervision-item">
                    <div class="supervision-header">
                      <check-circle-outlined class="supervision-icon" />
                      <span class="supervision-title">完成督查</span>
                    </div>
                    <div class="supervision-content">
                      <span class="supervision-value">{{ municipalData?.completedSupervision || 0 }}</span>
                      <span class="supervision-unit">个</span>
                    </div>
                  </div>
                </a-col>
                <a-col :xs="24" :sm="8">
                  <div class="supervision-item">
                    <div class="supervision-header">
                      <percentage-outlined class="supervision-icon" />
                      <span class="supervision-title">完成率</span>
                    </div>
                    <div class="supervision-content">
                      <span class="supervision-value">{{ municipalData?.completionRate || 0 }}</span>
                      <span class="supervision-unit">%</span>
                    </div>
                    <div class="supervision-progress">
                      <a-progress 
                        :percent="municipalData?.completionRate || 0"
                        stroke-color="#52c41a"
                        :show-info="false"
                      />
                    </div>
                  </div>
                </a-col>
              </a-row>
            </a-card>

            <!-- 趋势分析 -->
            <a-card title="趋势分析" class="chart-card" style="margin-top: 16px;">
              <div class="trend-analysis">
                <div class="trend-item">
                  <div class="trend-indicator">
                    <div class="trend-icon" :class="getTrendClass(municipalData?.trendAnalysis.direction)">
                      <component :is="getTrendIcon(municipalData?.trendAnalysis.direction)" />
                    </div>
                    <div class="trend-content">
                      <div class="trend-title">总体趋势</div>
                      <div class="trend-direction">{{ getTrendText(municipalData?.trendAnalysis.direction) }}</div>
                    </div>
                  </div>
                  <div class="trend-change">
                    <span class="change-rate" :class="getTrendClass(municipalData?.trendAnalysis.direction)">
                      {{ municipalData?.trendAnalysis.changeRate > 0 ? '+' : '' }}{{ municipalData?.trendAnalysis.changeRate || 0 }}%
                    </span>
                  </div>
                </div>
                <div class="trend-description">
                  {{ municipalData?.trendAnalysis.description || '暂无趋势分析' }}
                </div>
              </div>
            </a-card>
          </div>
        </a-tab-pane>

        <a-tab-pane key="districts" tab="区县机关数据">
          <div class="districts-data">
            <!-- 区县考核对比 -->
            <a-card title="区县考核对比" class="chart-card">
              <div class="district-search">
                <a-input-search
                  v-model:value="searchKeyword"
                  placeholder="搜索区县名称"
                  style="width: 300px; margin-bottom: 16px;"
                  @search="handleSearch"
                />
              </div>
              
              <a-table
                :columns="districtColumns"
                :data-source="filteredDistrictData"
                :pagination="{ pageSize: 10, showSizeChanger: true }"
                :loading="loading"
                size="middle"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'regionName'">
                    <a-button type="link" @click="handleDistrictClick(record)">
                      {{ record.regionName }}
                    </a-button>
                  </template>
                  <template v-else-if="column.key === 'latestScore'">
                    <div class="score-cell">
                      <span class="score-value">{{ getLatestScore(record) }}</span>
                      <a-tag :color="getLevelColor(getLatestLevel(record))" size="small">
                        {{ getLevelText(getLatestLevel(record)) }}
                      </a-tag>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'completionRate'">
                    <div class="completion-cell">
                      <a-progress 
                        :percent="record.completionRate" 
                        size="small"
                        :stroke-color="getProgressColor(record.completionRate)"
                      />
                    </div>
                  </template>
                  <template v-else-if="column.key === 'trend'">
                    <div class="trend-cell">
                      <span class="trend-icon" :class="getTrendClass(record.trendAnalysis?.direction)">
                        <component :is="getTrendIcon(record.trendAnalysis?.direction)" />
                      </span>
                      <span class="trend-text">{{ getTrendText(record.trendAnalysis?.direction) }}</span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'actions'">
                    <a-space>
                      <a-button size="small" @click="handleDistrictDetail(record)">
                        详情
                      </a-button>
                      <a-button size="small" @click="handleDistrictExport(record)">
                        导出
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-card>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="detailTitle"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedData" class="detail-content">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="区县名称">
            {{ selectedData.regionName || '市级机关' }}
          </a-descriptions-item>
          <a-descriptions-item label="督查单位">
            {{ selectedData.supervisionUnits }}个
          </a-descriptions-item>
          <a-descriptions-item label="完成督查">
            {{ selectedData.completedSupervision }}个
          </a-descriptions-item>
          <a-descriptions-item label="完成率">
            {{ selectedData.completionRate }}%
          </a-descriptions-item>
          <a-descriptions-item label="优秀单位">
            {{ selectedData.levelDistribution?.excellent || 0 }}个
          </a-descriptions-item>
          <a-descriptions-item label="良好单位">
            {{ selectedData.levelDistribution?.good || 0 }}个
          </a-descriptions-item>
          <a-descriptions-item label="一般单位">
            {{ selectedData.levelDistribution?.average || 0 }}个
          </a-descriptions-item>
          <a-descriptions-item label="待提升单位">
            {{ selectedData.levelDistribution?.poor || 0 }}个
          </a-descriptions-item>
          <a-descriptions-item label="趋势分析" :span="2">
            {{ selectedData.trendAnalysis?.description || '暂无趋势分析' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large" tip="加载考核督查数据中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { AssessmentData, AssessmentSupervisionProps } from '../types'
import { mockDataService } from '../mock/data'

// Props定义
interface Props {
  municipalData?: AssessmentData
  districtData?: AssessmentData[]
  selectedDistrict?: string
}

const props = withDefaults(defineProps<Props>(), {
  selectedDistrict: ''
})

// Emits定义
const emit = defineEmits<{
  'year-select': [year: number]
  'district-select': [district: string]
  'export': [data: any]
}>()

// 响应式数据
const loading = ref(false)
const activeTab = ref('municipal')
const selectedYear = ref(new Date().getFullYear())
const searchKeyword = ref('')
const detailVisible = ref(false)
const detailTitle = ref('')
const selectedData = ref<AssessmentData | null>(null)

const municipalData = ref<AssessmentData | null>(null)
const districtData = ref<AssessmentData[]>([])

// 可选年份
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  return Array.from({ length: 5 }, (_, i) => currentYear - 4 + i)
})

// 表格列定义
const districtColumns = [
  {
    title: '区县名称',
    dataIndex: 'regionName',
    key: 'regionName',
    width: 120,
    fixed: 'left'
  },
  {
    title: '最新得分',
    key: 'latestScore',
    width: 120
  },
  {
    title: '督查单位',
    dataIndex: 'supervisionUnits',
    key: 'supervisionUnits',
    width: 100,
    sorter: (a: AssessmentData, b: AssessmentData) => a.supervisionUnits - b.supervisionUnits
  },
  {
    title: '完成督查',
    dataIndex: 'completedSupervision',
    key: 'completedSupervision',
    width: 100,
    sorter: (a: AssessmentData, b: AssessmentData) => a.completedSupervision - b.completedSupervision
  },
  {
    title: '完成率',
    dataIndex: 'completionRate',
    key: 'completionRate',
    width: 120
  },
  {
    title: '趋势',
    key: 'trend',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 计算属性
const filteredDistrictData = computed(() => {
  if (!searchKeyword.value) {
    return districtData.value
  }
  
  return districtData.value.filter(item => 
    item.regionName?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const totalDistributionUnits = computed(() => {
  if (!municipalData.value?.levelDistribution) return 0
  const dist = municipalData.value.levelDistribution
  return dist.excellent + dist.good + dist.average + dist.poor
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await mockDataService.fetchAssessmentData()
    municipalData.value = response.data.municipal
    districtData.value = response.data.districts
  } catch (error) {
    console.error('加载考核督查数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  loadData()
}

const handleTabChange = (key: string) => {
  activeTab.value = key
}

const handleYearChange = (year: number) => {
  selectedYear.value = year
  emit('year-select', year)
}

const handleSearch = (value: string) => {
  searchKeyword.value = value
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  console.log('Table change:', pagination, filters, sorter)
}

const handleDistrictClick = (record: AssessmentData) => {
  emit('district-select', record.regionName || '')
}

const handleDistrictDetail = (record: AssessmentData) => {
  selectedData.value = record
  detailTitle.value = `${record.regionName || '市级机关'} - 考核督查详情`
  detailVisible.value = true
}

const handleDistrictExport = (record: AssessmentData) => {
  emit('export', { type: 'district', data: record })
}

const handleExport = () => {
  const data = activeTab.value === 'municipal' ? municipalData.value : districtData.value
  emit('export', { type: activeTab.value, data })
}

const getLevelText = (level: string) => {
  const levelMap = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return levelMap[level as keyof typeof levelMap] || level
}

const getLevelColor = (level: string) => {
  const colorMap = {
    excellent: 'green',
    good: 'blue',
    average: 'orange',
    poor: 'red'
  }
  return colorMap[level as keyof typeof colorMap] || 'default'
}

const getTrendText = (direction?: string) => {
  const trendMap = {
    up: '上升',
    down: '下降',
    stable: '稳定'
  }
  return trendMap[direction as keyof typeof trendMap] || '稳定'
}

const getTrendIcon = (direction?: string) => {
  const iconMap = {
    up: 'rise-outlined',
    down: 'fall-outlined',
    stable: 'minus-outlined'
  }
  return iconMap[direction as keyof typeof iconMap] || 'minus-outlined'
}

const getTrendClass = (direction?: string) => {
  const classMap = {
    up: 'trend-up',
    down: 'trend-down',
    stable: 'trend-stable'
  }
  return classMap[direction as keyof typeof classMap] || 'trend-stable'
}

const getDistributionPercent = (level: string) => {
  if (!municipalData.value?.levelDistribution || totalDistributionUnits.value === 0) return 0
  const count = municipalData.value.levelDistribution[level as keyof typeof municipalData.value.levelDistribution]
  return Math.round((count / totalDistributionUnits.value) * 100)
}

const getLatestScore = (record: AssessmentData) => {
  if (!record.yearlyAssessments || record.yearlyAssessments.length === 0) return 0
  return record.yearlyAssessments[record.yearlyAssessments.length - 1].score.toFixed(1)
}

const getLatestLevel = (record: AssessmentData) => {
  if (!record.yearlyAssessments || record.yearlyAssessments.length === 0) return 'average'
  return record.yearlyAssessments[record.yearlyAssessments.length - 1].level
}

const getProgressColor = (percent: number) => {
  if (percent >= 90) return '#52c41a'
  if (percent >= 70) return '#faad14'
  if (percent >= 50) return '#1890ff'
  return '#f5222d'
}

// 生命周期
onMounted(() => {
  if (!props.municipalData || !props.districtData) {
    loadData()
  }
})

// 监听props变化
watch(() => props.municipalData, (newValue) => {
  if (newValue) {
    municipalData.value = newValue
  }
})

watch(() => props.districtData, (newValue) => {
  if (newValue) {
    districtData.value = newValue
  }
})

watch(() => props.selectedDistrict, (newValue) => {
  if (newValue && activeTab.value === 'districts') {
    searchKeyword.value = newValue
  }
})
</script>

<style scoped lang="scss">
.assessment-supervision {
  position: relative;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .header-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .title-area {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .data-tabs {
    padding: 20px;

    .ant-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-tabs-content-holder {
        padding: 20px;
      }
    }
  }

  .chart-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .ant-card-head {
      border-bottom: 1px solid #e8e8e8;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .trend-chart {
    .chart-header {
      margin-bottom: 20px;

      .chart-subtitle {
        font-size: 14px;
        color: #666;
      }
    }

    .chart-content {
      .year-bars {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        height: 200px;
        padding: 20px 0;

        .year-bar {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 8px;

          .bar-container {
            height: 120px;
            width: 40px;
            background: #f0f0f0;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            margin-bottom: 12px;

            .bar-fill {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              border-radius: 4px 4px 0 0;
              transition: height 0.3s ease;

              &.level-excellent {
                background: linear-gradient(to top, #52c41a, #73d13d);
              }

              &.level-good {
                background: linear-gradient(to top, #1890ff, #40a9ff);
              }

              &.level-average {
                background: linear-gradient(to top, #faad14, #ffc53d);
              }

              &.level-poor {
                background: linear-gradient(to top, #f5222d, #ff4d4f);
              }
            }
          }

          .bar-info {
            text-align: center;

            .bar-score {
              font-size: 14px;
              font-weight: 600;
              color: #262626;
              margin-bottom: 2px;
            }

            .bar-level {
              font-size: 11px;
              color: #666;
              margin-bottom: 4px;
            }

            .bar-year {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
  }

  .level-stat {
    padding: 16px;
    border-radius: 8px;
    border: 2px solid transparent;
    background: #fafafa;
    transition: all 0.3s ease;
    height: 100%;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    &.excellent {
      border-color: rgba(82, 196, 26, 0.3);
      background: rgba(82, 196, 26, 0.05);

      .stat-icon {
        color: #52c41a;
      }

      .stat-value {
        color: #52c41a;
      }
    }

    &.good {
      border-color: rgba(24, 144, 255, 0.3);
      background: rgba(24, 144, 255, 0.05);

      .stat-icon {
        color: #1890ff;
      }

      .stat-value {
        color: #1890ff;
      }
    }

    &.average {
      border-color: rgba(250, 173, 20, 0.3);
      background: rgba(250, 173, 20, 0.05);

      .stat-icon {
        color: #faad14;
      }

      .stat-value {
        color: #faad14;
      }
    }

    &.poor {
      border-color: rgba(245, 34, 45, 0.3);
      background: rgba(245, 34, 45, 0.05);

      .stat-icon {
        color: #f5222d;
      }

      .stat-value {
        color: #f5222d;
      }
    }

    .stat-icon {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .stat-content {
      margin-bottom: 12px;

      .stat-value {
        font-size: 28px;
        font-weight: 600;
        display: block;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }

    .stat-progress {
      margin-top: 8px;
    }
  }

  .supervision-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f6f9ff;
    }

    .supervision-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .supervision-icon {
        font-size: 16px;
        color: #1890ff;
        margin-right: 8px;
      }

      .supervision-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .supervision-content {
      margin-bottom: 12px;

      .supervision-value {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
        margin-right: 4px;
      }

      .supervision-unit {
        font-size: 14px;
        color: #666;
      }
    }

    .supervision-progress {
      margin-top: 8px;
    }
  }

  .trend-analysis {
    .trend-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .trend-indicator {
        display: flex;
        align-items: center;

        .trend-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 18px;

          &.trend-up {
            background: rgba(82, 196, 26, 0.1);
            color: #52c41a;
          }

          &.trend-down {
            background: rgba(245, 34, 45, 0.1);
            color: #f5222d;
          }

          &.trend-stable {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
          }
        }

        .trend-content {
          .trend-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 2px;
          }

          .trend-direction {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
          }
        }
      }

      .trend-change {
        .change-rate {
          font-size: 18px;
          font-weight: 600;

          &.trend-up {
            color: #52c41a;
          }

          &.trend-down {
            color: #f5222d;
          }

          &.trend-stable {
            color: #1890ff;
          }
        }
      }
    }

    .trend-description {
      padding: 12px;
      background: #f6f8fa;
      border-radius: 6px;
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }

  .district-search {
    margin-bottom: 16px;
  }

  .score-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .score-value {
      font-weight: 600;
    }
  }

  .completion-cell {
    .ant-progress {
      margin-bottom: 0;
    }
  }

  .trend-cell {
    display: flex;
    align-items: center;
    gap: 4px;

    .trend-icon {
      &.trend-up {
        color: #52c41a;
      }

      &.trend-down {
        color: #f5222d;
      }

      &.trend-stable {
        color: #1890ff;
      }
    }

    .trend-text {
      font-size: 12px;
    }
  }

  .detail-content {
    .ant-descriptions {
      margin-top: 16px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .assessment-supervision {
    .trend-chart {
      .chart-content {
        .year-bars {
          .year-bar {
            .bar-container {
              width: 32px;
            }
          }
        }
      }
    }

    .level-stat {
      .stat-content {
        .stat-value {
          font-size: 24px;
        }
      }
    }

    .supervision-item {
      .supervision-content {
        .supervision-value {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .assessment-supervision {
    .header-section {
      padding: 12px 16px;

      .title-area {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h3 {
          font-size: 16px;
        }
      }
    }

    .data-tabs {
      padding: 16px;

      .ant-tabs-content-holder {
        padding: 16px;
      }
    }

    .trend-chart {
      .chart-content {
        .year-bars {
          height: 150px;
          padding: 15px 0;

          .year-bar {
            margin: 0 4px;

            .bar-container {
              height: 80px;
              width: 24px;
            }

            .bar-info {
              .bar-score {
                font-size: 12px;
              }

              .bar-level {
                font-size: 10px;
              }

              .bar-year {
                font-size: 11px;
              }
            }
          }
        }
      }
    }

    .level-stat {
      padding: 12px;
      margin-bottom: 12px;

      .stat-icon {
        font-size: 20px;
      }

      .stat-content {
        .stat-value {
          font-size: 20px;
        }
      }
    }

    .supervision-item {
      padding: 12px;
      margin-bottom: 12px;

      .supervision-content {
        .supervision-value {
          font-size: 18px;
        }
      }
    }

    .trend-analysis {
      .trend-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .trend-indicator {
          .trend-icon {
            width: 32px;
            height: 32px;
            font-size: 16px;
          }
        }

        .trend-change {
          .change-rate {
            font-size: 16px;
          }
        }
      }
    }

    // 表格在移动端的优化
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .assessment-supervision {
    .trend-chart {
      .chart-content {
        .year-bars {
          height: 120px;

          .year-bar {
            .bar-container {
              height: 60px;
              width: 20px;
            }
          }
        }
      }
    }

    .level-stat {
      .stat-content {
        .stat-value {
          font-size: 18px;
        }
      }
    }

    .supervision-item {
      .supervision-content {
        .supervision-value {
          font-size: 16px;
        }
      }
    }

    // 移动端表格滚动
    .ant-table-wrapper {
      .ant-table {
        min-width: 600px;
      }
    }
  }
}
</style>
