<template>
  <div class="warning-monitor">
    <!-- 标题和操作栏 -->
    <div class="header-section">
      <div class="title-area">
        <h3>监督预警数据概览</h3>
        <a-space>
          <a-dropdown>
            <a-button>
              <template #icon><filter-outlined /></template>
              预警级别
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleLevelFilter">
                <a-menu-item key="">全部级别</a-menu-item>
                <a-menu-item key="critical">重点关注</a-menu-item>
                <a-menu-item key="major">较大问题</a-menu-item>
                <a-menu-item key="general">一般问题</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
          <a-button @click="handleExport">
            <template #icon><download-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 预警统计概览 -->
    <div class="warning-overview">
      <a-card title="预警统计概览" class="chart-card">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat total">
              <div class="stat-header">
                <exclamation-circle-outlined class="stat-icon" />
                <span class="stat-title">预警总数</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ data?.totalWarnings || 0 }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-badge">
                <a-badge 
                  :count="data?.totalWarnings || 0" 
                  :number-style="{ backgroundColor: '#f5222d' }"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat municipal">
              <div class="stat-header">
                <bank-outlined class="stat-icon" />
                <span class="stat-title">市级预警</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ data?.municipalWarnings || 0 }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-progress">
                <a-progress 
                  :percent="getMunicipalPercent()"
                  stroke-color="#1890ff"
                  :show-info="false"
                  size="small"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat district">
              <div class="stat-header">
                <apartment-outlined class="stat-icon" />
                <span class="stat-title">区县预警</span>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ data?.districtWarnings || 0 }}</span>
                <span class="stat-unit">项</span>
              </div>
              <div class="stat-progress">
                <a-progress 
                  :percent="getDistrictPercent()"
                  stroke-color="#52c41a"
                  :show-info="false"
                  size="small"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="warning-stat highest">
              <div class="stat-header">
                <fire-outlined class="stat-icon" />
                <span class="stat-title">最高预警项目</span>
              </div>
              <div class="stat-content">
                <div class="project-name">{{ data?.highestWarningProject.projectName || '暂无' }}</div>
                <div class="project-count">{{ data?.highestWarningProject.warningCount || 0 }}项预警</div>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 问题分析 -->
    <div class="problem-analysis">
      <a-card title="问题分析" class="chart-card">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :md="12">
            <div class="problem-stats">
              <h4>问题分级统计</h4>
              <div class="problem-levels">
                <div class="problem-level critical">
                  <div class="level-header">
                    <warning-outlined class="level-icon" />
                    <span class="level-title">重点关注</span>
                    <a-badge 
                      :count="data?.problemAnalysis.criticalProblems || 0" 
                      :number-style="{ backgroundColor: '#f5222d' }"
                    />
                  </div>
                  <div class="level-progress">
                    <a-progress 
                      :percent="getProblemPercent('critical')"
                      stroke-color="#f5222d"
                      :show-info="false"
                    />
                  </div>
                </div>
                <div class="problem-level major">
                  <div class="level-header">
                    <alert-outlined class="level-icon" />
                    <span class="level-title">较大问题</span>
                    <a-badge 
                      :count="data?.problemAnalysis.majorProblems || 0" 
                      :number-style="{ backgroundColor: '#faad14' }"
                    />
                  </div>
                  <div class="level-progress">
                    <a-progress 
                      :percent="getProblemPercent('major')"
                      stroke-color="#faad14"
                      :show-info="false"
                    />
                  </div>
                </div>
                <div class="problem-level general">
                  <div class="level-header">
                    <info-circle-outlined class="level-icon" />
                    <span class="level-title">一般问题</span>
                    <a-badge 
                      :count="data?.problemAnalysis.generalProblems || 0" 
                      :number-style="{ backgroundColor: '#1890ff' }"
                    />
                  </div>
                  <div class="level-progress">
                    <a-progress 
                      :percent="getProblemPercent('general')"
                      stroke-color="#1890ff"
                      :show-info="false"
                    />
                  </div>
                </div>
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :md="12">
            <div class="rectification-stats">
              <h4>整改进度统计</h4>
              <div class="rectification-items">
                <div class="rectification-item">
                  <div class="item-header">
                    <clock-circle-outlined class="item-icon in-progress" />
                    <span class="item-title">整改推进</span>
                  </div>
                  <div class="item-content">
                    <span class="item-value">{{ data?.problemAnalysis.inProgress || 0 }}</span>
                    <span class="item-unit">项</span>
                  </div>
                </div>
                <div class="rectification-item">
                  <div class="item-header">
                    <check-circle-outlined class="item-icon completed" />
                    <span class="item-title">整改销号</span>
                  </div>
                  <div class="item-content">
                    <span class="item-value">{{ data?.problemAnalysis.completed || 0 }}</span>
                    <span class="item-unit">项</span>
                  </div>
                </div>
                <div class="rectification-item">
                  <div class="item-header">
                    <close-circle-outlined class="item-icon overdue" />
                    <span class="item-title">逾期未改</span>
                  </div>
                  <div class="item-content">
                    <span class="item-value">{{ data?.problemAnalysis.overdue || 0 }}</span>
                    <span class="item-unit">项</span>
                  </div>
                </div>
              </div>
              <div class="rectification-summary">
                <div class="summary-item">
                  <span class="summary-label">整改完成率</span>
                  <span class="summary-value">{{ data?.problemAnalysis.rectificationRate || 0 }}%</span>
                </div>
                <a-progress 
                  :percent="data?.problemAnalysis.rectificationRate || 0"
                  stroke-color="#52c41a"
                  :show-info="false"
                />
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 预警趋势 -->
    <div class="warning-trend">
      <a-card title="预警趋势分析" class="chart-card">
        <div class="trend-chart">
          <div class="chart-header">
            <span class="chart-subtitle">近7天预警数据变化趋势</span>
          </div>
          <div class="chart-content">
            <div class="trend-lines">
              <div 
                v-for="(item, index) in data?.warningTrend || []" 
                :key="item.date"
                class="trend-point"
                :style="{ left: `${(index / Math.max(1, (data?.warningTrend?.length || 1) - 1)) * 100}%` }"
              >
                <div class="point-marker warning" :style="{ height: `${Math.max(4, item.warningCount * 2)}px` }"></div>
                <div class="point-marker problem" :style="{ height: `${Math.max(4, item.problemCount * 2)}px` }"></div>
                <div class="point-marker rectification" :style="{ height: `${Math.max(4, item.rectificationCount * 2)}px` }"></div>
                <div class="point-info">
                  <div class="point-date">{{ formatDate(item.date) }}</div>
                  <div class="point-values">
                    <div class="value-item warning">预警: {{ item.warningCount }}</div>
                    <div class="value-item problem">问题: {{ item.problemCount }}</div>
                    <div class="value-item rectification">整改: {{ item.rectificationCount }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="trend-legend">
              <div class="legend-item">
                <span class="legend-color warning"></span>
                <span class="legend-text">预警数量</span>
              </div>
              <div class="legend-item">
                <span class="legend-color problem"></span>
                <span class="legend-text">问题数量</span>
              </div>
              <div class="legend-item">
                <span class="legend-color rectification"></span>
                <span class="legend-text">整改数量</span>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 预警详情列表 -->
    <div class="warning-list">
      <a-card title="预警详情列表" class="chart-card">
        <div class="list-controls">
          <a-row :gutter="16" align="middle" style="margin-bottom: 16px;">
            <a-col :xs="24" :sm="12" :md="8">
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索预警内容"
                @search="handleSearch"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-select
                v-model:value="selectedLevel"
                placeholder="选择预警级别"
                style="width: 100%"
                allow-clear
                @change="handleLevelFilter"
              >
                <a-select-option value="">全部级别</a-select-option>
                <a-select-option value="critical">重点关注</a-select-option>
                <a-select-option value="major">较大问题</a-select-option>
                <a-select-option value="general">一般问题</a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-select
                v-model:value="selectedStatus"
                placeholder="选择处理状态"
                style="width: 100%"
                allow-clear
                @change="handleStatusFilter"
              >
                <a-select-option value="">全部状态</a-select-option>
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
                <a-select-option value="overdue">已逾期</a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>

        <div class="warning-items">
          <div 
            v-for="warning in filteredWarnings" 
            :key="warning.id"
            class="warning-item"
            :class="warning.level"
            @click="handleWarningClick(warning)"
          >
            <div class="warning-header">
              <div class="warning-level">
                <a-tag :color="getLevelColor(warning.level)">
                  {{ getLevelText(warning.level) }}
                </a-tag>
              </div>
              <div class="warning-status">
                <a-tag :color="getStatusColor(warning.status)">
                  {{ getStatusText(warning.status) }}
                </a-tag>
              </div>
            </div>
            <div class="warning-content">
              <div class="warning-title">{{ warning.title }}</div>
              <div class="warning-description">{{ warning.description }}</div>
            </div>
            <div class="warning-footer">
              <div class="warning-info">
                <span class="warning-unit">{{ warning.unit }}</span>
                <span class="warning-date">{{ warning.date }}</span>
              </div>
              <div class="warning-actions">
                <a-button size="small" @click.stop="handleWarningDetail(warning)">
                  详情
                </a-button>
                <a-button size="small" @click.stop="handleWarningProcess(warning)">
                  处理
                </a-button>
              </div>
            </div>
          </div>
        </div>

        <div v-if="filteredWarnings.length === 0" class="empty-state">
          <a-empty description="暂无预警数据" />
        </div>
      </a-card>
    </div>

    <!-- 预警详情弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="selectedWarning?.title"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedWarning" class="warning-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="预警级别">
            <a-tag :color="getLevelColor(selectedWarning.level)">
              {{ getLevelText(selectedWarning.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="处理状态">
            <a-tag :color="getStatusColor(selectedWarning.status)">
              {{ getStatusText(selectedWarning.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预警单位">
            {{ selectedWarning.unit }}
          </a-descriptions-item>
          <a-descriptions-item label="预警时间">
            {{ selectedWarning.date }}
          </a-descriptions-item>
          <a-descriptions-item label="预警内容" :span="2">
            {{ selectedWarning.description }}
          </a-descriptions-item>
          <a-descriptions-item label="处理建议" :span="2">
            {{ selectedWarning.suggestion || '暂无处理建议' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large" tip="加载预警监控数据中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { WarningData, WarningMonitorProps } from '../types'
import { mockDataService } from '../mock/data'

// Props定义
interface Props {
  data?: WarningData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits定义
const emit = defineEmits<{
  'warning-click': [warningId: string]
  'data-refresh': []
  'export': [data: any]
}>()

// 响应式数据
const loading = ref(props.loading)
const data = ref<WarningData | null>(null)
const searchKeyword = ref('')
const selectedLevel = ref('')
const selectedStatus = ref('')
const detailVisible = ref(false)
const selectedWarning = ref<any>(null)

// 模拟预警列表数据
const warningList = ref([
  {
    id: 'warning_1',
    title: '党费收缴管理异常',
    description: '部分单位党费收缴不及时，存在拖欠现象',
    level: 'critical',
    status: 'pending',
    unit: '市委组织部',
    date: '2025-06-15',
    suggestion: '建立党费收缴提醒机制，定期督促检查'
  },
  {
    id: 'warning_2',
    title: '组织生活会开展不规范',
    description: '个别支部组织生活会流于形式，质量不高',
    level: 'major',
    status: 'processing',
    unit: '市政府办公厅',
    date: '2025-06-14',
    suggestion: '加强组织生活会指导，提高会议质量'
  },
  {
    id: 'warning_3',
    title: '党建工作台账不完整',
    description: '部分单位党建工作台账记录不完整',
    level: 'general',
    status: 'completed',
    unit: '市发展改革委',
    date: '2025-06-13',
    suggestion: '完善台账管理制度，规范记录要求'
  }
])

// 计算属性
const filteredWarnings = computed(() => {
  let warnings = warningList.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    warnings = warnings.filter(warning => 
      warning.title.toLowerCase().includes(keyword) ||
      warning.description.toLowerCase().includes(keyword) ||
      warning.unit.toLowerCase().includes(keyword)
    )
  }

  // 按级别筛选
  if (selectedLevel.value) {
    warnings = warnings.filter(warning => warning.level === selectedLevel.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    warnings = warnings.filter(warning => warning.status === selectedStatus.value)
  }

  return warnings
})

const totalProblems = computed(() => {
  if (!data.value?.problemAnalysis) return 0
  const analysis = data.value.problemAnalysis
  return analysis.criticalProblems + analysis.majorProblems + analysis.generalProblems
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await mockDataService.fetchWarningData()
    data.value = response.data
  } catch (error) {
    console.error('加载预警监控数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  emit('data-refresh')
  loadData()
}

const getMunicipalPercent = () => {
  if (!data.value || data.value.totalWarnings === 0) return 0
  return Math.round((data.value.municipalWarnings / data.value.totalWarnings) * 100)
}

const getDistrictPercent = () => {
  if (!data.value || data.value.totalWarnings === 0) return 0
  return Math.round((data.value.districtWarnings / data.value.totalWarnings) * 100)
}

const getProblemPercent = (level: string) => {
  if (!data.value?.problemAnalysis || totalProblems.value === 0) return 0
  
  let count = 0
  switch (level) {
    case 'critical':
      count = data.value.problemAnalysis.criticalProblems
      break
    case 'major':
      count = data.value.problemAnalysis.majorProblems
      break
    case 'general':
      count = data.value.problemAnalysis.generalProblems
      break
  }
  
  return Math.round((count / totalProblems.value) * 100)
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

const getLevelText = (level: string) => {
  const levelMap = {
    critical: '重点关注',
    major: '较大问题',
    general: '一般问题'
  }
  return levelMap[level as keyof typeof levelMap] || level
}

const getLevelColor = (level: string) => {
  const colorMap = {
    critical: 'red',
    major: 'orange',
    general: 'blue'
  }
  return colorMap[level as keyof typeof colorMap] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    pending: 'red',
    processing: 'orange',
    completed: 'green',
    overdue: 'purple'
  }
  return colorMap[status as keyof typeof colorMap] || 'default'
}

// 事件处理
const handleSearch = (value: string) => {
  searchKeyword.value = value
}

const handleLevelFilter = (value?: any) => {
  if (typeof value === 'object' && value.key !== undefined) {
    selectedLevel.value = value.key
  }
}

const handleStatusFilter = () => {
  // 触发筛选更新
}

const handleWarningClick = (warning: any) => {
  emit('warning-click', warning.id)
}

const handleWarningDetail = (warning: any) => {
  selectedWarning.value = warning
  detailVisible.value = true
}

const handleWarningProcess = (warning: any) => {
  // 处理预警
  console.log('Process warning:', warning)
}

const handleExport = () => {
  emit('export', data.value)
}

// 生命周期
onMounted(() => {
  if (!props.data) {
    loadData()
  }
})

// 监听props变化
watch(() => props.data, (newValue) => {
  if (newValue) {
    data.value = newValue
  }
})

watch(() => props.loading, (newValue) => {
  loading.value = newValue
})
</script>

<style scoped lang="scss">
.warning-monitor {
  position: relative;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .header-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .title-area {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .warning-overview,
  .problem-analysis,
  .warning-trend,
  .warning-list {
    padding: 20px;
    padding-top: 0;

    &:first-child {
      padding-top: 20px;
    }
  }

  .chart-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    .ant-card-head {
      border-bottom: 1px solid #e8e8e8;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .warning-stat {
    padding: 16px;
    border-radius: 8px;
    border: 2px solid transparent;
    background: #fafafa;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    &.total {
      border-color: rgba(245, 34, 45, 0.3);
      background: rgba(245, 34, 45, 0.05);

      .stat-icon {
        color: #f5222d;
      }

      .stat-value {
        color: #f5222d;
      }
    }

    &.municipal {
      border-color: rgba(24, 144, 255, 0.3);
      background: rgba(24, 144, 255, 0.05);

      .stat-icon {
        color: #1890ff;
      }

      .stat-value {
        color: #1890ff;
      }
    }

    &.district {
      border-color: rgba(82, 196, 26, 0.3);
      background: rgba(82, 196, 26, 0.05);

      .stat-icon {
        color: #52c41a;
      }

      .stat-value {
        color: #52c41a;
      }
    }

    &.highest {
      border-color: rgba(250, 173, 20, 0.3);
      background: rgba(250, 173, 20, 0.05);

      .stat-icon {
        color: #faad14;
      }
    }

    .stat-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .stat-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .stat-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .stat-content {
      margin-bottom: 12px;

      .stat-value {
        font-size: 28px;
        font-weight: 600;
        margin-right: 4px;
      }

      .stat-unit {
        font-size: 14px;
        color: #666;
      }

      .project-name {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 4px;
        line-height: 1.4;
      }

      .project-count {
        font-size: 12px;
        color: #666;
      }
    }

    .stat-badge {
      position: absolute;
      top: 8px;
      right: 8px;
    }

    .stat-progress {
      margin-top: 8px;
    }
  }

  .problem-stats {
    h4 {
      margin-bottom: 16px;
      color: #262626;
      font-size: 16px;
    }

    .problem-levels {
      .problem-level {
        margin-bottom: 16px;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        background: #fafafa;

        &.critical {
          border-color: rgba(245, 34, 45, 0.3);
          background: rgba(245, 34, 45, 0.05);

          .level-icon {
            color: #f5222d;
          }
        }

        &.major {
          border-color: rgba(250, 173, 20, 0.3);
          background: rgba(250, 173, 20, 0.05);

          .level-icon {
            color: #faad14;
          }
        }

        &.general {
          border-color: rgba(24, 144, 255, 0.3);
          background: rgba(24, 144, 255, 0.05);

          .level-icon {
            color: #1890ff;
          }
        }

        .level-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .level-icon {
            font-size: 16px;
            margin-right: 8px;
          }

          .level-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            flex: 1;
          }
        }

        .level-progress {
          margin-top: 8px;
        }
      }
    }
  }

  .rectification-stats {
    h4 {
      margin-bottom: 16px;
      color: #262626;
      font-size: 16px;
    }

    .rectification-items {
      .rectification-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        margin-bottom: 12px;
        border-radius: 6px;
        background: #fafafa;
        border: 1px solid #e8e8e8;

        .item-header {
          display: flex;
          align-items: center;

          .item-icon {
            font-size: 16px;
            margin-right: 8px;

            &.in-progress {
              color: #faad14;
            }

            &.completed {
              color: #52c41a;
            }

            &.overdue {
              color: #f5222d;
            }
          }

          .item-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }
        }

        .item-content {
          .item-value {
            font-size: 18px;
            font-weight: 600;
            color: #1890ff;
            margin-right: 4px;
          }

          .item-unit {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }

    .rectification-summary {
      margin-top: 16px;
      padding: 12px;
      background: #f6f8fa;
      border-radius: 6px;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .summary-label {
          font-size: 14px;
          color: #666;
        }

        .summary-value {
          font-size: 16px;
          font-weight: 600;
          color: #52c41a;
        }
      }
    }
  }

  .trend-chart {
    .chart-header {
      margin-bottom: 20px;

      .chart-subtitle {
        font-size: 14px;
        color: #666;
      }
    }

    .chart-content {
      .trend-lines {
        position: relative;
        height: 120px;
        margin-bottom: 20px;
        background: linear-gradient(to right, #f0f0f0 0%, #f0f0f0 100%);
        border-radius: 4px;
        padding: 10px;

        .trend-point {
          position: absolute;
          bottom: 10px;
          display: flex;
          flex-direction: column;
          align-items: center;
          cursor: pointer;

          .point-marker {
            width: 4px;
            border-radius: 2px;
            margin-bottom: 2px;
            transition: all 0.3s ease;

            &.warning {
              background: #f5222d;
            }

            &.problem {
              background: #faad14;
            }

            &.rectification {
              background: #52c41a;
            }
          }

          .point-info {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            margin-bottom: 8px;

            .point-date {
              font-weight: 600;
              margin-bottom: 4px;
            }

            .point-values {
              .value-item {
                margin-bottom: 2px;

                &:last-child {
                  margin-bottom: 0;
                }

                &.warning {
                  color: #ff7875;
                }

                &.problem {
                  color: #ffd666;
                }

                &.rectification {
                  color: #95de64;
                }
              }
            }
          }

          &:hover {
            .point-info {
              opacity: 1;
            }

            .point-marker {
              transform: scale(1.5);
            }
          }
        }
      }

      .trend-legend {
        display: flex;
        justify-content: center;
        gap: 24px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 6px;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;

            &.warning {
              background: #f5222d;
            }

            &.problem {
              background: #faad14;
            }

            &.rectification {
              background: #52c41a;
            }
          }

          .legend-text {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }

  .list-controls {
    margin-bottom: 16px;
  }

  .warning-items {
    .warning-item {
      padding: 16px;
      margin-bottom: 12px;
      border-radius: 8px;
      border: 1px solid #e8e8e8;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }

      &.critical {
        border-left: 4px solid #f5222d;
      }

      &.major {
        border-left: 4px solid #faad14;
      }

      &.general {
        border-left: 4px solid #1890ff;
      }

      .warning-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .warning-content {
        margin-bottom: 12px;

        .warning-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 4px;
        }

        .warning-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
      }

      .warning-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .warning-info {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #999;

          .warning-unit {
            color: #666;
          }
        }

        .warning-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .warning-detail {
    .ant-descriptions {
      margin-top: 16px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .warning-monitor {
    .warning-stat {
      .stat-content {
        .stat-value {
          font-size: 24px;
        }
      }
    }

    .rectification-items {
      .rectification-item {
        .item-content {
          .item-value {
            font-size: 16px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .warning-monitor {
    .header-section {
      padding: 12px 16px;

      .title-area {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h3 {
          font-size: 16px;
        }
      }
    }

    .warning-overview,
    .problem-analysis,
    .warning-trend,
    .warning-list {
      padding: 16px;
    }

    .warning-stat {
      padding: 12px;
      margin-bottom: 12px;

      .stat-content {
        .stat-value {
          font-size: 20px;
        }

        .project-name {
          font-size: 13px;
        }
      }
    }

    .problem-analysis {
      .ant-row {
        flex-direction: column;
      }

      .ant-col {
        width: 100% !important;
        margin-bottom: 16px;
      }
    }

    .trend-chart {
      .chart-content {
        .trend-lines {
          height: 80px;

          .trend-point {
            .point-marker {
              width: 3px;
            }
          }
        }

        .trend-legend {
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .list-controls {
      .ant-row {
        flex-direction: column;
        gap: 8px;
      }

      .ant-col {
        width: 100% !important;
      }
    }

    .warning-items {
      .warning-item {
        padding: 12px;

        .warning-footer {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .warning-monitor {
    .warning-stat {
      .stat-content {
        .stat-value {
          font-size: 18px;
        }
      }
    }

    .trend-chart {
      .chart-content {
        .trend-lines {
          height: 60px;
        }
      }
    }

    .warning-items {
      .warning-item {
        .warning-content {
          .warning-title {
            font-size: 14px;
          }

          .warning-description {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
