<template>
  <div class="error-boundary">
    <!-- 网络错误 -->
    <div v-if="errorType === 'network'" class="error-container network-error">
      <div class="error-icon">
        <wifi-outlined />
      </div>
      <div class="error-title">网络连接异常</div>
      <div class="error-message">
        {{ errorMessage || '请检查您的网络连接，然后重试' }}
      </div>
      <div class="error-actions">
        <a-button @click="handleRetry" type="primary">
          <template #icon><reload-outlined /></template>
          重新加载
        </a-button>
        <a-button @click="handleRefresh">刷新页面</a-button>
      </div>
    </div>

    <!-- 服务器错误 -->
    <div v-else-if="errorType === 'server'" class="error-container server-error">
      <div class="error-icon">
        <exclamation-circle-outlined />
      </div>
      <div class="error-title">服务器错误</div>
      <div class="error-message">
        {{ errorMessage || '服务器暂时无法响应，请稍后重试' }}
      </div>
      <div class="error-actions">
        <a-button @click="handleRetry" type="primary">
          <template #icon><reload-outlined /></template>
          重试
        </a-button>
        <a-button @click="handleReport">报告问题</a-button>
      </div>
    </div>

    <!-- 权限错误 -->
    <div v-else-if="errorType === 'permission'" class="error-container permission-error">
      <div class="error-icon">
        <lock-outlined />
      </div>
      <div class="error-title">访问权限不足</div>
      <div class="error-message">
        {{ errorMessage || '您没有权限访问此内容，请联系管理员' }}
      </div>
      <div class="error-actions">
        <a-button @click="handleLogin" type="primary">
          <template #icon><user-outlined /></template>
          重新登录
        </a-button>
        <a-button @click="handleBack">返回上页</a-button>
      </div>
    </div>

    <!-- 数据错误 -->
    <div v-else-if="errorType === 'data'" class="error-container data-error">
      <div class="error-icon">
        <file-exclamation-outlined />
      </div>
      <div class="error-title">数据加载失败</div>
      <div class="error-message">
        {{ errorMessage || '数据格式异常或加载失败，请重试' }}
      </div>
      <div class="error-actions">
        <a-button @click="handleRetry" type="primary">
          <template #icon><reload-outlined /></template>
          重新加载
        </a-button>
        <a-button @click="handleReset">重置筛选</a-button>
      </div>
    </div>

    <!-- 404错误 -->
    <div v-else-if="errorType === 'notfound'" class="error-container notfound-error">
      <div class="error-icon">
        <question-circle-outlined />
      </div>
      <div class="error-title">页面不存在</div>
      <div class="error-message">
        {{ errorMessage || '抱歉，您访问的页面不存在或已被移除' }}
      </div>
      <div class="error-actions">
        <a-button @click="handleHome" type="primary">
          <template #icon><home-outlined /></template>
          返回首页
        </a-button>
        <a-button @click="handleBack">返回上页</a-button>
      </div>
    </div>

    <!-- 通用错误 -->
    <div v-else class="error-container generic-error">
      <div class="error-icon">
        <warning-outlined />
      </div>
      <div class="error-title">{{ errorTitle || '出现错误' }}</div>
      <div class="error-message">
        {{ errorMessage || '系统遇到了一个问题，请稍后重试' }}
      </div>
      <div class="error-details" v-if="showDetails && errorDetails">
        <a-collapse>
          <a-collapse-panel key="details" header="错误详情">
            <pre>{{ errorDetails }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <div class="error-actions">
        <a-button @click="handleRetry" type="primary">
          <template #icon><reload-outlined /></template>
          重试
        </a-button>
        <a-button @click="handleReport">报告问题</a-button>
        <a-button v-if="!showDetails && errorDetails" @click="toggleDetails" type="link">
          {{ showDetails ? '隐藏详情' : '查看详情' }}
        </a-button>
      </div>
    </div>

    <!-- 错误报告弹窗 -->
    <a-modal
      v-model:visible="reportVisible"
      title="报告问题"
      width="500px"
      @ok="submitReport"
      @cancel="reportVisible = false"
    >
      <a-form :model="reportForm" layout="vertical">
        <a-form-item label="问题描述" required>
          <a-textarea
            v-model:value="reportForm.description"
            placeholder="请描述您遇到的问题..."
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="联系方式">
          <a-input
            v-model:value="reportForm.contact"
            placeholder="邮箱或电话（可选）"
          />
        </a-form-item>
        <a-form-item label="错误信息" v-if="errorDetails">
          <a-textarea
            :value="errorDetails"
            :rows="3"
            readonly
            style="background: #f5f5f5;"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

// Props定义
interface Props {
  errorType?: 'network' | 'server' | 'permission' | 'data' | 'notfound' | 'generic'
  errorTitle?: string
  errorMessage?: string
  errorDetails?: string
  showRetry?: boolean
  showReport?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  errorType: 'generic',
  showRetry: true,
  showReport: true
})

// Emits定义
const emit = defineEmits<{
  'retry': []
  'refresh': []
  'reset': []
  'back': []
  'home': []
  'login': []
  'report': [data: any]
}>()

// 响应式数据
const showDetails = ref(false)
const reportVisible = ref(false)
const reportForm = reactive({
  description: '',
  contact: ''
})

// 方法
const handleRetry = () => {
  emit('retry')
}

const handleRefresh = () => {
  window.location.reload()
}

const handleReset = () => {
  emit('reset')
}

const handleBack = () => {
  window.history.back()
}

const handleHome = () => {
  window.location.href = '/'
}

const handleLogin = () => {
  emit('login')
}

const handleReport = () => {
  reportVisible.value = true
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const submitReport = () => {
  if (!reportForm.description.trim()) {
    message.error('请描述您遇到的问题')
    return
  }

  const reportData = {
    type: props.errorType,
    title: props.errorTitle,
    message: props.errorMessage,
    details: props.errorDetails,
    description: reportForm.description,
    contact: reportForm.contact,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href
  }

  emit('report', reportData)
  
  // 重置表单
  reportForm.description = ''
  reportForm.contact = ''
  reportVisible.value = false
  
  message.success('问题报告已提交，感谢您的反馈')
}
</script>

<style scoped lang="scss">
@import '../styles/global.scss';

.error-boundary {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.error-container {
  @extend .error-container;
  max-width: 500px;
  
  &.network-error {
    .error-icon {
      color: var(--warning-color);
    }
  }
  
  &.server-error {
    .error-icon {
      color: var(--error-color);
    }
  }
  
  &.permission-error {
    .error-icon {
      color: var(--warning-color);
    }
  }
  
  &.data-error {
    .error-icon {
      color: var(--info-color);
    }
  }
  
  &.notfound-error {
    .error-icon {
      color: var(--text-color-secondary);
    }
  }
  
  &.generic-error {
    .error-icon {
      color: var(--error-color);
    }
  }
  
  .error-details {
    margin: var(--spacing-lg) 0;
    
    .ant-collapse {
      .ant-collapse-content {
        .ant-collapse-content-box {
          padding: var(--spacing-md);
          
          pre {
            margin: 0;
            font-size: var(--font-size-sm);
            color: var(--text-color-secondary);
            background: var(--background-color-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius-sm);
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
          }
        }
      }
    }
  }
}

// 响应式适配
@include respond-below(md) {
  .error-container {
    max-width: 90%;
    padding: var(--spacing-lg);
    
    .error-icon {
      font-size: 40px;
    }
    
    .error-title {
      font-size: var(--font-size-lg);
    }
    
    .error-message {
      font-size: var(--font-size-md);
    }
    
    .error-actions {
      flex-direction: column;
      gap: var(--spacing-sm);
      
      .ant-btn {
        width: 100%;
      }
    }
  }
}

@include respond-below(sm) {
  .error-boundary {
    min-height: 250px;
    padding: var(--spacing-md);
  }
  
  .error-container {
    padding: var(--spacing-md);
    
    .error-icon {
      font-size: 36px;
    }
    
    .error-title {
      font-size: var(--font-size-md);
    }
    
    .error-message {
      font-size: var(--font-size-sm);
    }
  }
}

// 动画效果
.error-container {
  animation: fadeIn 0.6s ease-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.error-container.shake {
  animation: shake 0.6s ease-in-out;
}
</style>
