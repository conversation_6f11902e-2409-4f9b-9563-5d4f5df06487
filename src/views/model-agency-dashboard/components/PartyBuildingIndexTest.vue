<template>
  <div class="party-building-index-test">
    <h2>PartyBuildingIndex组件测试</h2>
    
    <!-- 组件展示 -->
    <div class="component-demo">
      <PartyBuildingIndex 
        :data="testData"
        :loading="loading"
        @agency-click="handleAgencyClick"
        @data-refresh="handleDataRefresh"
        @export="handleExport"
      />
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="测试控制">
        <a-space direction="vertical" style="width: 100%">
          <a-space>
            <a-button @click="loadTestData" type="primary">加载测试数据</a-button>
            <a-button @click="clearData">清空数据</a-button>
            <a-button @click="toggleLoading">切换加载状态</a-button>
            <a-button @click="randomizeData">随机化数据</a-button>
            <a-button @click="addAgency">添加机构</a-button>
          </a-space>
          
          <a-divider />
          
          <div>
            <h4>等级分布调整</h4>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="优秀单位">
                  <a-input-number 
                    v-model:value="testData.excellentCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="良好单位">
                  <a-input-number 
                    v-model:value="testData.goodCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="一般单位">
                  <a-input-number 
                    v-model:value="testData.averageCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="待提升单位">
                  <a-input-number 
                    v-model:value="testData.poorCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          
          <a-divider />
          
          <div>
            <h4>核心指标调整</h4>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="平均指数">
                  <a-input-number 
                    v-model:value="testData.averageIndex" 
                    :min="0" 
                    :max="100"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="目标指数">
                  <a-input-number 
                    v-model:value="testData.targetIndex" 
                    :min="0" 
                    :max="100"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="完成率">
                  <a-input-number 
                    v-model:value="testData.completionRate" 
                    :min="0" 
                    :max="100"
                    :step="0.1"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-space>
      </a-card>
    </div>
    
    <!-- 事件日志 -->
    <div class="event-log">
      <a-card title="事件日志">
        <div class="log-container">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <a-tag :color="getLogColor(log.type)">{{ log.type }}</a-tag>
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
            <div v-if="log.data" class="log-data">
              <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <a-button @click="clearLogs" size="small" style="margin-top: 8px;">清空日志</a-button>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import PartyBuildingIndex from './PartyBuildingIndex.vue'
import type { PartyBuildingData } from '../types'
import { mockDataService } from '../mock/data'

// 响应式数据
const loading = ref(false)
const eventLogs = ref<Array<{ type: string; time: string; message: string; data?: any }>>([])

const testData = ref<PartyBuildingData>({
  excellentCount: 45,
  goodCount: 78,
  averageCount: 34,
  poorCount: 12,
  averageIndex: 82.5,
  targetIndex: 85.0,
  completionRate: 97.1,
  municipalAgencies: 89,
  partyGroupDepartments: 45,
  governmentDepartments: 44,
  agencies: [
    {
      id: 'agency_1',
      name: '市委办公厅',
      type: 'party',
      comprehensiveIndex: 92.5,
      level: 'excellent',
      contactPerson: '张伟',
      contactPhone: '13812345678'
    },
    {
      id: 'agency_2',
      name: '市政府办公厅',
      type: 'government',
      comprehensiveIndex: 88.3,
      level: 'good',
      contactPerson: '李芳',
      contactPhone: '13887654321'
    },
    {
      id: 'agency_3',
      name: '市发展改革委',
      type: 'government',
      comprehensiveIndex: 85.7,
      level: 'good',
      contactPerson: '王强',
      contactPhone: '13856789012'
    }
  ]
})

// 计算属性
const totalUnits = computed(() => {
  return testData.value.excellentCount + testData.value.goodCount + 
         testData.value.averageCount + testData.value.poorCount
})

const totalAgencies = computed(() => {
  return testData.value.municipalAgencies + testData.value.partyGroupDepartments + 
         testData.value.governmentDepartments
})

// 方法
const loadTestData = async () => {
  loading.value = true
  addLog('INFO', '开始加载测试数据')
  
  try {
    const response = await mockDataService.fetchPartyBuildingData()
    testData.value = response.data
    
    addLog('SUCCESS', '测试数据加载成功', { 
      totalUnits: totalUnits.value,
      agencies: response.data.agencies.length 
    })
  } catch (error) {
    addLog('ERROR', `数据加载失败: ${error}`)
  } finally {
    loading.value = false
  }
}

const clearData = () => {
  testData.value = {
    excellentCount: 0,
    goodCount: 0,
    averageCount: 0,
    poorCount: 0,
    averageIndex: 0,
    targetIndex: 0,
    completionRate: 0,
    municipalAgencies: 0,
    partyGroupDepartments: 0,
    governmentDepartments: 0,
    agencies: []
  }
  
  addLog('INFO', '数据已清空')
}

const toggleLoading = () => {
  loading.value = !loading.value
  addLog('INFO', `加载状态: ${loading.value ? '开启' : '关闭'}`)
}

const randomizeData = () => {
  testData.value.excellentCount = Math.floor(Math.random() * 50)
  testData.value.goodCount = Math.floor(Math.random() * 80)
  testData.value.averageCount = Math.floor(Math.random() * 40)
  testData.value.poorCount = Math.floor(Math.random() * 20)
  
  testData.value.averageIndex = Math.round((Math.random() * 30 + 70) * 10) / 10
  testData.value.targetIndex = Math.round((Math.random() * 20 + 80) * 10) / 10
  testData.value.completionRate = Math.round((Math.random() * 30 + 70) * 10) / 10
  
  testData.value.municipalAgencies = Math.floor(Math.random() * 50) + 50
  testData.value.partyGroupDepartments = Math.floor(Math.random() * 30) + 20
  testData.value.governmentDepartments = Math.floor(Math.random() * 30) + 20
  
  addLog('INFO', '数据已随机化')
}

const addAgency = () => {
  const agencyNames = ['市教委', '市科技局', '市工业信息化局', '市公安局', '市民政局']
  const types = ['municipal', 'party', 'government']
  const levels = ['excellent', 'good', 'average', 'poor']
  
  const randomName = agencyNames[Math.floor(Math.random() * agencyNames.length)]
  const randomType = types[Math.floor(Math.random() * types.length)]
  const randomLevel = levels[Math.floor(Math.random() * levels.length)]
  
  const newAgency = {
    id: `agency_${Date.now()}`,
    name: randomName,
    type: randomType,
    comprehensiveIndex: Math.round((Math.random() * 40 + 60) * 10) / 10,
    level: randomLevel,
    contactPerson: `联系人${Math.floor(Math.random() * 100)}`,
    contactPhone: `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`
  }
  
  testData.value.agencies.push(newAgency)
  
  addLog('INFO', `添加了新机构: ${randomName}`)
}

const handleAgencyClick = (agencyId: string) => {
  const agency = testData.value.agencies.find(a => a.id === agencyId)
  addLog('AGENCY_CLICK', `点击了机构: ${agency?.name || agencyId}`, { agencyId })
}

const handleDataRefresh = () => {
  addLog('REFRESH', '触发数据刷新')
  loadTestData()
}

const handleExport = (data: any) => {
  addLog('EXPORT', '导出党建指数数据', data)
}

const addLog = (type: string, message: string, data?: any) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ type, time, message, data })
  
  // 保持最多30条日志
  if (eventLogs.value.length > 30) {
    eventLogs.value = eventLogs.value.slice(0, 30)
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

const getLogColor = (type: string) => {
  const colors = {
    INFO: 'blue',
    SUCCESS: 'green',
    ERROR: 'red',
    AGENCY_CLICK: 'purple',
    REFRESH: 'orange',
    EXPORT: 'cyan'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 监听数据变化
watch(() => [testData.value.excellentCount, testData.value.goodCount, testData.value.averageCount, testData.value.poorCount], () => {
  addLog('INFO', `等级分布已更新: 优秀${testData.value.excellentCount} 良好${testData.value.goodCount} 一般${testData.value.averageCount} 待提升${testData.value.poorCount}`)
}, { deep: true })

watch(() => [testData.value.averageIndex, testData.value.targetIndex, testData.value.completionRate], () => {
  addLog('INFO', `核心指标已更新: 平均${testData.value.averageIndex} 目标${testData.value.targetIndex} 完成率${testData.value.completionRate}%`)
}, { deep: true })

// 生命周期
onMounted(() => {
  addLog('INFO', 'PartyBuildingIndex测试组件已加载')
})
</script>

<style scoped>
.party-building-index-test {
  padding: 20px;
}

.component-demo {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.control-panel {
  margin-bottom: 24px;
}

.event-log {
  .log-container {
    max-height: 400px;
    overflow-y: auto;
    
    .log-item {
      margin-bottom: 12px;
      padding: 8px;
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      font-size: 12px;
      
      .log-time {
        margin: 0 8px;
        color: #666;
        min-width: 80px;
        display: inline-block;
      }
      
      .log-message {
        flex: 1;
        margin-right: 8px;
      }
      
      .log-data {
        margin-top: 8px;
        padding: 8px;
        background: #f5f5f5;
        border-radius: 4px;
        
        pre {
          margin: 0;
          font-size: 11px;
          max-height: 100px;
          overflow-y: auto;
        }
      }
    }
  }
}
</style>
