<template>
  <div class="party-building-index">
    <!-- 标题和操作栏 -->
    <div class="header-section">
      <div class="title-area">
        <h3>机关党建指数概览</h3>
        <a-space>
          <a-button @click="refreshData" :loading="loading">
            <template #icon><reload-outlined /></template>
            刷新数据
          </a-button>
          <a-dropdown>
            <a-button>
              <template #icon><filter-outlined /></template>
              筛选等级
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleLevelFilter">
                <a-menu-item key="">全部等级</a-menu-item>
                <a-menu-item key="excellent">优秀</a-menu-item>
                <a-menu-item key="good">良好</a-menu-item>
                <a-menu-item key="average">一般</a-menu-item>
                <a-menu-item key="poor">较差</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button @click="handleExport">
            <template #icon><download-outlined /></template>
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 指数等级分布 -->
    <div class="index-distribution">
      <a-card title="党建指数等级分布" class="chart-card">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="6">
            <div class="level-item excellent">
              <div class="level-header">
                <crown-outlined class="level-icon" />
                <span class="level-title">优秀单位</span>
              </div>
              <div class="level-content">
                <span class="level-count">{{ data?.excellentCount || 0 }}</span>
                <span class="level-unit">个</span>
              </div>
              <div class="level-footer">
                <span class="level-range">90分以上</span>
                <a-progress 
                  :percent="getPercentage(data?.excellentCount, totalUnits)"
                  size="small"
                  stroke-color="#52c41a"
                  :show-info="false"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="level-item good">
              <div class="level-header">
                <like-outlined class="level-icon" />
                <span class="level-title">良好单位</span>
              </div>
              <div class="level-content">
                <span class="level-count">{{ data?.goodCount || 0 }}</span>
                <span class="level-unit">个</span>
              </div>
              <div class="level-footer">
                <span class="level-range">80-90分</span>
                <a-progress 
                  :percent="getPercentage(data?.goodCount, totalUnits)"
                  size="small"
                  stroke-color="#1890ff"
                  :show-info="false"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="level-item average">
              <div class="level-header">
                <minus-circle-outlined class="level-icon" />
                <span class="level-title">一般单位</span>
              </div>
              <div class="level-content">
                <span class="level-count">{{ data?.averageCount || 0 }}</span>
                <span class="level-unit">个</span>
              </div>
              <div class="level-footer">
                <span class="level-range">70-80分</span>
                <a-progress 
                  :percent="getPercentage(data?.averageCount, totalUnits)"
                  size="small"
                  stroke-color="#faad14"
                  :show-info="false"
                />
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="12" :md="6">
            <div class="level-item poor">
              <div class="level-header">
                <warning-outlined class="level-icon" />
                <span class="level-title">待提升单位</span>
              </div>
              <div class="level-content">
                <span class="level-count">{{ data?.poorCount || 0 }}</span>
                <span class="level-unit">个</span>
              </div>
              <div class="level-footer">
                <span class="level-range">70分以下</span>
                <a-progress 
                  :percent="getPercentage(data?.poorCount, totalUnits)"
                  size="small"
                  stroke-color="#f5222d"
                  :show-info="false"
                />
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 核心指标对比 -->
    <div class="core-indicators">
      <a-card title="核心指标对比" class="chart-card">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="8">
            <div class="indicator-item">
              <div class="indicator-header">
                <bar-chart-outlined class="indicator-icon" />
                <span class="indicator-title">平均指数</span>
              </div>
              <div class="indicator-content">
                <span class="indicator-value">{{ data?.averageIndex || 0 }}</span>
                <span class="indicator-unit">分</span>
              </div>
              <div class="indicator-progress">
                <a-progress 
                  :percent="data?.averageIndex || 0"
                  stroke-color="#1890ff"
                  :show-info="false"
                />
                <span class="progress-text">当前平均水平</span>
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="8">
            <div class="indicator-item">
              <div class="indicator-header">
                <aim-outlined class="indicator-icon" />
                <span class="indicator-title">目标指数</span>
              </div>
              <div class="indicator-content">
                <span class="indicator-value">{{ data?.targetIndex || 0 }}</span>
                <span class="indicator-unit">分</span>
              </div>
              <div class="indicator-progress">
                <a-progress 
                  :percent="data?.targetIndex || 0"
                  stroke-color="#52c41a"
                  :show-info="false"
                />
                <span class="progress-text">年度目标值</span>
              </div>
            </div>
          </a-col>
          <a-col :xs="24" :sm="8">
            <div class="indicator-item">
              <div class="indicator-header">
                <rise-outlined class="indicator-icon" />
                <span class="indicator-title">完成率</span>
              </div>
              <div class="indicator-content">
                <span class="indicator-value">{{ data?.completionRate || 0 }}</span>
                <span class="indicator-unit">%</span>
              </div>
              <div class="indicator-progress">
                <a-progress 
                  :percent="data?.completionRate || 0"
                  stroke-color="#faad14"
                  :show-info="false"
                />
                <span class="progress-text">目标完成情况</span>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 机构分类统计 -->
    <div class="organization-stats">
      <a-card title="机构分类统计" class="chart-card">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="8">
            <a-card class="org-card municipal">
              <a-statistic
                title="市直机关"
                :value="data?.municipalAgencies || 0"
                suffix="个"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <bank-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="8">
            <a-card class="org-card party">
              <a-statistic
                title="党群部门"
                :value="data?.partyGroupDepartments || 0"
                suffix="个"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <team-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
          <a-col :xs="24" :sm="8">
            <a-card class="org-card government">
              <a-statistic
                title="政府部门"
                :value="data?.governmentDepartments || 0"
                suffix="个"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <apartment-outlined />
                </template>
              </a-statistic>
            </a-card>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 机构详细列表 -->
    <div class="agency-list">
      <a-card title="机构详细列表" class="chart-card">
        <div class="table-controls">
          <a-row :gutter="16" align="middle" style="margin-bottom: 16px;">
            <a-col :xs="24" :sm="12" :md="8">
              <a-input-search
                v-model:value="searchKeyword"
                placeholder="搜索机构名称"
                @search="handleSearch"
              />
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-select
                v-model:value="selectedType"
                placeholder="选择机构类型"
                style="width: 100%"
                allow-clear
                @change="handleTypeFilter"
              >
                <a-select-option value="">全部类型</a-select-option>
                <a-select-option value="municipal">市直机关</a-select-option>
                <a-select-option value="party">党群部门</a-select-option>
                <a-select-option value="government">政府部门</a-select-option>
              </a-select>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8">
              <a-select
                v-model:value="selectedLevel"
                placeholder="选择等级"
                style="width: 100%"
                allow-clear
                @change="handleLevelFilter"
              >
                <a-select-option value="">全部等级</a-select-option>
                <a-select-option value="excellent">优秀</a-select-option>
                <a-select-option value="good">良好</a-select-option>
                <a-select-option value="average">一般</a-select-option>
                <a-select-option value="poor">较差</a-select-option>
              </a-select>
            </a-col>
          </a-row>
        </div>

        <a-table
          :columns="agencyColumns"
          :data-source="filteredAgencies"
          :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
          :loading="loading"
          size="middle"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-button type="link" @click="handleAgencyClick(record)">
                {{ record.name }}
              </a-button>
            </template>
            <template v-else-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'comprehensiveIndex'">
              <div class="index-cell">
                <span class="index-value">{{ record.comprehensiveIndex.toFixed(1) }}</span>
                <a-progress 
                  :percent="record.comprehensiveIndex" 
                  size="small"
                  :stroke-color="getLevelColor(record.level)"
                  :show-info="false"
                />
              </div>
            </template>
            <template v-else-if="column.key === 'level'">
              <a-tag :color="getLevelColor(record.level)">
                {{ getLevelText(record.level) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button size="small" @click="handleAgencyDetail(record)">
                  详情
                </a-button>
                <a-button size="small" @click="handleAgencyEdit(record)">
                  编辑
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 机构详情弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      :title="selectedAgency?.name"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedAgency" class="agency-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="机构名称">
            {{ selectedAgency.name }}
          </a-descriptions-item>
          <a-descriptions-item label="机构类型">
            <a-tag :color="getTypeColor(selectedAgency.type)">
              {{ getTypeText(selectedAgency.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="综合指数">
            <a-tag :color="getLevelColor(selectedAgency.level)">
              {{ selectedAgency.comprehensiveIndex.toFixed(1) }}分
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="评定等级">
            <a-tag :color="getLevelColor(selectedAgency.level)">
              {{ getLevelText(selectedAgency.level) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="联系人">
            {{ selectedAgency.contactPerson }}
          </a-descriptions-item>
          <a-descriptions-item label="联系电话">
            {{ selectedAgency.contactPhone }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <a-spin size="large" tip="加载党建指数数据中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { PartyBuildingData, PartyBuildingIndexProps } from '../types'
import { mockDataService } from '../mock/data'

// Props定义
interface Props {
  data?: PartyBuildingData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits定义
const emit = defineEmits<{
  'agency-click': [agencyId: string]
  'data-refresh': []
  'export': [data: any]
}>()

// 响应式数据
const loading = ref(props.loading)
const data = ref<PartyBuildingData | null>(null)
const searchKeyword = ref('')
const selectedType = ref('')
const selectedLevel = ref('')
const detailVisible = ref(false)
const selectedAgency = ref<any>(null)

// 表格列定义
const agencyColumns = [
  {
    title: '机构名称',
    dataIndex: 'name',
    key: 'name',
    width: 200,
    fixed: 'left'
  },
  {
    title: '机构类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '综合指数',
    dataIndex: 'comprehensiveIndex',
    key: 'comprehensiveIndex',
    width: 150,
    sorter: (a: any, b: any) => a.comprehensiveIndex - b.comprehensiveIndex
  },
  {
    title: '评定等级',
    dataIndex: 'level',
    key: 'level',
    width: 100
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    key: 'contactPerson',
    width: 100
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    key: 'contactPhone',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 计算属性
const totalUnits = computed(() => {
  if (!data.value) return 0
  return data.value.excellentCount + data.value.goodCount + 
         data.value.averageCount + data.value.poorCount
})

const filteredAgencies = computed(() => {
  if (!data.value?.agencies) return []
  
  let agencies = data.value.agencies

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    agencies = agencies.filter(agency => 
      agency.name.toLowerCase().includes(keyword) ||
      agency.contactPerson.toLowerCase().includes(keyword)
    )
  }

  // 按机构类型筛选
  if (selectedType.value) {
    agencies = agencies.filter(agency => agency.type === selectedType.value)
  }

  // 按等级筛选
  if (selectedLevel.value) {
    agencies = agencies.filter(agency => agency.level === selectedLevel.value)
  }

  return agencies
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await mockDataService.fetchPartyBuildingData()
    data.value = response.data
  } catch (error) {
    console.error('加载党建指数数据失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  emit('data-refresh')
  loadData()
}

const getPercentage = (count: number = 0, total: number = 0) => {
  if (total === 0) return 0
  return Math.round((count / total) * 100)
}

const getLevelText = (level: string) => {
  const levelMap = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return levelMap[level as keyof typeof levelMap] || level
}

const getLevelColor = (level: string) => {
  const colorMap = {
    excellent: 'green',
    good: 'blue',
    average: 'orange',
    poor: 'red'
  }
  return colorMap[level as keyof typeof colorMap] || 'default'
}

const getTypeText = (type: string) => {
  const typeMap = {
    municipal: '市直机关',
    party: '党群部门',
    government: '政府部门'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getTypeColor = (type: string) => {
  const colorMap = {
    municipal: 'blue',
    party: 'purple',
    government: 'green'
  }
  return colorMap[type as keyof typeof colorMap] || 'default'
}

// 事件处理
const handleSearch = (value: string) => {
  searchKeyword.value = value
}

const handleTypeFilter = () => {
  // 触发筛选更新
}

const handleLevelFilter = (value?: any) => {
  if (typeof value === 'object' && value.key !== undefined) {
    selectedLevel.value = value.key
  }
}

const handleTableChange = (pagination: any, filters: any, sorter: any) => {
  console.log('Table change:', pagination, filters, sorter)
}

const handleAgencyClick = (agency: any) => {
  emit('agency-click', agency.id)
}

const handleAgencyDetail = (agency: any) => {
  selectedAgency.value = agency
  detailVisible.value = true
}

const handleAgencyEdit = (agency: any) => {
  // 编辑功能
  console.log('Edit agency:', agency)
}

const handleExport = () => {
  emit('export', data.value)
}

// 生命周期
onMounted(() => {
  if (!props.data) {
    loadData()
  }
})

// 监听props变化
watch(() => props.data, (newValue) => {
  if (newValue) {
    data.value = newValue
  }
})

watch(() => props.loading, (newValue) => {
  loading.value = newValue
})
</script>

<style scoped lang="scss">
.party-building-index {
  position: relative;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;

  .header-section {
    padding: 16px 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .title-area {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .index-distribution,
  .core-indicators,
  .organization-stats,
  .agency-list {
    padding: 20px;
    padding-top: 0;

    &:first-child {
      padding-top: 20px;
    }
  }

  .chart-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    .ant-card-head {
      border-bottom: 1px solid #e8e8e8;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }
  }

  .level-item {
    padding: 20px;
    border-radius: 8px;
    border: 2px solid transparent;
    background: #fafafa;
    transition: all 0.3s ease;
    height: 100%;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    &.excellent {
      border-color: rgba(82, 196, 26, 0.3);
      background: rgba(82, 196, 26, 0.05);

      &:hover {
        border-color: #52c41a;
        background: rgba(82, 196, 26, 0.1);
      }
    }

    &.good {
      border-color: rgba(24, 144, 255, 0.3);
      background: rgba(24, 144, 255, 0.05);

      &:hover {
        border-color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
      }
    }

    &.average {
      border-color: rgba(250, 173, 20, 0.3);
      background: rgba(250, 173, 20, 0.05);

      &:hover {
        border-color: #faad14;
        background: rgba(250, 173, 20, 0.1);
      }
    }

    &.poor {
      border-color: rgba(245, 34, 45, 0.3);
      background: rgba(245, 34, 45, 0.05);

      &:hover {
        border-color: #f5222d;
        background: rgba(245, 34, 45, 0.1);
      }
    }

    .level-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .level-icon {
        font-size: 18px;
        margin-right: 8px;
      }

      .level-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .level-content {
      margin-bottom: 16px;

      .level-count {
        font-size: 32px;
        font-weight: 600;
        margin-right: 4px;
      }

      .level-unit {
        font-size: 14px;
        color: #666;
      }
    }

    .level-footer {
      .level-range {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        display: block;
      }
    }

    // 等级特定颜色
    &.excellent {
      .level-icon,
      .level-count {
        color: #52c41a;
      }
    }

    &.good {
      .level-icon,
      .level-count {
        color: #1890ff;
      }
    }

    &.average {
      .level-icon,
      .level-count {
        color: #faad14;
      }
    }

    &.poor {
      .level-icon,
      .level-count {
        color: #f5222d;
      }
    }
  }

  .indicator-item {
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f6f9ff;
    }

    .indicator-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .indicator-icon {
        font-size: 16px;
        color: #1890ff;
        margin-right: 8px;
      }

      .indicator-title {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .indicator-content {
      margin-bottom: 12px;

      .indicator-value {
        font-size: 24px;
        font-weight: 600;
        color: #1890ff;
        margin-right: 4px;
      }

      .indicator-unit {
        font-size: 14px;
        color: #666;
      }
    }

    .indicator-progress {
      .progress-text {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        display: block;
      }
    }
  }

  .org-card {
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &.municipal {
      border-left: 4px solid #1890ff;
    }

    &.party {
      border-left: 4px solid #722ed1;
    }

    &.government {
      border-left: 4px solid #52c41a;
    }

    .ant-statistic {
      .ant-statistic-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .table-controls {
    margin-bottom: 16px;
  }

  .index-cell {
    .index-value {
      font-weight: 600;
      margin-bottom: 4px;
      display: block;
    }
  }

  .agency-detail {
    .ant-descriptions {
      margin-top: 16px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 8px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .party-building-index {
    .level-item {
      .level-content {
        .level-count {
          font-size: 28px;
        }
      }
    }

    .indicator-item {
      .indicator-content {
        .indicator-value {
          font-size: 20px;
        }
      }
    }

    .org-card {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .party-building-index {
    .header-section {
      padding: 12px 16px;

      .title-area {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        h3 {
          font-size: 16px;
        }
      }
    }

    .index-distribution,
    .core-indicators,
    .organization-stats,
    .agency-list {
      padding: 16px;
    }

    .level-item {
      padding: 16px;
      margin-bottom: 12px;

      .level-content {
        .level-count {
          font-size: 24px;
        }
      }
    }

    .indicator-item {
      padding: 12px;
      margin-bottom: 12px;

      .indicator-content {
        .indicator-value {
          font-size: 18px;
        }
      }
    }

    .org-card {
      margin-bottom: 12px;

      .ant-statistic {
        .ant-statistic-content {
          font-size: 18px;
        }
      }
    }

    .table-controls {
      .ant-row {
        flex-direction: column;
        gap: 8px;
      }

      .ant-col {
        width: 100% !important;
      }
    }

    // 表格在移动端的优化
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .party-building-index {
    .level-item {
      .level-content {
        .level-count {
          font-size: 20px;
        }
      }
    }

    .indicator-item {
      .indicator-content {
        .indicator-value {
          font-size: 16px;
        }
      }
    }

    .org-card {
      .ant-statistic {
        .ant-statistic-content {
          font-size: 16px;
        }
      }
    }

    // 移动端表格滚动
    .ant-table-wrapper {
      .ant-table {
        min-width: 600px;
      }
    }
  }
}
</style>
