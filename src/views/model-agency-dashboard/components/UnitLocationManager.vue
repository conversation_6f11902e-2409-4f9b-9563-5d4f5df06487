<template>
    <div class="unit-location-manager">
        <div class="manager-header">
            <h3 class="manager-title">
                <environment-outlined />
                单位位置管理
            </h3>
            <div class="header-actions">
                <a-button type="primary" @click="showAddModal">
                    <template #icon><plus-outlined /></template>
                    录入单位坐标
                </a-button>
                <a-button @click="refreshData" :loading="loading">
                    <template #icon><reload-outlined /></template>
                    刷新数据
                </a-button>
            </div>
        </div>

        <!-- 单位位置列表 -->
        <div class="location-table">
            <a-table :columns="columns" :data-source="unitLocations" :loading="loading" :pagination="pagination"
                row-key="id" size="middle" :scroll="{ x: 800 }">
                <!-- 单位名称列 -->
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'name'">
                        <div class="unit-name-cell">
                            <a-tag v-if="record.unitType === 'model'" color="gold">
                                <star-outlined />
                                模范机关
                            </a-tag>
                            <a-tag v-else-if="record.partyBuildingIndex >= 90" color="green">A级</a-tag>
                            <a-tag v-else-if="record.partyBuildingIndex >= 80" color="blue">B级</a-tag>
                            <a-tag v-else-if="record.partyBuildingIndex >= 70" color="orange">C级</a-tag>
                            <a-tag v-else-if="record.partyBuildingIndex >= 60" color="volcano">D级</a-tag>
                            <a-tag v-else color="red">E级</a-tag>
                            <span class="unit-name">{{ record.name }}</span>
                        </div>
                    </template>

                    <!-- 坐标列 -->
                    <template v-else-if="column.key === 'coordinates'">
                        <div class="coordinates-cell">
                            <div class="coordinate-item">
                                <span class="coordinate-label">经度:</span>
                                <span class="coordinate-value">{{ record.longitude?.toFixed(6) || '-' }}</span>
                            </div>
                            <div class="coordinate-item">
                                <span class="coordinate-label">纬度:</span>
                                <span class="coordinate-value">{{ record.latitude?.toFixed(6) || '-' }}</span>
                            </div>
                        </div>
                    </template>

                    <!-- 状态列 -->
                    <template v-else-if="column.key === 'status'">
                        <a-tag v-if="record.longitude && record.latitude" color="success">
                            <check-circle-outlined />
                            已定位
                        </a-tag>
                        <a-tag v-else color="warning">
                            <exclamation-circle-outlined />
                            未定位
                        </a-tag>
                    </template>

                    <!-- 操作列 -->
                    <template v-else-if="column.key === 'actions'">
                        <a-space>
                            <a-button type="link" size="small" @click="editLocation(record)"
                                :disabled="!record.longitude || !record.latitude">
                                <template #icon><edit-outlined /></template>
                                修改
                            </a-button>
                            <a-button type="link" size="small" @click="addLocation(record)"
                                v-if="!record.longitude || !record.latitude">
                                <template #icon><plus-outlined /></template>
                                录入
                            </a-button>
                            <a-popconfirm v-if="record.longitude && record.latitude" title="确定要删除该单位的坐标信息吗？"
                                ok-text="确定" cancel-text="取消" @confirm="deleteLocation(record)">
                                <a-button type="link" size="small" danger>
                                    <template #icon><delete-outlined /></template>
                                    删除
                                </a-button>
                            </a-popconfirm>
                        </a-space>
                    </template>
                </template>
            </a-table>
        </div>

        <!-- 单位选择弹窗 -->
        <a-modal v-model:visible="unitSelectVisible" title="选择要录入坐标的单位" width="800px" @cancel="handleUnitSelectCancel"
            :footer="null">
            <div class="unit-select-content">
                <a-spin :spinning="unitsLoading" tip="正在加载单位数据...">
                    <div v-if="!unitsLoading && availableUnits.length === 0" class="empty-state">
                        <a-empty description="暂无可录入坐标的单位" />
                    </div>
                    <div v-else class="units-grid">
                        <div v-for="unit in availableUnits" :key="unit.id" class="unit-card"
                            @click="handleUnitSelect(unit)">
                            <div class="unit-header">
                                <div class="unit-info">
                                    <h4 class="unit-name">{{ unit.name }}</h4>
                                    <span class="unit-district">{{ unit.district }}</span>
                                </div>
                                <div class="unit-badges">
                                    <a-tag v-if="unit.level === 'A'" color="green">A级</a-tag>
                                    <a-tag v-else-if="unit.level === 'B'" color="blue">B级</a-tag>
                                    <a-tag v-else-if="unit.level === 'C'" color="orange">C级</a-tag>
                                    <a-tag v-else color="red">{{ unit.level }}级</a-tag>
                                </div>
                            </div>
                            <div class="unit-details">
                                <div class="party-index">
                                    <span class="label">党建指数:</span>
                                    <span class="value">{{ unit.partyBuildingIndex }}分</span>
                                </div>
                                <div class="unit-type">
                                    <span class="type-tag">{{ unit.type === 'municipal' ? '市级机关' : '区县机关' }}</span>
                                </div>
                            </div>
                            <div class="select-hint">
                                <plus-outlined />
                                点击选择此单位
                            </div>
                        </div>
                    </div>
                </a-spin>
            </div>
        </a-modal>

        <!-- 坐标录入/编辑弹窗 -->
        <a-modal v-model:visible="modalVisible" :title="modalTitle" width="600px" @ok="handleSubmit"
            @cancel="handleCancel" :confirm-loading="submitting">
            <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item label="单位名称" name="name">
                            <a-input v-model:value="formData.name" disabled />
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="经度" name="longitude">
                            <a-input-number v-model:value="formData.longitude" :precision="6" :min="-180" :max="180"
                                style="width: 100%" placeholder="请输入经度坐标" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="纬度" name="latitude">
                            <a-input-number v-model:value="formData.latitude" :precision="6" :min="-90" :max="90"
                                style="width: 100%" placeholder="请输入纬度坐标" />
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="16">
                    <a-col :span="24">
                        <a-form-item label="详细地址" name="address">
                            <a-textarea v-model:value="formData.address" :rows="3" placeholder="请输入详细地址" />
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-row :gutter="16">
                    <a-col :span="12">
                        <a-form-item label="联系人" name="contactPerson">
                            <a-input v-model:value="formData.contactPerson" placeholder="请输入联系人" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-item label="联系电话" name="contactPhone">
                            <a-input v-model:value="formData.contactPhone" placeholder="请输入联系电话" />
                        </a-form-item>
                    </a-col>
                </a-row>

                <a-alert message="坐标录入提示" description="请确保输入的经纬度坐标准确无误。重庆市区经度范围约为105.17°-110.11°，纬度范围约为28.10°-32.13°。"
                    type="info" show-icon style="margin-top: 16px" />
            </a-form>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
    EnvironmentOutlined,
    PlusOutlined,
    ReloadOutlined,
    StarOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
    EditOutlined,
    DeleteOutlined
} from '@ant-design/icons-vue'
import type { UnitLocation } from '../types'

// Props
interface Props {
    data?: UnitLocation[]
    loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    data: () => [],
    loading: false
})

// Emits
const emit = defineEmits<{
    'location-update': [location: UnitLocation]
    'location-delete': [locationId: string | number]
    'data-refresh': []
}>()

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const unitSelectVisible = ref(false)
const submitting = ref(false)
const formRef = ref()
const editingRecord = ref<UnitLocation | null>(null)
const availableUnits = ref<any[]>([])
const unitsLoading = ref(false)
const selectedUnit = ref<any>(null)

// 表格列配置
const columns = [
    {
        title: '单位名称',
        key: 'name',
        dataIndex: 'name',
        width: 280,
        fixed: 'left'
    },
    {
        title: '所属区县',
        key: 'district',
        dataIndex: 'district',
        width: 120
    },
    {
        title: '坐标信息',
        key: 'coordinates',
        width: 200
    },
    {
        title: '定位状态',
        key: 'status',
        width: 100,
        align: 'center'
    },
    {
        title: '党建指数',
        key: 'partyBuildingIndex',
        dataIndex: 'partyBuildingIndex',
        width: 100,
        align: 'center'
    },
    {
        title: '操作',
        key: 'actions',
        width: 180,
        fixed: 'right'
    }
]

// 分页配置
const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`
})

// 表单数据
const formData = reactive({
    id: '',
    name: '',
    longitude: undefined as number | undefined,
    latitude: undefined as number | undefined,
    address: '',
    contactPerson: '',
    contactPhone: ''
})

// 表单验证规则
const formRules = {
    longitude: [
        { required: true, message: '请输入经度坐标' },
        { type: 'number', min: -180, max: 180, message: '经度范围应在-180到180之间' }
    ],
    latitude: [
        { required: true, message: '请输入纬度坐标' },
        { type: 'number', min: -90, max: 90, message: '纬度范围应在-90到90之间' }
    ]
}

// 计算属性
const unitLocations = computed(() => {
    const data = props.data || []
    pagination.total = data.length
    return data
})

const modalTitle = computed(() => {
    return editingRecord.value ? '修改单位坐标' : '录入单位坐标'
})

// 方法
const refreshData = () => {
    emit('data-refresh')
}

const showAddModal = () => {
    // 显示单位选择弹窗
    unitSelectVisible.value = true

    // 加载单位列表数据
    loadUnitsData()
}

// 加载单位数据的假接口
const loadUnitsData = async () => {
    unitsLoading.value = true
    try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 800))

        // 模拟单位数据
        const mockUnits = [
            {
                id: 'unit_001',
                name: '市委办公厅',
                district: '渝中区',
                type: 'municipal',
                level: 'A',
                partyBuildingIndex: 95,
                hasLocation: false
            },
            {
                id: 'unit_002',
                name: '市政府办公厅',
                district: '渝中区',
                type: 'municipal',
                level: 'A',
                partyBuildingIndex: 92,
                hasLocation: false
            },
            {
                id: 'unit_003',
                name: '市发展改革委',
                district: '渝中区',
                type: 'municipal',
                level: 'B',
                partyBuildingIndex: 88,
                hasLocation: false
            },
            {
                id: 'unit_004',
                name: '市教育委员会',
                district: '江北区',
                type: 'municipal',
                level: 'B',
                partyBuildingIndex: 85,
                hasLocation: false
            },
            {
                id: 'unit_005',
                name: '市科技局',
                district: '南岸区',
                type: 'municipal',
                level: 'C',
                partyBuildingIndex: 78,
                hasLocation: false
            },
            {
                id: 'unit_006',
                name: '市财政局',
                district: '渝中区',
                type: 'municipal',
                level: 'B',
                partyBuildingIndex: 86,
                hasLocation: false
            }
        ]

        // 过滤掉已有坐标的单位
        const existingUnitIds = unitLocations.value.map(item => item.id)
        availableUnits.value = mockUnits.filter(unit => !existingUnitIds.includes(unit.id))

        message.success(`加载到 ${availableUnits.value.length} 个可录入坐标的单位`)
    } catch (error) {
        console.error('加载单位数据失败:', error)
        message.error('加载单位数据失败，请重试')
    } finally {
        unitsLoading.value = false
    }
}

// 选择单位处理
const handleUnitSelect = (unit: any) => {
    selectedUnit.value = unit
    unitSelectVisible.value = false

    // 打开坐标录入弹窗
    editingRecord.value = {
        id: unit.id,
        name: unit.name,
        district: unit.district,
        partyBuildingIndex: unit.partyBuildingIndex,
        longitude: undefined,
        latitude: undefined,
        address: '',
        contactPerson: '',
        contactPhone: ''
    }

    resetForm()
    formData.id = unit.id
    formData.name = unit.name
    modalVisible.value = true
}

// 取消单位选择
const handleUnitSelectCancel = () => {
    unitSelectVisible.value = false
    selectedUnit.value = null
}

const addLocation = (record: UnitLocation) => {
    editingRecord.value = record
    resetForm()
    formData.id = record.id
    formData.name = record.name
    formData.address = record.address || ''
    formData.contactPerson = record.contactPerson || ''
    formData.contactPhone = record.contactPhone || ''
    modalVisible.value = true
}

const editLocation = (record: UnitLocation) => {
    editingRecord.value = record
    resetForm()
    formData.id = record.id
    formData.name = record.name
    formData.longitude = record.longitude
    formData.latitude = record.latitude
    formData.address = record.address || ''
    formData.contactPerson = record.contactPerson || ''
    formData.contactPhone = record.contactPhone || ''
    modalVisible.value = true
}

const deleteLocation = async (record: UnitLocation) => {
    try {
        loading.value = true
        // 这里调用删除API
        emit('location-delete', record.id)
        message.success('坐标信息删除成功')
    } catch (error) {
        console.error('删除坐标失败:', error)
        message.error('删除坐标失败，请重试')
    } finally {
        loading.value = false
    }
}

const handleSubmit = async () => {
    try {
        await formRef.value.validate()
        submitting.value = true

        const locationData: UnitLocation = {
            ...editingRecord.value!,
            longitude: formData.longitude!,
            latitude: formData.latitude!,
            address: formData.address,
            contactPerson: formData.contactPerson,
            contactPhone: formData.contactPhone
        }

        // 发送更新事件
        emit('location-update', locationData)

        message.success(editingRecord.value?.longitude ? '坐标修改成功' : '坐标录入成功')
        modalVisible.value = false
    } catch (error) {
        console.error('提交失败:', error)
    } finally {
        submitting.value = false
    }
}

const handleCancel = () => {
    modalVisible.value = false
    resetForm()
}

const resetForm = () => {
    formData.id = ''
    formData.name = ''
    formData.longitude = undefined
    formData.latitude = undefined
    formData.address = ''
    formData.contactPerson = ''
    formData.contactPhone = ''
    editingRecord.value = null
}

// 生命周期
onMounted(() => {
    // 初始化数据
})
</script>

<style scoped lang="scss">
.unit-location-manager {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .manager-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .manager-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;

            .anticon {
                color: #1890ff;
            }
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }
    }

    .location-table {
        .unit-name-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .unit-name {
                font-weight: 500;
            }
        }

        .coordinates-cell {
            .coordinate-item {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-bottom: 4px;

                &:last-child {
                    margin-bottom: 0;
                }

                .coordinate-label {
                    font-size: 12px;
                    color: #666;
                    min-width: 32px;
                }

                .coordinate-value {
                    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                    font-size: 12px;
                    color: #1890ff;
                    font-weight: 500;
                }
            }
        }
    }
}

// 单位选择弹窗样式
.unit-select-content {
    .empty-state {
        text-align: center;
        padding: 40px 0;
    }

    .units-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 16px;
        max-height: 500px;
        overflow-y: auto;
        padding: 8px;

        .unit-card {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
            position: relative;

            &:hover {
                border-color: #1890ff;
                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
                transform: translateY(-2px);
                background: white;

                .select-hint {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .unit-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;

                .unit-info {
                    flex: 1;

                    .unit-name {
                        margin: 0 0 4px 0;
                        font-size: 16px;
                        font-weight: 600;
                        color: #262626;
                        line-height: 1.4;
                    }

                    .unit-district {
                        font-size: 12px;
                        color: #8c8c8c;
                    }
                }

                .unit-badges {
                    flex-shrink: 0;
                }
            }

            .unit-details {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .party-index {
                    .label {
                        font-size: 12px;
                        color: #8c8c8c;
                        margin-right: 4px;
                    }

                    .value {
                        font-size: 14px;
                        font-weight: 600;
                        color: #1890ff;
                    }
                }

                .unit-type {
                    .type-tag {
                        font-size: 11px;
                        padding: 2px 6px;
                        background: #f0f0f0;
                        border-radius: 4px;
                        color: #666;
                    }
                }
            }

            .select-hint {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                font-size: 12px;
                color: #1890ff;
                opacity: 0;
                transform: translateY(4px);
                transition: all 0.3s ease;
                margin-top: 8px;
                padding-top: 8px;
                border-top: 1px solid #f0f0f0;
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .unit-location-manager {
        padding: 16px;

        .manager-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .header-actions {
                width: 100%;
                justify-content: flex-start;
            }
        }

        .location-table {
            :deep(.ant-table) {
                font-size: 12px;
            }
        }
    }

    .unit-select-content {
        .units-grid {
            grid-template-columns: 1fr;
            gap: 12px;

            .unit-card {
                padding: 12px;

                .unit-header {
                    .unit-info {
                        .unit-name {
                            font-size: 14px;
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .unit-location-manager {
        padding: 12px;

        .manager-header {
            .manager-title {
                font-size: 16px;
            }

            .header-actions {
                .ant-btn {
                    font-size: 12px;
                    padding: 4px 8px;
                }
            }
        }
    }
}
</style>