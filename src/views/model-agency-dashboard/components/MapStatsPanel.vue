<template>
  <div class="map-stats-panel" v-show="visible">
    <a-card class="stats-card" size="small">
      <template #title>
        <div class="stats-title">
          <span>{{ title }}</span>
          <a-button type="text" size="small" @click="onClose">
            <template #icon>
              <CloseOutlined />
            </template>
          </a-button>
        </div>
      </template>

      <!-- 区县统计信息 -->
      <div v-if="districtStats" class="district-stats">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-statistic
              title="机关单位总数"
              :value="districtStats.totalUnits"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="模范机关数量"
              :value="districtStats.modelOrganCount"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-statistic
              title="平均党建指数"
              :value="districtStats.averagePartyBuildingIndex"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="预警单位数量"
              :value="districtStats.warningUnitCount"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-col>
        </a-row>

        <!-- 项目数据 -->
        <a-divider orientation="left" style="margin: 16px 0 12px 0">项目数据</a-divider>
        <a-row :gutter="8">
          <a-col :span="8">
            <a-statistic
              title="项目总数"
              :value="districtStats.projectData.totalProjects"
              :value-style="{ fontSize: '14px' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="已完成"
              :value="districtStats.projectData.completedProjects"
              :value-style="{ fontSize: '14px', color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="进行中"
              :value="districtStats.projectData.ongoingProjects"
              :value-style="{ fontSize: '14px', color: '#1890ff' }"
            />
          </a-col>
        </a-row>

        <!-- 考核数据 -->
        <a-divider orientation="left" style="margin: 16px 0 12px 0">考核数据</a-divider>
        <a-row :gutter="8">
          <a-col :span="12">
            <a-statistic
              title="当前分数"
              :value="districtStats.assessmentData.currentScore"
              :precision="1"
              suffix="分"
              :value-style="{ fontSize: '14px', color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="整改完成率"
              :value="districtStats.assessmentData.rectificationRate"
              :precision="1"
              suffix="%"
              :value-style="{ fontSize: '14px', color: '#52c41a' }"
            />
          </a-col>
        </a-row>

        <!-- 等级分布 -->
        <a-divider orientation="left" style="margin: 16px 0 12px 0">等级分布</a-divider>
        <div class="level-distribution">
          <div v-for="level in districtStats.levelDistribution" :key="level.level" class="level-item">
            <span class="level-badge" :class="`level-${level.level.toLowerCase()}`">
              {{ level.level }}级
            </span>
            <span class="level-count">{{ level.count }}个</span>
            <span class="level-percentage">({{ level.percentage.toFixed(1) }}%)</span>
          </div>
        </div>
      </div>

      <!-- 市级统计信息 -->
      <div v-if="municipalStats" class="municipal-stats">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic
              title="总单位数"
              :value="municipalStats.totalUnits"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="模范机关"
              :value="municipalStats.modelOrganCount"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="预警单位"
              :value="municipalStats.warningUnitCount"
              :value-style="{ color: '#ff4d4f' }"
            />
          </a-col>
        </a-row>

        <a-row :gutter="16" style="margin-top: 16px">
          <a-col :span="12">
            <a-statistic
              title="平均党建指数"
              :value="municipalStats.averagePartyBuildingIndex"
              :precision="1"
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
          <a-col :span="12">
            <a-statistic
              title="模范机关率"
              :value="municipalStats.modelOrganRate"
              :precision="1"
              suffix="%"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>

        <!-- 区县排名 -->
        <a-divider orientation="left" style="margin: 16px 0 12px 0">区县排名 (前5名)</a-divider>
        <div class="district-ranking">
          <div v-for="(district, index) in municipalStats.districtRanking.slice(0, 5)" :key="district.name" class="ranking-item">
            <span class="ranking-number" :class="`rank-${index + 1}`">{{ index + 1 }}</span>
            <span class="district-name">{{ district.name }}</span>
            <span class="district-score">{{ district.averageScore.toFixed(1) }}分</span>
          </div>
        </div>

        <!-- 月度趋势 -->
        <a-divider orientation="left" style="margin: 16px 0 12px 0">月度趋势</a-divider>
        <div class="trend-chart">
          <div class="trend-item" v-for="trend in municipalStats.monthlyTrend.slice(-6)" :key="trend.month">
            <div class="trend-month">{{ trend.month }}</div>
            <div class="trend-bar">
              <div class="trend-fill" :style="{ width: `${(trend.averageScore / 100) * 100}%` }"></div>
            </div>
            <div class="trend-score">{{ trend.averageScore.toFixed(1) }}</div>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { CloseOutlined } from '@ant-design/icons-vue'
import type { DistrictStats, MunicipalStats } from '../types/map'

// Props
interface Props {
  visible?: boolean
  title?: string
  districtStats?: DistrictStats | null
  municipalStats?: MunicipalStats | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '统计信息',
  districtStats: null,
  municipalStats: null
})

// Emits
const emit = defineEmits<{
  'close': []
}>()

// 方法
const onClose = () => {
  emit('close')
}
</script>

<style scoped lang="scss">
.map-stats-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;

  .stats-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

    :deep(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    :deep(.ant-card-body) {
      padding: 16px;
    }
  }

  .stats-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .district-stats,
  .municipal-stats {
    .level-distribution {
      .level-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 6px 8px;
        background: #f8f9fa;
        border-radius: 4px;

        &:last-child {
          margin-bottom: 0;
        }

        .level-badge {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          color: white;
          margin-right: 8px;
          min-width: 40px;
          text-align: center;

          &.level-a {
            background: #52c41a;
          }

          &.level-b {
            background: #1890ff;
          }

          &.level-c {
            background: #faad14;
          }

          &.level-d {
            background: #ff7a45;
          }

          &.level-e {
            background: #ff4d4f;
          }
        }

        .level-count {
          font-weight: 500;
          margin-right: 8px;
        }

        .level-percentage {
          color: #666;
          font-size: 12px;
        }
      }
    }

    .district-ranking {
      .ranking-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .ranking-number {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          border-radius: 50%;
          font-size: 12px;
          font-weight: bold;
          color: white;
          margin-right: 12px;

          &.rank-1 {
            background: #ffd700;
          }

          &.rank-2 {
            background: #c0c0c0;
          }

          &.rank-3 {
            background: #cd7f32;
          }

          &.rank-4,
          &.rank-5 {
            background: #1890ff;
          }
        }

        .district-name {
          flex: 1;
          font-weight: 500;
        }

        .district-score {
          color: #1890ff;
          font-weight: 500;
        }
      }
    }

    .trend-chart {
      .trend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .trend-month {
          width: 60px;
          font-size: 12px;
          color: #666;
        }

        .trend-bar {
          flex: 1;
          height: 16px;
          background: #f0f0f0;
          border-radius: 8px;
          margin: 0 8px;
          overflow: hidden;

          .trend-fill {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #52c41a);
            border-radius: 8px;
            transition: width 0.3s ease;
          }
        }

        .trend-score {
          width: 40px;
          font-size: 12px;
          font-weight: 500;
          color: #1890ff;
          text-align: right;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .map-stats-panel {
    width: 95vw;
    max-height: 85vh;

    .stats-card {
      :deep(.ant-card-body) {
        padding: 12px;
      }
    }

    .district-stats,
    .municipal-stats {
      .level-distribution {
        .level-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }

      .district-ranking {
        .ranking-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .map-stats-panel {
    width: 98vw;
    max-height: 90vh;

    .stats-card {
      :deep(.ant-card-head) {
        padding: 8px 12px;
      }

      :deep(.ant-card-body) {
        padding: 8px;
      }
    }
  }
}
</style>