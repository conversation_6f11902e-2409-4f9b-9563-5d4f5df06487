/**
 * 模范机关总览看板 - 类型定义
 */

// 单位位置信息
export interface UnitLocation {
  id: string
  name: string
  longitude: number
  latitude: number
  district: string
  address: string
  partyBuildingIndex: number
  level: 'excellent' | 'good' | 'average' | 'poor'
  unitType: 'municipal' | 'district' | 'county'
  contactPerson?: string
  contactPhone?: string
  description?: string
}

// 项目统计数据
export interface ProjectData {
  // 基础统计
  totalProjects: number
  completedProjects: number
  completionRate: number
  
  // 单位数量统计
  applicantUnits: number
  cultivationUnits: number
  benchmarkUnits: number
  
  // 比例数据
  applicantRatio: number
  cultivationRatio: number
  benchmarkRatio: number
  
  // 指标统计
  totalIndicators: number
  smartFilterIndicators: number
  dataResources: number
  dataSourceUnits: number
  
  // 区域类型
  regionType: 'municipal' | 'district'
  regionName?: string
}

// 党建指数数据
export interface PartyBuildingData {
  // 等次分布
  excellentCount: number
  goodCount: number
  averageCount: number
  poorCount: number
  
  // 核心指标
  averageIndex: number
  targetIndex: number
  completionRate: number
  
  // 机构分类统计
  municipalAgencies: number
  partyGroupDepartments: number
  governmentDepartments: number
  
  // 详细机构列表
  agencies: Array<{
    id: string
    name: string
    type: 'municipal' | 'party' | 'government'
    comprehensiveIndex: number
    level: 'excellent' | 'good' | 'average' | 'poor'
    contactPerson: string
    contactPhone: string
  }>
}

// 考核督查数据
export interface AssessmentData {
  // 历年考核数据
  yearlyAssessments: Array<{
    year: number
    score: number
    level: 'excellent' | 'good' | 'average' | 'poor'
    rank?: number
  }>
  
  // 督查统计
  supervisionUnits: number
  completedSupervision: number
  completionRate: number
  
  // 等次分布
  levelDistribution: {
    excellent: number
    good: number
    average: number
    poor: number
  }
  
  // 趋势分析
  trendAnalysis: {
    direction: 'up' | 'down' | 'stable'
    changeRate: number
    description: string
  }
}

// 预警监控数据
export interface WarningData {
  // 预警统计
  totalWarnings: number
  municipalWarnings: number
  districtWarnings: number
  
  // 最高预警项目
  highestWarningProject: {
    projectName: string
    warningCount: number
    affectedUnits: number
  }
  
  // 问题分析
  problemAnalysis: {
    totalProblems: number
    rectificationRate: number
    
    // 分级问题统计
    criticalProblems: number
    majorProblems: number
    generalProblems: number
    
    // 整改进度
    inProgress: number
    completed: number
    overdue: number
  }
  
  // 预警趋势
  warningTrend: Array<{
    date: string
    warningCount: number
    problemCount: number
    rectificationCount: number
  }>
}

// 区县数据
export interface DistrictData {
  districtId: string
  districtName: string
  
  // 基础统计
  totalUnits: number
  partyOrganizations: number
  
  // 党建评分
  overallScore: number
  modelAgencyCount: number
  modelAgencyRatio: number
  
  // 指数等级分布
  indexDistribution: {
    excellent: number
    good: number
    average: number
    poor: number
  }
  
  // 地理信息
  coordinates: {
    longitude: number
    latitude: number
  }
  boundary?: Array<{
    longitude: number
    latitude: number
  }>
}

// 搜索参数
export interface SearchParams {
  keyword?: string
  district?: string
  unitType?: 'municipal' | 'district' | 'county'
  level?: 'excellent' | 'good' | 'average' | 'poor'
  dateRange?: [string, string]
}

// 组件Props类型
export interface MapViewProps {
  unitLocations: UnitLocation[]
  selectedDistrict?: string
  onUnitClick?: (unit: UnitLocation) => void
  onDistrictChange?: (district: string) => void
}

export interface StatisticsOverviewProps {
  projectData: ProjectData
  partyBuildingData: PartyBuildingData
  loading?: boolean
}

export interface ProjectDataOverviewProps {
  municipalData: ProjectData
  districtData: ProjectData[]
  selectedDistrict?: string
  onDistrictSelect?: (district: string) => void
}

export interface PartyBuildingIndexProps {
  data: PartyBuildingData
  loading?: boolean
  onAgencyClick?: (agencyId: string) => void
}

export interface AssessmentSupervisionProps {
  municipalData: AssessmentData
  districtData: AssessmentData[]
  selectedDistrict?: string
  onYearSelect?: (year: number) => void
}

export interface WarningMonitorProps {
  data: WarningData
  loading?: boolean
  onWarningClick?: (warningId: string) => void
}

// 事件类型
export interface DashboardEvents {
  'unit-selected': UnitLocation
  'district-changed': string
  'data-export': {
    type: 'project' | 'assessment' | 'warning'
    format: 'excel' | 'pdf'
  }
  'detail-view': {
    type: 'unit' | 'project' | 'assessment'
    id: string
  }
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: number
}

// 分页参数
export interface PaginationParams {
  current: number
  pageSize: number
  total?: number
}

// 排序参数
export interface SortParams {
  field: string
  order: 'ascend' | 'descend'
}

// 筛选参数
export interface FilterParams {
  [key: string]: any
}
