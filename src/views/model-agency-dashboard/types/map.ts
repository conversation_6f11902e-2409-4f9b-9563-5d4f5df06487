// 地图相关类型定义

// 机关单位信息
export interface OrganUnit {
    id: string
    name: string
    code: string
    type: 'municipal' | 'district' | 'county' // 市级机关 | 区县机关 | 县级机关
    category: 'party' | 'government' | 'other' // 党群部门 | 政府部门 | 其他
    district: string // 所属区县
    address: string
    longitude: number // 经度
    latitude: number // 纬度
    contactPerson: string
    contactPhone: string
    description?: string

    // 党建指数相关
    partyBuildingIndex: number // 党建指数分数
    partyBuildingLevel: 'A' | 'B' | 'C' | 'D' | 'E' // 党建指数等级
    isModelOrgan: boolean // 是否为模范机关

    // 项目数据
    projectData: {
        totalProjects: number // 项目总数
        completedProjects: number // 完成评选项目数
        applicationUnits: number // 申报单位数
        cultivationUnits: number // 培育单位数
        benchmarkUnits: number // 标杆单位数
        applicationRatio: number // 申报单位比例
        cultivationRatio: number // 培育单位比例
        benchmarkRatio: number // 标杆单位比例
        totalIndicators: number // 指标总数
        smartFilterIndicators: number // 智能筛选指标数
        dataResources: number // 数据资源总数
        dataSourceUnits: number // 数据来源单位数
    }

    // 考核数据
    assessmentData: {
        currentScore: number // 当前考核分数
        currentLevel: 'excellent' | 'good' | 'average' | 'poor' // 考核等次
        historicalScores: Array<{
            year: number
            score: number
            level: string
        }>
        supervisionLists: number // 收到督查清单数
        completedSupervision: number // 完成整改反馈数
        supervisionCompletionRate: number // 督查整改完成率
    }

    // 预警数据
    warningData: {
        warningItems: number // 预警项数
        highestWarningProject: string // 最高预警量项目
        highestWarningUnits: number // 最高预警量项目单位数
        totalProblems: number // 问题总数
        rectificationRate: number // 整改率
        focusProblems: number // 重点关注问题
        majorProblems: number // 较大问题
        generalProblems: number // 一般问题
        rectificationInProgress: number // 整改推进中
        rectificationCompleted: number // 整改销号
        overdueRectification: number // 逾期整改
    }
}

// 区县统计数据
export interface DistrictStats {
    districtName: string
    districtCode: string

    // 基础统计
    totalUnits: number // 机关单位总数
    totalPartyOrganizations: number // 机关党组（党委）总数

    // 党建指数统计
    partyBuildingStats: {
        averageIndex: number // 平均党建指数
        levelACount: number // A级单位数
        levelBCount: number // B级单位数
        levelCCount: number // C级单位数
        levelDCount: number // D级单位数
        levelECount: number // E级单位数
        modelOrganCount: number // 模范机关数量
        modelOrganRatio: number // 模范机关占比
    }

    // 项目统计
    projectStats: {
        totalProjects: number
        completedProjects: number
        applicationUnits: number
        cultivationUnits: number
        benchmarkUnits: number
        applicationRatio: number
        cultivationRatio: number
        benchmarkRatio: number
        totalIndicators: number
        smartFilterIndicators: number
        dataResources: number
        dataSourceUnits: number
    }

    // 考核统计
    assessmentStats: {
        averageScore: number
        excellentCount: number
        goodCount: number
        averageCount: number
        poorCount: number
        supervisionLists: number
        completedSupervision: number
        supervisionCompletionRate: number
    }

    // 预警统计
    warningStats: {
        totalWarningItems: number
        highestWarningProject: string
        highestWarningUnits: number
        totalProblems: number
        rectificationRate: number
        focusProblems: number
        majorProblems: number
        generalProblems: number
    }
}

// 市级统计数据
export interface MunicipalStats {
    // 基础统计
    totalMunicipalUnits: number // 市级机关单位总数
    totalMunicipalPartyOrganizations: number // 市级机关党组（党委）总数

    // 项目统计
    municipalProjectStats: {
        totalProjects: number
        completedProjects: number
        applicationUnits: number
        cultivationUnits: number
        benchmarkUnits: number
        applicationRatio: number
        cultivationRatio: number
        benchmarkRatio: number
        totalIndicators: number
        smartFilterIndicators: number
        dataResources: number
        dataSourceUnits: number
    }

    // 党建指数统计
    municipalPartyBuildingStats: {
        levelACount: number
        levelBCount: number
        levelCCount: number
        levelDCount: number
        levelECount: number
    }

    // 考核统计
    municipalAssessmentStats: {
        excellentCount: number
        goodCount: number
        averageCount: number
        poorCount: number
        supervisionLists: number
        completedSupervision: number
        supervisionCompletionRate: number
    }

    // 预警统计
    municipalWarningStats: {
        totalWarningItems: number
        highestWarningProject: string
        highestWarningUnits: number
    }

    // 区县统计汇总
    districtStats: DistrictStats[]
}

// 地图搜索结果
export interface MapSearchResult {
    id: string
    name: string
    type: 'unit' | 'district' | 'address'
    coordinates: [number, number]
    address: string
    district?: string
}

// 导航相关
export interface NavigationRoute {
    id: string
    startPoint: [number, number]
    endPoint: [number, number]
    waypoints: Array<[number, number]>
    distance: number
    duration: number
    routePath: Array<[number, number]>
    createTime: string
}

// 地图配置
export interface MapConfig {
    center: [number, number] // 地图中心点
    zoom: number // 缩放级别
    minZoom: number
    maxZoom: number
    enableNavigation: boolean // 是否启用导航
    enableSearch: boolean // 是否启用搜索
    enableDistrictDrill: boolean // 是否启用区县下钻
}

// 地图标注点样式配置
export interface MarkerStyleConfig {
    excellent: {
        color: string
        icon: string
        size: number
    }
    good: {
        color: string
        icon: string
        size: number
    }
    average: {
        color: string
        icon: string
        size: number
    }
    poor: {
        color: string
        icon: string
        size: number
    }
    modelOrgan: {
        color: string
        icon: string
        size: number
        animation: boolean
    }
}

// 地图事件类型
export interface MapEvents {
    'unit-click': (unit: OrganUnit) => void
    'district-click': (district: string, stats: DistrictStats) => void
    'search-result': (results: MapSearchResult[]) => void
    'navigation-start': (route: NavigationRoute) => void
    'navigation-end': (route: NavigationRoute) => void
    'zoom-change': (zoom: number) => void
    'center-change': (center: [number, number]) => void
}