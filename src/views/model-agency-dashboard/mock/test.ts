/**
 * 模拟数据测试文件
 * 用于验证数据生成函数是否正常工作
 */

import {
  generateUnitLocations,
  generateProjectStats,
  generatePartyBuildingData,
  generateAssessmentData,
  generateWarningData,
  generateDistrictData,
  mockDataService
} from './data'

// 测试数据生成函数
console.log('=== 模拟数据测试 ===')

try {
  // 测试单位位置数据
  const unitLocations = generateUnitLocations()
  console.log(`✅ 单位位置数据生成成功: ${unitLocations.length} 个单位`)
  
  // 测试项目统计数据
  const projectStats = generateProjectStats()
  console.log(`✅ 项目统计数据生成成功: 市级 ${projectStats.municipal.totalProjects} 个项目, ${projectStats.districts.length} 个区县`)
  
  // 测试党建指数数据
  const partyBuildingData = generatePartyBuildingData()
  console.log(`✅ 党建指数数据生成成功: ${partyBuildingData.agencies.length} 个机构`)
  
  // 测试考核督查数据
  const assessmentData = generateAssessmentData()
  console.log(`✅ 考核督查数据生成成功: 市级 ${assessmentData.municipal.yearlyAssessments.length} 年数据, ${assessmentData.districts.length} 个区县`)
  
  // 测试预警监控数据
  const warningData = generateWarningData()
  console.log(`✅ 预警监控数据生成成功: ${warningData.totalWarnings} 个预警`)
  
  // 测试区县数据
  const districtData = generateDistrictData()
  console.log(`✅ 区县数据生成成功: ${districtData.length} 个区县`)
  
  console.log('🎉 所有模拟数据生成测试通过!')
  
} catch (error) {
  console.error('❌ 模拟数据生成测试失败:', error)
}

// 导出测试函数供外部调用
export function runMockDataTests() {
  return {
    unitLocations: generateUnitLocations(),
    projectStats: generateProjectStats(),
    partyBuildingData: generatePartyBuildingData(),
    assessmentData: generateAssessmentData(),
    warningData: generateWarningData(),
    districtData: generateDistrictData()
  }
}
