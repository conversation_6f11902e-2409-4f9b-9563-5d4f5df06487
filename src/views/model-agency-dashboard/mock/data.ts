/**
 * 模范机关总览看板 - 模拟数据服务
 */

import type {
  UnitLocation,
  ProjectData,
  PartyBuildingData,
  AssessmentData,
  WarningData,
  DistrictData,
  ApiResponse
} from '../types'

// 重庆市区县列表
const DISTRICTS = [
  '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区',
  '北碚区', '渝北区', '巴南区', '涪陵区', '长寿区', '江津区',
  '合川区', '永川区', '南川区', '綦江区', '大足区', '璧山区',
  '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区',
  '城口县', '丰都县', '垫江县', '忠县', '云阳县', '奉节县',
  '巫山县', '巫溪县', '石柱县', '秀山县', '酉阳县', '彭水县'
]

// 机关单位类型
const UNIT_TYPES = [
  '市委办公厅', '市政府办公厅', '市人大常委会办公厅', '市政协办公厅',
  '市纪委监委', '市委组织部', '市委宣传部', '市委统战部',
  '市发展改革委', '市教委', '市科技局', '市工业信息化局',
  '市公安局', '市民政局', '市司法局', '市财政局',
  '市人力社保局', '市规划自然资源局', '市生态环境局', '市住房城乡建委',
  '市交通局', '市水利局', '市农业农村委', '市商务委',
  '市文化旅游委', '市卫生健康委', '市应急局', '市审计局',
  '市市场监管局', '市国资委', '市税务局', '市统计局'
]

/**
 * 生成单位位置数据
 */
export function generateUnitLocations(): UnitLocation[] {
  const locations: UnitLocation[] = []
  
  // 生成市级机关单位
  UNIT_TYPES.forEach((unitName, index) => {
    const baseId = `municipal_${index + 1}`
    const longitude = 106.5 + (Math.random() - 0.5) * 0.1 // 重庆市中心附近
    const latitude = 29.5 + (Math.random() - 0.5) * 0.1
    
    locations.push({
      id: baseId,
      name: unitName,
      longitude,
      latitude,
      district: '渝中区',
      address: `重庆市渝中区${unitName.replace('市', '')}大楼`,
      partyBuildingIndex: 75 + Math.random() * 25, // 75-100分
      level: getRandomLevel(),
      unitType: 'municipal',
      contactPerson: `${getRandomName()}`,
      contactPhone: generatePhoneNumber(),
      description: `${unitName}负责相关职能工作`
    })
  })
  
  // 生成区县机关单位
  DISTRICTS.slice(0, 10).forEach((district, districtIndex) => {
    const unitCount = 3 + Math.floor(Math.random() * 3) // 每个区县3-5个单位
    
    for (let i = 0; i < unitCount; i++) {
      const unitId = `district_${districtIndex}_${i + 1}`
      const longitude = 106.3 + Math.random() * 0.4
      const latitude = 29.3 + Math.random() * 0.4
      
      locations.push({
        id: unitId,
        name: `${district}${['党委办公室', '政府办公室', '组织部', '宣传部', '统战部'][i] || '机关工委'}`,
        longitude,
        latitude,
        district,
        address: `重庆市${district}政府大楼`,
        partyBuildingIndex: 60 + Math.random() * 35, // 60-95分
        level: getRandomLevel(),
        unitType: 'district',
        contactPerson: getRandomName(),
        contactPhone: generatePhoneNumber(),
        description: `${district}机关党建工作`
      })
    }
  })
  
  return locations
}

/**
 * 生成项目统计数据
 */
export function generateProjectStats(): { municipal: ProjectData; districts: ProjectData[] } {
  const municipal: ProjectData = {
    totalProjects: 156,
    completedProjects: 142,
    completionRate: 91.0,
    applicantUnits: 89,
    cultivationUnits: 67,
    benchmarkUnits: 34,
    applicantRatio: 57.1,
    cultivationRatio: 43.0,
    benchmarkRatio: 21.8,
    totalIndicators: 1247,
    smartFilterIndicators: 892,
    dataResources: 2156,
    dataSourceUnits: 78,
    regionType: 'municipal',
    regionName: '重庆市'
  }
  
  const districts: ProjectData[] = DISTRICTS.slice(0, 12).map((district) => ({
    totalProjects: 12 + Math.floor(Math.random() * 20),
    completedProjects: 8 + Math.floor(Math.random() * 15),
    completionRate: 70 + Math.random() * 25,
    applicantUnits: 5 + Math.floor(Math.random() * 10),
    cultivationUnits: 3 + Math.floor(Math.random() * 8),
    benchmarkUnits: 1 + Math.floor(Math.random() * 5),
    applicantRatio: 40 + Math.random() * 30,
    cultivationRatio: 30 + Math.random() * 25,
    benchmarkRatio: 10 + Math.random() * 20,
    totalIndicators: 80 + Math.floor(Math.random() * 120),
    smartFilterIndicators: 50 + Math.floor(Math.random() * 80),
    dataResources: 150 + Math.floor(Math.random() * 200),
    dataSourceUnits: 8 + Math.floor(Math.random() * 15),
    regionType: 'district' as const,
    regionName: district
  }))
  
  return { municipal, districts }
}

/**
 * 生成党建指数数据
 */
export function generatePartyBuildingData(): PartyBuildingData {
  return {
    excellentCount: 45,
    goodCount: 78,
    averageCount: 34,
    poorCount: 12,
    averageIndex: 82.5,
    targetIndex: 85.0,
    completionRate: 97.1,
    municipalAgencies: 89,
    partyGroupDepartments: 45,
    governmentDepartments: 44,
    agencies: UNIT_TYPES.slice(0, 15).map((name, index) => ({
      id: `agency_${index + 1}`,
      name,
      type: index < 8 ? 'party' : (index < 16 ? 'government' : 'municipal'),
      comprehensiveIndex: 70 + Math.random() * 30,
      level: getRandomLevel(),
      contactPerson: getRandomName(),
      contactPhone: generatePhoneNumber()
    }))
  }
}

/**
 * 生成考核督查数据
 */
export function generateAssessmentData(): { municipal: AssessmentData; districts: AssessmentData[] } {
  const currentYear = new Date().getFullYear()
  
  const municipal: AssessmentData = {
    yearlyAssessments: Array.from({ length: 5 }, (_, i) => ({
      year: currentYear - 4 + i,
      score: 80 + Math.random() * 15,
      level: getRandomLevel(),
      rank: 1 + Math.floor(Math.random() * 10)
    })),
    supervisionUnits: 156,
    completedSupervision: 142,
    completionRate: 91.0,
    levelDistribution: {
      excellent: 45,
      good: 78,
      average: 28,
      poor: 5
    },
    trendAnalysis: {
      direction: 'up',
      changeRate: 5.2,
      description: '整体考核成绩呈上升趋势，党建工作成效显著'
    }
  }
  
  const districts: AssessmentData[] = DISTRICTS.slice(0, 10).map(district => ({
    yearlyAssessments: Array.from({ length: 5 }, (_, i) => ({
      year: currentYear - 4 + i,
      score: 70 + Math.random() * 20,
      level: getRandomLevel()
    })),
    supervisionUnits: 8 + Math.floor(Math.random() * 12),
    completedSupervision: 6 + Math.floor(Math.random() * 10),
    completionRate: 75 + Math.random() * 20,
    levelDistribution: {
      excellent: Math.floor(Math.random() * 8),
      good: Math.floor(Math.random() * 12),
      average: Math.floor(Math.random() * 6),
      poor: Math.floor(Math.random() * 3)
    },
    trendAnalysis: {
      direction: Math.random() > 0.3 ? 'up' : (Math.random() > 0.5 ? 'stable' : 'down'),
      changeRate: -5 + Math.random() * 10,
      description: `${district}考核督查工作稳步推进`
    }
  }))
  
  return { municipal, districts }
}

/**
 * 生成预警监控数据
 */
export function generateWarningData(): WarningData {
  return {
    totalWarnings: 23,
    municipalWarnings: 15,
    districtWarnings: 8,
    highestWarningProject: {
      projectName: '党费收缴管理',
      warningCount: 8,
      affectedUnits: 12
    },
    problemAnalysis: {
      totalProblems: 45,
      rectificationRate: 87.5,
      criticalProblems: 3,
      majorProblems: 12,
      generalProblems: 30,
      inProgress: 18,
      completed: 25,
      overdue: 2
    },
    warningTrend: Array.from({ length: 7 }, (_, i) => {
      const date = new Date()
      date.setDate(date.getDate() - 6 + i)
      return {
        date: date.toISOString().split('T')[0],
        warningCount: Math.floor(Math.random() * 10),
        problemCount: Math.floor(Math.random() * 15),
        rectificationCount: Math.floor(Math.random() * 12)
      }
    })
  }
}

// 辅助函数
function getRandomLevel(): 'excellent' | 'good' | 'average' | 'poor' {
  const rand = Math.random()
  if (rand < 0.3) return 'excellent'
  if (rand < 0.6) return 'good'
  if (rand < 0.85) return 'average'
  return 'poor'
}

function getRandomName(): string {
  const surnames = ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴']
  const names = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋']
  return surnames[Math.floor(Math.random() * surnames.length)] + 
         names[Math.floor(Math.random() * names.length)]
}

function generatePhoneNumber(): string {
  return `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`
}

/**
 * 生成区县数据
 */
export function generateDistrictData(): DistrictData[] {
  return DISTRICTS.slice(0, 15).map((district, index) => {
    const longitude = 106.0 + Math.random() * 1.0
    const latitude = 29.0 + Math.random() * 1.0

    return {
      districtId: `district_${index + 1}`,
      districtName: district,
      totalUnits: 15 + Math.floor(Math.random() * 20),
      partyOrganizations: 8 + Math.floor(Math.random() * 12),
      overallScore: 75 + Math.random() * 20,
      modelAgencyCount: 2 + Math.floor(Math.random() * 8),
      modelAgencyRatio: 15 + Math.random() * 25,
      indexDistribution: {
        excellent: Math.floor(Math.random() * 8),
        good: Math.floor(Math.random() * 12),
        average: Math.floor(Math.random() * 8),
        poor: Math.floor(Math.random() * 3)
      },
      coordinates: {
        longitude,
        latitude
      },
      boundary: generateDistrictBoundary(longitude, latitude)
    }
  })
}

function generateDistrictBoundary(centerLng: number, centerLat: number) {
  const points = []
  const radius = 0.05 + Math.random() * 0.03

  for (let i = 0; i < 8; i++) {
    const angle = (i / 8) * 2 * Math.PI
    const lng = centerLng + radius * Math.cos(angle) * (0.8 + Math.random() * 0.4)
    const lat = centerLat + radius * Math.sin(angle) * (0.8 + Math.random() * 0.4)
    points.push({ longitude: lng, latitude: lat })
  }

  return points
}

// API模拟函数
/**
 * 模拟获取单位位置数据
 */
export async function fetchUnitLocations(params?: {
  district?: string
  unitType?: string
  level?: string
}): Promise<ApiResponse<UnitLocation[]>> {
  await delay(300) // 模拟网络延迟

  let data = generateUnitLocations()

  // 应用筛选条件
  if (params?.district) {
    data = data.filter(unit => unit.district === params.district)
  }
  if (params?.unitType) {
    data = data.filter(unit => unit.unitType === params.unitType)
  }
  if (params?.level) {
    data = data.filter(unit => unit.level === params.level)
  }

  return {
    code: 200,
    message: 'success',
    data,
    timestamp: Date.now()
  }
}

/**
 * 模拟获取项目统计数据
 */
export async function fetchProjectStats(_regionType?: 'municipal' | 'district'): Promise<ApiResponse<{
  municipal: ProjectData
  districts: ProjectData[]
}>> {
  await delay(200)

  const data = generateProjectStats()

  return {
    code: 200,
    message: 'success',
    data,
    timestamp: Date.now()
  }
}

/**
 * 模拟获取党建指数数据
 */
export async function fetchPartyBuildingData(): Promise<ApiResponse<PartyBuildingData>> {
  await delay(250)

  return {
    code: 200,
    message: 'success',
    data: generatePartyBuildingData(),
    timestamp: Date.now()
  }
}

/**
 * 模拟获取考核督查数据
 */
export async function fetchAssessmentData(): Promise<ApiResponse<{
  municipal: AssessmentData
  districts: AssessmentData[]
}>> {
  await delay(300)

  return {
    code: 200,
    message: 'success',
    data: generateAssessmentData(),
    timestamp: Date.now()
  }
}

/**
 * 模拟获取预警监控数据
 */
export async function fetchWarningData(): Promise<ApiResponse<WarningData>> {
  await delay(200)

  return {
    code: 200,
    message: 'success',
    data: generateWarningData(),
    timestamp: Date.now()
  }
}

/**
 * 模拟获取区县数据
 */
export async function fetchDistrictData(): Promise<ApiResponse<DistrictData[]>> {
  await delay(250)

  return {
    code: 200,
    message: 'success',
    data: generateDistrictData(),
    timestamp: Date.now()
  }
}

/**
 * 模拟获取单位详情
 */
export async function fetchUnitDetail(unitId: string): Promise<ApiResponse<UnitLocation | null>> {
  await delay(150)

  const units = generateUnitLocations()
  const unit = units.find(u => u.id === unitId)

  return {
    code: 200,
    message: unit ? 'success' : 'Unit not found',
    data: unit || null,
    timestamp: Date.now()
  }
}

/**
 * 模拟搜索单位
 */
export async function searchUnits(keyword: string): Promise<ApiResponse<UnitLocation[]>> {
  await delay(200)

  const units = generateUnitLocations()
  const filteredUnits = units.filter(unit =>
    unit.name.includes(keyword) ||
    unit.district.includes(keyword) ||
    unit.address.includes(keyword)
  )

  return {
    code: 200,
    message: 'success',
    data: filteredUnits,
    timestamp: Date.now()
  }
}

// 工具函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 导出所有数据生成函数
export const mockDataService = {
  // 数据生成
  generateUnitLocations,
  generateProjectStats,
  generatePartyBuildingData,
  generateAssessmentData,
  generateWarningData,
  generateDistrictData,

  // API模拟
  fetchUnitLocations,
  fetchProjectStats,
  fetchPartyBuildingData,
  fetchAssessmentData,
  fetchWarningData,
  fetchDistrictData,
  fetchUnitDetail,
  searchUnits
}
