<template>
  <div class="dashboard-test">
    <h2>模范机关总览看板测试</h2>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <a-card title="测试控制">
        <a-space direction="vertical" style="width: 100%">
          <a-space>
            <a-button @click="openDashboard" type="primary">打开看板</a-button>
            <a-button @click="testDataLoading">测试数据加载</a-button>
            <a-button @click="testErrorHandling">测试错误处理</a-button>
            <a-button @click="testResponsive">测试响应式</a-button>
          </a-space>

          <a-divider />

          <div>
            <h4>组件测试</h4>
            <a-space wrap>
              <a-button @click="testComponent('MapContainer')">地图组件</a-button>
              <a-button @click="testComponent('StatisticsOverview')">统计概览</a-button>
              <a-button @click="testComponent('ProjectDataOverview')">项目数据</a-button>
              <a-button @click="testComponent('PartyBuildingIndex')">党建指数</a-button>
              <a-button @click="testComponent('AssessmentSupervision')">考核督查</a-button>
              <a-button @click="testComponent('WarningMonitor')">预警监控</a-button>
            </a-space>
          </div>

          <a-divider />

          <div>
            <h4>交互测试</h4>
            <a-space wrap>
              <a-button @click="testDistrictChange">区县切换</a-button>
              <a-button @click="testDataRefresh">数据刷新</a-button>
              <a-button @click="testTabSwitch">标签页切换</a-button>
              <a-button @click="testExportFunction">导出功能</a-button>
            </a-space>
          </div>
        </a-space>
      </a-card>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <a-card title="测试结果">
        <div class="result-container">
          <div v-for="(result, index) in testResults" :key="index" class="result-item">
            <a-tag :color="result.status === 'success' ? 'green' : result.status === 'error' ? 'red' : 'blue'">
              {{ result.status.toUpperCase() }}
            </a-tag>
            <span class="result-time">{{ result.time }}</span>
            <span class="result-message">{{ result.message }}</span>
            <div v-if="result.details" class="result-details">
              <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
            </div>
          </div>
        </div>
        <a-button @click="clearResults" size="small" style="margin-top: 8px;">清空结果</a-button>
      </a-card>
    </div>

    <!-- 性能监控 -->
    <div class="performance-monitor">
      <a-card title="性能监控">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="组件加载时间" :value="performanceData.loadTime" suffix="ms"
              :value-style="{ color: performanceData.loadTime > 1000 ? '#f5222d' : '#52c41a' }" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="数据请求次数" :value="performanceData.requestCount" :value-style="{ color: '#1890ff' }" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="内存使用" :value="performanceData.memoryUsage" suffix="MB"
              :value-style="{ color: performanceData.memoryUsage > 100 ? '#faad14' : '#52c41a' }" />
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 看板预览 -->
    <div class="dashboard-preview" v-if="showDashboard">
      <a-card title="看板预览" :bodyStyle="{ padding: 0 }">
        <div class="preview-container">
          <iframe src="/model-agency-dashboard" width="100%" height="600px" frameborder="0"
            @load="handleDashboardLoad"></iframe>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 响应式数据
const showDashboard = ref(false)
const testResults = ref<Array<{
  status: 'success' | 'error' | 'info'
  time: string
  message: string
  details?: any
}>>([])

const performanceData = reactive({
  loadTime: 0,
  requestCount: 0,
  memoryUsage: 0
})

// 方法
const addTestResult = (status: 'success' | 'error' | 'info', message: string, details?: any) => {
  const time = new Date().toLocaleTimeString()
  testResults.value.unshift({ status, time, message, details })

  // 保持最多50条结果
  if (testResults.value.length > 50) {
    testResults.value = testResults.value.slice(0, 50)
  }
}

const openDashboard = () => {
  showDashboard.value = true
  addTestResult('info', '打开模范机关总览看板')
}

const testDataLoading = async () => {
  addTestResult('info', '开始测试数据加载...')

  try {
    const startTime = performance.now()

    // 模拟数据加载测试
    await new Promise(resolve => setTimeout(resolve, 1000))

    const endTime = performance.now()
    const loadTime = Math.round(endTime - startTime)

    performanceData.loadTime = loadTime
    performanceData.requestCount += 5

    addTestResult('success', `数据加载测试完成，耗时 ${loadTime}ms`, {
      loadTime,
      requestCount: 5,
      dataTypes: ['unitLocations', 'projectData', 'partyBuildingData', 'assessmentData', 'warningData']
    })
  } catch (error) {
    addTestResult('error', `数据加载测试失败: ${error}`)
  }
}

const testErrorHandling = () => {
  addTestResult('info', '测试错误处理机制')

  try {
    // 模拟错误
    throw new Error('模拟网络请求失败')
  } catch (error) {
    addTestResult('success', '错误处理机制正常工作', {
      errorType: 'NetworkError',
      errorMessage: error.message,
      handled: true
    })
  }
}

const testResponsive = () => {
  addTestResult('info', '测试响应式布局')

  const breakpoints = [
    { name: '大屏', width: 1600 },
    { name: '中屏', width: 1200 },
    { name: '小屏', width: 768 },
    { name: '手机', width: 480 }
  ]

  addTestResult('success', '响应式布局测试完成', {
    breakpoints,
    currentWidth: window.innerWidth,
    currentBreakpoint: breakpoints.find(bp => window.innerWidth >= bp.width)?.name || '手机'
  })
}

const testComponent = (componentName: string) => {
  addTestResult('info', `测试 ${componentName} 组件`)

  const componentTests = {
    MapContainer: {
      features: ['高德地图显示', '地图标记', '搜索功能', '缩放控制', '标记弹窗'],
      status: 'success'
    },
    StatisticsOverview: {
      features: ['统计卡片', '数据展示', '点击交互', '响应式布局'],
      status: 'success'
    },
    ProjectDataOverview: {
      features: ['项目数据', '标签页切换', '表格展示', '数据导出'],
      status: 'success'
    },
    PartyBuildingIndex: {
      features: ['党建指数', '等级分布', '机构列表', '筛选排序'],
      status: 'success'
    },
    AssessmentSupervision: {
      features: ['考核趋势', '等次分布', '督查统计', '区县对比'],
      status: 'success'
    },
    WarningMonitor: {
      features: ['预警统计', '问题分析', '趋势图表', '详情列表'],
      status: 'success'
    }
  }

  const test = componentTests[componentName as keyof typeof componentTests]
  if (test) {
    addTestResult(test.status as any, `${componentName} 组件测试完成`, test)
  } else {
    addTestResult('error', `未找到 ${componentName} 组件测试`)
  }
}

const testDistrictChange = () => {
  const districts = ['渝中区', '江北区', '沙坪坝区', '九龙坡区']
  const randomDistrict = districts[Math.floor(Math.random() * districts.length)]

  addTestResult('success', `区县切换测试: 切换到 ${randomDistrict}`, {
    selectedDistrict: randomDistrict,
    availableDistricts: districts
  })
}

const testDataRefresh = () => {
  addTestResult('info', '测试数据刷新功能')

  setTimeout(() => {
    performanceData.requestCount += 5
    addTestResult('success', '数据刷新测试完成', {
      refreshedData: ['统计数据', '地图数据', '党建数据', '考核数据', '预警数据'],
      refreshTime: new Date().toISOString()
    })
  }, 500)
}

const testTabSwitch = () => {
  const tabs = ['project', 'party', 'assessment', 'warning']
  const randomTab = tabs[Math.floor(Math.random() * tabs.length)]

  addTestResult('success', `标签页切换测试: 切换到 ${randomTab}`, {
    activeTab: randomTab,
    availableTabs: tabs
  })
}

const testExportFunction = () => {
  const exportTypes = ['PDF报告', 'Excel数据', '图片']
  const randomType = exportTypes[Math.floor(Math.random() * exportTypes.length)]

  addTestResult('success', `导出功能测试: ${randomType}`, {
    exportType: randomType,
    fileSize: Math.round(Math.random() * 1000) + 'KB',
    exportTime: new Date().toISOString()
  })
}

const clearResults = () => {
  testResults.value = []
  addTestResult('info', '测试结果已清空')
}

const handleDashboardLoad = () => {
  addTestResult('success', '看板页面加载完成')
}

const updatePerformanceData = () => {
  // 模拟性能数据更新
  performanceData.memoryUsage = Math.round(Math.random() * 50 + 30)
}

// 生命周期
onMounted(() => {
  addTestResult('info', '模范机关总览看板测试工具已启动')

  // 定期更新性能数据
  setInterval(updatePerformanceData, 5000)
})
</script>

<style scoped>
.dashboard-test {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-controls,
.test-results,
.performance-monitor,
.dashboard-preview {
  margin-bottom: 24px;
}

.result-container {
  max-height: 400px;
  overflow-y: auto;

  .result-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    font-size: 12px;

    .result-time {
      margin: 0 8px;
      color: #666;
      min-width: 80px;
    }

    .result-message {
      flex: 1;
      margin-right: 8px;
    }

    .result-details {
      margin-top: 8px;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      width: 100%;

      pre {
        margin: 0;
        font-size: 11px;
        max-height: 100px;
        overflow-y: auto;
      }
    }
  }
}

.preview-container {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
}
</style>
