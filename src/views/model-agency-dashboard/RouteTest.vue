<template>
  <div class="route-test">
    <a-card title="模范机关总览看板路由测试">
      <div class="test-section">
        <h3>路由配置验证</h3>
        <a-space direction="vertical" style="width: 100%">
          <a-alert
            :message="routeStatus.message"
            :type="routeStatus.type"
            show-icon
          />
          
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="路由路径">
              {{ routeInfo.path }}
            </a-descriptions-item>
            <a-descriptions-item label="路由名称">
              {{ routeInfo.name }}
            </a-descriptions-item>
            <a-descriptions-item label="页面标题">
              {{ routeInfo.title }}
            </a-descriptions-item>
            <a-descriptions-item label="组件路径">
              {{ routeInfo.component }}
            </a-descriptions-item>
          </a-descriptions>
          
          <a-space>
            <a-button @click="testRouteAccess" type="primary" :loading="testing">
              测试路由访问
            </a-button>
            <a-button @click="testMenuNavigation">
              测试菜单导航
            </a-button>
            <a-button @click="testBreadcrumb">
              测试面包屑
            </a-button>
            <a-button @click="goToDashboard" type="link">
              前往看板页面
            </a-button>
          </a-space>
        </a-space>
      </div>
      
      <a-divider />
      
      <div class="test-results">
        <h3>测试结果</h3>
        <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
          <a-tag :color="result.success ? 'green' : 'red'">
            {{ result.success ? '通过' : '失败' }}
          </a-tag>
          <span class="test-name">{{ result.testName }}</span>
          <span class="test-message">{{ result.message }}</span>
          <span class="test-time">{{ result.timestamp }}</span>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const testing = ref(false)
const routeStatus = ref({
  type: 'info' as 'success' | 'info' | 'warning' | 'error',
  message: '正在检查路由配置...'
})

const routeInfo = ref({
  path: '/model-agency-dashboard',
  name: 'ModelAgencyDashboard',
  title: '模范机关总览看板',
  component: '@/views/model-agency-dashboard/Index.vue'
})

const testResults = ref<Array<{
  testName: string
  success: boolean
  message: string
  timestamp: string
}>>([])

// 方法
const addTestResult = (testName: string, success: boolean, message: string) => {
  testResults.value.unshift({
    testName,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
}

const checkRouteConfiguration = () => {
  try {
    // 检查路由是否存在
    const routes = router.getRoutes()
    const targetRoute = routes.find(r => r.path === '/model-agency-dashboard')
    
    if (targetRoute) {
      routeStatus.value = {
        type: 'success',
        message: '路由配置正确，已在路由表中找到模范机关总览看板路由'
      }
      
      // 更新路由信息
      routeInfo.value = {
        path: targetRoute.path,
        name: targetRoute.name as string,
        title: targetRoute.meta?.title as string || '未设置',
        component: targetRoute.components?.default?.toString() || '动态导入'
      }
      
      addTestResult('路由配置检查', true, '路由已正确配置在路由表中')
    } else {
      routeStatus.value = {
        type: 'error',
        message: '路由配置错误，未在路由表中找到模范机关总览看板路由'
      }
      addTestResult('路由配置检查', false, '路由未在路由表中找到')
    }
  } catch (error) {
    routeStatus.value = {
      type: 'error',
      message: `路由配置检查失败: ${error}`
    }
    addTestResult('路由配置检查', false, `检查失败: ${error}`)
  }
}

const testRouteAccess = async () => {
  testing.value = true
  try {
    // 尝试导航到目标路由
    await router.push('/model-agency-dashboard')
    
    // 检查当前路由
    if (route.path === '/model-agency-dashboard') {
      addTestResult('路由访问测试', true, '成功导航到模范机关总览看板页面')
      message.success('路由访问测试通过')
    } else {
      addTestResult('路由访问测试', false, `导航失败，当前路径: ${route.path}`)
      message.error('路由访问测试失败')
    }
  } catch (error) {
    addTestResult('路由访问测试', false, `导航异常: ${error}`)
    message.error(`路由访问失败: ${error}`)
  } finally {
    testing.value = false
  }
}

const testMenuNavigation = () => {
  try {
    // 检查菜单配置
    const menuKey = 'model-agency-dashboard'
    const expectedPath = '/model-agency-dashboard'
    
    // 模拟菜单点击
    router.push(expectedPath).then(() => {
      addTestResult('菜单导航测试', true, '菜单导航功能正常')
      message.success('菜单导航测试通过')
    }).catch((error) => {
      addTestResult('菜单导航测试', false, `菜单导航失败: ${error}`)
      message.error('菜单导航测试失败')
    })
  } catch (error) {
    addTestResult('菜单导航测试', false, `菜单导航异常: ${error}`)
    message.error(`菜单导航失败: ${error}`)
  }
}

const testBreadcrumb = () => {
  try {
    // 检查面包屑配置
    const currentRoute = route
    const title = currentRoute.meta?.title
    
    if (title === '模范机关总览看板') {
      addTestResult('面包屑测试', true, '页面标题配置正确')
      message.success('面包屑测试通过')
    } else {
      addTestResult('面包屑测试', false, `页面标题不匹配，当前: ${title}`)
      message.warning('面包屑测试部分通过')
    }
  } catch (error) {
    addTestResult('面包屑测试', false, `面包屑测试异常: ${error}`)
    message.error(`面包屑测试失败: ${error}`)
  }
}

const goToDashboard = () => {
  router.push('/model-agency-dashboard')
}

// 生命周期
onMounted(() => {
  checkRouteConfiguration()
})
</script>

<style scoped>
.route-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 24px;
}

.test-results {
  .test-result-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    padding: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    
    .test-name {
      font-weight: 500;
      min-width: 120px;
    }
    
    .test-message {
      flex: 1;
      color: #666;
    }
    
    .test-time {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
