<template>
  <div class="health-check-results">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>结果管理</h2>
        <p>数据体检结果管理，支持异常结果查看、整改操作、验证复检等闭环处理功能</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="batchRemediate" :disabled="selectedRowKeys.length === 0">
            <template #icon><tool-outlined /></template>
            批量整改
          </a-button>
          <a-button @click="batchRecheck" :disabled="selectedRowKeys.length === 0">
            <template #icon><audit-outlined /></template>
            批量复检
          </a-button>
          <a-button @click="exportResults">
            <template #icon><export-outlined /></template>
            导出结果
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 结果统计卡片 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="异常总数"
              :value="getExceptionCountByStatus('all')"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待整改"
              :value="getExceptionCountByStatus(1)"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="整改中"
              :value="getExceptionCountByStatus(2)"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <tool-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已完成"
              :value="getExceptionCountByStatus(3)"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="异常类型" class="form-item-full">
                <a-select v-model:value="searchForm.exceptionType" placeholder="请选择异常类型" allow-clear>
                  <a-select-option v-for="item in ExceptionTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="严重程度" class="form-item-full">
                <a-select v-model:value="searchForm.severity" placeholder="请选择严重程度" allow-clear>
                  <a-select-option v-for="item in SeverityOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="处理状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择处理状态" allow-clear>
                  <a-select-option v-for="item in ExceptionStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="责任人" class="form-item-full">
                <a-input v-model:value="searchForm.assignee" placeholder="请输入责任人姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="16" :lg="12">
              <a-form-item label="发现时间" class="form-item-full">
                <a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="8" :lg="12">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 异常结果表格 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>异常结果</span>
            <span class="record-count">共 {{ exceptionList.length }} 条记录</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="filterStatus" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option v-for="item in ExceptionStatusOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-button size="small" @click="showBatchRemediateModal" :disabled="selectedRowKeys.length === 0">
              批量处理
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredExceptionList"
          :loading="exceptionLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1600 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'exceptionType'">
              <a-tag :color="getExceptionTypeColor(record.exceptionType)">
                {{ getExceptionTypeName(record.exceptionType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'severity'">
              <a-tag :color="getSeverityColor(record.exceptionLevel)">
                {{ getSeverityName(record.exceptionLevel) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'foundTime'">
              {{ formatTime(record.createTime) }}
            </template>
            <template v-else-if="column.key === 'lastUpdateTime'">
              <span v-if="record.fixTime">{{ formatTime(record.fixTime) }}</span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 1 || record.status === 2" 
                  type="link" 
                  size="small" 
                  @click="showRemediateModal(record)"
                >
                  整改
                </a-button>
                <a-button 
                  v-if="record.status === 2" 
                  type="link" 
                  size="small" 
                  @click="recheckException(record)"
                >
                  复检
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="viewRemediationHistory(record)"
                >
                  整改历史
                </a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="jumpToSource(record)"
                >
                  跳转源头
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 异常详情弹窗 -->
    <a-modal 
      title="异常详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-descriptions bordered :column="1" v-if="currentException">
        <a-descriptions-item label="异常类型">
          <a-tag :color="getExceptionTypeColor(currentException.exceptionType)">
            {{ getExceptionTypeName(currentException.exceptionType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="严重程度">
          <a-tag :color="getSeverityColor(currentException.exceptionLevel)">
            {{ getSeverityName(currentException.exceptionLevel) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="处理状态">
          <a-tag :color="getStatusColor(currentException.status)">
            {{ getStatusText(currentException.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="异常描述">
          <div class="exception-description">{{ currentException.exceptionDescription }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="影响对象">{{ currentException.affectedObject || '未知' }}</a-descriptions-item>
        <a-descriptions-item label="责任人">{{ currentException.fixOperator || '未分配' }}</a-descriptions-item>
        <a-descriptions-item label="发现时间">{{ formatTime(currentException.createTime) }}</a-descriptions-item>
        <a-descriptions-item label="最后更新">
          {{ currentException.fixTime ? formatTime(currentException.fixTime) : '无' }}
        </a-descriptions-item>
        <a-descriptions-item label="整改说明">
          <div class="remediation-notes">{{ currentException.solution || '暂无整改说明' }}</div>
        </a-descriptions-item>
        <a-descriptions-item label="验证结果">
          <div class="verification-result">{{ currentException.fixResult || '暂无验证结果' }}</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 整改操作弹窗 -->
    <a-modal 
      title="整改操作" 
      :visible="remediateVisible" 
      @cancel="remediateVisible = false" 
      @ok="submitRemediation"
      :confirm-loading="remediating"
      width="600px"
    >
      <div class="remediate-form" v-if="currentException">
        <a-descriptions bordered :column="1" size="small">
          <a-descriptions-item label="异常类型">
            {{ getExceptionTypeName(currentException.exceptionType) }}
          </a-descriptions-item>
          <a-descriptions-item label="异常描述">{{ currentException.exceptionDescription }}</a-descriptions-item>
          <a-descriptions-item label="发现时间">{{ formatTime(currentException.createTime) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>整改信息</a-divider>
        
        <a-form :model="remediationForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="责任人" required>
            <a-input v-model:value="remediationForm.assignee" placeholder="请输入责任人姓名" />
          </a-form-item>
          <a-form-item label="整改方案" required>
            <a-textarea 
              v-model:value="remediationForm.plan" 
              :rows="4" 
              placeholder="请输入详细的整改方案"
            />
          </a-form-item>
          <a-form-item label="预计完成时间">
            <a-date-picker v-model:value="remediationForm.expectedTime" style="width: 100%" />
          </a-form-item>
          <a-form-item label="整改说明">
            <a-textarea 
              v-model:value="remediationForm.notes" 
              :rows="3" 
              placeholder="请输入整改说明"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
// 导入新的结果管理API - 替换Store模式
import * as resultsApi from '@/api/results-management'
import type { HealthCheckException, HealthCheckExceptionSearchParams } from '@/types/health-check'
import {
  ExceptionTypeOptions,
  SeverityOptions,
  ExceptionStatusOptions,
  ExceptionTypeTextMap,
  SeverityTextMap,
  ExceptionStatusTextMap,
  ExceptionTypeColorMap,
  SeverityColorMap,
  ExceptionStatusColorMap
} from '@/types/health-check'
import {
  ToolOutlined,
  AuditOutlined,
  ExportOutlined,
  ReloadOutlined,
  WarningOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'

// 响应式数据 - 替换Store模式为本地状态管理
const exceptionList = ref<HealthCheckException[]>([])
const exceptionLoading = ref(false)
const statisticsLoading = ref(false)
const statistics = ref<any>(null)
const dataSource = ref<any>(null)

const searchForm = ref<HealthCheckExceptionSearchParams>({
  exceptionType: undefined,
  exceptionLevel: undefined,
  status: undefined,
  affectedObject: '',
  dateRange: undefined
})

const detailVisible = ref(false)
const remediateVisible = ref(false)
const remediating = ref(false)
const selectedRowKeys = ref<number[]>([])
const filterStatus = ref<number | undefined>(undefined)
const currentException = ref<HealthCheckException | null>(null)

// 整改表单数据
const remediationForm = ref({
  assignee: '',
  plan: '',
  expectedTime: null,
  notes: ''
})

// 计算属性
const filteredExceptionList = computed(() => {
  let filtered = [...exceptionList.value]

  if (filterStatus.value !== undefined) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '异常类型',
    key: 'exceptionType',
    width: 120,
    align: 'center'
  },
  {
    title: '异常描述',
    dataIndex: 'exceptionDescription',
    key: 'exceptionDescription',
    width: 200,
    ellipsis: true
  },
  {
    title: '严重程度',
    key: 'severity',
    width: 100,
    align: 'center'
  },
  {
    title: '影响对象',
    dataIndex: 'affectedObject',
    key: 'affectedObject',
    width: 120
  },
  {
    title: '责任人',
    dataIndex: 'fixOperator',
    key: 'fixOperator',
    width: 100
  },
  {
    title: '处理状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '发现时间',
    key: 'foundTime',
    width: 180
  },
  {
    title: '最后更新',
    key: 'lastUpdateTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: HealthCheckException) {
  return filteredExceptionList.value.findIndex(item => item.id === record.id) + 1
}

function getExceptionCountByStatus(status: number | string) {
  if (status === 'all') {
    return exceptionList.value.length
  }
  // 基于真实数据的统计
  return exceptionList.value.filter(ex => ex.status === status).length
}

function getExceptionTypeName(type: number) {
  return ExceptionTypeTextMap[type as keyof typeof ExceptionTypeTextMap] || '未知'
}

function getExceptionTypeColor(type: number) {
  return ExceptionTypeColorMap[type as keyof typeof ExceptionTypeColorMap] || 'default'
}

function getSeverityName(severity: number) {
  return SeverityTextMap[severity as keyof typeof SeverityTextMap] || '未知'
}

function getSeverityColor(severity: number) {
  return SeverityColorMap[severity as keyof typeof SeverityColorMap] || 'default'
}

function getStatusText(status: number) {
  return ExceptionStatusTextMap[status as keyof typeof ExceptionStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return ExceptionStatusColorMap[status as keyof typeof ExceptionStatusColorMap] || 'default'
}

function formatTime(timeStr: string) {
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 数据刷新方法 - 使用新的API
async function refreshData() {
  try {
    exceptionLoading.value = true
    statisticsLoading.value = true
    
    const [exceptionResponse, statisticsResponse] = await Promise.all([
      resultsApi.fetchExceptionList(searchForm.value),
      resultsApi.fetchExceptionStatistics()
    ])
    
    // 更新异常列表数据
    exceptionList.value = exceptionResponse.data.data
    dataSource.value = exceptionResponse.dataSource
    
    // 更新统计数据
    statistics.value = statisticsResponse.data
    
    // 显示数据来源信息
    if (!exceptionResponse.dataSource.isFromAPI) {
      message.warning('使用静态数据，Mock API连接失败: ' + exceptionResponse.dataSource.error)
    } else {
      message.success('数据刷新成功')
    }
    
    console.log('🔄 数据刷新完成:', {
      异常数量: exceptionList.value.length,
      数据来源: exceptionResponse.dataSource,
      统计数据: statistics.value
    })
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('数据刷新失败')
  } finally {
    exceptionLoading.value = false
    statisticsLoading.value = false
  }
}

function showDetail(record: HealthCheckException) {
  currentException.value = record
  detailVisible.value = true
}

function showRemediateModal(record: HealthCheckException) {
  currentException.value = record
  remediationForm.value = {
    assignee: record.fixOperator || '',
    plan: '',
    expectedTime: null,
    notes: ''
  }
  remediateVisible.value = true
}

// 整改提交方法 - 使用新的API
async function submitRemediation() {
  try {
    remediating.value = true

    if (!remediationForm.value.assignee.trim()) {
      message.error('请输入责任人')
      return
    }

    if (!remediationForm.value.plan.trim()) {
      message.error('请输入整改方案')
      return
    }

    if (!currentException.value?.id) {
      message.error('未选择异常项')
      return
    }

    // 调用单项整改API
    const response = await resultsApi.remediateException(currentException.value.id, {
      description: `整改负责人: ${remediationForm.value.assignee}`,
      solution: remediationForm.value.plan,
      estimatedTime: remediationForm.value.expectedTime?.format?.('YYYY-MM-DD') || '待定',
      attachments: []
    })

    if (response.data.success) {
      message.success('整改信息提交成功')
      remediateVisible.value = false
      
      // 如果使用静态数据，显示警告信息
      if (!response.dataSource.isFromAPI) {
        message.warning('使用静态模拟，Mock API: ' + response.dataSource.error)
      }
      
      await refreshData()
    } else {
      message.error('整改提交失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('整改提交异常:', error)
    message.error('提交失败，请重试')
  } finally {
    remediating.value = false
  }
}

// 复检方法 - 使用新的API
async function recheckException(record: HealthCheckException) {
  try {
    console.log('🚀 启动单项复检:', record.id)
    
    const response = await resultsApi.recheckException(record.id)
    
    message.success(`复检已启动，任务ID: ${response.data.taskId}，预计${response.data.estimatedDuration}秒完成`)
    
    if (!response.dataSource.isFromAPI) {
      message.warning('使用静态模拟，Mock API: ' + response.dataSource.error)
    }
    
    await refreshData()
  } catch (error) {
    console.error('复检失败:', error)
    message.error('复检失败，请重试')
  }
}

function viewRemediationHistory(record: HealthCheckException) {
  message.info(`查看 ${record.exceptionDescription} 的整改历史`)
  // 这里可以跳转到整改历史页面或打开历史弹窗
}

function jumpToSource(record: HealthCheckException) {
  message.info(`跳转到 ${record.affectedObject} 的数据源头`)
  // 这里可以实现跳转到问题源头的功能
}

// 表格选择处理
function onSelectChange(selectedKeys: number[]) {
  selectedRowKeys.value = selectedKeys
}

// 批量操作 - 使用新的API
async function batchRemediate() {
  try {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择需要整改的异常项')
      return
    }
    
    console.log('🚀 启动批量整改:', selectedRowKeys.value)
    
    const response = await resultsApi.batchRemediateExceptions(selectedRowKeys.value, {
      description: '批量整改操作',
      estimatedTime: '3天',
      priority: 2
    })
    
    message.success(`批量整改启动成功，批次ID: ${response.data.batchId}，成功处理 ${response.data.successCount} 项`)
    
    if (!response.dataSource.isFromAPI) {
      message.warning('使用静态模拟，Mock API: ' + response.dataSource.error)
    }
    
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    console.error('批量整改失败:', error)
    message.error('批量整改失败')
  }
}

async function batchRecheck() {
  try {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择需要复检的异常项')
      return
    }
    
    console.log('🚀 启动批量复检:', selectedRowKeys.value)
    
    const response = await resultsApi.batchRecheckExceptions(selectedRowKeys.value, {
      checkTypes: [1, 2, 3, 4],
      scope: 'targeted',
      priority: 1
    })
    
    message.success(`批量复检启动成功，任务ID: ${response.data.taskId}，处理 ${response.data.successCount} 项，预计${response.data.estimatedDuration}秒完成`)
    
    if (!response.dataSource.isFromAPI) {
      message.warning('使用静态模拟，Mock API: ' + response.dataSource.error)
    }
    
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    console.error('批量复检失败:', error)
    message.error('批量复检失败')
  }
}

function showBatchRemediateModal() {
  message.info('批量处理弹窗功能待开发')
}

// 导出结果 - 使用新的API
async function exportResults() {
  try {
    console.log('🚀 启动结果导出')
    
    const response = await resultsApi.exportExceptionResults({
      format: 'excel',
      filters: {
        exceptionTypes: searchForm.value.exceptionType ? [searchForm.value.exceptionType] : undefined,
        severityLevels: searchForm.value.exceptionLevel ? [searchForm.value.exceptionLevel] : undefined,
        statusList: searchForm.value.status ? [searchForm.value.status] : undefined,
        includeFixedItems: true
      },
      exportConfig: {
        includeCharts: true,
        includeSummary: true
      }
    })
    
    message.success(`导出成功！文件: ${response.data.filename}`)
    
    if (!response.dataSource.isFromAPI) {
      message.warning('使用静态模拟，Mock API: ' + response.dataSource.error)
    } else {
      // 在新窗口打开下载链接
      window.open(response.data.downloadUrl, '_blank')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

// 搜索和重置 - 使用新的API
async function handleSearch() {
  try {
    console.log('🔍 执行搜索:', searchForm.value)
    
    exceptionLoading.value = true
    const response = await resultsApi.fetchExceptionList(searchForm.value)
    
    exceptionList.value = response.data.data
    dataSource.value = response.dataSource
    
    message.success(`搜索完成，找到 ${response.data.data.length} 条结果`)
    
    if (!response.dataSource.isFromAPI) {
      message.warning('使用静态数据，Mock API: ' + response.dataSource.error)
    }
  } catch (error) {
    console.error('搜索失败:', error)
    message.error('搜索失败')
  } finally {
    exceptionLoading.value = false
  }
}

function resetSearch() {
  searchForm.value = {
    exceptionType: undefined,
    exceptionLevel: undefined,
    status: undefined,
    affectedObject: '',
    dateRange: undefined
  }
  // 重置后重新加载数据
  refreshData()
}

// 生命周期 - 使用新的API初始化
onMounted(async () => {
  try {
    console.log('🚀 结果管理页面初始化 - 使用新的API架构')
    
    // 初始化页面数据 - 替换Store调用为直接API调用
    await refreshData()
  } catch (error) {
    console.error('页面初始化失败:', error)
    message.error('页面数据加载失败')
  }
})
</script>

<style lang="scss" scoped>
.health-check-results {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .exception-description,
  .remediation-notes,
  .verification-result {
    padding: 12px;
    background: #fafafa;
    border-radius: 4px;
    line-height: 1.6;
    color: #333;
    max-height: 200px;
    overflow-y: auto;
  }

  .remediate-form {
    .ant-descriptions {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .health-check-results {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
