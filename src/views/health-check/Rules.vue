<template>
  <div class="health-check-config">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>体检规则配置</h2>
        <p>配置和管理各类数据体检规则，确保体检标准的准确性和完整性</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            新建规则
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="exportRules">
            <template #icon><export-outlined /></template>
            导出规则
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 规则类型标签页 -->
    <div class="config-tabs-section">
      <a-card>
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <a-tab-pane key="1" tab="党组（党委）设置">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="党组（党委）设置体检规则" 
                  description="配置党组织设置的完整性、规范性检查规则，包括组织架构、人员配置、职责分工等方面的要求。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="党务干部任免">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="党务干部任免体检规则" 
                  description="配置党务干部任免程序的规范性检查规则，包括任免程序、审批流程、档案管理等方面的要求。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="3" tab="任务体检">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="任务体检规则" 
                  description="配置任务执行情况的检查规则，包括任务完成度、执行质量、时效性等方面的评估标准。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="4" tab="用户信息完整">
            <div class="tab-content">
              <div class="tab-description">
                <a-alert 
                  message="用户信息完整性体检规则" 
                  description="配置用户信息完整性检查规则，包括基本信息、联系方式、身份验证等方面的完整性要求。"
                  type="info" 
                  show-icon 
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="规则名称" class="form-item-full">
                <a-input v-model:value="searchForm.ruleName" placeholder="请输入规则名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="启用状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">已启用</a-select-option>
                  <a-select-option :value="false">已禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="优先级" class="form-item-full">
                <a-select v-model:value="searchForm.priority" placeholder="请选择优先级" allow-clear>
                  <a-select-option :value="1">高</a-select-option>
                  <a-select-option :value="2">中</a-select-option>
                  <a-select-option :value="3">低</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 规则列表区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>{{ getTabTitle() }}规则列表</span>
            <span class="record-count">共 {{ filteredRuleList.length }} 条规则</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredRuleList"
          :loading="ruleLoading.value"
          :pagination="paginationConfig"
          :scroll="{ x: 1200 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'isEnabled'">
              <a-switch 
                v-model:checked="record.isEnabled" 
                @change="toggleRuleStatus(record)"
                :loading="record.updating"
              />
            </template>
            <template v-else-if="column.key === 'priority'">
              <a-tag :color="getPriorityColor(record.priority)">
                {{ getPriorityText(record.priority) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'ruleContent'">
              <a-button type="link" size="small" @click="previewRule(record)">
                预览规则
              </a-button>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button type="link" size="small" @click="editRule(record)">编辑</a-button>
                <a-button type="link" size="small" @click="copyRule(record)">复制</a-button>
                <a-popconfirm
                  title="确定要删除这条规则吗？"
                  @confirm="deleteRule(record)"
                  ok-text="确定"
                  cancel-text="取消"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 规则表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-form-item label="规则名称" name="ruleName">
          <a-input v-model:value="form.ruleName" placeholder="请输入规则名称" />
        </a-form-item>
        <a-form-item label="规则类型" name="ruleType">
          <a-select v-model:value="form.ruleType" placeholder="请选择规则类型" :disabled="formMode === 'edit'">
            <a-select-option v-for="item in HealthCheckTypeOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" name="priority">
          <a-select v-model:value="form.priority" placeholder="请选择优先级">
            <a-select-option :value="1">高</a-select-option>
            <a-select-option :value="2">中</a-select-option>
            <a-select-option :value="3">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="启用状态" name="isEnabled">
          <a-switch v-model:checked="form.isEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.isEnabled ? '已启用' : '已禁用' }}
          </span>
        </a-form-item>
        <a-form-item label="规则内容" name="ruleContent">
          <a-textarea 
            v-model:value="form.ruleContent" 
            :rows="6" 
            placeholder="请输入规则内容（JSON格式）"
          />
          <div class="rule-content-help">
            <a-typography-text type="secondary">
              规则内容应为有效的JSON格式，例如：{"minValue": 1, "maxValue": 100, "required": true}
            </a-typography-text>
          </div>
        </a-form-item>
        <a-form-item label="规则说明" name="description">
          <a-textarea 
            v-model:value="form.description" 
            :rows="3" 
            placeholder="请输入规则说明"
          />
        </a-form-item>
        <a-form-item :wrapper-col="{ offset: 4, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button @click="validateRuleContent">验证规则</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 规则详情弹窗 -->
    <a-modal 
      title="规则详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="700px"
    >
      <a-descriptions bordered :column="1" v-if="currentRule">
        <a-descriptions-item label="规则名称">{{ currentRule.ruleName }}</a-descriptions-item>
        <a-descriptions-item label="规则类型">
          <a-tag color="blue">{{ getCheckTypeName(currentRule.ruleType) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="getPriorityColor(currentRule.priority)">
            {{ getPriorityText(currentRule.priority) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="启用状态">
          <a-tag :color="currentRule.isEnabled ? 'success' : 'default'">
            {{ currentRule.isEnabled ? '已启用' : '已禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentRule.createTime }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ currentRule.updateTime }}</a-descriptions-item>
        <a-descriptions-item label="规则内容">
          <pre class="rule-content-preview">{{ formatRuleContent(currentRule.ruleContent) }}</pre>
        </a-descriptions-item>
        <a-descriptions-item label="规则说明">
          {{ currentRule.description || '无' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 规则预览弹窗 -->
    <a-modal 
      title="规则内容预览" 
      :visible="previewVisible" 
      @cancel="previewVisible = false" 
      :footer="null" 
      width="600px"
    >
      <div class="rule-preview-content">
        <a-typography-title :level="4">规则结构</a-typography-title>
        <pre class="rule-json-preview">{{ previewContent }}</pre>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useHealthCheckStore } from '@/store/modules/health-check'
import * as healthCheckApi from '@/api/health-check'
import type { HealthCheckRule, HealthCheckRuleSearchParams } from '@/types/health-check'
import {
  HealthCheckTypeOptions,
  HealthCheckTypeTextMap
} from '@/types/health-check'
import {
  PlusOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

// 使用store
const healthCheckStore = useHealthCheckStore()

// 响应式数据
const activeTab = ref('1')
const searchForm = ref<HealthCheckRuleSearchParams>({
  ruleName: '',
  isEnabled: undefined,
  priority: undefined
})

const formModalVisible = ref(false)
const detailVisible = ref(false)
const previewVisible = ref(false)
const submitting = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentRule = ref<HealthCheckRule | null>(null)
const previewContent = ref('')

// 表单数据
const form = ref<Partial<HealthCheckRule>>({
  ruleName: '',
  ruleType: 1,
  ruleContent: '',
  isEnabled: true,
  priority: 2,
  description: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '规则名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ruleType: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  ruleContent: [
    { required: true, message: '请输入规则内容', trigger: 'blur' },
    { validator: validateJSON, trigger: 'blur' }
  ]
}

// 计算属性 - 保持响应式特性
const ruleList = computed(() => healthCheckStore.ruleList)
const ruleLoading = computed(() => healthCheckStore.ruleLoading)

const filteredRuleList = computed(() => {
  let filtered = ruleList.value.filter(rule => rule.ruleType === parseInt(activeTab.value))

  if (searchForm.value.ruleName) {
    filtered = filtered.filter(rule =>
      rule.ruleName.includes(searchForm.value.ruleName!)
    )
  }

  if (searchForm.value.isEnabled !== undefined) {
    filtered = filtered.filter(rule =>
      rule.isEnabled === searchForm.value.isEnabled
    )
  }

  if (searchForm.value.priority) {
    filtered = filtered.filter(rule =>
      rule.priority === searchForm.value.priority
    )
  }

  return filtered
})

const modalTitle = computed(() => {
  return formMode.value === 'create' ? '新建规则' : '编辑规则'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '创建' : '更新'
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    key: 'ruleName',
    width: 200
  },
  {
    title: '优先级',
    key: 'priority',
    width: 100,
    align: 'center'
  },
  {
    title: '启用状态',
    key: 'isEnabled',
    width: 100,
    align: 'center'
  },
  {
    title: '规则内容',
    key: 'ruleContent',
    width: 120,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: HealthCheckRule) {
  return filteredRuleList.value.findIndex(item => item.id === record.id) + 1
}

function getCheckTypeName(type: number) {
  return HealthCheckTypeTextMap[type as keyof typeof HealthCheckTypeTextMap] || '未知'
}

function getPriorityText(priority: number) {
  const map = { 1: '高', 2: '中', 3: '低' }
  return map[priority as keyof typeof map] || '未知'
}

function getPriorityColor(priority: number) {
  const map = { 1: 'red', 2: 'orange', 3: 'green' }
  return map[priority as keyof typeof map] || 'default'
}

function getTabTitle() {
  const map = { '1': '党组（党委）设置', '2': '党务干部任免', '3': '任务体检', '4': '用户信息完整' }
  return map[activeTab.value as keyof typeof map] || ''
}

function formatRuleContent(content: string) {
  try {
    return JSON.stringify(JSON.parse(content), null, 2)
  } catch {
    return content
  }
}

// JSON验证器
function validateJSON(rule: any, value: string, callback: Function) {
  if (!value) {
    callback(new Error('请输入规则内容'))
    return
  }

  try {
    JSON.parse(value)
    callback()
  } catch (error) {
    callback(new Error('规则内容必须是有效的JSON格式'))
  }
}

async function handleTabChange(key: string) {
  activeTab.value = key
  await refreshData()
}

async function refreshData() {
  const searchParams = {
    ...searchForm.value,
    ruleType: parseInt(activeTab.value)
  }
  try {
    await healthCheckStore.fetchRuleList(searchParams)
  } catch (error) {
    console.error('fetchRuleList 调用失败:', error)
  }
}

function showCreateModal() {
  formMode.value = 'create'
  form.value = {
    ruleName: '',
    ruleType: parseInt(activeTab.value),
    ruleContent: '',
    isEnabled: true,
    priority: 2,
    description: ''
  }
  formModalVisible.value = true
}

function editRule(record: HealthCheckRule) {
  formMode.value = 'edit'
  form.value = { ...record }
  currentRule.value = record
  formModalVisible.value = true
}

function copyRule(record: HealthCheckRule) {
  formMode.value = 'create'
  form.value = {
    ...record,
    id: undefined,
    ruleName: `${record.ruleName}_副本`,
    createTime: undefined,
    updateTime: undefined
  }
  formModalVisible.value = true
}

function showDetail(record: HealthCheckRule) {
  currentRule.value = record
  detailVisible.value = true
}

function previewRule(record: HealthCheckRule) {
  previewContent.value = formatRuleContent(record.ruleContent)
  previewVisible.value = true
}

async function toggleRuleStatus(record: HealthCheckRule) {
  try {
    record.updating = true
    // 使用真实的规则更新API
    const success = await healthCheckStore.updateRule(record.id!, { isEnabled: record.isEnabled })
    if (success) {
      message.success(`规则已${record.isEnabled ? '启用' : '禁用'}`)
    } else {
      record.isEnabled = !record.isEnabled // 回滚状态
      message.error('状态更新失败')
    }
  } catch (error) {
    console.error('更新规则状态失败:', error)
    record.isEnabled = !record.isEnabled // 回滚状态
    message.error('状态更新失败')
  } finally {
    record.updating = false
  }
}

async function deleteRule(record: HealthCheckRule) {
  try {
    // 使用真实的规则删除API
    const success = await healthCheckApi.deleteHealthCheckRule(record.id!, record.ruleType)
    if (success) {
      message.success('规则删除成功')
      await refreshData()
    } else {
      message.error('删除失败，请重试')
    }
  } catch (error) {
    console.error('删除规则失败:', error)
    message.error('删除失败，请重试')
  }
}

async function onSubmit() {
  try {
    submitting.value = true

    // 验证表单
    await formRef.value.validate()

    // 使用真实的规则管理API
    let success = false
    if (formMode.value === 'create') {
      const ruleId = await healthCheckStore.createRule(form.value)
      success = !!ruleId
      if (success) {
      }
    } else {
      success = await healthCheckStore.updateRule(currentRule.value?.id!, form.value)
    }

    if (success) {
      message.success(`规则${formMode.value === 'create' ? '创建' : '更新'}成功`)
      formModalVisible.value = false
      await refreshData()
    } else {
      message.error(`规则${formMode.value === 'create' ? '创建' : '更新'}失败`)
    }
  } catch (error) {
    console.error('提交规则失败:', error)
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

function validateRuleContent() {
  try {
    const parsed = JSON.parse(form.value.ruleContent || '{}')
    message.success('规则内容格式正确')
  } catch (error) {
    message.error('规则内容格式错误，请检查JSON语法')
  }
}

async function handleSearch() {
  await refreshData()
}

function resetSearch() {
  searchForm.value = {
    ruleName: '',
    isEnabled: undefined,
    priority: undefined
  }
  refreshData()
}

function exportRules() {
  message.info('规则导出功能开发中...')
}

// 生命周期
onMounted(async () => {
  try {
    await refreshData()
  } catch (error) {
    console.error('加载规则数据失败:', error)
  }
})
</script>

<style lang="scss" scoped>
.health-check-config {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .config-tabs-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .tab-content {
      .tab-description {
        margin-bottom: 16px;

        .ant-alert {
          border-radius: 6px;
        }
      }
    }

    .ant-tabs {
      .ant-tabs-tab {
        font-weight: 500;

        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #1890ff;
            font-weight: 600;
          }
        }
      }

      .ant-tabs-content-holder {
        padding: 16px 0;
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }

  .rule-content-help {
    margin-top: 8px;
  }

  .rule-content-preview {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
  }

  .rule-preview-content {
    .rule-json-preview {
      background: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.6;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 400px;
      overflow-y: auto;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .health-check-config {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }

    .config-tabs-section {
      .ant-tabs {
        .ant-tabs-tab {
          font-size: 12px;
          padding: 8px 12px;
        }
      }
    }
  }
}
</style>
