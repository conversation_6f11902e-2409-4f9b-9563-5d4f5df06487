<template>
  <div class="health-check-statistics">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>体检统计分析</h2>
        <p>多维度统计数据体检结果，按异常类型、单位、时间等维度深度分析数据质量</p>
        <!-- 数据源状态提示 -->
        <div v-if="!dataSourceInfo.isFromAPI" class="data-source-warning">
          <a-alert
            message="当前显示离线数据"
            :description="`接口连接失败，正在显示静态数据。错误信息：${dataSourceInfo.error || '网络连接问题'}`"
            type="warning"
            show-icon
            closable
          />
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="generateReport">
            📊
            生成报告
          </a-button>
          <a-button @click="exportData">
            📤
            导出数据
          </a-button>
          <a-button @click="refreshData">
            🔄
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计维度选择器 -->
    <div class="dimension-selector-section">
      <a-card>
        <template #title>
          <div class="selector-title">
            <span>统计维度配置</span>
            <a-tag color="blue">支持多维度交叉分析</a-tag>
          </div>
        </template>
        <a-form :model="dimensionForm" class="dimension-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="主要维度" class="form-item-full">
                <a-select v-model:value="dimensionForm.primaryDimension" placeholder="请选择主要统计维度">
                  <a-select-option value="exceptionType">异常类型</a-select-option>
                  <a-select-option value="unit">单位组织</a-select-option>
                  <a-select-option value="severity">严重程度</a-select-option>
                  <a-select-option value="checkType">体检类型</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="次要维度" class="form-item-full">
                <a-select v-model:value="dimensionForm.secondaryDimension" placeholder="请选择次要统计维度" allow-clear>
                  <a-select-option value="timeRange">时间范围</a-select-option>
                  <a-select-option value="operator">操作人员</a-select-option>
                  <a-select-option value="status">处理状态</a-select-option>
                  <a-select-option value="source">数据源</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="时间范围" class="form-item-full">
                <a-range-picker v-model:value="dimensionForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full">
                <a-space>
                  <a-button type="primary" @click="applyDimensions">应用配置</a-button>
                  <a-button @click="resetDimensions">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 核心统计指标卡片 -->
    <div class="metrics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-card class="metric-card">
            <a-statistic
              title="异常总数"
              :value="statisticsData.totalExceptions"
              :value-style="{ color: '#ff4d4f' }"
              :precision="0"
            >
              <template #prefix>
                ⚠️
              </template>
              <template #suffix>
                <span class="trend-indicator" :class="{ 'trend-up': statisticsData.exceptionTrend > 0, 'trend-down': statisticsData.exceptionTrend < 0 }">
                  {{ statisticsData.exceptionTrend > 0 ? '↑' : '↓' }} {{ Math.abs(statisticsData.exceptionTrend) }}%
                </span>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-card class="metric-card">
            <a-statistic
              title="数据完整性"
              :value="statisticsData.completenessRate"
              :value-style="{ color: '#52c41a' }"
              :precision="1"
            >
              <template #prefix>
                ✅
              </template>
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-card class="metric-card">
            <a-statistic
              title="数据准确性"
              :value="statisticsData.accuracyRate"
              :value-style="{ color: '#1890ff' }"
              :precision="1"
            >
              <template #prefix>
                🎯
              </template>
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="8" :lg="6">
          <a-card class="metric-card">
            <a-statistic
              title="整改完成率"
              :value="statisticsData.remediationRate"
              :value-style="{ color: '#722ed1' }"
              :precision="1"
            >
              <template #prefix>
                🔧
              </template>
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要统计图表区域 -->
    <div class="charts-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="12">
          <a-card title="异常类型分布" class="chart-card">
            <div class="chart-container">
              <div class="exception-type-chart">
                <div v-for="(item, index) in statisticsData.exceptionTypeStats" :key="index" class="chart-item">
                  <div class="chart-item-header">
                    <div class="chart-item-label">{{ item.typeName }}</div>
                    <div class="chart-item-value">{{ item.count }}项</div>
                  </div>
                  <div class="chart-item-bar">
                    <div 
                      class="chart-item-progress" 
                      :style="{ 
                        width: (item.count / Math.max(...statisticsData.exceptionTypeStats.map(s => s.count), 1) * 100) + '%',
                        backgroundColor: getExceptionTypeColor(item.type)
                      }"
                    ></div>
                  </div>
                  <div class="chart-item-details">
                    <a-tag size="small" :color="getSeverityColor(item.avgSeverity)">
                      平均严重度: {{ item.avgSeverity }}
                    </a-tag>
                    <span class="percentage">{{ ((item.count / statisticsData.totalExceptions) * 100).toFixed(1) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :lg="12">
          <a-card title="单位异常统计" class="chart-card">
            <div class="chart-container">
              <div class="unit-stats-chart">
                <div v-for="(item, index) in statisticsData.unitStats" :key="index" class="unit-stat-item">
                  <div class="unit-info">
                    <div class="unit-name">{{ item.unitName }}</div>
                    <div class="unit-metrics">
                      <a-tag color="red" size="small">{{ item.exceptionCount }}异常</a-tag>
                      <a-tag color="green" size="small">{{ item.completenessRate }}%完整</a-tag>
                    </div>
                  </div>
                  <div class="unit-progress">
                    <a-progress 
                      :percent="item.overallScore" 
                      :stroke-color="getScoreColor(item.overallScore)"
                      :show-info="false"
                      size="small"
                    />
                  </div>
                  <div class="unit-score">{{ item.overallScore }}分</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 时间趋势分析 -->
    <div class="trend-section">
      <a-card title="异常趋势分析" class="trend-card">
        <template #extra>
          <a-radio-group v-model:value="trendPeriod" button-style="solid" size="small">
            <a-radio-button value="7d">近7天</a-radio-button>
            <a-radio-button value="30d">近30天</a-radio-button>
            <a-radio-button value="90d">近90天</a-radio-button>
          </a-radio-group>
        </template>
        <div class="trend-chart-container">
          <div class="trend-chart">
            <div v-for="(item, index) in trendData" :key="index" class="trend-point">
              <div class="trend-date">{{ formatTrendDate(item.date) }}</div>
              <div class="trend-bars">
                <div class="trend-bar-group">
                  <div 
                    class="trend-bar exceptions" 
                    :style="{ height: (item.exceptions / maxTrendValue * 80) + 'px' }"
                    :title="`异常数: ${item.exceptions}`"
                  ></div>
                  <div 
                    class="trend-bar checks" 
                    :style="{ height: (item.checks / maxTrendValue * 80) + 'px' }"
                    :title="`体检数: ${item.checks}`"
                  ></div>
                </div>
              </div>
              <div class="trend-values">
                <div class="trend-exceptions">{{ item.exceptions }}</div>
                <div class="trend-checks">{{ item.checks }}</div>
              </div>
            </div>
          </div>
          <div class="trend-legend">
            <div class="legend-item">
              <div class="legend-color exceptions"></div>
              <span>异常数量</span>
            </div>
            <div class="legend-item">
              <div class="legend-color checks"></div>
              <span>体检次数</span>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 详细统计表格 -->
    <div class="detailed-table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>{{ getDimensionTitle() }}详细统计</span>
            <span class="record-count">共 {{ detailedStats.length }} 条记录</span>
          </div>
        </template>
        <a-table
          :columns="getTableColumns()"
          :data-source="detailedStats"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1200 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'exceptionRate'">
              <div class="rate-display">
                <a-progress 
                  :percent="record.exceptionRate" 
                  :stroke-color="getRateColor(record.exceptionRate)"
                  size="small"
                />
              </div>
            </template>
            <template v-else-if="column.key === 'trend'">
              <span class="trend-indicator" :class="{ 'trend-up': record.trend > 0, 'trend-down': record.trend < 0 }">
                {{ record.trend > 0 ? '↑' : '↓' }} {{ Math.abs(record.trend) }}%
              </span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
                <a-button type="link" size="small" @click="drillDown(record)">下钻分析</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 钻取分析弹窗 -->
    <a-modal 
      title="钻取分析详情" 
      :visible="drillDownVisible" 
      @cancel="drillDownVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="drill-down-content" v-if="drillDownData">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="分析维度">{{ drillDownData.dimension }}</a-descriptions-item>
          <a-descriptions-item label="分析对象">{{ drillDownData.target }}</a-descriptions-item>
          <a-descriptions-item label="时间范围">{{ drillDownData.dateRange }}</a-descriptions-item>
          <a-descriptions-item label="数据样本">{{ drillDownData.sampleSize }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider>异常详细分解</a-divider>
        
        <div class="drill-down-breakdown">
          <div v-for="item in drillDownData.breakdown" :key="item.category" class="breakdown-item">
            <div class="breakdown-header">
              <span class="breakdown-title">{{ item.category }}</span>
              <a-tag :color="item.severity === 'high' ? 'red' : item.severity === 'medium' ? 'orange' : 'green'">
                {{ item.count }}项
              </a-tag>
            </div>
            <div class="breakdown-details">
              <ul>
                <li v-for="detail in item.details" :key="detail">{{ detail }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  getCoreMetrics,
  getDepartmentStatistics,
  getExceptionTypeStatistics,
  getTrendAnalysis,
  getRealTimeStatistics,
  applyStatisticsDimensions,
  resetStatisticsDimensions
} from '@/api/statistics-analysis'
// Temporarily using text instead of icons to avoid import issues
// import {
//   FileTextOutlined,
//   ExportOutlined,
//   ReloadOutlined,
//   WarningOutlined,
//   CheckCircleOutlined,
//   TargetOutlined,
//   ToolOutlined
// } from '@ant-design/icons-vue'

// 接口定义
interface DimensionForm {
  primaryDimension: string
  secondaryDimension?: string
  dateRange?: [string, string]
}

interface StatisticsData {
  totalExceptions: number
  exceptionTrend: number
  completenessRate: number
  accuracyRate: number
  remediationRate: number
  exceptionTypeStats: ExceptionTypeStat[]
  unitStats: UnitStat[]
}

interface ExceptionTypeStat {
  type: number
  typeName: string
  count: number
  avgSeverity: number
}

interface UnitStat {
  unitId: string
  unitName: string
  exceptionCount: number
  completenessRate: number
  overallScore: number
}

interface TrendDataPoint {
  date: string
  exceptions: number
  checks: number
}

interface DetailedStat {
  id: string
  name: string
  exceptionCount: number
  totalChecks: number
  exceptionRate: number
  trend: number
  lastCheck: string
}

interface DrillDownData {
  dimension: string
  target: string
  dateRange: string
  sampleSize: number
  breakdown: DrillDownBreakdown[]
}

interface DrillDownBreakdown {
  category: string
  count: number
  severity: 'high' | 'medium' | 'low'
  details: string[]
}

// 响应式数据
const loading = ref(false)
const drillDownVisible = ref(false)
const trendPeriod = ref('30d')
const dataSourceInfo = ref<{ isFromAPI: boolean; error?: string; timestamp?: string }>({ isFromAPI: true })

const dimensionForm = ref<DimensionForm>({
  primaryDimension: 'exceptionType',
  secondaryDimension: undefined,
  dateRange: undefined
})

// 统计数据
const statisticsData = ref<StatisticsData>({
  totalExceptions: 0,
  exceptionTrend: 0,
  completenessRate: 0,
  accuracyRate: 0,
  remediationRate: 0,
  exceptionTypeStats: [],
  unitStats: []
})

const trendData = ref<TrendDataPoint[]>([])

const detailedStats = ref<DetailedStat[]>([])

const drillDownData = ref<DrillDownData | null>(null)

// 计算属性
const maxTrendValue = computed(() => {
  const allValues = trendData.value.flatMap(item => [item.exceptions, item.checks])
  return Math.max(...allValues)
})

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 方法定义
function getRowIndex(record: DetailedStat) {
  return detailedStats.value.findIndex(item => item.id === record.id) + 1
}

function getExceptionTypeColor(type: number) {
  const colors = { 1: '#ff4d4f', 2: '#faad14', 3: '#722ed1', 4: '#13c2c2', 5: '#52c41a' }
  return colors[type] || '#d9d9d9'
}

function getSeverityColor(severity: number) {
  if (severity >= 4) return 'red'
  if (severity >= 3) return 'orange'
  return 'green'
}

function getScoreColor(score: number) {
  if (score >= 90) return '#52c41a'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

function getRateColor(rate: number) {
  if (rate <= 5) return '#52c41a'
  if (rate <= 10) return '#faad14'
  return '#ff4d4f'
}

function formatTrendDate(date: string) {
  return dayjs(date).format('MM-DD')
}

function getDimensionTitle() {
  const titles = {
    exceptionType: '异常类型',
    unit: '单位组织',
    severity: '严重程度',
    checkType: '体检类型'
  }
  return titles[dimensionForm.value.primaryDimension] || '统计'
}

function getTableColumns() {
  const baseColumns = [
    { title: '序号', key: 'index', width: 80, align: 'center' },
    { title: '名称', dataIndex: 'name', key: 'name', width: 200 },
    { title: '异常数量', dataIndex: 'exceptionCount', key: 'exceptionCount', width: 100, align: 'center' },
    { title: '检查总数', dataIndex: 'totalChecks', key: 'totalChecks', width: 100, align: 'center' },
    { title: '异常率', key: 'exceptionRate', width: 150 },
    { title: '趋势', key: 'trend', width: 100, align: 'center' },
    { title: '最近检查', dataIndex: 'lastCheck', key: 'lastCheck', width: 180 },
    { title: '操作', key: 'actions', width: 150, align: 'center', fixed: 'right' }
  ]
  return baseColumns
}

// 加载统计数据
async function loadStatisticsData() {
  loading.value = true
  let hasApiFailure = false
  let apiErrors: string[] = []
  
  try {
    // 并行加载数据体检相关数据
    const [
      coreMetricsResponse,
      departmentStatsResponse,
      exceptionTypeResponse,
      trendAnalysisResponse,
      realTimeDataResponse
    ] = await Promise.all([
      getCoreMetrics(),
      getDepartmentStatistics({ limit: 10 }),
      getExceptionTypeStatistics({ sortBy: 'count', order: 'desc' }),
      getTrendAnalysis({ type: 'daily', period: 7 }),
      getRealTimeStatistics()
    ])

    // 检查数据来源
    const responses = [coreMetricsResponse, departmentStatsResponse, exceptionTypeResponse, trendAnalysisResponse, realTimeDataResponse]
    responses.forEach(response => {
      if (!response.dataSource.isFromAPI) {
        hasApiFailure = true
        if (response.dataSource.error) {
          apiErrors.push(response.dataSource.error)
        }
      }
    })

    // 更新数据源信息
    dataSourceInfo.value = {
      isFromAPI: !hasApiFailure,
      error: apiErrors.length > 0 ? apiErrors[0] : undefined,
      timestamp: new Date().toISOString()
    }

    // 提取数据体检相关数据
    const coreMetrics = coreMetricsResponse.data
    const departmentStats = departmentStatsResponse.data
    const exceptionTypes = exceptionTypeResponse.data
    const trendAnalysis = trendAnalysisResponse.data
    const realTimeData = realTimeDataResponse.data

    // 更新统计数据
    statisticsData.value = {
      totalExceptions: coreMetrics.totalExceptions,
      exceptionTrend: -8.2, // 可以从trendAnalysis计算趋势
      completenessRate: coreMetrics.completenessRate,
      accuracyRate: coreMetrics.accuracyRate,
      remediationRate: coreMetrics.remediationRate,
      exceptionTypeStats: exceptionTypes.data.map(type => ({
        type: type.type,
        typeName: type.typeName,
        count: type.count,
        avgSeverity: type.avgSeverity
      })),
      unitStats: departmentStats.data.map(dept => ({
        unitId: dept.unitId,
        unitName: dept.unitName,
        exceptionCount: dept.exceptionCount,
        completenessRate: dept.completenessRate,
        overallScore: dept.overallScore
      }))
    }

    // 更新趋势数据 - 使用数据体检趋势数据
    if (trendAnalysis.data && trendAnalysis.data.length > 0) {
      trendData.value = trendAnalysis.data.map(item => ({
        date: item.date,
        exceptions: item.exceptions,
        checks: item.checks
      }))
    }

    // 更新详细统计 - 直接使用数据体检数据
    detailedStats.value = departmentStats.data.map((dept, index) => ({
      id: (index + 1).toString(),
      name: dept.unitName,
      exceptionCount: dept.exceptionCount,
      totalChecks: dept.totalChecks,
      exceptionRate: parseFloat(((dept.exceptionCount / dept.totalChecks) * 100).toFixed(1)),
      trend: dept.trend,
      lastCheck: dept.lastCheckTime
    }))

  } catch (error) {
    console.error('加载统计数据失败:', error)
    message.error('加载统计数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

async function refreshData() {
  await loadStatisticsData()
  message.success('统计数据刷新成功')
}

// 应用统计维度配置 - 调用真实Mock API
async function applyDimensions() {
  loading.value = true
  try {
    console.log('🚀 应用统计维度配置:', dimensionSelectors.value)

    const config = {
      primaryDimension: dimensionSelectors.value.primaryDimension || 'department',
      secondaryDimension: dimensionSelectors.value.secondaryDimension,
      timeRange: dimensionSelectors.value.dateRange ? '30d' : '7d',
      metricType: 'count',
      chartType: 'bar'
    }

    const response = await applyStatisticsDimensions(config)

    if (response.dataSource.isFromAPI) {
      message.success(`配置已应用：${response.data.message}，重新计算了 ${response.data.dataPoints} 个数据点`)
    } else {
      message.warning(`离线模式：${response.data.message}。${response.dataSource.error || ''}`)
    }

    // 重新加载统计数据以反映新配置
    await loadStatisticsData()

    console.log('✅ 维度配置应用成功:', response.data)
  } catch (error) {
    console.error('❌ 应用维度配置失败:', error)
    message.error('应用配置失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置统计维度配置 - 调用真实Mock API
async function resetDimensions() {
  try {
    console.log('🚀 重置统计维度配置')

    const response = await resetStatisticsDimensions()

    if (response.dataSource.isFromAPI) {
      message.success(`配置已重置：${response.data.message}`)
    } else {
      message.warning(`离线模式：${response.data.message}。${response.dataSource.error || ''}`)
    }

    // 使用API返回的默认配置更新本地表单
    const defaultConfig = response.data.defaultConfig
    dimensionSelectors.value = {
      primaryDimension: defaultConfig.primaryDimension,
      secondaryDimension: defaultConfig.secondaryDimension,
      dateRange: defaultConfig.timeRange === '30d' ? [dayjs().subtract(30, 'day'), dayjs()] : undefined
    }

    // 重新加载统计数据
    await loadStatisticsData()

    console.log('✅ 维度配置重置成功:', response.data)
  } catch (error) {
    console.error('❌ 重置维度配置失败:', error)
    message.error('重置配置失败，请重试')
  }
}

function generateReport() {
  message.success('正在生成统计报告，请稍候...')
}

function exportData() {
  message.success('统计数据导出成功')
}

function viewDetails(record: DetailedStat) {
  message.info(`查看 ${record.name} 的详细信息`)
}

function drillDown(record: DetailedStat) {
  drillDownData.value = {
    dimension: getDimensionTitle(),
    target: record.name,
    dateRange: dimensionForm.value.dateRange?.join(' 至 ') || '全部时间',
    sampleSize: record.totalChecks,
    breakdown: [
      {
        category: '数据完整性异常',
        count: Math.floor(record.exceptionCount * 0.4),
        severity: 'medium',
        details: [
          '必填字段缺失 5项',
          '关键信息不完整 3项',
          '格式不规范 2项'
        ]
      },
      {
        category: '数据准确性异常',
        count: Math.floor(record.exceptionCount * 0.3),
        severity: 'high',
        details: [
          '数值范围异常 4项',
          '逻辑关系错误 2项'
        ]
      },
      {
        category: '数据一致性异常',
        count: Math.floor(record.exceptionCount * 0.3),
        severity: 'low',
        details: [
          '关联数据不一致 3项',
          '状态同步异常 1项'
        ]
      }
    ]
  }
  drillDownVisible.value = true
}

// 生命周期
onMounted(() => {
  // 初始化加载数据
  loadStatisticsData()
})
</script>

<style lang="scss" scoped>
.health-check-statistics {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
      
      .data-source-warning {
        margin-top: 16px;
      }
    }
  }

  .dimension-selector-section {
    margin-bottom: 24px;

    .selector-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .dimension-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }
    }
  }

  .metrics-section {
    margin-bottom: 24px;

    .metric-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .trend-indicator {
        font-size: 12px;
        margin-left: 8px;

        &.trend-up {
          color: #ff4d4f;
        }

        &.trend-down {
          color: #52c41a;
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-container {
        min-height: 200px;
        padding: 16px;
      }
    }

    .exception-type-chart {
      .chart-item {
        margin-bottom: 16px;

        .chart-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .chart-item-label {
            font-weight: 500;
            color: #333;
          }

          .chart-item-value {
            font-size: 14px;
            color: #666;
          }
        }

        .chart-item-bar {
          height: 8px;
          background: #f0f0f0;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 8px;

          .chart-item-progress {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
          }
        }

        .chart-item-details {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .percentage {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }

    .unit-stats-chart {
      .unit-stat-item {
        display: flex;
        align-items: center;
        padding: 12px;
        margin-bottom: 12px;
        background: #fafafa;
        border-radius: 6px;

        .unit-info {
          flex: 0 0 120px;

          .unit-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .unit-metrics {
            display: flex;
            gap: 4px;
          }
        }

        .unit-progress {
          flex: 1;
          margin: 0 16px;
        }

        .unit-score {
          flex: 0 0 60px;
          text-align: right;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .trend-section {
    margin-bottom: 24px;

    .trend-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .trend-chart-container {
      padding: 16px;

      .trend-chart {
        display: flex;
        justify-content: space-between;
        align-items: end;
        height: 200px;
        margin-bottom: 16px;

        .trend-point {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 4px;

          .trend-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
          }

          .trend-bars {
            height: 120px;
            display: flex;
            align-items: end;
            margin-bottom: 8px;

            .trend-bar-group {
              display: flex;
              gap: 2px;
              align-items: end;

              .trend-bar {
                width: 12px;
                border-radius: 2px 2px 0 0;
                transition: height 0.3s ease;

                &.exceptions {
                  background: #ff4d4f;
                }

                &.checks {
                  background: #52c41a;
                }
              }
            }
          }

          .trend-values {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;

            .trend-exceptions,
            .trend-checks {
              font-size: 11px;
              color: #999;
            }
          }
        }
      }

      .trend-legend {
        display: flex;
        justify-content: center;
        gap: 24px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;
          color: #666;

          .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;

            &.exceptions {
              background: #ff4d4f;
            }

            &.checks {
              background: #52c41a;
            }
          }
        }
      }
    }
  }

  .detailed-table-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .rate-display {
      width: 120px;
    }

    .trend-indicator {
      font-size: 12px;
      font-weight: 500;

      &.trend-up {
        color: #ff4d4f;
      }

      &.trend-down {
        color: #52c41a;
      }
    }
  }

  .drill-down-content {
    .drill-down-breakdown {
      .breakdown-item {
        margin-bottom: 16px;
        padding: 12px;
        background: #fafafa;
        border-radius: 6px;

        .breakdown-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .breakdown-title {
            font-weight: 500;
            color: #333;
          }
        }

        .breakdown-details {
          ul {
            margin: 0;
            padding-left: 16px;
            color: #666;

            li {
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .health-check-statistics {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .trend-chart {
      .trend-point {
        margin: 0 2px;

        .trend-bars {
          .trend-bar-group {
            .trend-bar {
              width: 8px;
            }
          }
        }
      }
    }
  }
}
</style>