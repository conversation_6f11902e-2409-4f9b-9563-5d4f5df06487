<template>
  <div class="health-check-dashboard">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>数据体检仪表板</h2>
        <p>实时监控数据质量，及时发现和处理异常</p>
        <!-- 数据源状态提示 -->
        <div v-if="!dataSourceInfo.isFromAPI" class="data-source-warning">
          <a-alert
            message="当前显示离线数据"
            :description="`接口连接失败，正在显示静态数据。错误信息：${dataSourceInfo.error || '网络连接问题'}`"
            type="warning"
            show-icon
            closable
          />
        </div>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="refreshData" :loading="refreshing">
            <template #icon><reload-outlined v-if="!refreshing" /></template>
            {{ refreshing ? '刷新中...' : '刷新数据' }}
          </a-button>
          <a-button @click="showExecuteModal" :disabled="executing">
            <template #icon><play-circle-outlined /></template>
            执行体检
          </a-button>
          <a-button @click="exportReport" :loading="exporting">
            <template #icon><export-outlined v-if="!exporting" /></template>
            {{ exporting ? '导出中...' : '导出报告' }}
          </a-button>
        </a-space>
      </div>
    </div>


    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总体检次数"
              :value="healthCheckStore.statistics?.totalChecks || 0"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <audit-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="已完成"
              :value="healthCheckStore.statistics?.completedChecks || 0"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="异常项总数"
              :value="healthCheckStore.statistics?.totalExceptions || 0"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="高级别异常"
              :value="healthCheckStore.statistics?.highLevelExceptions || 0"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <exclamation-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="12">
          <a-card title="体检类型分布" class="chart-card">
            <div class="chart-container">
              <!-- 空状态提示 -->
              <a-empty v-if="!hasStatisticsData && !initialLoading" description="暂无体检类型统计数据" />
              <!-- 正常数据展示 -->
              <div v-else class="chart-placeholder">
                <div v-for="item in healthCheckStore.statistics?.checkTypeStats || []" :key="item.type" class="type-stat-item">
                  <div class="type-info">
                    <span class="type-name">{{ getCheckTypeName(item.type) }}</span>
                    <span class="type-count">{{ item.count }}次</span>
                  </div>
                  <div class="type-progress">
                    <a-progress 
                      :percent="getTypePercentage(item.count)" 
                      :stroke-color="getTypeColor(item.type)"
                      :show-info="false"
                    />
                  </div>
                  <div class="exception-info">
                    <a-tag v-if="item.exceptionCount > 0" color="orange">
                      {{ item.exceptionCount }}个异常
                    </a-tag>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :lg="12">
          <a-card title="异常趋势" class="chart-card">
            <div class="chart-container">
              <!-- 空状态提示 -->
              <a-empty v-if="!hasStatisticsData && !initialLoading" description="暂无异常趋势数据" />
              <!-- 正常数据展示 -->
              <div v-else class="trend-chart">
                <div v-for="item in healthCheckStore.statistics?.exceptionTrend || []" :key="item.date" class="trend-item">
                  <div class="trend-date">{{ formatDate(item.date) }}</div>
                  <div class="trend-bar">
                    <div 
                      class="trend-bar-fill" 
                      :style="{ width: getTrendPercentage(item.count) + '%' }"
                    ></div>
                  </div>
                  <div class="trend-count">{{ item.count }}</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="体检名称" class="form-item-full">
                <a-input v-model:value="searchForm.checkName" placeholder="请输入体检名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="体检类型" class="form-item-full">
                <a-select v-model:value="searchForm.checkType" placeholder="请选择体检类型" allow-clear>
                  <a-select-option v-for="item in HealthCheckTypeOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="状态" class="form-item-full">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
                  <a-select-option v-for="item in HealthCheckStatusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 数据为空时的全局提示 -->
    <div v-if="isDataEmpty" class="empty-state-section">
      <a-card>
        <a-empty description="暂无数据">
          <template #image>
            <audit-outlined style="font-size: 64px; color: #d9d9d9;" />
          </template>
          <template #description>
            <span>暂无体检数据，请尝试执行体检任务或检查数据源配置</span>
          </template>
          <a-button type="primary" @click="refreshData" :loading="refreshing">
            重新加载
          </a-button>
        </a-empty>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div v-else class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>体检记录</span>
            <span class="record-count">共 {{ healthCheckStore.healthCheckList?.length || 0 }} 条记录</span>
          </div>
        </template>
        <a-table
          :columns="columns"
          :data-source="healthCheckStore.healthCheckList || []"
          :loading="healthCheckStore.loading || initialLoading"
          :pagination="paginationConfig"
          :scroll="{ x: 1200 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'checkType'">
              <a-tag :color="getCheckTypeColor(record.checkType)">
                {{ getCheckTypeName(record.checkType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'exceptionCount'">
              <a-badge 
                :count="record.exceptionCount || 0" 
                :number-style="{ backgroundColor: record.exceptionCount > 0 ? '#ff4d4f' : '#52c41a' }"
              />
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">查看</a-button>
                <a-button 
                  v-if="record.status === 3 && record.exceptionCount > 0" 
                  type="link" 
                  size="small" 
                  @click="viewExceptions(record)"
                >
                  查看异常
                </a-button>
                <a-button 
                  v-if="record.status === 1" 
                  type="link" 
                  size="small" 
                  @click="executeCheck(record)"
                >
                  执行
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 详情查看弹窗 -->
    <a-modal 
      title="体检详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="700px"
    >
      <a-descriptions bordered :column="1" v-if="currentRecord">
        <a-descriptions-item label="体检名称">{{ currentRecord.checkName }}</a-descriptions-item>
        <a-descriptions-item label="体检类型">
          <a-tag :color="getCheckTypeColor(currentRecord.checkType)">
            {{ getCheckTypeName(currentRecord.checkType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="检查对象">{{ currentRecord.targetObject }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(currentRecord.status)">
            {{ getStatusText(currentRecord.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentRecord.createTime }}</a-descriptions-item>
        <a-descriptions-item label="最后检查时间">
          {{ currentRecord.lastCheckTime || '未执行' }}
        </a-descriptions-item>
        <a-descriptions-item label="异常数量">
          <a-badge 
            :count="currentRecord.exceptionCount || 0" 
            :number-style="{ backgroundColor: currentRecord.exceptionCount > 0 ? '#ff4d4f' : '#52c41a' }"
          />
        </a-descriptions-item>
        <a-descriptions-item label="操作人">{{ currentRecord.operator || '系统' }}</a-descriptions-item>
        <a-descriptions-item label="检查结果">
          {{ currentRecord.checkResult || '暂无结果' }}
        </a-descriptions-item>
        <a-descriptions-item label="说明">{{ currentRecord.description || '无' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 执行体检弹窗 -->
    <a-modal 
      title="执行体检" 
      :visible="executeVisible" 
      @cancel="executeVisible = false" 
      @ok="confirmExecute"
      :confirm-loading="executing"
    >
      <p>确定要执行体检任务吗？</p>
      <a-alert 
        message="执行提示" 
        description="体检任务将在后台执行，请耐心等待执行完成。" 
        type="info" 
        show-icon 
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { useHealthCheckStore } from '@/store/modules/health-check'
import * as healthCheckApi from '@/api/health-check'
import type { HealthCheckRecord, HealthCheckSearchParams } from '@/types/health-check'
import {
  HealthCheckTypeOptions,
  HealthCheckStatusOptions,
  HealthCheckTypeTextMap,
  HealthCheckStatusTextMap,
  HealthCheckStatusColorMap
} from '@/types/health-check'
import {
  ReloadOutlined,
  PlayCircleOutlined,
  ExportOutlined,
  AuditOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue'

// 使用store
const healthCheckStore = useHealthCheckStore()

// 彻底重构 - 使用storeToRefs确保正确的响应式绑定
const { healthCheckList, statistics, loading } = storeToRefs(healthCheckStore)

// 响应式数据
const searchForm = ref<HealthCheckSearchParams>({
  checkName: '',
  checkType: undefined,
  status: undefined
})

const detailVisible = ref(false)
const executeVisible = ref(false)
const executing = ref(false)
const currentRecord = ref<HealthCheckRecord | null>(null)

// 新增：加载状态管理
const refreshing = ref(false)
const exporting = ref(false)
const initialLoading = ref(true)
const dataSourceInfo = ref<{ isFromAPI: boolean; error?: string; timestamp?: string }>({ isFromAPI: true })


// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '体检名称',
    dataIndex: 'checkName',
    key: 'checkName',
    width: 200
  },
  {
    title: '体检类型',
    key: 'checkType',
    width: 120,
    align: 'center'
  },
  {
    title: '检查对象',
    dataIndex: 'targetObject',
    key: 'targetObject',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '异常数量',
    key: 'exceptionCount',
    width: 100,
    align: 'center'
  },
  {
    title: '最后检查时间',
    dataIndex: 'lastCheckTime',
    key: 'lastCheckTime',
    width: 180
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    key: 'operator',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 计算属性 - 提供数据状态检查
const hasStatisticsData = computed(() => {
  const stats = healthCheckStore.statistics
  return stats && (stats.totalChecks > 0 || stats.completedChecks > 0)
})

const hasHealthCheckData = computed(() => {
  return healthCheckStore.healthCheckList && healthCheckStore.healthCheckList.length > 0
})

const isDataEmpty = computed(() => {
  return !initialLoading.value && !hasStatisticsData.value && !hasHealthCheckData.value
})

// 方法定义
function getRowIndex(record: HealthCheckRecord) {
  return (healthCheckStore.healthCheckList || []).findIndex(item => item.id === record.id) + 1
}

function getCheckTypeName(type: number) {
  return HealthCheckTypeTextMap[type as keyof typeof HealthCheckTypeTextMap] || '未知'
}

function getCheckTypeColor(type: number) {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange', 4: 'purple' }
  return colors[type as keyof typeof colors] || 'default'
}

function getStatusText(status: number) {
  return HealthCheckStatusTextMap[status as keyof typeof HealthCheckStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return HealthCheckStatusColorMap[status as keyof typeof HealthCheckStatusColorMap] || 'default'
}

function getTypePercentage(count: number) {
  const total = healthCheckStore.statistics?.totalChecks || 1
  return Math.min(Math.round((count / total) * 100), 100) // 确保不超过100%
}

function getTypeColor(type: number) {
  const colors = { 1: '#1890ff', 2: '#52c41a', 3: '#faad14', 4: '#722ed1' }
  return colors[type as keyof typeof colors] || '#d9d9d9'
}

function getTrendPercentage(count: number) {
  const trendData = healthCheckStore.statistics?.exceptionTrend || []
  if (trendData.length === 0) return 0
  
  const maxCount = Math.max(...trendData.map(item => item.count), 1) // 至少为1，避免除零
  return Math.min(Math.round((count / maxCount) * 100), 100) // 确保不超过100%
}

function formatDate(dateStr: string) {
  return dateStr.substring(5) // 显示月-日
}

// 新增：错误处理工具函数
function handleError(error: any, operation: string, defaultMessage: string = '操作失败，请重试') {
  console.error(`${operation}错误:`, error)
  
  // 根据错误类型提供更具体的提示
  if (error?.response?.status === 401) {
    message.error('登录已过期，请重新登录')
    return
  }
  
  if (error?.response?.status === 403) {
    message.error('没有权限执行此操作')
    return
  }
  
  if (error?.response?.status >= 500) {
    message.error('服务器内部错误，请稍后重试')
    return
  }
  
  if (error?.code === 'NETWORK_ERROR' || !error?.response) {
    message.error('网络连接异常，请检查网络后重试')
    return
  }
  
  // 显示服务端返回的错误信息或默认消息
  const errorMessage = error?.response?.data?.message || error?.message || defaultMessage
  message.error(errorMessage)
}

// 新增：数据验证工具函数
function validateStatisticsData(data: any) {
  return {
    totalChecks: Number(data?.totalChecks) || 0,
    completedChecks: Number(data?.completedChecks) || 0,
    totalExceptions: Number(data?.totalExceptions) || 0,
    highLevelExceptions: Number(data?.highLevelExceptions) || 0,
    checkTypeStats: Array.isArray(data?.checkTypeStats) ? data.checkTypeStats : [],
    exceptionTrend: Array.isArray(data?.exceptionTrend) ? data.exceptionTrend : []
  }
}

async function refreshData() {
  if (refreshing.value) return // 防重复请求
  
  refreshing.value = true
  const errors: string[] = []
  let hasStaticData = false
  let staticDataError = ''
  
  try {
    // 分别处理每个请求的错误
    const promises = [
      healthCheckStore.fetchHealthCheckList().catch(error => {
        errors.push('体检记录')
        handleError(error, '获取体检记录', '')
        return null
      }),
      // 直接调用API以获取数据源信息
      healthCheckApi.fetchHealthCheckStatistics().then(response => {
        healthCheckStore.statistics = response.data
        if (!response.dataSource.isFromAPI) {
          hasStaticData = true
          staticDataError = response.dataSource.error || '网络连接问题'
        }
        return response
      }).catch(error => {
        errors.push('统计数据')
        handleError(error, '获取统计数据', '')
        return null
      })
    ]
    
    await Promise.all(promises)
    
    // 更新数据源信息
    dataSourceInfo.value = {
      isFromAPI: !hasStaticData,
      error: staticDataError,
      timestamp: new Date().toISOString()
    }
    
    if (errors.length === 0) {
      if (hasStaticData) {
        message.warning('数据刷新完成，部分数据来自静态降级')
      } else {
        message.success('数据刷新成功')
      }
    } else if (errors.length < 2) {
      message.warning(`部分数据刷新失败：${errors.join('、')}`)
    } else {
      message.error('数据刷新失败，请检查网络连接')
    }
  } catch (error) {
    handleError(error, '数据刷新', '刷新失败，请重试')
  } finally {
    refreshing.value = false
  }
}

function showExecuteModal() {
  executeVisible.value = true
}

async function confirmExecute() {
  if (executing.value) return // 防重复执行
  
  executing.value = true
  try {
    // 使用真实体检引擎执行体检
    const result = await healthCheckStore.executeHealthCheck([1, 2, 3, 4])
    
    if (result) {
      const exceptionCount = result.exceptions?.length || 0
      const successMessage = exceptionCount > 0 
        ? `体检任务已完成，发现 ${exceptionCount} 个异常项，请及时处理`
        : '体检任务已完成，未发现异常项'
      
      message.success(successMessage)
      executeVisible.value = false
      
      // 刷新数据，但不显示刷新成功消息（避免消息重复）
      await refreshData()
    } else {
      message.error('体检任务执行失败，请检查配置后重试')
    }
  } catch (error) {
    handleError(error, '体检执行', '体检执行失败，请检查网络连接或联系管理员')
  } finally {
    executing.value = false
  }
}

async function exportReport() {
  if (exporting.value) return // 防重复点击
  
  exporting.value = true
  const loadingMessage = message.loading('正在生成报告，请稍候...', 0)
  
  try {
    const result = await healthCheckStore.exportHealthCheckResults('excel')
    loadingMessage() // 关闭loading消息
    
    if (result) {
      message.success('报告导出成功，正在准备下载...')
      // 在实际环境中，这里可以触发文件下载
      console.log('导出文件:', result)
    } else {
      message.error('报告生成失败，暂无数据可导出')
    }
  } catch (error) {
    loadingMessage() // 关闭loading消息
    handleError(error, '报告导出', '报告导出失败，请稍后重试')
  } finally {
    exporting.value = false
  }
}

function showDetail(record: HealthCheckRecord) {
  currentRecord.value = record
  detailVisible.value = true
}

function viewExceptions(record: HealthCheckRecord) {
  message.info(`查看 ${record.checkName} 的异常详情`)
  // 这里可以跳转到异常管理页面或打开异常详情弹窗
}

async function executeCheck(record: HealthCheckRecord) {
  if (executing.value) return // 防重复执行
  
  executing.value = true
  const loadingMessage = message.loading(`正在执行 ${record.checkName}，请稍候...`, 0)
  
  try {
    // 根据体检类型执行相应的体检
    const checkType = record.checkType as any
    const result = await healthCheckStore.executeHealthCheck([checkType])
    loadingMessage() // 关闭loading消息
    
    if (result) {
      const exceptionCount = result.exceptions?.length || 0
      const successMessage = exceptionCount > 0
        ? `${record.checkName} 执行完成，发现 ${exceptionCount} 个异常项`
        : `${record.checkName} 执行完成，未发现异常项`
      
      message.success(successMessage)
      await refreshData()
    } else {
      message.error(`${record.checkName} 执行失败，请检查配置`)
    }
  } catch (error) {
    loadingMessage() // 关闭loading消息
    handleError(error, `执行${record.checkName}`, `${record.checkName} 执行失败，请重试`)
  } finally {
    executing.value = false
  }
}

async function handleSearch() {
  try {
    await healthCheckStore.fetchHealthCheckList(searchForm.value)
    message.success('搜索完成')
  } catch (error) {
    handleError(error, '搜索数据', '搜索失败，请重试')
  }
}

function resetSearch() {
  searchForm.value = {
    checkName: '',
    checkType: undefined,
    status: undefined
  }
  
  // 重置后重新获取数据
  healthCheckStore.fetchHealthCheckList().catch(error => {
    handleError(error, '重置搜索', '重置失败，请重试')
  })
}

// 生命周期
onMounted(async () => {
  initialLoading.value = true
  const errors: string[] = []
  
  try {
    // 使用重试机制加载初始数据
    const loadWithRetry = async (fn: () => Promise<any>, name: string, retries = 2) => {
      for (let i = 0; i <= retries; i++) {
        try {
          await fn()
          return
        } catch (error) {
          if (i === retries) {
            errors.push(name)
            console.error(`${name}加载失败(重试${retries}次):`, error)
          } else {
            console.warn(`${name}加载失败，正在重试...`)
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))) // 递增延时
          }
        }
      }
    }
    
    await Promise.allSettled([
      loadWithRetry(() => healthCheckStore.fetchHealthCheckList(), '体检记录'),
      loadWithRetry(() => healthCheckStore.fetchStatistics(), '统计数据')
    ])
    
    // 根据加载结果显示提示
    if (errors.length === 0) {
      // 全部加载成功，不显示消息（避免干扰用户）
    } else if (errors.length === 1) {
      message.warning(`${errors[0]}加载失败，请尝试手动刷新`)
    } else {
      message.error('数据加载失败，请检查网络连接后刷新页面')
    }
  } catch (error) {
    console.error('初始化失败:', error)
    message.error('页面初始化失败，请刷新页面重试')
  } finally {
    initialLoading.value = false
  }
})
</script>

<style lang="scss" scoped>
.health-check-dashboard {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
      
      .data-source-warning {
        margin-top: 16px;
      }
    }

    .header-actions {
      .ant-btn {
        margin-left: 8px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 28px;
          font-weight: 600;
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-container {
        height: 300px;
        padding: 16px;

        .chart-placeholder {
          .type-stat-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            background: #fafafa;
            border-radius: 6px;

            .type-info {
              flex: 0 0 120px;
              display: flex;
              flex-direction: column;

              .type-name {
                font-weight: 500;
                color: #333;
              }

              .type-count {
                font-size: 12px;
                color: #666;
              }
            }

            .type-progress {
              flex: 1;
              margin: 0 16px;
            }

            .exception-info {
              flex: 0 0 80px;
              text-align: right;
            }
          }
        }

        .trend-chart {
          .trend-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .trend-date {
              flex: 0 0 60px;
              font-size: 12px;
              color: #666;
            }

            .trend-bar {
              flex: 1;
              height: 20px;
              background: #f0f0f0;
              border-radius: 10px;
              margin: 0 12px;
              overflow: hidden;

              .trend-bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #1890ff, #52c41a);
                border-radius: 10px;
                transition: width 0.3s ease;
              }
            }

            .trend-count {
              flex: 0 0 40px;
              text-align: right;
              font-weight: 500;
              color: #333;
            }
          }
        }
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .empty-state-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      text-align: center;
      padding: 40px 0;
      
      .ant-empty-description {
        color: #666;
        margin: 16px 0;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .health-check-dashboard {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
