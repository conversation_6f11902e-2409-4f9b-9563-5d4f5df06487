<template>
  <div class="data-collection-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>数据收集与继承</h2>
        <p>全面接入各类数据源，确保数据的完整性、准确性、一致性和安全性</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showDataSourceModal">
            <template #icon><plus-outlined /></template>
            添加数据源
          </a-button>
          <a-button @click="syncAllDataSources" :loading="syncingAll">
            <template #icon><sync-outlined /></template>
            全量同步
          </a-button>
          <a-button @click="testConnections">
            <template #icon><api-outlined /></template>
            测试连接
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据源状态概览 -->
    <div class="overview-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="数据源总数"
              :value="dataSourceStats.totalSources"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <database-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="在线数据源"
              :value="dataSourceStats.onlineSources"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="今日同步量"
              :value="dataSourceStats.todaySyncCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <cloud-sync-outlined />
              </template>
              <template #suffix>条</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="数据质量评分"
              :value="dataSourceStats.qualityScore"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
              <template #suffix>分</template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据源分类标签页 -->
    <div class="data-source-tabs-section">
      <a-card>
        <a-tabs v-model:activeKey="activeSourceType" @change="handleSourceTypeChange">
          <a-tab-pane key="all" tab="全部数据源">
            <div class="source-type-info">
              <a-alert 
                message="全部数据源" 
                description="显示系统中接入的所有数据源，包括数据库、API接口、文件系统、外部系统等。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="database" tab="数据库">
            <div class="source-type-info">
              <a-alert 
                message="数据库数据源" 
                description="MySQL、PostgreSQL、Oracle等关系型数据库以及MongoDB等非关系型数据库。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="api" tab="API接口">
            <div class="source-type-info">
              <a-alert 
                message="API接口数据源" 
                description="REST API、GraphQL、SOAP等各类API接口数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="file" tab="文件系统">
            <div class="source-type-info">
              <a-alert 
                message="文件系统数据源" 
                description="Excel、CSV、XML、JSON等各类文件格式的数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="external" tab="外部系统">
            <div class="source-type-info">
              <a-alert 
                message="外部系统数据源" 
                description="第三方系统、云服务、消息队列等外部系统数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 数据源列表 -->
    <div class="data-source-list-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>{{ getSourceTypeTitle() }}列表</span>
            <span class="record-count">共 {{ filteredDataSources.length }} 个数据源</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option value="online">在线</a-select-option>
              <a-select-option value="offline">离线</a-select-option>
              <a-select-option value="error">异常</a-select-option>
            </a-select>
            <a-button size="small" @click="batchSync" :disabled="selectedRowKeys.length === 0">
              批量同步
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredDataSources"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1600 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                <template #icon>
                  <loading-outlined v-if="record.syncing" />
                  <check-circle-outlined v-else-if="record.status === 'online'" />
                  <close-circle-outlined v-else-if="record.status === 'offline'" />
                  <exclamation-circle-outlined v-else />
                </template>
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'sourceType'">
              <a-tag :color="getSourceTypeColor(record.sourceType)">
                {{ getSourceTypeText(record.sourceType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'dataQuality'">
              <div class="quality-display">
                <a-progress 
                  :percent="record.dataQuality" 
                  :stroke-color="getQualityColor(record.dataQuality)"
                  size="small"
                  :show-info="false"
                />
                <span class="quality-score">{{ record.dataQuality }}%</span>
              </div>
            </template>
            <template v-else-if="column.key === 'lastSyncTime'">
              <span v-if="record.lastSyncTime">{{ formatDateTime(record.lastSyncTime) }}</span>
              <span v-else style="color: #999;">未同步</span>
            </template>
            <template v-else-if="column.key === 'syncStatus'">
              <a-tag v-if="record.syncing" color="processing">同步中</a-tag>
              <a-tag v-else-if="record.lastSyncStatus === 'success'" color="success">成功</a-tag>
              <a-tag v-else-if="record.lastSyncStatus === 'failed'" color="error">失败</a-tag>
              <a-tag v-else color="default">待同步</a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="editDataSource(record)">编辑</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="syncDataSource(record)" 
                  :loading="record.syncing"
                >
                  同步
                </a-button>
                <a-button type="link" size="small" @click="testConnection(record)">测试</a-button>
                <a-popconfirm
                  title="确定要删除这个数据源吗？"
                  @confirm="deleteDataSource(record.id)"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 数据流监控图表 -->
    <div class="data-flow-section">
      <a-card title="数据流监控" class="flow-card">
        <template #extra>
          <a-radio-group v-model:value="flowTimeRange" button-style="solid" size="small">
            <a-radio-button value="1h">近1小时</a-radio-button>
            <a-radio-button value="6h">近6小时</a-radio-button>
            <a-radio-button value="24h">近24小时</a-radio-button>
          </a-radio-group>
        </template>
        <div class="flow-chart-container">
          <div class="flow-metrics">
            <div class="flow-metric-item">
              <div class="metric-title">总流入量</div>
              <div class="metric-value">{{ formatDataVolume(dataFlowMetrics.totalInflow) }}</div>
              <div class="metric-trend" :class="{ 'trend-up': dataFlowMetrics.inflowTrend > 0 }">
                {{ dataFlowMetrics.inflowTrend > 0 ? '↑' : '↓' }} {{ Math.abs(dataFlowMetrics.inflowTrend) }}%
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">处理速度</div>
              <div class="metric-value">{{ dataFlowMetrics.processSpeed }}/s</div>
              <div class="metric-indicator" :class="getSpeedLevel(dataFlowMetrics.processSpeed)">
                {{ getSpeedText(dataFlowMetrics.processSpeed) }}
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">错误率</div>
              <div class="metric-value">{{ dataFlowMetrics.errorRate }}%</div>
              <div class="metric-indicator" :class="getErrorLevel(dataFlowMetrics.errorRate)">
                {{ getErrorText(dataFlowMetrics.errorRate) }}
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">队列积压</div>
              <div class="metric-value">{{ dataFlowMetrics.queueBacklog }}</div>
              <div class="metric-indicator" :class="getBacklogLevel(dataFlowMetrics.queueBacklog)">
                {{ getBacklogText(dataFlowMetrics.queueBacklog) }}
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 数据源表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
      :mask-closable="false"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-divider orientation="left">基本信息</a-divider>
        <a-form-item label="数据源名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入数据源名称" />
        </a-form-item>
        <a-form-item label="数据源类型" name="sourceType">
          <a-select v-model:value="form.sourceType" placeholder="请选择数据源类型">
            <a-select-option value="database">数据库</a-select-option>
            <a-select-option value="api">API接口</a-select-option>
            <a-select-option value="file">文件系统</a-select-option>
            <a-select-option value="external">外部系统</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述说明" name="description">
          <a-textarea v-model:value="form.description" :rows="2" placeholder="请输入数据源描述" />
        </a-form-item>

        <a-divider orientation="left">连接配置</a-divider>
        <a-form-item v-if="form.sourceType === 'database'" label="数据库类型" name="dbType">
          <a-select v-model:value="form.dbType" placeholder="请选择数据库类型">
            <a-select-option value="mysql">MySQL</a-select-option>
            <a-select-option value="postgresql">PostgreSQL</a-select-option>
            <a-select-option value="oracle">Oracle</a-select-option>
            <a-select-option value="mongodb">MongoDB</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="连接地址" name="connectionUrl">
          <a-input v-model:value="form.connectionUrl" placeholder="请输入连接地址" />
        </a-form-item>
        <a-form-item v-if="form.sourceType !== 'file'" label="用户名" name="username">
          <a-input v-model:value="form.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item v-if="form.sourceType !== 'file'" label="密码" name="password">
          <a-input-password v-model:value="form.password" placeholder="请输入密码" />
        </a-form-item>

        <a-divider orientation="left">同步配置</a-divider>
        <a-form-item label="同步频率" name="syncFrequency">
          <a-select v-model:value="form.syncFrequency" placeholder="请选择同步频率">
            <a-select-option value="realtime">实时同步</a-select-option>
            <a-select-option value="5min">每5分钟</a-select-option>
            <a-select-option value="15min">每15分钟</a-select-option>
            <a-select-option value="1hour">每小时</a-select-option>
            <a-select-option value="6hour">每6小时</a-select-option>
            <a-select-option value="daily">每日</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="数据校验" name="enableValidation">
          <a-switch v-model:checked="form.enableValidation" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.enableValidation ? '启用数据校验' : '关闭数据校验' }}
          </span>
        </a-form-item>
        <a-form-item label="增量同步" name="enableIncremental">
          <a-switch v-model:checked="form.enableIncremental" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.enableIncremental ? '启用增量同步' : '全量同步' }}
          </span>
        </a-form-item>
        <a-form-item label="异常处理" name="errorHandling">
          <a-select v-model:value="form.errorHandling" placeholder="请选择异常处理策略">
            <a-select-option value="skip">跳过错误数据</a-select-option>
            <a-select-option value="retry">重试机制</a-select-option>
            <a-select-option value="stop">停止同步</a-select-option>
            <a-select-option value="alert">发送告警</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 4, span: 18 }" style="margin-top: 24px">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button @click="testFormConnection" :loading="testing">测试连接</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 数据源详情弹窗 -->
    <a-modal 
      title="数据源详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <div class="detail-content" v-if="currentDataSource">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="数据源名称" :span="2">
            {{ currentDataSource.name }}
          </a-descriptions-item>
          <a-descriptions-item label="数据源类型">
            <a-tag :color="getSourceTypeColor(currentDataSource.sourceType)">
              {{ getSourceTypeText(currentDataSource.sourceType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="连接状态">
            <a-tag :color="getStatusColor(currentDataSource.status)">
              {{ getStatusText(currentDataSource.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="数据质量">
            <a-progress 
              :percent="currentDataSource.dataQuality" 
              :stroke-color="getQualityColor(currentDataSource.dataQuality)"
              size="small"
            />
          </a-descriptions-item>
          <a-descriptions-item label="同步频率">
            {{ getSyncFrequencyText(currentDataSource.syncFrequency) }}
          </a-descriptions-item>
          <a-descriptions-item label="最后同步">
            {{ currentDataSource.lastSyncTime ? formatDateTime(currentDataSource.lastSyncTime) : '未同步' }}
          </a-descriptions-item>
          <a-descriptions-item label="同步状态">
            <a-tag :color="getSyncStatusColor(currentDataSource.lastSyncStatus)">
              {{ getSyncStatusText(currentDataSource.lastSyncStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="数据量" :span="2">
            {{ formatDataVolume(currentDataSource.dataVolume) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(currentDataSource.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(currentDataSource.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="描述说明" :span="2">
            {{ currentDataSource.description || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  SyncOutlined,
  ApiOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  CloudSyncOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface DataSource {
  id: string
  name: string
  sourceType: 'database' | 'api' | 'file' | 'external'
  status: 'online' | 'offline' | 'error'
  connectionUrl: string
  username?: string
  password?: string
  dbType?: string
  syncFrequency: string
  enableValidation: boolean
  enableIncremental: boolean
  errorHandling: string
  dataQuality: number
  dataVolume: number
  lastSyncTime?: string
  lastSyncStatus?: 'success' | 'failed' | 'pending'
  createTime: string
  updateTime: string
  description?: string
  syncing?: boolean
}

interface DataSourceStats {
  totalSources: number
  onlineSources: number
  todaySyncCount: number
  qualityScore: number
}

interface DataFlowMetrics {
  totalInflow: number
  inflowTrend: number
  processSpeed: number
  errorRate: number
  queueBacklog: number
}

// 响应式数据
const loading = ref(false)
const syncingAll = ref(false)
const formModalVisible = ref(false)
const detailVisible = ref(false)
const submitting = ref(false)
const testing = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const activeSourceType = ref('all')
const statusFilter = ref<string | undefined>(undefined)
const flowTimeRange = ref('24h')
const selectedRowKeys = ref<string[]>([])
const currentDataSource = ref<DataSource | null>(null)

// 表单数据
const form = ref<Partial<DataSource>>({
  name: '',
  sourceType: 'database',
  connectionUrl: '',
  username: '',
  password: '',
  dbType: 'mysql',
  syncFrequency: 'daily',
  enableValidation: true,
  enableIncremental: true,
  errorHandling: 'retry',
  description: ''
})

// 表单引用
const formRef = ref()

// 模拟数据
const dataSourceStats = ref<DataSourceStats>({
  totalSources: 12,
  onlineSources: 10,
  todaySyncCount: 25687,
  qualityScore: 94
})

const dataFlowMetrics = ref<DataFlowMetrics>({
  totalInflow: 1250000,
  inflowTrend: 12.5,
  processSpeed: 1250,
  errorRate: 0.8,
  queueBacklog: 23
})

const dataSourceList = ref<DataSource[]>([
  {
    id: '1',
    name: '党建管理数据库',
    sourceType: 'database',
    status: 'online',
    connectionUrl: '************************************',
    username: 'admin',
    dbType: 'mysql',
    syncFrequency: 'daily',
    enableValidation: true,
    enableIncremental: true,
    errorHandling: 'retry',
    dataQuality: 98,
    dataVolume: 156780,
    lastSyncTime: '2025-01-07 02:00:00',
    lastSyncStatus: 'success',
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-07 02:00:00',
    description: '党建基础数据管理数据库'
  },
  {
    id: '2',
    name: '组织架构API',
    sourceType: 'api',
    status: 'online',
    connectionUrl: 'https://api.example.com/org',
    username: 'api_user',
    syncFrequency: '15min',
    enableValidation: true,
    enableIncremental: false,
    errorHandling: 'alert',
    dataQuality: 92,
    dataVolume: 45620,
    lastSyncTime: '2025-01-07 15:45:00',
    lastSyncStatus: 'success',
    createTime: '2025-01-02 14:30:00',
    updateTime: '2025-01-07 15:45:00',
    description: '组织架构信息API接口'
  },
  {
    id: '3',
    name: '干部档案文件',
    sourceType: 'file',
    status: 'offline',
    connectionUrl: '/data/cadre_files/',
    syncFrequency: '6hour',
    enableValidation: false,
    enableIncremental: true,
    errorHandling: 'skip',
    dataQuality: 76,
    dataVolume: 12340,
    lastSyncTime: '2025-01-06 18:00:00',
    lastSyncStatus: 'failed',
    createTime: '2025-01-03 09:15:00',
    updateTime: '2025-01-06 18:00:00',
    description: '干部档案Excel文件导入'
  }
])

// 计算属性
const filteredDataSources = computed(() => {
  let filtered = [...dataSourceList.value]
  
  if (activeSourceType.value !== 'all') {
    filtered = filtered.filter(ds => ds.sourceType === activeSourceType.value)
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(ds => ds.status === statusFilter.value)
  }
  
  return filtered
})

const modalTitle = computed(() => {
  return formMode.value === 'create' ? '添加数据源' : '编辑数据源'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '添加' : '更新'
})

// 表格列定义
const columns = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '数据源名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '类型', key: 'sourceType', width: 100, align: 'center' },
  { title: '连接状态', key: 'status', width: 120, align: 'center' },
  { title: '数据质量', key: 'dataQuality', width: 120 },
  { title: '数据量', dataIndex: 'dataVolume', key: 'dataVolume', width: 100, align: 'right' },
  { title: '最后同步', key: 'lastSyncTime', width: 150 },
  { title: '同步状态', key: 'syncStatus', width: 100, align: 'center' },
  { title: '操作', key: 'action', width: 280, align: 'center', fixed: 'right' }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 个数据源`
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  sourceType: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  connectionUrl: [
    { required: true, message: '请输入连接地址', trigger: 'blur' }
  ]
}

// 方法定义
function getRowIndex(record: DataSource) {
  return filteredDataSources.value.findIndex(item => item.id === record.id) + 1
}

function getStatusColor(status: string) {
  const colors = {
    online: 'success',
    offline: 'default',
    error: 'error'
  }
  return colors[status] || 'default'
}

function getStatusText(status: string) {
  const texts = {
    online: '在线',
    offline: '离线',
    error: '异常'
  }
  return texts[status] || '未知'
}

function getSourceTypeColor(type: string) {
  const colors = {
    database: 'blue',
    api: 'green',
    file: 'orange',
    external: 'purple'
  }
  return colors[type] || 'default'
}

function getSourceTypeText(type: string) {
  const texts = {
    database: '数据库',
    api: 'API接口',
    file: '文件系统',
    external: '外部系统'
  }
  return texts[type] || '未知'
}

function getSourceTypeTitle() {
  const titles = {
    all: '全部数据源',
    database: '数据库数据源',
    api: 'API接口数据源', 
    file: '文件系统数据源',
    external: '外部系统数据源'
  }
  return titles[activeSourceType.value] || '数据源'
}

function getQualityColor(quality: number) {
  if (quality >= 90) return '#52c41a'
  if (quality >= 70) return '#faad14'
  return '#ff4d4f'
}

function getSyncStatusColor(status?: string) {
  const colors = {
    success: 'success',
    failed: 'error',
    pending: 'processing'
  }
  return colors[status || 'pending'] || 'default'
}

function getSyncStatusText(status?: string) {
  const texts = {
    success: '成功',
    failed: '失败',
    pending: '待同步'
  }
  return texts[status || 'pending'] || '未知'
}

function getSyncFrequencyText(frequency: string) {
  const texts = {
    realtime: '实时同步',
    '5min': '每5分钟',
    '15min': '每15分钟',
    '1hour': '每小时',
    '6hour': '每6小时',
    daily: '每日'
  }
  return texts[frequency] || frequency
}

function formatDateTime(dateTime: string) {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

function formatDataVolume(volume: number) {
  if (volume >= 1000000) {
    return (volume / 1000000).toFixed(1) + 'M'
  } else if (volume >= 1000) {
    return (volume / 1000).toFixed(1) + 'K'
  }
  return volume.toString()
}

function getSpeedLevel(speed: number) {
  if (speed >= 1000) return 'speed-fast'
  if (speed >= 500) return 'speed-normal'
  return 'speed-slow'
}

function getSpeedText(speed: number) {
  if (speed >= 1000) return '高速'
  if (speed >= 500) return '正常'
  return '缓慢'
}

function getErrorLevel(rate: number) {
  if (rate <= 1) return 'error-low'
  if (rate <= 5) return 'error-medium'
  return 'error-high'
}

function getErrorText(rate: number) {
  if (rate <= 1) return '良好'
  if (rate <= 5) return '一般'
  return '较高'
}

function getBacklogLevel(backlog: number) {
  if (backlog <= 10) return 'backlog-low'
  if (backlog <= 50) return 'backlog-medium'
  return 'backlog-high'
}

function getBacklogText(backlog: number) {
  if (backlog <= 10) return '正常'
  if (backlog <= 50) return '中等'
  return '较高'
}

async function refreshData() {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('数据刷新成功')
  } finally {
    loading.value = false
  }
}

async function syncAllDataSources() {
  syncingAll.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 3000))
    message.success('全量同步完成')
  } finally {
    syncingAll.value = false
  }
}

async function testConnections() {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    const onlineCount = dataSourceList.value.filter(ds => ds.status === 'online').length
    message.success(`连接测试完成，${onlineCount}/${dataSourceList.value.length} 个数据源在线`)
  } finally {
    loading.value = false
  }
}

function handleSourceTypeChange(key: string) {
  activeSourceType.value = key
}

function showDataSourceModal() {
  formMode.value = 'create'
  form.value = {
    name: '',
    sourceType: 'database',
    connectionUrl: '',
    username: '',
    password: '',
    dbType: 'mysql',
    syncFrequency: 'daily',
    enableValidation: true,
    enableIncremental: true,
    errorHandling: 'retry',
    description: ''
  }
  formModalVisible.value = true
}

function editDataSource(record: DataSource) {
  formMode.value = 'edit'
  form.value = { ...record }
  currentDataSource.value = record
  formModalVisible.value = true
}

function showDetail(record: DataSource) {
  currentDataSource.value = record
  detailVisible.value = true
}

async function syncDataSource(record: DataSource) {
  record.syncing = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    record.lastSyncTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    record.lastSyncStatus = 'success'
    message.success(`${record.name} 同步成功`)
  } catch (error) {
    record.lastSyncStatus = 'failed'
    message.error(`${record.name} 同步失败`)
  } finally {
    record.syncing = false
  }
}

async function testConnection(record: DataSource) {
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success(`${record.name} 连接测试成功`)
  } catch (error) {
    message.error(`${record.name} 连接测试失败`)
  }
}

async function deleteDataSource(id: string) {
  const index = dataSourceList.value.findIndex(item => item.id === id)
  if (index > -1) {
    dataSourceList.value.splice(index, 1)
    message.success('数据源删除成功')
  }
}

function onSelectChange(selectedKeys: string[]) {
  selectedRowKeys.value = selectedKeys
}

async function batchSync() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要同步的数据源')
    return
  }

  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success(`${selectedRowKeys.value.length} 个数据源批量同步成功`)
    selectedRowKeys.value = []
  } catch (error) {
    message.error('批量同步失败')
  }
}

async function onSubmit() {
  try {
    submitting.value = true
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      const newDataSource: DataSource = {
        ...form.value as DataSource,
        id: Date.now().toString(),
        status: 'offline',
        dataQuality: 0,
        dataVolume: 0,
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      dataSourceList.value.unshift(newDataSource)
      message.success('数据源添加成功')
    } else {
      const index = dataSourceList.value.findIndex(item => item.id === currentDataSource.value?.id)
      if (index > -1) {
        dataSourceList.value[index] = { 
          ...dataSourceList.value[index], 
          ...form.value,
          updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
        }
        message.success('数据源更新成功')
      }
    }
    
    formModalVisible.value = false
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

async function testFormConnection() {
  testing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('连接测试成功')
  } catch (error) {
    message.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.data-collection-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .status-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .data-source-tabs-section {
    margin-bottom: 24px;

    .source-type-info {
      padding: 16px 0;
    }
  }

  .data-source-list-section {
    margin-bottom: 24px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .quality-display {
      display: flex;
      align-items: center;
      gap: 8px;

      .quality-score {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .data-flow-section {
    .flow-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .flow-chart-container {
      padding: 16px;

      .flow-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;

        .flow-metric-item {
          text-align: center;
          padding: 20px;
          background: #fafafa;
          border-radius: 8px;

          .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .metric-trend {
            font-size: 12px;
            color: #52c41a;

            &.trend-up {
              color: #ff4d4f;
            }
          }

          .metric-indicator {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;

            &.speed-fast {
              background: #f6ffed;
              color: #52c41a;
            }

            &.speed-normal {
              background: #fff7e6;
              color: #faad14;
            }

            &.speed-slow {
              background: #fff1f0;
              color: #ff4d4f;
            }

            &.error-low {
              background: #f6ffed;
              color: #52c41a;
            }

            &.error-medium {
              background: #fff7e6;
              color: #faad14;
            }

            &.error-high {
              background: #fff1f0;
              color: #ff4d4f;
            }

            &.backlog-low {
              background: #f6ffed;
              color: #52c41a;
            }

            &.backlog-medium {
              background: #fff7e6;
              color: #faad14;
            }

            &.backlog-high {
              background: #fff1f0;
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-collection-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .flow-metrics {
      grid-template-columns: 1fr;
    }
  }
}
</style>