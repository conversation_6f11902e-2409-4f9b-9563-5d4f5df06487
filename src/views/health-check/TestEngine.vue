<template>
  <div class="health-check-test-engine">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>体检执行引擎</h2>
        <p>高性能数据体检引擎，支持多维度检测、智能规则匹配、实时处理和结果分析</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="startFullHealthCheck" :loading="executing">
            <template #icon><thunderbolt-outlined /></template>
            启动全面体检
          </a-button>
          <a-button @click="showEngineConfig">
            <template #icon><setting-outlined /></template>
            引擎配置
          </a-button>
          <a-button @click="showPerformanceMonitor">
            <template #icon><dashboard-outlined /></template>
            性能监控
          </a-button>
          <a-button @click="refreshEngineStatus">
            <template #icon><reload-outlined /></template>
            刷新状态
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 引擎状态概览 -->
    <div class="engine-status-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="引擎状态"
              :value="engineStatus.status"
              :value-style="{ color: getEngineStatusColor(engineStatus.status) }"
            >
              <template #prefix>
                <api-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="处理速度"
              :value="engineStatus.processingSpeed"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <rocket-outlined />
              </template>
              <template #suffix>/秒</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="内存使用率"
              :value="engineStatus.memoryUsage"
              :value-style="{ color: getMemoryColor(engineStatus.memoryUsage) }"
            >
              <template #prefix>
                <database-outlined />
              </template>
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="检测准确率"
              :value="engineStatus.accuracy"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <aim-outlined />
              </template>
              <template #suffix>%</template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 实时检测控制台 -->
    <div class="detection-console-section">
      <a-card title="实时检测控制台" class="console-card">
        <template #extra>
          <a-space>
            <a-switch v-model:checked="realTimeMode" @change="toggleRealTimeMode">
              <template #checkedChildren>实时</template>
              <template #unCheckedChildren>手动</template>
            </a-switch>
            <a-button size="small" @click="pauseDetection" v-if="realTimeMode && !detectionPaused">暂停检测</a-button>
            <a-button size="small" @click="resumeDetection" v-if="realTimeMode && detectionPaused">恢复检测</a-button>
          </a-space>
        </template>
        <div class="console-content">
          <div class="console-log" ref="consoleLogRef">
            <div v-for="(log, index) in consoleLogs" :key="index" class="log-entry" :class="log.level">
              <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
              <a-tag :color="getLogLevelColor(log.level)" size="small">{{ log.level.toUpperCase() }}</a-tag>
              <span class="log-message">{{ log.message }}</span>
              <div v-if="log.details" class="log-details">{{ log.details }}</div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 检测任务队列 -->
    <div class="task-queue-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :lg="12">
          <a-card title="任务队列状态" class="queue-card">
            <div class="queue-stats">
              <div class="queue-stat-item">
                <div class="stat-label">队列长度</div>
                <div class="stat-value">{{ taskQueue.length }}</div>
              </div>
              <div class="queue-stat-item">
                <div class="stat-label">执行中</div>
                <div class="stat-value running">{{ runningTasks }}</div>
              </div>
              <div class="queue-stat-item">
                <div class="stat-label">等待中</div>
                <div class="stat-value pending">{{ pendingTasks }}</div>
              </div>
              <div class="queue-stat-item">
                <div class="stat-label">已完成</div>
                <div class="stat-value completed">{{ completedTasks }}</div>
              </div>
            </div>
            <a-divider />
            <div class="queue-list">
              <div v-for="task in taskQueue.slice(0, 5)" :key="task.id" class="task-item">
                <div class="task-info">
                  <span class="task-name">{{ task.name }}</span>
                  <a-tag :color="getTaskStatusColor(task.status)" size="small">
                    {{ getTaskStatusText(task.status) }}
                  </a-tag>
                </div>
                <div class="task-progress" v-if="task.status === 'running'">
                  <a-progress :percent="task.progress" size="small" />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :xs="24" :lg="12">
          <a-card title="检测引擎配置" class="config-card">
            <a-form :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-form-item label="并发线程数">
                <a-input-number v-model:value="engineConfig.threads" :min="1" :max="16" />
              </a-form-item>
              <a-form-item label="批处理大小">
                <a-input-number v-model:value="engineConfig.batchSize" :min="100" :max="10000" />
              </a-form-item>
              <a-form-item label="超时时间(秒)">
                <a-input-number v-model:value="engineConfig.timeout" :min="10" :max="300" />
              </a-form-item>
              <a-form-item label="启用缓存">
                <a-switch v-model:checked="engineConfig.enableCache" />
              </a-form-item>
              <a-form-item label="调试模式">
                <a-switch v-model:checked="engineConfig.debugMode" />
              </a-form-item>
              <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
                <a-space>
                  <a-button type="primary" @click="applyEngineConfig">应用配置</a-button>
                  <a-button @click="resetEngineConfig">重置</a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 检测结果统计 -->
    <div class="results-section">
      <a-card title="检测结果统计" class="results-card">
        <template #extra>
          <a-radio-group v-model:value="resultTimeFrame" button-style="solid" size="small">
            <a-radio-button value="1h">近1小时</a-radio-button>
            <a-radio-button value="24h">近24小时</a-radio-button>
            <a-radio-button value="7d">近7天</a-radio-button>
          </a-radio-group>
        </template>
        <div class="results-overview">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="6">
              <div class="result-metric">
                <div class="metric-value">{{ detectionResults.totalChecks }}</div>
                <div class="metric-label">总检测次数</div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="result-metric">
                <div class="metric-value error">{{ detectionResults.exceptionsFound }}</div>
                <div class="metric-label">发现异常</div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="result-metric">
                <div class="metric-value success">{{ detectionResults.successRate }}%</div>
                <div class="metric-label">检测成功率</div>
              </div>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6">
              <div class="result-metric">
                <div class="metric-value">{{ detectionResults.avgResponseTime }}ms</div>
                <div class="metric-label">平均响应时间</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 性能监控图表 -->
    <div class="performance-section">
      <a-card title="性能监控" class="performance-card">
        <div class="performance-charts">
          <div class="chart-row">
            <div class="chart-item">
              <h4>CPU使用率</h4>
              <div class="chart-placeholder">
                <div class="performance-line">
                  <div 
                    v-for="point in performanceData.cpu" 
                    :key="point.time" 
                    class="performance-point"
                    :style="{ height: point.value + '%', backgroundColor: getCpuColor(point.value) }"
                  ></div>
                </div>
                <div class="chart-legend">{{ performanceData.cpu[performanceData.cpu.length - 1]?.value }}%</div>
              </div>
            </div>
            <div class="chart-item">
              <h4>内存使用率</h4>
              <div class="chart-placeholder">
                <div class="performance-line">
                  <div 
                    v-for="point in performanceData.memory" 
                    :key="point.time" 
                    class="performance-point"
                    :style="{ height: point.value + '%', backgroundColor: getMemoryColor(point.value) }"
                  ></div>
                </div>
                <div class="chart-legend">{{ performanceData.memory[performanceData.memory.length - 1]?.value }}%</div>
              </div>
            </div>
            <div class="chart-item">
              <h4>检测吞吐量</h4>
              <div class="chart-placeholder">
                <div class="performance-line">
                  <div 
                    v-for="point in performanceData.throughput" 
                    :key="point.time" 
                    class="performance-point"
                    :style="{ height: (point.value / 100) + '%', backgroundColor: '#52c41a' }"
                  ></div>
                </div>
                <div class="chart-legend">{{ performanceData.throughput[performanceData.throughput.length - 1]?.value }}/s</div>
              </div>
            </div>
            <div class="chart-item">
              <h4>响应时间</h4>
              <div class="chart-placeholder">
                <div class="performance-line">
                  <div 
                    v-for="point in performanceData.responseTime" 
                    :key="point.time" 
                    class="performance-point"
                    :style="{ height: (point.value / 10) + '%', backgroundColor: getResponseTimeColor(point.value) }"
                  ></div>
                </div>
                <div class="chart-legend">{{ performanceData.responseTime[performanceData.responseTime.length - 1]?.value }}ms</div>
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 引擎配置弹窗 -->
    <a-modal 
      title="引擎配置" 
      :visible="configModalVisible" 
      @cancel="configModalVisible = false" 
      :footer="null" 
      width="600px"
    >
      <a-tabs>
        <a-tab-pane key="basic" tab="基础配置">
          <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="引擎名称">
              <a-input v-model:value="advancedConfig.engineName" />
            </a-form-item>
            <a-form-item label="最大并发">
              <a-input-number v-model:value="advancedConfig.maxConcurrency" :min="1" :max="100" />
            </a-form-item>
            <a-form-item label="队列大小">
              <a-input-number v-model:value="advancedConfig.queueSize" :min="100" :max="50000" />
            </a-form-item>
            <a-form-item label="心跳间隔">
              <a-input-number v-model:value="advancedConfig.heartbeatInterval" :min="1" :max="60" addon-after="秒" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="algorithm" tab="算法配置">
          <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="检测算法">
              <a-select v-model:value="advancedConfig.algorithm">
                <a-select-option value="rule-based">基于规则</a-select-option>
                <a-select-option value="ml-enhanced">机器学习增强</a-select-option>
                <a-select-option value="hybrid">混合模式</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="敏感度">
              <a-slider v-model:value="advancedConfig.sensitivity" :min="1" :max="10" />
            </a-form-item>
            <a-form-item label="阈值调整">
              <a-switch v-model:checked="advancedConfig.adaptiveThreshold" />
            </a-form-item>
            <a-form-item label="学习模式">
              <a-switch v-model:checked="advancedConfig.learningMode" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="optimization" tab="性能优化">
          <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
            <a-form-item label="启用索引">
              <a-switch v-model:checked="advancedConfig.enableIndexing" />
            </a-form-item>
            <a-form-item label="缓存策略">
              <a-select v-model:value="advancedConfig.cacheStrategy">
                <a-select-option value="lru">LRU</a-select-option>
                <a-select-option value="lfu">LFU</a-select-option>
                <a-select-option value="ttl">TTL</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="预加载">
              <a-switch v-model:checked="advancedConfig.preload" />
            </a-form-item>
            <a-form-item label="压缩存储">
              <a-switch v-model:checked="advancedConfig.compression" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      <div style="text-align: right; margin-top: 16px;">
        <a-space>
          <a-button @click="configModalVisible = false">取消</a-button>
          <a-button type="primary" @click="saveAdvancedConfig">保存配置</a-button>
        </a-space>
      </div>
    </a-modal>

    <!-- 性能监控弹窗 -->
    <a-modal 
      title="性能监控详情" 
      :visible="performanceModalVisible" 
      @cancel="performanceModalVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="detailed-performance">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :md="12">
            <a-card title="系统资源" size="small">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="CPU使用率">
                  <a-progress :percent="systemMetrics.cpu" size="small" />
                </a-descriptions-item>
                <a-descriptions-item label="内存使用">
                  <a-progress :percent="systemMetrics.memory" size="small" />
                </a-descriptions-item>
                <a-descriptions-item label="磁盘I/O">
                  <a-progress :percent="systemMetrics.disk" size="small" />
                </a-descriptions-item>
                <a-descriptions-item label="网络I/O">
                  <a-progress :percent="systemMetrics.network" size="small" />
                </a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
          <a-col :xs="24" :md="12">
            <a-card title="检测引擎" size="small">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="引擎状态">{{ engineStatus.status }}</a-descriptions-item>
                <a-descriptions-item label="处理速度">{{ engineStatus.processingSpeed }}/秒</a-descriptions-item>
                <a-descriptions-item label="队列积压">{{ taskQueue.length }}</a-descriptions-item>
                <a-descriptions-item label="平均延迟">{{ detectionResults.avgResponseTime }}ms</a-descriptions-item>
              </a-descriptions>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ThunderboltOutlined,
  SettingOutlined,
  DashboardOutlined,
  ReloadOutlined,
  ApiOutlined,
  RocketOutlined,
  DatabaseOutlined,
  AimOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface EngineStatus {
  status: 'running' | 'stopped' | 'error'
  processingSpeed: number
  memoryUsage: number
  accuracy: number
}

interface Task {
  id: string
  name: string
  status: 'pending' | 'running' | 'completed' | 'error'
  progress: number
  createTime: string
}

interface ConsoleLog {
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'success'
  message: string
  details?: string
}

interface EngineConfig {
  threads: number
  batchSize: number
  timeout: number
  enableCache: boolean
  debugMode: boolean
}

interface DetectionResults {
  totalChecks: number
  exceptionsFound: number
  successRate: number
  avgResponseTime: number
}

interface PerformancePoint {
  time: string
  value: number
}

interface PerformanceData {
  cpu: PerformancePoint[]
  memory: PerformancePoint[]
  throughput: PerformancePoint[]
  responseTime: PerformancePoint[]
}

// 响应式数据
const executing = ref(false)
const realTimeMode = ref(false)
const detectionPaused = ref(false)
const configModalVisible = ref(false)
const performanceModalVisible = ref(false)
const resultTimeFrame = ref('24h')
const consoleLogRef = ref()

const engineStatus = ref<EngineStatus>({
  status: 'running',
  processingSpeed: 1247,
  memoryUsage: 68,
  accuracy: 96.8
})

const engineConfig = ref<EngineConfig>({
  threads: 4,
  batchSize: 1000,
  timeout: 60,
  enableCache: true,
  debugMode: false
})

const advancedConfig = ref({
  engineName: 'HealthCheck-Engine-v2.0',
  maxConcurrency: 16,
  queueSize: 10000,
  heartbeatInterval: 30,
  algorithm: 'hybrid',
  sensitivity: 7,
  adaptiveThreshold: true,
  learningMode: false,
  enableIndexing: true,
  cacheStrategy: 'lru',
  preload: true,
  compression: false
})

const taskQueue = ref<Task[]>([
  { id: '1', name: '党组设置数据检测', status: 'running', progress: 75, createTime: dayjs().subtract(5, 'minute').format() },
  { id: '2', name: '干部任免信息验证', status: 'pending', progress: 0, createTime: dayjs().subtract(3, 'minute').format() },
  { id: '3', name: '用户信息完整性检查', status: 'pending', progress: 0, createTime: dayjs().subtract(1, 'minute').format() },
  { id: '4', name: '任务执行状态检测', status: 'completed', progress: 100, createTime: dayjs().subtract(10, 'minute').format() }
])

const consoleLogs = ref<ConsoleLog[]>([
  { timestamp: dayjs().subtract(2, 'minute').format(), level: 'info', message: '引擎初始化完成，开始数据体检流程' },
  { timestamp: dayjs().subtract(1, 'minute').format(), level: 'success', message: '党组设置数据检测完成', details: '共检测1247条记录，发现3个异常项' },
  { timestamp: dayjs().subtract(30, 'second').format(), level: 'warn', message: '检测到内存使用率较高', details: '当前使用率68%，建议检查内存泄漏' },
  { timestamp: dayjs().format(), level: 'info', message: '开始干部任免信息验证任务' }
])

const detectionResults = ref<DetectionResults>({
  totalChecks: 15247,
  exceptionsFound: 127,
  successRate: 99.2,
  avgResponseTime: 245
})

const performanceData = ref<PerformanceData>({
  cpu: Array.from({ length: 20 }, (_, i) => ({
    time: dayjs().subtract(20 - i, 'minute').format(),
    value: Math.floor(Math.random() * 30 + 40)
  })),
  memory: Array.from({ length: 20 }, (_, i) => ({
    time: dayjs().subtract(20 - i, 'minute').format(),
    value: Math.floor(Math.random() * 20 + 60)
  })),
  throughput: Array.from({ length: 20 }, (_, i) => ({
    time: dayjs().subtract(20 - i, 'minute').format(),
    value: Math.floor(Math.random() * 500 + 1000)
  })),
  responseTime: Array.from({ length: 20 }, (_, i) => ({
    time: dayjs().subtract(20 - i, 'minute').format(),
    value: Math.floor(Math.random() * 100 + 200)
  }))
})

const systemMetrics = ref({
  cpu: 65,
  memory: 72,
  disk: 45,
  network: 38
})

// 计算属性
const runningTasks = computed(() => taskQueue.value.filter(t => t.status === 'running').length)
const pendingTasks = computed(() => taskQueue.value.filter(t => t.status === 'pending').length)
const completedTasks = computed(() => taskQueue.value.filter(t => t.status === 'completed').length)

// 定时器
let performanceTimer: NodeJS.Timeout | null = null
let logTimer: NodeJS.Timeout | null = null

// 方法定义
function getEngineStatusColor(status: string) {
  const colors = {
    running: '#52c41a',
    stopped: '#faad14',
    error: '#ff4d4f'
  }
  return colors[status] || '#d9d9d9'
}

function getMemoryColor(usage: number) {
  if (usage < 70) return '#52c41a'
  if (usage < 85) return '#faad14'
  return '#ff4d4f'
}

function getCpuColor(usage: number) {
  if (usage < 60) return '#52c41a'
  if (usage < 80) return '#faad14'
  return '#ff4d4f'
}

function getResponseTimeColor(time: number) {
  if (time < 200) return '#52c41a'
  if (time < 500) return '#faad14'
  return '#ff4d4f'
}

function getLogLevelColor(level: string) {
  const colors = {
    info: 'blue',
    warn: 'orange',
    error: 'red',
    success: 'green'
  }
  return colors[level] || 'default'
}

function getTaskStatusColor(status: string) {
  const colors = {
    pending: 'default',
    running: 'processing',
    completed: 'success',
    error: 'error'
  }
  return colors[status] || 'default'
}

function getTaskStatusText(status: string) {
  const texts = {
    pending: '等待中',
    running: '执行中',
    completed: '已完成',
    error: '错误'
  }
  return texts[status] || '未知'
}

function formatLogTime(timestamp: string) {
  return dayjs(timestamp).format('HH:mm:ss')
}

async function startFullHealthCheck() {
  executing.value = true
  try {
    // 模拟全面体检流程
    addLog('info', '开始启动全面数据体检')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    addLog('info', '正在加载体检规则')
    await new Promise(resolve => setTimeout(resolve, 800))
    
    addLog('success', '规则加载完成，共加载24条规则')
    await new Promise(resolve => setTimeout(resolve, 500))
    
    addLog('info', '开始执行党组设置数据检测')
    updateTaskProgress('1', 'running', 25)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateTaskProgress('1', 'running', 50)
    addLog('info', '党组设置数据检测进行中...')
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    updateTaskProgress('1', 'running', 75)
    await new Promise(resolve => setTimeout(resolve, 800))
    
    updateTaskProgress('1', 'completed', 100)
    addLog('success', '党组设置数据检测完成，发现5个异常项')
    
    addLog('info', '开始干部任免信息验证')
    updateTaskProgress('2', 'running', 0)
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    updateTaskProgress('2', 'completed', 100)
    addLog('success', '全面数据体检完成，共发现12个异常项')
    
    message.success('全面体检执行完成')
  } catch (error) {
    addLog('error', '体检执行过程中出现错误', error.message)
    message.error('体检执行失败')
  } finally {
    executing.value = false
  }
}

function addLog(level: 'info' | 'warn' | 'error' | 'success', message: string, details?: string) {
  consoleLogs.value.push({
    timestamp: dayjs().format(),
    level,
    message,
    details
  })
  
  // 保持日志数量在合理范围
  if (consoleLogs.value.length > 100) {
    consoleLogs.value.splice(0, 20)
  }
  
  // 自动滚动到底部
  nextTick(() => {
    if (consoleLogRef.value) {
      consoleLogRef.value.scrollTop = consoleLogRef.value.scrollHeight
    }
  })
}

function updateTaskProgress(taskId: string, status: Task['status'], progress: number) {
  const task = taskQueue.value.find(t => t.id === taskId)
  if (task) {
    task.status = status
    task.progress = progress
  }
}

function showEngineConfig() {
  configModalVisible.value = true
}

function showPerformanceMonitor() {
  performanceModalVisible.value = true
}

function refreshEngineStatus() {
  // 模拟状态刷新
  engineStatus.value.processingSpeed = Math.floor(Math.random() * 500 + 1000)
  engineStatus.value.memoryUsage = Math.floor(Math.random() * 30 + 50)
  engineStatus.value.accuracy = Math.floor(Math.random() * 5 + 95)
  message.success('引擎状态已刷新')
}

function toggleRealTimeMode(enabled: boolean) {
  if (enabled) {
    startRealTimeMonitoring()
    addLog('info', '实时监控模式已启用')
  } else {
    stopRealTimeMonitoring()
    addLog('info', '实时监控模式已关闭')
  }
}

function pauseDetection() {
  detectionPaused.value = true
  addLog('warn', '检测已暂停')
}

function resumeDetection() {
  detectionPaused.value = false
  addLog('info', '检测已恢复')
}

function applyEngineConfig() {
  addLog('info', `引擎配置已更新: 线程数=${engineConfig.value.threads}, 批大小=${engineConfig.value.batchSize}`)
  message.success('引擎配置已应用')
}

function resetEngineConfig() {
  engineConfig.value = {
    threads: 4,
    batchSize: 1000,
    timeout: 60,
    enableCache: true,
    debugMode: false
  }
  message.info('引擎配置已重置')
}

function saveAdvancedConfig() {
  addLog('info', `高级配置已保存: 算法=${advancedConfig.value.algorithm}, 敏感度=${advancedConfig.value.sensitivity}`)
  configModalVisible.value = false
  message.success('高级配置保存成功')
}

function startRealTimeMonitoring() {
  // 启动性能监控定时器
  performanceTimer = setInterval(() => {
    updatePerformanceData()
  }, 2000)
  
  // 启动日志生成定时器
  logTimer = setInterval(() => {
    if (!detectionPaused.value) {
      generateRandomLog()
    }
  }, 3000)
}

function stopRealTimeMonitoring() {
  if (performanceTimer) {
    clearInterval(performanceTimer)
    performanceTimer = null
  }
  if (logTimer) {
    clearInterval(logTimer)
    logTimer = null
  }
}

function updatePerformanceData() {
  // 更新CPU数据
  performanceData.value.cpu.push({
    time: dayjs().format(),
    value: Math.floor(Math.random() * 30 + 40)
  })
  if (performanceData.value.cpu.length > 20) {
    performanceData.value.cpu.shift()
  }
  
  // 更新内存数据
  performanceData.value.memory.push({
    time: dayjs().format(),
    value: Math.floor(Math.random() * 20 + 60)
  })
  if (performanceData.value.memory.length > 20) {
    performanceData.value.memory.shift()
  }
  
  // 更新吞吐量数据
  performanceData.value.throughput.push({
    time: dayjs().format(),
    value: Math.floor(Math.random() * 500 + 1000)
  })
  if (performanceData.value.throughput.length > 20) {
    performanceData.value.throughput.shift()
  }
  
  // 更新响应时间数据
  performanceData.value.responseTime.push({
    time: dayjs().format(),
    value: Math.floor(Math.random() * 100 + 200)
  })
  if (performanceData.value.responseTime.length > 20) {
    performanceData.value.responseTime.shift()
  }
}

function generateRandomLog() {
  const messages = [
    { level: 'info', message: '检测任务已提交到队列' },
    { level: 'success', message: '用户信息完整性检查完成' },
    { level: 'warn', message: '检测到数据质量下降' },
    { level: 'info', message: '正在执行增量数据同步' },
    { level: 'success', message: '规则引擎优化完成' }
  ]
  
  const randomMessage = messages[Math.floor(Math.random() * messages.length)]
  addLog(randomMessage.level as any, randomMessage.message)
}

// 生命周期
onMounted(() => {
  addLog('info', '体检执行引擎已启动')
})

onUnmounted(() => {
  stopRealTimeMonitoring()
})
</script>

<style lang="scss" scoped>
.health-check-test-engine {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .engine-status-section {
    margin-bottom: 24px;

    .status-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .detection-console-section {
    margin-bottom: 24px;

    .console-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .console-content {
      .console-log {
        height: 300px;
        overflow-y: auto;
        background: #1e1e1e;
        border-radius: 4px;
        padding: 12px;

        .log-entry {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          font-family: 'Monaco', 'Menlo', monospace;
          font-size: 12px;

          .log-time {
            color: #666;
            margin-right: 8px;
            min-width: 60px;
          }

          .log-message {
            color: #fff;
            margin-left: 8px;
            flex: 1;
          }

          .log-details {
            color: #aaa;
            font-size: 11px;
            margin-left: 8px;
            margin-top: 4px;
          }

          &.info .log-message {
            color: #1890ff;
          }

          &.success .log-message {
            color: #52c41a;
          }

          &.warn .log-message {
            color: #faad14;
          }

          &.error .log-message {
            color: #ff4d4f;
          }
        }
      }
    }
  }

  .task-queue-section {
    margin-bottom: 24px;

    .queue-card,
    .config-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .queue-stats {
      display: flex;
      justify-content: space-around;
      text-align: center;

      .queue-stat-item {
        .stat-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;

          &.running {
            color: #1890ff;
          }

          &.pending {
            color: #faad14;
          }

          &.completed {
            color: #52c41a;
          }
        }
      }
    }

    .queue-list {
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .task-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .task-name {
            font-size: 13px;
            color: #333;
          }
        }

        .task-progress {
          width: 120px;
        }
      }
    }
  }

  .results-section,
  .performance-section {
    margin-bottom: 24px;

    .results-card,
    .performance-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .results-overview {
    .result-metric {
      text-align: center;
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;

      .metric-value {
        font-size: 28px;
        font-weight: 600;
        color: #333;

        &.error {
          color: #ff4d4f;
        }

        &.success {
          color: #52c41a;
        }
      }

      .metric-label {
        font-size: 14px;
        color: #666;
        margin-top: 8px;
      }
    }
  }

  .performance-charts {
    .chart-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;

      .chart-item {
        text-align: center;

        h4 {
          margin: 0 0 12px 0;
          font-size: 14px;
          color: #333;
        }

        .chart-placeholder {
          height: 120px;
          position: relative;
          background: #fafafa;
          border-radius: 8px;
          padding: 8px;

          .performance-line {
            display: flex;
            align-items: end;
            height: 80px;
            gap: 2px;

            .performance-point {
              flex: 1;
              min-height: 4px;
              border-radius: 2px 2px 0 0;
              transition: height 0.3s ease;
            }
          }

          .chart-legend {
            position: absolute;
            bottom: 8px;
            right: 8px;
            font-size: 12px;
            color: #666;
            background: rgba(255, 255, 255, 0.8);
            padding: 2px 6px;
            border-radius: 4px;
          }
        }
      }
    }
  }

  .detailed-performance {
    .ant-card {
      height: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .health-check-test-engine {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .queue-stats {
      flex-direction: column;
      gap: 16px;
    }

    .chart-row {
      grid-template-columns: 1fr;
    }
  }
}
</style>