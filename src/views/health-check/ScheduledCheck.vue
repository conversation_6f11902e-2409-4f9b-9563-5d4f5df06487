<template>
  <div class="scheduled-check-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>定时自动体检</h2>
        <p>配置定时体检任务，自动收集数据并执行全面体检，确保数据质量持续监控</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCreateModal">
            <template #icon><plus-outlined /></template>
            创建定时任务
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="runAllTasks" :loading="runningAll">
            <template #icon><thunderbolt-outlined /></template>
            立即执行所有
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总任务数"
              :value="taskList.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <schedule-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="启用任务"
              :value="enabledTaskCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日执行"
              :value="todayExecutionCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="异常检出"
              :value="totalExceptionsFound"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <warning-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>定时任务列表</span>
            <span class="record-count">共 {{ taskList.length }} 个任务</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option :value="true">启用</a-select-option>
              <a-select-option :value="false">禁用</a-select-option>
            </a-select>
            <a-button size="small" @click="batchOperation" :disabled="selectedRowKeys.length === 0">
              批量操作
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredTaskList"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1400 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'enabled'">
              <a-switch 
                v-model:checked="record.enabled" 
                @change="toggleTaskStatus(record)"
                :loading="record.updating"
              />
            </template>
            <template v-else-if="column.key === 'checkTypes'">
              <a-tag v-for="type in record.checkTypes" :key="type" :color="getCheckTypeColor(type)" size="small">
                {{ getCheckTypeName(type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'nextRun'">
              <span :style="{ color: getNextRunColor(record.nextRun) }">
                {{ formatDateTime(record.nextRun) }}
              </span>
            </template>
            <template v-else-if="column.key === 'lastRun'">
              <span v-if="record.lastRun">{{ formatDateTime(record.lastRun) }}</span>
              <span v-else style="color: #999;">未执行</span>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="editTask(record)">编辑</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="runTask(record)" 
                  :loading="record.running"
                >
                  立即执行
                </a-button>
                <a-button type="link" size="small" @click="viewLogs(record)">执行日志</a-button>
                <a-popconfirm
                  title="确定要删除这个定时任务吗？"
                  @confirm="deleteTask(record.id)"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 任务表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
      :mask-closable="false"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-divider orientation="left">基本信息</a-divider>
        <a-form-item label="任务名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入任务名称" />
        </a-form-item>
        <a-form-item label="任务描述" name="description">
          <a-textarea v-model:value="form.description" :rows="2" placeholder="请输入任务描述" />
        </a-form-item>

        <a-divider orientation="left">调度配置</a-divider>
        <a-form-item label="调度类型" name="scheduleType">
          <a-radio-group v-model:value="form.scheduleType">
            <a-radio value="cron">Cron表达式</a-radio>
            <a-radio value="interval">固定间隔</a-radio>
            <a-radio value="daily">每日定时</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item v-if="form.scheduleType === 'cron'" label="Cron表达式" name="cronExpression">
          <a-input v-model:value="form.cronExpression" placeholder="例如：0 0 2 * * ? 表示每天凌晨2点执行" />
          <div class="cron-help">
            <a-typography-text type="secondary">
              格式：秒 分 时 日 月 周年，常用示例：每小时执行(0 0 * * * ?)，每日2点执行(0 0 2 * * ?)
            </a-typography-text>
          </div>
        </a-form-item>
        
        <a-form-item v-if="form.scheduleType === 'interval'" label="执行间隔" name="intervalMinutes">
          <a-input-number 
            v-model:value="form.intervalMinutes" 
            :min="5" 
            :max="1440" 
            addon-after="分钟"
            placeholder="执行间隔"
          />
        </a-form-item>
        
        <a-form-item v-if="form.scheduleType === 'daily'" label="执行时间" name="dailyTime">
          <a-time-picker v-model:value="form.dailyTime" format="HH:mm" placeholder="选择执行时间" />
        </a-form-item>

        <a-divider orientation="left">体检配置</a-divider>
        <a-form-item label="体检类型" name="checkTypes">
          <a-checkbox-group v-model:value="form.checkTypes">
            <a-checkbox :value="1">党组（党委）设置</a-checkbox>
            <a-checkbox :value="2">党务干部任免</a-checkbox>
            <a-checkbox :value="3">任务体检</a-checkbox>
            <a-checkbox :value="4">用户信息完整性</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="数据检测维度" name="checkDimensions">
          <a-checkbox-group v-model:value="form.checkDimensions">
            <a-checkbox value="completeness">完整性检查</a-checkbox>
            <a-checkbox value="accuracy">准确性验证</a-checkbox>
            <a-checkbox value="consistency">一致性核验</a-checkbox>
            <a-checkbox value="security">安全性检查</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item label="异常阈值" name="exceptionThreshold">
          <a-input-number 
            v-model:value="form.exceptionThreshold" 
            :min="0" 
            :max="100" 
            addon-after="%"
            placeholder="异常率达到此值时触发告警"
          />
        </a-form-item>

        <a-divider orientation="left">通知配置</a-divider>
        <a-form-item label="启用通知" name="notificationEnabled">
          <a-switch v-model:checked="form.notificationEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.notificationEnabled ? '开启执行结果通知' : '关闭通知' }}
          </span>
        </a-form-item>
        
        <a-form-item v-if="form.notificationEnabled" label="通知方式" name="notificationMethods">
          <a-checkbox-group v-model:value="form.notificationMethods">
            <a-checkbox value="email">邮件</a-checkbox>
            <a-checkbox value="sms">短信</a-checkbox>
            <a-checkbox value="system">系统通知</a-checkbox>
            <a-checkbox value="webhook">Webhook</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
        
        <a-form-item v-if="form.notificationEnabled" label="通知人员" name="notificationRecipients">
          <a-select 
            v-model:value="form.notificationRecipients" 
            mode="multiple" 
            placeholder="请选择通知人员"
          >
            <a-select-option value="admin">系统管理员</a-select-option>
            <a-select-option value="operator1">运维人员1</a-select-option>
            <a-select-option value="operator2">运维人员2</a-select-option>
            <a-select-option value="manager">部门经理</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 4, span: 18 }" style="margin-top: 24px">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button @click="validateSchedule">验证调度</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 任务详情弹窗 -->
    <a-modal 
      title="任务详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="700px"
    >
      <a-descriptions bordered :column="1" v-if="currentTask">
        <a-descriptions-item label="任务名称">{{ currentTask.name }}</a-descriptions-item>
        <a-descriptions-item label="任务描述">{{ currentTask.description }}</a-descriptions-item>
        <a-descriptions-item label="调度配置">
          <div v-if="currentTask.scheduleType === 'cron'">
            <a-tag color="blue">Cron表达式</a-tag>
            <span>{{ currentTask.cronExpression }}</span>
          </div>
          <div v-else-if="currentTask.scheduleType === 'interval'">
            <a-tag color="green">固定间隔</a-tag>
            <span>每{{ currentTask.intervalMinutes }}分钟</span>
          </div>
          <div v-else>
            <a-tag color="orange">每日定时</a-tag>
            <span>{{ currentTask.dailyTime }}</span>
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="体检类型">
          <a-tag v-for="type in currentTask.checkTypes" :key="type" :color="getCheckTypeColor(type)">
            {{ getCheckTypeName(type) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="检测维度">
          <a-tag v-for="dimension in currentTask.checkDimensions" :key="dimension" color="purple">
            {{ getDimensionName(dimension) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="下次执行">{{ formatDateTime(currentTask.nextRun) }}</a-descriptions-item>
        <a-descriptions-item label="上次执行">
          {{ currentTask.lastRun ? formatDateTime(currentTask.lastRun) : '未执行' }}
        </a-descriptions-item>
        <a-descriptions-item label="执行状态">
          <a-tag :color="getStatusColor(currentTask.status)">
            {{ getStatusText(currentTask.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ formatDateTime(currentTask.createTime) }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 执行日志弹窗 -->
    <a-modal 
      title="执行日志" 
      :visible="logVisible" 
      @cancel="logVisible = false" 
      :footer="null" 
      width="900px"
    >
      <div class="log-content">
        <a-timeline>
          <a-timeline-item v-for="log in executionLogs" :key="log.id" :color="getLogColor(log.level)">
            <div class="log-item">
              <div class="log-header">
                <span class="log-time">{{ formatDateTime(log.timestamp) }}</span>
                <a-tag :color="getLogColor(log.level)" size="small">{{ log.level }}</a-tag>
              </div>
              <div class="log-message">{{ log.message }}</div>
              <div v-if="log.details" class="log-details">
                <pre>{{ log.details }}</pre>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  ScheduleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons-vue'

// 接口定义
interface ScheduledTask {
  id: string
  name: string
  description: string
  scheduleType: 'cron' | 'interval' | 'daily'
  cronExpression?: string
  intervalMinutes?: number
  dailyTime?: string
  checkTypes: number[]
  checkDimensions: string[]
  exceptionThreshold: number
  notificationEnabled: boolean
  notificationMethods: string[]
  notificationRecipients: string[]
  enabled: boolean
  status: 'idle' | 'running' | 'success' | 'failed'
  nextRun: string
  lastRun?: string
  createTime: string
  updating?: boolean
  running?: boolean
}

interface ExecutionLog {
  id: string
  level: 'info' | 'warning' | 'error' | 'success'
  timestamp: string
  message: string
  details?: string
}

// 响应式数据
const loading = ref(false)
const runningAll = ref(false)
const formModalVisible = ref(false)
const detailVisible = ref(false)
const logVisible = ref(false)
const submitting = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentTask = ref<ScheduledTask | null>(null)
const selectedRowKeys = ref<string[]>([])
const statusFilter = ref<boolean | undefined>(undefined)

// 表单数据
const form = ref<Partial<ScheduledTask>>({
  name: '',
  description: '',
  scheduleType: 'daily',
  checkTypes: [1, 2, 3, 4],
  checkDimensions: ['completeness', 'accuracy'],
  exceptionThreshold: 10,
  notificationEnabled: true,
  notificationMethods: ['system'],
  notificationRecipients: ['admin'],
  enabled: true
})

// 表单引用
const formRef = ref()

// 模拟数据
const taskList = ref<ScheduledTask[]>([
  {
    id: '1',
    name: '每日数据质量体检',
    description: '每日凌晨2点执行全面数据质量体检',
    scheduleType: 'daily',
    dailyTime: '02:00',
    checkTypes: [1, 2, 3, 4],
    checkDimensions: ['completeness', 'accuracy', 'consistency'],
    exceptionThreshold: 5,
    notificationEnabled: true,
    notificationMethods: ['email', 'system'],
    notificationRecipients: ['admin', 'manager'],
    enabled: true,
    status: 'success',
    nextRun: dayjs().add(1, 'day').hour(2).minute(0).format('YYYY-MM-DD HH:mm:ss'),
    lastRun: dayjs().subtract(1, 'day').hour(2).minute(0).format('YYYY-MM-DD HH:mm:ss'),
    createTime: '2025-01-01 10:00:00'
  },
  {
    id: '2',
    name: '用户信息完整性检查',
    description: '每4小时检查用户信息完整性',
    scheduleType: 'interval',
    intervalMinutes: 240,
    checkTypes: [4],
    checkDimensions: ['completeness'],
    exceptionThreshold: 15,
    notificationEnabled: false,
    notificationMethods: [],
    notificationRecipients: [],
    enabled: true,
    status: 'idle',
    nextRun: dayjs().add(4, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    lastRun: dayjs().subtract(4, 'hour').format('YYYY-MM-DD HH:mm:ss'),
    createTime: '2025-01-02 15:30:00'
  }
])

const executionLogs = ref<ExecutionLog[]>([
  {
    id: '1',
    level: 'info',
    timestamp: dayjs().subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    message: '开始执行定时体检任务'
  },
  {
    id: '2',
    level: 'success',
    timestamp: dayjs().subtract(8, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    message: '党组设置体检完成，发现2个异常项'
  },
  {
    id: '3',
    level: 'warning',
    timestamp: dayjs().subtract(5, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    message: '用户信息完整性检查发现异常率较高',
    details: '异常率：12.5%，超过阈值10%'
  },
  {
    id: '4',
    level: 'success',
    timestamp: dayjs().subtract(2, 'minute').format('YYYY-MM-DD HH:mm:ss'),
    message: '定时体检任务执行完成，共发现5个异常项'
  }
])

// 计算属性
const enabledTaskCount = computed(() => taskList.value.filter(task => task.enabled).length)
const todayExecutionCount = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return taskList.value.filter(task => 
    task.lastRun && dayjs(task.lastRun).format('YYYY-MM-DD') === today
  ).length
})
const totalExceptionsFound = computed(() => 23) // 模拟数据

const filteredTaskList = computed(() => {
  let filtered = [...taskList.value]
  if (statusFilter.value !== undefined) {
    filtered = filtered.filter(task => task.enabled === statusFilter.value)
  }
  return filtered
})

const modalTitle = computed(() => {
  return formMode.value === 'create' ? '创建定时任务' : '编辑定时任务'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '创建任务' : '更新任务'
})

// 表格列定义
const columns = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '任务名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '调度类型', dataIndex: 'scheduleType', key: 'scheduleType', width: 100 },
  { title: '体检类型', key: 'checkTypes', width: 200 },
  { title: '下次执行', key: 'nextRun', width: 150 },
  { title: '上次执行', key: 'lastRun', width: 150 },
  { title: '状态', key: 'status', width: 100, align: 'center' },
  { title: '启用', key: 'enabled', width: 80, align: 'center' },
  { title: '操作', key: 'action', width: 300, align: 'center', fixed: 'right' }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 个任务`
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  scheduleType: [
    { required: true, message: '请选择调度类型', trigger: 'change' }
  ],
  checkTypes: [
    { required: true, type: 'array', min: 1, message: '请选择至少一个体检类型', trigger: 'change' }
  ]
}

// 方法定义
function getRowIndex(record: ScheduledTask) {
  return filteredTaskList.value.findIndex(item => item.id === record.id) + 1
}

function getCheckTypeName(type: number) {
  const map = {
    1: '党组设置',
    2: '干部任免', 
    3: '任务体检',
    4: '用户信息'
  }
  return map[type] || '未知'
}

function getCheckTypeColor(type: number) {
  const colors = { 1: 'blue', 2: 'green', 3: 'orange', 4: 'purple' }
  return colors[type] || 'default'
}

function getDimensionName(dimension: string) {
  const map = {
    completeness: '完整性',
    accuracy: '准确性',
    consistency: '一致性', 
    security: '安全性'
  }
  return map[dimension] || '未知'
}

function getStatusText(status: string) {
  const map = {
    idle: '空闲',
    running: '运行中',
    success: '成功',
    failed: '失败'
  }
  return map[status] || '未知'
}

function getStatusColor(status: string) {
  const map = {
    idle: 'default',
    running: 'processing',
    success: 'success',
    failed: 'error'
  }
  return map[status] || 'default'
}

function getNextRunColor(nextRun: string) {
  const now = dayjs()
  const next = dayjs(nextRun)
  const diff = next.diff(now, 'hour')
  
  if (diff < 1) return '#ff4d4f' // 红色 - 即将执行
  if (diff < 24) return '#faad14' // 橙色 - 今日执行
  return '#52c41a' // 绿色 - 正常
}

function getLogColor(level: string) {
  const map = {
    info: 'blue',
    success: 'green',
    warning: 'orange',
    error: 'red'
  }
  return map[level] || 'default'
}

function formatDateTime(dateTime: string) {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

async function refreshData() {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('数据刷新成功')
  } finally {
    loading.value = false
  }
}

async function runAllTasks() {
  runningAll.value = true
  try {
    // 模拟执行所有任务
    await new Promise(resolve => setTimeout(resolve, 3000))
    message.success('所有定时任务执行完成')
  } finally {
    runningAll.value = false
  }
}

function showCreateModal() {
  formMode.value = 'create'
  form.value = {
    name: '',
    description: '',
    scheduleType: 'daily',
    checkTypes: [1, 2, 3, 4],
    checkDimensions: ['completeness', 'accuracy'],
    exceptionThreshold: 10,
    notificationEnabled: true,
    notificationMethods: ['system'],
    notificationRecipients: ['admin'],
    enabled: true
  }
  formModalVisible.value = true
}

function editTask(record: ScheduledTask) {
  formMode.value = 'edit'
  form.value = { ...record }
  currentTask.value = record
  formModalVisible.value = true
}

function showDetail(record: ScheduledTask) {
  currentTask.value = record
  detailVisible.value = true
}

async function toggleTaskStatus(record: ScheduledTask) {
  record.updating = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    record.enabled = !record.enabled
    message.success(`任务已${record.enabled ? '启用' : '禁用'}`)
  } finally {
    record.updating = false
  }
}

async function runTask(record: ScheduledTask) {
  record.running = true
  record.status = 'running'
  try {
    // 模拟任务执行
    await new Promise(resolve => setTimeout(resolve, 3000))
    record.status = 'success'
    record.lastRun = dayjs().format('YYYY-MM-DD HH:mm:ss')
    record.nextRun = dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
    message.success(`任务 ${record.name} 执行成功`)
  } catch (error) {
    record.status = 'failed'
    message.error(`任务 ${record.name} 执行失败`)
  } finally {
    record.running = false
  }
}

function deleteTask(id: string) {
  const index = taskList.value.findIndex(item => item.id === id)
  if (index > -1) {
    taskList.value.splice(index, 1)
    message.success('定时任务删除成功')
  }
}

function viewLogs(record: ScheduledTask) {
  currentTask.value = record
  logVisible.value = true
}

async function onSubmit() {
  try {
    submitting.value = true
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      const newTask: ScheduledTask = {
        ...form.value as ScheduledTask,
        id: Date.now().toString(),
        status: 'idle',
        nextRun: dayjs().add(1, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
      taskList.value.unshift(newTask)
      message.success('定时任务创建成功')
    } else {
      const index = taskList.value.findIndex(item => item.id === currentTask.value?.id)
      if (index > -1) {
        taskList.value[index] = { ...taskList.value[index], ...form.value }
        message.success('定时任务更新成功')
      }
    }
    
    formModalVisible.value = false
  } catch (error) {
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

function validateSchedule() {
  if (form.value.scheduleType === 'cron' && form.value.cronExpression) {
    // 简单验证Cron表达式格式
    const cronParts = form.value.cronExpression.split(' ')
    if (cronParts.length === 6 || cronParts.length === 7) {
      message.success('Cron表达式格式正确')
    } else {
      message.error('Cron表达式格式错误')
    }
  } else {
    message.success('调度配置验证通过')
  }
}

function onSelectChange(selectedKeys: string[]) {
  selectedRowKeys.value = selectedKeys
}

function batchOperation() {
  message.info('批量操作功能开发中...')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style lang="scss" scoped>
.scheduled-check-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;

    .stat-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .task-list-section {
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }
  }

  .cron-help {
    margin-top: 4px;
  }

  .log-content {
    max-height: 500px;
    overflow-y: auto;

    .log-item {
      .log-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 4px;

        .log-time {
          font-size: 12px;
          color: #666;
        }
      }

      .log-message {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
      }

      .log-details {
        background: #f5f5f5;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;

        pre {
          margin: 0;
          font-family: monospace;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .scheduled-check-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
}
</style>