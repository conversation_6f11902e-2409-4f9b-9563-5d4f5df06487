<template>
  <div class="activity-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>活动管理</h1>
        <p>统一的活动组织、参与和监控平台</p>
      </div>
      <div class="header-stats">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="活动总数" :value="stats.totalActivities" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="进行中活动" :value="stats.ongoingActivities" :value-style="{ color: '#1890ff' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="今日报名" :value="stats.todayRegistrations" :value-style="{ color: '#52c41a' }" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="参与率" :value="stats.participationRate" suffix="%" :value-style="{ color: '#faad14' }" />
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
      <a-card title="快速操作" :bordered="false">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToActivityList">
              <div class="action-icon">
                <calendar-outlined />
              </div>
              <div class="action-content">
                <h3>活动列表</h3>
                <p>查看和管理所有活动</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="createActivity">
              <div class="action-icon">
                <plus-outlined />
              </div>
              <div class="action-content">
                <h3>创建活动</h3>
                <p>发起新的活动</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToParticipationManage">
              <div class="action-icon">
                <team-outlined />
              </div>
              <div class="action-content">
                <h3>参与管理</h3>
                <p>管理活动参与情况</p>
              </div>
            </div>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="action-card" @click="goToAnalytics">
              <div class="action-icon">
                <bar-chart-outlined />
              </div>
              <div class="action-content">
                <h3>数据分析</h3>
                <p>活动统计和分析报告</p>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 功能模块区域 -->
    <div class="feature-modules">
      <a-row :gutter="[24, 24]">
        <!-- 活动管理服务 -->
        <a-col :xs="24" :lg="12">
          <a-card title="活动管理服务" :bordered="false">
            <template #extra>
              <a @click="goToActivityList">查看更多</a>
            </template>
            
            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>智能展示</h4>
                    <p>按时间、标签、标题智能排序展示</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>动态计算</h4>
                    <p>活动时间智能计算和状态自动更新</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>权限控制</h4>
                    <p>完善的活动查询数据权限验证</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToActivityList">
                管理活动
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 参与功能模块 -->
        <a-col :xs="24" :lg="12">
          <a-card title="参与功能" :bordered="false">
            <template #extra>
              <a @click="goToParticipationManage">查看更多</a>
            </template>

            <div class="module-content">
              <div class="feature-list">
                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>在线报名</h4>
                    <p>支持活动报名和信息校验</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>现场签到</h4>
                    <p>地理位置和时间验证签到</p>
                  </div>
                </div>

                <div class="feature-item">
                  <check-circle-outlined class="feature-icon" />
                  <div class="feature-text">
                    <h4>问卷投票</h4>
                    <p>在线问卷调查和投票功能</p>
                  </div>
                </div>
              </div>

              <a-button type="primary" block @click="goToParticipationManage">
                管理参与
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities">
      <a-card title="最近活动" :bordered="false">
        <template #extra>
          <a @click="goToActivityList">查看全部</a>
        </template>
        
        <a-spin :spinning="loading">
          <div v-if="recentActivities.length === 0" class="empty-state">
            <a-empty description="暂无活动动态">
              <template #image>
                <calendar-outlined style="font-size: 48px; color: #d9d9d9;" />
              </template>
            </a-empty>
          </div>
          
          <div v-else class="activity-timeline">
            <a-timeline>
              <a-timeline-item
                v-for="activity in recentActivities"
                :key="activity.id"
                :color="getActivityColor(activity.type)"
              >
                <div class="timeline-content">
                  <div class="timeline-header">
                    <span class="activity-type">{{ getActivityTypeText(activity.type) }}</span>
                    <span class="activity-time">{{ activity.timestamp }}</span>
                  </div>
                  <div class="timeline-body">
                    <h4>{{ activity.activityTitle }}</h4>
                    <p>{{ activity.description }}</p>
                    <span class="activity-user">{{ activity.userName }}</span>
                  </div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </a-spin>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  CalendarOutlined,
  PlusOutlined,
  TeamOutlined,
  BarChartOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import type { ActivityStatistics, ActivityActivity } from '@/types/activity'
import { MockActivityService } from './mock/data'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 统计数据
const stats = reactive<ActivityStatistics>({
  totalActivities: 0,
  upcomingActivities: 0,
  ongoingActivities: 0,
  endedActivities: 0,
  cancelledActivities: 0,
  totalParticipants: 0,
  todayRegistrations: 0,
  participationRate: 0,
  averageRating: 0,
  activitiesByType: {} as any,
  activitiesByStatus: {} as any,
  participationsByType: {} as any,
  recentActivities: [],
  popularActivities: [],
  topOrganizers: []
})

// 最近活动
const recentActivities = ref<ActivityActivity[]>([])

// 导航方法
const goToActivityList = () => {
  router.push('/activity-management/list')
}

const createActivity = () => {
  message.info('创建活动功能开发中...')
}

const goToParticipationManage = () => {
  message.info('参与管理功能开发中...')
}

const goToAnalytics = () => {
  message.info('数据分析功能开发中...')
}

// 工具方法
const getActivityColor = (type: string) => {
  const colors = {
    create: 'blue',
    update: 'green',
    register: 'orange',
    check_in: 'purple',
    complete: 'cyan',
    cancel: 'red'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getActivityTypeText = (type: string) => {
  const texts = {
    create: '创建活动',
    update: '更新活动',
    register: '活动报名',
    check_in: '活动签到',
    complete: '完成活动',
    cancel: '取消活动'
  }
  return texts[type as keyof typeof texts] || type
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 加载统计数据
    const statisticsData = await MockActivityService.getActivityStatistics()
    Object.assign(stats, statisticsData)
    
    // 加载最近活动
    recentActivities.value = statisticsData.recentActivities
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.activity-management {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    padding: 32px;
    margin-bottom: 24px;
    color: white;

    .header-content {
      text-align: center;
      margin-bottom: 32px;

      h1 {
        font-size: 32px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: white;
      }

      p {
        font-size: 16px;
        margin: 0;
        opacity: 0.9;
      }
    }

    .header-stats {
      :deep(.ant-statistic) {
        .ant-statistic-title {
          color: rgba(255, 255, 255, 0.85);
          font-size: 14px;
        }

        .ant-statistic-content {
          color: white;
          font-size: 24px;
          font-weight: 600;
        }
      }
    }
  }

  .quick-actions,
  .feature-modules,
  .recent-activities {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;

        .ant-card-head-title {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }
  }

  .action-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      transform: translateY(-2px);
    }

    .action-icon {
      font-size: 32px;
      color: #1890ff;
      margin-right: 16px;
    }

    .action-content {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #8c8c8c;
      }
    }
  }

  .module-content {
    .feature-list {
      margin-bottom: 24px;

      .feature-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .feature-icon {
          font-size: 16px;
          color: #52c41a;
          margin-right: 12px;
          margin-top: 2px;
        }

        .feature-text {
          flex: 1;

          h4 {
            margin: 0 0 4px 0;
            font-size: 14px;
            font-weight: 600;
            color: #262626;
          }

          p {
            margin: 0;
            font-size: 13px;
            color: #8c8c8c;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }

  .activity-timeline {
    .timeline-content {
      .timeline-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .activity-type {
          background: #f0f0f0;
          color: #666;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
        }

        .activity-time {
          font-size: 12px;
          color: #999;
        }
      }

      .timeline-body {
        h4 {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: #262626;
        }

        p {
          margin: 0 0 8px 0;
          font-size: 13px;
          color: #666;
          line-height: 1.4;
        }

        .activity-user {
          font-size: 12px;
          color: #1890ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-management {
    padding: 16px;

    .page-header {
      padding: 24px 16px;

      .header-content h1 {
        font-size: 24px;
      }

      .header-stats {
        :deep(.ant-col) {
          margin-bottom: 16px;
        }
      }
    }

    .action-card {
      padding: 16px;

      .action-icon {
        font-size: 24px;
        margin-right: 12px;
      }
    }
  }
}
</style>
