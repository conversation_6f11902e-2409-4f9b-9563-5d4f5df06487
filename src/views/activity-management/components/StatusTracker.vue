<template>
  <div class="status-tracker">
    <div v-if="title" class="tracker-title">
      <h4>{{ title }}</h4>
    </div>

    <a-steps 
      :direction="direction" 
      :size="size" 
      :current="currentStep"
      :status="currentStatus"
    >
      <a-step
        v-for="(step, index) in steps"
        :key="step.key"
        :title="step.title"
        :status="getStepStatus(index)"
        :icon="getStepIcon(step, index)"
      >
        <template #description>
          <div class="step-description">
            <div v-if="step.description" class="desc-text">
              {{ step.description }}
            </div>
            <div v-if="step.time" class="desc-time">
              <clock-circle-outlined />
              {{ formatTime(step.time) }}
            </div>
            <div v-if="step.user" class="desc-user">
              <user-outlined />
              {{ step.user }}
            </div>
            <div v-if="step.status && showStatus" class="desc-status">
              <a-tag :color="getStatusColor(step.status)" size="small">
                {{ getStatusText(step.status) }}
              </a-tag>
            </div>
            <div v-if="step.notes" class="desc-notes">
              {{ step.notes }}
            </div>
          </div>
        </template>
      </a-step>
    </a-steps>

    <!-- 操作按钮 -->
    <div v-if="showActions && actions.length > 0" class="tracker-actions">
      <a-space>
        <a-button
          v-for="action in actions"
          :key="action.key"
          :type="action.type || 'default'"
          :size="action.size || 'small'"
          :disabled="action.disabled"
          :loading="action.loading"
          @click="handleAction(action)"
        >
          <component v-if="action.icon" :is="action.icon" />
          {{ action.label }}
        </a-button>
      </a-space>
    </div>

    <!-- 详细信息 -->
    <div v-if="showDetails && currentStepData" class="tracker-details">
      <a-card size="small" :bordered="false">
        <template #title>
          <span class="details-title">当前步骤详情</span>
        </template>
        
        <a-descriptions :column="1" size="small">
          <a-descriptions-item 
            v-for="(value, key) in currentStepData.details"
            :key="key"
            :label="getDetailLabel(key)"
          >
            {{ value }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 进度统计 -->
    <div v-if="showProgress" class="tracker-progress">
      <div class="progress-info">
        <span class="progress-text">
          进度：{{ completedSteps }}/{{ totalSteps }}
        </span>
        <span class="progress-percent">
          {{ Math.round((completedSteps / totalSteps) * 100) }}%
        </span>
      </div>
      <a-progress 
        :percent="Math.round((completedSteps / totalSteps) * 100)"
        :stroke-color="getProgressColor()"
        size="small"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  ClockCircleOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  CloseCircleOutlined
} from '@ant-design/icons-vue'
import type { ParticipationStatus } from '@/types/activity'
import dayjs from 'dayjs'

// 步骤数据接口
interface Step {
  key: string
  title: string
  description?: string
  time?: string
  user?: string
  status?: ParticipationStatus | 'waiting' | 'processing' | 'completed' | 'error'
  notes?: string
  details?: Record<string, any>
}

// 操作按钮接口
interface Action {
  key: string
  label: string
  type?: 'primary' | 'default' | 'dashed' | 'link' | 'text'
  size?: 'small' | 'middle' | 'large'
  icon?: any
  disabled?: boolean
  loading?: boolean
}

// Props
interface Props {
  steps: Step[]
  currentStep?: number
  currentStatus?: 'wait' | 'process' | 'finish' | 'error'
  direction?: 'horizontal' | 'vertical'
  size?: 'default' | 'small'
  title?: string
  showStatus?: boolean
  showActions?: boolean
  showDetails?: boolean
  showProgress?: boolean
  actions?: Action[]
}

const props = withDefaults(defineProps<Props>(), {
  currentStep: 0,
  currentStatus: 'process',
  direction: 'vertical',
  size: 'small',
  showStatus: true,
  showActions: false,
  showDetails: false,
  showProgress: false,
  actions: () => []
})

// Emits
const emit = defineEmits<{
  action: [action: Action]
  stepClick: [step: Step, index: number]
}>()

// 计算属性
const totalSteps = computed(() => props.steps.length)

const completedSteps = computed(() => {
  return props.steps.filter((step, index) => {
    return index < props.currentStep || step.status === 'completed'
  }).length
})

const currentStepData = computed(() => {
  if (props.currentStep >= 0 && props.currentStep < props.steps.length) {
    return props.steps[props.currentStep]
  }
  return null
})

// 工具方法
const getStepStatus = (index: number) => {
  const step = props.steps[index]
  
  if (step.status) {
    const statusMap = {
      waiting: 'wait',
      processing: 'process',
      completed: 'finish',
      error: 'error',
      pending: 'wait',
      approved: 'finish',
      rejected: 'error',
      cancelled: 'error'
    }
    return statusMap[step.status as keyof typeof statusMap] || 'wait'
  }
  
  if (index < props.currentStep) return 'finish'
  if (index === props.currentStep) return props.currentStatus
  return 'wait'
}

const getStepIcon = (step: Step, index: number) => {
  const status = getStepStatus(index)
  
  if (step.status === 'processing' || (index === props.currentStep && props.currentStatus === 'process')) {
    return LoadingOutlined
  }
  
  switch (status) {
    case 'finish':
      return CheckCircleOutlined
    case 'error':
      return CloseCircleOutlined
    case 'process':
      return LoadingOutlined
    default:
      return undefined
  }
}

const getStatusColor = (status: string) => {
  const colors = {
    waiting: 'default',
    processing: 'blue',
    completed: 'green',
    error: 'red',
    pending: 'orange',
    approved: 'green',
    rejected: 'red',
    cancelled: 'default'
  }
  return colors[status as keyof typeof colors] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    waiting: '等待中',
    processing: '处理中',
    completed: '已完成',
    error: '失败',
    pending: '待处理',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消'
  }
  return texts[status as keyof typeof texts] || status
}

const getDetailLabel = (key: string) => {
  const labels = {
    time: '时间',
    user: '操作人',
    location: '地点',
    notes: '备注',
    reason: '原因',
    result: '结果'
  }
  return labels[key as keyof typeof labels] || key
}

const getProgressColor = () => {
  const percent = Math.round((completedSteps.value / totalSteps.value) * 100)
  if (percent >= 100) return '#52c41a'
  if (percent >= 70) return '#1890ff'
  if (percent >= 30) return '#faad14'
  return '#ff4d4f'
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

// 事件处理
const handleAction = (action: Action) => {
  emit('action', action)
}
</script>

<style lang="scss" scoped>
.status-tracker {
  .tracker-title {
    margin-bottom: 16px;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  :deep(.ant-steps) {
    .ant-steps-item-title {
      font-weight: 500;
    }

    .ant-steps-item-description {
      margin-top: 4px;
    }
  }

  .step-description {
    .desc-text {
      color: #595959;
      font-size: 13px;
      line-height: 1.4;
      margin-bottom: 4px;
    }

    .desc-time,
    .desc-user {
      display: flex;
      align-items: center;
      color: #8c8c8c;
      font-size: 12px;
      margin-bottom: 2px;

      .anticon {
        margin-right: 4px;
      }
    }

    .desc-status {
      margin: 4px 0;
    }

    .desc-notes {
      color: #8c8c8c;
      font-size: 12px;
      font-style: italic;
      margin-top: 4px;
    }
  }

  .tracker-actions {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .tracker-details {
    margin-top: 16px;

    .details-title {
      font-size: 14px;
      font-weight: 500;
    }

    :deep(.ant-card) {
      background: #fafafa;

      .ant-card-body {
        padding: 12px;
      }

      .ant-descriptions-item-label {
        font-weight: 500;
        color: #595959;
      }

      .ant-descriptions-item-content {
        color: #262626;
      }
    }
  }

  .tracker-progress {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .progress-text {
        font-size: 13px;
        color: #595959;
      }

      .progress-percent {
        font-size: 13px;
        font-weight: 500;
        color: #262626;
      }
    }
  }
}

// 水平方向样式调整
.status-tracker {
  &.horizontal {
    :deep(.ant-steps-horizontal) {
      .ant-steps-item-description {
        max-width: 140px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .status-tracker {
    .tracker-actions {
      :deep(.ant-space) {
        width: 100%;

        .ant-space-item {
          flex: 1;

          .ant-btn {
            width: 100%;
          }
        }
      }
    }

    .step-description {
      .desc-text {
        font-size: 12px;
      }

      .desc-time,
      .desc-user {
        font-size: 11px;
      }
    }
  }
}
</style>
