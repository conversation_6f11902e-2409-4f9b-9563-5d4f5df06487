<template>
  <div class="activity-status">
    <!-- 状态徽章 -->
    <div class="status-badge">
      <a-badge 
        :status="getStatusBadge(status)" 
        :text="getStatusText(status)"
        class="status-indicator"
      />
      <a-tag 
        v-if="showTag"
        :color="getStatusColor(status)" 
        class="status-tag"
      >
        {{ getStatusText(status) }}
      </a-tag>
    </div>

    <!-- 倒计时 -->
    <div v-if="showCountdown && (status === 'upcoming' || status === 'ongoing')" class="countdown-section">
      <div class="countdown-label">
        {{ status === 'upcoming' ? '距离开始' : '距离结束' }}
      </div>
      <div class="countdown-time">
        <a-statistic-countdown 
          :value="getCountdownValue()"
          :format="countdownFormat"
          @finish="handleCountdownFinish"
        />
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="showProgress && maxParticipants" class="progress-section">
      <div class="progress-label">
        <span>参与进度</span>
        <span class="progress-text">{{ currentParticipants }}/{{ maxParticipants }}</span>
      </div>
      <a-progress 
        :percent="getParticipationRate()" 
        :stroke-color="getProgressColor()"
        :show-info="false"
        size="small"
      />
    </div>

    <!-- 状态描述 -->
    <div v-if="showDescription" class="status-description">
      <div class="description-text">
        {{ getStatusDescription() }}
      </div>
      <div v-if="lastUpdateTime" class="update-time">
        最后更新：{{ formatTime(lastUpdateTime) }}
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions" class="status-actions">
      <a-space>
        <a-button 
          v-if="status === 'upcoming'"
          type="primary" 
          size="small"
          @click="$emit('participate')"
        >
          立即报名
        </a-button>
        <a-button 
          v-else-if="status === 'ongoing'"
          type="primary" 
          size="small"
          @click="$emit('checkin')"
        >
          现场签到
        </a-button>
        <a-button 
          v-if="status === 'ongoing'"
          size="small"
          @click="$emit('survey')"
        >
          填写问卷
        </a-button>
        <a-button 
          v-if="status === 'ended'"
          size="small"
          @click="$emit('review')"
        >
          查看回顾
        </a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { ActivityStatus } from '@/types/activity'
import dayjs from 'dayjs'

// Props
interface Props {
  status: ActivityStatus
  startTime?: string
  endTime?: string
  currentParticipants?: number
  maxParticipants?: number
  lastUpdateTime?: string
  showTag?: boolean
  showCountdown?: boolean
  showProgress?: boolean
  showDescription?: boolean
  showActions?: boolean
  countdownFormat?: string
}

const props = withDefaults(defineProps<Props>(), {
  showTag: false,
  showCountdown: true,
  showProgress: true,
  showDescription: true,
  showActions: false,
  countdownFormat: 'D 天 H 时 m 分 s 秒'
})

// Emits
const emit = defineEmits<{
  participate: []
  checkin: []
  survey: []
  review: []
  countdownFinish: []
}>()

// 工具方法
const getStatusBadge = (status: ActivityStatus) => {
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusColor = (status: ActivityStatus) => {
  const colors = {
    upcoming: 'orange',
    ongoing: 'blue',
    ended: 'green',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status: ActivityStatus) => {
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getStatusDescription = () => {
  const descriptions = {
    upcoming: '活动尚未开始，请耐心等待',
    ongoing: '活动正在进行中，欢迎参与',
    ended: '活动已圆满结束，感谢参与',
    cancelled: '活动已取消，如有疑问请联系组织者'
  }
  return descriptions[props.status] || ''
}

const getCountdownValue = () => {
  if (props.status === 'upcoming' && props.startTime) {
    return dayjs(props.startTime).valueOf()
  } else if (props.status === 'ongoing' && props.endTime) {
    return dayjs(props.endTime).valueOf()
  }
  return Date.now()
}

const getParticipationRate = () => {
  if (!props.maxParticipants || !props.currentParticipants) return 0
  return Math.round((props.currentParticipants / props.maxParticipants) * 100)
}

const getProgressColor = () => {
  const rate = getParticipationRate()
  if (rate >= 90) return '#ff4d4f'
  if (rate >= 70) return '#faad14'
  return '#52c41a'
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 事件处理
const handleCountdownFinish = () => {
  emit('countdownFinish')
}
</script>

<style lang="scss" scoped>
.activity-status {
  .status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .status-indicator {
      :deep(.ant-badge-status-text) {
        font-weight: 500;
        color: #262626;
      }
    }

    .status-tag {
      margin: 0;
      font-weight: 500;
    }
  }

  .countdown-section {
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    text-align: center;

    .countdown-label {
      font-size: 13px;
      color: #8c8c8c;
      margin-bottom: 8px;
    }

    .countdown-time {
      :deep(.ant-statistic) {
        .ant-statistic-content {
          font-size: 18px;
          font-weight: 600;
          color: #1890ff;
        }
      }
    }
  }

  .progress-section {
    margin-bottom: 16px;

    .progress-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
      color: #595959;

      .progress-text {
        font-weight: 500;
        color: #262626;
      }
    }
  }

  .status-description {
    margin-bottom: 16px;
    padding: 12px;
    background: #fafafa;
    border-radius: 6px;
    border-left: 3px solid #1890ff;

    .description-text {
      font-size: 14px;
      color: #595959;
      line-height: 1.5;
      margin-bottom: 4px;
    }

    .update-time {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .status-actions {
    .ant-btn {
      font-weight: 500;
    }
  }
}

// 不同状态的样式变化
.activity-status {
  &.status-upcoming {
    .countdown-section {
      background: #fff7e6;
      border: 1px solid #ffd591;

      .countdown-time {
        :deep(.ant-statistic-content) {
          color: #fa8c16;
        }
      }
    }

    .status-description {
      border-left-color: #fa8c16;
    }
  }

  &.status-ongoing {
    .countdown-section {
      background: #e6f7ff;
      border: 1px solid #91d5ff;

      .countdown-time {
        :deep(.ant-statistic-content) {
          color: #1890ff;
        }
      }
    }

    .status-description {
      border-left-color: #1890ff;
    }
  }

  &.status-ended {
    .status-description {
      background: #f6ffed;
      border-left-color: #52c41a;
    }
  }

  &.status-cancelled {
    .status-description {
      background: #fff2f0;
      border-left-color: #ff4d4f;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-status {
    .countdown-section {
      padding: 8px;

      .countdown-time {
        :deep(.ant-statistic-content) {
          font-size: 16px;
        }
      }
    }

    .status-description {
      padding: 8px;

      .description-text {
        font-size: 13px;
      }
    }

    .status-actions {
      :deep(.ant-space) {
        width: 100%;

        .ant-space-item {
          flex: 1;

          .ant-btn {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
