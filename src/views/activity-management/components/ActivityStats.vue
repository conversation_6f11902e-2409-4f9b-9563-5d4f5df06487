<template>
  <div class="activity-stats">
    <!-- 统计卡片 -->
    <div v-if="showCards" class="stats-cards">
      <a-row :gutter="[16, 16]">
        <a-col 
          v-for="card in statsCards" 
          :key="card.key"
          :xs="24" 
          :sm="12" 
          :md="6"
        >
          <a-card :bordered="false" class="stats-card">
            <a-statistic
              :title="card.title"
              :value="card.value"
              :precision="card.precision"
              :suffix="card.suffix"
              :prefix="card.prefix"
              :value-style="{ color: card.color }"
            >
              <template v-if="card.icon" #prefix>
                <component :is="card.icon" />
              </template>
            </a-statistic>
            <div v-if="card.trend" class="stats-trend">
              <span :class="['trend-text', card.trend.type]">
                <component :is="getTrendIcon(card.trend.type)" />
                {{ card.trend.value }}{{ card.trend.suffix || '%' }}
              </span>
              <span class="trend-desc">{{ card.trend.description }}</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div v-if="showCharts" class="stats-charts">
      <a-row :gutter="[24, 24]">
        <!-- 活动类型分布 -->
        <a-col v-if="showTypeChart" :xs="24" :lg="12">
          <a-card title="活动类型分布" :bordered="false">
            <div class="chart-container">
              <div class="chart-placeholder">
                <pie-chart-outlined class="chart-icon" />
                <div class="chart-text">活动类型分布图</div>
                <div class="chart-data">
                  <div 
                    v-for="(value, type) in stats.activitiesByType" 
                    :key="type"
                    class="data-item"
                  >
                    <a-tag :color="getTypeColor(type)">{{ getTypeText(type) }}</a-tag>
                    <span class="data-value">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 参与趋势 -->
        <a-col v-if="showTrendChart" :xs="24" :lg="12">
          <a-card title="参与趋势" :bordered="false">
            <div class="chart-container">
              <div class="chart-placeholder">
                <line-chart-outlined class="chart-icon" />
                <div class="chart-text">参与趋势图</div>
                <div class="trend-summary">
                  <div class="summary-item">
                    <span class="summary-label">总参与人数</span>
                    <span class="summary-value">{{ stats.totalParticipants }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">平均参与率</span>
                    <span class="summary-value">{{ stats.participationRate }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 活动状态统计 -->
        <a-col v-if="showStatusChart" :xs="24" :lg="12">
          <a-card title="活动状态统计" :bordered="false">
            <div class="status-stats">
              <div 
                v-for="(value, status) in stats.activitiesByStatus"
                :key="status"
                class="status-item"
              >
                <div class="status-info">
                  <a-badge 
                    :status="getStatusBadge(status)" 
                    :text="getStatusText(status)"
                  />
                  <span class="status-count">{{ value }}</span>
                </div>
                <div class="status-progress">
                  <a-progress 
                    :percent="getStatusPercent(value)"
                    :stroke-color="getStatusColor(status)"
                    size="small"
                    :show-info="false"
                  />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 热门活动 -->
        <a-col v-if="showPopularActivities" :xs="24" :lg="12">
          <a-card title="热门活动" :bordered="false">
            <template #extra>
              <a @click="$emit('viewMore', 'popular')">查看更多</a>
            </template>
            <div class="popular-activities">
              <div 
                v-for="(activity, index) in stats.popularActivities.slice(0, 5)"
                :key="activity.id"
                class="popular-item"
                @click="$emit('viewActivity', activity)"
              >
                <div class="popular-rank">{{ index + 1 }}</div>
                <div class="popular-info">
                  <div class="popular-title">{{ activity.title }}</div>
                  <div class="popular-meta">
                    <span>{{ activity.currentParticipants }}人参与</span>
                    <a-tag :color="getTypeColor(activity.type)" size="small">
                      {{ getTypeText(activity.type) }}
                    </a-tag>
                  </div>
                </div>
                <div class="popular-status">
                  <a-badge 
                    :status="getStatusBadge(activity.status)" 
                    :text="getStatusText(activity.status)"
                  />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 组织者排行 -->
    <div v-if="showOrganizerRank" class="organizer-rank">
      <a-card title="组织者排行" :bordered="false">
        <template #extra>
          <a @click="$emit('viewMore', 'organizers')">查看更多</a>
        </template>
        <a-list 
          :data-source="stats.topOrganizers.slice(0, 5)"
          size="small"
        >
          <template #renderItem="{ item, index }">
            <a-list-item>
              <a-list-item-meta>
                <template #avatar>
                  <a-avatar :style="{ backgroundColor: getRankColor(index) }">
                    {{ index + 1 }}
                  </a-avatar>
                </template>
                <template #title>
                  {{ item.organizerName }}
                </template>
                <template #description>
                  <a-space>
                    <span>{{ item.totalActivities }}个活动</span>
                    <span>{{ item.totalParticipants }}人参与</span>
                    <span>评分{{ item.averageRating }}</span>
                  </a-space>
                </template>
              </a-list-item-meta>
              <template #actions>
                <a-rate 
                  :value="item.averageRating" 
                  :count="5" 
                  disabled 
                  size="small"
                />
              </template>
            </a-list-item>
          </template>
        </a-list>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  CalendarOutlined,
  TeamOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  PieChartOutlined,
  LineChartOutlined,
  CaretUpOutlined,
  CaretDownOutlined
} from '@ant-design/icons-vue'
import type { 
  ActivityStatistics, 
  Activity,
  ActivityType, 
  ActivityStatus 
} from '@/types/activity'

// Props
interface Props {
  stats: ActivityStatistics
  showCards?: boolean
  showCharts?: boolean
  showTypeChart?: boolean
  showTrendChart?: boolean
  showStatusChart?: boolean
  showPopularActivities?: boolean
  showOrganizerRank?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showCards: true,
  showCharts: true,
  showTypeChart: true,
  showTrendChart: true,
  showStatusChart: true,
  showPopularActivities: true,
  showOrganizerRank: true,
  loading: false
})

// Emits
const emit = defineEmits<{
  viewMore: [type: string]
  viewActivity: [activity: Activity]
}>()

// 计算属性
const statsCards = computed(() => [
  {
    key: 'total',
    title: '活动总数',
    value: props.stats.totalActivities,
    color: '#1890ff',
    icon: CalendarOutlined,
    trend: {
      type: 'up',
      value: 12,
      description: '较上月'
    }
  },
  {
    key: 'participants',
    title: '总参与人数',
    value: props.stats.totalParticipants,
    color: '#52c41a',
    icon: TeamOutlined,
    trend: {
      type: 'up',
      value: 8,
      description: '较上月'
    }
  },
  {
    key: 'rate',
    title: '参与率',
    value: props.stats.participationRate,
    suffix: '%',
    color: '#faad14',
    icon: TrophyOutlined,
    trend: {
      type: 'down',
      value: 2,
      description: '较上月'
    }
  },
  {
    key: 'rating',
    title: '平均评分',
    value: props.stats.averageRating,
    precision: 1,
    color: '#722ed1',
    icon: RiseOutlined,
    trend: {
      type: 'up',
      value: 0.3,
      suffix: '分',
      description: '较上月'
    }
  }
])

// 工具方法
const getTypeColor = (type: string) => {
  const colors = {
    conference: 'blue',
    training: 'green',
    workshop: 'orange',
    seminar: 'purple',
    competition: 'red',
    exhibition: 'cyan',
    social: 'pink',
    other: 'default'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getTypeText = (type: string) => {
  const texts = {
    conference: '会议',
    training: '培训',
    workshop: '研讨会',
    seminar: '讲座',
    competition: '竞赛',
    exhibition: '展览',
    social: '社交活动',
    other: '其他'
  }
  return texts[type as keyof typeof texts] || type
}

const getStatusBadge = (status: string) => {
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status as keyof typeof badges] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status as keyof typeof texts] || status
}

const getStatusColor = (status: string) => {
  const colors = {
    upcoming: '#faad14',
    ongoing: '#1890ff',
    ended: '#52c41a',
    cancelled: '#ff4d4f'
  }
  return colors[status as keyof typeof colors] || '#d9d9d9'
}

const getStatusPercent = (value: number) => {
  const total = props.stats.totalActivities
  return total > 0 ? Math.round((value / total) * 100) : 0
}

const getTrendIcon = (type: string) => {
  return type === 'up' ? CaretUpOutlined : CaretDownOutlined
}

const getRankColor = (index: number) => {
  const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068']
  return colors[index] || '#1890ff'
}
</script>

<style lang="scss" scoped>
.activity-stats {
  .stats-cards {
    margin-bottom: 24px;

    .stats-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      :deep(.ant-card-body) {
        padding: 20px;
      }

      :deep(.ant-statistic) {
        .ant-statistic-title {
          font-size: 14px;
          color: #8c8c8c;
          margin-bottom: 8px;
        }

        .ant-statistic-content {
          font-size: 24px;
          font-weight: 600;
        }
      }

      .stats-trend {
        margin-top: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .trend-text {
          font-size: 12px;
          font-weight: 500;

          &.up {
            color: #52c41a;
          }

          &.down {
            color: #ff4d4f;
          }

          .anticon {
            margin-right: 2px;
          }
        }

        .trend-desc {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .stats-charts {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-head-title {
        font-size: 16px;
        font-weight: 600;
      }
    }

    .chart-container {
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;

      .chart-placeholder {
        text-align: center;
        color: #8c8c8c;

        .chart-icon {
          font-size: 48px;
          margin-bottom: 12px;
          color: #d9d9d9;
        }

        .chart-text {
          font-size: 14px;
          margin-bottom: 16px;
        }

        .chart-data {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: center;

          .data-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .data-value {
              font-weight: 500;
              color: #262626;
            }
          }
        }

        .trend-summary {
          display: flex;
          gap: 24px;
          justify-content: center;

          .summary-item {
            text-align: center;

            .summary-label {
              display: block;
              font-size: 12px;
              color: #8c8c8c;
              margin-bottom: 4px;
            }

            .summary-value {
              font-size: 18px;
              font-weight: 600;
              color: #262626;
            }
          }
        }
      }
    }

    .status-stats {
      .status-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .status-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .status-count {
            font-weight: 500;
            color: #262626;
          }
        }
      }
    }

    .popular-activities {
      .popular-item {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #fafafa;
        }

        &:last-child {
          border-bottom: none;
        }

        .popular-rank {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #1890ff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          margin-right: 12px;
        }

        .popular-info {
          flex: 1;

          .popular-title {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .popular-meta {
            font-size: 12px;
            color: #8c8c8c;

            .ant-tag {
              margin-left: 8px;
            }
          }
        }

        .popular-status {
          margin-left: 12px;
        }
      }
    }
  }

  .organizer-rank {
    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    :deep(.ant-list-item) {
      padding: 12px 0;

      .ant-list-item-meta-title {
        font-size: 14px;
        font-weight: 500;
      }

      .ant-list-item-meta-description {
        font-size: 12px;
        color: #8c8c8c;
      }

      .ant-rate {
        font-size: 12px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-stats {
    .stats-cards {
      .stats-card {
        :deep(.ant-card-body) {
          padding: 16px;
        }

        :deep(.ant-statistic-content) {
          font-size: 20px;
        }
      }
    }

    .chart-container {
      height: 160px;

      .chart-placeholder {
        .chart-icon {
          font-size: 36px;
        }

        .trend-summary {
          flex-direction: column;
          gap: 12px;
        }
      }
    }

    .popular-activities {
      .popular-item {
        .popular-rank {
          width: 20px;
          height: 20px;
          font-size: 11px;
        }

        .popular-info {
          .popular-title {
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>
