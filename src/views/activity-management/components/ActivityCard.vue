<template>
  <div class="activity-card" @click="handleClick">
    <a-card :hoverable="clickable" :bordered="false">
      <!-- 活动封面图片 -->
      <div v-if="activity.images && activity.images.length > 0" class="card-cover">
        <a-image
          :src="getCoverImage()"
          :alt="activity.title"
          :preview="false"
          class="cover-image"
        />
        <div class="cover-overlay">
          <a-tag :color="getTypeColor(activity.type)" class="type-tag">
            {{ getTypeText(activity.type) }}
          </a-tag>
        </div>
      </div>

      <!-- 活动信息 -->
      <div class="card-content">
        <div class="activity-header">
          <h3 class="activity-title">{{ activity.title }}</h3>
          <div class="activity-status">
            <a-badge 
              :status="getStatusBadge(activity.status)" 
              :text="getStatusText(activity.status)"
            />
          </div>
        </div>

        <div class="activity-description">
          <p>{{ activity.description }}</p>
        </div>

        <div class="activity-meta">
          <div class="meta-item">
            <calendar-outlined />
            <span>{{ formatTime(activity.startTime) }}</span>
          </div>
          <div class="meta-item">
            <environment-outlined />
            <span>{{ activity.location }}</span>
          </div>
          <div class="meta-item">
            <user-outlined />
            <span>{{ activity.organizer }}</span>
          </div>
        </div>

        <!-- 参与情况 -->
        <div class="participation-info">
          <div class="participants">
            <team-outlined />
            <span>{{ activity.currentParticipants }}</span>
            <span v-if="activity.maxParticipants">/ {{ activity.maxParticipants }}</span>
            <span>人参与</span>
          </div>
          <div v-if="activity.maxParticipants" class="progress">
            <a-progress 
              :percent="getParticipationRate()" 
              size="small" 
              :show-info="false"
            />
          </div>
        </div>

        <!-- 活动标签 -->
        <div v-if="activity.tags && activity.tags.length > 0" class="activity-tags">
          <a-tag 
            v-for="tag in activity.tags.slice(0, 3)" 
            :key="tag" 
            size="small"
            color="blue"
          >
            {{ tag }}
          </a-tag>
          <span v-if="activity.tags.length > 3" class="more-tags">
            +{{ activity.tags.length - 3 }}
          </span>
        </div>

        <!-- 操作按钮 -->
        <div v-if="showActions" class="card-actions">
          <a-space>
            <a-button size="small" @click.stop="$emit('view', activity)">
              查看详情
            </a-button>
            <a-button 
              v-if="canParticipate" 
              type="primary" 
              size="small" 
              @click.stop="$emit('participate', activity)"
            >
              立即参与
            </a-button>
            <a-dropdown v-if="showMoreActions">
              <a-button size="small" @click.stop>
                更多 <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="edit">编辑</a-menu-item>
                  <a-menu-item key="duplicate">复制</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" class="danger-item">删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  CalendarOutlined,
  EnvironmentOutlined,
  UserOutlined,
  TeamOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { Activity, ActivityType, ActivityStatus } from '@/types/activity'
import dayjs from 'dayjs'

// Props
interface Props {
  activity: Activity
  clickable?: boolean
  showActions?: boolean
  showMoreActions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  clickable: true,
  showActions: true,
  showMoreActions: false
})

// Emits
const emit = defineEmits<{
  click: [activity: Activity]
  view: [activity: Activity]
  participate: [activity: Activity]
  edit: [activity: Activity]
  duplicate: [activity: Activity]
  delete: [activity: Activity]
}>()

// 计算属性
const canParticipate = computed(() => {
  return props.activity.status === 'upcoming' || props.activity.status === 'ongoing'
})

// 工具方法
const getTypeColor = (type: ActivityType) => {
  const colors = {
    conference: 'blue',
    training: 'green',
    workshop: 'orange',
    seminar: 'purple',
    competition: 'red',
    exhibition: 'cyan',
    social: 'pink',
    other: 'default'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: ActivityType) => {
  const texts = {
    conference: '会议',
    training: '培训',
    workshop: '研讨会',
    seminar: '讲座',
    competition: '竞赛',
    exhibition: '展览',
    social: '社交活动',
    other: '其他'
  }
  return texts[type] || type
}

const getStatusBadge = (status: ActivityStatus) => {
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: ActivityStatus) => {
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const getCoverImage = () => {
  const coverImage = props.activity.images?.find(img => img.isCover)
  return coverImage?.url || props.activity.images?.[0]?.url || ''
}

const getParticipationRate = () => {
  if (!props.activity.maxParticipants) return 0
  return Math.round((props.activity.currentParticipants / props.activity.maxParticipants) * 100)
}

// 事件处理
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.activity)
  }
}

const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'edit':
      emit('edit', props.activity)
      break
    case 'duplicate':
      emit('duplicate', props.activity)
      break
    case 'delete':
      emit('delete', props.activity)
      break
  }
}
</script>

<style lang="scss" scoped>
.activity-card {
  height: 100%;

  :deep(.ant-card) {
    height: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .ant-card-body {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .card-cover {
    position: relative;
    height: 160px;
    overflow: hidden;

    .cover-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .cover-overlay {
      position: absolute;
      top: 12px;
      right: 12px;

      .type-tag {
        margin: 0;
        font-weight: 500;
      }
    }
  }

  .card-content {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;

    .activity-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .activity-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        line-height: 1.4;
        flex: 1;
        margin-right: 12px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .activity-status {
        flex-shrink: 0;
      }
    }

    .activity-description {
      margin-bottom: 16px;
      flex: 1;

      p {
        margin: 0;
        color: #595959;
        font-size: 14px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .activity-meta {
      margin-bottom: 12px;

      .meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        color: #8c8c8c;
        font-size: 13px;

        .anticon {
          margin-right: 6px;
          color: #1890ff;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .participation-info {
      margin-bottom: 12px;

      .participants {
        display: flex;
        align-items: center;
        color: #595959;
        font-size: 13px;
        margin-bottom: 6px;

        .anticon {
          margin-right: 6px;
          color: #52c41a;
        }
      }

      .progress {
        margin-top: 4px;
      }
    }

    .activity-tags {
      margin-bottom: 16px;

      .ant-tag {
        margin-right: 4px;
        margin-bottom: 4px;
      }

      .more-tags {
        color: #8c8c8c;
        font-size: 12px;
      }
    }

    .card-actions {
      margin-top: auto;
      padding-top: 12px;
      border-top: 1px solid #f0f0f0;

      :deep(.danger-item) {
        color: #ff4d4f !important;

        &:hover {
          background-color: #fff2f0 !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-card {
    .card-cover {
      height: 120px;
    }

    .card-content {
      padding: 12px;

      .activity-header {
        .activity-title {
          font-size: 14px;
        }
      }

      .activity-meta {
        .meta-item {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
