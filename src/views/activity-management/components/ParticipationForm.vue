<template>
  <div class="participation-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <!-- 报名表单 -->
      <template v-if="type === 'registration'">
        <a-form-item label="姓名" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入您的姓名" />
        </a-form-item>
        <a-form-item label="手机号码" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入手机号码" />
        </a-form-item>
        <a-form-item label="邮箱地址" name="email">
          <a-input v-model:value="formData.email" placeholder="请输入邮箱地址" />
        </a-form-item>
        <a-form-item label="所属部门" name="department">
          <a-input v-model:value="formData.department" placeholder="请输入所属部门" />
        </a-form-item>
        <a-form-item label="职位" name="position">
          <a-input v-model:value="formData.position" placeholder="请输入职位" />
        </a-form-item>
        <a-form-item label="参与原因" name="reason">
          <a-textarea 
            v-model:value="formData.reason" 
            placeholder="请简述您参与此活动的原因"
            :rows="4"
          />
        </a-form-item>
      </template>

      <!-- 签到表单 -->
      <template v-if="type === 'check_in'">
        <a-form-item label="签到时间" name="checkInTime">
          <a-date-picker
            v-model:value="formData.checkInTime"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择签到时间"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="签到地点" name="checkInLocation">
          <a-input v-model:value="formData.checkInLocation" placeholder="请输入签到地点" />
        </a-form-item>
        <a-form-item label="备注" name="checkInNotes">
          <a-textarea 
            v-model:value="formData.checkInNotes" 
            placeholder="签到备注信息"
            :rows="3"
          />
        </a-form-item>
      </template>

      <!-- 问卷表单 -->
      <template v-if="type === 'survey'">
        <a-form-item label="活动满意度" name="satisfaction">
          <a-rate v-model:value="formData.satisfaction" :count="5" />
          <div class="rate-desc">
            {{ getSatisfactionText(formData.satisfaction) }}
          </div>
        </a-form-item>
        <a-form-item label="活动组织评价" name="organization">
          <a-radio-group v-model:value="formData.organization">
            <a-radio value="excellent">优秀</a-radio>
            <a-radio value="good">良好</a-radio>
            <a-radio value="average">一般</a-radio>
            <a-radio value="poor">较差</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="内容质量评价" name="content">
          <a-radio-group v-model:value="formData.content">
            <a-radio value="excellent">优秀</a-radio>
            <a-radio value="good">良好</a-radio>
            <a-radio value="average">一般</a-radio>
            <a-radio value="poor">较差</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="改进建议" name="suggestions">
          <a-textarea 
            v-model:value="formData.suggestions" 
            placeholder="请提出您的改进建议"
            :rows="4"
          />
        </a-form-item>
        <a-form-item label="推荐指数" name="recommendation">
          <a-rate v-model:value="formData.recommendation" :count="10" />
          <div class="rate-desc">
            您向他人推荐此活动的可能性：{{ formData.recommendation }}/10
          </div>
        </a-form-item>
      </template>

      <!-- 投票表单 -->
      <template v-if="type === 'vote'">
        <a-form-item label="投票选项" name="voteOption">
          <a-radio-group v-model:value="formData.voteOption">
            <a-radio 
              v-for="option in voteOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="投票理由" name="voteReason">
          <a-textarea 
            v-model:value="formData.voteReason" 
            placeholder="请说明您的投票理由"
            :rows="4"
          />
        </a-form-item>
      </template>

      <!-- 提交按钮 -->
      <a-form-item>
        <a-space>
          <a-button 
            type="primary" 
            html-type="submit" 
            :loading="loading"
            size="large"
          >
            {{ getSubmitButtonText() }}
          </a-button>
          <a-button @click="resetForm" size="large">
            重置
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import type { ParticipationType } from '@/types/activity'
import dayjs, { type Dayjs } from 'dayjs'

// Props
interface Props {
  type: ParticipationType
  loading?: boolean
  initialData?: Record<string, any>
  voteOptions?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  initialData: () => ({}),
  voteOptions: () => [
    { value: 'option1', label: '选项一：继续举办类似活动' },
    { value: 'option2', label: '选项二：增加活动频次' },
    { value: 'option3', label: '选项三：改进活动形式' },
    { value: 'option4', label: '选项四：其他建议' }
  ]
})

// Emits
const emit = defineEmits<{
  submit: [data: Record<string, any>]
  reset: []
}>()

// 响应式数据
const formRef = ref()

// 表单数据
const formData = reactive<Record<string, any>>({
  // 报名表单
  name: '',
  phone: '',
  email: '',
  department: '',
  position: '',
  reason: '',
  
  // 签到表单
  checkInTime: null as Dayjs | null,
  checkInLocation: '',
  checkInNotes: '',
  
  // 问卷表单
  satisfaction: 0,
  organization: '',
  content: '',
  suggestions: '',
  recommendation: 0,
  
  // 投票表单
  voteOption: '',
  voteReason: ''
})

// 表单验证规则
const formRules = computed(() => {
  const rules: Record<string, any> = {}
  
  if (props.type === 'registration') {
    rules.name = [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    rules.phone = [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
    rules.email = [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
    rules.department = [{ required: true, message: '请输入所属部门', trigger: 'blur' }]
  } else if (props.type === 'check_in') {
    rules.checkInTime = [{ required: true, message: '请选择签到时间', trigger: 'change' }]
    rules.checkInLocation = [{ required: true, message: '请输入签到地点', trigger: 'blur' }]
  } else if (props.type === 'survey') {
    rules.satisfaction = [{ required: true, message: '请评价活动满意度', trigger: 'change' }]
    rules.organization = [{ required: true, message: '请评价活动组织', trigger: 'change' }]
    rules.content = [{ required: true, message: '请评价内容质量', trigger: 'change' }]
  } else if (props.type === 'vote') {
    rules.voteOption = [{ required: true, message: '请选择投票选项', trigger: 'change' }]
  }
  
  return rules
})

// 工具方法
const getSubmitButtonText = () => {
  const texts = {
    registration: '提交报名',
    check_in: '确认签到',
    survey: '提交问卷',
    vote: '提交投票'
  }
  return texts[props.type] || '提交'
}

const getSatisfactionText = (value: number) => {
  const texts = ['', '非常不满意', '不满意', '一般', '满意', '非常满意']
  return texts[value] || ''
}

// 事件处理
const handleSubmit = (values: any) => {
  // 处理日期格式
  if (values.checkInTime && dayjs.isDayjs(values.checkInTime)) {
    values.checkInTime = values.checkInTime.format('YYYY-MM-DD HH:mm:ss')
  }
  
  emit('submit', values)
}

const resetForm = () => {
  formRef.value?.resetFields()
  emit('reset')
}

// 初始化数据
const initializeForm = () => {
  if (props.initialData) {
    Object.assign(formData, props.initialData)
    
    // 处理日期字段
    if (props.initialData.checkInTime && typeof props.initialData.checkInTime === 'string') {
      formData.checkInTime = dayjs(props.initialData.checkInTime)
    }
  }
}

// 监听初始数据变化
watch(() => props.initialData, initializeForm, { immediate: true, deep: true })

// 暴露方法
defineExpose({
  resetForm,
  validateForm: () => formRef.value?.validate(),
  getFormData: () => formData
})
</script>

<style lang="scss" scoped>
.participation-form {
  :deep(.ant-form) {
    .ant-form-item-label {
      font-weight: 500;
    }

    .ant-btn-lg {
      height: 44px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .rate-desc {
    margin-top: 8px;
    font-size: 13px;
    color: #8c8c8c;
  }

  :deep(.ant-rate) {
    font-size: 20px;
  }

  :deep(.ant-radio-group) {
    .ant-radio-wrapper {
      display: block;
      margin-bottom: 8px;
      line-height: 1.8;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .participation-form {
    :deep(.ant-form) {
      .ant-form-item {
        margin-bottom: 16px;
      }

      .ant-btn-lg {
        height: 40px;
        font-size: 14px;
      }
    }

    :deep(.ant-space) {
      width: 100%;

      .ant-space-item {
        flex: 1;

        .ant-btn {
          width: 100%;
        }
      }
    }
  }
}
</style>
