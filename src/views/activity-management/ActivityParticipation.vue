<template>
  <div class="activity-participation">
    <a-spin :spinning="loading">
      <!-- 页面头部 -->
      <div class="page-header">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/activity-management">活动管理</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link to="/activity-management/list">活动列表</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link :to="`/activity-management/detail/${activityId}`">活动详情</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>{{ getParticipationTitle() }}</a-breadcrumb-item>
        </a-breadcrumb>
        
        <div class="header-actions">
          <a-space>
            <a-button @click="goBack">
              <template #icon><arrow-left-outlined /></template>
              返回
            </a-button>
          </a-space>
        </div>
      </div>

      <!-- 活动信息概览 -->
      <div class="activity-overview">
        <a-card>
          <div class="activity-info">
            <h2>{{ activity?.title }}</h2>
            <div class="activity-meta">
              <a-space wrap>
                <span><calendar-outlined /> {{ formatTime(activity?.startTime) }}</span>
                <span><environment-outlined /> {{ activity?.location }}</span>
                <span><user-outlined /> {{ activity?.organizer }}</span>
                <a-badge 
                  :status="getStatusBadge(activity?.status)" 
                  :text="getStatusText(activity?.status)"
                />
              </a-space>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 参与类型切换 -->
      <div class="participation-tabs">
        <a-card>
          <a-tabs v-model:activeKey="currentType" @change="handleTypeChange">
            <a-tab-pane key="registration" tab="活动报名">
              <template #tab>
                <span>
                  <user-add-outlined />
                  活动报名
                </span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="check_in" tab="现场签到">
              <template #tab>
                <span>
                  <check-circle-outlined />
                  现场签到
                </span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="survey" tab="问卷调查">
              <template #tab>
                <span>
                  <form-outlined />
                  问卷调查
                </span>
              </template>
            </a-tab-pane>
            <a-tab-pane key="vote" tab="活动投票">
              <template #tab>
                <span>
                  <like-outlined />
                  活动投票
                </span>
              </template>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </div>

      <!-- 参与表单 -->
      <div class="participation-form">
        <a-row :gutter="[24, 24]">
          <!-- 左侧表单区域 -->
          <a-col :xs="24" :lg="16">
            <a-card :title="getParticipationTitle()">
              <!-- 权限检查提示 -->
              <a-alert
                v-if="!hasPermission"
                type="warning"
                :message="getPermissionMessage()"
                show-icon
                style="margin-bottom: 24px"
              />

              <!-- 已完成提示 -->
              <a-alert
                v-else-if="isCompleted"
                type="success"
                message="您已完成此项参与"
                description="如需修改，请联系活动组织者"
                show-icon
                style="margin-bottom: 24px"
              />

              <!-- 参与表单 -->
              <a-form
                v-else
                ref="formRef"
                :model="formData"
                :rules="formRules"
                layout="vertical"
                @finish="handleSubmit"
              >
                <!-- 报名表单 -->
                <template v-if="currentType === 'registration'">
                  <a-form-item label="姓名" name="name">
                    <a-input v-model:value="formData.name" placeholder="请输入您的姓名" />
                  </a-form-item>
                  <a-form-item label="手机号码" name="phone">
                    <a-input v-model:value="formData.phone" placeholder="请输入手机号码" />
                  </a-form-item>
                  <a-form-item label="邮箱地址" name="email">
                    <a-input v-model:value="formData.email" placeholder="请输入邮箱地址" />
                  </a-form-item>
                  <a-form-item label="所属部门" name="department">
                    <a-input v-model:value="formData.department" placeholder="请输入所属部门" />
                  </a-form-item>
                  <a-form-item label="职位" name="position">
                    <a-input v-model:value="formData.position" placeholder="请输入职位" />
                  </a-form-item>
                  <a-form-item label="参与原因" name="reason">
                    <a-textarea 
                      v-model:value="formData.reason" 
                      placeholder="请简述您参与此活动的原因"
                      :rows="4"
                    />
                  </a-form-item>
                </template>

                <!-- 签到表单 -->
                <template v-if="currentType === 'check_in'">
                  <a-form-item label="签到时间" name="checkInTime">
                    <a-date-picker
                      v-model:value="formData.checkInTime"
                      show-time
                      format="YYYY-MM-DD HH:mm:ss"
                      placeholder="选择签到时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                  <a-form-item label="签到地点" name="checkInLocation">
                    <a-input v-model:value="formData.checkInLocation" placeholder="请输入签到地点" />
                  </a-form-item>
                  <a-form-item label="备注" name="checkInNotes">
                    <a-textarea 
                      v-model:value="formData.checkInNotes" 
                      placeholder="签到备注信息"
                      :rows="3"
                    />
                  </a-form-item>
                </template>

                <!-- 问卷表单 -->
                <template v-if="currentType === 'survey'">
                  <a-form-item label="活动满意度" name="satisfaction">
                    <a-rate v-model:value="formData.satisfaction" :count="5" />
                    <div class="rate-desc">
                      {{ getSatisfactionText(formData.satisfaction) }}
                    </div>
                  </a-form-item>
                  <a-form-item label="活动组织评价" name="organization">
                    <a-radio-group v-model:value="formData.organization">
                      <a-radio value="excellent">优秀</a-radio>
                      <a-radio value="good">良好</a-radio>
                      <a-radio value="average">一般</a-radio>
                      <a-radio value="poor">较差</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="内容质量评价" name="content">
                    <a-radio-group v-model:value="formData.content">
                      <a-radio value="excellent">优秀</a-radio>
                      <a-radio value="good">良好</a-radio>
                      <a-radio value="average">一般</a-radio>
                      <a-radio value="poor">较差</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="改进建议" name="suggestions">
                    <a-textarea 
                      v-model:value="formData.suggestions" 
                      placeholder="请提出您的改进建议"
                      :rows="4"
                    />
                  </a-form-item>
                  <a-form-item label="推荐指数" name="recommendation">
                    <a-rate v-model:value="formData.recommendation" :count="10" />
                    <div class="rate-desc">
                      您向他人推荐此活动的可能性：{{ formData.recommendation }}/10
                    </div>
                  </a-form-item>
                </template>

                <!-- 投票表单 -->
                <template v-if="currentType === 'vote'">
                  <a-form-item label="投票选项" name="voteOption">
                    <a-radio-group v-model:value="formData.voteOption">
                      <a-radio value="option1">选项一：继续举办类似活动</a-radio>
                      <a-radio value="option2">选项二：增加活动频次</a-radio>
                      <a-radio value="option3">选项三：改进活动形式</a-radio>
                      <a-radio value="option4">选项四：其他建议</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item label="投票理由" name="voteReason">
                    <a-textarea 
                      v-model:value="formData.voteReason" 
                      placeholder="请说明您的投票理由"
                      :rows="4"
                    />
                  </a-form-item>
                </template>

                <!-- 提交按钮 -->
                <a-form-item>
                  <a-space>
                    <a-button 
                      type="primary" 
                      html-type="submit" 
                      :loading="submitting"
                      size="large"
                    >
                      {{ getSubmitButtonText() }}
                    </a-button>
                    <a-button @click="resetForm" size="large">
                      重置
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-form>
            </a-card>
          </a-col>

          <!-- 右侧状态面板 -->
          <a-col :xs="24" :lg="8">
            <!-- 参与进度 -->
            <a-card title="参与进度" :bordered="false">
              <a-steps direction="vertical" size="small" :current="currentStep">
                <a-step title="活动报名" :status="getStepStatus('registration')">
                  <template #description>
                    <span v-if="participationStatus.registration">
                      {{ participationStatus.registration.submitTime }}
                    </span>
                    <span v-else class="text-muted">未报名</span>
                  </template>
                </a-step>
                <a-step title="现场签到" :status="getStepStatus('check_in')">
                  <template #description>
                    <span v-if="participationStatus.check_in">
                      {{ participationStatus.check_in.submitTime }}
                    </span>
                    <span v-else class="text-muted">未签到</span>
                  </template>
                </a-step>
                <a-step title="问卷调查" :status="getStepStatus('survey')">
                  <template #description>
                    <span v-if="participationStatus.survey">
                      {{ participationStatus.survey.submitTime }}
                    </span>
                    <span v-else class="text-muted">未完成</span>
                  </template>
                </a-step>
                <a-step title="活动投票" :status="getStepStatus('vote')">
                  <template #description>
                    <span v-if="participationStatus.vote">
                      {{ participationStatus.vote.submitTime }}
                    </span>
                    <span v-else class="text-muted">未投票</span>
                  </template>
                </a-step>
              </a-steps>
            </a-card>

            <!-- 注意事项 -->
            <a-card title="注意事项" :bordered="false">
              <div class="notice-content">
                <template v-if="currentType === 'registration'">
                  <ul>
                    <li>请确保填写信息真实有效</li>
                    <li>报名成功后将收到确认通知</li>
                    <li>如需取消报名请提前联系组织者</li>
                  </ul>
                </template>
                <template v-if="currentType === 'check_in'">
                  <ul>
                    <li>请在活动开始前完成签到</li>
                    <li>签到时间不能早于活动开始时间</li>
                    <li>请确保在指定地点签到</li>
                  </ul>
                </template>
                <template v-if="currentType === 'survey'">
                  <ul>
                    <li>请根据实际体验如实填写</li>
                    <li>您的反馈对我们很重要</li>
                    <li>问卷提交后不可修改</li>
                  </ul>
                </template>
                <template v-if="currentType === 'vote'">
                  <ul>
                    <li>每人只能投票一次</li>
                    <li>投票结果将公开展示</li>
                    <li>投票截止时间为活动结束后24小时</li>
                  </ul>
                </template>
              </div>
            </a-card>

            <!-- 联系信息 -->
            <a-card title="联系我们" :bordered="false" v-if="activity?.contactInfo">
              <div class="contact-info">
                <div class="contact-item">
                  <user-outlined />
                  <span>{{ activity.contactInfo.name }}</span>
                </div>
                <div v-if="activity.contactInfo.phone" class="contact-item">
                  <phone-outlined />
                  <span>{{ activity.contactInfo.phone }}</span>
                </div>
                <div v-if="activity.contactInfo.email" class="contact-item">
                  <mail-outlined />
                  <span>{{ activity.contactInfo.email }}</span>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  UserOutlined,
  UserAddOutlined,
  CheckCircleOutlined,
  FormOutlined,
  LikeOutlined,
  PhoneOutlined,
  MailOutlined
} from '@ant-design/icons-vue'
import type { 
  Activity, 
  ActivityStatus,
  ParticipationType,
  ParticipationStatus,
  ParticipationRecord
} from '@/types/activity'
import { MockActivityService } from './mock/data'
import dayjs, { type Dayjs } from 'dayjs'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const activity = ref<Activity | null>(null)
const formRef = ref()

// 路由参数
const activityId = computed(() => route.params.id as string)
const currentType = ref<ParticipationType>((route.params.type as ParticipationType) || 'registration')

// 表单数据
const formData = reactive<Record<string, any>>({
  // 报名表单
  name: '',
  phone: '',
  email: '',
  department: '',
  position: '',
  reason: '',
  
  // 签到表单
  checkInTime: null as Dayjs | null,
  checkInLocation: '',
  checkInNotes: '',
  
  // 问卷表单
  satisfaction: 0,
  organization: '',
  content: '',
  suggestions: '',
  recommendation: 0,
  
  // 投票表单
  voteOption: '',
  voteReason: ''
})

// 参与状态
const participationStatus = reactive<Record<ParticipationType, ParticipationRecord | null>>({
  registration: null,
  check_in: null,
  survey: null,
  vote: null
})

// 表单验证规则
const formRules = computed(() => {
  const rules: Record<string, any> = {}
  
  if (currentType.value === 'registration') {
    rules.name = [{ required: true, message: '请输入姓名', trigger: 'blur' }]
    rules.phone = [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
    rules.email = [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ]
    rules.department = [{ required: true, message: '请输入所属部门', trigger: 'blur' }]
  } else if (currentType.value === 'check_in') {
    rules.checkInTime = [{ required: true, message: '请选择签到时间', trigger: 'change' }]
    rules.checkInLocation = [{ required: true, message: '请输入签到地点', trigger: 'blur' }]
  } else if (currentType.value === 'survey') {
    rules.satisfaction = [{ required: true, message: '请评价活动满意度', trigger: 'change' }]
    rules.organization = [{ required: true, message: '请评价活动组织', trigger: 'change' }]
    rules.content = [{ required: true, message: '请评价内容质量', trigger: 'change' }]
  } else if (currentType.value === 'vote') {
    rules.voteOption = [{ required: true, message: '请选择投票选项', trigger: 'change' }]
  }
  
  return rules
})

// 计算属性
const currentStep = computed(() => {
  if (participationStatus.vote) return 3
  if (participationStatus.survey) return 2
  if (participationStatus.check_in) return 1
  if (participationStatus.registration) return 0
  return -1
})

const hasPermission = computed(() => {
  // 模拟权限检查逻辑
  if (currentType.value === 'registration') {
    return activity.value?.status === 'upcoming' || activity.value?.status === 'ongoing'
  } else if (currentType.value === 'check_in') {
    return activity.value?.status === 'ongoing' && participationStatus.registration
  } else if (currentType.value === 'survey') {
    return activity.value?.status === 'ongoing' && participationStatus.check_in
  } else if (currentType.value === 'vote') {
    return activity.value?.status === 'ongoing' && participationStatus.check_in
  }
  return false
})

const isCompleted = computed(() => {
  return participationStatus[currentType.value] !== null
})

// 工具方法
const getStatusBadge = (status?: ActivityStatus) => {
  if (!status) return 'default'
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status?: ActivityStatus) => {
  if (!status) return ''
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (time?: string) => {
  if (!time) return ''
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const getParticipationTitle = () => {
  const titles = {
    registration: '活动报名',
    check_in: '现场签到',
    survey: '问卷调查',
    vote: '活动投票'
  }
  return titles[currentType.value] || ''
}

const getPermissionMessage = () => {
  if (currentType.value === 'registration') {
    return '活动已结束，无法报名'
  } else if (currentType.value === 'check_in') {
    return '请先完成活动报名'
  } else if (currentType.value === 'survey') {
    return '请先完成现场签到'
  } else if (currentType.value === 'vote') {
    return '请先完成现场签到'
  }
  return '暂无权限参与此项活动'
}

const getSubmitButtonText = () => {
  const texts = {
    registration: '提交报名',
    check_in: '确认签到',
    survey: '提交问卷',
    vote: '提交投票'
  }
  return texts[currentType.value] || '提交'
}

const getSatisfactionText = (value: number) => {
  const texts = ['', '非常不满意', '不满意', '一般', '满意', '非常满意']
  return texts[value] || ''
}

const getStepStatus = (type: ParticipationType) => {
  if (participationStatus[type]) {
    return participationStatus[type]?.status === 'completed' ? 'finish' : 'process'
  }
  return 'wait'
}

// 事件处理
const goBack = () => {
  router.go(-1)
}

const handleTypeChange = (key: string) => {
  router.push(`/activity-management/participation/${activityId.value}/${key}`)
}

const resetForm = () => {
  formRef.value?.resetFields()
}

const handleSubmit = async (values: any) => {
  submitting.value = true
  try {
    // 模拟提交过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 创建参与记录
    const participationData = {
      activityId: activityId.value,
      activityTitle: activity.value?.title,
      userId: 'current_user',
      userName: values.name || '当前用户',
      userPhone: values.phone,
      userEmail: values.email,
      type: currentType.value,
      data: values
    }
    
    const result = await MockActivityService.createParticipation(participationData)
    
    if (result.success) {
      message.success('提交成功！')
      // 更新参与状态
      participationStatus[currentType.value] = result.data!
      // 重置表单
      formRef.value?.resetFields()
    } else {
      message.error(result.message)
    }
    
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    // 加载活动详情
    const activityData = await MockActivityService.getActivityById(activityId.value)
    if (!activityData) {
      message.error('活动不存在')
      router.push('/activity-management/list')
      return
    }
    activity.value = activityData
    
    // 模拟加载当前用户的参与状态
    // 这里应该根据实际的用户ID来查询
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 监听路由变化
watch(() => route.params.type, (newType) => {
  if (newType) {
    currentType.value = newType as ParticipationType
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.activity-participation {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .activity-overview {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .activity-info {
      h2 {
        margin: 0 0 12px 0;
        font-size: 20px;
        font-weight: 600;
        color: #262626;
      }

      .activity-meta {
        color: #8c8c8c;
        font-size: 14px;

        .anticon {
          margin-right: 4px;
        }
      }
    }
  }

  .participation-tabs {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-body {
        padding: 0;
      }

      .ant-tabs {
        .ant-tabs-nav {
          margin: 0;
          padding: 0 24px;

          .ant-tabs-tab {
            padding: 16px 20px;
            font-weight: 500;

            .anticon {
              margin-right: 8px;
            }
          }
        }

        .ant-tabs-content-holder {
          padding: 24px;
        }
      }
    }
  }

  .participation-form {
    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;

      .ant-card-head {
        .ant-card-head-title {
          font-size: 18px;
          font-weight: 600;
        }
      }
    }

    .rate-desc {
      margin-top: 8px;
      font-size: 13px;
      color: #8c8c8c;
    }

    .text-muted {
      color: #bfbfbf;
    }

    .notice-content {
      ul {
        margin: 0;
        padding-left: 20px;
        color: #595959;

        li {
          margin-bottom: 8px;
          line-height: 1.5;
        }
      }
    }

    .contact-info {
      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        color: #595959;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    :deep(.ant-steps) {
      .ant-steps-item-title {
        font-size: 13px;
      }

      .ant-steps-item-description {
        font-size: 12px;
      }
    }

    :deep(.ant-form) {
      .ant-form-item-label {
        font-weight: 500;
      }

      .ant-btn-lg {
        height: 44px;
        font-size: 16px;
        font-weight: 500;
      }
    }

    :deep(.ant-alert) {
      border-radius: 6px;
    }

    :deep(.ant-rate) {
      font-size: 20px;
    }

    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        display: block;
        margin-bottom: 8px;
        line-height: 1.8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-participation {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .participation-tabs {
      :deep(.ant-tabs) {
        .ant-tabs-nav {
          .ant-tabs-tab {
            padding: 12px 16px;
            font-size: 13px;

            .anticon {
              margin-right: 4px;
            }
          }
        }

        .ant-tabs-content-holder {
          padding: 16px;
        }
      }
    }

    .participation-form {
      :deep(.ant-form) {
        .ant-form-item {
          margin-bottom: 16px;
        }

        .ant-btn-lg {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
