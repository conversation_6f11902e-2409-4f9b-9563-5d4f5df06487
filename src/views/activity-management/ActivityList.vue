<template>
  <div class="activity-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>活动列表</h2>
        <p>查看和管理所有活动，支持多维度筛选和批量操作</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="batchDelete" :disabled="!selectedRowKeys.length">
            <template #icon><delete-outlined /></template>
            批量删除
          </a-button>
          <a-button type="primary" @click="createActivity">
            <template #icon><plus-outlined /></template>
            创建活动
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 活动统计 -->
    <div class="activity-stats">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="全部活动"
              :value="stats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <calendar-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="进行中活动"
              :value="stats.ongoing"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <play-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="即将开始"
              :value="stats.upcoming"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <clock-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card>
            <a-statistic
              title="已结束"
              :value="stats.ended"
              :value-style="{ color: '#8c8c8c' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <a-card>
        <a-form :model="filterForm" class="filter-form">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="关键词搜索">
                <a-input 
                  v-model:value="filterForm.keyword" 
                  placeholder="搜索活动标题或内容"
                  allow-clear
                  @change="handleFilter"
                >
                  <template #prefix>
                    <search-outlined />
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="活动状态">
                <a-select 
                  v-model:value="filterForm.status" 
                  placeholder="选择状态"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option value="upcoming">即将开始</a-select-option>
                  <a-select-option value="ongoing">进行中</a-select-option>
                  <a-select-option value="ended">已结束</a-select-option>
                  <a-select-option value="cancelled">已取消</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="活动类型">
                <a-select 
                  v-model:value="filterForm.type" 
                  placeholder="选择类型"
                  allow-clear
                  @change="handleFilter"
                >
                  <a-select-option value="conference">会议</a-select-option>
                  <a-select-option value="training">培训</a-select-option>
                  <a-select-option value="workshop">研讨会</a-select-option>
                  <a-select-option value="seminar">讲座</a-select-option>
                  <a-select-option value="competition">竞赛</a-select-option>
                  <a-select-option value="exhibition">展览</a-select-option>
                  <a-select-option value="social">社交活动</a-select-option>
                  <a-select-option value="other">其他</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="时间范围">
                <a-range-picker 
                  v-model:value="filterForm.timeRange"
                  @change="handleFilter"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-space>
                <a-button @click="resetFilter">重置筛选</a-button>
                <a-button type="primary" @click="handleFilter">搜索</a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 活动列表 -->
    <div class="activity-table">
      <a-card>
        <a-table
          :columns="columns"
          :data-source="activityList"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <div class="activity-title">
                <a @click="viewDetail(record)">{{ record.title }}</a>
                <div class="activity-meta">
                  <a-tag v-for="tag in record.tags" :key="tag" size="small">{{ tag }}</a-tag>
                </div>
              </div>
            </template>
            
            <template v-if="column.key === 'type'">
              <a-tag :color="getTypeColor(record.type)">
                {{ getTypeText(record.type) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-badge 
                :status="getStatusBadge(record.status)" 
                :text="getStatusText(record.status)"
              />
            </template>
            
            <template v-if="column.key === 'participants'">
              <div class="participants-info">
                <span>{{ record.currentParticipants }}</span>
                <span v-if="record.maxParticipants">/ {{ record.maxParticipants }}</span>
                <a-progress 
                  v-if="record.maxParticipants"
                  :percent="Math.round((record.currentParticipants / record.maxParticipants) * 100)"
                  size="small"
                  :show-info="false"
                  style="margin-top: 4px;"
                />
              </div>
            </template>
            
            <template v-if="column.key === 'time'">
              <div class="time-info">
                <div>{{ formatTime(record.startTime) }}</div>
                <div class="time-duration">{{ getTimeDuration(record.startTime, record.endTime) }}</div>
              </div>
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  查看
                </a-button>
                <a-button type="link" size="small" @click="editActivity(record)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="manageParticipants(record)">
                  参与管理
                </a-button>
                <a-dropdown>
                  <a-button type="link" size="small">
                    更多 <down-outlined />
                  </a-button>
                  <template #overlay>
                    <a-menu @click="handleMenuClick($event, record)">
                      <a-menu-item key="duplicate">复制活动</a-menu-item>
                      <a-menu-item key="export">导出数据</a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" class="danger-item">删除活动</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType, TableProps } from 'ant-design-vue'
import {
  ReloadOutlined,
  DeleteOutlined,
  PlusOutlined,
  CalendarOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import type { 
  Activity, 
  ActivityType, 
  ActivityStatus, 
  ActivityQueryParams 
} from '@/types/activity'
import { MockActivityService } from './mock/data'
import dayjs, { type Dayjs } from 'dayjs'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 统计数据
const stats = reactive({
  total: 0,
  ongoing: 0,
  upcoming: 0,
  ended: 0
})

// 筛选表单
const filterForm = reactive({
  keyword: '',
  status: undefined as ActivityStatus | undefined,
  type: undefined as ActivityType | undefined,
  timeRange: undefined as [Dayjs, Dayjs] | undefined
})

// 活动列表
const activityList = ref<Activity[]>([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格选择
const selectedRowKeys = ref<string[]>([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  }
}

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '活动标题',
    key: 'title',
    width: 300,
    ellipsis: true
  },
  {
    title: '类型',
    key: 'type',
    width: 100
  },
  {
    title: '状态',
    key: 'status',
    width: 120
  },
  {
    title: '参与情况',
    key: 'participants',
    width: 120
  },
  {
    title: '活动时间',
    key: 'time',
    width: 180,
    sorter: true
  },
  {
    title: '地点',
    dataIndex: 'location',
    key: 'location',
    width: 150,
    ellipsis: true
  },
  {
    title: '组织者',
    dataIndex: 'organizer',
    key: 'organizer',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right'
  }
]

// 工具方法
const getTypeColor = (type: ActivityType) => {
  const colors = {
    conference: 'blue',
    training: 'green',
    workshop: 'orange',
    seminar: 'purple',
    competition: 'red',
    exhibition: 'cyan',
    social: 'pink',
    other: 'default'
  }
  return colors[type] || 'default'
}

const getTypeText = (type: ActivityType) => {
  const texts = {
    conference: '会议',
    training: '培训',
    workshop: '研讨会',
    seminar: '讲座',
    competition: '竞赛',
    exhibition: '展览',
    social: '社交活动',
    other: '其他'
  }
  return texts[type] || type
}

const getStatusBadge = (status: ActivityStatus) => {
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: ActivityStatus) => {
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const getTimeDuration = (startTime: string, endTime: string) => {
  const start = dayjs(startTime)
  const end = dayjs(endTime)
  const duration = end.diff(start, 'hour')
  return `${duration}小时`
}

// 事件处理
const handleFilter = async () => {
  pagination.current = 1
  await loadData()
}

const resetFilter = () => {
  Object.assign(filterForm, {
    keyword: '',
    status: undefined,
    type: undefined,
    timeRange: undefined
  })
  handleFilter()
}

const handleTableChange: TableProps['onChange'] = (pag, filters, sorter) => {
  if (pag) {
    pagination.current = pag.current || 1
    pagination.pageSize = pag.pageSize || 10
  }
  loadData()
}

const refreshData = () => {
  loadData()
}

// 操作方法
const viewDetail = (record: Activity) => {
  router.push(`/activity-management/detail/${record.id}`)
}

const editActivity = (record: Activity) => {
  message.info('编辑活动功能开发中...')
}

const createActivity = () => {
  message.info('创建活动功能开发中...')
}

const manageParticipants = (record: Activity) => {
  router.push(`/activity-management/participation/${record.id}/registration`)
}

const batchDelete = () => {
  if (selectedRowKeys.value.length === 0) return
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个活动吗？`,
    onOk: () => {
      message.success('删除成功')
      selectedRowKeys.value = []
      loadData()
    }
  })
}

const handleMenuClick = ({ key }: { key: string }, record: Activity) => {
  switch (key) {
    case 'duplicate':
      message.info('复制活动功能开发中...')
      break
    case 'export':
      message.info('导出数据功能开发中...')
      break
    case 'delete':
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除活动"${record.title}"吗？`,
        onOk: () => {
          message.success('删除成功')
          loadData()
        }
      })
      break
  }
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const params: ActivityQueryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      keyword: filterForm.keyword || undefined,
      status: filterForm.status,
      type: filterForm.type
    }
    
    if (filterForm.timeRange) {
      params.startDate = filterForm.timeRange[0].format('YYYY-MM-DD')
      params.endDate = filterForm.timeRange[1].format('YYYY-MM-DD')
    }
    
    const result = await MockActivityService.getActivities(params)
    activityList.value = result.data
    pagination.total = result.total
    
    // 更新统计数据
    const statisticsData = await MockActivityService.getActivityStatistics()
    stats.total = statisticsData.totalActivities
    stats.ongoing = statisticsData.ongoingActivities
    stats.upcoming = statisticsData.upcomingActivities
    stats.ended = statisticsData.endedActivities
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.activity-list {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .activity-stats {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .ant-card-body {
        padding: 20px;
      }

      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: #8c8c8c;
        }

        .ant-statistic-content {
          font-size: 24px;
          font-weight: 600;
        }
      }
    }
  }

  .filter-section {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .filter-form {
      .ant-form-item {
        margin-bottom: 16px;

        .ant-form-item-label {
          font-weight: 500;
        }
      }
    }
  }

  .activity-table {
    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .activity-title {
      a {
        color: #1890ff;
        font-weight: 500;
        text-decoration: none;

        &:hover {
          color: #40a9ff;
        }
      }

      .activity-meta {
        margin-top: 4px;

        .ant-tag {
          margin-right: 4px;
          margin-bottom: 2px;
        }
      }
    }

    .participants-info {
      font-size: 13px;
      color: #666;
    }

    .time-info {
      .time-duration {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
      }
    }

    :deep(.danger-item) {
      color: #ff4d4f !important;

      &:hover {
        background-color: #fff2f0 !important;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-list {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .filter-section {
      .filter-form {
        :deep(.ant-col) {
          margin-bottom: 16px;
        }
      }
    }

    .activity-table {
      :deep(.ant-table) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
