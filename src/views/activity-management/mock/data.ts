// 活动管理模拟数据服务

import type {
  Activity,
  ActivityStatus,
  ActivityType,
  ActivityPriority,
  ParticipationType,
  ParticipationStatus,
  ParticipationRecord,
  ActivityStatistics,
  ActivityActivity,
  OrganizerStats,
  ActivityQueryParams,
  ParticipationQueryParams,
  ActivityOperationResult,
  ParticipationOperationResult,
  ActivityRating
} from '@/types/activity'

// 模拟活动数据
const mockActivities: Activity[] = [
  {
    id: 'act_001',
    title: '党建工作交流会',
    description: '深入学习贯彻党的二十大精神，加强基层党建工作交流',
    content: '本次交流会将围绕党建工作创新实践、基层组织建设、党员教育管理等主题展开深入讨论...',
    type: 'conference' as ActivityType,
    status: 'ongoing' as ActivityStatus,
    priority: 3 as ActivityPriority,
    startTime: '2025-01-28 09:00:00',
    endTime: '2025-01-28 17:00:00',
    location: '市委党校大礼堂',
    organizer: '市委组织部',
    organizerId: 'org_001',
    maxParticipants: 200,
    currentParticipants: 156,
    tags: ['党建', '交流', '学习'],
    images: [
      // {
      //   id: 'img_001',
      //   url: '/images/activity/party-meeting.jpg',
      //   title: '会议现场',
      //   isCover: true,
      //   sort: 1,
      //   createTime: '2025-01-20 10:00:00'
      // }
    ],
    requirements: ['党员身份', '单位推荐'],
    benefits: ['学习证书', '交流机会'],
    contactInfo: {
      name: '张主任',
      phone: '023-12345678',
      email: '<EMAIL>'
    },
    isPublic: true,
    requiresApproval: true,
    allowWaitlist: true,
    createTime: '2025-01-20 10:00:00',
    updateTime: '2025-01-25 14:30:00',
    createdBy: 'admin'
  },
  {
    id: 'act_002',
    title: '业务技能培训',
    description: '提升干部职工业务能力和工作技能',
    content: '培训内容包括政务服务、数字化办公、沟通技巧等方面...',
    type: 'training' as ActivityType,
    status: 'upcoming' as ActivityStatus,
    priority: 2 as ActivityPriority,
    startTime: '2025-02-15 14:00:00',
    endTime: '2025-02-15 17:00:00',
    location: '培训中心201室',
    organizer: '人事处',
    organizerId: 'org_002',
    maxParticipants: 50,
    currentParticipants: 32,
    tags: ['培训', '技能', '提升'],
    images: [],
    requirements: ['在职人员'],
    benefits: ['培训证书'],
    contactInfo: {
      name: '李老师',
      phone: '023-87654321',
      email: '<EMAIL>'
    },
    isPublic: true,
    requiresApproval: false,
    allowWaitlist: false,
    createTime: '2025-01-22 09:00:00',
    updateTime: '2025-01-26 16:00:00',
    createdBy: 'hr_admin'
  },
  {
    id: 'act_003',
    title: '年度工作总结会',
    description: '总结2024年工作成果，部署2025年工作计划',
    content: '会议将回顾全年工作亮点，分析存在问题，明确新年目标...',
    type: 'conference' as ActivityType,
    status: 'ended' as ActivityStatus,
    priority: 4 as ActivityPriority,
    startTime: '2024-12-28 09:00:00',
    endTime: '2024-12-28 18:00:00',
    location: '行政中心大会议室',
    organizer: '办公室',
    organizerId: 'org_003',
    maxParticipants: 100,
    currentParticipants: 98,
    tags: ['总结', '规划', '年度'],
    images: [],
    requirements: ['中层以上干部'],
    benefits: ['年度总结材料'],
    contactInfo: {
      name: '王秘书',
      phone: '023-11111111',
      email: '<EMAIL>'
    },
    isPublic: false,
    requiresApproval: true,
    allowWaitlist: false,
    createTime: '2024-12-15 10:00:00',
    updateTime: '2024-12-28 20:00:00',
    createdBy: 'office_admin'
  }
]

// 模拟参与记录数据
const mockParticipationRecords: ParticipationRecord[] = [
  {
    id: 'part_001',
    activityId: 'act_001',
    activityTitle: '党建工作交流会',
    userId: 'user_001',
    userName: '周海军',
    userPhone: '13800138001',
    userEmail: '<EMAIL>',
    type: 'registration' as ParticipationType,
    status: 'approved' as ParticipationStatus,
    data: {
      department: '组织部',
      position: '科员',
      experience: '5年党务工作经验'
    },
    submitTime: '2025-01-21 10:30:00',
    approveTime: '2025-01-22 09:00:00',
    approvedBy: 'admin',
    notes: '符合参会条件',
    createTime: '2025-01-21 10:30:00',
    updateTime: '2025-01-22 09:00:00'
  },
  {
    id: 'part_002',
    activityId: 'act_001',
    activityTitle: '党建工作交流会',
    userId: 'user_001',
    userName: '周海军',
    type: 'check_in' as ParticipationType,
    status: 'completed' as ParticipationStatus,
    data: {
      checkInTime: '2025-01-28 08:45:00',
      location: '市委党校大礼堂'
    },
    submitTime: '2025-01-28 08:45:00',
    completeTime: '2025-01-28 08:45:00',
    createTime: '2025-01-28 08:45:00',
    updateTime: '2025-01-28 08:45:00'
  }
]

// 模拟活动动态数据
const mockActivityActivities: ActivityActivity[] = [
  {
    id: 'activity_001',
    type: 'register',
    activityId: 'act_001',
    activityTitle: '党建工作交流会',
    userId: 'user_001',
    userName: '周海军',
    timestamp: '2025-01-21 10:30:00',
    description: '报名参加党建工作交流会'
  },
  {
    id: 'activity_002',
    type: 'check_in',
    activityId: 'act_001',
    activityTitle: '党建工作交流会',
    userId: 'user_001',
    userName: '周海军',
    timestamp: '2025-01-28 08:45:00',
    description: '签到参加党建工作交流会'
  }
]

// 模拟组织者统计数据
const mockOrganizerStats: OrganizerStats[] = [
  {
    organizerId: 'org_001',
    organizerName: '市委组织部',
    totalActivities: 15,
    totalParticipants: 1200,
    averageRating: 4.8,
    completionRate: 95
  },
  {
    organizerId: 'org_002',
    organizerName: '人事处',
    totalActivities: 8,
    totalParticipants: 320,
    averageRating: 4.5,
    completionRate: 88
  }
]

// 模拟数据服务类
export class MockActivityService {
  // 模拟网络延迟
  private static delay(ms: number = 800): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 获取活动列表
  static async getActivities(params?: ActivityQueryParams): Promise<{
    data: Activity[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockActivities]
    
    // 关键词搜索
    if (params?.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredData = filteredData.filter(activity => 
        activity.title.toLowerCase().includes(keyword) ||
        activity.description.toLowerCase().includes(keyword)
      )
    }
    
    // 状态筛选
    if (params?.status) {
      filteredData = filteredData.filter(activity => activity.status === params.status)
    }
    
    // 类型筛选
    if (params?.type) {
      filteredData = filteredData.filter(activity => activity.type === params.type)
    }
    
    // 标签筛选
    if (params?.tags && params.tags.length > 0) {
      filteredData = filteredData.filter(activity => 
        params.tags!.some(tag => activity.tags.includes(tag))
      )
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 获取活动详情
  static async getActivityById(id: string): Promise<Activity | null> {
    await this.delay()
    return mockActivities.find(activity => activity.id === id) || null
  }

  // 获取活动统计
  static async getActivityStatistics(): Promise<ActivityStatistics> {
    await this.delay()
    
    const total = mockActivities.length
    const upcoming = mockActivities.filter(a => a.status === 'upcoming').length
    const ongoing = mockActivities.filter(a => a.status === 'ongoing').length
    const ended = mockActivities.filter(a => a.status === 'ended').length
    const cancelled = mockActivities.filter(a => a.status === 'cancelled').length
    
    return {
      totalActivities: total,
      upcomingActivities: upcoming,
      ongoingActivities: ongoing,
      endedActivities: ended,
      cancelledActivities: cancelled,
      totalParticipants: mockActivities.reduce((sum, a) => sum + a.currentParticipants, 0),
      todayRegistrations: 25,
      participationRate: 85.6,
      averageRating: 4.6,
      activitiesByType: {
        conference: 2,
        training: 1,
        workshop: 0,
        seminar: 0,
        competition: 0,
        exhibition: 0,
        social: 0,
        other: 0
      } as any,
      activitiesByStatus: {
        upcoming: upcoming,
        ongoing: ongoing,
        ended: ended,
        cancelled: cancelled
      } as any,
      participationsByType: {
        registration: 156,
        check_in: 98,
        survey: 45,
        vote: 23
      } as any,
      recentActivities: mockActivityActivities,
      popularActivities: mockActivities.slice(0, 3),
      topOrganizers: mockOrganizerStats
    }
  }

  // 获取参与记录
  static async getParticipationRecords(params?: ParticipationQueryParams): Promise<{
    data: ParticipationRecord[]
    total: number
    page: number
    pageSize: number
  }> {
    await this.delay()
    
    let filteredData = [...mockParticipationRecords]
    
    // 活动ID筛选
    if (params?.activityId) {
      filteredData = filteredData.filter(record => record.activityId === params.activityId)
    }
    
    // 用户ID筛选
    if (params?.userId) {
      filteredData = filteredData.filter(record => record.userId === params.userId)
    }
    
    // 参与类型筛选
    if (params?.type) {
      filteredData = filteredData.filter(record => record.type === params.type)
    }
    
    // 状态筛选
    if (params?.status) {
      filteredData = filteredData.filter(record => record.status === params.status)
    }
    
    // 分页
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    
    return {
      data: filteredData.slice(start, end),
      total: filteredData.length,
      page,
      pageSize
    }
  }

  // 创建活动参与记录
  static async createParticipation(data: Partial<ParticipationRecord>): Promise<ParticipationOperationResult> {
    await this.delay()
    
    const newRecord: ParticipationRecord = {
      id: `part_${Date.now()}`,
      activityId: data.activityId!,
      activityTitle: data.activityTitle!,
      userId: data.userId!,
      userName: data.userName!,
      userPhone: data.userPhone,
      userEmail: data.userEmail,
      type: data.type!,
      status: 'pending' as ParticipationStatus,
      data: data.data || {},
      submitTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updateTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
    
    mockParticipationRecords.push(newRecord)
    
    return {
      success: true,
      message: '参与记录创建成功',
      data: newRecord
    }
  }

  // 更新参与记录状态
  static async updateParticipationStatus(
    id: string, 
    status: ParticipationStatus, 
    notes?: string
  ): Promise<ParticipationOperationResult> {
    await this.delay()
    
    const record = mockParticipationRecords.find(r => r.id === id)
    if (!record) {
      return {
        success: false,
        message: '参与记录不存在',
        errors: ['Record not found']
      }
    }
    
    record.status = status
    record.updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
    if (notes) {
      record.notes = notes
    }
    
    if (status === 'approved') {
      record.approveTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      record.approvedBy = 'admin'
    } else if (status === 'completed') {
      record.completeTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
    }
    
    return {
      success: true,
      message: '状态更新成功',
      data: record
    }
  }
}
