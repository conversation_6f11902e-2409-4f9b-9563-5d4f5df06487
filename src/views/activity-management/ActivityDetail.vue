<template>
  <div class="activity-detail">
    <a-spin :spinning="loading">
      <!-- 页面头部 -->
      <div class="page-header">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/activity-management">活动管理</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <router-link to="/activity-management/list">活动列表</router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>活动详情</a-breadcrumb-item>
        </a-breadcrumb>
        
        <div class="header-actions">
          <a-space>
            <a-button @click="goBack">
              <template #icon><arrow-left-outlined /></template>
              返回
            </a-button>
            <a-button @click="editActivity">
              <template #icon><edit-outlined /></template>
              编辑
            </a-button>
            <a-dropdown>
              <a-button>
                更多操作 <down-outlined />
              </a-button>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="duplicate">复制活动</a-menu-item>
                  <a-menu-item key="export">导出数据</a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" class="danger-item">删除活动</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>

      <!-- 活动基本信息 -->
      <div class="activity-info">
        <a-row :gutter="[24, 24]">
          <!-- 左侧主要信息 -->
          <a-col :xs="24" :lg="16">
            <a-card>
              <div class="activity-header">
                <h1>{{ activity?.title }}</h1>
                <div class="activity-meta">
                  <a-space wrap>
                    <a-tag :color="getTypeColor(activity?.type)">
                      {{ getTypeText(activity?.type) }}
                    </a-tag>
                    <a-badge 
                      :status="getStatusBadge(activity?.status)" 
                      :text="getStatusText(activity?.status)"
                    />
                    <a-tag v-for="tag in activity?.tags" :key="tag" color="blue">
                      {{ tag }}
                    </a-tag>
                  </a-space>
                </div>
              </div>

              <a-divider />

              <div class="activity-description">
                <h3>活动描述</h3>
                <p>{{ activity?.description }}</p>
              </div>

              <div v-if="activity?.content" class="activity-content">
                <h3>详细内容</h3>
                <div class="content-text">{{ activity.content }}</div>
              </div>

              <!-- 活动图片 -->
              <div v-if="activity?.images && activity.images.length > 0" class="activity-images">
                <h3>活动图片</h3>
                <div class="image-gallery">
                  <a-image-preview-group>
                    <a-image
                      v-for="image in activity.images"
                      :key="image.id"
                      :src="image.url"
                      :alt="image.title"
                      :width="120"
                      :height="80"
                      class="gallery-image"
                    />
                  </a-image-preview-group>
                </div>
              </div>

              <!-- 活动要求和收益 -->
              <a-row :gutter="[16, 16]" v-if="activity?.requirements || activity?.benefits">
                <a-col :xs="24" :md="12" v-if="activity?.requirements">
                  <div class="activity-requirements">
                    <h3>参与要求</h3>
                    <ul>
                      <li v-for="req in activity.requirements" :key="req">{{ req }}</li>
                    </ul>
                  </div>
                </a-col>
                <a-col :xs="24" :md="12" v-if="activity?.benefits">
                  <div class="activity-benefits">
                    <h3>活动收益</h3>
                    <ul>
                      <li v-for="benefit in activity.benefits" :key="benefit">{{ benefit }}</li>
                    </ul>
                  </div>
                </a-col>
              </a-row>
            </a-card>
          </a-col>

          <!-- 右侧信息面板 -->
          <a-col :xs="24" :lg="8">
            <!-- 活动状态和倒计时 -->
            <a-card class="status-card" :bordered="false">
              <div class="status-info">
                <div class="status-badge">
                  <a-badge 
                    :status="getStatusBadge(activity?.status)" 
                    :text="getStatusText(activity?.status)"
                  />
                </div>
                <div v-if="activity?.status === 'upcoming'" class="countdown">
                  <h4>距离开始还有</h4>
                  <div class="countdown-time">
                    <a-statistic-countdown 
                      :value="getCountdownValue(activity?.startTime)"
                      format="D 天 H 时 m 分 s 秒"
                    />
                  </div>
                </div>
                <div v-else-if="activity?.status === 'ongoing'" class="countdown">
                  <h4>距离结束还有</h4>
                  <div class="countdown-time">
                    <a-statistic-countdown 
                      :value="getCountdownValue(activity?.endTime)"
                      format="D 天 H 时 m 分 s 秒"
                    />
                  </div>
                </div>
              </div>
            </a-card>

            <!-- 活动详细信息 -->
            <a-card title="活动信息" :bordered="false">
              <a-descriptions :column="1" size="small">
                <a-descriptions-item label="开始时间">
                  <calendar-outlined /> {{ formatTime(activity?.startTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="结束时间">
                  <calendar-outlined /> {{ formatTime(activity?.endTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="活动地点">
                  <environment-outlined /> {{ activity?.location }}
                </a-descriptions-item>
                <a-descriptions-item label="组织者">
                  <user-outlined /> {{ activity?.organizer }}
                </a-descriptions-item>
                <a-descriptions-item label="参与情况">
                  <team-outlined /> 
                  {{ activity?.currentParticipants }}
                  <span v-if="activity?.maxParticipants">/ {{ activity.maxParticipants }}</span>
                  人
                </a-descriptions-item>
                <a-descriptions-item v-if="activity?.contactInfo" label="联系方式">
                  <phone-outlined /> {{ activity.contactInfo.phone }}
                  <br v-if="activity.contactInfo.email">
                  <mail-outlined v-if="activity.contactInfo.email" /> {{ activity.contactInfo.email }}
                </a-descriptions-item>
              </a-descriptions>
            </a-card>

            <!-- 参与操作 -->
            <a-card title="参与活动" :bordered="false">
              <div class="participation-actions">
                <a-space direction="vertical" style="width: 100%">
                  <a-button 
                    type="primary" 
                    block 
                    size="large"
                    :disabled="!canParticipate"
                    @click="participate('registration')"
                  >
                    <template #icon><user-add-outlined /></template>
                    {{ getParticipationText('registration') }}
                  </a-button>
                  
                  <a-button 
                    block 
                    :disabled="!canCheckIn"
                    @click="participate('check_in')"
                  >
                    <template #icon><check-circle-outlined /></template>
                    {{ getParticipationText('check_in') }}
                  </a-button>
                  
                  <a-button 
                    block 
                    :disabled="!canSurvey"
                    @click="participate('survey')"
                  >
                    <template #icon><form-outlined /></template>
                    {{ getParticipationText('survey') }}
                  </a-button>
                  
                  <a-button 
                    block 
                    :disabled="!canVote"
                    @click="participate('vote')"
                  >
                    <template #icon><like-outlined /></template>
                    {{ getParticipationText('vote') }}
                  </a-button>
                </a-space>
              </div>
            </a-card>

            <!-- 参与状态 -->
            <a-card title="我的参与状态" :bordered="false">
              <div class="participation-status">
                <a-steps direction="vertical" size="small" :current="currentStep">
                  <a-step title="活动报名" :status="getStepStatus('registration')">
                    <template #description>
                      <span v-if="participationStatus.registration">
                        {{ participationStatus.registration.submitTime }}
                      </span>
                      <span v-else class="text-muted">未报名</span>
                    </template>
                  </a-step>
                  <a-step title="现场签到" :status="getStepStatus('check_in')">
                    <template #description>
                      <span v-if="participationStatus.check_in">
                        {{ participationStatus.check_in.submitTime }}
                      </span>
                      <span v-else class="text-muted">未签到</span>
                    </template>
                  </a-step>
                  <a-step title="问卷调查" :status="getStepStatus('survey')">
                    <template #description>
                      <span v-if="participationStatus.survey">
                        {{ participationStatus.survey.submitTime }}
                      </span>
                      <span v-else class="text-muted">未完成</span>
                    </template>
                  </a-step>
                  <a-step title="活动投票" :status="getStepStatus('vote')">
                    <template #description>
                      <span v-if="participationStatus.vote">
                        {{ participationStatus.vote.submitTime }}
                      </span>
                      <span v-else class="text-muted">未投票</span>
                    </template>
                  </a-step>
                </a-steps>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 参与者列表 -->
      <div class="participants-section">
        <a-card title="参与者列表" :bordered="false">
          <template #extra>
            <a-space>
              <span>共 {{ activity?.currentParticipants }} 人参与</span>
              <a-button size="small" @click="refreshParticipants">
                <template #icon><reload-outlined /></template>
                刷新
              </a-button>
            </a-space>
          </template>
          
          <div class="participants-list">
            <a-empty v-if="!participants.length" description="暂无参与者" />
            <a-list v-else :data-source="participants" :pagination="false">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #avatar>
                      <a-avatar>{{ item.userName.charAt(0) }}</a-avatar>
                    </template>
                    <template #title>
                      {{ item.userName }}
                    </template>
                    <template #description>
                      {{ item.type }} · {{ item.submitTime }}
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-tag :color="getParticipationStatusColor(item.status)">
                      {{ getParticipationStatusText(item.status) }}
                    </a-tag>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  EditOutlined,
  DownOutlined,
  CalendarOutlined,
  EnvironmentOutlined,
  UserOutlined,
  TeamOutlined,
  PhoneOutlined,
  MailOutlined,
  UserAddOutlined,
  CheckCircleOutlined,
  FormOutlined,
  LikeOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import type { 
  Activity, 
  ActivityType, 
  ActivityStatus,
  ParticipationType,
  ParticipationStatus,
  ParticipationRecord
} from '@/types/activity'
import { MockActivityService } from './mock/data'
import dayjs from 'dayjs'

// 路由
const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const activity = ref<Activity | null>(null)
const participants = ref<ParticipationRecord[]>([])

// 参与状态
const participationStatus = reactive<Record<ParticipationType, ParticipationRecord | null>>({
  registration: null,
  check_in: null,
  survey: null,
  vote: null
})

// 计算属性
const currentStep = computed(() => {
  if (participationStatus.vote) return 3
  if (participationStatus.survey) return 2
  if (participationStatus.check_in) return 1
  if (participationStatus.registration) return 0
  return -1
})

const canParticipate = computed(() => {
  return activity.value?.status === 'upcoming' || activity.value?.status === 'ongoing'
})

const canCheckIn = computed(() => {
  return activity.value?.status === 'ongoing' && participationStatus.registration
})

const canSurvey = computed(() => {
  return activity.value?.status === 'ongoing' && participationStatus.check_in
})

const canVote = computed(() => {
  return activity.value?.status === 'ongoing' && participationStatus.check_in
})

// 工具方法
const getTypeColor = (type?: ActivityType) => {
  if (!type) return 'default'
  const colors = {
    conference: 'blue',
    training: 'green',
    workshop: 'orange',
    seminar: 'purple',
    competition: 'red',
    exhibition: 'cyan',
    social: 'pink',
    other: 'default'
  }
  return colors[type] || 'default'
}

const getTypeText = (type?: ActivityType) => {
  if (!type) return ''
  const texts = {
    conference: '会议',
    training: '培训',
    workshop: '研讨会',
    seminar: '讲座',
    competition: '竞赛',
    exhibition: '展览',
    social: '社交活动',
    other: '其他'
  }
  return texts[type] || type
}

const getStatusBadge = (status?: ActivityStatus) => {
  if (!status) return 'default'
  const badges = {
    upcoming: 'warning',
    ongoing: 'processing',
    ended: 'success',
    cancelled: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status?: ActivityStatus) => {
  if (!status) return ''
  const texts = {
    upcoming: '即将开始',
    ongoing: '进行中',
    ended: '已结束',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatTime = (time?: string) => {
  if (!time) return ''
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const getCountdownValue = (time?: string) => {
  if (!time) return Date.now()
  return dayjs(time).valueOf()
}

const getParticipationText = (type: ParticipationType) => {
  const texts = {
    registration: participationStatus.registration ? '已报名' : '立即报名',
    check_in: participationStatus.check_in ? '已签到' : '现场签到',
    survey: participationStatus.survey ? '已完成' : '填写问卷',
    vote: participationStatus.vote ? '已投票' : '参与投票'
  }
  return texts[type]
}

const getStepStatus = (type: ParticipationType) => {
  if (participationStatus[type]) {
    return participationStatus[type]?.status === 'completed' ? 'finish' : 'process'
  }
  return 'wait'
}

const getParticipationStatusColor = (status: ParticipationStatus) => {
  const colors = {
    pending: 'orange',
    approved: 'blue',
    rejected: 'red',
    completed: 'green',
    cancelled: 'default'
  }
  return colors[status] || 'default'
}

const getParticipationStatusText = (status: ParticipationStatus) => {
  const texts = {
    pending: '待处理',
    approved: '已通过',
    rejected: '已拒绝',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 事件处理
const goBack = () => {
  router.go(-1)
}

const editActivity = () => {
  message.info('编辑活动功能开发中...')
}

const handleMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'duplicate':
      message.info('复制活动功能开发中...')
      break
    case 'export':
      message.info('导出数据功能开发中...')
      break
    case 'delete':
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除活动"${activity.value?.title}"吗？`,
        onOk: () => {
          message.success('删除成功')
          router.push('/activity-management/list')
        }
      })
      break
  }
}

const participate = (type: ParticipationType) => {
  router.push(`/activity-management/participation/${route.params.id}/${type}`)
}

const refreshParticipants = () => {
  loadParticipants()
}

// 数据加载
const loadData = async () => {
  loading.value = true
  try {
    const activityId = route.params.id as string
    
    // 加载活动详情
    const activityData = await MockActivityService.getActivityById(activityId)
    if (!activityData) {
      message.error('活动不存在')
      router.push('/activity-management/list')
      return
    }
    activity.value = activityData
    
    // 加载参与记录
    await loadParticipants()
    
    // 模拟加载当前用户的参与状态
    // 这里应该根据实际的用户ID来查询
    // 暂时使用模拟数据
    
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadParticipants = async () => {
  try {
    const activityId = route.params.id as string
    const result = await MockActivityService.getParticipationRecords({
      activityId,
      page: 1,
      pageSize: 20
    })
    participants.value = result.data
  } catch (error) {
    console.error('加载参与者失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.activity-detail {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .activity-info {
    margin-bottom: 24px;

    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;

      .ant-card-body {
        padding: 24px;
      }
    }

    .activity-header {
      h1 {
        margin: 0 0 16px 0;
        font-size: 28px;
        font-weight: 600;
        color: #262626;
        line-height: 1.3;
      }

      .activity-meta {
        .ant-tag,
        .ant-badge {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
    }

    .activity-description,
    .activity-content {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #595959;
        line-height: 1.6;
      }

      .content-text {
        color: #595959;
        line-height: 1.6;
        white-space: pre-wrap;
      }
    }

    .activity-images {
      margin-bottom: 24px;

      h3 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .gallery-image {
          border-radius: 6px;
          object-fit: cover;
        }
      }
    }

    .activity-requirements,
    .activity-benefits {
      h3 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      ul {
        margin: 0;
        padding-left: 20px;
        color: #595959;

        li {
          margin-bottom: 4px;
          line-height: 1.5;
        }
      }
    }

    .status-card {
      .status-info {
        text-align: center;

        .status-badge {
          margin-bottom: 16px;

          :deep(.ant-badge) {
            font-size: 16px;
            font-weight: 500;
          }
        }

        .countdown {
          h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #8c8c8c;
          }

          .countdown-time {
            :deep(.ant-statistic) {
              .ant-statistic-content {
                font-size: 18px;
                font-weight: 600;
                color: #1890ff;
              }
            }
          }
        }
      }
    }

    .participation-actions {
      .ant-btn {
        height: 40px;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .participation-status {
      :deep(.ant-steps) {
        .ant-steps-item-title {
          font-size: 13px;
        }

        .ant-steps-item-description {
          font-size: 12px;
        }
      }

      .text-muted {
        color: #bfbfbf;
      }
    }
  }

  .participants-section {
    :deep(.ant-card) {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .participants-list {
      :deep(.ant-list-item) {
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ant-list-item-meta {
          .ant-list-item-meta-title {
            font-size: 14px;
            font-weight: 500;
          }

          .ant-list-item-meta-description {
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }
  }

  :deep(.danger-item) {
    color: #ff4d4f !important;

    &:hover {
      background-color: #fff2f0 !important;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .activity-info {
      .activity-header {
        h1 {
          font-size: 22px;
        }
      }

      .image-gallery {
        .gallery-image {
          width: 80px !important;
          height: 60px !important;
        }
      }
    }
  }
}
</style>
