<template>
	<div class="honor-apply-page">
		<!-- 页面头部操作区 -->
		<div class="page-header">
			<div class="header-actions">
				<a-space size="middle">
					<a-button type="primary" @click="showApplyModal">
						<template #icon><plus-outlined /></template>
						新建申报
					</a-button>
					<a-button @click="showBatchImportModal">
						<template #icon><upload-outlined /></template>
						批量导入
					</a-button>
					<a-button @click="exportData">
						<template #icon><export-outlined /></template>
						批量导出
					</a-button>
				</a-space>
			</div>
		</div>

		<!-- 搜索筛选区域 -->
		<div class="search-filter-section">
			<a-card>
				<a-form :model="searchForm" class="search-form">
					<a-row :gutter="[16, 16]" style="width: 100%">
						<a-col :xs="24" :sm="12" :md="8" :lg="4">
							<a-form-item label="姓名" class="form-item-full">
								<a-input v-model:value="searchForm.applicant" placeholder="请输入申报人姓名" allow-clear />
							</a-form-item>
						</a-col>
						<a-col :xs="24" :sm="12" :md="8" :lg="5">
							<a-form-item label="申报类型" class="form-item-full">
								<a-select v-model:value="searchForm.type" placeholder="请选择申报类型" allow-clear>
									<a-select-option v-for="item in HonorTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xs="24" :sm="12" :md="8" :lg="4">
							<a-form-item label="状态" class="form-item-full">
								<a-select v-model:value="searchForm.status" placeholder="请选择状态" allow-clear>
									<a-select-option :value="1">待审核</a-select-option>
									<a-select-option :value="2">已通过</a-select-option>
									<a-select-option :value="3">已驳回</a-select-option>
								</a-select>
							</a-form-item>
						</a-col>
						<a-col :xs="24" :sm="24" :md="16" :lg="7">
							<a-form-item label="提交时间" class="form-item-full">
								<a-range-picker v-model:value="searchForm.dateRange" format="YYYY-MM-DD" style="width: 100%" />
							</a-form-item>
						</a-col>
						<a-col :xs="24" :sm="24" :md="8" :lg="3">
							<a-form-item class="form-item-full search-actions">
								<a-space>
									<a-button type="primary" @click="handleSearch">搜索</a-button>
									<a-button @click="resetSearch">重置</a-button>
								</a-space>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-card>
		</div>

		<!-- 批量导入弹窗 -->
		<a-modal title="批量导入荣誉申报" :visible="batchImportVisible" @cancel="batchImportVisible = false" :footer="null" width="700px">
			<div class="batch-import-content">
				<a-alert message="导入说明" description="请先下载模板文件，按照模板格式填写数据后上传。" type="info" show-icon />
				<a-button @click="downloadTemplate" class="download-template-button">
					<template #icon><download-outlined /></template>
					下载模板
				</a-button>
				<a-upload-dragger :before-upload="handleBatchImport" :show-upload-list="false" accept=".xlsx,.xls">
					<p class="ant-upload-drag-icon">
						<inbox-outlined />
					</p>
					<p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
					<p class="ant-upload-hint">支持单个文件上传，仅支持Excel格式</p>
				</a-upload-dragger>
				<!-- <div class="mt-4 text-center">
					<a-button @click="batchImportVisible = false">取消</a-button>
				</div> -->
			</div>
		</a-modal>

		<!-- 统一的申报表单弹窗 -->
		<a-modal :title="modalTitle" :visible="formModalVisible" @cancel="formModalVisible = false" :footer="null" width="700px">
			<a-form :model="form" :rules="rules" ref="formRef" :label-col="{ span: 4 }" :wrapper-col="{ span: 16 }" @finish="onSubmit">
				<a-form-item label="申报人" name="applicant">
					<a-input v-model:value="form.applicant" placeholder="请输入申报人姓名" />
				</a-form-item>
				<a-form-item label="部门" name="department">
					<a-input v-model:value="form.department" placeholder="请输入所属部门" />
				</a-form-item>
				<a-form-item label="类型" name="type">
					<a-select v-model:value="form.type" placeholder="请选择申报类型">
						<a-select-option v-for="item in HonorTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item label="申报材料" name="materialFiles">
					<FileUpload
						v-model="form.materialFiles"
						:multiple="true"
						:max-size="50"
						:max-count="5"
						up-type="eval-file"
						accept=".pdf,.doc,.docx,.jpg,.png,.jpeg"
						@change="handleMaterialChange"
						@error="handleUploadError"
					/>
				</a-form-item>
				<a-form-item label="说明" name="description">
					<a-textarea v-model:value="form.description" :rows="3" placeholder="请输入申报说明" />
				</a-form-item>
				<a-form-item :wrapper-col="{ offset: 4, span: 16 }">
					<a-button type="primary" html-type="submit" :loading="submitting">{{ submitButtonText }}</a-button>
					<a-button style="margin-left: 10px" @click="formModalVisible = false">取消</a-button>
				</a-form-item>
			</a-form>
		</a-modal>

		<a-modal title="荣誉申报详情" :visible="detailModalVisible" @cancel="detailModalVisible = false" :footer="false" width="700px">
			<a-descriptions bordered :column="1">
				<a-descriptions-item label="申报人">{{ currentRecord?.applicant }}</a-descriptions-item>
				<a-descriptions-item label="部门">{{ currentRecord?.department }}</a-descriptions-item>
				<a-descriptions-item label="类型">
					<a-tag color="blue">{{ getTypeText(currentRecord?.type) }}</a-tag>
				</a-descriptions-item>
				<a-descriptions-item label="状态">
					<a-tag :color="getStatusColor(currentRecord?.status)">{{ getStatusText(currentRecord?.status) }}</a-tag>
				</a-descriptions-item>
				<a-descriptions-item label="提交时间">{{ currentRecord?.createTime || '暂无' }}</a-descriptions-item>
				<a-descriptions-item label="材料">
					<a v-if="currentRecord?.materialUrl" :href="currentRecord.materialUrl" target="_blank">查看材料</a>
					<span v-else>无</span>
				</a-descriptions-item>
				<a-descriptions-item label="说明">{{ currentRecord?.description || '无' }}</a-descriptions-item>
				<a-descriptions-item v-if="currentRecord?.auditOpinion" label="审核意见">{{ currentRecord.auditOpinion }}</a-descriptions-item>
			</a-descriptions>
		</a-modal>

		<!-- 数据表格区域 -->
		<div class="table-section">
			<a-card>
				<template #title>
					<div class="table-header">
						<span>我的申报记录</span>
						<span class="record-count">共 {{ filteredHonorList.length }} 条记录</span>
					</div>
				</template>
				<a-table
					:dataSource="filteredHonorList"
					:loading="honorStore.loading"
					rowKey="id"
					:pagination="{
						pageSize: 50,
						showSizeChanger: true,
						showQuickJumper: true,
						pageSizeOptions: ['20', '50', '100', '200'],
						showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
					}"
				>
					<a-table-column title="序号" width="80">
						<template #default="{ index }">
							{{ index + 1 }}
						</template>
					</a-table-column>
					<a-table-column title="申报人" dataIndex="applicant" />
					<a-table-column title="所属部门" dataIndex="department" />
					<a-table-column title="申报类型" dataIndex="type">
						<template #default="{ record }">
							{{ getTypeText(record.type) }}
						</template>
					</a-table-column>
					<a-table-column title="提交时间" dataIndex="createTime" />
					<a-table-column title="状态" dataIndex="status">
						<template #default="{ record }">
							<a-tag :color="getStatusColor(record.status)">
								{{ getStatusText(record.status) }}
							</a-tag>
						</template>
					</a-table-column>
					<a-table-column title="操作" key="action" width="200">
						<template #default="{ record }">
							<a-space>
								<a-button type="link" size="small" @click="viewDetail(record)">查看</a-button>
								<a-button type="link" size="small" @click="editRecord(record)">编辑</a-button>
								<a-popconfirm title="确定删除？" @confirm="deleteRecord(record.id)">
									<a-button type="link" size="small" danger>删除</a-button>
								</a-popconfirm>
							</a-space>
						</template>
					</a-table-column>
				</a-table>
			</a-card>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'
import { useHonorStore } from '@/store/modules/honor'
import type { HonorApplyForm, HonorRecord, HonorStatus } from '@/types/honor'
import { StatusTextMap, StatusColorMap, HonorTypeTextMap, HonorTypeOptions } from '@/types/honor'
import { submitHonorApply, updateHonorApply, deleteHonor } from '@/api/honor'
import type { HonorSearchParams } from '@/api/honor'
import { message } from 'ant-design-vue'
import { PlusOutlined, UploadOutlined, DownloadOutlined, ExportOutlined, InboxOutlined } from '@ant-design/icons-vue'
import * as XLSX from 'xlsx'
import ExcelJS from 'exceljs'
import dayjs from 'dayjs'
import { fetchOP } from '@/utils/request'
import { debounce } from 'lodash-es'
import FileUpload from '@/components/file-upload/index.vue'

// 文件接口定义
interface FileItem {
  id?: string | number
  file_id?: string | number
  name: string
  path?: string
  size?: number
  type?: string
  status?: 'uploading' | 'done' | 'error'
  progress?: number
  uploadTime?: string
  url?: string
}

// 错误处理工具函数
const handleApiError = (error: any, defaultMessage: string = '操作失败') => {
	console.error('API错误:', error)
	if (error.response?.data?.message) {
		message.error(error.response.data.message)
	} else if (error.message) {
		message.error(error.message)
	} else {
		message.error(defaultMessage)
	}
}

// 搜索筛选表单
const searchForm = reactive({
	applicant: '',
	department: '',
	type: undefined as number | undefined,
	status: undefined as HonorStatus | undefined,
	dateRange: [] as any[],
})

// 批量导入
const batchImportVisible = ref(false)

// 统一表单弹窗
const formModalVisible = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const formRef = ref()
const form = ref<HonorApplyForm & { materialFiles: FileItem[] }>({
	applicant: '',
	department: '',
	type: 1,
	materialUrl: '',
	description: '',
	materialFiles: []
})

// 详情弹窗
const detailModalVisible = ref(false)
const currentRecord = ref<HonorRecord | null>(null)

// 计算属性：弹窗标题
const modalTitle = computed(() => {
	return formMode.value === 'create' ? '新建荣誉申报' : '编辑荣誉申报'
})

// 计算属性：提交按钮文本
const submitButtonText = computed(() => {
	return formMode.value === 'create' ? '提交申报' : '更新申报'
})

const rules = {
	applicant: [{ required: true, message: '请输入申报人' }],
	department: [{ required: true, message: '请输入部门' }],
	type: [{ required: true, message: '请选择类型' }],
	materialFiles: [{ required: true, message: '请上传材料' }],
	description: [{ required: true, message: '请输入说明' }],
}
const submitting = ref(false)

const honorStore = useHonorStore()

// 筛选后的数据
const filteredHonorList = computed(() => {
	// 确保 honorList 是数组
	if (!honorStore.honorList || !Array.isArray(honorStore.honorList)) {
		return []
	}

	let filtered = [...honorStore.honorList]

	// 按姓名筛选
	if (searchForm.applicant) {
		filtered = filtered.filter((item) => item.applicant && item.applicant.includes(searchForm.applicant))
	}

	// 按部门筛选
	if (searchForm.department) {
		filtered = filtered.filter((item) => item.department === searchForm.department)
	}

	// 按类型筛选
	if (searchForm.type) {
		filtered = filtered.filter((item) => item.type === searchForm.type)
	}

	// 按状态筛选
	if (searchForm.status) {
		filtered = filtered.filter((item) => item.status === searchForm.status)
	}

	// 按时间范围筛选
	if (searchForm.dateRange && searchForm.dateRange.length === 2) {
		const [startDate, endDate] = searchForm.dateRange
		filtered = filtered.filter((item) => {
			if (!item.createTime) return false
			const itemDate = dayjs(item.createTime)
			return itemDate.isAfter(dayjs(startDate).subtract(1, 'day')) && itemDate.isBefore(dayjs(endDate).add(1, 'day'))
		})
	}

	return filtered
})

// 状态相关工具函数
function getStatusColor(status?: HonorStatus) {
	return status ? StatusColorMap[status] || 'default' : 'default'
}

function getStatusText(status?: HonorStatus) {
	return status ? StatusTextMap[status] || status : '未知'
}

// 申报类型工具函数
function getTypeText(type?: number) {
	if (!type) return '未知'
	return HonorTypeTextMap[type as keyof typeof HonorTypeTextMap] || type
}

// 搜索和重置功能
const debouncedSearch = debounce(async () => {
	// 构建搜索参数
	const searchParams: HonorSearchParams = {
		applicant: searchForm.applicant || undefined,
		department: searchForm.department || undefined,
		type: searchForm.type || undefined,
		status: searchForm.status || undefined,
	}

	// 处理时间范围
	if (searchForm.dateRange && searchForm.dateRange.length === 2) {
		searchParams.startTime = dayjs(searchForm.dateRange[0]).format('YYYY-MM-DD')
		searchParams.endTime = dayjs(searchForm.dateRange[1]).format('YYYY-MM-DD')
	}

	console.log('执行搜索', searchParams)
	await honorStore.fetchList(searchParams)
}, 300)

function handleSearch() {
	debouncedSearch()
}

function resetSearch() {
	searchForm.applicant = ''
	searchForm.department = ''
	searchForm.type = undefined
	searchForm.status = undefined
	searchForm.dateRange = []
	// 重置后重新搜索
	handleSearch()
}

// 显示新建申报弹窗
function showApplyModal() {
	formMode.value = 'create'
	// 重置表单数据
	form.value = {
		applicant: '',
		department: '',
		type: undefined,
		materialUrl: '',
		description: '',
		materialFiles: []
	}
	formModalVisible.value = true
}

function showBatchImportModal() {
	batchImportVisible.value = true
}

// 模板下载功能（使用ExcelJS支持下拉选择）
async function downloadTemplate() {
	const workbook = new ExcelJS.Workbook()

	// 创建主模板工作表
	const worksheet = workbook.addWorksheet('荣誉申报模板')

	// 设置表头
	worksheet.columns = [
		{ header: '申报人', key: 'applicant', width: 15 },
		{ header: '所属部门', key: 'department', width: 20 },
		{ header: '申报类型', key: 'type', width: 20 },
		{ header: '申报说明', key: 'description', width: 40 },
	]

	// 添加示例数据
	worksheet.addRow({
		applicant: '周海军',
		department: '技术研发部',
		type: '优秀共产党员',
		description: '工作表现突出，积极参与党组织活动',
	})

	// 设置表头样式
	worksheet.getRow(1).font = { bold: true }
	worksheet.getRow(1).fill = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: { argb: 'FFE6F3FF' },
	}

	// 为申报类型列添加数据验证（下拉选择）
	const typeLabels = HonorTypeOptions.map((item) => item.label)
	for (let i = 2; i <= 1000; i++) {
		worksheet.getCell(`C${i}`).dataValidation = {
			type: 'list',
			allowBlank: false,
			formulae: [`"${typeLabels.join(',')}"`],
			showErrorMessage: true,
			errorStyle: 'error',
			errorTitle: '输入错误',
			error: '请从下拉列表中选择有效的申报类型',
		}
	}

	// 创建填写说明工作表
	const instructionSheet = workbook.addWorksheet('填写说明')
	instructionSheet.columns = [
		{ header: '字段名称', key: 'field', width: 15 },
		{ header: '是否必填', key: 'required', width: 12 },
		{ header: '说明', key: 'description', width: 35 },
		{ header: '示例', key: 'example', width: 25 },
	]

	instructionSheet.addRows([
		{ field: '申报人', required: '是', description: '输入申报人姓名', example: '周海军' },
		{ field: '所属部门', required: '是', description: '输入所属部门名称', example: '技术研发部' },
		{ field: '申报类型', required: '是', description: '从下拉列表中选择申报类型', example: '优秀共产党员' },
		{ field: '申报说明', required: '是', description: '详细说明申报理由和工作表现', example: '工作表现突出，积极参与党组织活动' },
	])

	// 设置说明表头样式
	instructionSheet.getRow(1).font = { bold: true }
	instructionSheet.getRow(1).fill = {
		type: 'pattern',
		pattern: 'solid',
		fgColor: { argb: 'FFFFEAA7' },
	}

	// 生成并下载文件
	const buffer = await workbook.xlsx.writeBuffer()
	const blob = new Blob([buffer], {
		type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
	})

	const url = window.URL.createObjectURL(blob)
	const link = document.createElement('a')
	link.href = url
	link.download = '荣誉申报导入模板.xlsx'
	link.click()
	window.URL.revokeObjectURL(url)
	message.success('模板下载成功！')
}

// 批量导入功能
function handleBatchImport(file: File) {
	const reader = new FileReader()
	reader.onload = (e) => {
		try {
			const data = new Uint8Array(e.target?.result as ArrayBuffer)
			const workbook = XLSX.read(data, { type: 'array' })
			const sheetName = workbook.SheetNames[0]
			const worksheet = workbook.Sheets[sheetName]
			const jsonData = XLSX.utils.sheet_to_json(worksheet)

			if (jsonData.length === 0) {
				message.error('Excel文件为空')
				return
			}

			// 验证数据格式
			const validData = jsonData.filter((row: any) => {
				return row['申报人'] && row['所属部门'] && row['申报类型']
			})

			if (validData.length === 0) {
				message.error('没有找到有效的数据行')
				return
			}

			// 转换数据格式
			const importData = validData.map((row: any) => {
				// 将申报类型文本转换为数字
				const typeText = row['申报类型']
				const typeOption = HonorTypeOptions.find((option) => option.label === typeText)
				const typeValue = typeOption ? typeOption.value : 1 // 默认为第一个选项

				return {
					applicant: row['申报人'],
					department: row['所属部门'],
					type: typeValue,
					materialUrl: row['材料说明'] || '',
					description: row['申报说明'] || '',
				}
			})

			// 批量提交数据
			batchSubmitData(importData)
		} catch (error) {
			message.error('文件解析失败，请检查文件格式')
			console.error('导入错误:', error)
		}
	}
	reader.readAsArrayBuffer(file)
	return false // 阻止自动上传
}

// 批量提交数据
async function batchSubmitData(dataList: HonorApplyForm[]) {
	let successCount = 0
	let failCount = 0

	for (const data of dataList) {
		try {
			const response = await submitHonorApply(data)
			const success = fetchOP(response, false)
			if (success) {
				successCount++
			} else {
				failCount++
			}
		} catch (error) {
			failCount++
			console.error('提交失败:', data, error)
		}
	}

	message.success(`导入完成：成功 ${successCount} 条，失败 ${failCount} 条`)
	batchImportVisible.value = false
	honorStore.fetchList()
}

// 批量导出功能（异步优化）
async function exportData() {
	if (filteredHonorList.value.length === 0) {
		message.warning('没有数据可导出')
		return
	}

	const loading = message.loading('正在导出数据，请稍候...', 0)

	try {
		// 让UI先更新
		await new Promise((resolve) => setTimeout(resolve, 100))

		const exportData = filteredHonorList.value.map((item, index) => ({
			序号: index + 1,
			申报人: item.applicant,
			所属部门: item.department,
			申报类型: getTypeText(item.type),
			提交时间: item.createTime,
			状态: getStatusText(item.status),
			申报说明: item.description,
		}))

		const ws = XLSX.utils.json_to_sheet(exportData)
		const wb = XLSX.utils.book_new()
		XLSX.utils.book_append_sheet(wb, ws, '荣誉申报记录')

		// 设置列宽
		ws['!cols'] = [{ width: 8 }, { width: 15 }, { width: 20 }, { width: 20 }, { width: 20 }, { width: 12 }, { width: 40 }]

		const fileName = `荣誉申报记录_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`
		XLSX.writeFile(wb, fileName)
		message.success('导出成功')
	} catch (error) {
		handleApiError(error, '导出失败')
	} finally {
		loading()
	}
}

// 文件上传处理函数
function handleMaterialChange(files: FileItem[]) {
	// 更新表单中的材料URL字段
	if (files.length > 0) {
		form.value.materialUrl = files.map(f => f.path || f.name).join(',')
	} else {
		form.value.materialUrl = ''
	}
}

function handleUploadError(error: string, file?: File) {
	console.error('文件上传错误:', error, file)
	message.error(`文件上传失败: ${error}`)
}

async function onSubmit(values: HonorApplyForm) {
	try {
		submitting.value = true

		// 构建提交数据，包含文件信息
		const submitData: HonorApplyForm = {
			applicant: values.applicant,
			department: values.department,
			type: values.type,
			description: values.description,
			materialUrl: '' // 将在下面设置
		}

		// 设置文件信息到materialUrl字段
		if (form.value.materialFiles && form.value.materialFiles.length > 0) {
			// 将文件列表序列化为JSON字符串存储在materialUrl中
			const fileList = form.value.materialFiles
				.filter(file => file.status === 'done') // 只包含上传成功的文件
				.map(file => ({
					id: file.id,
					name: file.name,
					path: file.path,
					url: file.url || file.path,
					type: file.type
				}))

			submitData.materialUrl = JSON.stringify(fileList)
		} else {
			submitData.materialUrl = ''
		}
		// 编辑模式需要包含ID
		if (formMode.value === 'edit' && form.value.id) {
			submitData.id = form.value.id
			submitData.status = form.value.status || undefined
		}

		// 根据模式调用不同的API
		const response = formMode.value === 'create'
			? await submitHonorApply(submitData)
			: await updateHonorApply(submitData)

		const success = fetchOP(response)
		if (success) {
			message.success(formMode.value === 'create' ? '申报提交成功' : '更新成功')
			formModalVisible.value = false
			honorStore.fetchList()
			// 重置表单
			form.value = {
				applicant: '',
				department: '',
				type: undefined,
				materialUrl: '',
				description: '',
				materialFiles: []
			}
			// 重置表单验证状态
			formRef.value.resetFields()
		}
	} catch (errorInfo: any) {
		console.log('表单提交失败:', errorInfo)
		const errorMsg = formMode.value === 'create' ? '申报失败' : '更新失败'
		handleApiError(errorInfo, errorMsg)
	} finally {
		submitting.value = false
	}
}

function viewDetail(record: HonorRecord) {
	currentRecord.value = record
	detailModalVisible.value = true
}

// 显示编辑申报弹窗
function editRecord(record: HonorRecord) {
	formMode.value = 'edit'

	// 解析已有文件信息
	let materialFiles: FileItem[] = []
	if (record.materialUrl) {
		try {
			// 尝试解析JSON格式的文件信息
			const fileList = JSON.parse(record.materialUrl)
			if (Array.isArray(fileList)) {
				materialFiles = fileList.map(file => ({
					id: file.id || `existing_${Date.now()}_${Math.random()}`,
					name: file.name || '未知文件',
					path: file.path,
					url: file.url || file.path,
					type: file.type,
					status: 'done' as const,
					uploadTime: record.createTime
				}))
			} else {
				// 如果不是JSON格式，按旧格式处理（逗号分隔的URL）
				const urls = record.materialUrl.split(',').filter(url => url.trim())
				materialFiles = urls.map((url, index) => ({
					id: `existing_${index}`,
					name: url.split('/').pop() || `文件${index + 1}`,
					path: url,
					url: url,
					status: 'done' as const,
					uploadTime: record.createTime
				}))
			}
		} catch (error) {
			console.warn('解析文件信息失败，尝试按旧格式处理:', error)
			// 如果JSON解析失败，按旧格式处理
			const urls = record.materialUrl.split(',').filter(url => url.trim())
			materialFiles = urls.map((url, index) => ({
				id: `existing_${index}`,
				name: url.split('/').pop() || `文件${index + 1}`,
				path: url,
				url: url,
				status: 'done' as const,
				uploadTime: record.createTime
			}))
		}
	}

	// 填充表单数据
	form.value = {
    ...record,
		id: record.id,
		applicant: record.applicant,
		department: record.department,
		type: record.type,
		materialUrl: record.materialUrl,
		description: record.description,
		materialFiles: materialFiles
	}
	formModalVisible.value = true
}

async function deleteRecord(id: number) {
	try {
		const response = await deleteHonor(id)
		const success = fetchOP(response)
		if (success) {
			honorStore.fetchList()
		}
	} catch (e) {
		message.error('删除失败')
	}
}

// 初始化
honorStore.fetchList()
</script>

<style lang="scss" scoped>
.honor-apply-page {
	padding: 24px;
	background-color: #f5f5f5;
	min-height: 100vh;
}
.download-template-button {
	margin: 8px 0;
}

.page-header {
	margin-bottom: 16px;
}

.header-actions {
	display: flex;
	justify-content: flex-start;
}

.search-filter-section {
	margin-bottom: 16px;
}

.search-form {
	width: 100%;
}

.form-item-full {
	width: 100%;
	margin-bottom: 0;
}

.form-item-full :deep(.ant-form-item-control) {
	width: 100%;
}

.search-actions {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.table-section {
	background: white;
}

.table-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.record-count {
	font-size: 14px;
	color: #666;
	font-weight: normal;
}

.batch-import-content {
	padding: 16px 0;
}

.mt-6 {
	margin-top: 1.5rem;
}

.ml-2 {
	margin-left: 0.5rem;
}

.mb-4 {
	margin-bottom: 16px;
}

.mt-4 {
	margin-top: 16px;
}

/* 表格滚动优化 */
.table-section :deep(.ant-table-wrapper) {
	overflow-x: auto;
}

.table-section :deep(.ant-table) {
	min-width: 800px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.header-actions :deep(.ant-space) {
		flex-wrap: wrap;
	}
}

@media (max-width: 768px) {
	.honor-apply-page {
		padding: 12px;
	}

	.header-actions {
		margin-bottom: 12px;
	}

	.header-actions :deep(.ant-space) {
		width: 100%;
		justify-content: center;
	}

	.header-actions :deep(.ant-btn) {
		flex: 1;
		min-width: 120px;
	}

	.search-filter-section {
		margin-bottom: 12px;
	}

	.search-actions {
		justify-content: center;
		margin-top: 8px;
	}

	.table-section :deep(.ant-card-body) {
		padding: 12px;
	}

	.table-section :deep(.ant-table-pagination) {
		text-align: center;
	}
}

@media (max-width: 480px) {
	.honor-apply-page {
		padding: 8px;
	}

	.header-actions :deep(.ant-btn) {
		font-size: 12px;
		padding: 4px 8px;
		height: auto;
	}

	.search-form :deep(.ant-form-item-label) {
		text-align: left;
		padding-bottom: 4px;
	}

	.table-section :deep(.ant-table-thead > tr > th) {
		padding: 8px 4px;
		font-size: 12px;
	}

	.table-section :deep(.ant-table-tbody > tr > td) {
		padding: 8px 4px;
		font-size: 12px;
	}
}
</style>
