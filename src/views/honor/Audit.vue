<template>
	<a-card title="荣誉申报审核">
		<a-table
			:dataSource="honorStore.honorList"
			:loading="honorStore.loading"
			rowKey="id"
			:pagination="{
				showSizeChanger: true,
				showQuickJumper: true,
				showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
			}"
		>
			<a-table-column title="序号" width="80">
				<template #default="{ index }">
					{{ index + 1 }}
				</template>
			</a-table-column>
			<a-table-column title="申报人" dataIndex="applicant" />
			<a-table-column title="所属部门" dataIndex="department" />
			<a-table-column title="申报类型" dataIndex="type">
				<template #default="{ record }">
					{{ getTypeText(record.type) }}
				</template>
			</a-table-column>
			<a-table-column title="提交时间" dataIndex="createTime" />
			<a-table-column title="状态" dataIndex="status">
				<template #default="{ record }">
					<a-tag :color="getStatusColor(record.status)">
						{{ getStatusText(record.status) }}
					</a-tag>
				</template>
			</a-table-column>
			<a-table-column title="操作" key="action" width="150">
				<template #default="{ record }">
					<a-space>
						<a-button type="link" size="small" v-if="record.status === 1" @click="viewDetail(record)"> 审核 </a-button>
						<a-button v-else type="link" size="small" @click="viewDetail(record)">查看</a-button>
					</a-space>
				</template>
			</a-table-column>
		</a-table>
	</a-card>

	<!-- 查看详情弹窗 -->
	<a-modal v-model:open="detailVisible" title="申报详情" :footer="null" width="700px">
		<div class="detail-content">
			<a-descriptions bordered :column="1">
				<a-descriptions-item label="申报人">
					{{ currentRecord?.applicant }}
				</a-descriptions-item>
				<a-descriptions-item label="所属部门">
					{{ currentRecord?.department }}
				</a-descriptions-item>
				<a-descriptions-item label="申报类型">
					{{ getTypeText(currentRecord?.type) }}
				</a-descriptions-item>
				<a-descriptions-item label="申报材料">
					<a v-if="currentRecord?.materialUrl" :href="currentRecord.materialUrl" target="_blank" class="material-link">
						{{ currentRecord.materialUrl }}
					</a>
					<span v-else class="no-material">无</span>
				</a-descriptions-item>
				<a-descriptions-item label="申报说明">
					<div class="description-text">
						{{ currentRecord?.description || '无' }}
					</div>
				</a-descriptions-item>
				<a-descriptions-item label="提交时间">
					{{ currentRecord?.createTime }}
				</a-descriptions-item>
				<a-descriptions-item label="审核状态">
					<a-tag :color="getStatusColor(currentRecord?.status)">
						{{ getStatusText(currentRecord?.status) }}
					</a-tag>
				</a-descriptions-item>
				<a-descriptions-item v-if="currentRecord?.auditOpinion" label="审核意见">
					<div class="audit-opinion">
						{{ currentRecord.auditOpinion }}
					</div>
				</a-descriptions-item>
			</a-descriptions>
			<div class="detail-actions">
				<a-radio-group v-model:value="auditStatus">
					<a-radio :value="2">通过</a-radio>
					<a-radio :value="3">驳回</a-radio>
				</a-radio-group>
				<a-textarea v-model:value="auditOpinion" placeholder="请输入审核意见" class="mt-2" />
			</div>
		</div>
	</a-modal>

	<!-- 审核弹窗 -->
	<a-modal v-model:open="auditVisible" title="审核申报" @ok="submitAudit">
		<div>
			<p>申报人：{{ currentRecord?.applicant }}</p>
			<p>部门：{{ currentRecord?.department }}</p>
			<p>类型：{{ getTypeText(currentRecord?.type) }}</p>
			<a-radio-group v-model:value="auditStatus">
				<a-radio :value="2">通过</a-radio>
				<a-radio :value="3">驳回</a-radio>
			</a-radio-group>
			<a-textarea v-model:value="auditOpinion" placeholder="请输入审核意见" class="mt-2" />
		</div>
	</a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useHonorStore } from '@/store/modules/honor'
import { auditHonor } from '@/api/honor'
import { message } from 'ant-design-vue'
import type { HonorRecord, HonorStatus } from '@/types/honor'
import { StatusTextMap, StatusColorMap, HonorTypeTextMap } from '@/types/honor'
import { fetchOP } from '@/utils/request'

const honorStore = useHonorStore()

// 查看详情相关
const detailVisible = ref(false)
// 审核相关
const auditVisible = ref(false)
const currentRecord = ref<HonorRecord | null>(null)
const auditStatus = ref<HonorStatus>(2)
const auditOpinion = ref('')

// 状态相关工具函数
function getStatusColor(status?: HonorStatus) {
	return status ? StatusColorMap[status] : 'default'
}

function getStatusText(status?: HonorStatus) {
	return status ? StatusTextMap[status] : '未知'
}

function getTypeText(type?: number) {
	if (!type) return '未知'
	return HonorTypeTextMap[type as keyof typeof HonorTypeTextMap] || type
}

function viewDetail(record: HonorRecord) {
	currentRecord.value = record
	detailVisible.value = true
}
async function submitAudit() {
	if (!currentRecord.value) return
	try {
		const response = await auditHonor(currentRecord.value.id!, auditStatus.value, auditOpinion.value)
		const success = fetchOP(response)
		if (success) {
			auditVisible.value = false
			honorStore.fetchList()
		}
	} catch (e) {
		message.error('审核失败')
	}
}

// 初始化
honorStore.fetchList()
</script>

<style scoped>
.detail-content {
	padding: 16px 0;
}

.material-link {
	color: #1890ff;
	text-decoration: none;
}

.material-link:hover {
	text-decoration: underline;
}

.no-material {
	color: #999;
}

.description-text {
	line-height: 1.6;
	word-break: break-word;
	white-space: pre-wrap;
}

.audit-opinion {
	line-height: 1.6;
	word-break: break-word;
	white-space: pre-wrap;
	background-color: #f5f5f5;
	padding: 8px 12px;
	border-radius: 4px;
	border-left: 3px solid #1890ff;
}

.detail-actions {
	text-align: center;
	margin-top: 24px;
	padding-top: 16px;
	border-top: 1px solid #f0f0f0;
}

.mt-2 {
	margin-top: 8px;
}
</style>
