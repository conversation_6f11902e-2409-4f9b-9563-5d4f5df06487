<template>
  <div class="word-library-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>敏感词库管理</h2>
        <p>管理敏感词分类和级别，建立完整的词库体系</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showCategoryModal">
            <template #icon><plus-outlined /></template>
            创建分类
          </a-button>
          <a-button @click="showLevelModal">
            <template #icon><plus-outlined /></template>
            创建级别
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="statistics-section">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic title="总分类数" :value="categories.length" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="总级别数" :value="levels.length" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="总词条数" :value="statistics?.totalWords || 0" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic title="启用词条" :value="statistics?.enabledWords || 0" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-section">
      <a-row :gutter="24">
        <!-- 分类管理 -->
        <a-col :span="12">
          <a-card title="分类管理" class="section-card">
            <template #extra>
              <a-button type="link" @click="showCategoryModal">
                <plus-outlined />
                添加分类
              </a-button>
            </template>


            <a-tree v-if="categoryTree.length > 0" :tree-data="categoryTree"
              :field-names="{ title: 'categoryName', key: 'id', children: 'children' }" show-line :show-icon="false"
              class="category-tree">
              <template #title="nodeData">
                <div class="tree-node">
                  <span class="node-title">{{ nodeData.categoryName }}</span>
                  <div class="node-actions">
                    <a-tag :color="nodeData.isEnabled ? 'green' : 'red'" size="small">
                      {{ nodeData.wordCount || 0 }}词条
                    </a-tag>
                    <a-button type="link" size="small" @click.stop="editCategory(nodeData)">
                      编辑
                    </a-button>
                    <a-popconfirm title="确定删除此分类吗？" @confirm="deleteCategory(nodeData.id)" @click.stop>
                      <a-button type="link" size="small" danger>删除</a-button>
                    </a-popconfirm>
                  </div>
                </div>
              </template>
            </a-tree>

            <a-empty v-else description="暂无分类数据" />
          </a-card>
        </a-col>

        <!-- 级别管理 -->
        <a-col :span="12">
          <a-card title="级别管理" class="section-card">
            <template #extra>
              <a-button type="link" @click="showLevelModal">
                <plus-outlined />
                添加级别
              </a-button>
            </template>


            <a-table :columns="levelColumns" :data-source="levels" :loading="loading" :pagination="false" size="small">
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'levelName'">
                  <a-tag :color="record.color">{{ record.levelName }}</a-tag>
                </template>
                <template v-else-if="column.key === 'priority'">
                  <a-tag :color="getPriorityColor(record.priority)">
                    {{ getPriorityText(record.priority) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'action'">
                  <a-tag :color="getActionColor(record.action)">
                    {{ getActionText(record.action) }}
                  </a-tag>
                </template>
                <template v-else-if="column.key === 'operations'">
                  <a-space>
                    <a-button type="link" size="small" @click="editLevel(record)">编辑</a-button>
                    <a-popconfirm title="确定删除此级别吗？" @confirm="deleteLevel(record.id!)">
                      <a-button type="link" size="small" danger>删除</a-button>
                    </a-popconfirm>
                  </a-space>
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 分类编辑Modal -->
    <a-modal v-model:open="categoryModalVisible" :title="categoryModalTitle" @ok="handleCategorySubmit"
      :confirm-loading="submitting">
      <a-form :model="categoryForm" layout="vertical">
        <a-form-item label="分类名称" required>
          <a-input v-model:value="categoryForm.categoryName" placeholder="请输入分类名称" />
        </a-form-item>
        <a-form-item label="分类编码" required>
          <a-input v-model:value="categoryForm.categoryCode" placeholder="请输入分类编码" />
        </a-form-item>
        <a-form-item label="父分类">
          <a-tree-select v-model:value="categoryForm.parentId" :tree-data="categoryTree"
            :field-names="{ label: 'categoryName', value: 'id', children: 'children' }" placeholder="请选择父分类（可选）"
            allow-clear />
        </a-form-item>
        <a-form-item label="排序">
          <a-input-number v-model:value="categoryForm.sortOrder" :min="1" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="categoryForm.description" placeholder="请输入描述" />
        </a-form-item>
        <a-form-item>
          <a-checkbox v-model:checked="categoryForm.isEnabled">启用</a-checkbox>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 级别编辑Modal -->
    <a-modal v-model:open="levelModalVisible" :title="levelModalTitle" @ok="handleLevelSubmit"
      :confirm-loading="submitting">
      <a-form :model="levelForm" layout="vertical">
        <a-form-item label="级别名称" required>
          <a-input v-model:value="levelForm.levelName" placeholder="请输入级别名称" />
        </a-form-item>
        <a-form-item label="级别编码" required>
          <a-input v-model:value="levelForm.levelCode" placeholder="请输入级别编码" />
        </a-form-item>
        <a-form-item label="优先级" required>
          <a-select v-model:value="levelForm.priority" placeholder="请选择优先级">
            <a-select-option :value="1">高风险</a-select-option>
            <a-select-option :value="2">中风险</a-select-option>
            <a-select-option :value="3">低风险</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="显示颜色">
          <a-input v-model:value="levelForm.color" placeholder="请输入颜色值，如：red" />
        </a-form-item>
        <a-form-item label="默认动作" required>
          <a-select v-model:value="levelForm.action" placeholder="请选择默认处理动作">
            <a-select-option :value="1">屏蔽</a-select-option>
            <a-select-option :value="2">替换</a-select-option>
            <a-select-option :value="3">警告</a-select-option>
            <a-select-option :value="4">通过</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model:value="levelForm.description" placeholder="请输入描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { useSensitiveWordsStore } from '@/store/modules/sensitive-words'
import type { WordCategory, WordLevel } from '@/types/sensitive-words'
import {
  FilterActionTextMap,
  FilterActionColorMap
} from '@/types/sensitive-words'

// Store
const sensitiveWordsStore = useSensitiveWordsStore()

// 响应式数据
const categoryModalVisible = ref(false)
const levelModalVisible = ref(false)
const submitting = ref(false)
const editingCategoryId = ref<number | null>(null)
const editingLevelId = ref<number | null>(null)

// 表单数据
const categoryForm = ref<Partial<WordCategory>>({
  categoryName: '',
  categoryCode: '',
  parentId: undefined,
  level: 1,
  sortOrder: 1,
  isEnabled: true,
  description: ''
})

const levelForm = ref<Partial<WordLevel>>({
  levelName: '',
  levelCode: '',
  priority: 1,
  color: 'blue',
  action: 1,
  description: ''
})

// 级别表格列定义
const levelColumns = [
  { title: '级别名称', dataIndex: 'levelName', key: 'levelName' },
  { title: '优先级', dataIndex: 'priority', key: 'priority' },
  { title: '默认动作', dataIndex: 'action', key: 'action' },
  { title: '词条数', dataIndex: 'wordCount', key: 'wordCount' },
  { title: '操作', key: 'operations', width: 120 }
]

// 计算属性 - 使用 storeToRefs 保持响应式
const { categories, levels, categoryTree, statistics, loading } = storeToRefs(sensitiveWordsStore)

const categoryModalTitle = computed(() =>
  editingCategoryId.value ? '编辑分类' : '创建分类'
)

const levelModalTitle = computed(() =>
  editingLevelId.value ? '编辑级别' : '创建级别'
)

// 方法定义
function getPriorityText(priority: number) {
  const map = { 1: '高风险', 2: '中风险', 3: '低风险' }
  return map[priority as keyof typeof map] || '未知'
}

function getPriorityColor(priority: number) {
  const map = { 1: 'red', 2: 'orange', 3: 'green' }
  return map[priority as keyof typeof map] || 'default'
}

function getActionText(action: number) {
  return FilterActionTextMap[action as keyof typeof FilterActionTextMap] || '未知'
}

function getActionColor(action: number) {
  return FilterActionColorMap[action as keyof typeof FilterActionColorMap] || 'default'
}

async function refreshData() {
  await Promise.all([
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels(),
    sensitiveWordsStore.fetchStatistics()
  ])
  message.success('数据刷新成功')
}

function showCategoryModal() {
  editingCategoryId.value = null
  categoryForm.value = {
    categoryName: '',
    categoryCode: '',
    parentId: undefined,
    level: 1,
    sortOrder: 1,
    isEnabled: true,
    description: ''
  }
  categoryModalVisible.value = true
}

function showLevelModal() {
  editingLevelId.value = null
  levelForm.value = {
    levelName: '',
    levelCode: '',
    priority: 1,
    color: 'blue',
    action: 1,
    description: ''
  }
  levelModalVisible.value = true
}

function editCategory(category: Partial<WordCategory>) {
  editingCategoryId.value = category.id!
  categoryForm.value = { ...category }
  categoryModalVisible.value = true
}

function editLevel(level: WordLevel) {
  editingLevelId.value = level.id!
  levelForm.value = { ...level }
  levelModalVisible.value = true
}

async function deleteCategory(id: number) {
  try {
    const success = await sensitiveWordsStore.deleteCategory(id)
    if (success) {
      await refreshData()
    }
  } catch (error) {
    message.error('删除失败，请重试')
  }
}

async function deleteLevel(id: number) {
  try {
    const success = await sensitiveWordsStore.deleteLevel(id)
    if (success) {
      await refreshData()
    }
  } catch (error) {
    message.error('删除失败，请重试')
  }
}

async function handleCategorySubmit() {
  submitting.value = true
  try {
    let success = false
    if (editingCategoryId.value) {
      success = await sensitiveWordsStore.updateCategory(editingCategoryId.value, categoryForm.value)
    } else {
      success = await sensitiveWordsStore.createCategory(categoryForm.value)
    }

    if (success) {
      categoryModalVisible.value = false
      await refreshData()
    }
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

async function handleLevelSubmit() {
  submitting.value = true
  try {
    let success = false
    if (editingLevelId.value) {
      success = await sensitiveWordsStore.updateLevel(editingLevelId.value, levelForm.value)
    } else {
      success = await sensitiveWordsStore.createLevel(levelForm.value)
    }

    if (success) {
      levelModalVisible.value = false
      await refreshData()
    }
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels(),
    sensitiveWordsStore.fetchStatistics()
  ])

})
</script>

<style scoped>
.word-library-page {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-title h2 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 24px;
}

.statistics-section .ant-card {
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-section {
  margin-bottom: 24px;
}

.section-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 600px;
}

.section-card .ant-card-body {
  height: calc(100% - 57px);
  overflow-y: auto;
}

.category-tree {
  height: 100%;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 8px;
}

.node-title {
  flex: 1;
  font-weight: 500;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.ant-table-small .ant-table-tbody>tr>td {
  padding: 8px;
}

.ant-modal .ant-form-item {
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .word-library-page {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .content-section .ant-col {
    margin-bottom: 16px;
  }

  .section-card {
    height: auto;
    min-height: 400px;
  }
}
</style>
