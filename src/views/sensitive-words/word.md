| 消费方   | 前端应用                                    |
| -------- | ------------------------------------------- |
| 模块     | ows-captcha                                 |
| 功能项   | 图像验证                                    |
| 支持版本 | v3.0.0                                      |
| 设计者   | 樊虹伶                                      |
| 接口描述 | **图像内容审核检测**                         |
| 请求地址 | /image/v3/verify                            |
| 请求方式 | **POST**                                    |

### 1.1 请求参数
| 参数名称 | 参数类型     | 长度 | 是否必要 | 参数含义 | 参数说明 |
| -------- | ------------ | ---- | -------- | -------- | -------- |
| file     | MultipartFile| N/A  | Y        | 图像文件 | 上传的图像文件 |

### 1.2 响应
| 参数名称     | 参数类型  | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | --------- | ---- | -------- | -------------- | ---------- |
| suggestion   | string    | N/A  | Y        | 检测建议       | pass/review/block |
| details      | array     | N/A  | Y        | 检测详情列表   | DetectionDetailDTO数组 |

details数组元素结构（DetectionDetailDTO）：

| 参数名称     | 参数类型 | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | -------- | ---- | -------- | -------------- | ---------- |
| segment      | string   | N/A  | N        | 敏感内容片段   | 检测到的敏感内容 |
| category     | string   | N/A  | N        | 检测分类       | 检测分类（中文） |
| confidence   | string   | N/A  | N        | 置信度         | 检测置信度 |
| riskLevel    | string   | N/A  | N        | 风险级别       | 风险等级 |

### 1.3 请求报文示例
```http
POST /image/v3/verify
Content-Type: multipart/form-data

文件上传
```

### 1.4 响应报文示例
```json
{
    "code": 0,
    "timestamp": "2025-08-28T07:40:21.503+00:00",
    "message": "success",
    "data": {
        "suggestion": "block",
        "details": [
            {
                "segment": null,
                "category": "血腥",
                "confidence": "1.00",
                "riskLevel": "高风险"
            }
        ]
    },
    "status": 200,
    "time_string": "2025-08-28 07:40:21.021"
}
```