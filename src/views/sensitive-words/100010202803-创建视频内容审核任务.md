[TOCM]

| 消费方   | 前端应用                                    |
| -------- | ------------------------------------------- |
| 模块     | ows-captcha                                 |
| 功能项   | 视频验证                                    |
| 支持版本 | v3.0.0                                      |
| 设计者   | 樊虹伶                                      |
| 接口描述 | **创建视频内容审核任务**                     |
| 请求地址 | /video/v3/verify                            |
| 请求方式 | **POST**                                    |

### 3.1 请求参数
| 参数名称    | 参数类型     | 长度 | 是否必要 | 参数含义 | 参数说明 |
| ----------- | ------------ | ---- | -------- | -------- | -------- |
| videoUrl    | string       | N/A  | Y        | 视频地址 | 待检测的视频URL地址 |

### 3.2 响应
| 参数名称     | 参数类型 | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | -------- | ---- | -------- | -------------- | ---------- |
| job_id         | number   | N/A  | Y        | 任务id         |  |

### 3.3 请求报文示例
```json
{
    "videoUrl": "https://example.com/video.mp4"
}
```

### 3.4 响应报文示例
```json
{
    "code": 0,
    "timestamp": "2025-08-28T10:30:00.000+00:00",
    "message": "success",
    "data": {
        "job_id": "fb498a8b-8b14-4b28-b24b-7b8b8b8b8b8b",
        "request_id": "ddddd"
    },
    "status": 200,
    "time_string": "2025-08-28 10:30:00.123"
}
```