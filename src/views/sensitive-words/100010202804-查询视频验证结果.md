[TOCM]

| 消费方   | 前端应用                                    |
| -------- | ------------------------------------------- |
| 模块     | ows-captcha                                 |
| 功能项   | 查询视频验证结果                             |
| 支持版本 | v3.0.0                                      |
| 设计者   | 樊虹伶                                      |
| 接口描述 | **查询视频内容审核结果**                     |
| 请求地址 | /video/v3/getResult/{jobId}                 |
| 请求方式 | **GET**                                     |

### 4.1 请求参数
| 参数名称 | 参数类型     | 长度 | 是否必要 | 参数含义 | 参数说明 |
| -------- | ------------ | ---- | -------- | -------- | -------- |
| jobId    | string       | N/A  | Y        | 任务ID   | 视频审核任务的唯一标识符 |

### 4.2 响应
| 参数名称     | 参数类型  | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | --------- | ---- | -------- | -------------- | ---------- |
| suggestion   | string    | N/A  | Y        | 检测建议       | pass/review/block |
| details      | array     | N/A  | Y        | 检测详情列表   | DetectionDetailDTO数组 |

details数组元素结构（DetectionDetailDTO）：

| 参数名称     | 参数类型 | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | -------- | ---- | -------- | -------------- | ---------- |
| segment      | string   | N/A  | N        | 敏感内容片段   | 检测到的敏感内容 |
| category     | string   | N/A  | N        | 检测分类       | 检测分类（中文） |
| confidence   | string   | N/A  | N        | 置信度         | 检测置信度 |
| riskLevel    | string   | N/A  | N        | 风险级别       | 风险等级 |

### 4.3 请求报文示例
```http
GET /video/v3/getResult/fb498a8b-8b14-4b28-b24b-7b8b8b8b8b8b
```

### 4.4 响应报文示例
```json
{
    "code": 0,
    "timestamp": "2025-08-28T10:30:00.000+00:00",
    "message": "success",
    "data": {
        "suggestion": "block",
        "details": [
            {
                "segment": "敏感视频内容",
                "category": "涉政",
                "confidence": "0.88",
                "riskLevel": "high"
            },
            {
                "segment": "敏感音频内容",
                "category": "色情",
                "confidence": "0.75",
                "riskLevel": "medium"
            }
        ]
    },
    "status": 200,
    "time_string": "2025-08-28 10:30:00.123"
}
```