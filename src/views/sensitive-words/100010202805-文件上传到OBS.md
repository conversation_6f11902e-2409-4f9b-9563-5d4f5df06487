[TOCM]

| 消费方   | 前端应用                                    |
| -------- | ------------------------------------------- |
| 模块     | ows-captcha                           |
| 功能项   | 文件上传                                     |
| 支持版本 | v4.0.2                                      |
| 设计者   | System                                       |
| 接口描述 | **文件上传到OBS对象存储**                    |
| 请求地址 | /api/obs/files/upload                        |
| 请求方式 | **POST**                                    |

## 1. 请求参数
| 参数名称         | 参数类型     | 长度 | 是否必要 | 参数含义     | 参数说明 |
| --------------- | ------------ | ---- | -------- | ------------ | -------- |
| file            | MultipartFile| N/A  | Y        | 上传文件     | 上传的文件对象 |
| generateThumbnail| boolean     | N/A  | N        | 生成缩略图   | 是否为视频生成缩略图，默认true |
| getDuration     | boolean      | N/A  | N        | 获取时长     | 是否获取视频时长信息，默认true |

## 2. 响应
| 参数名称     | 参数类型 | 长度 | 是否必要 | 参数含义       | 参数说明   |
| ------------ | -------- | ---- | -------- | -------------- | ---------- |
| fileId       | string   | N/A  | Y        | 文件ID         | 系统生成的唯一文件标识 |
| fileName     | string   | N/A  | Y        | 文件名         | 原始文件名 |
| filePath     | string   | N/A  | Y        | 文件路径       | 在OBS中的存储路径 |
| fileSize     | number   | N/A  | Y        | 文件大小       | 文件大小（字节） |
| contentType  | string   | N/A  | Y        | 文件类型       | MIME类型 |
| uploadTime   | string   | N/A  | Y        | 上传时间       | 文件上传时间戳 |
| url          | string   | N/A  | Y        | 访问URL        | 文件的完整访问地址 |


## 3. 请求报文示例



## 4. 响应报文示例

### 成功响应
```json
{
    "code": 0,
    "timestamp": "2024-08-29T10:30:45.123+00:00",
    "message": "success",
    "data": {
        "fileId": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
        "fileName": "example.jpg",
        "filePath": "img/202408/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6.jpg",
        "fileSize": 204800,
        "contentType": "image/jpeg",
        "uploadTime": "2024-08-29T10:30:45.123Z",
        "url": "https://obs.example.com/img/202408/a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6.jpg"
    },
    "status": 200,
    "time_string": "2024-08-29 10:30:45.123"
}
```


### 错误响应 - 文件为空
```json
{
    "code": 400,
    "timestamp": "2024-08-29T10:40:15.789+00:00",
    "message": "上传文件不能为空",
    "data": null,
    "status": 400,
    "time_string": "2024-08-29 10:40:15.789"
}
```