<template>
  <div class="policy-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>过滤策略管理</h2>
        <p>配置过滤策略和规则，设置不同场景的处理方式</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showPolicyModal">
            <template #icon><plus-outlined /></template>
            创建策略
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="策略名称" class="form-item-full">
                <a-input v-model:value="searchForm.policyName" placeholder="请输入策略名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="应用范围" class="form-item-full">
                <a-select v-model:value="searchForm.scope" placeholder="请选择应用范围" allow-clear>
                  <a-select-option v-for="option in PolicyScopeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item label="状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">启用</a-select-option>
                  <a-select-option :value="false">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="8" :lg="6">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 策略列表 -->
    <div class="policy-list-section">
      <a-row :gutter="[16, 16]">
        <a-col 
          v-for="policy in policies" 
          :key="policy.id" 
          :xs="24" 
          :sm="12" 
          :lg="8"
        >
          <a-card class="policy-card" :class="{ disabled: !policy.isEnabled }">
            <template #title>
              <div class="policy-header">
                <span class="policy-name">{{ policy.policyName }}</span>
                <a-tag :color="policy.isEnabled ? 'green' : 'red'">
                  {{ policy.isEnabled ? '启用' : '禁用' }}
                </a-tag>
              </div>
            </template>
            <template #extra>
              <a-dropdown>
                <a-button type="text" size="small">
                  <template #icon><more-outlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="editPolicy(policy)">
                      <edit-outlined /> 编辑
                    </a-menu-item>
                    <a-menu-item @click="copyPolicy(policy)">
                      <copy-outlined /> 复制
                    </a-menu-item>
                    <a-menu-item @click="togglePolicyStatus(policy)">
                      <switch-outlined /> {{ policy.isEnabled ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="deletePolicy(policy.id!)" danger>
                      <delete-outlined /> 删除
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>

            <div class="policy-content">
              <div class="policy-info">
                <p class="policy-description">{{ policy.description || '暂无描述' }}</p>
                <div class="policy-meta">
                  <div class="meta-item">
                    <span class="meta-label">应用范围:</span>
                    <a-tag color="blue">{{ getScopeText(policy.scope) }}</a-tag>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">优先级:</span>
                    <a-tag :color="getPriorityColor(policy.priority)">{{ policy.priority }}</a-tag>
                  </div>
                  <div class="meta-item">
                    <span class="meta-label">规则数量:</span>
                    <span>{{ policy.rules?.length || 0 }}条</span>
                  </div>
                </div>
              </div>
              
              <div class="policy-actions">
                <a-space>
                  <a-button type="link" size="small" @click="viewPolicyDetail(policy)">
                    查看详情
                  </a-button>
                  <a-button type="link" size="small" @click="configRules(policy)">
                    配置规则
                  </a-button>
                </a-space>
              </div>
            </div>

            <div class="policy-footer">
              <div class="footer-info">
                <span class="create-time">创建时间: {{ policy.createTime }}</span>
                <span class="creator">创建人: {{ policy.creator || '系统' }}</span>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 策略编辑弹窗 -->
    <a-modal
      :title="policyModalTitle"
      v-model:open="policyModalVisible"
      @ok="handlePolicySubmit"
      :confirm-loading="submitting"
      width="800px"
    >
      <a-form
        ref="policyFormRef"
        :model="policyForm"
        :rules="policyRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="策略名称" name="policyName">
          <a-input v-model:value="policyForm.policyName" placeholder="请输入策略名称" />
        </a-form-item>
        <a-form-item label="策略编码" name="policyCode">
          <a-input v-model:value="policyForm.policyCode" placeholder="请输入策略编码" />
        </a-form-item>
        <a-form-item label="应用范围" name="scope">
          <a-select v-model:value="policyForm.scope" placeholder="请选择应用范围">
            <a-select-option v-for="option in PolicyScopeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" name="priority">
          <a-input-number
            v-model:value="policyForm.priority"
            :min="1"
            :max="100"
            placeholder="数值越小优先级越高"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="状态" name="isEnabled">
          <a-switch v-model:checked="policyForm.isEnabled" />
          <span style="margin-left: 8px">{{ policyForm.isEnabled ? '启用' : '禁用' }}</span>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
            v-model:value="policyForm.description"
            placeholder="请输入策略描述"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 策略详情弹窗 -->
    <a-modal
      title="策略详情"
      v-model:open="detailVisible"
      :footer="null"
      width="700px"
    >
      <a-descriptions bordered :column="1" v-if="currentPolicy">
        <a-descriptions-item label="策略名称">{{ currentPolicy.policyName }}</a-descriptions-item>
        <a-descriptions-item label="策略编码">{{ currentPolicy.policyCode }}</a-descriptions-item>
        <a-descriptions-item label="应用范围">
          <a-tag color="blue">{{ getScopeText(currentPolicy.scope) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="getPriorityColor(currentPolicy.priority)">{{ currentPolicy.priority }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentPolicy.isEnabled ? 'green' : 'red'">
            {{ currentPolicy.isEnabled ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="规则数量">{{ currentPolicy.rules?.length || 0 }}条</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentPolicy.createTime }}</a-descriptions-item>
        <a-descriptions-item label="创建人">{{ currentPolicy.creator || '系统' }}</a-descriptions-item>
        <a-descriptions-item label="描述">{{ currentPolicy.description || '无' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 规则配置弹窗 -->
    <a-modal
      title="配置过滤规则"
      v-model:open="rulesVisible"
      @ok="handleRulesSubmit"
      :confirm-loading="submitting"
      width="900px"
    >
      <div class="rules-config">
        <div class="rules-header">
          <a-button type="primary" @click="addRule">
            <template #icon><plus-outlined /></template>
            添加规则
          </a-button>
        </div>
        
        <div class="rules-list">
          <div 
            v-for="(rule, index) in currentRules" 
            :key="index" 
            class="rule-item"
          >
            <div class="rule-header">
              <span class="rule-title">规则 {{ index + 1 }}</span>
              <a-button type="link" size="small" danger @click="removeRule(index)">
                删除
              </a-button>
            </div>
            <a-form :model="rule" layout="vertical">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="适用分类">
                    <a-select 
                      v-model:value="rule.categoryIds" 
                      mode="multiple" 
                      placeholder="请选择分类"
                    >
                      <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                        {{ category.categoryName }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="适用级别">
                    <a-select 
                      v-model:value="rule.levelIds" 
                      mode="multiple" 
                      placeholder="请选择级别"
                    >
                      <a-select-option v-for="level in levels" :key="level.id" :value="level.id">
                        <a-tag :color="level.color" style="margin-right: 8px">{{ level.levelName }}</a-tag>
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="匹配模式">
                    <a-select v-model:value="rule.matchMode" placeholder="匹配模式">
                      <a-select-option v-for="option in MatchModeOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="处理动作">
                    <a-select v-model:value="rule.action" placeholder="处理动作">
                      <a-select-option v-for="option in FilterActionOptions" :key="option.value" :value="option.value">
                        {{ option.label }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="优先级">
                    <a-input-number 
                      v-model:value="rule.priority" 
                      :min="1" 
                      :max="100" 
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="24" v-if="rule.action === 2">
                  <a-form-item label="替换内容">
                    <a-input v-model:value="rule.replacement" placeholder="请输入替换内容" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useSensitiveWordsStore } from '@/store/modules/sensitive-words'
import type { FilterPolicy, FilterRule, PolicySearchParams } from '@/types/sensitive-words'
import { 
  PolicyScopeOptions, 
  PolicyScopeTextMap,
  MatchModeOptions,
  FilterActionOptions
} from '@/types/sensitive-words'
import {
  PlusOutlined,
  ReloadOutlined,
  MoreOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

const sensitiveWordsStore = useSensitiveWordsStore()

// 响应式数据
const searchForm = ref<PolicySearchParams>({
  policyName: '',
  scope: undefined,
  isEnabled: undefined
})

const policyModalVisible = ref(false)
const detailVisible = ref(false)
const rulesVisible = ref(false)
const submitting = ref(false)
const editingPolicyId = ref<number | null>(null)
const currentPolicy = ref<FilterPolicy | null>(null)
const currentRules = ref<FilterRule[]>([])

// 表单数据
const policyForm = ref<Partial<FilterPolicy>>({
  policyName: '',
  policyCode: '',
  scope: 1,
  priority: 1,
  isEnabled: true,
  description: ''
})

// 表单验证规则
const policyRules = {
  policyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  policyCode: [{ required: true, message: '请输入策略编码', trigger: 'blur' }],
  scope: [{ required: true, message: '请选择应用范围', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
}

// 计算属性
const { policies, categories, levels, loading } = sensitiveWordsStore

const policyModalTitle = computed(() => 
  editingPolicyId.value ? '编辑策略' : '创建策略'
)

// 方法定义
function getScopeText(scope: number) {
  return PolicyScopeTextMap[scope as keyof typeof PolicyScopeTextMap] || '未知'
}

function getPriorityColor(priority: number) {
  if (priority <= 3) return 'red'
  if (priority <= 6) return 'orange'
  return 'green'
}

async function refreshData() {
  await Promise.all([
    sensitiveWordsStore.fetchPolicies(),
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels()
  ])
  message.success('数据刷新成功')
}

async function handleSearch() {
  await sensitiveWordsStore.fetchPolicies(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    policyName: '',
    scope: undefined,
    isEnabled: undefined
  }
  sensitiveWordsStore.fetchPolicies()
}

function showPolicyModal() {
  editingPolicyId.value = null
  policyForm.value = {
    policyName: '',
    policyCode: '',
    scope: 1,
    priority: 1,
    isEnabled: true,
    description: ''
  }
  policyModalVisible.value = true
}

function editPolicy(policy: FilterPolicy) {
  editingPolicyId.value = policy.id!
  policyForm.value = { ...policy }
  policyModalVisible.value = true
}

function copyPolicy(policy: FilterPolicy) {
  editingPolicyId.value = null
  policyForm.value = {
    ...policy,
    policyName: policy.policyName + '_副本',
    policyCode: policy.policyCode + '_COPY'
  }
  policyModalVisible.value = true
}

function viewPolicyDetail(policy: FilterPolicy) {
  currentPolicy.value = policy
  detailVisible.value = true
}

function configRules(policy: FilterPolicy) {
  currentPolicy.value = policy
  currentRules.value = policy.rules ? [...policy.rules] : []
  rulesVisible.value = true
}

async function togglePolicyStatus(policy: FilterPolicy) {
  try {
    // 模拟切换状态
    await new Promise(resolve => setTimeout(resolve, 500))
    policy.isEnabled = !policy.isEnabled
    message.success(policy.isEnabled ? '策略已启用' : '策略已禁用')
  } catch (error) {
    message.error('操作失败，请重试')
  }
}

async function deletePolicy(id: number) {
  try {
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 500))
    message.success('策略删除成功')
    await refreshData()
  } catch (error) {
    message.error('删除失败，请重试')
  }
}

async function handlePolicySubmit() {
  submitting.value = true
  try {
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success(editingPolicyId.value ? '策略更新成功' : '策略创建成功')
    policyModalVisible.value = false
    await refreshData()
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

function addRule() {
  currentRules.value.push({
    policyId: currentPolicy.value?.id || 0,
    categoryIds: [],
    levelIds: [],
    matchMode: 1,
    action: 1,
    isEnabled: true,
    priority: 1
  } as FilterRule)
}

function removeRule(index: number) {
  currentRules.value.splice(index, 1)
}

async function handleRulesSubmit() {
  submitting.value = true
  try {
    // 模拟保存规则
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('规则配置保存成功')
    rulesVisible.value = false
    await refreshData()
  } catch (error) {
    message.error('保存失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    sensitiveWordsStore.fetchPolicies(),
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels()
  ])
})
</script>

<style lang="scss" scoped>
.policy-manage-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .search-filter-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .policy-list-section {
    .policy-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      height: 100%;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &.disabled {
        opacity: 0.7;
        background: #f9f9f9;
      }

      .policy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .policy-name {
          font-weight: 600;
          color: #333;
        }
      }

      .policy-content {
        .policy-info {
          margin-bottom: 16px;

          .policy-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
            line-height: 1.5;
          }

          .policy-meta {
            .meta-item {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              .meta-label {
                font-size: 12px;
                color: #999;
                margin-right: 8px;
                min-width: 60px;
              }
            }
          }
        }

        .policy-actions {
          border-top: 1px solid #f0f0f0;
          padding-top: 12px;
        }
      }

      .policy-footer {
        margin-top: 16px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;

        .footer-info {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;

          .create-time,
          .creator {
            flex: 1;
          }
        }
      }
    }
  }

  .rules-config {
    .rules-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .rules-list {
      max-height: 400px;
      overflow-y: auto;

      .rule-item {
        margin-bottom: 24px;
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #e8e8e8;

        .rule-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;

          .rule-title {
            font-weight: 600;
            color: #333;
          }
        }

        .ant-form {
          .ant-form-item {
            margin-bottom: 16px;
          }

          .ant-form-item-label {
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .policy-manage-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }

      .search-actions {
        justify-content: flex-start !important;
      }
    }

    .policy-card {
      .policy-footer {
        .footer-info {
          flex-direction: column !important;
          gap: 4px;
        }
      }
    }

    .rules-config {
      .rules-list {
        max-height: 300px;
      }

      .rule-item {
        .ant-col {
          margin-bottom: 12px;
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .policy-manage-page {
    .policy-card {
      .policy-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px;
      }

      .policy-meta {
        .meta-item {
          flex-direction: column !important;
          align-items: flex-start !important;
          gap: 4px;

          .meta-label {
            min-width: auto !important;
          }
        }
      }
    }
  }
}
</style>
