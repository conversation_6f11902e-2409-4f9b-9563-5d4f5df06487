<template>
  <div class="word-manage-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>敏感词管理</h2>
        <p>添加、编辑、删除敏感词，支持批量操作和导入导出</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showWordModal">
            <template #icon><plus-outlined /></template>
            添加敏感词
          </a-button>
          <a-button @click="showBatchImport">
            <template #icon><cloud-upload-outlined /></template>
            批量导入
          </a-button>
          <a-dropdown>
            <a-button>
              <template #icon><download-outlined /></template>
              导出数据
              <down-outlined />
            </a-button>
            <template #overlay>
              <a-menu @click="handleExportMenuClick">
                <a-menu-item key="xlsx">
                  <file-excel-outlined />
                  导出为Excel
                </a-menu-item>
                <a-menu-item key="csv">
                  <file-text-outlined />
                  导出为CSV
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <a-card>
        <a-form :model="searchForm" class="search-form">
          <a-row :gutter="[16, 16]" style="width: 100%">
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="敏感词" class="form-item-full">
                <a-input v-model:value="searchForm.word" placeholder="请输入敏感词" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="分类" class="form-item-full">
                <a-select v-model:value="searchForm.categoryId" placeholder="请选择分类" allow-clear>
                  <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.categoryName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="级别" class="form-item-full">
                <a-select v-model:value="searchForm.levelId" placeholder="请选择级别" allow-clear>
                  <a-select-option v-for="level in levels" :key="level.id" :value="level.id">
                    {{ level.levelName }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="状态" class="form-item-full">
                <a-select v-model:value="searchForm.isEnabled" placeholder="请选择状态" allow-clear>
                  <a-select-option :value="true">启用</a-select-option>
                  <a-select-option :value="false">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item label="匹配类型" class="form-item-full">
                <a-select v-model:value="searchForm.matchType" placeholder="匹配类型" allow-clear>
                  <a-select-option v-for="option in MatchTypeOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :xs="24" :sm="12" :md="6" :lg="4">
              <a-form-item class="form-item-full search-actions">
                <a-space>
                  <a-button type="primary" @click="handleSearch">搜索</a-button>
                  <a-button @click="resetSearch">重置</a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>
    </div>

    <!-- 批量操作区域 -->
    <div class="batch-actions-section" v-if="selectedRowKeys.length > 0">
      <a-card>
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-button @click="batchEnable">批量启用</a-button>
          <a-button @click="batchDisable">批量禁用</a-button>
          <a-popconfirm title="确定删除选中的敏感词吗？" @confirm="batchDelete">
            <a-button danger>批量删除</a-button>
          </a-popconfirm>
          <a-button @click="clearSelection">清空选择</a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>敏感词列表</span>
            <span class="record-count">共 {{ sensitiveWords.length }} 条记录</span>
          </div>
        </template>
        <a-table :columns="columns" :data-source="sensitiveWords" :loading="loading" :pagination="paginationConfig"
          :scroll="{ x: 1200 }" :row-selection="rowSelection" row-key="id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'word'">
              <span class="word-text">{{ record.word }}</span>
            </template>
            <template v-else-if="column.key === 'categoryId'">
              <a-tag color="blue">
                {{ getCategoryName(record.categoryId) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'levelId'">
              <a-tag :color="getLevelColor(record.levelId)">
                {{ getLevelName(record.levelId) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'matchType'">
              <a-tag :color="record.matchType === 1 ? 'green' : 'orange'">
                {{ getMatchTypeText(record.matchType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'isEnabled'">
              <a-switch :checked="record.isEnabled" @change="toggleWordStatus(record.id!, $event)" size="small" />
            </template>
            <template v-else-if="column.key === 'replacement'">
              <span class="replacement-text">{{ record.replacement || '-' }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editWord(record)">编辑</a-button>
                <a-button type="link" size="small" @click="viewWordDetail(record)">详情</a-button>
                <a-popconfirm title="确定删除此敏感词吗？" @confirm="deleteWord(record.id!)">
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 敏感词编辑弹窗 -->
    <a-modal :title="wordModalTitle" :visible="wordModalVisible" @cancel="wordModalVisible = false"
      @ok="handleWordSubmit" :confirm-loading="submitting" width="700px">
      <a-form ref="wordFormRef" :model="wordForm" :rules="wordRules" :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }">
        <a-form-item label="敏感词" name="word">
          <a-input v-model:value="wordForm.word" placeholder="请输入敏感词" />
        </a-form-item>
        <a-form-item label="分类" name="categoryId">
          <a-select v-model:value="wordForm.categoryId" placeholder="请选择分类">
            <a-select-option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.categoryName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="级别" name="levelId">
          <a-select v-model:value="wordForm.levelId" placeholder="请选择级别">
            <a-select-option v-for="level in levels" :key="level.id" :value="level.id">
              <a-tag :color="level.color" style="margin-right: 8px">{{ level.levelName }}</a-tag>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="匹配类型" name="matchType">
          <a-radio-group v-model:value="wordForm.matchType">
            <a-radio v-for="option in MatchTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="替换词" name="replacement">
          <a-input v-model:value="wordForm.replacement" placeholder="请输入替换词（可选）" />
        </a-form-item>
        <a-form-item label="状态" name="isEnabled">
          <a-switch v-model:checked="wordForm.isEnabled" />
          <span style="margin-left: 8px">{{ wordForm.isEnabled ? '启用' : '禁用' }}</span>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="wordForm.description" placeholder="请输入描述信息" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情查看弹窗 -->
    <a-modal title="敏感词详情" :visible="detailVisible" @cancel="detailVisible = false" :footer="null" width="600px">
      <a-descriptions bordered :column="1" v-if="currentRecord">
        <a-descriptions-item label="敏感词">{{ currentRecord.word }}</a-descriptions-item>
        <a-descriptions-item label="分类">
          <a-tag color="blue">{{ getCategoryName(currentRecord.categoryId) }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="级别">
          <a-tag :color="getLevelColor(currentRecord.levelId)">
            {{ getLevelName(currentRecord.levelId) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="匹配类型">
          <a-tag :color="currentRecord.matchType === 1 ? 'green' : 'orange'">
            {{ getMatchTypeText(currentRecord.matchType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="替换词">{{ currentRecord.replacement || '无' }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="currentRecord.isEnabled ? 'green' : 'red'">
            {{ currentRecord.isEnabled ? '启用' : '禁用' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentRecord.createTime }}</a-descriptions-item>
        <a-descriptions-item label="创建人">{{ currentRecord.creator || '系统' }}</a-descriptions-item>
        <a-descriptions-item label="描述">{{ currentRecord.description || '无' }}</a-descriptions-item>
      </a-descriptions>
    </a-modal>

    <!-- 批量导入弹窗 -->
    <a-modal title="批量导入敏感词" :visible="importVisible" @cancel="importVisible = false" @ok="handleBatchImport"
      :confirm-loading="importing" width="700px">
      <div class="import-content">
        <a-alert message="导入说明" description="支持Excel(.xlsx)和CSV(.csv)格式文件。文件应包含：敏感词、分类、级别、匹配类型、替换词、描述等字段。" type="info"
          show-icon style="margin-bottom: 16px" />
        <a-upload-dragger v-model:fileList="importFileList" :before-upload="beforeUpload" accept=".xlsx,.csv"
          :multiple="false">
          <p class="ant-upload-drag-icon">
            <inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p class="ant-upload-hint">支持Excel和CSV格式文件，单个文件不超过10MB</p>
        </a-upload-dragger>

        <div class="import-options" style="margin-top: 16px">
          <a-checkbox v-model:checked="importOptions.skipDuplicate">跳过重复词条</a-checkbox>
          <a-checkbox v-model:checked="importOptions.updateExisting">更新已存在词条</a-checkbox>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useSensitiveWordsStore } from '@/store/modules/sensitive-words'
import type { SensitiveWord, SensitiveWordSearchParams } from '@/types/sensitive-words'
import { MatchTypeOptions, MatchTypeTextMap } from '@/types/sensitive-words'
import {
  PlusOutlined,
  CloudUploadOutlined,
  DownloadOutlined,
  ReloadOutlined,
  InboxOutlined,
  DownOutlined,
  FileExcelOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

const sensitiveWordsStore = useSensitiveWordsStore()

// 响应式数据
const searchForm = ref<SensitiveWordSearchParams>({
  word: '',
  categoryId: undefined,
  levelId: undefined,
  isEnabled: undefined,
  matchType: undefined
})

const wordModalVisible = ref(false)
const detailVisible = ref(false)
const importVisible = ref(false)
const submitting = ref(false)
const importing = ref(false)
const editingWordId = ref<number | null>(null)
const currentRecord = ref<SensitiveWord | null>(null)
const selectedRowKeys = ref<number[]>([])
const importFileList = ref([])
const importOptions = ref({
  skipDuplicate: true,
  updateExisting: false
})

// 表单数据
const wordForm = ref<Partial<SensitiveWord>>({
  word: '',
  categoryId: undefined,
  levelId: undefined,
  isEnabled: true,
  matchType: 1,
  replacement: '',
  description: ''
})

// 表单验证规则
const wordRules = {
  word: [{ required: true, message: '请输入敏感词', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
  levelId: [{ required: true, message: '请选择级别', trigger: 'change' }],
  matchType: [{ required: true, message: '请选择匹配类型', trigger: 'change' }]
}

// 计算属性
const sensitiveWords = computed(() => sensitiveWordsStore.sensitiveWords)
const categories = computed(() => sensitiveWordsStore.categories)
const levels = computed(() => sensitiveWordsStore.levels)
const loading = computed(() => sensitiveWordsStore.loading)

const wordModalTitle = computed(() =>
  editingWordId.value ? '编辑敏感词' : '添加敏感词'
)

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '敏感词',
    key: 'word',
    width: 150,
    ellipsis: true
  },
  {
    title: '分类',
    key: 'categoryId',
    width: 120,
    align: 'center'
  },
  {
    title: '级别',
    key: 'levelId',
    width: 100,
    align: 'center'
  },
  {
    title: '匹配类型',
    key: 'matchType',
    width: 100,
    align: 'center'
  },
  {
    title: '替换词',
    key: 'replacement',
    width: 120,
    ellipsis: true
  },
  {
    title: '状态',
    key: 'isEnabled',
    width: 80,
    align: 'center'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center',
    fixed: 'right'
  }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  }
}

// 方法定义
function getRowIndex(record: SensitiveWord) {
  // 添加安全检查，防止 sensitiveWords.value 为 undefined
  if (!sensitiveWords.value || !Array.isArray(sensitiveWords.value)) {
    return 0
  }
  return sensitiveWords.value.findIndex(item => item.id === record.id) + 1
}

function getCategoryName(categoryId: number) {
  const category = categories.value && categories.value.find(cat => cat.id === categoryId)
  return category?.categoryName || '未知分类'
}

function getLevelName(levelId: number) {
  const level = levels.value && levels.value.find(lv => lv.id === levelId)
  return level?.levelName || '未知级别'
}

function getLevelColor(levelId: number) {
  const level = levels.value && levels.value.find(lv => lv.id === levelId)
  return level?.color || '#d9d9d9'
}

function getMatchTypeText(matchType: number) {
  return MatchTypeTextMap[matchType as keyof typeof MatchTypeTextMap] || '未知'
}

async function refreshData() {
  await Promise.all([
    sensitiveWordsStore.fetchSensitiveWords(),
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels()
  ])
  message.success('数据刷新成功')
}

async function handleSearch() {
  await sensitiveWordsStore.fetchSensitiveWords(searchForm.value)
}

function resetSearch() {
  searchForm.value = {
    word: '',
    categoryId: undefined,
    levelId: undefined,
    isEnabled: undefined,
    matchType: undefined
  }
  sensitiveWordsStore.fetchSensitiveWords()
}

function showWordModal() {
  editingWordId.value = null
  wordForm.value = {
    word: '',
    categoryId: undefined,
    levelId: undefined,
    isEnabled: true,
    matchType: 1,
    replacement: '',
    description: ''
  }
  wordModalVisible.value = true
}

function editWord(record: SensitiveWord) {
  editingWordId.value = record.id!
  wordForm.value = { ...record }
  wordModalVisible.value = true
}

function viewWordDetail(record: SensitiveWord) {
  currentRecord.value = record
  detailVisible.value = true
}

async function deleteWord(id: number) {
  try {
    const success = await sensitiveWordsStore.deleteSensitiveWord(id)
    if (success) {
      message.success('删除成功')
    } else {
      message.error('删除失败')
    }
  } catch (error) {
    message.error('删除失败，请重试')
  }
}

async function toggleWordStatus(id: number, enabled: boolean) {
  try {
    const success = await sensitiveWordsStore.updateSensitiveWord(id, { isEnabled: enabled })
    if (success) {
      message.success(enabled ? '启用成功' : '禁用成功')
    } else {
      message.error('操作失败')
    }
  } catch (error) {
    message.error('操作失败，请重试')
  }
}

async function handleWordSubmit() {
  submitting.value = true
  try {
    let success = false
    if (editingWordId.value) {
      success = await sensitiveWordsStore.updateSensitiveWord(editingWordId.value, wordForm.value)
    } else {
      success = await sensitiveWordsStore.createSensitiveWord(wordForm.value as Omit<SensitiveWord, 'id'>)
    }

    if (success) {
      message.success(editingWordId.value ? '更新成功' : '创建成功')
      wordModalVisible.value = false
    } else {
      message.error('操作失败')
    }
  } catch (error) {
    message.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

function showBatchImport() {
  importVisible.value = true
}

function handleExportMenuClick({ key }: { key: string }) {
  exportWords(key as 'xlsx' | 'csv')
}

async function exportWords(format: 'xlsx' | 'csv' = 'xlsx') {
  try {
    // 获取当前搜索条件
    const exportParams = { ...searchForm.value }
    
    // 调用导出方法
    await sensitiveWordsStore.exportSensitiveWords(exportParams, format)
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

async function batchEnable() {
  try {
    // 模拟批量启用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success(`已启用 ${selectedRowKeys.value.length} 个敏感词`)
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    message.error('批量启用失败')
  }
}

async function batchDisable() {
  try {
    // 模拟批量禁用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success(`已禁用 ${selectedRowKeys.value.length} 个敏感词`)
    selectedRowKeys.value = []
    await refreshData()
  } catch (error) {
    message.error('批量禁用失败')
  }
}

async function batchDelete() {
  try {
    const success = await sensitiveWordsStore.batchDeleteWords(selectedRowKeys.value)
    if (success) {
      message.success(`已删除 ${selectedRowKeys.value.length} 个敏感词`)
      selectedRowKeys.value = []
    } else {
      message.error('批量删除失败')
    }
  } catch (error) {
    message.error('批量删除失败，请重试')
  }
}

function clearSelection() {
  selectedRowKeys.value = []
}

function beforeUpload(file: File) {
  const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'text/csv'
  if (!isValidType) {
    message.error('只支持Excel和CSV格式文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  return false // 阻止自动上传
}

async function handleBatchImport() {
  if (importFileList.value.length === 0) {
    message.warning('请选择要导入的文件')
    return
  }

  importing.value = true
  try {
    // 模拟导入过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    message.success('导入成功！共导入 245 条敏感词，跳过重复 12 条')
    importVisible.value = false
    importFileList.value = []
    await refreshData()
  } catch (error) {
    message.error('导入失败，请重试')
  } finally {
    importing.value = false
  }
}

// 生命周期
onMounted(async () => {


  await Promise.all([
    sensitiveWordsStore.fetchSensitiveWords(),
    sensitiveWordsStore.fetchCategories(),
    sensitiveWordsStore.fetchLevels()
  ])
  console.log('onMounted', sensitiveWordsStore.sensitiveWords);
})
</script>

<style lang="scss" scoped>
.word-manage-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .search-filter-section {
    margin-bottom: 16px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .search-form {
      .form-item-full {
        margin-bottom: 0;

        .ant-form-item-label {
          font-weight: 500;
        }
      }

      .search-actions {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
      }
    }
  }

  .batch-actions-section {
    margin-bottom: 16px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #1890ff;

      .ant-card-body {
        padding: 16px 24px;
      }
    }
  }

  .table-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .ant-table {
      .ant-table-thead>tr>th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody>tr:hover>td {
        background: #f5f5f5;
      }

      .word-text {
        font-weight: 500;
        color: #333;
      }

      .replacement-text {
        color: #666;
        font-style: italic;
      }
    }
  }

  .import-content {
    .ant-upload-dragger {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      text-align: center;
      padding: 40px 20px;

      &:hover {
        border-color: #1890ff;
      }

      .ant-upload-drag-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      .ant-upload-text {
        font-size: 16px;
        color: #666;
        margin-bottom: 8px;
      }

      .ant-upload-hint {
        font-size: 14px;
        color: #999;
      }
    }

    .import-options {
      padding: 16px;
      background: #f9f9f9;
      border-radius: 6px;
      border: 1px solid #e8e8e8;

      .ant-checkbox-wrapper {
        display: block;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .word-manage-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
          flex-wrap: wrap;
        }
      }
    }

    .search-form {
      .ant-col {
        margin-bottom: 16px;
      }

      .search-actions {
        justify-content: flex-start !important;
      }
    }

    .batch-actions-section {
      .ant-space {
        flex-wrap: wrap;
      }
    }

    .table-header {
      flex-direction: column !important;
      align-items: flex-start !important;
      gap: 8px;
    }
  }
}

@media (max-width: 576px) {
  .word-manage-page {
    .header-actions {
      .ant-space {
        .ant-btn {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }

    .ant-table {
      font-size: 12px;
    }
  }
}
</style>
