/**
 * 敏感词管理模拟API服务
 * 提供所有数据交互的假接口，方便后续真实接口入场
 */

// 模拟网络延迟
const mockDelay = (min = 300, max = 2000) => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min
    return new Promise(resolve => setTimeout(resolve, delay))
}

// 统一响应格式
interface ApiResponse<T = any> {
    code: number
    message: string
    data: T
    timestamp: number
}

const createResponse = <T>(data: T, message = '操作成功'): ApiResponse<T> => ({
    code: 200,
    message,
    data,
    timestamp: Date.now()
})

const createErrorResponse = (message = '操作失败', code = 500): ApiResponse<null> => ({
    code,
    message,
    data: null,
    timestamp: Date.now()
})

// 模拟数据生成器
const generateMockData = {
    // 生成统计数据
    statistics: () => ({
        totalWords: 702,
        enabledWords: 658,
        disabledWords: 44,
        totalCategories: 5,
        totalPolicies: 8,
        enabledPolicies: 6,
        todayDetections: 156,
        todayHits: 23,
        yesterdayDetections: 134,
        yesterdayHits: 19,
        weeklyDetections: 1089,
        weeklyHits: 167,
        monthlyDetections: 4567,
        monthlyHits: 678,
        categoryStats: [
            { categoryId: 1, categoryName: '暴力内容', wordCount: 156, hitCount: 8, riskLevel: 'high' },
            { categoryId: 2, categoryName: '色情内容', wordCount: 234, hitCount: 12, riskLevel: 'high' },
            { categoryId: 3, categoryName: '政治敏感', wordCount: 89, hitCount: 2, riskLevel: 'medium' },
            { categoryId: 4, categoryName: '违法违规', wordCount: 178, hitCount: 6, riskLevel: 'high' },
            { categoryId: 5, categoryName: '垃圾广告', wordCount: 45, hitCount: 1, riskLevel: 'low' }
        ],
        levelStats: [
            { levelId: 1, levelName: '高风险', wordCount: 345, hitCount: 18, color: '#ff4d4f' },
            { levelId: 2, levelName: '中风险', wordCount: 234, hitCount: 8, color: '#faad14' },
            { levelId: 3, levelName: '低风险', wordCount: 123, hitCount: 3, color: '#52c41a' }
        ],
        detectionTrend: Array.from({ length: 30 }, (_, i) => {
            const date = new Date()
            date.setDate(date.getDate() - (29 - i))
            return {
                date: date.toISOString().split('T')[0],
                detectionCount: Math.floor(Math.random() * 200) + 100,
                hitCount: Math.floor(Math.random() * 30) + 10,
                riskCount: Math.floor(Math.random() * 15) + 3
            }
        })
    }),

    // 生成敏感词列表
    sensitiveWords: (page = 1, pageSize = 20, filters: any = {}) => {
        const categories = ['暴力内容', '色情内容', '政治敏感', '违法违规', '垃圾广告']
        const levels = ['高风险', '中风险', '低风险']
        const statuses = ['enabled', 'disabled']

        const allWords = Array.from({ length: 702 }, (_, i) => ({
            id: i + 1,
            word: `敏感词${i + 1}`,
            category: categories[Math.floor(Math.random() * categories.length)],
            categoryId: Math.floor(Math.random() * 5) + 1,
            level: levels[Math.floor(Math.random() * levels.length)],
            levelId: Math.floor(Math.random() * 3) + 1,
            status: statuses[Math.floor(Math.random() * statuses.length)],
            hitCount: Math.floor(Math.random() * 100),
            lastHitTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            createTime: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
            updateTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            creator: `用户${Math.floor(Math.random() * 10) + 1}`,
            description: `这是一个${categories[Math.floor(Math.random() * categories.length)]}相关的敏感词`
        }))

        // 应用过滤条件
        let filteredWords = allWords
        if (filters.category) {
            filteredWords = filteredWords.filter(word => word.categoryId === filters.category)
        }
        if (filters.level) {
            filteredWords = filteredWords.filter(word => word.levelId === filters.level)
        }
        if (filters.status) {
            filteredWords = filteredWords.filter(word => word.status === filters.status)
        }
        if (filters.keyword) {
            filteredWords = filteredWords.filter(word =>
                word.word.includes(filters.keyword) ||
                word.description.includes(filters.keyword)
            )
        }

        // 分页
        const start = (page - 1) * pageSize
        const end = start + pageSize
        const pageData = filteredWords.slice(start, end)

        return {
            list: pageData,
            total: filteredWords.length,
            page,
            pageSize,
            totalPages: Math.ceil(filteredWords.length / pageSize)
        }
    },

    // 生成分类列表
    categories: () => [
        { id: 1, name: '暴力内容', description: '包含暴力、血腥等内容', wordCount: 156, status: 'enabled', createTime: '2024-01-01' },
        { id: 2, name: '色情内容', description: '包含色情、低俗等内容', wordCount: 234, status: 'enabled', createTime: '2024-01-01' },
        { id: 3, name: '政治敏感', description: '包含政治敏感内容', wordCount: 89, status: 'enabled', createTime: '2024-01-01' },
        { id: 4, name: '违法违规', description: '包含违法违规内容', wordCount: 178, status: 'enabled', createTime: '2024-01-01' },
        { id: 5, name: '垃圾广告', description: '包含垃圾广告内容', wordCount: 45, status: 'enabled', createTime: '2024-01-01' }
    ],

    // 生成策略列表
    policies: () => [
        { id: 1, name: '严格过滤策略', description: '对所有敏感词进行严格过滤', status: 'enabled', createTime: '2024-01-01' },
        { id: 2, name: '宽松过滤策略', description: '仅对高风险敏感词进行过滤', status: 'enabled', createTime: '2024-01-01' },
        { id: 3, name: '自定义策略1', description: '针对特定场景的自定义策略', status: 'disabled', createTime: '2024-01-01' },
        { id: 4, name: '自定义策略2', description: '针对特定场景的自定义策略', status: 'enabled', createTime: '2024-01-01' }
    ],

    // 生成检测记录
    detectionRecords: (page = 1, pageSize = 20) => {
        const records = Array.from({ length: 500 }, (_, i) => ({
            id: i + 1,
            content: `这是一段包含敏感词的测试内容${i + 1}`,
            hitWords: [`敏感词${Math.floor(Math.random() * 100) + 1}`],
            hitCount: Math.floor(Math.random() * 5) + 1,
            riskLevel: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)],
            source: ['用户输入', 'API调用', '批量检测'][Math.floor(Math.random() * 3)],
            createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            processResult: ['blocked', 'warned', 'passed'][Math.floor(Math.random() * 3)]
        }))

        const start = (page - 1) * pageSize
        const end = start + pageSize
        const pageData = records.slice(start, end)

        return {
            list: pageData,
            total: records.length,
            page,
            pageSize,
            totalPages: Math.ceil(records.length / pageSize)
        }
    }
}

// API接口实现
export const mockSensitiveWordsApi = {
    // 获取统计数据
    async fetchStatistics(params?: { dateRange?: [string, string] }) {
        await mockDelay()
        try {
            const data = generateMockData.statistics()
            return createResponse(data, '获取统计数据成功')
        } catch (error) {
            return createErrorResponse('获取统计数据失败')
        }
    },

    // 获取敏感词列表
    async fetchSensitiveWords(params?: {
        page?: number;
        pageSize?: number;
        category?: number;
        level?: number;
        status?: string;
        keyword?: string
    }) {
        await mockDelay()
        try {
            const { page = 1, pageSize = 20, ...filters } = params || {}
            const data = generateMockData.sensitiveWords(page, pageSize, filters)
            return createResponse(data, '获取敏感词列表成功')
        } catch (error) {
            return createErrorResponse('获取敏感词列表失败')
        }
    },

    // 创建敏感词
    async createSensitiveWord(wordData: any) {
        await mockDelay()
        try {
            const newWord = {
                id: Date.now(),
                ...wordData,
                hitCount: 0,
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString(),
                creator: '当前用户'
            }
            return createResponse(newWord, '创建敏感词成功')
        } catch (error) {
            return createErrorResponse('创建敏感词失败')
        }
    },

    // 更新敏感词
    async updateSensitiveWord(wordId: number, wordData: any) {
        await mockDelay()
        try {
            const updatedWord = {
                id: wordId,
                ...wordData,
                updateTime: new Date().toISOString()
            }
            return createResponse(updatedWord, '更新敏感词成功')
        } catch (error) {
            return createErrorResponse('更新敏感词失败')
        }
    },

    // 删除敏感词
    async deleteSensitiveWord(wordId: number) {
        await mockDelay()
        try {
            return createResponse({ id: wordId }, '删除敏感词成功')
        } catch (error) {
            return createErrorResponse('删除敏感词失败')
        }
    },

    // 批量删除敏感词
    async batchDeleteSensitiveWords(wordIds: number[]) {
        await mockDelay()
        try {
            return createResponse({ deletedIds: wordIds, count: wordIds.length }, `批量删除${wordIds.length}个敏感词成功`)
        } catch (error) {
            return createErrorResponse('批量删除敏感词失败')
        }
    },

    // 批量更新敏感词状态
    async batchUpdateWordStatus(wordIds: number[], status: string) {
        await mockDelay()
        try {
            return createResponse({ updatedIds: wordIds, status }, `批量${status === 'enabled' ? '启用' : '禁用'}${wordIds.length}个敏感词成功`)
        } catch (error) {
            return createErrorResponse('批量更新状态失败')
        }
    },

    // 获取分类列表
    async fetchCategories() {
        await mockDelay()
        try {
            const data = generateMockData.categories()
            return createResponse(data, '获取分类列表成功')
        } catch (error) {
            return createErrorResponse('获取分类列表失败')
        }
    },

    // 创建分类
    async createCategory(categoryData: any) {
        await mockDelay()
        try {
            const newCategory = {
                id: Date.now(),
                ...categoryData,
                wordCount: 0,
                createTime: new Date().toISOString()
            }
            return createResponse(newCategory, '创建分类成功')
        } catch (error) {
            return createErrorResponse('创建分类失败')
        }
    },

    // 更新分类
    async updateCategory(categoryId: number, categoryData: any) {
        await mockDelay()
        try {
            const updatedCategory = {
                id: categoryId,
                ...categoryData,
                updateTime: new Date().toISOString()
            }
            return createResponse(updatedCategory, '更新分类成功')
        } catch (error) {
            return createErrorResponse('更新分类失败')
        }
    },

    // 删除分类
    async deleteCategory(categoryId: number) {
        await mockDelay()
        try {
            return createResponse({ id: categoryId }, '删除分类成功')
        } catch (error) {
            return createErrorResponse('删除分类失败')
        }
    },

    // 获取策略列表
    async fetchPolicies() {
        await mockDelay()
        try {
            const data = generateMockData.policies()
            return createResponse(data, '获取策略列表成功')
        } catch (error) {
            return createErrorResponse('获取策略列表失败')
        }
    },

    // 创建策略
    async createPolicy(policyData: any) {
        await mockDelay()
        try {
            const newPolicy = {
                id: Date.now(),
                ...policyData,
                createTime: new Date().toISOString()
            }
            return createResponse(newPolicy, '创建策略成功')
        } catch (error) {
            return createErrorResponse('创建策略失败')
        }
    },

    // 更新策略
    async updatePolicy(policyId: number, policyData: any) {
        await mockDelay()
        try {
            const updatedPolicy = {
                id: policyId,
                ...policyData,
                updateTime: new Date().toISOString()
            }
            return createResponse(updatedPolicy, '更新策略成功')
        } catch (error) {
            return createErrorResponse('更新策略失败')
        }
    },

    // 删除策略
    async deletePolicy(policyId: number) {
        await mockDelay()
        try {
            return createResponse({ id: policyId }, '删除策略成功')
        } catch (error) {
            return createErrorResponse('删除策略失败')
        }
    },

    // 内容检测
    async detectContent(content: string, options?: any) {
        await mockDelay(500, 1500)
        try {
            // 模拟检测逻辑
            const sensitiveWords = ['敏感词1', '敏感词2', '违禁词']
            const hitWords = sensitiveWords.filter(word => content.includes(word))

            const result = {
                content,
                isHit: hitWords.length > 0,
                hitWords,
                hitCount: hitWords.length,
                riskLevel: hitWords.length > 2 ? 'high' : hitWords.length > 0 ? 'medium' : 'low',
                suggestion: hitWords.length > 0 ? 'blocked' : 'passed',
                processTime: Math.floor(Math.random() * 100) + 50, // ms
                detectionId: Date.now()
            }

            return createResponse(result, '内容检测完成')
        } catch (error) {
            return createErrorResponse('内容检测失败')
        }
    },

    // 批量导入敏感词
    async importSensitiveWords(file: File, options?: any) {
        await mockDelay(2000, 5000) // 导入需要更长时间
        try {
            // 模拟导入结果
            const importResult = {
                fileName: file.name,
                fileSize: file.size,
                totalRows: Math.floor(Math.random() * 500) + 100,
                successCount: Math.floor(Math.random() * 400) + 80,
                failCount: Math.floor(Math.random() * 20),
                duplicateCount: Math.floor(Math.random() * 30),
                importTime: new Date().toISOString(),
                errors: [
                    { row: 15, error: '敏感词不能为空' },
                    { row: 23, error: '分类不存在' },
                    { row: 45, error: '敏感词已存在' }
                ]
            }

            return createResponse(importResult, '导入完成')
        } catch (error) {
            return createErrorResponse('导入失败')
        }
    },

    // 导出敏感词
    async exportSensitiveWords(params?: any) {
        await mockDelay(1000, 3000)
        try {
            const exportResult = {
                fileName: `敏感词导出_${new Date().toISOString().split('T')[0]}.xlsx`,
                fileSize: Math.floor(Math.random() * 5000) + 1000, // KB
                downloadUrl: `https://mock-api.example.com/downloads/${Date.now()}.xlsx`,
                exportTime: new Date().toISOString(),
                recordCount: Math.floor(Math.random() * 500) + 200
            }
            return createResponse(exportResult, '导出成功')
        } catch (error) {
            return createErrorResponse('导出失败')
        }
    },

    // 获取检测记录
    async fetchDetectionRecords(params?: { page?: number; pageSize?: number; dateRange?: [string, string] }) {
        await mockDelay()
        try {
            const { page = 1, pageSize = 20 } = params || {}
            const data = generateMockData.detectionRecords(page, pageSize)
            return createResponse(data, '获取检测记录成功')
        } catch (error) {
            return createErrorResponse('获取检测记录失败')
        }
    },

    // 清空检测记录
    async clearDetectionRecords(beforeDate?: string) {
        await mockDelay()
        try {
            const clearedCount = Math.floor(Math.random() * 1000) + 500
            return createResponse({ clearedCount }, `清空${clearedCount}条检测记录成功`)
        } catch (error) {
            return createErrorResponse('清空检测记录失败')
        }
    },

    // 获取系统配置
    async fetchSystemConfig() {
        await mockDelay()
        try {
            const config = {
                maxWordLength: 50,
                maxBatchSize: 1000,
                detectionTimeout: 5000,
                cacheEnabled: true,
                logEnabled: true,
                autoUpdate: true,
                updateInterval: 24 // hours
            }
            return createResponse(config, '获取系统配置成功')
        } catch (error) {
            return createErrorResponse('获取系统配置失败')
        }
    },

    // 更新系统配置
    async updateSystemConfig(config: any) {
        await mockDelay()
        try {
            return createResponse(config, '更新系统配置成功')
        } catch (error) {
            return createErrorResponse('更新系统配置失败')
        }
    }
}

// 导出默认服务
export default mockSensitiveWordsApi