<template>
	<div class="sensitive-words-index">
		<!-- 页面头部 -->
		<div class="page-header">
			<div class="header-title">
				<h2>敏感词管理</h2>
				<p>多媒体内容检测系统，支持敏感词库管理、内容检测、过滤策略等功能</p>
			</div>
			<div class="header-actions">
				<a-space size="middle">
					<a-button type="primary" @click="refreshData">
						<template #icon><reload-outlined /></template>
						刷新数据
					</a-button>
					<a-button @click="showDetectionModal">
						<template #icon><safety-certificate-outlined /></template>
						内容检测
					</a-button>
				</a-space>
			</div>
		</div>

		<!-- 统计卡片区域 -->
		<div class="statistics-section">
			<a-row :gutter="[16, 16]">
				<a-col :xs="24" :sm="12" :md="6">
					<a-card class="stat-card">
						<a-statistic title="敏感词总数" :value="statistics?.totalWords || 0" :value-style="{ color: '#1890ff' }">
							<template #prefix>
								<file-text-outlined />
							</template>
						</a-statistic>
					</a-card>
				</a-col>
				<a-col :xs="24" :sm="12" :md="6">
					<a-card class="stat-card">
						<a-statistic title="启用词条" :value="statistics?.enabledWords || 0" :value-style="{ color: '#52c41a' }">
							<template #prefix>
								<check-circle-outlined />
							</template>
						</a-statistic>
					</a-card>
				</a-col>
				<a-col :xs="24" :sm="12" :md="6">
					<a-card class="stat-card">
						<a-statistic title="今日检测" :value="statistics?.todayDetections || 0" :value-style="{ color: '#faad14' }">
							<template #prefix>
								<radar-chart-outlined />
							</template>
						</a-statistic>
					</a-card>
				</a-col>
				<a-col :xs="24" :sm="12" :md="6">
					<a-card class="stat-card">
						<a-statistic title="今日命中" :value="statistics?.todayHits || 0" :value-style="{ color: '#ff4d4f' }">
							<template #prefix>
								<warning-outlined />
							</template>
						</a-statistic>
					</a-card>
				</a-col>
			</a-row>
		</div>

		<!-- 功能模块导航 -->
		<div class="modules-section">
			<a-row :gutter="[24, 24]">
				<!-- 敏感词库管理 - 外联华为 -->
				<a-col :span="24">
					<a-card hoverable class="module-card external-link" @click="openHuaweiWordLibrary">
						<template #cover>
							<div class="module-icon">
								<database-outlined />
								<div class="external-badge">
									<a-tag color="orange">外联华为</a-tag>
								</div>
							</div>
						</template>
						<a-card-meta title="敏感词库管理" description="管理敏感词分类和级别，建立完整的词库体系（华为云服务）" />
						<div class="module-stats">
							<a-space>
								<a-tag color="blue">{{ statistics?.totalCategories || 0 }}个分类</a-tag>
								<a-tag color="green">{{ statistics?.totalWords || 0 }}个词条</a-tag>
							</a-space>
						</div>
					</a-card>
				</a-col>

				<!-- 内容检测机制 - 核心功能 -->
				<a-col :span="24">
					<a-card hoverable class="module-card primary-feature" @click="showDetectionModal">
						<template #cover>
							<div class="module-icon">
								<safety-certificate-outlined />
								<div class="feature-badge">
									<a-tag color="red">核心功能</a-tag>
								</div>
							</div>
						</template>
						<a-card-meta title="内容检测机制" description="实时监控文本、图片、视频内容，智能识别敏感信息" />
						<div class="module-stats">
							<a-space>
								<a-tag color="red">今日 {{ statistics?.todayDetections || 0 }}</a-tag>
								<a-tag color="volcano">命中 {{ statistics?.todayHits || 0 }}</a-tag>
							</a-space>
						</div>
					</a-card>
				</a-col>

				<!-- 过滤策略 - 外联华为 -->
				<a-col :span="24">
					<a-card hoverable class="module-card external-link" @click="openHuaweiFilterPolicy">
						<template #cover>
							<div class="module-icon">
								<setting-outlined />
								<div class="external-badge">
									<a-tag color="orange">外联华为</a-tag>
								</div>
							</div>
						</template>
						<a-card-meta title="过滤策略管理" description="配置过滤策略和规则，设置不同场景的处理方式（华为云服务）" />
						<div class="module-stats">
							<a-space>
								<a-tag color="purple">{{ statistics?.enabledPolicies || 0 }}个启用</a-tag>
								<a-tag color="geekblue">{{ statistics?.totalPolicies || 0 }}个策略</a-tag>
							</a-space>
						</div>
					</a-card>
				</a-col>
			</a-row>
		</div>

		<!-- 内容检测弹窗 -->
		<a-modal
			title="内容检测机制"
			:visible="detectionVisible"
			@cancel="handleDetectionModalCancel"
			:footer="null"
			width="1000px"
			class="detection-modal"
		>
			<div class="detection-content">
				<!-- 检测输入区域 -->
				<div class="detection-input-section">
					<a-card title="内容输入" size="small">
						<a-tabs v-model:activeKey="activeDetectionTab" @change="handleTabChange">
							<a-tab-pane key="text" tab="文本检测">
								<a-textarea
									v-model:value="detectionForm.textContent"
									placeholder="请输入要检测的文本内容..."
									:rows="6"
									:maxlength="5000"
									show-count
								/>
							</a-tab-pane>
							<a-tab-pane key="image" tab="图片检测">
								<div class="image-upload-container">
									<!-- 上传区域 - 只在没有图片时显示 -->
									<a-upload-dragger
										v-if="!imagePreviewUrl"
										v-model:fileList="imageFileList"
										:before-upload="beforeImageUpload"
										accept="image/*"
										:multiple="false"
										@change="handleImageChange"
										:show-upload-list="false"
									>
										<p class="ant-upload-drag-icon">
											<picture-outlined />
										</p>
										<p class="ant-upload-text">点击或拖拽图片到此区域上传</p>
										<p class="ant-upload-hint">支持 JPG、PNG、GIF 等格式，单个图片检测</p>
									</a-upload-dragger>

									<!-- 图片预览区域 - 只在有图片时显示 -->
									<div v-if="imagePreviewUrl" class="image-preview-container">
										<div class="preview-header">
											<span>图片预览</span>
											<a-button type="text" size="small" @click="clearImagePreview">
												<template #icon><close-outlined /></template>
												删除图片
											</a-button>
										</div>
										<div class="preview-image-wrapper">
											<img :src="imagePreviewUrl" alt="预览图片" class="preview-image" />
										</div>
										<div class="image-info">
											<a-space>
												<a-tag>{{ imageFileName }}</a-tag>
												<a-tag color="blue">{{ imageFileSize }}</a-tag>
											</a-space>
										</div>
									</div>
								</div>
							</a-tab-pane>
							<a-tab-pane key="video" tab="视频检测">
								<div class="video-upload-container">
									<!-- 上传区域 - 只在没有视频时显示 -->
									<a-upload-dragger
										v-if="!videoPreviewUrl"
										v-model:fileList="videoFileList"
										:before-upload="beforeVideoUpload"
										accept="video/*"
										:multiple="false"
										@change="handleVideoChange"
										:show-upload-list="false"
									>
										<p class="ant-upload-drag-icon">
											<video-camera-outlined />
										</p>
										<p class="ant-upload-text">点击或拖拽视频到此区域上传</p>
										<p class="ant-upload-hint">支持 MP4、AVI、MOV 等格式，大小不超过100MB</p>
									</a-upload-dragger>

									<!-- 视频预览区域 - 只在有视频时显示 -->
									<div v-if="videoPreviewUrl" class="video-preview-container">
										<div class="preview-header">
											<span>视频预览</span>
											<a-space>
												<a-tag v-if="obsUploadResult" color="green">已上传OBS</a-tag>
												<a-button type="text" size="small" @click="clearVideoPreview">
													<template #icon><close-outlined /></template>
													删除视频
												</a-button>
											</a-space>
										</div>
										<div class="preview-video-wrapper">
											<video :src="videoPreviewUrl" controls class="preview-video" preload="metadata">您的浏览器不支持视频播放</video>
										</div>
										<div class="video-info">
											<a-space>
												<a-tag>{{ videoFileName }}</a-tag>
												<a-tag color="blue">{{ videoFileSize }}</a-tag>
												<a-tag v-if="obsUploadResult" color="orange">OBS: {{ obsUploadResult.fileName }}</a-tag>
											</a-space>
										</div>
									</div>
								</div>
							</a-tab-pane>
						</a-tabs>

						<div class="detection-actions">
							<a-space>
								<a-button 
									type="primary" 
									@click="startDetection" 
									:loading="detecting"
									:disabled="isDetectionDisabled"
								>
									<template #icon><play-circle-outlined /></template>
									开始检测
								</a-button>
								<a-button @click="clearDetectionForm">
									<template #icon><clear-outlined /></template>
									清空内容
								</a-button>
							</a-space>
						</div>
					</a-card>
				</div>

				<!-- 检测结果区域 -->
				<div class="detection-result-section" v-if="detectionResult">
					<a-card title="检测结果" size="small">
						<!-- 检测概览 -->
						<div class="result-overview">
							<a-row :gutter="16">
								<a-col :span="6">
									<a-statistic
										title="检测状态"
										:value="detectionResult.isClean ? '通过' : '未通过'"
										:value-style="{ color: detectionResult.isClean ? '#52c41a' : '#ff4d4f' }"
									/>
								</a-col>
								<a-col :span="6">
									<a-statistic
										title="风险级别"
										:value="getRiskLevelText(detectionResult.riskLevel)"
										:value-style="{ color: getRiskLevelColor(detectionResult.riskLevel) }"
									/>
								</a-col>
								<a-col :span="6">
									<a-statistic title="置信度" :value="Math.round(detectionResult.confidence * 100)" suffix="%" :value-style="{ color: '#1890ff' }" />
								</a-col>
								<a-col :span="6">
									<a-statistic title="命中词数" :value="detectionResult.hitWords?.length || 0" :value-style="{ color: '#faad14' }" />
								</a-col>
							</a-row>
						</div>

						<!-- 命中敏感词表格 -->
						<div class="hit-words-section" v-if="detectionResult.hitWords && detectionResult.hitWords.length > 0">
							<h4>命中敏感词</h4>
							<a-table :columns="hitWordsColumns" :data-source="detectionResult.hitWords" :pagination="false" size="small" bordered>
								<template #bodyCell="{ column, record }">
									<template v-if="column.key === 'word'">
										<a-tag color="red">{{ record.word }}</a-tag>
									</template>
									<template v-else-if="column.key === 'category'">
										<a-tag color="blue">{{ record.category }}</a-tag>
									</template>
									<template v-else-if="column.key === 'confidence'">
										<a-progress :percent="Math.round(record.confidence * 100)" size="small" :stroke-color="getConfidenceColor(record.confidence)" />
									</template>
								</template>
							</a-table>
						</div>

						<!-- 处理建议 -->
						<div class="suggestions-section">
							<h4>处理建议</h4>
							<a-list size="small" bordered>
								<a-list-item>
									<a-list-item-meta>
										<template #avatar>
											<a-avatar style="background-color: #f56a00">1</a-avatar>
										</template>
										<template #title>建议替换敏感词</template>
										<template #description> 将检测到的敏感词汇替换为合适的词语，保持内容完整性的同时消除敏感信息 </template>
									</a-list-item-meta>
								</a-list-item>
								<a-list-item>
									<a-list-item-meta>
										<template #avatar>
											<a-avatar style="background-color: #87d068">2</a-avatar>
										</template>
										<template #title>调整内容或描述</template>
										<template #description> 重新组织语言表达，调整内容描述方式，避免使用可能引起争议的词汇和表述 </template>
									</a-list-item-meta>
								</a-list-item>
								<a-list-item>
									<a-list-item-meta>
										<template #avatar>
											<a-avatar style="background-color: #108ee9">3</a-avatar>
										</template>
										<template #title>加强人工核查</template>
										<template #description> 对于复杂或边界情况，建议进行人工审核，确保内容符合平台规范和法律法规要求 </template>
									</a-list-item-meta>
								</a-list-item>
							</a-list>
						</div>

						<!-- 处理后内容 -->
						<!-- <div class="processed-content-section" v-if="detectionResult.processedContent">
              <h4>处理后内容</h4>
              <a-card size="small" class="processed-content">
                <pre>{{ detectionResult.processedContent }}</pre>
              </a-card>
            </div> -->
					</a-card>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useSensitiveWordsStore } from '@/store/modules/sensitive-words'
import { sensitiveWordsApi } from '@/api/sensitive-words'
import type { DetectionResult, HitWord } from '@/types/sensitive-words'
import {
	ReloadOutlined,
	SafetyCertificateOutlined,
	FileTextOutlined,
	CheckCircleOutlined,
	RadarChartOutlined,
	WarningOutlined,
	DatabaseOutlined,
	SettingOutlined,
	PictureOutlined,
	VideoCameraOutlined,
	PlayCircleOutlined,
	ClearOutlined,
	CloseOutlined,
} from '@ant-design/icons-vue'

const router = useRouter()
const sensitiveWordsStore = useSensitiveWordsStore()

// 响应式数据
const detectionVisible = ref(false)
const detecting = ref(false)
const activeDetectionTab = ref('text')
const pollingActive = ref(false) // 轮询状态标记

// 检测表单数据
const detectionForm = reactive({
	textContent: '',
	imageFile: null as File | null,
	videoFile: null as File | null,
})

// 文件列表
const imageFileList = ref([])
const videoFileList = ref([])

// 检测结果
const detectionResult = ref<DetectionResult | null>(null)

// 图片预览相关
const imagePreviewUrl = ref<string>('')
const imageFileName = ref<string>('')
const imageFileSize = ref<string>('')

// 视频预览相关
const videoPreviewUrl = ref<string>('')
const videoFileName = ref<string>('')
const videoFileSize = ref<string>('')

// OBS上传结果存储
const obsUploadResult = ref<{
	fileId: string
	fileName: string
	filePath: string
	fileSize: number
	contentType: string
	uploadTime: string
	url: string
} | null>(null)

// 统计数据使用store中的数据
const statistics = computed(() => sensitiveWordsStore.statistics)
const loading = computed(() => sensitiveWordsStore.loading)

// 检测按钮禁用状态
const isDetectionDisabled = computed(() => {
	// 视频检测时，必须有视频文件且成功上传到OBS
	if (activeDetectionTab.value === 'video') {
		return !detectionForm.videoFile || !obsUploadResult.value
	}
	// 图片检测时，必须有图片文件
	if (activeDetectionTab.value === 'image') {
		return !detectionForm.imageFile
	}
	// 文本检测时，必须有文本内容
	if (activeDetectionTab.value === 'text') {
		return !detectionForm.textContent.trim()
	}
	return false
})

// 命中敏感词表格列定义
const hitWordsColumns = [
	{
		title: '敏感词',
		dataIndex: 'word',
		key: 'word',
		width: 120,
		align: 'center',
	},
	{
		title: '分类',
		dataIndex: 'category',
		key: 'category',
		width: 100,
		align: 'center',
	},
	{
		title: '置信度',
		dataIndex: 'confidence',
		key: 'confidence',
		width: 120,
		align: 'center',
	},
]

// 方法定义
async function refreshData() {
	try {
		const success = await sensitiveWordsStore.fetchStatistics()
		if (success) {
			message.success('数据刷新成功')
		} else {
			message.error('获取数据失败')
		}
	} catch (error) {
		console.error('获取统计数据失败:', error)
		message.error('获取数据失败，请重试')
	}
}

function showDetectionModal() {
	detectionVisible.value = true
	detectionResult.value = null
}

function handleDetectionModalCancel() {
	// 停止轮询
	pollingActive.value = false
	// 关闭弹窗
	detectionVisible.value = false
}

// 华为云外联功能
function openHuaweiWordLibrary() {
	message.info('正在跳转到华为云敏感词库管理系统...')
	// 这里可以添加实际的华为云跳转逻辑
	window.open(
		'https://console.huaweicloud.com/moderation/?agencyId=1a7ff47926f64761b56b15d8a66dee23&region=cn-north-4&locale=zh-cn#/moderation/services/customDictionaryV3',
		'_blank'
	)
}

function openHuaweiFilterPolicy() {
	message.info('正在跳转到华为云过滤策略管理系统...')
	// 这里可以添加实际的华为云跳转逻辑
	window.open(
		'https://console.huaweicloud.com/moderation/?agencyId=1a7ff47926f64761b56b15d8a66dee23&region=cn-north-4&locale=zh-cn#/moderation/services/policyManageV3',
		'_blank'
	)
}

// 检测相关方法
function handleTabChange(key: string) {
	activeDetectionTab.value = key
	clearDetectionForm()
}

function clearDetectionForm() {
	// 停止轮询
	pollingActive.value = false

	detectionForm.textContent = ''
	detectionForm.imageFile = null
	detectionForm.videoFile = null
	imageFileList.value = []
	videoFileList.value = []
	detectionResult.value = null
	obsUploadResult.value = null
	// 清除图片预览
	clearImagePreview()
	// 清除视频预览
	clearVideoPreview()
}

function beforeImageUpload(file: File) {
	const isImage = file.type.startsWith('image/')
	if (!isImage) {
		message.error('只能上传图片文件!')
		return false
	}
	const isLt10M = file.size / 1024 / 1024 < 10
	if (!isLt10M) {
		message.error('图片大小不能超过10MB!')
		return false
	}

	// 保存文件并生成预览
	detectionForm.imageFile = file
	generateImagePreview(file)

	return false // 阻止自动上传
}

// 生成图片预览
function generateImagePreview(file: File) {
	const reader = new FileReader()
	reader.onload = (e) => {
		imagePreviewUrl.value = e.target?.result as string
		imageFileName.value = file.name
		imageFileSize.value = formatFileSize(file.size)
	}
	reader.readAsDataURL(file)
}

// 清除图片预览
function clearImagePreview() {
	imagePreviewUrl.value = ''
	imageFileName.value = ''
	imageFileSize.value = ''
	detectionForm.imageFile = null
	imageFileList.value = []
	detectionResult.value = null
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B'
	const k = 1024
	const sizes = ['B', 'KB', 'MB', 'GB']
	const i = Math.floor(Math.log(bytes) / Math.log(k))
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 轮询查询视频审核结果
async function pollVideoVerifyResult(jobId: string, maxAttempts: number = 20, interval: number = 5000) {
	let attempts = 0
	pollingActive.value = true // 开始轮询

	try {
		while (attempts < maxAttempts && pollingActive.value) {
			try {
				attempts++
				console.log(`正在进行第 ${attempts} 次查询，任务ID: ${jobId}`)
				console.log(detectionResult.value)

				const resultResponse = await sensitiveWordsApi.getVideoVerifyResult(jobId)

				if (resultResponse.code === 0 && resultResponse.data) {
					const { suggestion, details } = resultResponse.data

					// 检查是否有有效的审核结果
					const hasValidSuggestion = suggestion && (suggestion === 'pass' || suggestion === 'review' || suggestion === 'block')
					const hasValidDetails = details && Array.isArray(details) && details.length > 0

					// 审核完成的判断条件：有明确的 suggestion
					if (hasValidSuggestion) {
						console.log(`审核完成 - suggestion: ${suggestion}, details数量: ${details ? details.length : 0}`)
						
						// 根据 suggestion 决定是否显示检测结果
						if (suggestion === 'pass') {
							// 通过审核，无敏感词，不显示检测结果
							console.log('审核通过，无敏感内容，不显示检测结果')
							pollingActive.value = false
							return { type: 'pass', message: '视频审核成功' } // 返回特殊对象表示不显示检测结果但要显示成功消息
						} else if (suggestion === 'review') {
							// 等待人工审核，不显示检测结果
							console.log('等待人工审核，不显示检测结果')
							pollingActive.value = false
							return { type: 'review', message: '请人工审核该视频' } // 返回特殊对象表示不显示检测结果但要显示审核提示
						} else if (suggestion === 'block') {
							// 被阻止，显示检测结果
							console.log('内容被阻止，显示详细检测结果')
							
							// 转换华为云API响应为系统格式
							const finalResult = {
								isClean: false, // block 状态一定是未通过的
								riskLevel: 4, // block 状态为高风险
								confidence: details && details.length > 0 ? Math.min(...details.map((d: any) => parseFloat(d.confidence || '0.5'))) : 0.9,
								hitWords:
									details && details.length > 0
										? details.map((detail: any) => ({
												word: detail.segment || '视频敏感内容',
												category: detail.category || '未知',
												level: detail.riskLevel || '中风险',
												position: [0, 0],
												context: `检测到视频敏感内容: ${detail.category}`,
												action: 1,
												replacement: '***',
												confidence: parseFloat(detail.confidence || '0.5'),
										  }))
										: [],
								suggestions: ['建议替换敏感视频', '调整视频内容', '加强人工核查'],
								processedContent: '视频审核完成',
							}

							console.log('视频审核结果:', resultResponse.data)
							pollingActive.value = false // 结束轮询
							return finalResult
						}
					} else {
						// 记录当前状态，继续轮询
						console.log(`审核未完成 - suggestion: ${suggestion || 'undefined'}, details数量: ${details ? details.length : 0}, 继续轮询...`)

						// 当 suggestion 不是 pass 且 hasValidDetails 为 false 时，动态更新查询状态
						// if (detectionResult.value && detectionResult.value.hitWords.length > 0) {
						// 	detectionResult.value.hitWords[0].word = `正在查询中... (${attempts}/${maxAttempts})`
						// 	detectionResult.value.hitWords[0].context = `审核进行中，任务ID: ${jobId}，等待结果...`
						// 	detectionResult.value.processedContent = `审核进度: ${attempts}/${maxAttempts}，任务ID: ${jobId}`
						// }
					}
				}

				// 如果还没有结果，等待指定时间后继续查询
				if (attempts < maxAttempts && pollingActive.value) {
					console.log(`第 ${attempts} 次查询暂无结果，${interval / 1000}秒后重试...`)
					// 更新界面显示的查询进度
					if (detectionResult.value && detectionResult.value.hitWords.length > 0) {
						detectionResult.value.hitWords[0].word = `正在查询中... (${attempts}/${maxAttempts})`
						detectionResult.value.hitWords[0].context = `第 ${attempts} 次查询中，任务ID: ${jobId}`
						detectionResult.value.processedContent = `查询进度: ${attempts}/${maxAttempts}，任务ID: ${jobId}`
					}

					await new Promise((resolve) => setTimeout(resolve, interval))
				}
			} catch (error) {
				console.warn(`第 ${attempts} 次查询失败:`, error)

				// 如果不是最后一次尝试，继续下一次查询
				if (attempts < maxAttempts && pollingActive.value) {
					await new Promise((resolve) => setTimeout(resolve, interval))
				}
			}
		}

		// 轮询超时或被中断
		if (!pollingActive.value) {
			throw new Error('轮询被中断')
		} else {
			throw new Error(`查询视频审核结果超时，已尝试 ${maxAttempts} 次`)
		}
	} finally {
		pollingActive.value = false // 确保轮询状态被清除
	}
}

// 生成视频预览
function generateVideoPreview(file: File) {
	const videoUrl = URL.createObjectURL(file)
	videoPreviewUrl.value = videoUrl
	videoFileName.value = file.name
	videoFileSize.value = formatFileSize(file.size)
}

// 清除视频预览
function clearVideoPreview() {
	if (videoPreviewUrl.value) {
		URL.revokeObjectURL(videoPreviewUrl.value)
	}
	videoPreviewUrl.value = ''
	videoFileName.value = ''
	videoFileSize.value = ''
	detectionForm.videoFile = null
	videoFileList.value = []
	obsUploadResult.value = null
	detectionResult.value = null
}

async function beforeVideoUpload(file: File) {
	const isVideo = file.type.startsWith('video/')
	if (!isVideo) {
		message.error('只能上传视频文件!')
		return false
	}
	const isLt100M = file.size / 1024 / 1024 < 100
	if (!isLt100M) {
		message.error('视频大小不能超过100MB!')
		return false
	}

	// 文件验证通过后，生成预览并立即上传到OBS
	detectionForm.videoFile = file
	generateVideoPreview(file)

	try {
		message.loading('正在上传视频到OBS...', 0)
		const uploadResponse = await sensitiveWordsApi.uploadFileToObs(file)

		if (uploadResponse.code === 0 && uploadResponse.data) {
			obsUploadResult.value = uploadResponse.data
			message.destroy()
			message.success(`视频上传成功: ${uploadResponse.data.fileName}`)
			console.log('OBS上传成功:', uploadResponse.data)
		} else {
			message.destroy()
			message.warning('OBS上传失败，将使用本地检测')
			obsUploadResult.value = null
		}
	} catch (error) {
		message.destroy()
		console.warn('OBS上传失败:', error)
		message.warning('OBS上传失败，将使用本地检测')
		obsUploadResult.value = null
	}

	return false // 阻止antd自动上传
}

function handleImageChange(info: any) {
	if (info.fileList.length === 0) {
		detectionForm.imageFile = null
		return
	}

	// 只处理文件状态变化，不自动检测
	const file = info.file
	if (file && file.originFileObj) {
		detectionForm.imageFile = file.originFileObj
	}
}

// 新增图像检测方法
async function startImageDetection(file: File) {
	detecting.value = true
	try {
		let result: DetectionResult

		// 调用华为云图像检测API
		try {
			const apiResult = await sensitiveWordsApi.detectImageContentByHuawei(file)

			if (apiResult.code === 0 && apiResult.data) {
				// 转换华为云API响应为系统格式
				const huaweiData = apiResult.data as any
				const { suggestion, details } = huaweiData

				result = {
					isClean: suggestion === 'pass',
					riskLevel: suggestion === 'pass' ? 1 : suggestion === 'review' ? 3 : 4,
					confidence: details && details.length > 0 ? Math.min(...details.map((d: any) => parseFloat(d.confidence || '0.5'))) : 0.9,
					hitWords: details
						? details.map((detail: any) => ({
								word: detail.segment || '图像敏感内容',
								category: detail.category || '未知',
								level: detail.riskLevel || '中风险',
								position: [0, 0],
								context: `检测到图像敏感内容: ${detail.category}`,
								action: 1,
								replacement: '***',
								confidence: parseFloat(detail.confidence || '0.5'),
						  }))
						: [],
					suggestions: ['建议替换敏感图像', '调整图像内容', '加强人工核查'],
					processedContent: '图像检测完成',
				}

				console.log('华为云图像API检测结果:', apiResult.data)
			} else {
				throw new Error('华为云图像API返回异常')
			}
		} catch (apiError) {
			console.warn('华为云图像API调用失败，使用本地API:', apiError)
			// 降级到本地API
			const response = await sensitiveWordsApi.detectImageContent(file)
			result = response.data
		}

		detectionResult.value = result
		message.success('图像检测完成')
	} catch (error) {
		console.error('图像检测失败:', error)
		message.error('图像检测失败，请重试')

		// 如果所有API调用都失败，使用模拟数据作为最终降级方案
		const fallbackResult: DetectionResult = {
			isClean: Math.random() > 0.3,
			riskLevel: (Math.floor(Math.random() * 4) + 1) as any,
			confidence: Math.random() * 0.3 + 0.7,
			hitWords: generateMockImageHitWords(),
			suggestions: ['建议替换敏感图像', '调整图像内容', '加强人工核查'],
			processedContent: '图像检测完成（模拟数据）',
		}
		detectionResult.value = fallbackResult
		message.warning('使用模拟数据进行图像检测演示')
	} finally {
		detecting.value = false
	}
}

function generateMockImageHitWords(): HitWord[] {
	const mockCategories = ['血腥', '暴力', '违法违规', '不当内容']

	return mockCategories.slice(0, Math.floor(Math.random() * 3) + 1).map((category, index) => ({
		word: '图像敏感内容',
		category,
		level: '高风险',
		position: [0, 0],
		context: `检测到图像中包含${category}内容`,
		action: 1,
		replacement: '***',
		confidence: Math.random() * 0.3 + 0.7,
	}))
}

function handleVideoChange(info: any) {
	if (info.fileList.length === 0) {
		clearVideoPreview()
	}
}

async function startDetection() {
	// 验证输入
	if (activeDetectionTab.value === 'text' && !detectionForm.textContent.trim()) {
		message.warning('请输入要检测的文本内容')
		return
	}
	if (activeDetectionTab.value === 'image' && !detectionForm.imageFile) {
		message.warning('请上传要检测的图片')
		return
	}
	if (activeDetectionTab.value === 'video' && !detectionForm.videoFile) {
		message.warning('请上传要检测的视频')
		return
	}
	if (activeDetectionTab.value === 'video' && !obsUploadResult.value) {
		message.warning('请等待视频上传到OBS完成后再进行检测')
		return
	}

	detecting.value = true
	try {
		let result: DetectionResult

		// 根据检测类型调用不同的API
		if (activeDetectionTab.value === 'text') {
			// 文本检测 - 调用华为云内容审核API
			try {
				const apiResult = await sensitiveWordsApi.detectTextContentByHuawei(detectionForm.textContent)

				if (apiResult.code === 0 && apiResult.data) {
					// 转换华为云API响应为系统格式
					const huaweiData = apiResult.data as any
					const { suggestion, details } = huaweiData

					result = {
						isClean: suggestion === 'pass',
						riskLevel: suggestion === 'pass' ? 1 : suggestion === 'review' ? 3 : 4,
						confidence: details && details.length > 0 ? Math.min(...details.map((d: any) => parseFloat(d.confidence || '0.5'))) : 0.9,
						hitWords: details
							? details.map((detail: any) => ({
									word: detail.segment || '',
									category: detail.category || '未知',
									level: detail.riskLevel || '中风险',
									position: [0, detail.segment?.length || 0],
									context: `检测到敏感词: ${detail.segment}`,
									action: 1,
									replacement: '***',
									confidence: parseFloat(detail.confidence || '0.5'),
							  }))
							: [],
						suggestions: ['建议替换敏感词', '调整内容或描述', '加强人工核查'],
						processedContent: suggestion !== 'pass' ? detectionForm.textContent.replace(/[\u4e00-\u9fa5]+/g, '***') : detectionForm.textContent,
					}

					console.log('华为云API检测结果:', apiResult.data)
				} else {
					throw new Error('华为云API返回异常')
				}
			} catch (apiError) {
				console.warn('华为云API调用失败，使用本地API:', apiError)
				// 降级到本地API
				const response = await sensitiveWordsApi.detectTextContent(detectionForm.textContent)
				result = response.data
			}
		} else if (activeDetectionTab.value === 'image') {
			console.log(detectionForm.imageFile)
			// 图片检测 - 调用华为云图像检测API
			try {
				const apiResult = await sensitiveWordsApi.detectImageContentByHuawei(detectionForm.imageFile!)

				if (apiResult.code === 0 && apiResult.data) {
					// 转换华为云API响应为系统格式
					const huaweiData = apiResult.data as any
					const { suggestion, details } = huaweiData

					result = {
						isClean: suggestion === 'pass',
						riskLevel: suggestion === 'pass' ? 1 : suggestion === 'review' ? 3 : 4,
						confidence: details && details.length > 0 ? Math.min(...details.map((d: any) => parseFloat(d.confidence || '0.5'))) : 0.9,
						hitWords: details
							? details.map((detail: any) => ({
									word: detail.segment || '图像敏感内容',
									category: detail.category || '未知',
									level: detail.riskLevel || '中风险',
									position: [0, 0],
									context: `检测到图像敏感内容: ${detail.category}`,
									action: 1,
									replacement: '***',
									confidence: parseFloat(detail.confidence || '0.5'),
							  }))
							: [],
						suggestions: ['建议替换敏感图像', '调整图像内容', '加强人工核查'],
						processedContent: '图像检测完成',
					}

					console.log('华为云图像API检测结果:', apiResult.data)
				} else {
					throw new Error('华为云图像API返回异常')
				}
			} catch (apiError) {
				console.warn('华为云图像API调用失败，使用本地API:', apiError)
				// 降级到本地API
				const response = await sensitiveWordsApi.detectImageContent(detectionForm.imageFile!)
				result = response.data
			}
		} else if (activeDetectionTab.value === 'video') {
			// 视频检测：使用已经上传到OBS的文件或本地文件
			if (obsUploadResult.value) {
				// 使用已上传到OBS的文件进行华为云视频审核
				try {
					const obsFileUrl = obsUploadResult.value.url
					message.info('正在创建视频审核任务...')

					// 调用视频审核任务创建接口
					const verifyTaskResult = await sensitiveWordsApi.createVideoVerifyTask({ videoUrl: obsFileUrl })

					if (verifyTaskResult.code === 0 && verifyTaskResult.data) {
						const jobId = verifyTaskResult.data.job_id
						message.success(`视频审核任务创建成功，任务ID: ${jobId}`)
						if (!jobId) {
							return
						}
						// 先显示审核中的状态
						result = {
							isClean: false,
							riskLevel: 2,
							confidence: 0.8,
							hitWords: [
								{
									word: '正在查询中...',
									category: '审核任务',
									level: '查询中',
									position: [0, 0],
									context: `视频审核任务正在查询中，任务ID: ${jobId}`,
									action: 0,
									replacement: '正在获取结果',
									confidence: 1.0,
								},
							],
							suggestions: ['视频正在审核中，请稍候...', '系统正在获取审核结果', '请保持耐心等待'],
							processedContent: `正在查询审核结果，任务ID: ${jobId}`,
						}

						// 先显示查询中的状态
						// detectionResult.value = result
						detectionResult.value = null
						message.info('正在查询审核结果...')

						// 开始轮询查询结果
						try {
							const finalResult = await pollVideoVerifyResult(jobId)
							
							if (finalResult && finalResult.type) {
								// 审核完成但不需要显示检测结果（pass 或 review）
								detectionResult.value = null
								
								if (finalResult.type === 'pass') {
									message.success(finalResult.message) // '视频审核成功'
								} else if (finalResult.type === 'review') {
									message.warning(finalResult.message) // '请人工审核该视频'
								}
								return // 不显示检测结果区域
							} else if (finalResult) {
								// 审核完成且需要显示检测结果（block）
								result = finalResult
								message.success('视频审核结果获取成功')
							}
						} catch (resultError) {
							console.warn('轮询获取审核结果失败:', resultError)
							// 轮询失败，清除检测结果，不显示检测结果区域
							detectionResult.value = null
							message.warning('审核结果查询超时，请稍后重试')
							return // 直接返回，不显示检测结果
						}

						console.log('视频审核任务创建结果:', verifyTaskResult.data)
					} else {
						throw new Error('视频审核任务创建失败')
					}
				} catch (detectError) {
					console.warn('视频审核任务创建失败，使用本地API:', detectError)
					message.warning('华为云视频审核失败，使用本地检测')
					// 降级到本地检测
					const response = await sensitiveWordsApi.detectVideoContent(detectionForm.videoFile!)
					result = response.data
				}
			} else {
				// OBS上传失败，直接使用本地检测
				message.info('使用本地视频检测...')
				const response = await sensitiveWordsApi.detectVideoContent(detectionForm.videoFile!)
				result = response.data
			}
		} else {
			throw new Error('未知的检测类型')
		}

		detectionResult.value = result
		message.success('检测完成')
	} catch (error) {
		console.error('检测失败:', error)
		message.error('检测失败，请重试')

		// 如果所有API调用都失败，使用模拟数据作为最终降级方案
		const fallbackResult: DetectionResult = {
			isClean: Math.random() > 0.3,
			riskLevel: (Math.floor(Math.random() * 4) + 1) as any,
			confidence: Math.random() * 0.3 + 0.7,
			hitWords: generateMockHitWords(),
			suggestions: ['建议替换敏感词', '调整内容或描述', '加强人工核查'],
			processedContent: activeDetectionTab.value === 'text' ? detectionForm.textContent.replace(/敏感词/g, '***') : '已处理的内容',
		}
		detectionResult.value = fallbackResult
		message.warning('使用模拟数据进行检测演示')
	} finally {
		detecting.value = false
	}
}

function generateMockHitWords(): HitWord[] {
	const mockWords = ['敏感词', '违规内容', '不当言论']
	const categories = ['政治敏感', '暴力内容', '违法违规']

	return mockWords.map((word, index) => ({
		word,
		category: categories[index % categories.length],
		level: '高风险',
		position: [index * 10, index * 10 + word.length],
		context: `这是包含${word}的上下文内容`,
		action: 1,
		replacement: '***',
		confidence: Math.random() * 0.3 + 0.7,
	}))
}

function getRiskLevelText(level: number): string {
	const levelMap = {
		1: '安全',
		2: '低风险',
		3: '中风险',
		4: '高风险',
	}
	return levelMap[level as keyof typeof levelMap] || '未知'
}

function getRiskLevelColor(level: number): string {
	const colorMap = {
		1: '#52c41a',
		2: '#1890ff',
		3: '#faad14',
		4: '#ff4d4f',
	}
	return colorMap[level as keyof typeof colorMap] || '#666'
}

function getConfidenceColor(confidence: number): string {
	if (confidence >= 0.8) return '#52c41a'
	if (confidence >= 0.6) return '#faad14'
	return '#ff4d4f'
}

// 生命周期
onMounted(async () => {
	await refreshData()
})
</script>

<style lang="scss" scoped>
.sensitive-words-index {
	padding: 24px;
	background-color: #f5f5f5;
	min-height: 100vh;

	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24px;
		padding: 20px 24px;
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

		.header-title {
			h2 {
				margin: 0;
				color: #1890ff;
				font-size: 24px;
				font-weight: 600;
			}

			p {
				margin: 4px 0 0 0;
				color: #666;
				font-size: 14px;
			}
		}
	}

	.statistics-section {
		margin-bottom: 24px;

		.stat-card {
			text-align: center;
			border-radius: 8px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-2px);
				box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
			}
		}
	}

	.modules-section {
		margin-bottom: 24px;

		.module-card {
			height: 100%;
			cursor: pointer;
			transition: all 0.3s ease;
			position: relative;

			&:hover {
				transform: translateY(-4px);
				box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
			}

			&.external-link {
				border: 2px solid #faad14;

				.module-icon {
					background: linear-gradient(135deg, #fff7e6 0%, #ffe7ba 100%);
				}
			}

			&.primary-feature {
				border: 2px solid #ff4d4f;

				.module-icon {
					background: linear-gradient(135deg, #fff1f0 0%, #ffccc7 100%);
				}
			}

			.module-icon {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100px;
				font-size: 40px;
				color: #1890ff;
				background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
				position: relative;

				.external-badge,
				.feature-badge {
					position: absolute;
					top: 8px;
					right: 8px;
				}
			}

			.module-stats {
				margin-top: 12px;
				text-align: center;
			}
		}
	}
}

// 检测弹窗样式
.detection-modal {
	:deep(.ant-modal-body) {
		padding: 16px;
		max-height: 80vh;
		overflow-y: auto;
	}
}

.detection-content {
	.detection-input-section {
		margin-bottom: 16px;

		.detection-actions {
			margin-top: 16px;
			text-align: right;
		}

		.image-upload-container,
		.video-upload-container {
			.ant-upload-dragger {
				border: 2px dashed #d9d9d9;
				border-radius: 6px;
				background: #fafafa;
				text-align: center;
				padding: 40px 20px;
				margin-bottom: 16px;

				&:hover {
					border-color: #1890ff;
				}

				.ant-upload-drag-icon {
					font-size: 48px;
					color: #1890ff;
					margin-bottom: 16px;
				}
			}

			.image-preview-container,
			.video-preview-container {
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				padding: 16px;
				background: #fff;

				.preview-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 12px;
					font-weight: 500;
					color: #333;
				}

				.preview-image-wrapper {
					text-align: center;
					margin-bottom: 12px;

					.preview-image {
						max-width: 100%;
						max-height: 200px;
						border-radius: 4px;
						box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
					}
				}

				.preview-video-wrapper {
					text-align: center;
					margin-bottom: 12px;

					.preview-video {
						max-width: 100%;
						max-height: 300px;
						border-radius: 4px;
						box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
						background-color: #000;
					}
				}

				.image-info,
				.video-info {
					text-align: center;
				}
			}
		}
	}

	.detection-result-section {
		.result-overview {
			margin-bottom: 24px;
			padding: 16px;
			background: #fafafa;
			border-radius: 6px;
		}

		.hit-words-section {
			margin-bottom: 24px;

			h4 {
				margin-bottom: 12px;
				color: #333;
				font-weight: 600;
			}
		}

		.suggestions-section {
			margin-bottom: 24px;

			h4 {
				margin-bottom: 12px;
				color: #333;
				font-weight: 600;
			}
		}

		.processed-content-section {
			margin-bottom: 24px;

			h4 {
				margin-bottom: 12px;
				color: #333;
				font-weight: 600;
			}

			.processed-content {
				background: #f6ffed;
				border: 1px solid #b7eb8f;

				pre {
					margin: 0;
					white-space: pre-wrap;
					word-break: break-word;
				}
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.sensitive-words-index {
		padding: 16px;

		.page-header {
			flex-direction: column;
			align-items: flex-start;
			gap: 16px;
		}

		.module-icon {
			height: 80px !important;
			font-size: 32px !important;
		}
	}

	.detection-modal {
		:deep(.ant-modal) {
			margin: 0;
			max-width: 100vw;
		}
	}
}
</style>
