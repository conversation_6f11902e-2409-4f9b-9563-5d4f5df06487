<template>
  <div class="content-detection-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>内容检测</h2>
        <p>文本、图像、视频、语音等多媒体内容的敏感词检测</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
          <a-button @click="clearHistory">
            <template #icon><clear-outlined /></template>
            清空历史
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 检测区域 -->
    <div class="detection-section">
      <a-card title="内容检测">
        <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
          <!-- 文本检测 -->
          <a-tab-pane key="text" tab="文本检测">
            <div class="text-detection">
              <a-form :model="textForm" layout="vertical">
                <a-form-item label="检测内容">
                  <a-textarea
                    v-model:value="textForm.content"
                    placeholder="请输入要检测的文本内容..."
                    :rows="8"
                    show-count
                    :maxlength="10000"
                  />
                </a-form-item>
                <a-form-item>
                  <a-space>
                    <a-button 
                      type="primary" 
                      @click="detectText" 
                      :loading="detecting"
                      :disabled="!textForm.content.trim()"
                    >
                      开始检测
                    </a-button>
                    <a-button @click="clearText">清空内容</a-button>
                    <a-button @click="loadSample('text')">加载示例</a-button>
                  </a-space>
                </a-form-item>
              </a-form>
            </div>
          </a-tab-pane>

          <!-- 图像检测 -->
          <a-tab-pane key="image" tab="图像检测">
            <div class="image-detection">
              <a-upload-dragger
                v-model:fileList="imageFileList"
                :before-upload="beforeImageUpload"
                accept="image/*"
                :multiple="false"
                @change="handleImageChange"
              >
                <p class="ant-upload-drag-icon">
                  <picture-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽图片到此区域上传</p>
                <p class="ant-upload-hint">支持 JPG、PNG、GIF 格式，单个文件不超过10MB</p>
              </a-upload-dragger>
              
              <div class="upload-actions" v-if="imageFileList.length > 0">
                <a-space>
                  <a-button 
                    type="primary" 
                    @click="detectImage" 
                    :loading="detecting"
                  >
                    开始检测
                  </a-button>
                  <a-button @click="clearImage">清空图片</a-button>
                </a-space>
              </div>
            </div>
          </a-tab-pane>

          <!-- 视频检测 -->
          <a-tab-pane key="video" tab="视频检测">
            <div class="video-detection">
              <a-upload-dragger
                v-model:fileList="videoFileList"
                :before-upload="beforeVideoUpload"
                accept="video/*"
                :multiple="false"
                @change="handleVideoChange"
              >
                <p class="ant-upload-drag-icon">
                  <video-camera-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽视频到此区域上传</p>
                <p class="ant-upload-hint">支持 MP4、AVI、MOV 格式，单个文件不超过100MB</p>
              </a-upload-dragger>
              
              <div class="upload-actions" v-if="videoFileList.length > 0">
                <a-space>
                  <a-button 
                    type="primary" 
                    @click="detectVideo" 
                    :loading="detecting"
                  >
                    开始检测
                  </a-button>
                  <a-button @click="clearVideo">清空视频</a-button>
                </a-space>
              </div>
            </div>
          </a-tab-pane>

          <!-- 语音检测 -->
          <a-tab-pane key="audio" tab="语音检测">
            <div class="audio-detection">
              <a-upload-dragger
                v-model:fileList="audioFileList"
                :before-upload="beforeAudioUpload"
                accept="audio/*"
                :multiple="false"
                @change="handleAudioChange"
              >
                <p class="ant-upload-drag-icon">
                  <audio-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽音频到此区域上传</p>
                <p class="ant-upload-hint">支持 MP3、WAV、AAC 格式，单个文件不超过50MB</p>
              </a-upload-dragger>
              
              <div class="upload-actions" v-if="audioFileList.length > 0">
                <a-space>
                  <a-button 
                    type="primary" 
                    @click="detectAudio" 
                    :loading="detecting"
                  >
                    开始检测
                  </a-button>
                  <a-button @click="clearAudio">清空音频</a-button>
                </a-space>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 检测结果 -->
    <div class="result-section" v-if="currentResult">
      <a-card title="检测结果">
        <div class="result-header">
          <div class="result-status">
            <a-tag 
              :color="currentResult.isClean ? 'green' : 'red'" 
              class="status-tag"
            >
              {{ currentResult.isClean ? '内容安全' : '检测到风险' }}
            </a-tag>
            <a-tag 
              :color="getRiskLevelColor(currentResult.riskLevel)" 
              class="risk-tag"
            >
              {{ getRiskLevelText(currentResult.riskLevel) }}
            </a-tag>
            <span class="confidence">
              置信度: {{ Math.round(currentResult.confidence * 100) }}%
            </span>
          </div>
        </div>

        <div class="result-content">
          <a-row :gutter="[16, 16]">
            <!-- 命中敏感词 -->
            <a-col :xs="24" :lg="12" v-if="currentResult.hitWords.length > 0">
              <div class="hit-words-section">
                <h4>命中敏感词 ({{ currentResult.hitWords.length }}个)</h4>
                <div class="hit-words-list">
                  <div 
                    v-for="(hit, index) in currentResult.hitWords" 
                    :key="index"
                    class="hit-word-item"
                  >
                    <div class="hit-word-info">
                      <span class="hit-word">{{ hit.word }}</span>
                      <a-tag color="blue" size="small">{{ hit.category }}</a-tag>
                      <a-tag color="orange" size="small">{{ hit.level }}</a-tag>
                    </div>
                    <div class="hit-context">
                      <span class="context-label">上下文:</span>
                      <span class="context-text">{{ hit.context }}</span>
                    </div>
                    <div class="hit-action">
                      <span class="action-label">建议动作:</span>
                      <a-tag :color="getActionColor(hit.action)">
                        {{ getActionText(hit.action) }}
                      </a-tag>
                      <span v-if="hit.replacement" class="replacement">
                        替换为: {{ hit.replacement }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </a-col>

            <!-- 处理建议 -->
            <a-col :xs="24" :lg="12">
              <div class="suggestions-section">
                <h4>处理建议</h4>
                <ul class="suggestions-list">
                  <li v-for="(suggestion, index) in currentResult.suggestions" :key="index">
                    {{ suggestion }}
                  </li>
                </ul>
              </div>

              <!-- 处理后内容 -->
              <div class="processed-content-section" v-if="currentResult.processedContent">
                <h4>处理后内容</h4>
                <div class="processed-content">
                  {{ currentResult.processedContent }}
                </div>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-card>
    </div>

    <!-- 检测历史 -->
    <div class="history-section">
      <a-card title="检测历史">
        <a-table
          :columns="historyColumns"
          :data-source="detectionTasks"
          :loading="loading"
          :pagination="{ pageSize: 5 }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'contentType'">
              <a-tag :color="getContentTypeColor(record.contentType)">
                {{ getContentTypeText(record.contentType) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'result'">
              <div v-if="record.result">
                <a-tag :color="record.result.isClean ? 'green' : 'red'" size="small">
                  {{ record.result.isClean ? '安全' : '风险' }}
                </a-tag>
                <a-tag :color="getRiskLevelColor(record.result.riskLevel)" size="small">
                  {{ getRiskLevelText(record.result.riskLevel) }}
                </a-tag>
              </div>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="viewResult(record)">查看结果</a-button>
                <a-button type="link" size="small" @click="redetect(record)">重新检测</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useSensitiveWordsStore } from '@/store/modules/sensitive-words'
import type { DetectionTask, DetectionResult } from '@/types/sensitive-words'
import { 
  ContentTypeTextMap, 
  ContentTypeColorMap,
  DetectionStatusTextMap,
  DetectionStatusColorMap,
  RiskLevelTextMap,
  RiskLevelColorMap,
  FilterActionTextMap,
  FilterActionColorMap
} from '@/types/sensitive-words'
import {
  ReloadOutlined,
  ClearOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined
} from '@ant-design/icons-vue'

const sensitiveWordsStore = useSensitiveWordsStore()

// 响应式数据
const activeTab = ref('text')
const detecting = ref(false)
const currentResult = ref<DetectionResult | null>(null)

// 表单数据
const textForm = ref({
  content: ''
})

const imageFileList = ref([])
const videoFileList = ref([])
const audioFileList = ref([])

// 计算属性
const { detectionTasks, loading } = sensitiveWordsStore

// 表格列定义
const historyColumns = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
    key: 'taskName',
    width: 200,
    ellipsis: true
  },
  {
    title: '内容类型',
    key: 'contentType',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '检测结果',
    key: 'result',
    width: 150,
    align: 'center'
  },
  {
    title: '检测时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center'
  }
]

// 示例数据
const sampleTexts = {
  text: '这是一段包含敏感词的测试文本，用于演示检测功能。'
}

// 方法定义
function getContentTypeText(type: number) {
  return ContentTypeTextMap[type as keyof typeof ContentTypeTextMap] || '未知'
}

function getContentTypeColor(type: number) {
  return ContentTypeColorMap[type as keyof typeof ContentTypeColorMap] || 'default'
}

function getStatusText(status: number) {
  return DetectionStatusTextMap[status as keyof typeof DetectionStatusTextMap] || '未知'
}

function getStatusColor(status: number) {
  return DetectionStatusColorMap[status as keyof typeof DetectionStatusColorMap] || 'default'
}

function getRiskLevelText(level: number) {
  return RiskLevelTextMap[level as keyof typeof RiskLevelTextMap] || '未知'
}

function getRiskLevelColor(level: number) {
  return RiskLevelColorMap[level as keyof typeof RiskLevelColorMap] || 'default'
}

function getActionText(action: number) {
  return FilterActionTextMap[action as keyof typeof FilterActionTextMap] || '未知'
}

function getActionColor(action: number) {
  return FilterActionColorMap[action as keyof typeof FilterActionColorMap] || 'default'
}

async function refreshData() {
  await sensitiveWordsStore.fetchDetectionTasks()
  message.success('数据刷新成功')
}

function clearHistory() {
  message.info('清空历史功能开发中...')
}

function handleTabChange(key: string) {
  activeTab.value = key
  currentResult.value = null
}

async function detectText() {
  if (!textForm.value.content.trim()) {
    message.warning('请输入要检测的文本内容')
    return
  }

  detecting.value = true
  try {
    const task = await sensitiveWordsStore.detectContent({
      taskName: `文本检测-${new Date().toLocaleString()}`,
      contentType: 1,
      content: textForm.value.content
    })
    
    if (task && task.result) {
      currentResult.value = task.result
      message.success('文本检测完成')
    } else {
      message.error('检测失败')
    }
  } catch (error) {
    message.error('检测失败，请重试')
  } finally {
    detecting.value = false
  }
}

function clearText() {
  textForm.value.content = ''
  currentResult.value = null
}

function loadSample(type: string) {
  if (type === 'text') {
    textForm.value.content = sampleTexts.text
  }
}

function beforeImageUpload(file: File) {
  const isImage = file.type.startsWith('image/')
  if (!isImage) {
    message.error('只能上传图片文件!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('图片大小不能超过10MB!')
    return false
  }
  return false // 阻止自动上传
}

function beforeVideoUpload(file: File) {
  const isVideo = file.type.startsWith('video/')
  if (!isVideo) {
    message.error('只能上传视频文件!')
    return false
  }
  const isLt100M = file.size / 1024 / 1024 < 100
  if (!isLt100M) {
    message.error('视频大小不能超过100MB!')
    return false
  }
  return false
}

function beforeAudioUpload(file: File) {
  const isAudio = file.type.startsWith('audio/')
  if (!isAudio) {
    message.error('只能上传音频文件!')
    return false
  }
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    message.error('音频大小不能超过50MB!')
    return false
  }
  return false
}

function handleImageChange() {
  currentResult.value = null
}

function handleVideoChange() {
  currentResult.value = null
}

function handleAudioChange() {
  currentResult.value = null
}

async function detectImage() {
  detecting.value = true
  try {
    const task = await sensitiveWordsStore.detectContent({
      taskName: `图像检测-${new Date().toLocaleString()}`,
      contentType: 2,
      fileName: imageFileList.value[0]?.name || '未知图片'
    })
    
    if (task && task.result) {
      currentResult.value = task.result
      message.success('图像检测完成')
    } else {
      message.error('检测失败')
    }
  } catch (error) {
    message.error('检测失败，请重试')
  } finally {
    detecting.value = false
  }
}

async function detectVideo() {
  detecting.value = true
  try {
    const task = await sensitiveWordsStore.detectContent({
      taskName: `视频检测-${new Date().toLocaleString()}`,
      contentType: 3,
      fileName: videoFileList.value[0]?.name || '未知视频'
    })
    
    if (task && task.result) {
      currentResult.value = task.result
      message.success('视频检测完成')
    } else {
      message.error('检测失败')
    }
  } catch (error) {
    message.error('检测失败，请重试')
  } finally {
    detecting.value = false
  }
}

async function detectAudio() {
  detecting.value = true
  try {
    const task = await sensitiveWordsStore.detectContent({
      taskName: `语音检测-${new Date().toLocaleString()}`,
      contentType: 4,
      fileName: audioFileList.value[0]?.name || '未知音频'
    })
    
    if (task && task.result) {
      currentResult.value = task.result
      message.success('语音检测完成')
    } else {
      message.error('检测失败')
    }
  } catch (error) {
    message.error('检测失败，请重试')
  } finally {
    detecting.value = false
  }
}

function clearImage() {
  imageFileList.value = []
  currentResult.value = null
}

function clearVideo() {
  videoFileList.value = []
  currentResult.value = null
}

function clearAudio() {
  audioFileList.value = []
  currentResult.value = null
}

function viewResult(record: DetectionTask) {
  if (record.result) {
    currentResult.value = record.result
    message.info('已显示检测结果')
  } else {
    message.warning('该任务暂无检测结果')
  }
}

function redetect(record: DetectionTask) {
  message.info('重新检测功能开发中...')
}

// 生命周期
onMounted(async () => {
  await sensitiveWordsStore.fetchDetectionTasks()
})
</script>

<style lang="scss" scoped>
.content-detection-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .detection-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .text-detection {
      .ant-textarea {
        font-family: 'Courier New', monospace;
        line-height: 1.6;
      }
    }

    .ant-upload-dragger {
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      background: #fafafa;
      text-align: center;
      padding: 40px 20px;
      transition: border-color 0.3s ease;

      &:hover {
        border-color: #1890ff;
      }

      .ant-upload-drag-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      .ant-upload-text {
        font-size: 16px;
        color: #666;
        margin-bottom: 8px;
      }

      .ant-upload-hint {
        font-size: 14px;
        color: #999;
      }
    }

    .upload-actions {
      margin-top: 16px;
      text-align: center;
    }
  }

  .result-section {
    margin-bottom: 24px;

    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .result-header {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      .result-status {
        display: flex;
        align-items: center;
        gap: 12px;

        .status-tag {
          font-size: 16px;
          font-weight: 600;
          padding: 6px 12px;
        }

        .risk-tag {
          font-size: 14px;
          padding: 4px 8px;
        }

        .confidence {
          color: #666;
          font-size: 14px;
        }
      }
    }

    .result-content {
      .hit-words-section {
        h4 {
          color: #333;
          margin-bottom: 16px;
          font-weight: 600;
        }

        .hit-words-list {
          .hit-word-item {
            margin-bottom: 16px;
            padding: 16px;
            background: #fff2e8;
            border-radius: 8px;
            border-left: 4px solid #ff7a45;

            .hit-word-info {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;

              .hit-word {
                font-weight: 600;
                color: #d4380d;
                background: #fff1f0;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            .hit-context {
              margin-bottom: 8px;
              font-size: 14px;

              .context-label {
                color: #666;
                margin-right: 8px;
              }

              .context-text {
                color: #333;
                background: #f9f9f9;
                padding: 2px 6px;
                border-radius: 4px;
              }
            }

            .hit-action {
              font-size: 14px;

              .action-label {
                color: #666;
                margin-right: 8px;
              }

              .replacement {
                margin-left: 8px;
                color: #52c41a;
                font-weight: 500;
              }
            }
          }
        }
      }

      .suggestions-section {
        h4 {
          color: #333;
          margin-bottom: 16px;
          font-weight: 600;
        }

        .suggestions-list {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            color: #666;
            line-height: 1.6;
          }
        }
      }

      .processed-content-section {
        margin-top: 24px;

        h4 {
          color: #333;
          margin-bottom: 16px;
          font-weight: 600;
        }

        .processed-content {
          padding: 16px;
          background: #f6ffed;
          border: 1px solid #b7eb8f;
          border-radius: 6px;
          color: #333;
          line-height: 1.6;
          font-family: 'Courier New', monospace;
        }
      }
    }
  }

  .history-section {
    .ant-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-detection-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .header-actions {
        width: 100%;

        .ant-space {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    .result-header {
      .result-status {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px;
      }
    }

    .result-content {
      .hit-word-info {
        flex-wrap: wrap !important;
      }

      .hit-context,
      .hit-action {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 4px;
      }
    }

    .ant-upload-dragger {
      padding: 24px 16px !important;

      .ant-upload-drag-icon {
        font-size: 36px !important;
      }
    }
  }
}

@media (max-width: 576px) {
  .content-detection-page {
    .result-status {
      .status-tag,
      .risk-tag {
        font-size: 12px !important;
        padding: 4px 8px !important;
      }
    }

    .hit-word-item {
      padding: 12px !important;

      .hit-word-info {
        gap: 4px !important;
      }
    }

    .ant-table {
      font-size: 12px;

      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 8px 4px;
      }
    }
  }
}
</style>
