/**
 * 权限路由守卫
 * 用于控制案例征集系统的页面访问权限
 */

import type { Router, RouteLocationNormalized } from 'vue-router';
import { CASE_COLLECTION_PERMISSIONS, hasPermission } from '@/config/permissions';

/**
 * 路由权限映射
 * 定义每个路由需要的权限
 */
const ROUTE_PERMISSIONS: Record<string, string[]> = {
  // 数据看板
  '/case-collection': [CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW],
  '/case-collection/dashboard': [CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW],

  // 活动管理
  '/case-collection/activities': [CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW],
  '/case-collection/activities/list': [CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW],
  '/case-collection/activities/create': [CASE_COLLECTION_PERMISSIONS.ACTIVITY.CREATE],
  '/case-collection/activities/:id': [CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW],
  '/case-collection/activities/:id/edit': [CASE_COLLECTION_PERMISSIONS.ACTIVITY.EDIT],

  // 案例提交
  '/case-collection/submit': [CASE_COLLECTION_PERMISSIONS.SUBMISSION.CREATE],
  '/case-collection/submit/form': [CASE_COLLECTION_PERMISSIONS.SUBMISSION.CREATE],
  '/case-collection/submissions': [CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW],
  '/case-collection/submissions/:id': [CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW],

  // 审核管理
  '/case-collection/review': [CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW],
  '/case-collection/review/list': [CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW],
  '/case-collection/review/detail/:id': [CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW],
  '/case-collection/review/batch': [CASE_COLLECTION_PERMISSIONS.REVIEW.BATCH],
  '/case-collection/review/history': [CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW],
  '/case-collection/review/settings': [CASE_COLLECTION_PERMISSIONS.REVIEW.SETTINGS],

  // 分类管理
  '/case-collection/categories': [CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW]
};

/**
 * 获取用户权限
 * 这里应该从用户状态管理或API获取真实的用户权限
 * @returns 用户权限列表
 */
function getUserPermissions(): string[] {
  // 模拟获取用户权限
  // 实际项目中应该从 Vuex/Pinia 或其他状态管理获取
  const userRole = localStorage.getItem('userRole') || 'USER';
  
  // 根据角色返回对应权限
  switch (userRole) {
    case 'ADMIN':
      return Object.values(CASE_COLLECTION_PERMISSIONS).flatMap(group => Object.values(group));
    case 'ACTIVITY_MANAGER':
      return [
        CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
        CASE_COLLECTION_PERMISSIONS.DASHBOARD.EXPORT,
        ...Object.values(CASE_COLLECTION_PERMISSIONS.ACTIVITY),
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL,
        ...Object.values(CASE_COLLECTION_PERMISSIONS.REVIEW),
        ...Object.values(CASE_COLLECTION_PERMISSIONS.CATEGORY)
      ];
    case 'REVIEWER':
      return [
        CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
        CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW,
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW_ALL,
        CASE_COLLECTION_PERMISSIONS.REVIEW.VIEW,
        CASE_COLLECTION_PERMISSIONS.REVIEW.APPROVE,
        CASE_COLLECTION_PERMISSIONS.REVIEW.REJECT,
        CASE_COLLECTION_PERMISSIONS.REVIEW.BATCH,
        CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW
      ];
    default:
      return [
        CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW,
        CASE_COLLECTION_PERMISSIONS.ACTIVITY.VIEW,
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.VIEW,
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.CREATE,
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.EDIT,
        CASE_COLLECTION_PERMISSIONS.SUBMISSION.DELETE,
        CASE_COLLECTION_PERMISSIONS.CATEGORY.VIEW
      ];
  }
}

/**
 * 检查路由权限
 * @param route 目标路由
 * @returns 是否有权限访问
 */
function checkRoutePermission(route: RouteLocationNormalized): boolean {
  const userPermissions = getUserPermissions();
  const routePath = route.path;
  
  // 直接匹配
  if (ROUTE_PERMISSIONS[routePath]) {
    const requiredPermissions = ROUTE_PERMISSIONS[routePath];
    return requiredPermissions.some(permission => hasPermission(userPermissions, permission));
  }
  
  // 动态路由匹配
  for (const [pattern, permissions] of Object.entries(ROUTE_PERMISSIONS)) {
    if (pattern.includes(':')) {
      const regex = new RegExp('^' + pattern.replace(/:[^/]+/g, '[^/]+') + '$');
      if (regex.test(routePath)) {
        return permissions.some(permission => hasPermission(userPermissions, permission));
      }
    }
  }
  
  // 如果是案例征集系统的路由但没有配置权限，默认需要基础查看权限
  if (routePath.startsWith('/case-collection/')) {
    return hasPermission(userPermissions, CASE_COLLECTION_PERMISSIONS.DASHBOARD.VIEW);
  }
  
  // 非案例征集系统路由，允许访问
  return true;
}

/**
 * 安装权限路由守卫
 * @param router Vue Router 实例
 */
export function setupPermissionGuard(router: Router) {
  router.beforeEach((to, from, next) => {
    // 检查是否是案例征集系统的路由
    if (to.path.startsWith('/case-collection/')) {
      // 检查用户是否登录
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) {
        // 未登录，跳转到登录页
        next('/login?redirect=' + encodeURIComponent(to.fullPath));
        return;
      }
      
      // 检查权限
      if (!checkRoutePermission(to)) {
        // 无权限，跳转到403页面或首页
        next('/403');
        return;
      }
    }
    
    next();
  });
}

/**
 * 权限指令
 * 用于在模板中控制元素的显示/隐藏
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: any) {
    const { value } = binding;
    const userPermissions = getUserPermissions();
    
    if (value) {
      let hasAuth = false;
      
      if (Array.isArray(value)) {
        // 数组形式，检查是否有任一权限
        hasAuth = value.some(permission => hasPermission(userPermissions, permission));
      } else {
        // 字符串形式，检查单个权限
        hasAuth = hasPermission(userPermissions, value);
      }
      
      if (!hasAuth) {
        el.style.display = 'none';
      }
    }
  },
  
  updated(el: HTMLElement, binding: any) {
    const { value } = binding;
    const userPermissions = getUserPermissions();
    
    if (value) {
      let hasAuth = false;
      
      if (Array.isArray(value)) {
        hasAuth = value.some(permission => hasPermission(userPermissions, permission));
      } else {
        hasAuth = hasPermission(userPermissions, value);
      }
      
      el.style.display = hasAuth ? '' : 'none';
    }
  }
};

/**
 * 权限检查组合式函数
 * @returns 权限检查相关方法
 */
export function usePermission() {
  const userPermissions = getUserPermissions();
  
  const checkPermission = (permission: string | string[]): boolean => {
    if (Array.isArray(permission)) {
      return permission.some(p => hasPermission(userPermissions, p));
    }
    return hasPermission(userPermissions, permission);
  };
  
  const checkAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(p => hasPermission(userPermissions, p));
  };
  
  return {
    userPermissions,
    checkPermission,
    checkAllPermissions
  };
}
