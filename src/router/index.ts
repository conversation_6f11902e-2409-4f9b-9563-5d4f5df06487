import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import BasicLayout from '@/layouts/BasicLayout.vue'
import { setupPermissionGuard } from './guards/permission'

const routes: Array<RouteRecordRaw> = [
	{
		path: '/',
		component: BasicLayout,
		redirect: '/home',
		children: [
			{
				path: '/home',
				name: 'home',
				component: () => import('@/views/home.vue'),
				meta: { title: '荣誉申报' },
			},
			// 荣誉申报系统路由
			{
				path: '/honor/apply',
				name: 'HonorApply',
				component: () => import('@/views/honor/Apply.vue'),
				meta: { title: '荣誉申报' },
			},
			{
				path: '/honor/audit',
				name: 'HonorAudit',
				component: () => import('@/views/honor/Audit.vue'),
				meta: { title: '荣誉审核' },
			},
			// 季度亮晒系统路由
			{
				path: '/quarterly-showcase',
				name: 'QuarterlyShowcase',
				component: () => import('@/views/quarterly-showcase/Index.vue'),
				meta: { title: '季度亮晒' },
			},
			{
				path: '/quarterly-showcase/detail/:id',
				name: 'QuarterlyShowcaseDetail',
				component: () => import('@/views/quarterly-showcase/Detail.vue'),
				meta: { title: '详情分析' },
			},
			// 数据体检系统路由
			{
				path: '/health-check',
				name: 'HealthCheck',
				component: () => import('@/layouts/HealthCheckLayout.vue'),
				redirect: '/health-check/dashboard',
				meta: { title: '数据体检' },
				children: [
					{
						path: 'dashboard',
						name: 'HealthCheckDashboard',
						component: () => import('@/views/health-check/Dashboard.vue'),
						meta: { title: '数据体检仪表板' },
					},
					{
						path: 'rules',
						name: 'HealthCheckRules',
						component: () => import('@/views/health-check/Rules.vue'),
						meta: { title: '规则配置' },
					},
					{
						path: 'execution',
						name: 'HealthCheckExecution',
						component: () => import('@/views/health-check/Execution.vue'),
						meta: { title: '执行监控' },
					},
					{
						path: 'results',
						name: 'HealthCheckResults',
						component: () => import('@/views/health-check/Results.vue'),
						meta: { title: '结果管理' },
					},
					{
						path: 'scheduled',
						name: 'HealthCheckScheduled',
						component: () => import('@/views/health-check/ScheduledCheck.vue'),
						meta: { title: '定时自动体检' },
					},
					{
						path: 'statistics',
						name: 'HealthCheckStatistics',
						component: () => import('@/views/health-check/Statistics.vue'),
						meta: { title: '统计分析' },
					},
					{
						path: 'data-collection',
						name: 'HealthCheckDataCollection',
						component: () => import('@/views/health-check/DataCollection.vue'),
						meta: { title: '数据收集' },
					},
				],
			},
			// 智能审核系统路由
			{
				path: '/review',
				name: 'Review',
				redirect: '/review/application',
				meta: { title: '智能审核' },
				children: [
					{
						path: 'application',
						name: 'ReviewApplication',
						component: () => import('@/views/review/Application.vue'),
						meta: { title: '申报管理' },
					},
					{
						path: 'audit',
						name: 'ReviewAudit',
						component: () => import('@/views/review/Audit.vue'),
						meta: { title: '审核管理' },
					},
					{
						path: 'results',
						name: 'ReviewResults',
						component: () => import('@/views/review/Results.vue'),
						meta: { title: '结果管理' },
					},
					{
						path: 'appeal',
						name: 'ReviewAppeal',
						component: () => import('@/views/review/Appeal.vue'),
						meta: { title: '申诉管理' },
					},
					{
						path: 'workflow-config',
						name: 'ReviewWorkflowConfig',
						component: () => import('@/views/review/WorkflowConfig.vue'),
						meta: { title: '审核流程配置' },
					},
					{
						path: 'appeal-config',
						name: 'ReviewAppealConfig',
						component: () => import('@/views/review/AppealConfig.vue'),
						meta: { title: '申诉流程配置' },
					},
					{
						path: 'rescore',
						name: 'ReviewRescore',
						component: () => import('@/views/review/RescoreManagement.vue'),
						meta: { title: '再次评分管理' },
					},
					{
						path: 'score-detail/:id',
						name: 'ReviewScoreDetail',
						component: () => import('@/views/review/ScoreDetailReview.vue'),
						meta: { title: '评分详情审核' },
					},
					{
						path: 'cultivation-object-list',
						name: 'CultivationObjectList',
						component: () => import('@/views/review/CultivationObjectList.vue'),
						meta: { title: '培育对象列表管理' },
					},
					{
						path: 'review-process-config',
						name: 'ReviewProcessConfig',
						component: () => import('@/views/review/ReviewProcessConfig.vue'),
						meta: { title: '审核流程配置' },
					},
					{
						path: 'appeal-management',
						name: 'AppealManagement',
						component: () => import('@/views/review/AppealManagement.vue'),
						meta: { title: '申诉管理系统' },
					},
					{
						path: 'notification-template-config',
						name: 'NotificationTemplateConfig',
						component: () => import('@/views/review/NotificationTemplateConfig.vue'),
						meta: { title: '通知模板管理' },
					},
					{
						path: 'rescore-function',
						name: 'RescoreFunction',
						component: () => import('@/views/review/RescoreFunction.vue'),
						meta: { title: '再次评分功能' },
					},
				],
			},
			// 专班推荐系统路由
			{
				path: '/special-team',
				name: 'SpecialTeam',
				component: () => import('@/views/special-team/Index.vue'),
				meta: { title: '专班推荐' },
			},
			// 创建动态管理路由
			{
				path: '/dynamics',
				name: 'Dynamics',
				component: () => import('@/views/dynamics/Index.vue'),
				meta: { title: '创建动态管理' },
			},
			// 敏感词管理路由
			{
				path: '/sensitive-words/index',
				name: 'SensitiveWordsIndex',
				component: () => import('@/views/sensitive-words/Index.vue'),
				meta: { title: '敏感词管理首页' },
			},
			{
				path: '/sensitive-words/word-library',
				name: 'SensitiveWordsLibrary',
				component: () => import('@/views/sensitive-words/WordLibrary.vue'),
				meta: { title: '敏感词库管理' },
			},
			{
				path: '/sensitive-words/word-manage',
				name: 'SensitiveWordsManage',
				component: () => import('@/views/sensitive-words/WordManage.vue'),
				meta: { title: '敏感词管理' },
			},
			{
				path: '/sensitive-words/policy-manage',
				name: 'SensitiveWordsPolicyManage',
				component: () => import('@/views/sensitive-words/PolicyManage.vue'),
				meta: { title: '过滤策略管理' },
			},
			{
				path: '/sensitive-words/detection',
				name: 'SensitiveWordsDetection',
				component: () => import('@/views/sensitive-words/ContentDetection.vue'),
				meta: { title: '内容检测' },
			},
			{
				path: '/sensitive-words',
				redirect: '/sensitive-words/index'
			},
			// 案例推广路由
			{
				path: '/case-promotion',
				name: 'CasePromotion',
				component: () => import('@/views/case-promotion/Index.vue'),
				meta: { title: '案例推广' },
			},
			// 案例推广子路由（预留，后续任务中实现）
			{
				path: '/case-promotion/list',
				name: 'CasePromotionList',
				component: () => import('@/views/case-promotion/CaseList.vue'),
				meta: { title: '案例列表' },
			},
			{
				path: '/case-promotion/detail/:id',
				name: 'CasePromotionDetail',
				component: () => import('@/views/case-promotion/CaseDetail.vue'),
				meta: { title: '案例详情' },
			},
			{
				path: '/case-promotion/edit/:id?',
				name: 'CasePromotionEdit',
				component: () => import('@/views/case-promotion/CaseEdit.vue'),
				meta: { title: '案例编辑' },
			},
			{
				path: '/case-promotion/library',
				name: 'CasePromotionLibrary',
				component: () => import('@/views/case-promotion/CaseLibrary.vue'),
				meta: { title: '案例库管理' },
			},
			{
				path: '/case-promotion/category',
				name: 'CasePromotionCategory',
				component: () => import('@/views/case-promotion/CategoryManage.vue'),
				meta: { title: '分类管理' },
			},
			{
				path: '/case-promotion/tag',
				name: 'CasePromotionTag',
				component: () => import('@/views/case-promotion/TagManage.vue'),
				meta: { title: '标签管理' },
			},
			// 模范机关总览看板路由
			{
				path: '/model-agency-dashboard',
				name: 'ModelAgencyDashboard',
				component: () => import('@/views/model-agency-dashboard/Index.vue'),
				meta: {
					title: '模范机关总览看板',
					description: '展示模范机关创建工作的综合数据看板，包括地图展示、统计概览、党建指数、考核督查、预警监控等功能',
					keywords: '模范机关,数据看板,党建指数,考核督查,预警监控',
					requiresAuth: false,
					keepAlive: true,
					icon: 'dashboard'
				},
			},
			// 选育树推项目看板路由
			{
				path: '/project-dashboard',
				name: 'ProjectDashboard',
				component: () => import('@/views/project-dashboard/Index.vue'),
				meta: {
					title: '选育树推项目看板',
					description: '展示选育树推项目的全景概览、指标分析、组织洞察、督查系统等功能',
					keywords: '项目看板,指标分析,组织洞察,督查系统',
					requiresAuth: false,
					keepAlive: true,
					icon: 'project'
				},
			},
			// 流程引擎路由
			{
				path: '/workflow',
				name: 'Workflow',
				redirect: '/workflow/index',
				meta: { title: '流程引擎' },
				children: [
					{
						path: 'index',
						name: 'WorkflowIndex',
						component: () => import('@/views/workflow/Index.vue'),
						meta: { title: '流程引擎首页' },
					},
					{
						path: 'designer/:id?',
						name: 'ProcessDesigner',
						component: () => import('@/views/workflow/ProcessDesigner.vue'),
						meta: { title: '流程设计器' },
					},
					{
						path: 'template',
						name: 'TemplateManage',
						component: () => import('@/views/workflow/TemplateManage.vue'),
						meta: { title: '模板管理' },
					},
					{
						path: 'monitor',
						name: 'ProcessMonitor',
						component: () => import('@/views/workflow/ProcessMonitor.vue'),
						meta: { title: '流程监控' },
					},
					{
						path: 'task',
						name: 'TaskManage',
						component: () => import('@/views/workflow/TaskManage.vue'),
						meta: { title: '任务管理' },
					},
					{
						path: 'form/:id?',
						name: 'FormDesigner',
						component: () => import('@/views/workflow/FormDesigner.vue'),
						meta: { title: '表单设计器' },
					},
				],
			},

			// 消息中心路由
			{
				path: '/message-center',
				name: 'MessageCenter',
				redirect: '/message-center/index',
				meta: { title: '消息中心' },
				children: [
					{
						path: 'index',
						name: 'MessageCenterIndex',
						component: () => import('@/views/message-center/Index.vue'),
						meta: { title: '消息中心首页' },
					},
					{
						path: 'list',
						name: 'MessageList',
						component: () => import('@/views/message-center/MessageList.vue'),
						meta: { title: '消息列表' },
					},
					{
						path: 'push',
						name: 'PushManage',
						component: () => import('@/views/message-center/PushManage.vue'),
						meta: { title: '推送管理' },
					},
					{
						path: 'template',
						name: 'TemplateManage',
						component: () => import('@/views/message-center/TemplateManage.vue'),
						meta: { title: '模板管理' },
					},
					{
						path: 'analytics',
						name: 'MessageAnalytics',
						component: () => import('@/views/message-center/Analytics.vue'),
						meta: { title: '消息分析' },
					},
					{
						path: 'category',
						name: 'MessageCategoryManage',
						component: () => import('@/views/message-center/CategoryManage.vue'),
						meta: { title: '消息分类管理' },
					},
				],
			},

			// 活动管理路由
			{
				path: '/activity-management',
				name: 'ActivityManagement',
				redirect: '/activity-management/index',
				meta: { title: '活动管理' },
				children: [
					{
						path: 'index',
						name: 'ActivityManagementIndex',
						component: () => import('@/views/activity-management/Index.vue'),
						meta: { title: '活动管理首页' },
					},
					{
						path: 'list',
						name: 'ActivityList',
						component: () => import('@/views/activity-management/ActivityList.vue'),
						meta: { title: '活动列表' },
					},
					{
						path: 'detail/:id',
						name: 'ActivityDetail',
						component: () => import('@/views/activity-management/ActivityDetail.vue'),
						meta: { title: '活动详情' },
						props: true,
					},
					{
						path: 'participation/:id/:type',
						name: 'ActivityParticipation',
						component: () => import('@/views/activity-management/ActivityParticipation.vue'),
						meta: { title: '活动参与' },
						props: true,
					},
				],
			},

			// 审核确认系统路由
			{
				path: '/audit-confirmation',
				name: 'AuditConfirmation',
				component: () => import('@/layouts/AuditConfirmationLayout.vue'),
				redirect: '/audit-confirmation/index',
				meta: { title: '审核确认' },
				children: [
					{
						path: 'index',
						name: 'AuditConfirmationIndex',
						component: () => import('@/views/audit-confirmation/Index.vue'),
						meta: { title: '审核确认首页' },
					},
					{
						path: 'project-audit',
						name: 'ProjectAudit',
						component: () => import('@/views/audit-confirmation/ProjectAudit.vue'),
						meta: { title: '项目审核管理' },
					},
					{
						path: 'score-management',
						name: 'ScoreManagement',
						component: () => import('@/views/audit-confirmation/ScoreManagement.vue'),
						meta: { title: '评分结果管理' },
					},
					{
						path: 'audit-history',
						name: 'AuditHistory',
						component: () => import('@/views/audit-confirmation/AuditHistory.vue'),
						meta: { title: '审核历史' },
					},
					{
						path: 'cultivation-object/:id',
						name: 'CultivationObjectDetail',
						component: () => import('@/views/audit-confirmation/CultivationObjectDetail.vue'),
						meta: { title: '培育对象详情' },
					},
					{
						path: 'cultivation-object-list',
						name: 'AuditCultivationObjectList',
						component: () => import('@/views/audit-confirmation/CultivationObjectList.vue'),
						meta: { title: '培育对象列表管理' },
					},
					{
						path: 'review-process-config',
						name: 'AuditReviewProcessConfig',
						component: () => import('@/views/audit-confirmation/ReviewProcessConfig.vue'),
						meta: { title: '审核流程配置' },
					},
					{
						path: 'appeal-management',
						name: 'AuditAppealManagement',
						component: () => import('@/views/audit-confirmation/AppealManagement.vue'),
						meta: { title: '申诉管理系统' },
					},
					{
						path: 'notification-template-config',
						name: 'AuditNotificationTemplateConfig',
						component: () => import('@/views/audit-confirmation/NotificationTemplateConfig.vue'),
						meta: { title: '通知模板管理' },
					},
					{
						path: 'rescore-function',
						name: 'AuditRescoreFunction',
						component: () => import('@/views/audit-confirmation/RescoreFunction.vue'),
						meta: { title: '再次评分功能' },
					},
					{
						path: 'workflow-config',
						name: 'AuditWorkflowConfig',
						component: () => import('@/views/audit-confirmation/WorkflowConfig.vue'),
						meta: { title: '审核工作流配置' },
					},
					{
						path: 'appeal-config',
						name: 'AuditAppealConfig',
						component: () => import('@/views/audit-confirmation/AppealConfig.vue'),
						meta: { title: '申诉流程配置' },
					},
					{
						path: 'rescore-management',
						name: 'AuditRescoreManagement',
						component: () => import('@/views/audit-confirmation/RescoreManagement.vue'),
						meta: { title: '再次评分管理' },
					},
					{
						path: 'score-detail/:id',
						name: 'AuditScoreDetail',
						component: () => import('@/views/audit-confirmation/ScoreDetailReview.vue'),
						meta: { title: '评分详情审核' },
					},
				],
			},

			// 渝理面对面系统路由
			{
				path: '/yulifaceto-face',
				name: 'YulifacetoFace',
				redirect: '/yulifaceto-face/index',
				meta: { title: '渝理面对面' },
				children: [
					{
						path: 'index',
						name: 'YulifacetoFaceIndex',
						component: () => import('@/views/yulifaceto-face/Index.vue'),
						meta: { title: '渝理面对面首页' },
					},
					{
						path: 'document-management',
						name: 'DocumentManagement',
						component: () => import('@/views/yulifaceto-face/DocumentManagement.vue'),
						meta: { title: '纪实管理' },
					},
					{
						path: 'template-management',
						name: 'TemplateManagement',
						component: () => import('@/views/yulifaceto-face/TemplateManagement.vue'),
						meta: { title: '模板管理' },
					},
					{
						path: 'data-display',
						name: 'DataDisplay',
						component: () => import('@/views/yulifaceto-face/DataDisplay.vue'),
						meta: { title: '数据展示' },
					},
					{
						path: 'statistics-analysis',
						name: 'StatisticsAnalysis',
						component: () => import('@/views/yulifaceto-face/StatisticsAnalysis.vue'),
						meta: { title: '统计分析' },
					},
				],
			},

			// 培育过程预警系统路由
			{
				path: '/cultivation-warning',
				name: 'CultivationWarning',
				redirect: '/cultivation-warning/index',
				meta: {
					title: '培育过程预警',
					description: '智能化培育过程监控与预警管理平台，提供预警配置、数据采集、督办管理、报告生成等功能',
					keywords: '培育过程,预警监控,督办管理,数据采集,报告生成',
					requiresAuth: false,
					keepAlive: true,
					icon: 'alert'
				},
				children: [
					{
						path: 'index',
						name: 'CultivationWarningIndex',
						component: () => import('@/views/cultivation-warning/IndexSimple.vue'),
						meta: { title: '培育过程预警首页' },
					},
					{
						path: 'config',
						name: 'CultivationWarningConfig',
						component: () => import('@/views/cultivation-warning/ConfigManagementSimple.vue'),
						meta: { title: '预警配置管理' },
					},
					{
						path: 'data-collection',
						name: 'CultivationWarningDataCollection',
						component: () => import('@/views/cultivation-warning/DataCollectionSimple.vue'),
						meta: { title: '数据采集与处理' },
					},
					{
						path: 'alert-trigger',
						name: 'CultivationWarningAlertTrigger',
						component: () => import('@/views/cultivation-warning/AlertTriggerSimple.vue'),
						meta: { title: '预警触发与通知' },
					},
					{
						path: 'supervision',
						name: 'CultivationWarningSupervision',
						component: () => import('@/views/cultivation-warning/SupervisionManagementSimple.vue'),
						meta: { title: '督办管理' },
					},
					{
						path: 'report',
						name: 'CultivationWarningReport',
						component: () => import('@/views/cultivation-warning/ReportTemplateSimple.vue'),
						meta: { title: '预警报告与模板' },
					},
				],
			},

			// 案例征集系统路由
			{
				path: '/case-collection',
				name: 'CaseCollection',
				redirect: '/case-collection/activities',
				meta: {
					title: '案例征集系统',
					description: '案例征集活动管理、在线提交、预审评价的一体化平台',
					keywords: '案例征集,活动管理,在线提交,预审评价',
					requiresAuth: false,
					keepAlive: true,
					icon: 'collection'
				},
				children: [
					// 活动管理
					{
						path: 'activities',
						name: 'CaseCollectionActivities',
						component: () => import('@/views/case-collection/activities/Index.vue'),
						meta: { title: '活动管理' },
					},
					{
						path: 'activities/list',
						name: 'CaseCollectionActivitiesList',
						component: () => import('@/views/case-collection/activities/ActivityList.vue'),
						meta: { title: '活动列表' },
					},
					{
						path: 'activities/create',
						name: 'CaseCollectionActivitiesCreate',
						component: () => import('@/views/case-collection/activities/ActivityForm.vue'),
						meta: { title: '创建活动' },
					},
					{
						path: 'activities/:id',
						name: 'CaseCollectionActivitiesDetail',
						component: () => import('@/views/case-collection/activities/ActivityDetail.vue'),
						meta: { title: '活动详情' },
						props: true,
					},
					{
						path: 'activities/:id/edit',
						name: 'CaseCollectionActivitiesEdit',
						component: () => import('@/views/case-collection/activities/ActivityForm.vue'),
						meta: { title: '编辑活动' },
						props: true,
					},
					// 案例提交
					{
						path: 'submit',
						name: 'CaseCollectionSubmit',
						component: () => import('@/views/case-collection/submit/Index.vue'),
						meta: { title: '案例提交' },
					},
					{
						path: 'submit/form',
						name: 'CaseCollectionSubmitForm',
						component: () => import('@/views/case-collection/submit/SubmitForm.vue'),
						meta: { title: '提交案例' },
					},
					// 提交管理
					{
						path: 'submissions',
						name: 'CaseCollectionSubmissions',
						component: () => import('@/views/case-collection/submissions/Index.vue'),
						meta: { title: '我的提交' },
					},
					{
						path: 'submissions/:id',
						name: 'CaseCollectionSubmissionsDetail',
						component: () => import('@/views/case-collection/submissions/Detail.vue'),
						meta: { title: '提交详情' },
						props: true,
					},
					// 案例预审
					{
						path: 'review',
						name: 'CaseCollectionReview',
						component: () => import('@/views/case-collection/review/Index.vue'),
						meta: { title: '案例预审' },
					},
					{
						path: 'review/list',
						name: 'CaseCollectionReviewList',
						component: () => import('@/views/case-collection/review/ReviewList.vue'),
						meta: { title: '审核列表' },
					},
					{
						path: 'review/detail/:id',
						name: 'CaseCollectionReviewDetail',
						component: () => import('@/views/case-collection/review/ReviewDetail.vue'),
						meta: { title: '审核详情' },
						props: true,
					},
					{
						path: 'review/batch',
						name: 'CaseCollectionReviewBatch',
						component: () => import('@/views/case-collection/review/BatchReview.vue'),
						meta: { title: '批量审核' },
					},
					{
						path: 'review/history',
						name: 'CaseCollectionReviewHistory',
						component: () => import('@/views/case-collection/review/ReviewHistory.vue'),
						meta: { title: '审核历史' },
					},
					{
						path: 'review/settings',
						name: 'CaseCollectionReviewSettings',
						component: () => import('@/views/case-collection/review/ReviewSettings.vue'),
						meta: { title: '审核设置' },
					},
					// 分类管理
					{
						path: 'categories',
						name: 'CaseCollectionCategories',
						component: () => import('@/views/case-collection/categories/Index.vue'),
						meta: { title: '分类管理' },
					},
					// 数据统计
					{
						path: 'dashboard',
						name: 'CaseCollectionDashboard',
						component: () => import('@/views/case-collection/dashboard/Index.vue'),
						meta: { title: '数据统计' },
					},
					// API测试页面（开发环境）
					// {
					// 	path: 'test/api',
					// 	name: 'CaseCollectionApiTest',
					// 	component: () => import('@/views/case-collection/test/ApiTest.vue'),
					// 	meta: { title: 'API测试' },
					// },
				],
			},

			// 指标规则管理系统路由
			{
				path: '/indicator-rules',
				name: 'IndicatorRules',
				redirect: '/indicator-rules/index',
				meta: {
					title: '指标规则管理',
					description: '指标库管理系统，支持指标的全生命周期管理、权重设置、演算分析和模板管理',
					keywords: '指标管理,权重设置,演算分析,模板管理',
					requiresAuth: false,
					keepAlive: true,
					icon: 'setting'
				},
				children: [
					{
						path: 'index',
						name: 'IndicatorRulesIndex',
						component: () => import('@/views/indicator-rules/Index.vue'),
						meta: { title: '指标规则管理首页' },
					},
					{
						path: 'indicators',
						name: 'IndicatorManagement',
						component: () => import('@/views/indicator-rules/IndicatorManagement.vue'),
						meta: { title: '指标管理' },
					},
					{
						path: 'weights',
						name: 'WeightManagement',
						component: () => import('@/views/indicator-rules/WeightManagement.vue'),
						meta: { title: '权重设置' },
					},
					{
						path: 'calculation',
						name: 'CalculationAnalysis',
						component: () => import('@/views/indicator-rules/CalculationAnalysis.vue'),
						meta: { title: '演算分析' },
					},
					{
						path: 'templates',
						name: 'TemplateManagement',
						component: () => import('@/views/indicator-rules/TemplateManagement.vue'),
						meta: { title: '模板管理' },
					},
				],
			},

			// 模块集成管理路由 - 新增
			{
				path: '/module-integration',
				name: 'ModuleIntegration',
				redirect: '/module-integration/index',
				meta: {
					title: '模块集成管理',
					description: '协调指标规则、数据体检、审核确认三个模块的集成和数据同步',
					keywords: '模块集成,数据同步,流程管理,系统监控',
					requiresAuth: false,
					keepAlive: true,
					icon: 'integration'
				},
				children: [
					{
						path: 'index',
						name: 'ModuleIntegrationIndex',
						component: () => import('@/views/module-integration/Index.vue'),
						meta: { title: '集成管理首页' },
					},
					{
						path: 'process-monitor',
						name: 'ProcessMonitor',
						component: () => import('@/views/module-integration/ProcessMonitor.vue'),
						meta: { title: '流程监控' },
					},
					{
						path: 'data-sync',
						name: 'DataSync',
						component: () => import('@/views/module-integration/DataSync.vue'),
						meta: { title: '数据同步' },
					},
					{
						path: 'health-monitor',
						name: 'HealthMonitor',
						component: () => import('@/views/module-integration/HealthMonitor.vue'),
						meta: { title: '系统健康监控' },
					},
					{
						path: 'diagnostic',
						name: 'SystemDiagnostic',
						component: () => import('@/views/module-integration/SystemDiagnostic.vue'),
						meta: { title: '系统诊断' },
					},
				],
			},

			// 路由测试工具（开发环境）
			{
				path: '/route-test',
				name: 'RouteTest',
				component: () => import('@/views/RouteTest.vue'),
				meta: { title: '路由测试工具' },
			},
		],
	},
]

const router = createRouter({
	history: createWebHashHistory(),
	routes,
})

// 设置权限守卫
// setupPermissionGuard(router)

export default router