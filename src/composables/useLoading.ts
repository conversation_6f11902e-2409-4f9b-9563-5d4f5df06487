/**
 * 加载状态管理组合式函数
 * 提供统一的加载状态管理和用户反馈
 * 遵循项目编码规范和设计模式
 */

import { ref, computed, type Ref } from 'vue'
import { feedbackSystem } from '@/utils/feedback-system'

export interface LoadingState {
  loading: boolean
  error: string | null
  success: boolean
  data: any
}

export interface LoadingOptions {
  showLoading?: boolean
  showSuccess?: boolean
  showError?: boolean
  successMessage?: string
  errorMessage?: string
  loadingMessage?: string
  resetOnStart?: boolean
}

/**
 * 基础加载状态管理
 * @param initialLoading 初始加载状态
 * @returns 加载状态管理对象
 */
export function useLoading(initialLoading: boolean = false) {
  const loading = ref(initialLoading)
  const error = ref<string | null>(null)
  const success = ref(false)
  const data = ref<any>(null)

  // 计算属性
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const isSuccess = computed(() => success.value)
  const isEmpty = computed(() => !loading.value && !error.value && !data.value)

  /**
   * 设置加载状态
   * @param value 加载状态值
   */
  const setLoading = (value: boolean) => {
    loading.value = value
    if (value) {
      error.value = null
      success.value = false
    }
  }

  /**
   * 设置错误状态
   * @param errorMessage 错误消息
   */
  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
    loading.value = false
    success.value = false
  }

  /**
   * 设置成功状态
   * @param result 成功结果数据
   */
  const setSuccess = (result?: any) => {
    success.value = true
    loading.value = false
    error.value = null
    if (result !== undefined) {
      data.value = result
    }
  }

  /**
   * 设置数据
   * @param result 数据
   */
  const setData = (result: any) => {
    data.value = result
  }

  /**
   * 重置所有状态
   */
  const reset = () => {
    loading.value = false
    error.value = null
    success.value = false
    data.value = null
  }

  /**
   * 执行异步操作
   * @param asyncFn 异步函数
   * @param options 配置选项
   * @returns 执行结果
   */
  const execute = async <T>(
    asyncFn: () => Promise<T>,
    options: LoadingOptions = {}
  ): Promise<T | null> => {
    const {
      showLoading = false,
      showSuccess = false,
      showError = true,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      loadingMessage = '处理中...',
      resetOnStart = true
    } = options

    try {
      if (resetOnStart) {
        reset()
      }

      setLoading(true)

      let loadingInstance: (() => void) | null = null
      if (showLoading) {
        loadingInstance = feedbackSystem.loading(loadingMessage)
      }

      const result = await asyncFn()

      if (loadingInstance) {
        loadingInstance()
      }

      setSuccess(result)

      if (showSuccess) {
        feedbackSystem.success(successMessage)
      }

      return result
    } catch (err: any) {
      const errorMsg = err?.message || err?.toString() || errorMessage
      setError(errorMsg)

      if (showError) {
        feedbackSystem.error(errorMsg)
      }

      return null
    }
  }

  return {
    // 状态
    loading: readonly(loading),
    error: readonly(error),
    success: readonly(success),
    data: readonly(data),
    
    // 计算属性
    isLoading,
    hasError,
    isSuccess,
    isEmpty,
    
    // 方法
    setLoading,
    setError,
    setSuccess,
    setData,
    reset,
    execute
  }
}

/**
 * 多个加载状态管理
 * @param keys 状态键名数组
 * @returns 多个加载状态管理对象
 */
export function useMultipleLoading<T extends string>(keys: T[]) {
  const loadingStates = {} as Record<T, ReturnType<typeof useLoading>>
  
  keys.forEach(key => {
    loadingStates[key] = useLoading()
  })

  // 全局加载状态
  const globalLoading = computed(() => {
    return Object.values(loadingStates).some(state => state.isLoading.value)
  })

  // 全局错误状态
  const globalError = computed(() => {
    const errors = Object.values(loadingStates)
      .map(state => state.error.value)
      .filter(Boolean)
    return errors.length > 0 ? errors.join('; ') : null
  })

  // 重置所有状态
  const resetAll = () => {
    Object.values(loadingStates).forEach(state => state.reset())
  }

  return {
    ...loadingStates,
    globalLoading,
    globalError,
    resetAll
  }
}

/**
 * 分页加载状态管理
 * @param initialPage 初始页码
 * @param initialPageSize 初始页大小
 * @returns 分页加载状态管理对象
 */
export function usePaginationLoading(initialPage: number = 1, initialPageSize: number = 10) {
  const { execute, ...loadingState } = useLoading()
  
  const page = ref(initialPage)
  const pageSize = ref(initialPageSize)
  const total = ref(0)
  const list = ref<any[]>([])

  // 计算属性
  const totalPages = computed(() => Math.ceil(total.value / pageSize.value))
  const hasMore = computed(() => page.value < totalPages.value)
  const isEmpty = computed(() => !loadingState.isLoading.value && list.value.length === 0)

  /**
   * 加载数据
   * @param loadFn 加载函数
   * @param options 配置选项
   * @param append 是否追加数据
   */
  const load = async (
    loadFn: (page: number, pageSize: number) => Promise<{ list: any[]; total: number }>,
    options: LoadingOptions = {},
    append: boolean = false
  ) => {
    const result = await execute(
      () => loadFn(page.value, pageSize.value),
      options
    )

    if (result) {
      if (append) {
        list.value.push(...result.list)
      } else {
        list.value = result.list
      }
      total.value = result.total
    }

    return result
  }

  /**
   * 刷新当前页
   * @param loadFn 加载函数
   * @param options 配置选项
   */
  const refresh = (
    loadFn: (page: number, pageSize: number) => Promise<{ list: any[]; total: number }>,
    options: LoadingOptions = {}
  ) => {
    return load(loadFn, options, false)
  }

  /**
   * 加载下一页
   * @param loadFn 加载函数
   * @param options 配置选项
   */
  const loadMore = (
    loadFn: (page: number, pageSize: number) => Promise<{ list: any[]; total: number }>,
    options: LoadingOptions = {}
  ) => {
    if (hasMore.value) {
      page.value++
      return load(loadFn, options, true)
    }
    return Promise.resolve(null)
  }

  /**
   * 跳转到指定页
   * @param targetPage 目标页码
   * @param loadFn 加载函数
   * @param options 配置选项
   */
  const goToPage = (
    targetPage: number,
    loadFn: (page: number, pageSize: number) => Promise<{ list: any[]; total: number }>,
    options: LoadingOptions = {}
  ) => {
    if (targetPage >= 1 && targetPage <= totalPages.value) {
      page.value = targetPage
      return load(loadFn, options, false)
    }
    return Promise.resolve(null)
  }

  /**
   * 重置分页状态
   */
  const resetPagination = () => {
    page.value = initialPage
    pageSize.value = initialPageSize
    total.value = 0
    list.value = []
    loadingState.reset()
  }

  return {
    // 继承基础加载状态
    ...loadingState,
    
    // 分页状态
    page: readonly(page),
    pageSize: readonly(pageSize),
    total: readonly(total),
    list: readonly(list),
    
    // 计算属性
    totalPages,
    hasMore,
    isEmpty,
    
    // 方法
    load,
    refresh,
    loadMore,
    goToPage,
    resetPagination
  }
}

// 工具函数：只读包装
function readonly<T>(ref: Ref<T>): Readonly<Ref<T>> {
  return ref as Readonly<Ref<T>>
}
