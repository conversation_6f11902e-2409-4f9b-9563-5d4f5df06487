package com.goodsogood.ows.controller;

import com.alibaba.fastjson.JSON;
import com.goodsogood.ows.model.vo.DataInspectionRuleVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据体检规则配置Controller测试类
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@SpringBootTest
@AutoConfigureTestMvc
public class DataInspectionRuleConfigControllerTest {

    @Resource
    private MockMvc mockMvc;

    @Test
    public void testGetRuleList() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/health-check/rules")
                .param("pageNum", "1")
                .param("pageSize", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("规则列表查询结果: " + responseContent);
    }

    @Test
    public void testCreateRule() throws Exception {
        DataInspectionRuleVO.RuleCreateVO createVO = new DataInspectionRuleVO.RuleCreateVO();
        createVO.setRuleName("测试规则");
        createVO.setRuleType(1);
        createVO.setRuleContent("{\"requiredFields\": [\"testField\"]}");
        createVO.setIsEnabled(true);
        createVO.setPriority(1);
        createVO.setDescription("这是一个测试规则");

        MvcResult result = mockMvc.perform(post("/api/health-check/rules")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(createVO)))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("创建规则结果: " + responseContent);
    }

    @Test
    public void testGetRuleTypeOptions() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/health-check/rules/type-options")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("规则类型选项: " + responseContent);
    }

    @Test
    public void testValidateRuleContent() throws Exception {
        String validJson = "{\"requiredFields\": [\"field1\", \"field2\"]}";

        MvcResult result = mockMvc.perform(post("/api/health-check/rules/validate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(validJson))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("规则内容验证结果: " + responseContent);
    }

    @Test
    public void testGetRulesByType() throws Exception {
        MvcResult result = mockMvc.perform(get("/api/health-check/rules/type/1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();

        String responseContent = result.getResponse().getContentAsString();
        System.out.println("按类型查询规则结果: " + responseContent);
    }
}