package com.goodsogood.ows.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.model.vo.DataInspectionResultVO.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 数据体检结果管理控制器测试类
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@SpringBootTest
@AutoConfigureTestMvc
@Transactional
public class DataInspectionResultsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 测试获取异常列表接口
     */
    @Test
    public void testGetExceptionList() throws Exception {
        mockMvc.perform(get("/api/data-inspection/health-check/exceptions")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .param("exceptionType", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.total").exists())
                .andExpect(jsonPath("$.data.data").isArray());
    }

    /**
     * 测试获取异常统计数据接口
     */
    @Test
    public void testGetExceptionStatistics() throws Exception {
        mockMvc.perform(get("/api/data-inspection/health-check/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.totalExceptions").exists())
                .andExpect(jsonPath("$.data.checkTypeStats").isArray())
                .andExpect(jsonPath("$.data.exceptionTrend").isArray());
    }

    /**
     * 测试批量整改异常接口
     */
    @Test
    public void testBatchRemediateExceptions() throws Exception {
        // 构建测试数据
        RemediationPlanVO remediationPlan = RemediationPlanVO.builder()
                .description("批量整改测试")
                .estimatedTime("2025-01-20")
                .assignee("测试人员")
                .priority(2)
                .build();

        BatchRemediationVO batchRemediation = BatchRemediationVO.builder()
                .exceptionIds(Arrays.asList(1L, 2L, 3L))
                .remediationPlan(remediationPlan)
                .build();

        String requestJson = objectMapper.writeValueAsString(batchRemediation);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/batch-remediate")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.successCount").exists())
                .andExpect(jsonPath("$.data.batchId").exists());
    }

    /**
     * 测试单项整改异常接口
     */
    @Test
    public void testRemediateException() throws Exception {
        // 构建测试数据
        RemediationDataVO remediationData = RemediationDataVO.builder()
                .description("单项整改测试")
                .solution("解决方案测试")
                .estimatedTime("2025-01-20")
                .attachments(Arrays.asList("attachment1.pdf", "attachment2.doc"))
                .build();

        String requestJson = objectMapper.writeValueAsString(remediationData);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/1/remediate")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.success").exists())
                .andExpect(jsonPath("$.data.message").exists());
    }

    /**
     * 测试提交整改结果接口
     */
    @Test
    public void testSubmitRemediationResult() throws Exception {
        // 构建测试数据
        RemediationResultVO result = RemediationResultVO.builder()
                .status(3) // 已修复
                .description("整改结果测试")
                .actualTime("2小时")
                .attachments(Arrays.asList("result1.pdf"))
                .nextSteps("后续监控")
                .build();

        String requestJson = objectMapper.writeValueAsString(result);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/1/submit-remediation")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.success").exists())
                .andExpect(jsonPath("$.data.message").exists());
    }

    /**
     * 测试批量复检异常接口
     */
    @Test
    public void testBatchRecheckExceptions() throws Exception {
        // 构建测试数据
        RecheckConfigVO recheckConfig = RecheckConfigVO.builder()
                .checkTypes(Arrays.asList(1, 2, 3))
                .scope("全面检查")
                .priority(3)
                .build();

        BatchRecheckVO batchRecheck = BatchRecheckVO.builder()
                .exceptionIds(Arrays.asList(1L, 2L))
                .recheckConfig(recheckConfig)
                .build();

        String requestJson = objectMapper.writeValueAsString(batchRecheck);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/batch-recheck")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.taskId").exists())
                .andExpected(jsonPath("$.data.successCount").exists());
    }

    /**
     * 测试单项复检异常接口
     */
    @Test
    public void testRecheckException() throws Exception {
        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/1/recheck")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.taskId").exists())
                .andExpected(jsonPath("$.data.estimatedDuration").exists());
    }

    /**
     * 测试导出异常结果接口
     */
    @Test
    public void testExportExceptionResults() throws Exception {
        // 构建测试数据
        ExportFiltersVO filters = ExportFiltersVO.builder()
                .exceptionTypes(Arrays.asList(1, 2))
                .severityLevels(Arrays.asList(2, 3))
                .statusList(Arrays.asList(1, 2, 3))
                .includeFixedItems(true)
                .build();

        ExportConfigVO exportConfig = ExportConfigVO.builder()
                .includeCharts(true)
                .includeSummary(true)
                .template("standard")
                .build();

        ExportParamsVO exportParams = ExportParamsVO.builder()
                .format("excel")
                .filters(filters)
                .exportConfig(exportConfig)
                .build();

        String requestJson = objectMapper.writeValueAsString(exportParams);

        mockMvc.perform(post("/api/data-inspection/health-check/results/export")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.downloadUrl").exists())
                .andExpected(jsonPath("$.data.filename").exists());
    }

    /**
     * 测试获取影响单位排行接口
     */
    @Test
    public void testGetAffectedUnitsRanking() throws Exception {
        mockMvc.perform(get("/api/data-inspection/health-check/exceptions/affected-units-ranking")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").exists())
                .andExpected(jsonPath("$.data.units").isArray());
    }

    /**
     * 测试获取异常趋势分析接口
     */
    @Test
    public void testGetExceptionTrend() throws Exception {
        mockMvc.perform(get("/api/data-inspection/health-check/exceptions/trend")
                        .param("granularity", "daily")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isOk())
                .andExpected(jsonPath("$.code").value(200))
                .andExpected(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.trendData").isArray())
                .andExpected(jsonPath("$.data.insights").exists());
    }

    /**
     * 测试参数校验 - 批量整改异常空参数
     */
    @Test
    public void testBatchRemediateExceptionsWithEmptyIds() throws Exception {
        // 构建空ID列表的测试数据
        RemediationPlanVO remediationPlan = RemediationPlanVO.builder()
                .description("测试")
                .estimatedTime("2025-01-20")
                .build();

        BatchRemediationVO batchRemediation = BatchRemediationVO.builder()
                .exceptionIds(Arrays.asList()) // 空列表
                .remediationPlan(remediationPlan)
                .build();

        String requestJson = objectMapper.writeValueAsString(batchRemediation);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/batch-remediate")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isBadRequest())
                .andExpected(jsonPath("$.code").value(9904)); // 参数校验失败
    }

    /**
     * 测试参数校验 - 单项整改异常空描述
     */
    @Test
    public void testRemediateExceptionWithEmptyDescription() throws Exception {
        // 构建空描述的测试数据
        RemediationDataVO remediationData = RemediationDataVO.builder()
                .description("") // 空描述
                .solution("解决方案")
                .estimatedTime("2025-01-20")
                .build();

        String requestJson = objectMapper.writeValueAsString(remediationData);

        mockMvc.perform(post("/api/data-inspection/health-check/exceptions/1/remediate")
                        .content(requestJson)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpected(status().isBadRequest())
                .andExpected(jsonPath("$.code").value(9904)); // 参数校验失败
    }
}