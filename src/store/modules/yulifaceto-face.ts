// 渝理面对面系统状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  DocumentRecord,
  DocumentTemplate,
  StatisticsData,
  DocumentSearchParams,
  TemplateSearchParams
} from '@/types/yulifaceto-face'
import { yulifacetoFaceApi } from '@/api/yulifaceto-face'

export const useYulifacetoFaceStore = defineStore('yulifacetoFace', () => {
  // 状态数据
  const loading = ref(false)
  const statistics = ref<StatisticsData | null>(null)
  const documentList = ref<DocumentRecord[]>([])
  const templateList = ref<DocumentTemplate[]>([])
  
  // 当前选中的数据
  const currentDocument = ref<DocumentRecord | null>(null)
  const currentTemplate = ref<DocumentTemplate | null>(null)

  // 分页信息
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const totalDocuments = computed(() => {
    return documentList.value.length
  })

  const publishedDocuments = computed(() => {
    return documentList.value.filter(item => item.status === 2).length
  })

  const draftDocuments = computed(() => {
    return documentList.value.filter(item => item.status === 1).length
  })

  const totalSpeakers = computed(() => {
    const speakers = new Set(documentList.value.map(item => item.speaker))
    return speakers.size
  })

  const totalTemplates = computed(() => {
    return templateList.value.length
  })

  const sharedTemplates = computed(() => {
    return templateList.value.filter(item => item.isShared).length
  })

  const completionRate = computed(() => {
    if (totalDocuments.value === 0) return 0
    return Math.round((publishedDocuments.value / totalDocuments.value) * 100 * 100) / 100
  })

  const totalViews = computed(() => {
    return documentList.value.reduce((sum, item) => sum + item.viewCount, 0)
  })

  const totalDownloads = computed(() => {
    return documentList.value.reduce((sum, item) => sum + item.downloadCount, 0)
  })

  // Actions
  const fetchStatistics = async () => {
    try {
      loading.value = true
      statistics.value = await yulifacetoFaceApi.getStatistics()
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchDocumentList = async (params?: DocumentSearchParams) => {
    try {
      loading.value = true
      const result = await yulifacetoFaceApi.getDocumentList(params)
      documentList.value = result.list
      pagination.value.total = result.total
    } catch (error) {
      console.error('获取纪实列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchTemplateList = async (params?: TemplateSearchParams) => {
    try {
      loading.value = true
      const result = await yulifacetoFaceApi.getTemplateList(params)
      templateList.value = result.list
    } catch (error) {
      console.error('获取模板列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const createDocument = async (data: Partial<DocumentRecord>) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.createDocument(data)
      if (success) {
        // 添加到本地列表
        const newDocument: DocumentRecord = {
          id: Date.now(),
          title: data.title || '',
          speaker: data.speaker || '',
          content: data.content || '',
          lectureTime: data.lectureTime || '',
          location: data.location || '',
          contactInfo: data.contactInfo,
          summary: data.summary,
          coverImage: data.coverImage,
          status: data.status || 1,
          enableDownload: data.enableDownload !== undefined ? data.enableDownload : true,
          downloadFormat: data.downloadFormat || 'doc',
          publishTime: data.status === 2 ? new Date().toISOString() : undefined,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
          creator: data.creator || '当前用户',
          templateId: data.templateId,
          viewCount: 0,
          downloadCount: 0
        }
        documentList.value.unshift(newDocument)
      }
      return success
    } catch (error) {
      console.error('创建纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const updateDocument = async (id: number, data: Partial<DocumentRecord>) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.updateDocument(id, data)
      if (success) {
        // 更新本地数据
        const index = documentList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          documentList.value[index] = {
            ...documentList.value[index],
            ...data,
            updateTime: new Date().toISOString()
          }
        }
      }
      return success
    } catch (error) {
      console.error('更新纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const deleteDocument = async (id: number) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.deleteDocument(id)
      if (success) {
        // 从本地列表移除
        const index = documentList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          documentList.value.splice(index, 1)
        }
      }
      return success
    } catch (error) {
      console.error('删除纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const publishDocument = async (id: number) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.publishDocument(id)
      if (success) {
        // 更新本地状态
        const index = documentList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          documentList.value[index].status = 2
          documentList.value[index].publishTime = new Date().toISOString()
          documentList.value[index].updateTime = new Date().toISOString()
        }
      }
      return success
    } catch (error) {
      console.error('发布纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const createTemplate = async (data: Partial<DocumentTemplate>) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.createTemplate(data)
      if (success) {
        // 添加到本地列表
        const newTemplate: DocumentTemplate = {
          id: Date.now(),
          name: data.name || '',
          description: data.description || '',
          layout: data.layout || {
            id: 1,
            name: '默认布局',
            structure: 1,
            textPosition: [],
            imagePosition: [],
            videoPosition: []
          },
          style: data.style || {
            id: 1,
            name: '默认样式',
            colors: {
              primary: '#1890ff',
              secondary: '#f0f0f0',
              background: '#ffffff',
              text: '#333333',
              accent: '#52c41a'
            },
            fonts: {
              titleFont: 'Microsoft YaHei',
              contentFont: 'Microsoft YaHei',
              titleSize: 24,
              contentSize: 14,
              lineHeight: 1.6
            },
            spacing: {
              margin: 20,
              padding: 16,
              lineSpacing: 1.6,
              paragraphSpacing: 12
            },
            borders: {
              width: 1,
              style: 'solid',
              color: '#d9d9d9',
              radius: 4
            }
          },
          placeholders: data.placeholders || [],
          isShared: data.isShared || false,
          creator: data.creator || '当前用户',
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString(),
          usageCount: 0,
          category: data.category || 4
        }
        templateList.value.unshift(newTemplate)
      }
      return success
    } catch (error) {
      console.error('创建模板失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const updateTemplate = async (id: number, data: Partial<DocumentTemplate>) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.updateTemplate(id, data)
      if (success) {
        // 更新本地数据
        const index = templateList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          templateList.value[index] = {
            ...templateList.value[index],
            ...data,
            updateTime: new Date().toISOString()
          }
        }
      }
      return success
    } catch (error) {
      console.error('更新模板失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const deleteTemplate = async (id: number) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.deleteTemplate(id)
      if (success) {
        // 从本地列表移除
        const index = templateList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          templateList.value.splice(index, 1)
        }
      }
      return success
    } catch (error) {
      console.error('删除模板失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const applyTemplate = async (templateId: number, documentData: Partial<DocumentRecord>) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.applyTemplate(templateId, documentData)
      if (success) {
        // 增加模板使用次数
        const index = templateList.value.findIndex(item => item.id === templateId)
        if (index !== -1) {
          templateList.value[index].usageCount += 1
        }
      }
      return success
    } catch (error) {
      console.error('应用模板失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const restoreDocument = async (id: number) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.restoreDocument(id)
      if (success) {
        // 更新本地状态为草稿
        const index = documentList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          documentList.value[index].status = 1
          documentList.value[index].updateTime = new Date().toISOString()
        }
      }
      return success
    } catch (error) {
      console.error('恢复纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const downloadDocument = async (id: number) => {
    try {
      loading.value = true
      const success = await yulifacetoFaceApi.downloadDocument(id)
      if (success) {
        // 增加下载次数
        const index = documentList.value.findIndex(item => item.id === id)
        if (index !== -1) {
          documentList.value[index].downloadCount += 1
        }
      }
      return success
    } catch (error) {
      console.error('下载纪实失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置当前选中的数据
  const setCurrentDocument = (document: DocumentRecord | null) => {
    currentDocument.value = document
  }

  const setCurrentTemplate = (template: DocumentTemplate | null) => {
    currentTemplate.value = template
  }

  // 重置状态
  const resetState = () => {
    statistics.value = null
    documentList.value = []
    templateList.value = []
    currentDocument.value = null
    currentTemplate.value = null
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  return {
    // 状态
    loading,
    statistics,
    documentList,
    templateList,
    currentDocument,
    currentTemplate,
    pagination,
    
    // 计算属性
    totalDocuments,
    publishedDocuments,
    draftDocuments,
    totalSpeakers,
    totalTemplates,
    sharedTemplates,
    completionRate,
    totalViews,
    totalDownloads,
    
    // 方法
    fetchStatistics,
    fetchDocumentList,
    fetchTemplateList,
    createDocument,
    updateDocument,
    deleteDocument,
    publishDocument,
    restoreDocument,
    downloadDocument,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    applyTemplate,
    setCurrentDocument,
    setCurrentTemplate,
    resetState
  }
})
