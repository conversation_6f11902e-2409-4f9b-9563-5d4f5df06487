import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import sensitiveWordsAPI from '@/api/sensitive-words'
import type {
  SensitiveWord,
  WordCategory,
  WordLevel,
  FilterPolicy,
  DetectionTask,
  ImportTask,
  SensitiveWordStatistics,
  SensitiveWordSearchParams,
  CategorySearchParams,
  PolicySearchParams,
  DetectionSearchParams
} from '@/types/sensitive-words'

export const useSensitiveWordsStore = defineStore('sensitiveWords', () => {
  // 状态定义
  const loading = ref(false)
  const sensitiveWords = ref<SensitiveWord[]>([])
  const categories = ref<WordCategory[]>([])
  const levels = ref<WordLevel[]>([])
  const policies = ref<FilterPolicy[]>([])
  const detectionTasks = ref<DetectionTask[]>([])
  const importTasks = ref<ImportTask[]>([])
  const statistics = ref<SensitiveWordStatistics | null>(null)

  // 计算属性
  const enabledWords = computed(() =>
    sensitiveWords.value.filter(word => word.isEnabled)
  )

  const enabledPolicies = computed(() =>
    policies.value.filter(policy => policy.isEnabled)
  )

  const categoryTree = computed(() => {
    // API 返回的数据已经是树形结构，直接返回
    // 如果 categories 中的项目已经包含 children 属性，直接使用
    if (categories.value.length > 0 && categories.value[0].children !== undefined) {
      return categories.value
    }

    // 如果是扁平化数据，则构建树形结构（兼容旧数据格式）
    const buildTree = (parentId: number | undefined = undefined): WordCategory[] => {
      return categories.value
        .filter(cat => cat.parentId === parentId)
        .map(cat => ({
          ...cat,
          children: buildTree(cat.id)
        }))
    }
    return buildTree()
  })

  // 模拟数据
  const mockSensitiveWords: SensitiveWord[] = [
    {
      id: 1,
      word: '暴力',
      categoryId: 1,
      levelId: 1,
      isEnabled: true,
      matchType: 1,
      replacement: '***',
      description: '暴力相关敏感词',
      createTime: '2024-01-15 10:30:00',
      creator: '管理员'
    },
    {
      id: 2,
      word: '色情',
      categoryId: 2,
      levelId: 1,
      isEnabled: true,
      matchType: 1,
      replacement: '***',
      description: '色情相关敏感词',
      createTime: '2024-01-15 10:31:00',
      creator: '管理员'
    },
    {
      id: 3,
      word: '政治敏感',
      categoryId: 3,
      levelId: 1,
      isEnabled: true,
      matchType: 2,
      replacement: '[已屏蔽]',
      description: '政治敏感词汇',
      createTime: '2024-01-15 10:32:00',
      creator: '管理员'
    },
    {
      id: 4,
      word: '赌博',
      categoryId: 4,
      levelId: 2,
      isEnabled: true,
      matchType: 1,
      replacement: '***',
      description: '赌博相关词汇',
      createTime: '2024-01-15 10:33:00',
      creator: '管理员'
    },
    {
      id: 5,
      word: '欺诈',
      categoryId: 4,
      levelId: 2,
      isEnabled: true,
      matchType: 1,
      replacement: '***',
      description: '欺诈相关词汇',
      createTime: '2024-01-15 10:34:00',
      creator: '管理员'
    }
  ]

  const mockCategories: WordCategory[] = [
    {
      id: 1,
      categoryName: '暴力内容',
      categoryCode: 'VIOLENCE',
      level: 1,
      sortOrder: 1,
      isEnabled: true,
      description: '包含暴力、血腥等内容',
      wordCount: 156,
      createTime: '2024-01-10 09:00:00'
    },
    {
      id: 2,
      categoryName: '色情内容',
      categoryCode: 'PORNOGRAPHY',
      level: 1,
      sortOrder: 2,
      isEnabled: true,
      description: '包含色情、低俗等内容',
      wordCount: 234,
      createTime: '2024-01-10 09:01:00'
    },
    {
      id: 3,
      categoryName: '政治敏感',
      categoryCode: 'POLITICAL',
      level: 1,
      sortOrder: 3,
      isEnabled: true,
      description: '政治敏感相关内容',
      wordCount: 89,
      createTime: '2024-01-10 09:02:00'
    },
    {
      id: 4,
      categoryName: '违法违规',
      categoryCode: 'ILLEGAL',
      level: 1,
      sortOrder: 4,
      isEnabled: true,
      description: '违法违规相关内容',
      wordCount: 178,
      createTime: '2024-01-10 09:03:00'
    },
    {
      id: 5,
      categoryName: '赌博相关',
      categoryCode: 'GAMBLING',
      parentId: 4,
      level: 2,
      sortOrder: 1,
      isEnabled: true,
      description: '赌博相关子分类',
      wordCount: 67,
      createTime: '2024-01-10 09:04:00'
    }
  ]

  const mockLevels: WordLevel[] = [
    {
      id: 1,
      levelName: '高风险',
      levelCode: 'HIGH',
      priority: 1,
      color: '#ff4d4f',
      action: 1, // 屏蔽
      description: '严重违规内容，直接屏蔽',
      wordCount: 345,
      createTime: '2024-01-10 08:00:00'
    },
    {
      id: 2,
      levelName: '中风险',
      levelCode: 'MEDIUM',
      priority: 2,
      color: '#faad14',
      action: 2, // 替换
      description: '中等风险内容，进行替换',
      wordCount: 234,
      createTime: '2024-01-10 08:01:00'
    },
    {
      id: 3,
      levelName: '低风险',
      levelCode: 'LOW',
      priority: 3,
      color: '#52c41a',
      action: 3, // 警告
      description: '低风险内容，给出警告',
      wordCount: 123,
      createTime: '2024-01-10 08:02:00'
    }
  ]

  const mockPolicies: FilterPolicy[] = [
    {
      id: 1,
      policyName: '全局过滤策略',
      policyCode: 'GLOBAL_FILTER',
      description: '适用于所有内容的全局过滤策略',
      scope: 1, // 全局
      isEnabled: true,
      priority: 1,
      rules: [],
      createTime: '2024-01-12 14:00:00',
      creator: '系统管理员'
    },
    {
      id: 2,
      policyName: '用户组专用策略',
      policyCode: 'USER_GROUP_FILTER',
      description: '针对特定用户组的过滤策略',
      scope: 2, // 用户组
      isEnabled: true,
      priority: 2,
      rules: [],
      createTime: '2024-01-12 14:01:00',
      creator: '系统管理员'
    }
  ]

  const mockDetectionTasks: DetectionTask[] = [
    {
      id: 1,
      taskName: '文本内容检测-001',
      contentType: 1,
      content: '这是一段测试文本内容...',
      status: 3,
      progress: 100,
      result: {
        isClean: false,
        riskLevel: 2,
        hitWords: [
          {
            word: '测试敏感词',
            category: '违法违规',
            level: '中风险',
            position: [10, 15],
            context: '这是一段测试敏感词内容',
            action: 2,
            replacement: '***'
          }
        ],
        suggestions: ['建议替换敏感词汇', '加强内容审核'],
        processedContent: '这是一段测试***内容...',
        confidence: 0.85
      },
      startTime: '2024-01-20 10:00:00',
      endTime: '2024-01-20 10:00:05',
      duration: 5,
      operator: '检测员A'
    }
  ]

  const mockStatistics: SensitiveWordStatistics = {
    totalWords: 702,
    enabledWords: 658,
    totalCategories: 5,
    totalPolicies: 8,
    enabledPolicies: 6,
    todayDetections: 156,
    todayHits: 23,
    categoryStats: [
      { categoryId: 1, categoryName: '暴力内容', wordCount: 156, hitCount: 8 },
      { categoryId: 2, categoryName: '色情内容', wordCount: 234, hitCount: 12 },
      { categoryId: 3, categoryName: '政治敏感', wordCount: 89, hitCount: 2 },
      { categoryId: 4, categoryName: '违法违规', wordCount: 178, hitCount: 6 }
    ],
    levelStats: [
      { levelId: 1, levelName: '高风险', wordCount: 345, hitCount: 18 },
      { levelId: 2, levelName: '中风险', wordCount: 234, hitCount: 8 },
      { levelId: 3, levelName: '低风险', wordCount: 123, hitCount: 3 }
    ],
    detectionTrend: [
      { date: '2024-01-15', detectionCount: 145, hitCount: 21, riskCount: 8 },
      { date: '2024-01-16', detectionCount: 167, hitCount: 19, riskCount: 6 },
      { date: '2024-01-17', detectionCount: 134, hitCount: 25, riskCount: 9 },
      { date: '2024-01-18', detectionCount: 189, hitCount: 17, riskCount: 5 },
      { date: '2024-01-19', detectionCount: 156, hitCount: 23, riskCount: 7 }
    ]
  }

  // Actions
  async function fetchSensitiveWords(params?: SensitiveWordSearchParams) {
    loading.value = true
    try {
      const response = await sensitiveWordsAPI.sensitiveWords.getSensitiveWords(params)
      console.log('API response:', response)
      if (response && response.code === 200 && response.data) {
        sensitiveWords.value = response.data
        console.log('Setting sensitiveWords:', response.data)
        return true
      } else {
        console.warn('API响应格式异常，使用模拟数据:', response)
        // 降级到模拟数据
        sensitiveWords.value = mockSensitiveWords
        message.warning('API调用失败，使用模拟数据')
        return true
      }
    } catch (error) {
      console.error('获取敏感词列表失败:', error)
      // 降级到模拟数据
      sensitiveWords.value = mockSensitiveWords
      message.warning('网络错误，使用模拟数据')
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchCategories(params?: CategorySearchParams) {
    loading.value = true
    try {
      const response = await sensitiveWordsAPI.category.getCategoryTree()
      console.log('fetchCategories response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        categories.value = response.data
        return true
      } else if (response && Array.isArray(response)) {
        // 如果直接返回数组
        categories.value = response
        return true
      } else if (response && response.data && Array.isArray(response.data)) {
        // 如果数据在 data 字段中
        categories.value = response.data
        return true
      } else {
        console.warn('分类API响应格式异常，使用模拟数据:', response)
        categories.value = mockCategories
        message.warning('获取分类失败，使用模拟数据')
        return true
      }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      categories.value = mockCategories
      message.warning('网络错误，使用模拟数据')
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchLevels() {
    loading.value = true
    try {
      const response = await sensitiveWordsAPI.level.getLevels()
      console.log('fetchLevels response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        levels.value = response.data
        return true
      } else if (response && Array.isArray(response)) {
        // 如果直接返回数组
        levels.value = response
        return true
      } else if (response && response.data && Array.isArray(response.data)) {
        // 如果数据在 data 字段中
        levels.value = response.data
        return true
      } else {
        console.warn('级别API响应格式异常，使用模拟数据:', response)
        levels.value = mockLevels
        message.warning('获取级别失败，使用模拟数据')
        return true
      }
    } catch (error) {
      console.error('获取级别列表失败:', error)
      levels.value = mockLevels
      message.warning('网络错误，使用模拟数据')
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchPolicies(params?: PolicySearchParams) {
    loading.value = true
    try {
      const response = await sensitiveWordsAPI.policy.getPolicies(params)
      if (response.code === 200) {
        policies.value = response.data
        return true
      } else {
        policies.value = mockPolicies
        message.warning('获取策略失败，使用模拟数据')
        return true
      }
    } catch (error) {
      console.error('获取策略列表失败:', error)
      policies.value = mockPolicies
      message.warning('网络错误，使用模拟数据')
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchDetectionTasks(params?: DetectionSearchParams) {
    loading.value = true
    try {
      await new Promise(resolve => setTimeout(resolve, 400))
      detectionTasks.value = mockDetectionTasks
      return true
    } catch (error) {
      console.error('获取检测任务失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  async function fetchStatistics() {
    loading.value = true
    try {
      const response = await sensitiveWordsAPI.statistics.getStatistics()
      console.log('fetchStatistics response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        statistics.value = response.data
        return true
      } else if (response && response.data) {
        // 如果数据在 data 字段中
        statistics.value = response.data
        return true
      } else if (response && typeof response === 'object') {
        // 如果直接返回对象
        statistics.value = response
        return true
      } else {
        console.warn('统计API响应格式异常，使用模拟数据:', response)
        statistics.value = mockStatistics
        message.warning('获取统计失败，使用模拟数据')
        return true
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      statistics.value = mockStatistics
      message.warning('网络错误，使用模拟数据')
      return false
    } finally {
      loading.value = false
    }
  }

  async function createSensitiveWord(word: Omit<SensitiveWord, 'id'>) {
    try {
      const response = await sensitiveWordsAPI.sensitiveWords.createSensitiveWord(word)
      if (response.code === 200) {
        sensitiveWords.value.unshift(response.data)
        message.success('创建成功')
        return true
      } else {
        message.error(response.message || '创建失败')
        return false
      }
    } catch (error) {
      console.error('创建敏感词失败:', error)
      message.error('创建失败，请重试')
      return false
    }
  }

  async function updateSensitiveWord(id: number, word: Partial<SensitiveWord>) {
    try {
      const response = await sensitiveWordsAPI.sensitiveWords.updateSensitiveWord(id, word)
      if (response.code === 200) {
        const index = sensitiveWords.value.findIndex(item => item.id === id)
        if (index !== -1) {
          sensitiveWords.value[index] = response.data
        }
        message.success('更新成功')
        return true
      } else {
        message.error(response.message || '更新失败')
        return false
      }
    } catch (error) {
      console.error('更新敏感词失败:', error)
      message.error('更新失败，请重试')
      return false
    }
  }

  async function deleteSensitiveWord(id: number) {
    try {
      const response = await sensitiveWordsAPI.sensitiveWords.deleteSensitiveWord(id)
      if (response.code === 200) {
        const index = sensitiveWords.value.findIndex(item => item.id === id)
        if (index !== -1) {
          sensitiveWords.value.splice(index, 1)
        }
        message.success('删除成功')
        return true
      } else {
        message.error(response.message || '删除失败')
        return false
      }
    } catch (error) {
      console.error('删除敏感词失败:', error)
      message.error('删除失败，请重试')
      return false
    }
  }

  async function batchDeleteWords(ids: number[]) {
    try {
      const response = await sensitiveWordsAPI.sensitiveWords.batchDeleteSensitiveWords(ids)
      if (response.code === 200) {
        sensitiveWords.value = sensitiveWords.value.filter(word => !ids.includes(word.id!))
        message.success(`成功删除 ${ids.length} 个敏感词`)
        return true
      } else {
        message.error(response.message || '批量删除失败')
        return false
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      message.error('批量删除失败，请重试')
      return false
    }
  }

  async function detectContent(task: Omit<DetectionTask, 'id'>) {
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      const newTask: DetectionTask = {
        ...task,
        id: Date.now(),
        status: 3,
        progress: 100,
        result: {
          isClean: Math.random() > 0.3,
          riskLevel: Math.floor(Math.random() * 4) + 1 as any,
          hitWords: [],
          suggestions: ['内容检测完成'],
          confidence: Math.random() * 0.3 + 0.7
        },
        startTime: new Date().toLocaleString(),
        endTime: new Date().toLocaleString(),
        duration: 2,
        operator: '当前用户'
      }
      detectionTasks.value.unshift(newTask)
      return newTask
    } catch (error) {
      console.error('内容检测失败:', error)
      return null
    }
  }

  async function exportSensitiveWords(params?: SensitiveWordSearchParams, format: string = 'xlsx') {
    try {
      const exportParams = { ...params, format }
      const response = await sensitiveWordsAPI.sensitiveWords.exportSensitiveWords(exportParams)

      // 创建下载链接
      const blob = new Blob([response], {
        type: format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
      const fileName = `敏感词列表_${timestamp}.${format === 'csv' ? 'csv' : 'xlsx'}`
      link.download = fileName

      // 触发下载
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      message.success('导出成功')
      return true
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败，请重试')
      return false
    }
  }

  // 分类管理方法
  async function createCategory(category: Omit<WordCategory, 'id'>) {
    try {
      const response = await sensitiveWordsAPI.category.createCategory(category)
      console.log('createCategory response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        categories.value.push(response.data)
        message.success('分类创建成功')
        return true
      } else if (response && response.data) {
        categories.value.push(response.data)
        message.success('分类创建成功')
        return true
      } else if (response && response.code === 200) {
        message.success('分类创建成功')
        // 重新获取分类列表
        await fetchCategories()
        return true
      } else {
        message.error(response?.message || '分类创建失败')
        return false
      }
    } catch (error) {
      console.error('创建分类失败:', error)
      message.error('创建分类失败，请重试')
      return false
    }
  }

  async function updateCategory(id: number, category: Partial<WordCategory>) {
    try {
      const response = await sensitiveWordsAPI.category.updateCategory(id, category)
      console.log('updateCategory response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        const index = categories.value.findIndex(item => item.id === id)
        if (index !== -1) {
          categories.value[index] = response.data
        }
        message.success('分类更新成功')
        return true
      } else if (response && response.data) {
        const index = categories.value.findIndex(item => item.id === id)
        if (index !== -1) {
          categories.value[index] = response.data
        }
        message.success('分类更新成功')
        return true
      } else if (response && response.code === 200) {
        message.success('分类更新成功')
        // 重新获取分类列表
        await fetchCategories()
        return true
      } else {
        message.error(response?.message || '分类更新失败')
        return false
      }
    } catch (error) {
      console.error('更新分类失败:', error)
      message.error('更新分类失败，请重试')
      return false
    }
  }

  async function deleteCategory(id: number) {
    try {
      const response = await sensitiveWordsAPI.category.deleteCategory(id)
      console.log('deleteCategory response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200) {
        const index = categories.value.findIndex(item => item.id === id)
        if (index !== -1) {
          categories.value.splice(index, 1)
        }
        message.success('分类删除成功')
        return true
      } else if (response && response.success !== false) {
        // 如果没有明确的失败标识，认为是成功的
        const index = categories.value.findIndex(item => item.id === id)
        if (index !== -1) {
          categories.value.splice(index, 1)
        }
        message.success('分类删除成功')
        return true
      } else {
        message.error(response?.message || '分类删除失败')
        return false
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      message.error('删除分类失败，请重试')
      return false
    }
  }

  // 级别管理方法
  async function createLevel(level: Omit<WordLevel, 'id'>) {
    try {
      const response = await sensitiveWordsAPI.level.createLevel(level)
      console.log('createLevel response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        levels.value.push(response.data)
        message.success('级别创建成功')
        return true
      } else if (response && response.data) {
        levels.value.push(response.data)
        message.success('级别创建成功')
        return true
      } else if (response && response.code === 200) {
        message.success('级别创建成功')
        // 重新获取级别列表
        await fetchLevels()
        return true
      } else {
        message.error(response?.message || '级别创建失败')
        return false
      }
    } catch (error) {
      console.error('创建级别失败:', error)
      message.error('创建级别失败，请重试')
      return false
    }
  }

  async function updateLevel(id: number, level: Partial<WordLevel>) {
    try {
      const response = await sensitiveWordsAPI.level.updateLevel(id, level)
      console.log('updateLevel response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200 && response.data) {
        const index = levels.value.findIndex(item => item.id === id)
        if (index !== -1) {
          levels.value[index] = response.data
        }
        message.success('级别更新成功')
        return true
      } else if (response && response.data) {
        const index = levels.value.findIndex(item => item.id === id)
        if (index !== -1) {
          levels.value[index] = response.data
        }
        message.success('级别更新成功')
        return true
      } else if (response && response.code === 200) {
        message.success('级别更新成功')
        // 重新获取级别列表
        await fetchLevels()
        return true
      } else {
        message.error(response?.message || '级别更新失败')
        return false
      }
    } catch (error) {
      console.error('更新级别失败:', error)
      message.error('更新级别失败，请重试')
      return false
    }
  }

  async function deleteLevel(id: number) {
    try {
      const response = await sensitiveWordsAPI.level.deleteLevel(id)
      console.log('deleteLevel response:', response)

      // 检查响应格式，兼容不同的响应结构
      if (response && response.code === 200) {
        const index = levels.value.findIndex(item => item.id === id)
        if (index !== -1) {
          levels.value.splice(index, 1)
        }
        message.success('级别删除成功')
        return true
      } else if (response && response.success !== false) {
        // 如果没有明确的失败标识，认为是成功的
        const index = levels.value.findIndex(item => item.id === id)
        if (index !== -1) {
          levels.value.splice(index, 1)
        }
        message.success('级别删除成功')
        return true
      } else {
        message.error(response?.message || '级别删除失败')
        return false
      }
    } catch (error) {
      console.error('删除级别失败:', error)
      message.error('删除级别失败，请重试')
      return false
    }
  }

  return {
    // 状态
    loading,
    sensitiveWords,
    categories,
    levels,
    policies,
    detectionTasks,
    importTasks,
    statistics,

    // 计算属性
    enabledWords,
    enabledPolicies,
    categoryTree,

    // 方法
    fetchSensitiveWords,
    fetchCategories,
    fetchLevels,
    fetchPolicies,
    fetchDetectionTasks,
    fetchStatistics,
    createSensitiveWord,
    updateSensitiveWord,
    deleteSensitiveWord,
    batchDeleteWords,
    detectContent,
    exportSensitiveWords,
    createCategory,
    updateCategory,
    deleteCategory,
    createLevel,
    updateLevel,
    deleteLevel
  }
})
