import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { mockModelAgencyApi } from '@/views/model-agency-dashboard/api/mockModelAgencyApi'

// 单位位置接口定义
export interface UnitLocation {
    id: number
    name: string
    longitude: number
    latitude: number
    district: string
    address: string
    partyBuildingIndex: number
    level?: 'excellent' | 'good' | 'average' | 'poor'
    unitType: 'government' | 'enterprise' | 'institution' | 'model'
    contactPerson?: string
    contactPhone?: string
    description?: string
    establishedDate?: string
    lastUpdated?: string
}

// 搜索参数接口
export interface UnitLocationSearchParams {
    keyword?: string
    district?: string
    unitType?: string
    partyBuildingIndexRange?: [number, number]
    page?: number
    pageSize?: number
}

// 统计信息接口
export interface UnitLocationStatistics {
    totalCount: number
    districtDistribution: Record<string, number>
    unitTypeDistribution: Record<string, number>
    partyBuildingIndexDistribution: Record<string, number>
    recentUpdates: number
    averagePartyBuildingIndex: number
}

export const useUnitLocationStore = defineStore('unitLocation', () => {
    // 状态定义
    const loading = ref(false)
    const unitLocations = ref<UnitLocation[]>([])
    const currentLocation = ref<UnitLocation | null>(null)
    const statistics = ref<UnitLocationStatistics | null>(null)
    const searchParams = ref<UnitLocationSearchParams>({})
    const pagination = ref({
        current: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0
    })

    // 计算属性
    const totalCount = computed(() => unitLocations.value.length)

    const districtOptions = computed(() => {
        const districts = [...new Set(unitLocations.value.map(loc => loc.district))]
        return districts.map(district => ({ label: district, value: district }))
    })

    const unitTypeOptions = computed(() => [
        { label: '政府机关', value: 'government' },
        { label: '企业单位', value: 'enterprise' },
        { label: '事业单位', value: 'institution' },
        { label: '模范机关', value: 'model' }
    ])

    const levelOptions = computed(() => [
        { label: '优秀 (90-100分)', value: 'excellent' },
        { label: '良好 (80-89分)', value: 'good' },
        { label: '一般 (70-79分)', value: 'average' },
        { label: '较差 (60-69分)', value: 'poor' }
    ])

    // 根据党建指数计算等级
    const calculateLevel = (index: number): 'excellent' | 'good' | 'average' | 'poor' => {
        if (index >= 90) return 'excellent'
        if (index >= 80) return 'good'
        if (index >= 70) return 'average'
        return 'poor'
    }

    // Actions

    // 获取单位位置列表
    const fetchUnitLocations = async (params?: UnitLocationSearchParams) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.fetchUnitLocations(params)
            if (response.code === 200) {
                unitLocations.value = response.data.map((location: any) => ({
                    ...location,
                    level: calculateLevel(location.partyBuildingIndex)
                }))
                return true
            } else {
                message.error(response.message)
                return false
            }
        } catch (error) {
            console.error('获取单位位置列表失败:', error)
            message.error('获取单位位置列表失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 搜索单位位置
    const searchUnitLocations = async (params: UnitLocationSearchParams) => {
        loading.value = true
        try {
            searchParams.value = params
            const response = await mockModelAgencyApi.searchUnitLocations(params)
            if (response.code === 200) {
                unitLocations.value = response.data.list.map((location: any) => ({
                    ...location,
                    level: calculateLevel(location.partyBuildingIndex)
                }))
                pagination.value = response.data.pagination
                return true
            } else {
                message.error(response.message)
                return false
            }
        } catch (error) {
            console.error('搜索单位位置失败:', error)
            message.error('搜索单位位置失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 获取单位位置详情
    const getUnitLocationById = async (id: number) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.getUnitLocationById(id)
            if (response.code === 200) {
                currentLocation.value = {
                    ...response.data,
                    level: calculateLevel(response.data.partyBuildingIndex)
                }
                return currentLocation.value
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('获取单位位置详情失败:', error)
            message.error('获取单位位置详情失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 创建单位位置
    const createUnitLocation = async (locationData: Omit<UnitLocation, 'id' | 'level' | 'establishedDate' | 'lastUpdated'>) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.createUnitLocation(locationData)
            if (response.code === 200) {
                const newLocation = {
                    ...response.data,
                    level: calculateLevel(response.data.partyBuildingIndex)
                }
                unitLocations.value.unshift(newLocation)
                message.success('创建单位位置成功')
                return newLocation
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('创建单位位置失败:', error)
            message.error('创建单位位置失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 更新单位位置
    const updateUnitLocation = async (id: number, locationData: Partial<UnitLocation>) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.updateUnitLocation(id, locationData)
            if (response.code === 200) {
                const updatedLocation = {
                    ...response.data,
                    level: calculateLevel(response.data.partyBuildingIndex)
                }

                // 更新列表中的数据
                const index = unitLocations.value.findIndex(loc => loc.id === id)
                if (index !== -1) {
                    unitLocations.value[index] = updatedLocation
                }

                // 更新当前位置数据
                if (currentLocation.value?.id === id) {
                    currentLocation.value = updatedLocation
                }

                message.success('更新单位位置成功')
                return updatedLocation
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('更新单位位置失败:', error)
            message.error('更新单位位置失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 删除单位位置
    const deleteUnitLocation = async (id: number) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.deleteUnitLocation(id)
            if (response.code === 200) {
                // 从列表中移除
                const index = unitLocations.value.findIndex(loc => loc.id === id)
                if (index !== -1) {
                    unitLocations.value.splice(index, 1)
                }

                // 清除当前位置数据
                if (currentLocation.value?.id === id) {
                    currentLocation.value = null
                }

                message.success('删除单位位置成功')
                return true
            } else {
                message.error(response.message)
                return false
            }
        } catch (error) {
            console.error('删除单位位置失败:', error)
            message.error('删除单位位置失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 批量删除单位位置
    const batchDeleteUnitLocations = async (ids: number[]) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.batchDeleteUnitLocations(ids)
            if (response.code === 200) {
                // 从列表中移除
                unitLocations.value = unitLocations.value.filter(loc => !ids.includes(loc.id))

                // 清除当前位置数据（如果被删除）
                if (currentLocation.value && ids.includes(currentLocation.value.id)) {
                    currentLocation.value = null
                }

                message.success(`批量删除${ids.length}个单位位置成功`)
                return true
            } else {
                message.error(response.message)
                return false
            }
        } catch (error) {
            console.error('批量删除单位位置失败:', error)
            message.error('批量删除单位位置失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 批量更新单位位置
    const batchUpdateUnitLocations = async (updates: Array<{ id: number; data: Partial<UnitLocation> }>) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.batchUpdateUnitLocations(updates)
            if (response.code === 200) {
                // 更新列表中的数据
                response.data.forEach((updatedLocation: any) => {
                    const index = unitLocations.value.findIndex(loc => loc.id === updatedLocation.id)
                    if (index !== -1) {
                        unitLocations.value[index] = {
                            ...updatedLocation,
                            level: calculateLevel(updatedLocation.partyBuildingIndex)
                        }
                    }
                })

                message.success(`批量更新${updates.length}个单位位置成功`)
                return true
            } else {
                message.error(response.message)
                return false
            }
        } catch (error) {
            console.error('批量更新单位位置失败:', error)
            message.error('批量更新单位位置失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 导入单位位置数据
    const importUnitLocations = async (importData: any[]) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.importUnitLocations(importData)
            if (response.code === 200) {
                // 重新获取数据
                await fetchUnitLocations()
                message.success(`导入完成：成功${response.data.success}条，失败${response.data.failed}条`)
                return response.data
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('导入单位位置数据失败:', error)
            message.error('导入单位位置数据失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 导出单位位置数据
    const exportUnitLocations = async (params?: { district?: string; unitType?: string; format?: 'excel' | 'csv' }) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.exportUnitLocations(params)
            if (response.code === 200) {
                message.success(`导出成功：${response.data.fileName}`)
                return response.data
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('导出单位位置数据失败:', error)
            message.error('导出单位位置数据失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 获取统计信息
    const fetchStatistics = async (params?: { district?: string }) => {
        loading.value = true
        try {
            const response = await mockModelAgencyApi.getUnitLocationStatistics(params)
            if (response.code === 200) {
                statistics.value = response.data
                return response.data
            } else {
                message.error(response.message)
                return null
            }
        } catch (error) {
            console.error('获取统计信息失败:', error)
            message.error('获取统计信息失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 重置状态
    const resetState = () => {
        unitLocations.value = []
        currentLocation.value = null
        statistics.value = null
        searchParams.value = {}
        pagination.value = {
            current: 1,
            pageSize: 20,
            total: 0,
            totalPages: 0
        }
    }

    return {
        // 状态
        loading,
        unitLocations,
        currentLocation,
        statistics,
        searchParams,
        pagination,

        // 计算属性
        totalCount,
        districtOptions,
        unitTypeOptions,
        levelOptions,

        // 方法
        calculateLevel,
        fetchUnitLocations,
        searchUnitLocations,
        getUnitLocationById,
        createUnitLocation,
        updateUnitLocation,
        deleteUnitLocation,
        batchDeleteUnitLocations,
        batchUpdateUnitLocations,
        importUnitLocations,
        exportUnitLocations,
        fetchStatistics,
        resetState
    }
})