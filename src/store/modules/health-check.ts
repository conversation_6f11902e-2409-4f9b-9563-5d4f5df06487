import { defineStore } from 'pinia';
import { ref } from 'vue';
import { healthCheckEngine } from '@/services/health-check-engine';
import type { 
  HealthCheckRecord, 
  HealthCheckRule, 
  HealthCheckTask, 
  HealthCheckException,
  HealthCheckSearchParams,
  HealthCheckRuleSearchParams,
  HealthCheckTaskSearchParams,
  HealthCheckExceptionSearchParams,
  HealthCheckStatistics,
  HealthCheckType
} from '@/types/health-check';
import * as healthCheckApi from '@/api/health-check';

export const useHealthCheckStore = defineStore('healthCheck', () => {
  // 状态定义
  const healthCheckList = ref<HealthCheckRecord[]>([]);
  const ruleList = ref<HealthCheckRule[]>([]);
  const taskList = ref<HealthCheckTask[]>([]);
  const exceptionList = ref<HealthCheckException[]>([]);
  const statistics = ref<HealthCheckStatistics | null>(null);
  
  // 加载状态
  const loading = ref(false);
  const ruleLoading = ref(false);
  const taskLoading = ref(false);
  const exceptionLoading = ref(false);
  const statisticsLoading = ref(false);
  
  // 当前选中的记录
  const currentRecord = ref<HealthCheckRecord | null>(null);
  const currentRule = ref<HealthCheckRule | null>(null);
  const currentTask = ref<HealthCheckTask | null>(null);
  const currentException = ref<HealthCheckException | null>(null);

  // 获取体检记录列表
  async function fetchHealthCheckList(searchParams?: HealthCheckSearchParams) {
    loading.value = true;
    try {
      const response = await healthCheckApi.fetchHealthCheckList(searchParams);
      healthCheckList.value = response.data || [];
    } catch (e) {
      console.error('获取体检记录列表失败', e);
      healthCheckList.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 获取体检规则列表
  async function fetchRuleList(searchParams?: HealthCheckRuleSearchParams) {
    ruleLoading.value = true;
    try {
      const response = await healthCheckApi.fetchHealthCheckRuleList(searchParams);
      ruleList.value = response.data || [];
    } catch (e) {
      console.error('获取体检规则列表失败', e);
      ruleList.value = [];
    } finally {
      ruleLoading.value = false;
    }
  }

  // 获取体检任务列表
  async function fetchTaskList(searchParams?: HealthCheckTaskSearchParams) {
    taskLoading.value = true;
    try {
      const response = await healthCheckApi.fetchHealthCheckTaskList(searchParams);
      taskList.value = response.data || [];
    } catch (e) {
      console.error('获取体检任务列表失败', e);
      taskList.value = [];
    } finally {
      taskLoading.value = false;
    }
  }

  // 获取异常项列表
  async function fetchExceptionList(searchParams?: HealthCheckExceptionSearchParams) {
    exceptionLoading.value = true;
    try {
      const response = await healthCheckApi.fetchHealthCheckExceptionList(searchParams);
      exceptionList.value = response.data || [];
    } catch (e) {
      console.error('获取异常项列表失败', e);
      exceptionList.value = [];
    } finally {
      exceptionLoading.value = false;
    }
  }

  // 获取统计数据
  async function fetchStatistics() {
    statisticsLoading.value = true;
    try {
      const response = await healthCheckApi.fetchHealthCheckStatistics();
      statistics.value = response.data || null;
      
      // 记录数据来源信息
      if (!response.dataSource.isFromAPI) {
        console.warn('统计数据来源于静态降级数据:', response.dataSource.error);
      }
    } catch (e) {
      console.error('获取统计数据失败', e);
      statistics.value = null;
    } finally {
      statisticsLoading.value = false;
    }
  }

  // 创建体检记录
  async function createHealthCheck(data: Partial<HealthCheckRecord>) {
    try {
      const response = await healthCheckApi.createHealthCheck(data);
      if (response) {
        await fetchHealthCheckList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('创建体检记录失败', e);
      return false;
    }
  }

  // 创建体检规则
  async function createRule(rule: Partial<HealthCheckRule>) {
    try {
      const response = await healthCheckApi.createHealthCheckRule(rule);
      if (response) {
        await fetchRuleList();
        return response.id;
      }
      return null;
    } catch (e) {
      console.error('创建规则失败', e);
      return null;
    }
  }

  // 更新体检规则
  async function updateRule(id: number, rule: Partial<HealthCheckRule>) {
    try {
      const success = await healthCheckApi.updateHealthCheckRule(id, rule);
      if (success) {
        await fetchRuleList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('更新规则失败', e);
      return false;
    }
  }

  // 执行数据体检
  async function executeHealthCheck(checkTypes: HealthCheckType[]) {
    try {
      const result = await healthCheckApi.triggerHealthCheck(undefined, checkTypes);
      if (result) {
        // 更新异常列表
        await fetchExceptionList();
        // 更新统计数据
        await fetchStatistics();
        return result;
      }
      return null;
    } catch (e) {
      console.error('执行体检失败', e);
      return null;
    }
  }

  // 批量执行体检
  async function batchExecuteHealthCheck(checkTypes: HealthCheckType[], config?: any) {
    try {
      const result = await healthCheckApi.batchExecuteHealthCheck(checkTypes, config);
      if (result) {
        await fetchExceptionList();
        await fetchStatistics();
        return result;
      }
      return null;
    } catch (e) {
      console.error('批量执行体检失败', e);
      return null;
    }
  }

  // 导出体检结果
  async function exportHealthCheckResults(format: 'excel' | 'pdf' | 'json' = 'excel') {
    try {
      const result = await healthCheckApi.exportHealthCheckResults(format);
      return result;
    } catch (e) {
      console.error('导出体检结果失败', e);
      return null;
    }
  }

  // 更新体检记录
  async function updateHealthCheck(data: Partial<HealthCheckRecord>) {
    try {
      const response = await healthCheckApi.updateHealthCheck(data.id!, data);
      if (response) {
        // 重新获取列表
        await fetchHealthCheckList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('更新体检记录失败', e);
      return false;
    }
  }

  // 删除体检记录
  async function deleteHealthCheck(id: number) {
    try {
      const response = await healthCheckApi.deleteHealthCheck(id);
      if (response) {
        // 重新获取列表
        await fetchHealthCheckList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('删除体检记录失败', e);
      return false;
    }
  }

  // 执行体检任务
  async function executeTask(taskId: number) {
    try {
      const response = await healthCheckApi.executeHealthCheckTask(taskId);
      if (response) {
        // 重新获取任务列表
        await fetchTaskList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('执行体检任务失败', e);
      return false;
    }
  }

  // 修复异常项
  async function fixException(id: number, fixResult: string) {
    try {
      const response = await healthCheckApi.fixHealthCheckException(id, fixResult);
      if (response) {
        // 重新获取异常项列表
        await fetchExceptionList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('修复异常项失败', e);
      return false;
    }
  }

  // 设置当前选中的记录
  function setCurrentRecord(record: HealthCheckRecord | null) {
    currentRecord.value = record;
  }

  function setCurrentRule(rule: HealthCheckRule | null) {
    currentRule.value = rule;
  }

  function setCurrentTask(task: HealthCheckTask | null) {
    currentTask.value = task;
  }

  function setCurrentException(exception: HealthCheckException | null) {
    currentException.value = exception;
  }

  // 重置状态
  function resetState() {
    healthCheckList.value = [];
    ruleList.value = [];
    taskList.value = [];
    exceptionList.value = [];
    statistics.value = null;
    currentRecord.value = null;
    currentRule.value = null;
    currentTask.value = null;
    currentException.value = null;
  }

  // 获取指定类型的体检记录数量
  function getCheckCountByType(type: number) {
    return healthCheckList.value.filter(item => item.checkType === type).length;
  }

  // 获取指定状态的异常项数量
  function getExceptionCountByStatus(status: number) {
    return exceptionList.value.filter(item => item.status === status).length;
  }

  // 获取高级别异常项
  function getHighLevelExceptions() {
    return exceptionList.value.filter(item => item.exceptionLevel === 3);
  }

  // 获取最近的体检记录
  function getRecentHealthChecks(limit: number = 5) {
    return healthCheckList.value
      .sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
      .slice(0, limit);
  }

  return {
    // 状态
    healthCheckList,
    ruleList,
    taskList,
    exceptionList,
    statistics,
    loading,
    ruleLoading,
    taskLoading,
    exceptionLoading,
    statisticsLoading,
    currentRecord,
    currentRule,
    currentTask,
    currentException,
    
    // 方法
    fetchHealthCheckList,
    fetchRuleList,
    fetchTaskList,
    fetchExceptionList,
    fetchStatistics,
    createHealthCheck,
    updateHealthCheck,
    deleteHealthCheck,
    executeTask,
    fixException,
    setCurrentRecord,
    setCurrentRule,
    setCurrentTask,
    setCurrentException,
    resetState,
    
    // 新增方法
    createRule,
    updateRule,
    executeHealthCheck,
    batchExecuteHealthCheck,
    exportHealthCheckResults,
    
    // 计算方法
    getCheckCountByType,
    getExceptionCountByStatus,
    getHighLevelExceptions,
    getRecentHealthChecks
  };
});
