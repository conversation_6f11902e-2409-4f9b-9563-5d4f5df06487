import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { 
  ApplicationRecord, 
  ReviewRecord, 
  ShortlistRecord, 
  AppealRecord,
  FeedbackReport,
  ApplicationSearchParams,
  ReviewSearchParams,
  ShortlistSearchParams,
  AppealSearchParams,
  FeedbackSearchParams,
  ReviewStatistics,
  ReviewProcess,
  PreReviewConfig,
  ScoringCriteria
} from '@/types/review';
import * as reviewApi from '@/api/review';

export const useReviewStore = defineStore('review', () => {
  // 状态定义
  const applicationList = ref<ApplicationRecord[]>([]);
  const reviewList = ref<ReviewRecord[]>([]);
  const shortlistList = ref<ShortlistRecord[]>([]);
  const appealList = ref<AppealRecord[]>([]);
  const feedbackList = ref<FeedbackReport[]>([]);
  const statistics = ref<ReviewStatistics | null>(null);
  const reviewProcess = ref<ReviewProcess | null>(null);
  const preReviewConfigs = ref<PreReviewConfig[]>([]);
  
  // 加载状态
  const applicationLoading = ref(false);
  const reviewLoading = ref(false);
  const shortlistLoading = ref(false);
  const appealLoading = ref(false);
  const feedbackLoading = ref(false);
  const statisticsLoading = ref(false);
  const processLoading = ref(false);
  const configLoading = ref(false);
  
  // 当前选中的记录
  const currentApplication = ref<ApplicationRecord | null>(null);
  const currentReview = ref<ReviewRecord | null>(null);
  const currentShortlist = ref<ShortlistRecord | null>(null);
  const currentAppeal = ref<AppealRecord | null>(null);
  const currentFeedback = ref<FeedbackReport | null>(null);

  // 获取申报记录列表
  async function fetchApplicationList(searchParams?: ApplicationSearchParams) {
    applicationLoading.value = true;
    try {
      const response = await reviewApi.fetchApplicationList(searchParams);
      applicationList.value = response.data || [];
    } catch (e) {
      console.error('获取申报记录列表失败', e);
      applicationList.value = [];
    } finally {
      applicationLoading.value = false;
    }
  }

  // 获取审核记录列表
  async function fetchReviewList(searchParams?: ReviewSearchParams) {
    reviewLoading.value = true;
    try {
      const response = await reviewApi.fetchReviewList(searchParams);
      reviewList.value = response.data || [];
    } catch (e) {
      console.error('获取审核记录列表失败', e);
      reviewList.value = [];
    } finally {
      reviewLoading.value = false;
    }
  }

  // 获取入围名单列表
  async function fetchShortlistList(searchParams?: ShortlistSearchParams) {
    shortlistLoading.value = true;
    try {
      const response = await reviewApi.fetchShortlistList(searchParams);
      shortlistList.value = response.data || [];
    } catch (e) {
      console.error('获取入围名单列表失败', e);
      shortlistList.value = [];
    } finally {
      shortlistLoading.value = false;
    }
  }

  // 获取申诉记录列表
  async function fetchAppealList(searchParams?: AppealSearchParams) {
    appealLoading.value = true;
    try {
      const response = await reviewApi.fetchAppealList(searchParams);
      appealList.value = response.data || [];
    } catch (e) {
      console.error('获取申诉记录列表失败', e);
      appealList.value = [];
    } finally {
      appealLoading.value = false;
    }
  }

  // 获取反馈报告列表
  async function fetchFeedbackList(searchParams?: FeedbackSearchParams) {
    feedbackLoading.value = true;
    try {
      const response = await reviewApi.fetchFeedbackList(searchParams);
      feedbackList.value = response.data || [];
    } catch (e) {
      console.error('获取反馈报告列表失败', e);
      feedbackList.value = [];
    } finally {
      feedbackLoading.value = false;
    }
  }

  // 获取统计数据
  async function fetchStatistics() {
    statisticsLoading.value = true;
    try {
      const response = await reviewApi.fetchReviewStatistics();
      statistics.value = response.data || null;
    } catch (e) {
      console.error('获取统计数据失败', e);
      statistics.value = null;
    } finally {
      statisticsLoading.value = false;
    }
  }

  // 获取审核流程状态
  async function fetchReviewProcess(applicationId: number) {
    processLoading.value = true;
    try {
      const response = await reviewApi.fetchReviewProcess(applicationId);
      reviewProcess.value = response.data || null;
    } catch (e) {
      console.error('获取审核流程状态失败', e);
      reviewProcess.value = null;
    } finally {
      processLoading.value = false;
    }
  }

  // 获取预审配置
  async function fetchPreReviewConfigs(applicantType?: number) {
    configLoading.value = true;
    try {
      const response = await reviewApi.fetchPreReviewConfig(applicantType);
      preReviewConfigs.value = response.data || [];
    } catch (e) {
      console.error('获取预审配置失败', e);
      preReviewConfigs.value = [];
    } finally {
      configLoading.value = false;
    }
  }

  // 提交申报
  async function submitApplication(data: Partial<ApplicationRecord>) {
    try {
      const response = await reviewApi.submitApplication(data);
      if (response.data) {
        // 重新获取列表
        await fetchApplicationList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('提交申报失败', e);
      return false;
    }
  }

  // 更新申报
  async function updateApplication(data: Partial<ApplicationRecord>) {
    try {
      const response = await reviewApi.updateApplication(data);
      if (response.data) {
        // 重新获取列表
        await fetchApplicationList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('更新申报失败', e);
      return false;
    }
  }

  // 删除申报
  async function deleteApplication(id: number) {
    try {
      const response = await reviewApi.deleteApplication(id);
      if (response.data) {
        // 重新获取列表
        await fetchApplicationList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('删除申报失败', e);
      return false;
    }
  }

  // 执行智能预审
  async function executePreReview(applicationId: number) {
    try {
      const response = await reviewApi.executePreReview(applicationId);
      if (response.data) {
        // 重新获取申报列表和审核列表
        await Promise.all([
          fetchApplicationList(),
          fetchReviewList()
        ]);
        return true;
      }
      return false;
    } catch (e) {
      console.error('执行智能预审失败', e);
      return false;
    }
  }

  // 提交审核意见
  async function submitReview(data: Partial<ReviewRecord>) {
    try {
      const response = await reviewApi.submitReview(data);
      if (response.data) {
        // 重新获取审核列表
        await fetchReviewList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('提交审核意见失败', e);
      return false;
    }
  }

  // 生成入围名单
  async function generateShortlist(criteria: any) {
    try {
      const response = await reviewApi.generateShortlist(criteria);
      if (response.data) {
        // 重新获取入围名单
        await fetchShortlistList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('生成入围名单失败', e);
      return false;
    }
  }

  // 公示入围名单
  async function publishShortlist(shortlistIds: number[]) {
    try {
      const response = await reviewApi.publishShortlist(shortlistIds);
      if (response.data) {
        // 重新获取入围名单
        await fetchShortlistList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('公示入围名单失败', e);
      return false;
    }
  }

  // 提交申诉
  async function submitAppeal(data: Partial<AppealRecord>) {
    try {
      const response = await reviewApi.submitAppeal(data);
      if (response.data) {
        // 重新获取申诉列表
        await fetchAppealList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('提交申诉失败', e);
      return false;
    }
  }

  // 处理申诉
  async function processAppeal(appealId: number, decision: any) {
    try {
      const response = await reviewApi.processAppeal(appealId, decision);
      if (response.data) {
        // 重新获取申诉列表
        await fetchAppealList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('处理申诉失败', e);
      return false;
    }
  }

  // 生成反馈报告
  async function generateFeedbackReport(applicationId: number) {
    try {
      const response = await reviewApi.generateFeedbackReport(applicationId);
      if (response.data) {
        // 重新获取反馈报告列表
        await fetchFeedbackList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('生成反馈报告失败', e);
      return false;
    }
  }

  // 确认反馈报告
  async function confirmFeedbackReport(reportId: number) {
    try {
      const response = await reviewApi.confirmFeedbackReport(reportId);
      if (response.data) {
        // 重新获取反馈报告列表
        await fetchFeedbackList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('确认反馈报告失败', e);
      return false;
    }
  }

  // 批量更新申报状态
  async function batchUpdateApplicationStatus(ids: number[], status: number) {
    try {
      const response = await reviewApi.batchUpdateApplicationStatus(ids, status);
      if (response.data) {
        // 重新获取申报列表
        await fetchApplicationList();
        return true;
      }
      return false;
    } catch (e) {
      console.error('批量更新申报状态失败', e);
      return false;
    }
  }

  // 导出入围名单
  async function exportShortlist(format: 'excel' | 'pdf' = 'excel') {
    try {
      const response = await reviewApi.exportShortlist(format);
      return response.data;
    } catch (e) {
      console.error('导出入围名单失败', e);
      return null;
    }
  }

  // 保存预审配置
  async function savePreReviewConfig(data: Partial<PreReviewConfig>) {
    try {
      const response = await reviewApi.savePreReviewConfig(data);
      if (response.data) {
        // 重新获取配置列表
        await fetchPreReviewConfigs();
        return true;
      }
      return false;
    } catch (e) {
      console.error('保存预审配置失败', e);
      return false;
    }
  }

  // 设置当前选中的记录
  function setCurrentApplication(application: ApplicationRecord | null) {
    currentApplication.value = application;
  }

  function setCurrentReview(review: ReviewRecord | null) {
    currentReview.value = review;
  }

  function setCurrentShortlist(shortlist: ShortlistRecord | null) {
    currentShortlist.value = shortlist;
  }

  function setCurrentAppeal(appeal: AppealRecord | null) {
    currentAppeal.value = appeal;
  }

  function setCurrentFeedback(feedback: FeedbackReport | null) {
    currentFeedback.value = feedback;
  }

  // 重置状态
  function resetState() {
    applicationList.value = [];
    reviewList.value = [];
    shortlistList.value = [];
    appealList.value = [];
    feedbackList.value = [];
    statistics.value = null;
    reviewProcess.value = null;
    preReviewConfigs.value = [];
    currentApplication.value = null;
    currentReview.value = null;
    currentShortlist.value = null;
    currentAppeal.value = null;
    currentFeedback.value = null;
  }

  // 获取指定状态的申报数量
  function getApplicationCountByStatus(status: number) {
    return applicationList.value.filter(item => item.status === status).length;
  }

  // 获取指定类型的申报数量
  function getApplicationCountByType(type: number) {
    return applicationList.value.filter(item => item.applicantType === type).length;
  }

  // 获取指定状态的申诉数量
  function getAppealCountByStatus(status: number) {
    return appealList.value.filter(item => item.status === status).length;
  }

  // 获取高分申报记录
  function getHighScoreApplications(minScore: number = 80) {
    return applicationList.value.filter(item => (item.finalScore || 0) >= minScore);
  }

  // 获取最近的申报记录
  function getRecentApplications(limit: number = 5) {
    return applicationList.value
      .sort((a, b) => new Date(b.submitTime).getTime() - new Date(a.submitTime).getTime())
      .slice(0, limit);
  }

  // 获取待处理的申诉
  function getPendingAppeals() {
    return appealList.value.filter(item => item.status === 1 || item.status === 2);
  }

  // 获取入围名单排名
  function getShortlistRanking() {
    return shortlistList.value.sort((a, b) => a.ranking - b.ranking);
  }

  return {
    // 状态
    applicationList,
    reviewList,
    shortlistList,
    appealList,
    feedbackList,
    statistics,
    reviewProcess,
    preReviewConfigs,
    applicationLoading,
    reviewLoading,
    shortlistLoading,
    appealLoading,
    feedbackLoading,
    statisticsLoading,
    processLoading,
    configLoading,
    currentApplication,
    currentReview,
    currentShortlist,
    currentAppeal,
    currentFeedback,

    // 方法
    fetchApplicationList,
    fetchReviewList,
    fetchShortlistList,
    fetchAppealList,
    fetchFeedbackList,
    fetchStatistics,
    fetchReviewProcess,
    fetchPreReviewConfigs,
    submitApplication,
    updateApplication,
    deleteApplication,
    executePreReview,
    submitReview,
    generateShortlist,
    publishShortlist,
    submitAppeal,
    processAppeal,
    generateFeedbackReport,
    confirmFeedbackReport,
    batchUpdateApplicationStatus,
    exportShortlist,
    savePreReviewConfig,
    setCurrentApplication,
    setCurrentReview,
    setCurrentShortlist,
    setCurrentAppeal,
    setCurrentFeedback,
    resetState,

    // 计算方法
    getApplicationCountByStatus,
    getApplicationCountByType,
    getAppealCountByStatus,
    getHighScoreApplications,
    getRecentApplications,
    getPendingAppeals,
    getShortlistRanking
  };
});
