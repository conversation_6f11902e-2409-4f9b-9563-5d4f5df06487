// 审核确认系统状态管理

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  CultivationObject,
  ProjectInfo,
  AuditRecord,
  AuditProcessNode,
  NotificationTemplate,
  AppealRecord,
  AuditSearchParams,
  AuditStatistics
} from '@/types/audit-confirmation'
import { auditConfirmationApi } from '@/api/audit-confirmation'

export const useAuditConfirmationStore = defineStore('auditConfirmation', () => {
  // 状态数据
  const loading = ref(false)
  const statistics = ref<AuditStatistics | null>(null)
  const projectList = ref<ProjectInfo[]>([])
  const cultivationObjectList = ref<CultivationObject[]>([])
  const auditRecordList = ref<AuditRecord[]>([])
  const processNodes = ref<AuditProcessNode[]>([])
  const notificationTemplates = ref<NotificationTemplate[]>([])
  const appealRecords = ref<AppealRecord[]>([])
  
  // 当前选中的数据
  const currentProject = ref<ProjectInfo | null>(null)
  const currentCultivationObject = ref<CultivationObject | null>(null)
  const currentAuditRecord = ref<AuditRecord | null>(null)

  // 分页信息
  const pagination = ref({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const pendingAuditCount = computed(() => {
    return cultivationObjectList.value.filter(item => item.status === 1).length
  })

  const auditedCount = computed(() => {
    return cultivationObjectList.value.filter(item => item.status === 2).length
  })

  const rejectedCount = computed(() => {
    return cultivationObjectList.value.filter(item => item.status === 3).length
  })

  const avgScore = computed(() => {
    const scoredObjects = cultivationObjectList.value.filter(item => item.score)
    if (scoredObjects.length === 0) return 0
    const totalScore = scoredObjects.reduce((sum, item) => sum + (item.score || 0), 0)
    return Math.round((totalScore / scoredObjects.length) * 100) / 100
  })

  // Actions
  const fetchStatistics = async () => {
    try {
      loading.value = true
      statistics.value = await auditConfirmationApi.getStatistics()
    } catch (error) {
      console.error('获取统计数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchProjectList = async () => {
    try {
      loading.value = true
      projectList.value = await auditConfirmationApi.getProjectList()
    } catch (error) {
      console.error('获取项目列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchCultivationObjectList = async (params?: AuditSearchParams) => {
    try {
      loading.value = true
      const result = await auditConfirmationApi.getCultivationObjectList(params)
      cultivationObjectList.value = result.list
      pagination.value.total = result.total
    } catch (error) {
      console.error('获取培育对象列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchAuditRecordList = async (params?: AuditSearchParams) => {
    try {
      loading.value = true
      const result = await auditConfirmationApi.getAuditRecordList(params)
      auditRecordList.value = result.list
    } catch (error) {
      console.error('获取审核记录失败:', error)
    } finally {
      loading.value = false
    }
  }

  const fetchProcessNodes = async () => {
    try {
      processNodes.value = await auditConfirmationApi.getProcessNodes()
    } catch (error) {
      console.error('获取流程节点失败:', error)
    }
  }

  const fetchNotificationTemplates = async () => {
    try {
      notificationTemplates.value = await auditConfirmationApi.getNotificationTemplates()
    } catch (error) {
      console.error('获取通知模板失败:', error)
    }
  }

  const submitAuditResult = async (data: {
    cultivationObjectId: number
    auditResult: number
    score?: number
    comments: string
    suggestions?: string
  }) => {
    try {
      loading.value = true
      const success = await auditConfirmationApi.submitAuditResult(data)
      if (success) {
        // 更新本地数据
        const index = cultivationObjectList.value.findIndex(
          item => item.id === data.cultivationObjectId
        )
        if (index !== -1) {
          cultivationObjectList.value[index].status = data.auditResult === 1 ? 2 : 3
          cultivationObjectList.value[index].score = data.score
          cultivationObjectList.value[index].auditComments = data.comments
          cultivationObjectList.value[index].auditTime = new Date().toLocaleString()
        }
      }
      return success
    } catch (error) {
      console.error('提交审核结果失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const updateScore = async (data: {
    cultivationObjectId: number
    indicatorId: number
    score: number
    comments?: string
  }) => {
    try {
      loading.value = true
      const success = await auditConfirmationApi.updateScore(data)
      if (success) {
        // 更新本地数据
        const objectIndex = cultivationObjectList.value.findIndex(
          item => item.id === data.cultivationObjectId
        )
        if (objectIndex !== -1) {
          const indicatorIndex = cultivationObjectList.value[objectIndex].indicators.findIndex(
            indicator => indicator.id === data.indicatorId
          )
          if (indicatorIndex !== -1) {
            cultivationObjectList.value[objectIndex].indicators[indicatorIndex].score = data.score
            cultivationObjectList.value[objectIndex].indicators[indicatorIndex].lastModified = new Date().toLocaleString()
            
            // 重新计算总分
            const totalScore = cultivationObjectList.value[objectIndex].indicators.reduce(
              (sum, indicator) => sum + indicator.score, 0
            )
            cultivationObjectList.value[objectIndex].score = totalScore
          }
        }
      }
      return success
    } catch (error) {
      console.error('修改评分失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  const batchAudit = async (data: {
    cultivationObjectIds: number[]
    auditResult: number
    comments: string
  }) => {
    try {
      loading.value = true
      const success = await auditConfirmationApi.batchAudit(data)
      if (success) {
        // 更新本地数据
        data.cultivationObjectIds.forEach(id => {
          const index = cultivationObjectList.value.findIndex(item => item.id === id)
          if (index !== -1) {
            cultivationObjectList.value[index].status = data.auditResult === 1 ? 2 : 3
            cultivationObjectList.value[index].auditComments = data.comments
            cultivationObjectList.value[index].auditTime = new Date().toLocaleString()
          }
        })
      }
      return success
    } catch (error) {
      console.error('批量审核失败:', error)
      return false
    } finally {
      loading.value = false
    }
  }

  // 设置当前选中的数据
  const setCurrentProject = (project: ProjectInfo | null) => {
    currentProject.value = project
  }

  const setCurrentCultivationObject = (object: CultivationObject | null) => {
    currentCultivationObject.value = object
  }

  const setCurrentAuditRecord = (record: AuditRecord | null) => {
    currentAuditRecord.value = record
  }

  // 重置状态
  const resetState = () => {
    statistics.value = null
    projectList.value = []
    cultivationObjectList.value = []
    auditRecordList.value = []
    processNodes.value = []
    notificationTemplates.value = []
    appealRecords.value = []
    currentProject.value = null
    currentCultivationObject.value = null
    currentAuditRecord.value = null
    pagination.value = {
      current: 1,
      pageSize: 10,
      total: 0
    }
  }

  return {
    // 状态
    loading,
    statistics,
    projectList,
    cultivationObjectList,
    auditRecordList,
    processNodes,
    notificationTemplates,
    appealRecords,
    currentProject,
    currentCultivationObject,
    currentAuditRecord,
    pagination,
    
    // 计算属性
    pendingAuditCount,
    auditedCount,
    rejectedCount,
    avgScore,
    
    // 方法
    fetchStatistics,
    fetchProjectList,
    fetchCultivationObjectList,
    fetchAuditRecordList,
    fetchProcessNodes,
    fetchNotificationTemplates,
    submitAuditResult,
    updateScore,
    batchAudit,
    setCurrentProject,
    setCurrentCultivationObject,
    setCurrentAuditRecord,
    resetState
  }
})
