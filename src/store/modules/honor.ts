import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { HonorRecord } from '@/types/honor';
import * as honorApi from '@/api/honor';
import type { HonorSearchParams } from '@/api/honor';
import { fetch } from '@/utils/request';

export const useHonorStore = defineStore('honor', () => {
  const honorList = ref<HonorRecord[]>([]);
  const loading = ref(false);

  async function fetchList(searchParams?: HonorSearchParams) {
    loading.value = true;
    try {
      const response = await honorApi.fetchHonorList(searchParams);
      const data = fetch<HonorRecord[]>(response);
      honorList.value = data || [];
    } catch (e) {
      // 错误处理 - 提供模拟数据用于测试
      console.error('获取荣誉列表失败', e);
    } finally {
      loading.value = false;
    }
  }

  return { honorList, loading, fetchList };
});