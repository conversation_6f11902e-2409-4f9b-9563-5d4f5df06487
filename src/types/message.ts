// 消息中心类型定义

// 消息类型枚举
export enum MessageType {
  SYSTEM = 'system',      // 系统消息
  NOTICE = 'notice',      // 通知公告
  TASK = 'task',          // 任务消息
  APPROVAL = 'approval',  // 审批消息
  REMINDER = 'reminder',  // 提醒消息
  WARNING = 'warning',    // 警告消息
  ERROR = 'error'         // 错误消息
}

// 消息状态枚举
export enum MessageStatus {
  UNREAD = 1,    // 未读
  READ = 2,      // 已读
  ARCHIVED = 3,  // 已归档
  DELETED = 4    // 已删除
}

// 消息优先级枚举
export enum MessagePriority {
  LOW = 1,       // 低
  NORMAL = 2,    // 普通
  HIGH = 3,      // 高
  URGENT = 4     // 紧急
}

// 推送渠道枚举
export enum PushChannel {
  SYSTEM = 'system',        // 系统内推送
  SMS = 'sms',             // 短信
  YUKUAIZHENG = 'ykz',     // 渝快政
  YUKUAIBAN = 'ykb',       // 渝快办
  EMAIL = 'email',         // 邮件
  WECHAT = 'wechat'        // 微信
}

// 消息定义接口
export interface Message {
  id: string
  title: string
  content: string
  type: MessageType
  status: MessageStatus
  priority: MessagePriority
  senderId: string
  senderName: string
  receiverId: string
  receiverName: string
  categoryId?: string
  categoryName?: string
  tags?: string[]
  attachments?: MessageAttachment[]
  readTime?: string
  createTime: string
  updateTime: string
  expireTime?: string
  relatedId?: string
  relatedType?: string
  pushChannels: PushChannel[]
  metadata?: Record<string, any>
}

// 消息附件接口
export interface MessageAttachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  createTime: string
}

// 消息分类接口
export interface MessageCategory {
  id: string
  name: string
  description?: string
  parentId?: string
  level: number
  sort: number
  icon?: string
  color?: string
  isEnabled: boolean
  permissions?: string[]
  createTime: string
  updateTime: string
  children?: MessageCategory[]
}

// 消息规则接口
export interface MessageRule {
  id: string
  name: string
  description?: string
  conditions: MessageRuleCondition[]
  actions: MessageRuleAction[]
  priority: number
  isEnabled: boolean
  createTime: string
  updateTime: string
  createdBy: string
  lastTriggeredTime?: string
  triggerCount: number
}

// 消息规则条件接口
export interface MessageRuleCondition {
  field: string
  operator: string
  value: any
  logicOperator?: 'AND' | 'OR'
}

// 消息规则动作接口
export interface MessageRuleAction {
  type: string
  config: Record<string, any>
}

// 消息模板接口
export interface MessageTemplate {
  id: string
  name: string
  title: string
  content: string
  type: MessageType
  variables: MessageTemplateVariable[]
  isEnabled: boolean
  createTime: string
  updateTime: string
  createdBy: string
  usageCount: number
  tags?: string[]
}

// 消息模板变量接口
export interface MessageTemplateVariable {
  name: string
  label: string
  type: 'string' | 'number' | 'date' | 'boolean'
  required: boolean
  defaultValue?: any
  description?: string
}

// 推送对象接口
export interface PushTarget {
  id: string
  name: string
  type: 'user' | 'group' | 'role' | 'department'
  targetId: string
  isEnabled: boolean
  createTime: string
  updateTime: string
  description?: string
  metadata?: Record<string, any>
}

// 推送策略接口
export interface PushStrategy {
  id: string
  name: string
  description?: string
  channels: PushChannel[]
  targets: string[]
  conditions: MessageRuleCondition[]
  schedule?: PushSchedule
  isEnabled: boolean
  createTime: string
  updateTime: string
  createdBy: string
}

// 推送计划接口
export interface PushSchedule {
  type: 'immediate' | 'scheduled' | 'recurring'
  scheduleTime?: string
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    interval: number
    daysOfWeek?: number[]
    dayOfMonth?: number
    endDate?: string
  }
}

// 消息统计接口
export interface MessageStatistics {
  totalMessages: number
  unreadMessages: number
  readMessages: number
  archivedMessages: number
  messagesByType: Record<MessageType, number>
  messagesByPriority: Record<MessagePriority, number>
  messagesByCategory: Record<string, number>
  recentActivity: MessageActivity[]
  readingRate: number
  averageReadTime: number
}

// 消息活动接口
export interface MessageActivity {
  id: string
  type: 'send' | 'read' | 'archive' | 'delete'
  messageId: string
  messageTitle: string
  userId: string
  userName: string
  timestamp: string
  metadata?: Record<string, any>
}

// 阅读状态跟踪接口
export interface ReadingTracker {
  messageId: string
  userId: string
  readTime: string
  readDuration?: number
  deviceInfo?: string
  ipAddress?: string
  location?: string
  isFullyRead: boolean
  readProgress: number
}

// 消息查询参数接口
export interface MessageQueryParams {
  keyword?: string
  type?: MessageType
  status?: MessageStatus
  priority?: MessagePriority
  categoryId?: string
  senderId?: string
  receiverId?: string
  startTime?: string
  endTime?: string
  tags?: string[]
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 消息操作结果接口
export interface MessageOperationResult {
  success: boolean
  message: string
  data?: any
  errors?: string[]
}

// 批量操作参数接口
export interface BatchOperationParams {
  messageIds: string[]
  operation: 'read' | 'unread' | 'archive' | 'delete' | 'priority'
  value?: any
}
