// 流程引擎相关类型定义

// 节点类型枚举
export enum NodeType {
  START = 'start',           // 开始节点
  END = 'end',              // 结束节点
  TASK = 'task',            // 任务节点
  DECISION = 'decision',     // 决策节点
  PARALLEL = 'parallel',     // 并行节点
  MERGE = 'merge',          // 合并节点
  SUBPROCESS = 'subprocess'  // 子流程节点
}

// 连线类型枚举
export enum ConnectionType {
  SEQUENCE = 'sequence',     // 顺序流转
  CONDITION = 'condition'    // 条件流转
}

// 节点状态枚举
export enum NodeStatus {
  PENDING = 1,    // 待处理
  RUNNING = 2,    // 运行中
  COMPLETED = 3,  // 已完成
  FAILED = 4,     // 失败
  SKIPPED = 5     // 跳过
}

// 流程状态枚举
export enum ProcessStatus {
  DRAFT = 1,      // 草稿
  ACTIVE = 2,     // 激活
  SUSPENDED = 3,  // 挂起
  COMPLETED = 4,  // 完成
  TERMINATED = 5  // 终止
}

// 坐标位置接口
export interface Position {
  x: number
  y: number
}

// 尺寸接口
export interface Size {
  width: number
  height: number
}

// 节点定义接口
export interface NodeDefinition {
  id: string
  type: NodeType
  name: string
  description?: string
  position: Position
  size: Size
  properties: Record<string, any>
  formId?: string
  assignee?: string
  candidateUsers?: string[]
  candidateGroups?: string[]
  dueDate?: string
  priority?: number
}

// 连线定义接口
export interface ConnectionDefinition {
  id: string
  type: ConnectionType
  name?: string
  sourceNodeId: string
  targetNodeId: string
  condition?: string
  properties: Record<string, any>
  points?: Position[]
}

// 流程定义接口
export interface ProcessDefinition {
  id: string
  name: string
  description?: string
  version: number
  category?: string
  nodes: NodeDefinition[]
  connections: ConnectionDefinition[]
  variables?: Record<string, any>
  properties: Record<string, any>
  createTime: string
  updateTime: string
  createdBy: string
  status: ProcessStatus
}

// 流程实例接口
export interface ProcessInstance {
  id: string
  processDefinitionId: string
  processDefinitionName: string
  businessKey?: string
  status: ProcessStatus
  startTime: string
  endTime?: string
  duration?: number
  startUserId: string
  variables: Record<string, any>
  currentNodes: string[]
}

// 任务接口
export interface Task {
  id: string
  processInstanceId: string
  processDefinitionId: string
  nodeId: string
  name: string
  description?: string
  assignee?: string
  candidateUsers?: string[]
  candidateGroups?: string[]
  createTime: string
  dueDate?: string
  priority: number
  status: NodeStatus
  variables: Record<string, any>
  formData?: Record<string, any>
}

// 表单字段类型枚举
export enum FormFieldType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  NUMBER = 'number',
  SELECT = 'select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  DATE = 'date',
  DATETIME = 'datetime',
  FILE = 'file',
  IMAGE = 'image'
}

// 表单字段定义接口
export interface FormFieldDefinition {
  id: string
  name: string
  label: string
  type: FormFieldType
  required: boolean
  defaultValue?: any
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  properties: Record<string, any>
}

// 表单定义接口
export interface FormDefinition {
  id: string
  name: string
  description?: string
  fields: FormFieldDefinition[]
  layout: {
    columns: number
    labelWidth?: number
    size?: 'small' | 'default' | 'large'
  }
  properties: Record<string, any>
  createTime: string
  updateTime: string
}

// 设计器状态接口
export interface DesignerState {
  selectedNodeId?: string
  selectedConnectionId?: string
  draggedNodeType?: NodeType
  isConnecting: boolean
  connectingFromNodeId?: string
  scale: number
  offset: Position
  showGrid: boolean
  snapToGrid: boolean
  gridSize: number
}

// 设计器配置接口
export interface DesignerConfig {
  canvasSize: Size
  nodeDefaults: {
    [key in NodeType]: Partial<NodeDefinition>
  }
  connectionDefaults: Partial<ConnectionDefinition>
  theme: {
    primaryColor: string
    backgroundColor: string
    gridColor: string
    nodeColors: {
      [key in NodeType]: string
    }
  }
}
