// 培育过程预警类型定义

// 预警级别枚举
export enum AlertLevel {
  CRITICAL = 'critical',  // 严重
  MAJOR = 'major',        // 重要
  GENERAL = 'general',    // 一般
  INFO = 'info'           // 信息
}

// 预警状态枚举
export enum AlertStatus {
  PENDING = 'pending',        // 待处理
  PROCESSING = 'processing',  // 处理中
  COMPLETED = 'completed',    // 已完成
  CANCELLED = 'cancelled'     // 已取消
}

// 数据采集状态枚举
export enum DataCollectionStatus {
  COLLECTING = 'collecting',  // 采集中
  PROCESSING = 'processing',  // 处理中
  COMPLETED = 'completed',    // 已完成
  FAILED = 'failed'          // 失败
}

// 督办状态枚举
export enum SupervisionStatus {
  PENDING = 'pending',        // 待督办
  SUPERVISING = 'supervising', // 督办中
  COMPLETED = 'completed',    // 已完成
  OVERDUE = 'overdue'        // 已逾期
}

// 报告类型枚举
export enum ReportType {
  DAILY = 'daily',      // 日报
  WEEKLY = 'weekly',    // 周报
  MONTHLY = 'monthly',  // 月报
  QUARTERLY = 'quarterly', // 季报
  ANNUAL = 'annual',    // 年报
  CUSTOM = 'custom'     // 自定义
}

// 数据源类型枚举
export enum DataSourceType {
  DATABASE = 'database',    // 数据库
  API = 'api',             // API接口
  FILE = 'file',           // 文件
  MANUAL = 'manual'        // 手动录入
}

// 通知方式枚举
export enum NotificationMethod {
  SYSTEM = 'system',        // 系统内通知
  SMS = 'sms',             // 短信
  EMAIL = 'email',         // 邮件
  YUKUAIZHENG = 'ykz',     // 渝快政
  WECHAT = 'wechat'        // 微信
}

// 预警规则接口
export interface AlertRule {
  id: string
  name: string
  description: string
  level: AlertLevel
  isEnabled: boolean
  triggerConditions: TriggerCondition[]
  notificationMethods: NotificationMethod[]
  targetUsers: string[]
  targetRoles: string[]
  cooldownPeriod: number  // 冷却期（分钟）
  createTime: string
  updateTime: string
  createdBy: string
  lastTriggeredTime?: string
  triggerCount: number
  metadata?: Record<string, any>
}

// 触发条件接口
export interface TriggerCondition {
  id: string
  field: string
  operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq' | 'neq' | 'contains' | 'not_contains'
  value: any
  logicalOperator?: 'and' | 'or'
}

// 数据采集模型接口
export interface DataCollectionModel {
  id: string
  name: string
  description: string
  sourceType: DataSourceType
  sourceConfig: DataSourceConfig
  collectionFrequency: number  // 采集频率（分钟）
  dataFormat: string
  isEnabled: boolean
  lastCollectionTime?: string
  nextCollectionTime?: string
  status: DataCollectionStatus
  errorMessage?: string
  createTime: string
  updateTime: string
  createdBy: string
  collectionCount: number
  successRate: number
  metadata?: Record<string, any>
}

// 数据源配置接口
export interface DataSourceConfig {
  // 数据库配置
  host?: string
  port?: number
  database?: string
  username?: string
  password?: string
  query?: string
  
  // API配置
  url?: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  params?: Record<string, any>
  
  // 文件配置
  filePath?: string
  fileType?: 'csv' | 'excel' | 'json' | 'xml'
  encoding?: string
  
  // 其他配置
  timeout?: number
  retryCount?: number
  [key: string]: any
}

// 预警记录接口
export interface AlertRecord {
  id: string
  ruleId: string
  ruleName: string
  level: AlertLevel
  status: AlertStatus
  title: string
  content: string
  triggerTime: string
  triggerData: Record<string, any>
  affectedObjects: string[]
  notificationSent: boolean
  notificationMethods: NotificationMethod[]
  handlerId?: string
  handlerName?: string
  handleTime?: string
  handleNotes?: string
  resolveTime?: string
  resolveNotes?: string
  createTime: string
  updateTime: string
  metadata?: Record<string, any>
}

// 督办对象接口
export interface SupervisionObject {
  id: string
  name: string
  type: string
  description: string
  responsiblePerson: string
  responsibleDepartment: string
  contactInfo: ContactInfo
  evaluationScore: number
  evaluationLevel: string
  supervisionStatus: SupervisionStatus
  supervisionStartTime?: string
  supervisionEndTime?: string
  expectedCompletionTime?: string
  actualCompletionTime?: string
  supervisionNotes?: string
  improvementPlan?: string
  progressReports: ProgressReport[]
  attachments: Attachment[]
  createTime: string
  updateTime: string
  createdBy: string
  lastSyncTime?: string  // 与渝快政同步时间
  metadata?: Record<string, any>
}

// 联系信息接口
export interface ContactInfo {
  phone?: string
  email?: string
  address?: string
  wechat?: string
  qq?: string
}

// 进度报告接口
export interface ProgressReport {
  id: string
  reportTime: string
  progress: number  // 进度百分比
  description: string
  reportedBy: string
  attachments?: Attachment[]
}

// 附件接口
export interface Attachment {
  id: string
  name: string
  url: string
  size: number
  type: string
  uploadTime: string
  uploadedBy: string
}

// 报告模板接口
export interface ReportTemplate {
  id: string
  name: string
  description: string
  type: ReportType
  isDefault: boolean
  isEnabled: boolean
  templateContent: ReportContent
  generateRules: GenerateRule[]
  outputFormat: 'pdf' | 'excel' | 'word' | 'html'
  recipients: string[]
  scheduleConfig?: ScheduleConfig
  createTime: string
  updateTime: string
  createdBy: string
  lastGeneratedTime?: string
  generateCount: number
  metadata?: Record<string, any>
}

// 报告内容接口
export interface ReportContent {
  title: string
  sections: ReportSection[]
  charts: ChartConfig[]
  tables: TableConfig[]
  summary?: string
  footer?: string
}

// 报告章节接口
export interface ReportSection {
  id: string
  title: string
  content: string
  order: number
  isRequired: boolean
  dataSource?: string
  template?: string
}

// 图表配置接口
export interface ChartConfig {
  id: string
  title: string
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
  dataSource: string
  xAxis?: string
  yAxis?: string
  series?: string[]
  options?: Record<string, any>
}

// 表格配置接口
export interface TableConfig {
  id: string
  title: string
  dataSource: string
  columns: TableColumn[]
  pagination?: boolean
  sorting?: boolean
  filtering?: boolean
}

// 表格列配置接口
export interface TableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  formatter?: string
}

// 生成规则接口
export interface GenerateRule {
  id: string
  condition: string
  action: string
  parameters?: Record<string, any>
}

// 调度配置接口
export interface ScheduleConfig {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
  time: string  // HH:mm格式
  dayOfWeek?: number  // 1-7，周一到周日
  dayOfMonth?: number  // 1-31
  dayOfQuarter?: number  // 1-90
  dayOfYear?: number  // 1-365
  timezone?: string
  isEnabled: boolean
}

// 预警统计接口
export interface AlertStatistics {
  totalAlerts: number
  criticalAlerts: number
  majorAlerts: number
  generalAlerts: number
  infoAlerts: number
  pendingAlerts: number
  processingAlerts: number
  completedAlerts: number
  cancelledAlerts: number
  todayAlerts: number
  weekAlerts: number
  monthAlerts: number
  averageHandleTime: number  // 平均处理时间（小时）
  handleRate: number  // 处理率
  alertTrend: AlertTrendData[]
  topAlertRules: TopAlertRule[]
  alertsByLevel: Record<AlertLevel, number>
  alertsByStatus: Record<AlertStatus, number>
}

// 预警趋势数据接口
export interface AlertTrendData {
  date: string
  totalCount: number
  criticalCount: number
  majorCount: number
  generalCount: number
  infoCount: number
  handledCount: number
}

// 热门预警规则接口
export interface TopAlertRule {
  ruleId: string
  ruleName: string
  triggerCount: number
  level: AlertLevel
  lastTriggeredTime: string
}

// 数据采集统计接口
export interface DataCollectionStatistics {
  totalModels: number
  activeModels: number
  inactiveModels: number
  collectingModels: number
  processingModels: number
  completedModels: number
  failedModels: number
  totalCollections: number
  successfulCollections: number
  failedCollections: number
  averageSuccessRate: number
  dataQualityScore: number
  collectionTrend: CollectionTrendData[]
  topDataSources: TopDataSource[]
}

// 采集趋势数据接口
export interface CollectionTrendData {
  date: string
  totalCollections: number
  successfulCollections: number
  failedCollections: number
  dataVolume: number  // 数据量（MB）
  qualityScore: number
}

// 热门数据源接口
export interface TopDataSource {
  sourceId: string
  sourceName: string
  sourceType: DataSourceType
  collectionCount: number
  successRate: number
  lastCollectionTime: string
}

// 督办统计接口
export interface SupervisionStatistics {
  totalObjects: number
  pendingObjects: number
  supervisingObjects: number
  completedObjects: number
  overdueObjects: number
  averageCompletionTime: number  // 平均完成时间（天）
  completionRate: number
  supervisionTrend: SupervisionTrendData[]
  topDepartments: TopDepartment[]
  evaluationDistribution: Record<string, number>
}

// 督办趋势数据接口
export interface SupervisionTrendData {
  date: string
  newObjects: number
  completedObjects: number
  overdueObjects: number
  averageScore: number
}

// 热门部门接口
export interface TopDepartment {
  departmentId: string
  departmentName: string
  objectCount: number
  completionRate: number
  averageScore: number
}

// 查询参数接口
export interface AlertQueryParams {
  keyword?: string
  level?: AlertLevel
  status?: AlertStatus
  ruleId?: string
  startDate?: string
  endDate?: string
  handlerId?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 数据采集查询参数接口
export interface DataCollectionQueryParams {
  keyword?: string
  sourceType?: DataSourceType
  status?: DataCollectionStatus
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 督办查询参数接口
export interface SupervisionQueryParams {
  keyword?: string
  type?: string
  status?: SupervisionStatus
  responsibleDepartment?: string
  startDate?: string
  endDate?: string
  minScore?: number
  maxScore?: number
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 操作结果接口
export interface OperationResult {
  success: boolean
  message: string
  data?: any
  errors?: string[]
  timestamp: string
}

// 批量操作结果接口
export interface BatchOperationResult {
  success: boolean
  message: string
  totalCount: number
  successCount: number
  failureCount: number
  failures?: Array<{
    id: string
    error: string
  }>
  timestamp: string
}

// 导出配置接口
export interface ExportConfig {
  format: 'excel' | 'csv' | 'pdf'
  fields: string[]
  filters?: Record<string, any>
  fileName?: string
  includeHeaders?: boolean
  dateRange?: {
    startDate: string
    endDate: string
  }
}

// 系统配置接口
export interface SystemConfig {
  alertRetentionDays: number  // 预警记录保留天数
  dataRetentionDays: number   // 数据保留天数
  maxConcurrentCollections: number  // 最大并发采集数
  defaultNotificationMethods: NotificationMethod[]
  systemNotificationEnabled: boolean
  emailNotificationEnabled: boolean
  smsNotificationEnabled: boolean
  yukuaizhengEnabled: boolean
  autoCleanupEnabled: boolean
  debugMode: boolean
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}
