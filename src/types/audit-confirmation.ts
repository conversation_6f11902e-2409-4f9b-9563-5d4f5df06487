// 审核确认系统类型定义

// 培育对象接口
export interface CultivationObject {
  id: number;
  name: string;
  projectId: number;
  projectName: string;
  organizationName: string;
  contactPerson: string;
  contactPhone: string;
  status: AuditStatus;
  score?: number;
  submitTime: string;
  auditTime?: string;
  auditor?: string;
  auditComments?: string;
  indicators: IndicatorResult[];
}

// 指标结果接口
export interface IndicatorResult {
  id: number;
  indicatorName: string;
  indicatorCode: string;
  weight: number;
  score: number;
  maxScore: number;
  description: string;
  details?: string;
  lastModified?: string;
  modifier?: string;
}

// 审核记录接口
export interface AuditRecord {
  id: number;
  cultivationObjectId: number;
  cultivationObjectName: string;
  projectName: string;
  auditType: AuditType;
  auditor: string;
  auditTime: string;
  auditResult: AuditResult;
  originalScore?: number;
  finalScore?: number;
  comments: string;
  suggestions?: string;
  attachments?: string[];
}

// 项目信息接口
export interface ProjectInfo {
  id: number;
  name: string;
  description: string;
  startTime: string;
  endTime: string;
  status: ProjectStatus;
  cultivationObjects: CultivationObject[];
  totalCount: number;
  auditedCount: number;
  passedCount: number;
  rejectedCount: number;
}

// 审核流程节点接口
export interface AuditProcessNode {
  id: number;
  nodeName: string;
  nodeOrder: number;
  nodeType: NodeType;
  auditors: string[];
  auditStandard: string;
  auditCycle: number; // 审核周期（天）
  isRequired: boolean;
  nextNodeId?: number;
  isEnabled: boolean;
}

// 通知模板接口
export interface NotificationTemplate {
  id: number;
  templateName: string;
  templateType: NotificationType;
  title: string;
  content: string;
  recipients: string[];
  isEnabled: boolean;
  createTime: string;
  updateTime: string;
}

// 申诉记录接口
export interface AppealRecord {
  id: number;
  cultivationObjectId: number;
  cultivationObjectName: string;
  projectName: string;
  appealReason: string;
  appealContent: string;
  appealTime: string;
  status: AppealStatus;
  appealAttachments?: string[];
  processor?: string;
  processTime?: string;
  processResult?: string;
  processComments?: string;
}

// 审核状态枚举
export type AuditStatus = 1 | 2 | 3; // 1-待审核, 2-已审核, 3-已退回

// 审核类型枚举
export type AuditType = 1 | 2 | 3; // 1-初审, 2-复审, 3-终审

// 审核结果枚举
export type AuditResult = 1 | 2 | 3; // 1-通过, 2-不通过, 3-待补充

// 项目状态枚举
export type ProjectStatus = 1 | 2 | 3 | 4; // 1-进行中, 2-已完成, 3-已暂停, 4-已取消

// 节点类型枚举
export type NodeType = 1 | 2 | 3; // 1-审核节点, 2-评分节点, 3-确认节点

// 通知类型枚举
export type NotificationType = 1 | 2 | 3 | 4; // 1-审核通知, 2-评分通知, 3-申诉通知, 4-系统通知

// 申诉状态枚举
export type AppealStatus = 1 | 2 | 3 | 4 | 5; // 1-已提交, 2-处理中, 3-已受理, 4-已驳回, 5-已完成

// 审核状态映射
export const AuditStatusTextMap = {
  1: '待审核',
  2: '已审核',
  3: '已退回'
} as const;

// 审核状态颜色映射
export const AuditStatusColorMap = {
  1: 'processing',
  2: 'success',
  3: 'error'
} as const;

// 审核状态选项
export const AuditStatusOptions = [
  { label: '待审核', value: 1 },
  { label: '已审核', value: 2 },
  { label: '已退回', value: 3 }
] as const;

// 审核类型映射
export const AuditTypeTextMap = {
  1: '初审',
  2: '复审',
  3: '终审'
} as const;

// 审核结果映射
export const AuditResultTextMap = {
  1: '通过',
  2: '不通过',
  3: '待补充'
} as const;

// 审核结果颜色映射
export const AuditResultColorMap = {
  1: 'success',
  2: 'error',
  3: 'warning'
} as const;

// 审核结果选项
export const AuditResultOptions = [
  { label: '通过', value: 1 },
  { label: '不通过', value: 2 },
  { label: '待补充', value: 3 }
] as const;

// 项目状态映射
export const ProjectStatusTextMap = {
  1: '进行中',
  2: '已完成',
  3: '已暂停',
  4: '已取消'
} as const;

// 项目状态颜色映射
export const ProjectStatusColorMap = {
  1: 'processing',
  2: 'success',
  3: 'warning',
  4: 'error'
} as const;

// 申诉状态映射
export const AppealStatusTextMap = {
  1: '已提交',
  2: '处理中',
  3: '已受理',
  4: '已驳回',
  5: '已完成'
} as const;

// 申诉状态颜色映射
export const AppealStatusColorMap = {
  1: 'blue',
  2: 'processing',
  3: 'success',
  4: 'error',
  5: 'default'
} as const;

// 搜索参数接口
export interface AuditSearchParams {
  projectId?: number;
  cultivationObjectName?: string;
  status?: AuditStatus;
  auditor?: string;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 统计数据接口
export interface AuditStatistics {
  totalProjects: number;
  totalCultivationObjects: number;
  pendingAudit: number;
  auditCompleted: number;
  auditRejected: number;
  totalAppeals: number;
  avgScore: number;
  auditTrend: Array<{
    date: string;
    pending: number;
    completed: number;
    rejected: number;
  }>;
  scoreDistribution: Array<{
    range: string;
    count: number;
  }>;
}
