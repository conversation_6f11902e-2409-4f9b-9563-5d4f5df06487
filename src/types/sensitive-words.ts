// 敏感词管理系统类型定义

// 敏感词基础接口
export interface SensitiveWord {
  id?: number;
  word: string; // 敏感词内容
  categoryId: number; // 分类ID
  levelId: number; // 级别ID
  isEnabled: boolean; // 是否启用
  matchType: MatchType; // 匹配类型：精确/模糊
  replacement?: string; // 替换词
  description?: string; // 描述
  createTime?: string;
  updateTime?: string;
  creator?: string;
}

// 敏感词分类接口
export interface WordCategory {
  id?: number;
  categoryName: string; // 分类名称
  categoryCode: string; // 分类编码
  parentId?: number; // 父分类ID
  level: number; // 分类层级
  sortOrder: number; // 排序
  isEnabled: boolean;
  description?: string;
  wordCount?: number; // 词条数量
  createTime?: string;
  updateTime?: string;
  children?: WordCategory[]; // 子分类
}

// 敏感词级别接口
export interface WordLevel {
  id?: number;
  levelName: string; // 级别名称
  levelCode: string; // 级别编码
  priority: number; // 优先级 1-高 2-中 3-低
  color: string; // 显示颜色
  action: FilterAction; // 默认处理动作
  description?: string;
  wordCount?: number; // 词条数量
  createTime?: string;
  updateTime?: string;
}

// 过滤策略接口
export interface FilterPolicy {
  id?: number;
  policyName: string; // 策略名称
  policyCode: string; // 策略编码
  description?: string;
  scope: PolicyScope; // 应用范围
  isEnabled: boolean;
  priority: number; // 优先级
  rules: FilterRule[]; // 过滤规则
  createTime?: string;
  updateTime?: string;
  creator?: string;
}

// 过滤规则接口
export interface FilterRule {
  id?: number;
  policyId: number;
  categoryIds: number[]; // 适用分类
  levelIds: number[]; // 适用级别
  matchMode: MatchMode; // 匹配模式
  action: FilterAction; // 处理动作
  replacement?: string; // 替换内容
  isEnabled: boolean;
  priority: number;
}

// 内容检测任务接口
export interface DetectionTask {
  id?: number;
  taskName: string;
  contentType: ContentType; // 内容类型
  content?: string; // 文本内容
  fileUrl?: string; // 文件URL
  fileName?: string; // 文件名
  fileSize?: number; // 文件大小
  status: DetectionStatus;
  progress?: number; // 检测进度 0-100
  result?: DetectionResult; // 检测结果
  startTime?: string;
  endTime?: string;
  duration?: number; // 耗时(秒)
  operator?: string;
}

// 检测结果接口
export interface DetectionResult {
  isClean: boolean; // 是否通过检测
  riskLevel: RiskLevel; // 风险级别
  hitWords: HitWord[]; // 命中的敏感词
  suggestions: string[]; // 处理建议
  processedContent?: string; // 处理后内容
  confidence: number; // 置信度 0-1
}

// 命中敏感词接口
export interface HitWord {
  word: string; // 敏感词
  category: string; // 分类
  level: string; // 级别
  position: number[]; // 位置信息 [start, end]
  context: string; // 上下文
  action: FilterAction; // 建议动作
  replacement?: string; // 替换建议
}

// 批量导入任务接口
export interface ImportTask {
  id?: number;
  fileName: string;
  fileSize: number;
  totalCount: number; // 总数量
  successCount: number; // 成功数量
  failCount: number; // 失败数量
  status: ImportStatus;
  progress: number; // 进度 0-100
  errorLog?: string; // 错误日志
  startTime?: string;
  endTime?: string;
  operator?: string;
}

// 统计数据接口
export interface SensitiveWordStatistics {
  totalWords: number; // 总词条数
  enabledWords: number; // 启用词条数
  totalCategories: number; // 总分类数
  totalPolicies: number; // 总策略数
  enabledPolicies: number; // 启用策略数
  todayDetections: number; // 今日检测次数
  todayHits: number; // 今日命中次数
  categoryStats: Array<{
    categoryId: number;
    categoryName: string;
    wordCount: number;
    hitCount: number;
  }>;
  levelStats: Array<{
    levelId: number;
    levelName: string;
    wordCount: number;
    hitCount: number;
  }>;
  detectionTrend: Array<{
    date: string;
    detectionCount: number;
    hitCount: number;
    riskCount: number;
  }>;
}

// 枚举类型定义

// 匹配类型
export type MatchType = 1 | 2; // 1-精确匹配 2-模糊匹配

// 匹配模式
export type MatchMode = 1 | 2 | 3; // 1-包含 2-开头 3-结尾

// 过滤动作
export type FilterAction = 1 | 2 | 3 | 4; // 1-屏蔽 2-替换 3-警告 4-通过

// 策略范围
export type PolicyScope = 1 | 2 | 3 | 4; // 1-全局 2-用户组 3-角色 4-内容类型

// 内容类型
export type ContentType = 1 | 2 | 3 | 4; // 1-文本 2-图像 3-视频 4-语音

// 检测状态
export type DetectionStatus = 1 | 2 | 3 | 4; // 1-待检测 2-检测中 3-已完成 4-检测失败

// 风险级别
export type RiskLevel = 1 | 2 | 3 | 4; // 1-安全 2-低风险 3-中风险 4-高风险

// 导入状态
export type ImportStatus = 1 | 2 | 3 | 4; // 1-待导入 2-导入中 3-已完成 4-导入失败

// 匹配类型映射
export const MatchTypeTextMap = {
  1: '精确匹配',
  2: '模糊匹配'
} as const;

export const MatchTypeOptions = [
  { label: '精确匹配', value: 1 },
  { label: '模糊匹配', value: 2 }
] as const;

// 匹配模式映射
export const MatchModeTextMap = {
  1: '包含匹配',
  2: '开头匹配',
  3: '结尾匹配'
} as const;

export const MatchModeOptions = [
  { label: '包含匹配', value: 1 },
  { label: '开头匹配', value: 2 },
  { label: '结尾匹配', value: 3 }
] as const;

// 过滤动作映射
export const FilterActionTextMap = {
  1: '屏蔽内容',
  2: '替换词汇',
  3: '警告提示',
  4: '直接通过'
} as const;

export const FilterActionColorMap = {
  1: 'red',
  2: 'orange',
  3: 'gold',
  4: 'green'
} as const;

export const FilterActionOptions = [
  { label: '屏蔽内容', value: 1 },
  { label: '替换词汇', value: 2 },
  { label: '警告提示', value: 3 },
  { label: '直接通过', value: 4 }
] as const;

// 策略范围映射
export const PolicyScopeTextMap = {
  1: '全局应用',
  2: '用户组',
  3: '角色权限',
  4: '内容类型'
} as const;

export const PolicyScopeOptions = [
  { label: '全局应用', value: 1 },
  { label: '用户组', value: 2 },
  { label: '角色权限', value: 3 },
  { label: '内容类型', value: 4 }
] as const;

// 内容类型映射
export const ContentTypeTextMap = {
  1: '文本内容',
  2: '图像内容',
  3: '视频内容',
  4: '语音内容'
} as const;

export const ContentTypeColorMap = {
  1: 'blue',
  2: 'green',
  3: 'purple',
  4: 'orange'
} as const;

export const ContentTypeOptions = [
  { label: '文本内容', value: 1 },
  { label: '图像内容', value: 2 },
  { label: '视频内容', value: 3 },
  { label: '语音内容', value: 4 }
] as const;

// 检测状态映射
export const DetectionStatusTextMap = {
  1: '待检测',
  2: '检测中',
  3: '已完成',
  4: '检测失败'
} as const;

export const DetectionStatusColorMap = {
  1: 'default',
  2: 'processing',
  3: 'success',
  4: 'error'
} as const;

export const DetectionStatusOptions = [
  { label: '待检测', value: 1 },
  { label: '检测中', value: 2 },
  { label: '已完成', value: 3 },
  { label: '检测失败', value: 4 }
] as const;

// 风险级别映射
export const RiskLevelTextMap = {
  1: '安全',
  2: '低风险',
  3: '中风险',
  4: '高风险'
} as const;

export const RiskLevelColorMap = {
  1: 'green',
  2: 'blue',
  3: 'orange',
  4: 'red'
} as const;

export const RiskLevelOptions = [
  { label: '安全', value: 1 },
  { label: '低风险', value: 2 },
  { label: '中风险', value: 3 },
  { label: '高风险', value: 4 }
] as const;

// 导入状态映射
export const ImportStatusTextMap = {
  1: '待导入',
  2: '导入中',
  3: '已完成',
  4: '导入失败'
} as const;

export const ImportStatusColorMap = {
  1: 'default',
  2: 'processing',
  3: 'success',
  4: 'error'
} as const;

// 搜索参数接口
export interface SensitiveWordSearchParams {
  word?: string;
  categoryId?: number;
  levelId?: number;
  isEnabled?: boolean;
  matchType?: MatchType;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

export interface CategorySearchParams {
  categoryName?: string;
  parentId?: number;
  isEnabled?: boolean;
  pageNum?: number;
  pageSize?: number;
}

export interface PolicySearchParams {
  policyName?: string;
  scope?: PolicyScope;
  isEnabled?: boolean;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

export interface DetectionSearchParams {
  taskName?: string;
  contentType?: ContentType;
  status?: DetectionStatus;
  riskLevel?: RiskLevel;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}
