// 智能入围审核系统类型定义

// 基础申报表单接口
export interface ApplicationForm {
  id?: number;
  applicantName: string;
  applicantType: number; // 申报单位类型
  projectName: string;
  projectType: number;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
  applicationDate?: string;
  status?: number;
  description: string;
}

// 申报记录接口
export interface ApplicationRecord extends ApplicationForm {
  status: ApplicationStatus;
  applicationDate: string;
  submitTime: string;
  updateTime: string;
  preReviewResult?: string; // 预审结果
  preReviewScore?: number; // 预审得分
  finalScore?: number; // 最终得分
  reviewComments?: string; // 审核意见
  attachments?: string[]; // 附件列表
  operator?: string; // 操作人
  isQualified?: boolean; // 是否符合资格
}

// 审核记录接口
export interface ReviewRecord {
  id?: number;
  applicationId: number;
  reviewType: ReviewType;
  reviewerName: string;
  reviewTime: string;
  reviewResult: ReviewResult;
  score?: number;
  comments: string;
  suggestions?: string; // 修改建议
  attachments?: string[]; // 审核附件
  isPass: boolean;
  nextStep?: string; // 下一步操作
}

// 入围名单接口
export interface ShortlistRecord {
  id?: number;
  applicationId: number;
  applicantName: string;
  projectName: string;
  finalScore: number;
  ranking: number; // 排名
  status: ShortlistStatus;
  publicTime?: string; // 公示时间
  confirmTime?: string; // 确认时间
  comments?: string;
  createTime: string;
}

// 申诉记录接口
export interface AppealRecord {
  id?: number;
  applicationId: number;
  applicantName: string;
  appealReason: string;
  appealContent: string;
  appealTime: string;
  status: AppealStatus;
  appealAttachments?: string[]; // 申诉材料
  reviewerName?: string;
  reviewTime?: string;
  reviewResult?: string;
  reviewComments?: string;
  finalDecision?: AppealDecision;
  createTime: string;
  updateTime: string;
}

// 反馈报告接口
export interface FeedbackReport {
  id?: number;
  applicationId: number;
  applicantName: string;
  reportType: FeedbackType;
  reportContent: string;
  evaluationDetails: string; // 评估详情
  suggestions: string; // 改进建议
  decisionBasis: string; // 决策依据
  generateTime: string;
  isConfirmed: boolean;
  confirmTime?: string;
}

// 申报单位类型枚举
export type ApplicantType = 1 | 2 | 3 | 4;

// 项目类型枚举
export type ProjectType = 1 | 2 | 3 | 4 | 5;

// 申报状态枚举
export type ApplicationStatus = 1 | 2 | 3 | 4 | 5 | 6 | 7;

// 审核类型枚举
export type ReviewType = 1 | 2 | 3;

// 审核结果枚举
export type ReviewResult = 1 | 2 | 3;

// 入围状态枚举
export type ShortlistStatus = 1 | 2 | 3 | 4;

// 申诉状态枚举
export type AppealStatus = 1 | 2 | 3 | 4 | 5;

// 申诉决定枚举
export type AppealDecision = 1 | 2 | 3;

// 反馈类型枚举
export type FeedbackType = 1 | 2 | 3;

// 申报单位类型映射
export const ApplicantTypeTextMap = {
   1:'机关单位',
  // 1: '企业单位',
  2: '事业单位',
  3: '社会组织',
  4: '个人申报',
 
} as const;

// 申报单位类型选项
export const ApplicantTypeOptions = [
  { label: '机关单位', value: 1 },
  { label: '机关单位', value: 2 },
  { label: '机关单位', value: 3 },
  { label: '机关单位', value: 4 }
] as const;

// 项目类型映射
export const ProjectTypeTextMap = {
  1: '科技创新',
  2: '文化艺术',
  3: '社会公益',
  4: '教育培训',
  5: '其他类型'
} as const;

// 项目类型选项
export const ProjectTypeOptions = [
  { label: '科技创新', value: 1 },
  { label: '文化艺术', value: 2 },
  { label: '社会公益', value: 3 },
  { label: '教育培训', value: 4 },
  { label: '其他类型', value: 5 }
] as const;

// 申报状态映射
export const ApplicationStatusTextMap = {
  1: '草稿',
  2: '已提交',
  3: '预审中',
  4: '预审通过',
  5: '预审不通过',
  6: '正式审核中',
  7: '审核完成'
} as const;

// 申报状态颜色映射
export const ApplicationStatusColorMap = {
  1: 'default',
  2: 'processing',
  3: 'processing',
  4: 'success',
  5: 'error',
  6: 'processing',
  7: 'success'
} as const;

// 申报状态选项
export const ApplicationStatusOptions = [
  { label: '预审中', value: 1 },
  { label: '预审中', value: 2 },
  { label: '预审中', value: 3 },
  { label: '预审中', value: 4 },
  { label: '预审中', value: 5 },
  { label: '预审中', value: 6 },
  { label: '预审中', value: 7 }
] as const;

// 审核类型映射
export const ReviewTypeTextMap = {
  1: '智能预审',
  2: '人工审核',
  3: '复审'
} as const;

// 审核类型选项
export const ReviewTypeOptions = [
  { label: '智能预审', value: 1 },
  { label: '人工审核', value: 2 },
  { label: '复审', value: 3 }
] as const;

// 审核结果映射
export const ReviewResultTextMap = {
  1: '通过',
  2: '不通过',
  3: '待补充材料'
} as const;

// 审核结果颜色映射
export const ReviewResultColorMap = {
  1: 'success',
  2: 'error',
  3: 'warning'
} as const;

// 审核结果选项
export const ReviewResultOptions = [
  { label: '通过', value: 1 },
  { label: '不通过', value: 2 },
  { label: '待补充材料', value: 3 }
] as const;

// 入围状态映射
export const ShortlistStatusTextMap = {
  1: '候选',
  2: '入围',
  3: '公示中',
  4: '已确认'
} as const;

// 入围状态颜色映射
export const ShortlistStatusColorMap = {
  1: 'blue',
  2: 'green',
  3: 'orange',
  4: 'purple'
} as const;

// 入围状态选项
export const ShortlistStatusOptions = [
  { label: '候选', value: 1 },
  { label: '入围', value: 2 },
  { label: '公示中', value: 3 },
  { label: '已确认', value: 4 }
] as const;

// 申诉状态映射
export const AppealStatusTextMap = {
  1: '已提交',
  2: '审理中',
  3: '已受理',
  4: '已驳回',
  5: '已完成'
} as const;

// 申诉状态颜色映射
export const AppealStatusColorMap = {
  1: 'blue',
  2: 'processing',
  3: 'success',
  4: 'error',
  5: 'default'
} as const;

// 申诉状态选项
export const AppealStatusOptions = [
  { label: '已提交', value: 1 },
  { label: '审理中', value: 2 },
  { label: '已受理', value: 3 },
  { label: '已驳回', value: 4 },
  { label: '已完成', value: 5 }
] as const;

// 申诉决定映射
export const AppealDecisionTextMap = {
  1: '维持原决定',
  2: '撤销原决定',
  3: '部分支持'
} as const;

// 申诉决定颜色映射
export const AppealDecisionColorMap = {
  1: 'default',
  2: 'success',
  3: 'warning'
} as const;

// 申诉决定选项
export const AppealDecisionOptions = [
  { label: '维持原决定', value: 1 },
  { label: '撤销原决定', value: 2 },
  { label: '部分支持', value: 3 }
] as const;

// 反馈类型映射
export const FeedbackTypeTextMap = {
  1: '预审反馈',
  2: '审核反馈',
  3: '申诉反馈'
} as const;

// 反馈类型选项
export const FeedbackTypeOptions = [
  { label: '预审反馈', value: 1 },
  { label: '审核反馈', value: 2 },
  { label: '申诉反馈', value: 3 }
] as const;

// 申报搜索参数接口
export interface ApplicationSearchParams {
  applicantName?: string;
  projectName?: string;
  applicantType?: ApplicantType;
  projectType?: ProjectType;
  status?: ApplicationStatus;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 审核搜索参数接口
export interface ReviewSearchParams {
  applicantName?: string;
  reviewType?: ReviewType;
  reviewResult?: ReviewResult;
  reviewerName?: string;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 入围名单搜索参数接口
export interface ShortlistSearchParams {
  applicantName?: string;
  projectName?: string;
  status?: ShortlistStatus;
  minScore?: number;
  maxScore?: number;
  ranking?: number;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 申诉搜索参数接口
export interface AppealSearchParams {
  applicantName?: string;
  status?: AppealStatus;
  appealReason?: string;
  reviewerName?: string;
  finalDecision?: AppealDecision;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 反馈报告搜索参数接口
export interface FeedbackSearchParams {
  applicantName?: string;
  reportType?: FeedbackType;
  isConfirmed?: boolean;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 统计数据接口
export interface ReviewStatistics {
  totalApplications: number;
  submittedApplications: number;
  preReviewPassed: number;
  finalReviewPassed: number;
  totalShortlisted: number;
  totalAppeals: number;
  appealSuccess: number;
  applicationTypeStats: Array<{
    type: ApplicantType;
    count: number;
    passRate: number;
  }>;
  projectTypeStats: Array<{
    type: ProjectType;
    count: number;
    avgScore: number;
  }>;
  reviewTrend: Array<{
    date: string;
    submitted: number;
    reviewed: number;
    passed: number;
  }>;
  scoreDistribution: Array<{
    range: string;
    count: number;
  }>;
}

// 审核流程状态接口
export interface ReviewProcess {
  applicationId: number;
  currentStep: number;
  totalSteps: number;
  steps: Array<{
    stepName: string;
    status: 'completed' | 'current' | 'pending';
    completedTime?: string;
    operator?: string;
    comments?: string;
  }>;
}

// 智能预审配置接口
export interface PreReviewConfig {
  id?: number;
  configName: string;
  applicantType: ApplicantType;
  projectType: ProjectType;
  criteria: Array<{
    name: string;
    weight: number;
    minValue: number;
    maxValue: number;
    required: boolean;
  }>;
  passThreshold: number; // 通过阈值
  isEnabled: boolean;
  createTime?: string;
  updateTime?: string;
}

// 评分标准接口
export interface ScoringCriteria {
  id?: number;
  criteriaName: string;
  projectType: ProjectType;
  categories: Array<{
    name: string;
    weight: number;
    maxScore: number;
    description: string;
    subCriteria?: Array<{
      name: string;
      weight: number;
      maxScore: number;
      description: string;
    }>;
  }>;
  totalScore: number;
  isEnabled: boolean;
  createTime?: string;
  updateTime?: string;
}
