export interface HonorApplyForm {
  id?: number;
  applicant: string;
  department: string;
  type?: number; // 改为数字类型
  status?: number; // 改为数字类型
  materialUrl: string;
  description: string;
}

export type HonorStatus = 1 | 2 | 3;
export type HonorType = 1 | 2 | 3 | 4 | 5;

export interface HonorRecord extends HonorApplyForm {
  status: HonorStatus;
  createTime: string;  // 统一使用createTime作为提交时间字段
  auditOpinion?: string;
}

// 申报类型映射
export const HonorTypeTextMap = {
  1: '优秀共产党员',
  2: '优秀党务工作者',
  3: '先进基层党组织',
  4: '先进工作者',
  5: '模范机关标兵单位'
} as const;

// 申报类型选项
export const HonorTypeOptions = [
  { label: '优秀共产党员', value: 1 },
  { label: '优秀党务工作者', value: 2 },
  { label: '先进基层党组织', value: 3 },
  { label: '先进工作者', value: 4 },
  { label: '模范机关标兵单位', value: 5 }
] as const;

// 状态显示文本映射
export const StatusTextMap = {
  1: '待审核',
  2: '已通过',
  3: '已驳回'
} as const;

// 状态颜色映射
export const StatusColorMap = {
  1: 'orange',
  2: 'green',
  3: 'red'
} as const;

// 状态选项（用于下拉选择）
export const StatusOptions = [
  { label: '待审核', value: 1 },
  { label: '已通过', value: 2 },
  { label: '已驳回', value: 3 }
] as const;