// 数据体检系统类型定义

// 基础表单接口
export interface HealthCheckForm {
  id?: number;
  checkName: string;
  checkType: number;
  targetObject: string; // 检查对象
  status?: number;
  description: string;
  createTime?: string;
  updateTime?: string;
}

// 体检记录接口
export interface HealthCheckRecord extends HealthCheckForm {
  status: HealthCheckStatus;
  createTime: string;
  updateTime: string;
  checkResult?: string; // 检查结果
  exceptionCount?: number; // 异常数量
  lastCheckTime?: string; // 最后检查时间
  operator?: string; // 操作人
}

export interface HealthCheckRule {
  id?: number;
  ruleName: string;
  ruleType: HealthCheckType;
  ruleContent: string; // 规则内容JSON
  isEnabled: boolean;
  priority: number; // 优先级
  description?: string;
  createTime?: string;
  updateTime?: string;
}

// 体检执行任务接口
export interface HealthCheckTask {
  id?: number;
  taskName: string;
  taskType: HealthCheckType;
  scheduleType: ScheduleType; // 调度类型：手动/定时
  cronExpression?: string; // cron表达式
  status: TaskStatus;
  progress?: number; // 执行进度 0-100
  startTime?: string;
  endTime?: string;
  result?: string;
  errorMessage?: string;
}

// 体检结果异常项接口
export interface HealthCheckException {
  id?: number;
  checkTaskId: number;
  exceptionType: ExceptionType;
  exceptionLevel: ExceptionLevel;
  exceptionTitle: string;
  exceptionDescription: string;
  affectedObject: string; // 影响对象
  solution?: string; // 解决方案
  status: ExceptionStatus;
  fixTime?: string; // 整改时间
  fixOperator?: string; // 整改人
  fixResult?: string; // 整改结果
  createTime: string;
}

// 体检类型枚举
export type HealthCheckType = 1 | 2 | 3 | 4;

// 体检状态枚举
export type HealthCheckStatus = 1 | 2 | 3 | 4;

// 调度类型枚举
export type ScheduleType = 1 | 2;

// 任务状态枚举
export type TaskStatus = 1 | 2 | 3 | 4 | 5;

// 异常类型枚举
export type ExceptionType = 1 | 2 | 3 | 4;

// 异常级别枚举
export type ExceptionLevel = 1 | 2 | 3;

// 异常状态枚举
export type ExceptionStatus = 1 | 2 | 3 | 4;

// 体检类型映射
export const HealthCheckTypeTextMap = {
  1: '党组（党委）设置',
  2: '党务干部任免',
  3: '任务体检',
  4: '用户信息完整'
} as const;

// 体检类型选项
export const HealthCheckTypeOptions = [
  { label: '党组（党委）设置', value: 1 },
  { label: '党务干部任免', value: 2 },
  { label: '任务体检', value: 3 },
  { label: '用户信息完整', value: 4 }
] as const;

// 体检状态映射
export const HealthCheckStatusTextMap = {
  1: '待执行',
  2: '执行中',
  3: '已完成',
  4: '执行失败'
} as const;

// 体检状态颜色映射
export const HealthCheckStatusColorMap = {
  1: 'blue',
  2: 'processing',
  3: 'success',
  4: 'error'
} as const;

// 体检状态选项
export const HealthCheckStatusOptions = [
  { label: '待执行', value: 1 },
  { label: '执行中', value: 2 },
  { label: '已完成', value: 3 },
  { label: '执行失败', value: 4 }
] as const;

// 调度类型映射
export const ScheduleTypeTextMap = {
  1: '手动执行',
  2: '定时执行'
} as const;

// 调度类型选项
export const ScheduleTypeOptions = [
  { label: '手动执行', value: 1 },
  { label: '定时执行', value: 2 }
] as const;

// 任务状态映射
export const TaskStatusTextMap = {
  1: '待执行',
  2: '执行中',
  3: '已完成',
  4: '已暂停',
  5: '执行失败'
} as const;

// 任务状态颜色映射
export const TaskStatusColorMap = {
  1: 'default',
  2: 'processing',
  3: 'success',
  4: 'warning',
  5: 'error'
} as const;

// 任务状态选项
export const TaskStatusOptions = [
  { label: '待执行', value: 1 },
  { label: '执行中', value: 2 },
  { label: '已完成', value: 3 },
  { label: '已暂停', value: 4 },
  { label: '执行失败', value: 5 }
] as const;

// 异常类型映射
export const ExceptionTypeTextMap = {
  1: '完整性异常',
  2: '准确性异常',
  3: '一致性异常',
  4: '安全性异常'
} as const;

// 异常类型颜色映射
export const ExceptionTypeColorMap = {
  1: 'red',
  2: 'orange',
  3: 'blue',
  4: 'purple'
} as const;

// 异常类型选项
export const ExceptionTypeOptions = [
  { label: '完整性异常', value: 1 },
  { label: '准确性异常', value: 2 },
  { label: '一致性异常', value: 3 },
  { label: '安全性异常', value: 4 }
] as const;

// 异常级别映射
export const ExceptionLevelTextMap = {
  1: '低',
  2: '中',
  3: '高'
} as const;

// 异常级别颜色映射
export const ExceptionLevelColorMap = {
  1: 'green',
  2: 'orange',
  3: 'red'
} as const;

// 异常级别选项
export const ExceptionLevelOptions = [
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 }
] as const;

// 异常状态映射
export const ExceptionStatusTextMap = {
  1: '待处理',
  2: '处理中',
  3: '已修复',
  4: '已忽略'
} as const;

// 异常状态颜色映射
export const ExceptionStatusColorMap = {
  1: 'orange',
  2: 'processing',
  3: 'success',
  4: 'default'
} as const;

// 异常状态选项
export const ExceptionStatusOptions = [
  { label: '待处理', value: 1 },
  { label: '处理中', value: 2 },
  { label: '已修复', value: 3 },
  { label: '已忽略', value: 4 }
] as const;

// 搜索参数接口
export interface HealthCheckSearchParams {
  checkName?: string;
  checkType?: HealthCheckType;
  status?: HealthCheckStatus;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 规则搜索参数接口
export interface HealthCheckRuleSearchParams {
  ruleName?: string;
  ruleType?: HealthCheckType;
  isEnabled?: boolean;
  pageNum?: number;
  pageSize?: number;
}

// 任务搜索参数接口
export interface HealthCheckTaskSearchParams {
  taskName?: string;
  taskType?: HealthCheckType;
  status?: TaskStatus;
  scheduleType?: ScheduleType;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 异常搜索参数接口
export interface HealthCheckExceptionSearchParams {
  exceptionTitle?: string;
  exceptionType?: ExceptionType;
  exceptionLevel?: ExceptionLevel;
  status?: ExceptionStatus;
  affectedObject?: string;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 统计数据接口
export interface HealthCheckStatistics {
  totalChecks: number;
  completedChecks: number;
  failedChecks: number;
  totalExceptions: number;
  highLevelExceptions: number;
  fixedExceptions: number;
  checkTypeStats: Array<{
    type: HealthCheckType;
    count: number;
    exceptionCount: number;
  }>;
  exceptionTrend: Array<{
    date: string;
    count: number;
  }>;
}

// 异常搜索参数接口
export interface ExceptionSearchParams {
  exceptionType?: number;
  severity?: number;
  status?: number;
  assignee?: string;
  dateRange?: [string, string];
}

// 定时任务搜索参数(增强版)
export interface ScheduledTaskSearchParams {
  taskName?: string;
  taskType?: HealthCheckType;
  status?: TaskStatus;
  isEnabled?: boolean;
  creator?: string;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 严重程度映射 (使用异常级别)
export const SeverityTextMap = ExceptionLevelTextMap;
export const SeverityColorMap = ExceptionLevelColorMap;
export const SeverityOptions = ExceptionLevelOptions;

// ================================
// 数据源相关类型定义 - P0级接口支持
// ================================

// 数据源类型
export type DataSourceType = 'mysql' | 'postgresql' | 'mongodb' | 'api';

// 数据源状态
export type DataSourceStatus = 1 | 2 | 3; // 1-正常 2-异常 3-未测试

// 同步状态
export type SyncStatus = 'success' | 'failed' | 'syncing' | 'pending';

// 同步类型
export type SyncType = 'full' | 'incremental' | 'manual';

// 数据源接口
export interface DataSource {
  id: string;
  sourceName: string;
  sourceType: DataSourceType;
  connectionUrl: string;
  status: DataSourceStatus;
  lastSyncTime?: string;
  syncStatus: SyncStatus;
  description?: string;
  createTime: string;
  updateTime: string;
  authConfig?: {
    username?: string;
    password?: string;
    apiKey?: string;
    headers?: Record<string, string>;
  };
  syncConfig?: {
    autoSync: boolean;
    syncInterval: number; // 分钟
    syncFields: string[];
  };
}

// 数据源表单接口
export interface DataSourceForm {
  sourceName: string;
  sourceType: DataSourceType;
  connectionUrl: string;
  authConfig?: {
    username?: string;
    password?: string;
    apiKey?: string;
    headers?: Record<string, string>;
  };
  syncConfig?: {
    autoSync: boolean;
    syncInterval: number;
    syncFields: string[];
  };
  description?: string;
}

// 数据源搜索参数
export interface DataSourceSearchParams {
  sourceName?: string;
  sourceType?: DataSourceType;
  status?: DataSourceStatus;
  syncStatus?: SyncStatus;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 连接测试结果
export interface ConnectionTestResult {
  success: boolean;
  message: string;
  latency?: number;
  details?: {
    serverVersion?: string;
    databaseSize?: string;
    tableCount?: number;
    lastResponse?: string;
  };
}

// 同步任务结果
export interface SyncTaskResult {
  taskId: number;
  estimatedDuration: number;
  syncType: string;
}

// 同步状态详情
export interface DataSourceSyncStatus {
  isRunning: boolean;
  currentTask?: {
    taskId: number;
    progress: number;
    startTime: string;
    estimatedEndTime?: string;
    syncedRecords: number;
    totalRecords?: number;
    currentTable?: string;
  };
  lastSync?: {
    syncTime: string;
    status: 'success' | 'failed' | 'partial';
    syncedRecords: number;
    duration: number;
    errorMessage?: string;
  };
  nextScheduledSync?: string;
}

// 同步历史记录
export interface SyncHistoryRecord {
  id: number;
  syncTime: string;
  syncType: SyncType;
  status: 'success' | 'failed' | 'partial';
  duration: number; // 秒
  syncedRecords: number;
  totalRecords: number;
  errorMessage?: string;
  details?: {
    tablesProcessed: string[];
    changedRecords: number;
    deletedRecords: number;
    addedRecords: number;
  };
}

// 数据源类型映射
export const DataSourceTypeTextMap = {
  mysql: 'MySQL数据库',
  postgresql: 'PostgreSQL数据库',
  mongodb: 'MongoDB数据库',
  api: 'API接口'
} as const;

// 数据源类型选项
export const DataSourceTypeOptions = [
  { label: 'MySQL数据库', value: 'mysql' },
  { label: 'PostgreSQL数据库', value: 'postgresql' },
  { label: 'MongoDB数据库', value: 'mongodb' },
  { label: 'API接口', value: 'api' }
] as const;

// 数据源状态映射
export const DataSourceStatusTextMap = {
  1: '正常',
  2: '异常',
  3: '未测试'
} as const;

// 数据源状态颜色映射
export const DataSourceStatusColorMap = {
  1: 'success',
  2: 'error',
  3: 'default'
} as const;

// 数据源状态选项
export const DataSourceStatusOptions = [
  { label: '正常', value: 1 },
  { label: '异常', value: 2 },
  { label: '未测试', value: 3 }
] as const;

// 同步状态映射
export const SyncStatusTextMap = {
  success: '成功',
  failed: '失败',
  syncing: '同步中',
  pending: '等待中'
} as const;

// 同步状态颜色映射
export const SyncStatusColorMap = {
  success: 'success',
  failed: 'error',
  syncing: 'processing',
  pending: 'default'
} as const;

// 同步状态选项
export const SyncStatusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' },
  { label: '同步中', value: 'syncing' },
  { label: '等待中', value: 'pending' }
] as const;

// ================================
// 定时任务增强类型定义 - P1级功能支持
// ================================

// 定时任务增强接口
export interface ScheduledTaskEnhanced {
  id: number;
  taskName: string;
  taskType: HealthCheckType;
  checkTypes: HealthCheckType[];
  cronExpression: string;
  cronDescription: string; // 人类可读的cron描述
  isEnabled: boolean;
  status: TaskStatus;
  lastExecuteTime?: string;
  nextExecuteTime?: string;
  executionCount: number;
  successCount: number;
  failedCount: number;
  avgDuration: number; // 平均执行时长(秒)
  createTime: string;
  updateTime: string;
  creator: string;
  notificationSettings?: {
    onSuccess: boolean;
    onError: boolean;
    recipients: string[];
    template?: string;
  };
  executionConfig?: {
    timeout: number; // 超时时间(分钟)
    retryCount: number; // 重试次数
    concurrency: number; // 并发数
  };
}

// 定时任务表单
export interface ScheduledTaskForm {
  taskName: string;
  checkTypes: HealthCheckType[];
  cronExpression: string;
  isEnabled?: boolean;
  notificationSettings?: {
    onSuccess: boolean;
    onError: boolean;
    recipients: string[];
    template?: string;
  };
  executionConfig?: {
    timeout: number;
    retryCount: number;
    concurrency: number;
  };
  description?: string;
}

// 任务执行历史
export interface TaskExecutionHistory {
  id: number;
  executionId: string;
  startTime: string;
  endTime?: string;
  status: TaskStatus;
  duration: number; // 秒
  progress: number;
  checkResults: {
    totalChecked: number;
    exceptionsFound: number;
    checksByType: Array<{
      type: HealthCheckType;
      checked: number;
      exceptions: number;
    }>;
  };
  errorMessage?: string;
  logs?: Array<{
    time: string;
    level: 'info' | 'warn' | 'error';
    message: string;
  }>;
}

// 执行日志
export interface ExecutionLog {
  time: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  details?: Record<string, any>;
}
