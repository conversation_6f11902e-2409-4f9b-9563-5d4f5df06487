// 案例征集活动系统类型定义

// 案例征集活动状态枚举
export enum CaseCollectionActivityStatus {
  DRAFT = 'draft',           // 草稿
  PUBLISHED = 'published',   // 已发布
  ACTIVE = 'active',         // 进行中
  ENDED = 'ended',           // 已结束
  CANCELLED = 'cancelled'    // 已取消
}

// 案例提交状态枚举
export enum CaseSubmissionStatus {
  DRAFT = 'draft',           // 草稿
  SUBMITTED = 'submitted',   // 已提交
  REVIEWING = 'reviewing',   // 审核中
  APPROVED = 'approved',     // 已通过
  REJECTED = 'rejected',     // 已驳回
  WITHDRAWN = 'withdrawn'    // 已撤回
}

// 案例预审状态枚举
export enum CaseReviewStatus {
  PENDING = 'pending',       // 待审核
  IN_PROGRESS = 'in_progress', // 审核中
  COMPLETED = 'completed',   // 已完成
  REJECTED = 'rejected'      // 已驳回
}

// 案例预审结果枚举
export enum CaseReviewResult {
  PASS = 1,                  // 通过
  REJECT = 2,                // 驳回
  REVISE = 3                 // 需修改
}

// 案例分类状态枚举
export enum CaseCategoryStatus {
  ACTIVE = 1,                // 启用
  INACTIVE = 2               // 停用
}

// 案例征集活动优先级枚举
export enum ActivityPriority {
  LOW = 1,                   // 低
  NORMAL = 2,                // 普通
  HIGH = 3,                  // 高
  URGENT = 4                 // 紧急
}

// 案例征集活动基础接口
export interface CaseCollectionActivityBase {
  title: string;
  description: string;
  content?: string;
  theme: string;
  rules: string;
  startTime: string;
  endTime: string;
  submitDeadline: string;
  maxSubmissions?: number;
  allowedFileTypes?: string[];
  maxFileSize?: number; // MB
  awards?: string[];
  totalPrize?: number;
  contactInfo?: ActivityContactInfo;
  requirements?: string[];
  tags?: string[];
  coverImage?: string;
  images?: string[];
  attachments?: string[];
}

// 案例征集活动实体接口
export interface CaseCollectionActivity extends CaseCollectionActivityBase {
  id: number;
  status: CaseCollectionActivityStatus;
  priority: ActivityPriority;
  currentSubmissions: number;
  viewCount: number;
  isPublic: boolean;
  organizerId: number;
  organizerName: string;
  regionId: number;
  createTime: string;
  updateTime: string;
  createUser: number;
  createUserName: string;
  publishTime?: string;
  metadata?: Record<string, any>;
}

// 活动联系信息接口
export interface ActivityContactInfo {
  name: string;
  phone?: string;
  email?: string;
  wechat?: string;
  department?: string;
}

// 案例提交基础接口
export interface CaseSubmissionBase {
  activityId: number;
  title: string;
  summary: string;
  content: string;
  submitterName: string;
  submitterDepartment: string;
  submitterContact: string;
  submitterEmail?: string;
  categoryId?: number;
  tags?: string[];
  coverImage?: string;
  images?: string[];
  attachments?: CaseAttachment[];
}

// 案例提交实体接口
export interface CaseSubmission extends CaseSubmissionBase {
  id: number;
  activityTitle: string;
  status: CaseSubmissionStatus;
  submitTime: string;
  updateTime: string;
  reviewTime?: string;
  reviewerId?: number;
  reviewerName?: string;
  reviewComments?: string;
  reviewScore?: number;
  isQualified: boolean;
  viewCount: number;
  downloadCount: number;
  organizationId: number;
  regionId: number;
  createTime: string;
  createUser: number;
  metadata?: Record<string, any>;
}

// 案例预审基础接口
export interface CaseReviewBase {
  submissionId: number;
  reviewerId: number;
  reviewerName: string;
  reviewResult: CaseReviewResult;
  score: number;
  comments: string;
  suggestions?: string;
  reviewTime: string;
}

// 案例预审实体接口
export interface CaseReview extends CaseReviewBase {
  id: number;
  activityId: number;
  activityTitle: string;
  submissionTitle: string;
  submitterName: string;
  status: CaseReviewStatus;
  isPass: boolean;
  nextStep?: string;
  attachments?: string[];
  createTime: string;
  updateTime: string;
  metadata?: Record<string, any>;
}

// 案例分类基础接口
export interface CaseCategoryBase {
  name: string;
  description?: string;
  parentId?: number;
  sortOrder: number;
  icon?: string;
  color?: string;
}

// 案例分类实体接口
export interface CaseCategory extends CaseCategoryBase {
  id: number;
  level: number;
  path: string;
  status: CaseCategoryStatus;
  usageCount: number;
  createTime: string;
  updateTime: string;
  createUser: number;
  children?: CaseCategory[];
}

// 案例附件接口
export interface CaseAttachment {
  id?: number;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadTime: string;
  description?: string;
}

// 案例征集活动查询参数接口
export interface CaseCollectionActivityQueryParams {
  keyword?: string;
  status?: CaseCollectionActivityStatus;
  priority?: ActivityPriority;
  organizerId?: number;
  startDate?: string;
  endDate?: string;
  tags?: string[];
  isPublic?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 案例提交查询参数接口
export interface CaseSubmissionQueryParams {
  activityId?: number;
  keyword?: string;
  status?: CaseSubmissionStatus;
  submitterName?: string;
  submitterDepartment?: string;
  categoryId?: number;
  tags?: string[];
  startDate?: string;
  endDate?: string;
  isQualified?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 案例预审查询参数接口
export interface CaseReviewQueryParams {
  activityId?: number;
  submissionId?: number;
  reviewerId?: number;
  status?: CaseReviewStatus;
  reviewResult?: CaseReviewResult;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 案例分类查询参数接口
export interface CaseCategoryQueryParams {
  keyword?: string;
  parentId?: number;
  level?: number;
  status?: CaseCategoryStatus;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 案例征集活动统计接口
export interface CaseCollectionStatistics {
  totalActivities: number;
  activeActivities: number;
  endedActivities: number;
  totalSubmissions: number;
  pendingReviews: number;
  approvedSubmissions: number;
  rejectedSubmissions: number;
  averageScore: number;
  submissionsByActivity: Record<number, number>;
  submissionsByStatus: Record<CaseSubmissionStatus, number>;
  reviewsByResult: Record<CaseReviewResult, number>;
  recentActivities: CaseCollectionActivity[];
  popularActivities: CaseCollectionActivity[];
  topSubmitters: SubmitterStats[];
}

// 提交者统计接口
export interface SubmitterStats {
  submitterName: string;
  submitterDepartment: string;
  totalSubmissions: number;
  approvedSubmissions: number;
  averageScore: number;
  successRate: number;
}

// 活动进度接口
export interface ActivityProgress {
  activityId: number;
  activityTitle: string;
  totalSubmissions: number;
  pendingReviews: number;
  approvedSubmissions: number;
  rejectedSubmissions: number;
  completionRate: number;
  averageScore: number;
  remainingDays: number;
  isActive: boolean;
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp: string;
}

// 分页响应接口
export interface PageResult<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 操作结果接口
export interface OperationResult {
  success: boolean;
  message: string;
  data?: any;
  errors?: string[];
}

// ==================== 状态映射和选项配置 ====================

// 案例征集活动状态映射
export const CaseCollectionActivityStatusMap = {
  [CaseCollectionActivityStatus.DRAFT]: '草稿',
  [CaseCollectionActivityStatus.PUBLISHED]: '已发布',
  [CaseCollectionActivityStatus.ACTIVE]: '进行中',
  [CaseCollectionActivityStatus.ENDED]: '已结束',
  [CaseCollectionActivityStatus.CANCELLED]: '已取消'
} as const;

// 案例征集活动状态颜色映射
export const CaseCollectionActivityStatusColorMap = {
  [CaseCollectionActivityStatus.DRAFT]: 'orange',
  [CaseCollectionActivityStatus.PUBLISHED]: 'blue',
  [CaseCollectionActivityStatus.ACTIVE]: 'green',
  [CaseCollectionActivityStatus.ENDED]: 'default',
  [CaseCollectionActivityStatus.CANCELLED]: 'red'
} as const;

// 案例征集活动状态选项
export const CaseCollectionActivityStatusOptions = [
  { label: '草稿', value: CaseCollectionActivityStatus.DRAFT },
  { label: '已发布', value: CaseCollectionActivityStatus.PUBLISHED },
  { label: '进行中', value: CaseCollectionActivityStatus.ACTIVE },
  { label: '已结束', value: CaseCollectionActivityStatus.ENDED },
  { label: '已取消', value: CaseCollectionActivityStatus.CANCELLED }
] as const;

// 案例提交状态映射
export const CaseSubmissionStatusMap = {
  [CaseSubmissionStatus.DRAFT]: '草稿',
  [CaseSubmissionStatus.SUBMITTED]: '已提交',
  [CaseSubmissionStatus.REVIEWING]: '审核中',
  [CaseSubmissionStatus.APPROVED]: '已通过',
  [CaseSubmissionStatus.REJECTED]: '已驳回',
  [CaseSubmissionStatus.WITHDRAWN]: '已撤回'
} as const;

// 案例提交状态颜色映射
export const CaseSubmissionStatusColorMap = {
  [CaseSubmissionStatus.DRAFT]: 'orange',
  [CaseSubmissionStatus.SUBMITTED]: 'blue',
  [CaseSubmissionStatus.REVIEWING]: 'processing',
  [CaseSubmissionStatus.APPROVED]: 'green',
  [CaseSubmissionStatus.REJECTED]: 'red',
  [CaseSubmissionStatus.WITHDRAWN]: 'default'
} as const;

// 案例提交状态选项
export const CaseSubmissionStatusOptions = [
  { label: '草稿', value: CaseSubmissionStatus.DRAFT },
  { label: '已提交', value: CaseSubmissionStatus.SUBMITTED },
  { label: '审核中', value: CaseSubmissionStatus.REVIEWING },
  { label: '已通过', value: CaseSubmissionStatus.APPROVED },
  { label: '已驳回', value: CaseSubmissionStatus.REJECTED },
  { label: '已撤回', value: CaseSubmissionStatus.WITHDRAWN }
] as const;

// 案例预审状态映射
export const CaseReviewStatusMap = {
  [CaseReviewStatus.PENDING]: '待审核',
  [CaseReviewStatus.IN_PROGRESS]: '审核中',
  [CaseReviewStatus.COMPLETED]: '已完成',
  [CaseReviewStatus.REJECTED]: '已驳回'
} as const;

// 案例预审状态颜色映射
export const CaseReviewStatusColorMap = {
  [CaseReviewStatus.PENDING]: 'orange',
  [CaseReviewStatus.IN_PROGRESS]: 'processing',
  [CaseReviewStatus.COMPLETED]: 'green',
  [CaseReviewStatus.REJECTED]: 'red'
} as const;

// 案例预审结果映射
export const CaseReviewResultMap = {
  [CaseReviewResult.PASS]: '通过',
  [CaseReviewResult.REJECT]: '驳回',
  [CaseReviewResult.REVISE]: '需修改'
} as const;

// 案例预审结果颜色映射
export const CaseReviewResultColorMap = {
  [CaseReviewResult.PASS]: 'green',
  [CaseReviewResult.REJECT]: 'red',
  [CaseReviewResult.REVISE]: 'orange'
} as const;

// 案例预审结果选项
export const CaseReviewResultOptions = [
  { label: '通过', value: CaseReviewResult.PASS },
  { label: '驳回', value: CaseReviewResult.REJECT },
  { label: '需修改', value: CaseReviewResult.REVISE }
] as const;

// 活动优先级映射
export const ActivityPriorityMap = {
  [ActivityPriority.LOW]: '低',
  [ActivityPriority.NORMAL]: '普通',
  [ActivityPriority.HIGH]: '高',
  [ActivityPriority.URGENT]: '紧急'
} as const;

// 活动优先级颜色映射
export const ActivityPriorityColorMap = {
  [ActivityPriority.LOW]: 'default',
  [ActivityPriority.NORMAL]: 'blue',
  [ActivityPriority.HIGH]: 'orange',
  [ActivityPriority.URGENT]: 'red'
} as const;

// 活动优先级选项
export const ActivityPriorityOptions = [
  { label: '低', value: ActivityPriority.LOW },
  { label: '普通', value: ActivityPriority.NORMAL },
  { label: '高', value: ActivityPriority.HIGH },
  { label: '紧急', value: ActivityPriority.URGENT }
] as const;

// 案例分类状态映射
export const CaseCategoryStatusMap = {
  [CaseCategoryStatus.ACTIVE]: '启用',
  [CaseCategoryStatus.INACTIVE]: '停用'
} as const;

// 案例分类状态颜色映射
export const CaseCategoryStatusColorMap = {
  [CaseCategoryStatus.ACTIVE]: 'green',
  [CaseCategoryStatus.INACTIVE]: 'red'
} as const;

// 案例分类状态选项
export const CaseCategoryStatusOptions = [
  { label: '启用', value: CaseCategoryStatus.ACTIVE },
  { label: '停用', value: CaseCategoryStatus.INACTIVE }
] as const;

// ==================== 工具函数 ====================

// 获取案例征集活动状态文本
export function getCaseCollectionActivityStatusText(status: CaseCollectionActivityStatus): string {
  return CaseCollectionActivityStatusMap[status] || '未知';
}

// 获取案例征集活动状态颜色
export function getCaseCollectionActivityStatusColor(status: CaseCollectionActivityStatus): string {
  return CaseCollectionActivityStatusColorMap[status] || 'default';
}

// 获取案例提交状态文本
export function getCaseSubmissionStatusText(status: CaseSubmissionStatus): string {
  return CaseSubmissionStatusMap[status] || '未知';
}

// 获取案例提交状态颜色
export function getCaseSubmissionStatusColor(status: CaseSubmissionStatus): string {
  return CaseSubmissionStatusColorMap[status] || 'default';
}

// 获取案例预审状态文本
export function getCaseReviewStatusText(status: CaseReviewStatus): string {
  return CaseReviewStatusMap[status] || '未知';
}

// 获取案例预审状态颜色
export function getCaseReviewStatusColor(status: CaseReviewStatus): string {
  return CaseReviewStatusColorMap[status] || 'default';
}

// 获取案例预审结果文本
export function getCaseReviewResultText(result: CaseReviewResult): string {
  return CaseReviewResultMap[result] || '未知';
}

// 获取案例预审结果颜色
export function getCaseReviewResultColor(result: CaseReviewResult): string {
  return CaseReviewResultColorMap[result] || 'default';
}

// 获取活动优先级文本
export function getActivityPriorityText(priority: ActivityPriority): string {
  return ActivityPriorityMap[priority] || '未知';
}

// 获取活动优先级颜色
export function getActivityPriorityColor(priority: ActivityPriority): string {
  return ActivityPriorityColorMap[priority] || 'default';
}

// 获取案例分类状态文本
export function getCaseCategoryStatusText(status: CaseCategoryStatus): string {
  return CaseCategoryStatusMap[status] || '未知';
}

// 获取案例分类状态颜色
export function getCaseCategoryStatusColor(status: CaseCategoryStatus): string {
  return CaseCategoryStatusColorMap[status] || 'default';
}

// 判断活动是否可以提交案例
export function canSubmitCase(activity: CaseCollectionActivity): boolean {
  const now = new Date();
  const endTime = new Date(activity.submitDeadline);
  return activity.status === CaseCollectionActivityStatus.ACTIVE && now <= endTime;
}

// 判断活动是否已过期
export function isActivityExpired(activity: CaseCollectionActivity): boolean {
  const now = new Date();
  const endTime = new Date(activity.endTime);
  return now > endTime;
}

// 计算活动剩余天数
export function getActivityRemainingDays(activity: CaseCollectionActivity): number {
  const now = new Date();
  const endTime = new Date(activity.submitDeadline);
  const diffTime = endTime.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// 计算案例提交完成率
export function getSubmissionCompletionRate(activity: CaseCollectionActivity): number {
  if (!activity.maxSubmissions || activity.maxSubmissions === 0) {
    return 0;
  }
  return Math.round((activity.currentSubmissions / activity.maxSubmissions) * 100);
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 验证文件类型
export function isValidFileType(fileName: string, allowedTypes: string[]): boolean {
  if (!allowedTypes || allowedTypes.length === 0) return true;
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  return allowedTypes.some(type => type.toLowerCase().includes(fileExtension || ''));
}

// 验证文件大小
export function isValidFileSize(fileSize: number, maxSize: number): boolean {
  const maxSizeBytes = maxSize * 1024 * 1024; // 转换为字节
  return fileSize <= maxSizeBytes;
}

// 生成案例编号
export function generateCaseNumber(activityId: number, submissionId: number): string {
  const timestamp = Date.now().toString().slice(-6);
  return `CASE-${activityId}-${submissionId}-${timestamp}`;
}

// 计算审核进度百分比
export function getReviewProgress(total: number, completed: number): number {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

// ==================== 审核管理相关类型定义 ====================

// 审核历史记录接口
export interface ReviewHistoryRecord {
  id: number;
  submissionId: number;
  submissionTitle: string;
  activityId: number;
  activityTitle: string;
  reviewerName: string;
  reviewTime: string;
  reviewResult: CaseReviewResult;
  score: number;
  comments: string;
  suggestions?: string;
  submitterName: string;
  department: string;
  createTime?: string;
  updateTime?: string;
}

// 审核历史搜索参数接口
export interface ReviewHistorySearchParams {
  page?: number;
  pageSize?: number;
  activityId?: number;
  reviewResult?: CaseReviewResult;
  reviewerName?: string;
  dateRange?: [string, string];
  keyword?: string;
  submitterName?: string;
  department?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 审核统计数据接口
export interface ReviewStatistics {
  totalReviews: number;
  todayReviews: number;
  pendingReviews: number;
  approvedCount: number;
  rejectedCount: number;
  needRevisionCount: number;
  approvalRate: number;
  averageScore: number;
  averageReviewTime: number; // 小时
  reviewerStats: ReviewerStatistics[];
  monthlyTrend: MonthlyReviewTrend[];
  scoreDistribution: ScoreDistribution[];
}

// 审核员统计接口
export interface ReviewerStatistics {
  reviewerName: string;
  reviewCount: number;
  averageScore: number;
  approvalRate?: number;
  averageReviewTime?: number;
}

// 月度审核趋势接口
export interface MonthlyReviewTrend {
  month: string;
  reviews: number;
  approvalRate: number;
  averageScore?: number;
}

// 评分分布接口
export interface ScoreDistribution {
  range: string;
  count: number;
  percentage?: number;
}

// 审核设置配置接口
export interface ReviewSettings {
  id: number;
  autoAssignReviewer: boolean;
  maxReviewTime: number; // 小时
  requireDoubleReview: boolean;
  passingScore: number;
  excellentScore: number;
  notificationEnabled: boolean;
  emailNotification: boolean;
  smsNotification: boolean;
  reviewRules: ReviewRules;
  scoringCriteria: ScoringCriteria[];
  updateTime: string;
  updatedBy: string;
}

// 审核规则接口
export interface ReviewRules {
  allowSelfReview: boolean;
  requireExpertReview: boolean;
  maxReviewsPerDay: number;
  autoRejectLowScore?: boolean;
  autoApproveHighScore?: boolean;
  requireReviewComments?: boolean;
}

// 评分标准接口
export interface ScoringCriteria {
  name: string;
  weight: number;
  description: string;
  minScore?: number;
  maxScore?: number;
}

// 审核模板类型枚举
export enum ReviewTemplateType {
  APPROVE = 'approve',
  REJECT_INCOMPLETE = 'reject_incomplete',
  REJECT_QUALITY = 'reject_quality',
  REVISE_FORMAT = 'revise_format',
  REVISE_CONTENT = 'revise_content',
  CUSTOM = 'custom'
}

// 审核模板接口
export interface ReviewTemplate {
  id: number;
  name: string;
  type: ReviewTemplateType;
  reviewResult: CaseReviewResult;
  score: number;
  comments: string;
  suggestions?: string;
  isDefault: boolean;
  createTime: string;
  updateTime: string;
  createUser?: number;
  createUserName?: string;
  usageCount?: number;
}

// 审核模板创建/更新参数接口
export interface ReviewTemplateParams {
  id?: number;
  name: string;
  type: ReviewTemplateType;
  reviewResult: CaseReviewResult;
  score: number;
  comments: string;
  suggestions?: string;
}

// ==================== 审核管理映射和选项配置 ====================

// 审核模板类型映射
export const ReviewTemplateTypeMap = {
  [ReviewTemplateType.APPROVE]: '通过模板',
  [ReviewTemplateType.REJECT_INCOMPLETE]: '驳回-材料不完整',
  [ReviewTemplateType.REJECT_QUALITY]: '驳回-质量不达标',
  [ReviewTemplateType.REVISE_FORMAT]: '修改-格式问题',
  [ReviewTemplateType.REVISE_CONTENT]: '修改-内容问题',
  [ReviewTemplateType.CUSTOM]: '自定义模板'
} as const;

// 审核模板类型颜色映射
export const ReviewTemplateTypeColorMap = {
  [ReviewTemplateType.APPROVE]: 'green',
  [ReviewTemplateType.REJECT_INCOMPLETE]: 'red',
  [ReviewTemplateType.REJECT_QUALITY]: 'red',
  [ReviewTemplateType.REVISE_FORMAT]: 'orange',
  [ReviewTemplateType.REVISE_CONTENT]: 'orange',
  [ReviewTemplateType.CUSTOM]: 'blue'
} as const;

// 审核模板类型选项
export const ReviewTemplateTypeOptions = [
  { label: '通过模板', value: ReviewTemplateType.APPROVE },
  { label: '驳回-材料不完整', value: ReviewTemplateType.REJECT_INCOMPLETE },
  { label: '驳回-质量不达标', value: ReviewTemplateType.REJECT_QUALITY },
  { label: '修改-格式问题', value: ReviewTemplateType.REVISE_FORMAT },
  { label: '修改-内容问题', value: ReviewTemplateType.REVISE_CONTENT },
  { label: '自定义模板', value: ReviewTemplateType.CUSTOM }
] as const;

// ==================== 审核管理工具函数 ====================

// 获取审核模板类型文本
export function getReviewTemplateTypeText(type: ReviewTemplateType): string {
  return ReviewTemplateTypeMap[type] || '未知';
}

// 获取审核模板类型颜色
export function getReviewTemplateTypeColor(type: ReviewTemplateType): string {
  return ReviewTemplateTypeColorMap[type] || 'default';
}

// 计算审核通过率
export function calculateApprovalRate(approved: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((approved / total) * 100 * 10) / 10; // 保留一位小数
}

// 计算平均审核时间（小时）
export function calculateAverageReviewTime(totalTime: number, reviewCount: number): number {
  if (reviewCount === 0) return 0;
  return Math.round((totalTime / reviewCount) * 10) / 10; // 保留一位小数
}

// 格式化审核时间显示
export function formatReviewTime(hours: number): string {
  if (hours < 1) {
    return `${Math.round(hours * 60)}分钟`;
  } else if (hours < 24) {
    return `${Math.round(hours * 10) / 10}小时`;
  } else {
    const days = Math.floor(hours / 24);
    const remainingHours = Math.round((hours % 24) * 10) / 10;
    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`;
  }
}

// 获取评分等级
export function getScoreGrade(score: number): { grade: string; color: string } {
  if (score >= 90) {
    return { grade: '优秀', color: 'green' };
  } else if (score >= 80) {
    return { grade: '良好', color: 'blue' };
  } else if (score >= 70) {
    return { grade: '中等', color: 'orange' };
  } else if (score >= 60) {
    return { grade: '及格', color: 'yellow' };
  } else {
    return { grade: '不及格', color: 'red' };
  }
}

// 判断是否需要专家审核
export function requiresExpertReview(score: number, settings: ReviewSettings): boolean {
  return settings.reviewRules.requireExpertReview && score >= settings.excellentScore;
}

// 判断是否自动通过
export function shouldAutoApprove(score: number, settings: ReviewSettings): boolean {
  return settings.reviewRules.autoApproveHighScore === true && score >= settings.excellentScore;
}

// 判断是否自动驳回
export function shouldAutoReject(score: number, settings: ReviewSettings): boolean {
  return settings.reviewRules.autoRejectLowScore === true && score < settings.passingScore;
}

// 生成审核编号
export function generateReviewNumber(submissionId: number, reviewerId: number): string {
  const timestamp = Date.now().toString().slice(-6);
  return `REV-${submissionId}-${reviewerId}-${timestamp}`;
}
