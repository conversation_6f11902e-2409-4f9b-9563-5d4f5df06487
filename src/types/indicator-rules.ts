// 指标规则管理相关类型定义

// 数据来源类型
export enum DataSourceType {
  TASK_DATA = 1,           // 任务数据
  SURVEY_DATA = 2,         // 问卷调查数据
  VOTE_DATA = 3,           // 投票数据
  OTHER_DATA = 4           // 其他数据资源
}

// 转换类型
export enum ConversionType {
  TASK_TIMELINESS = 1,     // 任务完成及时性
  TASK_EVALUATION = 2,     // 任务评价等次
  RANKING_DATA = 3,        // 排名数据
  PARTICIPATION_RATE = 4,  // 活动参与率
  SURVEY_RESULT = 5,       // 调查问卷结果
  VOTE_RESULT = 6          // 投票结果
}

// 指标状态
export enum IndicatorStatus {
  ACTIVE = 1,              // 启用
  INACTIVE = 2             // 停用
}

// 指标基本信息
export interface Indicator {
  id: string
  name: string                    // 指标名称
  description?: string            // 指标描述
  category?: string               // 指标类别（党建工作类、组织建设类等）
  dataSourceType: DataSourceType // 数据来源类型
  surveyTitle?: string           // 问卷标题（当数据来源为问卷时）
  voteTitle?: string             // 投票标题（当数据来源为投票时）
  dataLink?: string              // 相关数据链接
  dataFiles?: any[]              // 上传的相关文件列表
  conversionType: ConversionType // 转换类型
  conversionRules: ConversionRule // 转换规则
  status: IndicatorStatus        // 指标状态
  createTime: string             // 创建时间
  updateTime: string             // 更新时间
}

// 转换规则
export interface ConversionRule {
  type: ConversionType
  rules: Record<string, any>     // 具体的转换规则配置
}

// 任务完成及时性转换规则
export interface TaskTimelinessRule {
  onTimeScore: number            // 按时完成得分 (0-100)
  overdueScore: number           // 逾期完成得分 (0-100)
  incompleteScore: number        // 未完成得分 (0-100)
}

// 任务评价等次转换规则
export interface TaskEvaluationRule {
  excellent: number              // 优秀得分 (0-100)
  good: number                   // 良好得分 (0-100)
  average: number                // 一般得分 (0-100)
  poor: number                   // 较差得分 (0-100)
}

// 排名数据转换规则
export interface RankingRule {
  totalCount: number             // 总数量
  rankingFormula: string         // 排名计算公式
}

// 结果性分值数据转换规则
export interface ResultScoreRule {
  maxOriginalScore: number       // 原始分值最大值
  scoreFormula: string           // 转换公式 (linear, percentage)
  minValidScore: number          // 最低有效分值
}

// 参与率转换规则
export interface ParticipationRateRule {
  participationFullScore: number    // 满分标准
  baseParticipationRate: number     // 基准参与率
  participationFormula: string      // 计算方式 (linear, weighted)
}

// 调查问卷结果转换规则
export interface SurveyResultRule {
  verySatisfied: number          // 非常满意得分
  satisfied: number              // 满意得分
  neutral: number                // 一般得分
  dissatisfied: number           // 不满意得分
  veryDissatisfied: number       // 非常不满意得分
}

// 投票结果转换规则
export interface VoteResultRule {
  voteType: string               // 投票类型 (ranking, scoring, approval)
  voteFormula: string            // 计算公式 (percentage, weighted, normalized)
}

// 指标权重配置
export interface IndicatorWeight {
  indicatorId: string            // 指标ID
  indicatorName: string          // 指标名称
  category?: string              // 指标类别
  weight: number                 // 权重值 (0-100)
  selected: boolean              // 是否选中
}

// 权重配置方案
export interface WeightScheme {
  id: string
  name: string                   // 方案名称
  description?: string           // 方案描述
  indicators: IndicatorWeight[]  // 指标权重列表
  totalWeight: number            // 总权重
  createTime: string             // 创建时间
  updateTime: string             // 更新时间
}

// 演算结果
export interface CalculationResult {
  schemeId: string               // 权重方案ID
  schemeName: string             // 权重方案名称
  results: Array<{
    organizationId: string       // 组织ID
    organizationName: string     // 组织名称
    totalScore: number           // 总分
    indicatorScores: Array<{     // 各指标得分
      indicatorId: string
      indicatorName: string
      score: number
      weight: number
      weightedScore: number
    }>
  }>
  calculateTime: string          // 计算时间
}

// 指标模板
export interface IndicatorTemplate {
  id: string
  name: string                   // 模板名称
  description?: string           // 模板描述
  indicators: string[]           // 包含的指标ID列表
  weights: IndicatorWeight[]     // 权重配置
  category?: string              // 模板分类
  isDefault: boolean             // 是否为默认模板
  createTime: string             // 创建时间
  updateTime: string             // 更新时间
}

// 查询参数
export interface IndicatorQuery {
  name?: string                  // 指标名称关键词
  category?: string              // 指标类别
  dataSourceType?: DataSourceType // 数据来源类型
  status?: IndicatorStatus       // 指标状态
  page: number                   // 页码
  pageSize: number               // 每页大小
}

// 分页结果
export interface PageResult<T> {
  data: T[]                      // 数据列表
  total: number                  // 总数
  page: number                   // 当前页
  pageSize: number               // 每页大小
}

// 现代化转换规则配置 - 匹配ConversionRulesConfig组件
export type ConversionRulesConfig = {
  // 任务完成及时性 (type=1)
  onTimeScore?: number
  overdueScore?: number
  incompleteScore?: number
  
  // 任务评价等次 (type=2)
  evaluationLevels?: Array<{
    name: string
    score: number
  }>
  
  // 排名数据 (type=3)
  conversionMethod?: 'linear' | 'segmented'
  firstPlaceScore?: number
  lastPlaceScore?: number
  rankSegments?: Array<{
    minRank: number
    maxRank: number
    score: number
  }>
  
  // 活动参与率 (type=4)
  mappingType?: 'linear' | 'custom'
  participationRanges?: Array<{
    minRate: number
    maxRate: number
    score: number
  }>
  
  // 调查问卷结果 (type=5)
  calculationMethod?: 'average' | 'weighted'
  optionScores?: Array<{
    label: string
    score: number
  }>
  
  // 投票结果 (type=6)
  voteCalculationBasis?: 'rate' | 'absolute'
  voteOptions?: Array<{
    label: string
    score: number
  }>
  
  // 扩展字段
  [key: string]: any
}

// 表单数据
export interface IndicatorForm {
  id?: string
  name: string
  description?: string
  category?: string              // 指标类别
  dataSourceType?: DataSourceType | number
  surveyTitle?: string
  voteTitle?: string
  dataLink?: string              // 数据链接
  dataFiles?: any[]              // 上传文件
  conversionType?: ConversionType | number
  conversionRules: ConversionRulesConfig | Record<string, any> // 转换规则配置，支持多种类型
  dataSourceConfig: DataSourceConfigType | Record<string, any> // 数据源详细配置
  status: IndicatorStatus | number
}

// 数据源配置类型
export interface DataSourceConfigType {
  // 任务数据配置 (type=1)
  taskType?: string[]
  dateRange?: [string, string]
  taskSources?: string[]
  
  // 问卷调查数据配置 (type=2)
  surveyId?: string
  statisticDimension?: 'overall' | 'question' | 'category'
  dataFilters?: string[]
  
  // 投票数据配置 (type=3)
  voteId?: string
  statisticMethod?: 'percentage' | 'absolute' | 'weighted'
  validityFilters?: string[]
  
  // 其他数据资源配置 (type=4)
  sourceType?: 'upload' | 'api' | 'database'
  dataFiles?: any[]
  fieldMappings?: Array<{
    sourceField: string
    targetField: string
    fieldType: string
  }>
  apiUrl?: string
  apiMethod?: string
  apiHeaders?: string
  databaseId?: string
  sqlQuery?: string
  
  // 扩展字段
  [key: string]: any
}

// 权重设置表单
export interface WeightForm {
  name: string
  description?: string
  selectedIndicators: string[]
  weights: Record<string, number>
}

// 模板表单
export interface TemplateForm {
  name: string
  description?: string
  selectedIndicators: string[]
  weights: Record<string, number>
  category?: string
  isDefault: boolean
}
