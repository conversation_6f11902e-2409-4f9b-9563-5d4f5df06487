// 案例推广系统类型定义

// 案例状态类型
export type CaseStatus = 1 | 2; // 1-草稿, 2-已发布

// 分类类型
export type CategoryType = 1 | 2 | 3 | 4; // 1-类型, 2-行业, 3-难度, 4-解决方案

// 标签类型
export type TagType = 1 | 2 | 3 | 4; // 1-自定义, 2-类型, 3-行业, 4-难度

// 难度等级
export type DifficultyLevel = 1 | 2 | 3; // 1-简单, 2-中等, 3-困难

// 访问权限级别
export type AccessLevel = 1 | 2 | 3; // 1-公开, 2-内部, 3-私有

// 案例基础表单接口
export interface CaseForm {
  id?: number;
  title: string;
  summary: string;
  content: string;
  source: string;
  category: number;
  tags: number[];
  difficulty: DifficultyLevel;
  industry?: number;
  status?: CaseStatus;
  coverImage?: string;
  images?: string[];
  attachments?: string[];
}

// 案例实体接口
export interface CaseEntity extends CaseForm {
  status: CaseStatus;
  publishTime?: string;
  createTime: string;
  updateTime: string;
  createUser: number;
  organizationId: number;
  regionId: number;
  viewCount?: number;
  downloadCount?: number;
}

// 分类实体接口
export interface CategoryEntity {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  type: CategoryType;
  sort: number;
  level: number;
  isEnabled: boolean;
  createTime: string;
  updateTime: string;
}

// 标签实体接口
export interface TagEntity {
  id: number;
  name: string;
  color: string;
  description?: string;
  type: TagType;
  usageCount: number;
  isEnabled: boolean;
  createTime: string;
  updateTime: string;
}

// 案例库实体接口
export interface CaseLibraryEntity {
  id: number;
  name: string;
  description?: string;
  accessLevel: AccessLevel;
  allowedUsers?: number[];
  allowedRoles?: string[];
  settings?: Record<string, any>;
  isEnabled: boolean;
  createTime: string;
  updateTime: string;
  createUser: number;
}

// 文件附件接口
export interface CaseAttachment {
  id: number;
  name: string;
  url: string;
  size: number;
  type: string;
  uploadTime: string;
}

// 搜索参数接口
export interface CaseSearchParams {
  title?: string;
  category?: number;
  tags?: number[];
  difficulty?: DifficultyLevel;
  status?: CaseStatus;
  source?: string;
  startTime?: string;
  endTime?: string;
  keyword?: string;
}

// 案例状态映射
export const CaseStatusMap = {
  1: '草稿',
  2: '已发布'
} as const;

// 案例状态颜色映射
export const CaseStatusColorMap = {
  1: 'orange',
  2: 'green'
} as const;

// 案例状态选项
export const CaseStatusOptions = [
  { label: '草稿', value: 1 },
  { label: '已发布', value: 2 }
] as const;

// 分类类型映射
export const CategoryTypeMap = {
  1: '类型',
  2: '行业', 
  3: '难度',
  4: '解决方案'
} as const;

// 分类类型选项
export const CategoryTypeOptions = [
  { label: '类型', value: 1 },
  { label: '行业', value: 2 },
  { label: '难度', value: 3 },
  { label: '解决方案', value: 4 }
] as const;

// 标签类型映射
export const TagTypeMap = {
  1: '自定义',
  2: '类型',
  3: '行业',
  4: '难度'
} as const;

// 标签类型选项
export const TagTypeOptions = [
  { label: '自定义', value: 1 },
  { label: '类型', value: 2 },
  { label: '行业', value: 3 },
  { label: '难度', value: 4 }
] as const;

// 难度等级映射
export const DifficultyLevelMap = {
  1: '简单',
  2: '中等',
  3: '困难'
} as const;

// 难度等级颜色映射
export const DifficultyLevelColorMap = {
  1: 'green',
  2: 'orange',
  3: 'red'
} as const;

// 难度等级选项
export const DifficultyLevelOptions = [
  { label: '简单', value: 1 },
  { label: '中等', value: 2 },
  { label: '困难', value: 3 }
] as const;

// 访问权限级别映射
export const AccessLevelMap = {
  1: '公开',
  2: '内部',
  3: '私有'
} as const;

// 访问权限级别颜色映射
export const AccessLevelColorMap = {
  1: 'green',
  2: 'blue',
  3: 'red'
} as const;

// 访问权限级别选项
export const AccessLevelOptions = [
  { label: '公开', value: 1 },
  { label: '内部', value: 2 },
  { label: '私有', value: 3 }
] as const;

// 预定义分类选项（类型维度）
export const CaseTypeOptions = [
  { label: '党建工作', value: 1 },
  { label: '创新实践', value: 2 },
  { label: '服务民生', value: 3 },
  { label: '效能提升', value: 4 },
  { label: '其他', value: 5 }
] as const;

// 预定义行业选项
export const IndustryOptions = [
  { label: '政府机关', value: 1 },
  { label: '事业单位', value: 2 },
  { label: '国有企业', value: 3 },
  { label: '民营企业', value: 4 },
  { label: '社会组织', value: 5 },
  { label: '其他', value: 6 }
] as const;

// 工具函数：获取状态文本
export function getCaseStatusText(status: CaseStatus): string {
  return CaseStatusMap[status] || '未知';
}

// 工具函数：获取状态颜色
export function getCaseStatusColor(status: CaseStatus): string {
  return CaseStatusColorMap[status] || 'default';
}

// 工具函数：获取难度等级文本
export function getDifficultyLevelText(level: DifficultyLevel): string {
  return DifficultyLevelMap[level] || '未知';
}

// 工具函数：获取难度等级颜色
export function getDifficultyLevelColor(level: DifficultyLevel): string {
  return DifficultyLevelColorMap[level] || 'default';
}

// 工具函数：获取分类类型文本
export function getCategoryTypeText(type: CategoryType): string {
  return CategoryTypeMap[type] || '未知';
}

// 工具函数：获取标签类型文本
export function getTagTypeText(type: TagType): string {
  return TagTypeMap[type] || '未知';
}

// 工具函数：获取访问权限文本
export function getAccessLevelText(level: AccessLevel): string {
  return AccessLevelMap[level] || '未知';
}

// 工具函数：获取访问权限颜色
export function getAccessLevelColor(level: AccessLevel): string {
  return AccessLevelColorMap[level] || 'default';
}
