// 渝理面对面系统类型定义

// 纪实文章接口
export interface DocumentRecord {
  id: number;
  title: string; // 宣讲主题
  speaker: string; // 主讲人
  content: string; // 宣讲内容
  lectureTime: string; // 宣讲时间
  location: string; // 宣讲地点
  contactInfo?: string; // 联系方式
  summary?: string; // 简介
  coverImage?: string; // 示意图
  status: DocumentStatus; // 状态
  enableDownload?: boolean; // 是否允许下载
  downloadFormat?: string; // 下载格式
  publishTime?: string; // 发布时间
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  creator: string; // 创建人
  templateId?: number; // 使用的模板ID
  viewCount: number; // 查看次数
  downloadCount: number; // 下载次数
}

// 纪实模板接口
export interface DocumentTemplate {
  id: number;
  name: string; // 模板名称
  description: string; // 模板描述
  layout: TemplateLayout; // 布局设置
  style: TemplateStyle; // 样式设置
  placeholders: TemplatePlaceholder[]; // 占位符
  isShared: boolean; // 是否共享
  creator: string; // 创建人
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
  usageCount: number; // 使用次数
  category: TemplateCategory; // 模板分类
}

// 模板布局接口
export interface TemplateLayout {
  id: number;
  name: string;
  structure: LayoutStructure; // 布局结构
  textPosition: Position[]; // 文字位置
  imagePosition: Position[]; // 图片位置
  videoPosition: Position[]; // 视频位置
}

// 模板样式接口
export interface TemplateStyle {
  id: number;
  name: string;
  colors: ColorScheme; // 颜色方案
  fonts: FontSettings; // 字体设置
  spacing: SpacingSettings; // 间距设置
  borders: BorderSettings; // 边框设置
}

// 模板占位符接口
export interface TemplatePlaceholder {
  id: number;
  name: string;
  type: PlaceholderType; // 占位符类型
  position: Position; // 位置
  defaultValue?: string; // 默认值
  required: boolean; // 是否必填
  validation?: ValidationRule; // 验证规则
}

// 权限管理接口
export interface UserPermission {
  userId: number;
  userName: string;
  permissions: Permission[]; // 权限列表
  role: UserRole; // 用户角色
  department: string; // 所属部门
  createTime: string;
  updateTime: string;
}

// 统计数据接口
export interface StatisticsData {
  totalDocuments: number; // 纪实总数
  publishedDocuments: number; // 已发布数量
  draftDocuments: number; // 草稿数量
  totalSpeakers: number; // 主讲人总数
  totalTemplates: number; // 模板总数
  completionRate: number; // 完成率
  lectureCount: number; // 宣讲次数
  departmentStats: DepartmentStatistics[]; // 部门统计
  monthlyStats: MonthlyStatistics[]; // 月度统计
  speakerStats: SpeakerStatistics[]; // 主讲人统计
}

// 部门统计接口
export interface DepartmentStatistics {
  departmentName: string;
  totalDocuments: number;
  publishedDocuments: number;
  completionRate: number;
  lectureCount: number;
  activeSpeakers: number;
}

// 月度统计接口
export interface MonthlyStatistics {
  month: string;
  documentCount: number;
  lectureCount: number;
  speakerCount: number;
  completionRate: number;
}

// 主讲人统计接口
export interface SpeakerStatistics {
  speakerName: string;
  department: string;
  lectureCount: number;
  documentCount: number;
  totalViews: number;
  avgRating: number;
}

// 下载规则接口
export interface DownloadRule {
  id: number;
  name: string;
  description: string;
  allowedRoles: UserRole[]; // 允许的角色
  allowedDepartments: string[]; // 允许的部门
  fileFormats: FileFormat[]; // 允许的文件格式
  maxDownloads: number; // 最大下载次数
  isEnabled: boolean; // 是否启用
  createTime: string;
  updateTime: string;
}

// 枚举类型定义

// 纪实状态枚举
export type DocumentStatus = 1 | 2; // 1-草稿, 2-已发布

// 模板分类枚举
export type TemplateCategory = 1 | 2 | 3 | 4; // 1-标准模板, 2-会议模板, 3-活动模板, 4-自定义模板

// 用户角色枚举
export type UserRole = 1 | 2 | 3 | 4; // 1-管理员, 2-编辑者, 3-查看者, 4-访客

// 权限类型枚举
export type Permission = 1 | 2 | 3 | 4 | 5; // 1-创建, 2-编辑, 3-删除, 4-发布, 5-查看

// 占位符类型枚举
export type PlaceholderType = 1 | 2 | 3 | 4 | 5; // 1-文本, 2-图片, 3-视频, 4-日期, 5-链接

// 文件格式枚举
export type FileFormat = 1 | 2 | 3 | 4; // 1-PDF, 2-Word, 3-图片, 4-视频

// 布局结构枚举
export type LayoutStructure = 1 | 2 | 3 | 4; // 1-单栏, 2-双栏, 3-三栏, 4-自定义

// 辅助类型定义

// 位置接口
export interface Position {
  x: number;
  y: number;
  width: number;
  height: number;
}

// 颜色方案接口
export interface ColorScheme {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
}

// 字体设置接口
export interface FontSettings {
  titleFont: string;
  contentFont: string;
  titleSize: number;
  contentSize: number;
  lineHeight: number;
}

// 间距设置接口
export interface SpacingSettings {
  margin: number;
  padding: number;
  lineSpacing: number;
  paragraphSpacing: number;
}

// 边框设置接口
export interface BorderSettings {
  width: number;
  style: string;
  color: string;
  radius: number;
}

// 验证规则接口
export interface ValidationRule {
  required: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  message: string;
}

// 状态映射
export const DocumentStatusTextMap = {
  1: '草稿',
  2: '已发布'
} as const;

export const DocumentStatusColorMap = {
  1: 'default',
  2: 'success'
} as const;

// 状态选项
export const DocumentStatusOptions = [
  { label: '草稿', value: 1 },
  { label: '已发布', value: 2 }
] as const;

// 模板分类映射
export const TemplateCategoryTextMap = {
  1: '标准模板',
  2: '会议模板',
  3: '活动模板',
  4: '自定义模板'
} as const;

export const TemplateCategoryColorMap = {
  1: 'blue',
  2: 'green',
  3: 'orange',
  4: 'purple'
} as const;

// 用户角色映射
export const UserRoleTextMap = {
  1: '管理员',
  2: '编辑者',
  3: '查看者',
  4: '访客'
} as const;

export const UserRoleColorMap = {
  1: 'red',
  2: 'blue',
  3: 'green',
  4: 'default'
} as const;

// 权限映射
export const PermissionTextMap = {
  1: '创建',
  2: '编辑',
  3: '删除',
  4: '发布',
  5: '查看'
} as const;

// 搜索参数接口
export interface DocumentSearchParams {
  title?: string;
  speaker?: string;
  location?: string;
  content?: string;
  status?: DocumentStatus;
  dateRange?: [string, string];
  lectureTimeRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}

// 模板搜索参数接口
export interface TemplateSearchParams {
  name?: string;
  category?: TemplateCategory;
  creator?: string;
  isShared?: boolean;
  dateRange?: [string, string];
  pageNum?: number;
  pageSize?: number;
}
