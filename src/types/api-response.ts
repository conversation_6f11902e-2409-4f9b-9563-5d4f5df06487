// 统一API响应类型定义
// 用于所有Mock API接口，确保响应格式的一致性

/**
 * 数据源信息接口
 * 用于标识数据来源和状态
 */
export interface DataSourceInfo {
  /** 是否来自真实API */
  isFromAPI: boolean
  /** 数据源类型 */
  source: 'api' | 'fallback' | 'mock'
  /** 响应时间戳 */
  timestamp: string
  /** 错误信息 (仅在降级时提供) */
  error?: string
}

/**
 * 统一API响应格式
 * 包含数据和数据源信息
 */
export interface ApiResponseWithSource<T> {
  /** 响应数据 */
  data: T
  /** 数据源信息 */
  dataSource: DataSourceInfo
}

/**
 * 创建降级数据源信息
 * @param error 错误信息
 * @returns 降级数据源信息
 */
export const createFallbackDataSource = (error?: string): DataSourceInfo => ({
  isFromAPI: false,
  source: 'fallback',
  timestamp: new Date().toISOString(),
  error
})

/**
 * 创建API数据源信息
 * @returns API数据源信息
 */
export const createAPIDataSource = (): DataSourceInfo => ({
  isFromAPI: true,
  source: 'api',
  timestamp: new Date().toISOString()
})

/**
 * 创建Mock数据源信息
 * @returns Mock数据源信息
 */
export const createMockDataSource = (): DataSourceInfo => ({
  isFromAPI: true, // Mock API也视为真实API
  source: 'mock',
  timestamp: new Date().toISOString()
})

/**
 * 标准API响应包装器
 * 用于包装任何数据为统一的API响应格式
 */
export const createApiResponse = <T>(
  data: T,
  dataSource: DataSourceInfo
): ApiResponseWithSource<T> => ({
  data,
  dataSource
})

/**
 * 成功响应快捷方法
 * @param data 响应数据
 * @returns API响应对象
 */
export const createSuccessResponse = <T>(data: T): ApiResponseWithSource<T> =>
  createApiResponse(data, createAPIDataSource())

/**
 * Mock响应快捷方法
 * @param data 响应数据
 * @returns Mock API响应对象
 */
export const createMockResponse = <T>(data: T): ApiResponseWithSource<T> =>
  createApiResponse(data, createMockDataSource())

/**
 * 降级响应快捷方法
 * @param data 降级数据
 * @param error 错误信息
 * @returns 降级响应对象
 */
export const createFallbackResponse = <T>(data: T, error?: string): ApiResponseWithSource<T> =>
  createApiResponse(data, createFallbackDataSource(error))

/**
 * API错误处理类型
 */
export interface ApiError {
  code: string
  message: string
  details?: any
}

/**
 * 标准错误响应格式
 */
export interface ApiErrorResponse {
  success: false
  error: ApiError
  timestamp: string
}

/**
 * 创建错误响应
 * @param code 错误代码
 * @param message 错误消息
 * @param details 错误详情
 * @returns 错误响应对象
 */
export const createErrorResponse = (
  code: string,
  message: string,
  details?: any
): ApiErrorResponse => ({
  success: false,
  error: { code, message, details },
  timestamp: new Date().toISOString()
})