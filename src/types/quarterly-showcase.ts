// 季度亮晒系统类型定义

// 培育对象信息
export interface CultivationTarget {
  id: string
  name: string // 姓名
  department: string // 部门
  position: string // 职位
  avatar?: string // 头像
}

// 项目信息
export interface Project {
  id: string
  name: string // 项目名称
  category: string // 项目类别
  description?: string // 项目描述
  weight: number // 权重
}

// 榜单项目
export interface RankingItem {
  id: string
  rank: number // 排名
  target: CultivationTarget // 培育对象
  project: Project // 项目
  score: number // 分数
  quarterlyScore: number // 季度得分
  yearlyScore: number // 年度得分
  trend: 'up' | 'down' | 'stable' // 趋势
  trendValue: number // 趋势变化值
  lastQuarterRank?: number // 上季度排名
  details?: ScoreDetail[] // 详细分数
}

// 分数详情
export interface ScoreDetail {
  id: string
  itemName: string // 评分项目名称
  score: number // 得分
  fullScore: number // 满分
  weight: number // 权重
  description?: string // 说明
  evidences?: Evidence[] // 证据材料
}

// 证据材料
export interface Evidence {
  id: string
  type: 'document' | 'image' | 'video' | 'link' // 类型
  name: string // 名称
  url: string // 链接
  uploadTime: string // 上传时间
}

// 榜单类型
export interface RankingType {
  id: string
  name: string // 榜单名称
  description: string // 描述
  type: 'total' | 'single' // 总分榜/单项榜
  sortOrder: 'asc' | 'desc' // 排序方式
  isActive: boolean // 是否启用
}

// 搜索筛选条件
export interface SearchFilters {
  keyword?: string // 关键词
  department?: string // 部门
  project?: string // 项目
  quarter?: string // 季度
  rankRange?: [number, number] // 排名范围
  scoreRange?: [number, number] // 分数范围
}

// 导出配置
export interface ExportConfig {
  format: 'excel' | 'csv' | 'pdf' | 'image' // 导出格式
  range: 'all' | 'current' | 'selected' // 导出范围
  fields: string[] // 导出字段
  fileName?: string // 文件名
}

// 统计数据
export interface Statistics {
  totalTargets: number // 总培育对象数
  totalProjects: number // 总项目数
  averageScore: number // 平均分
  highestScore: number // 最高分
  lowestScore: number // 最低分
  quarterlyGrowth: number // 季度增长率
}

// API响应类型
export interface ApiResponse<T> {
  code: number
  message: string
  data: T
  success: boolean
}

// 分页数据
export interface PageData<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}
