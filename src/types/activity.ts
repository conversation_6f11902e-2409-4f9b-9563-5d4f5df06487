// 活动管理类型定义

// 活动状态枚举
export enum ActivityStatus {
  UPCOMING = 'upcoming',    // 即将开始
  ONGOING = 'ongoing',      // 进行中
  ENDED = 'ended',          // 已结束
  CANCELLED = 'cancelled'   // 已取消
}

// 参与类型枚举
export enum ParticipationType {
  REGISTRATION = 'registration',  // 报名
  CHECK_IN = 'check_in',          // 签到
  SURVEY = 'survey',              // 问卷
  VOTE = 'vote'                   // 投票
}

// 活动类型枚举
export enum ActivityType {
  CONFERENCE = 'conference',      // 会议
  TRAINING = 'training',          // 培训
  WORKSHOP = 'workshop',          // 研讨会
  SEMINAR = 'seminar',           // 讲座
  COMPETITION = 'competition',    // 竞赛
  EXHIBITION = 'exhibition',      // 展览
  SOCIAL = 'social',             // 社交活动
  OTHER = 'other'                // 其他
}

// 活动优先级枚举
export enum ActivityPriority {
  LOW = 1,       // 低
  NORMAL = 2,    // 普通
  HIGH = 3,      // 高
  URGENT = 4     // 紧急
}

// 参与状态枚举
export enum ParticipationStatus {
  PENDING = 'pending',        // 待处理
  APPROVED = 'approved',      // 已通过
  REJECTED = 'rejected',      // 已拒绝
  COMPLETED = 'completed',    // 已完成
  CANCELLED = 'cancelled'     // 已取消
}

// 活动定义接口
export interface Activity {
  id: string
  title: string
  description: string
  content?: string
  type: ActivityType
  status: ActivityStatus
  priority: ActivityPriority
  startTime: string
  endTime: string
  location: string
  organizer: string
  organizerId: string
  maxParticipants?: number
  currentParticipants: number
  tags: string[]
  images?: ActivityImage[]
  requirements?: string[]
  benefits?: string[]
  contactInfo?: ActivityContact
  isPublic: boolean
  requiresApproval: boolean
  allowWaitlist: boolean
  createTime: string
  updateTime: string
  createdBy: string
  metadata?: Record<string, any>
}

// 活动图片接口
export interface ActivityImage {
  id: string
  url: string
  title?: string
  description?: string
  isCover: boolean
  sort: number
  createTime: string
}

// 活动联系信息接口
export interface ActivityContact {
  name: string
  phone?: string
  email?: string
  wechat?: string
  qq?: string
}

// 参与记录接口
export interface ParticipationRecord {
  id: string
  activityId: string
  activityTitle: string
  userId: string
  userName: string
  userPhone?: string
  userEmail?: string
  type: ParticipationType
  status: ParticipationStatus
  data?: Record<string, any>
  submitTime: string
  approveTime?: string
  completeTime?: string
  approvedBy?: string
  notes?: string
  score?: number
  feedback?: string
  createTime: string
  updateTime: string
}

// 活动统计接口
export interface ActivityStatistics {
  totalActivities: number
  upcomingActivities: number
  ongoingActivities: number
  endedActivities: number
  cancelledActivities: number
  totalParticipants: number
  todayRegistrations: number
  participationRate: number
  averageRating: number
  activitiesByType: Record<ActivityType, number>
  activitiesByStatus: Record<ActivityStatus, number>
  participationsByType: Record<ParticipationType, number>
  recentActivities: ActivityActivity[]
  popularActivities: Activity[]
  topOrganizers: OrganizerStats[]
}

// 活动动态接口
export interface ActivityActivity {
  id: string
  type: 'create' | 'update' | 'register' | 'check_in' | 'complete' | 'cancel'
  activityId: string
  activityTitle: string
  userId: string
  userName: string
  timestamp: string
  description: string
  metadata?: Record<string, any>
}

// 组织者统计接口
export interface OrganizerStats {
  organizerId: string
  organizerName: string
  totalActivities: number
  totalParticipants: number
  averageRating: number
  completionRate: number
}

// 活动查询参数接口
export interface ActivityQueryParams {
  keyword?: string
  type?: ActivityType
  status?: ActivityStatus
  priority?: ActivityPriority
  organizerId?: string
  startDate?: string
  endDate?: string
  location?: string
  tags?: string[]
  isPublic?: boolean
  requiresApproval?: boolean
  hasSlots?: boolean
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 参与查询参数接口
export interface ParticipationQueryParams {
  activityId?: string
  userId?: string
  type?: ParticipationType
  status?: ParticipationStatus
  startDate?: string
  endDate?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 活动操作结果接口
export interface ActivityOperationResult {
  success: boolean
  message: string
  data?: any
  errors?: string[]
}

// 参与操作结果接口
export interface ParticipationOperationResult {
  success: boolean
  message: string
  data?: ParticipationRecord
  errors?: string[]
}

// 活动表单数据接口
export interface ActivityFormData {
  title: string
  description: string
  content?: string
  type: ActivityType
  priority: ActivityPriority
  startTime: string
  endTime: string
  location: string
  organizer: string
  maxParticipants?: number
  tags: string[]
  requirements?: string[]
  benefits?: string[]
  contactInfo?: ActivityContact
  isPublic: boolean
  requiresApproval: boolean
  allowWaitlist: boolean
}

// 参与表单数据接口
export interface ParticipationFormData {
  activityId: string
  type: ParticipationType
  data: Record<string, any>
  notes?: string
}

// 活动评价接口
export interface ActivityRating {
  id: string
  activityId: string
  userId: string
  userName: string
  rating: number
  comment?: string
  createTime: string
}

// 活动通知接口
export interface ActivityNotification {
  id: string
  activityId: string
  activityTitle: string
  type: 'reminder' | 'update' | 'cancel' | 'approval'
  title: string
  content: string
  targetUsers: string[]
  sendTime: string
  isRead: boolean
  createTime: string
}
