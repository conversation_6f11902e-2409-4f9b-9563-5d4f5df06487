// 静态降级数据 - 当API调用失败时使用
// 用于确保系统在网络问题或服务器故障时仍能正常显示内容

import type { 
  CoreMetrics, 
  MonthlyStatistics, 
  DepartmentStatistics, 
  SpeakerStatistics, 
  TrendAnalysis, 
  UserBehaviorAnalysis,
  RealTimeData,
  HealthCheckStatistics,
  HealthCheckRecord
} from '@/types/health-check'

// ================================
// 统计分析模块静态数据
// ================================

export const FALLBACK_CORE_METRICS: CoreMetrics = {
  totalDocuments: 1542,
  publishedDocuments: 1398,
  completionRate: 90.66,
  totalSpeakers: 186,
  totalDepartments: 23,
  averageViews: 1247,
  totalDownloads: 15649,
  userEngagement: 78.3
}

export const FALLBACK_MONTHLY_STATISTICS: MonthlyStatistics[] = [
  { month: '2024-01', documentCount: 124, publishCount: 118, viewCount: 15420, downloadCount: 1320 },
  { month: '2024-02', documentCount: 135, publishCount: 128, viewCount: 16830, downloadCount: 1456 },
  { month: '2024-03', documentCount: 142, publishCount: 139, viewCount: 17650, downloadCount: 1587 },
  { month: '2024-04', documentCount: 128, publishCount: 121, viewCount: 15960, downloadCount: 1398 },
  { month: '2024-05', documentCount: 156, publishCount: 149, viewCount: 19420, downloadCount: 1678 },
  { month: '2024-06', documentCount: 163, publishCount: 158, viewCount: 20280, downloadCount: 1745 },
  { month: '2024-07', documentCount: 148, publishCount: 142, viewCount: 18430, downloadCount: 1589 },
  { month: '2024-08', documentCount: 139, publishCount: 134, viewCount: 17290, downloadCount: 1456 },
  { month: '2024-09', documentCount: 145, publishCount: 138, viewCount: 18050, downloadCount: 1523 },
  { month: '2024-10', documentCount: 152, publishCount: 146, viewCount: 18920, downloadCount: 1612 },
  { month: '2024-11', documentCount: 134, publishCount: 128, viewCount: 16680, downloadCount: 1423 },
  { month: '2024-12', documentCount: 176, publishCount: 167, viewCount: 21920, downloadCount: 1862 }
]

export const FALLBACK_DEPARTMENT_STATISTICS: DepartmentStatistics[] = [
  { department: '办公室', documentCount: 156, publishRate: 92.3, totalViews: 19450, avgScore: 4.5 },
  { department: '组织部', documentCount: 143, publishRate: 89.1, totalViews: 17830, avgScore: 4.3 },
  { department: '宣传部', documentCount: 178, publishRate: 94.7, totalViews: 22140, avgScore: 4.6 },
  { department: '政法委', documentCount: 132, publishRate: 87.9, totalViews: 16420, avgScore: 4.2 },
  { department: '统战部', documentCount: 125, publishRate: 91.2, totalViews: 15580, avgScore: 4.4 },
  { department: '纪检委', documentCount: 167, publishRate: 93.4, totalViews: 20790, avgScore: 4.7 },
  { department: '机关工委', documentCount: 145, publishRate: 90.3, totalViews: 18060, avgScore: 4.3 },
  { department: '老干部局', documentCount: 118, publishRate: 88.1, totalViews: 14720, avgScore: 4.1 },
  { department: '党校', documentCount: 189, publishRate: 95.8, totalViews: 23570, avgScore: 4.8 },
  { department: '档案局', documentCount: 112, publishRate: 86.6, totalViews: 13960, avgScore: 4.0 }
]

export const FALLBACK_SPEAKER_STATISTICS: SpeakerStatistics[] = [
  { speaker: '张明华', documentCount: 45, totalViews: 5640, avgRating: 4.7, department: '党校' },
  { speaker: '李建国', documentCount: 38, totalViews: 4750, avgRating: 4.5, department: '宣传部' },
  { speaker: '王德华', documentCount: 42, totalViews: 5250, avgRating: 4.6, department: '纪检委' },
  { speaker: '陈永强', documentCount: 36, totalViews: 4320, avgRating: 4.4, department: '组织部' },
  { speaker: '刘海燕', documentCount: 41, totalViews: 5130, avgRating: 4.5, department: '办公室' },
  { speaker: '赵志军', documentCount: 39, totalViews: 4870, avgRating: 4.3, department: '政法委' },
  { speaker: '孙丽萍', documentCount: 34, totalViews: 4080, avgRating: 4.2, department: '统战部' },
  { speaker: '周建军', documentCount: 37, totalViews: 4620, avgRating: 4.4, department: '机关工委' }
]

export const FALLBACK_TREND_ANALYSIS: TrendAnalysis[] = [
  { date: '2024-12-01', documentCount: 12, viewCount: 1450, downloadCount: 123, userCount: 267 },
  { date: '2024-12-02', documentCount: 15, viewCount: 1680, downloadCount: 142, userCount: 289 },
  { date: '2024-12-03', documentCount: 18, viewCount: 2120, downloadCount: 178, userCount: 345 },
  { date: '2024-12-04', documentCount: 14, viewCount: 1790, downloadCount: 156, userCount: 298 },
  { date: '2024-12-05', documentCount: 16, viewCount: 1920, downloadCount: 167, userCount: 312 },
  { date: '2024-12-06', documentCount: 13, viewCount: 1560, downloadCount: 134, userCount: 276 },
  { date: '2024-12-07', documentCount: 17, viewCount: 2040, downloadCount: 189, userCount: 334 }
]

export const FALLBACK_USER_BEHAVIOR_ANALYSIS: UserBehaviorAnalysis = {
  readingPatterns: {
    peakHours: ['09:00-10:00', '14:00-15:00', '19:00-20:00'],
    avgReadingTime: 8.5,
    bounceRate: 0.23,
    completionRate: 0.76
  },
  deviceAnalysis: {
    'PC端': { count: 1847, percentage: 68.2 },
    '移动端': { count: 742, percentage: 27.4 },
    '平板端': { count: 119, percentage: 4.4 }
  },
  sourceAnalysis: {
    '直接访问': { count: 1456, percentage: 53.8 },
    '搜索引擎': { count: 689, percentage: 25.4 },
    '社交媒体': { count: 347, percentage: 12.8 },
    '其他链接': { count: 216, percentage: 8.0 }
  }
}

export const FALLBACK_REAL_TIME_DATA: RealTimeData = {
  currentOnlineUsers: 127,
  todayViews: 3456,
  todayDownloads: 289,
  recentActivities: [
    { time: '14:35:12', activity: '用户张某查看《党建工作指南》', type: 'view' },
    { time: '14:34:58', activity: '用户李某下载《制度建设手册》', type: 'download' },
    { time: '14:34:23', activity: '用户王某评价《学习材料汇编》', type: 'rating' },
    { time: '14:33:47', activity: '用户赵某分享《工作流程图》', type: 'share' },
    { time: '14:33:15', activity: '用户陈某收藏《政策解读》', type: 'favorite' }
  ],
  systemStatus: {
    serverLoad: 45.6,
    responseTime: 156,
    errorRate: '0.02%'
  },
  timestamp: new Date().toISOString()
}

// ================================
// 数据体检模块静态数据
// ================================

export const FALLBACK_HEALTH_CHECK_STATISTICS: HealthCheckStatistics = {
  totalChecks: 245,
  completedChecks: 228,
  failedChecks: 17,
  totalExceptions: 156,
  highLevelExceptions: 23,
  fixedExceptions: 89,
  checkTypeStats: [
    { type: 1, name: '数据完整性', count: 68, exceptionCount: 12 },
    { type: 2, name: '数据一致性', count: 52, exceptionCount: 8 },
    { type: 3, name: '数据质量', count: 45, exceptionCount: 15 },
    { type: 4, name: '业务规则', count: 38, exceptionCount: 9 }
  ],
  exceptionTrend: [
    { date: '2024-12-01', count: 23 },
    { date: '2024-12-02', count: 18 },
    { date: '2024-12-03', count: 31 },
    { date: '2024-12-04', count: 25 },
    { date: '2024-12-05', count: 19 },
    { date: '2024-12-06', count: 28 },
    { date: '2024-12-07', count: 16 }
  ]
}

// 体检记录列表静态数据
export const FALLBACK_HEALTH_CHECK_RECORDS: HealthCheckRecord[] = [
  {
    id: 1,
    checkName: '党组织设置规范性检查',
    checkType: 1,
    targetObject: '全市党组织',
    status: 3,
    description: '检查党组织设置是否符合组织条例要求',
    checkResult: '发现12个不规范设置问题',
    exceptionCount: 12,
    lastCheckTime: '2025-09-10 10:30:15',
    operator: '张三',
    createTime: '2025-09-08 09:00:00',
    updateTime: '2025-09-10 10:30:15'
  },
  {
    id: 2,
    checkName: '党务干部任免程序检查',
    checkType: 2,
    targetObject: '各部门党务干部',
    status: 3,
    description: '检查党务干部任免是否按程序执行',
    checkResult: '发现8个程序不当问题',
    exceptionCount: 8,
    lastCheckTime: '2025-09-10 09:45:30',
    operator: '李四',
    createTime: '2025-09-07 14:00:00',
    updateTime: '2025-09-10 09:45:30'
  },
  {
    id: 3,
    checkName: '重点任务完成情况体检',
    checkType: 3,
    targetObject: '年度重点任务',
    status: 2,
    description: '检查重点任务执行进度和质量',
    checkResult: '正在检查中...',
    exceptionCount: 0,
    lastCheckTime: '2025-09-10 11:00:00',
    operator: '王五',
    createTime: '2025-09-10 08:00:00',
    updateTime: '2025-09-10 11:00:00'
  },
  {
    id: 4,
    checkName: '用户信息完整性验证',
    checkType: 4,
    targetObject: '系统用户数据',
    status: 3,
    description: '检查用户信息的完整性和准确性',
    checkResult: '发现9个信息缺失问题',
    exceptionCount: 9,
    lastCheckTime: '2025-09-09 16:20:45',
    operator: '赵六',
    createTime: '2025-09-09 10:00:00',
    updateTime: '2025-09-09 16:20:45'
  },
  {
    id: 5,
    checkName: '基层党建工作检查',
    checkType: 1,
    targetObject: '基层党支部',
    status: 1,
    description: '检查基层党建工作开展情况',
    checkResult: '',
    exceptionCount: 0,
    lastCheckTime: '',
    operator: '孙七',
    createTime: '2025-09-10 12:00:00',
    updateTime: '2025-09-10 12:00:00'
  },
  {
    id: 6,
    checkName: '党员教育培训记录检查',
    checkType: 2,
    targetObject: '党员教育培训数据',
    status: 4,
    description: '检查党员教育培训记录的完整性',
    checkResult: '系统异常，检查失败',
    exceptionCount: 0,
    lastCheckTime: '2025-09-09 14:15:20',
    operator: '周八',
    createTime: '2025-09-09 13:00:00',
    updateTime: '2025-09-09 14:15:20'
  }
]

// ================================
// 数据来源标识
// ================================

export interface DataSourceInfo {
  isFromAPI: boolean
  source: 'api' | 'fallback'
  timestamp: string
  error?: string
}

export const createFallbackDataSource = (error?: string): DataSourceInfo => ({
  isFromAPI: false,
  source: 'fallback',
  timestamp: new Date().toISOString(),
  error
})

export const createAPIDataSource = (): DataSourceInfo => ({
  isFromAPI: true,
  source: 'api',
  timestamp: new Date().toISOString()
})