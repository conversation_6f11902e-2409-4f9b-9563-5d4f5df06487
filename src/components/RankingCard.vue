<template>
  <div class="ranking-card">
    <div class="card-header">
      <div class="rank-section">
        <a-badge 
          :count="data.rank" 
          :number-style="getRankStyle(data.rank)"
        />
        <span class="rank-text">第{{ data.rank }}名</span>
      </div>
      <div class="trend-section">
        <a-tag 
          :color="getTrendColor(data.trend)" 
          size="small"
        >
          <template #icon>
            <arrow-up-outlined v-if="data.trend === 'up'" />
            <arrow-down-outlined v-if="data.trend === 'down'" />
            <minus-outlined v-if="data.trend === 'stable'" />
          </template>
          {{ data.trendValue }}
        </a-tag>
      </div>
    </div>
    
    <div class="card-body">
      <div class="target-section">
        <a-avatar :src="data.target.avatar" :size="40">
          {{ data.target.name.charAt(0) }}
        </a-avatar>
        <div class="target-info">
          <div class="name">{{ data.target.name }}</div>
          <div class="department">{{ data.target.department }}</div>
          <div class="position">{{ data.target.position }}</div>
        </div>
      </div>
      
      <div class="project-section">
        <div class="project-name">{{ data.project.name }}</div>
        <a-tag size="small" color="blue">{{ data.project.category }}</a-tag>
      </div>
      
      <div class="score-section">
        <div class="score-item">
          <span class="label">当前分数</span>
          <span class="value primary">{{ data.score }}</span>
        </div>
        <div class="score-item">
          <span class="label">季度得分</span>
          <span class="value">{{ data.quarterlyScore }}</span>
        </div>
        <div class="score-item">
          <span class="label">年度得分</span>
          <span class="value">{{ data.yearlyScore }}</span>
        </div>
      </div>
    </div>
    
    <div class="card-footer">
      <a-button type="link" size="small" @click="$emit('view-detail', data)">
        查看详情
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import type { RankingItem } from '@/types/quarterly-showcase'

interface Props {
  data: RankingItem
}

defineProps<Props>()
defineEmits<{
  'view-detail': [data: RankingItem]
}>()

const getRankStyle = (rank: number) => {
  if (rank <= 3) {
    return { backgroundColor: '#faad14', color: '#fff' }
  }
  return { backgroundColor: '#d9d9d9', color: '#666' }
}

const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up': return 'green'
    case 'down': return 'red'
    default: return 'default'
  }
}
</script>

<style scoped lang="scss">
.ranking-card {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fff;
  margin-bottom: 16px;
  overflow: hidden;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    
    .rank-section {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .rank-text {
        font-weight: 600;
        color: #262626;
      }
    }
  }
  
  .card-body {
    padding: 16px;
    
    .target-section {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      
      .target-info {
        flex: 1;
        
        .name {
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          margin-bottom: 4px;
        }
        
        .department {
          font-size: 13px;
          color: #8c8c8c;
          margin-bottom: 2px;
        }
        
        .position {
          font-size: 12px;
          color: #bfbfbf;
        }
      }
    }
    
    .project-section {
      margin-bottom: 16px;
      
      .project-name {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
        margin-bottom: 8px;
      }
    }
    
    .score-section {
      display: flex;
      justify-content: space-between;
      
      .score-item {
        text-align: center;
        
        .label {
          display: block;
          font-size: 12px;
          color: #8c8c8c;
          margin-bottom: 4px;
        }
        
        .value {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          
          &.primary {
            color: #1890ff;
            font-size: 18px;
          }
        }
      }
    }
  }
  
  .card-footer {
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    text-align: center;
  }
}
</style>
