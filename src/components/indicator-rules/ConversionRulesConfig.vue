<template>
  <div class="conversion-rules-config">
    <a-card :title="`${getConversionTypeText(conversionType)} 转换规则配置`" size="small">
      <!-- 任务完成及时性转换规则 -->
      <div v-if="conversionType === 1" class="rule-config-section">
        <a-alert 
          message="将任务完成及时性数据转换为百分制分数" 
          description="根据任务完成情况设置对应的分数，系统将自动按此规则进行数据转换"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="按时完成得分" required>
                <a-input-number
                  v-model:value="rules.onTimeScore"
                  :min="0"
                  :max="100"
                  :precision="1"
                  addon-after="分"
                  style="width: 100%"
                  placeholder="请输入按时完成得分"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="逾期完成得分" required>
                <a-input-number
                  v-model:value="rules.overdueScore"
                  :min="0"
                  :max="100"
                  :precision="1"
                  addon-after="分"
                  style="width: 100%"
                  placeholder="请输入逾期完成得分"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="未完成得分" required>
                <a-input-number
                  v-model:value="rules.incompleteScore"
                  :min="0"
                  :max="100"
                  :precision="1"
                  addon-after="分"
                  style="width: 100%"
                  placeholder="请输入未完成得分"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 任务评价等次转换规则 -->
      <div v-else-if="conversionType === 2" class="rule-config-section">
        <a-alert 
          message="将任务评价等次转换为百分制分数" 
          description="设置各评价等次对应的分数，支持自定义等次名称和分数"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <div class="evaluation-levels">
          <div v-for="(level, index) in rules.evaluationLevels" :key="index" class="level-item">
            <a-row :gutter="12" align="middle">
              <a-col :span="8">
                <a-input 
                  v-model:value="level.name" 
                  placeholder="等次名称（如：优秀）"
                />
              </a-col>
              <a-col :span="8">
                <a-input-number
                  v-model:value="level.score"
                  :min="0"
                  :max="100"
                  :precision="1"
                  addon-after="分"
                  style="width: 100%"
                  placeholder="对应分数"
                />
              </a-col>
              <a-col :span="8">
                <a-space>
                  <a-button 
                    type="primary" 
                    size="small"
                    @click="addEvaluationLevel"
                    v-if="index === rules.evaluationLevels.length - 1"
                  >
                    <plus-outlined />
                  </a-button>
                  <a-button 
                    danger 
                    size="small"
                    @click="removeEvaluationLevel(index)"
                    v-if="rules.evaluationLevels.length > 1"
                  >
                    <minus-outlined />
                  </a-button>
                </a-space>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>

      <!-- 排名数据转换规则 -->
      <div v-else-if="conversionType === 3" class="rule-config-section">
        <a-alert 
          message="将排名数据转换为百分制分数" 
          description="设置排名转分数的计算规则，支持线性转换和分段转换"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="转换方式">
            <a-radio-group v-model:value="rules.conversionMethod">
              <a-radio value="linear">线性转换</a-radio>
              <a-radio value="segmented">分段转换</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <div v-if="rules.conversionMethod === 'linear'">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="第1名得分">
                  <a-input-number
                    v-model:value="rules.firstPlaceScore"
                    :min="0"
                    :max="100"
                    :precision="1"
                    addon-after="分"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="最后一名得分">
                  <a-input-number
                    v-model:value="rules.lastPlaceScore"
                    :min="0"
                    :max="100"
                    :precision="1"
                    addon-after="分"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <div v-else-if="rules.conversionMethod === 'segmented'">
            <div class="rank-segments">
              <div v-for="(segment, index) in rules.rankSegments" :key="index" class="segment-item">
                <a-row :gutter="12" align="middle">
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="segment.minRank"
                      :min="1"
                      placeholder="最小排名"
                      style="width: 100%"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="segment.maxRank"
                      :min="1"
                      placeholder="最大排名"
                      style="width: 100%"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="segment.score"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="分"
                      style="width: 100%"
                      placeholder="得分"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-space>
                      <a-button 
                        type="primary" 
                        size="small"
                        @click="addRankSegment"
                        v-if="index === rules.rankSegments.length - 1"
                      >
                        <plus-outlined />
                      </a-button>
                      <a-button 
                        danger 
                        size="small"
                        @click="removeRankSegment(index)"
                        v-if="rules.rankSegments.length > 1"
                      >
                        <minus-outlined />
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-form>
      </div>

      <!-- 活动参与率转换规则 -->
      <div v-else-if="conversionType === 4" class="rule-config-section">
        <a-alert 
          message="将活动参与率转换为百分制分数" 
          description="设置参与率对应的分数区间，支持线性映射和分段映射"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="转换方式">
            <a-radio-group v-model:value="rules.mappingType">
              <a-radio value="linear">线性映射 (参与率直接等于得分)</a-radio>
              <a-radio value="custom">自定义映射</a-radio>
            </a-radio-group>
          </a-form-item>

          <div v-if="rules.mappingType === 'custom'">
            <div class="participation-ranges">
              <div v-for="(range, index) in rules.participationRanges" :key="index" class="range-item">
                <a-row :gutter="12" align="middle">
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="range.minRate"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="%"
                      style="width: 100%"
                      placeholder="最低参与率"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="range.maxRate"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="%"
                      style="width: 100%"
                      placeholder="最高参与率"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-input-number
                      v-model:value="range.score"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="分"
                      style="width: 100%"
                      placeholder="得分"
                    />
                  </a-col>
                  <a-col :span="6">
                    <a-space>
                      <a-button 
                        type="primary" 
                        size="small"
                        @click="addParticipationRange"
                        v-if="index === rules.participationRanges.length - 1"
                      >
                        <plus-outlined />
                      </a-button>
                      <a-button 
                        danger 
                        size="small"
                        @click="removeParticipationRange(index)"
                        v-if="rules.participationRanges.length > 1"
                      >
                        <minus-outlined />
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </div>
          </div>
        </a-form>
      </div>

      <!-- 调查问卷结果转换规则 -->
      <div v-else-if="conversionType === 5" class="rule-config-section">
        <a-alert 
          message="将调查问卷结果转换为百分制分数" 
          description="设置问卷选项对应的分数权重，支持多题目综合计算"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="计算方式">
            <a-radio-group v-model:value="rules.calculationMethod">
              <a-radio value="average">平均分</a-radio>
              <a-radio value="weighted">加权平均</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="选项分值设置">
            <div class="option-scores">
              <div v-for="(option, index) in rules.optionScores" :key="index" class="option-item">
                <a-row :gutter="12" align="middle">
                  <a-col :span="8">
                    <a-input 
                      v-model:value="option.label" 
                      placeholder="选项名称（如：非常满意）"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-input-number
                      v-model:value="option.score"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="分"
                      style="width: 100%"
                      placeholder="对应分数"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-space>
                      <a-button 
                        type="primary" 
                        size="small"
                        @click="addOptionScore"
                        v-if="index === rules.optionScores.length - 1"
                      >
                        <plus-outlined />
                      </a-button>
                      <a-button 
                        danger 
                        size="small"
                        @click="removeOptionScore(index)"
                        v-if="rules.optionScores.length > 1"
                      >
                        <minus-outlined />
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 投票结果转换规则 -->
      <div v-else-if="conversionType === 6" class="rule-config-section">
        <a-alert 
          message="将投票结果转换为百分制分数" 
          description="设置投票选项对应的分数，支持得票率和绝对票数两种计算方式"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="计算依据">
            <a-radio-group v-model:value="rules.voteCalculationBasis">
              <a-radio value="rate">得票率</a-radio>
              <a-radio value="absolute">绝对票数</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="投票选项分值">
            <div class="vote-options">
              <div v-for="(voteOption, index) in rules.voteOptions" :key="index" class="vote-option-item">
                <a-row :gutter="12" align="middle">
                  <a-col :span="8">
                    <a-input 
                      v-model:value="voteOption.label" 
                      placeholder="投票选项（如：赞成）"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-input-number
                      v-model:value="voteOption.score"
                      :min="0"
                      :max="100"
                      :precision="1"
                      addon-after="分"
                      style="width: 100%"
                      placeholder="对应分数"
                    />
                  </a-col>
                  <a-col :span="8">
                    <a-space>
                      <a-button 
                        type="primary" 
                        size="small"
                        @click="addVoteOption"
                        v-if="index === rules.voteOptions.length - 1"
                      >
                        <plus-outlined />
                      </a-button>
                      <a-button 
                        danger 
                        size="small"
                        @click="removeVoteOption(index)"
                        v-if="rules.voteOptions.length > 1"
                      >
                        <minus-outlined />
                      </a-button>
                    </a-space>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 规则预览和验证 -->
      <div class="rule-preview" style="margin-top: 24px;">
        <a-divider>规则预览</a-divider>
        <a-descriptions title="当前转换规则" :column="2" bordered size="small">
          <a-descriptions-item label="转换类型">
            {{ getConversionTypeText(conversionType) }}
          </a-descriptions-item>
          <a-descriptions-item label="规则状态">
            <a-tag :color="validateRules() ? 'green' : 'red'">
              {{ validateRules() ? '配置完整' : '配置不完整' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="规则摘要" :span="2">
            <pre class="rule-summary">{{ getRuleSummary() }}</pre>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue'

interface Props {
  conversionType: number
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  conversionType: 1,
  modelValue: () => ({})
})

const emit = defineEmits<Emits>()

// 初始化规则数据结构
const initializeRules = (type: number) => {
  switch (type) {
    case 1: // 任务完成及时性
      return {
        onTimeScore: 100,
        overdueScore: 60,
        incompleteScore: 0
      }
    case 2: // 任务评价等次
      return {
        evaluationLevels: [
          { name: '优秀', score: 95 },
          { name: '良好', score: 85 },
          { name: '一般', score: 70 },
          { name: '较差', score: 50 }
        ]
      }
    case 3: // 排名数据
      return {
        conversionMethod: 'linear',
        firstPlaceScore: 100,
        lastPlaceScore: 60,
        rankSegments: [
          { minRank: 1, maxRank: 5, score: 95 },
          { minRank: 6, maxRank: 10, score: 80 }
        ]
      }
    case 4: // 活动参与率
      return {
        mappingType: 'linear',
        participationRanges: [
          { minRate: 90, maxRate: 100, score: 95 },
          { minRate: 70, maxRate: 89, score: 80 }
        ]
      }
    case 5: // 调查问卷结果
      return {
        calculationMethod: 'average',
        optionScores: [
          { label: '非常满意', score: 100 },
          { label: '满意', score: 80 },
          { label: '一般', score: 60 },
          { label: '不满意', score: 40 }
        ]
      }
    case 6: // 投票结果
      return {
        voteCalculationBasis: 'rate',
        voteOptions: [
          { label: '赞成', score: 100 },
          { label: '反对', score: 0 },
          { label: '弃权', score: 50 }
        ]
      }
    default:
      return {}
  }
}

// 响应式数据
const rules = ref(props.modelValue && Object.keys(props.modelValue).length > 0 
  ? props.modelValue 
  : initializeRules(props.conversionType)
)

// 监听转换类型变化
watch(() => props.conversionType, (newType) => {
  rules.value = initializeRules(newType)
  emit('update:modelValue', rules.value)
}, { immediate: false })

// 监听规则变化
watch(rules, (newRules) => {
  emit('update:modelValue', newRules)
  emit('change', newRules)
}, { deep: true })

// 监听外部传入的值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    rules.value = newValue
  }
}, { deep: true })

// 获取转换类型文本
const getConversionTypeText = (type: number) => {
  const map: Record<number, string> = {
    1: '任务完成及时性',
    2: '任务评价等次',
    3: '排名数据',
    4: '活动参与率',
    5: '调查问卷结果',
    6: '投票结果'
  }
  return map[type] || '未知类型'
}

// 评价等次相关方法
const addEvaluationLevel = () => {
  rules.value.evaluationLevels.push({ name: '', score: 0 })
}

const removeEvaluationLevel = (index: number) => {
  rules.value.evaluationLevels.splice(index, 1)
}

// 排名段相关方法
const addRankSegment = () => {
  rules.value.rankSegments.push({ minRank: 1, maxRank: 1, score: 0 })
}

const removeRankSegment = (index: number) => {
  rules.value.rankSegments.splice(index, 1)
}

// 参与率范围相关方法
const addParticipationRange = () => {
  rules.value.participationRanges.push({ minRate: 0, maxRate: 0, score: 0 })
}

const removeParticipationRange = (index: number) => {
  rules.value.participationRanges.splice(index, 1)
}

// 问卷选项相关方法
const addOptionScore = () => {
  rules.value.optionScores.push({ label: '', score: 0 })
}

const removeOptionScore = (index: number) => {
  rules.value.optionScores.splice(index, 1)
}

// 投票选项相关方法
const addVoteOption = () => {
  rules.value.voteOptions.push({ label: '', score: 0 })
}

const removeVoteOption = (index: number) => {
  rules.value.voteOptions.splice(index, 1)
}

// 验证规则完整性
const validateRules = () => {
  switch (props.conversionType) {
    case 1:
      return !!(rules.value.onTimeScore >= 0 && rules.value.overdueScore >= 0 && rules.value.incompleteScore >= 0)
    case 2:
      return rules.value.evaluationLevels && rules.value.evaluationLevels.every((level: any) => level.name && level.score >= 0)
    case 3:
      if (rules.value.conversionMethod === 'linear') {
        return !!(rules.value.firstPlaceScore >= 0 && rules.value.lastPlaceScore >= 0)
      } else {
        return rules.value.rankSegments && rules.value.rankSegments.every((segment: any) => 
          segment.minRank >= 1 && segment.maxRank >= 1 && segment.score >= 0
        )
      }
    case 4:
      if (rules.value.mappingType === 'linear') {
        return true
      } else {
        return rules.value.participationRanges && rules.value.participationRanges.every((range: any) =>
          range.minRate >= 0 && range.maxRate >= 0 && range.score >= 0
        )
      }
    case 5:
      return rules.value.optionScores && rules.value.optionScores.every((option: any) => option.label && option.score >= 0)
    case 6:
      return rules.value.voteOptions && rules.value.voteOptions.every((option: any) => option.label && option.score >= 0)
    default:
      return false
  }
}

// 获取规则摘要
const getRuleSummary = () => {
  switch (props.conversionType) {
    case 1:
      return `按时完成: ${rules.value.onTimeScore}分\n逾期完成: ${rules.value.overdueScore}分\n未完成: ${rules.value.incompleteScore}分`
    case 2:
      return rules.value.evaluationLevels?.map((level: any) => `${level.name}: ${level.score}分`).join('\n') || ''
    case 3:
      if (rules.value.conversionMethod === 'linear') {
        return `线性转换: 第1名${rules.value.firstPlaceScore}分，最后一名${rules.value.lastPlaceScore}分`
      } else {
        return rules.value.rankSegments?.map((segment: any) => 
          `排名${segment.minRank}-${segment.maxRank}: ${segment.score}分`
        ).join('\n') || ''
      }
    case 4:
      if (rules.value.mappingType === 'linear') {
        return '线性映射: 参与率直接等于得分'
      } else {
        return rules.value.participationRanges?.map((range: any) => 
          `参与率${range.minRate}%-${range.maxRate}%: ${range.score}分`
        ).join('\n') || ''
      }
    case 5:
      return rules.value.optionScores?.map((option: any) => `${option.label}: ${option.score}分`).join('\n') || ''
    case 6:
      return rules.value.voteOptions?.map((option: any) => `${option.label}: ${option.score}分`).join('\n') || ''
    default:
      return '规则配置无效'
  }
}

onMounted(() => {
  // 确保初始值正确传递给父组件
  emit('update:modelValue', rules.value)
})
</script>

<style scoped>
.conversion-rules-config {
  width: 100%;
}

.rule-config-section {
  margin-top: 16px;
}

.evaluation-levels,
.rank-segments,
.participation-ranges,
.option-scores,
.vote-options {
  max-height: 300px;
  overflow-y: auto;
}

.level-item,
.segment-item,
.range-item,
.option-item,
.vote-option-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
}

.rule-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.rule-summary {
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>