<template>
  <div class="data-source-config">
    <a-card :title="`${getDataSourceTypeText(dataSourceType)} 详细配置`" size="small">
      <!-- 任务数据配置 -->
      <div v-if="dataSourceType === 1" class="config-section">
        <a-alert 
          message="任务数据配置" 
          description="系统将自动获取任务完成情况、评价等次等相关数据"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="任务类型筛选">
            <a-select 
              v-model:value="config.taskType" 
              placeholder="选择要统计的任务类型"
              mode="multiple"
              style="width: 100%"
            >
              <a-select-option value="daily">日常任务</a-select-option>
              <a-select-option value="weekly">周度任务</a-select-option>
              <a-select-option value="monthly">月度任务</a-select-option>
              <a-select-option value="project">项目任务</a-select-option>
              <a-select-option value="special">专项任务</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="时间范围">
            <a-range-picker 
              v-model:value="config.dateRange"
              style="width: 100%"
            />
          </a-form-item>
          <a-form-item label="任务来源">
            <a-checkbox-group v-model:value="config.taskSources">
              <a-checkbox value="system">系统分配</a-checkbox>
              <a-checkbox value="manual">手动创建</a-checkbox>
              <a-checkbox value="import">批量导入</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>

      <!-- 问卷调查数据配置 -->
      <div v-else-if="dataSourceType === 2" class="config-section">
        <a-alert 
          message="问卷调查数据配置" 
          description="选择具体的调查问卷，系统将获取问卷回答数据并按配置的转换规则计算分数"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="问卷选择" required>
            <a-select 
              v-model:value="config.surveyId" 
              placeholder="请选择具体的调查问卷"
              style="width: 100%"
              :loading="surveysLoading"
              @change="handleSurveyChange"
              show-search
              :filter-option="filterSurveyOption"
            >
              <a-select-option 
                v-for="survey in surveyList" 
                :key="survey.id" 
                :value="survey.id"
                :title="survey.description"
              >
                {{ survey.title }}
                <a-tag size="small" :color="getSurveyStatusColor(survey.status)" style="margin-left: 8px">
                  {{ survey.status }}
                </a-tag>
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <div v-if="config.surveyId && selectedSurvey" class="survey-details">
            <a-descriptions title="问卷详情" :column="2" size="small" bordered>
              <a-descriptions-item label="问卷标题">{{ selectedSurvey.title }}</a-descriptions-item>
              <a-descriptions-item label="问卷状态">
                <a-tag :color="getSurveyStatusColor(selectedSurvey.status)">
                  {{ selectedSurvey.status }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ selectedSurvey.createTime }}</a-descriptions-item>
              <a-descriptions-item label="题目数量">{{ selectedSurvey.questionCount }}题</a-descriptions-item>
              <a-descriptions-item label="参与人数" :span="2">{{ selectedSurvey.responseCount }}人</a-descriptions-item>
            </a-descriptions>
          </div>
          
          <a-form-item label="统计维度" v-if="config.surveyId">
            <a-radio-group v-model:value="config.statisticDimension">
              <a-radio value="overall">整体满意度</a-radio>
              <a-radio value="question">按题目分别统计</a-radio>
              <a-radio value="category">按题目分类统计</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="数据过滤" v-if="config.surveyId">
            <a-checkbox-group v-model:value="config.dataFilters">
              <a-checkbox value="completed">仅统计完整回答</a-checkbox>
              <a-checkbox value="verified">仅统计已验证回答</a-checkbox>
              <a-checkbox value="recent">仅统计近期回答</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>

      <!-- 投票数据配置 -->
      <div v-else-if="dataSourceType === 3" class="config-section">
        <a-alert 
          message="投票数据配置" 
          description="选择具体的投票活动，系统将获取投票结果并按配置的转换规则计算分数"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="投票选择" required>
            <a-select 
              v-model:value="config.voteId" 
              placeholder="请选择具体的投票活动"
              style="width: 100%"
              :loading="votesLoading"
              @change="handleVoteChange"
              show-search
              :filter-option="filterVoteOption"
            >
              <a-select-option 
                v-for="vote in voteList" 
                :key="vote.id" 
                :value="vote.id"
                :title="vote.description"
              >
                {{ vote.title }}
                <a-tag size="small" :color="getVoteStatusColor(vote.status)" style="margin-left: 8px">
                  {{ vote.status }}
                </a-tag>
              </a-select-option>
            </a-select>
          </a-form-item>
          
          <div v-if="config.voteId && selectedVote" class="vote-details">
            <a-descriptions title="投票详情" :column="2" size="small" bordered>
              <a-descriptions-item label="投票标题">{{ selectedVote.title }}</a-descriptions-item>
              <a-descriptions-item label="投票状态">
                <a-tag :color="getVoteStatusColor(selectedVote.status)">
                  {{ selectedVote.status }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="开始时间">{{ selectedVote.startTime }}</a-descriptions-item>
              <a-descriptions-item label="结束时间">{{ selectedVote.endTime }}</a-descriptions-item>
              <a-descriptions-item label="投票人数" :span="2">{{ selectedVote.voterCount }}人</a-descriptions-item>
            </a-descriptions>
          </div>
          
          <a-form-item label="统计方式" v-if="config.voteId">
            <a-radio-group v-model:value="config.statisticMethod">
              <a-radio value="percentage">按得票率统计</a-radio>
              <a-radio value="absolute">按绝对票数统计</a-radio>
              <a-radio value="weighted">按权重统计</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <a-form-item label="有效性过滤" v-if="config.voteId">
            <a-checkbox-group v-model:value="config.validityFilters">
              <a-checkbox value="authenticated">仅统计已认证用户</a-checkbox>
              <a-checkbox value="single">排除重复投票</a-checkbox>
              <a-checkbox value="timely">仅统计规定时间内投票</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
        </a-form>
      </div>

      <!-- 其他数据资源配置 -->
      <div v-else-if="dataSourceType === 4" class="config-section">
        <a-alert 
          message="其他数据资源配置" 
          description="配置外部数据源或上传数据文件，支持Excel、CSV等格式"
          type="info" 
          show-icon 
          style="margin-bottom: 16px"
        />
        <a-form layout="vertical">
          <a-form-item label="数据来源方式">
            <a-radio-group v-model:value="config.sourceType">
              <a-radio value="upload">文件上传</a-radio>
              <a-radio value="api">API接口</a-radio>
              <a-radio value="database">数据库查询</a-radio>
            </a-radio-group>
          </a-form-item>
          
          <!-- 文件上传 -->
          <div v-if="config.sourceType === 'upload'">
            <a-form-item label="上传数据文件">
              <a-upload-dragger
                v-model:fileList="config.dataFiles"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.csv,.json"
                :multiple="false"
              >
                <p class="ant-upload-drag-icon">
                  <inbox-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p class="ant-upload-hint">支持 Excel、CSV、JSON 格式文件</p>
              </a-upload-dragger>
            </a-form-item>
            
            <a-form-item label="数据字段映射" v-if="config.dataFiles.length > 0">
              <a-table 
                :columns="fieldMappingColumns" 
                :data-source="config.fieldMappings"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'sourceField'">
                    <a-select v-model:value="record.sourceField" style="width: 100%">
                      <a-select-option v-for="field in availableFields" :key="field" :value="field">
                        {{ field }}
                      </a-select-option>
                    </a-select>
                  </template>
                  <template v-else-if="column.key === 'targetField'">
                    <a-input v-model:value="record.targetField" />
                  </template>
                </template>
              </a-table>
            </a-form-item>
          </div>
          
          <!-- API接口 -->
          <div v-else-if="config.sourceType === 'api'">
            <a-form-item label="API地址">
              <a-input v-model:value="config.apiUrl" placeholder="请输入API接口地址" />
            </a-form-item>
            <a-form-item label="请求方法">
              <a-select v-model:value="config.apiMethod" style="width: 100%">
                <a-select-option value="GET">GET</a-select-option>
                <a-select-option value="POST">POST</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="请求头">
              <a-textarea 
                v-model:value="config.apiHeaders" 
                placeholder="JSON格式，如：{&quot;Authorization&quot;: &quot;Bearer token&quot;}"
                :rows="3"
              />
            </a-form-item>
          </div>
          
          <!-- 数据库查询 -->
          <div v-else-if="config.sourceType === 'database'">
            <a-form-item label="数据库连接">
              <a-select v-model:value="config.databaseId" placeholder="选择数据库连接">
                <a-select-option value="primary">主数据库</a-select-option>
                <a-select-option value="analytics">分析数据库</a-select-option>
                <a-select-option value="external">外部数据库</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="SQL查询语句">
              <a-textarea 
                v-model:value="config.sqlQuery" 
                placeholder="请输入SQL查询语句"
                :rows="5"
              />
            </a-form-item>
          </div>
        </a-form>
      </div>

      <!-- 配置预览 -->
      <div class="config-preview" style="margin-top: 24px;">
        <a-divider>配置预览</a-divider>
        <a-descriptions title="当前数据源配置" :column="1" bordered size="small">
          <a-descriptions-item label="数据源类型">
            {{ getDataSourceTypeText(dataSourceType) }}
          </a-descriptions-item>
          <a-descriptions-item label="配置状态">
            <a-tag :color="validateConfig() ? 'green' : 'orange'">
              {{ validateConfig() ? '配置完整' : '配置不完整' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="配置摘要">
            <pre class="config-summary">{{ getConfigSummary() }}</pre>
          </a-descriptions-item>
        </a-descriptions>
        
        <div v-if="!validateConfig()" style="margin-top: 16px;">
          <a-alert 
            message="配置提醒" 
            description="请完成必填配置项，确保数据源可以正常获取数据"
            type="warning" 
            show-icon 
          />
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'

interface Props {
  dataSourceType: number
  modelValue: any
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}

const props = withDefaults(defineProps<Props>(), {
  dataSourceType: 1,
  modelValue: () => ({})
})

const emit = defineEmits<Emits>()

// 初始化配置数据
const initializeConfig = (type: number) => {
  switch (type) {
    case 1: // 任务数据
      return {
        taskType: [],
        dateRange: [],
        taskSources: ['system']
      }
    case 2: // 问卷调查数据
      return {
        surveyId: '',
        statisticDimension: 'overall',
        dataFilters: ['completed']
      }
    case 3: // 投票数据
      return {
        voteId: '',
        statisticMethod: 'percentage',
        validityFilters: ['authenticated']
      }
    case 4: // 其他数据资源
      return {
        sourceType: 'upload',
        dataFiles: [],
        fieldMappings: [],
        apiUrl: '',
        apiMethod: 'GET',
        apiHeaders: '',
        databaseId: '',
        sqlQuery: ''
      }
    default:
      return {}
  }
}

// 响应式数据
const config = ref(props.modelValue && Object.keys(props.modelValue).length > 0 
  ? props.modelValue 
  : initializeConfig(props.dataSourceType)
)

// 问卷数据
const surveyList = ref([
  { id: '1', title: '党建工作满意度调查', status: '进行中', createTime: '2024-01-01', questionCount: 15, responseCount: 234, description: '针对党建工作开展情况的满意度调研' },
  { id: '2', title: '干部作风建设评议', status: '已结束', createTime: '2024-01-05', questionCount: 12, responseCount: 186, description: '对干部作风建设成效的评价调研' },
  { id: '3', title: '组织生活质量评估', status: '进行中', createTime: '2024-01-10', questionCount: 18, responseCount: 156, description: '组织生活开展质量和效果评估' }
])
const surveysLoading = ref(false)
const selectedSurvey = computed(() => surveyList.value.find(s => s.id === config.value.surveyId))

// 投票数据
const voteList = ref([
  { id: '1', title: '最佳党支部评选', status: '投票中', startTime: '2024-01-01', endTime: '2024-01-31', voterCount: 325, description: '年度最佳党支部评选投票' },
  { id: '2', title: '优秀党员推荐', status: '已结束', startTime: '2023-12-01', endTime: '2023-12-31', voterCount: 289, description: '优秀党员候选人推荐投票' },
  { id: '3', title: '工作改进建议征集', status: '投票中', startTime: '2024-01-15', endTime: '2024-02-15', voterCount: 167, description: '工作改进建议投票征集' }
])
const votesLoading = ref(false)
const selectedVote = computed(() => voteList.value.find(v => v.id === config.value.voteId))

// 字段映射
const availableFields = ref(['姓名', '部门', '职务', '得分', '评级', '时间'])
const fieldMappingColumns = [
  { title: '源字段', dataIndex: 'sourceField', key: 'sourceField' },
  { title: '目标字段', dataIndex: 'targetField', key: 'targetField' },
  { title: '字段类型', dataIndex: 'fieldType', key: 'fieldType' }
]

// 监听数据源类型变化
watch(() => props.dataSourceType, (newType) => {
  config.value = initializeConfig(newType)
  emit('update:modelValue', config.value)
})

// 监听配置变化
watch(config, (newConfig) => {
  emit('update:modelValue', newConfig)
  emit('change', newConfig)
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    config.value = newValue
  }
}, { deep: true })

// 获取数据源类型文本
const getDataSourceTypeText = (type: number) => {
  const map: Record<number, string> = {
    1: '任务数据',
    2: '问卷调查数据',
    3: '投票数据',
    4: '其他数据资源'
  }
  return map[type] || '未知类型'
}

// 处理问卷变化
const handleSurveyChange = (surveyId: string) => {
  console.log('选中问卷:', surveyId)
}

// 处理投票变化
const handleVoteChange = (voteId: string) => {
  console.log('选中投票:', voteId)
}

// 过滤问卷选项
const filterSurveyOption = (input: string, option: any) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 过滤投票选项
const filterVoteOption = (input: string, option: any) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 获取问卷状态颜色
const getSurveyStatusColor = (status: string) => {
  const map: Record<string, string> = {
    '进行中': 'green',
    '已结束': 'blue',
    '草稿': 'orange'
  }
  return map[status] || 'default'
}

// 获取投票状态颜色
const getVoteStatusColor = (status: string) => {
  const map: Record<string, string> = {
    '投票中': 'green',
    '已结束': 'blue',
    '未开始': 'orange'
  }
  return map[status] || 'default'
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isValidType = 
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel' ||
    file.type === 'text/csv' ||
    file.type === 'application/json'
  
  if (!isValidType) {
    message.error('只能上传 Excel、CSV 或 JSON 文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!')
    return false
  }
  
  // 模拟解析文件字段
  setTimeout(() => {
    config.value.fieldMappings = [
      { sourceField: '', targetField: '姓名', fieldType: '文本' },
      { sourceField: '', targetField: '部门', fieldType: '文本' },
      { sourceField: '', targetField: '得分', fieldType: '数字' }
    ]
  }, 1000)
  
  return false // 阻止自动上传
}

// 验证配置完整性
const validateConfig = () => {
  switch (props.dataSourceType) {
    case 1: // 任务数据
      return config.value.taskType && config.value.taskType.length > 0
    case 2: // 问卷调查数据
      return !!(config.value.surveyId)
    case 3: // 投票数据
      return !!(config.value.voteId)
    case 4: // 其他数据资源
      if (config.value.sourceType === 'upload') {
        return config.value.dataFiles && config.value.dataFiles.length > 0
      } else if (config.value.sourceType === 'api') {
        return !!(config.value.apiUrl)
      } else if (config.value.sourceType === 'database') {
        return !!(config.value.databaseId && config.value.sqlQuery)
      }
      return false
    default:
      return false
  }
}

// 获取配置摘要
const getConfigSummary = () => {
  switch (props.dataSourceType) {
    case 1:
      return `任务类型: ${config.value.taskType?.join(', ') || '未选择'}\n数据来源: ${config.value.taskSources?.join(', ') || '未配置'}`
    case 2:
      const survey = selectedSurvey.value
      return survey ? `问卷: ${survey.title}\n统计维度: ${config.value.statisticDimension}\n参与人数: ${survey.responseCount}人` : '未选择问卷'
    case 3:
      const vote = selectedVote.value
      return vote ? `投票: ${vote.title}\n统计方式: ${config.value.statisticMethod}\n投票人数: ${vote.voterCount}人` : '未选择投票'
    case 4:
      if (config.value.sourceType === 'upload') {
        return `数据来源: 文件上传\n文件数量: ${config.value.dataFiles?.length || 0}个`
      } else if (config.value.sourceType === 'api') {
        return `数据来源: API接口\nAPI地址: ${config.value.apiUrl || '未配置'}`
      } else if (config.value.sourceType === 'database') {
        return `数据来源: 数据库查询\n数据库: ${config.value.databaseId || '未选择'}`
      }
      return '未配置数据源'
    default:
      return '配置无效'
  }
}

onMounted(() => {
  emit('update:modelValue', config.value)
})
</script>

<style scoped>
.data-source-config {
  width: 100%;
}

.config-section {
  margin-top: 16px;
}

.survey-details,
.vote-details {
  margin: 16px 0;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.config-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.config-summary {
  background-color: #f9f9f9;
  padding: 8px;
  border-radius: 4px;
  margin: 0;
  font-family: inherit;
  white-space: pre-wrap;
  word-break: break-all;
}

.ant-upload-drag-icon {
  font-size: 48px;
  color: #999;
}

.ant-upload-text {
  margin-top: 16px;
  color: #666;
}

.ant-upload-hint {
  color: #999;
}
</style>