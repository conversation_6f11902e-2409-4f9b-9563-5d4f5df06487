import React, { PureComponent } from "react";
import { getIconMap, converFileSize } from "@/tools/util";
import { ajax } from "./utils";
import { Progress, message, Spin } from "antd";
import dayjs from "dayjs";
import http from "@/tools/axios";
import { previewImage } from "./utils";
import { imgReg } from "@/tools/regular";
import IconFontNew from "@/components/IconFontNew";
import IconFont from "@/components/IconFont";
import FileDownload from "../FileDownload";
import imageErr from "./images/image_err.png";
import fileImage from "./images/file.png";

export class FileUploadItem extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      propress: 0,
      loading: false,
      error: false,
    };
    (this.xhrObj = null), (this.updateFile = this.updateFile.bind(this));
    this.handleDel = this.handleDel.bind(this);
    this.handlePreview = this.handlePreview.bind(this);
    this.onImageError = this.onImageError.bind(this);
  }
  componentDidMount() {
    const { status } = this.props;
    if (status === "done") {
      // 上传图片
      this.updateFile();
    }
    if (status === "success") {
      // 无状态则代表上传成功，后端返回的数据
      this.getPreviewImageURL();
    }
  }
  updateFile() {
    const {
      upType,
      index,
      disabled,
      showTime,
      onError,
      onChange,
      onDelete,
      onPreview,
      ...otherProps
    } = this.props;
    const { fileData, name } = otherProps;
    const formData = new FormData();
    let upName = name || "";
    const imageFileTypes = ["image/png", "image/jpeg", "image/jpg"];
    if (imageFileTypes.indexOf(fileData.type) > -1) {
      // 上传的是图片，图片质量保真处理，作为新闻类型图片来处理
      // 对符合条件的文件进行处理
      formData.append("upType", "no-compress-image");
    } else formData.append("upType", "file");
    // 去除文件名中的空格字符
    if (typeof upName === "string") {
      upName = upName.replace(/\s*/g, "");
    }
    if (upType === "eval-file") {
      if (typeof window !== "undefined") {
        let orgName = sessionStorage.getItem("_on");
        if (orgName) {
          orgName = unescape(orgName) || "";
          upName = upName.replace(/(\.[^.]+)$/, `_${orgName}$1`);
        }
      }
    }
    formData.append("upfile", fileData);
    formData.append("up_name", upName);
    if (onChange) onChange({ ...otherProps, status: "uploading" }, index);

    // uploadFile(formData).then((res) => {
    //   const { code, data } = res;
    //   if (code == 0) {
    //   } else {
    //     message.err(res.message);
    //   }
    // });

    ajax({
      url: `${http.gateway}/file/file/upload`,
      type: "POST",
      data: formData,
      dataType: "json",
      success: (res = []) => {
        // console.log(res, "res");
        this.setState({ propress: 100 });
        const { id: file_id, id, file_name: name, path, size } = res[0];
        if (onChange) onChange({ id, file_id, name, path, size }, index);
      },
      error: (err) => {
        // console.log(err);
        console.error(err);
        message.error("文件上传失败");
        if (onChange) onChange({ ...otherProps, status: "error" }, index);
        if (onError) onError(index);
      },
      onprogress: (progress) => {
        this.setState({ propress: progress > 98 ? 98 : progress });
      },
      xhr: (xhr) => {
        this.xhrObj = xhr;
      },
    });
  }
  // 删除操作
  handleDel() {
    const { status, disabled, onDelete, index } = this.props;
    if (disabled) return;
    if (status === "uploading" && this.xhrObj) {
      this.xhrObj.abort();
    }
    if (onDelete) onDelete(index);
  }
  // 预览操作
  handlePreview() {
    const {
      upType,
      index,
      disabled,
      showTime,
      onError,
      onChange,
      onDelete,
      onPreview,
      ...otherProps
    } = this.props;
    if (onPreview)
      onPreview(otherProps, index, `${http.cdn}/${otherProps.path}`);
  }
  onImageError() {
    // if ()
    // this.setState({ error: true });
  }
  render() {
    const {
      upType,
      index,
      disabled,
      showTime,
      onError,
      onChange,
      onDelete,
      onPreview,
      status,
      ...otherProps
    } = this.props;
    const { name, listType, size, path } = otherProps;
    // console.log(otherProps, getIconMap(name), "otherProps");
    const { error, loading, propress } = this.state;
    const [fileType, fileColor] = getIconMap(name);
    const isImage = new RegExp(imgReg).test(name);
    if (listType === "text") {
      // 文本类
      return (
        <div className="file-upload-item">
          {/* TODO 这里因为icon问题矢量库没有 */}
          {/* <IconFont
            className="file-icon"
            type={`anticon-${fileType}`}
            color={fileColor}
          /> */}
          <div className="file-upload-info">
            <p>
              <span className="file-name" title={name}>
                {name}
              </span>
              {!disabled && <IconFont
                type="anticon-gsg-shanchu6"
                className="file-del-icon"
                remove={true}
                onClick={this.handleDel}
              />}
            </p>
            {status === "uploading" || status === "error" ? (
              <Progress
                className="file-upload-propress"
                size="small"
                strokeWidth={2}
                status={status === "error" ? "exception" : "active"}
                percent={propress}
              />
            ) : (
              <p className="file-size">
                {!!size && converFileSize(size)}
                {showTime
                  ? `  /  ${dayjs(Number(create_time) || new Date()).format(
                      "YYYY-MM-DD HH:mm:ss"
                    )}`
                  : ""}
                <FileDownload
                  filePath={`/file/file/download/${
                    otherProps.file_id || otherProps.id || otherProps.name
                  }`}
                  fileName={otherProps.name || "未知名称.txt"}
                  btnName="下载文件"
                  type="link"
                />
              </p>
            )}
          </div>
        </div>
      );
    }
    // 卡片类
    if (status === "uploading" || status === "error") {
      return (
        <div className="file-upload-card-item file-upload-card-progress-item">
          <Progress
            type="circle"
            strokeWidth={3}
            percent={propress}
            width={80}
            status={status === "error" ? "exception" : "active"}
          />
        </div>
      );
    }

    const url = error ? imageErr : `${http.cdn}/${path}`;
    return (
      <div className="file-upload-card-item file-upload-card-img-item">
        {loading ? (
          <Spin spinning={loading}>
            <div className="file-upload-card-item"></div>
          </Spin>
        ) : (
          path && (
            <img
              src={isImage ? url : fileImage}
              alt={otherProps.name}
              onError={this.onImageError}
            />
          )
        )}
        <div className="obscuration">
          {isImage ? (
            <IconFont
              type="anticon-xianshi"
              onClick={this.handlePreview}
              oneself
            />
          ) : (
            <FileDownload
              type="link"
              fileId={String(otherProps.file_id)}
              filePath={otherProps.path}
              fileName={otherProps.path}
            />
          )}
          {!disabled ? (
            <IconFont
              type="anticon-gsg-shanchu4"
              onClick={this.handleDel}
              remove={true}
            />
          ) : null}
        </div>
      </div>
    );
  }
}

export default FileUploadItem;
