import React, { PureComponent } from "react"
import "./index.less"
import { getUuid } from "@/tools/util"
import http from "@/tools/axios"
import FileUploadItem from "./FileUploadItem"
import FileUploadSelect from "./FileUploadSelect"
import PreviewModal from "./PreviewModal"
import { message } from "antd"

class FileUploadList extends PureComponent {
  constructor(props) {
    super(props)
    this.state = {
      previewData: null,
      visible: false,
    }
    this.onFileChange = this.onFileChange.bind(this)
    this.onItemChange = this.onItemChange.bind(this)
    this.onItemDel = this.onItemDel.bind(this)
    this.onPreview = this.onPreview.bind(this)
  }
  // 上传文件改变
  onFileChange(e) {
    const { onChange, value = [], maxSize, upload, limit } = this.props
    const { files } = e.target
    let newValue = Array.isArray(value) ? [...value] : []
    if (limit && files.length + newValue.length > limit) {
      message.warning(`最多上传${limit}个文件`)
      return
    }
    for (let i = 0; i < files.length; i++) {
      const fileItem = files[i]
      if (maxSize && fileItem.size > maxSize * 1024 * 1024) {
        message.error(`单个文件不能大于${maxSize}M`)
        return
      }
      if (newValue) {
        if (upload == "picture" && fileItem.name.indexOf("mp4") !== -1) {
          message.error("视频文件不支持上传")
          return
        } else if (fileItem.name.indexOf("jpeg") !== -1) {
          message.error("该类型不支持上传")
        } else {
          newValue.push({
            identify: getUuid(),
            status: "done",
            name: fileItem.name,
            size: fileItem.size,
            fileData: fileItem,
          })
        }
      }
    }
    if (onChange && newValue) onChange([...newValue])
  }
  // 子集item上传完成
  onItemChange(item, index) {
    const { onChange, value = [] } = this.props
    let newValue = Array.isArray(value) ? [...value] : []
    newValue[index] = item
    if (onChange && newValue) onChange([...newValue])
  }
  // 子集item删除
  onItemDel(index) {
    const { onChange, value = [] } = this.props
    let newValue = Array.isArray(value) ? [...value] : []
    newValue.splice(index, 1)
    if (onChange && newValue) onChange([...newValue])
  }

  onPreview(data, index, dataURL) {
    this.setState({
      previewData: {
        url: dataURL || `${http.cdn}/${data.path}`,
        name: data.name,
      },
      visible: true,
    })
  }

  render() {
    const { maxSize, onChange, showTime, ...otherProps } = this.props
    const { previewData, visible } = this.state
    const value = Array.isArray(this.props.value) ? this.props.value : []
    // console.log(value, this.props.value);
    return (
      <div className="file-upload-component">
        <FileUploadSelect
          onInputChage={this.onFileChange}
          currentLength={value.length}
          {...otherProps}
          maxSize={maxSize}
        >
          {value &&
            value.map((item, index) => (
              <FileUploadItem
                {...item}
                listType={otherProps.listType}
                key={item.identify || `${item.path}+${index}`}
                index={index}
                disabled={otherProps.disabled}
                onChange={this.onItemChange}
                onDelete={this.onItemDel}
                onError={this.onItemDel}
                onPreview={this.onPreview}
                showTime={showTime}
              />
            ))}
        </FileUploadSelect>
        <PreviewModal
          src={previewData ? previewData.url : ""}
          name={previewData ? previewData.name : ""}
          visible={visible}
          onCancel={() => this.setState({ visible: false })}
        />
      </div>
    )
  }
}

FileUploadList.defaultProps = {
  listType: "text",
  txt: "上传文件",
  tip: "",
  value: [],
  onChangee: () => { },
  disabled: false,
  showTip: true,
  limit: 3, // 最大上传数量
  maxSize: 5, // 单个文件最大上传大小
}

export default FileUploadList
