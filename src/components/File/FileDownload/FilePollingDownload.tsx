import React, { PureComponent } from "react";
import PropTypes from "prop-types";
import { But<PERSON>, message } from "antd";
import axios from "axios";
import http from "@/tools/axios";
import { getQuery } from "@/tools/util";
import { fileDownload } from "./index";

/**
 * 轮询下载
 */
export default class FilePollingDownload extends PureComponent {
  static propTypes: {
    filePath: PropTypes.Validator<string>; // 下载地址（需自行添加网关）
    fileName: PropTypes.Requireable<string>; // 下载文件的文件名
    btnName: PropTypes.Requireable<string>; // 生成的按钮名
    type: PropTypes.Requireable<string>; // 同Antd button的type  默认primary
    className: PropTypes.Requireable<string>;
    params: PropTypes.Requireable<any>; // 上传时候的传参
    disabled?: boolean;
  };
  static defaultProps: {
    type: string;
    btnName: string;
    className: string;
    disabled: boolean;
  };
  pollingTime: null;
  constructor(props: any) {
    super(props);
    this.state = {
      loading: false,
      uuid: "",
      percent: 0,
    };
    this.pollingTime = null;
    this.onDownloadFile = this.onDownloadFile.bind(this);
  }
  componentWillUnmount() {
    if (this.pollingTime) {
      clearTimeout(this.pollingTime);
    }
  }
  onDownloadFile(e: any) {
    e.preventDefault();
    const { filePath, params }: any = this.props;
    const customHeaders = {
      ...http.getHeader(),
      "Content-Type": "application/json",
    };
    axios
      .post(http.gateway + filePath, params || getQuery(decodeURI(filePath)), {
        data: params || getQuery(filePath),
        headers: customHeaders,
      })
      .then((res: any) => {
        // 后端返回json数据
        const { data, status, code, message: msg } = res.data;
        if (code === 0 && status === 200) {
          this.setState({ uuid: data, loading: true }, () => {
            this.pollingDownload();
          });
        } else {
          message.error(msg || "网络异常，请稍后再试");
        }
      });
  }
  pollingDownload() {
    const { filePath, fileName }: any = this.props;
    const { uuid }: any = this.state;
    const customHeaders = {
      ...http.getHeader(),
      "Content-Type": "application/json",
    };
    const url = (http.gateway + filePath).split("?")[0];
    axios
      .get(`${url}/result?token=${uuid}`, {
        headers: customHeaders,
      })
      .then((res: any) => {
        const { data } = res;
        if (!(data.code === 0 && data.status === 200)) {
          this.setState({
            loading: false,
            percent: 0,
          });
          message.error(data.message || "服务器异常，请稍后再试！");
          return;
        }
        if (typeof data.data === "object" && data.data[0].id === 100) {
          fileDownload(
            `/file/file/imgdownload?img_url=${data.data[0].path}`,
            fileName || null
          )
            .catch(() => {
              message.error("下载失败，请联系管理员");
            })
            .finally(() => {
              this.setState({
                loading: false,
                percent: 0,
              });
            });
        } else {
          this.setState({ percent: data.data || 0 }, () => {
            setTimeout(() => {
              this.pollingDownload();
            }, 1000);
          });
        }
      });
  }
  render() {
    const { btnName, className, type, disabled }: any = this.props;
    const { loading, percent }: any = this.state;
    return (
      <Button
        className={`file-download-btn ${className}`}
        loading={loading}
        type={type}
        onClick={this.onDownloadFile}
        disabled={disabled}
      >
        {btnName} {loading ? `(${percent}%)` : ""}
      </Button>
    );
  }
}

/* FilePollingDownload.propTypes = {
  filePath: PropTypes.string.isRequired, // 下载地址（需自行添加网关）
  fileName: PropTypes.string, // 下载文件的文件名
  btnName: PropTypes.string, // 生成的按钮名
  type: PropTypes.string, // 同Antd button的type  默认primary
  className: PropTypes.string, // 按钮类名
  params: PropTypes.any,
};

FilePollingDownload.defaultProps = {
  type: "primary",
  btnName: "下载",
  className: "",
};
 */
