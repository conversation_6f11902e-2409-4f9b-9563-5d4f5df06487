import React, { PureComponent } from "react";
import { But<PERSON>, message } from "antd";
import PropTypes from "prop-types";
import JSZ<PERSON> from "jszip";
import axios from "axios";
import http from "@/tools/axios";
import "./index.less";

export const download = (blob: any, fileName: any) => {
  const el = document.createElement("a");
  const href = window.URL.createObjectURL(blob); // 创建 URL 对象
  el.href = href;
  el.target = "_blank";
  el.style.display = "none";
  el.download = fileName;
  document.body.appendChild(el);
  el.click();
  document.body.removeChild(el); // 下载完成移除元素
  window.URL.revokeObjectURL(href); // 释放掉blob对象
};

/**
 * 新开发的页面不建议调用该方法，请使用FileDownload组件
 * 该方法是为了兼容之前直接写在a标签onClick上的下载方式
 * @param {string} filePath ---必传 文件url
 * @param {string} fileName  文件名  请加上文件后缀  不传会从后端返回中获取
 * @param {boolean} multiple  多文件打包下载
 */
export const fileDownload = (
  filePath: string,
  fileName?: string,
  multiple: boolean = false
) => {
  return new Promise((resolve, reject) => {
    const customHeaders = {
      ...http.getHeader(),
      "Content-Type": "multipart/*",
    };
    // 如果是融合商城请求，则需要在请求头中放入access_key
    if (filePath.indexOf("/score/mall") !== -1) {
      customHeaders.access_key = "9X7CLlez02SKTPh5wV7Px9irL60Vmsx8";
    }
    console.log("下载链接");
    console.log(http.gateway + filePath);
    axios
      .get(http.gateway + filePath, {
        headers: customHeaders,
        responseType: "blob",
      })
      .then((response: any) => {
        console.log(response);
        if (response.status === 200) {
          //处理文件 1. 多文件下载直接返回data 2.单个文件下载设置文件名，通过A标签下载
          const handleDownload = () => {
            if (multiple) {
              //多文件压缩下载
              resolve(response.data);
              return;
            }
            //未设置文件名则从response.headers中获取文件名
            // 后端必须配置Access-Control-Expose-Headers：Content-Disposition
            if (!fileName) {
              try {
                // 尝试解析content-disposition 后端必须配置
                const contentDisp =
                  response.headers["content-disposition"] || "";
                if (contentDisp) {
                  let fileNameArr = contentDisp.split("filename=");
                  if (fileNameArr[1]) {
                    fileNameArr[1] = fileNameArr[1].replace(/'|"/g, "");
                    // 替换火狐浏览器中，无法识别的包含空格文件名，把文本空格替换为编码空格
                    fileNameArr[1] = fileNameArr[1].replace(" ", "%20");
                    fileName = decodeURIComponent(fileNameArr[1]);
                  }
                } else {
                  fileName = "未知文件";
                }
              } catch (error) {}
            }
            const blob = new Blob([response.data], {
              // type: '"application/ynd.ms-excel;charset=UTF-8"',
              type: "application/octet-stream;charset=UTF-8",
            }); // 创建Blob实例
            //1 file-saver 保存文件  未验证
            // FileSaver.saveAs(blob, fileName)
            //2 a标签保存
            download(blob, fileName);
            resolve(true);
          };
          if (response.data.type === "application/json") {
            // 如果为json类型，则可能是下载错误，尝试解析后提示信息
            const reader = new FileReader();
            reader.onload = ({ target }: any) => {
              try {
                const res = JSON.parse(target?.result || "{}");
                if (res.code !== 0) {
                  message.error(res.message || "文件下载错误");
                }
                reject();
              } catch (error) {
                // 解析错误则认为依然是文件
                handleDownload();
              }
            };
            reader.readAsText(response.data);
          } else {
            handleDownload();
          }
        } else {
          reject();
          message.error(response.statusText || "系统错误");
        }
      })
      .catch((err: any) => {
        reject(err);
        message.error(err.message || "网络错误");
      });
  });
};

/**
 * 多文件打包下载
 * @param {array} files [{path, name}]
 * @param {string} fileName 压缩包名
 */
export const multipleFilesDownload = ({ files, fileName }: any) => {
  return new Promise((resolve, reject) => {
    if (!fileName) {
      message.error("fileName不能为空");
      reject();
      return;
    }
    const promises: any = [];
    const zip = new JSZip();
    files.forEach((item: any) => {
      promises.push(
        fileDownload(item.path, item.name, true).then((data: any) => {
          zip.file(item.name, data, { binary: true });
        })
      );
    });
    Promise.all(promises).then(() => {
      zip
        .generateAsync({ type: "blob" })
        .then((content: any) => {
          // 生成二进制流
          // FileSaver.saveAs(content, fileName + ".zip") // 利用file-saver保存文件
          download(content, fileName + ".zip");
          resolve(true);
        })
        .catch((err: any) => {
          message.error("网络出现了一点小问题，请稍后重试");
          reject(err);
        });
    });
  });
};
class FileDownload extends PureComponent {
  static propTypes: {
    filePath: PropTypes.Validator<string>; // 下载地址前记得添加--网关--
    fileName: PropTypes.Requireable<string>; //下载后的文件名  单个文件下载时必须添加后缀
    btnName: PropTypes.Requireable<string>; // 生成的按钮名
    icon: PropTypes.Requireable<string>; // 按钮的图标
    type: PropTypes.Requireable<string>;
  };
  static defaultProps: {
    type: string; // primary  link
    btnName: string;
  };

  constructor(props: any) {
    super(props);
    this.state = {
      downLoading: false,
    };
  }
  onDownloadFile(e: Event) {
    e.preventDefault();
    this.setState({
      downLoading: true,
    });
    let { filePath, fileName }: any = this.props;
    fileDownload(filePath, fileName)
      .finally(() => {
        this.setState({
          downLoading: false,
        });
      })
      .catch((err) => {
        console.error(err);
      });
  }
  render() {
    const { filePath, fileName, btnName, ...other }: any = this.props;
    const { downLoading }: any = this.state;
    return (
      <Button
        disabled={false}
        className="file-download-btn"
        loading={downLoading}
        onClick={(e: Event) => this.onDownloadFile(e)}
        {...other}
      >
        {btnName}
      </Button>
    );
  }
}
FileDownload.propTypes = {
  filePath: PropTypes.string.isRequired, // 下载地址前记得添加--网关--
  fileName: PropTypes.string, //下载后的文件名  单个文件下载时必须添加后缀
  btnName: PropTypes.string, // 生成的按钮名
  icon: PropTypes.string, // 按钮的图标
  type: PropTypes.string, // 同Antd button的type  默认primary
};
FileDownload.defaultProps = {
  type: "primary", // primary  link
  btnName: "下载",
};
export default FileDownload;
