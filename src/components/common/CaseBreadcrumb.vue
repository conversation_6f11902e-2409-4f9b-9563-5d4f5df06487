<template>
  <div class="case-breadcrumb">
    <a-breadcrumb>
      <a-breadcrumb-item
        v-for="(item, index) in breadcrumbItems"
        :key="index"
      >
        <template v-if="item.icon && index === 0">
          <component :is="getIcon(item.icon)" />
        </template>
        
        <router-link 
          v-if="item.path && index < breadcrumbItems.length - 1"
          :to="item.path"
          class="breadcrumb-link"
        >
          {{ item.title }}
        </router-link>
        
        <span v-else class="breadcrumb-current">
          {{ item.title }}
        </span>
      </a-breadcrumb-item>
    </a-breadcrumb>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { HomeOutlined } from '@ant-design/icons-vue';
import { getBreadcrumbConfig, type BreadcrumbItem } from '@/config/breadcrumb';

interface Props {
  customBreadcrumb?: BreadcrumbItem[];
  showIcon?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showIcon: true
});

const route = useRoute();

// 计算面包屑项目
const breadcrumbItems = computed(() => {
  if (props.customBreadcrumb) {
    return props.customBreadcrumb;
  }

  // 获取当前路径的面包屑配置
  const currentPath = route.path;
  const routeParams = route.params as Record<string, string>;
  
  return getBreadcrumbConfig(currentPath, routeParams);
});

// 获取图标组件
const getIcon = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'home': HomeOutlined
  };
  
  return iconMap[iconName] || HomeOutlined;
};
</script>

<style scoped lang="scss">
.case-breadcrumb {
  margin-bottom: 16px;

  :deep(.ant-breadcrumb) {
    font-size: 14px;

    .ant-breadcrumb-link {
      color: #666;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .breadcrumb-current {
      color: #262626;
      font-weight: 500;
    }

    .anticon {
      margin-right: 4px;
      color: #1890ff;
    }

    .ant-breadcrumb-separator {
      color: #d9d9d9;
      margin: 0 8px;
    }
  }
}

@media (max-width: 768px) {
  .case-breadcrumb {
    margin-bottom: 12px;

    :deep(.ant-breadcrumb) {
      font-size: 13px;

      .ant-breadcrumb-separator {
        margin: 0 6px;
      }
    }
  }
}
</style>
