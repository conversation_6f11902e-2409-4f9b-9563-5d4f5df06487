<template>
	<div class="file-upload-component">
		<!-- 文件上传区域 -->
		<div class="upload-area" v-if="!disabled && (!fileList.length || multiple)">
			<!-- 拖拽上传区域 -->
			<a-upload-dragger
				v-if="showDragger"
				:multiple="multiple"
				:show-upload-list="false"
				:before-upload="beforeUpload"
				:disabled="disabled"
				accept="*/*"
				class="upload-dragger"
			>
				<p class="ant-upload-drag-icon">
					<inbox-outlined />
				</p>
				<p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
				<p class="ant-upload-hint">
					{{ multiple ? '支持单个或批量上传' : '支持单个文件上传' }}
				</p>
			</a-upload-dragger>
			<a-upload
				v-else
				:multiple="multiple"
				:show-upload-list="false"
				:before-upload="beforeUpload"
				:disabled="disabled"
				accept="*/*"
				class="upload-trigger"
			>
				<a-button :loading="uploading" :disabled="disabled">
					<template #icon>
						<upload-outlined />
					</template>
					{{ uploading ? '上传中...' : '选择文件' }}
				</a-button>
			</a-upload>
		</div>

		<!-- 文件列表展示 -->
		<div class="file-list" v-if="fileList.length">
			<div v-for="(file, index) in fileList" :key="file.id || index" class="file-item" :class="{ 'file-item-error': file.status === 'error' }">
				<!-- 文件图标 -->
				<div class="file-icon">
					<file-outlined v-if="!isImage(file)" />
					<picture-outlined v-else />
				</div>

				<!-- 文件信息 -->
				<div class="file-info">
					<div class="file-name" :title="file.name">
						{{ file.name }}
					</div>
					<div class="file-meta">
						<span v-if="file.size" class="file-size">{{ formatFileSize(file.size) }}</span>
						<span v-if="showTime && file.uploadTime" class="upload-time">
							{{ formatTime(file.uploadTime) }}
						</span>
					</div>

					<!-- 上传进度 -->
					<a-progress v-if="file.status === 'uploading'" :percent="file.progress || 0" size="small" :show-info="false" />
				</div>

				<!-- 文件状态 -->
				<div class="file-status">
					<a-spin v-if="file.status === 'uploading'" size="small" />
					<check-circle-outlined v-else-if="file.status === 'done'" class="status-success" />
					<close-circle-outlined v-else-if="file.status === 'error'" class="status-error" />
				</div>

				<!-- 操作按钮 -->
				<div class="file-actions">
					<!-- 预览按钮 -->
					<a-button
						v-if="file.status === 'done' && (isImage(file) || isPdf(file))"
						type="link"
						size="small"
						@click="handlePreview(file, index)"
						title="预览"
					>
						<eye-outlined />
					</a-button>

					<!-- 下载按钮 -->
					<a-button v-if="file.status === 'done'" type="link" size="small" @click="handleDownload(file, index)" title="下载">
						<download-outlined />
					</a-button>

					<!-- 删除按钮 -->
					<a-button v-if="!disabled" type="link" size="small" danger @click="handleDelete(file, index)" title="删除">
						<delete-outlined />
					</a-button>
				</div>
			</div>
		</div>

		<!-- 图片预览模态框 -->
		<a-modal v-model:visible="previewVisible" title="文件预览" :footer="null" width="80%" centered>
			<div class="preview-content">
				<img
					v-if="previewFile && isImage(previewFile)"
					:src="getFileUrl(previewFile)"
					:alt="previewFile.name"
					style="max-width: 100%; max-height: 70vh; object-fit: contain"
				/>
				<iframe v-else-if="previewFile && isPdf(previewFile)" :src="getFileUrl(previewFile)" style="width: 100%; height: 70vh; border: none" />
				<div v-else class="preview-unsupported">
					<file-outlined style="font-size: 48px; color: #ccc" />
					<p>该文件类型不支持预览</p>
				</div>
			</div>
		</a-modal>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
	UploadOutlined,
	InboxOutlined,
	FileOutlined,
	PictureOutlined,
	EyeOutlined,
	DownloadOutlined,
	DeleteOutlined,
	CheckCircleOutlined,
	CloseCircleOutlined,
} from '@ant-design/icons-vue'
import { _headers } from '@/utils/request'
import dayjs from 'dayjs'

// 文件接口定义
interface FileItem {
	id?: string | number
	file_id?: string | number
	name: string
	path?: string
	size?: number
	type?: string
	status?: 'uploading' | 'done' | 'error'
	progress?: number
	uploadTime?: string
	url?: string
}

// Props定义
interface Props {
	modelValue?: FileItem[]
	multiple?: boolean
	disabled?: boolean
	showTime?: boolean
	showDragger?: boolean
	upType?: string
	maxSize?: number // MB
	accept?: string
	maxCount?: number
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: () => [],
	multiple: false,
	disabled: false,
	showTime: true,
	showDragger: false,
	upType: 'file',
	maxSize: 100,
	accept: '*/*',
	maxCount: 10,
})

// Emits定义
const emit = defineEmits<{
	'update:modelValue': [files: FileItem[]]
	change: [files: FileItem[]]
	preview: [file: FileItem, index: number]
	download: [file: FileItem, index: number]
	delete: [file: FileItem, index: number]
	'upload-success': [file: FileItem, response: any]
	error: [error: string, file?: File]
}>()

// 响应式数据
const fileList = ref<FileItem[]>([])
const uploading = ref(false)
const previewVisible = ref(false)
const previewFile = ref<FileItem | null>(null)

// 监听modelValue变化
watch(
	() => props.modelValue,
	(newVal) => {
		fileList.value = [...(newVal || [])]
	},
	{ immediate: true, deep: true }
)

// 计算属性
const canUpload = computed(() => {
	if (props.disabled) return false
	if (!props.multiple && fileList.value.length >= 1) return false
	if (props.multiple && fileList.value.length >= props.maxCount) return false
	return true
})

// 工具函数
const isImage = (file: FileItem) => {
	const imageTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp']
	return file.type ? imageTypes.includes(file.type) : /\.(png|jpe?g|gif|webp)$/i.test(file.name)
}

const isPdf = (file: FileItem) => {
	return file.type === 'application/pdf' || /\.pdf$/i.test(file.name)
}

const formatFileSize = (size: number) => {
	if (size < 1024) return `${size} B`
	if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
	return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const formatTime = (time: string) => {
	return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

const getFileUrl = (file: FileItem) => {
	const cdnBase = import.meta.env.VITE_APP_CDN || ''
	const filePath = file.url || file.path || ''

	// 如果已经是完整URL，直接返回
	if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
		return filePath
	}

	// 如果有CDN配置，使用CDN地址
	if (cdnBase && filePath) {
		return `${cdnBase}${filePath.startsWith('/') ? '' : '/'}${filePath}`
	}

	return filePath
}

// 上传前验证
const beforeUpload = (file: File) => {
	// 检查文件大小
	if (file.size > props.maxSize * 1024 * 1024) {
		const errorMsg = `文件大小不能超过 ${props.maxSize}MB`
		message.error(errorMsg)
		emit('error', errorMsg, file)
		return false
	}

	// 检查文件数量
	if (!canUpload.value) {
		const errorMsg = props.multiple ? `最多只能上传 ${props.maxCount} 个文件` : '只能上传一个文件'
		message.error(errorMsg)
		emit('error', errorMsg, file)
		return false
	}

	// 开始上传
	uploadFile(file)
	return false // 阻止默认上传
}

// 文件上传函数
const uploadFile = async (file: File) => {
	const fileItem: FileItem = {
		id: Date.now() + Math.random(),
		name: file.name,
		size: file.size,
		type: file.type,
		status: 'uploading',
		progress: 0,
		uploadTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	}

	// 添加到文件列表
	if (props.multiple) {
		fileList.value.push(fileItem)
	} else {
		fileList.value = [fileItem]
	}

	uploading.value = true

	try {
		// 构建FormData
		const formData = new FormData()
		let upName = file.name || ''

		// 图片类型处理
		const imageFileTypes = ['image/png', 'image/jpeg', 'image/jpg']
		if (imageFileTypes.indexOf(file.type) > -1) {
			formData.append('upType', 'no-compress-image')
		} else {
			formData.append('upType', props.upType || 'file')
		}

		// 去除文件名中的空格字符
		if (typeof upName === 'string') {
			upName = upName.replace(/\s*/g, '')
		}

		// 特殊类型处理
		if (props.upType === 'eval-file') {
			if (typeof window !== 'undefined') {
				/* let orgName = sessionStorage.getItem('_on')
				if (orgName) {
					try {
						orgName = decodeURIComponent(orgName) || ''
					} catch (e) {
						console.warn('解码组织名称失败:', e)
						orgName = orgName || ''
					}
					upName = upName.replace(/(\.[^.]+)$/, `_${orgName}$1`)
				} */
			}
		}

		formData.append('upfile', file)
		formData.append('up_name', upName)

		// 使用XMLHttpRequest上传以支持进度监听
		const xhr = new XMLHttpRequest()
		console.log('🚀 ~ xhr:', _headers());

		// 上传进度监听
		xhr.upload.addEventListener('progress', (event) => {
			if (event.lengthComputable) {
				const progress = Math.round((event.loaded / event.total) * 100)
				fileItem.progress = progress > 98 ? 98 : progress
				updateFileList()
			}
		})

		// 上传完成监听
		xhr.addEventListener('load', () => {
			if (xhr.status === 200) {
				try {
					const response = JSON.parse(xhr.responseText)
					console.log('文件上传响应:', response)

					// 根据实际接口返回格式处理数据
					let fileData
					if (Array.isArray(response) && response.length > 0) {
						fileData = response[0]
					} else if (response.data && Array.isArray(response.data)) {
						fileData = response.data[0]
					} else if (response.data) {
						fileData = response.data
					} else {
						fileData = response
					}

					// 更新文件信息
					fileItem.id = fileData.id || fileData.file_id
					fileItem.file_id = fileData.file_id || fileData.id
					fileItem.name = fileData.file_name || fileData.name || fileItem.name
					fileItem.path = fileData.path
					fileItem.url = fileData.path || fileData.url
					fileItem.size = fileData.size || fileItem.size
					fileItem.status = 'done'
					fileItem.progress = 100

					updateFileList()
					message.success('文件上传成功')

					// 返回完整的文件信息给父组件
					emit('upload-success', fileItem, response)
				} catch (error) {
					console.error('解析响应失败:', error, xhr.responseText)
					fileItem.status = 'error'
					updateFileList()
					message.error('文件上传失败：响应格式错误')
					emit('error', '解析响应失败', file)
				}
			} else {
				fileItem.status = 'error'
				updateFileList()
				message.error(`文件上传失败：${xhr.status} ${xhr.statusText}`)
				emit('error', `上传失败: ${xhr.status} ${xhr.statusText}`, file)
			}
			uploading.value = false
		})

		// 上传错误监听
		xhr.addEventListener('error', () => {
			fileItem.status = 'error'
			updateFileList()
			message.error('文件上传失败')
			emit('error', '网络错误')
			uploading.value = false
		})

		// 发送请求
		const baseURL = '' //import.meta.env.VITE_APP_BASE_API || ''
		const uploadUrl = '/file/file/upload'
		const fullUrl = baseURL ? `${baseURL}${uploadUrl}` : uploadUrl
		xhr.open('POST', fullUrl)

		// 设置请求头 - 只设置安全的请求头
		const headers = _headers()
		const safeHeaders = {
			'_tk': headers._tk || '',
			'_uid': headers._uid || '',
			'_type': headers._type || '',
			'_region_id': '3',
			'_oid': headers._oid || '',
			'_org_type': headers._org_type || '',
			'x-csrf-token': headers['x-csrf-token'] || '',
			'_cl': headers._cl || 'SERVER'
		}

		Object.keys(safeHeaders).forEach(key => {
			try {
				const value = safeHeaders[key as keyof typeof safeHeaders]
				if (value && typeof value === 'string') {
					// 确保值只包含ASCII字符
					const cleanValue = value.replace(/[^\x00-\x7F]/g, '')
					if (cleanValue) {
						xhr.setRequestHeader(key, cleanValue)
					}
				}
			} catch (error) {
				console.warn(`设置请求头失败: ${key}`, error)
			}
		})

		xhr.send(formData)
	} catch (error) {
		console.error('上传失败:', error)
		fileItem.status = 'error'
		updateFileList()
		message.error('文件上传失败')
		emit('error', '上传失败')
		uploading.value = false
	}
}

// 更新文件列表
const updateFileList = () => {
	console.log('🚀 ~ fileList.value:', fileList.value);
	emit('update:modelValue', [...fileList.value])
	emit('change', [...fileList.value])
}

// 预览文件
const handlePreview = (file: FileItem, index: number) => {
	previewFile.value = file
	previewVisible.value = true
	emit('preview', file, index)
}

// 下载文件
const handleDownload = (file: FileItem, index: number) => {
	if (!file.path && !file.url) {
		message.error('文件路径不存在')
		return
	}

	const url = getFileUrl(file)
	if (!url) {
		message.error('文件地址无效')
		return
	}

	const link = document.createElement('a')
	link.href = url
	link.download = file.name
	link.target = '_blank'
	document.body.appendChild(link)
	link.click()
	document.body.removeChild(link)

	emit('download', file, index)
}

// 删除文件
const handleDelete = (file: FileItem, index: number) => {
	fileList.value.splice(index, 1)
	updateFileList()
	message.success('文件删除成功')
	emit('delete', file, index)
}

// 暴露方法给父组件
defineExpose({
	fileList,
	uploadFile,
	clearFiles: () => {
		fileList.value = []
		updateFileList()
	},
})
</script>

<style lang="scss" scoped>
.file-upload-component {
	.upload-area {
		margin-bottom: 16px;

		.upload-trigger {
			margin-bottom: 8px;
		}

		.upload-dragger {
			margin-top: 8px;
		}
	}

	.file-list {
		.file-item {
			display: flex;
			align-items: center;
			padding: 12px;
			border: 1px solid #d9d9d9;
			border-radius: 6px;
			margin-bottom: 8px;
			background: #fafafa;
			transition: all 0.3s;

			&:hover {
				border-color: #40a9ff;
				background: #f0f8ff;
			}

			&.file-item-error {
				border-color: #ff4d4f;
				background: #fff2f0;
			}

			.file-icon {
				margin-right: 12px;
				font-size: 24px;
				color: #1890ff;
				flex-shrink: 0;
			}

			.file-info {
				flex: 1;
				min-width: 0;

				.file-name {
					font-weight: 500;
					color: #262626;
					margin-bottom: 4px;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.file-meta {
					display: flex;
					gap: 12px;
					font-size: 12px;
					color: #8c8c8c;

					.file-size {
						&::before {
							content: '📁 ';
						}
					}

					.upload-time {
						&::before {
							content: '🕒 ';
						}
					}
				}
			}

			.file-status {
				margin: 0 12px;
				flex-shrink: 0;

				.status-success {
					color: #52c41a;
					font-size: 16px;
				}

				.status-error {
					color: #ff4d4f;
					font-size: 16px;
				}
			}

			.file-actions {
				display: flex;
				gap: 4px;
				flex-shrink: 0;

				.ant-btn {
					padding: 4px;
					height: auto;
					border: none;
					box-shadow: none;

					&:hover {
						background: rgba(24, 144, 255, 0.1);
					}
				}
			}
		}
	}

	.preview-content {
		text-align: center;

		.preview-unsupported {
			padding: 40px;
			color: #8c8c8c;

			p {
				margin-top: 16px;
				font-size: 14px;
			}
		}
	}
}

// 全局样式覆盖
:deep(.ant-upload-dragger) {
	border: 2px dashed #d9d9d9 !important;
	border-radius: 6px !important;
	background: #fafafa !important;
	transition: all 0.3s !important;

	&:hover {
		border-color: #40a9ff !important;
		background: #f0f8ff !important;
	}

	.ant-upload-drag-icon {
		margin-bottom: 16px !important;

		.anticon {
			font-size: 48px !important;
			color: #40a9ff !important;
		}
	}

	.ant-upload-text {
		font-size: 16px !important;
		color: #262626 !important;
		margin-bottom: 8px !important;
	}

	.ant-upload-hint {
		font-size: 14px !important;
		color: #8c8c8c !important;
	}
}

:deep(.ant-progress-line) {
	margin-top: 4px;
}
</style>
