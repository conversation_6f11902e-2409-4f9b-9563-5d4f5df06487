# 文件上传组件 FileUpload

一个功能完整的Vue 3文件上传组件，支持文件上传、回显、删除和下载操作。

## 功能特性

- ✅ 文件上传（支持单个/多个文件）
- ✅ 拖拽上传
- ✅ 上传进度显示
- ✅ 文件预览（图片、PDF）
- ✅ 文件下载
- ✅ 文件删除
- ✅ 文件大小限制
- ✅ 文件类型限制
- ✅ 上传状态管理
- ✅ 错误处理

## 基本用法

```vue
<template>
  <div>
    <FileUpload
      v-model="fileList"
      :multiple="true"
      :max-size="10"
      :show-dragger="true"
      @change="handleFileChange"
      @error="handleError"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/file-upload/index.vue'

const fileList = ref([])

const handleFileChange = (files) => {
  console.log('文件列表变化:', files)
}

const handleError = (error, file) => {
  console.error('上传错误:', error, file)
}
</script>
```

## 在荣誉申报中的使用

```vue
<template>
  <a-form-item label="申报材料" name="materialFiles">
    <FileUpload
      v-model="form.materialFiles"
      :multiple="true"
      :max-size="50"
      :max-count="5"
      :show-dragger="true"
      up-type="eval-file"
      accept=".pdf,.doc,.docx,.jpg,.png"
      @change="handleMaterialChange"
    />
  </a-form-item>
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/file-upload/index.vue'

const form = ref({
  materialFiles: []
})

const handleMaterialChange = (files) => {
  // 更新表单中的材料URL字段
  if (files.length > 0) {
    form.value.materialUrl = files.map(f => f.path).join(',')
  } else {
    form.value.materialUrl = ''
  }
}
</script>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | FileItem[] | [] | 文件列表，支持v-model |
| multiple | boolean | false | 是否支持多选文件 |
| disabled | boolean | false | 是否禁用 |
| showTime | boolean | true | 是否显示上传时间 |
| showDragger | boolean | false | 是否显示拖拽上传区域 |
| upType | string | 'file' | 上传类型 |
| maxSize | number | 100 | 文件大小限制(MB) |
| accept | string | '*/*' | 接受的文件类型 |
| maxCount | number | 10 | 最大文件数量 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | files: FileItem[] | 文件列表更新 |
| change | files: FileItem[] | 文件列表变化 |
| preview | file: FileItem, index: number | 预览文件 |
| download | file: FileItem, index: number | 下载文件 |
| delete | file: FileItem, index: number | 删除文件 |
| error | error: string, file?: File | 上传错误 |

## FileItem 接口

```typescript
interface FileItem {
  id?: string | number
  file_id?: string | number
  name: string
  path?: string
  size?: number
  type?: string
  status?: 'uploading' | 'done' | 'error'
  progress?: number
  uploadTime?: string
  url?: string
}
```

## 暴露的方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| uploadFile | file: File | 手动上传文件 |
| clearFiles | - | 清空所有文件 |

## 样式定制

组件使用了SCSS，可以通过CSS变量或覆盖样式来定制外观：

```scss
.file-upload-component {
  // 自定义样式
  .file-item {
    border-radius: 8px;
    // ...
  }
}
```

## 注意事项

1. 确保后端接口 `/file/file/upload` 可用
2. 图片文件会自动设置为无压缩模式
3. 支持的预览格式：图片(png,jpg,jpeg,gif,webp)、PDF
4. 文件下载使用浏览器默认下载方式
5. 组件会自动处理文件名中的空格字符
