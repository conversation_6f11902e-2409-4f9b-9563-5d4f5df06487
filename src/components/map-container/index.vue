<template>
    <div class="amap-container-box">
        <div ref="mapRef" id="amap-container" class="amap-container" :style="{ height: containerHeight }"></div>

        <!-- 搜索面板 -->
        <div id="place-panel" class="place-panel"></div>

        <!-- 搜索输入框 -->
        <div v-if="showSearch && mapSearchPlan" class="search-input-container">
            <a-input id="place_input_id" v-model:value="searchKeyword" placeholder="请输入关键字后回车搜索" style="width: 320px"
                @change="handleInputChange" @pressEnter="handleSearch">
                <template #prefix>
                    <SearchOutlined />
                </template>
            </a-input>
        </div>

        <!-- 地图控制按钮 -->
        <div class="map-controls" v-if="showControls">
            <a-button-group size="small">
                <a-button @click="zoomIn" :disabled="currentZoom >= 18">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                </a-button>
                <a-button @click="zoomOut" :disabled="currentZoom <= 3">
                    <template #icon>
                        <MinusOutlined />
                    </template>
                </a-button>
            </a-button-group>

            <a-button size="small" @click="resetView" style="margin-top: 8px;">
                <template #icon>
                    <AimOutlined />
                </template>
                重置视图
            </a-button>
        </div>

        <!-- 标记信息弹窗 -->
        <a-modal v-model:visible="markerInfoVisible" :title="selectedMarker?.name" width="500px" :footer="null">
            <div v-if="selectedMarker" class="marker-info">
                <a-descriptions :column="1" bordered size="small">
                    <a-descriptions-item label="单位名称">
                        {{ selectedMarker.name }}
                    </a-descriptions-item>
                    <a-descriptions-item label="地址" v-if="selectedMarker.address">
                        {{ selectedMarker.address }}
                    </a-descriptions-item>
                    <a-descriptions-item label="联系人" v-if="selectedMarker.contactPerson">
                        {{ selectedMarker.contactPerson }}
                    </a-descriptions-item>
                    <a-descriptions-item label="联系电话" v-if="selectedMarker.contactPhone">
                        {{ selectedMarker.contactPhone }}
                    </a-descriptions-item>
                    <a-descriptions-item label="党建指数" v-if="selectedMarker.score">
                        <a-tag :color="getLevelColor(selectedMarker.level)">
                            {{ selectedMarker.score }}分
                        </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="等级" v-if="selectedMarker.level">
                        <a-tag :color="getLevelColor(selectedMarker.level)">
                            {{ selectedMarker.level }}级
                        </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="描述" v-if="selectedMarker.description">
                        {{ selectedMarker.description }}
                    </a-descriptions-item>
                </a-descriptions>
            </div>
        </a-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { SearchOutlined, PlusOutlined, MinusOutlined, AimOutlined } from '@ant-design/icons-vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import { getAmapConfig, getDefaultMapOptions, validateMapConfig, setMapSecurity } from '@/config/map'
import type { MapMarker, MapViewport, MapContainerProps, MapContainerEmits } from './types'

// Props定义
interface Props extends MapContainerProps {
    mapHeight?: string | number
    showSearch?: boolean
    showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    mapHeight: '500px',
    showSearch: true,
    showControls: true,
    markers: () => [],
    viewport: () => ({ center: [106.550483, 29.563707], zoom: 11 }),
    showMarkers: true,
    showModelOrgansOnly: false,
    showWarningUnits: false,
    selectedLevel: ''
})

// Emits定义
const emit = defineEmits<MapContainerEmits & {
    'place-select': [data: any]
    'data-filter': [filterData: {
        type: 'marker-select' | 'reset'
        data?: any
        unitId?: string | number
        unitName?: string
        level?: string
        score?: number
    }]
}>()

// 响应式数据
const mapRef = ref<HTMLElement>()
const map = ref<any>(null)
const AMap = ref<any>(null)
const mapSearchPlan = ref<any>(null)
const searchKeyword = ref('')
const currentZoom = ref(11)
const markerInfoVisible = ref(false)
const selectedMarker = ref<MapMarker | null>(null)
const markers = ref<any[]>([])

// 计算属性
const containerHeight = computed(() => {
    if (typeof props.mapHeight === 'number') {
        return `${props.mapHeight}px`
    }
    return props.mapHeight
})

// 方法
const initMap = async () => {
    try {
        // 获取地图配置
        const mapConfig = getAmapConfig()
        const defaultOptions = getDefaultMapOptions()

        // 验证配置
        if (!validateMapConfig(mapConfig)) {
            throw new Error('地图配置验证失败')
        }

        // 设置安全密钥
        setMapSecurity(mapConfig.securityCode)

        // 加载高德地图
        const AMapInstance = await AMapLoader.load({
            key: mapConfig.key,
            version: mapConfig.version,
            plugins: mapConfig.plugins,
        })

        AMap.value = AMapInstance

        // 创建地图实例
        map.value = new AMapInstance.Map('amap-container', {
            zoom: props.viewport.zoom || defaultOptions.zoom,
            center: props.viewport.center || defaultOptions.center,
            viewMode: defaultOptions.viewMode,
            features: defaultOptions.features,
            mapStyle: defaultOptions.mapStyle
        })

        // 监听地图完成加载事件
        map.value.on('complete', () => {
            initPlaceSearch()
            initMapEvents()
            updateMarkers()
        })

        // 监听缩放变化
        map.value.on('zoomchange', () => {
            currentZoom.value = map.value.getZoom()
        })

        console.log('地图初始化完成')
    } catch (error) {
        console.error('地图加载失败:', error)
        // 显示用户友好的错误提示
        if (error instanceof Error) {
            console.error('错误详情:', error.message)
        }
    }
}

const initPlaceSearch = () => {
    if (!AMap.value || !map.value) return

    // 创建地点搜索插件
    mapSearchPlan.value = new AMap.value.PlaceSearch({
        pageSize: 5,
        pageIndex: 1,
        citylimit: true,
        map: map.value,
        panel: 'place-panel',
        autoFitView: true,
    })

    // 监听搜索结果点击事件
    mapSearchPlan.value.on('listElementClick', (event: any) => {
        emit('place-select', event.data)
    })

    mapSearchPlan.value.on('markerClick', (event: any) => {
        emit('place-select', event.data)
    })
}

const initMapEvents = () => {
    if (!map.value) return

    // 监听地图点击事件
    map.value.on('click', (event: any) => {
        const position: [number, number] = [event.lnglat.lng, event.lnglat.lat]
        emit('map-click', position)
    })

    // 监听视图变化事件
    map.value.on('moveend', () => {
        const center = map.value.getCenter()
        const zoom = map.value.getZoom()
        const viewport: MapViewport = {
            center: [center.lng, center.lat],
            zoom: zoom
        }
        emit('viewport-change', viewport)
    })
}

const updateMarkers = () => {
    if (!map.value || !AMap.value || !props.showMarkers) return

    // 清除现有标记
    clearMarkers()

    // 过滤标记数据
    let filteredMarkers = props.markers

    if (props.showModelOrgansOnly) {
        filteredMarkers = filteredMarkers.filter(marker => marker.type === 'model')
    }

    if (props.showWarningUnits) {
        filteredMarkers = filteredMarkers.filter(marker => marker.type === 'warning')
    }

    if (props.selectedLevel) {
        filteredMarkers = filteredMarkers.filter(marker => marker.level === props.selectedLevel)
    }

    // 创建新标记
    filteredMarkers.forEach(markerData => {
        const marker = new AMap.value.Marker({
            position: markerData.position,
            title: markerData.name,
            icon: getMarkerIcon(markerData),
            anchor: 'bottom-center'
        })

        // 添加点击事件
        marker.on('click', () => {
            selectedMarker.value = markerData
            markerInfoVisible.value = true

            // 发送标记点击事件，用于数据联动
            emit('marker-click', markerData)

            // 发送数据联动事件，通知父组件更新右侧数据面板
            emit('data-filter', {
                type: 'marker-select',
                data: markerData,
                unitId: markerData.id,
                unitName: markerData.name,
                level: markerData.level,
                score: markerData.score
            })
        })

        // 添加到地图
        map.value.add(marker)
        markers.value.push(marker)
    })
}

const clearMarkers = () => {
    if (markers.value.length > 0) {
        map.value.remove(markers.value)
        markers.value = []
    }
}

const getMarkerIcon = (marker: MapMarker) => {
    const colors = {
        'A': '#00EE63',
        'B': '#1A9FFF',
        'C': '#F8B523',
        'D': '#FF6C00',
        'E': '#FF4042'
    }

    const color = colors[marker.level as keyof typeof colors] || '#1890ff'

    return new AMap.value.Icon({
        size: new AMap.value.Size(24, 32),
        image: `data:image/svg+xml;base64,${btoa(`
      <svg width="24" height="32" viewBox="0 0 24 32" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 0C5.373 0 0 5.373 0 12c0 9 12 20 12 20s12-11 12-20c0-6.627-5.373-12-12-12z" fill="${color}"/>
        <circle cx="12" cy="12" r="6" fill="white"/>
      </svg>
    `)}`,
        imageOffset: new AMap.value.Pixel(0, 0)
    })
}

const getLevelColor = (level?: string) => {
    const colorMap = {
        'A': 'green',
        'B': 'blue',
        'C': 'orange',
        'D': 'volcano',
        'E': 'red'
    }
    return colorMap[level as keyof typeof colorMap] || 'default'
}

const handleInputChange = (e: Event) => {
    const target = e.target as HTMLInputElement
    searchKeyword.value = target.value
}

const handleSearch = () => {
    if (mapSearchPlan.value && searchKeyword.value.trim()) {
        mapSearchPlan.value.search(searchKeyword.value.trim())
    }
}

const zoomIn = () => {
    if (map.value && currentZoom.value < 18) {
        map.value.zoomIn()
    }
}

const zoomOut = () => {
    if (map.value && currentZoom.value > 3) {
        map.value.zoomOut()
    }
}

const resetView = () => {
    if (map.value) {
        map.value.setZoomAndCenter(props.viewport.zoom, props.viewport.center)
    }
}

// 导航路线相关
const navigationRoute = ref<any>(null)
const currentRouteService = ref<any>(null)

const showNavigationRoute = async (startCoords: [number, number], endCoords: [number, number], travelMode: string = 'driving') => {
    if (!map.value || !AMap.value) {
        console.error('地图未初始化')
        return
    }

    try {
        // 清除之前的路线
        if (navigationRoute.value) {
            map.value.remove(navigationRoute.value)
            navigationRoute.value = null
        }

        // 根据出行方式选择路径规划服务
        let routeService: any

        switch (travelMode) {
            case 'walking':
                routeService = new AMap.value.Walking({
                    map: map.value,
                    panel: null
                })
                break
            case 'transit':
                routeService = new AMap.value.Transfer({
                    map: map.value,
                    panel: null,
                    city: '重庆市'
                })
                break
            case 'driving':
            default:
                routeService = new AMap.value.Driving({
                    map: map.value,
                    panel: null,
                    hideMarkers: false
                })
                break
        }

        // 保存当前路径规划服务引用
        currentRouteService.value = routeService
        console.log('执行搜索', startCoords, endCoords);
        // 执行路径规划
        return new Promise((resolve, reject) => {
            const searchCallback = (status: string, result: any) => {
                if (status === 'complete') {
                    console.log('导航路线规划成功:', result)

                    // 保存路线引用
                    navigationRoute.value = result

                    // 自动调整地图视图以显示完整路线
                    if (result.routes && result.routes.length > 0) {
                        const route = result.routes[0]
                        if (route.bounds) {
                            map.value.setBounds(route.bounds)
                        }
                    }

                    resolve(result)
                } else {
                    console.error('导航路线规划失败:', result)
                    reject(new Error('路线规划失败'))
                }
            }


            // 执行搜索
            if (travelMode === 'transit') {
                routeService.search(startCoords, endCoords, searchCallback)
            } else {
                routeService.search(startCoords, endCoords, searchCallback)
            }
        })
    } catch (error) {
        console.error('显示导航路线失败:', error)
        throw error
    }
}

const clearNavigationRoute = () => {
    console.log('开始清除导航路线')

    try {
        // 清除路径规划服务创建的所有元素
        if (currentRouteService.value) {
            // 调用路径规划服务的clear方法来清除所有相关元素
            if (typeof currentRouteService.value.clear === 'function') {
                currentRouteService.value.clear()
                console.log('已调用路径规划服务的clear方法')
            }

            // 销毁路径规划服务实例
            if (typeof currentRouteService.value.destroy === 'function') {
                currentRouteService.value.destroy()
                console.log('已销毁路径规划服务实例')
            }

            currentRouteService.value = null
        }

        // 清除保存的路线引用
        if (navigationRoute.value && map.value) {
            map.value.remove(navigationRoute.value)
            navigationRoute.value = null
            console.log('已移除地图上的路线对象')
        }

        // 强制清除地图上所有可能的路线相关覆盖物
        if (map.value) {
            // 获取地图上所有覆盖物
            const overlays = map.value.getAllOverlays()
            if (overlays && overlays.length > 0) {
                // 过滤出路线相关的覆盖物并移除
                overlays.forEach((overlay: any) => {
                    if (overlay.CLASS_NAME && (
                        overlay.CLASS_NAME.includes('Polyline') ||
                        overlay.CLASS_NAME.includes('Route') ||
                        overlay.CLASS_NAME.includes('Driving') ||
                        overlay.CLASS_NAME.includes('Walking') ||
                        overlay.CLASS_NAME.includes('Transfer')
                    )) {
                        map.value.remove(overlay)
                        console.log('已移除路线相关覆盖物:', overlay.CLASS_NAME)
                    }
                })
            }
        }

        console.log('导航路线清除完成')
    } catch (error) {
        console.error('清除导航路线时出错:', error)
    }
}

// 暴露方法给父组件
defineExpose({
    showNavigationRoute,
    clearNavigationRoute,
    resetView,
    zoomIn,
    zoomOut
})

// 监听props变化
watch(() => props.markers, () => {
    updateMarkers()
}, { deep: true })

watch(() => [props.showMarkers, props.showModelOrgansOnly, props.showWarningUnits, props.selectedLevel], () => {
    updateMarkers()
})

watch(() => props.viewport, (newViewport) => {
    if (map.value) {
        map.value.setZoomAndCenter(newViewport.zoom, newViewport.center)
    }
}, { deep: true })

// 生命周期
onMounted(async () => {
    await nextTick()
    initMap()
})

onUnmounted(() => {
    if (map.value) {
        map.value.destroy()
    }
})
</script>

<style scoped lang="scss">
.amap-container-box {
    position: relative;
    width: 100%;
    height: 100%;

    .amap-container {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;
    }

    .place-panel {
        position: absolute;
        top: 60px;
        left: 10px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        max-height: 300px;
        overflow-y: auto;
        z-index: 10;
    }

    .search-input-container {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 20;
    }

    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 20;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .marker-info {
        .ant-descriptions {
            margin-top: 16px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .amap-container-box {
        .search-input-container {
            :deep(.ant-input) {
                width: 250px !important;
            }
        }
    }
}

@media (max-width: 480px) {
    .amap-container-box {
        .search-input-container {
            :deep(.ant-input) {
                width: 200px !important;
            }
        }
    }
}
</style>