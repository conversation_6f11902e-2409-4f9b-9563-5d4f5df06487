export interface MapMarker {
  id: string
  name: string
  position: [number, number] // [lng, lat]
  type?: 'unit' | 'model' | 'warning'
  level?: 'A' | 'B' | 'C' | 'D' | 'E'
  score?: number
  description?: string
  address?: string
  contactPerson?: string
  contactPhone?: string
}

export interface MapViewport {
  center: [number, number]
  zoom: number
}

export interface MapContainerProps {
  markers?: MapMarker[]
  viewport?: MapViewport
  showMarkers?: boolean
  showModelOrgansOnly?: boolean
  showWarningUnits?: boolean
  selectedLevel?: string
  height?: string | number
}

export interface MapContainerEmits {
  'marker-click': [marker: MapMarker]
  'map-click': [position: [number, number]]
  'viewport-change': [viewport: MapViewport]
}