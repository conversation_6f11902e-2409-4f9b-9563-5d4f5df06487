/**
 * 路由测试工具
 * 用于验证敏感词管理模块的所有路由是否正常工作
 */

import { Router } from 'vue-router'

// 敏感词管理模块的所有路由
export const sensitiveWordsRoutes = [
  {
    path: '/sensitive-words',
    name: 'SensitiveWords',
    title: '敏感词管理（重定向）'
  },
  {
    path: '/sensitive-words/index',
    name: 'SensitiveWordsIndex',
    title: '敏感词管理首页'
  },
  {
    path: '/sensitive-words/word-library',
    name: 'SensitiveWordsLibrary',
    title: '敏感词库管理'
  },
  {
    path: '/sensitive-words/word-manage',
    name: 'SensitiveWordsManage',
    title: '敏感词管理'
  },
  {
    path: '/sensitive-words/policy-manage',
    name: 'SensitiveWordsPolicyManage',
    title: '过滤策略管理'
  },
  {
    path: '/sensitive-words/detection',
    name: 'SensitiveWordsDetection',
    title: '内容检测'
  },
  {
    path: '/sensitive-words/test',
    name: 'SensitiveWordsTest',
    title: '敏感词测试'
  }
]

// 路由测试结果接口
export interface RouteTestResult {
  path: string
  name: string
  title: string
  success: boolean
  error?: string
  timestamp: string
}

/**
 * 测试单个路由
 * @param router Vue Router 实例
 * @param route 要测试的路由
 * @returns 测试结果
 */
export async function testSingleRoute(
  router: Router, 
  route: { path: string; name: string; title: string }
): Promise<RouteTestResult> {
  const result: RouteTestResult = {
    path: route.path,
    name: route.name,
    title: route.title,
    success: false,
    timestamp: new Date().toISOString()
  }

  try {
    // 尝试导航到路由
    await router.push(route.path)
    
    // 检查当前路由是否匹配
    const currentRoute = router.currentRoute.value
    if (currentRoute.path === route.path || 
        (route.path === '/sensitive-words' && currentRoute.path === '/sensitive-words/index')) {
      result.success = true
    } else {
      result.success = false
      result.error = `路由不匹配: 期望 ${route.path}, 实际 ${currentRoute.path}`
    }
  } catch (error: any) {
    result.success = false
    result.error = error.message || '路由跳转失败'
  }

  return result
}

/**
 * 测试所有敏感词路由
 * @param router Vue Router 实例
 * @returns 所有路由的测试结果
 */
export async function testAllSensitiveWordsRoutes(router: Router): Promise<RouteTestResult[]> {
  const results: RouteTestResult[] = []
  
  for (const route of sensitiveWordsRoutes) {
    const result = await testSingleRoute(router, route)
    results.push(result)
    
    // 添加小延迟避免路由冲突
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  return results
}

/**
 * 生成路由测试报告
 * @param results 测试结果数组
 * @returns 格式化的测试报告
 */
export function generateRouteTestReport(results: RouteTestResult[]): string {
  const successCount = results.filter(r => r.success).length
  const failCount = results.length - successCount
  
  let report = `敏感词管理路由测试报告\n`
  report += `=========================\n`
  report += `测试时间: ${new Date().toLocaleString()}\n`
  report += `总路由数: ${results.length}\n`
  report += `成功: ${successCount}\n`
  report += `失败: ${failCount}\n`
  report += `成功率: ${((successCount / results.length) * 100).toFixed(2)}%\n\n`
  
  report += `详细结果:\n`
  report += `---------\n`
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ 成功' : '❌ 失败'
    report += `${index + 1}. ${result.title} (${result.path})\n`
    report += `   状态: ${status}\n`
    if (result.error) {
      report += `   错误: ${result.error}\n`
    }
    report += `   时间: ${result.timestamp}\n\n`
  })
  
  return report
}

/**
 * 在控制台输出路由测试报告
 * @param results 测试结果数组
 */
export function logRouteTestReport(results: RouteTestResult[]): void {
  const report = generateRouteTestReport(results)
  console.log(report)
}
