/**
 * 页面优化工具函数
 * 提供统一的loading管理、背景生成、图片占位符等功能
 */

import type { CSSProperties } from 'vue';

/**
 * 页面Loading管理配置
 */
export interface PageLoadingOptions {
  hideLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
}

/**
 * 统一的页面Loading管理
 * @param options 配置选项
 * @returns Loading管理对象
 */
export function usePageLoading(options: PageLoadingOptions = {}) {
  const defaultOptions = {
    hideLoading: true,
    showError: false,
    showSuccess: false,
    ...options
  };

  return {
    // 执行异步操作，但不显示loading
    execute: async <T>(
      asyncFn: () => Promise<T>,
      config?: {
        showLoading?: boolean;
        showError?: boolean;
        loadingMessage?: string;
        errorMessage?: string;
      }
    ): Promise<T> => {
      try {
        // 不显示loading状态，直接执行
        const result = await asyncFn();
        return result;
      } catch (error) {
        if (config?.showError && !defaultOptions.hideLoading) {
          console.error(config.errorMessage || '操作失败', error);
        }
        throw error;
      }
    },
    
    // 配置选项
    options: defaultOptions
  };
}

/**
 * 主题色彩配置
 */
const THEME_GRADIENTS: Record<string, string> = {
  '数字化转型': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  '绿色发展': 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)',
  '党建创新': 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)',
  '技术创新': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  '管理创新': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  '服务创新': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  '案例征集': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  '活动管理': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
  '审核管理': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
  '分类管理': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
  '默认': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
};

/**
 * 生成默认背景样式
 * @param theme 主题名称
 * @param type 背景类型
 * @returns CSS样式对象
 */
export function generateDefaultBackground(
  theme: string = '默认',
  type: 'card' | 'page' | 'placeholder' = 'card'
): CSSProperties {
  const gradient = THEME_GRADIENTS[theme] || THEME_GRADIENTS['默认'];
  
  const baseStyle: CSSProperties = {
    background: gradient,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    textAlign: 'center'
  };

  switch (type) {
    case 'card':
      return {
        ...baseStyle,
        height: '200px',
        borderRadius: '8px',
        fontSize: '16px',
        fontWeight: '500'
      };
    
    case 'page':
      return {
        ...baseStyle,
        minHeight: '100vh',
        fontSize: '24px',
        fontWeight: '600'
      };
    
    case 'placeholder':
      return {
        ...baseStyle,
        width: '100%',
        height: '150px',
        borderRadius: '4px',
        fontSize: '14px',
        opacity: '0.9'
      };
    
    default:
      return baseStyle;
  }
}

/**
 * 图片占位符状态
 */
export interface ImagePlaceholderState {
  src: string;
  isLoading: boolean;
  hasError: boolean;
  showPlaceholder: boolean;
}

/**
 * 图片占位符配置
 */
export interface ImagePlaceholderOptions {
  theme?: string;
  type?: 'card' | 'page' | 'placeholder';
  showImage?: boolean;
  placeholderText?: string;
  iconType?: string;
}

/**
 * 图片占位符处理
 * @param src 图片源地址
 * @param options 配置选项
 * @returns 图片状态和样式
 */
export function useImagePlaceholder(
  src: string = '',
  options: ImagePlaceholderOptions = {}
): ImagePlaceholderState & {
  placeholderStyle: CSSProperties;
  placeholderContent: {
    text: string;
    icon: string;
  };
} {
  const {
    theme = '默认',
    type = 'placeholder',
    showImage = false,
    placeholderText = '暂无图片',
    iconType = 'picture'
  } = options;

  const state: ImagePlaceholderState = {
    src: showImage ? src : '',
    isLoading: false,
    hasError: false,
    showPlaceholder: !showImage || !src
  };

  const placeholderStyle = generateDefaultBackground(theme, type);
  
  const placeholderContent = {
    text: placeholderText,
    icon: iconType
  };

  return {
    ...state,
    placeholderStyle,
    placeholderContent
  };
}

/**
 * 获取主题列表
 * @returns 可用的主题列表
 */
export function getAvailableThemes(): string[] {
  return Object.keys(THEME_GRADIENTS);
}

/**
 * 检查是否为有效主题
 * @param theme 主题名称
 * @returns 是否为有效主题
 */
export function isValidTheme(theme: string): boolean {
  return theme in THEME_GRADIENTS;
}

/**
 * 获取随机主题
 * @returns 随机主题名称
 */
export function getRandomTheme(): string {
  const themes = getAvailableThemes().filter(t => t !== '默认');
  return themes[Math.floor(Math.random() * themes.length)] || '默认';
}

/**
 * 页面数据处理工具
 */
export class PageDataProcessor {
  /**
   * 批量验证数据列表
   * @param dataList 数据列表
   * @param validator 验证函数
   * @returns 验证后的数据列表
   */
  static validateList<T>(dataList: any[], validator: (item: any) => T | null): T[] {
    if (!Array.isArray(dataList)) {
      return [];
    }
    return dataList.map(validator).filter(Boolean) as T[];
  }

  /**
   * 安全的分页数据处理
   * @param response API响应数据
   * @param validator 数据验证函数
   * @returns 处理后的分页数据
   */
  static processPaginationData<T>(
    response: any,
    validator: (item: any) => T | null
  ): {
    list: T[];
    total: number;
    current: number;
    pageSize: number;
  } {
    const data = response?.data || {};
    return {
      list: this.validateList(data.list || [], validator),
      total: Number(data.total) || 0,
      current: Number(data.current) || 1,
      pageSize: Number(data.pageSize) || 10
    };
  }

  /**
   * 处理统计数据
   * @param response API响应数据
   * @returns 处理后的统计数据
   */
  static processStatisticsData(response: any): Record<string, number> {
    const data = response?.data || {};
    const result: Record<string, number> = {};
    
    for (const [key, value] of Object.entries(data)) {
      result[key] = Number(value) || 0;
    }
    
    return result;
  }
}

/**
 * 导出所有工具函数
 */
export {
  THEME_GRADIENTS
};

export default {
  usePageLoading,
  generateDefaultBackground,
  useImagePlaceholder,
  getAvailableThemes,
  isValidTheme,
  getRandomTheme,
  PageDataProcessor
};
