export const getToken = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_tk') || '-1' : '-1';
export const getUserId = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_uid') || '-1' : '-1';
export const getCsrf = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('csrf') || '' : '';
export const getType = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_type') || '0' : '-1';
export const getOid = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_oid') || '1' : '-1';
export const getOrgName = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_org_name') || '-1' : '-1';
export const getOrgType = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_org_type') || '-1' : '-1';
export const getName = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_un') || '' : '-1';
export const getMenuId = (): string => typeof window !== 'undefined' ? window.sessionStorage.getItem('_menu_id') || '' : '-1';
export const getRegionId = (): string => {
  if (typeof window === 'undefined') return '3';
  const { regionId } = JSON.parse(window.sessionStorage.getItem('userInfo') || '{}');
  // @ts-ignore
  return regionId || (window as any).__TOG__?.regionId || '3';
};

export const logout = async () => Promise.resolve({ data: { code: 0 } }); 