/**
 * 数据验证和处理工具函数
 * 确保数据的完整性和安全性，避免NaN、undefined等问题
 */

/**
 * 安全的数字转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 安全的数字
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }
  
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 安全的字符串转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 安全的字符串
 */
export function safeString(value: any, defaultValue: string = ''): string {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  return String(value);
}

/**
 * 安全的数组转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 安全的数组
 */
export function safeArray<T>(value: any, defaultValue: T[] = []): T[] {
  if (!Array.isArray(value)) {
    return defaultValue;
  }
  return value;
}

/**
 * 安全的对象转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 安全的对象
 */
export function safeObject<T extends Record<string, any>>(value: any, defaultValue: T): T {
  if (typeof value !== 'object' || value === null || Array.isArray(value)) {
    return defaultValue;
  }
  return value as T;
}

/**
 * 验证和处理活动数据
 * @param activity 活动数据
 * @returns 处理后的活动数据
 */
export function validateActivity(activity: any): any {
  if (!activity || typeof activity !== 'object') {
    return null;
  }

  return {
    ...activity,
    id: safeNumber(activity.id),
    title: safeString(activity.title),
    description: safeString(activity.description),
    content: safeString(activity.content),
    theme: safeString(activity.theme),
    rules: safeString(activity.rules),
    startTime: safeString(activity.startTime),
    endTime: safeString(activity.endTime),
    submitDeadline: safeString(activity.submitDeadline),
    maxSubmissions: safeNumber(activity.maxSubmissions),
    currentSubmissions: safeNumber(activity.currentSubmissions),
    viewCount: safeNumber(activity.viewCount),
    totalPrize: safeNumber(activity.totalPrize),
    maxFileSize: safeNumber(activity.maxFileSize),
    organizerId: safeNumber(activity.organizerId),
    regionId: safeNumber(activity.regionId),
    createUser: safeNumber(activity.createUser),
    isPublic: Boolean(activity.isPublic),
    allowedFileTypes: safeArray(activity.allowedFileTypes),
    awards: safeArray(activity.awards),
    requirements: safeArray(activity.requirements),
    tags: safeArray(activity.tags),
    images: safeArray(activity.images),
    attachments: safeArray(activity.attachments),
    contactInfo: safeObject(activity.contactInfo, {}),
    metadata: activity.metadata || {}
  };
}

/**
 * 验证和处理统计数据
 * @param statistics 统计数据
 * @returns 处理后的统计数据
 */
export function validateStatistics(statistics: any): any {
  if (!statistics || typeof statistics !== 'object') {
    return null;
  }

  return {
    ...statistics,
    totalActivities: safeNumber(statistics.totalActivities),
    activeActivities: safeNumber(statistics.activeActivities),
    endedActivities: safeNumber(statistics.endedActivities),
    totalSubmissions: safeNumber(statistics.totalSubmissions),
    pendingReviews: safeNumber(statistics.pendingReviews),
    approvedSubmissions: safeNumber(statistics.approvedSubmissions),
    rejectedSubmissions: safeNumber(statistics.rejectedSubmissions),
    averageScore: safeNumber(statistics.averageScore),
    submissionsByActivity: safeObject(statistics.submissionsByActivity, {}),
    submissionsByStatus: safeObject(statistics.submissionsByStatus, {}),
    reviewsByResult: safeObject(statistics.reviewsByResult, {}),
    recentActivities: safeArray(statistics.recentActivities).map(validateActivity).filter(Boolean),
    popularActivities: safeArray(statistics.popularActivities).map(validateActivity).filter(Boolean),
    topSubmitters: safeArray(statistics.topSubmitters)
  };
}

/**
 * 计算安全的百分比
 * @param current 当前值
 * @param total 总值
 * @param precision 精度
 * @returns 安全的百分比
 */
export function safePercentage(current: any, total: any, precision: number = 2): number {
  const currentNum = safeNumber(current);
  const totalNum = safeNumber(total);
  
  if (totalNum === 0) {
    return 0;
  }
  
  const percentage = (currentNum / totalNum) * 100;
  return Number(percentage.toFixed(precision));
}

/**
 * 格式化数字显示
 * @param value 数值
 * @param defaultText 默认文本
 * @returns 格式化后的字符串
 */
export function formatNumber(value: any, defaultText: string = '0'): string {
  const num = safeNumber(value);
  if (num === 0 && defaultText !== '0') {
    return defaultText;
  }
  return num.toLocaleString();
}

/**
 * 验证和处理案例提交数据
 * @param submission 案例提交数据
 * @returns 处理后的案例提交数据
 */
export function validateSubmission(submission: any): any {
  if (!submission || typeof submission !== 'object') {
    return null;
  }

  return {
    ...submission,
    id: safeNumber(submission.id),
    activityId: safeNumber(submission.activityId),
    title: safeString(submission.title),
    description: safeString(submission.description),
    content: safeString(submission.content),
    submitterName: safeString(submission.submitterName),
    submitterDepartment: safeString(submission.submitterDepartment),
    submitterContact: safeString(submission.submitterContact),
    submitTime: safeString(submission.submitTime),
    reviewScore: safeNumber(submission.reviewScore),
    reviewComment: safeString(submission.reviewComment),
    coverImage: safeString(submission.coverImage),
    images: safeArray(submission.images),
    attachments: safeArray(submission.attachments),
    tags: safeArray(submission.tags),
    metadata: submission.metadata || {}
  };
}

/**
 * 验证和处理审核数据
 * @param review 审核数据
 * @returns 处理后的审核数据
 */
export function validateReview(review: any): any {
  if (!review || typeof review !== 'object') {
    return null;
  }

  return {
    ...review,
    id: safeNumber(review.id),
    submissionId: safeNumber(review.submissionId),
    reviewerId: safeNumber(review.reviewerId),
    reviewerName: safeString(review.reviewerName),
    score: safeNumber(review.score),
    comment: safeString(review.comment),
    reviewTime: safeString(review.reviewTime),
    suggestions: safeArray(review.suggestions),
    attachments: safeArray(review.attachments),
    metadata: review.metadata || {}
  };
}

/**
 * 验证和处理分类数据
 * @param category 分类数据
 * @returns 处理后的分类数据
 */
export function validateCategory(category: any): any {
  if (!category || typeof category !== 'object') {
    return null;
  }

  return {
    ...category,
    id: safeNumber(category.id),
    name: safeString(category.name),
    description: safeString(category.description),
    parentId: safeNumber(category.parentId),
    sort: safeNumber(category.sort),
    level: safeNumber(category.level),
    children: safeArray(category.children).map(validateCategory).filter(Boolean),
    metadata: category.metadata || {}
  };
}
