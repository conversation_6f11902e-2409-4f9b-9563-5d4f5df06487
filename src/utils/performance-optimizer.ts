/**
 * 性能优化工具
 * 提供防抖、节流、懒加载、虚拟滚动等性能优化功能
 * 遵循项目编码规范和设计模式
 */

import { ref, nextTick, onMounted, onUnmounted, type Ref } from 'vue'

/**
 * 防抖函数
 * @param fn 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param fn 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      fn.apply(this, args)
    }
  }
}

/**
 * 防抖组合式函数
 * @param fn 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数和取消函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout | null = null
  
  const debouncedFn = (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
  
  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = null
    }
  }
  
  onUnmounted(() => {
    cancel()
  })
  
  return { debouncedFn, cancel }
}

/**
 * 节流组合式函数
 * @param fn 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
) {
  let lastCall = 0
  
  const throttledFn = (...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastCall >= delay) {
      lastCall = now
      fn.apply(this, args)
    }
  }
  
  return { throttledFn }
}

/**
 * 懒加载组合式函数
 * @param target 目标元素引用
 * @param callback 进入视口时的回调函数
 * @param options IntersectionObserver 选项
 * @returns 是否可见的响应式引用
 */
export function useLazyLoad(
  target: Ref<HTMLElement | null>,
  callback: () => void,
  options: IntersectionObserverInit = {}
) {
  const isVisible = ref(false)
  let observer: IntersectionObserver | null = null
  
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '0px',
    threshold: 0.1,
    ...options
  }
  
  onMounted(() => {
    if (!target.value) return
    
    observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isVisible.value = true
          callback()
          // 一次性观察，执行后停止观察
          if (observer && target.value) {
            observer.unobserve(target.value)
          }
        }
      })
    }, defaultOptions)
    
    observer.observe(target.value)
  })
  
  onUnmounted(() => {
    if (observer) {
      observer.disconnect()
    }
  })
  
  return { isVisible }
}

/**
 * 虚拟滚动组合式函数
 * @param items 数据项数组
 * @param itemHeight 每项高度
 * @param containerHeight 容器高度
 * @returns 虚拟滚动相关状态和方法
 */
export function useVirtualScroll<T>(
  items: Ref<T[]>,
  itemHeight: number,
  containerHeight: number
) {
  const scrollTop = ref(0)
  const startIndex = ref(0)
  const endIndex = ref(0)
  const visibleItems = ref<T[]>([])
  const totalHeight = ref(0)
  const offsetY = ref(0)
  
  // 计算可见项数量
  const visibleCount = Math.ceil(containerHeight / itemHeight) + 2 // 额外渲染2项
  
  // 更新可见项
  const updateVisibleItems = () => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(start + visibleCount, items.value.length)
    
    startIndex.value = start
    endIndex.value = end
    visibleItems.value = items.value.slice(start, end)
    totalHeight.value = items.value.length * itemHeight
    offsetY.value = start * itemHeight
  }
  
  // 滚动处理
  const handleScroll = throttle((event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
    updateVisibleItems()
  }, 16) // 60fps
  
  // 监听数据变化
  const updateItems = () => {
    nextTick(() => {
      updateVisibleItems()
    })
  }
  
  return {
    scrollTop,
    startIndex,
    endIndex,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    updateItems
  }
}

/**
 * 图片懒加载组合式函数
 * @param src 图片源地址
 * @param placeholder 占位图地址
 * @returns 图片加载状态和当前显示的图片地址
 */
export function useImageLazyLoad(src: string, placeholder?: string) {
  const currentSrc = ref(placeholder || '')
  const isLoading = ref(false)
  const isLoaded = ref(false)
  const hasError = ref(false)
  
  const load = () => {
    if (isLoaded.value || isLoading.value) return
    
    isLoading.value = true
    hasError.value = false
    
    const img = new Image()
    
    img.onload = () => {
      currentSrc.value = src
      isLoading.value = false
      isLoaded.value = true
    }
    
    img.onerror = () => {
      isLoading.value = false
      hasError.value = true
    }
    
    img.src = src
  }
  
  return {
    currentSrc,
    isLoading,
    isLoaded,
    hasError,
    load
  }
}

/**
 * 内存优化：大数据分页处理
 * @param data 原始数据
 * @param pageSize 每页大小
 * @returns 分页处理后的数据和方法
 */
export function useMemoryOptimizedPagination<T>(
  data: Ref<T[]>,
  pageSize: number = 50
) {
  const currentPage = ref(1)
  const visibleData = ref<T[]>([])
  const totalPages = ref(0)
  
  const updateVisibleData = () => {
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    visibleData.value = data.value.slice(start, end)
    totalPages.value = Math.ceil(data.value.length / pageSize)
  }
  
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page
      updateVisibleData()
    }
  }
  
  const nextPage = () => {
    goToPage(currentPage.value + 1)
  }
  
  const prevPage = () => {
    goToPage(currentPage.value - 1)
  }
  
  // 初始化
  updateVisibleData()
  
  return {
    currentPage,
    visibleData,
    totalPages,
    goToPage,
    nextPage,
    prevPage,
    updateVisibleData
  }
}

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  private marks: Map<string, number> = new Map()
  
  /**
   * 开始性能标记
   * @param name 标记名称
   */
  mark(name: string): void {
    this.marks.set(name, performance.now())
  }
  
  /**
   * 测量性能
   * @param name 标记名称
   * @returns 耗时（毫秒）
   */
  measure(name: string): number {
    const startTime = this.marks.get(name)
    if (!startTime) {
      console.warn(`Performance mark "${name}" not found`)
      return 0
    }
    
    const duration = performance.now() - startTime
    this.marks.delete(name)
    return duration
  }
  
  /**
   * 测量并记录性能
   * @param name 标记名称
   * @param threshold 警告阈值（毫秒）
   */
  measureAndLog(name: string, threshold: number = 100): number {
    const duration = this.measure(name)
    
    if (duration > threshold) {
      console.warn(`Performance warning: "${name}" took ${duration.toFixed(2)}ms`)
    } else {
      console.log(`Performance: "${name}" took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  }
  
  /**
   * 清除所有标记
   */
  clear(): void {
    this.marks.clear()
  }
}

// 创建全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

/**
 * 性能监控组合式函数
 * @param name 监控名称
 * @returns 性能监控方法
 */
export function usePerformanceMonitor(name: string) {
  const start = () => {
    performanceMonitor.mark(name)
  }
  
  const end = (threshold?: number) => {
    return performanceMonitor.measureAndLog(name, threshold)
  }
  
  return { start, end }
}

/**
 * 组件渲染性能优化
 * @param fn 渲染函数
 * @param deps 依赖项
 * @returns 优化后的渲染函数
 */
export function optimizeRender<T extends (...args: any[]) => any>(
  fn: T,
  deps: any[] = []
): T {
  let lastDeps: any[] = []
  let lastResult: ReturnType<T>
  
  return ((...args: Parameters<T>) => {
    // 浅比较依赖项
    const depsChanged = deps.length !== lastDeps.length || 
      deps.some((dep, index) => dep !== lastDeps[index])
    
    if (depsChanged) {
      lastResult = fn.apply(this, args)
      lastDeps = [...deps]
    }
    
    return lastResult
  }) as T
}
