import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import qs from 'qs'
import { notifyError, notifySuc<PERSON>, notifyServiceError } from './notify'
import { getToken, getUserId, getCsrf, getType, getOid, getOrgName, getOrgType, getName, getMenuId, getRegionId, logout } from './auth'
import { hash, getKey, Base64, des } from './encrypt'

const channel = 'SERVER'
const Gateway = import.meta.env.VITE_APP_BASE_API || location.origin

const as: AxiosInstance = axios.create({
	baseURL: Gateway,
	withCredentials: false,
	headers: {
		'Content-Type': 'application/json',
		_cl: channel,
	} as Record<string, any>,
	responseType: 'json',
	timeout: import.meta.env.DEV ? 3000000 : 1200000,
})

interface HandleRequestParams {
	type?: 'get' | 'post' | 'put' | 'delete'
	url: string
	option?: any
	data?: any
}

const handleRequest = ({ type = 'get', url, option = {}, data = {} }: HandleRequestParams) => {
	try {
		const headers = {
			'Content-Type': 'application/json',
			_tk: getToken(),
			_uid: getUserId(),
			_un: getName(),
			_type: getType(),
			_oid: getOid(),
			_org_type: getOrgType(),
			_menu_id: getMenuId(),
			_org_name: getOrgName(),
			_region_id: getRegionId(),
			'x-csrf-token': getCsrf(),
		}
		option.headers = option.headers || headers
		switch (type) {
			case 'post':
				return as.post(url, data, option)
			case 'put':
				return as.put(url, data, option)
			case 'delete':
				return as.delete(url, { params: data, headers: option.headers })
			case 'get':
			default:
				return as.get(url, { params: data, headers: option.headers })
		}
	} catch (err: any) {
		notifyError(String(err))
		console.error(err)
		return Promise.reject(err)
	}
}

const isEncrypted = true
// 请求拦截器
as.interceptors.request.use(
	(config: InternalAxiosRequestConfig) => {
		config.paramsSerializer = (params) => {
			return qs.stringify(params, { arrayFormat: 'repeat' })
		}
		// 动态注入 headers
		Object.assign(config.headers || {}, {
			_tk: getToken(),
			_uid: getUserId(),
			_un: getName(),
			_type: getType(),
			_oid: getOid(),
			_org_type: getOrgType(),
			_menu_id: getMenuId(),
			_org_name: getOrgName(),
			_region_id: getRegionId(),
			'x-csrf-token': getCsrf(),
			_cl: channel,
		})
		let queryTarget = config.url?.replace(config.baseURL || '', '') || ''
		let action = queryTarget.split('?')
		let queryParams = qs.stringify(config.params, { arrayFormat: 'repeat' })
		let queryBody = config.data ? JSON.stringify(config.data) : ''
		let hs = hash(`${queryTarget}${queryParams ? '?' + queryParams : ''}`, queryBody)
		let keyHex = getKey(hs)
		let queryString = ''
		if (action.length > 1) {
			queryString = Base64.encode(des(keyHex, action[1]))
		} else if (queryParams) {
			queryString = Base64.encode(des(keyHex, queryParams))
		}
		if (queryString && !isEncrypted) {
			config.params = {
				query_string: queryString,
				...config.params,
			}
		}
		if (queryBody && !isEncrypted) {
			let body = Base64.encode(des(keyHex, queryBody))
			config.data = body
		}
		config.headers = config.headers || {}

		// ;(config.headers as Record<string, any>)['_hs'] = hs
		return config
	},
	(error: any) => {
		console.error(error)
		return Promise.reject(error)
	}
)

// 响应拦截器
as.interceptors.response.use(
	(response: AxiosResponse) => {
		console.log('🚀 ~ response:', response)
		if (response && response.data) {
			return response
		}
		return response
	},
	(error: any) => {
		if (error.response && error.response.data && error.response.data.code) {
			if ([9905, 119, 122, 141, 9900, 9908].includes(error.response.data.code)) {
				// notifyError('登录状态异常，请重新登录')
				return Promise.reject(new Error('登录状态异常，请重新登录'))
			}
		}
		if (error.response && [503, 500].includes(error.response.status)) {
			notifyServiceError(error.response.statusText)
		} else {
			// notifyError(`请求异常: ${error.message === 'timeout of 8000ms exceeded' ? '请求超时' : error.message}`)
		}
		return Promise.reject(error)
	}
)

// 消息数据剥离标准形式
export const fetch = <T = any>(res: any, showMsg = true): T | undefined => {
	let { status, data: body } = res
	let msgStr
	if (status === 200) {
		let { code, message: messageInfo, status, data } = body
		if (code === 0 && status === 200) {
			return data
		} else {
			msgStr = data || messageInfo
		}
	} else {
		msgStr = status + '错误'
	}
	if (msgStr && showMsg) {
		notifyError(msgStr)
	}
}
// 消息数据剥离带分页形式
export const fetchList = <T = any>(res: any, showMsg = true): { data: T; pageNum: number; pageSize: number; total: number } | undefined => {
	let { status, data: body } = res
	let msgStr
	if (status === 200) {
		let { code, message: messageInfo, status, data, pageNum, pageSize, total } = body
		if (code === 0 && messageInfo === 'success' && status === 200) {
			return { data, pageNum, pageSize, total }
		} else {
			msgStr = data || messageInfo
		}
	} else {
		msgStr = status + '错误'
	}
	if (msgStr && showMsg) {
		notifyError(msgStr)
	}
}
// 消息数据剥离操作处理形式
export const fetchOP = (res: any, showMsg = true): boolean => {
	let { status, data: body } = res
	let msgStr
	let success = false
	if (status === 200) {
		let { code, message: messageInfo, status, data } = body
		if (code === 0 && messageInfo === 'success' && status === 200) {
			msgStr = '操作成功'
			success = true
		} else {
			msgStr = data || messageInfo
			success = false
		}
	} else {
		msgStr = status + '错误'
		success = false
	}
	if (msgStr && showMsg) {
		;(success ? notifySuccess : notifyError)(msgStr)
	}
	return success
}

export const _headers = () => ({
	'Access-Control-Allow-Credentials': '*',
	'Content-Type': 'application/json',
	_tk: getToken(),
	_uid: getUserId(),
	_un: getName(),
	_type: getType(),
	_region_id: 3,
	_oid: typeof window !== 'undefined' && window.sessionStorage.getItem('_root_oid') != null ? window.sessionStorage.getItem('_root_oid') : '14092',
	_org_type: getOrgType(),
	_org_name: getOrgName(),
	'x-csrf-token': getCsrf(),
	_cl: channel,
})

export const formHeaders = () => ({
	'Access-Control-Allow-Credentials': 'true',
	_tk: getToken(),
	_uid: getUserId(),
	_un: getName(),
	_type: getType(),
	_oid: getOid(),
	_org_type: getOrgType(),
	_menu_id: getMenuId(),
	_org_name: getOrgName(),
	_region_id: getRegionId(),
	'x-csrf-token': getCsrf(),
})

export const http = {
	get(url: string, data = {}, option = {}) {
		return handleRequest({ url, data, option })
	},
	post(url: string, data = {}, option = {}) {
		return handleRequest({ url, option, data, type: 'post' })
	},
	delete(url: string, data = {}, option = {}) {
		return handleRequest({ url, option, data, type: 'delete' })
	},
	put(url: string, data = {}, option = {}) {
		return handleRequest({ url, option, data, type: 'put' })
	},
}

export { getToken, getUserId, getName, getType, getOid, getOrgType, getOrgName, getMenuId, getRegionId, getCsrf }

export default as
