/**
 * 全局用户反馈系统
 * 提供统一的成功、错误、警告、信息提示
 * 遵循项目编码规范和设计模式
 */

import { message, notification, Modal } from 'ant-design-vue'
import type { NotificationArgsProps, ModalFuncProps } from 'ant-design-vue'

export interface FeedbackOptions {
  duration?: number
  showIcon?: boolean
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight'
  maxCount?: number
}

export interface ConfirmOptions extends ModalFuncProps {
  onConfirm?: () => void | Promise<void>
  onCancel?: () => void
}

/**
 * 用户反馈系统类
 * 统一管理所有用户反馈相关功能
 */
export class FeedbackSystem {
  private defaultOptions: FeedbackOptions = {
    duration: 3,
    showIcon: true,
    placement: 'topRight',
    maxCount: 3
  }

  /**
   * 显示成功消息
   * @param content 消息内容
   * @param options 配置选项
   */
  success(content: string, options?: FeedbackOptions): void {
    const config = { ...this.defaultOptions, ...options }
    
    message.success({
      content,
      duration: config.duration,
      maxCount: config.maxCount
    })
  }

  /**
   * 显示错误消息
   * @param content 消息内容
   * @param options 配置选项
   */
  error(content: string, options?: FeedbackOptions): void {
    const config = { ...this.defaultOptions, ...options }
    
    message.error({
      content,
      duration: config.duration,
      maxCount: config.maxCount
    })
  }

  /**
   * 显示警告消息
   * @param content 消息内容
   * @param options 配置选项
   */
  warning(content: string, options?: FeedbackOptions): void {
    const config = { ...this.defaultOptions, ...options }
    
    message.warning({
      content,
      duration: config.duration,
      maxCount: config.maxCount
    })
  }

  /**
   * 显示信息消息
   * @param content 消息内容
   * @param options 配置选项
   */
  info(content: string, options?: FeedbackOptions): void {
    const config = { ...this.defaultOptions, ...options }
    
    message.info({
      content,
      duration: config.duration,
      maxCount: config.maxCount
    })
  }

  /**
   * 显示加载消息
   * @param content 消息内容
   * @param duration 持续时间，0表示手动关闭
   * @returns 关闭函数
   */
  loading(content: string = '加载中...', duration: number = 0): () => void {
    return message.loading(content, duration)
  }

  /**
   * 显示详细通知
   * @param type 通知类型
   * @param title 标题
   * @param description 描述
   * @param options 配置选项
   */
  notify(
    type: 'success' | 'error' | 'warning' | 'info',
    title: string,
    description?: string,
    options?: NotificationArgsProps
  ): void {
    const config: NotificationArgsProps = {
      message: title,
      description,
      duration: 4.5,
      placement: this.defaultOptions.placement,
      ...options
    }

    notification[type](config)
  }

  /**
   * 显示确认对话框
   * @param options 配置选项
   */
  confirm(options: ConfirmOptions): void {
    const {
      title = '确认操作',
      content = '确定要执行此操作吗？',
      okText = '确定',
      cancelText = '取消',
      okType = 'primary',
      onConfirm,
      onCancel,
      ...restOptions
    } = options

    Modal.confirm({
      title,
      content,
      okText,
      cancelText,
      okType,
      onOk: async () => {
        if (onConfirm) {
          try {
            await onConfirm()
          } catch (error) {
            console.error('确认操作执行失败:', error)
            this.error('操作执行失败，请重试')
          }
        }
      },
      onCancel: () => {
        if (onCancel) {
          onCancel()
        }
      },
      ...restOptions
    })
  }

  /**
   * 显示删除确认对话框
   * @param itemName 要删除的项目名称
   * @param onConfirm 确认回调
   * @param onCancel 取消回调
   */
  confirmDelete(
    itemName: string,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
  ): void {
    this.confirm({
      title: '确认删除',
      content: `确定要删除"${itemName}"吗？此操作不可恢复。`,
      okText: '删除',
      cancelText: '取消',
      okType: 'danger',
      onConfirm,
      onCancel
    })
  }

  /**
   * 显示批量操作确认对话框
   * @param action 操作名称
   * @param count 操作数量
   * @param onConfirm 确认回调
   * @param onCancel 取消回调
   */
  confirmBatch(
    action: string,
    count: number,
    onConfirm: () => void | Promise<void>,
    onCancel?: () => void
  ): void {
    this.confirm({
      title: `确认${action}`,
      content: `确定要${action}选中的 ${count} 项吗？`,
      okText: '确定',
      cancelText: '取消',
      onConfirm,
      onCancel
    })
  }

  /**
   * 显示操作成功反馈
   * @param action 操作名称
   * @param target 操作目标
   */
  successAction(action: string, target?: string): void {
    const content = target ? `${action}"${target}"成功` : `${action}成功`
    this.success(content)
  }

  /**
   * 显示操作失败反馈
   * @param action 操作名称
   * @param target 操作目标
   * @param reason 失败原因
   */
  errorAction(action: string, target?: string, reason?: string): void {
    let content = target ? `${action}"${target}"失败` : `${action}失败`
    if (reason) {
      content += `：${reason}`
    }
    this.error(content)
  }

  /**
   * 显示网络错误反馈
   * @param action 操作名称
   */
  networkError(action: string = '操作'): void {
    this.notify(
      'error',
      '网络错误',
      `${action}失败，请检查网络连接后重试`,
      { duration: 6 }
    )
  }

  /**
   * 显示权限错误反馈
   * @param action 操作名称
   */
  permissionError(action: string = '操作'): void {
    this.notify(
      'warning',
      '权限不足',
      `您没有权限执行${action}，请联系管理员`,
      { duration: 6 }
    )
  }

  /**
   * 显示表单验证错误反馈
   * @param errors 错误信息数组
   */
  validationError(errors: string[]): void {
    const content = errors.length === 1 ? errors[0] : '请检查表单填写是否正确'
    this.warning(content)
  }

  /**
   * 显示文件上传反馈
   * @param fileName 文件名
   * @param status 上传状态
   * @param progress 上传进度
   */
  uploadFeedback(fileName: string, status: 'uploading' | 'success' | 'error', progress?: number): void {
    switch (status) {
      case 'uploading':
        const progressText = progress ? ` (${progress}%)` : ''
        this.info(`正在上传"${fileName}"${progressText}`)
        break
      case 'success':
        this.success(`"${fileName}"上传成功`)
        break
      case 'error':
        this.error(`"${fileName}"上传失败`)
        break
    }
  }

  /**
   * 清除所有消息
   */
  clearAll(): void {
    message.destroy()
    notification.destroy()
  }

  /**
   * 设置全局配置
   * @param options 配置选项
   */
  configure(options: Partial<FeedbackOptions>): void {
    this.defaultOptions = { ...this.defaultOptions, ...options }
    
    // 设置 message 全局配置
    message.config({
      duration: this.defaultOptions.duration,
      maxCount: this.defaultOptions.maxCount
    })

    // 设置 notification 全局配置
    notification.config({
      placement: this.defaultOptions.placement,
      duration: this.defaultOptions.duration
    })
  }
}

// 创建全局实例
export const feedbackSystem = new FeedbackSystem()

// 导出常用方法的快捷方式
export const {
  success,
  error,
  warning,
  info,
  loading,
  notify,
  confirm,
  confirmDelete,
  confirmBatch,
  successAction,
  errorAction,
  networkError,
  permissionError,
  validationError,
  uploadFeedback,
  clearAll
} = feedbackSystem

// 默认导出
export default feedbackSystem
