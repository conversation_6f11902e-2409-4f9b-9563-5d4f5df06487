/**
 * 表单验证工具
 * 提供统一的表单验证规则和方法
 * 遵循项目编码规范和设计模式
 */

import type { Rule } from 'ant-design-vue/es/form'

export interface ValidationRule extends Rule {
  field?: string
  message?: string
}

export interface ValidationResult {
  valid: boolean
  errors: string[]
  fieldErrors: Record<string, string[]>
}

/**
 * 表单验证工具类
 */
export class FormValidation {
  
  /**
   * 必填验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static required(message: string = '此字段为必填项'): ValidationRule {
    return {
      required: true,
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * 字符串长度验证规则
   * @param min 最小长度
   * @param max 最大长度
   * @param message 错误消息
   * @returns 验证规则
   */
  static length(min?: number, max?: number, message?: string): ValidationRule {
    const rule: ValidationRule = {
      trigger: ['blur', 'change']
    }

    if (min !== undefined && max !== undefined) {
      rule.min = min
      rule.max = max
      rule.message = message || `长度应在 ${min} 到 ${max} 个字符之间`
    } else if (min !== undefined) {
      rule.min = min
      rule.message = message || `长度不能少于 ${min} 个字符`
    } else if (max !== undefined) {
      rule.max = max
      rule.message = message || `长度不能超过 ${max} 个字符`
    }

    return rule
  }

  /**
   * 邮箱验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static email(message: string = '请输入有效的邮箱地址'): ValidationRule {
    return {
      type: 'email',
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * 手机号验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static phone(message: string = '请输入有效的手机号码'): ValidationRule {
    return {
      pattern: /^1[3-9]\d{9}$/,
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * 身份证号验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static idCard(message: string = '请输入有效的身份证号码'): ValidationRule {
    return {
      validator: (rule: any, value: string) => {
        if (!value) return Promise.resolve()
        
        const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
        if (!idCardRegex.test(value)) {
          return Promise.reject(new Error(message))
        }
        
        return Promise.resolve()
      },
      trigger: ['blur', 'change']
    }
  }

  /**
   * 数字验证规则
   * @param min 最小值
   * @param max 最大值
   * @param message 错误消息
   * @returns 验证规则
   */
  static number(min?: number, max?: number, message?: string): ValidationRule {
    const rule: ValidationRule = {
      type: 'number',
      trigger: ['blur', 'change']
    }

    if (min !== undefined && max !== undefined) {
      rule.min = min
      rule.max = max
      rule.message = message || `数值应在 ${min} 到 ${max} 之间`
    } else if (min !== undefined) {
      rule.min = min
      rule.message = message || `数值不能小于 ${min}`
    } else if (max !== undefined) {
      rule.max = max
      rule.message = message || `数值不能大于 ${max}`
    } else {
      rule.message = message || '请输入有效的数字'
    }

    return rule
  }

  /**
   * 正整数验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static positiveInteger(message: string = '请输入正整数'): ValidationRule {
    return {
      pattern: /^[1-9]\d*$/,
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * URL验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static url(message: string = '请输入有效的URL地址'): ValidationRule {
    return {
      type: 'url',
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * 日期验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static date(message: string = '请选择有效的日期'): ValidationRule {
    return {
      type: 'date',
      message,
      trigger: ['blur', 'change']
    }
  }

  /**
   * 日期范围验证规则
   * @param message 错误消息
   * @returns 验证规则
   */
  static dateRange(message: string = '请选择有效的日期范围'): ValidationRule {
    return {
      validator: (rule: any, value: any[]) => {
        if (!value || value.length !== 2) {
          return Promise.reject(new Error(message))
        }
        
        const [start, end] = value
        if (!start || !end) {
          return Promise.reject(new Error(message))
        }
        
        if (start >= end) {
          return Promise.reject(new Error('开始日期不能晚于结束日期'))
        }
        
        return Promise.resolve()
      },
      trigger: ['blur', 'change']
    }
  }

  /**
   * 文件大小验证规则
   * @param maxSize 最大文件大小（MB）
   * @param message 错误消息
   * @returns 验证规则
   */
  static fileSize(maxSize: number, message?: string): ValidationRule {
    return {
      validator: (rule: any, value: any) => {
        if (!value) return Promise.resolve()
        
        const file = value.file || value
        if (file && file.size) {
          const sizeMB = file.size / 1024 / 1024
          if (sizeMB > maxSize) {
            const errorMessage = message || `文件大小不能超过 ${maxSize}MB`
            return Promise.reject(new Error(errorMessage))
          }
        }
        
        return Promise.resolve()
      },
      trigger: ['change']
    }
  }

  /**
   * 文件类型验证规则
   * @param allowedTypes 允许的文件类型
   * @param message 错误消息
   * @returns 验证规则
   */
  static fileType(allowedTypes: string[], message?: string): ValidationRule {
    return {
      validator: (rule: any, value: any) => {
        if (!value) return Promise.resolve()
        
        const file = value.file || value
        if (file && file.type) {
          if (!allowedTypes.includes(file.type)) {
            const errorMessage = message || `只允许上传 ${allowedTypes.join(', ')} 类型的文件`
            return Promise.reject(new Error(errorMessage))
          }
        }
        
        return Promise.resolve()
      },
      trigger: ['change']
    }
  }

  /**
   * 自定义验证规则
   * @param validator 验证函数
   * @param message 错误消息
   * @returns 验证规则
   */
  static custom(
    validator: (value: any) => boolean | Promise<boolean>,
    message: string = '验证失败'
  ): ValidationRule {
    return {
      validator: async (rule: any, value: any) => {
        try {
          const result = await validator(value)
          if (!result) {
            return Promise.reject(new Error(message))
          }
          return Promise.resolve()
        } catch (error) {
          return Promise.reject(new Error(message))
        }
      },
      trigger: ['blur', 'change']
    }
  }

  /**
   * 组合验证规则
   * @param rules 验证规则数组
   * @returns 组合后的验证规则
   */
  static combine(...rules: ValidationRule[]): ValidationRule[] {
    return rules
  }

  /**
   * 案例征集活动相关验证规则
   */
  static activity = {
    title: () => FormValidation.combine(
      FormValidation.required('请输入活动标题'),
      FormValidation.length(2, 100, '活动标题长度应在2-100个字符之间')
    ),
    
    description: () => FormValidation.combine(
      FormValidation.required('请输入活动描述'),
      FormValidation.length(10, 1000, '活动描述长度应在10-1000个字符之间')
    ),
    
    dateRange: () => FormValidation.combine(
      FormValidation.required('请选择活动时间'),
      FormValidation.dateRange('请选择有效的活动时间范围')
    ),
    
    maxSubmissions: () => FormValidation.combine(
      FormValidation.required('请输入最大提交数量'),
      FormValidation.number(1, 10000, '最大提交数量应在1-10000之间')
    )
  }

  /**
   * 案例提交相关验证规则
   */
  static submission = {
    title: () => FormValidation.combine(
      FormValidation.required('请输入案例标题'),
      FormValidation.length(2, 200, '案例标题长度应在2-200个字符之间')
    ),
    
    content: () => FormValidation.combine(
      FormValidation.required('请输入案例内容'),
      FormValidation.length(50, 5000, '案例内容长度应在50-5000个字符之间')
    ),
    
    category: () => FormValidation.required('请选择案例分类'),
    
    attachments: () => FormValidation.combine(
      FormValidation.fileSize(10, '附件大小不能超过10MB'),
      FormValidation.fileType(
        ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        '只支持PDF、DOC、DOCX格式的文件'
      )
    )
  }

  /**
   * 审核相关验证规则
   */
  static review = {
    score: () => FormValidation.combine(
      FormValidation.required('请输入评分'),
      FormValidation.number(0, 100, '评分应在0-100之间')
    ),
    
    comments: () => FormValidation.combine(
      FormValidation.required('请输入审核意见'),
      FormValidation.length(10, 500, '审核意见长度应在10-500个字符之间')
    )
  }
}

// 导出默认实例
export default FormValidation
