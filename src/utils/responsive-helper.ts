/**
 * 响应式设计辅助工具
 * 提供断点检测、设备类型判断、响应式配置等功能
 * 遵循项目编码规范和设计模式
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface BreakpointConfig {
  xs: number  // 超小屏幕
  sm: number  // 小屏幕
  md: number  // 中等屏幕
  lg: number  // 大屏幕
  xl: number  // 超大屏幕
  xxl: number // 超超大屏幕
}

export interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  isTouch: boolean
  orientation: 'portrait' | 'landscape'
  pixelRatio: number
}

export interface ResponsiveConfig {
  columns: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
    xxl: number
  }
  gutter: {
    xs: number
    sm: number
    md: number
    lg: number
    xl: number
    xxl: number
  }
}

/**
 * 默认断点配置（基于Ant Design Vue）
 */
export const DEFAULT_BREAKPOINTS: BreakpointConfig = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
}

/**
 * 响应式设计辅助类
 */
export class ResponsiveHelper {
  private breakpoints: BreakpointConfig
  private windowWidth = ref(0)
  private windowHeight = ref(0)
  private resizeObserver: ResizeObserver | null = null

  constructor(breakpoints: BreakpointConfig = DEFAULT_BREAKPOINTS) {
    this.breakpoints = breakpoints
    this.updateWindowSize()
    this.setupResizeListener()
  }

  /**
   * 更新窗口尺寸
   */
  private updateWindowSize() {
    this.windowWidth.value = window.innerWidth
    this.windowHeight.value = window.innerHeight
  }

  /**
   * 设置窗口大小监听器
   */
  private setupResizeListener() {
    const handleResize = () => {
      this.updateWindowSize()
    }

    window.addEventListener('resize', handleResize)
    
    // 返回清理函数
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }

  /**
   * 获取当前断点
   */
  get currentBreakpoint() {
    return computed(() => {
      const width = this.windowWidth.value
      
      if (width < this.breakpoints.xs) return 'xs'
      if (width < this.breakpoints.sm) return 'sm'
      if (width < this.breakpoints.md) return 'md'
      if (width < this.breakpoints.lg) return 'lg'
      if (width < this.breakpoints.xl) return 'xl'
      return 'xxl'
    })
  }

  /**
   * 检查是否匹配指定断点
   */
  matches(breakpoint: keyof BreakpointConfig) {
    return computed(() => {
      const width = this.windowWidth.value
      return width >= this.breakpoints[breakpoint]
    })
  }

  /**
   * 检查是否在指定断点范围内
   */
  between(min: keyof BreakpointConfig, max: keyof BreakpointConfig) {
    return computed(() => {
      const width = this.windowWidth.value
      return width >= this.breakpoints[min] && width < this.breakpoints[max]
    })
  }

  /**
   * 获取设备信息
   */
  get deviceInfo(): DeviceInfo {
    return {
      isMobile: this.windowWidth.value < this.breakpoints.md,
      isTablet: this.windowWidth.value >= this.breakpoints.md && this.windowWidth.value < this.breakpoints.lg,
      isDesktop: this.windowWidth.value >= this.breakpoints.lg,
      isTouch: 'ontouchstart' in window,
      orientation: this.windowWidth.value > this.windowHeight.value ? 'landscape' : 'portrait',
      pixelRatio: window.devicePixelRatio || 1
    }
  }

  /**
   * 获取响应式列数配置
   */
  getResponsiveColumns(config: Partial<ResponsiveConfig['columns']> = {}): ResponsiveConfig['columns'] {
    const defaultColumns = {
      xs: 1,
      sm: 2,
      md: 3,
      lg: 4,
      xl: 5,
      xxl: 6
    }

    return { ...defaultColumns, ...config }
  }

  /**
   * 获取响应式间距配置
   */
  getResponsiveGutter(config: Partial<ResponsiveConfig['gutter']> = {}): ResponsiveConfig['gutter'] {
    const defaultGutter = {
      xs: 8,
      sm: 12,
      md: 16,
      lg: 20,
      xl: 24,
      xxl: 32
    }

    return { ...defaultGutter, ...config }
  }

  /**
   * 根据当前断点获取值
   */
  getValueByBreakpoint<T>(values: Partial<Record<keyof BreakpointConfig, T>>, fallback: T): T {
    const currentBp = this.currentBreakpoint.value
    
    // 按优先级查找值
    const breakpointOrder: (keyof BreakpointConfig)[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs']
    const currentIndex = breakpointOrder.indexOf(currentBp)
    
    // 从当前断点开始向下查找
    for (let i = currentIndex; i < breakpointOrder.length; i++) {
      const bp = breakpointOrder[i]
      if (values[bp] !== undefined) {
        return values[bp]!
      }
    }
    
    return fallback
  }

  /**
   * 销毁监听器
   */
  destroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  }
}

// 创建全局实例
export const responsiveHelper = new ResponsiveHelper()

/**
 * 响应式设计组合式函数
 */
export function useResponsive(breakpoints?: BreakpointConfig) {
  const helper = breakpoints ? new ResponsiveHelper(breakpoints) : responsiveHelper
  
  // 在组件卸载时清理
  onUnmounted(() => {
    if (breakpoints) {
      helper.destroy()
    }
  })

  return {
    // 窗口尺寸
    windowWidth: helper['windowWidth'],
    windowHeight: helper['windowHeight'],
    
    // 断点检测
    currentBreakpoint: helper.currentBreakpoint,
    matches: helper.matches.bind(helper),
    between: helper.between.bind(helper),
    
    // 设备信息
    deviceInfo: computed(() => helper.deviceInfo),
    
    // 响应式配置
    getResponsiveColumns: helper.getResponsiveColumns.bind(helper),
    getResponsiveGutter: helper.getResponsiveGutter.bind(helper),
    getValueByBreakpoint: helper.getValueByBreakpoint.bind(helper)
  }
}

/**
 * 媒体查询组合式函数
 */
export function useMediaQuery(query: string) {
  const matches = ref(false)
  let mediaQuery: MediaQueryList | null = null

  const updateMatches = () => {
    if (mediaQuery) {
      matches.value = mediaQuery.matches
    }
  }

  onMounted(() => {
    mediaQuery = window.matchMedia(query)
    updateMatches()
    mediaQuery.addEventListener('change', updateMatches)
  })

  onUnmounted(() => {
    if (mediaQuery) {
      mediaQuery.removeEventListener('change', updateMatches)
    }
  })

  return matches
}

/**
 * 预定义的媒体查询
 */
export const usePresetMediaQueries = () => {
  return {
    isMobile: useMediaQuery('(max-width: 767px)'),
    isTablet: useMediaQuery('(min-width: 768px) and (max-width: 1023px)'),
    isDesktop: useMediaQuery('(min-width: 1024px)'),
    isLandscape: useMediaQuery('(orientation: landscape)'),
    isPortrait: useMediaQuery('(orientation: portrait)'),
    prefersReducedMotion: useMediaQuery('(prefers-reduced-motion: reduce)'),
    prefersDarkMode: useMediaQuery('(prefers-color-scheme: dark)')
  }
}

/**
 * 响应式表格列配置
 */
export function getResponsiveTableColumns(baseColumns: any[], deviceInfo: DeviceInfo) {
  if (deviceInfo.isMobile) {
    // 移动端只显示关键列
    return baseColumns.filter(col => col.key === 'title' || col.key === 'status' || col.key === 'actions')
  }
  
  if (deviceInfo.isTablet) {
    // 平板端显示主要列
    return baseColumns.filter(col => !col.hideOnTablet)
  }
  
  // 桌面端显示所有列
  return baseColumns
}

/**
 * 响应式卡片网格配置
 */
export function getResponsiveCardGrid(deviceInfo: DeviceInfo) {
  if (deviceInfo.isMobile) {
    return { gutter: 8, column: 1 }
  }
  
  if (deviceInfo.isTablet) {
    return { gutter: 16, column: 2 }
  }
  
  return { gutter: 24, column: 3 }
}
