import * as XLSX from 'xlsx'
import type { RankingItem, ExportConfig } from '@/types/quarterly-showcase'

/**
 * 导出榜单数据为Excel格式
 */
export const exportToExcel = (data: RankingItem[], config?: Partial<ExportConfig>) => {
  const fileName = config?.fileName || `季度亮晒榜单_${new Date().toISOString().split('T')[0]}.xlsx`
  
  // 准备导出数据
  const exportData = data.map((item, index) => ({
    '排名': item.rank,
    '姓名': item.target.name,
    '部门': item.target.department,
    '职位': item.target.position,
    '项目名称': item.project.name,
    '项目类别': item.project.category,
    '当前分数': item.score,
    '季度得分': item.quarterlyScore,
    '年度得分': item.yearlyScore,
    '趋势': getTrendText(item.trend),
    '趋势变化': item.trendValue,
    '上季度排名': item.lastQuarterRank || '-'
  }))

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(exportData)

  // 设置列宽
  const colWidths = [
    { wch: 8 },  // 排名
    { wch: 12 }, // 姓名
    { wch: 15 }, // 部门
    { wch: 12 }, // 职位
    { wch: 20 }, // 项目名称
    { wch: 12 }, // 项目类别
    { wch: 10 }, // 当前分数
    { wch: 10 }, // 季度得分
    { wch: 10 }, // 年度得分
    { wch: 8 },  // 趋势
    { wch: 10 }, // 趋势变化
    { wch: 12 }  // 上季度排名
  ]
  ws['!cols'] = colWidths

  // 添加工作表
  XLSX.utils.book_append_sheet(wb, ws, '季度亮晒榜单')

  // 导出文件
  XLSX.writeFile(wb, fileName)
}

/**
 * 导出榜单数据为CSV格式
 */
export const exportToCSV = (data: RankingItem[], config?: Partial<ExportConfig>) => {
  const fileName = config?.fileName || `季度亮晒榜单_${new Date().toISOString().split('T')[0]}.csv`
  
  // 准备CSV头部
  const headers = [
    '排名', '姓名', '部门', '职位', '项目名称', '项目类别', 
    '当前分数', '季度得分', '年度得分', '趋势', '趋势变化', '上季度排名'
  ]

  // 准备CSV数据
  const csvData = data.map(item => [
    item.rank,
    item.target.name,
    item.target.department,
    item.target.position,
    item.project.name,
    item.project.category,
    item.score,
    item.quarterlyScore,
    item.yearlyScore,
    getTrendText(item.trend),
    item.trendValue,
    item.lastQuarterRank || '-'
  ])

  // 组合CSV内容
  const csvContent = [
    headers.join(','),
    ...csvData.map(row => row.join(','))
  ].join('\n')

  // 创建Blob并下载
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', fileName)
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 导出详情数据为Excel格式
 */
export const exportDetailToExcel = (detailData: RankingItem, config?: Partial<ExportConfig>) => {
  const fileName = config?.fileName || `${detailData.target.name}_详情分析_${new Date().toISOString().split('T')[0]}.xlsx`
  
  // 创建工作簿
  const wb = XLSX.utils.book_new()

  // 基本信息工作表
  const basicInfo = [
    ['姓名', detailData.target.name],
    ['部门', detailData.target.department],
    ['职位', detailData.target.position],
    ['当前排名', detailData.rank],
    ['项目名称', detailData.project.name],
    ['项目类别', detailData.project.category],
    ['当前分数', detailData.score],
    ['季度得分', detailData.quarterlyScore],
    ['年度得分', detailData.yearlyScore],
    ['趋势', getTrendText(detailData.trend)],
    ['趋势变化', detailData.trendValue],
    ['上季度排名', detailData.lastQuarterRank || '-']
  ]
  
  const basicWs = XLSX.utils.aoa_to_sheet(basicInfo)
  basicWs['!cols'] = [{ wch: 15 }, { wch: 20 }]
  XLSX.utils.book_append_sheet(wb, basicWs, '基本信息')

  // 详细分数工作表
  if (detailData.details && detailData.details.length > 0) {
    const scoreDetails = detailData.details.map(detail => ({
      '评分项目': detail.itemName,
      '得分': detail.score,
      '满分': detail.fullScore,
      '权重': `${detail.weight}%`,
      '得分率': `${((detail.score / detail.fullScore) * 100).toFixed(1)}%`,
      '说明': detail.description || '-',
      '证据数量': detail.evidences?.length || 0
    }))

    const scoreWs = XLSX.utils.json_to_sheet(scoreDetails)
    scoreWs['!cols'] = [
      { wch: 20 }, // 评分项目
      { wch: 8 },  // 得分
      { wch: 8 },  // 满分
      { wch: 8 },  // 权重
      { wch: 10 }, // 得分率
      { wch: 30 }, // 说明
      { wch: 10 }  // 证据数量
    ]
    XLSX.utils.book_append_sheet(wb, scoreWs, '详细分数')
  }

  // 导出文件
  XLSX.writeFile(wb, fileName)
}

/**
 * 获取趋势文本
 */
const getTrendText = (trend: string): string => {
  switch (trend) {
    case 'up': return '上升'
    case 'down': return '下降'
    default: return '稳定'
  }
}

/**
 * 批量导出选中数据
 */
export const exportSelectedData = (
  selectedData: RankingItem[], 
  format: 'excel' | 'csv' = 'excel',
  config?: Partial<ExportConfig>
) => {
  if (selectedData.length === 0) {
    throw new Error('请选择要导出的数据')
  }

  const fileName = config?.fileName || `季度亮晒榜单_选中数据_${new Date().toISOString().split('T')[0]}`

  if (format === 'excel') {
    exportToExcel(selectedData, { ...config, fileName: `${fileName}.xlsx` })
  } else {
    exportToCSV(selectedData, { ...config, fileName: `${fileName}.csv` })
  }
}

/**
 * 生成导出文件名
 */
export const generateFileName = (prefix: string, extension: string): string => {
  const date = new Date().toISOString().split('T')[0]
  const time = new Date().toTimeString().split(' ')[0].replace(/:/g, '')
  return `${prefix}_${date}_${time}.${extension}`
}
