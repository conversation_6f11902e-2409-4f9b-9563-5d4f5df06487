/**
 * API包装器工具
 * 提供统一的错误处理、加载状态管理、重试机制等功能
 */

import { ref, reactive } from 'vue';
import { message, notification } from 'ant-design-vue';
import type { ApiResponse } from '@/types/case-collection';
import request from '@/utils/request';

export interface ApiState {
  loading: boolean;
  error: string | null;
  retryCount: number;
  lastRequestTime: number;
}

export interface ApiOptions {
  showLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
  successMessage?: string;
  errorMessage?: string;
  retryTimes?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTime?: number;
  timeout?: number;
}

export interface CacheItem {
  data: any;
  timestamp: number;
  expireTime: number;
}

/**
 * API包装器类
 */
export class ApiWrapper {
  private cache = new Map<string, CacheItem>();
  private requestQueue = new Map<string, Promise<any>>();
  
  /**
   * 包装API调用
   * @param apiCall API调用函数
   * @param options 配置选项
   * @returns 包装后的API调用结果
   */
  async wrapApiCall<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    options: ApiOptions = {}
  ): Promise<{ data: T | null; error: string | null; success: boolean }> {
    const {
      showLoading = true,
      showError = true,
      showSuccess = false,
      successMessage = '操作成功',
      errorMessage = '操作失败',
      retryTimes = 2,
      retryDelay = 1000,
      cache = false,
      cacheTime = 5 * 60 * 1000, // 5分钟
      timeout = 30000 // 30秒
    } = options;

    // 生成缓存键
    const cacheKey = cache ? this.generateCacheKey(apiCall.toString()) : '';
    
    // 检查缓存
    if (cache && this.hasValidCache(cacheKey)) {
      const cachedData = this.getFromCache(cacheKey);
      return { data: cachedData, error: null, success: true };
    }

    // 检查是否有相同的请求正在进行
    if (this.requestQueue.has(cacheKey)) {
      try {
        const result = await this.requestQueue.get(cacheKey);
        return result;
      } catch (error) {
        // 如果队列中的请求失败，继续执行新的请求
      }
    }

    let loadingInstance: any = null;
    
    try {
      // 显示加载状态
      if (showLoading) {
        loadingInstance = message.loading('处理中...', 0);
      }

      // 创建带超时的Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), timeout);
      });

      // 执行API调用
      const apiPromise = this.executeWithRetry(apiCall, retryTimes, retryDelay);
      
      // 将请求加入队列
      if (cache) {
        this.requestQueue.set(cacheKey, apiPromise);
      }

      const response = await Promise.race([apiPromise, timeoutPromise]);

      // 清理加载状态
      if (loadingInstance) {
        loadingInstance();
      }

      // 从队列中移除
      if (cache) {
        this.requestQueue.delete(cacheKey);
      }

      if (response.success) {
        // 缓存结果
        if (cache) {
          this.setCache(cacheKey, response.data, cacheTime);
        }

        // 显示成功消息
        if (showSuccess && successMessage) {
          message.success(successMessage);
        }

        return { data: response.data, error: null, success: true };
      } else {
        const errorMsg = response.message || errorMessage;
        
        if (showError) {
          this.showError(errorMsg, response.code);
        }

        return { data: null, error: errorMsg, success: false };
      }
    } catch (error: any) {
      // 清理加载状态
      if (loadingInstance) {
        loadingInstance();
      }

      // 从队列中移除
      if (cache) {
        this.requestQueue.delete(cacheKey);
      }

      const errorMsg = this.getErrorMessage(error) || errorMessage;
      
      if (showError) {
        this.showError(errorMsg);
      }

      return { data: null, error: errorMsg, success: false };
    }
  }

  /**
   * 带重试机制的API执行
   */
  private async executeWithRetry<T>(
    apiCall: () => Promise<ApiResponse<T>>,
    retryTimes: number,
    retryDelay: number
  ): Promise<ApiResponse<T>> {
    let lastError: any;
    
    for (let i = 0; i <= retryTimes; i++) {
      try {
        const result = await apiCall();
        return result;
      } catch (error) {
        lastError = error;
        
        // 如果是最后一次尝试，直接抛出错误
        if (i === retryTimes) {
          throw error;
        }

        // 等待重试延迟
        if (retryDelay > 0) {
          await this.delay(retryDelay * Math.pow(2, i)); // 指数退避
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(apiCallString: string): string {
    // 简单的哈希函数
    let hash = 0;
    for (let i = 0; i < apiCallString.length; i++) {
      const char = apiCallString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return `api_cache_${Math.abs(hash)}`;
  }

  /**
   * 检查缓存是否有效
   */
  private hasValidCache(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;
    
    const now = Date.now();
    if (now > item.expireTime) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache(key: string): any {
    const item = this.cache.get(key);
    return item ? item.data : null;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any, cacheTime: number): void {
    const now = Date.now();
    this.cache.set(key, {
      data,
      timestamp: now,
      expireTime: now + cacheTime
    });
  }

  /**
   * 清理过期缓存
   */
  public clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireTime) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 清理所有缓存
   */
  public clearAllCache(): void {
    this.cache.clear();
  }

  /**
   * 显示错误信息
   */
  private showError(message: string, code?: number): void {
    if (code === 401) {
      notification.error({
        message: '认证失败',
        description: '您的登录已过期，请重新登录',
        duration: 4.5
      });
    } else if (code === 403) {
      notification.error({
        message: '权限不足',
        description: '您没有权限执行此操作',
        duration: 4.5
      });
    } else if (code === 404) {
      notification.error({
        message: '资源不存在',
        description: '请求的资源不存在或已被删除',
        duration: 4.5
      });
    } else if (code === 500) {
      notification.error({
        message: '服务器错误',
        description: '服务器内部错误，请稍后重试',
        duration: 4.5
      });
    } else {
      notification.error({
        message: '操作失败',
        description: message,
        duration: 4.5
      });
    }
  }

  /**
   * 获取错误信息
   */
  private getErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error?.response?.statusText) {
      return error.response.statusText;
    }
    
    return '未知错误';
  }

  /**
   * 检查网络状态
   */
  public isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * 监听网络状态变化
   */
  public onNetworkChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // 返回清理函数
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  /**
   * GET请求
   */
  public async get<T = any>(url: string, params?: any, options?: any): Promise<ApiResponse<T>> {
    try {
      const response = await request.get(url, params, options);
      
      // 如果是blob类型的响应，直接返回数据
      if (options?.responseType === 'blob') {
        return response.data;
      }
      
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST请求
   */
  public async post<T = any>(url: string, data?: any, options?: any): Promise<ApiResponse<T>> {
    try {
      const response = await request.post(url, data, options);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT请求
   */
  public async put<T = any>(url: string, data?: any, options?: any): Promise<ApiResponse<T>> {
    try {
      const response = await request.put(url, data, options);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE请求
   */
  public async delete<T = any>(url: string, params?: any, options?: any): Promise<ApiResponse<T>> {
    try {
      const response = await request.delete(url, params, options);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PATCH请求
   */
  public async patch<T = any>(url: string, data?: any, options?: any): Promise<ApiResponse<T>> {
    try {
      const response = await request.put(url, data, options); // 使用PUT作为PATCH的替代
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

// 创建全局实例
export const apiWrapper = new ApiWrapper();

// 定期清理过期缓存
setInterval(() => {
  apiWrapper.clearExpiredCache();
}, 5 * 60 * 1000); // 每5分钟清理一次

/**
 * 创建API状态管理的组合式函数
 */
export function useApiState() {
  const state = reactive<ApiState>({
    loading: false,
    error: null,
    retryCount: 0,
    lastRequestTime: 0
  });

  const resetState = () => {
    state.loading = false;
    state.error = null;
    state.retryCount = 0;
  };

  const setLoading = (loading: boolean) => {
    state.loading = loading;
  };

  const setError = (error: string | null) => {
    state.error = error;
  };

  return {
    state,
    resetState,
    setLoading,
    setError
  };
}
