import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

// 获取命令行参数
const args = process.argv.slice(2);
const env = args.includes('--prod') ? 'production' : 'test';
const skipZip = args.includes('--no-zip');

console.log(`🚀 开始构建 ${env === 'production' ? '正式' : '测试'} 环境...`);

// 进度条显示函数
function showProgress(message) {
    const chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    let i = 0;

    const interval = setInterval(() => {
        process.stdout.write(`\r${chars[i]} ${message}`);
        i = (i + 1) % chars.length;
    }, 100);

    return () => {
        clearInterval(interval);
        process.stdout.write(`\r✅ ${message}\n`);
    };
}

// 检查环境和依赖
function checkEnvironment() {
    console.log('🔍 检查构建环境...');

    // 检查Node.js版本
    const nodeVersion = process.version;
    console.log(`📋 Node.js版本: ${nodeVersion}`);

    // 检查package.json
    if (!fs.existsSync('package.json')) {
        throw new Error('package.json文件不存在');
    }

    // 检查node_modules
    if (!fs.existsSync('node_modules')) {
        console.log('⚠️  node_modules不存在，正在安装依赖...');
        execSync('npm install', { stdio: 'inherit' });
    }

    console.log('✅ 环境检查完成');
}

// 构建项目
async function buildProject() {
    try {
        // 环境检查
        checkEnvironment();

        // 使用npx来调用vite build命令
        const buildCommand = `npx vite build --mode ${env}`;
        console.log(`📦 执行构建命令: ${buildCommand}`);

        // 显示进度条
        const stopProgress = showProgress('正在构建项目...');

        try {
            execSync(buildCommand, {
                stdio: 'inherit',
                encoding: 'utf8'
            });

            stopProgress();
            console.log('✅ 构建完成!');

        } catch (buildError) {
            stopProgress();
            console.error('❌ 构建失败!');
            console.error('错误详情:', buildError.message);
            throw buildError;
        }

        if (!skipZip) {
            // 创建zip压缩包
            await createZipArchive(env);
        }

    } catch (error) {
        console.error('❌ 构建失败:', error.message);
        console.log('\n🔧 故障排除建议:');
        console.log('1. 检查TypeScript语法错误');
        console.log('2. 确认所有依赖已正确安装');
        console.log('3. 尝试运行: npm install');
        console.log('4. 检查环境变量配置');
        process.exit(1);
    }
}

// 创建zip压缩包函数
async function createZipArchive(env) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const zipName = `project-dashboard-${env}.zip`;
    const zipPath = path.join(process.cwd(), zipName);

    console.log(`📦 正在创建压缩包: ${zipName}`);

    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', {
        zlib: { level: 9 } // 最高压缩级别
    });

    output.on('close', () => {
        const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
        console.log(`✅ 压缩包创建完成!`);
        console.log(`📁 文件路径: ${zipPath}`);
        console.log(`📊 文件大小: ${sizeInMB} MB`);
        console.log(`🎉 ${env === 'production' ? '正式' : '测试'}环境打包完成!`);
    });

    output.on('error', (err) => {
        console.error('❌ 创建压缩包失败:', err);
        process.exit(1);
    });

    archive.on('warning', (err) => {
        if (err.code === 'ENOENT') {
            console.warn('⚠️ 警告:', err);
        } else {
            throw err;
        }
    });

    archive.on('error', (err) => {
        console.error('❌ 压缩失败:', err);
        process.exit(1);
    });

    archive.pipe(output);

    // 添加dist目录到压缩包
    archive.directory('dist/', false);

    // 添加部署相关文件
    if (fs.existsSync('nginx.conf')) {
        archive.file('nginx.conf', { name: 'nginx.conf' });
    }

    if (fs.existsSync('docker-compose.yml')) {
        archive.file('docker-compose.yml', { name: 'docker-compose.yml' });
    }

    // 添加部署说明文件
    const deploymentInfo = {
        environment: env,
        buildTime: new Date().toISOString(),
        version: require('../package.json').version,
        nodeVersion: process.version,
        deploymentInstructions: {
            nginx: "将dist目录内容部署到nginx静态文件目录",
            docker: "使用docker-compose up -d启动容器",
            static: "直接将dist目录内容上传到静态文件服务器"
        }
    };

    archive.append(JSON.stringify(deploymentInfo, null, 2), { name: 'deployment-info.json' });

    archive.finalize();
}

// 启动构建
buildProject();
