import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const require = createRequire(import.meta.url);

// 获取命令行参数
const args = process.argv.slice(2);
const env = args.includes('--prod') ? 'production' : 'test';
const skipZip = args.includes('--no-zip');

console.log(`🚀 开始构建 ${env === 'production' ? '正式' : '测试'} 环境...`);

// 简单进度显示
let progressDots = 0;
const progressInterval = setInterval(() => {
    process.stdout.write('.');
    progressDots++;
    if (progressDots > 50) {
        process.stdout.write('\n');
        progressDots = 0;
    }
}, 500);

try {
    // 直接使用npm run命令
    const buildCommand = env === 'production' ? 'npm run build' : 'npm run build';
    console.log(`📦 执行构建命令: ${buildCommand}`);
    
    execSync(buildCommand, { 
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: env }
    });
    
    clearInterval(progressInterval);
    console.log('\n✅ 构建完成!');
    
    if (!skipZip) {
        console.log('📦 正在创建压缩包...');
        await createZipArchive(env);
    }
    
} catch (error) {
    clearInterval(progressInterval);
    console.error('\n❌ 构建失败:', error.message);
    process.exit(1);
}

async function createZipArchive(env) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const zipName = `project-dashboard-${env}-${timestamp}.zip`;
    const zipPath = path.join(process.cwd(), 'dist-packages', zipName);

    // 确保输出目录存在
    const outputDir = path.dirname(zipPath);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    console.log(`📦 正在创建压缩包: ${zipName}`);

    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', {
        zlib: { level: 9 }
    });

    return new Promise((resolve, reject) => {
        output.on('close', () => {
            const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
            console.log(`✅ 压缩包创建完成!`);
            console.log(`📁 文件路径: ${zipPath}`);
            console.log(`📊 文件大小: ${sizeInMB} MB`);
            console.log(`🎉 ${env === 'production' ? '正式' : '测试'}环境打包完成!`);
            resolve();
        });

        output.on('error', reject);
        archive.on('error', reject);

        archive.pipe(output);
        archive.directory('dist/', false);

        // 添加部署相关文件
        if (fs.existsSync('nginx.conf')) {
            archive.file('nginx.conf', { name: 'nginx.conf' });
        }

        if (fs.existsSync('docker-compose.yml')) {
            archive.file('docker-compose.yml', { name: 'docker-compose.yml' });
        }

        // 添加部署说明文件
        const deploymentInfo = {
            environment: env,
            buildTime: new Date().toISOString(),
            version: require('../package.json').version,
            nodeVersion: process.version,
            deploymentInstructions: {
                nginx: "将dist目录内容部署到nginx静态文件目录",
                docker: "使用docker-compose up -d启动容器",
                static: "直接将dist目录内容上传到静态文件服务器"
            }
        };

        archive.append(JSON.stringify(deploymentInfo, null, 2), { name: 'deployment-info.json' });
        archive.finalize();
    });
}