{"permissions": {"allow": ["mcp__smithery-ai-server-sequential-thinking__sequentialthinking", "<PERSON><PERSON>(dir:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(findstr:*)", "Bash(rg:*)", "Bash(npx vue-tsc:*)", "Bash(npx tsc:*)", "Bash(rm:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run build:test:*)", "Bash(/dev/null)", "Bash(npm run build:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_click", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_type", "mcp__playwright__browser_press_key", "<PERSON><PERSON>(powershell:*)", "mcp__exa__deep_researcher_start", "Read(C:\\Users\\<USER>\\Desktop\\审核确认/**)", "Read(C:\\Users\\<USER>\\Desktop\\审核确认/**)", "Read(C:\\Users\\<USER>\\Desktop\\审核确认/**)", "Read(C:\\Users\\<USER>\\Desktop\\审核确认/**)", "Read(C:\\Users\\<USER>\\Desktop\\审核确认/**)", "Bash(npm install)", "Bash(node:*)", "<PERSON><PERSON>(tasklist:*)", "<PERSON><PERSON>(taskkill:*)"], "deny": [], "ask": []}}