{
	"compilerOptions": {
		/* "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": ["ESNext", "DOM"],
    "skipLibCheck": true,
    "noEmit": true */
		"target": "ESNext",
		"useDefineForClassFields": true,
		"lib": ["DOM", "DOM.Iterable", "ESNext"], // 要包含在编译中的依赖库文件列表
		"allowJs": false, // 允许编译 JavaScript 文件
		"skipLibCheck": true, // 跳过所有声明文件的类型检查
		"esModuleInterop": false, // 禁用命名空间引用 (import * as fs from "fs") 启用 CJS/AMD/UMD 风格引用 (import fs from "fs")
		"allowSyntheticDefaultImports": true, // 允许从没有默认导出的模块进行默认导入
		"strict": true, // 启用所有严格类型检查选项
		"forceConsistentCasingInFileNames": true, // 不允许对同一个文件使用不一致格式的引用
		"module": "ESNext", // 指定模块代码生成
		"moduleResolution": "Node", // 使用 Node.js 风格解析模块
		"resolveJsonModule": true, // 允许使用 .json 扩展名导入的模块
		"isolatedModules": true,
		"noEmit": true, // 不输出(意思是不编译代码，只执行类型检查
		"jsx": "preserve", // 在.tsx文件中支持JSX
		"sourceMap": false, // 是否生成相应的.map文件
		"noUnusedLocals": false, // 报告未使用的本地变量的错误
		"noUnusedParameters": true, // 报告未使用参数的错误
		"incremental": true, // 只编译新内容，渐进式编译/增量式编译，通过从以前的编译中读取/写入信息到磁盘上的文件（tsconfig.tsbuildinfo）来启用增量编译
		"types": ["vite/client"],
		"baseUrl": ".",
		"paths": {
			"@/*": ["src/*"]
		}
	},
	"include": ["src/**/*"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
