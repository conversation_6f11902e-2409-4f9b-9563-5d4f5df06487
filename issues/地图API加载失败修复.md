# 地图API加载失败修复任务

## 问题描述
- **问题**：地图加载失败，提示"禁止多种API加载方式混用"
- **影响范围**：`src/components/map-container/index.vue`
- **根本原因**：硬编码API密钥，缺乏统一配置管理

## 解决方案
采用方案一：统一环境变量配置管理

## 执行步骤

### ✅ 步骤1：配置环境变量
- 修改 `.env` 文件，添加 `VITE_AMAP_KEY` 和 `VITE_AMAP_SECURITY_CODE`
- 修改 `.env.production` 文件，添加生产环境地图API配置
- **结果**：环境变量统一管理地图API密钥

### ✅ 步骤2：创建地图配置服务
- 新建 `src/config/map.ts` 文件
- 实现 `getAmapConfig()`, `getDefaultMapOptions()`, `validateMapConfig()`, `setMapSecurity()` 方法
- **结果**：统一的地图配置管理接口

### ✅ 步骤3：重构地图组件
- 修改 `src/components/map-container/index.vue`
- 移除硬编码的API密钥：`'36792dbc1b094a14e9394c47b3bb9d1e'` 和 `'34f7beae0aed881474f32de3427ec5ad'`
- 使用配置服务替代硬编码配置
- **结果**：地图组件使用环境变量配置

### ✅ 步骤4：清理API服务
- 修改 `src/api/map.ts`
- 移除硬编码token：`'265accaff670457b9202a1212b83e132'`
- **结果**：API服务配置清晰，无冗余代码

### ✅ 步骤5：修复HTML中的API混用问题
- 修改 `index.html`
- 移除直接引入的地图API：`<script src="https://webapi.amap.com/maps?v=1.4.6&key=6d4962f3a77c8a34997b669950f200a0"></script>`
- **结果**：彻底解决"禁止多种API加载方式混用"问题

## 技术改进

### 配置管理
- ✅ 环境变量统一管理API密钥
- ✅ 配置验证机制
- ✅ 错误处理增强

### 代码质量
- ✅ 移除硬编码配置
- ✅ 统一配置接口
- ✅ 类型安全保障

## 测试验证
需要验证以下功能：
1. 地图正常加载和显示
2. 地图标记功能正常
3. 地图搜索功能正常
4. 地图缩放和控制功能正常

## 注意事项
- 确保生产环境的API密钥有效
- 如需更换API密钥，只需修改环境变量文件
- 配置服务支持扩展其他地图提供商