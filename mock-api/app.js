const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3002;

// 中间件配置
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.path}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// 响应拦截器 - 添加统一响应格式
app.use((req, res, next) => {
  const originalSend = res.send;
  res.send = function(data) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Response ${res.statusCode} for ${req.method} ${req.path}`);
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        console.log('Response Body:', JSON.stringify(parsed, null, 2));
      } catch (e) {
        console.log('Response Body:', data);
      }
    } else {
      console.log('Response Body:', JSON.stringify(data, null, 2));
    }
    console.log('---');
    originalSend.call(this, data);
  };
  next();
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    code: 200,
    message: '服务运行正常',
    data: {
      service: 'MF模范机关Mock API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      modules: ['audit-confirmation', 'data-inspection', 'indicator-rules-management']
    }
  });
});

// API路由配置
try {
  // 审核确认模块
  const auditConfirmationRoutes = require('./routes/audit-confirmation');
  const auditConfirmationPart2Routes = require('./routes/audit-confirmation-part2');
  app.use('/api/audit-confirmation', auditConfirmationRoutes);
  app.use('/api/audit-confirmation', auditConfirmationPart2Routes);
  console.log('✅ 审核确认模块路由已加载');

  // 数据体检模块
  const dataInspectionRoutes = require('./routes/data-inspection');
  app.use('/api/data-inspection', dataInspectionRoutes);
  console.log('✅ 数据体检模块路由已加载');

  // 指标规则管理模块
  const indicatorRulesRoutes = require('./routes/indicator-rules-management');
  app.use('/api/indicator-rules', indicatorRulesRoutes);
  console.log('✅ 指标规则管理模块路由已加载');

} catch (error) {
  console.error('❌ 路由加载失败:', error.message);
  console.error('请确保所有路由文件存在且格式正确');
}

// API接口列表
app.get('/api', (req, res) => {
  res.json({
    code: 200,
    message: 'MF模范机关Mock API服务',
    data: {
      version: '1.0.0',
      modules: {
        'audit-confirmation': {
          name: '审核确认模块',
          baseUrl: '/api/audit-confirmation',
          endpoints: [
            'GET /workflows - 工作流列表',
            'POST /workflows - 创建工作流',
            'PUT /workflows/:id - 更新工作流',
            'DELETE /workflows/:id - 删除工作流',
            'GET /cultivation-objects - 培育对象列表',
            'POST /cultivation-objects - 创建培育对象',
            'PUT /cultivation-objects/:id - 更新培育对象',
            'POST /audit-operations - 审核操作',
            'GET /audit-history - 审核历史',
            'POST /appeals - 申诉管理',
            'GET /appeals - 申诉列表',
            'PUT /appeals/:id - 处理申诉',
            'GET /notifications - 通知列表',
            'POST /notifications/send - 发送通知'
          ]
        },
        'data-inspection': {
          name: '数据体检模块',
          baseUrl: '/api/data-inspection',
          endpoints: [
            'GET /exceptions - 异常数据列表',
            'GET /exceptions/:id - 异常详情',
            'PUT /exceptions/:id - 更新异常状态',
            'POST /exceptions/batch-handle - 批量处理',
            'GET /statistics - 异常统计',
            'GET /rules - 检查规则列表',
            'POST /rules - 创建检查规则',
            'PUT /rules/:id - 更新规则',
            'DELETE /rules/:id - 删除规则',
            'POST /auto-inspection - 触发自动体检',
            'GET /inspection-reports - 体检报告列表',
            'GET /remediation-suggestions - 修复建议'
          ]
        },
        'indicator-rules': {
          name: '指标规则管理模块',
          baseUrl: '/api/indicator-rules',
          endpoints: [
            'GET /indicators - 指标列表',
            'POST /indicators - 创建指标',
            'PUT /indicators/:id - 更新指标',
            'DELETE /indicators/:id - 删除指标',
            'GET /data-sources - 数据源选项',
            'POST /indicators/calculate - 指标演算',
            'GET /analysis-models - 分析模型',
            'GET /calculations - 演算历史',
            'GET /templates - 指标模板',
            'POST /templates - 创建模板',
            'PUT /templates/:id - 更新模板',
            'DELETE /templates/:id - 删除模板',
            'POST /templates/:id/import - 导入模板'
          ]
        }
      },
      usage: {
        baseUrl: `http://localhost:${PORT}`,
        contentType: 'application/json',
        auth: '暂未启用认证',
        cors: '已启用跨域支持'
      }
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: {
      method: req.method,
      path: req.originalUrl,
      suggestion: '请检查API路径是否正确，或访问 /api 查看可用接口列表'
    }
  });
});

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: {
      error: error.message,
      path: req.path,
      method: req.method,
      timestamp: new Date().toISOString()
    }
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log('\n===========================================');
  console.log('🚀 MF模范机关Mock API服务已启动');
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`📖 API文档: http://localhost:${PORT}/api`);
  console.log(`❤️  健康检查: http://localhost:${PORT}/health`);
  console.log('===========================================\n');
  
  console.log('📋 已加载的API模块:');
  console.log('  • 审核确认模块: /api/audit-confirmation');
  console.log('  • 数据体检模块: /api/data-inspection');
  console.log('  • 指标规则管理: /api/indicator-rules');
  console.log('\n等待API请求...\n');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('\n📴 收到终止信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n📴 收到中断信号，正在关闭服务器...');
  process.exit(0);
});

module.exports = app;