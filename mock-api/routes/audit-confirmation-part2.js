/**
 * 审核确认模块 Mock API - 第二部分
 * 审核操作、历史追溯、申诉管理、通知等接口
 */
const express = require('express');
const router = express.Router();

// Mock数据存储（与audit-confirmation.js共享）
const mockData = {
  cultivationObjects: [
    {
      id: 1,
      name: '市委办公室党支部',
      type: '党支部',
      department: '市委办公室',
      auditStatus: 'auditing',
      score: 86,
      level: '良好',
      createTime: '2024-12-01 10:00:00',
      auditor: null,
      auditTime: null
    }
  ],
  auditHistory: {},
  appeals: [],
  notifications: []
};

// ================= 审核操作接口 =================

// 选择评分审核结果
router.post('/cultivation-objects/:id/audit-decision', (req, res) => {
  const { id } = req.params;
  const { auditResult, auditOpinion, auditor } = req.body;
  
  const objectIndex = mockData.cultivationObjects.findIndex(obj => obj.id == id);
  if (objectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  // 更新审核状态
  const statusMap = {
    'approved': 'audited',
    'rejected': 'rejected', 
    'approved_with_conditions': 'audited'
  };
  
  mockData.cultivationObjects[objectIndex].auditStatus = statusMap[auditResult] || 'audited';
  mockData.cultivationObjects[objectIndex].auditor = auditor;
  mockData.cultivationObjects[objectIndex].auditTime = new Date().toLocaleString('zh-CN');
  
  // 记录审核历史
  if (!mockData.auditHistory[id]) {
    mockData.auditHistory[id] = [];
  }
  
  mockData.auditHistory[id].push({
    id: mockData.auditHistory[id].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: auditor,
    auditResult: auditResult,
    auditOpinion: auditOpinion,
    action: 'audit_decision'
  });
  
  res.json({
    success: true,
    message: '审核决策提交成功',
    data: {
      id: id,
      auditResult: auditResult,
      auditStatus: mockData.cultivationObjects[objectIndex].auditStatus
    }
  });
});

// 填写评分审核意见
router.post('/cultivation-objects/:id/audit-opinion', (req, res) => {
  const { id } = req.params;
  const { opinion, auditor, auditDate } = req.body;
  
  const object = mockData.cultivationObjects.find(obj => obj.id == id);
  if (!object) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  // 记录审核意见
  if (!mockData.auditHistory[id]) {
    mockData.auditHistory[id] = [];
  }
  
  const auditRecord = {
    id: mockData.auditHistory[id].length + 1,
    auditDate: auditDate || new Date().toLocaleString('zh-CN'),
    auditor: auditor,
    auditOpinion: opinion,
    action: 'opinion_added'
  };
  
  mockData.auditHistory[id].push(auditRecord);
  
  res.json({
    success: true,
    message: '审核意见提交成功',
    data: auditRecord
  });
});

// 提交审核结果
router.post('/cultivation-objects/:id/submit-audit', (req, res) => {
  const { id } = req.params;
  const { finalResult, finalOpinion, auditor } = req.body;
  
  const objectIndex = mockData.cultivationObjects.findIndex(obj => obj.id == id);
  if (objectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  // 更新最终状态
  mockData.cultivationObjects[objectIndex].auditStatus = finalResult === 'approved' ? 'audited' : 'rejected';
  mockData.cultivationObjects[objectIndex].auditor = auditor;
  mockData.cultivationObjects[objectIndex].auditTime = new Date().toLocaleString('zh-CN');
  
  // 记录最终审核结果
  if (!mockData.auditHistory[id]) {
    mockData.auditHistory[id] = [];
  }
  
  mockData.auditHistory[id].push({
    id: mockData.auditHistory[id].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: auditor,
    auditResult: finalResult,
    auditOpinion: finalOpinion,
    action: 'audit_submitted',
    isFinal: true
  });
  
  // 触发通知
  const notification = {
    id: Math.random().toString(36).substr(2, 9),
    recipientId: mockData.cultivationObjects[objectIndex].contactPerson,
    recipientName: mockData.cultivationObjects[objectIndex].contactPerson,
    title: finalResult === 'approved' ? '审核通过通知' : '审核不通过通知',
    content: `您的申请"${mockData.cultivationObjects[objectIndex].name}"已${finalResult === 'approved' ? '通过' : '未通过'}审核。审核意见：${finalOpinion}`,
    sendTime: new Date().toLocaleString('zh-CN'),
    status: 'sent'
  };
  
  res.json({
    success: true,
    message: '审核结果提交成功，通知已发送',
    data: {
      auditResult: mockData.cultivationObjects[objectIndex],
      notification: notification
    }
  });
});

// ================= 历史追溯接口 =================

// 记录评分结果历史数据
router.post('/score-history', (req, res) => {
  const { cultivationObjectId, oldScore, newScore, reason, operator } = req.body;
  
  if (!mockData.auditHistory[cultivationObjectId]) {
    mockData.auditHistory[cultivationObjectId] = [];
  }
  
  const historyRecord = {
    id: mockData.auditHistory[cultivationObjectId].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: operator,
    action: 'score_history_recorded',
    oldScore: oldScore,
    newScore: newScore,
    reason: reason
  };
  
  mockData.auditHistory[cultivationObjectId].push(historyRecord);
  
  res.json({
    success: true,
    message: '评分历史记录成功',
    data: historyRecord
  });
});

// 记录指标结果历史数据
router.post('/indicator-history', (req, res) => {
  const { cultivationObjectId, indicatorId, oldScore, newScore, reason, operator } = req.body;
  
  if (!mockData.auditHistory[cultivationObjectId]) {
    mockData.auditHistory[cultivationObjectId] = [];
  }
  
  const historyRecord = {
    id: mockData.auditHistory[cultivationObjectId].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: operator,
    action: 'indicator_history_recorded',
    indicatorId: indicatorId,
    oldScore: oldScore,
    newScore: newScore,
    reason: reason
  };
  
  mockData.auditHistory[cultivationObjectId].push(historyRecord);
  
  res.json({
    success: true,
    message: '指标历史记录成功',
    data: historyRecord
  });
});

// 展示评分结果历史数据
router.get('/cultivation-objects/:id/score-history', (req, res) => {
  const { id } = req.params;
  const { page = 1, pageSize = 10 } = req.query;
  
  const history = mockData.auditHistory[id] || [];
  const scoreHistory = history.filter(h => 
    h.action.includes('score') || h.action === 'audit_submitted'
  );
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedHistory = scoreHistory.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedHistory,
      total: scoreHistory.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 展示指标结果历史数据
router.get('/cultivation-objects/:id/indicator-history', (req, res) => {
  const { id } = req.params;
  const { indicatorId, page = 1, pageSize = 10 } = req.query;
  
  const history = mockData.auditHistory[id] || [];
  let indicatorHistory = history.filter(h => h.action.includes('indicator'));
  
  // 如果指定了指标ID，进一步筛选
  if (indicatorId) {
    indicatorHistory = indicatorHistory.filter(h => h.indicatorId == indicatorId);
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedHistory = indicatorHistory.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedHistory,
      total: indicatorHistory.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// ================= 通知管理接口 =================

// 获取审核意见通知模板
router.get('/notification-templates', (req, res) => {
  const { type } = req.query;
  let templates = mockData.notificationTemplates;
  
  if (type) {
    templates = templates.filter(t => t.type === type);
  }
  
  res.json({
    success: true,
    data: templates
  });
});

// 设置审核意见通知模板
router.post('/notification-templates', (req, res) => {
  const newTemplate = {
    id: mockData.notificationTemplates.length + 1,
    ...req.body,
    createTime: new Date().toLocaleString('zh-CN')
  };
  
  mockData.notificationTemplates.push(newTemplate);
  
  res.json({
    success: true,
    message: '通知模板设置成功',
    data: newTemplate
  });
});

// 修改通知模板
router.put('/notification-templates/:id', (req, res) => {
  const { id } = req.params;
  const templateIndex = mockData.notificationTemplates.findIndex(t => t.id == id);
  
  if (templateIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '通知模板不存在'
    });
  }
  
  mockData.notificationTemplates[templateIndex] = {
    ...mockData.notificationTemplates[templateIndex],
    ...req.body,
    updateTime: new Date().toLocaleString('zh-CN')
  };
  
  res.json({
    success: true,
    message: '通知模板修改成功',
    data: mockData.notificationTemplates[templateIndex]
  });
});

// 获取审核意见通知人员
router.get('/notification-personnel', (req, res) => {
  const { role } = req.query;
  let personnel = mockData.notificationPersonnel;
  
  if (role) {
    personnel = personnel.filter(p => p.role === role);
  }
  
  res.json({
    success: true,
    data: personnel
  });
});

// 设置审核意见通知人员
router.post('/notification-personnel', (req, res) => {
  const newPersonnel = {
    id: mockData.notificationPersonnel.length + 1,
    ...req.body,
    createTime: new Date().toLocaleString('zh-CN')
  };
  
  mockData.notificationPersonnel.push(newPersonnel);
  
  res.json({
    success: true,
    message: '通知人员设置成功',
    data: newPersonnel
  });
});

// 推送审核结果给相关人员
router.post('/notifications/push', (req, res) => {
  const { cultivationObjectId, templateId, recipientIds, customMessage } = req.body;
  
  const object = mockData.cultivationObjects.find(obj => obj.id == cultivationObjectId);
  const template = mockData.notificationTemplates.find(t => t.id == templateId);
  
  if (!object || !template) {
    return res.status(404).json({
      success: false,
      message: '培育对象或通知模板不存在'
    });
  }
  
  const notifications = recipientIds.map(recipientId => {
    const recipient = mockData.notificationPersonnel.find(p => p.userId == recipientId);
    return {
      id: Math.random().toString(36).substr(2, 9),
      recipientId: recipientId,
      recipientName: recipient ? recipient.userName : '未知用户',
      recipientEmail: recipient ? recipient.email : '',
      templateId: templateId,
      title: template.title,
      content: customMessage || template.content.replace('{{objectName}}', object.name),
      sendTime: new Date().toLocaleString('zh-CN'),
      status: 'sent'
    };
  });
  
  res.json({
    success: true,
    message: `通知推送成功，共推送${notifications.length}条消息`,
    data: notifications
  });
});

// ================= 动态评分机制接口 =================

// 获取最新评分规则
router.get('/scoring-rules/latest', (req, res) => {
  const latestRules = {
    id: 1,
    version: '2025.06',
    indicators: mockData.scoreIndicators,
    scoringMethod: 'weighted_average',
    updateTime: '2025-06-01 10:00:00',
    status: 'active'
  };
  
  res.json({
    success: true,
    data: latestRules
  });
});

// 实时更新评分数据
router.put('/scoring-data/update', (req, res) => {
  const { cultivationObjectIds, ruleVersion } = req.body;
  
  const updatedObjects = [];
  
  cultivationObjectIds.forEach(id => {
    const objectIndex = mockData.cultivationObjects.findIndex(obj => obj.id == id);
    if (objectIndex !== -1) {
      // 模拟重新计算评分
      const indicators = mockData.indicatorResults[id] || [];
      const newTotalScore = indicators.reduce((sum, ind) => sum + ind.score, 0);
      
      mockData.cultivationObjects[objectIndex].score = newTotalScore;
      updatedObjects.push(mockData.cultivationObjects[objectIndex]);
      
      // 记录更新历史
      if (!mockData.auditHistory[id]) {
        mockData.auditHistory[id] = [];
      }
      
      mockData.auditHistory[id].push({
        id: mockData.auditHistory[id].length + 1,
        auditDate: new Date().toLocaleString('zh-CN'),
        auditor: '系统自动',
        action: 'scoring_data_updated',
        reason: `根据规则版本${ruleVersion}重新计算评分`,
        newScore: newTotalScore
      });
    }
  });
  
  res.json({
    success: true,
    message: `评分数据更新成功，共更新${updatedObjects.length}个培育对象`,
    data: updatedObjects
  });
});

// ================= 申诉管理接口 =================

// 培育对象填写申诉申请
router.post('/appeals', (req, res) => {
  const { cultivationObjectId, appealReason, appealContent, submitter } = req.body;
  
  const object = mockData.cultivationObjects.find(obj => obj.id == cultivationObjectId);
  if (!object) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  const newAppeal = {
    id: mockData.appeals.length + 1,
    cultivationObjectId: cultivationObjectId,
    cultivationObjectName: object.name,
    appealReason: appealReason,
    appealContent: appealContent,
    appealStatus: 'submitted',
    submitter: submitter,
    submitTime: new Date().toLocaleString('zh-CN'),
    handlerId: null,
    handlerName: null,
    handleTime: null,
    handleResult: null,
    handleOpinion: null
  };
  
  mockData.appeals.push(newAppeal);
  
  res.json({
    success: true,
    message: '申诉申请提交成功',
    data: newAppeal
  });
});

// 获取申诉处理流程
router.get('/appeal-workflows', (req, res) => {
  const workflows = [
    {
      id: 1,
      name: '标准申诉处理流程',
      steps: [
        { step: 1, name: '申诉接收', description: '系统接收申诉申请', duration: 1 },
        { step: 2, name: '分配处理人', description: '自动分配申诉处理人', duration: 1 },
        { step: 3, name: '初步审查', description: '处理人进行初步审查', duration: 3 },
        { step: 4, name: '详细调查', description: '深入调查申诉内容', duration: 5 },
        { step: 5, name: '做出决定', description: '给出最终处理结果', duration: 2 },
        { step: 6, name: '结果通知', description: '通知申诉人处理结果', duration: 1 }
      ],
      totalDuration: 13,
      status: 'active'
    }
  ];
  
  res.json({
    success: true,
    data: workflows
  });
});

// 配置申诉处理流程
router.post('/appeal-workflows', (req, res) => {
  const newWorkflow = {
    id: Math.floor(Math.random() * 1000) + 2,
    ...req.body,
    createTime: new Date().toLocaleString('zh-CN'),
    status: 'active'
  };
  
  res.json({
    success: true,
    message: '申诉处理流程配置成功',
    data: newWorkflow
  });
});

// 获取申诉处理人
router.get('/appeals/:id/handlers', (req, res) => {
  const handlers = [
    { id: 1, name: '李处理员', department: '审核管理部', role: 'senior_handler', workload: 3 },
    { id: 2, name: '王处理员', department: '审核管理部', role: 'handler', workload: 5 },
    { id: 3, name: '张处理员', department: '质量监督部', role: 'specialist', workload: 2 }
  ];
  
  // 模拟自动分配逻辑：选择工作量最少的处理人
  const recommendedHandler = handlers.reduce((prev, current) => 
    prev.workload < current.workload ? prev : current
  );
  
  res.json({
    success: true,
    data: {
      availableHandlers: handlers,
      recommendedHandler: recommendedHandler
    }
  });
});

// 申诉处理
router.put('/appeals/:id/handle', (req, res) => {
  const { id } = req.params;
  const { handlerId, handlerName, handleResult, handleOpinion } = req.body;
  
  const appealIndex = mockData.appeals.findIndex(appeal => appeal.id == id);
  if (appealIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '申诉记录不存在'
    });
  }
  
  mockData.appeals[appealIndex] = {
    ...mockData.appeals[appealIndex],
    handlerId: handlerId,
    handlerName: handlerName,
    handleTime: new Date().toLocaleString('zh-CN'),
    handleResult: handleResult,
    handleOpinion: handleOpinion,
    appealStatus: 'handled'
  };
  
  res.json({
    success: true,
    message: '申诉处理完成',
    data: mockData.appeals[appealIndex]
  });
});

// 获取申诉列表
router.get('/appeals', (req, res) => {
  const { status, cultivationObjectId, page = 1, pageSize = 10 } = req.query;
  let filteredAppeals = [...mockData.appeals];
  
  if (status) {
    filteredAppeals = filteredAppeals.filter(appeal => appeal.appealStatus === status);
  }
  
  if (cultivationObjectId) {
    filteredAppeals = filteredAppeals.filter(appeal => appeal.cultivationObjectId == cultivationObjectId);
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedAppeals = filteredAppeals.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedAppeals,
      total: filteredAppeals.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

module.exports = router;