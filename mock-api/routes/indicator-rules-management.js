const express = require('express');
const router = express.Router();

// Mock数据存储
let indicators = [
  {
    id: 1,
    name: '理论中心组学习情况考核',
    dataSource: 'task',
    dataSourceTitle: '',
    calculationRule: {
      type: 'timeliness',
      onTimeScore: 100,
      overdueScore: 60,
      incompleteScore: 0
    },
    weight: 15,
    createTime: '2024-12-01 10:00:00',
    updateTime: '2024-12-01 10:00:00',
    creator: 'admin',
    status: 'active'
  },
  {
    id: 2,
    name: '问卷调查数据统计',
    dataSource: 'survey',
    dataSourceTitle: '党建工作满意度调查',
    calculationRule: {
      type: 'survey_result',
      excellentScore: 100,
      goodScore: 80,
      averageScore: 60,
      poorScore: 40
    },
    weight: 20,
    createTime: '2024-12-01 11:00:00',
    updateTime: '2024-12-01 11:00:00',
    creator: 'admin',
    status: 'active'
  },
  {
    id: 3,
    name: '投票活动参与度',
    dataSource: 'vote',
    dataSourceTitle: '优秀党员评选投票',
    calculationRule: {
      type: 'vote_result',
      highParticipationScore: 100,
      mediumParticipationScore: 70,
      lowParticipationScore: 40
    },
    weight: 10,
    createTime: '2024-12-01 12:00:00',
    updateTime: '2024-12-01 12:00:00',
    creator: 'admin',
    status: 'active'
  },
  {
    id: 4,
    name: '党建日常督查台账',
    dataSource: 'other',
    dataSourceTitle: '',
    calculationRule: {
      type: 'ranking',
      firstPlaceScore: 100,
      secondPlaceScore: 90,
      thirdPlaceScore: 80,
      otherScore: 60
    },
    weight: 25,
    createTime: '2024-12-01 13:00:00',
    updateTime: '2024-12-01 13:00:00',
    creator: 'admin',
    status: 'active'
  },
  {
    id: 5,
    name: '活动参与率统计',
    dataSource: 'activity',
    dataSourceTitle: '',
    calculationRule: {
      type: 'participation_rate',
      fullParticipationScore: 100,
      highParticipationScore: 85,
      mediumParticipationScore: 65,
      lowParticipationScore: 30
    },
    weight: 30,
    createTime: '2024-12-01 14:00:00',
    updateTime: '2024-12-01 14:00:00',
    creator: 'admin',
    status: 'active'
  }
];

let templates = [
  {
    id: 1,
    name: '党建工作评价模板',
    description: '适用于基层党组织的综合评价',
    indicators: [
      { indicatorId: 1, weight: 20 },
      { indicatorId: 2, weight: 30 },
      { indicatorId: 4, weight: 25 },
      { indicatorId: 5, weight: 25 }
    ],
    createTime: '2024-12-01 15:00:00',
    updateTime: '2024-12-01 15:00:00',
    creator: 'admin',
    status: 'active'
  },
  {
    id: 2,
    name: '先进集体评选模板',
    description: '用于评选先进党支部和党小组',
    indicators: [
      { indicatorId: 1, weight: 15 },
      { indicatorId: 2, weight: 20 },
      { indicatorId: 3, weight: 10 },
      { indicatorId: 4, weight: 30 },
      { indicatorId: 5, weight: 25 }
    ],
    createTime: '2024-12-01 16:00:00',
    updateTime: '2024-12-01 16:00:00',
    creator: 'admin',
    status: 'active'
  }
];

let analysisModels = [
  {
    id: 1,
    name: '加权平均模型',
    description: '基于指标权重的加权平均计算',
    formula: '∑(指标得分 × 权重)',
    parameters: {},
    status: 'active'
  },
  {
    id: 2,
    name: '层次分析模型',
    description: '多层次指标体系分析模型',
    formula: 'AHP综合评价',
    parameters: {
      consistencyRatio: 0.1
    },
    status: 'active'
  },
  {
    id: 3,
    name: '模糊综合评价模型',
    description: '考虑模糊性的综合评价模型',
    formula: '模糊算子综合评价',
    parameters: {
      membershipFunction: 'triangular'
    },
    status: 'active'
  }
];

let calculationResults = [
  {
    id: 1,
    templateId: 1,
    templateName: '党建工作评价模板',
    modelId: 1,
    modelName: '加权平均模型',
    targetUnit: '市委办公室党支部',
    indicators: [
      { indicatorId: 1, name: '理论中心组学习情况考核', score: 95, weight: 20 },
      { indicatorId: 2, name: '问卷调查数据统计', score: 88, weight: 30 },
      { indicatorId: 4, name: '党建日常督查台账', score: 90, weight: 25 },
      { indicatorId: 5, name: '活动参与率统计', score: 92, weight: 25 }
    ],
    finalScore: 90.65,
    level: '优秀',
    calculationTime: '2024-12-07 10:30:00',
    operator: 'admin'
  },
  {
    id: 2,
    templateId: 2,
    templateName: '先进集体评选模板',
    modelId: 1,
    modelName: '加权平均模型',
    targetUnit: '市政府办党支部',
    indicators: [
      { indicatorId: 1, name: '理论中心组学习情况考核', score: 85, weight: 15 },
      { indicatorId: 2, name: '问卷调查数据统计', score: 82, weight: 20 },
      { indicatorId: 3, name: '投票活动参与度', score: 78, weight: 10 },
      { indicatorId: 4, name: '党建日常督查台账', score: 88, weight: 30 },
      { indicatorId: 5, name: '活动参与率统计', score: 86, weight: 25 }
    ],
    finalScore: 84.6,
    level: '良好',
    calculationTime: '2024-12-07 11:30:00',
    operator: 'admin'
  }
];

// 数据源选项
const dataSourceOptions = {
  task: [
    { id: 1, title: '党建工作任务', type: 'task' },
    { id: 2, title: '理论学习任务', type: 'task' },
    { id: 3, title: '组织生活任务', type: 'task' }
  ],
  survey: [
    { id: 1, title: '党建工作满意度调查', type: 'survey' },
    { id: 2, title: '党员发展质量调查', type: 'survey' },
    { id: 3, title: '基层组织建设调查', type: 'survey' }
  ],
  vote: [
    { id: 1, title: '优秀党员评选投票', type: 'vote' },
    { id: 2, title: '先进党支部评选投票', type: 'vote' },
    { id: 3, title: '党务工作者评选投票', type: 'vote' }
  ],
  other: [
    { id: 1, title: '党建督查数据', type: 'other' },
    { id: 2, title: '组织活动数据', type: 'other' },
    { id: 3, title: '培训学习数据', type: 'other' }
  ]
};

// 工具函数
function generateId() {
  return Date.now();
}

function getCurrentTime() {
  return new Date().toISOString().replace('T', ' ').substr(0, 19);
}

function validateWeights(indicatorWeights) {
  const total = indicatorWeights.reduce((sum, item) => sum + (item.weight || 0), 0);
  return Math.abs(total - 100) < 0.01; // 允许0.01的误差
}

function calculateScore(indicators, selectedIndicators) {
  let totalScore = 0;
  let totalWeight = 0;

  selectedIndicators.forEach(selected => {
    const indicator = indicators.find(ind => ind.id === selected.indicatorId);
    if (indicator && indicator.calculationRule) {
      // 模拟计算指标得分
      let score = 0;
      const rule = indicator.calculationRule;
      
      switch (rule.type) {
        case 'timeliness':
          score = Math.random() > 0.7 ? rule.onTimeScore : (Math.random() > 0.5 ? rule.overdueScore : rule.incompleteScore);
          break;
        case 'survey_result':
          const surveyRand = Math.random();
          if (surveyRand > 0.8) score = rule.excellentScore;
          else if (surveyRand > 0.6) score = rule.goodScore;
          else if (surveyRand > 0.3) score = rule.averageScore;
          else score = rule.poorScore;
          break;
        case 'vote_result':
          const voteRand = Math.random();
          if (voteRand > 0.7) score = rule.highParticipationScore;
          else if (voteRand > 0.4) score = rule.mediumParticipationScore;
          else score = rule.lowParticipationScore;
          break;
        case 'ranking':
          const rankRand = Math.random();
          if (rankRand > 0.9) score = rule.firstPlaceScore;
          else if (rankRand > 0.8) score = rule.secondPlaceScore;
          else if (rankRand > 0.7) score = rule.thirdPlaceScore;
          else score = rule.otherScore;
          break;
        case 'participation_rate':
          const partRand = Math.random();
          if (partRand > 0.8) score = rule.fullParticipationScore;
          else if (partRand > 0.6) score = rule.highParticipationScore;
          else if (partRand > 0.3) score = rule.mediumParticipationScore;
          else score = rule.lowParticipationScore;
          break;
        default:
          score = 60 + Math.random() * 40; // 默认60-100分
      }
      
      totalScore += score * selected.weight / 100;
      totalWeight += selected.weight;
    }
  });

  return totalWeight > 0 ? totalScore : 0;
}

// ==================== 指标管理接口 ====================

// 获取指标列表
router.get('/indicators', (req, res) => {
  const { page = 1, pageSize = 10, name, dataSource, status } = req.query;
  
  let filteredIndicators = indicators;
  
  // 按名称筛选
  if (name) {
    filteredIndicators = filteredIndicators.filter(item => 
      item.name.includes(name)
    );
  }
  
  // 按数据源筛选
  if (dataSource) {
    filteredIndicators = filteredIndicators.filter(item => 
      item.dataSource === dataSource
    );
  }
  
  // 按状态筛选
  if (status) {
    filteredIndicators = filteredIndicators.filter(item => 
      item.status === status
    );
  }
  
  const total = filteredIndicators.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const data = filteredIndicators.slice(startIndex, endIndex);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '获取成功',
      data: {
        list: data,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  }, 100);
});

// 获取指标详情
router.get('/indicators/:id', (req, res) => {
  const { id } = req.params;
  const indicator = indicators.find(item => item.id == id);
  
  setTimeout(() => {
    if (indicator) {
      res.json({
        code: 200,
        message: '获取成功',
        data: indicator
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '指标不存在'
      });
    }
  }, 100);
});

// 创建指标
router.post('/indicators', (req, res) => {
  const { name, dataSource, dataSourceTitle, calculationRule } = req.body;
  
  // 验证必填字段
  if (!name || !dataSource || !calculationRule) {
    return res.status(400).json({
      code: 400,
      message: '请填写完整的指标信息'
    });
  }
  
  // 检查名称唯一性
  const existingIndicator = indicators.find(item => item.name === name);
  if (existingIndicator) {
    return res.status(400).json({
      code: 400,
      message: '指标名称已存在'
    });
  }
  
  const newIndicator = {
    id: generateId(),
    name,
    dataSource,
    dataSourceTitle: dataSourceTitle || '',
    calculationRule,
    weight: 0,
    createTime: getCurrentTime(),
    updateTime: getCurrentTime(),
    creator: 'admin',
    status: 'active'
  };
  
  indicators.push(newIndicator);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '创建成功',
      data: newIndicator
    });
  }, 200);
});

// 更新指标
router.put('/indicators/:id', (req, res) => {
  const { id } = req.params;
  const { name, dataSource, dataSourceTitle, calculationRule } = req.body;
  
  const indicatorIndex = indicators.findIndex(item => item.id == id);
  if (indicatorIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '指标不存在'
    });
  }
  
  // 检查名称唯一性（排除当前指标）
  if (name) {
    const existingIndicator = indicators.find(item => item.name === name && item.id != id);
    if (existingIndicator) {
      return res.status(400).json({
        code: 400,
        message: '指标名称已存在'
      });
    }
  }
  
  // 更新指标信息
  if (name !== undefined) indicators[indicatorIndex].name = name;
  if (dataSource !== undefined) indicators[indicatorIndex].dataSource = dataSource;
  if (dataSourceTitle !== undefined) indicators[indicatorIndex].dataSourceTitle = dataSourceTitle;
  if (calculationRule !== undefined) indicators[indicatorIndex].calculationRule = calculationRule;
  indicators[indicatorIndex].updateTime = getCurrentTime();
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '更新成功',
      data: indicators[indicatorIndex]
    });
  }, 200);
});

// 删除指标
router.delete('/indicators/:id', (req, res) => {
  const { id } = req.params;
  
  const indicatorIndex = indicators.findIndex(item => item.id == id);
  if (indicatorIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '指标不存在'
    });
  }
  
  // 检查是否被模板使用
  const isUsedInTemplate = templates.some(template => 
    template.indicators.some(ind => ind.indicatorId == id)
  );
  
  if (isUsedInTemplate) {
    return res.status(400).json({
      code: 400,
      message: '该指标正在被模板使用，无法删除'
    });
  }
  
  indicators.splice(indicatorIndex, 1);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '删除成功'
    });
  }, 200);
});

// 批量删除指标
router.delete('/indicators', (req, res) => {
  const { ids } = req.body;
  
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      code: 400,
      message: '请选择要删除的指标'
    });
  }
  
  // 检查是否有指标被模板使用
  const usedIndicators = [];
  ids.forEach(id => {
    const isUsed = templates.some(template => 
      template.indicators.some(ind => ind.indicatorId == id)
    );
    if (isUsed) {
      const indicator = indicators.find(item => item.id == id);
      if (indicator) usedIndicators.push(indicator.name);
    }
  });
  
  if (usedIndicators.length > 0) {
    return res.status(400).json({
      code: 400,
      message: `以下指标正在被模板使用，无法删除：${usedIndicators.join('、')}`
    });
  }
  
  // 执行删除
  indicators = indicators.filter(item => !ids.includes(item.id));
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '批量删除成功'
    });
  }, 200);
});

// ==================== 数据源管理接口 ====================

// 获取数据源选项
router.get('/data-sources', (req, res) => {
  const { type } = req.query;
  
  setTimeout(() => {
    if (type && dataSourceOptions[type]) {
      res.json({
        code: 200,
        message: '获取成功',
        data: dataSourceOptions[type]
      });
    } else {
      res.json({
        code: 200,
        message: '获取成功',
        data: dataSourceOptions
      });
    }
  }, 100);
});

// ==================== 指标演算接口 ====================

// 获取指标选择列表（用于演算）
router.get('/indicators/selection', (req, res) => {
  const activeIndicators = indicators.filter(item => item.status === 'active');
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '获取成功',
      data: activeIndicators.map(item => ({
        id: item.id,
        name: item.name,
        dataSource: item.dataSource,
        dataSourceTitle: item.dataSourceTitle,
        calculationRule: item.calculationRule
      }))
    });
  }, 100);
});

// 验证权重设置
router.post('/indicators/validate-weights', (req, res) => {
  const { indicators: selectedIndicators } = req.body;
  
  if (!Array.isArray(selectedIndicators) || selectedIndicators.length === 0) {
    return res.status(400).json({
      code: 400,
      message: '请选择至少一个指标'
    });
  }
  
  const isValid = validateWeights(selectedIndicators);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '验证完成',
      data: {
        valid: isValid,
        totalWeight: selectedIndicators.reduce((sum, item) => sum + (item.weight || 0), 0),
        message: isValid ? '权重设置正确' : '权重总和必须等于100%'
      }
    });
  }, 100);
});

// 执行指标演算
router.post('/indicators/calculate', (req, res) => {
  const { 
    selectedIndicators, 
    modelId, 
    targetUnit,
    description 
  } = req.body;
  
  // 验证参数
  if (!Array.isArray(selectedIndicators) || selectedIndicators.length === 0) {
    return res.status(400).json({
      code: 400,
      message: '请选择指标并设置权重'
    });
  }
  
  if (!validateWeights(selectedIndicators)) {
    return res.status(400).json({
      code: 400,
      message: '权重总和必须等于100%'
    });
  }
  
  const model = analysisModels.find(m => m.id == modelId);
  if (!model) {
    return res.status(400).json({
      code: 400,
      message: '请选择分析模型'
    });
  }
  
  // 计算得分
  const finalScore = calculateScore(indicators, selectedIndicators);
  
  // 确定等级
  let level;
  if (finalScore >= 90) level = '优秀';
  else if (finalScore >= 80) level = '良好';
  else if (finalScore >= 60) level = '合格';
  else level = '需改进';
  
  // 构建结果详情
  const detailedIndicators = selectedIndicators.map(selected => {
    const indicator = indicators.find(ind => ind.id === selected.indicatorId);
    const score = calculateScore([indicator], [selected]);
    return {
      indicatorId: selected.indicatorId,
      name: indicator?.name || '',
      score: Number(score.toFixed(2)),
      weight: selected.weight
    };
  });
  
  const result = {
    id: generateId(),
    selectedIndicators: detailedIndicators,
    modelId,
    modelName: model.name,
    targetUnit: targetUnit || '默认评价对象',
    finalScore: Number(finalScore.toFixed(2)),
    level,
    description: description || '',
    calculationTime: getCurrentTime(),
    operator: 'admin'
  };
  
  // 保存计算结果
  calculationResults.unshift(result);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '演算完成',
      data: result
    });
  }, 500);
});

// 获取分析模型列表
router.get('/analysis-models', (req, res) => {
  setTimeout(() => {
    res.json({
      code: 200,
      message: '获取成功',
      data: analysisModels.filter(model => model.status === 'active')
    });
  }, 100);
});

// 获取演算历史记录
router.get('/calculations', (req, res) => {
  const { page = 1, pageSize = 10, targetUnit, modelId } = req.query;
  
  let filteredResults = calculationResults;
  
  if (targetUnit) {
    filteredResults = filteredResults.filter(item => 
      item.targetUnit.includes(targetUnit)
    );
  }
  
  if (modelId) {
    filteredResults = filteredResults.filter(item => 
      item.modelId == modelId
    );
  }
  
  const total = filteredResults.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const data = filteredResults.slice(startIndex, endIndex);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '获取成功',
      data: {
        list: data,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  }, 100);
});

// 获取演算详情
router.get('/calculations/:id', (req, res) => {
  const { id } = req.params;
  const result = calculationResults.find(item => item.id == id);
  
  setTimeout(() => {
    if (result) {
      res.json({
        code: 200,
        message: '获取成功',
        data: result
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '演算记录不存在'
      });
    }
  }, 100);
});

// ==================== 指标模板管理接口 ====================

// 获取模板列表
router.get('/templates', (req, res) => {
  const { page = 1, pageSize = 10, name, status } = req.query;
  
  let filteredTemplates = templates;
  
  if (name) {
    filteredTemplates = filteredTemplates.filter(item => 
      item.name.includes(name)
    );
  }
  
  if (status) {
    filteredTemplates = filteredTemplates.filter(item => 
      item.status === status
    );
  }
  
  const total = filteredTemplates.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const data = filteredTemplates.slice(startIndex, endIndex);
  
  // 补充指标详细信息
  const enrichedData = data.map(template => ({
    ...template,
    indicators: template.indicators.map(item => {
      const indicator = indicators.find(ind => ind.id === item.indicatorId);
      return {
        ...item,
        name: indicator?.name || '未知指标',
        dataSource: indicator?.dataSource || ''
      };
    }),
    totalWeight: template.indicators.reduce((sum, item) => sum + item.weight, 0)
  }));
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '获取成功',
      data: {
        list: enrichedData,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    });
  }, 100);
});

// 获取模板详情
router.get('/templates/:id', (req, res) => {
  const { id } = req.params;
  const template = templates.find(item => item.id == id);
  
  setTimeout(() => {
    if (template) {
      // 补充指标详细信息
      const enrichedTemplate = {
        ...template,
        indicators: template.indicators.map(item => {
          const indicator = indicators.find(ind => ind.id === item.indicatorId);
          return {
            ...item,
            name: indicator?.name || '未知指标',
            dataSource: indicator?.dataSource || '',
            calculationRule: indicator?.calculationRule || {}
          };
        }),
        totalWeight: template.indicators.reduce((sum, item) => sum + item.weight, 0)
      };
      
      res.json({
        code: 200,
        message: '获取成功',
        data: enrichedTemplate
      });
    } else {
      res.status(404).json({
        code: 404,
        message: '模板不存在'
      });
    }
  }, 100);
});

// 创建模板
router.post('/templates', (req, res) => {
  const { name, description, indicators: templateIndicators } = req.body;
  
  if (!name || !Array.isArray(templateIndicators) || templateIndicators.length === 0) {
    return res.status(400).json({
      code: 400,
      message: '请填写模板名称并选择指标'
    });
  }
  
  // 验证权重
  if (!validateWeights(templateIndicators)) {
    return res.status(400).json({
      code: 400,
      message: '指标权重总和必须等于100%'
    });
  }
  
  // 检查名称唯一性
  const existingTemplate = templates.find(item => item.name === name);
  if (existingTemplate) {
    return res.status(400).json({
      code: 400,
      message: '模板名称已存在'
    });
  }
  
  const newTemplate = {
    id: generateId(),
    name,
    description: description || '',
    indicators: templateIndicators,
    createTime: getCurrentTime(),
    updateTime: getCurrentTime(),
    creator: 'admin',
    status: 'active'
  };
  
  templates.push(newTemplate);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '创建成功',
      data: newTemplate
    });
  }, 200);
});

// 更新模板
router.put('/templates/:id', (req, res) => {
  const { id } = req.params;
  const { name, description, indicators: templateIndicators } = req.body;
  
  const templateIndex = templates.findIndex(item => item.id == id);
  if (templateIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '模板不存在'
    });
  }
  
  // 验证权重
  if (templateIndicators && !validateWeights(templateIndicators)) {
    return res.status(400).json({
      code: 400,
      message: '指标权重总和必须等于100%'
    });
  }
  
  // 检查名称唯一性（排除当前模板）
  if (name) {
    const existingTemplate = templates.find(item => item.name === name && item.id != id);
    if (existingTemplate) {
      return res.status(400).json({
        code: 400,
        message: '模板名称已存在'
      });
    }
  }
  
  // 更新模板信息
  if (name !== undefined) templates[templateIndex].name = name;
  if (description !== undefined) templates[templateIndex].description = description;
  if (templateIndicators !== undefined) templates[templateIndex].indicators = templateIndicators;
  templates[templateIndex].updateTime = getCurrentTime();
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '更新成功',
      data: templates[templateIndex]
    });
  }, 200);
});

// 删除模板
router.delete('/templates/:id', (req, res) => {
  const { id } = req.params;
  
  const templateIndex = templates.findIndex(item => item.id == id);
  if (templateIndex === -1) {
    return res.status(404).json({
      code: 404,
      message: '模板不存在'
    });
  }
  
  templates.splice(templateIndex, 1);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '删除成功'
    });
  }, 200);
});

// 模板导入到项目
router.post('/templates/:id/import', (req, res) => {
  const { id } = req.params;
  const { projectId, projectName } = req.body;
  
  const template = templates.find(item => item.id == id);
  if (!template) {
    return res.status(404).json({
      code: 404,
      message: '模板不存在'
    });
  }
  
  // 模拟导入过程
  const importResult = {
    templateId: template.id,
    templateName: template.name,
    projectId: projectId || generateId(),
    projectName: projectName || '默认项目',
    indicators: template.indicators.map(item => {
      const indicator = indicators.find(ind => ind.id === item.indicatorId);
      return {
        ...item,
        name: indicator?.name || '未知指标'
      };
    }),
    importTime: getCurrentTime(),
    operator: 'admin'
  };
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '模板导入成功',
      data: importResult
    });
  }, 300);
});

// 复制模板
router.post('/templates/:id/copy', (req, res) => {
  const { id } = req.params;
  const { name } = req.body;
  
  const template = templates.find(item => item.id == id);
  if (!template) {
    return res.status(404).json({
      code: 404,
      message: '模板不存在'
    });
  }
  
  if (!name) {
    return res.status(400).json({
      code: 400,
      message: '请输入新模板名称'
    });
  }
  
  // 检查名称唯一性
  const existingTemplate = templates.find(item => item.name === name);
  if (existingTemplate) {
    return res.status(400).json({
      code: 400,
      message: '模板名称已存在'
    });
  }
  
  const newTemplate = {
    ...template,
    id: generateId(),
    name,
    description: `复制自：${template.name}`,
    createTime: getCurrentTime(),
    updateTime: getCurrentTime(),
    creator: 'admin'
  };
  
  templates.push(newTemplate);
  
  setTimeout(() => {
    res.json({
      code: 200,
      message: '复制成功',
      data: newTemplate
    });
  }, 200);
});

module.exports = router;