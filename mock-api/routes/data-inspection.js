/**
 * 数据体检模块 Mock API
 * 基于MCP工具深度分析需求文档生成
 */
const express = require('express');
const router = express.Router();

// 模拟数据存储
let mockData = {
  // 用户权限数据
  userPermissions: {
    1: { userId: 1, canViewInspection: true, canConfigureRules: true, canHandleExceptions: true },
    2: { userId: 2, canViewInspection: true, canConfigureRules: false, canHandleExceptions: true },
    3: { userId: 3, canViewInspection: true, canConfigureRules: false, canHandleExceptions: false }
  },
  
  // 异常类型统计
  exceptionStatistics: {
    byType: [
      { type: '数据缺失', count: 15, severity: 'high', description: '关键字段缺失或为空' },
      { type: '数据格式错误', count: 8, severity: 'medium', description: '数据格式不符合规范' },
      { type: '数据重复', count: 12, severity: 'medium', description: '存在重复记录' },
      { type: '数据不一致', count: 6, severity: 'high', description: '跨系统数据不一致' },
      { type: '数据过期', count: 4, severity: 'low', description: '数据超过有效期' },
      { type: '权限异常', count: 3, severity: 'high', description: '数据访问权限异常' }
    ],
    byUnit: [
      { unit: '市委办公室', totalExceptions: 12, highSeverity: 3, mediumSeverity: 6, lowSeverity: 3 },
      { unit: '市教育局', totalExceptions: 8, highSeverity: 2, mediumSeverity: 4, lowSeverity: 2 },
      { unit: '市卫健委', totalExceptions: 15, highSeverity: 5, mediumSeverity: 7, lowSeverity: 3 },
      { unit: '市财政局', totalExceptions: 6, highSeverity: 1, mediumSeverity: 3, lowSeverity: 2 },
      { unit: '市人社局', totalExceptions: 7, highSeverity: 2, mediumSeverity: 3, lowSeverity: 2 }
    ]
  },
  
  // 异常详情数据
  exceptionDetails: [
    {
      id: 1,
      type: '数据缺失',
      title: '党组织基本信息缺失',
      description: '市委办公室党支部的成立时间字段为空',
      unit: '市委办公室',
      source: '党组织管理系统',
      severity: 'high',
      status: 'pending',
      detectTime: '2025-06-15 09:30:00',
      affectedRecords: 1,
      impact: '影响党组织基础数据完整性统计',
      solution: '联系市委办公室补充党支部成立时间信息',
      handleTime: null,
      handler: null
    },
    {
      id: 2,
      type: '数据格式错误',
      title: '联系电话格式不规范',
      description: '教育局机关党支部联系电话格式不符合标准',
      unit: '市教育局',
      source: '党员信息管理系统',
      severity: 'medium',
      status: 'pending',
      detectTime: '2025-06-14 14:20:00',
      affectedRecords: 3,
      impact: '影响联系方式的标准化管理',
      solution: '将电话格式调整为标准的11位手机号或带区号的固话格式',
      handleTime: null,
      handler: null
    },
    {
      id: 3,
      type: '数据重复',
      title: '党员信息重复录入',
      description: '张三的党员信息在系统中存在重复记录',
      unit: '市卫健委',
      source: '党员信息管理系统',
      severity: 'medium',
      status: 'resolved',
      detectTime: '2025-06-13 16:45:00',
      affectedRecords: 2,
      impact: '影响党员统计数据准确性',
      solution: '合并重复记录，保留最新的党员信息',
      handleTime: '2025-06-14 10:30:00',
      handler: '李数据员'
    }
  ],
  
  // 体检规则配置
  inspectionRules: {
    partyOrganization: {
      id: 1,
      name: '党组（党委）设置体检',
      description: '检查党组（党委）设置的完整性和规范性',
      rules: [
        {
          id: 101,
          fieldName: '党组织名称',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '党组织名称不能为空'
        },
        {
          id: 102,
          fieldName: '成立时间',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '成立时间不能为空'
        },
        {
          id: 103,
          fieldName: '党组织类型',
          ruleType: 'enum',
          ruleValue: ['党委', '党总支', '党支部'],
          errorMessage: '党组织类型必须是：党委、党总支、党支部之一'
        },
        {
          id: 104,
          fieldName: '负责人',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '党组织负责人不能为空'
        }
      ],
      status: 'active',
      lastUpdate: '2025-06-01 10:00:00'
    },
    
    partyOfficials: {
      id: 2,
      name: '党务干部任免体检',
      description: '检查党务干部任免信息的完整性和合规性',
      rules: [
        {
          id: 201,
          fieldName: '干部姓名',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '党务干部姓名不能为空'
        },
        {
          id: 202,
          fieldName: '任职时间',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '任职时间不能为空'
        },
        {
          id: 203,
          fieldName: '职务',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '职务信息不能为空'
        },
        {
          id: 204,
          fieldName: '任免文件编号',
          ruleType: 'pattern',
          ruleValue: '^[A-Z]{2,4}\\[\\d{4}\\]\\d{1,3}号$',
          errorMessage: '任免文件编号格式不正确'
        }
      ],
      status: 'active',
      lastUpdate: '2025-05-20 14:30:00'
    },
    
    tasks: {
      id: 3,
      name: '任务体检',
      description: '检查任务执行情况和关键节点',
      rules: [
        {
          id: 301,
          fieldName: '任务名称',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '任务名称不能为空'
        },
        {
          id: 302,
          fieldName: '完成时间',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '任务完成时间不能为空'
        },
        {
          id: 303,
          fieldName: '完成状态',
          ruleType: 'enum',
          ruleValue: ['已完成', '进行中', '未开始', '已逾期'],
          errorMessage: '任务状态必须是规定的状态值之一'
        },
        {
          id: 304,
          fieldName: '执行质量评分',
          ruleType: 'range',
          ruleValue: { min: 0, max: 100 },
          errorMessage: '执行质量评分必须在0-100之间'
        }
      ],
      status: 'active',
      lastUpdate: '2025-06-10 09:15:00'
    },
    
    userInfo: {
      id: 4,
      name: '用户信息完整体检',
      description: '检查用户基本信息的完整性',
      rules: [
        {
          id: 401,
          fieldName: '用户姓名',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '用户姓名不能为空'
        },
        {
          id: 402,
          fieldName: '身份证号',
          ruleType: 'pattern',
          ruleValue: '^\\d{17}[\\dXx]$',
          errorMessage: '身份证号码格式不正确'
        },
        {
          id: 403,
          fieldName: '联系电话',
          ruleType: 'pattern',
          ruleValue: '^1[3-9]\\d{9}$',
          errorMessage: '手机号码格式不正确'
        },
        {
          id: 404,
          fieldName: '所属部门',
          ruleType: 'required',
          ruleValue: true,
          errorMessage: '所属部门不能为空'
        }
      ],
      status: 'active',
      lastUpdate: '2025-06-05 16:20:00'
    }
  },
  
  // 自动体检配置
  autoInspectionConfig: {
    id: 1,
    name: '定时全面体检',
    enabled: true,
    schedule: {
      frequency: 'daily',
      time: '02:00:00',
      timezone: 'Asia/Shanghai'
    },
    inspectionScope: [
      'party_organization',
      'party_officials', 
      'tasks',
      'user_info'
    ],
    checkTypes: [
      'completeness',
      'accuracy', 
      'consistency',
      'security'
    ],
    lastRun: '2025-06-15 02:00:00',
    nextRun: '2025-06-16 02:00:00',
    status: 'active'
  },
  
  // 体检结果
  inspectionResults: [
    {
      id: 1,
      inspectionDate: '2025-06-15 02:00:00',
      inspectionType: 'auto',
      totalRecords: 1250,
      exceptionCount: 48,
      exceptionRate: 0.0384,
      status: 'completed',
      duration: 120, // 秒
      summary: {
        completeness: { passed: 1202, failed: 48, rate: 0.9616 },
        accuracy: { passed: 1230, failed: 20, rate: 0.984 },
        consistency: { passed: 1238, failed: 12, rate: 0.9904 },
        security: { passed: 1247, failed: 3, rate: 0.9976 }
      }
    },
    {
      id: 2,
      inspectionDate: '2025-06-14 02:00:00',
      inspectionType: 'auto',
      totalRecords: 1245,
      exceptionCount: 52,
      exceptionRate: 0.0418,
      status: 'completed',
      duration: 115,
      summary: {
        completeness: { passed: 1193, failed: 52, rate: 0.9582 },
        accuracy: { passed: 1225, failed: 20, rate: 0.9839 },
        consistency: { passed: 1235, failed: 10, rate: 0.992 },
        security: { passed: 1242, failed: 3, rate: 0.9976 }
      }
    }
  ],
  
  // 整改记录
  remediationRecords: [
    {
      id: 1,
      exceptionId: 3,
      exceptionTitle: '党员信息重复录入',
      remediationAction: '合并重复记录',
      remediationDetail: '保留ID为12345的记录，删除重复记录ID为12346的记录',
      operator: '李数据员',
      startTime: '2025-06-14 09:00:00',
      completeTime: '2025-06-14 10:30:00',
      status: 'completed',
      verificationResult: 'passed',
      verificationTime: '2025-06-14 11:00:00'
    },
    {
      id: 2,
      exceptionId: 1,
      exceptionTitle: '党组织基本信息缺失',
      remediationAction: '补充缺失信息',
      remediationDetail: '联系市委办公室党支部，补充成立时间为2020年3月15日',
      operator: '王数据员',
      startTime: '2025-06-15 10:00:00',
      completeTime: null,
      status: 'in_progress',
      verificationResult: null,
      verificationTime: null
    }
  ]
};

// ================= 权限控制接口 =================

// 获取数据体检用户权限
router.get('/permissions/:userId', (req, res) => {
  const { userId } = req.params;
  const permission = mockData.userPermissions[userId];
  
  if (!permission) {
    return res.status(404).json({
      success: false,
      message: '用户权限信息不存在'
    });
  }
  
  res.json({
    success: true,
    data: permission
  });
});

// ================= 数据体检展示接口 =================

// 获取异常信息概览
router.get('/exceptions/overview', (req, res) => {
  const overview = {
    totalExceptions: mockData.exceptionStatistics.byType.reduce((sum, item) => sum + item.count, 0),
    highSeverityCount: mockData.exceptionStatistics.byType
      .filter(item => item.severity === 'high')
      .reduce((sum, item) => sum + item.count, 0),
    mediumSeverityCount: mockData.exceptionStatistics.byType
      .filter(item => item.severity === 'medium')
      .reduce((sum, item) => sum + item.count, 0),
    lowSeverityCount: mockData.exceptionStatistics.byType
      .filter(item => item.severity === 'low')
      .reduce((sum, item) => sum + item.count, 0),
    lastInspectionTime: mockData.inspectionResults[0]?.inspectionDate,
    nextInspectionTime: mockData.autoInspectionConfig.nextRun
  };
  
  res.json({
    success: true,
    data: overview
  });
});

// 按异常项统计
router.get('/exceptions/statistics/by-type', (req, res) => {
  res.json({
    success: true,
    data: mockData.exceptionStatistics.byType
  });
});

// 按单位统计
router.get('/exceptions/statistics/by-unit', (req, res) => {
  res.json({
    success: true,
    data: mockData.exceptionStatistics.byUnit
  });
});

// 获取异常详情列表
router.get('/exceptions/details', (req, res) => {
  const { type, unit, severity, status, page = 1, pageSize = 10 } = req.query;
  let filteredDetails = [...mockData.exceptionDetails];
  
  // 按类型筛选
  if (type) {
    filteredDetails = filteredDetails.filter(detail => detail.type === type);
  }
  
  // 按单位筛选
  if (unit) {
    filteredDetails = filteredDetails.filter(detail => detail.unit === unit);
  }
  
  // 按严重程度筛选
  if (severity) {
    filteredDetails = filteredDetails.filter(detail => detail.severity === severity);
  }
  
  // 按状态筛选
  if (status) {
    filteredDetails = filteredDetails.filter(detail => detail.status === status);
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedDetails = filteredDetails.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedDetails,
      total: filteredDetails.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 获取特定异常详情
router.get('/exceptions/details/:id', (req, res) => {
  const { id } = req.params;
  const detail = mockData.exceptionDetails.find(item => item.id == id);
  
  if (!detail) {
    return res.status(404).json({
      success: false,
      message: '异常详情不存在'
    });
  }
  
  res.json({
    success: true,
    data: detail
  });
});

// ================= 体检规则配置接口 =================

// 获取党组（党委）设置体检规则
router.get('/rules/party-organization', (req, res) => {
  res.json({
    success: true,
    data: mockData.inspectionRules.partyOrganization
  });
});

// 设置党组（党委）设置体检规则
router.post('/rules/party-organization', (req, res) => {
  const { rules } = req.body;
  
  mockData.inspectionRules.partyOrganization.rules = rules;
  mockData.inspectionRules.partyOrganization.lastUpdate = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: '党组（党委）设置体检规则更新成功',
    data: mockData.inspectionRules.partyOrganization
  });
});

// 获取党务干部任免体检规则
router.get('/rules/party-officials', (req, res) => {
  res.json({
    success: true,
    data: mockData.inspectionRules.partyOfficials
  });
});

// 设置党务干部任免体检规则
router.post('/rules/party-officials', (req, res) => {
  const { rules } = req.body;
  
  mockData.inspectionRules.partyOfficials.rules = rules;
  mockData.inspectionRules.partyOfficials.lastUpdate = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: '党务干部任免体检规则更新成功',
    data: mockData.inspectionRules.partyOfficials
  });
});

// 获取任务体检规则
router.get('/rules/tasks', (req, res) => {
  res.json({
    success: true,
    data: mockData.inspectionRules.tasks
  });
});

// 设置任务体检规则
router.post('/rules/tasks', (req, res) => {
  const { rules } = req.body;
  
  mockData.inspectionRules.tasks.rules = rules;
  mockData.inspectionRules.tasks.lastUpdate = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: '任务体检规则更新成功',
    data: mockData.inspectionRules.tasks
  });
});

// 获取用户信息完整体检规则
router.get('/rules/user-info', (req, res) => {
  res.json({
    success: true,
    data: mockData.inspectionRules.userInfo
  });
});

// 设置用户信息完整体检规则
router.post('/rules/user-info', (req, res) => {
  const { rules } = req.body;
  
  mockData.inspectionRules.userInfo.rules = rules;
  mockData.inspectionRules.userInfo.lastUpdate = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: '用户信息完整体检规则更新成功',
    data: mockData.inspectionRules.userInfo
  });
});

// 获取所有体检规则
router.get('/rules', (req, res) => {
  res.json({
    success: true,
    data: mockData.inspectionRules
  });
});

// ================= 自动体检执行接口 =================

// 获取自动体检配置
router.get('/auto-inspection/config', (req, res) => {
  res.json({
    success: true,
    data: mockData.autoInspectionConfig
  });
});

// 设置自动体检配置
router.put('/auto-inspection/config', (req, res) => {
  mockData.autoInspectionConfig = {
    ...mockData.autoInspectionConfig,
    ...req.body,
    lastUpdate: new Date().toLocaleString('zh-CN')
  };
  
  res.json({
    success: true,
    message: '自动体检配置更新成功',
    data: mockData.autoInspectionConfig
  });
});

// 手动触发体检
router.post('/inspection/trigger', (req, res) => {
  const { inspectionScope, checkTypes } = req.body;
  
  // 模拟体检执行
  const newInspectionResult = {
    id: mockData.inspectionResults.length + 1,
    inspectionDate: new Date().toLocaleString('zh-CN'),
    inspectionType: 'manual',
    totalRecords: Math.floor(Math.random() * 1000) + 1000,
    exceptionCount: Math.floor(Math.random() * 50) + 10,
    status: 'running',
    duration: null,
    inspectionScope: inspectionScope || mockData.autoInspectionConfig.inspectionScope,
    checkTypes: checkTypes || mockData.autoInspectionConfig.checkTypes
  };
  
  // 计算异常率
  newInspectionResult.exceptionRate = newInspectionResult.exceptionCount / newInspectionResult.totalRecords;
  
  mockData.inspectionResults.unshift(newInspectionResult);
  
  // 模拟异步完成
  setTimeout(() => {
    const resultIndex = mockData.inspectionResults.findIndex(r => r.id === newInspectionResult.id);
    if (resultIndex !== -1) {
      mockData.inspectionResults[resultIndex].status = 'completed';
      mockData.inspectionResults[resultIndex].duration = Math.floor(Math.random() * 60) + 60;
      mockData.inspectionResults[resultIndex].summary = {
        completeness: {
          passed: newInspectionResult.totalRecords - Math.floor(Math.random() * 30) - 10,
          failed: Math.floor(Math.random() * 30) + 10,
          rate: 0.95 + Math.random() * 0.04
        },
        accuracy: {
          passed: newInspectionResult.totalRecords - Math.floor(Math.random() * 20) - 5,
          failed: Math.floor(Math.random() * 20) + 5,
          rate: 0.97 + Math.random() * 0.02
        },
        consistency: {
          passed: newInspectionResult.totalRecords - Math.floor(Math.random() * 15) - 3,
          failed: Math.floor(Math.random() * 15) + 3,
          rate: 0.98 + Math.random() * 0.015
        },
        security: {
          passed: newInspectionResult.totalRecords - Math.floor(Math.random() * 5) - 1,
          failed: Math.floor(Math.random() * 5) + 1,
          rate: 0.995 + Math.random() * 0.004
        }
      };
    }
  }, 5000);
  
  res.json({
    success: true,
    message: '体检任务已启动',
    data: newInspectionResult
  });
});

// 获取体检结果列表
router.get('/inspection/results', (req, res) => {
  const { page = 1, pageSize = 10, status } = req.query;
  let filteredResults = [...mockData.inspectionResults];
  
  if (status) {
    filteredResults = filteredResults.filter(result => result.status === status);
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedResults = filteredResults.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedResults,
      total: filteredResults.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 获取特定体检结果详情
router.get('/inspection/results/:id', (req, res) => {
  const { id } = req.params;
  const result = mockData.inspectionResults.find(item => item.id == id);
  
  if (!result) {
    return res.status(404).json({
      success: false,
      message: '体检结果不存在'
    });
  }
  
  res.json({
    success: true,
    data: result
  });
});

// ================= 体检结果整改接口 =================

// 获取整改权限验证
router.get('/remediation/permissions/:userId', (req, res) => {
  const { userId } = req.params;
  const permission = mockData.userPermissions[userId];
  
  if (!permission) {
    return res.status(404).json({
      success: false,
      message: '用户权限信息不存在'
    });
  }
  
  res.json({
    success: true,
    data: {
      canHandleExceptions: permission.canHandleExceptions
    }
  });
});

// 创建整改记录
router.post('/remediation/records', (req, res) => {
  const { exceptionId, remediationAction, remediationDetail, operator } = req.body;
  
  const exception = mockData.exceptionDetails.find(item => item.id == exceptionId);
  if (!exception) {
    return res.status(404).json({
      success: false,
      message: '异常信息不存在'
    });
  }
  
  const newRecord = {
    id: mockData.remediationRecords.length + 1,
    exceptionId: exceptionId,
    exceptionTitle: exception.title,
    remediationAction: remediationAction,
    remediationDetail: remediationDetail,
    operator: operator,
    startTime: new Date().toLocaleString('zh-CN'),
    completeTime: null,
    status: 'in_progress',
    verificationResult: null,
    verificationTime: null
  };
  
  mockData.remediationRecords.push(newRecord);
  
  // 更新异常状态
  exception.status = 'in_remediation';
  exception.handler = operator;
  exception.handleTime = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: '整改记录创建成功',
    data: newRecord
  });
});

// 更新整改记录
router.put('/remediation/records/:id', (req, res) => {
  const { id } = req.params;
  const { status, completeTime, verificationResult } = req.body;
  
  const recordIndex = mockData.remediationRecords.findIndex(record => record.id == id);
  if (recordIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '整改记录不存在'
    });
  }
  
  mockData.remediationRecords[recordIndex] = {
    ...mockData.remediationRecords[recordIndex],
    status: status,
    completeTime: completeTime || new Date().toLocaleString('zh-CN'),
    verificationResult: verificationResult,
    verificationTime: verificationResult ? new Date().toLocaleString('zh-CN') : null
  };
  
  // 如果整改完成且验证通过，更新异常状态
  if (status === 'completed' && verificationResult === 'passed') {
    const exception = mockData.exceptionDetails.find(
      item => item.id === mockData.remediationRecords[recordIndex].exceptionId
    );
    if (exception) {
      exception.status = 'resolved';
    }
  }
  
  res.json({
    success: true,
    message: '整改记录更新成功',
    data: mockData.remediationRecords[recordIndex]
  });
});

// 获取整改记录列表
router.get('/remediation/records', (req, res) => {
  const { status, operator, page = 1, pageSize = 10 } = req.query;
  let filteredRecords = [...mockData.remediationRecords];
  
  if (status) {
    filteredRecords = filteredRecords.filter(record => record.status === status);
  }
  
  if (operator) {
    filteredRecords = filteredRecords.filter(record => 
      record.operator.toLowerCase().includes(operator.toLowerCase())
    );
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedRecords,
      total: filteredRecords.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 一键跳转到问题源头
router.get('/exceptions/:id/jump-to-source', (req, res) => {
  const { id } = req.params;
  const exception = mockData.exceptionDetails.find(item => item.id == id);
  
  if (!exception) {
    return res.status(404).json({
      success: false,
      message: '异常信息不存在'
    });
  }
  
  // 模拟生成跳转链接
  const jumpUrl = `/systems/${exception.source.replace(/\s/g, '-').toLowerCase()}?recordId=${exception.id}&unit=${exception.unit}`;
  
  res.json({
    success: true,
    data: {
      jumpUrl: jumpUrl,
      source: exception.source,
      description: `跳转到${exception.source}，定位相关数据记录`
    }
  });
});

// 整改后自动触发复检
router.post('/remediation/re-inspection/:remediationId', (req, res) => {
  const { remediationId } = req.params;
  
  const remediation = mockData.remediationRecords.find(record => record.id == remediationId);
  if (!remediation) {
    return res.status(404).json({
      success: false,
      message: '整改记录不存在'
    });
  }
  
  if (remediation.status !== 'completed') {
    return res.status(400).json({
      success: false,
      message: '整改尚未完成，无法进行复检'
    });
  }
  
  // 模拟复检过程
  const reInspectionResult = {
    id: Math.random().toString(36).substr(2, 9),
    remediationId: remediationId,
    exceptionId: remediation.exceptionId,
    inspectionTime: new Date().toLocaleString('zh-CN'),
    result: Math.random() > 0.2 ? 'passed' : 'failed', // 80%通过率
    details: '针对整改项目进行专项复检',
    operator: '系统自动复检'
  };
  
  // 更新整改记录的验证结果
  const recordIndex = mockData.remediationRecords.findIndex(record => record.id == remediationId);
  if (recordIndex !== -1) {
    mockData.remediationRecords[recordIndex].verificationResult = reInspectionResult.result;
    mockData.remediationRecords[recordIndex].verificationTime = reInspectionResult.inspectionTime;
  }
  
  res.json({
    success: true,
    message: '复检完成',
    data: reInspectionResult
  });
});

// ================================
// 数据源管理API - P0级关键功能补充
// ================================

// Mock数据：数据源相关
mockData.dataSources = [
  {
    id: 'ds-001',
    sourceName: '党组织基础数据库',
    sourceType: 'mysql',
    connectionUrl: 'mysql://localhost:3306/party_org',
    status: 1,
    lastSyncTime: '2025-01-18 14:30:00',
    syncStatus: 'success',
    description: '党组织基础信息数据源',
    createTime: '2025-01-01 00:00:00',
    updateTime: '2025-01-18 14:30:00'
  },
  {
    id: 'ds-002',
    sourceName: '党员档案管理系统',
    sourceType: 'postgresql',
    connectionUrl: 'postgresql://localhost:5432/member_archive',
    status: 2,
    lastSyncTime: '2025-01-18 10:00:00',
    syncStatus: 'failed',
    description: '党员个人档案信息数据源',
    createTime: '2025-01-01 00:00:00',
    updateTime: '2025-01-18 10:00:00'
  }
];

mockData.scheduledTasks = [
  {
    id: 1,
    taskName: '每日数据体检任务',
    taskType: 1,
    checkTypes: [1, 2, 3, 4],
    cronExpression: '0 2 * * *',
    cronDescription: '每天凌晨2点执行',
    isEnabled: true,
    status: 3,
    lastExecuteTime: '2025-01-18 02:00:00',
    nextExecuteTime: '2025-01-19 02:00:00',
    executionCount: 18,
    successCount: 17,
    failedCount: 1,
    avgDuration: 285,
    createTime: '2025-01-01 00:00:00',
    updateTime: '2025-01-18 02:00:00',
    creator: '系统管理员'
  }
];

// 获取数据源列表
router.get('/data-sources', (req, res) => {
  const { sourceName, sourceType, status } = req.query;
  let filteredData = [...mockData.dataSources];
  
  if (sourceName) {
    filteredData = filteredData.filter(item => item.sourceName.includes(sourceName));
  }
  if (sourceType) {
    filteredData = filteredData.filter(item => item.sourceType === sourceType);
  }
  if (status) {
    filteredData = filteredData.filter(item => item.status == status);
  }
  
  res.json({
    success: true,
    data: filteredData,
    total: filteredData.length
  });
});

// 创建数据源
router.post('/data-sources', (req, res) => {
  const { sourceName, sourceType, connectionUrl, description } = req.body;
  const newId = `ds-${Date.now()}`;
  
  const newDataSource = {
    id: newId,
    sourceName,
    sourceType,
    connectionUrl,
    status: 3, // 未测试
    syncStatus: 'pending',
    description,
    createTime: new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN')
  };
  
  mockData.dataSources.push(newDataSource);
  
  res.json({
    success: true,
    message: '创建数据源成功',
    data: { id: newId }
  });
});

// 更新数据源
router.put('/data-sources/:id', (req, res) => {
  const { id } = req.params;
  const updates = req.body;
  
  const index = mockData.dataSources.findIndex(ds => ds.id === id);
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: '数据源不存在'
    });
  }
  
  mockData.dataSources[index] = {
    ...mockData.dataSources[index],
    ...updates,
    updateTime: new Date().toLocaleString('zh-CN')
  };
  
  res.json({
    success: true,
    message: '更新数据源成功'
  });
});

// 删除数据源
router.delete('/data-sources/:id', (req, res) => {
  const { id } = req.params;
  const index = mockData.dataSources.findIndex(ds => ds.id === id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: '数据源不存在'
    });
  }
  
  mockData.dataSources.splice(index, 1);
  
  res.json({
    success: true,
    message: '删除数据源成功'
  });
});

// 测试数据源连接
router.post('/data-sources/:id/test-connection', (req, res) => {
  const { id } = req.params;
  
  // 模拟连接测试延迟
  setTimeout(() => {
    const isSuccess = Math.random() > 0.2; // 80%成功率
    
    if (isSuccess) {
      res.json({
        success: true,
        data: {
          success: true,
          message: '连接测试成功',
          latency: Math.floor(50 + Math.random() * 200),
          details: {
            serverVersion: '8.0.33',
            databaseSize: '2.3GB',
            tableCount: 25,
            lastResponse: new Date().toISOString()
          }
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          success: false,
          message: '连接超时或配置错误，请检查连接参数',
          latency: 5000
        }
      });
    }
  }, 1000 + Math.random() * 2000);
});

// 启动数据源同步
router.post('/data-sources/:id/sync', (req, res) => {
  const { id } = req.params;
  const { syncType = 'full' } = req.body;
  
  const taskId = Date.now();
  const estimatedDuration = syncType === 'full' ? 600 : 300;
  
  res.json({
    success: true,
    message: '同步任务已启动',
    data: {
      taskId,
      estimatedDuration,
      syncType
    }
  });
});

// 获取数据源同步状态
router.get('/data-sources/:id/sync-status', (req, res) => {
  const { id } = req.params;
  const isRunning = Math.random() > 0.7; // 30%概率正在同步
  
  const result = {
    isRunning,
    currentTask: isRunning ? {
      taskId: 1737180000000,
      progress: Math.floor(Math.random() * 100),
      startTime: '2025-01-18 14:00:00',
      estimatedEndTime: '2025-01-18 14:10:00',
      syncedRecords: Math.floor(Math.random() * 10000),
      totalRecords: 50000,
      currentTable: 'party_members'
    } : null,
    lastSync: {
      syncTime: '2025-01-18 10:30:00',
      status: 'success',
      syncedRecords: 48523,
      duration: 358
    },
    nextScheduledSync: '2025-01-18 18:00:00'
  };
  
  res.json({
    success: true,
    data: result
  });
});

// ================================
// 定时任务管理API - P1级功能增强
// ================================

// 获取定时任务列表
router.get('/scheduled-tasks', (req, res) => {
  const { taskName, taskType, status, isEnabled } = req.query;
  let filteredData = [...mockData.scheduledTasks];
  
  if (taskName) {
    filteredData = filteredData.filter(item => item.taskName.includes(taskName));
  }
  if (taskType) {
    filteredData = filteredData.filter(item => item.taskType == taskType);
  }
  if (status) {
    filteredData = filteredData.filter(item => item.status == status);
  }
  if (isEnabled !== undefined) {
    filteredData = filteredData.filter(item => item.isEnabled === (isEnabled === 'true'));
  }
  
  res.json({
    success: true,
    data: filteredData,
    total: filteredData.length
  });
});

// 创建定时任务
router.post('/scheduled-tasks', (req, res) => {
  const { taskName, checkTypes, cronExpression, isEnabled = true, description } = req.body;
  const newId = Date.now();
  
  const newTask = {
    id: newId,
    taskName,
    taskType: checkTypes[0] || 1,
    checkTypes,
    cronExpression,
    cronDescription: parseCronExpression(cronExpression),
    isEnabled,
    status: 1, // 待执行
    executionCount: 0,
    successCount: 0,
    failedCount: 0,
    avgDuration: 0,
    createTime: new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN'),
    creator: '当前用户'
  };
  
  mockData.scheduledTasks.push(newTask);
  
  res.json({
    success: true,
    message: '创建定时任务成功',
    data: { id: newId }
  });
});

// 更新定时任务
router.put('/scheduled-tasks/:id', (req, res) => {
  const { id } = req.params;
  const updates = req.body;
  
  const index = mockData.scheduledTasks.findIndex(task => task.id == id);
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: '定时任务不存在'
    });
  }
  
  mockData.scheduledTasks[index] = {
    ...mockData.scheduledTasks[index],
    ...updates,
    updateTime: new Date().toLocaleString('zh-CN')
  };
  
  res.json({
    success: true,
    message: '更新定时任务成功'
  });
});

// 删除定时任务
router.delete('/scheduled-tasks/:id', (req, res) => {
  const { id } = req.params;
  const index = mockData.scheduledTasks.findIndex(task => task.id == id);
  
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: '定时任务不存在'
    });
  }
  
  mockData.scheduledTasks.splice(index, 1);
  
  res.json({
    success: true,
    message: '删除定时任务成功'
  });
});

// 启用/禁用定时任务
router.put('/scheduled-tasks/:id/toggle', (req, res) => {
  const { id } = req.params;
  const { enabled } = req.body;
  
  const index = mockData.scheduledTasks.findIndex(task => task.id == id);
  if (index === -1) {
    return res.status(404).json({
      success: false,
      message: '定时任务不存在'
    });
  }
  
  mockData.scheduledTasks[index].isEnabled = enabled;
  mockData.scheduledTasks[index].updateTime = new Date().toLocaleString('zh-CN');
  
  res.json({
    success: true,
    message: `${enabled ? '启用' : '禁用'}定时任务成功`
  });
});

// 立即执行定时任务
router.post('/scheduled-tasks/:id/execute', (req, res) => {
  const { id } = req.params;
  const taskId = Date.now();
  const estimatedDuration = 300 + Math.floor(Math.random() * 600);
  
  res.json({
    success: true,
    message: '任务已加入执行队列',
    data: {
      taskId,
      estimatedDuration
    }
  });
});

// 停止数据源同步
router.post('/data-sources/:id/stop-sync', (req, res) => {
  const { id } = req.params;
  
  res.json({
    success: true,
    message: '数据源同步已停止',
    data: { id }
  });
});

// 获取数据源同步历史
router.get('/data-sources/:id/sync-history', (req, res) => {
  const { id } = req.params;
  const { status, dateRange } = req.query;
  
  const mockHistory = [
    {
      id: 1,
      syncTime: '2025-01-18 10:30:00',
      syncType: 'full',
      status: 'success',
      duration: 358,
      syncedRecords: 48523,
      totalRecords: 48523,
      details: {
        tablesProcessed: ['party_organizations', 'party_members', 'party_activities'],
        changedRecords: 156,
        deletedRecords: 3,
        addedRecords: 23
      }
    },
    {
      id: 2,
      syncTime: '2025-01-18 06:00:00',
      syncType: 'incremental',
      status: 'partial',
      duration: 124,
      syncedRecords: 1250,
      totalRecords: 1500,
      errorMessage: '部分表同步失败，连接超时',
      details: {
        tablesProcessed: ['party_members'],
        changedRecords: 45,
        deletedRecords: 0,
        addedRecords: 12
      }
    }
  ];
  
  let filteredData = [...mockHistory];
  if (status) {
    filteredData = filteredData.filter(item => item.status === status);
  }
  
  res.json({
    success: true,
    data: filteredData,
    total: filteredData.length
  });
});

// Cron表达式解析辅助函数
function parseCronExpression(cron) {
  const cronDescriptions = {
    '0 2 * * *': '每天凌晨2点执行',
    '0 */6 * * *': '每6小时执行一次',
    '0 1 * * 0': '每周日凌晨1点执行',
    '0 0 1 * *': '每月1号执行'
  };
  return cronDescriptions[cron] || '自定义时间表';
}

module.exports = router;