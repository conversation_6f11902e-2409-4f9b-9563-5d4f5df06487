/**
 * 审核确认模块 Mock API
 * 基于MCP工具深度分析需求文档生成
 */
const express = require('express');
const router = express.Router();

// 模拟数据存储
let mockData = {
  // 用户权限数据
  userPermissions: {
    1: { userId: 1, canAudit: true, canModifyScore: true, canViewHistory: true },
    2: { userId: 2, canAudit: false, canModifyScore: false, canViewHistory: true }
  },
  
  // 数据权限
  dataPermissions: {
    1: { userId: 1, projectIds: [1, 2], departmentIds: [1, 2, 3] },
    2: { userId: 2, projectIds: [1], departmentIds: [1] }
  },
  
  // 审核流程节点
  workflowNodes: [
    { id: 1, name: '初审', description: '初步审核阶段', order: 1, status: 'active' },
    { id: 2, name: '复审', description: '复核审核阶段', order: 2, status: 'active' },
    { id: 3, name: '终审', description: '最终审核阶段', order: 3, status: 'active' }
  ],
  
  // 审核标准
  auditStandards: [
    { id: 1, name: '党组织建设标准', description: '党组织建设相关审核标准', weight: 30 },
    { id: 2, name: '活动开展标准', description: '党建活动开展审核标准', weight: 25 },
    { id: 3, name: '制度执行标准', description: '制度建设与执行标准', weight: 20 },
    { id: 4, name: '作用发挥标准', description: '党组织作用发挥标准', weight: 25 }
  ],
  
  // 审核周期
  auditCycles: [
    { id: 1, name: '月度审核', period: 'monthly', description: '每月进行一次审核' },
    { id: 2, name: '季度审核', period: 'quarterly', description: '每季度进行一次审核' },
    { id: 3, name: '年度审核', period: 'yearly', description: '每年进行一次审核' }
  ],
  
  // 项目列表
  projects: [
    { 
      id: 1, 
      name: '2025年度优秀党支部培育项目', 
      status: 'ongoing', 
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      description: '2025年度优秀党支部培育评选项目'
    },
    { 
      id: 2, 
      name: '党员先锋模范培育计划', 
      status: 'ongoing', 
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      description: '优秀党员先锋模范培育评选项目'
    }
  ],
  
  // 培育对象
  cultivationObjects: [
    {
      id: 1,
      name: '市委办公室党支部',
      projectId: 1,
      projectName: '2025年度优秀党支部培育项目',
      department: '市委办公室',
      contactPerson: '周海军',
      auditStatus: 'audited',
      score: 86,
      level: '良好',
      submitTime: '2025-06-15 10:30:00',
      auditTime: '2025-06-28 16:00:00',
      auditor: '王审核'
    },
    {
      id: 2,
      name: '教育局机关党支部',
      projectId: 1,
      projectName: '2025年度优秀党支部培育项目',
      department: '市教育局',
      contactPerson: '王东',
      auditStatus: 'audited',
      score: 92,
      level: '优秀',
      submitTime: '2025-05-20 14:20:00',
      auditTime: '2025-06-01 09:15:00',
      auditor: '李审核'
    },
    {
      id: 3,
      name: '卫健委党支部',
      projectId: 1,
      projectName: '2025年度优秀党支部培育项目',
      department: '市卫健委',
      contactPerson: '杜佳佳',
      auditStatus: 'rejected',
      score: 68,
      level: '及格',
      submitTime: '2025-05-25 16:45:00',
      auditTime: '2025-06-05 14:30:00',
      auditor: '张审核'
    },
    {
      id: 4,
      name: '财政局党支部',
      projectId: 2,
      projectName: '党员先锋模范培育计划',
      department: '市财政局',
      contactPerson: '孙建',
      auditStatus: 'pending',
      score: 78,
      level: '中等',
      submitTime: '2025-03-10 11:20:00',
      auditTime: null,
      auditor: null
    },
    {
      id: 5,
      name: '人社局党支部',
      projectId: 2,
      projectName: '党员先锋模范培育计划',
      department: '市人社局',
      contactPerson: '黄鑫',
      auditStatus: 'audited',
      score: 95,
      level: '优秀',
      submitTime: '2025-03-15 09:30:00',
      auditTime: '2025-03-25 15:20:00',
      auditor: '赵审核'
    }
  ],
  
  // 评分指标
  scoreIndicators: [
    { id: 1, name: '组织建设', weight: 30, maxScore: 30 },
    { id: 2, name: '活动开展', weight: 25, maxScore: 25 },
    { id: 3, name: '制度建设', weight: 20, maxScore: 20 },
    { id: 4, name: '作用发挥', weight: 25, maxScore: 25 }
  ],
  
  // 培育对象指标结果
  indicatorResults: {
    1: [
      { indicatorId: 1, indicatorName: '组织建设', score: 27, maxScore: 30, description: '党支部组织建设情况' },
      { indicatorId: 2, indicatorName: '活动开展', score: 22, maxScore: 25, description: '党支部活动开展情况' },
      { indicatorId: 3, indicatorName: '制度建设', score: 18, maxScore: 20, description: '党支部制度建设情况' },
      { indicatorId: 4, indicatorName: '作用发挥', score: 19, maxScore: 25, description: '党支部作用发挥情况' }
    ],
    2: [
      { indicatorId: 1, indicatorName: '组织建设', score: 28, maxScore: 30, description: '党支部组织建设情况' },
      { indicatorId: 2, indicatorName: '活动开展', score: 23, maxScore: 25, description: '党支部活动开展情况' },
      { indicatorId: 3, indicatorName: '制度建设', score: 19, maxScore: 20, description: '党支部制度建设情况' },
      { indicatorId: 4, indicatorName: '作用发挥', score: 22, maxScore: 25, description: '党支部作用发挥情况' }
    ]
  },
  
  // 审核历史记录
  auditHistory: {
    1: [
      {
        id: 1,
        auditDate: '2025-06-28 16:00:00',
        auditor: '王审核',
        auditResult: 'approved_with_conditions',
        auditOpinion: '整体表现良好，但需补充部分材料',
        scoreChanges: [
          { indicatorId: 1, oldScore: 26, newScore: 27, reason: '组织建设有所改进' }
        ]
      }
    ]
  },
  
  // 申诉记录
  appeals: [
    {
      id: 1,
      cultivationObjectId: 3,
      cultivationObjectName: '卫健委党支部',
      appealReason: '对组织建设指标评分存在异议，认为评分过低',
      appealContent: '我支部在组织建设方面已按要求完成各项工作，评分应该更高',
      appealStatus: 'processing',
      submitTime: '2025-06-10 10:00:00',
      handlerId: 1,
      handlerName: '李处理员',
      handleTime: null,
      handleResult: null,
      handleOpinion: null
    }
  ],
  
  // 通知模板
  notificationTemplates: [
    {
      id: 1,
      name: '审核通过通知模板',
      type: 'audit_approved',
      title: '审核结果通知 - 审核通过',
      content: '您的申请已通过审核，请继续保持优秀表现。',
      variables: ['objectName', 'score', 'auditor', 'auditTime']
    },
    {
      id: 2,
      name: '审核不通过通知模板',
      type: 'audit_rejected',
      title: '审核结果通知 - 审核不通过',
      content: '您的申请未通过审核，请根据意见进行改进后重新提交。',
      variables: ['objectName', 'auditOpinion', 'auditor', 'auditTime']
    }
  ],
  
  // 通知人员配置
  notificationPersonnel: [
    { id: 1, role: 'project_manager', userId: 1, userName: '项目管理员', email: '<EMAIL>' },
    { id: 2, role: 'department_head', userId: 2, userName: '部门负责人', email: '<EMAIL>' }
  ]
};

// ================= 权限管理接口 =================

// 获取用户评分结果审核操作权限
router.get('/permissions/:userId', (req, res) => {
  const { userId } = req.params;
  const permission = mockData.userPermissions[userId];
  
  if (!permission) {
    return res.status(404).json({
      success: false,
      message: '用户权限信息不存在'
    });
  }
  
  res.json({
    success: true,
    data: permission
  });
});

// 获取用户数据权限
router.get('/data-permissions/:userId', (req, res) => {
  const { userId } = req.params;
  const dataPermission = mockData.dataPermissions[userId];
  
  if (!dataPermission) {
    return res.status(404).json({
      success: false,
      message: '用户数据权限不存在'
    });
  }
  
  res.json({
    success: true,
    data: dataPermission
  });
});

// ================= 流程配置接口 =================

// 获取审核流程节点
router.get('/workflow-nodes', (req, res) => {
  res.json({
    success: true,
    data: mockData.workflowNodes
  });
});

// 配置审核流程节点
router.post('/workflow-nodes', (req, res) => {
  const newNode = {
    id: mockData.workflowNodes.length + 1,
    ...req.body,
    status: 'active'
  };
  
  mockData.workflowNodes.push(newNode);
  
  res.json({
    success: true,
    message: '审核流程节点配置成功',
    data: newNode
  });
});

// 修改审核流程节点
router.put('/workflow-nodes/:id', (req, res) => {
  const { id } = req.params;
  const nodeIndex = mockData.workflowNodes.findIndex(n => n.id == id);
  
  if (nodeIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '流程节点不存在'
    });
  }
  
  mockData.workflowNodes[nodeIndex] = {
    ...mockData.workflowNodes[nodeIndex],
    ...req.body
  };
  
  res.json({
    success: true,
    message: '审核流程节点修改成功',
    data: mockData.workflowNodes[nodeIndex]
  });
});

// 获取审核标准
router.get('/audit-standards', (req, res) => {
  res.json({
    success: true,
    data: mockData.auditStandards
  });
});

// 设置审核标准
router.post('/audit-standards', (req, res) => {
  const newStandard = {
    id: mockData.auditStandards.length + 1,
    ...req.body
  };
  
  mockData.auditStandards.push(newStandard);
  
  res.json({
    success: true,
    message: '审核标准设置成功',
    data: newStandard
  });
});

// 获取审核周期
router.get('/audit-cycles', (req, res) => {
  res.json({
    success: true,
    data: mockData.auditCycles
  });
});

// 设置审核周期
router.post('/audit-cycles', (req, res) => {
  const newCycle = {
    id: mockData.auditCycles.length + 1,
    ...req.body
  };
  
  mockData.auditCycles.push(newCycle);
  
  res.json({
    success: true,
    message: '审核周期设置成功',
    data: newCycle
  });
});

// ================= 项目管理接口 =================

// 获取项目列表
router.get('/projects', (req, res) => {
  res.json({
    success: true,
    data: mockData.projects
  });
});

// 获取项目的培育对象列表
router.get('/projects/:projectId/cultivation-objects', (req, res) => {
  const { projectId } = req.params;
  const objects = mockData.cultivationObjects.filter(obj => obj.projectId == projectId);
  
  res.json({
    success: true,
    data: objects
  });
});

// 获取培育对象列表（支持筛选和搜索）
router.get('/cultivation-objects', (req, res) => {
  const { status, name, projectId, page = 1, pageSize = 10 } = req.query;
  let filteredObjects = [...mockData.cultivationObjects];
  
  // 状态筛选
  if (status) {
    filteredObjects = filteredObjects.filter(obj => obj.auditStatus === status);
  }
  
  // 名称搜索
  if (name) {
    filteredObjects = filteredObjects.filter(obj => 
      obj.name.toLowerCase().includes(name.toLowerCase())
    );
  }
  
  // 项目筛选
  if (projectId) {
    filteredObjects = filteredObjects.filter(obj => obj.projectId == projectId);
  }
  
  // 分页处理
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + parseInt(pageSize);
  const paginatedObjects = filteredObjects.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    data: {
      list: paginatedObjects,
      total: filteredObjects.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    }
  });
});

// 获取培育对象审核状态
router.get('/cultivation-objects/:id/audit-status', (req, res) => {
  const { id } = req.params;
  const object = mockData.cultivationObjects.find(obj => obj.id == id);
  
  if (!object) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  res.json({
    success: true,
    data: {
      id: object.id,
      name: object.name,
      auditStatus: object.auditStatus,
      auditor: object.auditor,
      auditTime: object.auditTime
    }
  });
});

// ================= 评分管理接口 =================

// 获取培育对象评分结果
router.get('/cultivation-objects/:id/score-result', (req, res) => {
  const { id } = req.params;
  const object = mockData.cultivationObjects.find(obj => obj.id == id);
  
  if (!object) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  res.json({
    success: true,
    data: {
      id: object.id,
      name: object.name,
      score: object.score,
      level: object.level,
      projectName: object.projectName
    }
  });
});

// 获取培育对象指标结果
router.get('/cultivation-objects/:id/indicator-results', (req, res) => {
  const { id } = req.params;
  const indicators = mockData.indicatorResults[id] || [];
  
  res.json({
    success: true,
    data: indicators
  });
});

// 获取培育对象指标详情
router.get('/cultivation-objects/:id/indicator-details', (req, res) => {
  const { id } = req.params;
  const { indicatorId } = req.query;
  
  const indicators = mockData.indicatorResults[id] || [];
  
  if (indicatorId) {
    const indicator = indicators.find(ind => ind.indicatorId == indicatorId);
    if (!indicator) {
      return res.status(404).json({
        success: false,
        message: '指标详情不存在'
      });
    }
    
    res.json({
      success: true,
      data: {
        ...indicator,
        detailData: {
          evidences: ['证据材料1', '证据材料2'],
          comments: '该指标表现良好，符合评估标准',
          lastUpdateTime: '2025-06-15 14:30:00'
        }
      }
    });
  } else {
    res.json({
      success: true,
      data: indicators
    });
  }
});

// 修改评分结果
router.put('/cultivation-objects/:id/score-result', (req, res) => {
  const { id } = req.params;
  const { score, indicatorScores, reason } = req.body;
  
  const objectIndex = mockData.cultivationObjects.findIndex(obj => obj.id == id);
  if (objectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '培育对象不存在'
    });
  }
  
  // 更新总分
  if (score !== undefined) {
    mockData.cultivationObjects[objectIndex].score = score;
  }
  
  // 更新指标分数
  if (indicatorScores && mockData.indicatorResults[id]) {
    indicatorScores.forEach(update => {
      const indicatorIndex = mockData.indicatorResults[id].findIndex(
        ind => ind.indicatorId === update.indicatorId
      );
      if (indicatorIndex !== -1) {
        mockData.indicatorResults[id][indicatorIndex].score = update.score;
      }
    });
    
    // 重新计算总分
    const totalScore = mockData.indicatorResults[id].reduce((sum, ind) => sum + ind.score, 0);
    mockData.cultivationObjects[objectIndex].score = totalScore;
  }
  
  // 记录历史
  if (!mockData.auditHistory[id]) {
    mockData.auditHistory[id] = [];
  }
  
  mockData.auditHistory[id].push({
    id: mockData.auditHistory[id].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: '系统管理员',
    action: 'score_modified',
    reason: reason || '评分调整',
    oldScore: mockData.cultivationObjects[objectIndex].score,
    newScore: score || mockData.cultivationObjects[objectIndex].score
  });
  
  res.json({
    success: true,
    message: '评分结果修改成功',
    data: mockData.cultivationObjects[objectIndex]
  });
});

// 修改指标结果
router.put('/cultivation-objects/:id/indicator-results', (req, res) => {
  const { id } = req.params;
  const { indicatorId, score, reason } = req.body;
  
  if (!mockData.indicatorResults[id]) {
    return res.status(404).json({
      success: false,
      message: '培育对象指标结果不存在'
    });
  }
  
  const indicatorIndex = mockData.indicatorResults[id].findIndex(
    ind => ind.indicatorId == indicatorId
  );
  
  if (indicatorIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '指标不存在'
    });
  }
  
  const oldScore = mockData.indicatorResults[id][indicatorIndex].score;
  mockData.indicatorResults[id][indicatorIndex].score = score;
  
  // 重新计算总分
  const totalScore = mockData.indicatorResults[id].reduce((sum, ind) => sum + ind.score, 0);
  const objectIndex = mockData.cultivationObjects.findIndex(obj => obj.id == id);
  if (objectIndex !== -1) {
    mockData.cultivationObjects[objectIndex].score = totalScore;
  }
  
  // 记录历史
  if (!mockData.auditHistory[id]) {
    mockData.auditHistory[id] = [];
  }
  
  mockData.auditHistory[id].push({
    id: mockData.auditHistory[id].length + 1,
    auditDate: new Date().toLocaleString('zh-CN'),
    auditor: '系统管理员',
    action: 'indicator_modified',
    reason: reason || '指标分数调整',
    indicatorId: indicatorId,
    indicatorName: mockData.indicatorResults[id][indicatorIndex].indicatorName,
    oldScore: oldScore,
    newScore: score
  });
  
  res.json({
    success: true,
    message: '指标结果修改成功',
    data: mockData.indicatorResults[id][indicatorIndex]
  });
});

module.exports = router;