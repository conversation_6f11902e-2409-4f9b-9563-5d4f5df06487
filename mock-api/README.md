# MF模范机关Mock API服务

## 项目概述

这是为MF模范机关系统提供的Mock API服务，包含三个核心业务模块的完整接口实现：

- **审核确认模块** (`/api/audit-confirmation`)
- **数据体检模块** (`/api/data-inspection`) 
- **指标规则管理模块** (`/api/indicator-rules`)

## 快速开始

### 1. 安装依赖

```bash
cd mock-api
npm install
```

### 2. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

### 3. 访问服务

- **服务地址**: http://localhost:3001
- **API文档**: http://localhost:3001/api
- **健康检查**: http://localhost:3001/health

## API模块详情

### 审核确认模块 (`/api/audit-confirmation`)

#### 工作流管理
- `GET /workflows` - 获取工作流列表
- `POST /workflows` - 创建工作流
- `PUT /workflows/:id` - 更新工作流
- `DELETE /workflows/:id` - 删除工作流

#### 培育对象管理  
- `GET /cultivation-objects` - 获取培育对象列表
- `POST /cultivation-objects` - 创建培育对象
- `PUT /cultivation-objects/:id` - 更新培育对象

#### 审核操作
- `POST /audit-operations` - 执行审核操作
- `GET /audit-history` - 获取审核历史
- `GET /audit-statistics` - 获取审核统计

#### 申诉管理
- `GET /appeals` - 获取申诉列表
- `POST /appeals` - 提交申诉
- `PUT /appeals/:id` - 处理申诉

#### 通知系统
- `GET /notifications` - 获取通知列表
- `POST /notifications/send` - 发送通知

### 数据体检模块 (`/api/data-inspection`) 🚀 已全面升级

#### 🔥 新增：数据源管理 (P0级 - 9个接口)
- `GET /data-sources` - 获取数据源列表
- `POST /data-sources` - 创建数据源配置
- `PUT /data-sources/:id` - 更新数据源配置
- `DELETE /data-sources/:id` - 删除数据源配置
- `POST /data-sources/:id/test-connection` - 测试数据源连接
- `POST /data-sources/:id/sync` - 启动数据源同步
- `GET /data-sources/:id/sync-status` - 获取同步状态
- `POST /data-sources/:id/stop-sync` - 停止数据源同步
- `GET /data-sources/:id/sync-history` - 获取同步历史

#### 🔥 新增：定时任务管理 (P1级 - 6个接口)
- `GET /scheduled-tasks` - 获取定时任务列表
- `POST /scheduled-tasks` - 创建定时任务
- `PUT /scheduled-tasks/:id` - 更新定时任务
- `DELETE /scheduled-tasks/:id` - 删除定时任务
- `PUT /scheduled-tasks/:id/toggle` - 启用/禁用定时任务
- `POST /scheduled-tasks/:id/execute` - 立即执行定时任务

#### 🔥 新增：高级监控功能 (2个接口)
- `GET /scheduled-tasks/:id/history` - 获取任务执行历史
- `GET /executions/:id/logs` - 获取任务执行日志

#### 异常管理 (原有功能)
- `GET /exceptions` - 获取异常数据列表
- `GET /exceptions/:id` - 获取异常详情
- `PUT /exceptions/:id` - 更新异常状态
- `POST /exceptions/batch-handle` - 批量处理异常

#### 统计分析 (原有功能)
- `GET /statistics` - 获取异常统计
- `GET /statistics/trends` - 获取趋势分析
- `GET /inspection-reports` - 获取体检报告列表

#### 规则配置 (原有功能)
- `GET /rules` - 获取检查规则列表
- `POST /rules` - 创建检查规则
- `PUT /rules/:id` - 更新检查规则
- `DELETE /rules/:id` - 删除检查规则

#### 自动体检 (原有功能)
- `POST /auto-inspection` - 触发自动体检
- `GET /remediation-suggestions` - 获取修复建议

**📊 总计：38个接口 (新增17个P0/P1级核心接口)**

### 指标规则管理模块 (`/api/indicator-rules`)

#### 指标管理
- `GET /indicators` - 获取指标列表
- `POST /indicators` - 创建指标
- `PUT /indicators/:id` - 更新指标
- `DELETE /indicators/:id` - 删除指标
- `DELETE /indicators` - 批量删除指标

#### 数据源管理
- `GET /data-sources` - 获取数据源选项

#### 指标演算
- `GET /indicators/selection` - 获取可选指标列表
- `POST /indicators/validate-weights` - 验证权重设置
- `POST /indicators/calculate` - 执行指标演算
- `GET /analysis-models` - 获取分析模型列表
- `GET /calculations` - 获取演算历史记录

#### 模板管理
- `GET /templates` - 获取模板列表
- `POST /templates` - 创建模板
- `PUT /templates/:id` - 更新模板
- `DELETE /templates/:id` - 删除模板
- `POST /templates/:id/import` - 导入模板到项目
- `POST /templates/:id/copy` - 复制模板

## 数据模型

### 审核确认模块

```javascript
// 工作流节点
{
  id: Number,
  name: String,
  description: String,
  type: 'start' | 'audit' | 'decision' | 'end',
  permissions: Array,
  nextNodes: Array,
  conditions: Object
}

// 培育对象
{
  id: Number,
  name: String,
  type: String,
  department: String,
  auditStatus: 'pending' | 'auditing' | 'audited' | 'appeal',
  score: Number,
  level: String,
  auditHistory: Array
}
```

### 数据体检模块

```javascript
// 异常数据
{
  id: Number,
  type: String,
  severity: 'low' | 'medium' | 'high' | 'critical',
  description: String,
  dataSource: String,
  status: 'pending' | 'fixing' | 'fixed' | 'ignored',
  detectTime: String,
  fixTime: String
}

// 检查规则
{
  id: Number,
  name: String,
  type: String,
  condition: Object,
  severity: String,
  autoFix: Boolean,
  status: 'active' | 'inactive'
}
```

### 指标规则管理模块

```javascript
// 指标定义
{
  id: Number,
  name: String,
  dataSource: 'task' | 'survey' | 'vote' | 'other',
  dataSourceTitle: String,
  calculationRule: Object,
  weight: Number,
  status: 'active' | 'inactive'
}

// 指标模板
{
  id: Number,
  name: String,
  description: String,
  indicators: Array,
  status: 'active' | 'inactive'
}
```

## 配置说明

### 端口配置
默认端口：`3001`
如需修改，请编辑 `app.js` 中的 `PORT` 常量

### CORS配置
已启用跨域支持，默认允许：
- http://localhost:5173
- http://127.0.0.1:5173

### 日志配置
服务自动记录：
- 请求日志（方法、路径、请求体）
- 响应日志（状态码、响应体）
- 错误日志（异常信息、堆栈）

## 开发说明

### 添加新接口
1. 在对应的路由文件中添加新的路由处理
2. 更新 `app.js` 中的接口列表
3. 更新本README文档

### 数据持久化
当前使用内存存储，重启服务会重置所有数据
如需持久化，可考虑：
- JSON文件存储
- SQLite数据库
- Redis缓存

### 错误处理
所有接口都包含统一的错误处理：
- 参数验证错误：400
- 资源不存在：404  
- 服务器错误：500

## 测试建议

### 使用Postman测试
1. 导入API集合（可基于 `/api` 接口文档生成）
2. 设置环境变量 `baseUrl = http://localhost:3001`
3. 测试各模块的CRUD操作

### 使用curl测试
```bash
# 健康检查
curl http://localhost:3001/health

# 获取API列表
curl http://localhost:3001/api

# 获取工作流列表
curl http://localhost:3001/api/audit-confirmation/workflows

# 创建指标
curl -X POST http://localhost:3001/api/indicator-rules/indicators \
  -H "Content-Type: application/json" \
  -d '{"name":"测试指标","dataSource":"task","calculationRule":{"type":"timeliness","onTimeScore":100}}'
```

## 部署说明

### 本地部署
```bash
npm install
npm start
```

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

### PM2部署
```bash
npm install -g pm2
pm2 start app.js --name "mf-mock-api"
```

## 🚀 架构升级记录

### Mock API 架构统一 (2025-01-18)

#### 升级背景
原项目存在Mock接口分散在前端API文件中的问题：
- **代码重复**: 超过3000行重复的Mock实现
- **维护困难**: Mock数据散布在多个文件中
- **架构混乱**: 前端API文件包含大量Mock逻辑

#### 升级方案
**集中化Mock API架构**：
1. **统一服务器**: 独立Express.js Mock API服务器
2. **模块化设计**: 按业务模块组织路由
3. **标准化协议**: 统一HTTP请求/响应格式  
4. **前后分离**: 彻底分离前端和Mock逻辑

#### 技术成果
- ✅ **转换规模**: 17个API函数完成重构
- ✅ **消除冗余**: 删除3000+行重复代码
- ✅ **新增功能**: 补充17个P0/P1级核心接口
- ✅ **架构验证**: 完成端到端测试

#### 影响范围
- **前端API**: `src/api/health-check.ts` (17个函数重构)
- **Mock服务**: `mock-api/routes/data-inspection.js` (新增接口)
- **开发流程**: 需同时启动前端和Mock API服务器

#### 使用指南
```bash
# 终端1：启动Mock API服务器 (必须)
cd mock-api && node app.js

# 终端2：启动前端开发服务器
cd model && npm run dev
```

#### 架构优势
1. **开发效率**: 统一管理，修改便捷
2. **代码质量**: 消除重复，逻辑清晰
3. **功能完整**: 新增P0/P1核心功能
4. **扩展性强**: 易于添加新模块和接口

## 版本信息

- **版本**: 1.0.0 → 2.0.0 (架构升级)
- **Node.js**: >= 14.0.0
- **主要依赖**: Express 4.18.2, CORS 2.8.5
- **开发依赖**: Nodemon 3.0.2
- **新增模块**: 数据源管理、定时任务管理

## 技术支持

如遇问题请检查：
1. Node.js版本是否符合要求
2. 端口3001是否被占用
3. 依赖包是否正确安装
4. 前端请求地址是否正确配置

---

**注意**: 这是Mock API服务，仅用于开发和测试目的，不适合生产环境使用。