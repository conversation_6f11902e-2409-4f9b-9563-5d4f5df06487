# 数据体检规则配置功能实现说明

## 功能概述

根据前端 Rules.vue 页面和相关 mock 数据结构，完整实现了数据体检规则配置的后端逻辑，包括完整的 Controller、Service、Mapper 三层架构。实现了新建规则、刷新页面数据、根据查询条件搜索规则及分页展示规则列表等功能。

## 实现架构

### 1. 后端架构

#### 1.1 VO数据传输对象 (`DataInspectionRuleVO`)
- **路径**: `src/main/java/com/goodsogood/ows/model/vo/DataInspectionRuleVO.java`
- **功能**: 定义API请求和响应的数据结构
- **包含类**:
  - `HealthCheckRuleVO` - 规则基本信息
  - `RuleSearchVO` - 规则搜索参数
  - `PageResult<T>` - 分页结果封装
  - `RuleCreateVO` - 规则创建请求
  - `RuleUpdateVO` - 规则更新请求
  - `CommonResult<T>` - 统一响应格式
  - `RuleTypeOptionVO` - 规则类型选项
  - `RuleValidationVO` - 规则验证结果

#### 1.2 Mapper数据访问层 (`DataInspectionRuleConfigMapper`)
- **路径**: `src/main/java/com/goodsogood/ows/mapper/DataInspectionRuleConfigMapper.java`
- **功能**: 数据访问层，使用MyBatis注解实现SQL查询
- **特性**:
  - 使用PostgreSQL语法
  - 支持动态SQL查询和分页
  - 实现规则类型的枚举映射
  - 支持批量操作和统计查询
- **主要方法**:
  - `selectRuleListPage` - 分页查询规则列表
  - `countRuleList` - 查询规则总数
  - `selectRuleById` - 根据ID查询规则
  - `selectRulesByType` - 根据类型查询规则
  - `insertRule` - 插入规则
  - `updateRule` - 更新规则
  - `deleteRule` - 删除规则
  - `toggleRuleStatus` - 启用/禁用规则

#### 1.3 Service业务逻辑层 (`DataInspectionRuleConfigService`)
- **路径**: `src/main/java/com/goodsogood/ows/services/DataInspectionRuleConfigService.java`
- **功能**: 业务逻辑处理，数据转换和计算
- **特性**:
  - 使用Spring Cache注解实现缓存机制
  - 完整的异常处理和降级处理
  - JSON格式验证和规则内容处理
  - 支持事务管理和数据一致性
- **主要方法**:
  - `getRuleList` - 分页查询规则列表
  - `createRule` - 创建规则
  - `updateRule` - 更新规则
  - `deleteRule` - 删除规则
  - `validateRuleContent` - 验证规则内容格式
  - `getRuleTypeOptions` - 获取规则类型选项

#### 1.4 Controller层 (`DataInspectionRuleConfigController`)
- **路径**: `src/main/java/com/goodsogood/ows/controller/DataInspectionRuleConfigController.java`
- **功能**: 提供REST API接口，处理HTTP请求和响应
- **主要接口**:
  - `GET /api/health-check/rules` - 分页查询规则列表
  - `GET /api/health-check/rules/{id}` - 根据ID查询规则详情
  - `GET /api/health-check/rules/type/{ruleType}` - 根据类型查询规则
  - `POST /api/health-check/rules` - 创建规则
  - `PUT /api/health-check/rules/{id}` - 更新规则
  - `DELETE /api/health-check/rules/{id}` - 删除规则
  - `PUT /api/health-check/rules/{id}/toggle` - 启用/禁用规则
  - `POST /api/health-check/rules/validate` - 验证规则内容
  - `GET /api/health-check/rules/type-options` - 获取规则类型选项

### 2. 前端调整

#### 2.1 API接口文件更新
- **文件**: `frontpage/model/src/api/health-check.ts`
- **调整内容**:
  - 更新 `fetchHealthCheckRuleList` 函数优先调用后端API
  - 更新 `createHealthCheckRule` 函数调用真实后端接口
  - 更新 `updateHealthCheckRule` 函数支持后端API
  - 更新 `deleteHealthCheckRule` 函数优先使用后端接口
  - 保持原有的降级处理机制（引擎调用 → 静态数据）

#### 2.2 响应格式处理
更新前端代码以处理后端统一响应格式：
```javascript
// 后端响应格式: {code: 200, message: "操作成功", data: {...}}
if (response.data && response.data.code === 200) {
  return response.data.data
} else {
  throw new Error('后端API返回异常: ' + response.data?.message)
}
```

### 3. 数据库映射

基于已有的数据库表结构（20250909.sql），主要使用以下表：
- `t_data_inspection_rules` - 规则主表
- `t_data_inspection_rule_details` - 规则详情表

#### 3.1 规则类型映射
- 1 - 党组（党委）设置 → `partyOrganization`
- 2 - 党务干部任免 → `partyOfficials`
- 3 - 任务体检 → `tasks`
- 4 - 用户信息完整 → `userInfo`

#### 3.2 状态映射
- `true`/启用 → `active`
- `false`/禁用 → `inactive`

### 4. 工具类

#### 4.1 BooleanStatusTypeHandler
- **路径**: `src/main/java/com/goodsogood/ows/util/BooleanStatusTypeHandler.java`
- **功能**: MyBatis类型转换器，实现Boolean与数据库状态字符串的转换

### 5. 测试

#### 5.1 单元测试
- **文件**: `src/test/java/com/goodsogood/ows/controller/DataInspectionRuleConfigControllerTest.java`
- **覆盖**: 主要API接口的测试用例
- **包含**:
  - 规则列表查询测试
  - 规则创建测试
  - 规则类型选项获取测试
  - 规则内容验证测试
  - 按类型查询规则测试

### 6. 核心特性

#### 6.1 完整的CRUD操作
- ✅ 创建规则（Create）
- ✅ 查询规则（Read）- 支持分页、搜索、按类型查询
- ✅ 更新规则（Update）- 支持部分更新
- ✅ 删除规则（Delete）- 支持单个和批量删除

#### 6.2 搜索和过滤
- 规则名称模糊搜索
- 规则类型精确匹配
- 启用状态筛选
- 优先级筛选
- 分页查询支持

#### 6.3 数据验证
- 请求参数验证（使用@Valid注解）
- 规则内容JSON格式验证
- 规则名称唯一性检查
- 规则类型范围验证

#### 6.4 缓存机制
- 使用Spring Cache实现查询缓存
- 缓存key策略：`inspection:rules:type:pageNum:pageSize`
- 更新/删除操作自动清除相关缓存

#### 6.5 异常处理
- 完整的try-catch异常捕获
- 统一的错误响应格式
- 详细的日志记录
- 友好的错误提示信息

### 7. API文档

所有接口都使用Swagger注解进行文档化：
- 完整的参数说明和示例
- 响应数据结构说明
- 错误码和状态说明
- 支持在线测试

### 8. 部署和配置

#### 8.1 数据库配置
确保以下表和数据已创建：
```sql
-- 运行 20250909.sql 创建表结构和初始数据
-- 主要表：t_data_inspection_rules, t_data_inspection_rule_details
```

#### 8.2 应用配置
确保以下依赖和配置正确：
- Spring Boot 2.5.14
- MyBatis配置和Mapper扫描
- 缓存配置（Redis）
- Swagger配置

#### 8.3 前端配置
确保前端API请求指向正确的后端地址：
```typescript
// 前端会优先调用后端API，失败时降级到mock数据
```

## 使用说明

### 1. 启动后端服务
```bash
mvn spring-boot:run
```

### 2. 启动前端服务
```bash
cd frontpage/model
npm run dev
```

### 3. 访问API
- Swagger文档：http://localhost:8080/swagger-ui.html
- 规则列表API：GET http://localhost:8080/api/health-check/rules
- 创建规则API：POST http://localhost:8080/api/health-check/rules

### 4. 前端页面
访问规则配置页面：`http://localhost:5173/health-check/rules`

## 注意事项

1. **数据库连接**: 确保PostgreSQL数据库连接配置正确
2. **缓存配置**: 建议在生产环境中配置Redis缓存
3. **权限控制**: 可根据需要添加接口访问权限控制
4. **数据安全**: 所有输入参数都进行了验证和过滤
5. **兼容性**: 保持了与原有前端代码的兼容性
6. **降级处理**: 前端具备完善的降级机制，确保功能可用性

## 扩展功能

1. **规则导入导出**: 可以添加Excel/JSON格式的规则导入导出功能
2. **规则版本管理**: 可以添加规则变更历史和版本管理
3. **规则执行监控**: 可以添加规则执行情况的监控和统计
4. **权限细化**: 可以添加基于角色的规则管理权限
5. **规则模板**: 可以添加常用规则模板功能