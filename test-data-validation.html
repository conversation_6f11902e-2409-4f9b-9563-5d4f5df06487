<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h1>案例征集数据看板 - 数据验证测试</h1>
    
    <div id="test-results"></div>

    <script>
        // 复制我们修复的安全函数
        const safeNumber = (value, defaultValue = 0) => {
            if (value === null || value === undefined || value === '') {
                return defaultValue;
            }
            
            const num = Number(value);
            return isNaN(num) || !isFinite(num) ? defaultValue : num;
        };

        const safePercentage = (numerator, denominator, precision = 0) => {
            const num = safeNumber(numerator);
            const den = safeNumber(denominator);
            
            if (den === 0) return 0;
            
            const percentage = (num / den) * 100;
            return precision > 0 ? Number(percentage.toFixed(precision)) : Math.round(percentage);
        };

        const safeDateFormat = (dateValue) => {
            if (!dateValue || dateValue === 'Invalid Date') {
                return '-';
            }
            
            try {
                const date = new Date(dateValue);
                if (isNaN(date.getTime())) {
                    return '-';
                }
                
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch {
                return '-';
            }
        };

        const formatMetricValue = (value) => {
            const num = safeNumber(value);
            
            // 如果原始值为空或无效，返回占位符
            if ((value === null || value === undefined || value === '') && num === 0) {
                return '-';
            }
            
            return num.toLocaleString();
        };

        const formatPercentageValue = (value) => {
            const num = safeNumber(value);
            
            // 如果原始值为空或无效，返回占位符
            if ((value === null || value === undefined || value === '') && num === 0) {
                return '-';
            }
            
            // 确保百分比值在合理范围内
            if (num < 0) return 0;
            if (num > 100) return 100;
            
            return Math.round(num * 100) / 100; // 保留两位小数
        };

        // 测试用例
        const testCases = [
            // safeNumber 测试
            { name: 'safeNumber(null)', func: () => safeNumber(null), expected: 0 },
            { name: 'safeNumber(undefined)', func: () => safeNumber(undefined), expected: 0 },
            { name: 'safeNumber("")', func: () => safeNumber(''), expected: 0 },
            { name: 'safeNumber(NaN)', func: () => safeNumber(NaN), expected: 0 },
            { name: 'safeNumber(Infinity)', func: () => safeNumber(Infinity), expected: 0 },
            { name: 'safeNumber(42)', func: () => safeNumber(42), expected: 42 },
            { name: 'safeNumber("123")', func: () => safeNumber('123'), expected: 123 },
            
            // safePercentage 测试
            { name: 'safePercentage(0, 0)', func: () => safePercentage(0, 0), expected: 0 },
            { name: 'safePercentage(null, null)', func: () => safePercentage(null, null), expected: 0 },
            { name: 'safePercentage(50, 100)', func: () => safePercentage(50, 100), expected: 50 },
            { name: 'safePercentage(75, 100)', func: () => safePercentage(75, 100), expected: 75 },
            { name: 'safePercentage(NaN, 100)', func: () => safePercentage(NaN, 100), expected: 0 },
            
            // safeDateFormat 测试
            { name: 'safeDateFormat(null)', func: () => safeDateFormat(null), expected: '-' },
            { name: 'safeDateFormat("Invalid Date")', func: () => safeDateFormat('Invalid Date'), expected: '-' },
            { name: 'safeDateFormat("")', func: () => safeDateFormat(''), expected: '-' },
            { name: 'safeDateFormat("2024-01-01")', func: () => safeDateFormat('2024-01-01'), expected: '2024/01/01' },
            
            // formatMetricValue 测试
            { name: 'formatMetricValue(null)', func: () => formatMetricValue(null), expected: '-' },
            { name: 'formatMetricValue(undefined)', func: () => formatMetricValue(undefined), expected: '-' },
            { name: 'formatMetricValue("")', func: () => formatMetricValue(''), expected: '-' },
            { name: 'formatMetricValue(0)', func: () => formatMetricValue(0), expected: '0' },
            { name: 'formatMetricValue(1234)', func: () => formatMetricValue(1234), expected: '1,234' },
            
            // formatPercentageValue 测试
            { name: 'formatPercentageValue(null)', func: () => formatPercentageValue(null), expected: '-' },
            { name: 'formatPercentageValue(undefined)', func: () => formatPercentageValue(undefined), expected: '-' },
            { name: 'formatPercentageValue("")', func: () => formatPercentageValue(''), expected: '-' },
            { name: 'formatPercentageValue(0)', func: () => formatPercentageValue(0), expected: 0 },
            { name: 'formatPercentageValue(50.5)', func: () => formatPercentageValue(50.5), expected: 50.5 },
            { name: 'formatPercentageValue(-10)', func: () => formatPercentageValue(-10), expected: 0 },
            { name: 'formatPercentageValue(150)', func: () => formatPercentageValue(150), expected: 100 }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach(testCase => {
                const result = testCase.func();
                const passed = result === testCase.expected;
                
                if (passed) passCount++;

                const div = document.createElement('div');
                div.className = `test-case ${passed ? 'pass' : 'fail'}`;
                div.innerHTML = `
                    <strong>${testCase.name}</strong><br>
                    期望: ${testCase.expected}<br>
                    实际: ${result}<br>
                    结果: ${passed ? '✅ 通过' : '❌ 失败'}
                `;
                resultsDiv.appendChild(div);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.style.marginTop = '20px';
            summaryDiv.style.padding = '15px';
            summaryDiv.style.backgroundColor = passCount === totalCount ? '#d4edda' : '#f8d7da';
            summaryDiv.innerHTML = `
                <h2>测试总结</h2>
                <p>通过: ${passCount}/${totalCount}</p>
                <p>成功率: ${Math.round((passCount / totalCount) * 100)}%</p>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
