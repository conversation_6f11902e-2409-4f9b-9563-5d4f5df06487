module.exports = {
	env: {
		browser: true,
		es2021: true,
		node: true,
	},
	extends: [
		'plugin:vue/vue3-essential',
		'standard-with-typescript',
		'plugin:prettier/recommended', // 添加 prettier 插件
	],
	overrides: [
		{
			env: {
				node: true,
			},
			files: ['.eslintrc.{js,cjs}'],
			parserOptions: {
				sourceType: 'script',
			},
		},
	],
	// parser: 'vue-eslint-parser', // 新增
	parserOptions: {
		ecmaVersion: 'latest',
		// parser: '@typescript-eslint/parser',
		sourceType: 'module',
		tsconfigRootDir: __dirname,
		project: ['tsconfig.json'],
	},
	plugins: ['@typescript-eslint', 'vue'],
	rules: {
		'linebreak-style': ['error', 'unix'],
		// semi: ["error", "always"],
		'no-empty': 0,
		'comma-dangle': 0,
		'no-unused-vars': 0,
		'no-console': 0,
		'no-const-assign': 2,
		'no-dupe-class-members': 2,
		'no-duplicate-case': 2,
		'no-extra-parens': [2, 'functions'],
		'no-self-compare': 2,
		'accessor-pairs': 2,
		'comma-spacing': [
			2,
			{
				before: false,
				after: true,
			},
		],
		'constructor-super': 2,
		'new-cap': [
			2,
			{
				newIsCap: true,
				capIsNew: false,
			},
		],
		'new-parens': 2,
		'no-array-constructor': 2,
		'no-class-assign': 2,
		'no-cond-assign': 2,
	},
}
