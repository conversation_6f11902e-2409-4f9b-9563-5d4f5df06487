# 项目打包部署说明

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

## 📦 打包命令

### 1. 测试环境打包
```bash
# 仅构建，不生成压缩包
npm run build:test

# 构建并生成压缩包
npm run build:package:test
```

### 2. 生产环境打包
```bash
# 仅构建，不生成压缩包
npm run build:prod

# 构建并生成压缩包
npm run build:package:prod
```

### 3. 自定义打包
```bash
# 测试环境打包（默认）
node scripts/build.js

# 生产环境打包
node scripts/build.js --prod

# 仅构建，不生成压缩包
node scripts/build.js --no-zip
```

## 📁 输出文件

### 构建产物
- `dist/` - 构建后的静态文件
- `dist-packages/` - 压缩包输出目录

### 压缩包内容
- `dist/` - 前端静态文件
- `nginx.conf` - Nginx配置文件
- `docker-compose.yml` - Docker部署配置
- `deployment-info.json` - 部署信息文件

## 🚀 部署方式

### 1. Nginx部署
```bash
# 解压压缩包
unzip project-dashboard-production-*.zip

# 复制文件到nginx目录
cp -r dist/* /usr/share/nginx/html/
cp nginx.conf /etc/nginx/conf.d/project-dashboard.conf

# 重启nginx
systemctl reload nginx
```

### 2. Docker部署
```bash
# 解压压缩包
unzip project-dashboard-production-*.zip

# 启动容器
docker-compose up -d
```

### 3. 静态文件服务器
```bash
# 直接将dist目录内容上传到静态文件服务器
# 如：Apache、IIS、CDN等
```

## 🔧 环境配置

### 测试环境 (.env.test)
- API地址：test-api.example.com
- 标题：选育树推项目看板-测试环境
- 版本：1.0.0-test

### 生产环境 (.env.production)
- API地址：js.cqjgdj.gov.cn
- 标题：选育树推项目看板
- 版本：1.0.0

## 📋 部署检查清单

- [ ] 确认环境配置正确
- [ ] 检查API地址可访问
- [ ] 验证静态资源加载
- [ ] 测试路由跳转功能
- [ ] 确认地图API正常
- [ ] 检查数据展示正常

## 🐛 常见问题

### 1. 构建失败
- 检查Node.js版本是否符合要求
- 确认依赖包安装完整
- 查看构建日志错误信息

### 2. 部署后页面空白
- 检查nginx配置中的root路径
- 确认静态文件权限正确
- 查看浏览器控制台错误

### 3. API请求失败
- 检查环境配置文件中的API地址
- 确认服务器网络连通性
- 查看跨域配置是否正确

## 📞 技术支持

如遇到问题，请联系开发团队或查看项目文档。