# CODEBUDDY.md

This file provides guidance to CodeBuddy Code when working with this Vue 3 + TypeScript enterprise application for the "Model Government Agencies" (模范机关) system.

## Core Development Commands

```bash
# Start frontend development server (port 5173/5174)
npm run dev

# Build for production
npm run build

# Build with TypeScript checking
npm run build1

# Development build
npm run test

# Preview production build
npm run preview

# Package builds with custom configs
npm run build:package
npm run build:master
npm run build:simple
npm run build:simple:prod
```

## Technology Stack & Architecture

**Frontend**: Vue 3.2.45 + TypeScript 4.9.5 + Vite 4.1.0
**UI Framework**: Ant Design Vue 4.2.6 with auto-import via `unplugin-vue-components`
**State Management**: Pinia 3.0.3 with domain-specific modular stores
**Routing**: Vue Router 4.2.4 (Hash mode)
**HTTP Client**: Axios 1.10.0 with unified request wrapper in `src/utils/request.ts`

## Project Structure

```
src/
├── api/              # HTTP API modules, one per business domain
├── components/       # Reusable Vue components
├── store/modules/    # Pinia stores, domain-specific
├── views/           # Page components organized by business system  
├── types/           # TypeScript definitions per domain
├── utils/           # Shared utilities and helpers
├── router/          # Route definitions with nested structure
└── config/          # App configuration (breadcrumbs, permissions)
```

## Business Systems Architecture

The application contains 9 integrated business systems following the pattern: `api/` → `store/` → `types/` → `views/[system]/`

- **Health Check** (`health-check`) - Data quality monitoring
- **Honor Management** (`honor`) - Honor application and approval
- **Review System** (`review`) - Intelligent audit workflows  
- **Case Collection** (`case-collection`) - Case submission and review
- **Model Agency Dashboard** (`model-agency-dashboard`) - Executive dashboard
- **Cultivation Warning** (`cultivation-warning`) - Process monitoring alerts
- **Sensitive Words** (`sensitive-words`) - Content filtering
- **Workflow Engine** (`workflow`) - Process design and management
- **Message Center** (`message-center`) - Notification system

## Key Development Patterns

### API Development
- All HTTP requests use `src/utils/request.ts` with standardized `{code, message, data}` response format
- Authentication headers automatically injected via request interceptors
- Response data extracted using `fetch()`, `fetchList()`, and `fetchOP()` utilities

### State Management
- Domain-specific Pinia stores in `src/store/modules/`
- Use composition API pattern for all stores
- Import stores using the `store/` alias

### Component Architecture
- Ant Design Vue components auto-import (no manual imports needed)
- Shared components in `src/components/`
- Business-specific components in `src/views/[system]/components/`

### Path Aliases
- `@/` → `/src`
- `store/` → `@/store`
- `components/` → `@/components`
- `utils/` → `@/utils`
- `api/` → `@/api`

## Environment Configuration

- `.env` - Development environment variables
- `.env.production` - Production environment settings
- Vite proxy routes `/api/*` and `/file/*` to `http://localhost:8567`
- Base URL: `/pc/verify` for deployment path

## Critical Development Notes

1. **TypeScript**: Strict mode enabled with `vue-tsc` checking
2. **Responsive Design**: Uses `postcss-px-to-viewport` for mobile compatibility  
3. **Authentication**: Complex auth system with encrypted requests and multiple headers
4. **API Pattern**: All API responses follow `{code, message, data}` structure
5. **Build System**: Multiple build targets for different environments (test/prod)

## Adding New Business Modules

1. Create API module in `src/api/[module].ts`
2. Create Pinia store in `src/store/modules/[module].ts`
3. Define TypeScript types in `src/types/[module].ts`
4. Build views in `src/views/[module]/`
5. Add routes to `src/router/index.ts`