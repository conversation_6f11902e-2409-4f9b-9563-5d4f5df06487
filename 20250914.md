# 社工部PC端功能数据融合方案

## 一、方案概述

**目标：** 实现当前系统与电信侧系统的数据融合，确保用户操作无感知，数据统一存储。

**原则：** 保持现有功能稳定，用户操作方式不变，数据平滑迁移。

**核心业务系统：** 涵盖9个业务系统的完整迁移
- 健康检查系统 (health-check)
- 荣誉管理系统 (honor)
- 审查系统 (review)
- 案例收集系统 (case-collection)
- 模范机关仪表板 (model-agency-dashboard)
- 培养预警系统 (cultivation-warning)
- 敏感词管理 (sensitive-words)
- 工作流引擎 (workflow)
- 消息中心 (message-center)

## 二、系统架构设计

### 2.1 整体系统架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                        用户界面层 (Vue 3 + TypeScript)                │
├─────────────────────────────────────────────────────────────────────┤
│  健康检查  │  荣誉管理  │  审查系统  │  案例收集  │  模范机关Dashboard  │
│  cultivation │ message  │  工作流   │  敏感词   │                    │
│  warning    │  center  │  engine   │  管理     │                    │
├─────────────────────────────────────────────────────────────────────┤
│                      业务逻辑层 (Pinia Stores)                       │
├─────────────────────────────────────────────────────────────────────┤
│                      API网关层 (Axios + Request Utils)                │
├─────────────────────────────────────────────────────────────────────┤
│                      数据源切换层 (Multi-DataSource)                  │
├─────────────────┬───────────────────────────────────┬─────────────────┤
│   当前数据源     │        数据迁移中间层              │   目标数据源     │
│  (原红岩先锋)    │     (数据同步 & 校验)             │  (电信侧红岩先锋) │
│                │                                   │                │
│  ┌───────────┐  │  ┌─────────────────────────────┐  │  ┌───────────┐  │
│  │MySQL/Oracle│  │  │   数据迁移工具              │  │  │MySQL/Oracle│  │
│  │   数据库   │◄─┼─►│   - 全量迁移               │◄─┼─►│   数据库   │  │
│  │           │  │  │   - 增量同步               │  │  │           │  │
│  └───────────┘  │  │   - 一致性校验             │  │  └───────────┘  │
│                │  └─────────────────────────────┘  │                │
└─────────────────┴───────────────────────────────────┴─────────────────┘
```

### 2.2 数据流转架构图

```
用户请求 → 前端路由 → API调用 → 数据源路由器 → 数据库

阶段一（迁移前）:
┌─────────┐    ┌──────────┐    ┌─────────────┐    ┌─────────────┐
│ 用户操作 │───►│Vue3前端  │───►│ 原数据源API │───►│  原数据库   │
└─────────┘    └──────────┘    └─────────────┘    └─────────────┘

阶段二（迁移中）:
┌─────────┐    ┌──────────┐    ┌─────────────────┐    
│ 用户操作 │───►│Vue3前端  │───►│ 数据源路由器     │    
└─────────┘    └──────────┘    └─────────┬───────┘    
                                        │
                              ┌─────────▼───────┐    ┌─────────────┐
                              │ 双写策略控制器   │───►│  目标数据库  │
                              └─────────┬───────┘    └─────────────┘
                                        │
                                        ▼
                                ┌─────────────┐
                                │  原数据库   │
                                └─────────────┘

阶段三（迁移后）:
┌─────────┐    ┌──────────┐    ┌─────────────┐    ┌─────────────┐
│ 用户操作 │───►│Vue3前端  │───►│ 目标数据源API│───►│  目标数据库  │
└─────────┘    └──────────┘    └─────────────┘    └─────────────┘
```

### 2.3 多数据源配置架构

```
┌──────────────────────────────────────────────────────────────────┐
│                    应用配置层                                      │
├──────────────────────────────────────────────────────────────────┤
│  数据源配置管理器 (DataSourceConfigManager)                        │
│  ├─ 原系统数据源配置 (primary)                                     │
│  ├─ 电信侧数据源配置 (target)                                     │
│  └─ 切换策略配置 (migration-strategy)                             │
├──────────────────────────────────────────────────────────────────┤
│                    数据源路由层                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐    │
│  │   读取路由器     │  │   写入路由器     │  │   事务管理器     │    │
│  │ (ReadRouter)    │  │ (WriteRouter)   │  │(TransactionMgr) │    │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘    │
├──────────────────────────────────────────────────────────────────┤
│                      连接池管理层                                  │
│  ┌─────────────────┐                    ┌─────────────────┐      │
│  │  原系统连接池    │                    │ 电信侧连接池     │      │
│  │ (HikariCP)      │                    │ (HikariCP)      │      │
│  └─────────────────┘                    └─────────────────┘      │
└──────────────────────────────────────────────────────────────────┘
```

## 三、核心业务模块详细设计

### 3.1 健康检查系统 (health-check)
**功能描述：** 数据质量监控和系统健康状态检查

**核心组件：**
- 数据质量监控面板
- 系统性能指标收集
- 异常告警机制
- 自动修复建议

**迁移要点：**
- 监控配置数据迁移
- 历史检查记录保留
- 告警规则同步
- 性能基线数据转移

### 3.2 荣誉管理系统 (honor)
**功能描述：** 荣誉申请、审批和管理流程

**核心组件：**
- 荣誉申请表单
- 多级审批工作流
- 荣誉证书生成
- 统计分析报表

**迁移要点：**
- 荣誉类别配置迁移
- 审批流程定义同步
- 历史荣誉记录完整迁移
- 证书模板和附件转移

### 3.3 审查系统 (review)
**功能描述：** 智能审计工作流和审查管理

**核心组件：**
- 智能审查引擎
- 审查任务分配
- 审查结果管理
- 审查报告生成

**迁移要点：**
- 审查规则配置迁移
- 审查模板同步
- 历史审查记录保留
- 审查人员权限映射

### 3.4 案例收集系统 (case-collection)
**功能描述：** 案例提交、审查和管理

**核心组件：**
- 案例提交界面
- 案例分类管理
- 案例审核流程
- 案例库搜索

**迁移要点：**
- 案例分类体系迁移
- 案例内容和附件转移
- 审核状态保持
- 标签和关键词同步

### 3.5 模范机关仪表板 (model-agency-dashboard)
**功能描述：** 执行仪表板和数据可视化

**核心组件：**
- 实时数据展示
- 多维度统计图表
- 关键指标监控
- 自定义报表

**迁移要点：**
- 仪表板配置迁移
- 历史数据统计保留
- 自定义报表定义同步
- 用户个性化设置转移

### 3.6 培养预警系统 (cultivation-warning)
**功能描述：** 过程监控和预警机制

**核心组件：**
- 预警规则引擎
- 实时监控面板
- 通知推送机制
- 预警处理流程

**迁移要点：**
- 预警规则配置迁移
- 监控指标定义同步
- 历史预警记录保留
- 通知模板转移

### 3.7 敏感词管理 (sensitive-words)
**功能描述：** 内容过滤和敏感词管理

**核心组件：**
- 敏感词库管理
- 内容检测引擎
- 过滤规则配置
- 检测日志记录

**迁移要点：**
- 敏感词库完整迁移
- 过滤规则同步
- 检测历史记录保留
- 白名单和例外配置转移

### 3.8 工作流引擎 (workflow)
**功能描述：** 流程设计和管理

**核心组件：**
- 可视化流程设计器
- 流程实例管理
- 任务分配引擎
- 流程监控面板

**迁移要点：**
- 流程定义模板迁移
- 运行中流程实例保持
- 历史流程记录转移
- 用户任务状态同步

### 3.9 消息中心 (message-center)
**功能描述：** 通知和消息管理系统

**核心组件：**
- 消息推送服务
- 通知模板管理
- 消息历史记录
- 用户订阅管理

**迁移要点：**
- 消息模板配置迁移
- 历史消息记录保留
- 用户订阅设置转移
- 推送配置同步

## 四、技术实现方案

### 4.1 前端技术栈迁移策略
```
Vue 3.2.45 + TypeScript 4.9.5
├── 组件库：Ant Design Vue 4.2.6
├── 状态管理：Pinia 3.0.3 (模块化stores)
├── 路由：Vue Router 4.2.4 (Hash模式)
├── HTTP客户端：Axios 1.10.0
└── 构建工具：Vite 4.1.0
```

**迁移重点：**
- API请求地址动态配置
- 数据源切换对前端透明
- 保持现有用户体验
- 渐进式功能迁移

### 4.2 后端数据源切换机制

```java
@Configuration
public class DataSourceConfig {
    
    @Bean
    @Primary
    public DataSource routingDataSource() {
        DynamicRoutingDataSource routingDataSource = new DynamicRoutingDataSource();
        
        Map<Object, Object> dataSourceMap = new HashMap<>();
        dataSourceMap.put("primary", primaryDataSource());
        dataSourceMap.put("target", targetDataSource());
        
        routingDataSource.setTargetDataSources(dataSourceMap);
        routingDataSource.setDefaultTargetDataSource(primaryDataSource());
        
        return routingDataSource;
    }
    
    @Bean
    public DataSource primaryDataSource() {
        // 原红岩先锋系统数据源配置
        return DataSourceBuilder.create()
                .url("${spring.datasource.primary.url}")
                .username("${spring.datasource.primary.username}")
                .password("${spring.datasource.primary.password}")
                .build();
    }
    
    @Bean
    public DataSource targetDataSource() {
        // 电信侧红岩先锋数据源配置
        return DataSourceBuilder.create()
                .url("${spring.datasource.target.url}")
                .username("${spring.datasource.target.username}")
                .password("${spring.datasource.target.password}")
                .build();
    }
}
```

### 4.3 数据迁移工具架构

```
数据迁移工具 (Migration Tool)
├── 全量迁移模块
│   ├── 表结构同步
│   ├── 数据批量迁移
│   ├── 索引重建
│   └── 约束检查
├── 增量同步模块
│   ├── 变更日志捕获
│   ├── 实时数据同步
│   ├── 冲突解决
│   └── 同步状态监控
└── 一致性校验模块
    ├── 数据完整性检查
    ├── 业务逻辑验证
    ├── 性能对比测试
    └── 回滚机制
```

## 五、实施步骤详细规划

### 阶段一：准备阶段 (2-3周)

#### 1. 网络连通性确认
- 协调华为开通网络策略，确保数据源能正常访问
- 网络延迟和带宽测试
- VPN连接稳定性验证
- 防火墙规则配置

#### 2. 环境准备
- **数据库环境搭建**
  - 在电信侧创建相同的数据库表结构
  - 同步所有表结构、索引、约束
  - 配置数据库用户权限
  - 数据库性能调优

- **应用环境配置**
  - 多数据源配置部署
  - 连接池参数优化
  - 监控工具部署
  - 日志收集配置

- **功能改造验证**
  - 固守端涉及所有两企三新功能改造
  - 包括用户、党组织以及单位两企三新基础信息维护
  - 改造完成并测试验证通过
  - 自动化测试脚本编写

#### 3. 迁移工具开发
- 数据迁移脚本开发
- 一致性校验工具
- 监控面板搭建
- 回滚机制实现

### 阶段二：数据迁移 (3-4周)

#### 1. 历史数据迁移
- **数据备份**
  - 进行整体数据备份：包括用户、党组织以及单位两企三新基础信息
  - 备份验证和完整性检查
  - 备份文件安全存储

- **分批迁移策略**
  - 按业务模块分批迁移
  - 优先级排序：核心业务 → 辅助功能
  - 迁移进度监控和报告

- **数据转换和清洗**
  - 数据格式标准化
  - 重复数据清理
  - 数据关联关系维护

#### 2. 数据一致性校验
- **多层次验证**
  - 数据完整性检查以及准确性验证
  - 业务逻辑一致性验证
  - 性能基准对比测试
  - 用户体验验证

- **问题修复流程**
  - 差异数据标识
  - 根因分析
  - 修复方案制定
  - 修复效果验证

#### 3. 增量同步机制
- 实时数据同步配置
- 冲突检测和解决
- 同步延迟监控
- 故障自动恢复

### 阶段三：全面上线 (1-2周)

#### 1. 切换前准备
- 最终数据一致性校验
- 切换流程演练
- 应急预案准备
- 用户通知和培训

#### 2. 完全切换电信侧数据源
- **切换策略**
  - 数据完整性及准确性校验完成后
  - 分模块逐步切换
  - 实时监控切换状态
  - 快速回滚机制待命

- **切换验证**
  - 功能完整性测试
  - 性能基准验证
  - 用户体验确认
  - 数据一致性最终检查

#### 3. 上线后监控
- 系统稳定性监控
- 性能指标跟踪
- 用户反馈收集
- 问题快速响应

## 六、风险评估与应对方案

### 6.1 技术风险
| 风险项 | 风险等级 | 影响 | 应对方案 |
|--------|----------|------|----------|
| 网络连接不稳定 | 高 | 数据同步中断 | 多重网络备份，断线重连机制 |
| 数据迁移失败 | 高 | 业务中断 | 分批迁移，完整回滚方案 |
| 性能下降 | 中 | 用户体验差 | 性能调优，缓存优化 |
| 数据不一致 | 高 | 业务数据错误 | 多重校验，实时监控 |

### 6.2 业务风险
| 风险项 | 风险等级 | 影响 | 应对方案 |
|--------|----------|------|----------|
| 用户操作中断 | 中 | 工作效率下降 | 维护窗口规划，用户提前通知 |
| 数据丢失 | 高 | 业务数据损失 | 多重备份，版本控制 |
| 功能缺失 | 中 | 业务流程受阻 | 功能对比测试，补丁快速部署 |

### 6.3 运维风险
| 风险项 | 风险等级 | 影响 | 应对方案 |
|--------|----------|------|----------|
| 人员技能不足 | 中 | 问题处理延迟 | 技术培训，专家支持 |
| 监控盲区 | 中 | 问题发现延迟 | 全面监控体系，告警机制 |
| 应急响应慢 | 中 | 故障恢复时间长 | 应急预案，快速响应团队 |

## 七、成功标准与验收要求

### 7.1 功能验收标准
- ✅ 所有9个业务系统功能完整可用
- ✅ 用户操作体验与原系统一致
- ✅ 数据完整性100%保证
- ✅ 系统性能不低于原系统95%

### 7.2 数据验收标准
- ✅ 历史数据迁移完整性100%
- ✅ 数据一致性校验通过率100%
- ✅ 增量数据同步延迟<5秒
- ✅ 数据备份和恢复机制完整

### 7.3 技术验收标准
- ✅ 系统稳定运行7天无故障
- ✅ 多数据源切换机制稳定可靠
- ✅ 监控和告警机制完整有效
- ✅ 应急预案验证通过

## 八、项目里程碑

```
项目时间线 (总计6-9周)
│
├── Week 1-2: 环境准备和工具开发
│   ├── 网络连通性确认
│   ├── 数据库环境搭建
│   ├── 多数据源配置
│   └── 迁移工具开发
│
├── Week 3-4: 功能改造和测试
│   ├── 两企三新功能改造
│   ├── API接口适配
│   ├── 前端界面调整
│   └── 集成测试验证
│
├── Week 5-7: 数据迁移执行
│   ├── 历史数据全量迁移
│   ├── 数据一致性校验
│   ├── 增量同步配置
│   └── 迁移质量验证
│
├── Week 8: 系统切换准备
│   ├── 最终数据校验
│   ├── 切换流程演练
│   ├── 应急预案准备
│   └── 用户培训通知
│
└── Week 9: 正式上线
    ├── 数据源切换执行
    ├── 功能验证测试
    ├── 性能监控确认
    └── 项目验收完成
```
