<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .route-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .route-card {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            padding: 16px;
            background: #fafafa;
        }
        .route-card h3 {
            margin: 0 0 10px 0;
            color: #262626;
        }
        .route-card p {
            margin: 0 0 15px 0;
            color: #666;
            font-size: 14px;
        }
        .route-link {
            display: inline-block;
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .route-link:hover {
            background: #40a9ff;
        }
        .status {
            margin-top: 20px;
            padding: 16px;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
        }
        .status h3 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>党建管理系统 - 路由测试页面</h1>
        
        <div class="route-grid">
            <div class="route-card">
                <h3>首页</h3>
                <p>系统主页面</p>
                <a href="http://localhost:5173/#/home" class="route-link" target="_blank">访问首页</a>
            </div>
            
            <div class="route-card">
                <h3>荣誉申报</h3>
                <p>荣誉申报功能页面</p>
                <a href="http://localhost:5173/#/honor/apply" class="route-link" target="_blank">荣誉申报</a>
            </div>
            
            <div class="route-card">
                <h3>荣誉审核</h3>
                <p>荣誉审核功能页面</p>
                <a href="http://localhost:5173/#/honor/audit" class="route-link" target="_blank">荣誉审核</a>
            </div>
            
            <div class="route-card">
                <h3>数据体检仪表板</h3>
                <p>数据体检系统仪表板</p>
                <a href="http://localhost:5173/#/health-check/dashboard" class="route-link" target="_blank">仪表板</a>
            </div>
            
            <div class="route-card">
                <h3>智能审核申报</h3>
                <p>智能审核申报管理</p>
                <a href="http://localhost:5173/#/review/application" class="route-link" target="_blank">申报管理</a>
            </div>
            
            <div class="route-card">
                <h3>专班推荐</h3>
                <p>专班推荐功能页面</p>
                <a href="http://localhost:5173/#/special-team" class="route-link" target="_blank">专班推荐</a>
            </div>
            
            <div class="route-card">
                <h3>创建动态管理</h3>
                <p>创建动态管理功能</p>
                <a href="http://localhost:5173/#/dynamics" class="route-link" target="_blank">动态管理</a>
            </div>
            
            <div class="route-card">
                <h3>敏感词管理</h3>
                <p>敏感词管理功能</p>
                <a href="http://localhost:5173/#/sensitive-words" class="route-link" target="_blank">敏感词管理首页</a>
                <a href="http://localhost:5173/#/sensitive-words/word-library" class="route-link" target="_blank">敏感词库管理</a>
                <a href="http://localhost:5173/#/sensitive-words/word-manage" class="route-link" target="_blank">敏感词管理</a>
                <a href="http://localhost:5173/#/sensitive-words/policy-manage" class="route-link" target="_blank">过滤策略管理</a>
                <a href="http://localhost:5173/#/sensitive-words/detection" class="route-link" target="_blank">内容检测</a>
                <a href="http://localhost:5173/#/sensitive-words/test" class="route-link" target="_blank">测试页面</a>
            </div>

            <div class="route-card">
                <h3>路由测试工具</h3>
                <p>开发环境路由测试工具</p>
                <a href="http://localhost:5173/#/route-test" class="route-link" target="_blank">路由测试工具</a>
            </div>
            
            <div class="route-card">
                <h3>案例推广</h3>
                <p>案例推广功能页面</p>
                <a href="http://localhost:5173/#/case-promotion" class="route-link" target="_blank">案例推广</a>
            </div>
            
            <div class="route-card">
                <h3>模范机关总览看板</h3>
                <p>模范机关数据看板</p>
                <a href="http://localhost:5173/#/model-agency-dashboard" class="route-link" target="_blank">总览看板</a>
            </div>
            
            <div class="route-card">
                <h3>流程引擎</h3>
                <p>工作流程引擎管理</p>
                <a href="http://localhost:5173/#/workflow" class="route-link" target="_blank">流程引擎</a>
            </div>
            
            <div class="route-card">
                <h3>消息中心</h3>
                <p>系统消息中心</p>
                <a href="http://localhost:5173/#/message-center" class="route-link" target="_blank">消息中心</a>
            </div>
            
            <div class="route-card">
                <h3>选育树推项目看板</h3>
                <p>项目管理看板</p>
                <a href="http://localhost:5173/#/project-dashboard" class="route-link" target="_blank">项目看板</a>
            </div>
        </div>
        
        <div class="status">
            <h3>修复状态</h3>
            <ul>
                <li>✅ 已创建8个缺失的Vue组件文件</li>
                <li>✅ 已完善BasicLayout.vue中的路由映射</li>
                <li>✅ 已添加路由变化监听机制</li>
                <li>✅ 开发服务器已启动 (http://localhost:5173/)</li>
            </ul>
            <p><strong>测试说明：</strong>点击上方链接测试各个路由是否能正常跳转，菜单是否能正确高亮显示。</p>
        </div>
    </div>
</body>
</html>
