{"timestamp": "2025-09-09T12:00:49.608Z", "summary": {"total": 11, "passed": 11, "failed": 0, "errors": 0}, "details": [{"name": "获取用户权限", "method": "GET", "path": "/permissions/1", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"userId": 1, "canViewInspection": true, "canConfigureRules": true, "canHandleExceptions": true}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "113", "etag": "W/\"71-7da4D+slD7vSd8HigNZV9zzBDGM\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取异常概览统计", "method": "GET", "path": "/exceptions/overview", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"totalExceptions": 48, "highSeverityCount": 24, "mediumSeverityCount": 20, "lowSeverityCount": 4, "lastInspectionTime": "2025-06-15 02:00:00", "nextInspectionTime": "2025-06-16 02:00:00"}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "201", "etag": "W/\"c9-pLv+Wv1O9AAUdzN5k8S1KlUFeqo\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "按异常类型统计", "method": "GET", "path": "/exceptions/statistics/by-type", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": [{"type": "数据缺失", "count": 15, "severity": "high", "description": "关键字段缺失或为空"}, {"type": "数据格式错误", "count": 8, "severity": "medium", "description": "数据格式不符合规范"}, {"type": "数据重复", "count": 12, "severity": "medium", "description": "存在重复记录"}, {"type": "数据不一致", "count": 6, "severity": "high", "description": "跨系统数据不一致"}, {"type": "数据过期", "count": 4, "severity": "low", "description": "数据超过有效期"}, {"type": "权限异常", "count": 3, "severity": "high", "description": "数据访问权限异常"}]}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "594", "etag": "W/\"252-V0zmRE5tbGhFQfq7WqOhfygzrYQ\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "按单位统计", "method": "GET", "path": "/exceptions/statistics/by-unit", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": [{"unit": "市委办公室", "totalExceptions": 12, "highSeverity": 3, "mediumSeverity": 6, "lowSeverity": 3}, {"unit": "市教育局", "totalExceptions": 8, "highSeverity": 2, "mediumSeverity": 4, "lowSeverity": 2}, {"unit": "市卫健委", "totalExceptions": 15, "highSeverity": 5, "mediumSeverity": 7, "lowSeverity": 3}, {"unit": "市财政局", "totalExceptions": 6, "highSeverity": 1, "mediumSeverity": 3, "lowSeverity": 2}, {"unit": "市人社局", "totalExceptions": 7, "highSeverity": 2, "mediumSeverity": 3, "lowSeverity": 2}]}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "510", "etag": "W/\"1fe-GV1CthhkY+4hQ/7WIPOX71CPsbw\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取异常详情列表", "method": "GET", "path": "/exceptions/details?page=1&pageSize=10", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"list": [{"id": 1, "type": "数据缺失", "title": "党组织基本信息缺失", "description": "市委办公室党支部的成立时间字段为空", "unit": "市委办公室", "source": "党组织管理系统", "severity": "high", "status": "pending", "detectTime": "2025-06-15 09:30:00", "affectedRecords": 1, "impact": "影响党组织基础数据完整性统计", "solution": "联系市委办公室补充党支部成立时间信息", "handleTime": null, "handler": null}, {"id": 2, "type": "数据格式错误", "title": "联系电话格式不规范", "description": "教育局机关党支部联系电话格式不符合标准", "unit": "市教育局", "source": "党员信息管理系统", "severity": "medium", "status": "pending", "detectTime": "2025-06-14 14:20:00", "affectedRecords": 3, "impact": "影响联系方式的标准化管理", "solution": "将电话格式调整为标准的11位手机号或带区号的固话格式", "handleTime": null, "handler": null}, {"id": 3, "type": "数据重复", "title": "党员信息重复录入", "description": "张三的党员信息在系统中存在重复记录", "unit": "市卫健委", "source": "党员信息管理系统", "severity": "medium", "status": "resolved", "detectTime": "2025-06-13 16:45:00", "affectedRecords": 2, "impact": "影响党员统计数据准确性", "solution": "合并重复记录，保留最新的党员信息", "handleTime": "2025-06-14 10:30:00", "handler": "李数据员"}], "total": 3, "page": 1, "pageSize": 10}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "1433", "etag": "W/\"599-m26RempLp9EV5NHmmHgvACGLj3c\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取党组织规则", "method": "GET", "path": "/rules/party-organization", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"id": 1, "name": "党组（党委）设置体检", "description": "检查党组（党委）设置的完整性和规范性", "rules": [{"id": 101, "fieldName": "党组织名称", "ruleType": "required", "ruleValue": true, "errorMessage": "党组织名称不能为空"}, {"id": 102, "fieldName": "成立时间", "ruleType": "required", "ruleValue": true, "errorMessage": "成立时间不能为空"}, {"id": 103, "fieldName": "党组织类型", "ruleType": "enum", "ruleValue": ["党委", "党总支", "党支部"], "errorMessage": "党组织类型必须是：党委、党总支、党支部之一"}, {"id": 104, "fieldName": "负责人", "ruleType": "required", "ruleValue": true, "errorMessage": "党组织负责人不能为空"}], "status": "active", "lastUpdate": "2025-06-01 10:00:00"}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "759", "etag": "W/\"2f7-Oa9Tcw+AfIDt6dkaX8i3TGafVeg\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取所有规则", "method": "GET", "path": "/rules", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"partyOrganization": {"id": 1, "name": "党组（党委）设置体检", "description": "检查党组（党委）设置的完整性和规范性", "rules": [{"id": 101, "fieldName": "党组织名称", "ruleType": "required", "ruleValue": true, "errorMessage": "党组织名称不能为空"}, {"id": 102, "fieldName": "成立时间", "ruleType": "required", "ruleValue": true, "errorMessage": "成立时间不能为空"}, {"id": 103, "fieldName": "党组织类型", "ruleType": "enum", "ruleValue": ["党委", "党总支", "党支部"], "errorMessage": "党组织类型必须是：党委、党总支、党支部之一"}, {"id": 104, "fieldName": "负责人", "ruleType": "required", "ruleValue": true, "errorMessage": "党组织负责人不能为空"}], "status": "active", "lastUpdate": "2025-06-01 10:00:00"}, "partyOfficials": {"id": 2, "name": "党务干部任免体检", "description": "检查党务干部任免信息的完整性和合规性", "rules": [{"id": 201, "fieldName": "干部姓名", "ruleType": "required", "ruleValue": true, "errorMessage": "党务干部姓名不能为空"}, {"id": 202, "fieldName": "任职时间", "ruleType": "required", "ruleValue": true, "errorMessage": "任职时间不能为空"}, {"id": 203, "fieldName": "职务", "ruleType": "required", "ruleValue": true, "errorMessage": "职务信息不能为空"}, {"id": 204, "fieldName": "任免文件编号", "ruleType": "pattern", "ruleValue": "^[A-Z]{2,4}\\[\\d{4}\\]\\d{1,3}号$", "errorMessage": "任免文件编号格式不正确"}], "status": "active", "lastUpdate": "2025-05-20 14:30:00"}, "tasks": {"id": 3, "name": "任务体检", "description": "检查任务执行情况和关键节点", "rules": [{"id": 301, "fieldName": "任务名称", "ruleType": "required", "ruleValue": true, "errorMessage": "任务名称不能为空"}, {"id": 302, "fieldName": "完成时间", "ruleType": "required", "ruleValue": true, "errorMessage": "任务完成时间不能为空"}, {"id": 303, "fieldName": "完成状态", "ruleType": "enum", "ruleValue": ["已完成", "进行中", "未开始", "已逾期"], "errorMessage": "任务状态必须是规定的状态值之一"}, {"id": 304, "fieldName": "执行质量评分", "ruleType": "range", "ruleValue": {"min": 0, "max": 100}, "errorMessage": "执行质量评分必须在0-100之间"}], "status": "active", "lastUpdate": "2025-06-10 09:15:00"}, "userInfo": {"id": 4, "name": "用户信息完整体检", "description": "检查用户基本信息的完整性", "rules": [{"id": 401, "fieldName": "用户姓名", "ruleType": "required", "ruleValue": true, "errorMessage": "用户姓名不能为空"}, {"id": 402, "fieldName": "身份证号", "ruleType": "pattern", "ruleValue": "^\\d{17}[\\dXx]$", "errorMessage": "身份证号码格式不正确"}, {"id": 403, "fieldName": "联系电话", "ruleType": "pattern", "ruleValue": "^1[3-9]\\d{9}$", "errorMessage": "手机号码格式不正确"}, {"id": 404, "fieldName": "所属部门", "ruleType": "required", "ruleValue": true, "errorMessage": "所属部门不能为空"}], "status": "active", "lastUpdate": "2025-06-05 16:20:00"}}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "2911", "etag": "W/\"b5f-07HdnYoKtZcK4ZGrKPBqh5ja6f0\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取自动体检配置", "method": "GET", "path": "/auto-inspection/config", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"id": 1, "name": "定时全面体检", "enabled": true, "schedule": {"frequency": "daily", "time": "02:00:00", "timezone": "Asia/Shanghai"}, "inspectionScope": ["party_organization", "party_officials", "tasks", "user_info"], "checkTypes": ["completeness", "accuracy", "consistency", "security"], "lastRun": "2025-06-15 02:00:00", "nextRun": "2025-06-16 02:00:00", "status": "active"}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "380", "etag": "W/\"17c-jvf4lf1frqESSFh2TdJdjxo5muI\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取数据源列表", "method": "GET", "path": "/data-sources", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": [{"id": "ds-001", "sourceName": "党组织基础数据库", "sourceType": "mysql", "connectionUrl": "mysql://localhost:3306/party_org", "status": 1, "lastSyncTime": "2025-01-18 14:30:00", "syncStatus": "success", "description": "党组织基础信息数据源", "createTime": "2025-01-01 00:00:00", "updateTime": "2025-01-18 14:30:00"}, {"id": "ds-002", "sourceName": "党员档案管理系统", "sourceType": "postgresql", "connectionUrl": "postgresql://localhost:5432/member_archive", "status": 2, "lastSyncTime": "2025-01-18 10:00:00", "syncStatus": "failed", "description": "党员个人档案信息数据源", "createTime": "2025-01-01 00:00:00", "updateTime": "2025-01-18 10:00:00"}], "total": 2}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "684", "etag": "W/\"2ac-/QUUmJsz+mBtSSQNMCFf4RFypI4\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取定时任务列表", "method": "GET", "path": "/scheduled-tasks", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": [{"id": 1, "taskName": "每日数据体检任务", "taskType": 1, "checkTypes": [1, 2, 3, 4], "cronExpression": "0 2 * * *", "cronDescription": "每天凌晨2点执行", "isEnabled": true, "status": 3, "lastExecuteTime": "2025-01-18 02:00:00", "nextExecuteTime": "2025-01-19 02:00:00", "executionCount": 18, "successCount": 17, "failedCount": 1, "avgDuration": 285, "createTime": "2025-01-01 00:00:00", "updateTime": "2025-01-18 02:00:00", "creator": "系统管理员"}], "total": 1}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "468", "etag": "W/\"1d4-1ZQb3KlzSotfwT7XjEz+PAC0/xQ\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}, {"name": "获取整改权限", "method": "GET", "path": "/remediation/permissions/1", "passed": true, "mockResponse": {"success": true, "data": {"success": true, "data": {"canHandleExceptions": true}}, "status": 200, "headers": {"x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "content-type": "application/json; charset=utf-8", "content-length": "52", "etag": "W/\"34-zaRhqgsRhxpael3c+F2LDj93NHg\"", "date": "<PERSON><PERSON>, 09 Sep 2025 12:00:49 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}}, "backendResponse": null, "comparison": null, "errors": []}]}