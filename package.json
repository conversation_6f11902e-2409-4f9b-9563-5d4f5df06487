{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "vite build --mode development", "build": "vite build", "build:package": "node scripts/build.js", "build:master": "node scripts/build.js --prod", "build:test": "node scripts/build.js", "build:simple": "node scripts/build-simple.js", "build:simple:prod": "node scripts/build-simple.js --prod", "build1": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "pinia": "^3.0.3", "postcss-px-to-viewport": "^1.1.1", "qs": "^6.14.0", "vue": "^3.2.45", "vue-router": "^4.2.4", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.5.4", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "archiver": "^7.0.1", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-config-standard-with-typescript": "^34.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.9.0", "prettier": "^2.8.4", "sass": "^1.58.3", "typescript": "^4.9.5", "unplugin-vue-components": "^0.24.0", "vite": "^4.1.0", "vue-tsc": "^1.0.24"}}